#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板图片管理器
负责从服务器下载图片并在本地缓存，供自动化脚本使用
"""

import os
import hashlib
import requests
from pathlib import Path
from typing import Dict, Optional, List
import json
import time
from urllib.parse import urljoin
import urllib3

# 禁用 SSL 警告（仅用于开发环境）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class TemplateImageManager:
    """模板图片管理器"""
    
    def __init__(self, api_client, cache_dir: str = "templates"):
        """
        初始化图片管理器
        
        Args:
            api_client: API客户端实例
            cache_dir: 本地缓存目录
        """
        self.api_client = api_client
        self.cache_dir = Path(__file__).parent / cache_dir
        self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存元数据文件
        self.cache_meta_file = self.cache_dir / "cache_meta.json"
        self.cache_meta = self.load_cache_meta()
        
        # 会话配置
        self.session = requests.Session()
        self.session.verify = False  # 开发环境禁用SSL验证
        
        print(f"模板图片管理器初始化完成，缓存目录: {self.cache_dir}")
    
    def load_cache_meta(self) -> Dict:
        """加载缓存元数据"""
        try:
            if self.cache_meta_file.exists():
                with open(self.cache_meta_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载缓存元数据失败: {e}")
        
        return {}
    
    def save_cache_meta(self):
        """保存缓存元数据"""
        try:
            with open(self.cache_meta_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_meta, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存元数据失败: {e}")
    
    def get_template_image_path(self, template: Dict) -> Optional[str]:
        """
        获取模板图片的本地路径，如果不存在则下载
        
        Args:
            template: 模板信息字典
            
        Returns:
            本地图片文件路径或None
        """
        template_id = template.get('id')
        # 修复字段名称匹配问题 - 支持多种可能的字段名
        file_path = (template.get('FilePath') or
                    template.get('filePath') or
                    template.get('filepath'))

        if not template_id or not file_path:
            print(f"模板信息不完整: id={template_id}, filePath={file_path}")
            return None
        
        # 生成本地文件名
        local_filename = self.generate_local_filename(template_id, file_path)
        local_path = self.cache_dir / local_filename
        
        # 检查是否需要下载
        if self.should_download(template_id, file_path, local_path):
            if self.download_template_image(template_id, file_path, local_path):
                self.update_cache_meta(template_id, file_path, local_path)
            else:
                return None
        
        return str(local_path) if local_path.exists() else None
    
    def generate_local_filename(self, template_id: int, file_path: str) -> str:
        """
        生成本地文件名
        
        Args:
            template_id: 模板ID
            file_path: 服务器文件路径
            
        Returns:
            本地文件名
        """
        # 提取文件扩展名
        file_ext = Path(file_path).suffix or '.png'
        
        # 使用模板ID和文件路径的哈希值生成唯一文件名
        path_hash = hashlib.md5(file_path.encode()).hexdigest()[:8]
        
        return f"template_{template_id}_{path_hash}{file_ext}"
    
    def should_download(self, template_id: int, file_path: str, local_path: Path) -> bool:
        """
        判断是否需要下载图片
        
        Args:
            template_id: 模板ID
            file_path: 服务器文件路径
            local_path: 本地文件路径
            
        Returns:
            是否需要下载
        """
        # 如果本地文件不存在，需要下载
        if not local_path.exists():
            return True
        
        # 检查缓存元数据
        cache_key = str(template_id)
        if cache_key not in self.cache_meta:
            return True
        
        cached_info = self.cache_meta[cache_key]
        
        # 检查文件路径是否变化
        if cached_info.get('file_path') != file_path:
            return True
        
        # 检查文件是否过期（24小时）
        cache_time = cached_info.get('cache_time', 0)
        if time.time() - cache_time > 24 * 3600:
            return True
        
        return False
    
    def download_template_image(self, template_id: int, file_path: str, local_path: Path) -> bool:
        """
        从服务器下载模板图片
        
        Args:
            template_id: 模板ID
            file_path: 服务器文件路径
            local_path: 本地保存路径
            
        Returns:
            是否下载成功
        """
        try:
            # 构建下载URL
            # 服务器端的图片访问API: /api/custom-templates/image/{filePath}
            download_url = f"{self.api_client.base_url}/api/custom-templates/image/{file_path}"
            
            print(f"下载模板图片: {download_url}")
            
            # 添加认证头
            headers = {}
            if self.api_client.token:
                headers['Authorization'] = f"Bearer {self.api_client.token}"
            
            # 下载文件
            response = self.session.get(
                download_url,
                headers=headers,
                timeout=30,
                stream=True
            )
            
            response.raise_for_status()
            
            # 保存到本地
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 模板图片下载成功: {local_path}")
            return True
            
        except Exception as e:
            print(f"❌ 下载模板图片失败: {e}")
            # 如果下载失败，删除可能的不完整文件
            if local_path.exists():
                try:
                    local_path.unlink()
                except:
                    pass
            return False
    
    def update_cache_meta(self, template_id: int, file_path: str, local_path: Path):
        """
        更新缓存元数据
        
        Args:
            template_id: 模板ID
            file_path: 服务器文件路径
            local_path: 本地文件路径
        """
        cache_key = str(template_id)
        self.cache_meta[cache_key] = {
            'template_id': template_id,
            'file_path': file_path,
            'local_path': str(local_path),
            'cache_time': time.time(),
            'file_size': local_path.stat().st_size if local_path.exists() else 0
        }
        self.save_cache_meta()
    
    def clear_cache(self, template_id: Optional[int] = None):
        """
        清理缓存
        
        Args:
            template_id: 指定模板ID，如果为None则清理所有缓存
        """
        if template_id is None:
            # 清理所有缓存
            for file in self.cache_dir.glob("template_*"):
                try:
                    file.unlink()
                except:
                    pass
            self.cache_meta.clear()
            print("✅ 已清理所有模板图片缓存")
        else:
            # 清理指定模板的缓存
            cache_key = str(template_id)
            if cache_key in self.cache_meta:
                cached_info = self.cache_meta[cache_key]
                local_path = Path(cached_info.get('local_path', ''))
                if local_path.exists():
                    try:
                        local_path.unlink()
                    except:
                        pass
                del self.cache_meta[cache_key]
                print(f"✅ 已清理模板 {template_id} 的图片缓存")
        
        self.save_cache_meta()
    
    def get_cache_info(self) -> Dict:
        """
        获取缓存信息
        
        Returns:
            缓存统计信息
        """
        total_files = len(self.cache_meta)
        total_size = sum(info.get('file_size', 0) for info in self.cache_meta.values())
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'cache_dir': str(self.cache_dir),
            'templates': list(self.cache_meta.keys())
        }
    
    def sync_templates(self, templates: List[Dict]) -> Dict:
        """
        同步模板图片
        
        Args:
            templates: 模板列表
            
        Returns:
            同步结果统计
        """
        results = {
            'total': len(templates),
            'downloaded': 0,
            'cached': 0,
            'failed': 0,
            'errors': []
        }
        
        for template in templates:
            try:
                local_path = self.get_template_image_path(template)
                if local_path:
                    template_id = template.get('id')
                    cache_key = str(template_id)
                    
                    if cache_key in self.cache_meta:
                        # 检查是否是新下载的
                        cache_time = self.cache_meta[cache_key].get('cache_time', 0)
                        if time.time() - cache_time < 60:  # 1分钟内下载的算作新下载
                            results['downloaded'] += 1
                        else:
                            results['cached'] += 1
                    else:
                        results['downloaded'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(f"模板 {template.get('id')} 图片获取失败")
                    
            except Exception as e:
                results['failed'] += 1
                results['errors'].append(f"模板 {template.get('id')} 处理错误: {str(e)}")
        
        return results


def main():
    """测试模板图片管理器"""
    print("🖼️ 模板图片管理器测试")
    print("=" * 40)
    
    # 这里需要API客户端实例
    # manager = TemplateImageManager(api_client)
    # print(manager.get_cache_info())


if __name__ == "__main__":
    main()
