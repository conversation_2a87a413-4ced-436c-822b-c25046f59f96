<template>
  <div class="er-diagram-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="$router.back()" type="text" class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
            返回设计列表
          </el-button>
          <div class="title-section">
            <h1 class="page-title">
              <el-icon><DataBoard /></el-icon>
              {{ isEditMode ? '编辑ER图' : (isCreateMode ? '创建ER图' : 'ER图设计') }}
            </h1>
            <p class="page-description" v-if="currentDiagram">
              {{ currentDiagram.diagramName }} - v{{ currentDiagram.version }}
            </p>
          </div>
        </div>
        <div class="header-actions">
          <el-button v-if="!isCreateMode" @click="toggleEditMode" :type="isEditMode ? 'default' : 'primary'">
            <el-icon><Edit /></el-icon>
            {{ isEditMode ? '取消编辑' : '编辑' }}
          </el-button>
          <el-button v-if="isEditMode || isCreateMode" @click="saveDiagram" type="primary" :loading="saving">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-dropdown v-if="!isCreateMode && !isEditMode" trigger="click">
            <el-button>
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="exportDiagram('svg')">
                  <el-icon><Download /></el-icon>
                  导出SVG
                </el-dropdown-item>
                <el-dropdown-item @click="exportDiagram('png')">
                  <el-icon><Download /></el-icon>
                  导出PNG
                </el-dropdown-item>
                <el-dropdown-item @click="exportDiagram('pdf')">
                  <el-icon><Download /></el-icon>
                  导出PDF
                </el-dropdown-item>
                <el-dropdown-item divided @click="deleteDiagram">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧编辑区域 -->
        <el-col :span="isEditMode || isCreateMode ? 12 : 24">
          <el-card class="diagram-card">
            <template #header>
              <div class="card-header">
                <span>ER图预览</span>
                <div class="header-actions">
                  <el-button @click="refreshDiagram" size="small" text>
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="toggleFullscreen" size="small" text>
                    <el-icon><FullScreen /></el-icon>
                    全屏
                  </el-button>
                </div>
              </div>
            </template>

            <!-- Mermaid图表渲染区域 -->
            <div class="diagram-container" ref="diagramContainer">
              <div v-if="loading" class="loading-container">
                <el-skeleton :rows="8" animated />
              </div>
              <div v-else-if="error" class="error-container">
                <el-empty description="图表加载失败">
                  <el-button @click="loadDiagram" type="primary">重新加载</el-button>
                </el-empty>
              </div>
              <div v-else-if="!mermaidDefinition" class="empty-container">
                <el-empty description="暂无图表内容">
                  <el-button v-if="isEditMode || isCreateMode" @click="generateSampleDiagram" type="primary">
                    生成示例图表
                  </el-button>
                </el-empty>
              </div>
              <div v-else id="mermaid-diagram" class="mermaid-diagram"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧编辑面板 -->
        <el-col v-if="isEditMode || isCreateMode" :span="12">
          <el-card class="edit-panel">
            <template #header>
              <span>编辑ER图</span>
            </template>

            <el-form :model="diagramForm" label-width="100px" @submit.prevent>
              <el-form-item label="图表名称" required>
                <el-input
                  v-model="diagramForm.diagramName"
                  placeholder="请输入ER图名称"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="描述">
                <el-input
                  v-model="diagramForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入ER图描述"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="版本">
                <el-input
                  v-model="diagramForm.version"
                  placeholder="如: 1.0"
                  maxlength="20"
                />
              </el-form-item>

              <el-form-item label="Mermaid定义" required>
                <div class="mermaid-editor">
                  <div class="editor-toolbar">
                    <el-button @click="generateAIDiagram" size="small" type="primary" :loading="generating">
                      <el-icon><MagicStick /></el-icon>
                      AI生成
                    </el-button>
                    <el-button @click="insertTemplate" size="small">
                      <el-icon><DocumentAdd /></el-icon>
                      插入模板
                    </el-button>
                    <el-button @click="validateMermaid" size="small">
                      <el-icon><CircleCheck /></el-icon>
                      验证语法
                    </el-button>
                  </div>
                  <el-input
                    v-model="diagramForm.mermaidDefinition"
                    type="textarea"
                    :rows="15"
                    placeholder="请输入Mermaid ER图定义..."
                    class="mermaid-textarea"
                    @input="onMermaidChange"
                  />
                  <div class="editor-help">
                    <el-link href="https://mermaid.js.org/syntax/entityRelationshipDiagram.html" target="_blank" type="primary">
                      查看Mermaid ER图语法帮助
                    </el-link>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- AI生成对话框 -->
    <el-dialog v-model="showAIDialog" title="AI生成ER图" width="600px">
      <el-form :model="aiForm" label-width="120px">
        <el-form-item label="数据库类型">
          <el-select v-model="aiForm.databaseType" placeholder="请选择数据库类型">
            <el-option label="SQL Server" value="SqlServer" />
            <el-option label="MySQL" value="MySQL" />
            <el-option label="PostgreSQL" value="PostgreSQL" />
            <el-option label="Oracle" value="Oracle" />
          </el-select>
        </el-form-item>
        <el-form-item label="需求描述">
          <el-input
            v-model="aiForm.requirements"
            type="textarea"
            :rows="4"
            placeholder="请描述您的数据库设计需求..."
          />
        </el-form-item>
        <el-form-item label="AI供应商">
          <el-select
            v-model="aiForm.aiProvider"
            placeholder="选择AI供应商"
            :loading="loadingAIProviders"
          >
            <el-option
              v-for="provider in aiProviders"
              :key="provider.id"
              :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
              :value="provider.id"
              :disabled="!provider.isActive"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ provider.modelName }}</span>
                <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAIDialog = false">取消</el-button>
        <el-button @click="startAIGeneration" type="primary" :loading="generating">
          开始生成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  ArrowLeft,
  DataBoard,
  Edit,
  Check,
  MoreFilled,
  Download,
  Delete,
  Refresh,
  FullScreen,
  MagicStick,
  DocumentAdd,
  CircleCheck
} from '@element-plus/icons-vue'
import { DesignService } from '@/services/design'
import { AIProviderService } from '@/services/aiProvider'
import type { ERDiagram } from '@/types'
import mermaid from 'mermaid'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const generating = ref(false)
const error = ref('')
const isEditMode = ref(false)
const isCreateMode = ref(false)
const showAIDialog = ref(false)
const diagramContainer = ref<HTMLElement>()
const isPolling = ref(false) // 防止多个轮询同时运行

// AI供应商相关
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// 当前项目ID和图表ID
const projectId = computed(() => Number(route.params.projectId))
const diagramId = computed(() => route.query.id ? Number(route.query.id) : undefined)
const taskId = computed(() => route.query.taskId as string)

// 当前图表数据
const currentDiagram = ref<ERDiagram | null>(null)
const mermaidDefinition = ref('')

// 表单数据
const diagramForm = ref({
  diagramName: '',
  description: '',
  version: '1.0',
  mermaidDefinition: ''
})

// AI生成表单
const aiForm = ref({
  databaseType: 'SqlServer',
  requirements: '',
  aiProvider: undefined as number | undefined
})

// 计算属性
const pageTitle = computed(() => {
  if (isCreateMode.value) return '创建ER图'
  if (isEditMode.value) return '编辑ER图'
  return currentDiagram.value?.diagramName || 'ER图设计'
})

// 生命周期
onMounted(async () => {
  // 初始化Mermaid
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose',
    er: {
      diagramPadding: 20,
      layoutDirection: 'TB',
      minEntityWidth: 100,
      minEntityHeight: 75,
      entityPadding: 15,
      stroke: '#333333',
      fill: '#ECECFF',
      fontSize: 12
    }
  })

  // 检查模式
  if (route.query.mode === 'create') {
    isCreateMode.value = true
    isEditMode.value = true
    initializeNewDiagram()
  } else if (route.query.mode === 'edit') {
    isEditMode.value = true
    await loadDiagram()
  } else if (diagramId.value) {
    await loadDiagram()
  } else if (taskId.value) {
    // 如果有taskId，说明是AI生成任务，需要轮询任务状态
    await waitForAIGeneration()
  } else {
    // 如果没有ID且不是创建模式，跳转回列表
    router.push('/design')
  }
})

// 页面挂载后确保图表渲染
onMounted(() => {
  // 延迟一段时间后检查是否需要渲染图表
  setTimeout(() => {
    if (mermaidDefinition.value && !loading.value) {
      console.log('页面挂载后检查图表渲染...')
      renderMermaidDiagram()
    }
  }, 200)
})

// 监听Mermaid定义变化
watch(() => diagramForm.value.mermaidDefinition, (newValue) => {
  mermaidDefinition.value = newValue
  if (newValue) {
    nextTick(() => {
      renderMermaidDiagram()
    })
  }
}, { immediate: true })

// 方法
const initializeNewDiagram = () => {
  diagramForm.value = {
    diagramName: '新建ER图',
    description: '',
    version: '1.0',
    mermaidDefinition: ''
  }
  mermaidDefinition.value = ''
}

const loadDiagram = async () => {
  if (!diagramId.value) return

  try {
    loading.value = true
    error.value = ''

    const diagram = await DesignService.getERDiagram(diagramId.value)
    currentDiagram.value = diagram

    // 填充表单
    diagramForm.value = {
      diagramName: diagram.diagramName,
      description: diagram.description || '',
      version: diagram.version || '1.0',
      mermaidDefinition: diagram.mermaidDefinition
    }

    mermaidDefinition.value = diagram.mermaidDefinition

    // 渲染图表 - 使用延迟确保DOM完全准备好
    await nextTick()
    setTimeout(() => {
      renderMermaidDiagram()
    }, 100)

  } catch (err) {
    console.error('加载ER图失败:', err)
    error.value = '加载ER图失败'
    ElMessage.error('加载ER图失败')
  } finally {
    loading.value = false
  }
}

const waitForAIGeneration = async () => {
  if (!taskId.value || taskId.value === 'undefined') {
    console.log('taskId为空或undefined，结束轮询')
    return
  }

  // 防止多个轮询同时运行
  if (isPolling.value) {
    console.log('轮询已在进行中，跳过')
    return
  }

  console.log('开始等待AI生成，任务ID:', taskId.value)
  isPolling.value = true

  try {
    loading.value = true
    error.value = ''

    // 显示AI生成进度提示
    ElNotification({
      title: 'AI生成中',
      message: 'AI正在生成ER图，请稍候...',
      type: 'info',
      duration: 0
    })

    // 轮询任务状态
    const maxAttempts = 60 // 最多轮询60次（5分钟）
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        console.log(`第 ${attempts + 1} 次轮询任务状态...`)

        // 调用任务状态查询API
        const taskStatus = await DesignService.getTaskStatus(taskId.value)

        // 添加调试信息
        console.log('任务状态查询结果:', taskStatus)

        if (taskStatus.status === 'Completed') {
          console.log('任务已完成，开始处理结果...')

          // 任务完成，处理生成的ER图
          ElNotification.closeAll()
          ElMessage.success('ER图生成完成！')

          // 立即重置loading状态，确保DOM元素能够渲染
          loading.value = false

          if (taskStatus.result) {
            // 设置为创建模式，显示生成的内容
            isCreateMode.value = true
            isEditMode.value = true

            // 处理任务结果，可能是直接的Mermaid定义或包含更多信息的对象
            let mermaidDef = ''
            let diagramName = `AI生成ER图 - ${new Date().toLocaleString()}`
            let description = '由AI生成的ER图'

            if (typeof taskStatus.result === 'string') {
              // 如果结果是字符串，直接作为Mermaid定义
              mermaidDef = taskStatus.result
            } else if (taskStatus.result.MermaidDefinition) {
              // 如果结果是对象，提取相关字段
              mermaidDef = taskStatus.result.MermaidDefinition
              diagramName = taskStatus.result.DiagramName || diagramName
              description = taskStatus.result.Description || description
            } else if (taskStatus.result.mermaidDefinition) {
              // 兼容小写字段名
              mermaidDef = taskStatus.result.mermaidDefinition
              diagramName = taskStatus.result.diagramName || diagramName
              description = taskStatus.result.description || description
            } else {
              throw new Error('生成结果格式错误：缺少Mermaid定义')
            }

            diagramForm.value = {
              diagramName: diagramName,
              description: description,
              version: '1.0',
              mermaidDefinition: mermaidDef
            }

            mermaidDefinition.value = mermaidDef

            console.log('设置mermaidDefinition.value:', mermaidDef)
            console.log('diagramForm.value:', diagramForm.value)
            console.log('当前状态 - loading:', loading.value, 'error:', error.value, 'mermaidDefinition:', !!mermaidDefinition.value)

            // 渲染图表 - 等待多个tick确保DOM完全更新
            await nextTick()
            await nextTick()
            console.log('调用renderMermaidDiagram...')

            // 如果元素还没有渲染，再等待一下
            setTimeout(async () => {
              console.log('延迟渲染尝试...')
              await renderMermaidDiagram()
            }, 100)

            renderMermaidDiagram()

            console.log('ER图处理完成，跳出轮询循环')
          } else {
            console.error('任务结果为空')
            throw new Error('生成结果为空')
          }

          // 确保跳出循环
          console.log('任务完成，停止轮询')
          isPolling.value = false
          loading.value = false // 确保loading状态被重置
          return

        } else if (taskStatus.status === 'Failed') {
          console.error('任务失败:', taskStatus.error)
          throw new Error(taskStatus.error || '生成失败')
        } else if (taskStatus.status === 'Running' || taskStatus.status === 'Pending') {
          // 任务还在进行中，继续等待
          console.log(`任务进行中，状态: ${taskStatus.status}, 进度: ${taskStatus.progress || 0}%`)
          attempts++

          // 更新进度提示
          ElNotification.closeAll()
          ElNotification({
            title: 'AI生成中',
            message: `AI正在生成ER图，进度: ${taskStatus.progress || 0}%`,
            type: 'info',
            duration: 0
          })

          // 等待5秒后重试
          await new Promise(resolve => setTimeout(resolve, 5000))
        } else {
          // 未知状态
          console.warn('未知任务状态:', taskStatus.status)
          attempts++
          await new Promise(resolve => setTimeout(resolve, 5000))
        }

      } catch (pollError) {
        console.error('轮询任务状态失败:', pollError)
        attempts++

        // 如果是404错误且taskId包含undefined，直接结束
        if ((pollError as any)?.response?.status === 404 && taskId.value?.includes('undefined')) {
          console.error('检测到taskId为undefined，停止轮询')
          throw new Error('任务ID无效，请重新生成')
        }

        if (attempts >= maxAttempts) {
          throw new Error('任务状态查询超时，已重试60次')
        }

        // 等待5秒后重试
        console.log(`轮询失败，${5}秒后进行第${attempts + 1}次重试...`)
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }

    if (attempts >= maxAttempts) {
      throw new Error('AI生成超时，请稍后重试')
    }

  } catch (err: any) {
    console.error('等待AI生成失败:', err)
    error.value = 'AI生成失败'
    ElNotification.closeAll()
    ElMessage.error(`AI生成失败: ${err?.message || err}`)

    // 生成失败，跳转回设计列表
    setTimeout(() => {
      router.push('/design')
    }, 2000)
  } finally {
    loading.value = false
    isPolling.value = false
    console.log('轮询结束')
  }
}

const renderMermaidDiagram = async () => {
  console.log('开始渲染Mermaid图表...')
  console.log('mermaidDefinition.value:', mermaidDefinition.value)

  if (!mermaidDefinition.value) {
    console.log('mermaidDefinition为空，跳过渲染')
    return
  }

  // 等待DOM完全渲染
  await nextTick()

  // 使用重试机制确保DOM元素存在
  const maxRetries = 10
  let retries = 0

  while (retries < maxRetries) {
    const element = document.getElementById('mermaid-diagram')

    if (element) {
      try {
        console.log('找到渲染元素，开始渲染...')

        // 清空之前的内容
        element.innerHTML = ''

        // 生成唯一ID
        const id = `mermaid-${Date.now()}`

        // 渲染图表
        const { svg } = await mermaid.render(id, mermaidDefinition.value)
        element.innerHTML = svg

        console.log('Mermaid图表渲染成功')
        return // 渲染成功，退出函数

      } catch (err) {
        console.error('渲染Mermaid图表失败:', err)
        console.error('Mermaid定义内容:', mermaidDefinition.value)

        // 分析错误类型并提供修复建议
        const errorMessage = err instanceof Error ? err.message : String(err)
        let fixSuggestion = ''

        if (errorMessage.includes('Parse error') && errorMessage.includes('NEWLINE')) {
          fixSuggestion = '语法解析错误：可能包含不支持的分隔符或格式。请检查是否有表格分隔符（如 --- 或 ===）需要移除。'
        } else if (errorMessage.includes('Expecting')) {
          fixSuggestion = '语法错误：请检查 Mermaid ER 图语法是否正确。'
        } else {
          fixSuggestion = '图表渲染失败，请检查 Mermaid 语法或尝试重新生成。'
        }

        element.innerHTML = `
          <div class="error-message">
            <h4>图表渲染失败</h4>
            <p><strong>错误信息：</strong>${errorMessage}</p>
            <p><strong>修复建议：</strong>${fixSuggestion}</p>
            <div class="error-actions">
              <button onclick="window.location.reload()" class="retry-btn">重新加载</button>
              <button onclick="document.querySelector('.el-button').click()" class="regenerate-btn">重新生成</button>
            </div>
          </div>
        `
        return
      }
    } else {
      console.log(`第${retries + 1}次尝试：未找到mermaid-diagram元素，等待DOM渲染...`)
      retries++

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  console.error('达到最大重试次数，无法找到mermaid-diagram元素')
}

const toggleEditMode = () => {
  if (isEditMode.value) {
    // 取消编辑，恢复原始数据
    if (currentDiagram.value) {
      diagramForm.value = {
        diagramName: currentDiagram.value.diagramName,
        description: currentDiagram.value.description || '',
        version: currentDiagram.value.version || '1.0',
        mermaidDefinition: currentDiagram.value.mermaidDefinition
      }
      mermaidDefinition.value = currentDiagram.value.mermaidDefinition
    }
  }
  isEditMode.value = !isEditMode.value
}

const saveDiagram = async () => {
  if (!diagramForm.value.diagramName.trim()) {
    ElMessage.error('请输入图表名称')
    return
  }

  if (!diagramForm.value.mermaidDefinition.trim()) {
    ElMessage.error('请输入Mermaid定义')
    return
  }

  try {
    saving.value = true

    const data = {
      projectId: projectId.value,
      diagramName: diagramForm.value.diagramName,
      description: diagramForm.value.description,
      version: diagramForm.value.version,
      mermaidDefinition: diagramForm.value.mermaidDefinition
    }

    if (isCreateMode.value) {
      // 创建新图表
      const newDiagram = await DesignService.createERDiagram(data)
      currentDiagram.value = newDiagram
      isCreateMode.value = false
      isEditMode.value = false

      // 更新URL
      router.replace({
        name: 'ERDiagram',
        params: { projectId: projectId.value },
        query: { id: newDiagram.id }
      })

      ElMessage.success('ER图创建成功')
    } else {
      // 更新现有图表
      const updatedDiagram = await DesignService.updateERDiagram(diagramId.value!, data)
      currentDiagram.value = updatedDiagram
      isEditMode.value = false

      ElMessage.success('ER图保存成功')
    }

  } catch (err) {
    console.error('保存ER图失败:', err)
    ElMessage.error('保存ER图失败')
  } finally {
    saving.value = false
  }
}

const refreshDiagram = () => {
  renderMermaidDiagram()
}

const toggleFullscreen = () => {
  const element = diagramContainer.value
  if (!element) return

  if (!document.fullscreenElement) {
    element.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 加载AI供应商
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
  } catch (error: any) {
    console.error('加载AI供应商列表失败:', error)
    ElMessage.error('加载AI供应商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}

const generateAIDiagram = () => {
  showAIDialog.value = true
  loadAIProviders()
}

const startAIGeneration = async () => {
  if (!aiForm.value.requirements.trim()) {
    ElMessage.error('请输入需求描述')
    return
  }

  if (!aiForm.value.aiProvider) {
    ElMessage.error('请选择AI供应商')
    return
  }

  try {
    generating.value = true

    const result = await DesignService.generateERDiagram({
      projectId: projectId.value,
      databaseType: aiForm.value.databaseType,
      diagramFormat: 'Mermaid',
      aiProviderConfigId: aiForm.value.aiProvider
    })

    showAIDialog.value = false

    ElNotification({
      title: 'AI生成任务已启动',
      message: '正在生成ER图，请稍候...',
      type: 'info',
      duration: 3000
    })

    // 跳转到带有taskId的页面进行任务状态轮询
    if (result.taskId) {
      router.push({
        name: 'ERDiagram',
        params: { projectId: projectId.value },
        query: { taskId: result.taskId }
      })
    }

  } catch (err) {
    console.error('AI生成失败:', err)
    ElMessage.error('AI生成失败')
  } finally {
    generating.value = false
  }
}

const insertTemplate = () => {
  const template = `erDiagram
    USER {
        int id PK
        string username
        string email
        string password_hash
        datetime created_at
        datetime updated_at
    }

    PROJECT {
        int id PK
        string name
        string description
        int owner_id FK
        string status
        datetime created_at
    }

    TASK {
        int id PK
        string title
        text description
        int project_id FK
        int assignee_id FK
        string status
        datetime due_date
    }

    USER ||--o{ PROJECT : owns
    PROJECT ||--o{ TASK : contains
    USER ||--o{ TASK : assigned_to`

  diagramForm.value.mermaidDefinition = template
}

const validateMermaid = async () => {
  if (!diagramForm.value.mermaidDefinition.trim()) {
    ElMessage.warning('请先输入Mermaid定义')
    return
  }

  try {
    // 尝试渲染以验证语法
    const id = `validate-${Date.now()}`
    await mermaid.render(id, diagramForm.value.mermaidDefinition)
    ElMessage.success('Mermaid语法验证通过')
  } catch (err) {
    ElMessage.error(`Mermaid语法错误: ${err}`)
  }
}

const generateSampleDiagram = () => {
  insertTemplate()
}

let mermaidRenderTimer: NodeJS.Timeout | null = null

const onMermaidChange = () => {
  // 防抖渲染
  if (mermaidRenderTimer) {
    clearTimeout(mermaidRenderTimer)
  }
  mermaidRenderTimer = setTimeout(() => {
    renderMermaidDiagram()
  }, 1000)
}

const exportDiagram = async (format: 'svg' | 'png' | 'pdf') => {
  if (!mermaidDefinition.value) {
    ElMessage.error('没有可导出的图表内容')
    return
  }

  try {
    ElMessage.info('正在导出图表，请稍候...')

    const { exportDiagram: exportDiagramUtil } = await import('@/utils/exportUtils')
    const filename = currentDiagram.value?.diagramName || 'er-diagram'

    await exportDiagramUtil(mermaidDefinition.value, format, filename, {
      width: format === 'pdf' ? 297 : 1200, // PDF使用A4横向，图片使用1200px
      height: format === 'pdf' ? 210 : 800,
      backgroundColor: 'white',
      scale: 2
    })

    ElMessage.success('导出成功')
  } catch (err) {
    console.error('导出失败:', err)
    ElMessage.error(`导出失败: ${err}`)
  }
}

const deleteDiagram = async () => {
  if (!diagramId.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这个ER图吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DesignService.deleteERDiagram(diagramId.value)
    ElMessage.success('删除成功')
    router.push('/design')
  } catch (err) {
    if (err !== 'cancel') {
      console.error('删除失败:', err)
      ElMessage.error('删除失败')
    }
  }
}
</script>

<style lang="scss" scoped>
.er-diagram-view {
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;

  .page-header {
    background: white;
    border-bottom: 1px solid #ebeef5;
    padding: 16px 24px;
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        display: flex;
        align-items: flex-start;
        gap: 16px;

        .back-btn {
          margin-top: 8px;
          color: #606266;

          &:hover {
            color: #409eff;
          }
        }

        .title-section {
          .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;

            .el-icon {
              color: #409eff;
            }
          }

          .page-description {
            margin: 0;
            color: #606266;
            font-size: 14px;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }

  .main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;

    .diagram-card {
      height: calc(100vh - 200px);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
          display: flex;
          gap: 8px;
        }
      }

      .diagram-container {
        height: calc(100vh - 280px);
        overflow: auto;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background: white;
        position: relative;

        .loading-container,
        .error-container,
        .empty-container {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .mermaid-diagram {
          padding: 20px;
          text-align: center;

          :deep(svg) {
            max-width: 100%;
            height: auto;
          }
        }

        .error-message {
          color: #f56c6c;
          padding: 20px;
          text-align: center;
          background: #fef0f0;
          border: 1px solid #fbc4c4;
          border-radius: 4px;
          margin: 20px;
        }
      }
    }

    .edit-panel {
      height: calc(100vh - 200px);

      :deep(.el-card__body) {
        height: calc(100vh - 280px);
        overflow-y: auto;
      }

      .mermaid-editor {
        .editor-toolbar {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;
          flex-wrap: wrap;
        }

        .mermaid-textarea {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

          :deep(textarea) {
            font-family: inherit;
            line-height: 1.5;
          }
        }

        .editor-help {
          margin-top: 8px;
          text-align: right;
        }
      }
    }
  }
}

// 全屏模式样式
:deep(.diagram-container:fullscreen) {
  background: white;
  padding: 20px;

  .mermaid-diagram {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .er-diagram-view {
    .main-content {
      padding: 0 16px;

      .el-col {
        margin-bottom: 16px;
      }
    }

    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-left {
        flex-direction: column;
        gap: 12px;

        .back-btn {
          align-self: flex-start;
          margin-top: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .er-diagram-view {
    .page-header {
      padding: 12px 16px;

      .header-content .header-actions {
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .main-content {
      padding: 0 12px;

      .diagram-card,
      .edit-panel {
        height: auto;
        min-height: 400px;

        .diagram-container {
          height: 400px;
        }

        :deep(.el-card__body) {
          height: auto;
          max-height: none;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.er-diagram-view {
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;

  .page-header {
    background: white;
    border-bottom: 1px solid #ebeef5;
    padding: 16px 24px;
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        display: flex;
        align-items: flex-start;
        gap: 16px;

        .back-btn {
          margin-top: 8px;
          color: #606266;

          &:hover {
            color: #409eff;
          }
        }

        .title-section {
          .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;

            .el-icon {
              color: #409eff;
            }
          }

          .page-description {
            margin: 0;
            color: #606266;
            font-size: 14px;
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }

  .main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;

    .diagram-card {
      height: calc(100vh - 200px);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
          display: flex;
          gap: 8px;
        }
      }

      .diagram-container {
        height: calc(100vh - 280px);
        overflow: auto;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background: white;
        position: relative;

        .loading-container,
        .error-container,
        .empty-container {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .mermaid-diagram {
          padding: 20px;
          text-align: center;

          :deep(svg) {
            max-width: 100%;
            height: auto;
          }
        }

        .error-message {
          color: #f56c6c;
          padding: 20px;
          text-align: center;
          background: #fef0f0;
          border: 1px solid #fbc4c4;
          border-radius: 4px;
          margin: 20px;
        }
      }
    }

    .edit-panel {
      height: calc(100vh - 200px);

      :deep(.el-card__body) {
        height: calc(100vh - 280px);
        overflow-y: auto;
      }

      .mermaid-editor {
        .editor-toolbar {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;
          flex-wrap: wrap;
        }

        .mermaid-textarea {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

          :deep(textarea) {
            font-family: inherit;
            line-height: 1.5;
          }
        }

        .editor-help {
          margin-top: 8px;
          text-align: right;
        }
      }
    }
  }
}

// 全屏模式样式
:deep(.diagram-container:fullscreen) {
  background: white;
  padding: 20px;

  .mermaid-diagram {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .er-diagram-view {
    .main-content {
      padding: 0 16px;

      .el-col {
        margin-bottom: 16px;
      }
    }

    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-left {
        flex-direction: column;
        gap: 12px;

        .back-btn {
          align-self: flex-start;
          margin-top: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .er-diagram-view {
    .page-header {
      padding: 12px 16px;

      .header-content .header-actions {
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .main-content {
      padding: 0 12px;

      .diagram-card,
      .edit-panel {
        height: auto;
        min-height: 400px;

        .diagram-container {
          height: 400px;
        }

        :deep(.el-card__body) {
          height: auto;
          max-height: none;
        }
      }
    }
  }
}

// 错误信息样式
:deep(.error-message) {
  padding: 20px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 8px;
  color: #f56c6c;
  text-align: center;

  h4 {
    margin: 0 0 12px 0;
    color: #e6a23c;
    font-size: 16px;
  }

  p {
    margin: 8px 0;
    line-height: 1.5;

    strong {
      color: #303133;
    }
  }

  .error-actions {
    margin-top: 16px;
    display: flex;
    gap: 12px;
    justify-content: center;

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;

      &.retry-btn {
        background: #409eff;
        color: white;

        &:hover {
          background: #337ecc;
        }
      }

      &.regenerate-btn {
        background: #67c23a;
        color: white;

        &:hover {
          background: #529b2e;
        }
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .er-diagram-view {
    background-color: #1a1a1a;

    .page-header {
      background: #2d2d2d;
      border-bottom-color: #404040;
    }

    .diagram-container {
      background: #2d2d2d;
      border-color: #404040;

      .error-message {
        background: #3d2626;
        border-color: #5c3333;
        color: #ff7875;
      }
    }
  }
}
</style>
