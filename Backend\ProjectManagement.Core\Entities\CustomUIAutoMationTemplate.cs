using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 自定义UI自动化模板实体
    /// </summary>
    [SugarTable("CustomUIAutoMationTemplates", "自定义UI自动化模板表")]
    public class CustomUIAutoMationTemplate : BaseEntity
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [SugarColumn(ColumnDescription = "模板名称", Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "模板名称不能为空")]
        [StringLength(100, ErrorMessage = "模板名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [SugarColumn(ColumnDescription = "模板描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "模板描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 模板分类（兼容性字段，逐步迁移到CategoryId）
        /// </summary>
        [SugarColumn(ColumnDescription = "模板分类", Length = 50, IsNullable = true)]
        public string? Category { get; set; }

        /// <summary>
        /// 分类ID（关联CustomTemplateCategories表）
        /// </summary>
        [SugarColumn(ColumnDescription = "分类ID", IsNullable = true)]
        public int? CategoryId { get; set; }

        /// <summary>
        /// 模板文件路径
        /// </summary>
        [SugarColumn(ColumnDescription = "模板文件路径", Length = 500, IsNullable = false)]
        [Required(ErrorMessage = "模板文件路径不能为空")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 匹配置信度
        /// </summary>
        [SugarColumn(ColumnDescription = "匹配置信度", DecimalDigits = 2, IsNullable = false)]
        [Range(0.1, 1.0, ErrorMessage = "置信度必须在0.1到1.0之间")]
        public decimal Confidence { get; set; } = 0.7m;

        /// <summary>
        /// 标签（JSON格式存储）
        /// </summary>
        [SugarColumn(ColumnDescription = "标签", Length = 500, IsNullable = true)]
        public string? Tags { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息", Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        [SugarColumn(ColumnDescription = "使用次数", IsNullable = false)]
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        [SugarColumn(ColumnDescription = "最后使用时间", IsNullable = true)]
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 区域图片文件路径（可选）
        /// </summary>
        [SugarColumn(ColumnDescription = "区域图片文件路径，用于限定模板匹配的区域范围", Length = 500, IsNullable = true)]
        public string? RegionFilePath { get; set; }

        /// <summary>
        /// 区域图片描述
        /// </summary>
        [SugarColumn(ColumnDescription = "区域图片的描述信息", Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "区域图片描述长度不能超过200个字符")]
        public string? RegionDescription { get; set; }

        /// <summary>
        /// 区域图片匹配置信度
        /// </summary>
        [SugarColumn(ColumnDescription = "区域图片匹配的置信度阈值", DecimalDigits = 2, IsNullable = true)]
        [Range(0.1, 1.0, ErrorMessage = "区域置信度必须在0.1到1.0之间")]
        public decimal? RegionConfidence { get; set; } = 0.7m;

        /// <summary>
        /// 区域边界扩展像素
        /// </summary>
        [SugarColumn(ColumnDescription = "区域边界扩展的像素数量", IsNullable = true)]
        [Range(0, 200, ErrorMessage = "区域扩展像素必须在0到200之间")]
        public int? RegionExpand { get; set; } = 10;

        /// <summary>
        /// 是否启用区域匹配
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用区域匹配功能", IsNullable = false)]
        public bool UseRegionMatching { get; set; } = false;

        /// <summary>
        /// 分类信息（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CustomTemplateCategory? CategoryInfo { get; set; }

        /// <summary>
        /// 标签列表（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<string> TagList
        {
            get
            {
                if (string.IsNullOrEmpty(Tags))
                    return new List<string>();

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(Tags) ?? new List<string>();
                }
                catch
                {
                    return new List<string>();
                }
            }
            set
            {
                Tags = value?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(value) : null;
            }
        }
    }
}
