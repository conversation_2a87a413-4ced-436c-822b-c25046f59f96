"""
自动化任务管理UI组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .auto_task_manager import AutoTaskManager


class AutoTaskUI:
    """自动化任务管理UI"""
    
    def __init__(self, parent_frame, parent_ui, api_client):
        self.parent_frame = parent_frame
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # 创建自动化任务管理器
        self.auto_manager = AutoTaskManager(parent_ui, api_client)
        
        # 设置回调
        self.auto_manager.on_task_completed = self.on_task_completed
        self.auto_manager.on_task_failed = self.on_task_failed
        self.auto_manager.on_queue_completed = self.on_queue_completed
        
        # UI组件
        self.auto_frame = None
        self.status_label = None
        self.enable_button = None
        self.disable_button = None
        self.queue_info_label = None
        self.current_task_label = None
        
        # 状态更新定时器
        self.status_timer = None
        
        self.create_ui()

    def center_window(self, window):
        """将窗口居中显示"""
        window.update_idletasks()  # 确保窗口尺寸已计算
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def create_ui(self):
        """创建UI"""
        # 自动化控制框架
        self.auto_frame = ttk.LabelFrame(self.parent_frame, text="🤖 自动化任务管理", padding=10)
        self.auto_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行：控制按钮
        control_frame = ttk.Frame(self.auto_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.enable_button = ttk.Button(control_frame, text="🚀 启用自动模式", command=self.enable_auto_mode)
        self.enable_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.disable_button = ttk.Button(control_frame, text="⏹️ 禁用自动模式", command=self.disable_auto_mode, state="disabled")
        self.disable_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="⚙️ 设置", command=self.show_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="📋 队列管理", command=self.show_queue_manager).pack(side=tk.LEFT, padx=(0, 5))
        
        # 第二行：状态信息
        status_frame = ttk.Frame(self.auto_frame)
        status_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_frame, text="🔴 未启用", foreground="red")
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第三行：队列信息
        queue_frame = ttk.Frame(self.auto_frame)
        queue_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(queue_frame, text="队列:").pack(side=tk.LEFT)
        self.queue_info_label = ttk.Label(queue_frame, text="0/0 任务")
        self.queue_info_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 第四行：当前任务
        current_frame = ttk.Frame(self.auto_frame)
        current_frame.pack(fill=tk.X)
        
        ttk.Label(current_frame, text="当前:").pack(side=tk.LEFT)
        self.current_task_label = ttk.Label(current_frame, text="无")
        self.current_task_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 启动状态更新定时器
        self.start_status_timer()
        
    def enable_auto_mode(self):
        """启用自动模式"""
        try:
            # 获取当前开发步骤作为任务队列
            if hasattr(self.parent_ui, 'step_manager') and hasattr(self.parent_ui.step_manager, 'development_steps'):
                tasks = self.parent_ui.step_manager.development_steps.copy()
                
                if not tasks:
                    messagebox.showwarning("警告", "没有可用的开发步骤")
                    return

                # 显示所有任务的状态信息（调试用）
                print(f"📋 总共获取到 {len(tasks)} 个开发步骤:")
                for i, task in enumerate(tasks):
                    task_name = task.get('stepName', '未知')
                    task_status = task.get('status', 'Unknown')
                    print(f"   {i+1}. {task_name} - 状态: {task_status}")

                # 过滤出待处理状态的任务（包括NotStarted, Pending, InProgress）
                pending_tasks = [task for task in tasks if task.get('status') in ['NotStarted', 'Pending', 'InProgress']]

                print(f"🔍 过滤后的待处理任务数量: {len(pending_tasks)}")

                if not pending_tasks:
                    messagebox.showwarning("警告", "没有待处理的开发步骤\n\n当前任务状态分布:\n" +
                                         "\n".join([f"• {task.get('stepName', '未知')}: {task.get('status', 'Unknown')}"
                                                   for task in tasks[:5]]))  # 只显示前5个
                    return
                    
                # 设置任务队列
                self.auto_manager.set_task_queue(pending_tasks)
                
                # 启用自动模式
                self.auto_manager.enable_auto_mode()
                
                # 更新UI状态
                self.enable_button.config(state="disabled")
                self.disable_button.config(state="normal")
                self.status_label.config(text="🟢 运行中", foreground="green")

                print(f"✅ 自动模式已启用，队列中有 {len(pending_tasks)} 个任务")
                
            else:
                messagebox.showerror("错误", "无法获取开发步骤信息")
                
        except Exception as e:
            messagebox.showerror("错误", f"启用自动模式失败: {e}")
            
    def disable_auto_mode(self):
        """禁用自动模式"""
        try:
            self.auto_manager.disable_auto_mode()
            
            # 更新UI状态
            self.enable_button.config(state="normal")
            self.disable_button.config(state="disabled")
            self.status_label.config(text="🔴 已停止", foreground="red")

            print("🛑 自动模式已禁用")
            
        except Exception as e:
            messagebox.showerror("错误", f"禁用自动模式失败: {e}")
            
    def show_settings(self):
        """显示设置对话框"""
        settings_window = tk.Toplevel(self.parent_frame)
        settings_window.title("自动化设置")
        settings_window.geometry("400x300")
        settings_window.transient(self.parent_frame)
        settings_window.grab_set()

        # 设置窗口居中显示
        self.center_window(settings_window)
        
        # 设置框架
        main_frame = ttk.Frame(settings_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 检查间隔设置
        ttk.Label(main_frame, text="检查间隔 (秒):").grid(row=0, column=0, sticky=tk.W, pady=5)
        check_interval_var = tk.StringVar(value=str(self.auto_manager.config['check_interval']))
        ttk.Entry(main_frame, textvariable=check_interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 完成超时设置
        ttk.Label(main_frame, text="完成超时 (秒):").grid(row=1, column=0, sticky=tk.W, pady=5)
        timeout_var = tk.StringVar(value=str(self.auto_manager.config['completion_timeout']))
        ttk.Entry(main_frame, textvariable=timeout_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 自动下一个任务
        auto_next_var = tk.BooleanVar(value=self.auto_manager.config['auto_next_task'])
        ttk.Checkbutton(main_frame, text="自动进行下一个任务", variable=auto_next_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 自动状态更新
        auto_status_var = tk.BooleanVar(value=self.auto_manager.config['auto_status_update'])
        ttk.Checkbutton(main_frame, text="自动状态更新", variable=auto_status_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 失败时继续
        continue_on_failure_var = tk.BooleanVar(value=self.auto_manager.config.get('continue_on_failure', False))
        ttk.Checkbutton(main_frame, text="任务失败时继续下一个", variable=continue_on_failure_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        def save_settings():
            try:
                self.auto_manager.config['check_interval'] = int(check_interval_var.get())
                self.auto_manager.config['completion_timeout'] = int(timeout_var.get())
                self.auto_manager.config['auto_next_task'] = auto_next_var.get()
                self.auto_manager.config['auto_status_update'] = auto_status_var.get()
                self.auto_manager.config['continue_on_failure'] = continue_on_failure_var.get()
                
                messagebox.showinfo("成功", "设置已保存")
                settings_window.destroy()
                
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
                
        ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=settings_window.destroy).pack(side=tk.LEFT)
        
    def show_queue_manager(self):
        """显示队列管理对话框"""
        queue_window = tk.Toplevel(self.parent_frame)
        queue_window.title("任务队列管理")
        queue_window.geometry("600x400")
        queue_window.transient(self.parent_frame)
        queue_window.grab_set()

        # 设置窗口居中显示
        self.center_window(queue_window)
        
        # 主框架
        main_frame = ttk.Frame(queue_window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 队列列表
        ttk.Label(main_frame, text="任务队列:").pack(anchor=tk.W)
        
        # 创建Treeview
        columns = ('序号', '任务名称', '状态', '类型')
        queue_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            queue_tree.heading(col, text=col)
            queue_tree.column(col, width=120)
            
        queue_tree.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        # 填充队列数据
        for i, task in enumerate(self.auto_manager.task_queue):
            queue_tree.insert('', tk.END, values=(
                i + 1,
                task.get('stepName', '未知'),
                task.get('status', 'Unknown'),
                task.get('stepType', 'Unknown')
            ))
            
        # 当前任务高亮
        if self.auto_manager.task_queue:
            current_item = queue_tree.get_children()[self.auto_manager.current_task_index]
            queue_tree.selection_set(current_item)
            queue_tree.focus(current_item)
            
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="刷新", command=lambda: self.refresh_queue_display(queue_tree)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=queue_window.destroy).pack(side=tk.RIGHT)
        
    def refresh_queue_display(self, queue_tree):
        """刷新队列显示"""
        # 清空现有项目
        for item in queue_tree.get_children():
            queue_tree.delete(item)
            
        # 重新填充
        for i, task in enumerate(self.auto_manager.task_queue):
            queue_tree.insert('', tk.END, values=(
                i + 1,
                task.get('stepName', '未知'),
                task.get('status', 'Unknown'),
                task.get('stepType', 'Unknown')
            ))
            
    def start_status_timer(self):
        """启动状态更新定时器"""
        self.update_status_display()
        # 每2秒更新一次状态
        self.status_timer = self.parent_frame.after(2000, self.start_status_timer)
        
    def update_status_display(self):
        """更新状态显示"""
        try:
            status = self.auto_manager.get_status()
            
            # 更新队列信息
            current_index = status['current_task_index'] + 1 if status['current_task'] != '无' else 0
            total_tasks = status['task_queue_length']
            self.queue_info_label.config(text=f"{current_index}/{total_tasks} 任务")
            
            # 更新当前任务
            self.current_task_label.config(text=status['current_task'])
            
            # 更新状态标签
            if status['auto_mode_enabled']:
                if status['current_task'] != '无':
                    self.status_label.config(text="🟢 运行中", foreground="green")
                else:
                    self.status_label.config(text="🟡 等待中", foreground="orange")
            else:
                self.status_label.config(text="🔴 未启用", foreground="red")
                
        except Exception as e:
            print(f"❌ 更新状态显示异常: {e}")
            
    def on_task_completed(self, task):
        """任务完成回调"""
        task_name = task.get('stepName', '未知任务')
        print(f"🎉 任务完成回调: {task_name}")
        
        # 刷新父UI的开发步骤显示
        if hasattr(self.parent_ui, 'refresh_development_steps'):
            self.parent_ui.refresh_development_steps()
            
    def on_task_failed(self, task):
        """任务失败回调"""
        task_name = task.get('stepName', '未知任务')
        print(f"❌ 任务失败回调: {task_name}")
        
        # 显示失败通知
        messagebox.showerror("任务失败", f"任务 '{task_name}' 执行失败")
        
    def on_queue_completed(self):
        """队列完成回调"""
        print("🎉 所有任务已完成")
        
        # 更新UI状态
        self.enable_button.config(state="normal")
        self.disable_button.config(state="disabled")
        self.status_label.config(text="🎉 已完成", foreground="blue")

        print("🎉 所有任务已自动完成！")
        
    def destroy(self):
        """销毁组件"""
        if self.status_timer:
            self.parent_frame.after_cancel(self.status_timer)
            
        if self.auto_manager.auto_mode_enabled:
            self.auto_manager.disable_auto_mode()
