using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 开发步骤实体 - 对应DatabaseSchema.sql中的DevelopmentSteps表
/// 功能: 存储需求分解后的具体开发步骤
/// 支持: 步骤依赖管理、AI代码生成、执行状态跟踪、层级结构
/// </summary>
[SugarTable("DevelopmentSteps", "开发步骤管理表")]
public class DevelopmentStep : BaseEntity
{
    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的需求文档ID，可为空")]
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 父步骤ID，用于构建步骤层级结构
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "父步骤ID，用于构建步骤层级结构")]
    public int? ParentStepId { get; set; }

    /// <summary>
    /// 步骤名称
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "步骤名称")]
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// 步骤详细描述
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "步骤详细描述")]
    public string? StepDescription { get; set; }

    /// <summary>
    /// 步骤类型: Development(开发), Testing(测试), Documentation(文档), Deployment(部署), Review(审查)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "步骤类型")]
    public string StepType { get; set; } = "Development";

    /// <summary>
    /// 优先级: Low(低), Medium(中), High(高), Critical(紧急)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "优先级")]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// 预估工时（小时）
    /// </summary>
    [SugarColumn(ColumnDataType = "decimal(10,2)", IsNullable = true, ColumnDescription = "预估工时（小时）")]
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 实际工时（小时）
    /// </summary>
    [SugarColumn(ColumnDataType = "decimal(10,2)", IsNullable = true, ColumnDescription = "实际工时（小时）")]
    public decimal? ActualHours { get; set; }

    /// <summary>
    /// 技术栈: Vue, React, C#, Python, Java等
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "技术栈")]
    public string? TechnologyStack { get; set; }

    /// <summary>
    /// 文件类型: Component, Service, Controller, Model, View等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "文件类型")]
    public string? FileType { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "文件路径")]
    public string? FilePath { get; set; }

    /// <summary>
    /// 组件类型: Frontend, Backend, Database, API等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "组件类型")]
    public string? ComponentType { get; set; }

    /// <summary>
    /// AI生成提示词
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成提示词")]
    public string? AIPrompt { get; set; }

    /// <summary>
    /// AI生成的代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成的代码")]
    public string? AIGeneratedCode { get; set; }

    /// <summary>
    /// AI提供商: OpenAI, Azure, Claude等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "AI提供商")]
    public string? AIProvider { get; set; }

    /// <summary>
    /// AI模型: GPT-4, GPT-3.5, Claude-3等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "AI模型")]
    public string? AIModel { get; set; }

    /// <summary>
    /// 步骤状态: Pending(待处理), InProgress(进行中), Completed(已完成), Failed(失败), Blocked(阻塞)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "步骤状态")]
    public string Status { get; set; } = "Pending";

    /// <summary>
    /// 完成进度（0-100）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "完成进度（0-100）")]
    public int Progress { get; set; } = 0;

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "开始时间")]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "结束时间")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "完成时间")]
    public DateTime? CompletedTime { get; set; }

    /// <summary>
    /// 执行结果: Success(成功), Failed(失败), Partial(部分成功)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "执行结果")]
    public string? ExecutionResult { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 输出路径
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "输出路径")]
    public string? OutputPath { get; set; }

    /// <summary>
    /// 生成的文件列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "生成的文件列表（JSON格式）")]
    public string? GeneratedFiles { get; set; }

    /// <summary>
    /// 步骤排序号
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "步骤排序号")]
    public int StepOrder { get; set; } = 0;

    /// <summary>
    /// 步骤分组
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "步骤分组")]
    public string? StepGroup { get; set; }

    /// <summary>
    /// 步骤层级（1为顶级）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "步骤层级（1为顶级）")]
    public int StepLevel { get; set; } = 1;

    /// <summary>
    /// 参考图片路径列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "参考图片路径列表（JSON格式）")]
    public string? ReferenceImages { get; set; }

    #region 导航属性

    /// <summary>
    /// 所属项目
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的需求文档
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RequirementDocument? RequirementDocument { get; set; }

    /// <summary>
    /// 父步骤
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DevelopmentStep? ParentStep { get; set; }

    /// <summary>
    /// 子步骤列表
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<DevelopmentStep> ChildSteps { get; set; } = new();

    /// <summary>
    /// 步骤依赖列表（此步骤依赖的其他步骤）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<StepDependency> Dependencies { get; set; } = new();

    /// <summary>
    /// 被依赖列表（依赖此步骤的其他步骤）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<StepDependency> Dependents { get; set; } = new();

    /// <summary>
    /// 执行历史记录
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<StepExecutionHistory> ExecutionHistories { get; set; } = new();

    /// <summary>
    /// 创建人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Creator { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Updater { get; set; }

    #endregion

    #region 业务方法

    /// <summary>
    /// 是否可以开始执行（检查依赖是否完成）
    /// </summary>
    public bool CanStart()
    {
        return Dependencies.All(d => d.DependsOnStep?.Status == "Completed" || !d.IsRequired);
    }

    /// <summary>
    /// 开始执行步骤
    /// </summary>
    public void Start()
    {
        if (Status == "Pending" && CanStart())
        {
            Status = "InProgress";
            StartTime = DateTime.Now;
            Progress = 0;
        }
    }

    /// <summary>
    /// 完成步骤
    /// </summary>
    public void Complete()
    {
        Status = "Completed";
        Progress = 100;
        EndTime = DateTime.Now;
        CompletedTime = DateTime.Now;
        ExecutionResult = "Success";
    }

    /// <summary>
    /// 标记步骤失败
    /// </summary>
    public void Fail(string errorMessage)
    {
        Status = "Failed";
        EndTime = DateTime.Now;
        ExecutionResult = "Failed";
        ErrorMessage = errorMessage;
    }

    /// <summary>
    /// 阻塞步骤
    /// </summary>
    public void Block(string reason)
    {
        Status = "Blocked";
        ErrorMessage = reason;
    }

    /// <summary>
    /// 更新进度
    /// </summary>
    public void UpdateProgress(int progress)
    {
        Progress = Math.Max(0, Math.Min(100, progress));
        if (Progress == 100 && Status == "InProgress")
        {
            Complete();
        }
    }

    #endregion
}
