using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 用户AI配置实体类
/// 功能: 管理每个用户的个人AI模型配置和API密钥
/// 支持: 个人API密钥、模型偏好、使用统计、成本控制
/// </summary>
[SugarTable("UserAIConfigurations", "用户个人AI模型配置管理表")]
public class UserAIConfiguration
{
    /// <summary>
    /// 用户AI配置唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "用户AI配置唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID，关联Users表
    /// </summary>
    [SugarColumn(ColumnDescription = "用户ID，关联Users表")]
    public int UserId { get; set; }

    /// <summary>
    /// AI提供商名称: Azure, OpenAI, DeepSeek, Claude, Ollama等
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(50)", IsNullable = false, ColumnDescription = "AI提供商名称")]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(100)", IsNullable = false, ColumnDescription = "AI模型名称")]
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型用途类型: RequirementAnalysis(需求分析), CodeGeneration(代码生成), Testing(测试), Debugging(调试)
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(50)", IsNullable = false, ColumnDescription = "模型用途类型")]
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(500)", IsNullable = true, ColumnDescription = "API端点URL地址")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// 用户个人API密钥（加密存储）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(255)", IsNullable = true, ColumnDescription = "用户个人API密钥（加密存储）")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置，JSON格式存储（温度、最大令牌数等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "模型参数配置，JSON格式存储")]
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用该配置，0=禁用, 1=启用
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "是否启用该配置")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 是否为该用户的默认配置，0=否, 1=是
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "是否为该用户的默认配置")]
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 每日令牌使用限制（成本控制）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "每日令牌使用限制（成本控制）")]
    public int? DailyTokenLimit { get; set; }

    /// <summary>
    /// 每月令牌使用限制（成本控制）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "每月令牌使用限制（成本控制）")]
    public int? MonthlyTokenLimit { get; set; }

    /// <summary>
    /// 总计使用的令牌数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "总计使用的令牌数")]
    public long TotalTokensUsed { get; set; } = 0;

    /// <summary>
    /// 总计请求次数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "总计请求次数")]
    public int TotalRequests { get; set; } = 0;

    /// <summary>
    /// 成功请求次数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "成功请求次数")]
    public int SuccessfulRequests { get; set; } = 0;

    /// <summary>
    /// 失败请求次数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "失败请求次数")]
    public int FailedRequests { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "最后使用时间")]
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 预估总成本（美元）
    /// </summary>
    [SugarColumn(ColumnDataType = "decimal(10,4)", IsNullable = true, ColumnDescription = "预估总成本（美元）")]
    public decimal EstimatedCost { get; set; } = 0;

    /// <summary>
    /// 当月成本
    /// </summary>
    [SugarColumn(ColumnDataType = "decimal(10,4)", IsNullable = true, ColumnDescription = "当月成本")]
    public decimal CurrentMonthCost { get; set; } = 0;

    /// <summary>
    /// 当日已使用令牌数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "当日已使用令牌数")]
    public int CurrentDayTokens { get; set; } = 0;

    /// <summary>
    /// 当月已使用令牌数
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "当月已使用令牌数")]
    public long CurrentMonthTokens { get; set; } = 0;

    /// <summary>
    /// 配置创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "配置创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 配置最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "配置最后更新时间")]
    public DateTime? UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 用户导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, List<string> Errors) ValidateConfiguration()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(ProviderName))
            errors.Add("提供商名称不能为空");

        if (string.IsNullOrWhiteSpace(ModelName))
            errors.Add("模型名称不能为空");

        if (string.IsNullOrWhiteSpace(ModelType))
            errors.Add("模型类型不能为空");

        // 验证API密钥（某些提供商需要）
        if (ProviderName != "Ollama" && string.IsNullOrWhiteSpace(ApiKey))
            errors.Add("API密钥不能为空");

        // 验证令牌限制
        if (DailyTokenLimit.HasValue && DailyTokenLimit <= 0)
            errors.Add("每日令牌限制必须大于0");

        if (MonthlyTokenLimit.HasValue && MonthlyTokenLimit <= 0)
            errors.Add("每月令牌限制必须大于0");

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 检查是否超出使用限制
    /// </summary>
    /// <param name="tokensToUse">要使用的令牌数</param>
    /// <returns>是否超出限制</returns>
    public bool IsWithinUsageLimit(int tokensToUse)
    {
        // 检查每日限制
        if (DailyTokenLimit.HasValue && CurrentDayTokens + tokensToUse > DailyTokenLimit.Value)
            return false;

        // 检查每月限制
        if (MonthlyTokenLimit.HasValue && CurrentMonthTokens + tokensToUse > MonthlyTokenLimit.Value)
            return false;

        return true;
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    /// <param name="tokensUsed">使用的令牌数</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="cost">成本</param>
    public void UpdateUsageStatistics(int tokensUsed, bool isSuccess, decimal cost = 0)
    {
        TotalRequests++;
        if (isSuccess)
        {
            SuccessfulRequests++;
            TotalTokensUsed += tokensUsed;
            CurrentDayTokens += tokensUsed;
            CurrentMonthTokens += tokensUsed;
            EstimatedCost += cost;
            CurrentMonthCost += cost;
        }
        else
        {
            FailedRequests++;
        }

        LastUsedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 重置每日统计
    /// </summary>
    public void ResetDailyStatistics()
    {
        CurrentDayTokens = 0;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 重置每月统计
    /// </summary>
    public void ResetMonthlyStatistics()
    {
        CurrentMonthTokens = 0;
        CurrentMonthCost = 0;
        UpdatedAt = DateTime.UtcNow;
    }
}
