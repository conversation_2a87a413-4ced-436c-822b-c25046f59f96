using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 执行日志仓储实现
    /// </summary>
    public class ExecutionLogRepository : BaseRepository<UIAutoMationTemplateExecutionLog>, IExecutionLogRepository
    {
        public ExecutionLogRepository(ISqlSugarClient db, ILogger<BaseRepository<UIAutoMationTemplateExecutionLog>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 分页查询执行日志
        /// </summary>
        public async Task<PagedResultDto<UIAutoMationTemplateExecutionLog>> GetPagedAsync(ExecutionLogQueryDto query)
        {
            var queryable = _db.Queryable<UIAutoMationTemplateExecutionLog>();

            // 序列ID过滤
            if (query.SequenceId.HasValue)
            {
                queryable = queryable.Where(x => x.SequenceId == query.SequenceId.Value);
            }

            // 模板ID过滤
            if (query.TemplateId.HasValue)
            {
                queryable = queryable.Where(x => x.TemplateId == query.TemplateId.Value);
            }

            // 步骤ID过滤
            if (query.StepId.HasValue)
            {
                queryable = queryable.Where(x => x.StepId == query.StepId.Value);
            }

            // 执行类型过滤
            if (!string.IsNullOrEmpty(query.ExecutionType))
            {
                queryable = queryable.Where(x => x.ExecutionType == query.ExecutionType);
            }

            // 状态过滤
            if (!string.IsNullOrEmpty(query.Status))
            {
                queryable = queryable.Where(x => x.Status == query.Status);
            }

            // 执行者过滤
            if (!string.IsNullOrEmpty(query.ExecutedBy))
            {
                queryable = queryable.Where(x => x.ExecutedBy == query.ExecutedBy);
            }

            // 开始时间范围过滤
            if (query.StartTimeStart.HasValue)
            {
                queryable = queryable.Where(x => x.StartTime >= query.StartTimeStart.Value);
            }
            if (query.StartTimeEnd.HasValue)
            {
                queryable = queryable.Where(x => x.StartTime <= query.StartTimeEnd.Value);
            }

            // 排序
            var sortBy = query.SortBy ?? "StartTime";
            var sortDirection = query.SortDirection ?? "desc";

            queryable = sortDirection.ToLower() == "asc"
                ? queryable.OrderBy(sortBy)
                : queryable.OrderBy($"{sortBy} desc");

            // 分页
            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .ToPageListAsync(query.Page, query.PageSize);

            return new PagedResultDto<UIAutoMationTemplateExecutionLog>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 根据序列ID获取执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetBySequenceIdAsync(int sequenceId, int count = 50)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.SequenceId == sequenceId)
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 根据模板ID获取执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetByTemplateIdAsync(int templateId, int count = 50)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.TemplateId == templateId)
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 根据步骤ID获取执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetByStepIdAsync(int stepId, int count = 50)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.StepId == stepId)
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 获取最近的执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetRecentAsync(int count = 20)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 根据执行者获取执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetByExecutorAsync(string executedBy, int count = 50)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.ExecutedBy == executedBy)
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 根据状态获取执行日志
        /// </summary>
        public async Task<List<UIAutoMationTemplateExecutionLog>> GetByStatusAsync(string status, int count = 50)
        {
            return await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.Status == status)
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 获取执行统计信息
        /// </summary>
        public async Task<ExecutionStatisticsDto> GetStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null)
        {
            var queryable = _db.Queryable<UIAutoMationTemplateExecutionLog>();

            if (startTime.HasValue)
            {
                queryable = queryable.Where(x => x.StartTime >= startTime.Value);
            }
            if (endTime.HasValue)
            {
                queryable = queryable.Where(x => x.StartTime <= endTime.Value);
            }

            var totalExecutions = await queryable.CountAsync();
            var successfulExecutions = await queryable.Where(x => x.Status == ExecutionStatuses.Completed).CountAsync();
            var failedExecutions = await queryable.Where(x => x.Status == ExecutionStatuses.Failed).CountAsync();

            var successRate = totalExecutions > 0 ? (decimal)successfulExecutions / totalExecutions : 0;

            // 计算平均执行时间
            var avgDuration = await queryable
                .Where(x => x.Duration.HasValue)
                .AvgAsync(x => x.Duration.Value);

            // 按状态分组统计
            var statusStats = await queryable
                .GroupBy(x => x.Status)
                .Select(x => new { Status = x.Status, Count = SqlFunc.AggregateCount(x.Id) })
                .ToListAsync();

            // 按执行类型分组统计
            var typeStats = await queryable
                .GroupBy(x => x.ExecutionType)
                .Select(x => new { Type = x.ExecutionType, Count = SqlFunc.AggregateCount(x.Id) })
                .ToListAsync();

            return new ExecutionStatisticsDto
            {
                TotalExecutions = totalExecutions,
                SuccessfulExecutions = successfulExecutions,
                FailedExecutions = failedExecutions,
                SuccessRate = successRate,
                AverageExecutionTime = avgDuration,
                StatusStats = statusStats.ToDictionary(x => x.Status, x => x.Count),
                TypeStats = typeStats.ToDictionary(x => x.Type, x => x.Count)
            };
        }

        /// <summary>
        /// 更新执行状态
        /// </summary>
        public async Task<bool> UpdateStatusAsync(int id, string status, DateTime? endTime = null,
            string? result = null, string? errorMessage = null)
        {
            var updateable = _db.Updateable<UIAutoMationTemplateExecutionLog>()
                .SetColumns(x => x.Status == status);

            if (endTime.HasValue)
            {
                updateable = updateable.SetColumns(x => x.EndTime == endTime.Value);

                // 计算执行时长
                var log = await GetByIdAsync(id);
                if (log != null)
                {
                    var duration = (int)(endTime.Value - log.StartTime).TotalMilliseconds;
                    updateable = updateable.SetColumns(x => x.Duration == duration);
                }
            }

            if (!string.IsNullOrEmpty(result))
            {
                updateable = updateable.SetColumns(x => x.Result == result);
            }

            if (!string.IsNullOrEmpty(errorMessage))
            {
                updateable = updateable.SetColumns(x => x.ErrorMessage == errorMessage);
            }

            return await updateable.Where(x => x.Id == id).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 记录执行开始
        /// </summary>
        public async Task<int> LogExecutionStartAsync(string executionType, int? sequenceId = null,
            int? templateId = null, int? stepId = null, string? executedBy = null)
        {
            var log = new UIAutoMationTemplateExecutionLog
            {
                SequenceId = sequenceId,
                TemplateId = templateId,
                StepId = stepId,
                ExecutionType = executionType,
                Status = ExecutionStatuses.Started,
                StartTime = DateTime.Now,
                ExecutedBy = executedBy,
                CreatedTime = DateTime.Now
            };

            var result = await _db.Insertable(log).ExecuteReturnEntityAsync();
            return result.Id;
        }

        /// <summary>
        /// 记录执行完成
        /// </summary>
        public async Task<bool> LogExecutionCompletedAsync(int id, string? result = null, string? screenshotPath = null)
        {
            var endTime = DateTime.Now;
            var updateable = _db.Updateable<UIAutoMationTemplateExecutionLog>()
                .SetColumns(x => x.Status == ExecutionStatuses.Completed)
                .SetColumns(x => x.EndTime == endTime);

            if (!string.IsNullOrEmpty(result))
            {
                updateable = updateable.SetColumns(x => x.Result == result);
            }

            if (!string.IsNullOrEmpty(screenshotPath))
            {
                updateable = updateable.SetColumns(x => x.ScreenshotPath == screenshotPath);
            }

            // 计算执行时长
            var log = await GetByIdAsync(id);
            if (log != null)
            {
                var duration = (int)(endTime - log.StartTime).TotalMilliseconds;
                updateable = updateable.SetColumns(x => x.Duration == duration);
            }

            return await updateable.Where(x => x.Id == id).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 记录执行失败
        /// </summary>
        public async Task<bool> LogExecutionFailedAsync(int id, string errorMessage, string? screenshotPath = null)
        {
            var endTime = DateTime.Now;
            var updateable = _db.Updateable<UIAutoMationTemplateExecutionLog>()
                .SetColumns(x => x.Status == ExecutionStatuses.Failed)
                .SetColumns(x => x.EndTime == endTime)
                .SetColumns(x => x.ErrorMessage == errorMessage);

            if (!string.IsNullOrEmpty(screenshotPath))
            {
                updateable = updateable.SetColumns(x => x.ScreenshotPath == screenshotPath);
            }

            // 计算执行时长
            var log = await GetByIdAsync(id);
            if (log != null)
            {
                var duration = (int)(endTime - log.StartTime).TotalMilliseconds;
                updateable = updateable.SetColumns(x => x.Duration == duration);
            }

            return await updateable.Where(x => x.Id == id).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 清理旧日志
        /// </summary>
        public async Task<int> CleanupOldLogsAsync(int daysToKeep = 30)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);

            return await _db.Deleteable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.CreatedTime < cutoffDate)
                .ExecuteCommandAsync();
        }
    }
}
