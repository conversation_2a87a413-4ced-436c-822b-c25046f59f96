using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 系统参数仓储接口
    /// </summary>
    public interface ISystemParameterRepository : IRepository<SystemParameter>
    {
        /// <summary>
        /// 根据分类获取参数列表
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="includeInactive">是否包含未启用的参数</param>
        /// <returns>参数列表</returns>
        Task<List<SystemParameter>> GetByCategoryAsync(string category, bool includeInactive = false);

        /// <summary>
        /// 根据分类和键名获取参数
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="key">参数键名</param>
        /// <returns>参数实体</returns>
        Task<SystemParameter?> GetByKeyAsync(string category, string key);

        /// <summary>
        /// 根据分类和值获取参数
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="value">参数值</param>
        /// <returns>参数实体</returns>
        Task<SystemParameter?> GetByValueAsync(string category, string value);

        /// <summary>
        /// 获取所有分类
        /// </summary>
        /// <returns>分类列表</returns>
        Task<List<string>> GetCategoriesAsync();

        /// <summary>
        /// 批量更新排序
        /// </summary>
        /// <param name="parameters">参数列表（包含新的排序）</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateSortOrderAsync(List<SystemParameter> parameters);

        /// <summary>
        /// 检查键名是否存在
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="key">参数键名</param>
        /// <param name="excludeId">排除的ID（用于更新时检查）</param>
        /// <returns>是否存在</returns>
        Task<bool> KeyExistsAsync(string category, string key, int? excludeId = null);

        /// <summary>
        /// 获取分类统计
        /// </summary>
        /// <returns>分类统计信息</returns>
        Task<Dictionary<string, int>> GetCategoryStatisticsAsync();

        /// <summary>
        /// 根据分类获取键值对字典
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="includeInactive">是否包含未启用的参数</param>
        /// <returns>键值对字典</returns>
        Task<Dictionary<string, string>> GetKeyValueDictionaryAsync(string category, bool includeInactive = false);

        /// <summary>
        /// 根据分类获取显示名称字典
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="includeInactive">是否包含未启用的参数</param>
        /// <returns>值-显示名称字典</returns>
        Task<Dictionary<string, string>> GetDisplayNameDictionaryAsync(string category, bool includeInactive = false);
    }
}
