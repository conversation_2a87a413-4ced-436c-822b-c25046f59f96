#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域图像条件执行器
实现"在图片A区域内检测图片B是否存在"的精准条件逻辑判断功能
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple
from pathlib import Path


class RegionImageConditionExecutor:
    """区域图像条件执行器类"""
    
    def __init__(self, automation_manager):
        """
        初始化区域图像条件执行器
        
        Args:
            automation_manager: 自动化管理器实例
        """
        self.automation_manager = automation_manager
        self.ui_actions = automation_manager.ui_actions
        self.screenshot_manager = automation_manager.screenshot_manager
        self.image_manager = automation_manager.image_manager
        
        print("区域图像条件执行器初始化完成")
    
    def execute_region_image_condition(self, condition_config: Dict) -> Dict:
        """
        执行区域图像条件判断
        
        Args:
            condition_config: 条件配置字典，包含以下字段：
                - region_template_id: 区域图像模板ID
                - region_template_name: 区域图像模板名称
                - target_template_id: 目标图像模板ID (在区域内检测的图片)
                - target_template_name: 目标图像模板名称
                - action_template_id: 动作图像模板ID (要操作的图片)
                - action_template_name: 动作图像模板名称
                - action_type: 动作类型 (click, wait, verify)
                - region_confidence: 区域图像置信度 (默认0.7)
                - target_confidence: 目标图像置信度 (默认0.7)
                - action_confidence: 动作图像置信度 (默认0.7)
                - reverse_condition: 是否反向条件判断 (默认False)
                - region_expand: 区域扩展像素 (默认10)
                - timeout: 超时时间 (默认5秒)
        
        Returns:
            执行结果字典
        """
        try:
            print(f"🔍 开始执行区域图像条件判断")
            print(f"   配置: {condition_config}")
            
            # 1. 找到区域图像位置
            region_result = self._find_region_image(condition_config)
            if not region_result['found']:
                return {
                    'success': False,
                    'error': region_result.get('error', '找不到区域图像'),
                    'message': '区域图像定位失败'
                }
            
            # 2. 在区域内检测目标图像
            target_result = self._detect_target_in_region(condition_config, region_result)
            
            # 3. 根据条件判断结果执行动作
            return self._execute_action_based_on_condition(condition_config, target_result)
            
        except Exception as e:
            print(f"❌ 区域图像条件判断执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'执行失败: {str(e)}'
            }
    
    def _find_region_image(self, config: Dict) -> Dict:
        """找到区域图像位置"""
        try:
            # 获取区域图像模板
            region_template = self._get_template_by_config(
                config.get('region_template_id'),
                config.get('region_template_name')
            )
            
            if not region_template:
                return {
                    'found': False,
                    'error': '找不到区域图像模板'
                }
            
            # 获取区域图像文件路径
            region_image_path = self.image_manager.get_template_image_path(region_template)
            if not region_image_path:
                return {
                    'found': False,
                    'error': '无法获取区域图像文件'
                }
            
            # 在屏幕上查找区域图像
            confidence = config.get('region_confidence', 0.7)
            location = self.screenshot_manager.find_template_on_screen(
                region_image_path,
                confidence=confidence
            )
            
            if location is None:
                return {
                    'found': False,
                    'error': '屏幕上找不到区域图像'
                }
            
            # 获取区域图像尺寸
            region_image = cv2.imread(region_image_path)
            if region_image is None:
                return {
                    'found': False,
                    'error': '无法读取区域图像文件'
                }
            
            height, width = region_image.shape[:2]
            
            # 计算区域边界（可选扩展）
            expand = config.get('region_expand', 10)
            region_bounds = {
                'x': max(0, location[0] - expand),
                'y': max(0, location[1] - expand),
                'width': width + 2 * expand,
                'height': height + 2 * expand
            }
            
            print(f"✅ 找到区域图像: {region_template.get('name')}")
            print(f"   位置: ({location[0]}, {location[1]})")
            print(f"   区域边界: {region_bounds}")
            
            return {
                'found': True,
                'template': region_template,
                'location': location,
                'bounds': region_bounds,
                'image_path': region_image_path
            }
            
        except Exception as e:
            print(f"❌ 查找区域图像失败: {e}")
            return {
                'found': False,
                'error': str(e)
            }
    
    def _detect_target_in_region(self, config: Dict, region_result: Dict) -> Dict:
        """在区域内检测目标图像"""
        try:
            # 获取目标图像模板
            target_template = self._get_template_by_config(
                config.get('target_template_id'),
                config.get('target_template_name')
            )
            
            if not target_template:
                return {
                    'found': False,
                    'error': '找不到目标图像模板'
                }
            
            # 获取目标图像文件路径
            target_image_path = self.image_manager.get_template_image_path(target_template)
            if not target_image_path:
                return {
                    'found': False,
                    'error': '无法获取目标图像文件'
                }
            
            # 截取当前屏幕
            screenshot_path = "./logs/screenshots/region_detection.png"
            Path(screenshot_path).parent.mkdir(parents=True, exist_ok=True)
            
            if not self.screenshot_manager.take_screenshot(screenshot_path):
                return {
                    'found': False,
                    'error': '截图失败'
                }
            
            # 使用OpenCV进行区域内图像检测
            result = self._opencv_region_detection(
                screenshot_path,
                target_image_path,
                region_result['bounds'],
                config.get('target_confidence', 0.7)
            )
            
            if result['found']:
                print(f"✅ 在区域内找到目标图像: {target_template.get('name')}")
                print(f"   相对位置: ({result['relative_location'][0]}, {result['relative_location'][1]})")
                print(f"   绝对位置: ({result['absolute_location'][0]}, {result['absolute_location'][1]})")
            else:
                print(f"❌ 在区域内未找到目标图像: {target_template.get('name')}")
            
            result['template'] = target_template
            return result
            
        except Exception as e:
            print(f"❌ 区域内目标检测失败: {e}")
            return {
                'found': False,
                'error': str(e)
            }
    
    def _opencv_region_detection(self, screenshot_path: str, target_path: str, 
                                region_bounds: Dict, confidence: float) -> Dict:
        """使用OpenCV在指定区域内检测目标图像"""
        try:
            # 读取截图和目标图像
            screenshot = cv2.imread(screenshot_path)
            target_image = cv2.imread(target_path)
            
            if screenshot is None or target_image is None:
                return {
                    'found': False,
                    'error': '无法读取图像文件'
                }
            
            # 提取区域
            x, y, w, h = region_bounds['x'], region_bounds['y'], region_bounds['width'], region_bounds['height']
            screen_height, screen_width = screenshot.shape[:2]
            
            # 确保区域边界在屏幕范围内
            x = max(0, min(x, screen_width - 1))
            y = max(0, min(y, screen_height - 1))
            w = min(w, screen_width - x)
            h = min(h, screen_height - y)
            
            region_image = screenshot[y:y+h, x:x+w]
            
            # 保存区域图像用于调试
            region_debug_path = "./logs/screenshots/region_extracted.png"
            cv2.imwrite(region_debug_path, region_image)
            print(f"🔍 区域图像已保存: {region_debug_path}")
            
            # 模板匹配
            result = cv2.matchTemplate(region_image, target_image, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            print(f"🔍 模板匹配结果: 最大置信度 = {max_val:.3f}, 阈值 = {confidence}")
            
            if max_val >= confidence:
                # 计算相对于区域的位置
                relative_location = max_loc
                
                # 计算绝对屏幕位置
                absolute_location = (x + max_loc[0], y + max_loc[1])
                
                return {
                    'found': True,
                    'confidence': max_val,
                    'relative_location': relative_location,
                    'absolute_location': absolute_location,
                    'region_bounds': region_bounds
                }
            else:
                return {
                    'found': False,
                    'confidence': max_val,
                    'threshold': confidence
                }
                
        except Exception as e:
            print(f"❌ OpenCV区域检测失败: {e}")
            return {
                'found': False,
                'error': str(e)
            }
    
    def _execute_action_based_on_condition(self, config: Dict, target_result: Dict) -> Dict:
        """根据条件判断结果执行动作"""
        try:
            # 判断条件是否满足
            reverse_condition = config.get('reverse_condition', False)
            target_found = target_result['found']
            
            if reverse_condition:
                condition_satisfied = not target_found
                print(f"🔄 反向条件: 目标图像{'不存在' if condition_satisfied else '存在'}在区域内")
            else:
                condition_satisfied = target_found
                print(f"✅ 正向条件: 目标图像{'存在' if condition_satisfied else '不存在'}在区域内")
            
            if condition_satisfied:
                print(f"✅ 条件满足，执行动作")
                # 执行动作
                action_result = self._execute_action(config)
                
                return {
                    'success': action_result['success'],
                    'condition_satisfied': True,
                    'target_found_in_region': target_found,
                    'reverse_condition': reverse_condition,
                    'action_executed': True,
                    'action_result': action_result,
                    'target_detection': target_result,
                    'message': f"条件满足，动作{'成功' if action_result['success'] else '失败'}"
                }
            else:
                print(f"❌ 条件不满足，跳过动作")
                return {
                    'success': True,  # 条件不满足也算成功执行
                    'condition_satisfied': False,
                    'target_found_in_region': target_found,
                    'reverse_condition': reverse_condition,
                    'action_executed': False,
                    'target_detection': target_result,
                    'message': '条件不满足，跳过动作'
                }
                
        except Exception as e:
            print(f"❌ 执行动作失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'执行动作失败: {str(e)}'
            }
    
    def _execute_action(self, config: Dict) -> Dict:
        """执行指定的动作"""
        try:
            # 获取动作图像模板
            action_template = self._get_template_by_config(
                config.get('action_template_id'),
                config.get('action_template_name')
            )
            
            if not action_template:
                return {
                    'success': False,
                    'error': '找不到动作图像模板'
                }
            
            # 获取动作图像文件路径
            action_image_path = self.image_manager.get_template_image_path(action_template)
            if not action_image_path:
                return {
                    'success': False,
                    'error': '无法获取动作图像文件'
                }
            
            # 执行动作
            action_type = config.get('action_type', 'click').lower()
            confidence = config.get('action_confidence', 0.7)
            timeout = config.get('timeout', 5)
            
            return self._execute_action_on_template(
                action_type, action_image_path, action_template, confidence, timeout
            )
            
        except Exception as e:
            print(f"❌ 执行动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_action_on_template(self, action: str, image_path: str, template: Dict, 
                                   confidence: float, timeout: int) -> Dict:
        """在模板上执行指定动作"""
        try:
            template_name = template.get('name', '未知模板')
            print(f"🎯 在模板 '{template_name}' 上执行动作: {action}")
            
            success = False
            result_data = {}
            
            if action == 'click':
                success = self.ui_actions.click_template(
                    image_path,
                    confidence=confidence,
                    timeout=timeout
                )
                result_data['action'] = 'click'
                
            elif action == 'wait':
                location = self.screenshot_manager.wait_for_template(
                    image_path,
                    timeout=timeout,
                    confidence=confidence
                )
                success = location is not None
                result_data['action'] = 'wait'
                result_data['location'] = location
                
            elif action == 'verify':
                location = self.screenshot_manager.find_template_on_screen(
                    image_path,
                    confidence=confidence
                )
                success = location is not None
                result_data['action'] = 'verify'
                result_data['location'] = location
                
            else:
                return {
                    'success': False,
                    'error': f'不支持的动作类型: {action}'
                }
            
            if success:
                print(f"✅ 动作执行成功: {action} on {template_name}")
            else:
                print(f"❌ 动作执行失败: {action} on {template_name}")
            
            return {
                'success': success,
                'template': template,
                'confidence': confidence,
                **result_data
            }
            
        except Exception as e:
            print(f"❌ 执行动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_template_by_config(self, template_id: Optional[int], 
                               template_name: Optional[str]) -> Optional[Dict]:
        """根据配置获取模板"""
        try:
            if template_id:
                return self.automation_manager.get_template(template_id)
            elif template_name:
                templates = self.automation_manager.get_templates()
                return next((t for t in templates if t.get('name') == template_name), None)
            else:
                return None
        except Exception as e:
            print(f"❌ 获取模板失败: {e}")
            return None
    
    def create_region_condition(self, region_image_name: str, target_image_name: str, 
                               action_image_name: str, action_type: str = 'click',
                               reverse: bool = False) -> Dict:
        """
        创建区域图像条件配置
        
        Args:
            region_image_name: 区域图像模板名称
            target_image_name: 目标图像模板名称（在区域内检测）
            action_image_name: 动作图像模板名称（要操作的图像）
            action_type: 动作类型 (click, wait, verify)
            reverse: 是否为反向条件
        
        Returns:
            区域条件配置字典
        """
        return {
            'region_template_name': region_image_name,
            'target_template_name': target_image_name,
            'action_template_name': action_image_name,
            'action_type': action_type,
            'reverse_condition': reverse,
            'region_confidence': 0.7,
            'target_confidence': 0.7,
            'action_confidence': 0.7,
            'region_expand': 10,
            'timeout': 5
        }
