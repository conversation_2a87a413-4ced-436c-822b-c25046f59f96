-- 删除PromptTemplates表的TaskType约束
-- 用于允许添加任意TaskType值，不受约束限制

USE [ProjectManagementAI]
GO

PRINT '开始删除PromptTemplates表的TaskType约束...'

-- 检查并删除PromptTemplates表的TaskType约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE [dbo].[PromptTemplates] DROP CONSTRAINT [CK_PromptTemplates_TaskType]
    PRINT '✓ 已删除PromptTemplates表的TaskType约束'
END
ELSE
BEGIN
    PRINT '⚠ PromptTemplates表的TaskType约束不存在'
END

-- 验证约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    PRINT '✓ 确认TaskType约束已成功删除'
END
ELSE
BEGIN
    PRINT '✗ TaskType约束删除失败'
END

-- 显示当前PromptTemplates表的所有约束
PRINT ''
PRINT '当前PromptTemplates表的约束信息:'
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition,
    cc.is_disabled AS IsDisabled
FROM sys.check_constraints cc
WHERE cc.parent_object_id = OBJECT_ID('PromptTemplates')
ORDER BY cc.name;

PRINT ''
PRINT '约束删除完成！现在可以添加任意TaskType值的提示词模板了。'
GO
