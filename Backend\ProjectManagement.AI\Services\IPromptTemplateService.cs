using ProjectManagement.Core.Entities;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt模板服务接口
/// </summary>
public interface IPromptTemplateService
{
    /// <summary>
    /// 获取所有模板
    /// </summary>
    Task<List<PromptTemplate>> GetAllTemplatesAsync();

    /// <summary>
    /// 根据ID获取模板
    /// </summary>
    Task<PromptTemplate?> GetTemplateByIdAsync(int id);

    /// <summary>
    /// 根据分类获取模板
    /// </summary>
    Task<List<PromptTemplate>> GetTemplatesByCategoryAsync(int categoryId);

    /// <summary>
    /// 根据任务类型获取模板
    /// </summary>
    Task<List<PromptTemplate>> GetTemplatesByTaskTypeAsync(string taskType);

    /// <summary>
    /// 获取默认模板
    /// </summary>
    Task<PromptTemplate?> GetDefaultTemplateAsync(string taskType);

    /// <summary>
    /// 搜索模板
    /// </summary>
    Task<List<PromptTemplate>> SearchTemplatesAsync(string keyword, int? categoryId = null, string? taskType = null);

    /// <summary>
    /// 获取热门模板
    /// </summary>
    Task<List<PromptTemplate>> GetPopularTemplatesAsync(int count = 10);

    /// <summary>
    /// 获取用户收藏的模板
    /// </summary>
    Task<List<PromptTemplate>> GetUserFavoriteTemplatesAsync(int userId);

    /// <summary>
    /// 获取用户最近使用的模板
    /// </summary>
    Task<List<PromptTemplate>> GetUserRecentTemplatesAsync(int userId, int count = 10);

    /// <summary>
    /// 创建模板
    /// </summary>
    Task<PromptTemplate> CreateTemplateAsync(PromptTemplate template);

    /// <summary>
    /// 更新模板
    /// </summary>
    Task<PromptTemplate> UpdateTemplateAsync(PromptTemplate template);

    /// <summary>
    /// 删除模板
    /// </summary>
    Task<bool> DeleteTemplateAsync(int id);

    /// <summary>
    /// 复制模板
    /// </summary>
    Task<PromptTemplate> CloneTemplateAsync(int templateId, string newName, int userId);

    /// <summary>
    /// 设置默认模板
    /// </summary>
    Task<bool> SetDefaultTemplateAsync(int templateId, string taskType);

    /// <summary>
    /// 启用/禁用模板
    /// </summary>
    Task<bool> ToggleTemplateStatusAsync(int templateId, bool isEnabled);

    /// <summary>
    /// 记录模板使用
    /// </summary>
    Task RecordTemplateUsageAsync(int templateId, int userId, int? projectId, string aiProvider, string aiModel, 
        string inputParameters, string generatedPrompt, string? aiResponse, int? responseTimeMs, 
        int? tokenUsage, decimal? cost, bool isSuccess, string? errorMessage);

    /// <summary>
    /// 评价模板
    /// </summary>
    Task RateTemplateAsync(int templateId, int userId, int overallRating, int? accuracyRating, 
        int? usefulnessRating, int? easeOfUseRating, string? feedback, string? suggestions, 
        string? tags, bool? wouldRecommend);

    /// <summary>
    /// 获取模板统计信息
    /// </summary>
    Task<object> GetTemplateStatsAsync(int templateId);

    /// <summary>
    /// 获取用户模板偏好
    /// </summary>
    Task<List<UserPromptPreference>> GetUserPreferencesAsync(int userId);

    /// <summary>
    /// 设置用户模板偏好
    /// </summary>
    Task SetUserPreferenceAsync(int userId, int templateId, string preferenceType, 
        string? customParameters = null, int sortOrder = 0);

    /// <summary>
    /// 移除用户模板偏好
    /// </summary>
    Task RemoveUserPreferenceAsync(int userId, int templateId, string preferenceType);
}
