<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑步骤' : '添加步骤'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="动作类型" prop="actionType">
        <el-select v-model="form.actionType" placeholder="请选择动作类型" style="width: 100%">
          <el-option
            v-for="action in uiActionTypes"
            :key="action.value"
            :label="action.label"
            :value="action.value"
          >
            <div class="action-option">
              <span>{{ action.label }}</span>
              <span class="action-desc">{{ action.description }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="逻辑语句" prop="logicType">
        <el-select v-model="form.logicType" placeholder="请选择逻辑语句（可选）" style="width: 100%" clearable>
          <el-option
            v-for="logic in logicTypes"
            :key="logic.value"
            :label="logic.label"
            :value="logic.value"
          >
            <div class="action-option">
              <span>{{ logic.label }}</span>
              <span class="action-desc">{{ logic.description }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="步骤描述" prop="description">
        <el-input
          v-model="form.description"
          placeholder="请输入步骤描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 模板选择（仅对需要模板的动作类型显示） -->
      <el-form-item
        v-if="needsTemplate"
        label="选择模板"
        prop="templateId"
      >
        <el-select
          v-model="form.templateId"
          placeholder="请选择模板"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="template in templates"
            :key="template.id"
            :label="template.name"
            :value="template.id"
          >
            <div class="template-option">
              <span>{{ template.name }}</span>
              <el-tag :type="getCategoryTagType(template.category)" size="small">
                {{ template.category }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 逻辑控制字段 -->
      <el-form-item label="条件表达式" v-if="showConditionExpression">
        <el-input
          v-model="form.conditionExpression"
          placeholder="例如: {result} === 'success' || {count} > 5"
          maxlength="1000"
          show-word-limit
        />
        <div class="form-tip">
          支持JavaScript表达式，使用 {变量名} 引用变量。例如：<br>
          • {result} === 'success' - 检查结果是否成功<br>
          • {count} > 5 - 检查计数是否大于5<br>
          • {status} !== 'error' && {retry} < 3 - 复合条件
        </div>
      </el-form-item>

      <el-form-item label="跳转目标步骤" v-if="showJumpToStep">
        <el-input-number
          v-model="form.jumpToStepId"
          :min="1"
          :max="999"
          :step="1"
          controls
          controls-position="right"
          style="width: 100%"
          placeholder="请输入目标步骤序号"
        />
        <div class="form-tip">指定条件满足时要跳转到的步骤序号</div>
      </el-form-item>

      <el-form-item label="循环设置" v-if="showLoopSettings">
        <el-row :gutter="10">
          <el-col :span="12">
            <label class="sub-label">循环次数:</label>
            <el-input-number
              v-model="form.loopCount"
              :min="-1"
              :max="9999"
              :step="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="循环次数"
            />
            <div class="form-tip">-1表示无限循环</div>
          </el-col>
          <el-col :span="12">
            <label class="sub-label">循环变量:</label>
            <el-input
              v-model="form.loopVariable"
              placeholder="例如: counter, index"
              maxlength="50"
            />
            <div class="form-tip">循环中使用的变量名</div>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="分组标识" v-if="showGroupId">
        <el-input
          v-model="form.groupId"
          placeholder="例如: loop_1, condition_branch_a"
          maxlength="50"
        />
        <div class="form-tip">用于标识逻辑分组，如循环体、条件分支等</div>
      </el-form-item>

      <!-- 参数配置 -->
      <el-form-item label="参数配置">
        <div class="parameters-config">
          <!-- 通用参数 -->
          <div v-if="form.actionType === 'input'" class="param-group">
            <label>输入文本:</label>
            <el-input
              v-model="form.parameters.text"
              placeholder="请输入要输入的文本，支持变量 {variable}"
            />
          </div>

          <div v-if="form.actionType === 'delay'" class="param-group">
            <label>延迟时间(秒):</label>
            <el-input-number
              v-model="form.parameters.seconds"
              :min="0.1"
              :max="60"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="请输入延迟时间"
            />
          </div>

          <div v-if="form.actionType === 'key_press'" class="param-group">
            <label>按键:</label>
            <el-select v-model="form.parameters.key" placeholder="选择按键">
              <el-option label="Enter" value="enter" />
              <el-option label="Tab" value="tab" />
              <el-option label="Escape" value="escape" />
              <el-option label="Space" value="space" />
              <el-option label="Ctrl+C" value="ctrl+c" />
              <el-option label="Ctrl+V" value="ctrl+v" />
              <el-option label="Ctrl+A" value="ctrl+a" />
            </el-select>
          </div>

          <!-- 流程控制参数 -->
          <div v-if="form.logicType === 'condition'" class="param-group">
            <label>条件为真时的动作:</label>
            <el-select v-model="form.parameters.true_action" placeholder="选择动作">
              <el-option label="继续执行" value="continue" />
              <el-option label="跳转到步骤" value="jump_to_step" />
              <el-option label="退出序列" value="exit" />
            </el-select>
          </div>

          <div v-if="form.logicType === 'condition'" class="param-group">
            <label>条件为假时的动作:</label>
            <el-select v-model="form.parameters.false_action" placeholder="选择动作">
              <el-option label="跳过后续步骤" value="skip_to_step" />
              <el-option label="继续执行" value="continue" />
              <el-option label="退出序列" value="exit" />
            </el-select>
          </div>

          <div v-if="form.logicType === 'loop'" class="param-group">
            <label>退出条件:</label>
            <el-input
              v-model="form.parameters.exit_condition"
              placeholder="例如: {counter} >= 10"
            />
          </div>

          <!-- 图像条件参数 -->
          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label class="required-label">条件图像模板:</label>
            <el-select v-model="form.parameters.condition_template_id" placeholder="选择条件图像模板" style="width: 100%">
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag :type="getCategoryTagType(template.category)" size="small">
                    {{ template.category }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择作为条件判断的图像模板</div>
          </div>

          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label class="required-label">目标图像模板:</label>
            <el-select v-model="form.parameters.target_template_id" placeholder="选择目标图像模板" style="width: 100%">
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag :type="getCategoryTagType(template.category)" size="small">
                    {{ template.category }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择条件满足时要操作的图像模板</div>
          </div>

          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label class="required-label">目标动作:</label>
            <el-select v-model="form.parameters.target_action" placeholder="选择目标动作">
              <el-option label="点击" value="click" />
              <el-option label="等待" value="wait" />
              <el-option label="验证" value="verify" />
            </el-select>
            <div class="form-tip">条件满足时对目标图像执行的动作</div>
          </div>

          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label>条件图像置信度:</label>
            <el-input-number
              v-model="form.parameters.condition_confidence"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="条件图像匹配置信度"
            />
            <div class="form-tip">条件图像匹配的置信度阈值 (0.1-1.0)</div>
          </div>

          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label>目标图像置信度:</label>
            <el-input-number
              v-model="form.parameters.target_confidence"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="目标图像匹配置信度"
            />
            <div class="form-tip">目标图像匹配的置信度阈值 (0.1-1.0)</div>
          </div>

          <div v-if="form.logicType === 'image_condition'" class="param-group">
            <label>条件类型:</label>
            <el-radio-group v-model="form.parameters.reverse_condition">
              <el-radio :label="false">正向条件</el-radio>
              <el-radio :label="true">反向条件</el-radio>
            </el-radio-group>
            <div class="form-tip">
              正向条件: 如果图像存在，则执行动作<br>
              反向条件: 如果图像不存在，则执行动作
            </div>
          </div>

          <!-- 区域图像条件参数 -->
          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label class="required-label">区域图像模板:</label>
            <el-select v-model="form.parameters.region_template_id" placeholder="选择区域图像模板" style="width: 100%">
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag :type="getCategoryTagType(template.category)" size="small">
                    {{ template.category }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择作为检测区域的大图像模板</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label>目标图像模板:</label>
            <el-select v-model="form.parameters.target_template_id" placeholder="选择目标图像模板" style="width: 100%">
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag :type="getCategoryTagType(template.category)" size="small">
                    {{ template.category }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择在区域内检测的小图像模板</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label class="required-label">动作图像模板:</label>
            <el-select v-model="form.parameters.action_template_id" placeholder="选择动作图像模板" style="width: 100%">
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag :type="getCategoryTagType(template.category)" size="small">
                    {{ template.category }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
            <div class="form-tip">选择条件满足时要操作的图像模板</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label class="required-label">动作类型:</label>
            <el-select v-model="form.parameters.action_type" placeholder="选择动作类型">
              <el-option label="点击" value="click" />
              <el-option label="等待" value="wait" />
              <el-option label="验证" value="verify" />
            </el-select>
            <div class="form-tip">条件满足时对动作图像执行的操作</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label>条件类型:</label>
            <el-radio-group v-model="form.parameters.reverse_condition">
              <el-radio :label="false">正向条件</el-radio>
              <el-radio :label="true">反向条件</el-radio>
            </el-radio-group>
            <div class="form-tip">
              正向条件: 如果在区域内找到目标图像，则执行动作<br>
              反向条件: 如果在区域内找不到目标图像，则执行动作
            </div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label>区域图像置信度:</label>
            <el-input-number
              v-model="form.parameters.region_confidence"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="区域图像匹配置信度"
            />
            <div class="form-tip">区域图像匹配的置信度阈值 (0.1-1.0)</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label>动作图像置信度:</label>
            <el-input-number
              v-model="form.parameters.action_confidence"
              :min="0.1"
              :max="1.0"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="动作图像匹配置信度"
            />
            <div class="form-tip">动作图像匹配的置信度阈值 (0.1-1.0)</div>
          </div>

          <div v-if="form.logicType === 'region_image_condition'" class="param-group">
            <label>区域扩展像素:</label>
            <el-input-number
              v-model="form.parameters.region_expand"
              :min="0"
              :max="100"
              :step="5"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="区域扩展像素"
            />
            <div class="form-tip">扩展检测区域的像素数量 (0-100)</div>
          </div>

          <!-- 等待后延迟 -->
          <div v-if="['click', 'input'].includes(form.actionType)" class="param-group">
            <label>操作后等待(秒):</label>
            <el-input-number
              v-model="form.parameters.wait_after"
              :min="0"
              :max="10"
              :step="0.1"
              :precision="1"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="请输入等待时间"
            />
          </div>

          <!-- 自定义参数 -->
          <div class="param-group">
            <label>自定义参数 (JSON):</label>
            <el-input
              v-model="customParametersJson"
              type="textarea"
              :rows="3"
              placeholder='{"key": "value"}'
              @blur="parseCustomParameters"
            />
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="超时时间" prop="timeoutSeconds">
            <el-input-number
              v-model="form.timeoutSeconds"
              :min="1"
              :max="300"
              :step="1"
              controls
              controls-position="right"
              class="number-input"
              @focus="handleInputFocus('timeout')"
              @blur="handleInputBlur('timeout')"
              @change="handleInputChange('timeout', $event)"
            />
            <div class="form-tip">单位：秒 (当前值: {{ form.timeoutSeconds }})</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最大重试" prop="maxRetries">
            <el-input-number
              v-model="form.maxRetries"
              :min="0"
              :max="10"
              :step="1"
              controls
              controls-position="right"
              class="number-input"
              @focus="handleInputFocus('retries')"
              @blur="handleInputBlur('retries')"
              @change="handleInputChange('retries', $event)"
            />
            <div class="form-tip">单位：次 (当前值: {{ form.maxRetries }})</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="启用状态">
            <el-switch v-model="form.isActive" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import CustomTemplateService, {
  type TemplateStep,
  type CustomTemplate
} from '@/services/customTemplate'
import UIActionTypeService, { type UIActionType } from '@/services/actionType'
import FlowControlTypeService, { type FlowControlType } from '@/services/flowControlType'

// 定义通用的动作类型接口
interface ActionType {
  id: number
  value: string
  label: string
  description?: string
  icon?: string
  color?: string
  sortOrder: number
  isActive: boolean
  isBuiltIn: boolean
  needsTemplate?: boolean
  parameterSchema?: string
  createdTime: string
  updatedTime?: string
}

// Props
interface Props {
  modelValue: boolean
  step?: Partial<TemplateStep> | null
  stepIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  step: null,
  stepIndex: -1
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [step: Partial<TemplateStep>]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const templates = ref<CustomTemplate[]>([])
const customParametersJson = ref('')
const actionTypes = ref<ActionType[]>([])
const loading = ref(false)

// UI动作类型（基础操作）
const uiActionTypes = computed(() =>
  actionTypes.value.filter(at => ['click', 'wait', 'input', 'delay', 'screenshot', 'verify', 'scroll', 'key_press'].includes(at.value))
)

// 逻辑语句类型（条件判断、循环等）
const logicTypes = computed(() =>
  actionTypes.value.filter(at => ['condition', 'image_condition', 'region_image_condition', 'loop', 'loop_end', 'branch', 'jump', 'exit'].includes(at.value))
)

// 表单数据
const form = reactive({
  id: undefined as number | undefined, // 添加id字段
  actionType: 'click' as TemplateStep['actionType'],
  logicType: '' as string, // 逻辑语句类型（可选）
  description: '',
  templateId: undefined as number | undefined,
  parameters: {} as Record<string, any>,
  timeoutSeconds: 5,
  maxRetries: 3,
  isActive: true,
  // 逻辑控制字段
  conditionExpression: '',
  jumpToStepId: undefined as number | undefined,
  loopCount: undefined as number | undefined,
  loopVariable: '',
  groupId: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => props.stepIndex >= 0)

const needsTemplate = computed(() => {
  // 如果选择了图像条件逻辑语句，则不显示通用模板选择框
  if (['image_condition', 'region_image_condition'].includes(form.logicType)) {
    return false
  }

  // 检查动作类型是否需要模板
  const actionType = actionTypes.value.find(at => at.value === form.actionType)
  const actionNeedsTemplate = actionType?.needsTemplate ||
    ['click', 'wait', 'input', 'verify'].includes(form.actionType)

  return actionNeedsTemplate
})

// 逻辑控制字段显示条件
const showConditionExpression = computed(() => {
  // 当选择了逻辑语句或者总是可以设置条件表达式
  return form.logicType || true
})

const showJumpToStep = computed(() => {
  // 当设置了条件表达式或选择了跳转类型的逻辑语句时
  return (form.conditionExpression && form.conditionExpression.trim().length > 0) ||
         ['condition', 'branch', 'jump'].includes(form.logicType)
})

const showLoopSettings = computed(() => {
  // 当选择了循环相关的逻辑语句时
  return ['loop', 'loop_end'].includes(form.logicType)
})

const showGroupId = computed(() => {
  // 当选择了需要分组的逻辑语句时
  return ['condition', 'branch', 'loop', 'loop_end'].includes(form.logicType) ||
         (form.conditionExpression && form.conditionExpression.trim().length > 0)
})

// 显示图像条件参数
const showImageConditionParams = computed(() => {
  return form.logicType === 'image_condition'
})

const showRegionImageConditionParams = computed(() => {
  return form.logicType === 'region_image_condition'
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  actionType: [
    { required: true, message: '请选择动作类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入步骤描述', trigger: 'blur' }
  ],
  templateId: [
    {
      required: needsTemplate.value,
      message: '请选择模板',
      trigger: 'change'
    }
  ],
  // 图像条件必选参数
  'parameters.condition_template_id': [
    {
      required: form.logicType === 'image_condition',
      message: '请选择条件图像模板',
      trigger: 'change'
    }
  ],
  'parameters.target_template_id': [
    {
      required: form.logicType === 'image_condition',
      message: '请选择目标图像模板',
      trigger: 'change'
    }
  ],
  'parameters.target_action': [
    {
      required: form.logicType === 'image_condition',
      message: '请选择目标动作',
      trigger: 'change'
    }
  ],
  // 区域图像条件必选参数
  'parameters.region_template_id': [
    {
      required: form.logicType === 'region_image_condition',
      message: '请选择区域图像模板',
      trigger: 'change'
    }
  ],
  'parameters.action_template_id': [
    {
      required: form.logicType === 'region_image_condition',
      message: '请选择动作图像模板',
      trigger: 'change'
    }
  ],
  'parameters.action_type': [
    {
      required: form.logicType === 'region_image_condition',
      message: '请选择动作类型',
      trigger: 'change'
    }
  ],
  timeoutSeconds: [
    { required: true, message: '请设置超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '超时时间必须在 1 到 300 秒之间', trigger: 'blur' }
  ],
  maxRetries: [
    { required: true, message: '请设置最大重试次数', trigger: 'blur' },
    { type: 'number', min: 0, max: 10, message: '重试次数必须在 0 到 10 次之间', trigger: 'blur' }
  ]
}))

// 方法定义
const resetForm = () => {
  Object.assign(form, {
    id: undefined, // 重置id字段
    actionType: 'click',
    logicType: '', // 重置逻辑语句类型
    description: '',
    templateId: undefined,
    parameters: {
      // 图像条件默认参数
      condition_template_id: undefined,
      target_template_id: undefined,
      target_action: 'click',
      condition_confidence: 0.7,
      target_confidence: 0.7,
      reverse_condition: false,
      // 区域图像条件默认参数
      region_template_id: undefined,
      action_template_id: undefined,
      action_type: 'click',
      region_confidence: 0.7,
      action_confidence: 0.7,
      region_expand: 10
    },
    timeoutSeconds: 5,
    maxRetries: 3,
    isActive: true,
    // 重置流程控制字段
    conditionExpression: '',
    jumpToStepId: undefined,
    loopCount: undefined,
    loopVariable: '',
    groupId: ''
  })
  customParametersJson.value = '{}'
}

// 监听器
watch(() => props.step, (newStep) => {
  if (newStep) {
    const actionType = newStep.actionType || 'click'
    const logicType = (newStep as any).logicType || ''

    Object.assign(form, {
      id: (newStep as any).id, // 保留原始ID
      actionType: actionType,
      logicType: logicType, // 设置逻辑语句类型
      description: newStep.description || '',
      templateId: newStep.templateId,
      parameters: { ...(newStep.parameters || {}) },
      timeoutSeconds: newStep.timeoutSeconds || 5,
      maxRetries: newStep.maxRetries || 3,
      isActive: newStep.isActive !== false,
      // 流程控制字段
      conditionExpression: (newStep as any).conditionExpression || '',
      jumpToStepId: (newStep as any).jumpToStepId,
      loopCount: (newStep as any).loopCount,
      loopVariable: (newStep as any).loopVariable || '',
      groupId: (newStep as any).groupId || ''
    })
    customParametersJson.value = JSON.stringify(newStep.parameters || {}, null, 2)
  } else {
    resetForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadTemplates()
  loadActionTypes()
})

// 方法
const loadTemplates = async () => {
  try {
    const result = await CustomTemplateService.getTemplates({ page: 1, pageSize: 1000 })
    templates.value = result.items
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

const loadActionTypes = async () => {
  try {
    loading.value = true

    // 并行加载UI操作类型和流程控制类型
    const [uiActionTypes, flowControlTypes] = await Promise.all([
      UIActionTypeService.getActiveUIActionTypes(),
      loadFlowControlTypes()
    ])

    // 合并两种类型，转换为统一格式
    const allActionTypes: ActionType[] = [
      ...uiActionTypes.map(mapUIActionType),
      ...flowControlTypes
    ]

    actionTypes.value = allActionTypes
  } catch (error) {
    console.error('加载动作类型失败:', error)
    ElMessage.error('加载动作类型失败')
  } finally {
    loading.value = false
  }
}

// 加载流程控制类型（如果API不存在则使用硬编码数据）
const loadFlowControlTypes = async (): Promise<ActionType[]> => {
  try {
    const flowControlTypes = await FlowControlTypeService.getActiveFlowControlTypes()
    return flowControlTypes.map(mapFlowControlType)
  } catch (error) {
    console.warn('流程控制类型API不可用，使用硬编码数据:', error)
    // 如果API不存在，返回硬编码的流程控制类型
    return getHardcodedFlowControlTypes()
  }
}

// 映射UI操作类型到通用格式
const mapUIActionType = (uiType: UIActionType): ActionType => ({
  id: uiType.id,
  value: uiType.value,
  label: uiType.label,
  description: uiType.description,
  icon: uiType.icon,
  color: uiType.color,
  sortOrder: uiType.sortOrder,
  isActive: uiType.isActive,
  isBuiltIn: uiType.isBuiltIn,
  needsTemplate: uiType.needsTemplate,
  parameterSchema: uiType.parameterSchema,
  createdTime: uiType.createdTime,
  updatedTime: uiType.updatedTime
})

// 映射流程控制类型到通用格式
const mapFlowControlType = (flowType: FlowControlType): ActionType => ({
  id: flowType.id + 1000, // 避免ID冲突
  value: flowType.value,
  label: flowType.label,
  description: flowType.description,
  icon: flowType.icon,
  color: flowType.color,
  sortOrder: flowType.sortOrder + 100, // 排在UI操作类型后面
  isActive: flowType.isActive,
  isBuiltIn: flowType.isBuiltIn,
  needsTemplate: false, // 流程控制类型通常不需要模板
  parameterSchema: flowType.parameterSchema,
  createdTime: flowType.createdTime,
  updatedTime: flowType.updatedTime
})

// 硬编码的流程控制类型（备用）
const getHardcodedFlowControlTypes = (): ActionType[] => [
  {
    id: 1001,
    value: 'condition',
    label: '条件判断',
    description: '根据条件决定是否执行后续步骤',
    icon: 'el-icon-question',
    color: 'warning',
    sortOrder: 101,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  },
  {
    id: 1002,
    value: 'image_condition',
    label: '图像条件',
    description: '如果图片A存在，则点击图片B',
    icon: 'el-icon-picture',
    color: 'warning',
    sortOrder: 102,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: true,
    createdTime: new Date().toISOString()
  },
  {
    id: 1003,
    value: 'region_image_condition',
    label: '区域图像条件',
    description: '在指定区域内检测图像并执行操作',
    icon: 'el-icon-crop',
    color: 'warning',
    sortOrder: 103,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: true,
    createdTime: new Date().toISOString()
  },
  {
    id: 1004,
    value: 'loop',
    label: '循环开始',
    description: '开始循环执行',
    icon: 'el-icon-refresh',
    color: 'primary',
    sortOrder: 104,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  },
  {
    id: 1005,
    value: 'loop_end',
    label: '循环结束',
    description: '结束循环执行',
    icon: 'el-icon-refresh-right',
    color: 'primary',
    sortOrder: 105,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  },
  {
    id: 1006,
    value: 'branch',
    label: '分支执行',
    description: '根据条件选择执行分支',
    icon: 'el-icon-share',
    color: 'info',
    sortOrder: 106,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  },
  {
    id: 1007,
    value: 'jump',
    label: '跳转',
    description: '跳转到指定步骤',
    icon: 'el-icon-right',
    color: 'success',
    sortOrder: 107,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  },
  {
    id: 1008,
    value: 'exit',
    label: '退出',
    description: '退出序列执行',
    icon: 'el-icon-close',
    color: 'danger',
    sortOrder: 108,
    isActive: true,
    isBuiltIn: true,
    needsTemplate: false,
    createdTime: new Date().toISOString()
  }
]

const parseCustomParameters = () => {
  try {
    if (customParametersJson.value.trim()) {
      const parsed = JSON.parse(customParametersJson.value)
      Object.assign(form.parameters, parsed)
    }
  } catch (error) {
    ElMessage.warning('自定义参数格式不正确，请检查JSON格式')
  }
}

const getCategoryTagType = (category: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '按钮': 'primary',
    '菜单': 'success',
    '对话框': 'warning',
    '输入框': 'info',
    '图标': 'danger',
    'CopilotChat自动化': 'primary',
    '其他': 'info'
  }
  return typeMap[category] || 'info'
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 合并自定义参数
    parseCustomParameters()

    // 准备提交数据
    const submitData = { ...form }

    // 如果选择了逻辑语句，需要决定如何保存
    // 方案1：保持新格式（推荐）- 同时保存actionType和logicType
    // 方案2：兼容旧格式 - 如果有logicType，则将其作为actionType保存

    // 这里使用方案1：保持新格式
    emit('success', submitData)
    handleClose()
  } catch (error) {
    console.error('保存步骤失败:', error)
    ElMessage.error('保存步骤失败')
  }
}

const handleInputFocus = (type: string) => {
  console.log(`Input ${type} focused`)
}

const handleInputBlur = (type: string) => {
  console.log(`Input ${type} blurred`)
}

const handleInputChange = (type: string, value: any) => {
  console.log(`Input ${type} changed to:`, value)
}

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  resetForm()
}
</script>

<style scoped lang="scss">
.action-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .action-desc {
    font-size: 12px;
    color: #909399;
  }
}

.template-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.parameters-config {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;

  .param-group {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.sub-label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.required-label {
  position: relative;
}

.required-label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.number-input {
  width: 100%;

  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    pointer-events: auto !important;
  }

  :deep(.el-input__inner) {
    text-align: left !important;
    pointer-events: auto !important;
  }
}

.flow-control-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #f8f9fa;

  .el-form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
