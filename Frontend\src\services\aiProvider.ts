import { ApiService } from './api'

export interface AIProvider {
  name: string
  displayName: string
  description: string
  isEnabled: boolean
  isAvailable: boolean
  supportedModels: string[]
  config: Record<string, any>
  usageStats: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    totalTokens: number
    totalCost: number
    lastUpdated: string
  }
}

export interface AIProviderConfig {
  name: string
  endpoint?: string
  apiKey?: string
  deploymentName?: string
  gpt4DeploymentName?: string
  gpt35DeploymentName?: string
  embeddingDeploymentName?: string
  timeoutSeconds?: number
  maxRetries?: number
  enabled: boolean
  [key: string]: any
}

export interface CreateProviderRequest {
  name: string
  displayName: string
  description?: string
  apiEndpoint: string
  apiKey: string
  modelName: string
  timeoutSeconds?: number
  maxRetries?: number
  enabled: boolean
}

export interface AIConfiguration {
  defaultProvider: string
  defaultConfig: {
    provider: string
    model: string
    maxTokens: number
    temperature: number
    topP?: number
    frequencyPenalty?: number
    presencePenalty?: number
  }
  taskMapping: Record<string, string>
  providers: Record<string, AIProviderConfig>
}

// ==================== 用户AI配置相关接口 ====================

export interface UserAIConfiguration {
  id: number
  userId: number
  providerName: string
  modelName?: string
  modelType: string
  apiEndpoint?: string
  apiKey?: string
  modelParameters?: string
  isActive: boolean
  isDefault: boolean
  dailyTokenLimit?: number
  monthlyTokenLimit?: number
  currentDayTokens: number
  currentMonthTokens: number
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  totalTokensUsed: number
  estimatedCost: number
  lastUsedAt?: string
  createdAt: string
  updatedAt: string
}

export interface CreateUserAIConfigurationRequest {
  providerName: string
  modelName?: string
  modelType: string
  apiEndpoint?: string
  apiKey?: string
  modelParameters?: string
  isActive?: boolean
  isDefault?: boolean
  dailyTokenLimit?: number
  monthlyTokenLimit?: number
}

export interface UpdateUserAIConfigurationRequest {
  modelName?: string
  apiEndpoint?: string
  apiKey?: string
  modelParameters?: string
  isActive?: boolean
  isDefault?: boolean
  dailyTokenLimit?: number
  monthlyTokenLimit?: number
}

// ==================== 用户任务映射相关接口 ====================

export interface UserTaskMapping {
  id: number
  userId: number
  taskType: string
  taskTypeDisplayName: string
  providerName: string
  providerDisplayName: string
  modelName?: string
  isActive: boolean
  isDefault: boolean
  priority: number
  configurationParameters?: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface CreateUserTaskMappingRequest {
  taskType: string
  providerName: string
  modelName?: string
  isActive?: boolean
  isDefault?: boolean
  priority?: number
  configurationParameters?: string
  description?: string
}

export interface UpdateUserTaskMappingRequest {
  taskType?: string
  providerName?: string
  modelName?: string
  isActive?: boolean
  isDefault?: boolean
  priority?: number
  configurationParameters?: string
  description?: string
}

export interface TaskType {
  value: string
  displayName: string
}

export interface AIProviderOption {
  value: string
  displayName: string
}

export interface UserTaskMappingStats {
  totalMappings: number
  activeMappings: number
  inactiveMappings: number
  defaultMappings: number
  taskTypeStats: Array<{
    taskType: string
    count: number
    activeCount: number
    hasDefault: boolean
  }>
  providerStats: Array<{
    providerName: string
    count: number
    activeCount: number
  }>
}

// AI内容生成服务
export class AIContentGenerationService {
  /**
   * 生成AI内容
   */
  static async generateContent(prompt: string, taskType: string = 'general'): Promise<string> {
    try {
      // 获取用户的任务映射配置
      const taskMapping = await AIProviderService.getDefaultTaskMapping(taskType)

      if (!taskMapping) {
        // 如果没有任务映射配置，尝试获取默认AI配置
        const defaultConfig = await AIProviderService.getDefaultUserAIConfiguration()
        if (!defaultConfig) {
          // 尝试自动创建默认配置
          console.log('未找到AI配置，尝试创建默认配置...')
          const created = await AIProviderService.createDefaultConfigurations()

          if (!created) {
            // 如果创建失败，提供详细的错误信息和解决方案
            const errorMessage = `未找到可用的AI配置，且自动创建失败。请按以下步骤手动配置：
1. 访问 AI Chat 页面 (http://localhost:3000/ai-chat)
2. 在右侧面板选择一个AI提供商
3. 或者运行数据库脚本 Database/fix_ai_configuration_404.sql 创建默认配置
4. 确保您已正确登录系统`
            throw new Error(errorMessage)
          }

          console.log('默认配置创建成功，继续执行AI内容生成')
        } else {
          // 如果有默认配置但没有任务映射，记录警告
          console.warn(`未找到任务类型 "${taskType}" 的映射配置，将使用默认AI配置`)
        }
      }

      // 调用AI API生成内容
      const response = await ApiService.post<{ content: string }>('/api/ai/generate', {
        prompt,
        taskType,
        maxTokens: 4000,
        temperature: 0.7
      })

      return response.content
    } catch (error: any) {
      console.error('AI内容生成失败:', error)

      // 如果是配置相关的错误，直接抛出原始错误信息
      if (error.message && error.message.includes('未找到可用的AI配置')) {
        throw error
      }

      // 其他错误提供通用错误信息
      throw new Error('AI内容生成失败，请检查配置或网络连接')
    }
  }

  /**
   * 生成测试相关内容
   */
  static async generateTestContent(prompt: string): Promise<string> {
    return this.generateContent(prompt, 'test-generation')
  }

  /**
   * 生成代码分析内容
   */
  static async generateAnalysisContent(prompt: string): Promise<string> {
    return this.generateContent(prompt, 'code-analysis')
  }

  /**
   * 生成优化建议内容
   */
  static async generateOptimizationContent(prompt: string): Promise<string> {
    return this.generateContent(prompt, 'code-optimization')
  }
}

// 创建AI提供商实例
export const aiProvider = {
  generateContent: AIContentGenerationService.generateContent.bind(AIContentGenerationService),
  generateTestContent: AIContentGenerationService.generateTestContent.bind(AIContentGenerationService),
  generateAnalysisContent: AIContentGenerationService.generateAnalysisContent.bind(AIContentGenerationService),
  generateOptimizationContent: AIContentGenerationService.generateOptimizationContent.bind(AIContentGenerationService)
}

export class AIProviderService {
  /**
   * 获取所有AI提供商列表
   */
  static async getProviders(): Promise<AIProvider[]> {
    return ApiService.get<AIProvider[]>('/api/ai/providers')
  }

  /**
   * 创建新的AI提供商
   */
  static async createProvider(request: CreateProviderRequest): Promise<AIProvider> {
    return ApiService.post<AIProvider>('/api/ai/providers', request)
  }

  /**
   * 获取AI配置
   */
  static async getConfiguration(): Promise<AIConfiguration> {
    return ApiService.get<AIConfiguration>('/api/ai/configuration')
  }

  /**
   * 更新AI配置
   */
  static async updateConfiguration(config: AIConfiguration): Promise<void> {
    return ApiService.put<void>('/api/ai/configuration', config)
  }

  /**
   * 获取特定提供商配置
   */
  static async getProviderConfig(providerName: string): Promise<AIProviderConfig> {
    return ApiService.get<AIProviderConfig>(`/api/ai/providers/${providerName}/config`)
  }

  /**
   * 更新提供商配置
   */
  static async updateProviderConfig(providerName: string, config: AIProviderConfig): Promise<void> {
    return ApiService.put<void>(`/api/ai/providers/${providerName}/config`, config)
  }

  /**
   * 测试提供商连接
   */
  static async testProvider(providerName: string): Promise<{
    success: boolean
    message: string
    responseTime?: number
    error?: string
  }> {
    return ApiService.post(`/api/ai/providers/${providerName}/test`, {})
  }

  /**
   * 启用/禁用提供商
   */
  static async toggleProvider(providerName: string, enabled: boolean): Promise<void> {
    return ApiService.post(`/api/ai/providers/${providerName}/toggle`, { enabled })
  }

  /**
   * 获取提供商使用统计
   */
  static async getProviderStats(providerName: string): Promise<{
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    totalTokens: number
    totalCost: number
    lastUpdated: string
  }> {
    return ApiService.get(`/api/ai/providers/${providerName}/stats`)
  }

  /**
   * 重置提供商统计
   */
  static async resetProviderStats(providerName: string): Promise<void> {
    return ApiService.post(`/api/ai/providers/${providerName}/reset-stats`, {})
  }

  /**
   * 获取支持的模型列表
   */
  static async getSupportedModels(providerName: string): Promise<string[]> {
    return ApiService.get<string[]>(`/api/ai/providers/${providerName}/models`)
  }

  /**
   * 获取AI使用总统计
   */
  static async getTotalUsageStats(): Promise<{
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    totalTokensUsed: number
    estimatedCost: number
    lastUsed: string
    providerBreakdown: Record<string, {
      requests: number
      tokens: number
      cost: number
    }>
  }> {
    return ApiService.get('/api/ai/total-usage-statistics')
  }

  /**
   * 获取AI模型配置列表
   */
  static async getModelConfigurations(): Promise<{
    id: number
    userId: number
    modelName: string
    apiEndpoint?: string
    apiKey?: string
    modelParameters?: string
    isActive: boolean
    createdAt: string
    updatedAt: string
  }[]> {
    return ApiService.get('/api/ai/model-configurations')
  }

  /**
   * 导出配置
   */
  static async exportConfiguration(): Promise<void> {
    return ApiService.download('/api/ai/configuration/export', 'ai-configuration.json')
  }

  /**
   * 导入配置
   */
  static async importConfiguration(file: File): Promise<{
    success: boolean
    message: string
    importedProviders: string[]
    errors: string[]
  }> {
    return ApiService.upload('/api/ai/configuration/import', file)
  }

  /**
   * 获取配置模板
   */
  static async getConfigTemplate(providerName: string): Promise<Record<string, any>> {
    return ApiService.get(`/api/ai/providers/${providerName}/config-template`)
  }

  /**
   * 验证配置
   */
  static async validateConfig(providerName: string, config: AIProviderConfig): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    return ApiService.post(`/api/ai/providers/${providerName}/validate-config`, config)
  }

  /**
   * 获取提供商健康状态
   */
  static async getProviderHealth(): Promise<Record<string, {
    isHealthy: boolean
    lastCheck: string
    responseTime?: number
    error?: string
  }>> {
    return ApiService.get('/api/ai/providers/health')
  }

  /**
   * 刷新提供商健康状态
   */
  static async refreshProviderHealth(): Promise<void> {
    return ApiService.post('/api/ai/providers/health/refresh', {})
  }

  // ==================== 用户AI配置相关方法 ====================

  /**
   * 获取当前用户的所有AI配置
   */
  static async getUserAIConfigurations(): Promise<UserAIConfiguration[]> {
    return ApiService.get<UserAIConfiguration[]>('/api/useraiconfiguration')
  }

  /**
   * 根据类型获取用户AI配置
   */
  static async getUserAIConfigurationsByType(modelType: string): Promise<UserAIConfiguration[]> {
    return ApiService.get<UserAIConfiguration[]>(`/api/useraiconfiguration/by-type/${modelType}`)
  }

  /**
   * 获取用户默认AI配置
   */
  static async getDefaultUserAIConfiguration(modelType?: string): Promise<UserAIConfiguration | null> {
    try {
      const url = modelType
        ? `/api/useraiconfiguration/default?modelType=${modelType}`
        : '/api/useraiconfiguration/default'
      return await ApiService.get<UserAIConfiguration>(url)
    } catch (error: any) {
      if (error.status === 404) {
        console.warn(`未找到默认AI配置 (modelType: ${modelType || 'any'})`)
        return null
      }
      throw error
    }
  }

  /**
   * 创建用户AI配置
   */
  static async createUserAIConfiguration(config: CreateUserAIConfigurationRequest): Promise<UserAIConfiguration> {
    return ApiService.post<UserAIConfiguration>('/api/useraiconfiguration', config)
  }

  /**
   * 更新用户AI配置
   */
  static async updateUserAIConfiguration(id: number, config: UpdateUserAIConfigurationRequest): Promise<void> {
    return ApiService.put<void>(`/api/useraiconfiguration/${id}`, config)
  }

  /**
   * 删除用户AI配置
   */
  static async deleteUserAIConfiguration(id: number): Promise<void> {
    return ApiService.delete<void>(`/api/useraiconfiguration/${id}`)
  }

  // ==================== 用户任务映射相关方法 ====================

  /**
   * 获取当前用户的所有任务映射
   */
  static async getUserTaskMappings(): Promise<UserTaskMapping[]> {
    return ApiService.get<UserTaskMapping[]>('/api/usertaskmapping')
  }

  /**
   * 根据任务类型获取用户任务映射
   */
  static async getUserTaskMappingsByType(taskType: string): Promise<UserTaskMapping[]> {
    return ApiService.get<UserTaskMapping[]>(`/api/usertaskmapping/by-task-type/${taskType}`)
  }

  /**
   * 获取用户指定任务类型的默认配置
   */
  static async getDefaultTaskMapping(taskType: string): Promise<UserTaskMapping | null> {
    try {
      return await ApiService.get<UserTaskMapping>(`/api/usertaskmapping/default/${taskType}`)
    } catch (error: any) {
      if (error.status === 404) {
        console.warn(`未找到任务类型 "${taskType}" 的默认映射配置`)
        return null
      }
      throw error
    }
  }

  /**
   * 获取用户指定任务类型的最高优先级配置
   */
  static async getHighestPriorityTaskMapping(taskType: string): Promise<UserTaskMapping | null> {
    try {
      return await ApiService.get<UserTaskMapping>(`/api/usertaskmapping/highest-priority/${taskType}`)
    } catch (error: any) {
      if (error.status === 404) {
        return null
      }
      throw error
    }
  }

  /**
   * 创建默认AI配置和任务映射
   */
  static async createDefaultConfigurations(): Promise<boolean> {
    try {
      // 创建默认的用户AI配置 - 使用正确的DTO格式
      const defaultAIConfigs = [
        {
          providerName: 'DeepSeek',
          modelName: 'deepseek-chat',
          modelType: 'RequirementAnalysis',
          apiEndpoint: 'https://api.deepseek.com/v1',
          isActive: true,
          isDefault: true,
          dailyTokenLimit: 10000,
          monthlyTokenLimit: 300000
        },
        {
          providerName: 'DeepSeek',
          modelName: 'deepseek-chat',
          modelType: 'Testing',
          apiEndpoint: 'https://api.deepseek.com/v1',
          isActive: true,
          isDefault: false,
          dailyTokenLimit: 10000,
          monthlyTokenLimit: 300000
        }
      ]

      // 创建AI配置
      for (const config of defaultAIConfigs) {
        try {
          await ApiService.post('/api/useraiconfiguration', config)
          console.log(`创建AI配置成功: ${config.modelType}`)
        } catch (error: any) {
          // 如果配置已存在，忽略错误
          if (error.status !== 409) {
            throw error
          }
          console.log(`AI配置已存在: ${config.modelType}`)
        }
      }

      // 创建默认的任务映射 - 使用正确的DTO格式
      const defaultTaskMappings = [
        {
          taskType: 'test-analysis',
          providerName: 'DeepSeek',
          modelName: 'deepseek-chat',
          isActive: true,
          isDefault: true,
          priority: 50,
          description: '测试代码分析任务映射'
        },
        {
          taskType: 'general',
          providerName: 'DeepSeek',
          modelName: 'deepseek-chat',
          isActive: true,
          isDefault: true,
          priority: 50,
          description: '通用任务映射'
        }
      ]

      // 创建任务映射
      for (const mapping of defaultTaskMappings) {
        try {
          await ApiService.post('/api/usertaskmapping', mapping)
          console.log(`创建任务映射成功: ${mapping.taskType}`)
        } catch (error: any) {
          // 如果映射已存在，忽略错误
          if (error.status !== 409) {
            throw error
          }
          console.log(`任务映射已存在: ${mapping.taskType}`)
        }
      }

      console.log('默认AI配置和任务映射创建成功')
      return true
    } catch (error) {
      console.error('创建默认配置失败:', error)
      return false
    }
  }

  /**
   * 创建任务映射
   */
  static async createTaskMapping(mapping: CreateUserTaskMappingRequest): Promise<UserTaskMapping> {
    return ApiService.post<UserTaskMapping>('/api/usertaskmapping', mapping)
  }

  /**
   * 创建用户任务映射（别名方法，用于兼容）
   */
  static async createUserTaskMapping(mapping: CreateUserTaskMappingRequest): Promise<UserTaskMapping> {
    return this.createTaskMapping(mapping)
  }

  /**
   * 更新任务映射
   */
  static async updateTaskMapping(id: number, mapping: UpdateUserTaskMappingRequest): Promise<void> {
    return ApiService.put<void>(`/api/usertaskmapping/${id}`, mapping)
  }

  /**
   * 更新用户任务映射（别名方法，用于兼容）
   */
  static async updateUserTaskMapping(id: number, mapping: UpdateUserTaskMappingRequest): Promise<void> {
    return this.updateTaskMapping(id, mapping)
  }

  /**
   * 删除任务映射
   */
  static async deleteTaskMapping(id: number): Promise<void> {
    return ApiService.delete<void>(`/api/usertaskmapping/${id}`)
  }

  /**
   * 启用/禁用任务映射
   */
  static async toggleTaskMapping(id: number, isActive: boolean): Promise<void> {
    return ApiService.post<void>(`/api/usertaskmapping/${id}/toggle`, { isActive })
  }

  /**
   * 设置默认配置
   */
  static async setDefaultTaskMapping(id: number): Promise<void> {
    return ApiService.post<void>(`/api/usertaskmapping/${id}/set-default`, {})
  }

  /**
   * 批量创建任务映射
   */
  static async createTaskMappingBatch(mappings: CreateUserTaskMappingRequest[]): Promise<UserTaskMapping[]> {
    return ApiService.post<UserTaskMapping[]>('/api/usertaskmapping/batch', { mappings })
  }

  /**
   * 获取用户任务映射统计信息
   */
  static async getTaskMappingStats(): Promise<UserTaskMappingStats> {
    return ApiService.get<UserTaskMappingStats>('/api/usertaskmapping/stats')
  }

  /**
   * 获取可用的任务类型列表
   */
  static async getTaskTypes(): Promise<TaskType[]> {
    return ApiService.get<TaskType[]>('/api/usertaskmapping/task-types')
  }

  /**
   * 获取可用的AI提供商列表（用于任务映射）
   */
  static async getTaskMappingProviders(): Promise<AIProviderOption[]> {
    return ApiService.get<AIProviderOption[]>('/api/usertaskmapping/providers')
  }

  /**
   * 检查用户是否已配置指定任务类型的映射
   */
  static async hasTaskMapping(taskType: string): Promise<boolean> {
    const result = await ApiService.get<{ hasMapping: boolean }>(`/api/usertaskmapping/has-mapping/${taskType}`)
    return result.hasMapping
  }

  /**
   * 从任务映射创建AI配置（用于兼容现有的updateConfiguration方法）
   */
  static async updateConfigurationFromTaskMappings(taskMappings: UserTaskMapping[]): Promise<void> {
    // 将用户任务映射转换为AI配置格式
    const taskMapping: Record<string, string> = {}

    taskMappings.forEach(mapping => {
      if (mapping.isActive && mapping.isDefault) {
        taskMapping[mapping.taskType] = mapping.providerName
      }
    })

    // 调用现有的updateConfiguration方法
    const config: Partial<AIConfiguration> = {
      taskMapping
    }

    return ApiService.put<void>('/api/ai/configuration', config)
  }
}
