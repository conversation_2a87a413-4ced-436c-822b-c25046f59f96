<template>
  <div class="avatar-upload-test">
    <h2>头像上传测试</h2>
    
    <div class="test-section">
      <h3>当前头像</h3>
      <el-avatar :size="120" :src="getAvatarUrl(currentAvatar)">
        <el-icon size="60"><User /></el-icon>
      </el-avatar>
      <p>当前头像路径: {{ currentAvatar || '无' }}</p>
    </div>

    <div class="test-section">
      <h3>上传新头像</h3>
      <el-upload
        ref="uploadRef"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :http-request="handleUpload"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传jpg/png文件，且不超过2MB
          </div>
        </template>
      </el-upload>
      
      <el-button 
        type="primary" 
        :loading="uploading"
        @click="triggerUpload"
        style="margin-top: 20px;"
      >
        {{ uploading ? '上传中...' : '选择文件上传' }}
      </el-button>
    </div>

    <div class="test-section">
      <h3>上传进度</h3>
      <el-progress 
        v-if="uploadProgress > 0" 
        :percentage="uploadProgress" 
        :status="uploadProgress === 100 ? 'success' : undefined"
      />
    </div>

    <div class="test-section">
      <h3>测试结果</h3>
      <el-alert
        v-if="testResult"
        :title="testResult.title"
        :type="testResult.type"
        :description="testResult.description"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, type UploadInstance, type UploadRequestOptions } from 'element-plus'
import { UserService } from '@/services/user'
import { useAuthStore } from '@/stores/auth'
import { User, UploadFilled } from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 响应式数据
const uploading = ref(false)
const uploadProgress = ref(0)
const currentAvatar = ref(authStore.user?.avatar || '')
const uploadRef = ref<UploadInstance>()

const testResult = ref<{
  title: string
  type: 'success' | 'warning' | 'info' | 'error'
  description: string
} | null>(null)

// 方法
const getAvatarUrl = (avatar?: string) => {
  if (!avatar) return ''
  if (avatar.startsWith('http')) return avatar
  return `https://localhost:61136/${avatar}`
}

const beforeUpload = (file: File) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、JPEG、PNG、WEBP 格式的图片')
    return false
  }

  const maxSize = 2 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('头像文件大小不能超过 2MB')
    return false
  }

  return true
}

const handleUpload = async (options: UploadRequestOptions) => {
  const file = options.file as File
  
  try {
    uploading.value = true
    uploadProgress.value = 0
    testResult.value = null
    
    console.log('开始上传文件:', file.name, '大小:', file.size, '类型:', file.type)
    
    const response = await UserService.uploadAvatar(file, (progress) => {
      uploadProgress.value = progress
      console.log('上传进度:', progress + '%')
    })
    
    console.log('上传响应:', response)
    
    if (response.success) {
      currentAvatar.value = response.data.avatar
      
      // 更新认证store中的用户信息
      await authStore.getCurrentUser()
      
      testResult.value = {
        title: '上传成功',
        type: 'success',
        description: `头像已成功上传到: ${response.data.avatar}`
      }
      
      ElMessage.success('头像上传成功')
    } else {
      testResult.value = {
        title: '上传失败',
        type: 'error',
        description: response.message || '未知错误'
      }
      ElMessage.error('头像上传失败')
    }
    
  } catch (error: any) {
    console.error('上传失败:', error)
    
    testResult.value = {
      title: '上传异常',
      type: 'error',
      description: error.message || '网络错误或服务器异常'
    }
    
    ElMessage.error(error.message || '头像上传失败')
  } finally {
    uploading.value = false
  }
}

const triggerUpload = () => {
  uploadRef.value?.clearFiles()
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/jpeg,image/jpg,image/png,image/webp'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file && beforeUpload(file)) {
      handleUpload({ file } as UploadRequestOptions)
    }
  }
  input.click()
}
</script>

<style lang="scss" scoped>
.avatar-upload-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: var(--el-text-color-primary);
  }
}

.el-upload {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
