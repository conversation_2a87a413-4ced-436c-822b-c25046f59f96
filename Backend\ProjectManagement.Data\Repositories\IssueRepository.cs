using SqlSugar;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 问题仓储接口
/// </summary>
public interface IIssueRepository : IRepository<Issue>
{
    /// <summary>
    /// 根据项目ID获取问题列表
    /// </summary>
    Task<List<Issue>> GetByProjectIdAsync(int projectId);

    /// <summary>
    /// 根据分配用户ID获取问题列表
    /// </summary>
    Task<List<Issue>> GetByAssignedUserIdAsync(int userId);

    /// <summary>
    /// 根据报告用户ID获取问题列表
    /// </summary>
    Task<List<Issue>> GetByReportedUserIdAsync(int userId);

    /// <summary>
    /// 搜索问题
    /// </summary>
    Task<PagedResult<Issue>> SearchIssuesAsync(
        string? keyword = null,
        int? projectId = null,
        string? status = null,
        string? priority = null,
        string? issueType = null,
        int? assignedTo = null,
        int pageIndex = 1,
        int pageSize = 20);

    /// <summary>
    /// 获取问题统计信息
    /// </summary>
    Task<IssueStatistics> GetIssueStatisticsAsync(int? projectId = null, int? userId = null);

    /// <summary>
    /// 更新问题状态
    /// </summary>
    Task<bool> UpdateIssueStatusAsync(int issueId, string status, int? resolvedBy = null);

    /// <summary>
    /// 分配问题给用户
    /// </summary>
    Task<bool> AssignIssueAsync(int issueId, int? assignedTo);
}

/// <summary>
/// 问题仓储实现
/// </summary>
public class IssueRepository : BaseRepository<Issue>, IIssueRepository
{
    public IssueRepository(ISqlSugarClient db, ILogger<IssueRepository> logger)
        : base(db, logger)
    {
    }

    public async Task<List<Issue>> GetByProjectIdAsync(int projectId)
    {
        try
        {
            return await _db.Queryable<Issue>()
                .LeftJoin<Project>((i, p) => i.ProjectId == p.Id)
                .LeftJoin<User>((i, p, au) => i.AssignedTo == au.Id)
                .LeftJoin<User>((i, p, au, ru) => i.ReportedBy == ru.Id)
                .Where(i => i.ProjectId == projectId)
                .Select((i, p, au, ru) => new Issue
                {
                    Id = i.Id,
                    ProjectId = i.ProjectId,
                    Title = i.Title,
                    Description = i.Description,
                    IssueType = i.IssueType,
                    Priority = i.Priority,
                    Status = i.Status,
                    AssignedTo = i.AssignedTo,
                    ReportedBy = i.ReportedBy,
                    Labels = i.Labels,
                    CreatedTime = i.CreatedTime,
                    UpdatedTime = i.UpdatedTime,
                    ResolvedAt = i.ResolvedAt,
                    Project = p,
                    AssignedToUser = au,
                    ReportedByUser = ru
                })
                .OrderByDescending(i => i.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目ID获取问题列表失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<Issue>> GetByAssignedUserIdAsync(int userId)
    {
        try
        {
            return await _db.Queryable<Issue>()
                .LeftJoin<Project>((i, p) => i.ProjectId == p.Id)
                .LeftJoin<User>((i, p, au) => i.AssignedTo == au.Id)
                .LeftJoin<User>((i, p, au, ru) => i.ReportedBy == ru.Id)
                .Where(i => i.AssignedTo == userId)
                .Select((i, p, au, ru) => new Issue
                {
                    Id = i.Id,
                    ProjectId = i.ProjectId,
                    Title = i.Title,
                    Description = i.Description,
                    IssueType = i.IssueType,
                    Priority = i.Priority,
                    Status = i.Status,
                    AssignedTo = i.AssignedTo,
                    ReportedBy = i.ReportedBy,
                    Labels = i.Labels,
                    CreatedTime = i.CreatedTime,
                    UpdatedTime = i.UpdatedTime,
                    ResolvedAt = i.ResolvedAt,
                    Project = p,
                    AssignedToUser = au,
                    ReportedByUser = ru
                })
                .OrderByDescending(i => i.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据分配用户ID获取问题列表失败，UserId: {UserId}", userId);
            throw;
        }
    }

    public async Task<List<Issue>> GetByReportedUserIdAsync(int userId)
    {
        try
        {
            return await _db.Queryable<Issue>()
                .LeftJoin<Project>((i, p) => i.ProjectId == p.Id)
                .LeftJoin<User>((i, p, au) => i.AssignedTo == au.Id)
                .LeftJoin<User>((i, p, au, ru) => i.ReportedBy == ru.Id)
                .Where(i => i.ReportedBy == userId)
                .Select((i, p, au, ru) => new Issue
                {
                    Id = i.Id,
                    ProjectId = i.ProjectId,
                    Title = i.Title,
                    Description = i.Description,
                    IssueType = i.IssueType,
                    Priority = i.Priority,
                    Status = i.Status,
                    AssignedTo = i.AssignedTo,
                    ReportedBy = i.ReportedBy,
                    Labels = i.Labels,
                    CreatedTime = i.CreatedTime,
                    UpdatedTime = i.UpdatedTime,
                    ResolvedAt = i.ResolvedAt,
                    Project = p,
                    AssignedToUser = au,
                    ReportedByUser = ru
                })
                .OrderByDescending(i => i.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据报告用户ID获取问题列表失败，UserId: {UserId}", userId);
            throw;
        }
    }

    public async Task<PagedResult<Issue>> SearchIssuesAsync(
        string? keyword = null,
        int? projectId = null,
        string? status = null,
        string? priority = null,
        string? issueType = null,
        int? assignedTo = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<Issue>()
                .LeftJoin<Project>((i, p) => i.ProjectId == p.Id)
                .LeftJoin<User>((i, p, au) => i.AssignedTo == au.Id)
                .LeftJoin<User>((i, p, au, ru) => i.ReportedBy == ru.Id);

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where((i, p, au, ru) =>
                    i.Title.Contains(keyword) ||
                    i.Description.Contains(keyword) ||
                    (i.Labels != null && i.Labels.Contains(keyword)));
            }

            // 项目筛选
            if (projectId.HasValue)
            {
                query = query.Where((i, p, au, ru) => i.ProjectId == projectId.Value);
            }

            // 状态筛选
            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where((i, p, au, ru) => i.Status == status);
            }

            // 优先级筛选
            if (!string.IsNullOrWhiteSpace(priority))
            {
                query = query.Where((i, p, au, ru) => i.Priority == priority);
            }

            // 问题类型筛选
            if (!string.IsNullOrWhiteSpace(issueType))
            {
                query = query.Where((i, p, au, ru) => i.IssueType == issueType);
            }

            // 分配用户筛选
            if (assignedTo.HasValue)
            {
                query = query.Where((i, p, au, ru) => i.AssignedTo == assignedTo.Value);
            }

            var totalCount = await query.CountAsync();

            var items = await query
                .Select((i, p, au, ru) => new Issue
                {
                    Id = i.Id,
                    ProjectId = i.ProjectId,
                    Title = i.Title,
                    Description = i.Description,
                    IssueType = i.IssueType,
                    Priority = i.Priority,
                    Status = i.Status,
                    AssignedTo = i.AssignedTo,
                    ReportedBy = i.ReportedBy,
                    Labels = i.Labels,
                    CreatedTime = i.CreatedTime,
                    UpdatedTime = i.UpdatedTime,
                    ResolvedAt = i.ResolvedAt,
                    Project = p,
                    AssignedToUser = au,
                    ReportedByUser = ru
                })
                .OrderByDescending(i => i.CreatedTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<Issue>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索问题失败");
            throw;
        }
    }

    public async Task<IssueStatistics> GetIssueStatisticsAsync(int? projectId = null, int? userId = null)
    {
        try
        {
            var query = _db.Queryable<Issue>();

            if (projectId.HasValue)
            {
                query = query.Where(i => i.ProjectId == projectId.Value);
            }

            if (userId.HasValue)
            {
                query = query.Where(i => i.AssignedTo == userId.Value || i.ReportedBy == userId.Value);
            }

            var totalCount = await query.CountAsync();
            var openCount = await query.Where(i => i.Status == "Open").CountAsync();
            var inProgressCount = await query.Where(i => i.Status == "InProgress").CountAsync();
            var resolvedCount = await query.Where(i => i.Status == "Resolved").CountAsync();
            var closedCount = await query.Where(i => i.Status == "Closed").CountAsync();

            var bugCount = await query.Where(i => i.IssueType == "Bug").CountAsync();
            var featureCount = await query.Where(i => i.IssueType == "Feature").CountAsync();
            var enhancementCount = await query.Where(i => i.IssueType == "Enhancement").CountAsync();
            var taskCount = await query.Where(i => i.IssueType == "Task").CountAsync();

            var highPriorityCount = await query.Where(i => i.Priority == "High").CountAsync();
            var criticalPriorityCount = await query.Where(i => i.Priority == "Critical").CountAsync();

            return new IssueStatistics
            {
                TotalCount = totalCount,
                OpenCount = openCount,
                InProgressCount = inProgressCount,
                ResolvedCount = resolvedCount,
                ClosedCount = closedCount,
                BugCount = bugCount,
                FeatureCount = featureCount,
                EnhancementCount = enhancementCount,
                TaskCount = taskCount,
                HighPriorityCount = highPriorityCount,
                CriticalPriorityCount = criticalPriorityCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取问题统计信息失败");
            throw;
        }
    }

    public async Task<bool> UpdateIssueStatusAsync(int issueId, string status, int? resolvedBy = null)
    {
        try
        {
            var issue = await GetByIdAsync(issueId);
            if (issue == null) return false;

            issue.Status = status;
            issue.UpdatedTime = DateTime.Now;

            if (status == "Resolved" || status == "Closed")
            {
                issue.ResolvedAt = DateTime.Now;
            }

            return await UpdateAsync(issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新问题状态失败，IssueId: {IssueId}", issueId);
            throw;
        }
    }

    public async Task<bool> AssignIssueAsync(int issueId, int? assignedTo)
    {
        try
        {
            var issue = await GetByIdAsync(issueId);
            if (issue == null) return false;

            issue.AssignedTo = assignedTo;
            issue.UpdatedTime = DateTime.Now;

            return await UpdateAsync(issue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配问题失败，IssueId: {IssueId}", issueId);
            throw;
        }
    }
}

/// <summary>
/// 问题统计信息
/// </summary>
public class IssueStatistics
{
    public int TotalCount { get; set; }
    public int OpenCount { get; set; }
    public int InProgressCount { get; set; }
    public int ResolvedCount { get; set; }
    public int ClosedCount { get; set; }
    public int BugCount { get; set; }
    public int FeatureCount { get; set; }
    public int EnhancementCount { get; set; }
    public int TaskCount { get; set; }
    public int HighPriorityCount { get; set; }
    public int CriticalPriorityCount { get; set; }
}
