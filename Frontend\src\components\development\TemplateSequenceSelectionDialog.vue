<template>
  <el-dialog
    v-model="visible"
    title="选择模板序列"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="template-sequence-selection">
      <!-- 搜索和过滤 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索序列名称或描述"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterCategory"
              placeholder="选择分类"
              clearable
              style="width: 100%"
              @change="handleSearch"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterActive"
              placeholder="状态"
              clearable
              style="width: 100%"
              @change="handleSearch"
            >
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 目标步骤信息 -->
      <div class="target-steps-section" v-if="selectedSteps.length > 0">
        <h4>目标步骤 ({{ selectedSteps.length }}个)</h4>
        <div class="steps-preview">
          <el-tag
            v-for="step in selectedSteps.slice(0, 5)"
            :key="step.id"
            class="step-tag"
            size="small"
          >
            {{ step.stepName }}
          </el-tag>
          <el-tag v-if="selectedSteps.length > 5" size="small" type="info">
            +{{ selectedSteps.length - 5 }}个
          </el-tag>
        </div>
      </div>

      <!-- 序列列表 -->
      <div class="sequences-section">
        <h4>可用模板序列</h4>
        <el-table
          v-loading="loading"
          :data="sequences"
          style="width: 100%"
          @row-click="selectSequence"
          highlight-current-row
          ref="sequenceTable"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="name" label="序列名称" min-width="200">
            <template #default="{ row }">
              <div class="sequence-name">
                <el-icon class="sequence-icon">
                  <List v-if="row.isActive" />
                  <CircleClose v-else />
                </el-icon>
                <span>{{ row.name }}</span>
                <el-tag v-if="!row.isActive" type="info" size="small">已禁用</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />

          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag :type="getCategoryTagType(row.category)" size="small">
                {{ row.category }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="步骤数" width="120">
            <template #default="{ row }">
              <div class="step-count">
                <el-icon class="step-icon"><Operation /></el-icon>
                <span class="step-text">{{ getStepCountText(row.steps?.length || 0) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="usageCount" label="使用次数" width="100" />

          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                :disabled="!row.isActive || !row.steps?.length"
                @click.stop="previewSequence(row)"
              >
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 序列预览 -->
      <div class="sequence-preview" v-if="previewingSequence">
        <h4>序列预览: {{ previewingSequence.name }}</h4>
        <div class="steps-list">
          <div
            v-for="(step, index) in previewingSequence.steps"
            :key="step.id"
            class="step-item"
          >
            <div class="step-order">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-action">{{ getActionTypeText(step.actionType) }}</div>
              <div class="step-description">{{ step.description }}</div>
              <div class="step-params" v-if="Object.keys(step.parameters).length > 0">
                参数: {{ formatParameters(step.parameters) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleApply"
          :disabled="!selectedSequence || !selectedSequence.isActive"
        >
          应用序列
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, List, CircleClose, Operation } from '@element-plus/icons-vue'
import CustomTemplateService, { type TemplateSequence, type PageQuery } from '@/services/customTemplate'
import type { DevelopmentStep } from '@/types/development'

// Props
interface Props {
  modelValue: boolean
  projectId: number
  selectedSteps: DevelopmentStep[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'apply': [sequenceId: number, targetSteps: DevelopmentStep[]]
}>()

// 响应式数据
const loading = ref(false)
const sequences = ref<TemplateSequence[]>([])
const categories = ref<string[]>([])
const selectedSequence = ref<TemplateSequence | null>(null)
const previewingSequence = ref<TemplateSequence | null>(null)
const sequenceTable = ref()

// 搜索和过滤
const searchKeyword = ref('')
const filterCategory = ref('')
const filterActive = ref<boolean | undefined>(undefined)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const queryParams = computed((): PageQuery => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  keyword: searchKeyword.value || undefined,
  category: filterCategory.value || undefined,
  isActive: filterActive.value
}))

// 监听器
watch(visible, (newVal) => {
  if (newVal) {
    loadData()
    loadCategories()
  } else {
    resetDialog()
  }
})

// 生命周期
onMounted(() => {
  if (visible.value) {
    loadData()
    loadCategories()
  }
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const result = await CustomTemplateService.getSequences(queryParams.value)
    sequences.value = result.items
    pagination.total = result.total
  } catch (error) {
    console.error('加载模板序列失败:', error)
    ElMessage.error('加载模板序列失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    categories.value = await CustomTemplateService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const selectSequence = (sequence: TemplateSequence) => {
  selectedSequence.value = sequence
  sequenceTable.value?.setCurrentRow(sequence)
}

const previewSequence = async (sequence: TemplateSequence) => {
  try {
    // 获取完整的序列信息（包含步骤）
    previewingSequence.value = await CustomTemplateService.getSequence(sequence.id)
  } catch (error) {
    console.error('获取序列详情失败:', error)
    ElMessage.error('获取序列详情失败')
  }
}

const handleApply = () => {
  if (!selectedSequence.value) {
    ElMessage.warning('请选择一个模板序列')
    return
  }

  if (!selectedSequence.value.isActive) {
    ElMessage.warning('所选序列已禁用，无法应用')
    return
  }

  if (!selectedSequence.value.steps?.length) {
    ElMessage.warning('所选序列没有配置步骤，无法应用')
    return
  }

  emit('apply', selectedSequence.value.id, props.selectedSteps)
  handleClose()
}

const handleClose = () => {
  visible.value = false
}

const resetDialog = () => {
  selectedSequence.value = null
  previewingSequence.value = null
  searchKeyword.value = ''
  filterCategory.value = ''
  filterActive.value = undefined
  pagination.page = 1
}

// 工具方法
const getCategoryTagType = (category: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '按钮': 'primary',
    '菜单': 'success',
    '对话框': 'warning',
    '输入框': 'info',
    '图标': 'danger',
    'CopilotChat自动化': 'primary',
    '其他': 'info'
  }
  return typeMap[category] || 'info'
}

const getActionTypeText = (actionType: string) => {
  const actionMap: Record<string, string> = {
    'click': '点击',
    'wait': '等待',
    'input': '输入',
    'delay': '延迟',
    'screenshot': '截图',
    'verify': '验证',
    'scroll': '滚动',
    'key_press': '按键'
  }
  return actionMap[actionType] || actionType
}

const formatParameters = (parameters: Record<string, any>) => {
  return Object.entries(parameters)
    .map(([key, value]) => `${key}: ${value}`)
    .join(', ')
}

// 将数字转换为中文文字
const getStepCountText = (count: number): string => {
  if (count === 0) return '无步骤'

  const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千']

  if (count < 10) {
    return `${chineseNumbers[count]}步`
  } else if (count < 20) {
    return count === 10 ? '十步' : `一十${chineseNumbers[count % 10]}步`
  } else if (count < 100) {
    const tens = Math.floor(count / 10)
    const ones = count % 10
    return ones === 0 ? `${chineseNumbers[tens]}十步` : `${chineseNumbers[tens]}十${chineseNumbers[ones]}步`
  } else if (count < 1000) {
    const hundreds = Math.floor(count / 100)
    const remainder = count % 100
    if (remainder === 0) {
      return `${chineseNumbers[hundreds]}百步`
    } else if (remainder < 10) {
      return `${chineseNumbers[hundreds]}百零${chineseNumbers[remainder]}步`
    } else {
      const tens = Math.floor(remainder / 10)
      const ones = remainder % 10
      if (ones === 0) {
        return `${chineseNumbers[hundreds]}百${chineseNumbers[tens]}十步`
      } else {
        return `${chineseNumbers[hundreds]}百${chineseNumbers[tens]}十${chineseNumbers[ones]}步`
      }
    }
  } else {
    // 超过999步，直接显示数字
    return `${count}步`
  }
}
</script>

<style scoped lang="scss">
.template-sequence-selection {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .target-steps-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #e8f4fd;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      color: #409eff;
    }

    .steps-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .step-tag {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .sequences-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
    }

    .sequence-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .sequence-icon {
        color: #409eff;
      }
    }

    .step-count {
      display: flex;
      align-items: center;
      gap: 6px;

      .step-icon {
        color: #409eff;
        font-size: 16px;
      }

      .step-text {
        color: #606266;
        font-size: 13px;
        font-weight: 500;
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      text-align: center;
    }
  }

  .sequence-preview {
    padding: 16px;
    background: #f0f9ff;
    border-radius: 6px;
    border: 1px solid #e1f5fe;

    h4 {
      margin: 0 0 16px 0;
      color: #0277bd;
    }

    .steps-list {
      max-height: 300px;
      overflow-y: auto;

      .step-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px;
        margin-bottom: 8px;
        background: white;
        border-radius: 4px;
        border: 1px solid #e3f2fd;

        .step-order {
          width: 24px;
          height: 24px;
          background: #409eff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .step-content {
          flex: 1;

          .step-action {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .step-description {
            color: #606266;
            margin-bottom: 4px;
          }

          .step-params {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
