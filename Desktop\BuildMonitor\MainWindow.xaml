<Window x:Class="BuildMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编译监控器 - Build Monitor" Height="800" Width="1100"
        WindowStartupLocation="CenterScreen">
    
    <DockPanel>
        <!-- 菜单栏 -->
        <Menu DockPanel.Dock="Top" Background="#F0F0F0">
            <MenuItem Header="文件(_F)">
                <MenuItem Name="LogoutMenuItem" Header="退出登录(_L)" Click="Logout_Click"/>
                <Separator/>
                <MenuItem Header="退出(_X)" Click="Exit_Click"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Click="About_Click"/>
            </MenuItem>
        </Menu>

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <TextBlock Grid.Row="0" Text="编译监控器" FontSize="24" FontWeight="Bold"
                       HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- 控制面板 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,10">
                <!-- 第一行：登录和编译按钮 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                    <Button Name="CompileBackendBtn" Content="🔨 编译后端" Width="120" Height="35"
                            Margin="0,0,10,0" Click="CompileBackend_Click"/>
                    <Button Name="CompileFrontendBtn" Content="🎨 编译前端" Width="120" Height="35"
                            Margin="0,0,10,0" Click="CompileFrontend_Click"/>
                </StackPanel>

                <!-- 第二行：清理、刷新和自动编译按钮 -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                    <Button Name="ClearErrorsBtn" Content="🗑️ 清空错误" Width="120" Height="35"
                            Margin="0,0,10,0" Click="ClearErrors_Click"/>
                    <Button Name="CleanupDuplicatesBtn" Content="🧹 去重" Width="80" Height="35"
                            Margin="0,0,10,0" Click="CleanupDuplicates_Click" ToolTip="清理重复的编译错误"/>
                    <Button Name="RefreshBtn" Content="🔄 刷新" Width="80" Height="35"
                            Margin="0,0,15,0" Click="Refresh_Click"/>

                    <!-- 自动编译控件 -->
                    <Separator Margin="10,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>
                    <Button Name="StartAutoCompileBtn" Content="▶️ 启动自动编译" Width="130" Height="35"
                            Margin="0,0,10,0" Click="StartAutoCompile_Click"/>
                    <Button Name="StopAutoCompileBtn" Content="⏹️ 停止自动编译" Width="130" Height="35"
                            Margin="0,0,10,0" Click="StopAutoCompile_Click" IsEnabled="False"/>
                    <TextBlock Text="间隔:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                    <TextBox Name="IntervalTextBox" Width="50" Height="25" Text="30" VerticalAlignment="Center"
                             ToolTip="自动编译间隔时间(秒)" TextAlignment="Center"/>
                    <TextBlock Text="秒" VerticalAlignment="Center" Margin="5,0,10,0"/>
                    <Button Name="UpdateIntervalBtn" Content="更新" Width="50" Height="25"
                            Margin="0,0,10,0" Click="UpdateInterval_Click"/>
                </StackPanel>
            </StackPanel>

            <!-- 第二行：项目选择和路径配置 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 项目选择 -->
                <TextBlock Grid.Column="0" Text="项目:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Grid.Column="1" Name="ProjectComboBox" Height="25"
                          DisplayMemberPath="Name" SelectedValuePath="Id"
                          SelectionChanged="ProjectComboBox_SelectionChanged"/>

                <!-- 后端项目路径 -->
                <TextBlock Grid.Column="2" Text="后端:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox Grid.Column="3" Name="BackendPathTextBox" Height="25"
                         ToolTip="后端项目文件路径(.csproj)" IsReadOnly="True" Background="LightGray"/>
                <Button Grid.Column="4" Name="SelectBackendBtn" Content="📁" Width="30" Height="25"
                        Margin="5,0,0,0" Click="SelectBackend_Click" ToolTip="选择后端项目文件"/>

                <!-- 前端项目路径 -->
                <TextBlock Grid.Column="5" Text="前端:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBox Grid.Column="6" Name="FrontendPathTextBox" Height="25"
                         ToolTip="前端项目目录路径" IsReadOnly="True" Background="LightGray"/>
                <Button Grid.Column="7" Name="SelectFrontendBtn" Content="📁" Width="30" Height="25"
                        Margin="5,0,0,0" Click="SelectFrontend_Click" ToolTip="选择前端项目目录"/>
            </Grid>
        </Grid>
        
        <!-- 错误列表 -->
        <TabControl Grid.Row="2">
            <TabItem Header="后端错误">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Name="BackendErrorCountLabel" Text="后端错误: 0" 
                               FontWeight="Bold" Margin="5"/>
                    
                    <DataGrid Grid.Row="1" Name="BackendErrorsGrid" AutoGenerateColumns="False" 
                              IsReadOnly="True" GridLinesVisibility="Horizontal">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="严重程度" Binding="{Binding Severity}" Width="80"/>
                            <DataGridTextColumn Header="错误代码" Binding="{Binding Code}" Width="80"/>
                            <DataGridTextColumn Header="错误信息" Binding="{Binding Message}" Width="300"/>
                            <DataGridTextColumn Header="文件" Binding="{Binding FilePath}" Width="200"/>
                            <DataGridTextColumn Header="行号" Binding="{Binding LineNumber}" Width="60"/>
                            <DataGridTextColumn Header="时间" Binding="{Binding CompilationTime}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <TabItem Header="前端错误">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Name="FrontendErrorCountLabel" Text="前端错误: 0" 
                               FontWeight="Bold" Margin="5"/>
                    
                    <DataGrid Grid.Row="1" Name="FrontendErrorsGrid" AutoGenerateColumns="False" 
                              IsReadOnly="True" GridLinesVisibility="Horizontal">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="严重程度" Binding="{Binding Severity}" Width="80"/>
                            <DataGridTextColumn Header="错误代码" Binding="{Binding Code}" Width="80"/>
                            <DataGridTextColumn Header="错误信息" Binding="{Binding Message}" Width="300"/>
                            <DataGridTextColumn Header="文件" Binding="{Binding FilePath}" Width="200"/>
                            <DataGridTextColumn Header="行号" Binding="{Binding LineNumber}" Width="60"/>
                            <DataGridTextColumn Header="时间" Binding="{Binding CompilationTime}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 登录选项卡 -->
            <TabItem Header="🔐 登录">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                    <!-- 标题区域 -->
                    <Border Grid.Row="0" Background="#2196F3" CornerRadius="10,10,0,0" Padding="20" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="🔧" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="BuildMonitor 用户登录" FontSize="18" FontWeight="Bold"
                                      Foreground="White" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- 登录表单区域 -->
                    <Border Grid.Row="1" Background="White" CornerRadius="10"
                            BorderBrush="#E0E0E0" BorderThickness="1" Padding="40" Margin="0,20,0,20">
                        <StackPanel MaxWidth="400" HorizontalAlignment="Center">
                            <TextBlock Text="请登录以使用系统功能" FontSize="16" FontWeight="Bold"
                                      HorizontalAlignment="Center" Margin="0,0,0,30" Foreground="#333"/>

                            <!-- 用户名输入 -->
                            <TextBlock Text="用户名:" Margin="0,0,0,5" FontWeight="SemiBold"/>
                            <TextBox Name="LoginUsernameTextBox" Height="35" FontSize="14"
                                     Padding="10,8" BorderBrush="#DDD" BorderThickness="1"
                                     VerticalContentAlignment="Center" Margin="0,0,0,15"/>

                            <!-- 密码输入 -->
                            <TextBlock Text="密码:" Margin="0,0,0,5" FontWeight="SemiBold"/>
                            <PasswordBox Name="LoginPasswordBox" Height="35" FontSize="14"
                                        Padding="10,8" BorderBrush="#DDD" BorderThickness="1"
                                        VerticalContentAlignment="Center" Margin="0,0,0,15"/>

                            <!-- 记住我选项 -->
                            <CheckBox Name="LoginRememberMeCheckBox" Content="记住我" Margin="0,0,0,20"
                                     FontSize="12" Foreground="#666"/>

                            <!-- 错误信息显示 -->
                            <TextBlock Name="LoginErrorMessageTextBlock" Margin="0,0,0,20"
                                      Foreground="Red" FontSize="12" TextWrapping="Wrap"
                                      Visibility="Collapsed"/>

                            <!-- 登录按钮 -->
                            <Button Name="TabLoginButton" Content="登录" Width="120" Height="40"
                                   Background="#2196F3" Foreground="White" FontSize="14" FontWeight="Bold"
                                   BorderThickness="0" Cursor="Hand"
                                   Click="TabLoginButton_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- 状态信息区域 -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,10,10" Padding="15" Margin="0,20,0,0">
                        <StackPanel>
                            <TextBlock Name="LoginStatusTextBlock" Text="请先登录以使用系统功能"
                                      FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Name="CurrentUserTextBlock" Text=""
                                      FontSize="12" FontWeight="Bold" Foreground="#2196F3"
                                      HorizontalAlignment="Center" Margin="0,5,0,0"
                                      Visibility="Collapsed"/>
                        </StackPanel>
                    </Border>
                </Grid>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Name="StatusLabel" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem>
                <Separator Margin="10,0"/>
            </StatusBarItem>
            <StatusBarItem>
                <TextBlock Name="ConfigStatusLabel" Text="配置: 未加载" Foreground="Orange"/>
            </StatusBarItem>
            <StatusBarItem>
                <Separator Margin="10,0"/>
            </StatusBarItem>
            <StatusBarItem>
                <TextBlock Name="AutoCompileStatusLabel" Text="自动编译: 已停止" Foreground="Gray"/>
            </StatusBarItem>
            <StatusBarItem>
                <Separator Margin="10,0"/>
            </StatusBarItem>
            <StatusBarItem>
                <TextBlock Name="LastCompileTimeLabel" Text="上次编译: 无" Foreground="Gray"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="DatabaseStatusLabel" Text="数据库: 未连接" Foreground="Red" Margin="0,0,10,0"/>
                    <TextBlock Name="TimeLabel" Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy-MM-dd HH:mm:ss'}"
                               xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
        </Grid>
    </DockPanel>
</Window>
