<template>
  <div class="category-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><FolderOpened /></el-icon>
          分类管理
        </h1>
        <p class="page-description">管理自定义模板的分类结构，支持层级分类和自定义图标颜色</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建分类
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalCategories }}</div>
                <div class="stat-label">总分类数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon system">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.systemCategories }}</div>
                <div class="stat-label">系统分类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon user">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.userCategories }}</div>
                <div class="stat-label">自定义分类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon templates">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalTemplates }}</div>
                <div class="stat-label">总模板数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分类列表 -->
    <el-card class="category-card">
      <template #header>
        <div class="card-header">
          <span>分类列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'tree' ? 'primary' : ''"
                @click="viewMode = 'tree'"
              >
                <el-icon><Share /></el-icon>
                树形
              </el-button>
              <el-button
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree'" class="tree-view">
        <el-tree
          :data="categoryTree"
          :props="treeProps"
          node-key="id"
          :default-expand-all="true"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          draggable
          @node-drop="handleNodeDrop"
          class="category-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-content">
                <el-icon class="node-icon" :style="{ color: data.color }">
                  <component :is="data.icon || 'FolderOpened'" />
                </el-icon>
                <span class="node-name">{{ data.name }}</span>
                <el-tag v-if="data.isSystem" type="info" size="small">系统</el-tag>
                <span class="node-count">({{ data.templateCount }})</span>
              </div>
              <div class="node-actions">
                <el-button size="small" text @click="editCategory(data)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  text
                  type="primary"
                  @click="createSubCategory(data)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  text
                  type="danger"
                  @click="deleteCategory(data)"
                  :disabled="!canDeleteCategory(data)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 表格视图 -->
      <el-table
        v-else
        v-loading="loading"
        :data="flatCategories"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template #default="{ row }">
            <div class="category-name">
              <el-icon class="category-icon" :style="{ color: row.color }">
                <component :is="row.icon || 'FolderOpened'" />
              </el-icon>
              <span>{{ row.name }}</span>
              <el-tag v-if="row.isSystem" type="info" size="small">系统</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="templateCount" label="模板数量" width="100" align="center" />
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        <el-table-column prop="isEnabled" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="toggleEnabled(row)"
              :disabled="row.isSystem"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="editCategory(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="createSubCategory(row)"
              >
                <el-icon><Plus /></el-icon>
                子分类
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteCategory(row)"
                :disabled="!canDeleteCategory(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑分类对话框 -->
    <CategoryFormDialog
      v-model="showCategoryDialog"
      :category="currentCategory"
      :parent-category="parentCategory"
      :all-categories="allCategories"
      @success="handleCategorySuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderOpened, Plus, Refresh, Lock, User, Document, Share, List,
  Edit, Delete, Operation, Menu, ChatDotRound, Picture, DataLine,
  Tools, Box, Cpu, Lightning, Reading, Setting, Star, MagicStick, Link
} from '@element-plus/icons-vue'
import CustomTemplateCategoryService, {
  type CustomTemplateCategory,
  type CategoryTreeNode
} from '@/services/customTemplateCategory'
import CategoryFormDialog from '@/components/automation/CategoryFormDialog.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref<'tree' | 'table'>('tree')
const allCategories = ref<CustomTemplateCategory[]>([])
const categoryTree = ref<CategoryTreeNode[]>([])

// 统计信息
const statistics = reactive({
  totalCategories: 0,
  systemCategories: 0,
  userCategories: 0,
  totalTemplates: 0
})

// 对话框状态
const showCategoryDialog = ref(false)
const currentCategory = ref<CustomTemplateCategory | null>(null)
const parentCategory = ref<CustomTemplateCategory | null>(null)

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const flatCategories = computed(() => {
  const flatten = (categories: CustomTemplateCategory[], level = 0): CustomTemplateCategory[] => {
    const result: CustomTemplateCategory[] = []
    for (const category of categories) {
      result.push({ ...category, level } as any)
      if (category.children && category.children.length > 0) {
        result.push(...flatten(category.children, level + 1))
      }
    }
    return result
  }
  return flatten(allCategories.value)
})

// 生命周期
onMounted(() => {
  loadData()
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const [categories, tree] = await Promise.all([
      CustomTemplateCategoryService.getCategories(),
      CustomTemplateCategoryService.getCategoryTree()
    ])

    allCategories.value = categories
    categoryTree.value = tree

    // 计算统计信息
    updateStatistics(categories)
  } catch (error) {
    console.error('加载分类数据失败:', error)
    ElMessage.error('加载分类数据失败')
  } finally {
    loading.value = false
  }
}

const updateStatistics = (categories: CustomTemplateCategory[]) => {
  statistics.totalCategories = categories.length
  statistics.systemCategories = categories.filter(c => c.isSystem).length
  statistics.userCategories = categories.filter(c => !c.isSystem).length
  statistics.totalTemplates = categories.reduce((sum, c) => sum + c.templateCount, 0)
}

const refreshData = () => {
  loadData()
}

const showCreateDialog = () => {
  currentCategory.value = null
  parentCategory.value = null
  showCategoryDialog.value = true
}

const editCategory = (category: CustomTemplateCategory) => {
  currentCategory.value = category
  parentCategory.value = null
  showCategoryDialog.value = true
}

const createSubCategory = (parent: CustomTemplateCategory) => {
  currentCategory.value = null
  parentCategory.value = parent
  showCategoryDialog.value = true
}

const deleteCategory = async (category: CustomTemplateCategory) => {
  const canDelete = canDeleteCategory(category)
  if (!canDelete.canDelete) {
    ElMessage.warning(canDelete.reason)
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await CustomTemplateCategoryService.deleteCategory(category.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }
}

const toggleEnabled = async (category: CustomTemplateCategory) => {
  try {
    await CustomTemplateCategoryService.toggleEnabled(category.id, category.isEnabled)
    ElMessage.success(category.isEnabled ? '已启用' : '已禁用')
  } catch (error) {
    console.error('切换分类状态失败:', error)
    ElMessage.error('切换分类状态失败')
    // 恢复原状态
    category.isEnabled = !category.isEnabled
  }
}

const canDeleteCategory = (category: CustomTemplateCategory) => {
  return CustomTemplateCategoryService.canDeleteCategory(category)
}

// 拖拽相关方法
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 系统分类不允许拖拽
  if (draggingNode.data.isSystem) {
    return false
  }

  // 不允许拖拽到自己的子节点
  if (type === 'inner' && isDescendant(draggingNode.data, dropNode.data)) {
    return false
  }

  return true
}

const allowDrag = (draggingNode: any) => {
  // 系统分类不允许拖拽
  return !draggingNode.data.isSystem
}

const handleNodeDrop = async (draggingNode: any, dropNode: any, dropType: string) => {
  try {
    const dragCategory = draggingNode.data
    let newParentId: number | undefined

    if (dropType === 'inner') {
      newParentId = dropNode.data.id
    } else {
      newParentId = dropNode.data.parentId
    }

    await CustomTemplateCategoryService.moveCategory(dragCategory.id, newParentId)
    ElMessage.success('移动成功')
    loadData()
  } catch (error) {
    console.error('移动分类失败:', error)
    ElMessage.error('移动分类失败')
    loadData() // 重新加载数据恢复原状态
  }
}

const isDescendant = (ancestor: any, node: any): boolean => {
  if (!node.children) return false

  for (const child of node.children) {
    if (child.id === ancestor.id || isDescendant(ancestor, child)) {
      return true
    }
  }

  return false
}

const handleCategorySuccess = () => {
  loadData()
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.category-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.system {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.user {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.templates {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.category-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-view {
  min-height: 400px;
}

.category-tree {
  background: transparent;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.node-name {
  font-weight: 500;
}

.node-count {
  color: #909399;
  font-size: 12px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 16px;
}
</style>
