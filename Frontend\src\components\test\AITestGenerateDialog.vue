<template>
  <el-dialog
    v-model="visible"
    title="🤖 AI智能测试生成"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="ai-generate-dialog">
      <!-- 生成方式选择 -->
      <el-card class="generation-method">
        <template #header>
          <span>📋 选择生成方式</span>
        </template>
        
        <el-radio-group v-model="generationConfig.sourceType" class="method-options">
          <el-radio-button label="requirement">基于需求文档</el-radio-button>
          <el-radio-button label="page">基于页面结构</el-radio-button>
          <el-radio-button label="business-flow">基于业务流程</el-radio-button>
          <el-radio-button label="api">基于API文档</el-radio-button>
        </el-radio-group>
      </el-card>

      <!-- 具体配置 -->
      <el-card class="generation-config">
        <template #header>
          <span>⚙️ 生成配置</span>
        </template>

        <el-form :model="generationConfig" label-width="120px">
          <!-- 项目选择 -->
          <el-form-item label="选择项目" required>
            <el-select 
              v-model="selectedProjectId" 
              placeholder="请先选择项目"
              style="width: 100%"
              filterable
              @change="handleProjectChange"
            >
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              >
                <div class="project-option">
                  <span class="project-name">{{ project.name }}</span>
                  <el-tag size="small" :type="getProjectStatusType(project.status)">
                    {{ project.status }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 基于需求文档 -->
          <div v-if="generationConfig.sourceType === 'requirement'">
            <el-form-item label="选择需求">
              <el-select 
                v-model="generationConfig.sourceId" 
                placeholder="选择需求文档"
                style="width: 100%"
                filterable
                :loading="loadingRequirements"
                :disabled="!selectedProjectId"
              >
                <el-option
                  v-for="req in requirements"
                  :key="req.id"
                  :label="req.title"
                  :value="req.id"
                >
                  <div class="requirement-option">
                    <span class="requirement-title">{{ req.title }}</span>
                    <el-tag size="small" type="info">{{ req.status || '需求' }}</el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- 基于页面结构 -->
          <div v-if="generationConfig.sourceType === 'page'">
            <el-form-item label="页面URL">
              <el-input 
                v-model="pageUrl" 
                placeholder="输入要测试的页面URL"
                @blur="analyzePageStructure"
              />
            </el-form-item>
            
            <el-form-item label="页面分析" v-if="pageAnalysis">
              <div class="page-analysis">
                <el-tag v-for="element in pageAnalysis.elements" :key="element" size="small">
                  {{ element }}
                </el-tag>
              </div>
            </el-form-item>
          </div>

          <!-- 基于业务流程 -->
          <div v-if="generationConfig.sourceType === 'business-flow'">
            <el-form-item label="业务流程">
              <el-select 
                v-model="selectedBusinessFlow" 
                placeholder="选择业务流程"
                style="width: 100%"
                :loading="loadingBusinessFlows"
                :disabled="!selectedProjectId"
              >
                <el-option
                  v-for="flow in businessFlows"
                  :key="flow.id"
                  :label="flow.name"
                  :value="flow.id"
                >
                  <div class="business-flow-option">
                    <span class="flow-name">{{ flow.name }}</span>
                    <el-tag size="small" type="success">{{ flow.stepCount || 0 }}步骤</el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- 基于API文档 -->
          <div v-if="generationConfig.sourceType === 'api'">
            <el-form-item label="API文档">
              <el-input 
                v-model="apiDocUrl" 
                placeholder="输入API文档URL或上传Swagger文件"
              />
            </el-form-item>
          </div>

          <!-- 测试类型选择 -->
          <el-form-item label="测试类型">
            <el-checkbox-group v-model="generationConfig.testTypes">
              <el-checkbox label="functional">功能测试</el-checkbox>
              <el-checkbox label="ui">界面测试</el-checkbox>
              <el-checkbox label="integration">集成测试</el-checkbox>
              <el-checkbox label="performance">性能测试</el-checkbox>
              <el-checkbox label="accessibility">可访问性测试</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 浏览器选择 -->
          <el-form-item label="目标浏览器">
            <el-checkbox-group v-model="generationConfig.browsers">
              <el-checkbox label="chromium">Chromium</el-checkbox>
              <el-checkbox label="firefox">Firefox</el-checkbox>
              <el-checkbox label="webkit">WebKit</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 优先级 -->
          <el-form-item label="测试优先级">
            <el-radio-group v-model="generationConfig.priority">
              <el-radio label="high">高</el-radio>
              <el-radio label="medium">中</el-radio>
              <el-radio label="low">低</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 高级选项 -->
          <el-form-item label="高级选项">
            <el-checkbox v-model="generationConfig.generateE2E">生成端到端测试</el-checkbox>
            <el-checkbox v-model="generationConfig.generateAPI">生成API测试</el-checkbox>
            <el-checkbox v-model="generationConfig.generatePerformance">生成性能测试</el-checkbox>
          </el-form-item>

          <!-- AI服务选择 -->
          <el-form-item label="AI服务">
            <el-radio-group v-model="aiServiceType">
              <el-radio label="frontend">前端AI服务（使用提示词模板）</el-radio>
              <el-radio label="backend">后端AI服务（完整集成）</el-radio>
            </el-radio-group>
            <div class="service-description">
              <p v-if="aiServiceType === 'frontend'" class="service-desc">
                使用前端AI服务，支持自定义提示词模板，适合快速测试
              </p>
              <p v-if="aiServiceType === 'backend'" class="service-desc">
                使用后端AI服务，完整的企业级集成，支持任务管理和结果持久化
              </p>
            </div>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- AI提示词自定义 -->
      <el-card class="custom-prompt">
        <template #header>
          <span>💡 自定义AI提示词（可选）</span>
        </template>
        
        <el-input
          v-model="customPrompt"
          type="textarea"
          :rows="4"
          placeholder="输入自定义的AI提示词，用于指导测试生成的特殊要求..."
        />
      </el-card>

      <!-- 预览生成计划 -->
      <el-card class="generation-preview" v-if="generationPreview">
        <template #header>
          <span>👀 生成预览</span>
        </template>
        
        <div class="preview-content">
          <p><strong>预计生成测试数量：</strong>{{ generationPreview.estimatedCount }}</p>
          <p><strong>测试类型分布：</strong></p>
          <ul>
            <li v-for="(count, type) in generationPreview.typeDistribution" :key="type">
              {{ type }}: {{ count }} 个
            </li>
          </ul>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="previewGeneration" :loading="previewing">预览生成</el-button>
        <el-button 
          type="primary" 
          @click="startGeneration" 
          :loading="generating"
          :disabled="!canGenerate"
        >
          <el-icon><MagicStick /></el-icon>
          开始生成
        </el-button>
      </div>
    </template>

    <!-- 生成进度对话框 -->
    <el-dialog
      v-model="showProgress"
      title="🚀 AI正在生成测试用例"
      width="600px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="generation-progress">
        <el-progress 
          :percentage="progressPercentage" 
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
        
        <div class="progress-logs">
          <div 
            v-for="(log, index) in progressLogs" 
            :key="index"
            class="progress-log"
          >
            <el-icon><InfoFilled /></el-icon>
            {{ log }}
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { MagicStick, InfoFilled } from '@element-plus/icons-vue'
import type { TestGenerationConfig } from '@/services/aiTestGeneration'
import { batchGenerateTests, BackendAITestGenerationService } from '@/services/aiTestGeneration'
import { ProjectApi, RequirementApi } from '@/utils/api'
import { useAuthStore } from '@/stores/auth'

// Props & Emits
const props = defineProps<{
  modelValue: boolean
  projectId: number
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'generated': [tests: any[]]
}>()

// 认证状态
const authStore = useAuthStore()

// AI服务类型选择
const aiServiceType = ref<'frontend' | 'backend'>('backend')

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const generationConfig = reactive<TestGenerationConfig>({
  projectId: props.projectId,
  sourceType: 'requirement',
  sourceId: undefined,
  testTypes: ['functional', 'ui'],
  browsers: ['chromium'],
  priority: 'medium',
  generateE2E: true,
  generateAPI: false,
  generatePerformance: false
})

const pageUrl = ref('')
const pageAnalysis = ref<any>(null)
const selectedBusinessFlow = ref<number>()
const apiDocUrl = ref('')
const customPrompt = ref('')

// 项目相关数据
const selectedProjectId = ref<number>(props.projectId)
const projects = ref<any[]>([])
const requirements = ref<any[]>([])
const businessFlows = ref<any[]>([])

// 加载状态
const loadingProjects = ref(false)
const loadingRequirements = ref(false)
const loadingBusinessFlows = ref(false)

const generationPreview = ref<any>(null)
const previewing = ref(false)
const generating = ref(false)

const showProgress = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | undefined>()
const progressText = ref('')
const progressLogs = ref<string[]>([])

// 计算属性
const canGenerate = computed(() => {
  // 首先检查用户是否已登录
  if (!authStore.isAuthenticated || !authStore.token) {
    return false
  }

  // 然后必须选择项目
  if (!selectedProjectId.value) {
    return false
  }

  if (generationConfig.sourceType === 'requirement') {
    return !!generationConfig.sourceId
  }
  if (generationConfig.sourceType === 'page') {
    return !!pageUrl.value
  }
  if (generationConfig.sourceType === 'business-flow') {
    return !!selectedBusinessFlow.value
  }
  if (generationConfig.sourceType === 'api') {
    return !!apiDocUrl.value
  }
  return false
})

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  generationConfig.sourceId = undefined
  pageUrl.value = ''
  pageAnalysis.value = null
  selectedBusinessFlow.value = undefined
  apiDocUrl.value = ''
  customPrompt.value = ''
  generationPreview.value = null
}

// 项目相关方法
const getProjectStatusType = (status: string): 'success' | 'warning' | 'danger' | 'info' => {
  const statusMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    'Planning': 'info',
    'InProgress': 'warning', 
    'Testing': 'warning',
    'Deployed': 'success',
    'Completed': 'success',
    'Paused': 'danger',
    'Cancelled': 'info',
    // 添加其他可能的状态
    'Active': 'success',
    'Draft': 'info',
    'Archived': 'info'
  }
  return statusMap[status] || 'info'
}

const handleProjectChange = async (projectId: number) => {
  if (!projectId) return
  
  // 更新配置中的项目ID
  generationConfig.projectId = projectId
  
  // 清空之前的数据
  requirements.value = []
  businessFlows.value = []
  generationConfig.sourceId = undefined
  selectedBusinessFlow.value = undefined
  
  // 加载项目相关数据
  await loadProjectData(projectId)
}

const loadProjects = async () => {
  loadingProjects.value = true
  try {
    const data = await ProjectApi.getProjects({ pageSize: 100 })
    projects.value = data.items || data || []
    
    if (projects.value.length === 0) {
      ElMessage.info('暂无项目，请先创建项目')
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
    if (error.message !== 'Unauthorized') {
      ElMessage.error('加载项目列表失败，请检查网络连接')
    }
    projects.value = []
  } finally {
    loadingProjects.value = false
  }
}

const loadProjectData = async (projectId: number) => {
  await Promise.all([
    loadRequirements(projectId),
    loadBusinessFlows(projectId)
  ])
}

const loadRequirements = async (projectId: number) => {
  loadingRequirements.value = true
  try {
    const data = await RequirementApi.getProjectRequirements(projectId, { pageSize: 100 })
    requirements.value = data.items || data || []
    
    if (requirements.value.length === 0) {
      console.info('该项目暂无需求文档')
    }
  } catch (error) {
    console.error('加载需求列表失败:', error)
    if (error.message === 'Not Found') {
      console.warn('该项目暂无需求文档')
    } else if (error.message !== 'Unauthorized') {
      ElMessage.warning('加载需求列表失败，该项目可能暂无需求文档')
    }
    requirements.value = []
  } finally {
    loadingRequirements.value = false
  }
}

const loadBusinessFlows = async (projectId: number) => {
  loadingBusinessFlows.value = true
  try {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    
    if (!token) {
      ElMessage.error('请先登录')
      businessFlows.value = []
      return
    }

    // 业务流程API暂未实现，先返回空数组
    console.warn('业务流程功能暂未实现')
    businessFlows.value = []
    
    // TODO: 当业务流程API实现后，取消注释以下代码
    /*
    const response = await fetch(`/api/business-flows/projects/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      if (response.status === 404) {
        console.warn('该项目暂无业务流程')
        businessFlows.value = []
        return
      }
      if (response.status === 401) {
        ElMessage.error('认证失败，请重新登录')
        businessFlows.value = []
        return
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    businessFlows.value = data.items || data || []
    */
  } catch (error) {
    console.error('加载业务流程失败:', error)
    businessFlows.value = []
  } finally {
    loadingBusinessFlows.value = false
  }
}

const analyzePageStructure = async () => {
  if (!pageUrl.value) return
  
  try {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 页面分析功能暂未实现，提供基础分析
    console.warn('页面分析功能暂未实现，提供基础元素识别')
    
    // 模拟页面分析结果
    pageAnalysis.value = {
      elements: [
        'input[type="text"]',
        'input[type="password"]', 
        'button[type="submit"]',
        'form',
        'nav',
        'header',
        'footer',
        '.container',
        '#main-content'
      ]
    }
    
    ElMessage.success('页面结构分析完成（模拟数据）')
    
    // TODO: 当页面分析API实现后，取消注释以下代码
    /*
    const response = await fetch('/api/page-analysis', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ url: pageUrl.value })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    pageAnalysis.value = await response.json()
    */
  } catch (error) {
    console.error('页面分析失败:', error)
    ElMessage.error('页面分析失败')
  }
}

const previewGeneration = async () => {
  previewing.value = true
  
  try {
    // 模拟预览生成计划
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    generationPreview.value = {
      estimatedCount: Math.floor(Math.random() * 10) + 5,
      typeDistribution: {
        '功能测试': Math.floor(Math.random() * 5) + 2,
        '界面测试': Math.floor(Math.random() * 3) + 1,
        '集成测试': Math.floor(Math.random() * 2) + 1,
        '端到端测试': generationConfig.generateE2E ? Math.floor(Math.random() * 2) + 1 : 0
      }
    }
    
    ElMessage.success('生成预览完成')
  } catch (error) {
    console.error('预览生成失败:', error)
    ElMessage.error('预览生成失败')
  } finally {
    previewing.value = false
  }
}

const startGeneration = async () => {
  // 检查用户登录状态
  if (!authStore.isAuthenticated || !authStore.token) {
    ElMessage.error('请先登录系统才能使用AI生成功能')
    return
  }

  generating.value = true
  showProgress.value = true
  progressPercentage.value = 0
  progressLogs.value = []

  try {
    // 开始生成过程
    updateProgress(10, '正在分析输入源...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateProgress(30, '正在调用AI模型生成测试用例...')

    let generatedTests: any[] = []

    if (aiServiceType.value === 'backend') {
      // 使用后端AI服务
      updateProgress(40, '正在调用后端AI服务...')

      try {
        // 使用同步版本进行快速测试
        generatedTests = await BackendAITestGenerationService.generateTestsSync(generationConfig)
        updateProgress(80, '后端AI服务生成完成，正在处理结果...')
      } catch (error: any) {
        // 如果后端服务失败，回退到前端服务
        console.warn('后端AI服务失败，回退到前端服务:', error)
        updateProgress(50, '后端服务不可用，使用前端AI服务...')
        generatedTests = await batchGenerateTests(generationConfig)
      }
    } else {
      // 使用前端AI服务
      updateProgress(50, '正在使用前端AI服务生成测试用例...')
      generatedTests = await batchGenerateTests(generationConfig)
    }

    updateProgress(90, '正在验证测试用例语法...')
    await new Promise(resolve => setTimeout(resolve, 1000))

    updateProgress(100, '测试用例生成完成！', 'success')

    // 发送生成的测试用例
    emit('generated', generatedTests)

    const serviceType = aiServiceType.value === 'backend' ? '后端AI服务' : '前端AI服务'
    ElMessage.success(`${serviceType}成功生成 ${generatedTests.length} 个测试用例`)
    
    setTimeout(() => {
      showProgress.value = false
      visible.value = false
    }, 1500)
    
  } catch (error: any) {
    console.error('生成测试用例失败:', error)
    progressStatus.value = 'exception'

    // 根据错误类型提供不同的提示
    if (error.message && error.message.includes('请先登录')) {
      progressText.value = '认证失败，请重新登录'
      ElMessage.error('请先登录系统才能使用AI生成功能')
    } else if (error.message && error.message.includes('AI配置')) {
      progressText.value = 'AI配置未完成'
      ElMessage.error('请先配置AI提供商')
    } else if (error.message && error.message.includes('不存在')) {
      progressText.value = '数据不存在'
      ElMessage.error(error.message)
    } else {
      progressText.value = '生成失败，请重试'
      ElMessage.error('生成测试用例失败: ' + (error.message || '未知错误'))
    }
  } finally {
    generating.value = false
  }
}

const updateProgress = (percentage: number, text: string, status?: 'success' | 'exception') => {
  progressPercentage.value = percentage
  progressText.value = text
  progressStatus.value = status
  progressLogs.value.push(text)
}

// 监听项目ID变化
watch(() => props.projectId, (newProjectId) => {
  selectedProjectId.value = newProjectId
  generationConfig.projectId = newProjectId
  if (newProjectId) {
    loadProjectData(newProjectId)
  }
})

// 组件挂载时加载数据
watch(visible, (newVisible) => {
  if (newVisible) {
    loadProjects()
    if (selectedProjectId.value) {
      loadProjectData(selectedProjectId.value)
    }
  }
})
</script>

<style lang="scss" scoped>
.ai-generate-dialog {
  .generation-method,
  .generation-config,
  .custom-prompt,
  .generation-preview {
    margin-bottom: 16px;
    
    :deep(.el-card__header) {
      padding: 12px 16px;
      font-weight: 500;
    }
  }
  
  .method-options {
    width: 100%;
    
    :deep(.el-radio-button__inner) {
      padding: 8px 16px;
    }
  }
  
  .page-analysis {
    .el-tag {
      margin: 2px 4px 2px 0;
    }
  }
  
  .preview-content {
    ul {
      margin: 8px 0;
      padding-left: 20px;
    }
  }
}

.generation-progress {
  text-align: center;
  
  .progress-text {
    margin: 16px 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
  
  .progress-logs {
    max-height: 200px;
    overflow-y: auto;
    text-align: left;
    margin-top: 20px;
    
    .progress-log {
      display: flex;
      align-items: center;
      padding: 4px 0;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      
      .el-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 8px;
  }
}

.project-option,
.requirement-option,
.business-flow-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .project-name,
  .requirement-title,
  .flow-name {
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.service-description {
  margin-top: 8px;
}

.service-desc {
  margin: 0;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  font-size: 12px;
  color: #0277bd;
  line-height: 1.4;
}
</style>