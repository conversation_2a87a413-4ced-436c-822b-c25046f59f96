namespace ProjectManagement.Core.DTOs.LangChain
{
    /// <summary>
    /// 推理链定义
    /// </summary>
    public class ChainDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<ChainStep> Steps { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 推理链步骤
    /// </summary>
    public class ChainStep
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // analysis, recommendation, design, assessment, etc.
        public string PromptTemplate { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string> Dependencies { get; set; } = new(); // 依赖的前置步骤
        public bool Optional { get; set; } = false;
    }

    /// <summary>
    /// 推理链上下文
    /// </summary>
    public class ChainContext
    {
        public Dictionary<string, object> Variables { get; set; } = new();
        public Dictionary<string, object> StepResults { get; set; } = new();
        public string ProjectId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public void AddVariable(string key, object value)
        {
            Variables[key] = value;
        }

        public void AddStepResult(string stepName, object result)
        {
            StepResults[stepName] = result;
        }

        public T? GetVariable<T>(string key)
        {
            if (Variables.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default;
        }

        public ChainContext Clone()
        {
            return new ChainContext
            {
                Variables = new Dictionary<string, object>(Variables),
                StepResults = new Dictionary<string, object>(StepResults),
                ProjectId = ProjectId,
                UserId = UserId,
                CreatedAt = CreatedAt
            };
        }
    }

    /// <summary>
    /// 推理链执行结果
    /// </summary>
    public class ChainExecutionResult
    {
        public string ChainName { get; set; } = string.Empty;
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
        public string FinalOutput { get; set; } = string.Empty;
        public List<ChainStepResult> Steps { get; set; } = new();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 推理链步骤执行结果
    /// </summary>
    public class ChainStepResult
    {
        public string StepName { get; set; } = string.Empty;
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Output { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 推理链请求DTO
    /// </summary>
    public class ChainExecutionRequest
    {
        public string ChainType { get; set; } = string.Empty;
        public string ProjectId { get; set; } = string.Empty;
        public string UserMessage { get; set; } = string.Empty;
        public Dictionary<string, object> Variables { get; set; } = new();
        public int? AIProviderConfigId { get; set; }
    }

    /// <summary>
    /// 推理链响应DTO
    /// </summary>
    public class ChainExecutionResponse
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string ChainName { get; set; } = string.Empty;
        public string FinalOutput { get; set; } = string.Empty;
        public List<ChainStepSummary> Steps { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 推理链步骤摘要
    /// </summary>
    public class ChainStepSummary
    {
        public string StepName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Output { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
    }
}
