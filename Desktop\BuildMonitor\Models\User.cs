using SqlSugar;

namespace BuildMonitor.Models
{
    /// <summary>
    /// 用户模型
    /// </summary>
    [SugarTable("Users")]
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码哈希
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 真实姓名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? RealName { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        public string? Phone { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string? Avatar { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Role { get; set; } = "User";

        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最后登录IP
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 显示名称（用于UI显示）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DisplayName => !string.IsNullOrEmpty(RealName) ? $"{RealName} ({Username})" : Username;
    }
}
