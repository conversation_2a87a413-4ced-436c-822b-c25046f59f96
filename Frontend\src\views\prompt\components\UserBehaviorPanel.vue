<template>
  <div class="user-behavior-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户行为分析</span>
          <el-button type="text" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else class="behavior-content">
        <!-- 用户活跃度概览 -->
        <div class="activity-overview">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="活跃用户数" :value="data.activeUsers" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="新用户数" :value="data.newUsers" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均会话时长" :value="data.avgSessionDuration" suffix="分钟" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="用户留存率" :value="data.retentionRate" suffix="%" :precision="1" />
            </el-col>
          </el-row>
        </div>

        <!-- 用户活跃度趋势 -->
        <div class="activity-trend">
          <h4>用户活跃度趋势</h4>
          <div ref="activityTrendChart" class="chart-container"></div>
        </div>

        <!-- 功能使用分布 -->
        <div class="feature-usage">
          <h4>功能使用分布</h4>
          <div ref="featureUsageChart" class="chart-container"></div>
        </div>

        <!-- 用户行为路径 -->
        <div class="user-journey">
          <h4>用户行为路径</h4>
          <el-table :data="data.userJourney" style="width: 100%">
            <el-table-column prop="path" label="行为路径" />
            <el-table-column prop="userCount" label="用户数量" align="right" />
            <el-table-column prop="percentage" label="占比" align="right">
              <template #default="{ row }">
                {{ row.percentage.toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column prop="avgDuration" label="平均时长" align="right">
              <template #default="{ row }">
                {{ row.avgDuration }}分钟
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 用户反馈统计 -->
        <div class="user-feedback">
          <h4>用户反馈统计</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="feedback-stats">
                <el-statistic title="正面反馈" :value="data.positiveFeedback" />
                <el-statistic title="负面反馈" :value="data.negativeFeedback" />
                <el-statistic title="反馈率" :value="data.feedbackRate" suffix="%" :precision="1" />
              </div>
            </el-col>
            <el-col :span="12">
              <div ref="feedbackChart" class="chart-container-small"></div>
            </el-col>
          </el-row>
        </div>

        <!-- 用户分群分析 -->
        <div class="user-segments">
          <h4>用户分群分析</h4>
          <el-table :data="data.userSegments" style="width: 100%">
            <el-table-column prop="segment" label="用户群体" />
            <el-table-column prop="userCount" label="用户数量" align="right" />
            <el-table-column prop="avgUsage" label="平均使用频率" align="right">
              <template #default="{ row }">
                {{ row.avgUsage }}次/天
              </template>
            </el-table-column>
            <el-table-column prop="satisfaction" label="满意度" align="right">
              <template #default="{ row }">
                <el-rate
                  v-model="row.satisfaction"
                  disabled
                  show-score
                  text-color="#ff9900"
                  :score-template="'{value}/5'"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 行为洞察 -->
        <div class="behavior-insights">
          <h4>行为洞察</h4>
          <el-alert
            v-for="insight in data.insights"
            :key="insight.id"
            :title="insight.title"
            :description="insight.description"
            :type="insight.type"
            show-icon
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  data: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  refresh: []
}>()

const activityTrendChart = ref<HTMLElement>()
const featureUsageChart = ref<HTMLElement>()
const feedbackChart = ref<HTMLElement>()
let activityTrendChartInstance: echarts.ECharts | null = null
let featureUsageChartInstance: echarts.ECharts | null = null
let feedbackChartInstance: echarts.ECharts | null = null

const initCharts = () => {
  initActivityTrendChart()
  initFeatureUsageChart()
  initFeedbackChart()
}

const initActivityTrendChart = () => {
  if (!activityTrendChart.value) return

  activityTrendChartInstance = echarts.init(activityTrendChart.value)
  updateActivityTrendChart()
}

const initFeatureUsageChart = () => {
  if (!featureUsageChart.value) return

  featureUsageChartInstance = echarts.init(featureUsageChart.value)
  updateFeatureUsageChart()
}

const initFeedbackChart = () => {
  if (!feedbackChart.value) return

  feedbackChartInstance = echarts.init(feedbackChart.value)
  updateFeedbackChart()
}

const updateActivityTrendChart = () => {
  if (!activityTrendChartInstance || !props.data?.activityTrend) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['活跃用户', '新用户']
    },
    xAxis: {
      type: 'category',
      data: props.data.activityTrend.map((item: any) => item.date)
    },
    yAxis: {
      type: 'value',
      name: '用户数'
    },
    series: [
      {
        name: '活跃用户',
        type: 'line',
        data: props.data.activityTrend.map((item: any) => item.activeUsers),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '新用户',
        type: 'line',
        data: props.data.activityTrend.map((item: any) => item.newUsers),
        smooth: true,
        lineStyle: { color: '#67C23A' },
        itemStyle: { color: '#67C23A' }
      }
    ]
  }

  activityTrendChartInstance.setOption(option)
}

const updateFeatureUsageChart = () => {
  if (!featureUsageChartInstance || !props.data?.featureUsage) return

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '功能使用',
        type: 'pie',
        radius: '50%',
        data: props.data.featureUsage,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  featureUsageChartInstance.setOption(option)
}

const updateFeedbackChart = () => {
  if (!feedbackChartInstance || !props.data) return

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户反馈',
        type: 'pie',
        radius: '60%',
        data: [
          { value: props.data.positiveFeedback, name: '正面反馈', itemStyle: { color: '#67C23A' } },
          { value: props.data.negativeFeedback, name: '负面反馈', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  feedbackChartInstance.setOption(option)
}

const refreshData = () => {
  emit('refresh')
}

const resizeCharts = () => {
  if (activityTrendChartInstance) {
    activityTrendChartInstance.resize()
  }
  if (featureUsageChartInstance) {
    featureUsageChartInstance.resize()
  }
  if (feedbackChartInstance) {
    feedbackChartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
  
  window.addEventListener('resize', resizeCharts)
})

watch(() => props.data, () => {
  if (activityTrendChartInstance) {
    updateActivityTrendChart()
  }
  if (featureUsageChartInstance) {
    updateFeatureUsageChart()
  }
  if (feedbackChartInstance) {
    updateFeedbackChart()
  }
}, { deep: true })
</script>

<style scoped>
.user-behavior-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.behavior-content {
  padding: 10px 0;
}

.activity-overview {
  margin-bottom: 30px;
}

.activity-trend,
.feature-usage,
.user-journey,
.user-feedback,
.user-segments {
  margin-bottom: 30px;
}

.activity-trend h4,
.feature-usage h4,
.user-journey h4,
.user-feedback h4,
.user-segments h4,
.behavior-insights h4 {
  margin-bottom: 15px;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container-small {
  width: 100%;
  height: 200px;
}

.feedback-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
</style>
