using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Prompt评价实体 - 记录用户对提示词模板的评价
/// 功能: 收集用户对模板效果的评价和建议
/// 支持: 评分统计、反馈分析、模板优化
/// </summary>
[SugarTable("PromptRatings", "AI提示词评价表")]
public class PromptRating : BaseEntity
{
    /// <summary>
    /// 关联的模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的模板ID")]
    public int TemplateId { get; set; }

    /// <summary>
    /// 评价用户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "评价用户ID")]
    public int UserId { get; set; }

    /// <summary>
    /// 关联的使用统计ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的使用统计ID")]
    public int? UsageStatsId { get; set; }

    /// <summary>
    /// 总体评分（1-5分）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "总体评分")]
    public int OverallRating { get; set; }

    /// <summary>
    /// 准确性评分（1-5分）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "准确性评分")]
    public int? AccuracyRating { get; set; }

    /// <summary>
    /// 实用性评分（1-5分）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "实用性评分")]
    public int? UsefulnessRating { get; set; }

    /// <summary>
    /// 易用性评分（1-5分）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "易用性评分")]
    public int? EaseOfUseRating { get; set; }

    /// <summary>
    /// 文字反馈
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "文字反馈")]
    public string? Feedback { get; set; }

    /// <summary>
    /// 改进建议
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "改进建议")]
    public string? Suggestions { get; set; }

    /// <summary>
    /// 评价标签，多个用逗号分隔
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "评价标签")]
    public string? Tags { get; set; }

    /// <summary>
    /// 是否推荐给其他用户
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "是否推荐给其他用户")]
    public bool? WouldRecommend { get; set; }

    /// <summary>
    /// 评价时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "评价时间")]
    public DateTime RatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的模板 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptTemplate? Template { get; set; }

    /// <summary>
    /// 评价用户 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }

    /// <summary>
    /// 关联的使用统计 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptUsageStats? UsageStats { get; set; }
}
