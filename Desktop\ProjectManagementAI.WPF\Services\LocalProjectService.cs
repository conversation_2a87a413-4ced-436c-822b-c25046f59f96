using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;
using Microsoft.Extensions.Logging;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 本地项目服务实现
    /// </summary>
    public class LocalProjectService : ILocalProjectService
    {
        private readonly ILogger<LocalProjectService> _logger;
        private readonly IAIService _aiService;

        // 支持的项目文件模式
        private readonly Dictionary<ProjectType, string[]> _projectPatterns = new()
        {
            { ProjectType.DotNetCore, new[] { "*.csproj", "*.sln" } },
            { ProjectType.DotNetFramework, new[] { "*.csproj", "*.sln", "*.vbproj" } },
            { ProjectType.NodeJs, new[] { "package.json" } },
            { ProjectType.Python, new[] { "requirements.txt", "setup.py", "pyproject.toml", "Pipfile" } },
            { ProjectType.Java, new[] { "pom.xml", "build.gradle", "build.gradle.kts" } },
            { ProjectType.React, new[] { "package.json" } },
            { ProjectType.Vue, new[] { "package.json", "vue.config.js" } },
            { ProjectType.Angular, new[] { "angular.json", "package.json" } },
            { ProjectType.Flutter, new[] { "pubspec.yaml" } },
            { ProjectType.Unity, new[] { "*.unity", "ProjectSettings" } },
            { ProjectType.Go, new[] { "go.mod", "go.sum" } },
            { ProjectType.Rust, new[] { "Cargo.toml" } },
            { ProjectType.PHP, new[] { "composer.json" } },
            { ProjectType.Ruby, new[] { "Gemfile", "*.gemspec" } }
        };

        // 代码文件扩展名
        private readonly Dictionary<string, string> _languageExtensions = new()
        {
            { ".cs", "C#" },
            { ".vb", "VB.NET" },
            { ".js", "JavaScript" },
            { ".ts", "TypeScript" },
            { ".jsx", "React" },
            { ".tsx", "React TypeScript" },
            { ".vue", "Vue" },
            { ".py", "Python" },
            { ".java", "Java" },
            { ".kt", "Kotlin" },
            { ".go", "Go" },
            { ".rs", "Rust" },
            { ".cpp", "C++" },
            { ".c", "C" },
            { ".h", "C/C++ Header" },
            { ".php", "PHP" },
            { ".rb", "Ruby" },
            { ".swift", "Swift" },
            { ".dart", "Dart" },
            { ".html", "HTML" },
            { ".css", "CSS" },
            { ".scss", "SCSS" },
            { ".less", "LESS" },
            { ".xml", "XML" },
            { ".json", "JSON" },
            { ".yaml", "YAML" },
            { ".yml", "YAML" },
            { ".md", "Markdown" },
            { ".sql", "SQL" }
        };

        public LocalProjectService(ILogger<LocalProjectService> logger, IAIService aiService)
        {
            _logger = logger;
            _aiService = aiService;
        }

        public async Task<IEnumerable<LocalProject>> ScanProjectsAsync(string rootPath)
        {
            try
            {
                _logger.LogInformation("开始扫描目录: {RootPath}", rootPath);
                
                if (!Directory.Exists(rootPath))
                {
                    _logger.LogWarning("目录不存在: {RootPath}", rootPath);
                    return Enumerable.Empty<LocalProject>();
                }

                var projects = new List<LocalProject>();
                var directories = Directory.GetDirectories(rootPath, "*", SearchOption.TopDirectoryOnly);

                foreach (var directory in directories)
                {
                    try
                    {
                        var project = await AnalyzeProjectAsync(directory);
                        if (project.Type != ProjectType.Unknown)
                        {
                            projects.Add(project);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "分析项目失败: {Directory}", directory);
                    }
                }

                // 也检查根目录本身是否是项目
                try
                {
                    var rootProject = await AnalyzeProjectAsync(rootPath);
                    if (rootProject.Type != ProjectType.Unknown)
                    {
                        projects.Insert(0, rootProject);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "分析根目录项目失败: {RootPath}", rootPath);
                }

                _logger.LogInformation("扫描完成，发现 {Count} 个项目", projects.Count);
                return projects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描项目失败");
                throw;
            }
        }

        public async Task<LocalProject> AnalyzeProjectAsync(string projectPath)
        {
            try
            {
                var project = new LocalProject
                {
                    Name = Path.GetFileName(projectPath),
                    Path = projectPath,
                    Type = DetectProjectType(projectPath),
                    LastModified = Directory.GetLastWriteTime(projectPath)
                };

                // 获取项目大小
                project.SizeInBytes = await GetDirectorySizeAsync(projectPath);

                // 获取代码统计
                project.Statistics = await GetCodeStatisticsAsync(projectPath);

                // 检测技术栈
                project.TechnologyStack = DetectTechnologyStack(projectPath, project.Type).ToList();

                // 获取依赖信息
                project.Dependencies = (await GetDependenciesAsync(projectPath)).ToList();

                // 检测框架
                project.Framework = DetectFramework(projectPath, project.Type);

                // 检测主要编程语言
                project.Language = DetectPrimaryLanguage(project.Statistics);

                // 生成描述
                project.Description = GenerateProjectDescription(project);

                // 计算复杂度和质量评分
                project.ComplexityScore = CalculateComplexityScore(project);
                project.QualityScore = CalculateQualityScore(project);

                // 生成问题和建议
                project.Issues = DetectIssues(project).ToList();
                project.Recommendations = GenerateRecommendations(project).ToList();

                return project;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析项目失败: {ProjectPath}", projectPath);
                throw;
            }
        }

        public ProjectType DetectProjectType(string projectPath)
        {
            try
            {
                foreach (var kvp in _projectPatterns)
                {
                    foreach (var pattern in kvp.Value)
                    {
                        if (Directory.GetFiles(projectPath, pattern, SearchOption.TopDirectoryOnly).Any())
                        {
                            // 特殊处理：区分不同的JavaScript框架
                            if (kvp.Key == ProjectType.NodeJs && File.Exists(Path.Combine(projectPath, "package.json")))
                            {
                                return DetectJavaScriptFramework(projectPath);
                            }
                            return kvp.Key;
                        }
                    }
                }

                return ProjectType.Unknown;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检测项目类型失败: {ProjectPath}", projectPath);
                return ProjectType.Unknown;
            }
        }

        private ProjectType DetectJavaScriptFramework(string projectPath)
        {
            try
            {
                var packageJsonPath = Path.Combine(projectPath, "package.json");
                if (!File.Exists(packageJsonPath)) return ProjectType.NodeJs;

                var packageJson = File.ReadAllText(packageJsonPath);
                var packageData = JsonSerializer.Deserialize<JsonElement>(packageJson);

                if (packageData.TryGetProperty("dependencies", out var deps) ||
                    packageData.TryGetProperty("devDependencies", out deps))
                {
                    var depsString = deps.ToString().ToLower();
                    
                    if (depsString.Contains("react")) return ProjectType.React;
                    if (depsString.Contains("vue")) return ProjectType.Vue;
                    if (depsString.Contains("@angular")) return ProjectType.Angular;
                }

                // 检查特定配置文件
                if (File.Exists(Path.Combine(projectPath, "angular.json"))) return ProjectType.Angular;
                if (File.Exists(Path.Combine(projectPath, "vue.config.js"))) return ProjectType.Vue;

                return ProjectType.NodeJs;
            }
            catch
            {
                return ProjectType.NodeJs;
            }
        }

        public async Task<CodeStatistics> GetCodeStatisticsAsync(string projectPath)
        {
            var stats = new CodeStatistics();
            
            try
            {
                var files = Directory.GetFiles(projectPath, "*", SearchOption.AllDirectories)
                    .Where(f => !IsIgnoredPath(f))
                    .ToArray();

                stats.TotalFiles = files.Length;

                foreach (var file in files)
                {
                    var extension = Path.GetExtension(file).ToLower();
                    
                    // 统计文件类型
                    if (!stats.FilesByExtension.ContainsKey(extension))
                        stats.FilesByExtension[extension] = 0;
                    stats.FilesByExtension[extension]++;

                    // 只分析代码文件
                    if (_languageExtensions.ContainsKey(extension))
                    {
                        stats.CodeFiles++;
                        var language = _languageExtensions[extension];
                        
                        try
                        {
                            var lines = await File.ReadAllLinesAsync(file);
                            var lineStats = AnalyzeLines(lines);
                            
                            stats.TotalLines += lineStats.Total;
                            stats.CodeLines += lineStats.Code;
                            stats.CommentLines += lineStats.Comments;
                            stats.BlankLines += lineStats.Blank;

                            if (!stats.LinesByLanguage.ContainsKey(language))
                                stats.LinesByLanguage[language] = 0;
                            stats.LinesByLanguage[language] += lineStats.Code;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "分析文件失败: {File}", file);
                        }
                    }
                }

                // 计算比率
                stats.CommentRatio = stats.TotalLines > 0 ? (double)stats.CommentLines / stats.TotalLines : 0;
                stats.CodeDensity = stats.TotalLines > 0 ? (double)stats.CodeLines / stats.TotalLines : 0;

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取代码统计失败: {ProjectPath}", projectPath);
                return stats;
            }
        }

        private (int Total, int Code, int Comments, int Blank) AnalyzeLines(string[] lines)
        {
            int total = lines.Length;
            int code = 0;
            int comments = 0;
            int blank = 0;

            foreach (var line in lines)
            {
                var trimmed = line.Trim();
                if (string.IsNullOrEmpty(trimmed))
                {
                    blank++;
                }
                else if (IsCommentLine(trimmed))
                {
                    comments++;
                }
                else
                {
                    code++;
                }
            }

            return (total, code, comments, blank);
        }

        private bool IsCommentLine(string line)
        {
            return line.StartsWith("//") || line.StartsWith("/*") || line.StartsWith("*") ||
                   line.StartsWith("#") || line.StartsWith("<!--") || line.StartsWith("'") ||
                   line.StartsWith("\"\"\"") || line.StartsWith("'''");
        }

        private bool IsIgnoredPath(string path)
        {
            var ignoredPaths = new[] { "bin", "obj", "node_modules", ".git", ".vs", ".vscode", 
                                     "packages", "target", "build", "dist", "__pycache__", 
                                     ".pytest_cache", "coverage", ".nyc_output" };
            
            return ignoredPaths.Any(ignored => path.Contains(Path.DirectorySeparatorChar + ignored + Path.DirectorySeparatorChar) ||
                                              path.EndsWith(Path.DirectorySeparatorChar + ignored));
        }

        public async Task<IEnumerable<ProjectDependency>> GetDependenciesAsync(string projectPath)
        {
            var dependencies = new List<ProjectDependency>();

            try
            {
                // .NET 项目
                await ExtractDotNetDependencies(projectPath, dependencies);
                
                // Node.js 项目
                await ExtractNodeJsDependencies(projectPath, dependencies);
                
                // Python 项目
                await ExtractPythonDependencies(projectPath, dependencies);
                
                // Java 项目
                await ExtractJavaDependencies(projectPath, dependencies);

                return dependencies;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取依赖信息失败: {ProjectPath}", projectPath);
                return dependencies;
            }
        }

        private async Task ExtractDotNetDependencies(string projectPath, List<ProjectDependency> dependencies)
        {
            var csprojFiles = Directory.GetFiles(projectPath, "*.csproj", SearchOption.TopDirectoryOnly);
            
            foreach (var csprojFile in csprojFiles)
            {
                try
                {
                    var content = await File.ReadAllTextAsync(csprojFile);
                    var doc = XDocument.Parse(content);
                    
                    var packageRefs = doc.Descendants("PackageReference");
                    foreach (var packageRef in packageRefs)
                    {
                        var name = packageRef.Attribute("Include")?.Value;
                        var version = packageRef.Attribute("Version")?.Value;
                        
                        if (!string.IsNullOrEmpty(name))
                        {
                            dependencies.Add(new ProjectDependency
                            {
                                Name = name,
                                Version = version ?? "Unknown",
                                Type = "NuGet",
                                IsDevDependency = false
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析.csproj文件失败: {File}", csprojFile);
                }
            }
        }

        private async Task ExtractNodeJsDependencies(string projectPath, List<ProjectDependency> dependencies)
        {
            var packageJsonPath = Path.Combine(projectPath, "package.json");
            if (!File.Exists(packageJsonPath)) return;

            try
            {
                var content = await File.ReadAllTextAsync(packageJsonPath);
                var packageData = JsonSerializer.Deserialize<JsonElement>(content);

                // 生产依赖
                if (packageData.TryGetProperty("dependencies", out var deps))
                {
                    foreach (var dep in deps.EnumerateObject())
                    {
                        dependencies.Add(new ProjectDependency
                        {
                            Name = dep.Name,
                            Version = dep.Value.GetString() ?? "Unknown",
                            Type = "npm",
                            IsDevDependency = false
                        });
                    }
                }

                // 开发依赖
                if (packageData.TryGetProperty("devDependencies", out var devDeps))
                {
                    foreach (var dep in devDeps.EnumerateObject())
                    {
                        dependencies.Add(new ProjectDependency
                        {
                            Name = dep.Name,
                            Version = dep.Value.GetString() ?? "Unknown",
                            Type = "npm",
                            IsDevDependency = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析package.json失败: {File}", packageJsonPath);
            }
        }

        private async Task ExtractPythonDependencies(string projectPath, List<ProjectDependency> dependencies)
        {
            var requirementsPath = Path.Combine(projectPath, "requirements.txt");
            if (File.Exists(requirementsPath))
            {
                try
                {
                    var lines = await File.ReadAllLinesAsync(requirementsPath);
                    foreach (var line in lines)
                    {
                        var trimmed = line.Trim();
                        if (string.IsNullOrEmpty(trimmed) || trimmed.StartsWith("#")) continue;

                        var parts = trimmed.Split(new[] { "==", ">=", "<=", "~=", "!=" }, StringSplitOptions.RemoveEmptyEntries);
                        var name = parts[0].Trim();
                        var version = parts.Length > 1 ? parts[1].Trim() : "Unknown";

                        dependencies.Add(new ProjectDependency
                        {
                            Name = name,
                            Version = version,
                            Type = "pip",
                            IsDevDependency = false
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析requirements.txt失败: {File}", requirementsPath);
                }
            }
        }

        private async Task ExtractJavaDependencies(string projectPath, List<ProjectDependency> dependencies)
        {
            // Maven pom.xml
            var pomPath = Path.Combine(projectPath, "pom.xml");
            if (File.Exists(pomPath))
            {
                try
                {
                    var content = await File.ReadAllTextAsync(pomPath);
                    var doc = XDocument.Parse(content);
                    var ns = doc.Root?.GetDefaultNamespace();
                    
                    var deps = doc.Descendants(ns + "dependency");
                    foreach (var dep in deps)
                    {
                        var groupId = dep.Element(ns + "groupId")?.Value;
                        var artifactId = dep.Element(ns + "artifactId")?.Value;
                        var version = dep.Element(ns + "version")?.Value;
                        var scope = dep.Element(ns + "scope")?.Value;

                        if (!string.IsNullOrEmpty(artifactId))
                        {
                            dependencies.Add(new ProjectDependency
                            {
                                Name = $"{groupId}:{artifactId}",
                                Version = version ?? "Unknown",
                                Type = "Maven",
                                IsDevDependency = scope == "test"
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析pom.xml失败: {File}", pomPath);
                }
            }
        }

        private IEnumerable<string> DetectTechnologyStack(string projectPath, ProjectType projectType)
        {
            var technologies = new List<string>();

            try
            {
                // 基于项目类型添加基础技术
                technologies.Add(projectType.ToString());

                // 检测特定技术
                if (Directory.GetFiles(projectPath, "Dockerfile", SearchOption.TopDirectoryOnly).Any())
                    technologies.Add("Docker");

                if (Directory.GetFiles(projectPath, "docker-compose.yml", SearchOption.TopDirectoryOnly).Any() ||
                    Directory.GetFiles(projectPath, "docker-compose.yaml", SearchOption.TopDirectoryOnly).Any())
                    technologies.Add("Docker Compose");

                if (Directory.GetDirectories(projectPath, ".git", SearchOption.TopDirectoryOnly).Any())
                    technologies.Add("Git");

                if (Directory.GetFiles(projectPath, "*.yml", SearchOption.AllDirectories).Any(f => f.Contains("github") || f.Contains("azure-pipelines")))
                    technologies.Add("CI/CD");

                // 检测数据库相关文件
                var files = Directory.GetFiles(projectPath, "*", SearchOption.AllDirectories);
                if (files.Any(f => f.Contains("appsettings") && File.ReadAllText(f).Contains("ConnectionString")))
                    technologies.Add("Database");

                if (files.Any(f => Path.GetExtension(f).ToLower() == ".sql"))
                    technologies.Add("SQL");

                return technologies.Distinct();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检测技术栈失败: {ProjectPath}", projectPath);
                return technologies;
            }
        }

        private string DetectFramework(string projectPath, ProjectType projectType)
        {
            try
            {
                switch (projectType)
                {
                    case ProjectType.DotNetCore:
                    case ProjectType.DotNetFramework:
                        return DetectDotNetFramework(projectPath);
                    
                    case ProjectType.NodeJs:
                    case ProjectType.React:
                    case ProjectType.Vue:
                    case ProjectType.Angular:
                        return DetectNodeFramework(projectPath);
                    
                    default:
                        return "Unknown";
                }
            }
            catch
            {
                return "Unknown";
            }
        }

        private string DetectDotNetFramework(string projectPath)
        {
            var csprojFiles = Directory.GetFiles(projectPath, "*.csproj", SearchOption.TopDirectoryOnly);
            
            foreach (var csprojFile in csprojFiles)
            {
                try
                {
                    var content = File.ReadAllText(csprojFile);
                    var doc = XDocument.Parse(content);
                    
                    var targetFramework = doc.Descendants("TargetFramework").FirstOrDefault()?.Value;
                    if (!string.IsNullOrEmpty(targetFramework))
                        return targetFramework;
                        
                    var targetFrameworks = doc.Descendants("TargetFrameworks").FirstOrDefault()?.Value;
                    if (!string.IsNullOrEmpty(targetFrameworks))
                        return targetFrameworks.Split(';')[0];
                }
                catch { }
            }
            
            return "Unknown";
        }

        private string DetectNodeFramework(string projectPath)
        {
            var packageJsonPath = Path.Combine(projectPath, "package.json");
            if (!File.Exists(packageJsonPath)) return "Unknown";

            try
            {
                var content = File.ReadAllText(packageJsonPath);
                var packageData = JsonSerializer.Deserialize<JsonElement>(content);

                if (packageData.TryGetProperty("dependencies", out var deps))
                {
                    var depsString = deps.ToString().ToLower();
                    
                    if (depsString.Contains("\"react\"")) return "React";
                    if (depsString.Contains("\"vue\"")) return "Vue.js";
                    if (depsString.Contains("\"@angular/core\"")) return "Angular";
                    if (depsString.Contains("\"express\"")) return "Express.js";
                    if (depsString.Contains("\"next\"")) return "Next.js";
                    if (depsString.Contains("\"nuxt\"")) return "Nuxt.js";
                }

                return "Node.js";
            }
            catch
            {
                return "Unknown";
            }
        }

        private string DetectPrimaryLanguage(CodeStatistics statistics)
        {
            if (!statistics.LinesByLanguage.Any())
                return "Unknown";

            return statistics.LinesByLanguage
                .OrderByDescending(kvp => kvp.Value)
                .First()
                .Key;
        }

        private string GenerateProjectDescription(LocalProject project)
        {
            var description = $"这是一个{project.Type}项目";
            
            if (!string.IsNullOrEmpty(project.Language) && project.Language != "Unknown")
                description += $"，主要使用{project.Language}开发";
                
            if (!string.IsNullOrEmpty(project.Framework) && project.Framework != "Unknown")
                description += $"，基于{project.Framework}框架";

            if (project.Statistics.CodeLines > 0)
                description += $"，包含{project.Statistics.CodeLines:N0}行代码";

            if (project.Dependencies.Any())
                description += $"，使用了{project.Dependencies.Count}个依赖包";

            return description + "。";
        }

        private double CalculateComplexityScore(LocalProject project)
        {
            double score = 0.0;

            // 基于代码行数
            if (project.Statistics.CodeLines > 0)
            {
                if (project.Statistics.CodeLines > 100000) score += 0.4;
                else if (project.Statistics.CodeLines > 50000) score += 0.3;
                else if (project.Statistics.CodeLines > 10000) score += 0.2;
                else score += 0.1;
            }

            // 基于文件数量
            if (project.Statistics.CodeFiles > 500) score += 0.3;
            else if (project.Statistics.CodeFiles > 100) score += 0.2;
            else if (project.Statistics.CodeFiles > 50) score += 0.1;

            // 基于依赖数量
            if (project.Dependencies.Count > 50) score += 0.2;
            else if (project.Dependencies.Count > 20) score += 0.1;

            // 基于技术栈复杂度
            if (project.TechnologyStack.Count > 5) score += 0.1;

            return Math.Min(score, 1.0);
        }

        private double CalculateQualityScore(LocalProject project)
        {
            double score = 0.5; // 基础分数

            // 注释比例
            if (project.Statistics.CommentRatio > 0.2) score += 0.2;
            else if (project.Statistics.CommentRatio > 0.1) score += 0.1;

            // 代码密度
            if (project.Statistics.CodeDensity > 0.6) score += 0.1;

            // 有README文件
            if (File.Exists(Path.Combine(project.Path, "README.md")) ||
                File.Exists(Path.Combine(project.Path, "README.txt")))
                score += 0.1;

            // 有测试文件
            var testFiles = Directory.GetFiles(project.Path, "*test*", SearchOption.AllDirectories);
            if (testFiles.Any()) score += 0.1;

            return Math.Min(score, 1.0);
        }

        private IEnumerable<string> DetectIssues(LocalProject project)
        {
            var issues = new List<string>();

            if (project.Statistics.CommentRatio < 0.05)
                issues.Add("代码注释不足，建议增加注释");

            if (project.Statistics.CodeLines > 50000 && project.Statistics.CodeFiles < 50)
                issues.Add("单个文件代码行数过多，建议拆分");

            if (project.Dependencies.Count > 100)
                issues.Add("依赖包过多，可能存在依赖冗余");

            if (!File.Exists(Path.Combine(project.Path, "README.md")))
                issues.Add("缺少README文档");

            var testFiles = Directory.GetFiles(project.Path, "*test*", SearchOption.AllDirectories);
            if (!testFiles.Any())
                issues.Add("缺少测试文件");

            return issues;
        }

        private IEnumerable<string> GenerateRecommendations(LocalProject project)
        {
            var recommendations = new List<string>();

            if (project.ComplexityScore > 0.7)
                recommendations.Add("项目复杂度较高，建议进行模块化重构");

            if (project.QualityScore < 0.6)
                recommendations.Add("代码质量有待提升，建议增加注释和测试");

            if (project.Dependencies.Count > 50)
                recommendations.Add("定期审查和清理不必要的依赖包");

            if (project.Statistics.CodeLines > 100000)
                recommendations.Add("考虑将大型项目拆分为多个微服务");

            recommendations.Add("定期进行代码审查和重构");
            recommendations.Add("建立完善的CI/CD流程");

            return recommendations;
        }

        private async Task<long> GetDirectorySizeAsync(string directoryPath)
        {
            try
            {
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories)
                    .Where(f => !IsIgnoredPath(f));

                long size = 0;
                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        size += fileInfo.Length;
                    }
                    catch { }
                }

                return size;
            }
            catch
            {
                return 0;
            }
        }
    }
}
