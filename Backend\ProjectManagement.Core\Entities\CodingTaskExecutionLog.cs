using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 编码任务执行日志实体
    /// </summary>
    [SugarTable("CodingTaskExecutionLogs")]
    public class CodingTaskExecutionLog : BaseEntity
    {

        /// <summary>
        /// 编码任务ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CodingTaskId { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string ExecutionType { get; set; } = string.Empty; // Start, Progress, Complete, Fail, Cancel

        /// <summary>
        /// 执行内容
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2000)]
        public string? Content { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2000)]
        public string? Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime ExecutionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 执行人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ExecutedBy { get; set; }



        // 导航属性（不映射到数据库）
        
        /// <summary>
        /// 关联的编码任务
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CodingTask? CodingTask { get; set; }

        /// <summary>
        /// 执行人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Executor { get; set; }
    }
}
