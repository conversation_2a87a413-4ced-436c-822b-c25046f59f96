using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Models;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Services;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;
using System.Security.Claims;
using System.Text;
using ProjectManagement.AI.Models;
using SqlSugar;
using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 需求管理控制器
/// 功能: 处理需求文档、需求对话、需求分析等操作
/// 支持: 需求CRUD、AI需求分析、需求版本管理
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("GeneralApi")]
[Produces("application/json")]
public class RequirementsController : ControllerBase
{
    private readonly ILogger<RequirementsController> _logger;
    private readonly IAIService _aiService;
    private readonly IUserTaskMappingRepository _userTaskMappingRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IUserAIConfigurationRepository _userAIConfigurationRepository;
    private readonly IAIModelConfigurationRepository _aiModelConfigurationRepository;
    private readonly ConversationContextService _conversationContextService;
    private readonly IRequirementConversationRepository _conversationRepository;
    private readonly IRequirementDocumentRepository _requirementDocumentRepository;
    private readonly ISqlSugarClient _db;

    public RequirementsController(
        ILogger<RequirementsController> logger,
        IAIService aiService,
        IUserTaskMappingRepository userTaskMappingRepository,
        IProjectRepository projectRepository,
        IUserAIConfigurationRepository userAIConfigurationRepository,
        IAIModelConfigurationRepository aiModelConfigurationRepository,
        ConversationContextService conversationContextService,
        IRequirementConversationRepository conversationRepository,
        IRequirementDocumentRepository requirementDocumentRepository,
        ISqlSugarClient db
        )
    {
        _logger = logger;
        _aiService = aiService;
        _userTaskMappingRepository = userTaskMappingRepository;
        _projectRepository = projectRepository;
        _userAIConfigurationRepository = userAIConfigurationRepository;
        _aiModelConfigurationRepository = aiModelConfigurationRepository;
        _conversationContextService = conversationContextService;
        _conversationRepository = conversationRepository;
        _requirementDocumentRepository = requirementDocumentRepository;
        _db = db;
    }

    /// <summary>
    /// 获取所有需求文档列表
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="projectId">项目ID过滤（可选）</param>
    /// <param name="status">状态过滤（可选）</param>
    /// <returns>需求文档列表</returns>
    [HttpGet("documents")]
    public async Task<ActionResult<PagedResultDto<RequirementDocumentDto>>> GetAllRequirements(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] int? projectId = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取需求文档列表: 用户 {UserId}, 项目 {ProjectId}, 状态 {Status}", userId, projectId, status);

            // 构建查询条件
            var requirements = await _requirementDocumentRepository.GetAllAsync();

            // 过滤已删除的记录
            requirements = requirements.Where(r => !r.IsDeleted).ToList();

            // 按项目过滤
            if (projectId.HasValue)
            {
                requirements = requirements.Where(r => r.ProjectId == projectId.Value).ToList();
            }

            // 按状态过滤
            if (!string.IsNullOrWhiteSpace(status))
            {
                requirements = requirements.Where(r => r.Status == status).ToList();
            }

            // 排序
            requirements = requirements.OrderByDescending(r => r.CreatedTime).ToList();

            // 分页处理
            var totalCount = requirements.Count;
            var pagedRequirements = requirements
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            // 转换为DTO
            var requirementDtos = pagedRequirements.Select(r => new RequirementDocumentDto
            {
                Id = r.Id,
                Title = r.Title,
                Status = r.Status,
                Version = r.DocumentVersion,
                ProjectId = r.ProjectId,
                CreatedAt = r.CreatedTime,
                UpdatedAt = r.UpdatedTime,
                CreatedByName = "系统用户" // TODO: 从用户表获取真实姓名
            }).ToList();

            var result = new PagedResultDto<RequirementDocumentDto>
            {
                Items = requirementDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            _logger.LogInformation("获取需求文档列表成功: 共 {Count} 个文档", totalCount);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求文档列表时发生错误");
            return StatusCode(500, new { message = "获取需求文档列表失败" });
        }
    }

    /// <summary>
    /// 获取项目的需求文档列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>需求文档列表</returns>
    [HttpGet("projects/{projectId}/documents")]
    public async Task<ActionResult<PagedResultDto<RequirementDocumentDto>>> GetProjectRequirements(
        int projectId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var userId = GetCurrentUserId();

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 查询需求文档
            var requirements = await _requirementDocumentRepository.GetRequirementDocumentsByProjectAsync(projectId);

            // 3. 分页处理
            var totalCount = requirements.Count;
            var pagedRequirements = requirements
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            // 4. 转换为DTO
            var requirementDtos = pagedRequirements.Select(r => new RequirementDocumentDto
            {
                Id = r.Id,
                Title = r.Title,
                Status = r.Status,
                Version = r.DocumentVersion,
                ProjectId = r.ProjectId,
                CreatedAt = r.CreatedTime,
                UpdatedAt = r.UpdatedTime,
                CreatedByName = "系统用户" // TODO: 从用户表获取真实姓名
            }).ToList();

            var result = new PagedResultDto<RequirementDocumentDto>
            {
                Items = requirementDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目需求文档列表时发生错误: {ProjectId}", projectId);
            return StatusCode(500, new { message = "获取需求文档列表失败" });
        }
    }

    /// <summary>
    /// 根据ID获取需求文档详情
    /// </summary>
    /// <param name="id">需求文档ID</param>
    /// <returns>需求文档详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<RequirementDocumentDetailDto>> GetRequirement(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取需求文档详情: {RequirementId}, 用户 {UserId}", id, userId);

            // 查询需求文档
            var requirement = await _requirementDocumentRepository.GetByIdAsync(id);
            if (requirement == null || requirement.IsDeleted)
            {
                return NotFound(new { message = "需求文档不存在" });
            }

            // 转换为DTO
            var result = new RequirementDocumentDetailDto
            {
                Id = requirement.Id,
                Title = requirement.Title,
                Content = requirement.Content ?? string.Empty,
                FunctionalRequirements = requirement.FunctionalRequirements,
                NonFunctionalRequirements = requirement.NonFunctionalRequirements,
                UserStories = requirement.UserStories,
                AcceptanceCriteria = requirement.AcceptanceCriteria,
                Status = requirement.Status,
                Version = requirement.DocumentVersion,
                ProjectId = requirement.ProjectId,
                CreatedAt = requirement.CreatedTime,
                UpdatedAt = requirement.UpdatedTime,
                GeneratedBy = requirement.GeneratedBy,
                ProjectName = "项目名称" // TODO: 从项目表获取真实项目名称
            };

            _logger.LogInformation("获取需求文档详情成功: {RequirementId}", id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求文档详情时发生错误: {RequirementId}", id);
            return StatusCode(500, new { message = "获取需求文档详情失败" });
        }
    }

    /// <summary>
    /// 创建需求文档
    /// </summary>
    /// <param name="request">创建需求文档请求</param>
    /// <returns>创建的需求文档</returns>
    [HttpPost]
    public async Task<ActionResult<RequirementDocumentDetailDto>> CreateRequirement([FromBody] CreateRequirementRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("创建需求文档: 项目 {ProjectId}, 用户 {UserId}", request.ProjectId, userId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 创建需求文档实体
            var requirementDocument = new RequirementDocument
            {
                ProjectId = request.ProjectId,
                Title = request.Title,
                Content = request.Content,
                Status = "Draft",
                DocumentVersion = "1.0",
                GeneratedBy = "Manual",
                CreatedBy = userId,
                CreatedTime = DateTime.Now
            };

            // 3. 保存到数据库
            var savedDocument = await _requirementDocumentRepository.AddAsync(requirementDocument);

            // 4. 构建返回结果
            var result = new RequirementDocumentDetailDto
            {
                Id = savedDocument.Id,
                Title = savedDocument.Title,
                Content = savedDocument.Content,
                Status = savedDocument.Status,
                Version = savedDocument.DocumentVersion,
                ProjectId = savedDocument.ProjectId,
                CreatedAt = savedDocument.CreatedTime,
                GeneratedBy = savedDocument.GeneratedBy
            };

            _logger.LogInformation("需求文档创建成功: {RequirementId}", result.Id);

            return CreatedAtAction(nameof(GetRequirement), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建需求文档时发生错误");
            return StatusCode(500, new { message = "创建需求文档失败" });
        }
    }

    /// <summary>
    /// 更新需求文档
    /// </summary>
    /// <param name="id">需求文档ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新后的需求文档</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<RequirementDocumentDetailDto>> UpdateRequirement(int id, [FromBody] UpdateRequirementRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("更新需求文档: {RequirementId}, 用户 {UserId}", id, userId);

            // 1. 查询需求文档是否存在
            var requirement = await _requirementDocumentRepository.GetByIdAsync(id);
            if (requirement == null || requirement.IsDeleted)
            {
                return NotFound(new { message = "需求文档不存在" });
            }

            // 2. 更新需求文档属性
            if (!string.IsNullOrWhiteSpace(request.Title))
                requirement.Title = request.Title;

            if (!string.IsNullOrWhiteSpace(request.Content))
                requirement.Content = request.Content;

            if (!string.IsNullOrWhiteSpace(request.Status))
            {
                // 验证状态值是否有效
                var validStatuses = new[] { "Draft", "Review", "Approved", "Rejected", "Published" };
                if (!validStatuses.Contains(request.Status))
                {
                    return BadRequest(new { message = $"无效的状态值: {request.Status}。有效状态: {string.Join(", ", validStatuses)}" });
                }
                requirement.Status = request.Status;
            }

            if (!string.IsNullOrWhiteSpace(request.FunctionalRequirements))
                requirement.FunctionalRequirements = request.FunctionalRequirements;

            if (!string.IsNullOrWhiteSpace(request.NonFunctionalRequirements))
                requirement.NonFunctionalRequirements = request.NonFunctionalRequirements;

            if (!string.IsNullOrWhiteSpace(request.UserStories))
                requirement.UserStories = request.UserStories;

            if (!string.IsNullOrWhiteSpace(request.AcceptanceCriteria))
                requirement.AcceptanceCriteria = request.AcceptanceCriteria;

            requirement.UpdatedBy = userId;
            requirement.UpdatedTime = DateTime.Now;

            // 3. 保存更新
            var updatedRequirement = await _requirementDocumentRepository.UpdateAsync(requirement);

            return Ok(requirement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新需求文档时发生错误: {RequirementId}", id);
            return StatusCode(500, new { message = "更新需求文档失败" });
        }
    }

    /// <summary>
    /// 从AI对话保存需求文档
    /// </summary>
    /// <param name="request">保存需求文档请求</param>
    /// <returns>创建的需求文档</returns>
    [HttpPost("save-from-chat")]
    public async Task<ActionResult<RequirementDocumentDetailDto>> SaveRequirementFromChat([FromBody] SaveRequirementFromChatRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("从AI对话保存需求文档: 项目 {ProjectId}, 用户 {UserId}, 对话 {ConversationId}",
                request.ProjectId, userId, request.ConversationId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 构建需求文档内容
            var documentContent = $@"# {request.Title}
## 用户需求描述
{request.UserMessage}

## AI分析结果
{request.AiContent}";

            // 如果有结构化的需求信息，添加到内容中
            if (!string.IsNullOrWhiteSpace(request.FunctionalRequirements))
            {
                documentContent += $@"

## 功能性需求
{request.FunctionalRequirements}";
            }

            if (!string.IsNullOrWhiteSpace(request.NonFunctionalRequirements))
            {
                documentContent += $@"

## 非功能性需求
{request.NonFunctionalRequirements}";
            }

            if (!string.IsNullOrWhiteSpace(request.UserStories))
            {
                documentContent += $@"

## 用户故事
{request.UserStories}";
            }

            if (!string.IsNullOrWhiteSpace(request.AcceptanceCriteria))
            {
                documentContent += $@"

## 验收标准
{request.AcceptanceCriteria}";
            }

            // 3. 创建需求文档实体
            var requirementDocument = new RequirementDocument
            {
                ProjectId = request.ProjectId,
                Title = request.Title,
                Content = documentContent,
                FunctionalRequirements = request.FunctionalRequirements,
                NonFunctionalRequirements = request.NonFunctionalRequirements,
                UserStories = request.UserStories,
                AcceptanceCriteria = request.AcceptanceCriteria,
                Status = "Draft",
                DocumentVersion = "1.0",
                GeneratedBy = "AI", // 标记为AI生成
                CreatedBy = userId,
                CreatedTime = DateTime.Now
            };

            // 4. 保存到数据库
            var savedDocument = await _requirementDocumentRepository.AddAsync(requirementDocument);

            // 5. 构建返回结果
            var result = new RequirementDocumentDetailDto
            {
                Id = savedDocument.Id,
                Title = savedDocument.Title,
                Content = savedDocument.Content,
                FunctionalRequirements = savedDocument.FunctionalRequirements,
                NonFunctionalRequirements = savedDocument.NonFunctionalRequirements,
                UserStories = savedDocument.UserStories,
                AcceptanceCriteria = savedDocument.AcceptanceCriteria,
                Status = savedDocument.Status,
                Version = savedDocument.DocumentVersion,
                ProjectId = savedDocument.ProjectId,
                CreatedAt = savedDocument.CreatedTime,
                GeneratedBy = savedDocument.GeneratedBy
            };

            _logger.LogInformation("从AI对话保存需求文档成功: {RequirementId}, 对话 {ConversationId}",
                result.Id, request.ConversationId);

            return CreatedAtAction(nameof(GetRequirement), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从AI对话保存需求文档时发生错误");
            return StatusCode(500, new { message = "保存需求文档失败" });
        }
    }

    /// <summary>
    /// 删除需求文档
    /// </summary>
    /// <param name="id">需求文档ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteRequirement(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("删除需求文档: {RequirementId}, 用户 {UserId}", id, userId);

            // 查询需求文档是否存在
            var requirement = await _requirementDocumentRepository.GetByIdAsync(id);
            if (requirement == null || requirement.IsDeleted)
            {
                return NotFound(new { message = "需求文档不存在" });
            }

            // 软删除需求文档
            await _requirementDocumentRepository.DeleteAsync(id);

            _logger.LogInformation("需求文档删除成功: {RequirementId}", id);

            return Ok(new { message = "需求文档删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除需求文档时发生错误: {RequirementId}", id);
            return StatusCode(500, new { message = "删除需求文档失败" });
        }
    }

    /// <summary>
    /// 开始需求对话
    /// </summary>
    /// <param name="request">需求对话请求</param>
    /// <returns>对话ID</returns>
    [HttpPost("conversations")]
    public async Task<ActionResult<RequirementConversationResponseDto>> StartRequirementConversation([FromBody] StartRequirementConversationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("开始需求对话: 项目 {ProjectId}, 用户 {UserId}", request.ProjectId, userId);

            // TODO: 实现需求对话逻辑
            // 模拟响应
            var result = new RequirementConversationResponseDto
            {
                ConversationId = Guid.NewGuid().ToString(),
                ProjectId = request.ProjectId,
                Status = "Active",
                CreatedAt = DateTime.UtcNow
            };

            _logger.LogInformation("需求对话创建成功: {ConversationId}", result.ConversationId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始需求对话时发生错误");
            return StatusCode(500, new { message = "开始需求对话失败" });
        }
    }

    /// <summary>
    /// 发送需求对话消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="request">消息请求</param>
    /// <returns>AI回复</returns>
    [HttpPost("conversations/{conversationId}/messages")]
    public async Task<ActionResult<RequirementMessageResponseDto>> SendRequirementMessage(string conversationId, [FromBody] SendRequirementMessageRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("发送需求对话消息: 对话 {ConversationId}, 用户 {UserId}", conversationId, userId);

            // 获取项目信息
            Project? projectInfo = null;
            if (request.ProjectId.HasValue)
            {
                projectInfo = await _projectRepository.GetByIdAsync(request.ProjectId.Value);
                if (projectInfo == null)
                {
                    _logger.LogWarning("未找到项目信息，项目ID: {ProjectId}", request.ProjectId.Value);
                }
            }

            // 获取AI配置 - 优先使用指定的AI提供商配置ID，否则使用默认配置
            AIModelConfiguration? selectedAIConfig = null;
            if (request.AIProviderConfigId.HasValue)
            {
                // 使用指定的AI模型配置
                selectedAIConfig = await _aiModelConfigurationRepository.GetByIdAsync(request.AIProviderConfigId.Value);
                if (selectedAIConfig == null || !selectedAIConfig.IsActive)
                {
                    _logger.LogWarning("指定的AI模型配置不存在或未激活: {ConfigId}",
                        request.AIProviderConfigId.Value);
                    return BadRequest(new { message = "指定的AI模型配置不存在或未激活" });
                }
                _logger.LogInformation("使用指定的AI模型配置: {ConfigId}, 模型: {ModelName}",
                    selectedAIConfig.Id, selectedAIConfig.ModelName);
            }
            else
            {
                // 获取用户的默认AI配置
                var userConfigs = await _aiModelConfigurationRepository.GetByUserIdAsync(userId);
                selectedAIConfig = userConfigs.FirstOrDefault(c => c.IsActive);

                if (selectedAIConfig == null)
                {
                    _logger.LogWarning("用户没有可用的AI配置: {UserId}", userId);
                    return BadRequest(new { message = "请先配置AI提供商" });
                }

                _logger.LogInformation("使用默认AI配置: {ConfigId}, 模型: {ModelName}",
                    selectedAIConfig.Id, selectedAIConfig.ModelName);
            }



            // 使用对话上下文服务构建包含历史的提示词
            var maxTokens = 4000; // 默认token限制
            string prompt;

            // 获取用户AI配置并确定最大token数
            if (selectedAIConfig != null && !string.IsNullOrEmpty(selectedAIConfig.ModelParameters))
            {
                try
                {
                    var modelParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(selectedAIConfig.ModelParameters);
                    if (modelParams?.TryGetValue("MaxTokens", out var maxTokensValue) == true)
                    {
                        int.TryParse(maxTokensValue.ToString(), out maxTokens);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析用户AI配置参数失败，使用默认值");
                }
            }

            // 使用对话上下文服务构建包含历史的提示词
            prompt = await _conversationContextService.BuildContextualPromptAsync(
                conversationId, request.Message, projectInfo, maxTokens);

            // 调用AI服务生成回复
            string aiResponse;
            try
            {
                if (selectedAIConfig != null)
                {
                    // 解析用户AI配置的模型参数
                    var responseMaxTokens = 1000;
                    var temperature = 0.7f;

                    if (!string.IsNullOrEmpty(selectedAIConfig.ModelParameters))
                    {
                        try
                        {
                            var modelParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(selectedAIConfig.ModelParameters);
                            if (modelParams != null)
                            {
                                if (modelParams.TryGetValue("MaxTokens", out var maxTokensValue))
                                    int.TryParse(maxTokensValue.ToString(), out responseMaxTokens);

                                if (modelParams.TryGetValue("Temperature", out var temperatureValue))
                                    float.TryParse(temperatureValue.ToString(), out temperature);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "解析用户AI配置参数失败，使用默认值");
                        }
                    }

                    // 根据模型名称推断提供商
                    string providerName = GetProviderFromModelName(selectedAIConfig.ModelName);

                    // 使用选定的AI配置
                    var config = new AIModelConfig
                    {
                        Provider = providerName,
                        Model = selectedAIConfig.ModelName,
                        ApiKey = selectedAIConfig.ApiKey ?? string.Empty,
                        Endpoint = selectedAIConfig.ApiEndpoint ?? string.Empty,
                        MaxTokens = responseMaxTokens,
                        Temperature = temperature
                    };

                    _logger.LogInformation("使用AI配置: {Provider}, {Model}, 配置ID: {ConfigId}",
                        providerName, selectedAIConfig.ModelName, selectedAIConfig.Id);

                    aiResponse = await _aiService.GenerateTextAsync(prompt, config);

                    // 注意：AIModelConfiguration 不包含使用统计功能，如需统计请使用 UserAIConfiguration
                }
                else
                {
                    _logger.LogWarning("没有找到可用的AI配置，使用默认配置，用户: {UserId}", userId);
                    aiResponse = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
                }
            }
            catch (Exception aiEx)
            {
                _logger.LogError(aiEx, "AI服务调用失败，使用备用回复");
                aiResponse = GetFallbackResponse(request.Message, projectInfo);
            }

            // 保存对话记录到数据库
            try
            {
                await _conversationContextService.SaveConversationAsync(
                    conversationId,
                    request.ProjectId, // 直接传入，可以为NULL
                    userId,
                    request.Message,
                    aiResponse,
                    "Requirement");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存对话记录失败: {ConversationId}", conversationId);
                // 不影响主流程，继续返回结果
            }

            var result = new RequirementMessageResponseDto
            {
                MessageId = Guid.NewGuid().ToString(),
                ConversationId = conversationId,
                UserMessage = request.Message,
                AIResponse = aiResponse,
                Timestamp = DateTime.UtcNow,
                SuggestedQuestions = GenerateSuggestedQuestions(request.Message),
                ExtractedRequirements = ExtractRequirements(request.Message)
            };

            _logger.LogInformation("需求对话消息处理完成: {ConversationId}", conversationId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送需求对话消息时发生错误: {ConversationId}", conversationId);
            return StatusCode(500, new { message = "发送消息失败" });
        }
    }

    /// <summary>
    /// 获取对话历史
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <returns>对话历史列表</returns>
    [HttpGet("conversations/{conversationId}/messages")]
    public async Task<ActionResult<List<ConversationMessageDto>>> GetConversationHistory(string conversationId)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取对话历史: 对话 {ConversationId}, 用户 {UserId}", conversationId, userId);

            var history = await _conversationRepository.GetConversationHistoryAsync(conversationId, 50);

            var result = history.OrderBy(x => x.Timestamp).Select(x => new ConversationMessageDto
            {
                MessageId = x.Id.ToString(),
                ConversationId = x.ConversationId ?? conversationId,
                UserMessage = x.UserMessage,
                AIResponse = x.AIResponse ?? "",
                MessageType = x.MessageType,
                Timestamp = x.Timestamp ?? DateTime.UtcNow
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话历史失败: {ConversationId}", conversationId);
            return StatusCode(500, new { message = "获取对话历史失败" });
        }
    }

    /// <summary>
    /// 获取对话token使用统计
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <returns>token统计信息</returns>
    [HttpGet("conversations/{conversationId}/stats")]
    public async Task<ActionResult<ConversationTokenStats>> GetConversationStats(string conversationId)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取对话统计: 对话 {ConversationId}, 用户 {UserId}", conversationId, userId);

            var stats = await _conversationContextService.GetConversationStatsAsync(conversationId);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话统计失败: {ConversationId}", conversationId);
            return StatusCode(500, new { message = "获取对话统计失败" });
        }
    }

    /// <summary>
    /// 导出需求文档
    /// </summary>
    /// <param name="id">需求文档ID</param>
    /// <param name="format">导出格式 (pdf, word, markdown)</param>
    /// <returns>导出文件</returns>
    [HttpGet("{id}/export")]
    public async Task<ActionResult> ExportRequirementDocument(int id, [FromQuery] string format = "pdf")
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("导出需求文档: {RequirementId}, 格式: {Format}, 用户: {UserId}", id, format, userId);

            // 1. 获取需求文档
            var requirement = await _requirementDocumentRepository.GetByIdAsync(id);
            if (requirement == null || requirement.IsDeleted)
            {
                return NotFound(new { message = "需求文档不存在" });
            }

            // 2. 获取项目信息
            var project = await _projectRepository.GetByIdAsync(requirement.ProjectId);
            var projectName = project?.Name ?? "未知项目";

            // 3. 根据格式生成文档内容
            string content;
            string fileName;
            string contentType;

            switch (format.ToLower())
            {
                case "markdown":
                case "md":
                    content = GenerateMarkdownContent(requirement, projectName);
                    fileName = $"{requirement.Title}.md";
                    contentType = "text/markdown";
                    break;

                case "word":
                case "docx":
                    // TODO: 实现Word文档生成
                    content = GenerateMarkdownContent(requirement, projectName);
                    fileName = $"{requirement.Title}.txt";
                    contentType = "text/plain";
                    break;

                case "pdf":
                default:
                    // TODO: 实现PDF文档生成
                    content = GenerateMarkdownContent(requirement, projectName);
                    fileName = $"{requirement.Title}.txt";
                    contentType = "text/plain";
                    break;
            }

            // 4. 返回文件
            var fileBytes = System.Text.Encoding.UTF8.GetBytes(content);
            return File(fileBytes, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出需求文档时发生错误: {RequirementId}", id);
            return StatusCode(500, new { message = "导出需求文档失败" });
        }
    }


       

    #region 私有方法

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }

    /// <summary>
    /// 生成Markdown格式的需求文档内容
    /// </summary>
    /// <param name="requirement">需求文档</param>
    /// <param name="projectName">项目名称</param>
    /// <returns>Markdown内容</returns>
    private string GenerateMarkdownContent(RequirementDocument requirement, string projectName)
    {
        var content = new StringBuilder();

        // 文档标题和基本信息
        content.AppendLine($"# {requirement.Title}");
        content.AppendLine();
        content.AppendLine("## 文档信息");
        content.AppendLine($"- **项目名称**: {projectName}");
        content.AppendLine($"- **文档版本**: {requirement.DocumentVersion}");
        content.AppendLine($"- **文档状态**: {requirement.Status}");
        content.AppendLine($"- **生成方式**: {requirement.GeneratedBy}");
        content.AppendLine($"- **创建时间**: {requirement.CreatedTime:yyyy-MM-dd HH:mm:ss}");
        if (requirement.UpdatedTime.HasValue)
        {
            content.AppendLine($"- **更新时间**: {requirement.UpdatedTime:yyyy-MM-dd HH:mm:ss}");
        }
        content.AppendLine();

        // 需求概述
        if (!string.IsNullOrEmpty(requirement.Content))
        {
            content.AppendLine("## 需求概述");
            content.AppendLine(requirement.Content);
            content.AppendLine();
        }

        // 功能性需求
        if (!string.IsNullOrEmpty(requirement.FunctionalRequirements))
        {
            content.AppendLine("## 功能性需求");
            content.AppendLine(requirement.FunctionalRequirements);
            content.AppendLine();
        }

        // 非功能性需求
        if (!string.IsNullOrEmpty(requirement.NonFunctionalRequirements))
        {
            content.AppendLine("## 非功能性需求");
            content.AppendLine(requirement.NonFunctionalRequirements);
            content.AppendLine();
        }

        // 用户故事
        if (!string.IsNullOrEmpty(requirement.UserStories))
        {
            content.AppendLine("## 用户故事");
            content.AppendLine(requirement.UserStories);
            content.AppendLine();
        }

        // 验收标准
        if (!string.IsNullOrEmpty(requirement.AcceptanceCriteria))
        {
            content.AppendLine("## 验收标准");
            content.AppendLine(requirement.AcceptanceCriteria);
            content.AppendLine();
        }

        // 文档尾部
        content.AppendLine("---");
        content.AppendLine($"*此文档由系统自动生成于 {DateTime.Now:yyyy-MM-dd HH:mm:ss}*");

        return content.ToString();
    }

    /// <summary>
    /// 获取备用回复（当AI服务不可用时）
    /// </summary>
    private string GetFallbackResponse(string userMessage, Project? projectInfo = null)
    {
        var projectContext = projectInfo != null ? $"针对您的项目「{projectInfo.Name}」，" : "";

        var fallbackResponses = new[]
        {
            $"{projectContext}感谢您的需求描述。我理解您的想法，请问您能提供更多关于目标用户和主要功能的详细信息吗？",
            $"{projectContext}这是一个很有趣的项目想法。为了更好地帮助您，请问您希望这个系统解决什么具体问题？",
            $"{projectContext}我需要了解更多信息来为您提供准确的建议。请问您的预期用户群体是什么？",
            $"{projectContext}根据您的描述，我建议我们先明确一下核心功能需求。您认为最重要的功能是什么？",
            $"{projectContext}为了给您更好的建议，请问您对技术实现有什么特殊要求吗？",
            $"{projectContext}这个想法很不错。请问您预期的项目规模和时间安排是怎样的？"
        };

        // 根据用户消息内容选择合适的回复
        if (userMessage.Contains("电商") || userMessage.Contains("商城"))
            return $"{projectContext}我理解您想开发电商系统。请问您主要面向哪类用户？B2B还是B2C？需要支持哪些核心功能？";

        if (userMessage.Contains("管理系统") || userMessage.Contains("管理平台"))
            return $"{projectContext}管理系统是个很好的方向。请问您希望管理什么类型的数据或流程？用户角色有哪些？";

        if (userMessage.Contains("APP") || userMessage.Contains("移动"))
            return $"{projectContext}移动应用开发很有前景。请问您希望开发iOS、Android还是跨平台应用？主要功能是什么？";

        // 默认回复
        return fallbackResponses[Random.Shared.Next(fallbackResponses.Length)];
    }

    /// <summary>
    /// 生成建议的后续问题
    /// </summary>
    private List<string> GenerateSuggestedQuestions(string userMessage)
    {
        var suggestions = new List<string>();

        // 根据用户消息内容生成相关问题
        if (userMessage.Contains("系统") || userMessage.Contains("平台"))
        {
            suggestions.AddRange(new[]
            {
                "这个系统的主要用户群体是谁？",
                "您希望支持多少并发用户？",
                "有什么特殊的安全要求吗？"
            });
        }

        if (userMessage.Contains("功能"))
        {
            suggestions.AddRange(new[]
            {
                "请详细描述核心功能",
                "用户权限如何划分？",
                "需要集成第三方服务吗？"
            });
        }

        // 通用建议问题
        if (suggestions.Count == 0)
        {
            suggestions.AddRange(new[]
            {
                "请描述一下具体的业务场景",
                "您的目标用户是谁？",
                "预期什么时候上线？"
            });
        }

        return suggestions.Take(3).ToList();
    }

    /// <summary>
    /// 从用户消息中提取需求要点
    /// </summary>
    private List<string> ExtractRequirements(string userMessage)
    {
        var requirements = new List<string>();

        // 简单的关键词提取
        var keywords = new Dictionary<string, string>
        {
            { "用户管理", "用户注册、登录、权限管理" },
            { "数据管理", "数据录入、查询、统计" },
            { "报表", "数据分析和报表生成" },
            { "支付", "在线支付功能" },
            { "消息", "消息通知系统" },
            { "文件", "文件上传下载管理" }
        };

        foreach (var keyword in keywords)
        {
            if (userMessage.Contains(keyword.Key))
            {
                requirements.Add(keyword.Value);
            }
        }

        return requirements;
    }

    /// <summary>
    /// 根据模型名称推断AI提供商
    /// </summary>
    /// <param name="modelName">模型名称</param>
    /// <returns>提供商名称</returns>
    private static string GetProviderFromModelName(string modelName)
    {
        if (string.IsNullOrEmpty(modelName))
            return "Unknown";

        var lowerModelName = modelName.ToLower();

        if (lowerModelName.Contains("gpt") || lowerModelName.Contains("openai"))
            return "OpenAI";

        if (lowerModelName.Contains("deepseek"))
            return "DeepSeek";

        if (lowerModelName.Contains("claude"))
            return "Claude";

        if (lowerModelName.Contains("azure"))
            return "Azure";

        if (lowerModelName.Contains("ollama"))
            return "Ollama";

        // 默认返回模型名称作为提供商
        return modelName;
    }

    #endregion
}
