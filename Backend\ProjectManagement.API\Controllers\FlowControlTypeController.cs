using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.API.Helper;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 流程控制类型控制器
    /// </summary>
    [ApiController]
    [Route("api/flow-control-types")]
    [Authorize]
    public class FlowControlTypeController : ControllerBase
    {
        private readonly IFlowControlTypeRepository _flowControlTypeRepository;
        private readonly ILogger<FlowControlTypeController> _logger;

        public FlowControlTypeController(
            IFlowControlTypeRepository flowControlTypeRepository,
            ILogger<FlowControlTypeController> logger)
        {
            _flowControlTypeRepository = flowControlTypeRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取流程控制类型列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResultDto<FlowControlTypeDto>>> GetFlowControlTypes([FromQuery] FlowControlTypeQueryDto query)
        {
            try
            {
                var result = await _flowControlTypeRepository.GetPagedAsync(query);

                var dtoResult = new PagedResultDto<FlowControlTypeDto>
                {
                    Items = result.Items.Select(MapToDto).ToList(),
                    TotalCount = result.TotalCount,
                    PageNumber = result.PageNumber,
                    PageSize = result.PageSize
                };

                return Ok(dtoResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取流程控制类型列表失败");
                return StatusCode(500, "获取流程控制类型列表失败");
            }
        }

        /// <summary>
        /// 获取启用的流程控制类型
        /// </summary>
        [HttpGet("active")]
        public async Task<ActionResult<List<FlowControlTypeDto>>> GetActiveFlowControlTypes()
        {
            try
            {
                var flowControlTypes = await _flowControlTypeRepository.GetActiveAsync();
                return Ok(flowControlTypes.Select(MapToDto).ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的流程控制类型失败");
                return StatusCode(500, "获取启用的流程控制类型失败");
            }
        }

        /// <summary>
        /// 根据执行类型获取流程控制类型
        /// </summary>
        [HttpGet("by-execution-type/{executionType}")]
        public async Task<ActionResult<List<FlowControlTypeDto>>> GetFlowControlTypesByExecutionType(string executionType)
        {
            try
            {
                var flowControlTypes = await _flowControlTypeRepository.GetByExecutionTypeAsync(executionType);
                return Ok(flowControlTypes.Select(MapToDto).ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据执行类型获取流程控制类型失败，ExecutionType: {ExecutionType}", executionType);
                return StatusCode(500, "根据执行类型获取流程控制类型失败");
            }
        }

        /// <summary>
        /// 获取启用的流程控制类型（按执行类型分组）
        /// </summary>
        [HttpGet("active-grouped")]
        public async Task<ActionResult<Dictionary<string, List<FlowControlTypeDto>>>> GetActiveFlowControlTypesGrouped()
        {
            try
            {
                var groupedFlowControlTypes = await _flowControlTypeRepository.GetActiveByExecutionTypeAsync();
                var result = groupedFlowControlTypes.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.Select(MapToDto).ToList()
                );
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分组的启用流程控制类型失败");
                return StatusCode(500, "获取分组的启用流程控制类型失败");
            }
        }

        /// <summary>
        /// 获取流程控制类型详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<FlowControlTypeDto>> GetFlowControlType(int id)
        {
            try
            {
                var flowControlType = await _flowControlTypeRepository.GetByIdAsync(id);
                if (flowControlType == null || flowControlType.IsDeleted)
                {
                    return NotFound("流程控制类型不存在");
                }

                return Ok(MapToDto(flowControlType));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取流程控制类型详情失败，ID: {Id}", id);
                return StatusCode(500, "获取流程控制类型详情失败");
            }
        }

        /// <summary>
        /// 创建流程控制类型
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<FlowControlTypeDto>> CreateFlowControlType([FromBody] CreateFlowControlTypeDto dto)
        {
            try
            {
                // 检查流程控制类型值是否已存在
                if (await _flowControlTypeRepository.ExistsValueAsync(dto.Value))
                {
                    return BadRequest($"流程控制类型值 '{dto.Value}' 已存在");
                }

                var flowControlType = new FlowControlType
                {
                    Value = dto.Value,
                    Label = dto.Label,
                    Description = dto.Description,
                    Icon = dto.Icon,
                    Color = dto.Color,
                    SortOrder = dto.SortOrder > 0 ? dto.SortOrder : await _flowControlTypeRepository.GetMaxSortOrderAsync(dto.ExecutionType) + 1,
                    IsActive = dto.IsActive,
                    IsBuiltIn = false, // 新创建的都是自定义类型
                    ParameterSchema = dto.ParameterSchema,
                    ExecutionType = dto.ExecutionType,
                    RequiresTarget = dto.RequiresTarget,
                    CanNest = dto.CanNest,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    CreatedBy = UserHelper.GetCurrentUserId(User),
                    IsDeleted = false
                };

                var result = await _flowControlTypeRepository.AddAsync(flowControlType);
                return Ok(MapToDto(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建流程控制类型失败");
                return StatusCode(500, "创建流程控制类型失败");
            }
        }

        /// <summary>
        /// 更新流程控制类型
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<FlowControlTypeDto>> UpdateFlowControlType(int id, [FromBody] UpdateFlowControlTypeDto dto)
        {
            try
            {
                var flowControlType = await _flowControlTypeRepository.GetByIdAsync(id);
                if (flowControlType == null || flowControlType.IsDeleted)
                {
                    return NotFound("流程控制类型不存在");
                }

                // 内置类型不能修改核心属性
                if (flowControlType.IsBuiltIn)
                {
                    return BadRequest("内置流程控制类型不能修改");
                }

                // 更新属性
                if (!string.IsNullOrEmpty(dto.Label))
                    flowControlType.Label = dto.Label;
                if (dto.Description != null)
                    flowControlType.Description = dto.Description;
                if (dto.Icon != null)
                    flowControlType.Icon = dto.Icon;
                if (dto.Color != null)
                    flowControlType.Color = dto.Color;
                if (dto.SortOrder.HasValue)
                    flowControlType.SortOrder = dto.SortOrder.Value;
                if (dto.IsActive.HasValue)
                    flowControlType.IsActive = dto.IsActive.Value;
                if (dto.ParameterSchema != null)
                    flowControlType.ParameterSchema = dto.ParameterSchema;
                if (dto.ExecutionType != null)
                    flowControlType.ExecutionType = dto.ExecutionType;
                if (dto.RequiresTarget.HasValue)
                    flowControlType.RequiresTarget = dto.RequiresTarget.Value;
                if (dto.CanNest.HasValue)
                    flowControlType.CanNest = dto.CanNest.Value;

                flowControlType.UpdatedTime = DateTime.Now;
                flowControlType.UpdatedBy = UserHelper.GetCurrentUserId(User);

                var success = await _flowControlTypeRepository.UpdateAsync(flowControlType);
                if (!success)
                {
                    return StatusCode(500, "更新流程控制类型失败");
                }

                // 重新获取更新后的实体
                var updatedEntity = await _flowControlTypeRepository.GetByIdAsync(id);
                return Ok(MapToDto(updatedEntity!));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新流程控制类型失败，ID: {Id}", id);
                return StatusCode(500, "更新流程控制类型失败");
            }
        }

        /// <summary>
        /// 删除流程控制类型
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteFlowControlType(int id)
        {
            try
            {
                var flowControlType = await _flowControlTypeRepository.GetByIdAsync(id);
                if (flowControlType == null || flowControlType.IsDeleted)
                {
                    return NotFound("流程控制类型不存在");
                }

                // 内置类型不能删除
                if (flowControlType.IsBuiltIn)
                {
                    return BadRequest("内置流程控制类型不能删除");
                }

                // TODO: 检查是否有步骤在使用这个流程控制类型

                flowControlType.IsDeleted = true;
                flowControlType.DeletedTime = DateTime.Now;
                flowControlType.DeletedBy = UserHelper.GetCurrentUserId(ClaimsPrincipal.Current);

                await _flowControlTypeRepository.UpdateAsync(flowControlType);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除流程控制类型失败，ID: {Id}", id);
                return StatusCode(500, "删除流程控制类型失败");
            }
        }

        /// <summary>
        /// 启用/禁用流程控制类型
        /// </summary>
        [HttpPut("{id}/status")]
        public async Task<ActionResult> SetActiveStatus(int id, [FromBody] bool isActive)
        {
            try
            {
                var success = await _flowControlTypeRepository.SetActiveStatusAsync(id, isActive);
                if (!success)
                {
                    return NotFound("流程控制类型不存在");
                }

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置流程控制类型状态失败，ID: {Id}", id);
                return StatusCode(500, "设置流程控制类型状态失败");
            }
        }

        #region 私有方法


        /// <summary>
        /// 实体转DTO
        /// </summary>
        private static FlowControlTypeDto MapToDto(FlowControlType entity)
        {
            return new FlowControlTypeDto
            {
                Id = entity.Id,
                Value = entity.Value,
                Label = entity.Label,
                Description = entity.Description,
                Icon = entity.Icon,
                Color = entity.Color,
                SortOrder = entity.SortOrder,
                IsActive = entity.IsActive,
                IsBuiltIn = entity.IsBuiltIn,
                ParameterSchema = entity.ParameterSchema,
                ExecutionType = entity.ExecutionType,
                RequiresTarget = entity.RequiresTarget,
                CanNest = entity.CanNest,
                CreatedTime = entity.CreatedTime,
                UpdatedTime = entity.UpdatedTime
            };
        }

        #endregion
    }
}
