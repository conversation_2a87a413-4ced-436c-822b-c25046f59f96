using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Text.Json;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// Playwright测试管理控制器
    /// </summary>
    [ApiController]
    [Route("api/playwright-tests")]
    // [Authorize] // 暂时注释掉以便测试
    public class PlaywrightTestController : ControllerBase
    {
        private readonly ILogger<PlaywrightTestController> _logger;
        private readonly IPlaywrightScriptRepository _repository;

        public PlaywrightTestController(
            ILogger<PlaywrightTestController> logger,
            IPlaywrightScriptRepository repository)
        {
            _logger = logger;
            _repository = repository;
        }

        /// <summary>
        /// 获取测试列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<object>> GetTests(
            [FromQuery] int? projectId,
            [FromQuery] string? category,
            [FromQuery] string? browser,
            [FromQuery] string? keyword,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetCurrentUserId();

                // 使用Repository搜索
                var (items, total) = await _repository.Search(
                    keyword: keyword,
                    category: category,
                    browser: browser,
                    projectId: projectId,
                    pageIndex: page,
                    pageSize: pageSize);

                // 转换为DTO
                var tests = items.Select(ConvertToDto).ToList();

                var result = new
                {
                    data = tests,
                    total = total,
                    page = page,
                    pageSize = pageSize
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Playwright测试列表失败");
                return StatusCode(500, "获取测试列表失败");
            }
        }

        /// <summary>
        /// 获取单个测试
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<PlaywrightTestDto>> GetTest(int id)
        {
            try
            {
                var entity = await _repository.GetByIdAsync(id);

                if (entity == null)
                {
                    return NotFound("测试不存在");
                }

                var dto = ConvertToDto(entity);
                return Ok(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Playwright测试失败，ID: {TestId}", id);
                return StatusCode(500, "获取测试失败");
            }
        }

        /// <summary>
        /// 创建测试
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<PlaywrightTestDto>> CreateTest([FromBody] CreatePlaywrightTestDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();

                // 转换DTO为实体
                var entity = ConvertToEntity(dto, userId);

                // 保存到数据库
                var createdEntity = await _repository.AddAsync(entity);

                // 转换为DTO返回
                var result = ConvertToDto(createdEntity);

                _logger.LogInformation("创建Playwright测试成功，ID: {TestId}, Name: {TestName}", result.Id, result.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建Playwright测试失败");
                return StatusCode(500, "创建测试失败");
            }
        }

        /// <summary>
        /// 更新测试
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<PlaywrightTestDto>> UpdateTest(int id, [FromBody] UpdatePlaywrightTestDto dto)
        {
            try
            {
                var entity = await _repository.GetByIdAsync(id);

                if (entity == null)
                {
                    return NotFound("测试不存在");
                }

                // 更新字段
                if (!string.IsNullOrEmpty(dto.Name))
                    entity.Name = dto.Name;
                if (dto.Description != null)
                    entity.Description = dto.Description;
                if (!string.IsNullOrEmpty(dto.Category))
                    entity.Category = dto.Category;
                if (!string.IsNullOrEmpty(dto.Browser))
                    entity.Browser = dto.Browser;
                if (dto.Code != null)
                    entity.Code = dto.Code;
                if (dto.Config != null)
                    entity.ConfigJson = JsonSerializer.Serialize(dto.Config);
                if (dto.Tags != null)
                    entity.TagsJson = JsonSerializer.Serialize(dto.Tags);
                if (!string.IsNullOrEmpty(dto.Priority))
                    entity.Priority = dto.Priority;
                if (!string.IsNullOrEmpty(dto.Status))
                    entity.Status = dto.Status;
                if (dto.ProjectId.HasValue)
                    entity.ProjectId = dto.ProjectId;

                entity.UpdatedTime = DateTime.Now;

                // 保存到数据库
                await _repository.UpdateAsync(entity);

                // 转换为DTO返回
                var result = ConvertToDto(entity);

                _logger.LogInformation("更新Playwright测试成功，ID: {TestId}, Name: {TestName}", result.Id, result.Name);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新Playwright测试失败，ID: {TestId}", id);
                return StatusCode(500, "更新测试失败");
            }
        }

        /// <summary>
        /// 下载单个测试文件
        /// </summary>
        [HttpGet("{id}/download")]
        public async Task<ActionResult> DownloadSingleTest(int id)
        {
            try
            {
                var entity = await _repository.GetByIdAsync(id);
                if (entity == null)
                {
                    return NotFound("测试不存在");
                }

                var testContent = GenerateTestFileContent(entity);
                var fileName = $"{SanitizeFileName(entity.Name)}.spec.ts";

                return File(
                    System.Text.Encoding.UTF8.GetBytes(testContent),
                    "text/typescript",
                    fileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载测试文件失败，ID: {TestId}", id);
                return StatusCode(500, "下载测试文件失败");
            }
        }

        /// <summary>
        /// 下载多个测试文件（ZIP格式）
        /// </summary>
        [HttpPost("download-multiple")]
        public async Task<ActionResult> DownloadMultipleTests([FromBody] DownloadTestsDto dto)
        {
            try
            {
                var tests = new List<PlaywrightScript>();

                if (dto.TestIds != null && dto.TestIds.Any())
                {
                    // 下载指定的测试
                    foreach (var testId in dto.TestIds)
                    {
                        var entity = await _repository.GetByIdAsync(testId);
                        if (entity != null)
                        {
                            tests.Add(entity);
                        }
                    }
                }
                else
                {
                    // 下载所有测试
                    var searchResult = await _repository.Search(
                        category: dto.Category,
                        browser: dto.Browser,
                        projectId: dto.ProjectId,
                        pageSize: 1000
                    );
                    tests = searchResult.items;
                }

                if (!tests.Any())
                {
                    return BadRequest("没有找到要下载的测试");
                }

                // 创建ZIP文件
                using var memoryStream = new MemoryStream();
                using (var archive = new System.IO.Compression.ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true))
                {
                    foreach (var test in tests)
                    {
                        var fileName = $"{SanitizeFileName(test.Name)}.spec.ts";
                        var content = GenerateTestFileContent(test);

                        var entry = archive.CreateEntry(fileName);
                        using var entryStream = entry.Open();
                        using var writer = new StreamWriter(entryStream, System.Text.Encoding.UTF8);
                        await writer.WriteAsync(content);
                    }
                }

                var zipFileName = $"playwright-tests-{DateTime.Now:yyyyMMdd-HHmmss}.zip";

                _logger.LogInformation("下载Playwright测试成功，共 {Count} 个文件", tests.Count);

                return File(
                    memoryStream.ToArray(),
                    "application/zip",
                    zipFileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载Playwright测试失败");
                return StatusCode(500, "下载测试失败");
            }
        }

        /// <summary>
        /// 导出单个测试文件
        /// </summary>
        [HttpGet("{id}/export")]
        public async Task<ActionResult> ExportSingleTestFile(int id)
        {
            try
            {
                var entity = await _repository.GetByIdAsync(id);
                if (entity == null)
                {
                    return NotFound("测试不存在");
                }

                var testContent = GenerateTestFileContent(entity);
                var fileName = $"{SanitizeFileName(entity.Name)}.spec.ts";

                return File(
                    System.Text.Encoding.UTF8.GetBytes(testContent),
                    "text/typescript",
                    fileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出单个测试文件失败，ID: {TestId}", id);
                return StatusCode(500, "导出测试文件失败");
            }
        }

        /// <summary>
        /// 删除测试
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteTest(int id)
        {
            try
            {
                var entity = await _repository.GetByIdAsync(id);

                if (entity == null)
                {
                    return NotFound("测试不存在");
                }

                // 软删除
                await _repository.SoftDeleteAsync(id);

                _logger.LogInformation("删除Playwright测试成功，ID: {TestId}, Name: {TestName}", entity.Id, entity.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除Playwright测试失败，ID: {TestId}", id);
                return StatusCode(500, "删除测试失败");
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
            return 1; // 默认用户ID
        }

        /// <summary>
        /// 转换实体为DTO
        /// </summary>
        private PlaywrightTestDto ConvertToDto(PlaywrightScript entity)
        {
            var config = new object();
            var tags = Array.Empty<string>();

            // 解析JSON配置
            if (!string.IsNullOrEmpty(entity.ConfigJson))
            {
                try
                {
                    config = JsonSerializer.Deserialize<object>(entity.ConfigJson) ?? new object();
                }
                catch
                {
                    config = new object();
                }
            }

            // 解析JSON标签
            if (!string.IsNullOrEmpty(entity.TagsJson))
            {
                try
                {
                    tags = JsonSerializer.Deserialize<string[]>(entity.TagsJson) ?? Array.Empty<string>();
                }
                catch
                {
                    tags = Array.Empty<string>();
                }
            }

            return new PlaywrightTestDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description ?? "",
                Category = entity.Category,
                Browser = entity.Browser,
                Code = entity.Code ?? "",
                Config = config,
                Tags = tags,
                Priority = entity.Priority,
                Status = entity.Status,
                CreatedTime = entity.CreatedTime,
                UpdatedTime = entity.UpdatedTime ?? entity.CreatedTime,
                CreatedBy = entity.CreatedBy,
                ProjectId = entity.ProjectId,
                UserId = entity.CreatedBy ?? 1
            };
        }



        /// <summary>
        /// 生成测试文件内容
        /// </summary>
        private string GenerateTestFileContent(PlaywrightScript entity)
        {
            var config = "";
            if (!string.IsNullOrEmpty(entity.ConfigJson))
            {
                try
                {
                    var configObj = JsonSerializer.Deserialize<object>(entity.ConfigJson);
                    config = $"// 配置: {entity.ConfigJson}";
                }
                catch
                {
                    config = "// 配置解析失败";
                }
            }

            var tags = "";
            if (!string.IsNullOrEmpty(entity.TagsJson))
            {
                try
                {
                    var tagsArray = JsonSerializer.Deserialize<string[]>(entity.TagsJson);
                    tags = $"// 标签: {string.Join(", ", tagsArray ?? Array.Empty<string>())}";
                }
                catch
                {
                    tags = "// 标签解析失败";
                }
            }

            return $@"/**
 * 测试名称: {entity.Name}
 * 测试描述: {entity.Description ?? "无描述"}
 * 测试分类: {entity.Category}
 * 浏览器: {entity.Browser}
 * 优先级: {entity.Priority}
 * 状态: {entity.Status}
 * 创建时间: {entity.CreatedTime:yyyy-MM-dd HH:mm:ss}
 * 更新时间: {entity.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未更新"}
 * {config}
 * {tags}
 */

{entity.Code ?? "// 测试代码为空"}
";
        }

        /// <summary>
        /// 清理文件名中的非法字符
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
            return string.IsNullOrEmpty(sanitized) ? "unnamed_test" : sanitized;
        }

        /// <summary>
        /// 转换DTO为实体
        /// </summary>
        private PlaywrightScript ConvertToEntity(CreatePlaywrightTestDto dto, int userId)
        {
            var configJson = "";
            var tagsJson = "";

            // 序列化配置
            if (dto.Config != null)
            {
                try
                {
                    configJson = JsonSerializer.Serialize(dto.Config);
                }
                catch
                {
                    configJson = "{}";
                }
            }

            // 序列化标签
            if (dto.Tags != null && dto.Tags.Length > 0)
            {
                try
                {
                    tagsJson = JsonSerializer.Serialize(dto.Tags);
                }
                catch
                {
                    tagsJson = "[]";
                }
            }

            return new PlaywrightScript
            {
                Name = dto.Name,
                Description = dto.Description ?? "",
                Category = dto.Category ?? "e2e",
                Browser = dto.Browser ?? "chromium",
                Code = dto.Code ?? "",
                ConfigJson = configJson,
                TagsJson = tagsJson,
                Priority = dto.Priority ?? "medium",
                Status = dto.Status ?? "草稿",
                ProjectId = dto.ProjectId,
                CreatedBy = userId,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };
        }
    }

    /// <summary>
    /// Playwright测试DTO
    /// </summary>
    public class PlaywrightTestDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public string Browser { get; set; } = "";
        public string Code { get; set; } = "";
        public object Config { get; set; } = new object();
        public string[] Tags { get; set; } = Array.Empty<string>();
        public string Priority { get; set; } = "medium";
        public string Status { get; set; } = "";
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public int? CreatedBy { get; set; }
        public int? ProjectId { get; set; }
        public int UserId { get; set; }
    }

    /// <summary>
    /// 创建Playwright测试DTO
    /// </summary>
    public class CreatePlaywrightTestDto
    {
        public string Name { get; set; } = "";
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string? Browser { get; set; }
        public string? Code { get; set; }
        public object? Config { get; set; }
        public string[]? Tags { get; set; }
        public string? Priority { get; set; }
        public string? Status { get; set; }
        public int? ProjectId { get; set; }
    }

    /// <summary>
    /// 下载测试DTO
    /// </summary>
    public class DownloadTestsDto
    {
        public int[]? TestIds { get; set; }
        public string? Category { get; set; }
        public string? Browser { get; set; }
        public int? ProjectId { get; set; }
    }

    /// <summary>
    /// 更新Playwright测试DTO
    /// </summary>
    public class UpdatePlaywrightTestDto
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string? Browser { get; set; }
        public string? Code { get; set; }
        public object? Config { get; set; }
        public string[]? Tags { get; set; }
        public string? Priority { get; set; }
        public string? Status { get; set; }
        public int? ProjectId { get; set; }
    }
}
