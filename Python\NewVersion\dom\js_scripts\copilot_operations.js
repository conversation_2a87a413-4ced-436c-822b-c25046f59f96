function openCopilitIcon() {
    const copilotChatPannel=document.getElementById('workbench.parts.auxiliarybar');
    const element = document.querySelector('.action-label.codicon.codicon-copilot');
    let isOpen = false

    if(copilotChatPannel){
     const parent = copilotChatPannel.parentElement;
     const styleAttribute = parent.getAttribute('style')|| '';
  
     // 提取left和width值
     const leftMatch = styleAttribute.match(/left:\s*(\d+)px/);
     const widthMatch = styleAttribute.match(/width:\s*(\d+)px/);
     const width = widthMatch ? parseInt(widthMatch[1]) : 0;
     const left = leftMatch ? parseInt(leftMatch[1]) : 0;
     isOpen = left > 0 && width > 0;
    }
    if (element) {
        if(!isOpen){
        element.click();
        }
      
    }
    return { 
        success: true, 
        message: "已打开Copilot Chat",
        elementInfo: {
            tagName: element.tagName,
            className: element.className,
            id: element.id || 'no id',
            textContent: element.textContent?.substring(0, 50) || 'no text'
        }
    };
}

function confirmIsNewSession(isNewSession) {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');
    if (!copilotChatPannel) {
        return { success: false, error: "未找到Copilot面板" };
    }

    if (isNewSession) {
        console.log("用户选择开始新会话");

        var newSessionBtn = copilotChatPannel.querySelector('.action-label.codicon.codicon-plus');
        console.log("新会话按钮:", newSessionBtn);

        if (newSessionBtn) {
            newSessionBtn.click();
            console.log("已点击新会话按钮");

            // 等待一下让新会话加载
            setTimeout(() => {
                // 获取输入框焦点
                focusInputAfterNewSession(copilotChatPannel);
            }, 1000);

            return { success: true, message: "新会话已开始，正在设置焦点" };
        } else {
            return { success: false, error: "未找到新会话按钮" };
        }
    } else {
        console.log("用户取消新会话，直接设置焦点");

        // 用户取消新会话，直接在当前会话设置焦点
        focusInputAfterNewSession(copilotChatPannel);

        return { success: true, message: "继续使用当前会话，已设置焦点" };
    }

    // 获取焦点的内部函数
function focusInputAfterNewSession(panel) {
        console.log("开始设置输入框焦点");

        // 尝试多种选择器，优先使用native-edit-context
        const selectors = [
            '.native-edit-context[role="textbox"]',
            '.interactive-input-editor .native-edit-context',
            'div[role="textbox"][aria-multiline="true"]',
            'textarea.inputarea.monaco-mouse-cursor-text',
            'textarea.inputarea:not(.ime-text-area)',
            'textarea[data-mprt]:not(.ime-text-area)',
            'textarea:not(.ime-text-area):not([readonly])'
        ];

        for (const selector of selectors) {
            const textarea = panel.querySelector(selector);
            if (textarea) {
                // 设置焦点
                textarea.click();
                textarea.focus();
                return true;
            }
        }

        console.log('未找到输入框');
        return false;
    }
}

function focusTxtInput() {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');
    if (!copilotChatPannel) {
        return { success: false, error: "未找到Copilot面板" };
    }

    // 首先检查是否有AI回复内容占用焦点
    const aiResponseElements = copilotChatPannel.querySelectorAll('.interactive-item-container.editing-session.interactive-response.chat-most-recent-response .view-lines.monaco-mouse-cursor-text');
    if (aiResponseElements.length > 0) {
        console.log("检测到AI回复内容，需要重新定位到输入框");

        // 点击页面其他区域先移除AI回复的焦点
        const chatContainer = copilotChatPannel.querySelector('.monaco-tl-contents');
        if (chatContainer) {
            chatContainer.click();
        }

        // 等待一下让焦点切换
        setTimeout(() => {
            // 再次尝试设置输入框焦点
            focusInputBoxDirectly(copilotChatPannel);
        }, 100);
    } else {
        // 直接设置输入框焦点
        return focusInputBoxDirectly(copilotChatPannel);
    }

    return { success: true, message: "正在处理AI回复焦点冲突..." };
}

function focusInputBoxDirectly(copilotChatPannel) {
    // 尝试多种选择器，优先使用native-edit-context
    const selectors = [
        '.native-edit-context[role="textbox"]',
        '.interactive-input-editor .native-edit-context',
        'div[role="textbox"][aria-multiline="true"]',
        'textarea.inputarea.monaco-mouse-cursor-text:not(.view-lines)',  // 排除AI回复区域
        'textarea.inputarea:not(.ime-text-area)',
        'textarea[data-mprt]:not(.ime-text-area)',
        'textarea:not(.ime-text-area):not([readonly])'
    ];

    for (const selector of selectors) {
        const elements = copilotChatPannel.querySelectorAll(selector);
        for (const textarea of elements) {
            // 确保不是AI回复区域
            if (!textarea.closest('.interactive-response') &&
                !textarea.classList.contains('view-lines')) {

                console.log(`找到输入框，使用选择器: ${selector}`);
                console.log('输入框元素信息:', {
                    tagName: textarea.tagName,
                    className: textarea.className,
                    role: textarea.getAttribute('role'),
                    isVisible: textarea.offsetWidth > 0 && textarea.offsetHeight > 0
                });

                // 设置焦点
                textarea.click();
                textarea.focus();

                // 确保光标在输入框内
                if (textarea.tagName.toLowerCase() === 'textarea' || textarea.getAttribute('role') === 'textbox') {
                    // 将光标移到末尾
                    if (textarea.setSelectionRange) {
                        const length = textarea.value ? textarea.value.length : 0;
                        textarea.setSelectionRange(length, length);
                    } else if (textarea.textContent !== undefined) {
                        // 对于contenteditable元素
                        const range = document.createRange();
                        const selection = window.getSelection();
                        range.selectNodeContents(textarea);
                        range.collapse(false);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }

                return { success: true, message: "已设置输入框焦点", selector: selector };
            }
        }
    }

    return { success: false, error: "未找到有效的输入框" };
}

async function inputText(text) {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');

    if (copilotChatPannel) {
        // 首先确保焦点在输入框而不是AI回复区域
        console.log("准备输入文本，先检查焦点位置...");
        focusTxtInput();

        // 等待焦点设置完成
        await new Promise(resolve => setTimeout(resolve, 200));

        // 尝试多种选择器，优先使用native-edit-context
        const selectors = [
            '.native-edit-context[role="textbox"]',
            '.interactive-input-editor .native-edit-context',
            'div[role="textbox"][aria-multiline="true"]',
            'textarea.inputarea.monaco-mouse-cursor-text:not(.view-lines)',  // 排除AI回复区域
            'textarea.inputarea:not(.ime-text-area)',
            'textarea[data-mprt]:not(.ime-text-area)',
            'textarea:not(.ime-text-area):not([readonly])'
        ];

        let inputElement = null;
        let foundSelector = '';

        for (const selector of selectors) {
            const elements = copilotChatPannel.querySelectorAll(selector);
            for (const element of elements) {
                // 检查元素是否可见且可用，并且不是AI回复区域
                if (element &&
                    element.style.display !== 'none' &&
                    element.offsetWidth > 0 &&
                    element.offsetHeight > 0 &&
                    !element.classList.contains('ime-text-area') &&
                    !element.closest('.interactive-response') &&
                    !element.classList.contains('view-lines')) {
                    inputElement = element;
                    foundSelector = selector;
                    break;
                }
            }
            if (inputElement) break;
        }

        if (inputElement) {
            console.log(`找到输入元素，使用选择器: ${foundSelector}`);
            console.log('输入元素信息:', {
                tagName: inputElement.tagName,
                className: inputElement.className,
                id: inputElement.id,
                role: inputElement.getAttribute('role'),
                tabIndex: inputElement.tabIndex
            });

            // 获取焦点
            inputElement.focus();
            inputElement.click();

            // 根据元素类型选择不同的输入方法
            if (inputElement.tagName.toLowerCase() === 'textarea') {
                // 传统textarea输入方法
                inputElement.value = text;
                inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                inputElement.dispatchEvent(new Event('change', { bubbles: true }));
            } else if (inputElement.getAttribute('role') === 'textbox') {
                // Monaco编辑器的native-edit-context输入方法
                // 根据记忆，VSCode Copilot Chat需要特殊处理

                // 方法1: 尝试使用剪贴板API
                try {
                    await navigator.clipboard.writeText(text);

                    // 模拟Ctrl+V粘贴事件
                    const pasteEvent = new KeyboardEvent('keydown', {
                        key: 'v',
                        code: 'KeyV',
                        ctrlKey: true,
                        bubbles: true,
                        cancelable: true
                    });

                    inputElement.dispatchEvent(pasteEvent);

                    // 等待一下让粘贴生效
                    setTimeout(() => {
                        const changeEvent = new Event('change', { bubbles: true });
                        inputElement.dispatchEvent(changeEvent);
                    }, 100);

                } catch (clipboardError) {
                    // 方法2: 直接设置内容并触发事件
                    inputElement.textContent = text;

                    // 创建并分发输入事件
                    const inputEvent = new InputEvent('input', {
                        inputType: 'insertText',
                        data: text,
                        bubbles: true,
                        cancelable: true
                    });

                    const changeEvent = new Event('change', { bubbles: true });

                    inputElement.dispatchEvent(inputEvent);
                    inputElement.dispatchEvent(changeEvent);
                }
            }

            return { success: true, message: `文本输入完成: ${text}`, selector: foundSelector };
        }

        return { success: false, error: "未找到输入框" };
    }

    return { success: false, error: "未找到Copilot面板" };
}

function inputTextSync(text) {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');

    if (copilotChatPannel) {
        // 首先确保焦点在输入框而不是AI回复区域
        console.log("准备输入文本，先检查焦点位置...");
        focusTxtInput();

        // 尝试多种选择器，优先使用native-edit-context
        const selectors = [
            '.native-edit-context[role="textbox"]',
            '.interactive-input-editor .native-edit-context',
            'div[role="textbox"][aria-multiline="true"]',
            'textarea.inputarea.monaco-mouse-cursor-text:not(.view-lines)',  // 排除AI回复区域
            'textarea.inputarea:not(.ime-text-area)',
            'textarea[data-mprt]:not(.ime-text-area)',
            'textarea:not(.ime-text-area):not([readonly])'
        ];

        let inputElement = null;
        let foundSelector = '';

        for (const selector of selectors) {
            const elements = copilotChatPannel.querySelectorAll(selector);
            for (const element of elements) {
                // 检查元素是否可见且可用，并且不是AI回复区域
                if (element &&
                    element.style.display !== 'none' &&
                    element.offsetWidth > 0 &&
                    element.offsetHeight > 0 &&
                    !element.classList.contains('ime-text-area') &&
                    !element.closest('.interactive-response') &&
                    !element.classList.contains('view-lines')) {
                    inputElement = element;
                    foundSelector = selector;
                    break;
                }
            }
            if (inputElement) break;
        }

        if (inputElement) {
            console.log(`找到输入元素，使用选择器: ${foundSelector}`);
            console.log('输入元素信息:', {
                tagName: inputElement.tagName,
                className: inputElement.className,
                id: inputElement.id,
                role: inputElement.getAttribute('role'),
                tabIndex: inputElement.tabIndex
            });

            // 获取焦点
            inputElement.focus();
            inputElement.click();

            // 根据元素类型选择不同的输入方法
            if (inputElement.tagName.toLowerCase() === 'textarea') {
                // 传统textarea输入方法
                inputElement.value = text;
                inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                inputElement.dispatchEvent(new Event('change', { bubbles: true }));
            } else if (inputElement.getAttribute('role') === 'textbox') {
                // Monaco编辑器的native-edit-context输入方法
                // 直接设置内容并触发事件
                inputElement.textContent = text;

                // 创建并分发输入事件
                const inputEvent = new InputEvent('input', {
                    inputType: 'insertText',
                    data: text,
                    bubbles: true,
                    cancelable: true
                });

                const changeEvent = new Event('change', { bubbles: true });

                inputElement.dispatchEvent(inputEvent);
                inputElement.dispatchEvent(changeEvent);
            }

            return { success: true, message: `文本输入完成: ${text}`, selector: foundSelector };
        }

        return { success: false, error: "未找到输入框" };
    }

    return { success: false, error: "未找到Copilot面板" };
}

function clickMsgBtn(){
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');

    if (copilotChatPannel) {
        const sendBtn = copilotChatPannel.querySelector('a.action-label.codicon.codicon-send');
        if (sendBtn) {
            sendBtn.click();
            return { success: true, message: "已点击发送按钮" };
        }
        return { success: false, error: "未找到发送按钮" };
    }

    return { success: false, error: "未找到Copilot面板" };
}

function detectElement(selectors) {
    /**
     * 检测元素是否存在
     * @param {Array|string} selectors - CSS选择器数组或单个选择器
     * @returns {Object} 检测结果
     */

    // 确保selectors是数组
    if (typeof selectors === 'string') {
        selectors = [selectors];
    }

    console.log('开始检测元素:', selectors);

    for (const selector of selectors) {
        try {
            const elements = document.querySelectorAll(selector);

            // 检查是否找到可见的元素
            for (const element of elements) {
                if (element &&
                    element.offsetWidth > 0 &&
                    element.offsetHeight > 0 &&
                    element.style.display !== 'none') {

                    console.log(`找到可见元素: ${selector}`);
                    return {
                        success: true,
                        selector: selector,
                        element: {
                            tagName: element.tagName,
                            className: element.className,
                            id: element.id,
                            textContent: element.textContent?.substring(0, 50) || ''
                        }
                    };
                }
            }
        } catch (error) {
            console.log(`检测选择器 ${selector} 时出错:`, error);
        }
    }

    return { success: false, message: "未找到任何匹配的可见元素" };
}

function detectAndClickElement(selectors) {
    /**
     * 检测元素并点击
     * @param {Array|string} selectors - CSS选择器数组或单个选择器
     * @returns {Object} 检测和点击结果
     */

    // 首先检测元素
    const detectResult = detectElement(selectors);

    if (!detectResult.success) {
        return detectResult;
    }

    // 找到元素，尝试点击
    const selector = detectResult.selector;

    try {
        // 使用querySelectorAll获取所有匹配的元素
        const elements = document.querySelectorAll(selector);

        if (elements.length === 0) {
            return { success: false, error: "未找到匹配的元素" };
        }

        let clickedCount = 0;
        const clickResults = [];

        // 循环点击所有匹配的元素
        elements.forEach((element, index) => {
            if (element &&
                element.offsetWidth > 0 &&
                element.offsetHeight > 0 &&
                element.style.display !== 'none') {

                try {
                    // 滚动到元素可见位置
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 延迟点击，确保滚动完成
                    setTimeout(() => {
                        element.click();
                        console.log(`成功点击元素 ${index + 1}/${elements.length}: ${selector}`);
                    }, 200 + index * 100); // 每个元素间隔100ms点击

                    clickedCount++;
                    clickResults.push({
                        index: index,
                        success: true,
                        element: {
                            tagName: element.tagName,
                            className: element.className,
                            textContent: element.textContent?.substring(0, 30) || ''
                        }
                    });
                } catch (error) {
                    console.log(`点击元素 ${index + 1} 时出错:`, error);
                    clickResults.push({
                        index: index,
                        success: false,
                        error: error.message
                    });
                }
            }
        });

        return {
            success: clickedCount > 0,
            selector: selector,
            action: 'clicked_multiple',
            totalElements: elements.length,
            clickedCount: clickedCount,
            results: clickResults
        };

    } catch (error) {
        console.log(`点击元素 ${selector} 时出错:`, error);
        return {
            success: false,
            error: `点击失败: ${error.message}`,
            selector: selector
        };
    }
}

// 配置驱动的检测功能
const ElementDetectionConfig = {
    // 从Python传入的配置
    config: null,

    // 设置配置
    setConfig: function(configData) {
        this.config = configData;
        console.log('JavaScript配置已更新:', this.config);
    },

    // 获取通用选择器
    getCommonSelector: function(name) {
        if (!this.config || !this.config.common_selectors) {
            return null;
        }
        return this.config.common_selectors[name];
    },

    // 获取Copilot选择器
    getCopilotSelector: function(elementName) {
        if (!this.config || !this.config.detection_classes || !this.config.detection_classes.copilot_elements) {
            return null;
        }

        const copilotClasses = this.config.detection_classes.copilot_elements;
        const classString = copilotClasses[elementName];

        if (!classString) {
            return null;
        }

        // 根据元素类型构建选择器
        if (elementName === 'chat_panel') {
            return `#${classString}`;
        } else {
            const classes = classString.split(' ');
            return '.' + classes.join('.');
        }
    },

    // 获取UI状态选择器
    getUIStateSelector: function(stateName) {
        if (!this.config || !this.config.detection_classes || !this.config.detection_classes.ui_states) {
            return null;
        }

        const uiStates = this.config.detection_classes.ui_states;
        const classString = uiStates[stateName];

        if (!classString) {
            return null;
        }

        // 创建多个可能的选择器
        const classes = classString.split(' ');
        const selectors = classes.map(cls => `.${cls}`);
        return selectors.join(', ');
    },

    // 获取自动点击元素配置
    getAutoClickElements: function() {
        if (!this.config || !this.config.auto_click_elements) {
            return [];
        }
        return this.config.auto_click_elements.filter(elem => elem.enabled);
    }
};

// 使用配置的检测函数
function detectElementByConfig(configKey, elementName = null) {
    /**
     * 使用配置检测元素
     * @param {string} configKey - 配置键名 ('common_selectors', 'copilot_elements', 'ui_states')
     * @param {string} elementName - 元素名称
     * @returns {Object} 检测结果
     */

    let selector = null;

    switch (configKey) {
        case 'common_selectors':
            selector = ElementDetectionConfig.getCommonSelector(elementName);
            break;
        case 'copilot_elements':
            selector = ElementDetectionConfig.getCopilotSelector(elementName);
            break;
        case 'ui_states':
            selector = ElementDetectionConfig.getUIStateSelector(elementName);
            break;
        default:
            return { success: false, error: `未知的配置键: ${configKey}` };
    }

    if (!selector) {
        return { success: false, error: `未找到配置: ${configKey}.${elementName}` };
    }

    console.log(`使用配置检测 ${configKey}.${elementName}: ${selector}`);
    return detectElement(selector);
}

// 使用配置的点击函数
function detectAndClickByConfig(configKey, elementName = null) {
    /**
     * 使用配置检测并点击元素
     * @param {string} configKey - 配置键名
     * @param {string} elementName - 元素名称
     * @returns {Object} 检测和点击结果
     */

    let selector = null;

    switch (configKey) {
        case 'common_selectors':
            selector = ElementDetectionConfig.getCommonSelector(elementName);
            break;
        case 'copilot_elements':
            selector = ElementDetectionConfig.getCopilotSelector(elementName);
            break;
        case 'ui_states':
            selector = ElementDetectionConfig.getUIStateSelector(elementName);
            break;
        default:
            return { success: false, error: `未知的配置键: ${configKey}` };
    }

    if (!selector) {
        return { success: false, error: `未找到配置: ${configKey}.${elementName}` };
    }

    console.log(`使用配置点击 ${configKey}.${elementName}: ${selector}`);
    return detectAndClickElement(selector);
}

// 自动处理对话框（JavaScript版本）
function autoHandleDialogs() {
    /**
     * 自动处理启用的对话框
     * @returns {Object} 处理结果
     */

    const autoClickElements = ElementDetectionConfig.getAutoClickElements();
    if (autoClickElements.length === 0) {
        return { success: false, message: "没有启用的自动点击元素" };
    }

    console.log(`开始自动处理 ${autoClickElements.length} 个对话框...`);

    let handledCount = 0;
    const results = [];

    for (const element of autoClickElements) {
        const name = element.name || '未知';
        const selector = element.selector || '';
        const description = element.description || '';

        console.log(`检测 '${name}': ${description}`);

        const result = detectAndClickElement(selector);

        if (result.success) {
            console.log(`✅ 已处理: ${name}`);
            handledCount++;
            results.push({ name, status: 'handled', selector });
        } else {
            console.log(`ℹ️ 未发现: ${name}`);
            results.push({ name, status: 'not_found', selector });
        }
    }

    console.log(`🎯 自动处理完成，共处理 ${handledCount} 个对话框`);

    return {
        success: handledCount > 0,
        handledCount,
        totalCount: autoClickElements.length,
        results
    };
}
function isWorking(){
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');
    if (!copilotChatPannel) {
        return { success: false, error: "未找到Copilot面板" };
    }

    // 检测多种工作状态指示器
    const stopButton = copilotChatPannel.querySelector('.action-label.codicon.codicon-stop-circle');
    const loadingSpinner = copilotChatPannel.querySelector('.codicon-loading, .codicon-sync-spin');
    const thinkingIndicator = copilotChatPannel.querySelector('[aria-label*="thinking"], [aria-label*="生成中"]');

    if (stopButton || loadingSpinner || thinkingIndicator) {
        console.log("Copilot正在工作");
        return {
            success: true,
            message: "Copilot正在工作",
            indicators: {
                stopButton: !!stopButton,
                loading: !!loadingSpinner,
                thinking: !!thinkingIndicator
            }
        };
    }
    console.log("Copilot未在工作");
    return {
        success: false,
        error: "Copilot未在工作",
        indicators: {
            stopButton: false,
            loading: false,
            thinking: false
        }
    };
}



function getLatestMessages(limit = 3) {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');
    if (!copilotChatPannel) {
        return { success: false, error: "未找到Copilot面板" };
    }

    try {
        // 查找聊天消息容器
        const messageContainer = copilotChatPannel.querySelector('.monaco-list-rows, .chat-messages, .messages-container');

        if (!messageContainer) {
            return { success: false, error: "未找到消息容器" };
        }

        // 获取所有消息元素
        const messageElements = messageContainer.querySelectorAll('.monaco-list-row, .chat-message, .message-item');

        if (messageElements.length === 0) {
            return { success: false, error: "未找到消息元素" };
        }

        // 获取最新的几条消息
        const messages = [];
        const startIndex = Math.max(0, messageElements.length - limit);

        for (let i = startIndex; i < messageElements.length; i++) {
            const element = messageElements[i];

            // 尝试提取消息文本
            let messageText = '';

            // 尝试多种选择器来获取消息文本
            const textSelectors = [
                '.monaco-tl-row',
                '.chat-message-content',
                '.message-content',
                '.rendered-markdown',
                '.markdown-content'
            ];

            for (const selector of textSelectors) {
                const textElement = element.querySelector(selector);
                if (textElement) {
                    messageText = textElement.textContent || textElement.innerText || '';
                    break;
                }
            }

            // 如果没有找到特定选择器，使用元素的文本内容
            if (!messageText) {
                messageText = element.textContent || element.innerText || '';
            }

            // 清理文本内容
            messageText = messageText.trim();

            if (messageText) {
                messages.push({
                    index: i,
                    text: messageText,
                    element: element.className || 'unknown'
                });
            }
        }

        return {
            success: true,
            messages: messages,
            totalFound: messageElements.length,
            returnedCount: messages.length
        };

    } catch (error) {
        return {
            success: false,
            error: "获取最新消息时出错: " + error.message
        };
    }
}

function sendMessage(message) {
    const copilotChatPannel = document.getElementById('workbench.parts.auxiliarybar');
    if (!copilotChatPannel) {
        return { success: false, error: "未找到Copilot面板" };
    }

    try {
        // 查找输入框
        const inputElement = copilotChatPannel.querySelector('textarea[aria-label*="Chat"], .monaco-scrollable-element .view-lines');

        if (!inputElement) {
            return { success: false, error: "未找到输入框" };
        }

        // 清空输入框并输入新消息
        if (inputElement.tagName === 'TEXTAREA') {
            // 如果是textarea元素
            inputElement.value = message;
            inputElement.dispatchEvent(new Event('input', { bubbles: true }));
            inputElement.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
            // 如果是Monaco编辑器的view-lines元素
            inputElement.textContent = message;
            inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // 查找并点击发送按钮
        const sendButton = copilotChatPannel.querySelector('[aria-label*="Send"], [title*="Send"], button[aria-label*="发送"]');

        if (sendButton) {
            // 等待一小段时间确保输入已处理
            setTimeout(() => {
                sendButton.click();
            }, 100);

            return {
                success: true,
                message: "消息已发送",
                messageLength: message.length
            };
        } else {
            return { success: false, error: "未找到发送按钮" };
        }

    } catch (error) {
        return {
            success: false,
            error: "发送消息时出错: " + error.message
        };
    }
}