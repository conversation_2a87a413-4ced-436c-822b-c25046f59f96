-- =============================================
-- 插入增强版上下文图生成Prompt模板
-- =============================================

USE ProjectManagementAI;
GO

-- 检查是否已存在ContextDiagramGeneration类型的模板
IF NOT EXISTS (SELECT 1 FROM PromptTemplates WHERE TaskType = 'ContextDiagramGeneration' AND Name = '增强版上下文图生成模板')
BEGIN
    -- 获取设计生成分类ID
    DECLARE @CategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '设计生成');
    
    INSERT INTO PromptTemplates (
        Name, 
        Description, 
        CategoryId,
        TaskType, 
        Content, 
        Parameters, 
        TemplateType,
        IsDefault, 
        IsEnabled, 
        CreatedBy, 
        CreatedTime
    )
    VALUES (
        '增强版上下文图生成模板',
        '结合项目背景和业务需求的专业上下文图生成模板，支持完整的系统架构和外部实体设计',
        @CategoryId,
        'ContextDiagramGeneration',
        N'你是一个专业的系统架构师和业务分析师。请根据以下项目信息和需求，为"{{project_name}}"项目生成一个完整的系统上下文图。

## 项目背景信息
{{project_background}}

## 项目需求分析
{{project_requirements}}

{{#if requirement_document_info}}
## 特定需求文档
{{requirement_document_info}}
{{/if}}

## 系统架构信息
{{system_architecture}}

## 生成要求

### 重要说明
**请仔细分析上述项目需求信息，根据实际的功能需求、用户故事和验收标准来设计系统上下文图，识别真实的外部实体和系统边界。**

### 上下文图设计原则
1. **基于需求驱动设计**
   - 仔细分析项目需求文档中的功能性需求
   - 根据用户故事识别外部参与者（Actor）
   - 基于验收标准确定系统边界和接口
   - 考虑非功能性需求对系统架构的影响

2. **外部实体识别**
   - 用户角色：不同类型的系统用户
   - 外部系统：需要集成的第三方系统
   - 数据源：外部数据提供方
   - 服务提供商：外部服务接口

3. **系统边界定义**
   - 明确系统的核心功能边界
   - 识别系统的输入和输出
   - 定义系统与外部实体的交互方式
   - 考虑数据流和控制流

4. **交互关系设计**
   - 明确定义系统与外部实体的关系
   - 标注主要的数据流方向
   - 考虑双向交互和单向交互
   - 体现业务流程的逻辑顺序

## 输出格式要求

**重要：请只返回纯Mermaid代码，不要包含任何解释文字、markdown标记或代码块标记。**

输出格式示例（请严格按照此格式）：

flowchart TD
    %% 外部用户
    User[用户]
    Admin[管理员]
    Developer[开发者]
    
    %% 外部系统
    EmailService[邮件服务]
    PaymentGateway[支付网关]
    CloudStorage[云存储服务]
    
    %% 系统核心
    subgraph SystemBoundary["{{project_name}}系统边界"]
        CoreSystem[核心系统]
        Database[(数据库)]
        APIGateway[API网关]
    end
    
    %% 用户交互
    User --> APIGateway
    Admin --> APIGateway
    Developer --> APIGateway
    
    %% 系统内部流
    APIGateway --> CoreSystem
    CoreSystem --> Database
    
    %% 外部服务交互
    CoreSystem --> EmailService
    CoreSystem --> PaymentGateway
    CoreSystem --> CloudStorage

## 输出要求

**严格要求：**
1. **只返回Mermaid代码**: 不要包含任何解释文字、标题、说明或markdown格式
2. **不要使用代码块**: 不要使用```mermaid```标记，直接输出flowchart开头的代码
3. **确保语法正确**: 严格遵循Mermaid流程图语法
4. **节点命名清晰**: 使用有意义的节点名称和标签
5. **关系完整**: 确保所有重要的交互关系都被正确定义
6. **系统边界明确**: 使用subgraph明确标识系统边界
7. **业务逻辑**: 体现{{project_name}}项目的具体业务逻辑和数据流

{{#if requirementDocumentId}}
## 相关需求文档
请特别参考需求文档ID: {{requirementDocumentId}} 中的具体业务需求进行设计。
{{/if}}

## 设计指导原则

1. **优先考虑实际需求**：请根据上述项目需求文档中的具体功能需求来设计外部实体，而不是机械地使用通用实体列表
2. **业务流程驱动**：确保上下文图能够支持项目需求中描述的所有核心业务流程
3. **接口完整性**：设计合适的接口和交互来保证系统的完整性和一致性
4. **可扩展性**：考虑未来可能的功能扩展需求
5. **安全性考虑**：为敏感交互设计合适的安全边界

**最终要求**：
请基于以上要求和项目的实际需求，生成一个完整、专业、符合{{project_name}}项目业务逻辑的系统上下文图。

**输出格式**：直接输出以"flowchart"开头的纯Mermaid代码，不要包含任何其他内容。

**特别注意**：如果项目需求文档中包含具体的外部系统和用户角色描述，请优先使用这些信息而不是通用的推荐实体。',
        N'[
            {"name": "projectId", "type": "int", "required": true, "description": "项目ID"},
            {"name": "diagramFormat", "type": "string", "required": false, "description": "图表格式", "default": "Mermaid"},
            {"name": "includeExternalSystems", "type": "boolean", "required": false, "description": "是否包含外部系统", "default": true},
            {"name": "requirementDocumentId", "type": "int", "required": false, "description": "需求文档ID"}
        ]',
        'System', -- TemplateType
        1, -- IsDefault
        1, -- IsEnabled
        1, -- CreatedBy (admin user)
        GETDATE()
    );

    PRINT '增强版上下文图生成模板插入成功';
END
ELSE
BEGIN
    PRINT '增强版上下文图生成模板已存在，跳过插入';
END

-- 更新旧的默认模板为非默认
UPDATE PromptTemplates 
SET IsDefault = 0 
WHERE TaskType = 'ContextDiagramGeneration' 
  AND Name != '增强版上下文图生成模板' 
  AND IsDefault = 1;

-- 验证插入结果
SELECT 
    Id, Name, TaskType, IsDefault, IsEnabled, CreatedTime
FROM PromptTemplates 
WHERE TaskType = 'ContextDiagramGeneration'
ORDER BY IsDefault DESC, CreatedTime DESC;

PRINT '上下文图模板更新完成！';
PRINT '增强版上下文图生成模板已成功插入并设置为默认模板';
GO
