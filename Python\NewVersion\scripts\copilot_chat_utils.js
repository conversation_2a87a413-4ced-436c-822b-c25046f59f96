/**
 * VSCode Copilot Chat 工具函数
 * 用于获取和操作Copilot聊天记录
 */

/**
 * 提取消息的实际内容
 * @param {Element} element - DOM元素
 * @returns {Object} 消息内容信息
 */
function extractMessageContent(element) {
    try {
        // 尝试多种方式提取消息内容
        let messageText = '';
        let cleanText = '';

        // 方法1: 查找特定的消息内容容器
        const messageContainers = [
            '.monaco-tl-contents',
            '.chat-message-content',
            '.message-content',
            '.copilot-message',
            '[data-message-content]',
            '.monaco-tl-row .monaco-tl-contents',
            '.chat-response-content',
            '.markdown-content'
        ];

        for (const selector of messageContainers) {
            const container = element.querySelector(selector);
            if (container) {
                // 优先使用innerText，它会过滤掉CSS样式
                messageText = container.innerText || container.textContent || '';
                if (messageText.trim()) {
                    cleanText = cleanMessageText(messageText);
                    break;
                }
            }
        }

        // 方法1.5: 尝试查找代码块或预格式化文本
        if (!messageText.trim()) {
            const codeElements = element.querySelectorAll('pre, code, .monaco-editor-background, .hljs, .language-javascript');
            for (const codeEl of codeElements) {
                const text = codeEl.textContent || codeEl.innerText || '';
                if (text.trim() && text.length > 10) {
                    messageText = text;
                    cleanText = cleanMessageText(messageText);
                    break;
                }
            }
        }

        // 方法1.6: 专门查找包含代码的元素
        if (!messageText.trim()) {
            const allElements = element.querySelectorAll('*');
            for (const el of allElements) {
                const text = el.textContent || el.innerText || '';
                // 查找包含JavaScript代码特征的文本
                if (text.includes('function') && text.includes('quickSort') && text.length > 50) {
                    messageText = text;
                    cleanText = extractCodeFromText(text);
                    break;
                }
            }
        }

        // 方法2: 如果没找到特定容器，尝试从子元素中提取
        if (!messageText.trim()) {
            const textElements = element.querySelectorAll('span, div, p');
            const textParts = [];
            const excludePatterns = [
                'monaco-list',
                'background-color',
                'var(--vscode',
                '.focused',
                'list_id_',
                'GitHub Copilot',
                'Used',
                'reference'
            ];

            for (const textEl of textElements) {
                const text = textEl.textContent || textEl.innerText || '';
                // 过滤掉CSS样式、空白和无关内容
                if (text.trim() &&
                    text.length > 3 &&
                    !excludePatterns.some(pattern => text.includes(pattern))) {

                    // 检查是否是有意义的文本（包含字母或数字）
                    if (/[a-zA-Z0-9\u4e00-\u9fff]/.test(text)) {
                        textParts.push(text.trim());
                    }
                }
            }

            messageText = textParts.join(' ');
            cleanText = cleanMessageText(messageText);
        }

        // 方法3: 如果还是没有内容，直接使用innerText（优先）或textContent
        if (!messageText.trim()) {
            messageText = element.innerText || element.textContent || '';
            cleanText = cleanMessageText(messageText);
        }

        return {
            text: cleanText || messageText,
            cleanText: cleanText,
            rawText: messageText
        };

    } catch (error) {
        return {
            text: element.textContent || '',
            cleanText: '',
            rawText: element.textContent || ''
        };
    }
}

/**
 * 从文本中提取代码内容
 * @param {string} text - 包含代码的文本
 * @returns {string} 提取的代码
 */
function extractCodeFromText(text) {
    if (!text) return '';

    // 查找JavaScript代码模式
    const codePatterns = [
        // 匹配 // 注释开始的代码块
        /\/\/[^]*?(?=\n\n|\n$|$)/g,
        // 匹配function开始的代码块
        /function\s+\w+[^]*?(?=\n\n|\n$|$)/g,
        // 匹配const/let/var开始的代码块
        /(?:const|let|var)\s+\w+[^]*?(?=\n\n|\n$|$)/g
    ];

    for (const pattern of codePatterns) {
        const matches = text.match(pattern);
        if (matches && matches.length > 0) {
            return matches.join('\n').trim();
        }
    }

    // 如果没有匹配到特定模式，尝试提取包含代码关键字的部分
    if (text.includes('function') || text.includes('const') || text.includes('//')) {
        // 移除前面的无关内容，保留从第一个代码关键字开始的部分
        const codeStart = Math.min(
            text.indexOf('//') !== -1 ? text.indexOf('//') : Infinity,
            text.indexOf('function') !== -1 ? text.indexOf('function') : Infinity,
            text.indexOf('const') !== -1 ? text.indexOf('const') : Infinity
        );

        if (codeStart !== Infinity) {
            return text.substring(codeStart).trim();
        }
    }

    return cleanMessageText(text);
}

/**
 * 清理消息文本，移除无关内容
 * @param {string} text - 原始文本
 * @returns {string} 清理后的文本
 */
function cleanMessageText(text) {
    if (!text) return '';

    // 移除CSS相关内容和无关信息
    let cleaned = text
        .replace(/monaco-list[^}]*}/g, '')
        .replace(/background-color[^;]*;/g, '')
        .replace(/var\(--vscode[^)]*\)/g, '')
        .replace(/\.focused[^}]*}/g, '')
        .replace(/list_id_\d+/g, '')
        .replace(/monaco-drag-image[^,]*,?/g, '')
        .replace(/\.monaco-[^,\s]*[,\s]*/g, '')
        .replace(/GitHub CopilotUsed \d+ reference\.?/gi, '')
        .replace(/Used \d+ reference/gi, '')
        .replace(/\.\s*\.\s*\.\s*/g, '') // 移除多个点
        .replace(/\s+/g, ' ')
        .trim();

    // 如果清理后的内容太短或主要是符号，返回空字符串
    if (cleaned.length < 5 || /^[.,\s\-_]*$/.test(cleaned)) {
        return '';
    }

    return cleaned;
}

/**
 * 识别消息类型和作者
 * @param {Element} element - DOM元素
 * @returns {Object} 消息类型信息
 */
function identifyMessageType(element) {
    const text = element.textContent.toLowerCase();
    const html = element.innerHTML.toLowerCase();
    const className = element.className.toLowerCase();
    
    // 检查是否为用户消息
    const isUser = html.includes('data-message-author="user"') || 
                  className.includes('user') ||
                  element.querySelector('[data-message-author="user"]') !== null;
    
    // 检查是否为助手消息
    const isAssistant = html.includes('data-message-author="assistant"') ||
                       html.includes('copilot') ||
                       className.includes('assistant') ||
                       element.querySelector('[data-message-author="assistant"]') !== null;
    
    let type = 'unknown';
    let author = 'unknown';
    
    if (isUser) {
        type = 'user';
        author = 'user';
    } else if (isAssistant) {
        type = 'assistant';
        author = 'assistant';
    } else {
        // 尝试从内容推断
        if (text.includes('you:') || text.includes('user:')) {
            type = 'user';
            author = 'user';
        } else if (text.includes('copilot:') || text.includes('assistant:')) {
            type = 'assistant';
            author = 'assistant';
        }
    }
    
    return {
        type: type,
        author: author,
        isUser: isUser,
        isAssistant: isAssistant
    };
}

/**
 * 获取Copilot Chat面板的聊天记录
 * @returns {Object} 聊天记录结果
 */
function getCopilotChatRecords() {
    try {
        // 查找Copilot Chat面板
        const chatPanel = document.getElementById('workbench.panel.chat');
        if (!chatPanel) {
            return {
                success: false,
                error: "未找到Copilot Chat面板 (id='workbench.panel.chat')",
                records: []
            };
        }
        
        // 查找monaco-list-rows容器
        const listRowsContainer = chatPanel.querySelector('.monaco-list-rows');
        if (!listRowsContainer) {
            return {
                success: false,
                error: "未找到聊天记录容器 (class='monaco-list-rows')",
                records: []
            };
        }
        
        // 获取所有聊天记录行
        const chatRows = Array.from(listRowsContainer.querySelectorAll('.monaco-list-row'));
        const records = [];
        
        for (let i = 0; i < chatRows.length; i++) {
            const row = chatRows[i];
            
            // 提取实际的消息内容
            const messageContent = extractMessageContent(row);

            // 获取行的基本信息
            const rowInfo = {
                index: i,
                rowId: row.id || 'row-' + i,
                className: row.className,
                style: row.getAttribute('style') || '',
                ariaLabel: row.getAttribute('aria-label') || '',
                role: row.getAttribute('role') || '',

                // 内容信息 - 使用提取的消息内容
                textContent: messageContent.text,
                cleanContent: messageContent.cleanText,
                rawTextContent: row.textContent || '',
                innerText: row.innerText || '',
                innerHTML: row.innerHTML,

                // 尺寸和位置信息
                offsetHeight: row.offsetHeight,
                offsetWidth: row.offsetWidth,
                offsetTop: row.offsetTop,
                offsetLeft: row.offsetLeft,

                // 可见性
                isVisible: row.offsetParent !== null,

                // 子元素信息
                childElementCount: row.childElementCount,
                children: []
            };
            
            // 获取子元素信息
            for (let j = 0; j < row.children.length; j++) {
                const child = row.children[j];
                rowInfo.children.push({
                    tagName: child.tagName,
                    className: child.className,
                    textContent: child.textContent || '',
                    innerHTML: child.innerHTML
                });
            }
            
            // 识别消息类型和作者
            const messageType = identifyMessageType(row);
            rowInfo.messageType = messageType.type;
            rowInfo.author = messageType.author;
            rowInfo.isUserMessage = messageType.isUser;
            rowInfo.isAssistantMessage = messageType.isAssistant;
            
            records.push(rowInfo);
        }
        
        return {
            success: true,
            panelFound: true,
            containerFound: true,
            totalRecords: records.length,
            records: records,
            containerInfo: {
                className: listRowsContainer.className,
                childElementCount: listRowsContainer.childElementCount,
                scrollHeight: listRowsContainer.scrollHeight,
                scrollTop: listRowsContainer.scrollTop,
                clientHeight: listRowsContainer.clientHeight
            }
        };
        
    } catch (error) {
        return {
            success: false,
            error: "JavaScript执行异常: " + error.message,
            records: []
        };
    }
}

/**
 * 获取Copilot Chat面板摘要信息
 * @returns {Object} 面板摘要信息
 */
function getCopilotChatSummary() {
    try {
        // 查找Copilot Chat面板
        const chatPanel = document.getElementById('workbench.panel.chat');
        if (!chatPanel) {
            return {
                panelExists: false,
                error: "未找到Copilot Chat面板"
            };
        }
        
        // 查找monaco-list-rows容器
        const listRowsContainer = chatPanel.querySelector('.monaco-list-rows');
        
        const summary = {
            panelExists: true,
            panelInfo: {
                id: chatPanel.id,
                className: chatPanel.className,
                isVisible: chatPanel.offsetParent !== null,
                offsetHeight: chatPanel.offsetHeight,
                offsetWidth: chatPanel.offsetWidth
            },
            listRowsContainer: {
                exists: !!listRowsContainer,
                className: listRowsContainer ? listRowsContainer.className : null,
                childCount: listRowsContainer ? listRowsContainer.childElementCount : 0,
                scrollHeight: listRowsContainer ? listRowsContainer.scrollHeight : 0,
                scrollTop: listRowsContainer ? listRowsContainer.scrollTop : 0,
                clientHeight: listRowsContainer ? listRowsContainer.clientHeight : 0
            },
            chatRecordCount: 0,
            userMessageCount: 0,
            assistantMessageCount: 0,
            unknownMessageCount: 0
        };
        
        if (listRowsContainer) {
            const chatRows = listRowsContainer.querySelectorAll('.monaco-list-row');
            summary.chatRecordCount = chatRows.length;
            
            // 统计不同类型的消息
            chatRows.forEach(row => {
                const html = row.innerHTML.toLowerCase();
                if (html.includes('data-message-author="user"') || row.querySelector('[data-message-author="user"]')) {
                    summary.userMessageCount++;
                } else if (html.includes('data-message-author="assistant"') || row.querySelector('[data-message-author="assistant"]')) {
                    summary.assistantMessageCount++;
                } else {
                    summary.unknownMessageCount++;
                }
            });
        }
        
        return summary;
        
    } catch (error) {
        return {
            panelExists: false,
            error: "JavaScript执行异常: " + error.message
        };
    }
}

/**
 * 检查Copilot Chat面板是否存在
 * @returns {Object} 检查结果
 */
function checkCopilotChatPanel() {
    try {
        const panel = document.getElementById('workbench.panel.chat');
        return {
            exists: !!panel,
            visible: panel ? panel.offsetParent !== null : false,
            className: panel ? panel.className : null,
            hasContainer: panel ? !!panel.querySelector('.monaco-list-rows') : false
        };
    } catch (error) {
        return {
            exists: false,
            error: error.message
        };
    }
}
