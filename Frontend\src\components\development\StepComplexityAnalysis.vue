<template>
  <div class="complexity-analysis" v-loading="loading">
    <!-- 分析按钮 -->
    <div class="analysis-toolbar">
      <el-button type="primary" @click="analyzeComplexity" :loading="analyzing">
        <el-icon><TrendCharts /></el-icon>
        分析复杂度
      </el-button>
      <el-button @click="refreshAnalysis" v-if="analysis">
        <el-icon><Refresh /></el-icon>
        重新分析
      </el-button>
    </div>

    <!-- 分析结果 -->
    <div v-if="analysis" class="analysis-result">
      <!-- 复杂度概览 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-card shadow="never" class="metric-card">
            <div class="metric-content">
              <div class="metric-value" :class="getComplexityScoreClass(analysis.complexityScore)">
                {{ analysis.complexityScore }}
              </div>
              <div class="metric-label">复杂度评分</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ analysis.estimatedHours }}h</div>
              <div class="metric-label">预估工时</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="metric-card">
            <div class="metric-content">
              <div class="metric-value" :class="getRiskLevelClass(analysis.riskLevel)">
                {{ getRiskLevelLabel(analysis.riskLevel) }}
              </div>
              <div class="metric-label">风险等级</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ analysis.complexityFactors?.length || 0 }}</div>
              <div class="metric-label">复杂度因子</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 复杂度因子 -->
      <el-card shadow="never" class="mb-4" v-if="analysis.complexityFactors?.length">
        <template #header>
          <span>复杂度因子分析</span>
        </template>

        <div class="factors-grid">
          <div
            v-for="factor in analysis.complexityFactors"
            :key="factor.name"
            class="factor-item"
          >
            <div class="factor-header">
              <span class="factor-name">{{ factor.name }}</span>
              <el-tag size="small" :type="getFactorImpactColor(factor.impact) as any">
                {{ getFactorImpactLabel(factor.impact) }}
              </el-tag>
            </div>
            <div class="factor-description">{{ factor.description }}</div>
            <div class="factor-score">
              <el-progress
                :percentage="(factor.score / 10) * 100"
                :stroke-width="6"
                :show-text="false"
                :color="getFactorScoreColor(factor.score)"
              />
              <span class="score-text">{{ factor.score }}/10</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 优化建议 -->
      <el-card shadow="never" v-if="analysis.recommendations?.length">
        <template #header>
          <span>优化建议</span>
        </template>

        <div class="recommendations">
          <div
            v-for="(recommendation, index) in analysis.recommendations"
            :key="index"
            class="recommendation-item"
          >
            <div class="recommendation-icon">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="recommendation-content">
              <div class="recommendation-title">{{ recommendation.title }}</div>
              <div class="recommendation-description">{{ recommendation.description }}</div>
              <div v-if="recommendation.priority" class="recommendation-priority">
                <el-tag size="small" :type="getPriorityColor(recommendation.priority) as any">
                  {{ recommendation.priority }} 优先级
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 分析时间 -->
      <div class="analysis-meta">
        <el-text size="small" type="info">
          分析时间: {{ formatDateTime(analysis.analyzedAt) }}
        </el-text>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-state">
      <el-empty description="暂无复杂度分析数据">
        <el-button type="primary" @click="analyzeComplexity">开始分析</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, Refresh, Sunny } from '@element-plus/icons-vue'
import type { StepComplexityAnalysis, DevelopmentStep } from '@/types/development'
import { DevelopmentService } from '@/services/development'

// Props
interface Props {
  stepId: number
  step: DevelopmentStep
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const analyzing = ref(false)
const analysis = ref<StepComplexityAnalysis>()

// 生命周期
onMounted(() => {
  loadAnalysis()
})

// 方法
const loadAnalysis = async () => {
  try {
    loading.value = true
    analysis.value = await DevelopmentService.getStepComplexityAnalysis(props.stepId)
  } catch (error) {
    console.error('加载复杂度分析失败:', error)
    // 不显示错误消息，因为可能是首次分析
  } finally {
    loading.value = false
  }
}

const analyzeComplexity = async () => {
  try {
    analyzing.value = true
    analysis.value = await DevelopmentService.analyzeStepComplexity(props.stepId)
    ElMessage.success('复杂度分析完成')
  } catch (error) {
    console.error('分析复杂度失败:', error)
    ElMessage.error('分析复杂度失败')
  } finally {
    analyzing.value = false
  }
}

const refreshAnalysis = () => {
  analyzeComplexity()
}

// 样式辅助方法
const getComplexityScoreClass = (score: number) => {
  if (score <= 3) return 'score-low'
  if (score <= 7) return 'score-medium'
  return 'score-high'
}

const getRiskLevelClass = (level: string) => {
  const classes: Record<string, string> = {
    'Low': 'risk-low',
    'Medium': 'risk-medium',
    'High': 'risk-high'
  }
  return classes[level] || 'risk-medium'
}

const getRiskLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    'Low': '低风险',
    'Medium': '中风险',
    'High': '高风险'
  }
  return labels[level] || level
}

const getFactorImpactColor = (impact: string) => {
  const colors: Record<string, string> = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'danger'
  }
  return colors[impact] || 'info'
}

const getFactorImpactLabel = (impact: string) => {
  const labels: Record<string, string> = {
    'Low': '低影响',
    'Medium': '中影响',
    'High': '高影响'
  }
  return labels[impact] || impact
}

const getFactorScoreColor = (score: number) => {
  if (score <= 3) return '#67c23a'
  if (score <= 7) return '#e6a23c'
  return '#f56c6c'
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'danger'
  }
  return colors[priority] || 'info'
}

const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.complexity-analysis {
  padding: 16px 0;
}

.analysis-toolbar {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
}

.metric-content {
  padding: 16px 0;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.metric-value.score-low {
  color: #67c23a;
}

.metric-value.score-medium {
  color: #e6a23c;
}

.metric-value.score-high {
  color: #f56c6c;
}

.metric-value.risk-low {
  color: #67c23a;
}

.metric-value.risk-medium {
  color: #e6a23c;
}

.metric-value.risk-high {
  color: #f56c6c;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.factors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.factor-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.factor-name {
  font-weight: 500;
}

.factor-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.factor-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  font-size: 12px;
  color: #909399;
  min-width: 40px;
}

.recommendations {
  space-y: 16px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 12px;
}

.recommendation-icon {
  color: #e6a23c;
  font-size: 18px;
  margin-top: 2px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.recommendation-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.recommendation-priority {
  margin-top: 8px;
}

.analysis-meta {
  margin-top: 16px;
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
