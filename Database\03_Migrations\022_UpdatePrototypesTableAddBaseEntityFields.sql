-- 更新Prototypes表，添加BaseEntity中的缺失字段
-- 修复 "Invalid column name" 错误

USE [ProjectManagementAI]
GO

PRINT '开始更新Prototypes表结构...'

-- 检查表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND type in (N'U'))
BEGIN
    PRINT 'Prototypes表已存在，开始添加缺失字段...'
    
    -- 添加IsDeleted字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'IsDeleted')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD [IsDeleted] [bit] NOT NULL DEFAULT 0
        PRINT '✓ 添加IsDeleted字段'
    END
    ELSE
    BEGIN
        PRINT '- IsDeleted字段已存在'
    END
    
    -- 添加DeletedTime字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'DeletedTime')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD [DeletedTime] [datetime2](7) NULL
        PRINT '✓ 添加DeletedTime字段'
    END
    ELSE
    BEGIN
        PRINT '- DeletedTime字段已存在'
    END
    
    -- 添加DeletedBy字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'DeletedBy')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD [DeletedBy] [int] NULL
        PRINT '✓ 添加DeletedBy字段'
    END
    ELSE
    BEGIN
        PRINT '- DeletedBy字段已存在'
    END
    
    -- 添加Version字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'Version')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD [Version] [int] NOT NULL DEFAULT 1
        PRINT '✓ 添加Version字段'
    END
    ELSE
    BEGIN
        PRINT '- Version字段已存在'
    END
    
    -- 添加Remarks字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'Remarks')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD [Remarks] [nvarchar](500) NULL
        PRINT '✓ 添加Remarks字段'
    END
    ELSE
    BEGIN
        PRINT '- Remarks字段已存在'
    END
    
    -- 修改CreatedBy字段为可空（如果需要）
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'CreatedBy' AND is_nullable = 0)
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ALTER COLUMN [CreatedBy] [int] NULL
        PRINT '✓ 修改CreatedBy字段为可空'
    END
    ELSE
    BEGIN
        PRINT '- CreatedBy字段已经是可空或不存在'
    END
    
    -- 添加外键约束（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Prototypes_Users_DeletedBy')
    BEGIN
        ALTER TABLE [dbo].[Prototypes] ADD CONSTRAINT [FK_Prototypes_Users_DeletedBy] 
        FOREIGN KEY([DeletedBy]) REFERENCES [dbo].[Users] ([Id])
        PRINT '✓ 添加DeletedBy外键约束'
    END
    ELSE
    BEGIN
        PRINT '- DeletedBy外键约束已存在'
    END
    
    -- 添加索引（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_IsDeleted')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_Prototypes_IsDeleted] ON [dbo].[Prototypes]([IsDeleted])
        PRINT '✓ 创建IsDeleted索引'
    END
    ELSE
    BEGIN
        PRINT '- IsDeleted索引已存在'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_CreatedBy')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_Prototypes_CreatedBy] ON [dbo].[Prototypes]([CreatedBy])
        PRINT '✓ 创建CreatedBy索引'
    END
    ELSE
    BEGIN
        PRINT '- CreatedBy索引已存在'
    END
    
    -- 添加字段注释
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'IsDeleted'))
    BEGIN
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'软删除标记，0=未删除，1=已删除', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'IsDeleted'
        PRINT '✓ 添加IsDeleted字段注释'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'DeletedTime'))
    BEGIN
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'删除时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'DeletedTime'
        PRINT '✓ 添加DeletedTime字段注释'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'DeletedBy'))
    BEGIN
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'删除者用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'DeletedBy'
        PRINT '✓ 添加DeletedBy字段注释'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'Version'))
    BEGIN
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'版本号，用于乐观锁并发控制', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Version'
        PRINT '✓ 添加Version字段注释'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = 'Remarks'))
    BEGIN
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Remarks'
        PRINT '✓ 添加Remarks字段注释'
    END
    
    PRINT 'Prototypes表结构更新完成！'
END
ELSE
BEGIN
    PRINT '❌ Prototypes表不存在，请先运行020_CreatePrototypesTable.sql脚本'
END

-- 验证表结构
PRINT ''
PRINT '=== 验证表结构 ==='
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '可空',
    COLUMN_DEFAULT as '默认值'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Prototypes' 
ORDER BY ORDINAL_POSITION

PRINT ''
PRINT 'Prototypes表结构更新脚本执行完成！'
GO
