<Application x:Class="ProjectManagementAI.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ProjectManagementAI.WPF.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design 主题 -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局样式 -->
            <Style x:Key="MaterialCard" TargetType="materialDesign:Card">
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>

            <Style x:Key="MaterialButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>

            <!-- 值转换器 -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
