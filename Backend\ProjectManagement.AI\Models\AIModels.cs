using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.AI.Models;

/// <summary>
/// AI配置
/// </summary>
public class AIConfiguration
{
    /// <summary>
    /// 默认提供商
    /// </summary>
    public string DefaultProvider { get; set; } = "Azure";

    /// <summary>
    /// 默认配置
    /// </summary>
    public AIModelConfig? DefaultConfig { get; set; }

    /// <summary>
    /// 任务映射配置
    /// </summary>
    public Dictionary<string, string> TaskMapping { get; set; } = new();

    /// <summary>
    /// 提供商配置
    /// </summary>
    public Dictionary<string, object> Providers { get; set; } = new();
}



/// <summary>
/// 需求分析结果
/// </summary>
public class RequirementAnalysisResult
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string ProjectDescription { get; set; } = string.Empty;

    /// <summary>
    /// 功能性需求列表
    /// </summary>
    public List<string> FunctionalRequirements { get; set; } = new();

    /// <summary>
    /// 非功能性需求列表
    /// </summary>
    public List<string> NonFunctionalRequirements { get; set; } = new();

    /// <summary>
    /// 业务规则
    /// </summary>
    public List<string> BusinessRules { get; set; } = new();

    /// <summary>
    /// 用户角色
    /// </summary>
    public List<string> UserRoles { get; set; } = new();

    /// <summary>
    /// 用例列表
    /// </summary>
    public List<UseCase> UseCases { get; set; } = new();

    /// <summary>
    /// 数据实体
    /// </summary>
    public List<DataEntity> DataEntities { get; set; } = new();

    /// <summary>
    /// 技术栈建议
    /// </summary>
    public TechStackSuggestion TechStack { get; set; } = new();

    /// <summary>
    /// 风险评估
    /// </summary>
    public List<RiskAssessment> Risks { get; set; } = new();

    /// <summary>
    /// 工作量估算
    /// </summary>
    public WorkloadEstimation WorkloadEstimation { get; set; } = new();

    /// <summary>
    /// 可行性评分（1-10）
    /// </summary>
    public int FeasibilityScore { get; set; }

    /// <summary>
    /// 复杂度评分（1-10）
    /// </summary>
    public int ComplexityScore { get; set; }

    /// <summary>
    /// 分析置信度（0-1）
    /// </summary>
    public float ConfidenceScore { get; set; }

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    public string AIModel { get; set; } = string.Empty;
}

/// <summary>
/// 用例
/// </summary>
public class UseCase
{
    /// <summary>
    /// 用例名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 用例描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参与者
    /// </summary>
    public List<string> Actors { get; set; } = new();

    /// <summary>
    /// 前置条件
    /// </summary>
    public List<string> Preconditions { get; set; } = new();

    /// <summary>
    /// 主要流程
    /// </summary>
    public List<string> MainFlow { get; set; } = new();

    /// <summary>
    /// 替代流程
    /// </summary>
    public List<string> AlternativeFlows { get; set; } = new();

    /// <summary>
    /// 后置条件
    /// </summary>
    public List<string> Postconditions { get; set; } = new();
}

/// <summary>
/// 数据实体
/// </summary>
public class DataEntity
{
    /// <summary>
    /// 实体名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 实体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 属性列表
    /// </summary>
    public List<EntityAttribute> Attributes { get; set; } = new();

    /// <summary>
    /// 关系列表
    /// </summary>
    public List<EntityRelationship> Relationships { get; set; } = new();
}

/// <summary>
/// 实体属性
/// </summary>
public class EntityAttribute
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// 是否主键
    /// </summary>
    public bool IsPrimaryKey { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 实体关系
/// </summary>
public class EntityRelationship
{
    /// <summary>
    /// 关系类型
    /// </summary>
    public string RelationType { get; set; } = string.Empty;

    /// <summary>
    /// 目标实体
    /// </summary>
    public string TargetEntity { get; set; } = string.Empty;

    /// <summary>
    /// 关系描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 技术栈建议
/// </summary>
public class TechStackSuggestion
{
    /// <summary>
    /// 前端技术
    /// </summary>
    public List<string> Frontend { get; set; } = new();

    /// <summary>
    /// 后端技术
    /// </summary>
    public List<string> Backend { get; set; } = new();

    /// <summary>
    /// 数据库
    /// </summary>
    public List<string> Database { get; set; } = new();

    /// <summary>
    /// 部署技术
    /// </summary>
    public List<string> Deployment { get; set; } = new();

    /// <summary>
    /// 第三方服务
    /// </summary>
    public List<string> ThirdPartyServices { get; set; } = new();
}

/// <summary>
/// 风险评估
/// </summary>
public class RiskAssessment
{
    /// <summary>
    /// 风险描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 风险等级（1-5）
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// 影响范围
    /// </summary>
    public string Impact { get; set; } = string.Empty;

    /// <summary>
    /// 缓解措施
    /// </summary>
    public string Mitigation { get; set; } = string.Empty;
}

/// <summary>
/// 工作量估算
/// </summary>
public class WorkloadEstimation
{
    /// <summary>
    /// 总工时（小时）
    /// </summary>
    public decimal TotalHours { get; set; }

    /// <summary>
    /// 开发工时
    /// </summary>
    public decimal DevelopmentHours { get; set; }

    /// <summary>
    /// 测试工时
    /// </summary>
    public decimal TestingHours { get; set; }

    /// <summary>
    /// 部署工时
    /// </summary>
    public decimal DeploymentHours { get; set; }

    /// <summary>
    /// 预计工期（天）
    /// </summary>
    public int EstimatedDays { get; set; }

    /// <summary>
    /// 建议团队规模
    /// </summary>
    public int RecommendedTeamSize { get; set; }
}

/// <summary>
/// 代码类型枚举
/// </summary>
public enum CodeType
{
    /// <summary>
    /// 前端代码
    /// </summary>
    Frontend,

    /// <summary>
    /// 后端代码
    /// </summary>
    Backend,

    /// <summary>
    /// 数据库脚本
    /// </summary>
    Database,

    /// <summary>
    /// 前端Vue组件
    /// </summary>
    VueComponent,

    /// <summary>
    /// 后端C# API
    /// </summary>
    CSharpAPI,

    /// <summary>
    /// SQL Server脚本
    /// </summary>
    SQLScript,

    /// <summary>
    /// 配置文件
    /// </summary>
    Configuration,

    /// <summary>
    /// 单元测试
    /// </summary>
    UnitTest,

    /// <summary>
    /// 集成测试
    /// </summary>
    IntegrationTest
}

/// <summary>
/// 代码生成结果
/// </summary>
public class CodeGenerationResult
{
    /// <summary>
    /// 生成的代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 生成的文件列表
    /// </summary>
    public List<GeneratedFile> GeneratedFiles { get; set; } = new();

    /// <summary>
    /// 生成说明
    /// </summary>
    public string Instructions { get; set; } = string.Empty;

    /// <summary>
    /// 代码类型
    /// </summary>
    public CodeType CodeType { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 代码说明
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 依赖项
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    public string AIModel { get; set; } = string.Empty;
}

/// <summary>
/// 代码质量分析结果
/// </summary>
public class CodeQualityAnalysisResult
{
    /// <summary>
    /// 总体评分（1-10）
    /// </summary>
    public int OverallScore { get; set; }

    /// <summary>
    /// 质量评分（1-10）
    /// </summary>
    public int QualityScore { get; set; }

    /// <summary>
    /// 可读性评分
    /// </summary>
    public int ReadabilityScore { get; set; }

    /// <summary>
    /// 可维护性评分
    /// </summary>
    public int MaintainabilityScore { get; set; }

    /// <summary>
    /// 性能评分
    /// </summary>
    public int PerformanceScore { get; set; }

    /// <summary>
    /// 安全性评分
    /// </summary>
    public int SecurityScore { get; set; }

    /// <summary>
    /// 发现的问题
    /// </summary>
    public List<CodeIssue> Issues { get; set; } = new();

    /// <summary>
    /// 改进建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 代码问题
/// </summary>
public class CodeIssue
{
    /// <summary>
    /// 问题类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public string Severity { get; set; } = string.Empty;

    /// <summary>
    /// 问题描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int LineNumber { get; set; }

    /// <summary>
    /// 修复建议
    /// </summary>
    public string FixSuggestion { get; set; } = string.Empty;
}

/// <summary>
/// 生成的文件
/// </summary>
public class GeneratedFile
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 步骤分解结果
/// </summary>
public class StepDecompositionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 分解生成的步骤
    /// </summary>
    public List<DecomposedStepData> Steps { get; set; } = new();

    /// <summary>
    /// AI分析结果
    /// </summary>
    public string? AIAnalysisResult { get; set; }

    /// <summary>
    /// 总预估工时
    /// </summary>
    public decimal TotalEstimatedHours { get; set; }

    /// <summary>
    /// 步骤依赖关系
    /// </summary>
    public List<StepDependencyData> Dependencies { get; set; } = new();
}

/// <summary>
/// 分解的步骤数据
/// </summary>
public class DecomposedStepData
{
    /// <summary>
    /// 步骤名称
    /// </summary>
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// 步骤描述
    /// </summary>
    public string? StepDescription { get; set; }

    /// <summary>
    /// 步骤类型
    /// </summary>
    public string? StepType { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string? Priority { get; set; }

    /// <summary>
    /// 预估工时
    /// </summary>
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechnologyStack { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string? FileType { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 组件类型
    /// </summary>
    public string? ComponentType { get; set; }

    /// <summary>
    /// AI提示词
    /// </summary>
    public string? AIPrompt { get; set; }
}

/// <summary>
/// 步骤依赖数据
/// </summary>
public class StepDependencyData
{
    /// <summary>
    /// 源步骤索引
    /// </summary>
    public int FromStep { get; set; }

    /// <summary>
    /// 目标步骤索引
    /// </summary>
    public int ToStep { get; set; }

    /// <summary>
    /// 依赖类型
    /// </summary>
    public string DependencyType { get; set; } = "Sequential";
}

/// <summary>
/// 步骤分解选项
/// </summary>
public class StepDecompositionOptions
{
    /// <summary>
    /// 分解粒度（细粒度、中等粒度、粗粒度）
    /// </summary>
    public string Granularity { get; set; } = "Medium";

    /// <summary>
    /// 技术栈偏好
    /// </summary>
    public string? TechnologyPreference { get; set; }

    /// <summary>
    /// 最大子步骤数量
    /// </summary>
    public int MaxSubSteps { get; set; } = 10;

    /// <summary>
    /// 是否包含测试步骤
    /// </summary>
    public bool IncludeTestSteps { get; set; } = true;

    /// <summary>
    /// 是否包含文档步骤
    /// </summary>
    public bool IncludeDocumentationSteps { get; set; } = false;

    /// <summary>
    /// 自定义分解要求
    /// </summary>
    public string? CustomRequirements { get; set; }
}
