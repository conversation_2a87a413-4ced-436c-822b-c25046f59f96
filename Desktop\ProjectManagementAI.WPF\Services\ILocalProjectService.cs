using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 本地项目服务接口
    /// </summary>
    public interface ILocalProjectService
    {
        /// <summary>
        /// 扫描指定目录下的项目
        /// </summary>
        /// <param name="rootPath">根目录路径</param>
        /// <returns>发现的项目列表</returns>
        Task<IEnumerable<LocalProject>> ScanProjectsAsync(string rootPath);

        /// <summary>
        /// 分析单个项目
        /// </summary>
        /// <param name="projectPath">项目路径</param>
        /// <returns>项目分析结果</returns>
        Task<LocalProject> AnalyzeProjectAsync(string projectPath);

        /// <summary>
        /// 获取项目的代码统计信息
        /// </summary>
        /// <param name="projectPath">项目路径</param>
        /// <returns>代码统计信息</returns>
        Task<CodeStatistics> GetCodeStatisticsAsync(string projectPath);

        /// <summary>
        /// 检测项目类型
        /// </summary>
        /// <param name="projectPath">项目路径</param>
        /// <returns>项目类型</returns>
        ProjectType DetectProjectType(string projectPath);

        /// <summary>
        /// 获取项目依赖信息
        /// </summary>
        /// <param name="projectPath">项目路径</param>
        /// <returns>依赖信息</returns>
        Task<IEnumerable<ProjectDependency>> GetDependenciesAsync(string projectPath);
    }

    /// <summary>
    /// 本地项目信息
    /// </summary>
    public class LocalProject
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public ProjectType Type { get; set; }
        public string Framework { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public DateTime LastModified { get; set; }
        public long SizeInBytes { get; set; }
        public CodeStatistics Statistics { get; set; } = new();
        public IList<ProjectDependency> Dependencies { get; set; } = new List<ProjectDependency>();
        public IList<string> TechnologyStack { get; set; } = new List<string>();
        public string Description { get; set; } = string.Empty;
        public double ComplexityScore { get; set; }
        public double QualityScore { get; set; }
        public IList<string> Issues { get; set; } = new List<string>();
        public IList<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// 项目类型枚举
    /// </summary>
    public enum ProjectType
    {
        Unknown,
        DotNetCore,
        DotNetFramework,
        NodeJs,
        Python,
        Java,
        React,
        Vue,
        Angular,
        Flutter,
        Unity,
        Xamarin,
        Go,
        Rust,
        Cpp,
        PHP,
        Ruby
    }

    /// <summary>
    /// 代码统计信息
    /// </summary>
    public class CodeStatistics
    {
        public int TotalFiles { get; set; }
        public int CodeFiles { get; set; }
        public int TotalLines { get; set; }
        public int CodeLines { get; set; }
        public int CommentLines { get; set; }
        public int BlankLines { get; set; }
        public Dictionary<string, int> FilesByExtension { get; set; } = new();
        public Dictionary<string, int> LinesByLanguage { get; set; } = new();
        public double CommentRatio { get; set; }
        public double CodeDensity { get; set; }
    }

    /// <summary>
    /// 项目依赖信息
    /// </summary>
    public class ProjectDependency
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // NuGet, npm, pip, etc.
        public bool IsDevDependency { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime? LastUpdated { get; set; }
        public bool HasSecurityIssues { get; set; }
        public bool IsOutdated { get; set; }
    }
}
