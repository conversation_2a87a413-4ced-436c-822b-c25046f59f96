using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;

namespace ProjectManagement.API.Hubs;

/// <summary>
/// AI处理进度实时通信Hub
/// 功能: 提供AI任务处理进度的实时通信功能
/// 支持: 需求分析进度、代码生成进度、测试生成进度等
/// </summary>
[Authorize]
public class AIProcessingHub : Hub
{
    private readonly ILogger<AIProcessingHub> _logger;

    public AIProcessingHub(ILogger<AIProcessingHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("用户 {UserId} 连接到AIProcessingHub", userId);
        
        await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("用户 {UserId} 从AIProcessingHub断开连接", userId);
        
        if (exception != null)
        {
            _logger.LogError(exception, "用户 {UserId} AI处理Hub连接异常断开", userId);
        }

        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 订阅AI任务进度
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    public async Task SubscribeToTask(string taskId, string taskType)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Task_{taskType}_{taskId}";
        
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("用户 {UserId} 订阅AI任务 {TaskId} ({TaskType})", userId, taskId, taskType);
    }

    /// <summary>
    /// 取消订阅AI任务进度
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    public async Task UnsubscribeFromTask(string taskId, string taskType)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Task_{taskType}_{taskId}";
        
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("用户 {UserId} 取消订阅AI任务 {TaskId} ({TaskType})", userId, taskId, taskType);
    }

    /// <summary>
    /// 通知AI任务开始
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="description">任务描述</param>
    public async Task NotifyTaskStarted(string taskId, string taskType, string description)
    {
        var groupName = $"Task_{taskType}_{taskId}";
        
        _logger.LogInformation("AI任务开始: {TaskId} ({TaskType}) - {Description}", taskId, taskType, description);
        
        await Clients.Group(groupName).SendAsync("TaskStarted", new
        {
            TaskId = taskId,
            TaskType = taskType,
            Description = description,
            Status = "Started",
            Progress = 0,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知AI任务进度更新
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="progress">进度百分比 (0-100)</param>
    /// <param name="message">进度消息</param>
    public async Task NotifyTaskProgress(string taskId, string taskType, int progress, string message)
    {
        var groupName = $"Task_{taskType}_{taskId}";
        
        _logger.LogInformation("AI任务进度更新: {TaskId} ({TaskType}) - {Progress}% - {Message}", taskId, taskType, progress, message);
        
        await Clients.Group(groupName).SendAsync("TaskProgress", new
        {
            TaskId = taskId,
            TaskType = taskType,
            Progress = progress,
            Message = message,
            Status = "InProgress",
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知AI任务完成
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="result">任务结果</param>
    public async Task NotifyTaskCompleted(string taskId, string taskType, object result)
    {
        var groupName = $"Task_{taskType}_{taskId}";
        
        _logger.LogInformation("AI任务完成: {TaskId} ({TaskType})", taskId, taskType);
        
        await Clients.Group(groupName).SendAsync("TaskCompleted", new
        {
            TaskId = taskId,
            TaskType = taskType,
            Result = result,
            Status = "Completed",
            Progress = 100,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知AI任务失败
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="error">错误信息</param>
    public async Task NotifyTaskFailed(string taskId, string taskType, string error)
    {
        var groupName = $"Task_{taskType}_{taskId}";
        
        _logger.LogError("AI任务失败: {TaskId} ({TaskType}) - {Error}", taskId, taskType, error);
        
        await Clients.Group(groupName).SendAsync("TaskFailed", new
        {
            TaskId = taskId,
            TaskType = taskType,
            Error = error,
            Status = "Failed",
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知需求分析进度
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="stage">分析阶段</param>
    /// <param name="progress">进度百分比</param>
    public async Task NotifyRequirementAnalysisProgress(int projectId, string stage, int progress)
    {
        var groupName = $"RequirementAnalysis_{projectId}";
        
        _logger.LogInformation("需求分析进度: 项目 {ProjectId} - {Stage} - {Progress}%", projectId, stage, progress);
        
        await Clients.Group(groupName).SendAsync("RequirementAnalysisProgress", new
        {
            ProjectId = projectId,
            Stage = stage,
            Progress = progress,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知代码生成进度
    /// </summary>
    /// <param name="taskId">代码生成任务ID</param>
    /// <param name="fileName">当前生成的文件名</param>
    /// <param name="completedFiles">已完成文件数</param>
    /// <param name="totalFiles">总文件数</param>
    public async Task NotifyCodeGenerationProgress(int taskId, string fileName, int completedFiles, int totalFiles)
    {
        var groupName = $"CodeGeneration_{taskId}";
        var progress = totalFiles > 0 ? (completedFiles * 100 / totalFiles) : 0;
        
        _logger.LogInformation("代码生成进度: 任务 {TaskId} - {FileName} - {CompletedFiles}/{TotalFiles}", taskId, fileName, completedFiles, totalFiles);
        
        await Clients.Group(groupName).SendAsync("CodeGenerationProgress", new
        {
            TaskId = taskId,
            CurrentFile = fileName,
            CompletedFiles = completedFiles,
            TotalFiles = totalFiles,
            Progress = progress,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知测试生成进度
    /// </summary>
    /// <param name="taskId">测试任务ID</param>
    /// <param name="testType">测试类型</param>
    /// <param name="progress">进度百分比</param>
    public async Task NotifyTestGenerationProgress(int taskId, string testType, int progress)
    {
        var groupName = $"TestGeneration_{taskId}";
        
        _logger.LogInformation("测试生成进度: 任务 {TaskId} - {TestType} - {Progress}%", taskId, testType, progress);
        
        await Clients.Group(groupName).SendAsync("TestGenerationProgress", new
        {
            TaskId = taskId,
            TestType = testType,
            Progress = progress,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知部署进度
    /// </summary>
    /// <param name="taskId">部署任务ID</param>
    /// <param name="stage">部署阶段</param>
    /// <param name="progress">进度百分比</param>
    public async Task NotifyDeploymentProgress(int taskId, string stage, int progress)
    {
        var groupName = $"Deployment_{taskId}";
        
        _logger.LogInformation("部署进度: 任务 {TaskId} - {Stage} - {Progress}%", taskId, stage, progress);
        
        await Clients.Group(groupName).SendAsync("DeploymentProgress", new
        {
            TaskId = taskId,
            Stage = stage,
            Progress = progress,
            Timestamp = DateTime.UtcNow
        });
    }
}
