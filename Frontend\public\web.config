<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- 启用URL重写 -->
    <rewrite>
      <rules>
        <!-- 处理Vue Router的History模式 -->
        <rule name="Handle History Mode and HTML5 routing" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <!-- 不是文件 -->
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <!-- 不是目录 -->
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <!-- 不是API请求 -->
            <add input="{REQUEST_URI}" pattern="^/Frontend/(api|backend)" negate="true" />
            <!-- 不是静态资源 -->
            <add input="{REQUEST_URI}" pattern="\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$" negate="true" />
          </conditions>
          <action type="Rewrite" url="/Frontend/index.html" />
        </rule>
      </rules>
    </rewrite>

    <!-- 设置默认文档 -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
      </files>
    </defaultDocument>
  </system.webServer>
</configuration>
