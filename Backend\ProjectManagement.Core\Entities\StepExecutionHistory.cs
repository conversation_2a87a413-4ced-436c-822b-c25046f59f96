using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 步骤执行历史实体 - 对应DatabaseSchema.sql中的StepExecutionHistory表
/// 功能: 记录开发步骤的执行历史和详细信息
/// 支持: 执行追踪、性能分析、错误诊断、AI调用记录
/// </summary>
[SugarTable("StepExecutionHistory", "步骤执行历史管理表")]
public class StepExecutionHistory : BaseEntity
{
    /// <summary>
    /// 关联的开发步骤ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的开发步骤ID")]
    public int StepId { get; set; }

    /// <summary>
    /// 执行ID（用于标识同一次执行的多个记录）
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "执行ID")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "执行开始时间")]
    public DateTime ExecutionStartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 执行结束时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "执行结束时间")]
    public DateTime? ExecutionEndTime { get; set; }

    /// <summary>
    /// 执行持续时间（秒）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "执行持续时间（秒）")]
    public int? ExecutionDuration { get; set; }

    /// <summary>
    /// 执行状态: Running(运行中), Completed(已完成), Failed(失败), Cancelled(已取消)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "执行状态")]
    public string ExecutionStatus { get; set; } = "Running";

    /// <summary>
    /// 执行结果: Success(成功), Failed(失败), Partial(部分成功), Timeout(超时)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "执行结果")]
    public string? ExecutionResult { get; set; }

    /// <summary>
    /// 使用的AI提供商
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "使用的AI提供商")]
    public string? AIProvider { get; set; }

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "使用的AI模型")]
    public string? AIModel { get; set; }

    /// <summary>
    /// 实际使用的提示词
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "实际使用的提示词")]
    public string? PromptUsed { get; set; }

    /// <summary>
    /// AI生成的代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成的代码")]
    public string? GeneratedCode { get; set; }

    /// <summary>
    /// 输出文件列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "输出文件列表（JSON格式）")]
    public string? OutputFiles { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行日志
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "执行日志")]
    public string? ExecutionLog { get; set; }

    /// <summary>
    /// 执行器类型: Manual(手动), Automated(自动), VSCode(VSCode插件), API(API调用)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "执行器类型")]
    public string ExecutorType { get; set; } = "Manual";

    /// <summary>
    /// 执行器信息（JSON格式，包含版本、配置等）
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "执行器信息")]
    public string? ExecutorInfo { get; set; }

    /// <summary>
    /// VSCode版本
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "VSCode版本")]
    public string? VSCodeVersion { get; set; }

    /// <summary>
    /// 插件版本（Augment/Copilot等）
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "插件版本")]
    public string? PluginVersion { get; set; }

    #region 导航属性

    /// <summary>
    /// 关联的开发步骤
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DevelopmentStep? Step { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Creator { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Updater { get; set; }

    #endregion

    #region 业务方法

    /// <summary>
    /// 开始执行
    /// </summary>
    public void StartExecution()
    {
        ExecutionStartTime = DateTime.Now;
        ExecutionStatus = "Running";
        ExecutionResult = null;
        ErrorMessage = null;
    }

    /// <summary>
    /// 完成执行
    /// </summary>
    public void CompleteExecution(string result = "Success")
    {
        ExecutionEndTime = DateTime.Now;
        ExecutionStatus = "Completed";
        ExecutionResult = result;
        CalculateDuration();
    }

    /// <summary>
    /// 执行失败
    /// </summary>
    public void FailExecution(string errorMessage)
    {
        ExecutionEndTime = DateTime.Now;
        ExecutionStatus = "Failed";
        ExecutionResult = "Failed";
        ErrorMessage = errorMessage;
        CalculateDuration();
    }

    /// <summary>
    /// 取消执行
    /// </summary>
    public void CancelExecution(string reason = "")
    {
        ExecutionEndTime = DateTime.Now;
        ExecutionStatus = "Cancelled";
        ExecutionResult = "Cancelled";
        if (!string.IsNullOrEmpty(reason))
        {
            ErrorMessage = $"执行被取消: {reason}";
        }
        CalculateDuration();
    }

    /// <summary>
    /// 计算执行持续时间
    /// </summary>
    private void CalculateDuration()
    {
        if (ExecutionEndTime.HasValue)
        {
            ExecutionDuration = (int)(ExecutionEndTime.Value - ExecutionStartTime).TotalSeconds;
        }
    }

    /// <summary>
    /// 添加执行日志
    /// </summary>
    public void AddLog(string message)
    {
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        var logEntry = $"[{timestamp}] {message}";
        
        if (string.IsNullOrEmpty(ExecutionLog))
        {
            ExecutionLog = logEntry;
        }
        else
        {
            ExecutionLog += Environment.NewLine + logEntry;
        }
    }

    /// <summary>
    /// 设置AI信息
    /// </summary>
    public void SetAIInfo(string provider, string model, string prompt)
    {
        AIProvider = provider;
        AIModel = model;
        PromptUsed = prompt;
    }

    /// <summary>
    /// 设置执行器信息
    /// </summary>
    public void SetExecutorInfo(string executorType, string? vscodeVersion = null, string? pluginVersion = null, string? additionalInfo = null)
    {
        ExecutorType = executorType;
        VSCodeVersion = vscodeVersion;
        PluginVersion = pluginVersion;
        ExecutorInfo = additionalInfo;
    }

    /// <summary>
    /// 获取执行时长的友好显示
    /// </summary>
    public string GetDurationDisplay()
    {
        if (!ExecutionDuration.HasValue)
            return "未知";

        var duration = ExecutionDuration.Value;
        if (duration < 60)
            return $"{duration}秒";
        else if (duration < 3600)
            return $"{duration / 60}分{duration % 60}秒";
        else
            return $"{duration / 3600}小时{(duration % 3600) / 60}分";
    }

    /// <summary>
    /// 获取执行状态的友好显示
    /// </summary>
    public string GetStatusDisplay()
    {
        return ExecutionStatus switch
        {
            "Running" => "运行中",
            "Completed" => "已完成",
            "Failed" => "失败",
            "Cancelled" => "已取消",
            _ => ExecutionStatus
        };
    }

    /// <summary>
    /// 获取执行结果的友好显示
    /// </summary>
    public string GetResultDisplay()
    {
        return ExecutionResult switch
        {
            "Success" => "成功",
            "Failed" => "失败",
            "Partial" => "部分成功",
            "Timeout" => "超时",
            "Cancelled" => "已取消",
            _ => ExecutionResult ?? "未知"
        };
    }

    #endregion

    #region 静态方法

    /// <summary>
    /// 创建新的执行历史记录
    /// </summary>
    public static StepExecutionHistory CreateNew(int stepId, string executorType = "Manual")
    {
        return new StepExecutionHistory
        {
            StepId = stepId,
            ExecutionId = Guid.NewGuid().ToString(),
            ExecutorType = executorType,
            ExecutionStartTime = DateTime.Now,
            ExecutionStatus = "Running"
        };
    }

    #endregion
}
