<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Helpers\**" />
    <Compile Remove="IntegrationTests\**" />
    <Compile Remove="TestData\**" />
    <Compile Remove="UnitTests\**" />
    <EmbeddedResource Remove="Helpers\**" />
    <EmbeddedResource Remove="IntegrationTests\**" />
    <EmbeddedResource Remove="TestData\**" />
    <EmbeddedResource Remove="UnitTests\**" />
    <None Remove="Helpers\**" />
    <None Remove="IntegrationTests\**" />
    <None Remove="TestData\**" />
    <None Remove="UnitTests\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectManagement.API\ProjectManagement.API.csproj" />
    <ProjectReference Include="..\ProjectManagement.Core\ProjectManagement.Core.csproj" />
    <ProjectReference Include="..\ProjectManagement.Data\ProjectManagement.Data.csproj" />
    <ProjectReference Include="..\ProjectManagement.AI\ProjectManagement.AI.csproj" />
  </ItemGroup>

</Project>
