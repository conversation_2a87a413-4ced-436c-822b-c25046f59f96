using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 自动化任务队列实体 - 统一的任务调度和分发系统
/// 功能: 管理本地自动化客户端的任务队列，支持多种任务类型
/// 支持: 任务调度、状态跟踪、客户端分配、错误处理
/// </summary>
[SugarTable("AutomationTasks", "自动化任务队列表")]
public class AutomationTask : BaseEntity
{
    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 任务来源类型: DevelopmentStep, CodeGenerationTask, TestTask, DeploymentTask, Manual
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "任务来源类型")]
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// 任务来源记录ID，关联到具体的源表记录
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务来源记录ID")]
    public int? SourceId { get; set; }

    /// <summary>
    /// 任务类型: CodeGeneration, FileOperation, GitOperation, VSCodeOperation, BuildOperation, TestExecution
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "任务类型")]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "任务名称")]
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 任务描述
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true, ColumnDescription = "任务描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 任务详细数据，JSON格式存储
    /// 包含: prompt, filePath, language, dependencies, config等
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "任务详细数据，JSON格式")]
    public string? TaskData { get; set; }

    /// <summary>
    /// 任务状态: Pending, Assigned, InProgress, Completed, Failed, Cancelled
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "任务状态")]
    public string Status { get; set; } = "Pending";

    /// <summary>
    /// 任务优先级: Low, Medium, High, Critical
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "任务优先级")]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// 分配给的客户端标识
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "分配给的客户端标识")]
    public string? AssignedTo { get; set; }

    /// <summary>
    /// 任务开始时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务开始时间")]
    public DateTime? StartedTime { get; set; }

    /// <summary>
    /// 任务完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务完成时间")]
    public DateTime? CompletedTime { get; set; }

    /// <summary>
    /// 任务执行结果，JSON格式存储
    /// 包含: generatedCode, outputPath, executionLog等
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "任务执行结果，JSON格式")]
    public string? Result { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "重试次数")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "最大重试次数")]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 任务超时时间（分钟）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务超时时间（分钟）")]
    public int? TimeoutMinutes { get; set; }

    /// <summary>
    /// 任务依赖，JSON格式存储依赖的任务ID列表
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "任务依赖，JSON格式")]
    public string? Dependencies { get; set; }

    /// <summary>
    /// 任务标签，用于分类和筛选
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "任务标签")]
    public string? Tags { get; set; }

    // 导航属性
    /// <summary>
    /// 关联的项目
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }
}

/// <summary>
/// 任务状态枚举
/// </summary>
public static class AutomationTaskStatus
{
    public const string Pending = "Pending";       // 待处理
    public const string Assigned = "Assigned";     // 已分配
    public const string InProgress = "InProgress"; // 执行中
    public const string Completed = "Completed";   // 已完成
    public const string Failed = "Failed";         // 失败
    public const string Cancelled = "Cancelled";   // 已取消
}

/// <summary>
/// 任务类型枚举
/// </summary>
public static class AutomationTaskType
{
    public const string CodeGeneration = "CodeGeneration";     // 代码生成
    public const string FileOperation = "FileOperation";       // 文件操作
    public const string GitOperation = "GitOperation";         // Git操作
    public const string VSCodeOperation = "VSCodeOperation";   // VSCode操作
    public const string BuildOperation = "BuildOperation";     // 构建操作
    public const string TestExecution = "TestExecution";       // 测试执行
    public const string DatabaseOperation = "DatabaseOperation"; // 数据库操作
    public const string DeploymentOperation = "DeploymentOperation"; // 部署操作
}

/// <summary>
/// 任务优先级枚举
/// </summary>
public static class AutomationTaskPriority
{
    public const string Low = "Low";           // 低优先级
    public const string Medium = "Medium";     // 中等优先级
    public const string High = "High";         // 高优先级
    public const string Critical = "Critical"; // 紧急
}

/// <summary>
/// 任务来源类型枚举
/// </summary>
public static class AutomationTaskSourceType
{
    public const string DevelopmentStep = "DevelopmentStep";           // 开发步骤
    public const string CodeGenerationTask = "CodeGenerationTask";     // 代码生成任务
    public const string TestTask = "TestTask";                         // 测试任务
    public const string DeploymentTask = "DeploymentTask";             // 部署任务
    public const string Manual = "Manual";                             // 手动创建
}
