using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs.LangChain;
using ProjectManagement.AI.Services;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// LangChain推理链API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LangChainController : ControllerBase
    {
        private readonly LangChainService _langChainService;
        private readonly ILogger<LangChainController> _logger;

        public LangChainController(LangChainService langChainService, ILogger<LangChainController> logger)
        {
            _langChainService = langChainService;
            _logger = logger;
        }

        /// <summary>
        /// 执行推理链
        /// </summary>
        [HttpPost("execute")]
        public async Task<ActionResult<ChainExecutionResponse>> ExecuteChain([FromBody] ChainExecutionRequest request)
        {
            try
            {
                _logger.LogInformation("执行推理链请求: {ChainType}", request.ChainType);

                // 获取推理链定义
                var chainDefinition = _langChainService.GetPredefinedChain(request.ChainType);

                // 构建上下文
                var context = new ChainContext
                {
                    ProjectId = request.ProjectId,
                    UserId = "current-user" // 从认证信息获取
                };

                // 添加变量
                context.AddVariable("projectName", "项目名称"); // 从数据库获取
                context.AddVariable("projectDescription", "项目描述");
                context.AddVariable("userMessage", request.UserMessage);

                foreach (var variable in request.Variables)
                {
                    context.AddVariable(variable.Key, variable.Value);
                }

                // 执行推理链
                var result = await _langChainService.ExecuteChainAsync(chainDefinition, context);

                // 构建响应
                var response = new ChainExecutionResponse
                {
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage,
                    ChainName = result.ChainName,
                    FinalOutput = result.FinalOutput,
                    Duration = result.Duration,
                    Steps = result.Steps.Select(s => new ChainStepSummary
                    {
                        StepName = s.StepName,
                        Success = s.Success,
                        Output = s.Output,
                        Duration = s.Duration
                    }).ToList(),
                    Metadata = result.Metadata
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推理链执行失败");
                return StatusCode(500, new { error = "推理链执行失败", details = ex.Message });
            }
        }

        /// <summary>
        /// 获取可用的推理链类型
        /// </summary>
        [HttpGet("chains")]
        public ActionResult<List<object>> GetAvailableChains()
        {
            var chains = new List<object>
            {
                new { type = "project-analysis", name = "项目分析链", description = "全面分析项目的各个方面" },
                new { type = "tech-evaluation", name = "技术评估链", description = "评估技术方案的可行性和优劣" },
                new { type = "risk-assessment", name = "风险评估链", description = "全面评估项目风险" },
                new { type = "requirement-analysis", name = "需求分析链", description = "深入分析和整理用户需求" }
            };

            return Ok(chains);
        }

        /// <summary>
        /// 获取推理链定义
        /// </summary>
        [HttpGet("chains/{chainType}")]
        public ActionResult<ChainDefinition> GetChainDefinition(string chainType)
        {
            try
            {
                var chainDefinition = _langChainService.GetPredefinedChain(chainType);
                return Ok(chainDefinition);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取推理链定义失败: {ChainType}", chainType);
                return StatusCode(500, new { error = "获取推理链定义失败" });
            }
        }
    }
}
