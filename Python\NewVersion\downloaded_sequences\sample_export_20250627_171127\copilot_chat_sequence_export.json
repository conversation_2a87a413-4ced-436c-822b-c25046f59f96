{"ExportTime": "2025-06-27T16:24:21.1219179+08:00", "Version": "1.0", "TotalCount": 1, "Sequences": [{"Name": "增强CopilotChat代码生成流程", "Description": "增强CopilotChat代码生成流程", "Category": "CopilotChat自动化", "Tags": [], "Notes": "增强CopilotChat代码生成流程", "IsActive": true, "Steps": [{"StepOrder": 1, "ActionType": "wait", "LogicType": "image_condition", "Description": "不存在则点击", "Parameters": {"condition_template_id": 4, "target_template_id": 1, "target_action": "click", "condition_confidence": 0.7, "target_confidence": 0.7, "reverse_condition": true}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": null, "LoopVariable": "", "GroupId": ""}, {"StepOrder": 2, "ActionType": "click", "LogicType": null, "Description": "新建会话", "Parameters": {"wait_after": 1}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": null, "LoopVariable": "", "GroupId": ""}, {"StepOrder": 3, "ActionType": "click", "LogicType": null, "Description": "点击消息输入框", "Parameters": {"wait_after": 1}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": null, "LoopVariable": "", "GroupId": ""}, {"StepOrder": 4, "ActionType": "input", "LogicType": null, "Description": "输入消息", "Parameters": {}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": null, "LoopVariable": "", "GroupId": ""}, {"StepOrder": 5, "ActionType": "click", "LogicType": null, "Description": "点击发送按钮", "Parameters": {"wait_after": 1}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": null, "LoopVariable": "", "GroupId": ""}, {"StepOrder": 6, "ActionType": "click", "LogicType": null, "Description": "出现重试按钮，则需要点击重试按钮", "Parameters": {}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "ConditionExpression": "", "JumpToStepId": null, "LoopCount": -1, "LoopVariable": "retry_count", "GroupId": "retry_loop"}]}]}