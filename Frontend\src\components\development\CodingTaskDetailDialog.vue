<template>
  <el-dialog
    v-model="visible"
    title="编码任务详情"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="task" class="task-detail">
      <!-- 任务基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">
            {{ task.taskName }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(task.status)">
              {{ getStatusText(task.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ task.priority }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分配给">
            <span v-if="task.assignedUserName">{{ task.assignedUserName }}</span>
            <el-text v-else type="info">未分配</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="预估工时">
            <span v-if="task.estimatedHours">{{ task.estimatedHours }} 小时</span>
            <el-text v-else type="info">未设置</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="实际工时">
            <span v-if="task.actualHours">{{ task.actualHours }} 小时</span>
            <el-text v-else type="info">0 小时</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">
            <span v-if="task.startDate">{{ formatDate(task.startDate) }}</span>
            <el-text v-else type="info">未设置</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="截止日期">
            <span v-if="task.dueDate">{{ formatDate(task.dueDate) }}</span>
            <el-text v-else type="info">未设置</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="技术栈" :span="2">
            <span v-if="task.technologyStack">{{ task.technologyStack }}</span>
            <el-text v-else type="info">未设置</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="标签" :span="2">
            <div v-if="task.tags && task.tags.length > 0" class="tags-container">
              <el-tag
                v-for="tag in task.tags"
                :key="tag"
                size="small"
                style="margin-right: 8px;"
              >
                {{ tag }}
              </el-tag>
            </div>
            <el-text v-else type="info">无标签</el-text>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 任务描述 -->
      <div class="detail-section">
        <h3 class="section-title">任务描述</h3>
        <div class="description-content">
          {{ task.description || '暂无描述' }}
        </div>
      </div>

      <!-- 关联步骤 -->
      <div class="detail-section">
        <h3 class="section-title">
          关联开发步骤
          <el-button type="primary" size="small" @click="showAddStepsDialog = true">
            添加步骤
          </el-button>
        </h3>
        <div v-if="relatedSteps.length > 0" class="steps-list">
          <el-table :data="relatedSteps" style="width: 100%">
            <el-table-column prop="stepName" label="步骤名称" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStepStatusType(row.status)" size="small">
                  {{ getStepStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="estimatedHours" label="预估工时" width="100">
              <template #default="{ row }">
                <span v-if="row.estimatedHours">{{ row.estimatedHours }}h</span>
                <el-text v-else type="info">-</el-text>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="viewStepDetail(row)">查看</el-button>
                <el-button size="small" type="danger" @click="removeStep(row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-empty v-else description="暂无关联步骤" />
      </div>

      <!-- 进度统计 -->
      <div class="detail-section">
        <h3 class="section-title">进度统计</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="progress-item">
              <div class="progress-label">总步骤数</div>
              <div class="progress-value">{{ relatedSteps.length }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="progress-item">
              <div class="progress-label">已完成</div>
              <div class="progress-value">{{ completedStepsCount }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="progress-item">
              <div class="progress-label">进行中</div>
              <div class="progress-value">{{ inProgressStepsCount }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="progress-item">
              <div class="progress-label">完成率</div>
              <div class="progress-value">{{ progressPercentage }}%</div>
            </div>
          </el-col>
        </el-row>
        <div class="progress-bar-container">
          <el-progress
            :percentage="progressPercentage"
            :stroke-width="12"
            :show-text="false"
          />
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section" v-if="task.notes">
        <h3 class="section-title">备注信息</h3>
        <div class="notes-content">
          {{ task.notes }}
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editTask">编辑任务</el-button>
        <el-button @click="viewInSteps">在步骤中查看</el-button>
      </div>
    </template>

    <!-- 添加步骤对话框 -->
    <AddStepsToTaskDialog
      v-model="showAddStepsDialog"
      :task="task"
      :project-id="task?.projectId"
      @success="handleAddStepsSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddStepsToTaskDialog from './AddStepsToTaskDialog.vue'
import { codingTaskService } from '@/services/codingTask'
import type { ElTagType } from '@/types/element-plus'
import dayjs from 'dayjs'

// Props
interface Props {
  modelValue: boolean
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
  edit: [task: any]
}>()

const router = useRouter()

// 响应式数据
const relatedSteps = ref<any[]>([])
const showAddStepsDialog = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const completedStepsCount = computed(() => {
  return relatedSteps.value.filter(step => step.status === 'Completed').length
})

const inProgressStepsCount = computed(() => {
  return relatedSteps.value.filter(step => step.status === 'InProgress').length
})

const progressPercentage = computed(() => {
  if (relatedSteps.value.length === 0) return 0
  return Math.round((completedStepsCount.value / relatedSteps.value.length) * 100)
})

// 监听器
watch(() => props.task, (newTask) => {
  if (newTask) {
    loadRelatedSteps()
  }
}, { immediate: true })

// 方法
const loadRelatedSteps = async () => {
  if (!props.task?.id) return

  try {
    relatedSteps.value = await codingTaskService.getTaskSteps(props.task.id)
  } catch (error) {
    console.error('加载关联步骤失败:', error)
    ElMessage.error('加载关联步骤失败')
    relatedSteps.value = []
  }
}

const handleClose = () => {
  visible.value = false
}

const editTask = () => {
  emit('edit', props.task)
  handleClose()
}

const viewInSteps = () => {
  router.push(`/development/steps?projectId=${props.task?.projectId}&taskId=${props.task?.id}`)
  handleClose()
}

const viewStepDetail = (step: any) => {
  // TODO: 显示步骤详情
  ElMessage.info('查看步骤详情功能正在开发中')
}

const removeStep = async (step: any) => {
  if (!props.task?.id) return

  try {
    await ElMessageBox.confirm(
      `确定要从任务中移除步骤 "${step.stepName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const removedCount = await codingTaskService.removeStepsFromTask(props.task.id, { stepIds: [step.id] })

    if (removedCount > 0) {
      ElMessage.success('步骤移除成功')
      loadRelatedSteps()
      emit('refresh')
    } else {
      ElMessage.warning('步骤移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除步骤失败:', error)
      ElMessage.error('移除步骤失败')
    }
  }
}

const handleAddStepsSuccess = () => {
  ElMessage.success('步骤添加成功')
  loadRelatedSteps()
  emit('refresh')
}

// 辅助方法
const getStatusType = (status: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    NotStarted: 'info',
    InProgress: 'warning',
    Completed: 'success',
    Blocked: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    NotStarted: '待开始',
    InProgress: '进行中',
    Completed: '已完成',
    Blocked: '已阻塞'
  }
  return textMap[status] || status
}

const getStepStatusType = (status: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    NotStarted: 'info',
    InProgress: 'warning',
    Completed: 'success',
    Blocked: 'danger',
    Cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStepStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    NotStarted: '未开始',
    InProgress: '进行中',
    Completed: '已完成',
    Blocked: '已阻塞',
    Cancelled: '已取消'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    High: 'danger',
    Medium: 'warning',
    Low: 'info'
  }
  return typeMap[priority] || 'info'
}

const formatDate = (date: Date | string) => {
  return dayjs(date).format('YYYY-MM-DD')
}
</script>

<style scoped>
.task-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.description-content,
.notes-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.steps-list {
  margin-top: 16px;
}

.progress-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.progress-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.progress-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.progress-bar-container {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
