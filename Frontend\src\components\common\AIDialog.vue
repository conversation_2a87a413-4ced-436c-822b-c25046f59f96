<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
    class="ai-dialog"
  >
    <!-- 对话区域 -->
    <div class="chat-container">
      <!-- 对话历史 -->
      <div class="chat-messages" ref="messagesContainer">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="ai-avatar">
            <el-icon size="32"><Service /></el-icon>
          </div>
          <div class="message-content">
            <h3>{{ welcomeTitle }}</h3>
            <div v-if="welcomeDescription" v-html="welcomeDescription"></div>
            <div v-if="contextInfo" class="context-info">
              <el-card shadow="never" class="context-card">
                <template #header>
                  <span>当前上下文</span>
                </template>
                <div class="context-details">
                  <div v-if="contextInfo.projectName" class="context-item">
                    <span class="label">项目：</span>
                    <span>{{ contextInfo.projectName }}</span>
                  </div>
                  <div v-if="contextInfo.taskType" class="context-item">
                    <span class="label">任务类型：</span>
                    <el-tag size="small" type="primary">{{ contextInfo.taskType }}</el-tag>
                  </div>
                  <div v-if="contextInfo.aiProvider" class="context-item">
                    <span class="label">AI模型：</span>
                    <el-tag size="small" type="success">{{ contextInfo.aiProvider }}</el-tag>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 对话消息 -->
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="message-item"
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
        >
          <div class="message-avatar">
            <el-avatar v-if="message.type === 'user'" :src="userAvatar" size="small">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div v-else class="ai-avatar">
              <el-icon size="20"><ChatDotRound /></el-icon>
            </div>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">{{ message.type === 'user' ? '您' : 'AI助手' }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.type === 'ai'" class="message-actions">
              <el-button size="small" text @click="copyMessage(message.content)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              <el-button size="small" text @click="likeMessage(message)">
                <el-icon><CircleCheck /></el-icon>
                有用
              </el-button>
              <el-button size="small" text @click="dislikeMessage(message)">
                <el-icon><CircleClose /></el-icon>
                无用
              </el-button>
            </div>
          </div>
        </div>

        <!-- AI正在输入指示器 -->
        <div v-if="aiTyping" class="message-item ai-message">
          <div class="ai-avatar">
            <el-icon size="20"><ChatDotRound /></el-icon>
          </div>
          <div class="message-content typing-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span class="typing-text">AI正在思考中...</span>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <el-input
            v-model="currentMessage"
            type="textarea"
            :rows="inputRows"
            :placeholder="inputPlaceholder"
            :disabled="aiTyping"
            @keydown.ctrl.enter="sendMessage"
            @keydown.meta.enter="sendMessage"
          />
          <div class="input-actions">
            <div class="input-tips">
              <el-icon><InfoFilled /></el-icon>
              按 Ctrl+Enter 发送消息
            </div>
            <div class="input-buttons">
              <el-button @click="clearChat" :disabled="aiTyping" size="small">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
              <el-button
                type="primary"
                @click="sendMessage"
                :loading="aiTyping"
                :disabled="!currentMessage.trim()"
                size="small"
              >
                <el-icon><Promotion /></el-icon>
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义操作按钮 -->
    <template #footer v-if="showFooter">
      <div class="dialog-footer">
        <slot name="footer" :messages="messages" :context="contextInfo">
          <el-button @click="handleClose">关闭</el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Service,
  User,
  ChatDotRound,
  DocumentCopy,
  CircleCheck,
  CircleClose,
  InfoFilled,
  Delete,
  Promotion
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// Props定义
interface Props {
  modelValue: boolean
  title?: string
  width?: string
  welcomeTitle?: string
  welcomeDescription?: string
  inputPlaceholder?: string
  inputRows?: number
  showFooter?: boolean
  contextInfo?: {
    projectId?: number
    projectName?: string
    taskType?: string
    aiProvider?: string
    [key: string]: any
  }
  // AI服务配置
  aiService?: {
    sendMessage: (conversationId: string, message: string, context?: any) => Promise<{
      messageId: string
      conversationId: string
      userMessage: string
      aiResponse: string
      timestamp: string
    }>
    getHistory?: (conversationId: string) => Promise<Array<{
      userMessage: string
      aiResponse: string
      timestamp: string
    }>>
  }
  conversationId?: string
  autoLoadHistory?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: 'AI助手',
  width: '1000px',
  welcomeTitle: '👋 您好！我是AI助手',
  welcomeDescription: '我可以帮助您解决各种问题，请告诉我您需要什么帮助。',
  inputPlaceholder: '请输入您的问题...',
  inputRows: 2,
  showFooter: true,
  autoLoadHistory: true
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'message-sent': [message: { type: 'user' | 'ai', content: string, timestamp: Date }]
  'conversation-cleared': []
  'dialog-closed': []
}>()

// 响应式数据
const authStore = useAuthStore()
const messagesContainer = ref<HTMLElement>()
const currentMessage = ref('')
const aiTyping = ref(false)
const loadingHistory = ref(false)

// 消息列表
const messages = ref<Array<{
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}>>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  if (props.contextInfo?.taskType) {
    return `${props.title} - ${props.contextInfo.taskType}`
  }
  return props.title
})

const dialogWidth = computed(() => props.width)

const userAvatar = computed(() => authStore.user?.avatar || '')

const conversationId = computed(() => {
  return props.conversationId || `dialog-${Date.now()}`
})

// 方法
const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatMessage = (content: string) => {
  // 简单的Markdown格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

// 加载对话历史
const loadConversationHistory = async () => {
  if (!props.aiService?.getHistory || !props.autoLoadHistory) return

  try {
    loadingHistory.value = true
    const history = await props.aiService.getHistory(conversationId.value)

    // 将历史对话转换为消息格式
    const historyMessages: Array<{
      type: 'user' | 'ai'
      content: string
      timestamp: Date
    }> = []

    history.forEach(item => {
      // 添加用户消息
      historyMessages.push({
        type: 'user',
        content: item.userMessage,
        timestamp: new Date(item.timestamp)
      })

      // 添加AI回复（如果存在）
      if (item.aiResponse) {
        historyMessages.push({
          type: 'ai',
          content: item.aiResponse,
          timestamp: new Date(item.timestamp)
        })
      }
    })

    messages.value = historyMessages
    scrollToBottom()
  } catch (error: any) {
    console.error('加载对话历史失败:', error)
    // 不显示错误消息，静默失败
  } finally {
    loadingHistory.value = false
  }
}

// 发送消息
const sendMessage = async () => {
  if (!currentMessage.value.trim() || aiTyping.value) return
  if (!props.aiService?.sendMessage) {
    ElMessage.error('AI服务未配置')
    return
  }

  const userMessage = currentMessage.value.trim()
  currentMessage.value = ''

  // 添加用户消息
  const userMsg = {
    type: 'user' as const,
    content: userMessage,
    timestamp: new Date()
  }
  messages.value.push(userMsg)
  emit('message-sent', userMsg)

  scrollToBottom()

  // 显示AI正在输入
  aiTyping.value = true

  try {
    // 调用AI服务
    const response = await props.aiService.sendMessage(
      conversationId.value,
      userMessage,
      props.contextInfo
    )

    // 添加AI回复
    const aiMsg = {
      type: 'ai' as const,
      content: response.aiResponse,
      timestamp: new Date()
    }
    messages.value.push(aiMsg)
    emit('message-sent', aiMsg)
  } catch (error: any) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请稍后重试')

    // 添加错误消息
    const errorMsg = {
      type: 'ai' as const,
      content: '抱歉，我暂时无法回复您的消息，请稍后重试。',
      timestamp: new Date()
    }
    messages.value.push(errorMsg)
    emit('message-sent', errorMsg)
  } finally {
    aiTyping.value = false
    scrollToBottom()
  }
}

// 清空对话
const clearChat = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空对话历史吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    messages.value = []
    emit('conversation-cleared')
  } catch (error) {
    // 用户取消
  }
}

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 点赞消息
const likeMessage = (message: any) => {
  ElMessage.success('感谢您的反馈！')
  // TODO: 发送反馈到后端
}

// 点踩消息
const dislikeMessage = (message: any) => {
  ElMessage.info('感谢您的反馈，我们会持续改进')
  // TODO: 发送反馈到后端
}

// 关闭对话框
const handleClose = () => {
  emit('dialog-closed')
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.autoLoadHistory) {
    loadConversationHistory()
  }
})

// 生命周期
onMounted(() => {
  if (props.modelValue && props.autoLoadHistory) {
    loadConversationHistory()
  }
})
</script>

<style scoped>
.ai-dialog {
  .el-dialog {
    border-radius: 12px;
  }

  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 20px 24px 16px;
  }

  .el-dialog__body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  background: var(--el-bg-color-page);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 3px;
  }
}

.welcome-message {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;

  .ai-avatar {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .message-content {
    flex: 1;

    h3 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
      font-size: 18px;
      font-weight: 600;
    }

    p, ul {
      margin: 8px 0;
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }

    ul {
      padding-left: 20px;
    }

    li {
      margin: 4px 0;
    }
  }
}

.context-info {
  margin-top: 16px;

  .context-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-lighter);
  }

  .context-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .context-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 60px;
    }
  }
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;

  &.user-message {
    flex-direction: row-reverse;

    .message-content {
      background: var(--el-color-primary);
      color: white;
      border-radius: 18px 18px 4px 18px;
      margin-left: 60px;
    }
  }

  &.ai-message {
    .message-content {
      background: white;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 18px 18px 18px 4px;
      margin-right: 60px;
    }
  }

  .message-avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
  }

  .ai-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .message-content {
    padding: 12px 16px;
    max-width: 70%;
    word-wrap: break-word;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .sender-name {
        font-weight: 500;
        font-size: 12px;
        opacity: 0.8;
      }

      .message-time {
        font-size: 11px;
        opacity: 0.6;
      }
    }

    .message-text {
      line-height: 1.6;

      :deep(strong) {
        font-weight: 600;
      }

      :deep(em) {
        font-style: italic;
      }
    }

    .message-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;

      .el-button {
        padding: 4px 8px;
        height: auto;
        font-size: 12px;
      }
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 18px 18px 18px 4px;

  .typing-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--el-color-primary);
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .typing-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  border-top: 1px solid var(--el-border-color-lighter);
  padding: 16px 24px;
  background: white;

  .input-container {
    .el-textarea {
      margin-bottom: 12px;
    }

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .input-tips {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }

      .input-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding: 16px 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .el-dialog__body {
      height: 60vh;
    }
  }

  .message-item {
    .message-content {
      max-width: 85%;
    }

    &.user-message .message-content {
      margin-left: 40px;
    }

    &.ai-message .message-content {
      margin-right: 40px;
    }
  }

  .chat-input {
    padding: 12px 16px;
  }
}
</style>
