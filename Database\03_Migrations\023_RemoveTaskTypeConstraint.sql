-- 移除UserTaskMappings表的TaskType约束，允许自定义任务类型
-- 执行日期: 2025-07-02
-- 目的: 支持用户自定义任务映射配置

USE ProjectManagementAI;
GO

-- 检查约束是否存在
IF EXISTS (
    SELECT 1 
    FROM sys.check_constraints 
    WHERE name = 'CK_UserTaskMappings_TaskType' 
    AND parent_object_id = OBJECT_ID('UserTaskMappings')
)
BEGIN
    PRINT '正在删除TaskType约束...';
    
    -- 删除TaskType的CHECK约束
    ALTER TABLE UserTaskMappings 
    DROP CONSTRAINT CK_UserTaskMappings_TaskType;
    
    PRINT 'TaskType约束已删除，现在支持自定义任务类型';
END
ELSE
BEGIN
    PRINT 'TaskType约束不存在，无需删除';
END
GO

-- 验证约束已删除
IF NOT EXISTS (
    SELECT 1 
    FROM sys.check_constraints 
    WHERE name = 'CK_UserTaskMappings_TaskType' 
    AND parent_object_id = OBJECT_ID('UserTaskMappings')
)
BEGIN
    PRINT '✅ 验证成功：TaskType约束已删除';
END
ELSE
BEGIN
    PRINT '❌ 验证失败：TaskType约束仍然存在';
END
GO

-- 添加基本的TaskType格式验证（可选）
-- 确保TaskType不为空且长度合理
IF NOT EXISTS (
    SELECT 1 
    FROM sys.check_constraints 
    WHERE name = 'CK_UserTaskMappings_TaskType_Format' 
    AND parent_object_id = OBJECT_ID('UserTaskMappings')
)
BEGIN
    PRINT '正在添加TaskType格式验证约束...';
    
    ALTER TABLE UserTaskMappings 
    ADD CONSTRAINT CK_UserTaskMappings_TaskType_Format 
    CHECK (
        TaskType IS NOT NULL 
        AND LEN(TaskType) > 0 
        AND LEN(TaskType) <= 50
        AND TaskType NOT LIKE '% %'  -- 不允许包含空格
        AND TaskType LIKE '[A-Za-z]%'  -- 必须以字母开头
    );
    
    PRINT '✅ TaskType格式验证约束已添加';
END
ELSE
BEGIN
    PRINT 'TaskType格式验证约束已存在';
END
GO

-- 显示当前UserTaskMappings表的约束信息
PRINT '当前UserTaskMappings表的约束信息:';
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition,
    cc.is_disabled AS IsDisabled
FROM sys.check_constraints cc
WHERE cc.parent_object_id = OBJECT_ID('UserTaskMappings')
ORDER BY cc.name;
GO

PRINT '迁移完成！现在可以创建自定义任务类型的映射了。';
