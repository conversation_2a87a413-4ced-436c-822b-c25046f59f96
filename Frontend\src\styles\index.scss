// 变量已在vite.config.ts中全局导入，无需重复导入

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

// 链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;

  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;

  &:hover {
    background: var(--el-border-color);
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }
.mb-4 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mt-4 { margin-top: 32px !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 8px !important; }
.ml-2 { margin-left: 16px !important; }
.ml-3 { margin-left: 24px !important; }
.ml-4 { margin-left: 32px !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 8px !important; }
.mr-2 { margin-right: 16px !important; }
.mr-3 { margin-right: 24px !important; }
.mr-4 { margin-right: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 8px !important; }
.p-2 { padding: 16px !important; }
.p-3 { padding: 24px !important; }
.p-4 { padding: 32px !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 8px !important; }
.pb-2 { padding-bottom: 16px !important; }
.pb-3 { padding-bottom: 24px !important; }
.pb-4 { padding-bottom: 32px !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 8px !important; }
.pt-2 { padding-top: 16px !important; }
.pt-3 { padding-top: 24px !important; }
.pt-4 { padding-top: 32px !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 8px !important; }
.pl-2 { padding-left: 16px !important; }
.pl-3 { padding-left: 24px !important; }
.pl-4 { padding-left: 32px !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 8px !important; }
.pr-2 { padding-right: 16px !important; }
.pr-3 { padding-right: 24px !important; }
.pr-4 { padding-right: 32px !important; }

// 卡片样式
.card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &.shadow {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// 页面标题
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 20px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 20px;
}

// 状态标签
.status-tag {
  &.planning { background-color: #909399; }
  &.in-progress { background-color: #409EFF; }
  &.testing { background-color: #E6A23C; }
  &.deployed { background-color: #67C23A; }
  &.completed { background-color: #67C23A; }
  &.paused { background-color: #F56C6C; }
  &.cancelled { background-color: #909399; }
}

.priority-tag {
  &.low { background-color: #67C23A; }
  &.medium { background-color: #E6A23C; }
  &.high { background-color: #F56C6C; }
  &.critical { background-color: #F56C6C; }
}

// 响应式
@media (max-width: 768px) {
  .card {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }
}

// Element Plus 组件样式覆盖
.el-table {
  .el-table__header {
    th {
      background-color: var(--el-bg-color-page);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
}

.el-card {
  border: 1px solid var(--el-border-color-lighter);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.el-button {
  &.is-link {
    padding: 0;
    height: auto;
    border: none;
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--el-text-color-secondary);
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--el-text-color-secondary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
  }
}
