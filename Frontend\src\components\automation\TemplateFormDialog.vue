<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑模板' : '创建模板'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="模板名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入模板名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类" prop="category">
        <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
          <el-option
            v-for="category in categories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="置信度" prop="confidence">
        <el-slider
          v-model="form.confidence"
          :min="0.1"
          :max="1.0"
          :step="0.1"
          :format-tooltip="formatConfidence"
          style="width: 80%"
        />
        <span class="confidence-value">{{ (form.confidence * 100).toFixed(0) }}%</span>
      </el-form-item>

      <el-form-item label="模板图片" prop="filePath">
        <div class="image-upload">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            accept="image/*"
            drag
          >
            <div v-if="!form.filePath" class="upload-placeholder">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">
                <p>点击或拖拽图片到此处上传</p>
                <p class="upload-tip">支持 PNG、JPG、JPEG 格式，建议大小不超过 2MB</p>
              </div>
            </div>
            <div v-else class="image-preview">
              <img :src="getImageUrl(form.filePath)" alt="模板预览" />
              <div class="image-overlay">
                <el-button type="primary" size="small" @click.stop="replaceImage">
                  <el-icon><Edit /></el-icon>
                  更换图片
                </el-button>
              </div>
            </div>
          </el-upload>
        </div>
      </el-form-item>

      <!-- 区域图片上传（可选） -->
      <el-form-item>
        <template #label>
          <span>区域图片</span>
          <el-tooltip
            content="可选功能：上传一个区域图片来限定模板匹配的范围，提高精度"
            placement="top"
          >
            <el-icon style="margin-left: 4px; color: #909399;"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>

        <div class="region-image-section">
          <!-- 启用区域匹配开关 -->
          <div class="region-switch">
            <el-switch
              v-model="form.useRegionMatching"
              active-text="启用区域匹配"
              inactive-text="禁用区域匹配"
              @change="handleRegionMatchingChange"
            />
          </div>

          <!-- 区域图片上传 -->
          <div v-if="form.useRegionMatching" class="region-upload-container">
            <div class="region-image-upload">
              <el-upload
                ref="regionUploadRef"
                :action="uploadAction"
                :headers="uploadHeaders"
                :before-upload="beforeRegionUpload"
                :on-success="handleRegionUploadSuccess"
                :on-error="handleRegionUploadError"
                :show-file-list="false"
                accept="image/*"
                drag
              >
                <div v-if="!form.regionFilePath" class="upload-placeholder small">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">
                    <p>上传区域图片</p>
                    <p class="upload-tip">用于限定匹配范围</p>
                  </div>
                </div>
                <div v-else class="image-preview small">
                  <img :src="getImageUrl(form.regionFilePath)" alt="区域图片预览" />
                  <div class="image-overlay">
                    <el-button type="primary" size="small" @click.stop="replaceRegionImage">
                      <el-icon><Edit /></el-icon>
                      更换
                    </el-button>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- 区域图片配置 -->
            <div class="region-config">
              <el-form-item label="区域描述" size="small">
                <el-input
                  v-model="form.regionDescription"
                  placeholder="描述区域图片的用途"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="区域置信度" size="small">
                <el-slider
                  v-model="form.regionConfidence"
                  :min="0.1"
                  :max="1.0"
                  :step="0.1"
                  :format-tooltip="formatConfidence"
                  style="width: 80%"
                />
                <span class="confidence-value">{{ ((form.regionConfidence || 0.7) * 100).toFixed(0) }}%</span>
              </el-form-item>

              <el-form-item label="区域扩展" size="small">
                <el-input-number
                  v-model="form.regionExpand"
                  :min="0"
                  :max="200"
                  :step="5"
                  controls-position="right"
                  style="width: 120px"
                />
                <span style="margin-left: 8px; color: #909399;">像素</span>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Edit, QuestionFilled } from '@element-plus/icons-vue'
import CustomTemplateService, { type CustomTemplate } from '@/services/customTemplate'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue: boolean
  template?: CustomTemplate | null
}

const props = withDefaults(defineProps<Props>(), {
  template: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const regionUploadRef = ref()
const submitting = ref(false)
const categories = ref<string[]>([])
const availableTags = ref<string[]>([])

const authStore = useAuthStore()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  filePath: '',
  confidence: 0.7,
  tags: [] as string[],
  notes: '',
  // 区域图片相关字段
  regionFilePath: '',
  regionDescription: '',
  regionConfidence: 0.7,
  regionExpand: 10,
  useRegionMatching: false
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.template)

const uploadAction = computed(() => {
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'
  const category = form.category || 'default'
  const templateId = isEdit.value && props.template ? props.template.id : ''
  let url = `${apiBaseUrl}/api/custom-templates/upload-image?category=${encodeURIComponent(category)}`
  if (templateId) {
    url += `&templateId=${templateId}`
  }
  return url
})

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 100, message: '模板名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入模板描述', trigger: 'blur' },
    { min: 5, max: 500, message: '描述长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  filePath: [
    { required: true, message: '请上传模板图片', trigger: 'change' }
  ],
  confidence: [
    { required: true, message: '请设置置信度', trigger: 'change' },
    { type: 'number', min: 0.1, max: 1.0, message: '置信度必须在 0.1 到 1.0 之间', trigger: 'change' }
  ]
}

// 方法定义
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    category: '',
    filePath: '',
    confidence: 0.7,
    tags: [],
    notes: '',
    // 区域图片相关字段
    regionFilePath: '',
    regionDescription: '',
    regionConfidence: 0.7,
    regionExpand: 10,
    useRegionMatching: false
  })
}

// 监听器
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    Object.assign(form, {
      name: newTemplate.name,
      description: newTemplate.description,
      category: newTemplate.category,
      filePath: newTemplate.filePath,
      confidence: newTemplate.confidence,
      tags: [...(newTemplate.tags || [])],
      notes: newTemplate.notes || '',
      // 区域图片相关字段
      regionFilePath: newTemplate.regionFilePath || '',
      regionDescription: newTemplate.regionDescription || '',
      regionConfidence: newTemplate.regionConfidence || 0.7,
      regionExpand: newTemplate.regionExpand || 10,
      useRegionMatching: newTemplate.useRegionMatching || false
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadCategories()
  loadTags()
})

// 方法
const loadCategories = async () => {
  try {
    categories.value = await CustomTemplateService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadTags = async () => {
  try {
    availableTags.value = await CustomTemplateService.getTags()
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const formatConfidence = (value: number) => {
  return `${(value * 100).toFixed(0)}%`
}

const getImageUrl = (filePath: string) => {
  if (filePath.startsWith('http')) {
    return filePath
  }
  // 使用配置的API基础URL构建图片访问路径
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'
  return `${apiBaseUrl}/api/custom-templates/image/${filePath}`
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any) => {
  if (response.success) {
    form.filePath = response.data.filePath
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleUploadError = () => {
  ElMessage.error('图片上传失败')
}

const replaceImage = () => {
  uploadRef.value?.clearFiles()
  // 触发文件选择
  const input = uploadRef.value?.$el.querySelector('input[type="file"]')
  input?.click()
}

// 区域图片相关方法
const handleRegionMatchingChange = (value: string | number | boolean) => {
  if (!Boolean(value)) {
    // 禁用区域匹配时清空相关字段
    form.regionFilePath = ''
    form.regionDescription = ''
    form.regionConfidence = 0.7
    form.regionExpand = 10
  }
}

const beforeRegionUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleRegionUploadSuccess = (response: any) => {
  if (response.success) {
    form.regionFilePath = response.data.filePath
    ElMessage.success('区域图片上传成功')
  } else {
    ElMessage.error(response.message || '区域图片上传失败')
  }
}

const handleRegionUploadError = () => {
  ElMessage.error('区域图片上传失败')
}

const replaceRegionImage = () => {
  regionUploadRef.value?.clearFiles()
  // 触发文件选择
  const input = regionUploadRef.value?.$el.querySelector('input[type="file"]')
  input?.click()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (isEdit.value && props.template) {
      await CustomTemplateService.updateTemplate(props.template.id, form)
      ElMessage.success('模板更新成功')
    } else {
      await CustomTemplateService.createTemplate(form)
      ElMessage.success('模板创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存模板失败:', error)
    ElMessage.error('保存模板失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  resetForm()
}
</script>

<style scoped lang="scss">
.confidence-value {
  margin-left: 16px;
  font-weight: 500;
  color: #409eff;
}

.image-upload {
  width: 100%;

  :deep(.el-upload) {
    width: 100%;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c939d;

    .upload-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .upload-text {
      text-align: center;

      p {
        margin: 0;
        line-height: 1.5;
      }

      .upload-tip {
        font-size: 12px;
        color: #c0c4cc;
        margin-top: 8px;
      }
    }
  }

  .image-preview {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1;
      }
    }
  }
}

// 区域图片相关样式
.region-image-section {
  .region-switch {
    margin-bottom: 16px;
  }

  .region-upload-container {
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 16px;
    background-color: #fafafa;

    .region-image-upload {
      margin-bottom: 16px;

      :deep(.el-upload-dragger) {
        width: 200px;
        height: 120px;
      }

      .upload-placeholder.small {
        .upload-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }

        .upload-text {
          p {
            margin: 0;
            font-size: 12px;
            line-height: 1.4;
          }

          .upload-tip {
            color: #909399;
            font-size: 11px;
          }
        }
      }

      .image-preview.small {
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .region-config {
      .el-form-item {
        margin-bottom: 12px;

        :deep(.el-form-item__label) {
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
