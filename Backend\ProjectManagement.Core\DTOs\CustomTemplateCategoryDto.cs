using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 自定义模板分类DTO
    /// </summary>
    public class CustomTemplateCategoryDto
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 父分类ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 父分类名称
        /// </summary>
        public string? ParentName { get; set; }

        /// <summary>
        /// 分类图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 分类颜色
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否为系统分类
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 模板数量
        /// </summary>
        public int TemplateCount { get; set; }

        /// <summary>
        /// 子分类列表
        /// </summary>
        public List<CustomTemplateCategoryDto> Children { get; set; } = new List<CustomTemplateCategoryDto>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }
    }

    /// <summary>
    /// 创建自定义模板分类DTO
    /// </summary>
    public class CreateCustomTemplateCategoryDto
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 父分类ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 分类图标
        /// </summary>
        [StringLength(50, ErrorMessage = "分类图标长度不能超过50个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 分类颜色
        /// </summary>
        [StringLength(20, ErrorMessage = "分类颜色长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "排序顺序必须大于等于0")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// 更新自定义模板分类DTO
    /// </summary>
    public class UpdateCustomTemplateCategoryDto
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 父分类ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 分类图标
        /// </summary>
        [StringLength(50, ErrorMessage = "分类图标长度不能超过50个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 分类颜色
        /// </summary>
        [StringLength(20, ErrorMessage = "分类颜色长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "排序顺序必须大于等于0")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// 分类树节点DTO
    /// </summary>
    public class CategoryTreeNodeDto
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类图标
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 分类颜色
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 模板数量
        /// </summary>
        public int TemplateCount { get; set; }

        /// <summary>
        /// 是否为系统分类
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// 子分类列表
        /// </summary>
        public List<CategoryTreeNodeDto> Children { get; set; } = new List<CategoryTreeNodeDto>();
    }

    /// <summary>
    /// 分类统计DTO
    /// </summary>
    public class CategoryStatisticsDto
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 模板数量
        /// </summary>
        public int TemplateCount { get; set; }

        /// <summary>
        /// 序列数量
        /// </summary>
        public int SequenceCount { get; set; }

        /// <summary>
        /// 总使用次数
        /// </summary>
        public int TotalUsageCount { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }
    }
}
