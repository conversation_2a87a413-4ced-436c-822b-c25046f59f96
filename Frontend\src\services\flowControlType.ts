/**
 * 流程控制类型服务
 */
import api from './api'

// 流程控制类型接口
export interface FlowControlType {
  id: number
  value: string
  label: string
  description?: string
  icon?: string
  color?: string
  sortOrder: number
  isActive: boolean
  isBuiltIn: boolean
  parameterSchema?: string
  executionType?: string
  requiresTarget: boolean
  canNest: boolean
  createdTime: string
  updatedTime?: string
}

// 分页查询参数
export interface FlowControlTypeQuery {
  page: number
  pageSize: number
  keyword?: string
  executionType?: string
  isActive?: boolean
  isBuiltIn?: boolean
  requiresTarget?: boolean
  canNest?: boolean
}

// 分页结果
export interface PageResult<T> {
  items: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
}

/**
 * 流程控制类型服务类
 */
export class FlowControlTypeService {

  /**
   * 获取流程控制类型列表
   */
  static async getFlowControlTypes(query: FlowControlTypeQuery): Promise<PageResult<FlowControlType>> {
    const response = await api.get('/api/flow-control-types', { params: query })
    return response.data
  }

  /**
   * 获取启用的流程控制类型
   */
  static async getActiveFlowControlTypes(): Promise<FlowControlType[]> {
    const response = await api.get('/api/flow-control-types/active')
    return response.data
  }

  /**
   * 根据执行类型获取流程控制类型
   */
  static async getFlowControlTypesByExecutionType(executionType: string): Promise<FlowControlType[]> {
    const response = await api.get(`/api/flow-control-types/by-execution-type/${executionType}`)
    return response.data
  }

  /**
   * 获取流程控制类型详情
   */
  static async getFlowControlType(id: number): Promise<FlowControlType> {
    const response = await api.get(`/api/flow-control-types/${id}`)
    return response.data
  }

  /**
   * 创建流程控制类型
   */
  static async createFlowControlType(data: Partial<FlowControlType>): Promise<FlowControlType> {
    const response = await api.post('/api/flow-control-types', data)
    return response.data
  }

  /**
   * 更新流程控制类型
   */
  static async updateFlowControlType(id: number, data: Partial<FlowControlType>): Promise<FlowControlType> {
    const response = await api.put(`/api/flow-control-types/${id}`, data)
    return response.data
  }

  /**
   * 删除流程控制类型
   */
  static async deleteFlowControlType(id: number): Promise<boolean> {
    const response = await api.delete(`/api/flow-control-types/${id}`)
    return response.data.success
  }

  /**
   * 设置流程控制类型状态
   */
  static async setFlowControlTypeStatus(id: number, isActive: boolean): Promise<boolean> {
    const response = await api.put(`/api/flow-control-types/${id}/status`, isActive)
    return response.data.success
  }
}

export default FlowControlTypeService
