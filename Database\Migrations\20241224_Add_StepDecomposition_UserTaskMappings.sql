-- =============================================
-- 添加步骤分解任务类型的用户任务映射数据
-- 创建时间: 2024-12-24
-- 说明: 为StepDecomposition任务类型添加默认的用户任务映射配置
-- =============================================

USE [ProjectManagementAI]
GO

-- 检查TaskType约束是否包含StepDecomposition
PRINT '检查TaskType约束...'
IF NOT EXISTS (
    SELECT 1 FROM sys.check_constraints cc
    INNER JOIN sys.columns c ON cc.parent_object_id = c.object_id AND cc.parent_column_id = c.column_id
    INNER JOIN sys.tables t ON c.object_id = t.object_id
    WHERE t.name = 'UserTaskMappings' 
    AND c.name = 'TaskType'
    AND cc.definition LIKE '%StepDecomposition%'
)
BEGIN
    PRINT '更新TaskType约束以包含StepDecomposition...'
    
    -- 删除现有约束
    DECLARE @ConstraintName NVARCHAR(128)
    SELECT @ConstraintName = cc.name
    FROM sys.check_constraints cc
    INNER JOIN sys.columns c ON cc.parent_object_id = c.object_id AND cc.parent_column_id = c.column_id
    INNER JOIN sys.tables t ON c.object_id = t.object_id
    WHERE t.name = 'UserTaskMappings' AND c.name = 'TaskType'
    
    IF @ConstraintName IS NOT NULL
    BEGIN
        EXEC('ALTER TABLE UserTaskMappings DROP CONSTRAINT ' + @ConstraintName)
        PRINT '已删除旧的TaskType约束: ' + @ConstraintName
    END
    
    -- 添加新约束
    ALTER TABLE UserTaskMappings
    ADD CONSTRAINT CK_UserTaskMappings_TaskType 
    CHECK (TaskType IN ('RequirementAnalysis', 'ERDiagramGeneration', 'ContextDiagramGeneration', 'StepDecomposition', 'CodeGeneration', 'TestGeneration', 'DocumentGeneration'))
    
    PRINT '已添加新的TaskType约束，包含StepDecomposition'
END
ELSE
BEGIN
    PRINT 'TaskType约束已包含StepDecomposition，跳过更新'
END

-- 为默认用户(UserId=1)添加StepDecomposition任务映射
PRINT '添加StepDecomposition任务映射数据...'

-- 检查是否已存在StepDecomposition的映射
IF NOT EXISTS (SELECT 1 FROM UserTaskMappings WHERE TaskType = 'StepDecomposition')
BEGIN
    -- 为默认用户添加Azure OpenAI的StepDecomposition映射
    INSERT INTO UserTaskMappings (UserId, TaskType, ProviderName, IsActive, CreatedAt, UpdatedAt)
    VALUES 
    (1, 'StepDecomposition', 'DeepSeek', 1, GETDATE(), GETDATE())
    
    PRINT '已为用户1添加Azure OpenAI的StepDecomposition任务映射'
    
    -- 如果有其他活跃用户，也为他们添加默认映射
    INSERT INTO UserTaskMappings (UserId, TaskType, ProviderName, IsActive, CreatedAt, UpdatedAt)
    SELECT DISTINCT 
        utm.UserId,
        'StepDecomposition',
        'DeepSeek',  -- 默认使用Azure OpenAI
        1,
        GETDATE(),
        GETDATE()
    FROM UserTaskMappings utm
    WHERE utm.UserId != 1  -- 排除已经添加的用户1
    AND utm.IsActive = 1
    AND NOT EXISTS (
        SELECT 1 FROM UserTaskMappings utm2 
        WHERE utm2.UserId = utm.UserId 
        AND utm2.TaskType = 'StepDecomposition'
    )
    
    PRINT '已为其他活跃用户添加StepDecomposition任务映射'
END
ELSE
BEGIN
    PRINT 'StepDecomposition任务映射已存在，跳过添加'
END

-- 验证数据
PRINT '验证添加的数据...'
SELECT 
    utm.Id,
    utm.UserId,
    utm.TaskType,
    utm.ProviderName,
    utm.IsActive,
    utm.CreatedAt,
    uac.ModelName,
    uac.IsActive as ConfigActive
FROM UserTaskMappings utm
LEFT JOIN UserAIConfigurations uac ON utm.UserId = uac.UserId AND utm.ProviderName = uac.ProviderName AND uac.IsActive = 1
WHERE utm.TaskType = 'StepDecomposition'
ORDER BY utm.UserId

PRINT '步骤分解用户任务映射数据添加完成！'

-- 显示统计信息
SELECT 
    TaskType,
    COUNT(*) as MappingCount,
    COUNT(DISTINCT UserId) as UserCount
FROM UserTaskMappings 
WHERE TaskType = 'StepDecomposition'
GROUP BY TaskType

PRINT '=== 步骤分解任务映射统计 ==='
