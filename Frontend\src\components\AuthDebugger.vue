<template>
  <div v-if="showDebugger" class="auth-debugger">
    <div class="debugger-header">
      <h4>认证状态调试器</h4>
      <button @click="showDebugger = false" class="close-btn">×</button>
    </div>
    <div class="debugger-content">
      <div class="debug-item">
        <strong>Token:</strong> {{ authStore.token ? '✓ 存在' : '✗ 不存在' }}
      </div>
      <div class="debug-item">
        <strong>User:</strong> {{ authStore.user ? '✓ 存在' : '✗ 不存在' }}
      </div>
      <div class="debug-item">
        <strong>IsAuthenticated:</strong> {{ authStore.isAuthenticated ? '✓ 是' : '✗ 否' }}
      </div>
      <div class="debug-item">
        <strong>Current Route:</strong> {{ $route.path }}
      </div>
      <div class="debug-item">
        <strong>Requires Auth:</strong> {{ $route.meta.requiresAuth ? '✓ 是' : '✗ 否' }}
      </div>
      <div v-if="authStore.user" class="debug-item">
        <strong>User Info:</strong>
        <pre>{{ JSON.stringify(authStore.user, null, 2) }}</pre>
      </div>
      <div class="debug-actions">
        <button @click="testNavigation" class="debug-btn">测试跳转到Dashboard</button>
        <button @click="clearAuth" class="debug-btn">清除认证信息</button>
        <button @click="refreshAuth" class="debug-btn">刷新认证状态</button>
      </div>
    </div>
  </div>
  <button v-else @click="showDebugger = true" class="debug-toggle">🐛</button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const showDebugger = ref(false)

const testNavigation = async () => {
  try {
    console.log('Testing navigation to dashboard...')
    await router.push('/dashboard')
    ElMessage.success('跳转成功')
  } catch (error) {
    console.error('Navigation failed:', error)
    ElMessage.error('跳转失败')
  }
}

const clearAuth = async () => {
  await authStore.logout()
  ElMessage.info('认证信息已清除')
}

const refreshAuth = async () => {
  try {
    await authStore.initializeAuth()
    ElMessage.success('认证状态已刷新')
  } catch (error) {
    console.error('Refresh auth failed:', error)
    ElMessage.error('刷新失败')
  }
}
</script>

<style scoped>
.auth-debugger {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-size: 12px;
}

.debugger-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  border-radius: 8px 8px 0 0;
}

.debugger-header h4 {
  margin: 0;
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debugger-content {
  padding: 15px;
}

.debug-item {
  margin-bottom: 10px;
  padding: 5px;
  background: #f9f9f9;
  border-radius: 4px;
}

.debug-item pre {
  margin: 5px 0 0 0;
  font-size: 10px;
  background: #fff;
  padding: 5px;
  border-radius: 2px;
  overflow: auto;
  max-height: 100px;
}

.debug-actions {
  margin-top: 15px;
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.debug-btn {
  padding: 5px 10px;
  font-size: 11px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-btn:hover {
  background: #337ecc;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.debug-toggle:hover {
  background: #337ecc;
}
</style>
