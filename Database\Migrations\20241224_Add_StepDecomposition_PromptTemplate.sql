-- 添加步骤分解提示词模板
-- 创建时间: 2024-12-24
-- 描述: 为AI步骤分解功能添加默认提示词模板，支持项目背景信息

USE [ProjectManagementAI]
GO

-- 首先更新TaskType约束，添加StepDecomposition类型
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE PromptTemplates DROP CONSTRAINT CK_PromptTemplates_TaskType;
    PRINT '已删除旧的TaskType约束';
END

-- 创建新的TaskType约束，包含StepDecomposition类型
ALTER TABLE PromptTemplates ADD CONSTRAINT CK_PromptTemplates_TaskType
CHECK (TaskType IN (
    'RequirementAnalysis',      -- 需求分析
    'CodeGeneration',          -- 代码生成
    'Testing',                 -- 测试生成
    'Debugging',               -- 调试辅助
    'Documentation',           -- 文档生成
    'Review',                  -- 代码审查
    'ERDiagramGeneration',     -- ER图生成
    'ContextDiagramGeneration', -- 上下文图生成
    'DesignGeneration',        -- 设计生成
    'ArchitectureAnalysis',    -- 架构分析
    'RequirementDecomposition', -- 需求分解
    'DependencyAnalysis',      -- 依赖分析
    'ComplexityAnalysis',      -- 复杂度分析
    'SQLCommentGeneration',    -- SQL注释生成
    'StepDecomposition'        -- 步骤分解
));
PRINT '已更新TaskType约束，添加StepDecomposition类型';

-- 同时更新UserTaskMappings表的约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserTaskMappings_TaskType')
BEGIN
    ALTER TABLE UserTaskMappings DROP CONSTRAINT CK_UserTaskMappings_TaskType;
    PRINT '已删除旧的UserTaskMappings TaskType约束';
END

ALTER TABLE UserTaskMappings ADD CONSTRAINT CK_UserTaskMappings_TaskType
CHECK (TaskType IN (
    'RequirementAnalysis',
    'CodeGeneration',
    'DocumentGeneration',
    'Embeddings',
    'Testing',
    'Debugging',
    'ERDiagramGeneration',
    'ContextDiagramGeneration',
    'RequirementDecomposition',
    'DependencyAnalysis',
    'StepDecomposition'
));
PRINT '已更新UserTaskMappings TaskType约束，添加StepDecomposition类型';
GO

-- 检查是否已存在步骤分解分类，如果不存在则创建
IF NOT EXISTS (SELECT 1 FROM PromptCategories WHERE Name = '开发步骤管理')
BEGIN
    INSERT INTO PromptCategories (Name, Description, ParentId, Icon, Color, SortOrder, IsEnabled, CreatedTime, CreatedBy)
    VALUES ('开发步骤管理', '用于开发步骤相关的AI提示词模板', NULL, 'el-icon-s-operation', '#409EFF', 4, 1, GETDATE(), 1)
END
GO

-- 获取分类ID
DECLARE @CategoryId INT
SELECT @CategoryId = Id FROM PromptCategories WHERE Name = '开发步骤管理'

-- 检查是否已存在步骤分解模板
IF NOT EXISTS (SELECT 1 FROM PromptTemplates WHERE TaskType = 'StepDecomposition')
BEGIN
    INSERT INTO PromptTemplates (
        Name,
        Description,
        CategoryId,
        Content,
        Parameters,
        TemplateType,
        TaskType,
        SupportedProviders,
        TemplateVersion,
        IsDefault,
        IsEnabled,
        UsageCount,
        Tags,
        CreatedTime,
        CreatedBy
    )
    VALUES (
        '开发步骤AI分解模板',
        '用于AI智能分解开发步骤的提示词模板，支持项目背景信息和多种分解粒度',
        @CategoryId,
        N'你是一个专业的软件开发项目管理专家和技术架构师，擅长将复杂的开发任务分解为具体可执行的子步骤。

项目背景信息：
- 项目名称：{ProjectName}
- 项目描述：{ProjectDescription}
- 项目状态：{ProjectStatus}
- 项目优先级：{ProjectPriority}
- 技术栈：{ProjectTechnologyStack}
- 项目进度：{ProjectProgress}%
- 预估工时：{ProjectEstimatedHours} 小时
- 项目预算：{ProjectBudget}

请将以下开发步骤分解为更具体的子步骤：

原步骤信息：
- 步骤名称：{StepName}
- 步骤描述：{StepDescription}
- 步骤类型：{StepType}
- 技术栈：{StepTechnologyStack}
- 文件路径：{StepFilePath}
- 组件类型：{StepComponentType}
- 优先级：{StepPriority}
- 预估工时：{StepEstimatedHours} 小时
- 当前层级：第 {StepLevel} 层

分解要求：
- 分解粒度：{GranularityDescription}
- 最大子步骤数量：{MaxSubSteps}
- 技术栈偏好：{TechnologyPreference}
- 包含测试步骤：{IncludeTestSteps}
- 包含文档步骤：{IncludeDocumentationSteps}
- 自定义要求：{CustomRequirements}

分解原则：
1. 结合项目背景和技术栈特点进行分解
2. 确保每个子步骤都有明确的交付物
3. 考虑步骤间的依赖关系和执行顺序
4. 工时估算要合理，符合项目整体进度
5. 包含必要的项目需求分析步骤
6. 体现项目的业务价值和技术要求

请以JSON格式返回分解结果：
{
    "success": true,
    "steps": [
        {
            "stepName": "子步骤名称",
            "stepDescription": "详细描述，包含具体的实现要点",
            "stepType": "Development/Testing/Documentation/Analysis",
            "priority": "High/Medium/Low",
            "estimatedHours": 2.0,
            "technologyStack": "具体技术栈",
            "fileType": "文件类型",
            "filePath": "具体文件路径",
            "componentType": "组件类型",
            "aiPrompt": "AI辅助提示词"
        }
    ],
    "analysis": "分解分析说明，包括对项目背景的考虑、技术选型理由、风险评估等",
    "totalEstimatedHours": 8.0,
    "dependencies": [
        {
            "fromStep": 0,
            "toStep": 1,
            "dependencyType": "Sequential/Parallel"
        }
    ]
}

特别要求：
1. 如果原步骤涉及功能开发，必须包含"项目需求分析"相关的子步骤
2. 子步骤名称要清晰明确，体现具体的工作内容
3. 每个子步骤都要有详细的描述，说明具体要做什么
4. 工时估算要基于项目复杂度和团队经验
5. 技术栈要与项目保持一致，如有特殊要求请在描述中说明
6. 返回的JSON必须格式正确，可以被程序解析
7. 分析部分要体现对项目背景的深度理解'

请结合项目背景和步骤信息，以JSON格式返回分解结果：
{
    "success": true,
    "steps": [
        {
            "stepName": "子步骤名称",
            "stepDescription": "详细描述",
            "stepType": "Development/Testing/Documentation",
            "priority": "High/Medium/Low",
            "estimatedHours": 2.0,
            "technologyStack": "技术栈",
            "fileType": "文件类型",
            "filePath": "文件路径",
            "componentType": "组件类型",
            "aiPrompt": "AI提示词"
        }
    ],
    "analysis": "分解分析说明，包括对项目背景的考虑",
    "totalEstimatedHours": 8.0,
    "dependencies": [
        {
            "fromStep": 0,
            "toStep": 1,
            "dependencyType": "Sequential"
        }
    ]
}

请确保：
1. 子步骤名称清晰明确，符合项目技术栈和规范
2. 每个子步骤都有具体的描述，考虑项目背景
3. 工时估算合理，参考项目整体进度
4. 步骤之间的依赖关系明确
5. 返回有效的JSON格式
6. 分解结果与项目目标和技术栈保持一致',
        N'[
            {
                "name": "step",
                "type": "object",
                "description": "要分解的开发步骤对象",
                "required": true,
                "properties": {
                    "StepName": {"type": "string", "description": "步骤名称"},
                    "StepDescription": {"type": "string", "description": "步骤描述"},
                    "StepType": {"type": "string", "description": "步骤类型"},
                    "TechnologyStack": {"type": "string", "description": "技术栈"},
                    "Priority": {"type": "string", "description": "优先级"},
                    "EstimatedHours": {"type": "number", "description": "预估工时"},
                    "StepLevel": {"type": "number", "description": "步骤层级"}
                }
            },
            {
                "name": "options",
                "type": "object",
                "description": "分解选项配置",
                "required": true,
                "properties": {
                    "Granularity": {"type": "string", "description": "分解粒度: Fine/Medium/Coarse"},
                    "MaxSubSteps": {"type": "number", "description": "最大子步骤数量"},
                    "TechnologyPreference": {"type": "string", "description": "技术栈偏好"},
                    "IncludeTestSteps": {"type": "boolean", "description": "是否包含测试步骤"},
                    "IncludeDocumentationSteps": {"type": "boolean", "description": "是否包含文档步骤"},
                    "CustomRequirements": {"type": "string", "description": "自定义要求"}
                }
            },
            {
                "name": "project",
                "type": "object",
                "description": "项目背景信息",
                "required": false,
                "properties": {
                    "Name": {"type": "string", "description": "项目名称"},
                    "Description": {"type": "string", "description": "项目描述"},
                    "Status": {"type": "string", "description": "项目状态"},
                    "Priority": {"type": "string", "description": "项目优先级"},
                    "TechnologyStack": {"type": "string", "description": "项目技术栈"},
                    "Progress": {"type": "number", "description": "项目进度"},
                    "EstimatedHours": {"type": "number", "description": "项目预估工时"},
                    "Budget": {"type": "number", "description": "项目预算"}
                }
            }
        ]',
        'System',
        'StepDecomposition',
        'Azure,OpenAI,DeepSeek,Claude',
        '1.0',
        1,
        1,
        0,
        '步骤分解,AI分解,开发步骤,项目背景,智能分析',
        GETDATE(),
        1
    )

    PRINT '步骤分解提示词模板创建成功'
END
ELSE
BEGIN
    PRINT '步骤分解提示词模板已存在，跳过创建'
END
GO

-- 验证创建结果
SELECT
    pt.Id,
    pt.Name,
    pt.TaskType,
    pt.IsDefault,
    pt.IsEnabled,
    pc.Name as CategoryName
FROM PromptTemplates pt
INNER JOIN PromptCategories pc ON pt.CategoryId = pc.Id
WHERE pt.TaskType = 'StepDecomposition'
GO

PRINT '步骤分解提示词模板迁移完成'
