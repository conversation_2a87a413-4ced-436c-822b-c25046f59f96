using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using SqlSugar;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 自定义模板分类仓储实现
    /// </summary>
    public class CustomTemplateCategoryRepository : ICustomTemplateCategoryRepository
    {
        private readonly ISqlSugarClient _db;

        public CustomTemplateCategoryRepository(ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetAllAsync()
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 获取启用的分类
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetEnabledCategoriesAsync()
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取分类
        /// </summary>
        public async Task<CustomTemplateCategory?> GetByIdAsync(int id)
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => x.Id == id && !x.IsDeleted)
                .FirstAsync();
        }

        /// <summary>
        /// 根据名称获取分类
        /// </summary>
        public async Task<CustomTemplateCategory?> GetByNameAsync(string name)
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => x.Name == name && !x.IsDeleted)
                .FirstAsync();
        }

        /// <summary>
        /// 获取分类树结构
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetCategoryTreeAsync()
        {
            var allCategories = await GetEnabledCategoriesAsync();
            return BuildCategoryTree(allCategories);
        }

        /// <summary>
        /// 获取根分类列表
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetRootCategoriesAsync()
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled && x.ParentId == null)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 获取子分类列表
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetChildCategoriesAsync(int parentId)
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled && x.ParentId == parentId)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 检查分类名称是否存在
        /// </summary>
        public async Task<bool> ExistsNameAsync(string name, int? excludeId = null)
        {
            var query = _db.Queryable<CustomTemplateCategory>()
                .Where(x => x.Name == name && !x.IsDeleted);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查分类是否有子分类
        /// </summary>
        public async Task<bool> HasChildrenAsync(int categoryId)
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => x.ParentId == categoryId && !x.IsDeleted)
                .AnyAsync();
        }

        /// <summary>
        /// 检查分类是否有模板
        /// </summary>
        public async Task<bool> HasTemplatesAsync(int categoryId)
        {
            var hasTemplates = await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => x.CategoryId == categoryId && !x.IsDeleted)
                .AnyAsync();

            var hasSequences = await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => x.CategoryId == categoryId && !x.IsDeleted)
                .AnyAsync();

            return hasTemplates || hasSequences;
        }

        /// <summary>
        /// 添加分类
        /// </summary>
        public async Task<CustomTemplateCategory> AddAsync(CustomTemplateCategory category)
        {
            return await _db.Insertable(category).ExecuteReturnEntityAsync();
        }

        /// <summary>
        /// 更新分类
        /// </summary>
        public async Task<bool> UpdateAsync(CustomTemplateCategory category)
        {
            category.UpdatedTime = DateTime.Now;
            return await _db.Updateable(category).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 删除分类
        /// </summary>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _db.Deleteable<CustomTemplateCategory>()
                .Where(x => x.Id == id)
                .ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 软删除分类
        /// </summary>
        public async Task<bool> SoftDeleteAsync(int id, int deletedBy)
        {
            return await _db.Updateable<CustomTemplateCategory>()
                .SetColumns(x => new CustomTemplateCategory
                {
                    IsDeleted = true,
                    DeletedTime = DateTime.Now,
                    DeletedBy = deletedBy
                })
                .Where(x => x.Id == id)
                .ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 更新分类的模板数量
        /// </summary>
        public async Task<bool> UpdateTemplateCountAsync(int categoryId)
        {
            var templateCount = await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => x.CategoryId == categoryId && !x.IsDeleted)
                .CountAsync();

            var sequenceCount = await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => x.CategoryId == categoryId && !x.IsDeleted)
                .CountAsync();

            var totalCount = templateCount + sequenceCount;

            return await _db.Updateable<CustomTemplateCategory>()
                .SetColumns(x => x.TemplateCount == totalCount)
                .Where(x => x.Id == categoryId)
                .ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 批量更新分类排序
        /// </summary>
        public async Task<bool> UpdateSortOrderAsync(List<(int CategoryId, int SortOrder)> sortOrders)
        {
            try
            {
                _db.Ado.BeginTran();

                foreach (var (categoryId, sortOrder) in sortOrders)
                {
                    await _db.Updateable<CustomTemplateCategory>()
                        .SetColumns(x => x.SortOrder == sortOrder)
                        .SetColumns(x => x.UpdatedTime == DateTime.Now)
                        .Where(x => x.Id == categoryId)
                        .ExecuteCommandAsync();
                }

                _db.Ado.CommitTran();
                return true;
            }
            catch
            {
                _db.Ado.RollbackTran();
                return false;
            }
        }

        /// <summary>
        /// 获取分类统计信息
        /// </summary>
        public async Task<List<CategoryStatisticsDto>> GetCategoryStatisticsAsync()
        {
            var categories = await GetEnabledCategoriesAsync();
            var statistics = new List<CategoryStatisticsDto>();

            foreach (var category in categories)
            {
                var templateCount = await _db.Queryable<CustomUIAutoMationTemplate>()
                    .Where(x => x.CategoryId == category.Id && !x.IsDeleted)
                    .CountAsync();

                var sequenceCount = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(x => x.CategoryId == category.Id && !x.IsDeleted)
                    .CountAsync();

                var totalUsageCount = await _db.Queryable<CustomUIAutoMationTemplate>()
                    .Where(x => x.CategoryId == category.Id && !x.IsDeleted)
                    .SumAsync(x => x.UsageCount);

                var lastUsedTime = await _db.Queryable<CustomUIAutoMationTemplate>()
                    .Where(x => x.CategoryId == category.Id && !x.IsDeleted && x.LastUsedTime != null)
                    .MaxAsync(x => x.LastUsedTime);

                statistics.Add(new CategoryStatisticsDto
                {
                    CategoryId = category.Id,
                    CategoryName = category.Name,
                    TemplateCount = templateCount,
                    SequenceCount = sequenceCount,
                    TotalUsageCount = totalUsageCount,
                    LastUsedTime = lastUsedTime
                });
            }

            return statistics;
        }

        /// <summary>
        /// 获取系统预定义分类
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetSystemCategoriesAsync()
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsSystem)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户自定义分类
        /// </summary>
        public async Task<List<CustomTemplateCategory>> GetUserCategoriesAsync()
        {
            return await _db.Queryable<CustomTemplateCategory>()
                .Where(x => !x.IsDeleted && !x.IsSystem)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 移动分类到新的父分类下
        /// </summary>
        public async Task<bool> MoveCategoryAsync(int categoryId, int? newParentId)
        {
            // 检查是否会形成循环引用
            if (newParentId.HasValue && await WouldCreateCircularReference(categoryId, newParentId.Value))
            {
                return false;
            }

            return await _db.Updateable<CustomTemplateCategory>()
                .SetColumns(x => new CustomTemplateCategory
                {
                    ParentId = newParentId,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == categoryId)
                .ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 启用/禁用分类
        /// </summary>
        public async Task<bool> SetEnabledAsync(int categoryId, bool enabled)
        {
            return await _db.Updateable<CustomTemplateCategory>()
                .SetColumns(x => new CustomTemplateCategory
                {
                    IsEnabled = enabled,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == categoryId)
                .ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 检查是否会形成循环引用
        /// </summary>
        private async Task<bool> WouldCreateCircularReference(int categoryId, int newParentId)
        {
            int? currentId = newParentId;
            while (currentId.HasValue)
            {
                if (currentId == categoryId)
                {
                    return true;
                }

                var parent = await _db.Queryable<CustomTemplateCategory>()
                    .Where(x => x.Id == currentId && !x.IsDeleted)
                    .Select(x => x.ParentId)
                    .FirstAsync();

                currentId = parent;
            }

            return false;
        }

        /// <summary>
        /// 构建分类树结构
        /// </summary>
        private List<CustomTemplateCategory> BuildCategoryTree(List<CustomTemplateCategory> allCategories)
        {
            var categoryDict = allCategories.ToDictionary(x => x.Id);
            var rootCategories = new List<CustomTemplateCategory>();

            foreach (var category in allCategories)
            {
                if (category.ParentId == null)
                {
                    rootCategories.Add(category);
                }
                else if (categoryDict.TryGetValue(category.ParentId.Value, out var parent))
                {
                    parent.Children.Add(category);
                }
            }

            return rootCategories;
        }
    }
}
