using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.API.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 用户AI配置控制器
/// 功能: 管理用户个人AI模型配置和API密钥
/// 支持: CRUD操作、使用统计、成本控制、默认配置管理
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("GeneralApi")]
[Produces("application/json")]
public class UserAIConfigurationController : ControllerBase
{
    private readonly ILogger<UserAIConfigurationController> _logger;
    private readonly IUserAIConfigurationRepository _userAIConfigRepository;

    public UserAIConfigurationController(
        ILogger<UserAIConfigurationController> logger,
        IUserAIConfigurationRepository userAIConfigRepository)
    {
        _logger = logger;
        _userAIConfigRepository = userAIConfigRepository;
    }

    /// <summary>
    /// 获取当前用户的所有AI配置
    /// </summary>
    /// <returns>用户AI配置列表</returns>
    [HttpGet]
    public async Task<ActionResult<List<UserAIConfigurationDto>>> GetMyConfigurations()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户AI配置: {UserId}", userId);

            var configurations = await _userAIConfigRepository.GetByUserIdAsync(userId);
            var result = configurations.Select(MapToDto).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户AI配置失败");
            return StatusCode(500, new { message = "获取配置失败" });
        }
    }

    /// <summary>
    /// 根据ID获取AI配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>AI配置详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserAIConfigurationDto>> GetConfiguration(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _userAIConfigRepository.GetByIdAsync(id);

            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 验证配置所有权
            if (configuration.UserId != userId)
            {
                return Forbid("无权访问此配置");
            }

            return Ok(MapToDto(configuration));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI配置失败: {Id}", id);
            return StatusCode(500, new { message = "获取配置失败" });
        }
    }

    /// <summary>
    /// 获取指定类型的AI配置
    /// </summary>
    /// <param name="modelType">模型类型</param>
    /// <returns>AI配置列表</returns>
    [HttpGet("by-type/{modelType}")]
    public async Task<ActionResult<List<UserAIConfigurationDto>>> GetConfigurationsByType(string modelType)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户指定类型AI配置: {UserId}, {ModelType}", userId, modelType);

            var configurations = await _userAIConfigRepository.GetByUserIdAndTypeAsync(userId, modelType);
            var result = configurations.Select(MapToDto).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取指定类型AI配置失败: {ModelType}", modelType);
            return StatusCode(500, new { message = "获取配置失败" });
        }
    }

    /// <summary>
    /// 获取默认AI配置
    /// </summary>
    /// <param name="modelType">模型类型（可选）</param>
    /// <returns>默认AI配置</returns>
    [HttpGet("default")]
    public async Task<ActionResult<UserAIConfigurationDto>> GetDefaultConfiguration([FromQuery] string? modelType = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户默认AI配置: {UserId}, {ModelType}", userId, modelType);

            var configuration = await _userAIConfigRepository.GetDefaultConfigurationAsync(userId, modelType);
            if (configuration == null)
            {
                return NotFound(new { message = "未找到默认配置" });
            }

            return Ok(MapToDto(configuration));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取默认AI配置失败: {ModelType}", modelType);
            return StatusCode(500, new { message = "获取默认配置失败" });
        }
    }

    /// <summary>
    /// 创建AI配置
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <returns>创建的配置</returns>
    [HttpPost]
    public async Task<ActionResult<UserAIConfigurationDto>> CreateConfiguration([FromBody] CreateUserAIConfigurationDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("创建用户AI配置: {UserId}, {ProviderName}, {ModelType}",
                userId, request.ProviderName, request.ModelType);

            var configuration = new UserAIConfiguration
            {
                UserId = userId,
                ProviderName = request.ProviderName,
                ModelName = request.ModelName,
                ModelType = request.ModelType,
                ApiEndpoint = request.ApiEndpoint,
                ApiKey = request.ApiKey,
                ModelParameters = request.ModelParameters,
                IsActive = request.IsActive,
                IsDefault = request.IsDefault,
                DailyTokenLimit = request.DailyTokenLimit,
                MonthlyTokenLimit = request.MonthlyTokenLimit
            };

            // 验证配置
            var (isValid, errors) = configuration.ValidateConfiguration();
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var result = await _userAIConfigRepository.CreateAsync(configuration);
            return CreatedAtAction(nameof(GetConfiguration), new { id = result.Id }, MapToDto(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建AI配置失败");
            return StatusCode(500, new { message = "创建配置失败" });
        }
    }

    /// <summary>
    /// 更新AI配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult> UpdateConfiguration(int id, [FromBody] UpdateUserAIConfigurationDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _userAIConfigRepository.GetByIdAsync(id);

            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 验证配置所有权
            if (configuration.UserId != userId)
            {
                return StatusCode(403, new { message = "无权修改此配置" });
            }

            // 更新配置
            if (!string.IsNullOrEmpty(request.ModelName))
                configuration.ModelName = request.ModelName;
            if (!string.IsNullOrEmpty(request.ApiEndpoint))
                configuration.ApiEndpoint = request.ApiEndpoint;
            if (!string.IsNullOrEmpty(request.ApiKey))
                configuration.ApiKey = request.ApiKey;
            if (!string.IsNullOrEmpty(request.ModelParameters))
                configuration.ModelParameters = request.ModelParameters;
            if (request.IsActive.HasValue)
                configuration.IsActive = request.IsActive.Value;
            if (request.IsDefault.HasValue)
                configuration.IsDefault = request.IsDefault.Value;
            if (request.DailyTokenLimit.HasValue)
                configuration.DailyTokenLimit = request.DailyTokenLimit.Value;
            if (request.MonthlyTokenLimit.HasValue)
                configuration.MonthlyTokenLimit = request.MonthlyTokenLimit.Value;

            // 验证配置
            var (isValid, errors) = configuration.ValidateConfiguration();
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var success = await _userAIConfigRepository.UpdateAsync(configuration);
            if (!success)
            {
                return StatusCode(500, new { message = "更新配置失败" });
            }

            return Ok(new { message = "配置更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AI配置失败: {Id}", id);
            return StatusCode(500, new { message = "更新配置失败" });
        }
    }

    /// <summary>
    /// 删除AI配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteConfiguration(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _userAIConfigRepository.GetByIdAsync(id);

            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 验证配置所有权
            if (configuration.UserId != userId)
            {
                return StatusCode(403, new { message = "无权删除此配置" });
            }

            var success = await _userAIConfigRepository.DeleteAsync(id);
            if (!success)
            {
                return StatusCode(500, new { message = "删除配置失败" });
            }

            return Ok(new { message = "配置删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除AI配置失败: {Id}", id);
            return StatusCode(500, new { message = "删除配置失败" });
        }
    }

    /// <summary>
    /// 设置默认配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>设置结果</returns>
    [HttpPost("{id}/set-default")]
    public async Task<ActionResult> SetDefaultConfiguration(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _userAIConfigRepository.GetByIdAsync(id);

            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 验证配置所有权
            if (configuration.UserId != userId)
            {
                return Forbid("无权设置此配置");
            }

            var success = await _userAIConfigRepository.SetDefaultConfigurationAsync(userId, id, configuration.ModelType);
            if (!success)
            {
                return StatusCode(500, new { message = "设置默认配置失败" });
            }

            return Ok(new { message = "默认配置设置成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认配置失败: {Id}", id);
            return StatusCode(500, new { message = "设置默认配置失败" });
        }
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }

    /// <summary>
    /// 将实体映射为DTO
    /// </summary>
    private static UserAIConfigurationDto MapToDto(UserAIConfiguration entity)
    {
        return new UserAIConfigurationDto
        {
            Id = entity.Id,
            UserId = entity.UserId,
            ProviderName = entity.ProviderName,
            ModelName = entity.ModelName,
            ModelType = entity.ModelType,
            ApiEndpoint = entity.ApiEndpoint,
            ApiKeyMasked = MaskApiKey(entity.ApiKey),
            ModelParameters = entity.ModelParameters,
            IsActive = entity.IsActive,
            IsDefault = entity.IsDefault,
            DailyTokenLimit = entity.DailyTokenLimit,
            MonthlyTokenLimit = entity.MonthlyTokenLimit,
            TotalTokensUsed = entity.TotalTokensUsed,
            TotalRequests = entity.TotalRequests,
            SuccessfulRequests = entity.SuccessfulRequests,
            FailedRequests = entity.FailedRequests,
            LastUsedAt = entity.LastUsedAt,
            EstimatedCost = entity.EstimatedCost,
            CurrentMonthCost = entity.CurrentMonthCost,
            CurrentDayTokens = entity.CurrentDayTokens,
            CurrentMonthTokens = entity.CurrentMonthTokens,
            CreatedAt = entity.CreatedAt,
            UpdatedAt = entity.UpdatedAt
        };
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    /// <returns>使用统计信息</returns>
    [HttpGet("usage-statistics")]
    public async Task<ActionResult<UserAIUsageStatisticsDto>> GetUsageStatistics()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户AI使用统计: {UserId}", userId);

            var statistics = await _userAIConfigRepository.GetUsageStatisticsAsync(userId);
            var result = new UserAIUsageStatisticsDto
            {
                UserId = statistics.UserId,
                TotalRequests = statistics.TotalRequests,
                SuccessfulRequests = statistics.SuccessfulRequests,
                FailedRequests = statistics.FailedRequests,
                TotalTokensUsed = statistics.TotalTokensUsed,
                TotalCost = statistics.TotalCost,
                CurrentMonthCost = statistics.CurrentMonthCost,
                CurrentDayTokens = statistics.CurrentDayTokens,
                CurrentMonthTokens = statistics.CurrentMonthTokens,
                LastUsedAt = statistics.LastUsedAt,
                ProviderStatistics = statistics.ProviderStatistics.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new ProviderUsageStatisticsDto
                    {
                        ProviderName = kvp.Value.ProviderName,
                        Requests = kvp.Value.Requests,
                        TokensUsed = kvp.Value.TokensUsed,
                        Cost = kvp.Value.Cost
                    }),
                ModelTypeStatistics = statistics.ModelTypeStatistics.ToDictionary(
                    kvp => kvp.Key,
                    kvp => new ModelTypeUsageStatisticsDto
                    {
                        ModelType = kvp.Value.ModelType,
                        Requests = kvp.Value.Requests,
                        TokensUsed = kvp.Value.TokensUsed,
                        Cost = kvp.Value.Cost
                    })
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取使用统计失败");
            return StatusCode(500, new { message = "获取使用统计失败" });
        }
    }

    /// <summary>
    /// 获取超出限制的配置
    /// </summary>
    /// <returns>超出限制的配置列表</returns>
    [HttpGet("over-limit")]
    public async Task<ActionResult<List<UserAIConfigurationDto>>> GetOverLimitConfigurations()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取超出限制的配置: {UserId}", userId);

            var configurations = await _userAIConfigRepository.GetOverLimitConfigurationsAsync(userId);
            var result = configurations.Select(MapToDto).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取超出限制的配置失败");
            return StatusCode(500, new { message = "获取超出限制的配置失败" });
        }
    }

    /// <summary>
    /// 批量更新配置状态
    /// </summary>
    /// <param name="request">批量更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPost("batch-update-status")]
    public async Task<ActionResult> BatchUpdateStatus([FromBody] BatchUpdateStatusRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("批量更新配置状态: {UserId}, {IsActive}", userId, request.IsActive);

            var success = await _userAIConfigRepository.BatchUpdateStatusAsync(userId, request.IsActive, request.ConfigurationIds);
            if (!success)
            {
                return StatusCode(500, new { message = "批量更新失败" });
            }

            return Ok(new { message = "批量更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新配置状态失败");
            return StatusCode(500, new { message = "批量更新失败" });
        }
    }

    /// <summary>
    /// 脱敏API密钥
    /// </summary>
    private static string? MaskApiKey(string? apiKey)
    {
        if (string.IsNullOrEmpty(apiKey) || apiKey.Length <= 8)
            return apiKey;

        return apiKey[..4] + "****" + apiKey[^4..];
    }
}

/// <summary>
/// 批量更新状态请求
/// </summary>
public class BatchUpdateStatusRequest
{
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 配置ID列表（可选，为空则更新所有配置）
    /// </summary>
    public List<int>? ConfigurationIds { get; set; }
}
