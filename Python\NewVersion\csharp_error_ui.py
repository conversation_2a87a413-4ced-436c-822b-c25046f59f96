#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C#编译错误捕获界面
提供项目路径设置和错误信息列表显示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import subprocess
import re
import os
from pathlib import Path
import threading
import time
from datetime import datetime

class CSharpErrorCaptureUI:
    """C#编译错误捕获界面"""

    def __init__(self, parent=None, api_client=None):
        """初始化界面"""
        if parent is None:
            self.root = tk.Tk()
            self.root.title("编译C#项目")
            self.root.geometry("1000x700")
            self.is_embedded = False
        elif isinstance(parent, (tk.Frame, ttk.Frame)):
            # 嵌入式模式 - 支持tk.Frame和ttk.Frame
            self.root = parent
            self.is_embedded = True
        else:
            self.root = tk.Toplevel(parent)
            self.root.title("编译C#项目")
            self.root.geometry("1000x700")
            self.is_embedded = False

        # API客户端
        self.api_client = api_client

        self.project_path = tk.StringVar()
        self.auto_compile = tk.BooleanVar(value=False)
        self.errors_only = tk.BooleanVar(value=True)  # 默认只获取错误
        self.auto_clear_errors = tk.BooleanVar(value=True)  # 默认自动清空错误列表
        self.error_history = []
        self.watch_thread = None

        # 自动编译相关变量
        self.auto_compile_thread = None
        self.is_auto_compiling = False
        self.auto_compile_interval = tk.IntVar(value=30)  # 默认30秒

        # 自动刷新相关变量
        self.auto_refresh = tk.BooleanVar(value=False)
        self.auto_refresh_thread = None
        self.auto_refresh_interval = tk.IntVar(value=10)  # 默认10秒

        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 项目路径设置区域
        self.create_project_path_section(main_frame)

        # 编译控制区域
        self.create_compile_control_section(main_frame)

        # 创建标签页
        self.create_notebook_section(main_frame)

        # 状态栏
        self.create_status_bar(main_frame)

    def create_project_path_section(self, parent):
        """创建项目路径设置区域"""
        path_frame = ttk.LabelFrame(parent, text="C#项目设置", padding=10)
        path_frame.pack(fill=tk.X, pady=(0, 10))

        # 项目路径输入
        ttk.Label(path_frame, text="项目路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        path_entry = ttk.Entry(path_frame, textvariable=self.project_path, width=60)
        path_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 5))

        ttk.Button(path_frame, text="浏览...", command=self.browse_project_path).grid(row=0, column=2)

        # 配置列权重
        path_frame.columnconfigure(1, weight=1)

        # 项目信息显示
        info_frame = ttk.Frame(path_frame)
        info_frame.grid(row=1, column=0, columnspan=3, sticky=tk.EW, pady=(10, 0))

        self.project_info_label = ttk.Label(info_frame, text="请选择C#项目路径", foreground="gray")
        self.project_info_label.pack(side=tk.LEFT)

        # 绑定路径变化事件
        self.project_path.trace('w', self.on_project_path_changed)

    def create_notebook_section(self, parent):
        """创建标签页区域"""
        # 创建Notebook控件
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 后端编译错误标签页
        self.backend_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.backend_frame, text="🔧 后端编译错误")

        # 前端编译错误标签页
        self.frontend_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.frontend_frame, text="🌐 前端编译错误")

        # 创建后端错误列表
        self.create_error_list_section(self.backend_frame, "backend")

        # 创建前端错误列表
        self.create_error_list_section(self.frontend_frame, "frontend")

        # 创建错误详情区域（共享）
        self.create_error_detail_section(parent)

    def create_compile_control_section(self, parent):
        """创建编译控制区域"""
        control_frame = ttk.LabelFrame(parent, text="编译控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        self.compile_button = ttk.Button(button_frame, text="🔨 立即编译", command=self.compile_now)
        self.compile_button.pack(side=tk.LEFT, padx=(0, 5))

        # API相关按钮
        ttk.Button(button_frame, text="🔄 从API获取错误", command=self.load_errors_from_api).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🧹 清空错误列表", command=self.clear_error_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 保存错误报告", command=self.save_error_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🤖 发送给AI修复", command=self.send_to_ai).pack(side=tk.LEFT, padx=(0, 5))

        # 编译选项
        options_frame = ttk.Frame(control_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        # 第一行选项
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X)

        ttk.Checkbutton(options_row1, text="监控文件变化自动编译", variable=self.auto_compile,
                       command=self.toggle_auto_compile).pack(side=tk.LEFT)

        ttk.Checkbutton(options_row1, text="仅获取错误（忽略警告）", variable=self.errors_only).pack(side=tk.LEFT, padx=(20, 0))

        self.auto_status_label = ttk.Label(options_row1, text="", foreground="green")
        self.auto_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 第二行选项 - 定时自动编译
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X, pady=(5, 0))

        self.auto_compile_enabled = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row2, text="定时自动编译", variable=self.auto_compile_enabled,
                       command=self.toggle_auto_compile_timer).pack(side=tk.LEFT)

        ttk.Label(options_row2, text="间隔:").pack(side=tk.LEFT, padx=(10, 5))

        interval_spinbox = ttk.Spinbox(options_row2, from_=10, to=300, width=8,
                                     textvariable=self.auto_compile_interval)
        interval_spinbox.pack(side=tk.LEFT)

        ttk.Label(options_row2, text="秒").pack(side=tk.LEFT, padx=(5, 10))

        ttk.Checkbutton(options_row2, text="编译前自动清空错误列表", variable=self.auto_clear_errors).pack(side=tk.LEFT, padx=(20, 0))

        self.auto_compile_status_label = ttk.Label(options_row2, text="", foreground="blue")
        self.auto_compile_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 第三行选项 - API自动刷新
        if self.api_client:
            options_row3 = ttk.Frame(options_frame)
            options_row3.pack(fill=tk.X, pady=(5, 0))

            ttk.Checkbutton(options_row3, text="自动从API刷新错误", variable=self.auto_refresh,
                           command=self.toggle_auto_refresh).pack(side=tk.LEFT)

            ttk.Label(options_row3, text="间隔:").pack(side=tk.LEFT, padx=(10, 5))

            refresh_spinbox = ttk.Spinbox(options_row3, from_=5, to=120, width=8,
                                        textvariable=self.auto_refresh_interval)
            refresh_spinbox.pack(side=tk.LEFT)

            ttk.Label(options_row3, text="秒").pack(side=tk.LEFT, padx=(5, 10))

            self.auto_refresh_status_label = ttk.Label(options_row3, text="", foreground="green")
            self.auto_refresh_status_label.pack(side=tk.LEFT, padx=(10, 0))

    def create_error_list_section(self, parent):
        """创建错误信息列表区域"""
        list_frame = ttk.LabelFrame(parent, text="编译错误列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建Treeview
        columns = ('时间', '类型', '文件', '行号', '错误代码', '错误信息')
        self.error_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        # 设置列标题和宽度
        self.error_tree.heading('时间', text='时间')
        self.error_tree.heading('类型', text='类型')
        self.error_tree.heading('文件', text='文件')
        self.error_tree.heading('行号', text='行号')
        self.error_tree.heading('错误代码', text='错误代码')
        self.error_tree.heading('错误信息', text='错误信息')

        self.error_tree.column('时间', width=120, minwidth=100)
        self.error_tree.column('类型', width=60, minwidth=50)
        self.error_tree.column('文件', width=200, minwidth=150)
        self.error_tree.column('行号', width=60, minwidth=50)
        self.error_tree.column('错误代码', width=80, minwidth=70)
        self.error_tree.column('错误信息', width=400, minwidth=300)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.error_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.error_tree.xview)
        self.error_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.error_tree.grid(row=0, column=0, sticky=tk.NSEW)
        scrollbar_y.grid(row=0, column=1, sticky=tk.NS)
        scrollbar_x.grid(row=1, column=0, sticky=tk.EW)

        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)

        # 绑定选择事件
        self.error_tree.bind('<<TreeviewSelect>>', self.on_error_selected)

        # 右键菜单
        self.create_context_menu()

    def create_error_detail_section(self, parent):
        """创建错误详情区域"""
        detail_frame = ttk.LabelFrame(parent, text="错误详情", padding=10)
        detail_frame.pack(fill=tk.X, pady=(0, 10))

        # 错误详情文本框
        self.error_detail_text = scrolledtext.ScrolledText(detail_frame, height=6, wrap=tk.WORD)
        self.error_detail_text.pack(fill=tk.BOTH, expand=True)

        # 初始提示
        self.error_detail_text.insert(tk.END, "请选择错误列表中的项目查看详细信息...")
        self.error_detail_text.config(state=tk.DISABLED)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)

        self.status_label = ttk.Label(status_frame, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 进度条（默认隐藏）
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate', length=200)

        self.compile_time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
        self.compile_time_label.pack(side=tk.RIGHT, padx=(5, 0))

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="复制错误信息", command=self.copy_error_info)
        self.context_menu.add_command(label="在文件中定位", command=self.locate_in_file)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除此错误", command=self.delete_selected_error)

        self.error_tree.bind("<Button-3>", self.show_context_menu)

    def browse_project_path(self):
        """浏览项目路径"""
        initial_dir = self.project_path.get() or os.getcwd()

        # 选择项目文件或文件夹
        file_path = filedialog.askopenfilename(
            title="选择C#项目文件",
            initialdir=initial_dir,
            filetypes=[
                ("C# Project Files", "*.csproj"),
                ("Solution Files", "*.sln"),
                ("All Files", "*.*")
            ]
        )

        if file_path:
            self.project_path.set(file_path)

    def on_project_path_changed(self, *args):
        """项目路径变化事件"""
        path = self.project_path.get().strip()
        if not path:
            self.project_info_label.config(text="请选择C#项目路径", foreground="gray")
            return

        path_obj = Path(path)
        if path_obj.exists():
            if path_obj.is_file() and path_obj.suffix in ['.csproj', '.sln']:
                self.project_info_label.config(text=f"✅ 项目: {path_obj.name}", foreground="green")
            elif path_obj.is_dir():
                # 查找项目文件
                csproj_files = list(path_obj.glob("*.csproj"))
                if csproj_files:
                    self.project_info_label.config(text=f"✅ 找到 {len(csproj_files)} 个项目文件", foreground="green")
                else:
                    self.project_info_label.config(text="⚠️ 目录中没有找到.csproj文件", foreground="orange")
            else:
                self.project_info_label.config(text="❌ 不是有效的项目文件", foreground="red")
        else:
            self.project_info_label.config(text="❌ 路径不存在", foreground="red")

    def compile_now(self):
        """立即编译"""
        path = self.project_path.get().strip()
        if not path:
            messagebox.showwarning("警告", "请先设置项目路径")
            return

        if not Path(path).exists():
            messagebox.showerror("错误", "项目路径不存在")
            return

        # 根据用户设置决定是否清空之前的错误列表
        if self.auto_clear_errors.get():
            self.clear_error_list_silent()

        # 在后台线程中执行编译
        self.status_label.config(text="🔨 正在编译，请稍候...")

        # 显示进度条并开始动画
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 10))
        self.progress_bar.start(10)

        # 禁用编译按钮防止重复点击
        self.compile_button.config(state="disabled")

        self.root.update()

        def compile_thread():
            try:
                result = self.execute_compile(path)
                # 将结果处理也放到后台线程，只将UI更新放到主线程
                self.root.after(0, lambda r=result: self.handle_compile_result_async(r))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda msg=error_msg: self.handle_compile_error(msg))
            finally:
                # 重新启用编译按钮
                self.root.after(0, self.enable_compile_button)

        threading.Thread(target=compile_thread, daemon=True).start()

    def enable_compile_button(self):
        """重新启用编译按钮"""
        self.compile_button.config(state="normal")
        # 停止并隐藏进度条
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

    def execute_compile(self, project_path: str) -> dict:
        """执行编译"""
        start_time = time.time()

        try:
            # 构建dotnet build命令
            cmd = ['dotnet', 'build', project_path, '--verbosity', 'normal']
            print(f"🔨 开始执行编译命令: {' '.join(cmd)}")

            # 使用Popen进行非阻塞执行，可以实时读取输出
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=Path(project_path).parent if Path(project_path).is_file() else project_path,
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )

            # 实时读取输出
            output_lines = []
            last_update_time = time.time()

            while True:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line)

                    # 每2秒更新一次状态，避免过于频繁的UI更新
                    current_time = time.time()
                    if current_time - last_update_time > 2:
                        # 实时显示编译进度
                        if "正在还原" in line or "Restoring" in line:
                            print("📦 正在还原包...")
                            self.root.after(0, lambda: self.status_label.config(text="📦 正在还原NuGet包..."))
                        elif "正在编译" in line or "Building" in line:
                            print("🔨 正在编译...")
                            self.root.after(0, lambda: self.status_label.config(text="🔨 正在编译项目..."))
                        elif "已完成" in line or "Finished" in line:
                            print("✅ 编译阶段完成")
                            self.root.after(0, lambda: self.status_label.config(text="✅ 编译阶段完成，正在处理结果..."))

                        last_update_time = current_time

                elif process.poll() is not None:
                    break

            # 等待进程完成
            return_code = process.wait(timeout=120)
            output = ''.join(output_lines)

            compile_time = time.time() - start_time

            # 解析编译结果
            errors, warnings = self.parse_build_output(output)

            # 如果只获取错误，则清空警告列表
            if self.errors_only.get():
                warnings = []

            return {
                'success': return_code == 0,
                'return_code': return_code,
                'errors': errors,
                'warnings': warnings,
                'compile_time': compile_time,
                'raw_output': output
            }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error_type': 'timeout',
                'message': '编译超时（超过2分钟）',
                'errors': [],
                'warnings': [],
                'compile_time': time.time() - start_time
            }
        except FileNotFoundError:
            return {
                'success': False,
                'error_type': 'dotnet_not_found',
                'message': '未找到dotnet命令，请确保已安装.NET SDK',
                'errors': [],
                'warnings': [],
                'compile_time': 0
            }

    def parse_build_output(self, output: str) -> tuple:
        """解析编译输出"""
        errors = []
        warnings = []

        # C#编译错误和警告的正则表达式模式 - 支持中英文
        # 格式: file.cs(line,col): error CS1234: message
        error_pattern = re.compile(r'([^(]+)\((\d+),(\d+)\):\s*error\s+([A-Z]+\d+):\s*(.+)', re.IGNORECASE)
        warning_pattern = re.compile(r'([^(]+)\((\d+),(\d+)\):\s*warning\s+([A-Z]+\d+):\s*(.+)', re.IGNORECASE)

        lines = output.split('\n')
        total_lines = len(lines)
        processed_lines = 0

        print(f"🔍 开始解析编译输出，共 {total_lines} 行")

        for line in lines:
            line = line.strip()
            if not line:
                processed_lines += 1
                continue

            # 每处理100行输出一次进度，避免长时间无响应
            if processed_lines % 100 == 0 and processed_lines > 0:
                print(f"📊 已处理 {processed_lines}/{total_lines} 行编译输出")

            # 匹配错误
            error_match = error_pattern.search(line)
            if error_match:
                file_path = error_match.group(1).strip()
                # 转换为绝对路径
                absolute_file_path = self.convert_to_absolute_path(file_path)

                errors.append({
                    'file': absolute_file_path,
                    'line': int(error_match.group(2)),
                    'column': int(error_match.group(3)),
                    'code': error_match.group(4),
                    'message': error_match.group(5).strip(),
                    'severity': 'error',
                    'raw_line': line,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                })
                processed_lines += 1
                continue

            # 匹配警告
            warning_match = warning_pattern.search(line)
            if warning_match:
                file_path = warning_match.group(1).strip()
                # 转换为绝对路径
                absolute_file_path = self.convert_to_absolute_path(file_path)

                warnings.append({
                    'file': absolute_file_path,
                    'line': int(warning_match.group(2)),
                    'column': int(warning_match.group(3)),
                    'code': warning_match.group(4),
                    'message': warning_match.group(5).strip(),
                    'severity': 'warning',
                    'raw_line': line,
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                })
                processed_lines += 1
                continue

            processed_lines += 1

        print(f"✅ 编译输出解析完成，发现 {len(errors)} 个错误，{len(warnings)} 个警告")
        return errors, warnings

    def convert_to_absolute_path(self, file_path: str) -> str:
        """将文件路径转换为绝对路径"""
        if not file_path:
            return file_path

        # 如果已经是绝对路径，直接返回
        if Path(file_path).is_absolute():
            return file_path

        # 如果是相对路径，基于项目路径转换为绝对路径
        try:
            project_path_str = self.project_path.get().strip()
            if not project_path_str:
                return file_path

            project_path = Path(project_path_str)
            # 基于项目根目录解析相对路径
            project_root = project_path.parent if project_path.is_file() else project_path
            absolute_path = (project_root / file_path).resolve()
            return str(absolute_path)
        except Exception:
            # 如果转换失败，返回原路径
            return file_path

    def handle_compile_result(self, result: dict):
        """处理编译结果（保留用于兼容性，建议使用handle_compile_result_async）"""
        try:
            compile_time = result.get('compile_time', 0)
            self.compile_time_label.config(text=f"编译时间: {compile_time:.2f}s")

            if result['success']:
                if not result['errors'] and not result['warnings']:
                    self.status_label.config(text="✅ 编译成功 - 无错误无警告")
                else:
                    warning_count = len(result.get('warnings', []))
                    if warning_count > 0:
                        self.status_label.config(text=f"✅ 编译成功 - {warning_count}个警告")
                    else:
                        self.status_label.config(text="✅ 编译成功")
            else:
                error_count = len(result.get('errors', []))
                warning_count = len(result.get('warnings', []))
                self.status_label.config(text=f"❌ 编译失败: {error_count}个错误, {warning_count}个警告")

            # 使用异步方式处理错误列表，避免阻塞UI
            all_items = result.get('errors', []) + result.get('warnings', [])
            if all_items:
                # 总是使用分批处理，避免UI卡顿
                self._add_items_in_batches(all_items)

            # 保存到历史记录
            self.error_history.append({
                'timestamp': datetime.now(),
                'result': result
            })

        except Exception as e:
            print(f"❌ 处理编译结果失败: {e}")
            self.status_label.config(text="❌ 处理结果失败")

    def handle_compile_result_async(self, result: dict):
        """异步处理编译结果，避免阻塞UI"""
        def process_result():
            try:
                # 先更新基本状态信息（快速操作）
                compile_time = result.get('compile_time', 0)
                self.root.after(0, lambda: self.compile_time_label.config(text=f"编译时间: {compile_time:.2f}s"))

                # 更新状态标签
                if result['success']:
                    if not result['errors'] and not result['warnings']:
                        self.root.after(0, lambda: self.status_label.config(text="✅ 编译成功 - 无错误无警告"))
                    else:
                        warning_count = len(result.get('warnings', []))
                        if warning_count > 0:
                            self.root.after(0, lambda: self.status_label.config(text=f"✅ 编译成功 - {warning_count}个警告"))
                        else:
                            self.root.after(0, lambda: self.status_label.config(text="✅ 编译成功"))
                else:
                    error_count = len(result.get('errors', []))
                    warning_count = len(result.get('warnings', []))
                    self.root.after(0, lambda: self.status_label.config(text=f"❌ 编译失败: {error_count}个错误, {warning_count}个警告"))

                # 处理错误和警告列表（可能耗时的操作）
                all_items = result.get('errors', []) + result.get('warnings', [])
                if all_items:
                    # 无论多少项目，都使用分批处理以确保UI流畅
                    self.root.after(0, lambda: self._add_items_in_batches(all_items, batch_size=10))

                # 保存到历史记录
                self.error_history.append({
                    'timestamp': datetime.now(),
                    'result': result
                })

            except Exception as e:
                print(f"❌ 处理编译结果异常: {e}")
                self.root.after(0, lambda: self.status_label.config(text="❌ 处理结果失败"))

        # 在后台线程中处理结果
        threading.Thread(target=process_result, daemon=True).start()

    def handle_compile_error(self, error_message: str):
        """处理编译异常"""
        self.status_label.config(text=f"❌ 编译异常: {error_message}")
        # 移除阻塞的messagebox，改为在控制台输出
        print(f"❌ 编译异常: {error_message}")

        # 如果需要显示错误对话框，可以使用非阻塞方式
        # 或者只在手动编译时显示，自动编译时不显示

    def add_errors_to_list(self, items: list):
        """添加错误/警告到列表"""
        if not items:
            return

        # 限制单次添加的数量，避免UI卡顿
        if len(items) > 10:
            print(f"⚠️ 单次添加项目过多({len(items)})，建议使用分批添加")

        # 批量处理以提高性能
        try:
            for item in items:
                # 获取文件名（用于显示）
                file_name = Path(item['file']).name if item['file'] else ''
                # 保留完整路径（用于复制）
                full_path = item['file'] if item['file'] else ''

                # 插入到TreeView，将完整路径存储在第7个位置（隐藏列）
                self.error_tree.insert('', tk.END, values=(
                    item['timestamp'],
                    item['severity'].upper(),
                    file_name,  # 显示文件名
                    item['line'],
                    item['code'],
                    item['message'][:100] + '...' if len(item['message']) > 100 else item['message'],
                    full_path  # 存储完整路径
                ), tags=(item['severity'],))

                # 减少update_idletasks的调用频率，只在处理较多项目时调用
                # 现在分批处理已经很小了，这里不需要频繁调用

            # 设置不同类型的颜色
            self.error_tree.tag_configure('error', foreground='red')
            self.error_tree.tag_configure('warning', foreground='orange')

        except Exception as e:
            print(f"❌ 添加错误项到列表失败: {e}")

        # 如果错误数量很多，滚动到最新的错误
        if len(items) > 0:
            try:
                children = self.error_tree.get_children()
                if children:
                    self.error_tree.see(children[-1])
            except Exception as e:
                print(f"❌ 滚动到最新错误失败: {e}")

    def _add_items_in_batches(self, items: list, batch_size: int = 3):
        """分批添加错误项以避免UI卡顿"""
        if not items:
            return

        print(f"🔄 开始分批添加 {len(items)} 个错误/警告项，每批 {batch_size} 个")

        def add_batch(start_index: int):
            try:
                # 添加一批项目
                end_index = min(start_index + batch_size, len(items))
                batch_items = items[start_index:end_index]

                if batch_items:
                    self.add_errors_to_list(batch_items)
                    print(f"📝 已添加第 {start_index//batch_size + 1} 批，共 {len(batch_items)} 项")

                # 如果还有更多项目，安排下一批
                if end_index < len(items):
                    # 增加延迟时间，给UI更多响应时间
                    self.root.after(100, lambda: add_batch(end_index))
                else:
                    print(f"✅ 完成添加所有 {len(items)} 个错误/警告项")

            except Exception as e:
                print(f"❌ 分批添加错误项失败: {e}")

        # 开始添加第一批
        add_batch(0)

    def clear_error_list(self):
        """清空错误列表"""
        if messagebox.askyesno("确认", "确定要清空所有错误记录吗？"):
            self.error_tree.delete(*self.error_tree.get_children())
            self.error_detail_text.config(state=tk.NORMAL)
            self.error_detail_text.delete(1.0, tk.END)
            self.error_detail_text.insert(tk.END, "错误列表已清空...")
            self.error_detail_text.config(state=tk.DISABLED)
            self.status_label.config(text="错误列表已清空")

    def clear_error_list_silent(self):
        """静默清空错误列表（不弹出确认对话框）"""
        try:
            # 清空TreeView中的所有项目
            self.error_tree.delete(*self.error_tree.get_children())

            # 清空错误详情文本
            self.error_detail_text.config(state=tk.NORMAL)
            self.error_detail_text.delete(1.0, tk.END)
            self.error_detail_text.insert(tk.END, "准备编译...")
            self.error_detail_text.config(state=tk.DISABLED)

            print("🧹 已清空之前的错误列表")

        except Exception as e:
            print(f"❌ 清空错误列表失败: {e}")

    def save_error_report(self):
        """保存错误报告"""
        if not self.error_tree.get_children():
            messagebox.showwarning("警告", "没有错误信息可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存错误报告",
            defaultextension=".txt",
            filetypes=[
                ("Text Files", "*.txt"),
                ("JSON Files", "*.json"),
                ("All Files", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"C#编译错误报告\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"项目路径: {self.project_path.get()}\n")
                    f.write("=" * 50 + "\n\n")

                    for item in self.error_tree.get_children():
                        values = self.error_tree.item(item)['values']
                        f.write(f"时间: {values[0]}\n")
                        f.write(f"类型: {values[1]}\n")
                        f.write(f"文件: {values[2]}\n")
                        f.write(f"行号: {values[3]}\n")
                        f.write(f"错误代码: {values[4]}\n")
                        f.write(f"错误信息: {values[5]}\n")
                        f.write("-" * 30 + "\n")

                messagebox.showinfo("成功", f"错误报告已保存到: {file_path}")
                self.status_label.config(text=f"错误报告已保存")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def send_to_ai(self):
        """发送错误信息给AI修复"""
        if not self.error_tree.get_children():
            messagebox.showwarning("警告", "没有错误信息可发送")
            return

        # 格式化错误信息
        error_text = self.format_errors_for_ai()

        # 创建AI修复对话框
        self.show_ai_dialog(error_text)

    def format_errors_for_ai(self) -> str:
        """格式化错误信息供AI处理"""
        errors = []
        warnings = []

        for item in self.error_tree.get_children():
            values = self.error_tree.item(item)['values']
            # 使用完整路径（存储在第7个位置，索引6）而不是显示的文件名
            full_path = values[6] if len(values) > 6 and values[6] else values[2]
            error_info = {
                'type': values[1],
                'file': full_path,
                'line': values[3],
                'code': values[4],
                'message': values[5]
            }

            if values[1] == 'ERROR':
                errors.append(error_info)
            else:
                warnings.append(error_info)

        formatted = f"C#项目编译错误报告\n"
        formatted += f"项目路径: {self.project_path.get()}\n"
        formatted += f"错误数量: {len(errors)}, 警告数量: {len(warnings)}\n\n"

        if errors:
            formatted += "编译错误:\n"
            for i, error in enumerate(errors, 1):
                formatted += f"{i}. 文件: {error['file']}, 行: {error['line']}\n"
                formatted += f"   错误代码: {error['code']}\n"
                formatted += f"   错误信息: {error['message']}\n\n"

        if warnings:
            formatted += "编译警告:\n"
            for i, warning in enumerate(warnings, 1):
                formatted += f"{i}. 文件: {warning['file']}, 行: {warning['line']}\n"
                formatted += f"   警告代码: {warning['code']}\n"
                formatted += f"   警告信息: {warning['message']}\n\n"

        return formatted

    def show_ai_dialog(self, error_text: str):
        """显示AI修复对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("发送给AI修复")
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # 错误信息显示
        ttk.Label(dialog, text="错误信息 (将发送给AI):").pack(anchor=tk.W, padx=10, pady=(10, 5))

        error_text_widget = scrolledtext.ScrolledText(dialog, height=20, wrap=tk.WORD)
        error_text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        error_text_widget.insert(tk.END, error_text)
        error_text_widget.config(state=tk.DISABLED)

        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="复制到剪贴板",
                  command=lambda: self.copy_to_clipboard(error_text)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT)

        # 说明文本
        info_label = ttk.Label(dialog,
                              text="提示: 您可以复制上述错误信息，然后粘贴到AI助手(如ChatGPT、Copilot等)中请求修复建议",
                              foreground="blue", wraplength=750)
        info_label.pack(anchor=tk.W, padx=10, pady=(0, 10))

    def copy_to_clipboard(self, text: str):
        """复制文本到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        # 移除弹出对话框，静默复制
        print("✅ 错误信息已复制到剪贴板")

    def toggle_auto_compile(self):
        """切换自动编译"""
        if self.auto_compile.get():
            if not self.project_path.get().strip():
                messagebox.showwarning("警告", "请先设置项目路径")
                self.auto_compile.set(False)
                return

            self.start_auto_compile()
        else:
            self.stop_auto_compile()

    def start_auto_compile(self):
        """开始自动编译监控"""
        self.auto_status_label.config(text="🔍 监控中...", foreground="green")

        def watch_loop():
            last_modified = 0
            project_path = Path(self.project_path.get())

            while self.auto_compile.get():
                try:
                    if project_path.exists():
                        # 检查C#文件的修改时间
                        cs_files = list(project_path.parent.rglob('*.cs')) if project_path.is_file() else list(project_path.rglob('*.cs'))

                        if cs_files:
                            current_modified = max(f.stat().st_mtime for f in cs_files)

                            if current_modified > last_modified and last_modified > 0:
                                self.root.after(0, lambda: self.auto_status_label.config(text="🔨 自动编译中...", foreground="orange"))
                                # 根据用户设置决定是否清空之前的错误列表
                                if self.auto_clear_errors.get():
                                    self.root.after(0, self.clear_error_list_silent)
                                result = self.execute_compile(str(project_path))
                                self.root.after(0, lambda: self.handle_compile_result_async(result))
                                self.root.after(0, lambda: self.auto_status_label.config(text="🔍 监控中...", foreground="green"))

                            last_modified = max(last_modified, current_modified)

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    print(f"自动编译监控错误: {e}")
                    time.sleep(5)

        self.watch_thread = threading.Thread(target=watch_loop, daemon=True)
        self.watch_thread.start()

    def stop_auto_compile(self):
        """停止自动编译监控"""
        self.auto_status_label.config(text="", foreground="black")
        if self.watch_thread:
            self.watch_thread = None

    def on_error_selected(self, event):
        """错误选择事件"""
        selection = self.error_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.error_tree.item(item)['values']

        # 显示详细信息
        detail_text = f"错误详情:\n\n"
        detail_text += f"时间: {values[0]}\n"
        detail_text += f"类型: {values[1]}\n"
        # 使用完整路径（存储在第7个位置，索引6）而不是显示的文件名
        full_path = values[6] if len(values) > 6 and values[6] else values[2]
        detail_text += f"文件: {full_path}\n"
        detail_text += f"行号: {values[3]}\n"
        detail_text += f"列号: 未知\n"
        detail_text += f"错误代码: {values[4]}\n"
        detail_text += f"错误信息: {values[5]}\n\n"

        # 添加修复建议
        error_code = values[4]
        suggestions = self.get_error_suggestions(error_code)
        if suggestions:
            detail_text += f"修复建议:\n{suggestions}\n"

        self.error_detail_text.config(state=tk.NORMAL)
        self.error_detail_text.delete(1.0, tk.END)
        self.error_detail_text.insert(tk.END, detail_text)
        self.error_detail_text.config(state=tk.DISABLED)

    def get_error_suggestions(self, error_code: str) -> str:
        """获取错误修复建议"""
        suggestions = {
            'CS0246': '类型或命名空间名称未找到。检查using语句和程序集引用。',
            'CS1002': '语法错误，应输入";"。检查语句结尾的分号。',
            'CS0103': '当前上下文中不存在名称。检查变量声明和拼写。',
            'CS0117': '不包含的定义。检查方法名称和参数。',
            'CS0029': '无法将类型隐式转换为。检查类型转换。',
            'CS0161': '并非所有代码路径都返回值。确保所有分支都有返回语句。',
            'CS0019': '运算符不能应用于类型的操作数。检查操作数类型。',
            'CS0266': '无法将类型隐式转换，存在显式转换。使用显式转换。'
        }

        return suggestions.get(error_code, '暂无针对此错误的修复建议。')

    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.error_tree.identify_row(event.y)
        if item:
            self.error_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def copy_error_info(self):
        """复制错误信息"""
        selection = self.error_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.error_tree.item(item)['values']

        # 使用完整路径（存储在第7个位置，索引6）而不是显示的文件名
        full_path = values[6] if len(values) > 6 and values[6] else values[2]
        error_text = f"文件: {full_path}\n行号: {values[3]}\n错误代码: {values[4]}\n错误信息: {values[5]}"

        self.root.clipboard_clear()
        self.root.clipboard_append(error_text)
        # 移除弹出对话框，静默复制
        print("✅ 错误信息已复制到剪贴板")

    def locate_in_file(self):
        """在文件中定位错误"""
        selection = self.error_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.error_tree.item(item)['values']
        file_name = values[2]
        line_number = values[3]

        messagebox.showinfo("定位", f"文件: {file_name}\n行号: {line_number}\n\n请在IDE中打开此文件并跳转到指定行号。")

    def delete_selected_error(self):
        """删除选中的错误"""
        selection = self.error_tree.selection()
        if not selection:
            return

        if messagebox.askyesno("确认", "确定要删除选中的错误记录吗？"):
            for item in selection:
                self.error_tree.delete(item)

    def load_settings(self):
        """加载设置"""
        # 设置默认项目路径
        default_paths = [
            r"d:\GitProjects\ProjectManagementAI\Backend\ProjectManagement.API\ProjectManagement.API.csproj",
            r"d:\Projects\ProjectManagement\Backend\ProjectManagement.API\ProjectManagement.API.csproj"
        ]

        for default_path in default_paths:
            if Path(default_path).exists():
                self.project_path.set(default_path)
                break

        # 可以在这里加载保存的定时编译设置
        # 例如从配置文件或注册表中读取上次的间隔设置

    def save_settings(self):
        """保存设置"""
        # 可以在这里保存定时编译设置
        # 例如保存到配置文件或注册表中
        pass

    def toggle_auto_compile_timer(self):
        """切换定时自动编译"""
        if self.auto_compile_enabled.get():
            self.start_auto_compile_timer()
        else:
            self.stop_auto_compile_timer()

    def start_auto_compile_timer(self):
        """启动定时自动编译"""
        if self.is_auto_compiling:
            return

        path = self.project_path.get().strip()
        if not path:
            messagebox.showwarning("警告", "请先设置项目路径")
            self.auto_compile_enabled.set(False)
            return

        if not Path(path).exists():
            messagebox.showerror("错误", "项目路径不存在")
            self.auto_compile_enabled.set(False)
            return

        self.is_auto_compiling = True
        self.auto_compile_status_label.config(text="⏰ 定时编译已启动")

        # 启动定时编译线程
        self.auto_compile_thread = threading.Thread(target=self._auto_compile_worker, daemon=True)
        self.auto_compile_thread.start()

        print(f"✅ 定时自动编译已启动，间隔: {self.auto_compile_interval.get()}秒")

    def stop_auto_compile_timer(self):
        """停止定时自动编译"""
        self.is_auto_compiling = False
        self.auto_compile_status_label.config(text="")

        if self.auto_compile_thread:
            self.auto_compile_thread = None

        print("⏹️ 定时自动编译已停止")

    def _auto_compile_worker(self):
        """定时自动编译工作线程"""
        while self.is_auto_compiling:
            try:
                # 等待指定的时间间隔
                for i in range(self.auto_compile_interval.get()):
                    if not self.is_auto_compiling:
                        return
                    time.sleep(1)

                    # 更新状态显示倒计时
                    remaining = self.auto_compile_interval.get() - i
                    self.root.after(0, lambda r=remaining:
                        self.auto_compile_status_label.config(text=f"⏰ 下次编译: {r}秒"))

                # 如果仍然启用，执行编译
                if self.is_auto_compiling:
                    self.root.after(0, self._execute_auto_compile)

            except Exception as e:
                print(f"❌ 定时编译线程异常: {e}")
                self.root.after(0, self.stop_auto_compile_timer)
                break

    def _execute_auto_compile(self):
        """执行自动编译（在主线程中调用，但编译在后台线程执行）"""
        try:
            path = self.project_path.get().strip()
            if not path or not Path(path).exists():
                self.stop_auto_compile_timer()
                return

            print(f"🔄 执行定时自动编译: {datetime.now().strftime('%H:%M:%S')}")

            # 根据用户设置决定是否清空之前的错误列表
            if self.auto_clear_errors.get():
                self.clear_error_list_silent()

            self.auto_compile_status_label.config(text="🔨 正在编译...")

            # 在后台线程中执行编译，避免阻塞UI
            def auto_compile_thread():
                try:
                    result = self.execute_compile(path)
                    # 使用异步方式处理结果，避免阻塞UI
                    self.root.after(0, lambda r=result: self.handle_compile_result_async(r))
                except Exception as e:
                    error_msg = str(e)
                    self.root.after(0, lambda msg=error_msg: self._handle_auto_compile_error(msg))

            threading.Thread(target=auto_compile_thread, daemon=True).start()

        except Exception as e:
            print(f"❌ 自动编译启动失败: {e}")
            self.auto_compile_status_label.config(text="❌ 编译失败")

    def _handle_auto_compile_error(self, error_message: str):
        """处理自动编译错误（不显示弹窗）"""
        print(f"❌ 自动编译执行失败: {error_message}")
        self.auto_compile_status_label.config(text="❌ 编译失败")

    # API相关方法
    def load_errors_from_api(self):
        """从API获取编译错误"""
        if not self.api_client:
            messagebox.showwarning("警告", "API客户端未初始化")
            return

        def load_thread():
            try:
                self.root.after(0, lambda: self.status_label.config(text="🔄 正在从API获取错误..."))

                # 获取C#编译错误
                result = self.api_client.get_compilation_errors(
                    project_type="Backend",
                    errors_only=self.errors_only.get(),
                    page_size=1000
                )

                if result and 'items' in result:
                    errors = result['items']
                    self.root.after(0, lambda: self.display_api_errors(errors))
                    self.root.after(0, lambda: self.status_label.config(text=f"✅ 从API获取到 {len(errors)} 个错误"))
                else:
                    self.root.after(0, lambda: self.status_label.config(text="⚠️ API返回数据格式错误"))

            except Exception as e:
                error_msg = f"❌ 从API获取错误失败: {str(e)}"
                self.root.after(0, lambda: self.status_label.config(text=error_msg))
                print(error_msg)

        threading.Thread(target=load_thread, daemon=True).start()

    def display_api_errors(self, errors):
        """显示从API获取的错误"""
        try:
            # 清空现有错误列表
            if self.auto_clear_errors.get():
                self.clear_error_list_silent()

            # 添加API错误到列表
            for error in errors:
                self.add_api_error_to_list(error)

            # 更新错误统计
            total_errors = len([e for e in errors if e.get('severity') == 'Error'])
            total_warnings = len([e for e in errors if e.get('severity') == 'Warning'])

            self.error_count_label.config(text=f"错误: {total_errors}, 警告: {total_warnings}")

        except Exception as e:
            print(f"❌ 显示API错误失败: {e}")

    def add_api_error_to_list(self, error):
        """将API错误添加到列表"""
        try:
            # 格式化时间
            compile_time = error.get('compilationTime', '')
            if compile_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(compile_time.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    time_str = compile_time[:8] if len(compile_time) >= 8 else compile_time
            else:
                time_str = datetime.now().strftime('%H:%M:%S')

            # 获取文件名（不包含路径）
            file_path = error.get('filePath', '')
            file_name = Path(file_path).name if file_path else ''

            # 插入到树形控件
            item = self.error_tree.insert('', 'end', values=(
                time_str,
                error.get('severity', 'Error'),
                file_name,
                error.get('lineNumber', ''),
                error.get('code', ''),
                error.get('message', '')
            ))

            # 保存完整错误信息
            error_info = {
                'time': time_str,
                'severity': error.get('severity', 'Error'),
                'file': file_path,
                'line': error.get('lineNumber', 0),
                'column': error.get('columnNumber', 0),
                'code': error.get('code', ''),
                'message': error.get('message', ''),
                'project_name': error.get('projectName', ''),
                'project_path': error.get('projectPath', ''),
                'source': 'API'
            }

            self.error_history.append(error_info)

        except Exception as e:
            print(f"❌ 添加API错误到列表失败: {e}")

    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if not self.api_client:
            messagebox.showwarning("警告", "API客户端未初始化")
            self.auto_refresh.set(False)
            return

        if self.auto_refresh.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """开始自动刷新"""
        if not self.api_client:
            return

        self.auto_refresh_status_label.config(text="🔄 自动刷新中...", foreground="green")

        def refresh_loop():
            while self.auto_refresh.get():
                try:
                    # 从API获取错误
                    result = self.api_client.get_compilation_errors(
                        project_type="Backend",
                        errors_only=self.errors_only.get(),
                        page_size=1000
                    )

                    if result and 'items' in result:
                        errors = result['items']
                        self.root.after(0, lambda: self.display_api_errors(errors))

                    # 等待指定间隔
                    for i in range(self.auto_refresh_interval.get()):
                        if not self.auto_refresh.get():
                            break
                        time.sleep(1)

                except Exception as e:
                    print(f"❌ 自动刷新失败: {e}")
                    self.root.after(0, self.stop_auto_refresh)
                    break

        self.auto_refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        self.auto_refresh_thread.start()

    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh.set(False)
        if hasattr(self, 'auto_refresh_status_label'):
            self.auto_refresh_status_label.config(text="", foreground="black")
        if self.auto_refresh_thread:
            self.auto_refresh_thread = None

    def run(self):
        """运行界面"""
        if not self.is_embedded:
            try:
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
                self.root.mainloop()
            except KeyboardInterrupt:
                print("程序被用户中断")

    def on_closing(self):
        """关闭事件"""
        if self.auto_compile.get():
            self.auto_compile.set(False)
            self.stop_auto_compile()

        # 停止定时自动编译
        if self.is_auto_compiling:
            self.stop_auto_compile_timer()

        # 停止自动刷新
        if hasattr(self, 'auto_refresh') and self.auto_refresh.get():
            self.stop_auto_refresh()

        self.save_settings()
        self.root.destroy()

    def get_current_errors(self):
        """获取当前错误列表

        Returns:
            list: 当前错误信息列表，每个错误包含file, line, code, message, severity等字段
        """
        try:
            errors = []

            # 遍历TreeView中的所有项目
            for item in self.error_tree.get_children():
                values = self.error_tree.item(item, 'values')
                if len(values) >= 6:  # 确保有足够的列
                    # 列顺序: ('时间', '类型', '文件', '行号', '错误代码', '错误信息')
                    error_info = {
                        'time': values[0],
                        'severity': values[1],
                        'file': values[2],
                        'line': values[3],
                        'code': values[4],
                        'message': values[5]
                    }

                    # 只返回错误类型的项目（如果设置了只获取错误）
                    if not self.errors_only.get() or values[1] == 'Error':
                        errors.append(error_info)

            return errors

        except Exception as e:
            print(f"❌ 获取当前错误失败: {e}")
            return []

def main():
    """主函数"""
    try:
        app = CSharpErrorCaptureUI()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()