using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// Selenium脚本DTO
    /// </summary>
    public class SeleniumScriptDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public SeleniumConfigDto Config { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string Priority { get; set; } = "medium";
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
        public int CreatedBy { get; set; }
        public int? ProjectId { get; set; }
        public string? ProjectName { get; set; }
    }

    /// <summary>
    /// 创建Selenium脚本DTO
    /// </summary>
    public class CreateSeleniumScriptDto
    {
        [Required(ErrorMessage = "脚本名称不能为空")]
        [StringLength(100, ErrorMessage = "脚本名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "分类不能为空")]
        public string Category { get; set; } = string.Empty;

        public string Code { get; set; } = string.Empty;
        public SeleniumConfigDto Config { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string Priority { get; set; } = "medium";
        public int? ProjectId { get; set; }
    }

    /// <summary>
    /// 更新Selenium脚本DTO
    /// </summary>
    public class UpdateSeleniumScriptDto
    {
        [Required(ErrorMessage = "脚本名称不能为空")]
        [StringLength(100, ErrorMessage = "脚本名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "分类不能为空")]
        public string Category { get; set; } = string.Empty;

        public string Code { get; set; } = string.Empty;
        public SeleniumConfigDto Config { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string Priority { get; set; } = "medium";
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Selenium配置DTO
    /// </summary>
    public class SeleniumConfigDto
    {
        public string Browser { get; set; } = "chrome";
        public bool Headless { get; set; } = false;
        public int WindowWidth { get; set; } = 1920;
        public int WindowHeight { get; set; } = 1080;
        public string? UserAgent { get; set; }
        public int PageLoadTimeout { get; set; } = 30;
        public int ImplicitWait { get; set; } = 10;
        public int ScriptTimeout { get; set; } = 30;
        public bool EnableLogging { get; set; } = true;
        public List<string> ScreenshotOptions { get; set; } = new();
        public int RetryCount { get; set; } = 1;
        public string WaitStrategy { get; set; } = "smart";
        public string? ChromeOptions { get; set; }
        public string Environment { get; set; } = "local";
        public string? BaseUrl { get; set; }
        public string? Proxy { get; set; }
    }

    /// <summary>
    /// 执行Selenium脚本DTO
    /// </summary>
    public class ExecuteSeleniumScriptDto
    {
        public SeleniumConfigDto? Config { get; set; }
        public Dictionary<string, object>? Parameters { get; set; }
    }

    /// <summary>
    /// Selenium执行结果DTO
    /// </summary>
    public class SeleniumExecutionResultDto
    {
        public string Id { get; set; } = string.Empty;
        public int ScriptId { get; set; }
        public string ScriptName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int? Duration { get; set; }
        public List<SeleniumExecutionLogDto> Logs { get; set; } = new();
        public List<string> Screenshots { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public SeleniumExecutionStatsDto Stats { get; set; } = new();
    }

    /// <summary>
    /// Selenium执行日志DTO
    /// </summary>
    public class SeleniumExecutionLogDto
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Step { get; set; }
    }

    /// <summary>
    /// Selenium执行统计DTO
    /// </summary>
    public class SeleniumExecutionStatsDto
    {
        public int TotalSteps { get; set; }
        public int SuccessSteps { get; set; }
        public int FailedSteps { get; set; }
        public int SkippedSteps { get; set; }
    }

    /// <summary>
    /// 验证Selenium脚本DTO
    /// </summary>
    public class ValidateSeleniumScriptDto
    {
        [Required(ErrorMessage = "代码不能为空")]
        public string Code { get; set; } = string.Empty;
    }

    /// <summary>
    /// Selenium脚本验证结果DTO
    /// </summary>
    public class SeleniumValidationResultDto
    {
        public bool Valid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// 格式化Selenium脚本DTO
    /// </summary>
    public class FormatSeleniumScriptDto
    {
        [Required(ErrorMessage = "代码不能为空")]
        public string Code { get; set; } = string.Empty;
    }

    /// <summary>
    /// Selenium脚本格式化结果DTO
    /// </summary>
    public class SeleniumFormatResultDto
    {
        public string Code { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Selenium统计DTO
    /// </summary>
    public class SeleniumStatisticsDto
    {
        public int TotalScripts { get; set; }
        public int TotalExecutions { get; set; }
        public decimal SuccessRate { get; set; }
        public int AvgDuration { get; set; }
        public Dictionary<string, int> CategoryStats { get; set; } = new();
        public List<SeleniumDailyStatsDto> DailyStats { get; set; } = new();
    }

    /// <summary>
    /// Selenium每日统计DTO
    /// </summary>
    public class SeleniumDailyStatsDto
    {
        public DateTime Date { get; set; }
        public int Executions { get; set; }
        public decimal SuccessRate { get; set; }
    }

    /// <summary>
    /// 批量执行Selenium脚本DTO
    /// </summary>
    public class BatchExecuteSeleniumDto
    {
        [Required(ErrorMessage = "脚本ID列表不能为空")]
        public List<int> ScriptIds { get; set; } = new();
        public SeleniumConfigDto? Config { get; set; }
        public bool Parallel { get; set; } = false;
    }

    /// <summary>
    /// Selenium批量执行结果DTO
    /// </summary>
    public class SeleniumBatchExecutionResultDto
    {
        public List<string> ExecutionIds { get; set; } = new();
        public int TotalScripts { get; set; }
        public int StartedScripts { get; set; }
        public List<string> FailedScripts { get; set; } = new();
    }

    /// <summary>
    /// Selenium健康检查结果DTO
    /// </summary>
    public class SeleniumHealthCheckResultDto
    {
        public bool Selenium { get; set; }
        public Dictionary<string, bool> Browsers { get; set; } = new();
        public Dictionary<string, bool> Drivers { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 分页结果DTO
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Data { get; set; } = new();
        public int Total { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);
    }
}
