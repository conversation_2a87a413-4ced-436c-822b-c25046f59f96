﻿using System.Security.Claims;

namespace ProjectManagement.API.Helper
{
    /// <summary>
    /// 用户相关帮助类
    /// 提供用户身份验证和用户信息获取的通用方法
    /// </summary>
    public static class UserHelper
    {
        /// <summary>
        /// 从 ClaimsPrincipal 获取当前用户ID
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>用户ID，如果无法解析则返回 null</returns>
        public static int? GetCurrentUserId(ClaimsPrincipal? user)
        {
            if (user == null)
                return null;

            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        /// <summary>
        /// 从 ClaimsPrincipal 获取当前用户名
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>用户名，如果不存在则返回 null</returns>
        public static string? GetCurrentUserName(ClaimsPrincipal? user)
        {
            return user?.FindFirst(ClaimTypes.Name)?.Value;
        }

        /// <summary>
        /// 从 ClaimsPrincipal 获取当前用户邮箱
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>用户邮箱，如果不存在则返回 null</returns>
        public static string? GetCurrentUserEmail(ClaimsPrincipal? user)
        {
            return user?.FindFirst(ClaimTypes.Email)?.Value;
        }

        /// <summary>
        /// 从 ClaimsPrincipal 获取当前用户角色列表
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>用户角色列表</returns>
        public static List<string> GetCurrentUserRoles(ClaimsPrincipal? user)
        {
            return user?.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList() ?? new List<string>();
        }

        /// <summary>
        /// 检查当前用户是否具有指定角色
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <param name="role">要检查的角色名称</param>
        /// <returns>如果用户具有指定角色则返回 true，否则返回 false</returns>
        public static bool HasRole(ClaimsPrincipal? user, string role)
        {
            return user?.IsInRole(role) ?? false;
        }

        /// <summary>
        /// 检查当前用户是否具有指定的任一角色
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <param name="roles">要检查的角色名称列表</param>
        /// <returns>如果用户具有任一指定角色则返回 true，否则返回 false</returns>
        public static bool HasAnyRole(ClaimsPrincipal? user, params string[] roles)
        {
            return user != null && roles.Any(role => user.IsInRole(role));
        }

        /// <summary>
        /// 检查当前用户是否具有所有指定角色
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <param name="roles">要检查的角色名称列表</param>
        /// <returns>如果用户具有所有指定角色则返回 true，否则返回 false</returns>
        public static bool HasAllRoles(ClaimsPrincipal? user, params string[] roles)
        {
            return user != null && roles.All(role => user.IsInRole(role));
        }

        /// <summary>
        /// 从 ClaimsPrincipal 获取指定类型的声明值
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <param name="claimType">声明类型</param>
        /// <returns>声明值，如果不存在则返回 null</returns>
        public static string? GetClaimValue(ClaimsPrincipal? user, string claimType)
        {
            return user?.FindFirst(claimType)?.Value;
        }

        /// <summary>
        /// 从 ClaimsPrincipal 获取指定类型的所有声明值
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <param name="claimType">声明类型</param>
        /// <returns>声明值列表</returns>
        public static List<string> GetClaimValues(ClaimsPrincipal? user, string claimType)
        {
            return user?.FindAll(claimType).Select(c => c.Value).ToList() ?? new List<string>();
        }

        /// <summary>
        /// 检查用户是否已认证
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>如果用户已认证则返回 true，否则返回 false</returns>
        public static bool IsAuthenticated(ClaimsPrincipal? user)
        {
            return user?.Identity?.IsAuthenticated ?? false;
        }

        /// <summary>
        /// 获取用户的认证类型
        /// </summary>
        /// <param name="user">当前用户的 ClaimsPrincipal 对象</param>
        /// <returns>认证类型，如果不存在则返回 null</returns>
        public static string? GetAuthenticationType(ClaimsPrincipal? user)
        {
            return user?.Identity?.AuthenticationType;
        }
    }
}
