{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "SqlSugar": "Information"
    }
  },
  "AllowedHosts": "*",

    "ConnectionStrings": {
        //"DefaultConnection": "Server=.;Database=ProjectManagementAI;Persist Security Info=True;User ID=sa;Password=**********;TrustServerCertificate=true;MultipleActiveResultSets=false;",
        "DefaultConnection": "Server=************;Database=ProjectManagementAI;Persist Security Info=True;User ID=test_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=false;",
        "RedisConnection": "localhost:6379"
    },

  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyForJWTTokenGeneration2024!@#$%^&*()",
    "Issuer": "ProjectManagement.API",
    "Audience": "ProjectManagement.Client",
    "ExpirationInMinutes": 480,
    "RefreshTokenExpirationInDays": 30
  },

  "EncryptionSettings": {
    "Key": "ProjectManagementEncryptionKey2024!@#$%^&*()"
  },

  "AI": {
    "DefaultProvider": "Mock",
    "DefaultConfig": {
      "Provider": "Mock",
      "Model": "mock-gpt-4",
      "MaxTokens": 4000,
      "Temperature": 0.7
    },
    "TaskMapping": {
      "RequirementAnalysis": "Mock",
      "CodeGeneration": "Mock",
      "DocumentGeneration": "Mock",
      "Embeddings": "Mock"
    },
    "Providers": {
      "Azure": {
        "Endpoint": "https://your-resource.openai.azure.com/",
        "ApiKey": "your-azure-openai-key",
        "GPT4DeploymentName": "gpt-4",
        "GPT35DeploymentName": "gpt-35-turbo",
        "EmbeddingDeploymentName": "text-embedding-ada-002",
        "TimeoutSeconds": 300,
        "MaxRetries": 3,
        "Enabled": false
      },
      "OpenAI": {
        "Endpoint": "https://api.openai.com/v1/",
        "ApiKey": "your-openai-api-key",
        "TimeoutSeconds": 300,
        "MaxRetries": 3,
        "Enabled": false
      },
      "DeepSeek": {
        "Endpoint": "https://api.deepseek.com/v1/",
        "ApiKey": "your-deepseek-api-key",
        "TimeoutSeconds": 300,
        "MaxRetries": 3,
        "Enabled": false
      },
      "Claude": {
        "Endpoint": "https://api.anthropic.com/v1/",
        "ApiKey": "your-anthropic-api-key",
        "TimeoutSeconds": 300,
        "MaxRetries": 3,
        "Enabled": false
      },
      "Ollama": {
        "Endpoint": "http://localhost:11434/",
        "TimeoutSeconds": 300,
        "Enabled": false
      },
      "Mock": {
        "Enabled": true
      }
    }
  },

  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "SqlSugar": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/log-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      }
    ]
  },

  "CORS": {
    "AllowedOrigins": [
        "http://localhost:3000",
        "https://localhost:3000",
        "http://localhost:3001",
        "https://localhost:3001",
        "http://localhost:8080",
        "https://localhost:8080",
        "http://localhost:5173",
        "https://localhost:5173",
        "http://localhost:62573",
        "https://localhost:62573",
        "http://localhost:61136",
        "https://localhost:61136",
        "http://************:62573",
        "https://************:62573",
        "http://***********:62573",
        "https://***********:62573"
    ],
    "AllowedMethods": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
    "AllowedHeaders": [ "*" ],
    "AllowCredentials": true
  },

  "RateLimiting": {
    "EnableRateLimiting": true,
    "GeneralRules": {
      "PermitLimit": 100,
      "Window": "00:01:00",
      "QueueLimit": 10
    },
    "AIApiRules": {
      "PermitLimit": 10,
      "Window": "00:01:00",
      "QueueLimit": 5
    }
  },

  "FileStorage": {
    "Provider": "Local",
    "LocalPath": "wwwroot/uploads",
    "MaxFileSize": 10485760,
    "AllowedExtensions": [ ".pdf", ".doc", ".docx", ".txt", ".md", ".json" ]
  },

  "Email": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "Project Management System"
  },

  "Cache": {
    "DefaultExpirationMinutes": 30,
    "UserCacheExpirationMinutes": 60,
    "ProjectCacheExpirationMinutes": 120
  },

  "Features": {
    "EnableSwagger": true,
    "EnableHealthChecks": true,
    "EnableMetrics": true,
    "EnableAIServices": true,
    "EnableRealTimeUpdates": true
  }
}
