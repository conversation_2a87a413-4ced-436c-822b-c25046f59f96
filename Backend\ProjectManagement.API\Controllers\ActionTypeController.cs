using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.API.Helper;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// UI操作类型控制器
    /// </summary>
    [ApiController]
    [Route("api/ui-action-types")]
    [Authorize]
    public class UIActionTypeController : ControllerBase
    {
        private readonly IUIActionTypeRepository _uiActionTypeRepository;
        private readonly ILogger<UIActionTypeController> _logger;

        public UIActionTypeController(
            IUIActionTypeRepository uiActionTypeRepository,
            ILogger<UIActionTypeController> logger)
        {
            _uiActionTypeRepository = uiActionTypeRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取UI操作类型列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResultDto<UIActionTypeDto>>> GetUIActionTypes([FromQuery] UIActionTypeQueryDto query)
        {
            try
            {
                var result = await _uiActionTypeRepository.GetPagedAsync(query);

                var dtoResult = new PagedResultDto<UIActionTypeDto>
                {
                    Items = result.Items.Select(MapToDto).ToList(),
                    TotalCount = result.TotalCount,
                    PageNumber = result.PageNumber,
                    PageSize = result.PageSize
                };

                return Ok(dtoResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UI操作类型列表失败");
                return StatusCode(500, "获取UI操作类型列表失败");
            }
        }

        /// <summary>
        /// 获取启用的UI操作类型
        /// </summary>
        [HttpGet("active")]
        public async Task<ActionResult<List<UIActionTypeDto>>> GetActiveUIActionTypes()
        {
            try
            {
                var actionTypes = await _uiActionTypeRepository.GetActiveAsync();
                return Ok(actionTypes.Select(MapToDto).ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的UI操作类型失败");
                return StatusCode(500, "获取启用的UI操作类型失败");
            }
        }

        /// <summary>
        /// 获取UI操作类型详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<UIActionTypeDto>> GetUIActionType(int id)
        {
            try
            {
                var actionType = await _uiActionTypeRepository.GetByIdAsync(id);
                if (actionType == null || actionType.IsDeleted)
                {
                    return NotFound("UI操作类型不存在");
                }

                return Ok(MapToDto(actionType));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UI操作类型详情失败，ID: {Id}", id);
                return StatusCode(500, "获取UI操作类型详情失败");
            }
        }

        /// <summary>
        /// 创建UI操作类型
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<UIActionTypeDto>> CreateUIActionType([FromBody] CreateUIActionTypeDto dto)
        {
            try
            {
                // 检查UI操作类型值是否已存在
                if (await _uiActionTypeRepository.ExistsValueAsync(dto.Value))
                {
                    return BadRequest($"UI操作类型值 '{dto.Value}' 已存在");
                }

                var actionType = new UIActionType
                {
                    Value = dto.Value,
                    Label = dto.Label,
                    Description = dto.Description,
                    Icon = dto.Icon,
                    Color = dto.Color,
                    SortOrder = dto.SortOrder > 0 ? dto.SortOrder : await _uiActionTypeRepository.GetMaxSortOrderAsync() + 1,
                    IsActive = dto.IsActive,
                    IsBuiltIn = false, // 新创建的都是自定义类型
                    NeedsTemplate = dto.NeedsTemplate,
                    ParameterSchema = dto.ParameterSchema,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    CreatedBy = UserHelper.GetCurrentUserId(User),
                    IsDeleted = false
                };

                var result = await _uiActionTypeRepository.AddAsync(actionType);
                return Ok(MapToDto(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建UI操作类型失败");
                return StatusCode(500, "创建UI操作类型失败");
            }
        }

        /// <summary>
        /// 更新UI操作类型
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<UIActionTypeDto>> UpdateUIActionType(int id, [FromBody] UpdateUIActionTypeDto dto)
        {
            try
            {
                var actionType = await _uiActionTypeRepository.GetByIdAsync(id);
                if (actionType == null || actionType.IsDeleted)
                {
                    return NotFound("UI操作类型不存在");
                }

                // 内置类型只能修改部分字段
                if (actionType.IsBuiltIn)
                {
                    // 只允许修改描述、图标、颜色、排序和状态
                    if (!string.IsNullOrEmpty(dto.Description))
                        actionType.Description = dto.Description;
                    if (!string.IsNullOrEmpty(dto.Icon))
                        actionType.Icon = dto.Icon;
                    if (!string.IsNullOrEmpty(dto.Color))
                        actionType.Color = dto.Color;
                    if (dto.SortOrder.HasValue)
                        actionType.SortOrder = dto.SortOrder.Value;
                    if (dto.IsActive.HasValue)
                        actionType.IsActive = dto.IsActive.Value;
                }
                else
                {
                    // 自定义类型可以修改所有字段
                    if (!string.IsNullOrEmpty(dto.Label))
                        actionType.Label = dto.Label;
                    if (!string.IsNullOrEmpty(dto.Description))
                        actionType.Description = dto.Description;
                    if (!string.IsNullOrEmpty(dto.Icon))
                        actionType.Icon = dto.Icon;
                    if (!string.IsNullOrEmpty(dto.Color))
                        actionType.Color = dto.Color;
                    if (dto.SortOrder.HasValue)
                        actionType.SortOrder = dto.SortOrder.Value;
                    if (dto.IsActive.HasValue)
                        actionType.IsActive = dto.IsActive.Value;
                    if (dto.NeedsTemplate.HasValue)
                        actionType.NeedsTemplate = dto.NeedsTemplate.Value;
                    if (dto.ParameterSchema != null)
                        actionType.ParameterSchema = dto.ParameterSchema;
                }

                actionType.UpdatedTime = DateTime.Now;
                actionType.UpdatedBy = UserHelper.GetCurrentUserId(User);

                var success = await _uiActionTypeRepository.UpdateAsync(actionType);
                if (!success)
                {
                    return StatusCode(500, "更新UI操作类型失败");
                }

                // 重新获取更新后的实体
                var updatedEntity = await _uiActionTypeRepository.GetByIdAsync(id);
                return Ok(MapToDto(updatedEntity!));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新UI操作类型失败，ID: {Id}", id);
                return StatusCode(500, "更新UI操作类型失败");
            }
        }

        /// <summary>
        /// 删除UI操作类型
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteUIActionType(int id)
        {
            try
            {
                var actionType = await _uiActionTypeRepository.GetByIdAsync(id);
                if (actionType == null || actionType.IsDeleted)
                {
                    return NotFound("UI操作类型不存在");
                }

                // 内置类型不能删除
                if (actionType.IsBuiltIn)
                {
                    return BadRequest("内置UI操作类型不能删除");
                }

                // TODO: 检查是否有步骤在使用这个UI操作类型

                actionType.IsDeleted = true;
                actionType.DeletedTime = DateTime.Now;
                actionType.DeletedBy = UserHelper.GetCurrentUserId(ClaimsPrincipal.Current);

                await _uiActionTypeRepository.UpdateAsync(actionType);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除UI操作类型失败，ID: {Id}", id);
                return StatusCode(500, "删除UI操作类型失败");
            }
        }

        /// <summary>
        /// 启用/禁用UI操作类型
        /// </summary>
        [HttpPut("{id}/status")]
        public async Task<ActionResult> SetActiveStatus(int id, [FromBody] bool isActive)
        {
            try
            {
                var success = await _uiActionTypeRepository.SetActiveStatusAsync(id, isActive);
                if (!success)
                {
                    return NotFound("UI操作类型不存在");
                }

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置UI操作类型状态失败，ID: {Id}", id);
                return StatusCode(500, "设置UI操作类型状态失败");
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
  

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static UIActionTypeDto MapToDto(UIActionType actionType)
        {
            return new UIActionTypeDto
            {
                Id = actionType.Id,
                Value = actionType.Value,
                Label = actionType.Label,
                Description = actionType.Description,
                Icon = actionType.Icon,
                Color = actionType.Color,
                SortOrder = actionType.SortOrder,
                IsActive = actionType.IsActive,
                IsBuiltIn = actionType.IsBuiltIn,
                NeedsTemplate = actionType.NeedsTemplate,
                ParameterSchema = actionType.ParameterSchema,
                CreatedTime = actionType.CreatedTime,
                UpdatedTime = actionType.UpdatedTime
            };
        }

        #endregion
    }
}
