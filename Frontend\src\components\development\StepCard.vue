<template>
  <el-card 
    class="step-card" 
    :class="[
      `step-card--${step.status.toLowerCase()}`,
      `step-card--${step.priority.toLowerCase()}`,
      { 'step-card--selected': selected }
    ]"
    shadow="hover"
    @click="$emit('click', step)"
  >
    <!-- 卡片头部 -->
    <template #header>
      <div class="step-header">
        <div class="step-title">
          <el-tag 
            :type="getStepTypeTagType(step.stepType)" 
            size="small"
            class="step-type-tag"
          >
            {{ getStepTypeIcon(step.stepType) }}
          </el-tag>
          <span class="step-name" :title="step.stepName">
            {{ step.stepName }}
          </span>
        </div>
        
        <div class="step-actions">
          <el-dropdown @command="handleCommand" trigger="click" @click.stop>
            <el-button type="text" size="small" :icon="MoreFilled" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="view">查看详情</el-dropdown-item>
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="execute" v-if="step.status === 'Pending'">
                  执行
                </el-dropdown-item>
                <el-dropdown-item command="dependencies">依赖关系</el-dropdown-item>
                <el-dropdown-item command="history">执行历史</el-dropdown-item>
                <el-dropdown-item command="complexity">复杂度分析</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <div class="step-content">
      <!-- 步骤描述 -->
      <div class="step-description" v-if="step.stepDescription">
        <p class="description-text">{{ truncateText(step.stepDescription, 100) }}</p>
      </div>

      <!-- 步骤信息 -->
      <div class="step-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">优先级：</span>
            <el-tag 
              :type="getPriorityTagType(step.priority)" 
              size="small"
            >
              {{ getPriorityIcon(step.priority) }} {{ step.priority }}
            </el-tag>
          </div>
          
          <div class="info-item">
            <span class="label">状态：</span>
            <el-tag 
              :type="getStatusTagType(step.status)" 
              size="small"
            >
              {{ getStatusIcon(step.status) }} {{ getStatusText(step.status) }}
            </el-tag>
          </div>
        </div>

        <div class="info-row" v-if="step.technologyStack || step.componentType">
          <div class="info-item" v-if="step.technologyStack">
            <span class="label">技术栈：</span>
            <el-tag size="small" type="info">{{ step.technologyStack }}</el-tag>
          </div>
          
          <div class="info-item" v-if="step.componentType">
            <span class="label">组件：</span>
            <el-tag size="small" type="info">{{ step.componentType }}</el-tag>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item" v-if="step.estimatedHours">
            <span class="label">预估：</span>
            <span class="value">{{ step.estimatedHours }}h</span>
          </div>
          
          <div class="info-item" v-if="step.actualHours">
            <span class="label">实际：</span>
            <span class="value">{{ step.actualHours }}h</span>
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="step-progress">
        <div class="progress-header">
          <span class="progress-label">进度</span>
          <span class="progress-value">{{ step.progress }}%</span>
        </div>
        <el-progress 
          :percentage="step.progress" 
          :stroke-width="6"
          :show-text="false"
          :color="getProgressColor(step.progress)"
        />
      </div>

      <!-- 时间信息 -->
      <div class="step-time" v-if="step.startTime || step.endTime || step.completedTime">
        <div class="time-item" v-if="step.startTime">
          <el-icon><Clock /></el-icon>
          <span>开始：{{ formatTime(step.startTime) }}</span>
        </div>
        <div class="time-item" v-if="step.endTime">
          <el-icon><Clock /></el-icon>
          <span>结束：{{ formatTime(step.endTime) }}</span>
        </div>
        <div class="time-item" v-if="step.completedTime">
          <el-icon><Check /></el-icon>
          <span>完成：{{ formatTime(step.completedTime) }}</span>
        </div>
      </div>

      <!-- 依赖信息 -->
      <div class="step-dependencies" v-if="showDependencies && (dependencyCount > 0 || dependentCount > 0)">
        <div class="dependency-item" v-if="dependencyCount > 0">
          <el-icon><Link /></el-icon>
          <span>依赖 {{ dependencyCount }} 个步骤</span>
        </div>
        <div class="dependency-item" v-if="dependentCount > 0">
          <el-icon><Link /></el-icon>
          <span>被 {{ dependentCount }} 个步骤依赖</span>
        </div>
      </div>

      <!-- 子步骤信息 -->
      <div class="step-children" v-if="step.children && step.children.length > 0">
        <el-icon><FolderOpened /></el-icon>
        <span>包含 {{ step.children.length }} 个子步骤</span>
      </div>
    </div>

    <!-- 卡片底部 -->
    <template #footer v-if="showFooter">
      <div class="step-footer">
        <div class="footer-left">
          <span class="created-time">{{ formatTime(step.createdTime) }}</span>
        </div>
        <div class="footer-right">
          <el-button 
            size="small" 
            type="primary" 
            @click.stop="$emit('execute', step)"
            v-if="step.status === 'Pending'"
          >
            执行
          </el-button>
          <el-button 
            size="small" 
            @click.stop="$emit('view', step)"
          >
            查看
          </el-button>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  MoreFilled,
  Clock,
  Check,
  Link,
  FolderOpened
} from '@element-plus/icons-vue'
import type { ElTagType } from '@/types/element-plus'
import type { DevelopmentStep } from '@/types/development'
import { 
  STEP_TYPE_CONFIG, 
  PRIORITY_CONFIG, 
  STATUS_CONFIG 
} from '@/types/development'

// Props
interface Props {
  step: DevelopmentStep
  selected?: boolean
  showDependencies?: boolean
  showFooter?: boolean
  dependencyCount?: number
  dependentCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  showDependencies: false,
  showFooter: true,
  dependencyCount: 0,
  dependentCount: 0
})

// Emits
const emit = defineEmits<{
  'click': [step: DevelopmentStep]
  'view': [step: DevelopmentStep]
  'edit': [step: DevelopmentStep]
  'execute': [step: DevelopmentStep]
  'delete': [step: DevelopmentStep]
  'command': [command: string, step: DevelopmentStep]
}>()

// 方法
const handleCommand = (command: string) => {
  emit('command', command, props.step)
  
  // 同时触发具体的事件
  switch (command) {
    case 'view':
      emit('view', props.step)
      break
    case 'edit':
      emit('edit', props.step)
      break
    case 'execute':
      emit('execute', props.step)
      break
    case 'delete':
      emit('delete', props.step)
      break
  }
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

// 辅助方法
const getStepTypeIcon = (type: string) => 
  STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG]?.icon || '📝'

const getStepTypeTagType = (type: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    Development: 'primary',
    Testing: 'success',
    Documentation: 'info',
    Deployment: 'warning',
    Review: 'danger'
  }
  return typeMap[type] || 'info'
}

const getPriorityIcon = (priority: string) => 
  PRIORITY_CONFIG[priority as keyof typeof PRIORITY_CONFIG]?.icon || '➡️'

const getPriorityTagType = (priority: string): ElTagType => {
  const priorityMap: Record<string, ElTagType> = {
    Low: 'success',
    Medium: 'primary',
    High: 'warning',
    Critical: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getStatusIcon = (status: string) => 
  STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]?.icon || '❓'

const getStatusText = (status: string) => 
  STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]?.description || status

const getStatusTagType = (status: string): ElTagType => {
  const statusMap: Record<string, ElTagType> = {
    Pending: 'info',
    InProgress: 'primary',
    Completed: 'success',
    Failed: 'danger',
    Blocked: 'warning'
  }
  return statusMap[status] || 'info'
}
</script>

<style scoped>
.step-card {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid transparent;
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step-card--selected {
  border-left-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 状态相关样式 */
.step-card--pending {
  border-left-color: #909399;
}

.step-card--inprogress {
  border-left-color: #409eff;
}

.step-card--completed {
  border-left-color: #67c23a;
}

.step-card--failed {
  border-left-color: #f56c6c;
}

.step-card--blocked {
  border-left-color: #e6a23c;
}

/* 优先级相关样式 */
.step-card--critical {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

.step-card--high {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.2);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.step-type-tag {
  flex-shrink: 0;
}

.step-name {
  font-weight: 500;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.step-actions {
  flex-shrink: 0;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-description {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.description-text {
  margin: 0;
}

.step-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.info-item .label {
  color: #666;
  font-weight: 500;
}

.info-item .value {
  color: #333;
}

.step-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.progress-label {
  color: #666;
  font-weight: 500;
}

.progress-value {
  color: #333;
  font-weight: 500;
}

.step-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.step-dependencies {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.step-children {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.step-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  font-size: 12px;
  color: #999;
}

.footer-right {
  display: flex;
  gap: 8px;
}
</style>
