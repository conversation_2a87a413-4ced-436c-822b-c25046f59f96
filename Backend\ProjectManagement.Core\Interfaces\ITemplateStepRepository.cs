using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 模板步骤仓储接口
    /// </summary>
    public interface ITemplateStepRepository : IRepository<UIAutoMationTemplateStep>
    {
        /// <summary>
        /// 根据序列ID获取步骤列表
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <returns>步骤列表</returns>
        Task<List<UIAutoMationTemplateStep>> GetBySequenceIdAsync(int sequenceId);

        /// <summary>
        /// 根据序列ID获取启用的步骤列表
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <returns>步骤列表</returns>
        Task<List<UIAutoMationTemplateStep>> GetActiveBySequenceIdAsync(int sequenceId);

        /// <summary>
        /// 获取步骤详情（包含关联信息）
        /// </summary>
        /// <param name="id">步骤ID</param>
        /// <returns>步骤详情</returns>
        Task<UIAutoMationTemplateStep?> GetWithRelationsAsync(int id);

        /// <summary>
        /// 批量创建步骤
        /// </summary>
        /// <param name="steps">步骤列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchCreateAsync(List<UIAutoMationTemplateStep> steps);

        /// <summary>
        /// 批量更新步骤
        /// </summary>
        /// <param name="steps">步骤列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchUpdateAsync(List<UIAutoMationTemplateStep> steps);

        /// <summary>
        /// 删除序列的所有步骤
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteBySequenceIdAsync(int sequenceId);

        /// <summary>
        /// 重新排序步骤
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <param name="stepIds">步骤ID列表（按新顺序）</param>
        /// <returns>是否成功</returns>
        Task<bool> ReorderStepsAsync(int sequenceId, List<int> stepIds);

        /// <summary>
        /// 获取序列中的最大步骤顺序
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <returns>最大步骤顺序</returns>
        Task<int> GetMaxStepOrderAsync(int sequenceId);

        /// <summary>
        /// 启用/禁用步骤
        /// </summary>
        /// <param name="id">步骤ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>是否成功</returns>
        Task<bool> SetActiveStatusAsync(int id, bool isActive);

        /// <summary>
        /// 根据模板ID获取使用该模板的步骤
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>步骤列表</returns>
        Task<List<UIAutoMationTemplateStep>> GetByTemplateIdAsync(int templateId);

        /// <summary>
        /// 检查模板是否被步骤使用
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>是否被使用</returns>
        Task<bool> IsTemplateUsedAsync(int templateId);

        /// <summary>
        /// 复制序列的步骤到新序列
        /// </summary>
        /// <param name="sourceSequenceId">源序列ID</param>
        /// <param name="targetSequenceId">目标序列ID</param>
        /// <returns>是否成功</returns>
        Task<bool> CopyStepsAsync(int sourceSequenceId, int targetSequenceId);
    }
}
