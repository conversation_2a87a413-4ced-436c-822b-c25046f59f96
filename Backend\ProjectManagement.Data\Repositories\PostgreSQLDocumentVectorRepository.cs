using Npgsql;
using ProjectManagement.Core.DTOs.VectorSearch;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// PostgreSQL文档向量仓储实现
    /// </summary>
    public class PostgreSQLDocumentVectorRepository : IDocumentVectorRepository
    {
        private readonly string _connectionString;
        private readonly ILogger<PostgreSQLDocumentVectorRepository> _logger;

        public PostgreSQLDocumentVectorRepository(
            IConfiguration configuration,
            ILogger<PostgreSQLDocumentVectorRepository> logger)
        {
            _connectionString = configuration.GetConnectionString("VectorConnection") 
                ?? throw new ArgumentNullException("VectorConnection not found in configuration");
            _logger = logger;
        }

        public async Task<DocumentVector> CreateAsync(DocumentVector documentVector)
        {
            const string sql = @"
                INSERT INTO document_vectors (document_id, chunk_index, content, embedding, metadata)
                VALUES (@DocumentId, @ChunkIndex, @Content, @Embedding, @Metadata::jsonb)
                RETURNING id";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DocumentId", documentVector.DocumentId);
            command.Parameters.AddWithValue("@ChunkIndex", documentVector.ChunkIndex);
            command.Parameters.AddWithValue("@Content", documentVector.Content);
            command.Parameters.AddWithValue("@Embedding", documentVector.Embedding);
            command.Parameters.AddWithValue("@Metadata", JsonSerializer.Serialize(documentVector.Metadata));

            var id = await command.ExecuteScalarAsync();
            documentVector.Id = id != null ? Convert.ToInt32(id) : 0;

            _logger.LogInformation("创建文档向量成功: DocumentId={DocumentId}, ChunkIndex={ChunkIndex}", 
                documentVector.DocumentId, documentVector.ChunkIndex);

            return documentVector;
        }

        public async Task<DocumentVector?> GetByIdAsync(int id)
        {
            const string sql = @"
                SELECT id, document_id, chunk_index, content, embedding, metadata, created_at
                FROM document_vectors 
                WHERE id = @Id";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToDocumentVector(reader);
            }

            return null;
        }

        public async Task<List<DocumentVector>> GetByDocumentIdAsync(string documentId)
        {
            const string sql = @"
                SELECT id, document_id, chunk_index, content, embedding, metadata, created_at
                FROM document_vectors 
                WHERE document_id = @DocumentId
                ORDER BY chunk_index";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DocumentId", documentId);

            var results = new List<DocumentVector>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                results.Add(MapToDocumentVector(reader));
            }

            return results;
        }

        public async Task<List<DocumentVector>> FindSimilarAsync(
            float[] queryEmbedding,
            int topK,
            float similarityThreshold = 0.7f,
            Dictionary<string, object>? filters = null)
        {
            var whereClause = "";
            var parameters = new List<NpgsqlParameter>();

            // 构建过滤条件
            if (filters != null && filters.Any())
            {
                var conditions = new List<string>();
                foreach (var filter in filters)
                {
                    conditions.Add($"metadata ->> '{filter.Key}' = @{filter.Key}");
                    parameters.Add(new NpgsqlParameter($"@{filter.Key}", filter.Value.ToString()));
                }
                whereClause = "WHERE " + string.Join(" AND ", conditions);
            }

            var sql = $@"
                SELECT 
                    id, document_id, chunk_index, content, embedding, metadata, created_at,
                    embedding <-> @QueryEmbedding AS distance
                FROM document_vectors 
                {whereClause}
                ORDER BY embedding <-> @QueryEmbedding
                LIMIT @TopK";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@QueryEmbedding", queryEmbedding);
            command.Parameters.AddWithValue("@TopK", topK);
            parameters.ForEach(p => command.Parameters.Add(p));

            var results = new List<DocumentVector>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var vector = MapToDocumentVector(reader);
                var distance = reader.IsDBNull(7) ? 0f : reader.GetFloat(7); // distance
                
                // 将距离转换为相似度分数 (1 - normalized_distance)
                vector.SimilarityScore = Math.Max(0, 1.0f - (distance / 20.0f)); // 简单的归一化
                
                // 只返回超过相似度阈值的结果
                if (vector.SimilarityScore >= similarityThreshold)
                {
                    results.Add(vector);
                }
            }

            _logger.LogInformation("向量相似度搜索完成: 查询维度={Dimension}, TopK={TopK}, 结果数={ResultCount}", 
                queryEmbedding.Length, topK, results.Count);

            return results;
        }

        public async Task<DocumentVector> UpdateAsync(DocumentVector documentVector)
        {
            const string sql = @"
                UPDATE document_vectors 
                SET content = @Content, embedding = @Embedding, metadata = @Metadata::jsonb, updated_at = CURRENT_TIMESTAMP
                WHERE id = @Id";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@Id", documentVector.Id);
            command.Parameters.AddWithValue("@Content", documentVector.Content);
            command.Parameters.AddWithValue("@Embedding", documentVector.Embedding);
            command.Parameters.AddWithValue("@Metadata", JsonSerializer.Serialize(documentVector.Metadata));

            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("更新文档向量成功: Id={Id}", documentVector.Id);
            return documentVector;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            const string sql = "DELETE FROM document_vectors WHERE id = @Id";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@Id", id);

            var rowsAffected = await command.ExecuteNonQueryAsync();
            
            _logger.LogInformation("删除文档向量: Id={Id}, 影响行数={RowsAffected}", id, rowsAffected);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteByDocumentIdAsync(string documentId)
        {
            const string sql = "DELETE FROM document_vectors WHERE document_id = @DocumentId";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DocumentId", documentId);

            var rowsAffected = await command.ExecuteNonQueryAsync();
            
            _logger.LogInformation("删除文档向量: DocumentId={DocumentId}, 影响行数={RowsAffected}", 
                documentId, rowsAffected);
            return rowsAffected > 0;
        }

        public async Task<List<DocumentVector>> SearchByMetadataAsync(Dictionary<string, object> filters)
        {
            var conditions = new List<string>();
            var parameters = new List<NpgsqlParameter>();

            foreach (var filter in filters)
            {
                conditions.Add($"metadata ->> '{filter.Key}' = @{filter.Key}");
                parameters.Add(new NpgsqlParameter($"@{filter.Key}", filter.Value.ToString()));
            }

            var sql = $@"
                SELECT id, document_id, chunk_index, content, embedding, metadata, created_at
                FROM document_vectors 
                WHERE {string.Join(" AND ", conditions)}
                ORDER BY created_at DESC";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            parameters.ForEach(p => command.Parameters.Add(p));

            var results = new List<DocumentVector>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                results.Add(MapToDocumentVector(reader));
            }

            return results;
        }

        private DocumentVector MapToDocumentVector(NpgsqlDataReader reader)
        {
            var metadataJson = reader.IsDBNull(5) ? string.Empty : reader.GetString(5); // metadata
            var metadata = string.IsNullOrEmpty(metadataJson)
                ? new Dictionary<string, object>()
                : JsonSerializer.Deserialize<Dictionary<string, object>>(metadataJson) ?? new Dictionary<string, object>();

            return new DocumentVector
            {
                Id = reader.IsDBNull(0) ? 0 : reader.GetInt32(0), // id
                DocumentId = reader.IsDBNull(1) ? string.Empty : reader.GetString(1), // document_id
                ChunkIndex = reader.IsDBNull(2) ? 0 : reader.GetInt32(2), // chunk_index
                Content = reader.IsDBNull(3) ? string.Empty : reader.GetString(3), // content
                Embedding = reader.IsDBNull(4) ? Array.Empty<float>() : (float[])reader[4], // embedding
                Metadata = metadata,
                CreatedAt = reader.IsDBNull(6) ? DateTime.MinValue : reader.GetDateTime(6) // created_at
            };
        }
    }
}
