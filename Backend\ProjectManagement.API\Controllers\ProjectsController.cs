using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;
using System.Security.Claims;
using SqlSugar;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 项目管理控制器
/// 功能: 处理项目的CRUD操作、状态管理、工作流控制等
/// 支持: 项目创建、更新、删除、查询、状态跟踪、权限控制
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("GeneralApi")]
[Produces("application/json")]
public class ProjectsController : ControllerBase
{
    private readonly IProjectRepository _projectRepository;
    private readonly IUserRepository _userRepository;
    private readonly IRepository<ERDiagram> _erDiagramRepository;
    private readonly IRepository<ContextDiagram> _contextDiagramRepository;
    private readonly IRequirementDocumentRepository _requirementDocumentRepository;
    private readonly ILogger<ProjectsController> _logger;
    private readonly ISqlSugarClient _db;

    public ProjectsController(
        IProjectRepository projectRepository,
        IUserRepository userRepository,
        IRepository<ERDiagram> erDiagramRepository,
        IRepository<ContextDiagram> contextDiagramRepository,
        IRequirementDocumentRepository requirementDocumentRepository,
        ILogger<ProjectsController> logger,
        ISqlSugarClient db)
    {
        _projectRepository = projectRepository;
        _userRepository = userRepository;
        _erDiagramRepository = erDiagramRepository;
        _contextDiagramRepository = contextDiagramRepository;
        _requirementDocumentRepository = requirementDocumentRepository;
        _logger = logger;
        _db = db;
    }

    /// <summary>
    /// 获取当前用户的项目列表
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="status">项目状态过滤</param>
    /// <param name="search">搜索关键词</param>
    /// <returns>项目列表</returns>
    [HttpGet]
    public async Task<ActionResult<PagedResultDto<ProjectSummaryDto>>> GetProjects(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] string? search = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户 {UserId} 的项目列表", userId);

            var projects = await _projectRepository.GetUserProjectsAsync(userId, pageNumber, pageSize, status, search);

            var result = new PagedResultDto<ProjectSummaryDto>
            {
                Items = projects.Items.Select(p => new ProjectSummaryDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    ProjectCode = p.ProjectCode,
                    Description = p.Description,
                    Status = p.Status,
                    Priority = p.Priority,
                    Progress = p.Progress,
                    CreatedAt = p.CreatedTime,
                    UpdatedAt = p.UpdatedTime,
                    OwnerName = p.Owner?.RealName ?? "未知"
                }).ToList(),
                TotalCount = projects.TotalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目列表时发生错误");
            return StatusCode(500, new { message = "获取项目列表失败" });
        }
    }

    /// <summary>
    /// 根据ID获取项目详细信息
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>项目详细信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ProjectDetailDto>> GetProject(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取项目详情: {ProjectId}, 用户: {UserId}", id, userId);

            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 检查用户权限
            if (!await HasProjectAccess(userId, project))
            {
                return Forbid("无权访问此项目");
            }

            var result = new ProjectDetailDto
            {
                Id = project.Id,
                Name = project.Name,
                ProjectCode = project.ProjectCode,
                Description = project.Description,
                Status = project.Status,
                Priority = project.Priority,
                Progress = project.Progress,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                EstimatedHours = project.EstimatedHours,
                ActualHours = project.ActualHours,
                Budget = project.Budget,
                TechnologyStack = project.TechnologyStack,
                CreatedAt = project.CreatedTime,
                UpdatedAt = project.UpdatedTime,
                OwnerId = project.OwnerId,
                OwnerName = project.Owner?.RealName ?? "未知"
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目详情时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "获取项目详情失败" });
        }
    }

    /// <summary>
    /// 创建新项目
    /// </summary>
    /// <param name="request">创建项目请求</param>
    /// <returns>创建的项目信息</returns>
    [HttpPost]
    public async Task<ActionResult<ProjectDetailDto>> CreateProject([FromBody] CreateProjectRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 创建新项目: {ProjectName}", userId, request.Name);

            // 生成唯一的项目编号
            var projectCode = await _projectRepository.GenerateUniqueProjectCodeAsync();

            // 最后一次检查项目编号是否真的唯一（防止极端并发情况）
            var existingProject = await _projectRepository.GetByProjectCodeAsync(projectCode);
            if (existingProject != null)
            {
                _logger.LogWarning("项目编号冲突检测到，重新生成: {ProjectCode}", projectCode);
                // 如果还是冲突，使用带时间戳的备用编号
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmssffff");
                projectCode = $"PROJ-{DateTime.Now.Year}-999-{timestamp}";
            }

            var project = new Project
            {
                Name = request.Name,
                Description = request.Description,
                ProjectCode = projectCode,
                Status = "Planning", // 默认状态
                Priority = request.Priority ?? "Medium",
                Progress = 0,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                EstimatedHours = request.EstimatedHours,
                Budget = request.Budget,
                TechnologyStack = request.TechnologyStack,
                OwnerId = userId,
                CreatedTime = DateTime.UtcNow,
                CreatedBy = userId
            };

            // 保存项目，如果仍然冲突则使用备用方案
            Project createdProject;
            try
            {
                createdProject = await _projectRepository.AddAsync(project);
            }
            catch (Exception ex) when (ex.Message.Contains("UNIQUE KEY") && ex.Message.Contains("ProjectCode"))
            {
                // 如果仍然出现唯一键冲突，使用 GUID 作为最终备用方案
                _logger.LogError(ex, "项目编号唯一键冲突，使用 GUID 备用方案: {ProjectCode}", projectCode);
                project.ProjectCode = $"PROJ-{DateTime.Now.Year}-999-{Guid.NewGuid().ToString("N")[..12].ToUpper()}";
                createdProject = await _projectRepository.AddAsync(project);
            }

            _logger.LogInformation("项目创建成功: {ProjectId} - {ProjectCode} - {ProjectName}",
                createdProject.Id, createdProject.ProjectCode, createdProject.Name);

            var result = new ProjectDetailDto
            {
                Id = createdProject.Id,
                Name = createdProject.Name,
                ProjectCode = createdProject.ProjectCode,
                Description = createdProject.Description,
                Status = createdProject.Status,
                Priority = createdProject.Priority,
                Progress = createdProject.Progress,
                StartDate = createdProject.StartDate,
                EndDate = createdProject.EndDate,
                EstimatedHours = createdProject.EstimatedHours,
                Budget = createdProject.Budget,
                TechnologyStack = createdProject.TechnologyStack,
                CreatedAt = createdProject.CreatedTime,
                OwnerId = createdProject.OwnerId
            };

            return CreatedAtAction(nameof(GetProject), new { id = createdProject.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建项目时发生错误: {ProjectName}", request.Name);
            return StatusCode(500, new { message = "创建项目失败" });
        }
    }

    /// <summary>
    /// 更新项目信息
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <param name="request">更新项目请求</param>
    /// <returns>更新后的项目信息</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ProjectDetailDto>> UpdateProject(int id, [FromBody] UpdateProjectRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 更新项目: {ProjectId}", userId, id);

            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 检查用户权限
            if (!await HasProjectAccess(userId, project))
            {
                return StatusCode(403, new { message = "无权修改此项目" });
            }

            // 更新项目信息
            project.Name = request.Name ?? project.Name;
            project.Description = request.Description ?? project.Description;
            project.Priority = request.Priority ?? project.Priority;
            project.StartDate = request.StartDate ?? project.StartDate;
            project.EndDate = request.EndDate ?? project.EndDate;
            project.EstimatedHours = request.EstimatedHours ?? project.EstimatedHours;
            project.ActualHours = request.ActualHours ?? project.ActualHours;
            project.Budget = request.Budget ?? project.Budget;
            project.TechnologyStack = request.TechnologyStack ?? project.TechnologyStack;
            project.UpdatedTime = DateTime.UtcNow;
            project.UpdatedBy = userId;

            var updateResult = await _projectRepository.UpdateAsync(project);

            if (!updateResult)
            {
                return StatusCode(500, new { message = "更新项目失败" });
            }

            _logger.LogInformation("项目更新成功: {ProjectId}", id);

            var result = new ProjectDetailDto
            {
                Id = project.Id,
                Name = project.Name,
                ProjectCode = project.ProjectCode,
                Description = project.Description,
                Status = project.Status,
                Priority = project.Priority,
                Progress = project.Progress,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                EstimatedHours = project.EstimatedHours,
                ActualHours = project.ActualHours,
                Budget = project.Budget,
                TechnologyStack = project.TechnologyStack,
                UpdatedAt = project.UpdatedTime,
                OwnerId = project.OwnerId
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "更新项目失败" });
        }
    }

    /// <summary>
    /// 删除项目
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteProject(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 删除项目: {ProjectId}", userId, id);

            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 检查用户权限（只有项目所有者可以删除）
            if (project.OwnerId != userId)
            {
                return StatusCode(403, new { message = "只有项目所有者可以删除项目" });
            }

            // 先软删除项目下的所有开发步骤
            await _projectRepository.SoftDeleteProjectDevelopmentStepsAsync(id, userId);

            // 软删除项目本身
            await _projectRepository.SoftDeleteAsync(id, userId);

            _logger.LogInformation("项目删除成功: {ProjectId}", id);

            return Ok(new { message = "项目删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除项目时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "删除项目失败" });
        }
    }

    /// <summary>
    /// 更新项目状态
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <param name="request">状态更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPatch("{id}/status")]
    public async Task<ActionResult> UpdateProjectStatus(int id, [FromBody] UpdateProjectStatusRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 更新项目状态: {ProjectId} -> {Status}", userId, id, request.Status);

            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 检查用户权限
            if (!await HasProjectAccess(userId, project))
            {
                return StatusCode(403, new { message = "无权修改此项目状态" });
            }

            // 更新状态
            project.Status = request.Status;
            project.Progress = request.Progress ?? project.Progress;
            project.UpdatedTime = DateTime.UtcNow;
            project.UpdatedBy = userId;

            await _projectRepository.UpdateAsync(project);

            _logger.LogInformation("项目状态更新成功: {ProjectId} -> {Status}", id, request.Status);

            return Ok(new { message = "项目状态更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目状态时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "更新项目状态失败" });
        }
    }

    /// <summary>
    /// 获取项目统计信息
    /// </summary>
    /// <returns>项目统计信息</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<ProjectStatisticsDto>> GetProjectStatistics()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户 {UserId} 的项目统计信息", userId);

            var statistics = await _projectRepository.GetUserProjectStatisticsAsync(userId);

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目统计信息时发生错误");
            return StatusCode(500, new { message = "获取统计信息失败" });
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }

    /// <summary>
    /// 检查用户是否有项目访问权限
    /// </summary>
    private async Task<bool> HasProjectAccess(int userId, Project project)
    {
        // 项目所有者有完全访问权限
        if (project.OwnerId == userId)
        {
            return true;
        }

        // 管理员有所有项目的访问权限
        var user = await _userRepository.GetByIdAsync(userId);
        if (user?.Role == "Admin")
        {
            return true;
        }

        // 其他权限检查可以在这里添加（如团队成员权限等）
        return false;
    }



    /// <summary>
    /// 获取项目的ER图列表
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>ER图列表</returns>
    [HttpGet("{id}/er-diagrams")]
    public async Task<ActionResult<List<ERDiagramDto>>> GetProjectERDiagrams(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取项目 {ProjectId} 的ER图列表, 用户 {UserId}", id, userId);

            // 验证项目是否存在和权限
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            if (!await HasProjectAccess(userId, project))
            {
                return Forbid("无权访问此项目");
            }

            // 获取ER图列表
            var erDiagrams = await _erDiagramRepository.GetListAsync(d => d.ProjectId == id);

            var result = erDiagrams.Select(d => new ERDiagramDto
            {
                Id = d.Id,
                ProjectId = d.ProjectId,
                RequirementDocumentId = d.RequirementDocumentId,
                DiagramName = d.DiagramName,
                MermaidDefinition = d.MermaidDefinition,
                Description = d.Description,
                Version = d.DiagramVersion ?? "1.0", // 使用DiagramVersion，如果为null则默认为"1.0"
                CreatedAt = d.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = d.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目ER图列表时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "获取ER图列表失败" });
        }
    }

    /// <summary>
    /// 获取项目的上下文图列表
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>上下文图列表</returns>
    [HttpGet("{id}/context-diagrams")]
    public async Task<ActionResult<List<ContextDiagramDto>>> GetProjectContextDiagrams(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取项目 {ProjectId} 的上下文图列表, 用户 {UserId}", id, userId);

            // 验证项目是否存在和权限
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            if (!await HasProjectAccess(userId, project))
            {
                return Forbid("无权访问此项目");
            }

            // 获取上下文图列表
            var contextDiagrams = await _contextDiagramRepository.GetListAsync(d => d.ProjectId == id);

            var result = contextDiagrams.Select(d => new ContextDiagramDto
            {
                Id = d.Id,
                ProjectId = d.ProjectId,
                RequirementDocumentId = d.RequirementDocumentId,
                DiagramName = d.DiagramName,
                MermaidDefinition = d.MermaidDefinition,
                ExternalEntities = d.ExternalEntities,
                SystemBoundary = d.SystemBoundary,
                DataFlows = d.DataFlows,
                Version = d.DiagramVersion,
                CreatedAt = d.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = d.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目上下文图列表时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "获取上下文图列表失败" });
        }
    }

    #endregion

    #region 项目资源下载

    /// <summary>
    /// 下载项目需求文档（浏览器下载）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>需求文档文件</returns>
    [HttpGet("{id}/download-requirements")]
    public async Task<ActionResult> DownloadProjectRequirements(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("下载项目需求文档: {ProjectId}, 用户: {UserId}", id, userId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 获取项目的所有需求文档
            var requirements = await _requirementDocumentRepository.GetRequirementDocumentsByProjectAsync(id);

            if (!requirements.Any())
            {
                return NotFound(new { message = "项目中没有需求文档" });
            }

            // 3. 合并所有需求文档内容
            var content = string.Join("\n\n---\n\n", requirements.Select(req =>
                $"# {req.Title}\n\n{req.Content}"));

            var fileName = $"{project.Name}_需求文档.md";
            var fileBytes = System.Text.Encoding.UTF8.GetBytes(content);

            return File(fileBytes, "text/markdown", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载项目需求文档时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "下载项目需求文档失败" });
        }
    }

    /// <summary>
    /// 下载项目ER图（浏览器下载）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>ER图文件</returns>
    [HttpGet("{id}/download-er-diagrams")]
    public async Task<ActionResult> DownloadProjectERDiagrams(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("下载项目ER图: {ProjectId}, 用户: {UserId}", id, userId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 获取项目的所有ER图
            var erDiagrams = await _db.Queryable<ERDiagram>()
                .Where(x => x.ProjectId == id && !x.IsDeleted)
                .ToListAsync();

            if (!erDiagrams.Any())
            {
                return NotFound(new { message = "项目中没有ER图" });
            }

            // 3. 合并所有ER图内容
            var content = string.Join("\n\n---\n\n", erDiagrams.Select(er =>
                $"# {er.DiagramName}\n\n{er.MermaidDefinition}"));

            var fileName = $"{project.Name}_ER图.mermaid";
            var fileBytes = System.Text.Encoding.UTF8.GetBytes(content);

            return File(fileBytes, "text/plain", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载项目ER图时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "下载项目ER图失败" });
        }
    }

    /// <summary>
    /// 下载项目上下文图（浏览器下载）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>上下文图文件</returns>
    [HttpGet("{id}/download-context-diagrams")]
    public async Task<ActionResult> DownloadProjectContextDiagrams(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("下载项目上下文图: {ProjectId}, 用户: {UserId}", id, userId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 获取项目的所有上下文图
            var contextDiagrams = await _db.Queryable<ContextDiagram>()
                .Where(x => x.ProjectId == id && !x.IsDeleted)
                .ToListAsync();

            if (!contextDiagrams.Any())
            {
                return NotFound(new { message = "项目中没有上下文图" });
            }

            // 3. 合并所有上下文图内容
            var content = string.Join("\n\n---\n\n", contextDiagrams.Select(ctx =>
                $"# {ctx.DiagramName}\n\n{ctx.MermaidDefinition}"));

            var fileName = $"{project.Name}_上下文图.mermaid";
            var fileBytes = System.Text.Encoding.UTF8.GetBytes(content);

            return File(fileBytes, "text/plain", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载项目上下文图时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "下载项目上下文图失败" });
        }
    }

    /// <summary>
    /// 下载项目原型图（浏览器下载）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>原型图文件</returns>
    [HttpGet("{id}/download-prototypes")]
    public async Task<ActionResult> DownloadProjectPrototypes(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("下载项目原型图: {ProjectId}, 用户: {UserId}", id, userId);

            // 1. 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 2. 获取项目的所有原型图
            var prototypes = await _db.Queryable<Prototype>()
                .Where(x => x.ProjectId == id && !x.IsDeleted)
                .ToListAsync();

            if (!prototypes.Any())
            {
                return NotFound(new { message = "项目中没有原型图" });
            }

            // 3. 合并所有原型图内容
            var content = string.Join("\n\n---\n\n", prototypes.Select(proto =>
                $"# {proto.PrototypeName}\n\n{proto.MermaidDefinition}"));

            var fileName = $"{project.Name}_原型图.mermaid";
            var fileBytes = System.Text.Encoding.UTF8.GetBytes(content);

            return File(fileBytes, "text/plain", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载项目原型图时发生错误: {ProjectId}", id);
            return StatusCode(500, new { message = "下载项目原型图失败" });
        }
    }


    #endregion
}
