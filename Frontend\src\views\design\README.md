# 设计生成模块 - ER图和上下文图

## 📋 功能概述

设计生成模块提供了完整的ER图和上下文图设计功能，支持AI自动生成和手动编辑，基于Mermaid图表引擎实现。

## 🎯 主要功能

### 1. **设计列表页面** (`DesignListView.vue`)
- **项目选择**: 支持选择不同项目查看对应的设计
- **设计类型切换**: ER图设计 vs 上下文图设计
- **设计管理**: 创建、查看、编辑、删除、导出、复制设计
- **AI生成**: 一键AI生成ER图和上下文图
- **搜索过滤**: 支持按名称和描述搜索设计

### 2. **ER图设计页面** (`ERDiagramView.vue`)
- **Mermaid渲染**: 实时渲染ER图
- **可视化编辑**: 左侧预览，右侧编辑
- **AI生成**: 基于需求描述生成ER图
- **模板插入**: 提供常用ER图模板
- **语法验证**: Mermaid语法实时验证
- **导出功能**: 支持SVG、PNG、PDF格式导出
- **全屏预览**: 支持全屏查看图表

### 3. **上下文图设计页面** (`ContextDiagramView.vue`)
- **系统边界**: 定义系统边界和外部实体
- **数据流**: 描述系统间的数据流向
- **AI生成**: 智能生成系统上下文图
- **模板支持**: 提供标准上下文图模板
- **交互式编辑**: 实时预览和编辑

## 🔧 技术实现

### 核心技术栈
- **Vue 3**: 组合式API + TypeScript
- **Element Plus**: UI组件库
- **Mermaid.js**: 图表渲染引擎
- **SCSS**: 样式预处理器

### 关键特性
- **响应式设计**: 支持桌面端和移动端
- **实时渲染**: Mermaid定义变化时自动重新渲染
- **防抖优化**: 编辑时防抖渲染，提升性能
- **错误处理**: 完善的错误提示和恢复机制
- **状态管理**: 编辑模式和查看模式切换

## 📊 页面结构

### ER图设计页面结构
```
ERDiagramView.vue
├── 页面头部
│   ├── 返回按钮
│   ├── 标题和描述
│   └── 操作按钮（编辑/保存/更多操作）
├── 主要内容
│   ├── 左侧：图表预览区域
│   │   ├── Mermaid图表渲染
│   │   ├── 全屏和刷新功能
│   │   └── 加载/错误/空状态处理
│   └── 右侧：编辑面板（编辑模式）
│       ├── 基本信息表单
│       ├── Mermaid编辑器
│       ├── AI生成工具
│       └── 模板和验证工具
└── AI生成对话框
```

### 上下文图设计页面结构
```
ContextDiagramView.vue
├── 页面头部（同ER图）
├── 主要内容
│   ├── 左侧：图表预览区域
│   └── 右侧：编辑面板
│       ├── 基本信息
│       ├── 外部实体描述
│       ├── 系统边界描述
│       ├── 数据流描述
│       └── Mermaid编辑器
└── AI生成对话框
```

## 🎨 样式特点

### 设计原则
- **一致性**: 与整体系统设计风格保持一致
- **可用性**: 清晰的视觉层次和操作反馈
- **响应式**: 适配不同屏幕尺寸
- **可访问性**: 支持键盘导航和屏幕阅读器

### 关键样式
- **卡片布局**: 使用Element Plus卡片组件
- **网格系统**: 响应式网格布局
- **色彩系统**: 
  - ER图主色：蓝色 (#409eff)
  - 上下文图主色：绿色 (#67c23a)
- **交互效果**: 悬停、点击、加载状态

## 🔌 API集成

### 设计服务 (`DesignService`)
```typescript
// ER图相关API
- getERDiagrams(projectId): 获取项目ER图列表
- getERDiagram(id): 获取ER图详情
- createERDiagram(data): 创建ER图
- updateERDiagram(id, data): 更新ER图
- deleteERDiagram(id): 删除ER图
- generateERDiagram(data): AI生成ER图

// 上下文图相关API
- getContextDiagrams(projectId): 获取项目上下文图列表
- getContextDiagram(id): 获取上下文图详情
- createContextDiagram(data): 创建上下文图
- updateContextDiagram(id, data): 更新上下文图
- deleteContextDiagram(id): 删除上下文图
- generateContextDiagram(data): AI生成上下文图

// 通用API
- exportDiagram(type, id, format): 导出图表
- getTaskStatus(taskId): 获取AI任务状态
```

## 🚀 使用方法

### 1. 访问设计页面
```
http://localhost:3000/design
```

### 2. 创建ER图
1. 选择项目
2. 点击"AI生成ER图"或"手动创建"
3. 填写图表信息
4. 编辑Mermaid定义
5. 保存图表

### 3. 创建上下文图
1. 选择项目
2. 切换到"上下文图"标签
3. 点击"AI生成上下文图"或"手动创建"
4. 填写系统信息和Mermaid定义
5. 保存图表

## 📝 Mermaid语法示例

### ER图示例
```mermaid
erDiagram
    USER {
        int id PK
        string username
        string email
        string password_hash
        datetime created_at
    }
    
    PROJECT {
        int id PK
        string name
        string description
        int owner_id FK
        datetime created_at
    }
    
    USER ||--o{ PROJECT : owns
```

### 上下文图示例
```mermaid
flowchart TD
    User[用户] --> System[系统核心]
    Admin[管理员] --> System
    System --> Database[(数据库)]
    System --> EmailService[邮件服务]
    
    subgraph "系统边界"
        System
        Database
    end
```

## 🔄 状态管理

### 页面状态
- `loading`: 加载状态
- `saving`: 保存状态
- `generating`: AI生成状态
- `error`: 错误状态
- `isEditMode`: 编辑模式
- `isCreateMode`: 创建模式

### 数据状态
- `currentDiagram`: 当前图表数据
- `mermaidDefinition`: Mermaid定义
- `diagramForm`: 表单数据

## 🎯 后续优化

### 功能增强
- [ ] 图表版本管理
- [ ] 协作编辑功能
- [ ] 更多导出格式
- [ ] 图表模板库
- [ ] 拖拽式编辑器

### 性能优化
- [ ] 图表渲染缓存
- [ ] 大图表虚拟滚动
- [ ] 懒加载优化
- [ ] 内存管理优化

### 用户体验
- [ ] 快捷键支持
- [ ] 撤销/重做功能
- [ ] 实时协作
- [ ] 移动端优化
