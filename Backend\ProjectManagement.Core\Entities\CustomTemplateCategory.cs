using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 自定义模板分类实体
    /// </summary>
    [SugarTable("CustomTemplateCategories", "自定义模板分类表")]
    public class CustomTemplateCategory : BaseEntity
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [SugarColumn(ColumnDescription = "分类名称", Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [SugarColumn(ColumnDescription = "分类描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 父分类ID（支持层级分类）
        /// </summary>
        [SugarColumn(ColumnDescription = "父分类ID", IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 分类图标
        /// </summary>
        [SugarColumn(ColumnDescription = "分类图标", Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "分类图标长度不能超过50个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 分类颜色
        /// </summary>
        [SugarColumn(ColumnDescription = "分类颜色", Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "分类颜色长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "排序顺序", IsNullable = false)]
        [Range(0, int.MaxValue, ErrorMessage = "排序顺序必须大于等于0")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否为系统分类
        /// </summary>
        [SugarColumn(ColumnDescription = "是否为系统分类", IsNullable = false)]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用", IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 模板数量（统计字段）
        /// </summary>
        [SugarColumn(ColumnDescription = "模板数量", IsNullable = false)]
        [Range(0, int.MaxValue, ErrorMessage = "模板数量必须大于等于0")]
        public int TemplateCount { get; set; } = 0;

        /// <summary>
        /// 父分类（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CustomTemplateCategory? Parent { get; set; }

        /// <summary>
        /// 子分类列表（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CustomTemplateCategory> Children { get; set; } = new List<CustomTemplateCategory>();

        /// <summary>
        /// 该分类下的模板列表（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CustomUIAutoMationTemplate> Templates { get; set; } = new List<CustomUIAutoMationTemplate>();

        /// <summary>
        /// 该分类下的模板序列列表（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<UIAutoMationTemplateSequence> TemplateSequences { get; set; } = new List<UIAutoMationTemplateSequence>();
    }
}
