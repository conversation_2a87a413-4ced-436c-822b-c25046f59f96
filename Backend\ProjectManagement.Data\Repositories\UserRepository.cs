using SqlSugar;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Enums;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : BaseRepository<User>, IUserRepository
{
    public UserRepository(ISqlSugarClient db, ILogger<UserRepository> logger) : base(db, logger)
    {
    }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        try
        {
            return await _db.Queryable<User>()
                .Where(x => x.Username == username && !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户名获取用户失败，用户名: {Username}", username);
            throw;
        }
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        try
        {
            return await _db.Queryable<User>()
                .Where(x => x.Email == email && !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据邮箱获取用户失败，邮箱: {Email}", email);
            throw;
        }
    }

    public async Task<bool> IsUsernameExistsAsync(string username, int? excludeUserId = null)
    {
        try
        {
            var query = _db.Queryable<User>()
                .Where(x => x.Username == username && !x.IsDeleted);

            if (excludeUserId.HasValue)
            {
                query = query.Where(x => x.Id != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户名是否存在失败，用户名: {Username}", username);
            throw;
        }
    }

    public async Task<bool> IsEmailExistsAsync(string email, int? excludeUserId = null)
    {
        try
        {
            var query = _db.Queryable<User>()
                .Where(x => x.Email == email && !x.IsDeleted);

            if (excludeUserId.HasValue)
            {
                query = query.Where(x => x.Id != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查邮箱是否存在失败，邮箱: {Email}", email);
            throw;
        }
    }

    public async Task<List<User>> GetUsersByRoleAsync(UserRole role)
    {
        try
        {
            var roleString = role.ToString();
            return await _db.Queryable<User>()
                .Where(x => x.Role == roleString && !x.IsDeleted)
                .OrderBy(x => x.Username)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据角色获取用户列表失败，角色: {Role}", role);
            throw;
        }
    }

    public async Task<List<User>> GetUsersByStatusAsync(UserStatus status)
    {
        try
        {
            var statusInt = (int)status;
            return await _db.Queryable<User>()
                .Where(x => x.Status == statusInt && !x.IsDeleted)
                .OrderBy(x => x.Username)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取用户列表失败，状态: {Status}", status);
            throw;
        }
    }

    public async Task<bool> UpdateLastLoginAsync(int userId, DateTime loginTime, string loginIp)
    {
        try
        {
            var result = await _db.Updateable<User>()
                .SetColumns(x => new User
                {
                    LastLoginTime = loginTime,
                    LastLoginIp = loginIp,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == userId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新用户最后登录信息成功，用户ID: {UserId}", userId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户最后登录信息失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> UpdatePasswordAsync(int userId, string passwordHash, string salt)
    {
        try
        {
            var result = await _db.Updateable<User>()
                .SetColumns(x => new User
                {
                    PasswordHash = passwordHash,
                    Salt = salt,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == userId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新用户密码成功，用户ID: {UserId}", userId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户密码失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> VerifyEmailAsync(int userId)
    {
        try
        {
            var result = await _db.Updateable<User>()
                .SetColumns(x => new User
                {
                    EmailVerified = true,
                    EmailVerifiedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == userId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("验证用户邮箱成功，用户ID: {UserId}", userId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证用户邮箱失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> UpdateTwoFactorAsync(int userId, bool enabled, string? secret = null)
    {
        try
        {
            var result = await _db.Updateable<User>()
                .SetColumns(x => new User
                {
                    TwoFactorEnabled = enabled,
                    TwoFactorSecret = secret,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == userId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新用户双因子认证成功，用户ID: {UserId}, 启用: {Enabled}", userId, enabled);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户双因子认证失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> UpdatePreferencesAsync(int userId, string preferences)
    {
        try
        {
            var result = await _db.Updateable<User>()
                .SetColumns(x => new User
                {
                    Preferences = preferences,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == userId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新用户偏好设置成功，用户ID: {UserId}", userId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户偏好设置失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<PagedResult<User>> SearchUsersAsync(string keyword, int pageIndex, int pageSize)
    {
        try
        {
            var query = _db.Queryable<User>()
                .Where(x => !x.IsDeleted);

            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x =>
                    x.Username.Contains(keyword) ||
                    x.Email.Contains(keyword) ||
                    (x.RealName != null && x.RealName.Contains(keyword)));
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(x => x.CreatedTime)
                .ToPageListAsync(pageIndex, pageSize);

            return new PagedResult<User>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索用户失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<int> GetActiveUserCountAsync(int days = 30)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-days);
            return await _db.Queryable<User>()
                .Where(x => !x.IsDeleted &&
                           x.Status == 1 &&
                           x.LastLoginTime >= cutoffDate)
                .CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃用户统计失败，天数: {Days}", days);
            throw;
        }
    }

    public async Task<int> GetRegistrationCountAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            return await _db.Queryable<User>()
                .Where(x => !x.IsDeleted &&
                           x.CreatedTime >= startDate &&
                           x.CreatedTime <= endDate)
                .CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户注册统计失败，开始日期: {StartDate}, 结束日期: {EndDate}", startDate, endDate);
            throw;
        }
    }

    public async Task<User?> GetByRefreshTokenAsync(string refreshToken)
    {
        try
        {
            return await _db.Queryable<User>()
                .Where(x => x.RefreshToken == refreshToken && !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据刷新令牌获取用户失败");
            throw;
        }
    }
}
