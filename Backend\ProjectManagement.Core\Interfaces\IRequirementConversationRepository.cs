using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Models;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 需求对话仓储接口
/// </summary>
public interface IRequirementConversationRepository
{
    /// <summary>
    /// 根据会话ID获取对话历史（按时间排序，用于构建AI上下文）
    /// </summary>
    /// <param name="conversationId">会话ID</param>
    /// <param name="limit">限制数量</param>
    /// <returns>对话历史列表</returns>
    Task<List<RequirementConversation>> GetConversationHistoryAsync(string conversationId, int limit = 10);

    /// <summary>
    /// 根据项目和用户获取最近的对话历史
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="limit">限制数量</param>
    /// <returns>最近对话列表</returns>
    Task<List<RequirementConversation>> GetRecentConversationsAsync(int projectId, int userId, int limit = 5);

    /// <summary>
    /// 创建对话记录
    /// </summary>
    /// <param name="conversation">对话记录</param>
    /// <returns>创建的对话记录</returns>
    Task<RequirementConversation> CreateConversationAsync(RequirementConversation conversation);

    /// <summary>
    /// 更新对话记录
    /// </summary>
    /// <param name="conversation">对话记录</param>
    /// <returns>更新的对话记录</returns>
    Task<RequirementConversation> UpdateConversationAsync(RequirementConversation conversation);

    /// <summary>
    /// 获取对话的token统计信息
    /// </summary>
    /// <param name="conversationId">会话ID</param>
    /// <returns>token统计信息</returns>
    Task<ConversationTokenStats> GetConversationTokenStatsAsync(string conversationId);

    /// <summary>
    /// 根据项目获取需求对话列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>需求对话列表</returns>
    Task<List<RequirementConversation>> GetConversationsByProjectAsync(int projectId);

    /// <summary>
    /// 根据用户获取需求对话列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>需求对话列表</returns>
    Task<List<RequirementConversation>> GetConversationsByUserAsync(int userId);

    /// <summary>
    /// 获取最新的需求对话
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>最新的需求对话</returns>
    Task<RequirementConversation?> GetLatestConversationAsync(int projectId, int userId);

    /// <summary>
    /// 搜索需求对话
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="projectId">项目ID（可选）</param>
    /// <param name="userId">用户ID（可选）</param>
    /// <param name="messageType">消息类型（可选）</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    Task<ProjectManagement.Core.Interfaces.PagedResult<RequirementConversation>> SearchConversationsAsync(
        string keyword,
        int? projectId = null,
        int? userId = null,
        string? messageType = null,
        int pageIndex = 1,
        int pageSize = 20);

    /// <summary>
    /// 获取需求对话统计信息
    /// </summary>
    /// <param name="projectId">项目ID（可选）</param>
    /// <returns>需求对话统计</returns>
    Task<RequirementConversationStatistics> GetConversationStatisticsAsync(int? projectId = null);
}
