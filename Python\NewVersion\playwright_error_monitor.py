#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright错误监控模块
用于获取浏览器F12控制台错误和运行时错误
"""

import asyncio
import json
import time
from typing import List, Dict, Optional, Callable
from pathlib import Path
import threading
from datetime import datetime

try:
    from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    print("⚠️ Playwright未安装，请运行: pip install playwright")
    PLAYWRIGHT_AVAILABLE = False


class PlaywrightErrorMonitor:
    """Playwright错误监控器"""
    
    def __init__(self, headless: bool = True):
        """
        初始化Playwright错误监控器
        
        Args:
            headless: 是否无头模式运行
        """
        self.headless = headless
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
        # 错误存储
        self.console_errors: List[Dict] = []
        self.page_errors: List[Dict] = []
        self.network_errors: List[Dict] = []
        self.runtime_errors: List[Dict] = []
        
        # 回调函数
        self.error_callback: Optional[Callable] = None
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
    async def start_browser(self, browser_type: str = "chromium") -> bool:
        """
        启动浏览器
        
        Args:
            browser_type: 浏览器类型 (chromium, firefox, webkit)
            
        Returns:
            bool: 是否启动成功
        """
        if not PLAYWRIGHT_AVAILABLE:
            print("❌ Playwright不可用")
            return False
            
        try:
            self.playwright = await async_playwright().start()
            
            # 选择浏览器
            if browser_type == "firefox":
                browser_launcher = self.playwright.firefox
            elif browser_type == "webkit":
                browser_launcher = self.playwright.webkit
            else:
                browser_launcher = self.playwright.chromium
            
            # 启动浏览器
            self.browser = await browser_launcher.launch(
                headless=self.headless,
                args=[
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--enable-logging',
                    '--log-level=0'
                ]
            )
            
            # 创建上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                ignore_https_errors=True
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置错误监听器
            await self._setup_error_listeners()
            
            print("✅ Playwright浏览器启动成功")
            return True
            
        except Exception as e:
            print(f"❌ 启动浏览器失败: {e}")
            return False
    
    async def _setup_error_listeners(self):
        """设置错误监听器"""
        if not self.page:
            return
            
        # 监听控制台消息
        self.page.on('console', self._handle_console_message)
        
        # 监听页面错误
        self.page.on('pageerror', self._handle_page_error)
        
        # 监听请求失败
        self.page.on('requestfailed', self._handle_request_failed)
        
        # 监听响应错误
        self.page.on('response', self._handle_response)
        
        # 启用CDP会话以获取运行时错误
        try:
            cdp = await self.context.new_cdp_session(self.page)
            await cdp.send('Runtime.enable')
            await cdp.send('Log.enable')
            
            # 监听运行时异常
            cdp.on('Runtime.exceptionThrown', self._handle_runtime_exception)
            cdp.on('Runtime.consoleAPICalled', self._handle_console_api)
            
        except Exception as e:
            print(f"⚠️ CDP会话设置失败: {e}")
    
    def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'type': 'console',
                'level': msg.type,
                'text': msg.text,
                'location': {
                    'url': msg.location.get('url', '') if msg.location else '',
                    'line': msg.location.get('lineNumber', 0) if msg.location else 0,
                    'column': msg.location.get('columnNumber', 0) if msg.location else 0
                },
                'args': [str(arg) for arg in msg.args]
            }
            
            self.console_errors.append(error_info)
            self._notify_error(error_info)
            print(f"🔍 Console {msg.type}: {msg.text}")
    
    def _handle_page_error(self, error):
        """处理页面错误"""
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'type': 'page_error',
            'name': error.name,
            'message': error.message,
            'stack': error.stack
        }
        
        self.page_errors.append(error_info)
        self._notify_error(error_info)
        print(f"🔍 Page Error: {error.name} - {error.message}")
    
    def _handle_request_failed(self, request):
        """处理请求失败"""
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'type': 'network_error',
            'url': request.url,
            'method': request.method,
            'failure': request.failure,
            'resource_type': request.resource_type
        }
        
        self.network_errors.append(error_info)
        self._notify_error(error_info)
        print(f"🔍 Request Failed: {request.method} {request.url}")
    
    def _handle_response(self, response):
        """处理HTTP响应错误"""
        if response.status >= 400:
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'type': 'http_error',
                'status': response.status,
                'status_text': response.status_text,
                'url': response.url,
                'method': response.request.method
            }
            
            self.network_errors.append(error_info)
            self._notify_error(error_info)
            print(f"🔍 HTTP Error: {response.status} - {response.url}")
    
    def _handle_runtime_exception(self, event):
        """处理运行时异常"""
        exception = event['exceptionDetails']
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'type': 'runtime_exception',
            'text': exception.get('text', ''),
            'line': exception.get('lineNumber', 0),
            'column': exception.get('columnNumber', 0),
            'url': exception.get('url', ''),
            'stack_trace': exception.get('stackTrace', {})
        }
        
        self.runtime_errors.append(error_info)
        self._notify_error(error_info)
        print(f"🔍 Runtime Exception: {exception.get('text', '')}")
    
    def _handle_console_api(self, event):
        """处理控制台API调用"""
        if event['type'] == 'error':
            args = []
            for arg in event.get('args', []):
                args.append(arg.get('value', arg.get('description', 'N/A')))
            
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'type': 'console_api',
                'level': event['type'],
                'args': args,
                'stack_trace': event.get('stackTrace', {})
            }
            
            self.runtime_errors.append(error_info)
            self._notify_error(error_info)
            print(f"🔍 Console API Error: {' '.join(map(str, args))}")
    
    def _notify_error(self, error_info: Dict):
        """通知错误回调"""
        if self.error_callback:
            try:
                self.error_callback(error_info)
            except Exception as e:
                print(f"⚠️ 错误回调执行失败: {e}")
    
    async def navigate_to(self, url: str) -> bool:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            
        Returns:
            bool: 是否导航成功
        """
        if not self.page:
            print("❌ 页面未初始化")
            return False
            
        try:
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            print(f"✅ 成功导航到: {url}")
            return True
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    async def execute_javascript(self, js_code: str) -> Optional[any]:
        """
        执行JavaScript代码
        
        Args:
            js_code: JavaScript代码
            
        Returns:
            执行结果
        """
        if not self.page:
            return None
            
        try:
            result = await self.page.evaluate(js_code)
            return result
        except Exception as e:
            print(f"❌ JavaScript执行失败: {e}")
            return None
    
    def get_all_errors(self) -> Dict[str, List[Dict]]:
        """
        获取所有错误信息
        
        Returns:
            Dict: 包含所有类型错误的字典
        """
        return {
            'console_errors': self.console_errors.copy(),
            'page_errors': self.page_errors.copy(),
            'network_errors': self.network_errors.copy(),
            'runtime_errors': self.runtime_errors.copy()
        }
    
    def clear_errors(self):
        """清空所有错误记录"""
        self.console_errors.clear()
        self.page_errors.clear()
        self.network_errors.clear()
        self.runtime_errors.clear()
        print("✅ 错误记录已清空")
    
    def set_error_callback(self, callback: Callable[[Dict], None]):
        """
        设置错误回调函数
        
        Args:
            callback: 错误回调函数
        """
        self.error_callback = callback
        print("✅ 错误回调函数已设置")
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            print("✅ Playwright浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {e}")


class PlaywrightErrorMonitorSync:
    """Playwright错误监控器的同步包装器"""
    
    def __init__(self, headless: bool = True):
        self.monitor = PlaywrightErrorMonitor(headless)
        self.loop = None
        self.thread = None
        self.is_running = False
    
    def start(self, browser_type: str = "chromium") -> bool:
        """启动监控器"""
        if self.is_running:
            print("⚠️ 监控器已在运行")
            return True
            
        def run_async_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            async def async_start():
                success = await self.monitor.start_browser(browser_type)
                if success:
                    self.is_running = True
                    # 保持事件循环运行
                    while self.is_running:
                        await asyncio.sleep(0.1)
                return success
            
            return self.loop.run_until_complete(async_start())
        
        self.thread = threading.Thread(target=run_async_loop, daemon=True)
        self.thread.start()
        
        # 等待启动完成
        time.sleep(2)
        return self.is_running
    
    def navigate_to(self, url: str) -> bool:
        """导航到URL"""
        if not self.loop or not self.is_running:
            return False
            
        future = asyncio.run_coroutine_threadsafe(
            self.monitor.navigate_to(url), self.loop
        )
        return future.result(timeout=30)
    
    def execute_javascript(self, js_code: str):
        """执行JavaScript"""
        if not self.loop or not self.is_running:
            return None
            
        future = asyncio.run_coroutine_threadsafe(
            self.monitor.execute_javascript(js_code), self.loop
        )
        return future.result(timeout=10)
    
    def get_all_errors(self) -> Dict[str, List[Dict]]:
        """获取所有错误"""
        return self.monitor.get_all_errors()
    
    def clear_errors(self):
        """清空错误"""
        self.monitor.clear_errors()
    
    def set_error_callback(self, callback: Callable[[Dict], None]):
        """设置错误回调"""
        self.monitor.set_error_callback(callback)
    
    def stop(self):
        """停止监控器"""
        if self.is_running:
            self.is_running = False
            if self.loop:
                asyncio.run_coroutine_threadsafe(
                    self.monitor.close(), self.loop
                )
            if self.thread:
                self.thread.join(timeout=5)
            print("✅ Playwright监控器已停止")


# 使用示例
if __name__ == "__main__":
    def error_handler(error_info):
        print(f"🚨 捕获到错误: {error_info['type']} - {error_info}")
    
    # 创建监控器
    monitor = PlaywrightErrorMonitorSync(headless=False)
    monitor.set_error_callback(error_handler)
    
    # 启动监控
    if monitor.start():
        print("✅ 监控器启动成功")
        
        # 导航到测试页面
        monitor.navigate_to("https://example.com")
        
        # 执行可能产生错误的JavaScript
        monitor.execute_javascript("console.error('测试错误消息')")
        monitor.execute_javascript("throw new Error('测试异常')")
        
        # 等待一段时间收集错误
        time.sleep(5)
        
        # 获取错误信息
        errors = monitor.get_all_errors()
        print(f"📊 收集到的错误: {json.dumps(errors, indent=2, ensure_ascii=False)}")
        
        # 停止监控
        monitor.stop()
    else:
        print("❌ 监控器启动失败")
