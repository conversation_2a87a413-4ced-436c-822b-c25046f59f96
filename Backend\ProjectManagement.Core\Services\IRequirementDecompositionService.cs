using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Services;

/// <summary>
/// 需求分解服务接口
/// 功能: 将需求文档分解成具体的开发步骤
/// </summary>
public interface IRequirementDecompositionService
{
    #region 需求分解

    /// <summary>
    /// 分解需求文档为开发步骤
    /// </summary>
    /// <param name="requirementDocumentId">需求文档ID</param>
    /// <param name="decompositionOptions">分解选项</param>
    /// <param name="userId">用户ID</param>
    /// <returns>分解结果</returns>
    Task<RequirementDecompositionResult> DecomposeRequirementAsync(
        int requirementDocumentId, 
        DecompositionOptions decompositionOptions, 
        int userId);

    /// <summary>
    /// 基于需求内容直接分解（不需要保存的需求文档）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="requirementContent">需求内容</param>
    /// <param name="decompositionOptions">分解选项</param>
    /// <param name="userId">用户ID</param>
    /// <returns>分解结果</returns>
    Task<RequirementDecompositionResult> DecomposeRequirementContentAsync(
        int projectId,
        string requirementContent,
        DecompositionOptions decompositionOptions,
        int userId);

    /// <summary>
    /// 重新分解需求（更新现有步骤）
    /// </summary>
    /// <param name="requirementDocumentId">需求文档ID</param>
    /// <param name="decompositionOptions">分解选项</param>
    /// <param name="userId">用户ID</param>
    /// <param name="preserveExistingSteps">是否保留现有步骤</param>
    /// <returns>分解结果</returns>
    Task<RequirementDecompositionResult> RedecomposeRequirementAsync(
        int requirementDocumentId,
        DecompositionOptions decompositionOptions,
        int userId,
        bool preserveExistingSteps = true);

    #endregion

    #region 步骤管理

    /// <summary>
    /// 添加自定义开发步骤
    /// </summary>
    /// <param name="step">开发步骤</param>
    /// <param name="userId">用户ID</param>
    /// <returns>添加的步骤</returns>
    Task<DevelopmentStep> AddCustomStepAsync(DevelopmentStep step, int userId);

    /// <summary>
    /// 更新开发步骤
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="updateData">更新数据</param>
    /// <param name="userId">用户ID</param>
    /// <returns>更新后的步骤</returns>
    Task<DevelopmentStep> UpdateStepAsync(int stepId, StepUpdateData updateData, int userId);

    /// <summary>
    /// 删除开发步骤
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="userId">用户ID</param>
    /// <param name="deleteChildSteps">是否删除子步骤</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteStepAsync(int stepId, int userId, bool deleteChildSteps = false);

    /// <summary>
    /// 移动步骤到新的父步骤下
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="newParentStepId">新父步骤ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否移动成功</returns>
    Task<bool> MoveStepAsync(int stepId, int? newParentStepId, int userId);

    /// <summary>
    /// 重新排序步骤
    /// </summary>
    /// <param name="stepOrders">步骤排序信息</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否排序成功</returns>
    Task<bool> ReorderStepsAsync(List<StepOrderInfo> stepOrders, int userId);

    #endregion

    #region 依赖管理

    /// <summary>
    /// 添加步骤依赖关系
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="dependsOnStepId">依赖的步骤ID</param>
    /// <param name="dependencyType">依赖类型</param>
    /// <param name="isRequired">是否必需</param>
    /// <param name="userId">用户ID</param>
    /// <returns>依赖关系</returns>
    Task<StepDependency> AddStepDependencyAsync(
        int stepId, 
        int dependsOnStepId, 
        string dependencyType = "Sequential", 
        bool isRequired = true, 
        int userId = 0);

    /// <summary>
    /// 移除步骤依赖关系
    /// </summary>
    /// <param name="dependencyId">依赖关系ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否移除成功</returns>
    Task<bool> RemoveStepDependencyAsync(int dependencyId, int userId);

    /// <summary>
    /// 自动分析并建立步骤依赖关系
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>建立的依赖关系数量</returns>
    Task<int> AutoAnalyzeDependenciesAsync(int projectId, int userId);

    /// <summary>
    /// 验证依赖关系（检查循环依赖）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>验证结果</returns>
    Task<DependencyValidationResult> ValidateDependenciesAsync(int projectId);

    #endregion

    #region 执行管理

    /// <summary>
    /// 开始执行步骤
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="executorType">执行器类型</param>
    /// <param name="userId">用户ID</param>
    /// <returns>执行历史记录</returns>
    Task<StepExecutionHistory> StartStepExecutionAsync(int stepId, string executorType, int userId);

    /// <summary>
    /// 完成步骤执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="result">执行结果</param>
    /// <param name="generatedCode">生成的代码</param>
    /// <param name="outputFiles">输出文件</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否完成成功</returns>
    Task<bool> CompleteStepExecutionAsync(
        string executionId, 
        string result, 
        string? generatedCode = null, 
        string? outputFiles = null, 
        int userId = 0);

    /// <summary>
    /// 标记步骤执行失败
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否标记成功</returns>
    Task<bool> FailStepExecutionAsync(string executionId, string errorMessage, int userId);

    /// <summary>
    /// 获取下一个可执行的步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="stepType">步骤类型筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <returns>可执行步骤列表</returns>
    Task<List<DevelopmentStep>> GetNextExecutableStepsAsync(
        int projectId, 
        string? stepType = null, 
        string? priority = null);

    #endregion

    #region 统计分析

    /// <summary>
    /// 获取项目分解统计信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    Task<ProjectDecompositionStatistics> GetProjectDecompositionStatisticsAsync(int projectId);

    /// <summary>
    /// 获取步骤执行进度
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>执行进度信息</returns>
    Task<StepExecutionProgress> GetStepExecutionProgressAsync(int projectId);

    /// <summary>
    /// 分析步骤复杂度和工时估算
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>分析结果</returns>
    Task<StepComplexityAnalysis> AnalyzeStepComplexityAsync(int stepId);

    /// <summary>
    /// 确认并保存分解的步骤到数据库
    /// </summary>
    Task<RequirementDecompositionResult> ConfirmAndSaveStepsAsync(
        int projectId,
        int? requirementDocumentId,
        List<DevelopmentStepData> steps,
        List<StepDependencyData> dependencies,
        int userId);

    #endregion
}

#region 数据传输对象

/// <summary>
/// 分解选项
/// </summary>
public class DecompositionOptions
{
    /// <summary>
    /// 技术栈
    /// </summary>
    public string TechnologyStack { get; set; } = string.Empty;

    /// <summary>
    /// 分解粒度: Coarse(粗粒度), Medium(中等), Fine(细粒度)
    /// </summary>
    public string Granularity { get; set; } = "Medium";

    /// <summary>
    /// 是否包含测试步骤
    /// </summary>
    public bool IncludeTestSteps { get; set; } = true;

    /// <summary>
    /// 是否包含文档步骤
    /// </summary>
    public bool IncludeDocumentationSteps { get; set; } = true;

    /// <summary>
    /// 是否自动分析依赖关系
    /// </summary>
    public bool AutoAnalyzeDependencies { get; set; } = true;

    /// <summary>
    /// 最大步骤数量限制
    /// </summary>
    public int MaxStepCount { get; set; } = 100;

    /// <summary>
    /// AI提供商（可以是配置ID或提供商名称）
    /// </summary>
    public string? AIProvider { get; set; }

    /// <summary>
    /// AI模型
    /// </summary>
    public string? AIModel { get; set; }
}

/// <summary>
/// 需求分解结果
/// </summary>
public class RequirementDecompositionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 生成的开发步骤
    /// </summary>
    public List<DevelopmentStep> Steps { get; set; } = new();

    /// <summary>
    /// 生成的依赖关系
    /// </summary>
    public List<StepDependency> Dependencies { get; set; } = new();

    /// <summary>
    /// 分解统计信息
    /// </summary>
    public DecompositionStatistics Statistics { get; set; } = new();

    /// <summary>
    /// AI分析结果
    /// </summary>
    public string? AIAnalysisResult { get; set; }

    /// <summary>
    /// 是否为预览模式（未保存到数据库）
    /// </summary>
    public bool IsPreview { get; set; } = false;
}

/// <summary>
/// 分解统计信息
/// </summary>
public class DecompositionStatistics
{
    public int TotalSteps { get; set; }
    public int DevelopmentSteps { get; set; }
    public int TestSteps { get; set; }
    public int DocumentationSteps { get; set; }
    public int DependencyCount { get; set; }
    public decimal EstimatedTotalHours { get; set; }
    public int MaxDepth { get; set; }
    public List<string> TechnologyStacks { get; set; } = new();
}

/// <summary>
/// 步骤更新数据
/// </summary>
public class StepUpdateData
{
    public string? StepName { get; set; }
    public string? StepDescription { get; set; }
    public string? Priority { get; set; }
    public string? Status { get; set; }
    public decimal? EstimatedHours { get; set; }
    public decimal? ActualHours { get; set; }
    public string? TechnologyStack { get; set; }
    public string? AIPrompt { get; set; }
    public int? Progress { get; set; }
}

/// <summary>
/// 步骤排序信息
/// </summary>
public class StepOrderInfo
{
    public int StepId { get; set; }
    public int Order { get; set; }
    public int? ParentStepId { get; set; }
}

/// <summary>
/// 依赖验证结果
/// </summary>
public class DependencyValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<CircularDependency> CircularDependencies { get; set; } = new();
}

/// <summary>
/// 循环依赖信息
/// </summary>
public class CircularDependency
{
    public List<int> StepIds { get; set; } = new();
    public List<string> StepNames { get; set; } = new();
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 项目分解统计信息
/// </summary>
public class ProjectDecompositionStatistics
{
    public int ProjectId { get; set; }
    public string ProjectName { get; set; } = string.Empty;
    public int RequirementCount { get; set; }
    public int TotalSteps { get; set; }
    public int CompletedSteps { get; set; }
    public double CompletionRate { get; set; }
    public decimal TotalEstimatedHours { get; set; }
    public decimal TotalActualHours { get; set; }
    public int AverageStepsPerRequirement { get; set; }
    public List<string> TechnologyStacks { get; set; } = new();
}

/// <summary>
/// 步骤执行进度
/// </summary>
public class StepExecutionProgress
{
    public int ProjectId { get; set; }
    public int TotalSteps { get; set; }
    public int PendingSteps { get; set; }
    public int InProgressSteps { get; set; }
    public int CompletedSteps { get; set; }
    public int FailedSteps { get; set; }
    public int BlockedSteps { get; set; }
    public double OverallProgress { get; set; }
    public List<StepTypeProgress> ProgressByType { get; set; } = new();
}

/// <summary>
/// 按类型的步骤进度
/// </summary>
public class StepTypeProgress
{
    public string StepType { get; set; } = string.Empty;
    public int Total { get; set; }
    public int Completed { get; set; }
    public double Progress { get; set; }
}

/// <summary>
/// 步骤复杂度分析
/// </summary>
public class StepComplexityAnalysis
{
    public int StepId { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int ComplexityScore { get; set; } // 1-10
    public decimal EstimatedHours { get; set; }
    public List<string> ComplexityFactors { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public string RiskLevel { get; set; } = "Low"; // Low, Medium, High
}

/// <summary>
/// 开发步骤数据模型
/// </summary>
public class DevelopmentStepData
{
    public string StepName { get; set; } = string.Empty;
    public string StepDescription { get; set; } = string.Empty;
    public string StepType { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public decimal? EstimatedHours { get; set; }
    public string? TechnologyStack { get; set; }
    public string? FileType { get; set; }
    public string? FilePath { get; set; }
    public string? ComponentType { get; set; }
    public string? AIPrompt { get; set; }
    public int StepOrder { get; set; }
    public string? StepGroup { get; set; }
    public int StepLevel { get; set; }
    public string? ParentStepName { get; set; }
}

/// <summary>
/// 步骤依赖关系数据模型
/// </summary>
public class StepDependencyData
{
    public string StepName { get; set; } = string.Empty;
    public string DependsOnStepName { get; set; } = string.Empty;
    public string DependencyType { get; set; } = "Sequential";
}

#endregion
