-- =============================================
-- 清理编码任务步骤表多余字段
-- 文件: 012_CleanupCodingTaskStepsTable.sql
-- 目的: 删除CodingTaskSteps表中不需要的字段
-- 作者: System
-- 创建时间: 2025-06-29
-- =============================================

PRINT '========================================';
PRINT '开始清理编码任务步骤表多余字段...';
PRINT '========================================';

-- 删除IsRequired字段的默认约束（如果存在）
IF EXISTS (SELECT * FROM sys.default_constraints dc
           INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
           WHERE dc.parent_object_id = OBJECT_ID('CodingTaskSteps') AND c.name = 'IsRequired')
BEGIN
    DECLARE @sql1 NVARCHAR(MAX)
    SELECT @sql1 = 'ALTER TABLE CodingTaskSteps DROP CONSTRAINT ' + dc.name
    FROM sys.default_constraints dc
    INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
    WHERE dc.parent_object_id = OBJECT_ID('CodingTaskSteps') AND c.name = 'IsRequired'

    EXEC sp_executesql @sql1
    PRINT '✓ 删除IsRequired字段的默认约束';
END

-- 删除IsRequired字段（如果存在）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IsRequired')
BEGIN
    ALTER TABLE CodingTaskSteps DROP COLUMN IsRequired;
    PRINT '✓ 删除字段IsRequired';
END

-- 删除EstimatedHours字段的默认约束（如果存在）
IF EXISTS (SELECT * FROM sys.default_constraints dc
           INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
           WHERE dc.parent_object_id = OBJECT_ID('CodingTaskSteps') AND c.name = 'EstimatedHours')
BEGIN
    DECLARE @sql2 NVARCHAR(MAX)
    SELECT @sql2 = 'ALTER TABLE CodingTaskSteps DROP CONSTRAINT ' + dc.name
    FROM sys.default_constraints dc
    INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
    WHERE dc.parent_object_id = OBJECT_ID('CodingTaskSteps') AND c.name = 'EstimatedHours'

    EXEC sp_executesql @sql2
    PRINT '✓ 删除EstimatedHours字段的默认约束';
END

-- 删除EstimatedHours字段（如果存在）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'EstimatedHours')
BEGIN
    ALTER TABLE CodingTaskSteps DROP COLUMN EstimatedHours;
    PRINT '✓ 删除字段EstimatedHours';
END

PRINT '========================================';
PRINT '✅ 编码任务步骤表清理完成！';
PRINT '========================================';
