using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 编码任务实体
    /// </summary>
    [SugarTable("CodingTasks")]
    public class CodingTask : BaseEntity
    {

        /// <summary>
        /// 项目ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ProjectId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 200)]
        [Required(ErrorMessage = "任务名称不能为空")]
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 1000)]
        [StringLength(1000, ErrorMessage = "任务描述长度不能超过1000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Status { get; set; } = "NotStarted"; // NotStarted, InProgress, Completed, Blocked, Cancelled

        /// <summary>
        /// 优先级
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 20)]
        public string Priority { get; set; } = "Medium"; // High, Medium, Low

        /// <summary>
        /// 分配给的用户ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AssignedTo { get; set; }

        /// <summary>
        /// 预估工时（小时）
        /// </summary>
        [SugarColumn(IsNullable = true, DecimalDigits = 2)]
        public decimal? EstimatedHours { get; set; }

        /// <summary>
        /// 实际工时（小时）
        /// </summary>
        [SugarColumn(IsNullable = true, DecimalDigits = 2)]
        public decimal? ActualHours { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 截止日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// 完成日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CompletedDate { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际结束时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ActualEndTime { get; set; }

        /// <summary>
        /// 技术栈
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 500)]
        [StringLength(500, ErrorMessage = "技术栈长度不能超过500个字符")]
        public string? TechnologyStack { get; set; }

        /// <summary>
        /// 标签（逗号分隔）
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 500)]
        [StringLength(500, ErrorMessage = "标签长度不能超过500个字符")]
        public string? Tags { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2000)]
        [StringLength(2000, ErrorMessage = "备注长度不能超过2000个字符")]
        public string? Notes { get; set; }



        // 导航属性（不映射到数据库）
        
        /// <summary>
        /// 关联的项目
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Project? Project { get; set; }

        /// <summary>
        /// 分配给的用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? AssignedUser { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 关联的开发步骤
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CodingTaskStep>? Steps { get; set; }

        /// <summary>
        /// 执行日志
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<CodingTaskExecutionLog>? ExecutionLogs { get; set; }

        // 计算属性

        /// <summary>
        /// 关联步骤数量
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int StepCount { get; set; }

        /// <summary>
        /// 进度百分比
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal ProgressPercentage
        {
            get
            {
                if (Steps == null || !Steps.Any()) return 0;

                var completedSteps = Steps.Count(s => s.Status == "Completed");
                return Math.Round((decimal)completedSteps / Steps.Count * 100, 2);
            }
        }

        /// <summary>
        /// 是否逾期
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOverdue
        {
            get
            {
                return DueDate.HasValue && DueDate.Value < DateTime.Now && Status != "Completed";
            }
        }

        /// <summary>
        /// 剩余天数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? RemainingDays
        {
            get
            {
                if (!DueDate.HasValue || Status == "Completed") return null;
                
                var days = (DueDate.Value.Date - DateTime.Now.Date).Days;
                return days;
            }
        }

        /// <summary>
        /// 标签列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<string> TagList
        {
            get
            {
                if (string.IsNullOrEmpty(Tags)) return new List<string>();
                
                return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                          .Select(t => t.Trim())
                          .Where(t => !string.IsNullOrEmpty(t))
                          .ToList();
            }
        }

        /// <summary>
        /// 工时利用率
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal? HourUtilization
        {
            get
            {
                if (!EstimatedHours.HasValue || !ActualHours.HasValue || EstimatedHours.Value == 0)
                    return null;
                
                return Math.Round(ActualHours.Value / EstimatedHours.Value * 100, 2);
            }
        }
    }
}
