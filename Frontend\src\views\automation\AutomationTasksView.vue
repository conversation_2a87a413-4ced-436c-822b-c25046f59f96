<template>
  <div class="automation-tasks-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Setting /></el-icon>
            自动化任务管理
          </h1>
          <p class="page-description">
            管理和监控本地自动化客户端的任务执行
          </p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            创建任务
          </el-button>
          <el-button @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.statusCounts.Pending || 0 }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon progress">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.statusCounts.InProgress || 0 }}</div>
                <div class="stat-label">执行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.statusCounts.Completed || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.statusCounts.Failed || 0 }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filters" inline>
        <el-form-item label="项目">
          <el-select v-model="filters.projectId" placeholder="选择项目" clearable style="width: 200px">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="待处理" value="Pending" />
            <el-option label="已分配" value="Assigned" />
            <el-option label="执行中" value="InProgress" />
            <el-option label="已完成" value="Completed" />
            <el-option label="失败" value="Failed" />
            <el-option label="已取消" value="Cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务类型">
          <el-select v-model="filters.taskType" placeholder="选择类型" clearable style="width: 180px">
            <el-option label="代码生成" value="CodeGeneration" />
            <el-option label="文件操作" value="FileOperation" />
            <el-option label="Git操作" value="GitOperation" />
            <el-option label="VSCode操作" value="VSCodeOperation" />
            <el-option label="构建操作" value="BuildOperation" />
            <el-option label="测试执行" value="TestExecution" />
            <el-option label="数据库操作" value="DatabaseOperation" />
            <el-option label="部署操作" value="DeploymentOperation" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadTasks">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="tasks-card" shadow="never">
      <el-table
        :data="tasks"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="taskName" label="任务名称" min-width="200" />
        <el-table-column prop="taskType" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTaskTypeTagType(row.taskType)">
              {{ getTaskTypeText(row.taskType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignedTo" label="分配给" width="120" />
        <el-table-column prop="createdTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewTask(row)">查看</el-button>
            <el-button
              size="small"
              type="warning"
              @click="cancelTask(row)"
              v-if="row.status === 'Pending' || row.status === 'Assigned' || row.status === 'InProgress'"
            >
              取消
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteTask(row)"
              v-if="row.status === 'Completed' || row.status === 'Failed' || row.status === 'Cancelled'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建自动化任务"
      width="600px"
    >
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="项目" required>
          <el-select v-model="createForm.projectId" placeholder="选择项目" style="width: 100%">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称" required>
          <el-input v-model="createForm.taskName" placeholder="输入任务名称" />
        </el-form-item>
        <el-form-item label="任务类型" required>
          <el-select v-model="createForm.taskType" placeholder="选择任务类型" style="width: 100%">
            <el-option label="代码生成" value="CodeGeneration" />
            <el-option label="文件操作" value="FileOperation" />
            <el-option label="Git操作" value="GitOperation" />
            <el-option label="VSCode操作" value="VSCodeOperation" />
            <el-option label="构建操作" value="BuildOperation" />
            <el-option label="测试执行" value="TestExecution" />
            <el-option label="数据库操作" value="DatabaseOperation" />
            <el-option label="部署操作" value="DeploymentOperation" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="createForm.priority" style="width: 100%">
            <el-option label="低" value="Low" />
            <el-option label="中" value="Medium" />
            <el-option label="高" value="High" />
            <el-option label="紧急" value="Critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="输入任务描述"
          />
        </el-form-item>
        <el-form-item label="任务数据">
          <el-input
            v-model="createForm.taskData"
            type="textarea"
            :rows="4"
            placeholder="输入JSON格式的任务数据"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask" :loading="creating">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting, Plus, Refresh, Clock, Loading, Check, Close
} from '@element-plus/icons-vue'
import type { ElTagType } from '@/types/element-plus'
import { AutomationService, type AutomationTask, type CreateTaskRequest } from '@/services/automation'
import { ProjectService } from '@/services/project'
import type { ProjectSummary } from '@/types'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)
const tasks = ref<AutomationTask[]>([])
const projects = ref<ProjectSummary[]>([])

const statistics = ref({
  statusCounts: {} as Record<string, number>,
  totalTasks: 0,
  activeClients: 0,
  todayCompleted: 0,
  todayFailed: 0
})

const filters = reactive({
  projectId: undefined as number | undefined,
  status: '',
  taskType: ''
})

const pagination = reactive({
  pageIndex: 1,
  pageSize: 20,
  totalCount: 0
})

const createForm = reactive<CreateTaskRequest>({
  projectId: 0,
  taskName: '',
  taskType: '',
  priority: 'Medium',
  description: '',
  taskData: ''
})

// 生命周期
onMounted(() => {
  loadProjects()
  loadStatistics()
  loadTasks()
})

// 方法
const loadProjects = async () => {
  try {
    const result = await ProjectService.getProjects({ pageSize: 100 })
    projects.value = result.items
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

const loadStatistics = async () => {
  try {
    const stats = await AutomationService.getTaskStatistics(filters.projectId || undefined)
    statistics.value = stats
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用默认值
    statistics.value = {
      statusCounts: {},
      totalTasks: 0,
      activeClients: 0,
      todayCompleted: 0,
      todayFailed: 0
    }
  }
}

const loadTasks = async () => {
  loading.value = true
  try {
    if (filters.projectId) {
      // 获取特定项目的任务
      const result = await AutomationService.getProjectTasks(
        filters.projectId,
        filters.status || undefined,
        filters.taskType || undefined,
        pagination.pageIndex,
        pagination.pageSize
      )
      tasks.value = result.items
      pagination.totalCount = result.totalCount
    } else {
      // 获取所有任务（这里需要后端支持全局任务查询）
      // 暂时使用第一个项目的任务作为示例
      if (projects.value.length > 0) {
        const result = await AutomationService.getProjectTasks(
          projects.value[0].id,
          filters.status || undefined,
          filters.taskType || undefined,
          pagination.pageIndex,
          pagination.pageSize
        )
        tasks.value = result.items
        pagination.totalCount = result.totalCount
      } else {
        tasks.value = []
        pagination.totalCount = 0
      }
    }
  } catch (error) {
    console.error('加载任务失败:', error)
    ElMessage.error('加载任务失败')
    tasks.value = []
    pagination.totalCount = 0
  } finally {
    loading.value = false
  }
}

const refreshTasks = () => {
  loadTasks()
  loadStatistics()
}

const resetFilters = () => {
  filters.projectId = undefined
  filters.status = ''
  filters.taskType = ''
  loadTasks()
}

const createTask = async () => {
  if (!createForm.projectId || !createForm.taskName || !createForm.taskType) {
    ElMessage.warning('请填写必填字段')
    return
  }

  creating.value = true
  try {
    await AutomationService.createTask(createForm)

    ElMessage.success('任务创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    loadTasks()
    loadStatistics()
  } catch (error) {
    console.error('创建任务失败:', error)
    ElMessage.error('创建任务失败')
  } finally {
    creating.value = false
  }
}

const resetCreateForm = () => {
  createForm.projectId = 0
  createForm.taskName = ''
  createForm.taskType = ''
  createForm.priority = 'Medium'
  createForm.description = ''
  createForm.taskData = ''
}

const viewTask = (task: AutomationTask) => {
  // TODO: 实现任务详情查看对话框
  console.log('查看任务详情:', task)
  ElMessage.info('任务详情功能开发中')
}

const cancelTask = async (task: AutomationTask) => {
  try {
    await ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
      type: 'warning'
    })

    await AutomationService.cancelTask(task.id, '用户手动取消')
    ElMessage.success('任务已取消')
    loadTasks()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

const deleteTask = async (task: AutomationTask) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？删除后无法恢复。', '确认删除', {
      type: 'warning'
    })

    await AutomationService.deleteTask(task.id)
    ElMessage.success('任务已删除')
    loadTasks()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      ElMessage.error('删除任务失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadTasks()
}

const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  loadTasks()
}

// 辅助方法
const getTaskTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    CodeGeneration: '代码生成',
    FileOperation: '文件操作',
    GitOperation: 'Git操作',
    VSCodeOperation: 'VSCode操作',
    BuildOperation: '构建操作',
    TestExecution: '测试执行',
    DatabaseOperation: '数据库操作',
    DeploymentOperation: '部署操作'
  }
  return typeMap[type] || type
}

const getTaskTypeTagType = (type: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    CodeGeneration: 'primary',
    FileOperation: 'info',
    GitOperation: 'warning',
    VSCodeOperation: 'success',
    BuildOperation: 'danger',
    TestExecution: 'warning',
    DatabaseOperation: 'primary',
    DeploymentOperation: 'danger'
  }
  return typeMap[type] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    Pending: '待处理',
    Assigned: '已分配',
    InProgress: '执行中',
    Completed: '已完成',
    Failed: '失败',
    Cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string): ElTagType => {
  const statusMap: Record<string, ElTagType> = {
    Pending: 'info',
    Assigned: 'warning',
    InProgress: 'primary',
    Completed: 'success',
    Failed: 'danger',
    Cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    Low: '低',
    Medium: '中',
    High: '高',
    Critical: '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTagType = (priority: string): ElTagType => {
  const priorityMap: Record<string, ElTagType> = {
    Low: 'info',
    Medium: 'primary',
    High: 'warning',
    Critical: 'danger'
  }
  return priorityMap[priority] || 'info'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.automation-tasks-view {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.statistics-cards {
  margin-bottom: 24px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffa726, #ff9800);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #42a5f5, #2196f3);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #66bb6a, #4caf50);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #ef5350, #f44336);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.filter-card {
  margin-bottom: 24px;
}

.tasks-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .automation-tasks-view {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .statistics-cards .el-col {
    margin-bottom: 16px;
  }
}
</style>
