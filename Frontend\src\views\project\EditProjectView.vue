<template>
  <div class="edit-project">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" type="text" class="back-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回项目详情
        </el-button>
        <h1 class="page-title">编辑项目</h1>
        <p class="page-subtitle">修改项目信息和配置</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="project" class="form-container">
      <el-card class="form-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          label-position="left"
          size="large"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>

            <el-form-item label="项目名称" prop="name" required>
              <el-input
                v-model="form.name"
                placeholder="请输入项目名称"
                maxlength="200"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-form-item label="项目描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                placeholder="请输入项目描述，包括项目目标、功能范围等"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择状态">
                    <el-option
                      v-for="option in statusOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目优先级" prop="priority">
                  <el-select v-model="form.priority" placeholder="请选择优先级">
                    <el-option
                      v-for="option in priorityOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="技术栈" prop="technologyStack">
              <el-select
                v-model="form.technologyStack"
                placeholder="请选择主要技术栈"
                clearable
              >
                <el-option
                  v-for="tech in technologyOptions"
                  :key="tech.value"
                  :label="tech.label"
                  :value="tech.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 项目进度 -->
          <div class="form-section">
            <h3 class="section-title">项目进度</h3>

            <el-form-item label="完成进度" prop="progress">
              <div class="progress-input">
                <el-slider
                  v-model="form.progress"
                  :min="0"
                  :max="100"
                  show-input
                  :format-tooltip="formatTooltip"
                />
              </div>
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>当前项目完成进度百分比</span>
              </div>
            </el-form-item>
          </div>

          <!-- 时间规划 -->
          <div class="form-section">
            <h3 class="section-title">时间规划</h3>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startDate">
                  <el-date-picker
                    v-model="form.startDate"
                    type="date"
                    placeholder="选择开始时间"
                    style="width: 100%"
                    :disabled-date="disabledStartDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endDate">
                  <el-date-picker
                    v-model="form.endDate"
                    type="date"
                    placeholder="选择结束时间"
                    style="width: 100%"
                    :disabled-date="disabledEndDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预估工时" prop="estimatedHours">
                  <el-input-number
                    v-model="form.estimatedHours"
                    :min="1"
                    :max="10000"
                    :step="1"
                    :precision="0"
                    controls
                    controls-position="right"
                    placeholder="预估工时（小时）"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际工时" prop="actualHours">
                  <el-input-number
                    v-model="form.actualHours"
                    :min="0"
                    :max="10000"
                    :step="0.5"
                    :precision="1"
                    controls
                    controls-position="right"
                    placeholder="实际工时（小时）"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 预算管理 -->
          <div class="form-section">
            <h3 class="section-title">预算管理</h3>

            <el-form-item label="项目预算" prop="budget">
              <el-input-number
                v-model="form.budget"
                :min="0"
                :step="1000"
                :precision="0"
                controls
                controls-position="right"
                placeholder="项目预算（元）"
                style="width: 100%"
                :formatter="(value: any) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value: any) => value.replace(/¥\s?|(,*)/g, '')"
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>包括开发成本、第三方服务费用等</span>
              </div>
            </el-form-item>
          </div>

          <!-- 项目信息 -->
          <div class="form-section">
            <h3 class="section-title">项目信息</h3>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间">
                  <el-input :value="formatDate(project.createdAt)" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最后更新">
                  <el-input :value="project.updatedAt ? formatDate(project.updatedAt) : '未更新'" disabled />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="项目负责人">
              <el-input :value="project.ownerName" disabled />
            </el-form-item>
          </div>

          <!-- 表单操作 -->
          <div class="form-actions">
            <el-button @click="resetForm" size="large">
              重置
            </el-button>
            <el-button @click="$router.back()" size="large">
              取消
            </el-button>
            <el-button
              @click="submitForm"
              type="primary"
              size="large"
              :loading="submitting"
            >
              保存更改
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 项目不存在 -->
    <div v-else class="not-found">
      <el-result
        icon="warning"
        title="项目不存在"
        sub-title="请检查项目ID是否正确"
      >
        <template #extra>
          <el-button @click="$router.push('/projects')" type="primary">
            返回项目列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { ProjectService } from '@/services/project'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import type { FormInstance, FormRules } from 'element-plus'
import type { UpdateProjectRequest } from '@/types'

// 图标导入
import {
  ArrowLeft,
  InfoFilled
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive<UpdateProjectRequest & {
  status: string
  progress: number
  actualHours?: number
}>({
  name: '',
  description: '',
  priority: '',
  startDate: '',
  endDate: '',
  estimatedHours: undefined,
  budget: undefined,
  technologyStack: '',
  status: '',
  progress: 0,
  actualHours: undefined
})

// 原始数据备份
const originalData = ref<any>({})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 200, message: '项目名称长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '项目描述不能超过 1000 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择项目优先级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ],
  progress: [
    { type: 'number', min: 0, max: 100, message: '进度必须在 0 到 100 之间', trigger: 'blur' }
  ],
  estimatedHours: [
    { type: 'number', min: 1, max: 10000, message: '预估工时必须在 1 到 10000 小时之间', trigger: 'blur' }
  ],
  actualHours: [
    { type: 'number', min: 0, max: 10000, message: '实际工时不能为负数', trigger: 'blur' }
  ],
  budget: [
    { type: 'number', min: 0, message: '预算不能为负数', trigger: 'blur' }
  ]
}

// 计算属性
const project = computed(() => projectStore.currentProject)
const projectId = computed(() => parseInt(route.params.id as string))

const statusOptions = computed(() => ProjectService.getStatusOptions())
const priorityOptions = computed(() => ProjectService.getPriorityOptions())

const technologyOptions = ref([
  { label: 'Vue.js + ASP.NET Core', value: 'Vue.js,ASP.NET Core,SQL Server' },
  { label: 'React + Node.js', value: 'React,Node.js,MongoDB' },
  { label: 'Angular + Spring Boot', value: 'Angular,Spring Boot,MySQL' },
  { label: 'Flutter + Firebase', value: 'Flutter,Firebase,Firestore' },
  { label: 'React Native + Express', value: 'React Native,Express,PostgreSQL' },
  { label: '微信小程序', value: '微信小程序,Node.js,MySQL' },
  { label: '自定义技术栈', value: 'Custom' }
])

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const formatTooltip = (value: number) => {
  return `${value}%`
}

// 日期验证
const disabledStartDate = (time: Date) => {
  // 编辑模式下允许选择过去的日期
  return false
}

const disabledEndDate = (time: Date) => {
  if (!form.startDate) return false
  const startTime = new Date(form.startDate).getTime()
  return time.getTime() < startTime // 结束时间不能早于开始时间
}

// 初始化表单数据
const initializeForm = () => {
  if (!project.value) return

  const proj = project.value

  // 保存原始数据
  originalData.value = { ...proj }

  // 填充表单
  form.name = proj.name
  form.description = proj.description || ''
  form.priority = proj.priority
  form.startDate = proj.startDate || ''
  form.endDate = proj.endDate || ''
  form.estimatedHours = proj.estimatedHours
  form.budget = proj.budget
  form.technologyStack = proj.technologyStack || ''
  form.status = proj.status
  form.progress = proj.progress
  form.actualHours = proj.actualHours
}

// 表单操作
const resetForm = () => {
  initializeForm()
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 准备提交数据
    const submitData: UpdateProjectRequest = {
      name: form.name,
      description: form.description,
      priority: form.priority,
      startDate: form.startDate,
      endDate: form.endDate,
      estimatedHours: form.estimatedHours,
      actualHours: form.actualHours,
      budget: form.budget,
      technologyStack: form.technologyStack
    }

    // 更新项目
    await projectStore.updateProject(projectId.value, submitData)

    // 如果状态或进度有变化，单独更新
    if (form.status !== originalData.value.status || form.progress !== originalData.value.progress) {
      await projectStore.updateProjectStatus(projectId.value, {
        status: form.status,
        progress: form.progress
      })
    }

    ElMessage.success('项目更新成功')

    // 跳转回项目详情页
    router.push(`/projects/${projectId.value}`)

  } catch (error) {
    console.error('Update project failed:', error)
    ElMessage.error('项目更新失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 获取项目数据
const fetchProjectData = async () => {
  try {
    loading.value = true
    await projectStore.fetchProject(projectId.value)
    initializeForm()
  } catch (error) {
    console.error('Failed to fetch project:', error)
    ElMessage.error('获取项目信息失败')
  } finally {
    loading.value = false
  }
}

// 监听项目数据变化
watch(
  () => project.value,
  (newProject) => {
    if (newProject) {
      initializeForm()
    }
  },
  { immediate: true }
)

// 组件挂载时获取数据
onMounted(async () => {
  await fetchProjectData()
})
</script>

<style lang="scss" scoped>
.edit-project {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;

  .page-header {
    margin-bottom: 32px;

    .back-btn {
      margin-bottom: 16px;
      color: #606266;

      &:hover {
        color: #409eff;
      }
    }

    .page-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: #303133;
    }

    .page-subtitle {
      margin: 0;
      color: #606266;
      font-size: 16px;
    }
  }

  .loading-container {
    padding: 40px 0;
  }

  .form-container {
    .form-card {
      border-radius: 12px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .form-section {
        margin-bottom: 32px;
        padding-bottom: 24px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .section-title {
          margin: 0 0 20px 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #409eff;
            margin-right: 12px;
            border-radius: 2px;
          }
        }

        .form-tip {
          display: flex;
          align-items: center;
          margin-top: 8px;
          color: #909399;
          font-size: 14px;

          .el-icon {
            margin-right: 6px;
            color: #409eff;
          }
        }

        .progress-input {
          padding-right: 20px;
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }

  .not-found {
    padding: 60px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .edit-project {
    padding: 16px;

    .form-container {
      .form-card {
        :deep(.el-form) {
          .el-form-item {
            .el-form-item__label {
              text-align: left;
            }
          }

          .el-row {
            .el-col {
              margin-bottom: 16px;
            }
          }
        }

        .form-actions {
          flex-direction: column;

          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

// Element Plus 组件样式覆盖
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-slider) {
  .el-slider__runway {
    border-radius: 3px;
  }

  .el-slider__button {
    border: 2px solid #409eff;
  }
}
</style>
