# AI驱动的VSCode自动化客户端 - GUI版本

这是Python自动化客户端的图形用户界面版本，提供直观的可视化管理和监控功能。

## 🎯 GUI功能特性

### 📊 主要功能
- **实时任务监控** - 可视化显示任务执行状态和进度
- **客户端状态管理** - 启动/停止客户端，查看运行状态
- **配置管理** - 图形化配置编辑和管理
- **日志查看器** - 实时日志显示和过滤
- **任务创建器** - 可视化创建各种类型的自动化任务
- **系统监控** - CPU、内存、磁盘使用情况监控

### 🖥️ 界面组成

#### 1. 主窗口
- **状态栏**: 显示客户端状态、API连接状态、运行任务数
- **控制按钮**: 启动/停止客户端、创建任务
- **标签页**: 任务监控、客户端状态、日志、配置

#### 2. 任务监控页面
- **任务统计**: 总计、待处理、执行中、已完成、失败任务数
- **任务列表**: 表格形式显示所有任务详情
- **过滤功能**: 按状态、类型过滤任务
- **任务详情**: 选中任务的详细信息显示

#### 3. 客户端状态页面
- **客户端信息**: ID、版本、环境等基本信息
- **系统信息**: 操作系统、硬件配置
- **性能监控**: 实时CPU、内存、磁盘使用率

#### 4. 日志页面
- **日志级别过滤**: DEBUG、INFO、WARNING、ERROR
- **实时日志**: 彩色编码的日志消息
- **日志导出**: 保存日志到文件

#### 5. 配置页面
- **配置编辑**: YAML格式的配置文件编辑
- **配置验证**: 保存前验证配置正确性
- **配置重载**: 实时重新加载配置

## 🚀 快速启动

### 方法1: 使用批处理脚本（推荐）
```bash
# Windows
start_gui.bat

# 或者双击 start_gui.bat 文件
```

### 方法2: 使用PowerShell脚本
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\start_gui.ps1
```

### 方法3: 直接运行Python
```bash
# 确保在Python目录下
cd Python
python gui_main.py
```

## 📋 使用指南

### 1. 首次启动
1. 运行启动脚本，程序会自动检查依赖项
2. 如果缺少依赖，会自动安装
3. 程序会创建默认配置文件 `config.yaml`
4. GUI界面启动后，可以在"配置"页面调整设置

### 2. 配置客户端
1. 点击"工具" → "设置"打开设置对话框
2. 配置以下关键信息：
   - **API地址**: 项目管理系统的API地址
   - **VSCode路径**: VSCode可执行文件位置
   - **工作区路径**: 项目工作目录
   - **支持的任务类型**: 选择要处理的任务类型

### 3. 启动客户端
1. 点击"启动客户端"按钮
2. 观察状态栏显示"运行中"和"已连接"
3. 客户端开始轮询任务队列

### 4. 监控任务执行
1. 切换到"任务监控"页面
2. 查看任务统计和列表
3. 双击任务查看详细信息
4. 使用过滤功能筛选特定任务

### 5. 创建测试任务
1. 点击"创建任务"按钮
2. 选择任务类型（Copilot生成、Augment生成等）
3. 填写任务参数
4. 点击"创建"提交任务

## 🎮 支持的任务类型

### 1. VSCode Copilot代码生成
- **功能**: 使用GitHub Copilot Chat生成代码
- **配置项**:
  - AI提示词
  - 目标文件路径
  - 编程语言和框架
  - 是否自动接受建议

### 2. VSCode Augment代码生成
- **功能**: 使用Augment插件生成代码
- **配置项**:
  - AI提示词
  - 目标文件路径
  - 项目上下文
  - 是否包含测试代码

### 3. VSCode Cursor代码生成
- **功能**: 使用Cursor AI生成代码
- **配置项**:
  - AI提示词
  - 目标文件路径
  - 代码选择范围
  - 是否自动接受建议

### 4. 文件操作
- **功能**: 创建、打开、保存、关闭文件
- **配置项**:
  - 操作类型
  - 文件路径
  - 文件内容
  - 文件编码

### 5. VSCode命令执行
- **功能**: 执行VSCode内置命令
- **配置项**:
  - 命令名称
  - 命令参数
  - 等待时间
  - 对话框处理

## 🔧 高级功能

### 任务过滤和搜索
- 按状态过滤：待处理、执行中、已完成、失败
- 按类型过滤：不同的任务类型
- 实时更新：任务状态自动刷新

### 日志管理
- 多级别日志：DEBUG、INFO、WARNING、ERROR
- 彩色编码：不同级别使用不同颜色
- 日志导出：保存日志到文件
- 实时显示：新日志自动滚动到底部

### 配置管理
- 可视化编辑：图形界面编辑配置
- 实时验证：保存前验证配置正确性
- 热重载：无需重启即可应用新配置
- 备份恢复：支持配置备份和恢复

### 系统监控
- 性能指标：CPU、内存、磁盘使用率
- 客户端状态：运行时间、处理任务数
- 错误统计：成功率、失败率统计

## 🛠️ 故障排除

### 常见问题

1. **GUI无法启动**
   - 检查Python版本（需要3.8+）
   - 确保tkinter模块可用
   - 运行 `python -m tkinter` 测试

2. **依赖项安装失败**
   - 使用管理员权限运行
   - 更新pip：`python -m pip install --upgrade pip`
   - 手动安装：`pip install -r requirements.txt`

3. **配置文件错误**
   - 检查YAML语法
   - 使用"重置"功能恢复默认配置
   - 查看错误日志获取详细信息

4. **VSCode连接失败**
   - 确认VSCode路径正确
   - 检查VSCode是否已安装
   - 测试VSCode命令行启动

### 调试模式
```bash
# 启用详细日志
python gui_main.py --verbose

# 查看配置信息
python gui_main.py --dry-run
```

## 📊 性能优化

### 系统要求
- **操作系统**: Windows 10/11
- **Python**: 3.8或更高版本
- **内存**: 最少2GB，推荐4GB+
- **磁盘**: 至少500MB可用空间

### 优化建议
1. **减少并发任务数**: 根据系统性能调整
2. **调整轮询间隔**: 避免过于频繁的API调用
3. **清理日志文件**: 定期清理旧日志
4. **关闭不必要的功能**: 禁用不需要的任务类型

## 🔒 安全注意事项

1. **API密钥保护**: 不要在配置文件中明文存储敏感信息
2. **文件权限**: 确保工作目录有适当的读写权限
3. **网络安全**: 使用HTTPS连接API服务器
4. **操作限制**: 配置允许的操作目录，避免误操作

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认网络连接和API服务器状态
4. 参考命令行版本的文档和故障排除指南

---

**注意**: GUI版本是命令行版本的可视化封装，核心功能保持一致。如果GUI出现问题，可以随时切换到命令行版本使用。
