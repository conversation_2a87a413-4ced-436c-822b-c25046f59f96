namespace ProjectManagement.Core.DTOs.AI
{
    /// <summary>
    /// AI模型配置
    /// </summary>
    public class AIModelConfig
    {
        /// <summary>
        /// 提供商名称
        /// </summary>
        public string Provider { get; set; } = string.Empty;

        /// <summary>
        /// 模型名称
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// API端点
        /// </summary>
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens { get; set; } = 4000;

        /// <summary>
        /// 温度参数（创造性）
        /// </summary>
        public float Temperature { get; set; } = 0.7f;

        /// <summary>
        /// Top-p参数
        /// </summary>
        public float TopP { get; set; } = 1.0f;

        /// <summary>
        /// 频率惩罚
        /// </summary>
        public float FrequencyPenalty { get; set; } = 0.0f;

        /// <summary>
        /// 存在惩罚
        /// </summary>
        public float PresencePenalty { get; set; } = 0.0f;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 60;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 额外参数
        /// </summary>
        public Dictionary<string, object> ExtraParameters { get; set; } = new();
    }
}
