import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface TabItem {
  name: string
  path: string
  title: string
  closable: boolean
  icon?: string
  params?: Record<string, any>
  query?: Record<string, any>
}

export const useTabsStore = defineStore('tabs', () => {
  // 状态
  const tabs = ref<TabItem[]>([])
  const activeTab = ref<string>('')

  // 计算属性
  const currentTab = computed(() => 
    tabs.value.find(tab => tab.name === activeTab.value)
  )

  const hasMultipleTabs = computed(() => tabs.value.length > 1)

  // 添加标签页
  const addTab = (route: RouteLocationNormalized) => {
    // 跳过不需要显示标签的页面
    if (route.meta?.hideInTabs || route.meta?.hideInMenu) {
      return
    }

    const tabName = route.name as string
    const existingTab = tabs.value.find(tab => tab.name === tabName)

    if (!existingTab) {
      const newTab: TabItem = {
        name: tabName,
        path: route.path,
        title: (route.meta?.title as string) || tabName,
        closable: route.path !== '/dashboard', // 仪表板不可关闭
        icon: route.meta?.icon as string,
        params: route.params,
        query: route.query
      }
      tabs.value.push(newTab)
    } else {
      // 更新现有标签的路径和参数（处理动态路由）
      existingTab.path = route.path
      existingTab.params = route.params
      existingTab.query = route.query
    }

    activeTab.value = tabName
  }

  // 移除标签页
  const removeTab = (tabName: string) => {
    const index = tabs.value.findIndex(tab => tab.name === tabName)
    if (index === -1) return null

    const removedTab = tabs.value[index]
    tabs.value.splice(index, 1)

    // 如果移除的是当前激活的标签，需要切换到其他标签
    if (activeTab.value === tabName && tabs.value.length > 0) {
      // 优先选择右边的标签，如果没有则选择左边的
      const nextTab = tabs.value[index] || tabs.value[index - 1]
      activeTab.value = nextTab.name
      return nextTab
    }

    return null
  }

  // 移除其他标签页
  const removeOtherTabs = (keepTabName: string) => {
    tabs.value = tabs.value.filter(tab => 
      tab.name === keepTabName || !tab.closable
    )
    activeTab.value = keepTabName
  }

  // 移除所有标签页
  const removeAllTabs = () => {
    tabs.value = tabs.value.filter(tab => !tab.closable)
    if (tabs.value.length > 0) {
      activeTab.value = tabs.value[0].name
      return tabs.value[0]
    }
    return null
  }

  // 设置激活标签
  const setActiveTab = (tabName: string) => {
    const tab = tabs.value.find(t => t.name === tabName)
    if (tab) {
      activeTab.value = tabName
      return tab
    }
    return null
  }

  // 获取标签页
  const getTab = (tabName: string) => {
    return tabs.value.find(tab => tab.name === tabName)
  }

  // 更新标签标题
  const updateTabTitle = (tabName: string, title: string) => {
    const tab = tabs.value.find(t => t.name === tabName)
    if (tab) {
      tab.title = title
    }
  }

  // 清空所有标签
  const clearTabs = () => {
    tabs.value = []
    activeTab.value = ''
  }

  // 初始化默认标签（仪表板）
  const initDefaultTab = () => {
    if (tabs.value.length === 0) {
      tabs.value.push({
        name: 'Dashboard',
        path: '/dashboard',
        title: '仪表板',
        closable: false,
        icon: 'Monitor'
      })
      activeTab.value = 'Dashboard'
    }
  }

  return {
    // 状态
    tabs,
    activeTab,
    
    // 计算属性
    currentTab,
    hasMultipleTabs,
    
    // 方法
    addTab,
    removeTab,
    removeOtherTabs,
    removeAllTabs,
    setActiveTab,
    getTab,
    updateTabTitle,
    clearTabs,
    initDefaultTab
  }
})
