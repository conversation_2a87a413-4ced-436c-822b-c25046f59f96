import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false,
        hideInMenu: true,
        hideInTabs: true
      }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/RegisterView.vue'),
      meta: {
        title: '注册',
        requiresAuth: false,
        hideInMenu: true,
        hideInTabs: true
      }
    },
    {
      path: '/test-auth',
      name: 'TestAuth',
      component: () => import('@/views/TestAuthView.vue'),
      meta: {
        title: '认证测试',
        requiresAuth: false
      }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: {
        title: '仪表板',
        requiresAuth: true,
        icon: 'Monitor',
        keepAlive: true
      }
    },
    {
      path: '/projects',
      name: 'Projects',
      component: () => import('@/views/project/ProjectListView.vue'),
      meta: {
        title: '项目管理',
        requiresAuth: true,
        icon: 'Folder',
        keepAlive: true
      }
    },
    {
      path: '/projects/create',
      name: 'CreateProject',
      component: () => import('@/views/project/CreateProjectView.vue'),
      meta: {
        title: '创建项目',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '项目管理', to: '/projects' },
          { title: '创建项目' }
        ]
      }
    },
    {
      path: '/projects/:id',
      name: 'ProjectDetail',
      component: () => import('@/views/project/ProjectDetailView.vue'),
      meta: {
        title: '项目详情',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '项目管理', to: '/projects' },
          { title: '项目详情' }
        ]
      }
    },
    {
      path: '/projects/:id/edit',
      name: 'EditProject',
      component: () => import('@/views/project/EditProjectView.vue'),
      meta: {
        title: '编辑项目',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '项目管理', to: '/projects' },
          { title: '编辑项目' }
        ]
      }
    },
    {
      path: '/requirements',
      name: 'Requirements',
      component: () => import('@/views/requirement/RequirementListView.vue'),
      meta: {
        title: '需求管理',
        requiresAuth: true,
        icon: 'Document',
        keepAlive: true
      }
    },
    {
      path: '/requirements/documents/:id',
      name: 'RequirementDetail',
      component: () => import('@/views/requirement/RequirementDetailView.vue'),
      meta: {
        title: '需求详情',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '需求管理', to: '/requirements' },
          { title: '需求详情' }
        ]
      }
    },
    {
      path: '/requirements/documents/:id/edit',
      name: 'RequirementEdit',
      component: () => import('@/views/requirement/RequirementEditView.vue'),
      meta: {
        title: '编辑需求',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '需求管理', to: '/requirements' },
          { title: '编辑需求' }
        ]
      }
    },
    {
      path: '/requirements/:projectId/chat',
      name: 'RequirementChat',
      component: () => import('@/views/requirement/RequirementChatView.vue'),
      meta: {
        title: 'AI需求对话',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '需求管理', to: '/requirements' },
          { title: 'AI需求对话' }
        ]
      }
    },
    {
      path: '/design',
      name: 'Design',
      component: () => import('@/views/design/DesignListView.vue'),
      meta: {
        title: '设计生成',
        requiresAuth: true,
        icon: 'PictureRounded',
        keepAlive: true
      }
    },
    {
      path: '/design/:projectId/er-diagram',
      name: 'ERDiagram',
      component: () => import('@/views/design/ERDiagramView.vue'),
      meta: {
        title: 'ER图设计',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '设计生成', to: '/design' },
          { title: 'ER图设计' }
        ]
      }
    },
    {
      path: '/design/:projectId/context-diagram',
      name: 'ContextDiagram',
      component: () => import('@/views/design/ContextDiagramView.vue'),
      meta: {
        title: '上下文图设计',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '设计生成', to: '/design' },
          { title: '上下文图设计' }
        ]
      }
    },
    {
      path: '/design/:projectId/prototype',
      name: 'PrototypeView',
      component: () => import('@/views/design/PrototypeView.vue'),
      meta: {
        title: '原型图设计',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '设计生成', to: '/design' },
          { title: '原型图设计' }
        ]
      }
    },
    {
      path: '/development',
      name: 'Development',
      component: () => import('@/views/development/DevelopmentView.vue'),
      meta: {
        title: '开发管理',
        requiresAuth: true,
        icon: 'Operation',
        keepAlive: true
      }
    },
    {
      path: '/development/steps',
      name: 'DevelopmentSteps',
      component: () => import('@/views/development/DevelopmentStepsView.vue'),
      meta: {
        title: '开发步骤',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '开发管理', to: '/development' },
          { title: '开发步骤' }
        ]
      }
    },
    {
      path: '/development/decompose',
      name: 'RequirementDecompose',
      component: () => import('@/views/development/RequirementDecomposeView.vue'),
      meta: {
        title: '需求分解',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '开发管理', to: '/development' },
          { title: '需求分解' }
        ]
      }
    },
    {
      path: '/development/associations',
      name: 'DevelopmentAssociations',
      component: () => import('@/views/development/AssociationManagementView.vue'),
      meta: {
        title: '关联管理',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '开发管理', to: '/development' },
          { title: '关联管理' }
        ]
      }
    },
    {
      path: '/development/build-errors',
      name: 'VSBuildErrors',
      component: () => import('@/views/development/VSBuildErrorView.vue'),
      meta: {
        title: 'VS编译错误管理',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '开发管理', to: '/development' },
          { title: 'VS编译错误管理' }
        ]
      }
    },
    {
      path: '/development/coding-tasks',
      name: 'CodingTasks',
      component: () => import('@/views/development/CodingTasksView.vue'),
      meta: {
        title: '编码任务',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: '开发管理', to: '/development' },
          { title: '编码任务' }
        ]
      }
    },
    // UI自动化管理父路由
    {
      path: '/automation',
      name: 'Automation',
      redirect: '/automation/tasks',
      meta: {
        title: 'UI自动化管理',
        requiresAuth: true,
        icon: 'Cpu'
      },
      children: [
        // {
        //   path: 'tasks',
        //   name: 'AutomationTasks',
        //   component: () => import('@/views/automation/AutomationTasksView.vue'),
        //   meta: {
        //     title: '自动化任务',
        //     requiresAuth: true,
        //     icon: 'Operation'
        //   }
        // },
        {
          path: 'templates',
          name: 'CustomTemplates',
          component: () => import('@/views/automation/CustomTemplateListView.vue'),
          meta: {
            title: '自定义模板',
            requiresAuth: true,
            icon: 'Picture',
            keepAlive: true
          }
        },
        {
          path: 'sequences',
          name: 'TemplateSequences',
          component: () => import('@/views/automation/TemplateSequenceView.vue'),
          meta: {
            title: '模板序列',
            requiresAuth: true,
            icon: 'List',
            keepAlive: true
          }
        },
        {
          path: 'categories',
          name: 'CategoryManagement',
          component: () => import('@/views/automation/CategoryManagementView.vue'),
          meta: {
            title: '分类管理',
            requiresAuth: true,
            icon: 'FolderOpened',
            keepAlive: true
          }
        }
      ]
    },
    // 测试管理父路由
    {
      path: '/testing',
      name: 'Testing',
      redirect: '/testing/overview',
      meta: {
        title: '测试管理',
        requiresAuth: true,
        icon: 'CircleCheck'
      },
      children: [
        {
          path: 'overview',
          name: 'TestingOverview',
          component: () => import('@/views/test/TestingOverview.vue'),
          meta: {
            title: '测试概览',
            requiresAuth: true,
            icon: 'Monitor'
          }
        },
        {
          path: 'selenium',
          name: 'SeleniumTesting',
          component: () => import('@/views/test/SeleniumTestView.vue'),
          meta: {
            title: 'Selenium自动化',
            requiresAuth: true,
            icon: 'Setting'
          }
        },
        {
          path: 'api-testing',
          name: 'APITesting',
          component: () => import('@/views/test/APITestView.vue'),
          meta: {
            title: 'API测试',
            requiresAuth: true,
            icon: 'Connection'
          }
        },
        {
          path: 'performance',
          name: 'PerformanceTesting',
          component: () => import('@/views/test/PerformanceTestView.vue'),
          meta: {
            title: '性能测试',
            requiresAuth: true,
            icon: 'TrendCharts'
          }
        },
        {
          path: 'playwright',
          name: 'PlaywrightTesting',
          component: () => import('@/views/test/PlaywrightTestView.vue'),
          meta: {
            title: 'Playwright现代化测试',
            requiresAuth: true,
            icon: 'VideoPlay'
          }
        },
        {
          path: 'prompt-templates',
          name: 'TestPromptTemplates',
          component: () => import('@/views/test/TestPromptTemplateView.vue'),
          meta: {
            title: '测试提示词模板',
            requiresAuth: true,
            icon: 'EditPen',
            hideInMenu: true
          }
        }
      ]
    },
    {
      path: '/deployment',
      name: 'Deployment',
      component: () => import('@/views/deploy/DeploymentView.vue'),
      meta: {
        title: '部署管理',
        requiresAuth: true,
        icon: 'Upload',
        keepAlive: true
      }
    },
    {
      path: '/prompt-templates',
      name: 'PromptTemplates',
      component: () => import('@/views/prompt/PromptTemplateManagement.vue'),
      meta: {
        title: 'Prompt工程',
        requiresAuth: true,
        icon: 'EditPen',
        keepAlive: true
      }
    },
    {
      path: '/prompt-analytics',
      name: 'PromptAnalytics',
      component: () => import('@/views/prompt/PromptAnalyticsDashboard.vue'),
      meta: {
        title: 'Prompt分析',
        requiresAuth: true,
        hideInMenu: true,
        breadcrumb: [
          { title: 'Prompt工程', to: '/prompt-templates' },
          { title: 'Prompt分析' }
        ]
      }
    },
    {
      path: '/issues',
      name: 'Issues',
      component: () => import('@/views/issue/IssueListView.vue'),
      meta: {
        title: '问题管理',
        requiresAuth: true,
        icon: 'Warning',
        keepAlive: true
      }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/user/ProfileView.vue'),
      meta: {
        title: '个人资料',
        requiresAuth: true,
        hideInMenu: true,
        hideInTabs: true
      }
    },
    {
      path: '/users',
      name: 'UserManagement',
      component: () => import('@/views/user/UserManagementView.vue'),
      meta: {
        title: '用户管理',
        requiresAuth: true,
        icon: 'UserFilled',
        permission: 'admin',
        keepAlive: true
      }
    },
    {
      path: '/test/avatar-upload',
      name: 'AvatarUploadTest',
      component: () => import('@/views/test/AvatarUploadTest.vue'),
      meta: {
        title: '头像上传测试',
        requiresAuth: true,
        hideInMenu: true
      }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('@/views/SettingsView.vue'),
      meta: {
        title: '系统设置',
        requiresAuth: true,
        icon: 'Setting',
        permission: 'admin',
        keepAlive: true
      }
    },
    {
      path: '/ai-chat',
      name: 'AIChat',
      component: () => import('@/views/ai/AIChatView.vue'),
      meta: {
        title: 'AI助手',
        requiresAuth: true,
        icon: 'ChatDotRound',
        keepAlive: true
      }
    },
    {
      path: '/ai-providers',
      name: 'AIProviderConfig',
      component: () => import('@/views/admin/AIProviderConfigView.vue'),
      meta: {
        title: 'AI提供商配置',
        requiresAuth: true,
        icon: 'Setting',
        keepAlive: true
      }
    },
    {
      path: '/admin/system-parameters',
      name: 'SystemParameter',
      component: () => import('@/views/admin/SystemParameterView.vue'),
      meta: {
        title: '系统参数管理',
        requiresAuth: true,
        icon: 'Setting',
        permission: 'admin',
        keepAlive: true,
        breadcrumb: [
          { title: '系统管理' },
          { title: '系统参数管理' }
        ]
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFoundView.vue'),
      meta: {
        title: '页面不存在',
        hideInMenu: true,
        hideInTabs: true
      }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const authStore = useAuthStore()

  console.log('Route guard:', {
    to: to.path,
    from: from.path,
    isAuthenticated: authStore.isAuthenticated,
    token: !!authStore.token,
    user: !!authStore.user,
    requiresAuth: to.meta.requiresAuth,
    loading: authStore.loading
  })

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI驱动软件开发自动化系统`
  }

  // 如果正在加载中，等待加载完成
  if (authStore.loading) {
    console.log('Auth store is loading, waiting...')
    // 等待认证状态稳定
    let attempts = 0
    while (authStore.loading && attempts < 50) { // 最多等待5秒
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果有token但没有用户信息，且不在加载中，说明初始化可能有问题
    if (authStore.token && !authStore.user && !authStore.loading) {
      console.log('Has token but no user, this should not happen after proper initialization')
      // 这种情况通常表示初始化过程中出现了问题，直接跳转到登录页
      authStore.logoutLocal()
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    if (!authStore.isAuthenticated) {
      console.log('Not authenticated, redirecting to login')
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
      console.log('Permission denied, redirecting to dashboard')
      // 权限不足
      next({ name: 'Dashboard' })
      return
    }
  } else {
    // 不需要认证的页面，如果已登录则跳转到仪表板
    if (authStore.isAuthenticated && (to.name === 'Login' || to.name === 'Register')) {
      console.log('Already authenticated, redirecting to dashboard')
      next({ name: 'Dashboard' })
      return
    }
  }

  console.log('Route guard passed, proceeding to:', to.path)
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
