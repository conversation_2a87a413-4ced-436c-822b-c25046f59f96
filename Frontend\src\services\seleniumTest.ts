import api from './api'

// 测试脚本接口
export interface TestScript {
  id: number
  name: string
  description: string
  category: string
  code: string
  config: SeleniumConfig
  tags: string[]
  priority: 'low' | 'medium' | 'high'
  status: string
  createdTime: Date
  updatedTime: Date
  createdBy?: number
  projectId?: number
}

// Selenium配置接口
export interface SeleniumConfig {
  browser: string
  headless: boolean
  windowWidth: number
  windowHeight: number
  userAgent?: string
  pageLoadTimeout: number
  implicitWait: number
  scriptTimeout: number
  enableLogging: boolean
  screenshotOptions: string[]
  retryCount: number
  waitStrategy: string
  chromeOptions?: string
  environment: string
  baseUrl?: string
  proxy?: string
}

// 执行结果接口
export interface ExecutionResult {
  id: number
  scriptId: number
  status: 'running' | 'success' | 'failed' | 'stopped'
  startTime: Date
  endTime?: Date
  duration?: number
  logs: ExecutionLog[]
  screenshots: string[]
  errorMessage?: string
  stats: ExecutionStats
}

export interface ExecutionLog {
  timestamp: Date
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
  step?: string
}

export interface ExecutionStats {
  totalSteps: number
  successSteps: number
  failedSteps: number
  skippedSteps: number
}

// Selenium测试服务类
export class SeleniumTestService {
  // 获取测试脚本列表
  static async getScripts(params?: {
    projectId?: number
    category?: string
    keyword?: string
    page?: number
    pageSize?: number
  }): Promise<{ data: TestScript[], total: number }> {
    const response = await api.get('/selenium-scripts', { params })
    return response.data
  }

  // 获取单个测试脚本
  static async getScript(id: number): Promise<TestScript> {
    const response = await api.get(`/selenium-scripts/${id}`)
    return response.data
  }

  // 创建测试脚本
  static async createScript(script: Partial<TestScript>): Promise<TestScript> {
    const response = await api.post('/selenium-scripts', script)
    return response.data
  }

  // 更新测试脚本
  static async updateScript(id: number, script: Partial<TestScript>): Promise<TestScript> {
    const response = await api.put(`/selenium-scripts/${id}`, script)
    return response.data
  }

  // 删除测试脚本
  static async deleteScript(id: number): Promise<void> {
    await api.delete(`/selenium-scripts/${id}`)
  }

  // 执行测试脚本
  static async executeScript(id: number, config?: Partial<SeleniumConfig>): Promise<{ executionId: string }> {
    const response = await api.post(`/selenium-scripts/${id}/execute`, { config })
    return response.data
  }

  // 停止执行
  static async stopExecution(executionId: string): Promise<void> {
    await api.post(`/selenium-executions/${executionId}/stop`)
  }

  // 获取执行结果
  static async getExecutionResult(executionId: string): Promise<ExecutionResult> {
    const response = await api.get(`/selenium-executions/${executionId}`)
    return response.data
  }

  // 获取执行历史
  static async getExecutionHistory(params?: {
    scriptId?: number
    status?: string
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }): Promise<{ data: ExecutionResult[], total: number }> {
    const response = await api.get('/selenium-executions', { params })
    return response.data
  }

  // 获取执行日志
  static async getExecutionLogs(executionId: string): Promise<ExecutionLog[]> {
    const response = await api.get(`/selenium-executions/${executionId}/logs`)
    return response.data
  }

  // 下载执行报告
  static async downloadReport(executionId: string, format: 'html' | 'pdf' | 'json' = 'html'): Promise<Blob> {
    const response = await api.get(`/selenium-executions/${executionId}/report`, {
      params: { format },
      responseType: 'blob'
    })
    return response.data
  }

  // 获取脚本模板
  static async getTemplates(): Promise<{ [key: string]: string }> {
    const response = await api.get('/selenium-scripts/templates')
    return response.data
  }

  // 验证脚本语法
  static async validateScript(code: string): Promise<{ valid: boolean, errors?: string[] }> {
    const response = await api.post('/selenium-scripts/validate', { code })
    return response.data
  }

  // 格式化脚本代码
  static async formatScript(code: string): Promise<{ code: string }> {
    const response = await api.post('/selenium-scripts/format', { code })
    return response.data
  }

  // 获取统计信息
  static async getStatistics(params?: {
    projectId?: number
    startDate?: string
    endDate?: string
  }): Promise<{
    totalScripts: number
    totalExecutions: number
    successRate: number
    avgDuration: number
    categoryStats: { [key: string]: number }
    dailyStats: { date: string, executions: number, successRate: number }[]
  }> {
    const response = await api.get('/selenium-scripts/statistics', { params })
    return response.data
  }

  // 批量执行脚本
  static async batchExecute(scriptIds: number[], config?: Partial<SeleniumConfig>): Promise<{ executionIds: string[] }> {
    const response = await api.post('/selenium-scripts/batch-execute', { scriptIds, config })
    return response.data
  }

  // 导出脚本
  static async exportScripts(scriptIds: number[], format: 'json' | 'zip' = 'json'): Promise<Blob> {
    const response = await api.post('/selenium-scripts/export', { scriptIds, format }, {
      responseType: 'blob'
    })
    return response.data
  }

  // 导入脚本
  static async importScripts(file: File): Promise<{ imported: number, failed: number, errors?: string[] }> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await api.post('/selenium-scripts/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // 复制脚本
  static async duplicateScript(id: number, name?: string): Promise<TestScript> {
    const response = await api.post(`/selenium-scripts/${id}/duplicate`, { name })
    return response.data
  }

  // 获取浏览器信息
  static async getBrowserInfo(): Promise<{
    browsers: { name: string, version: string, available: boolean }[]
    drivers: { browser: string, version: string, path: string }[]
  }> {
    const response = await api.get('/selenium-scripts/browser-info')
    return response.data
  }

  // 健康检查
  static async healthCheck(): Promise<{
    selenium: boolean
    browsers: { [key: string]: boolean }
    drivers: { [key: string]: boolean }
  }> {
    const response = await api.get('/selenium-scripts/health')
    return response.data
  }
}

// 默认导出
export default SeleniumTestService

// 工具函数
export const seleniumUtils = {
  // 生成基础脚本模板
  generateBasicTemplate: (config: Partial<SeleniumConfig> = {}) => {
    const browser = config.browser || 'chrome'
    const headless = config.headless ? ', options=chrome_options' : ''
    
    return `# Selenium自动化测试脚本
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
${config.headless ? 'from selenium.webdriver.chrome.options import Options' : ''}

def test_main():
    """主测试函数"""
    ${config.headless ? `
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    ` : ''}
    
    # 初始化WebDriver
    driver = webdriver.${browser.charAt(0).toUpperCase() + browser.slice(1)}(${headless})
    
    try:
        # 设置窗口大小
        driver.set_window_size(${config.windowWidth || 1920}, ${config.windowHeight || 1080})
        
        # 设置超时时间
        driver.implicitly_wait(${config.implicitWait || 10})
        driver.set_page_load_timeout(${config.pageLoadTimeout || 30})
        
        # 访问页面
        driver.get("${config.baseUrl || 'https://example.com'}")
        
        # 在这里添加您的测试代码
        print("测试开始执行...")
        
        # 示例：查找元素
        # element = driver.find_element(By.ID, "element-id")
        # element.click()
        
        # 示例：等待元素出现
        # wait = WebDriverWait(driver, 10)
        # element = wait.until(EC.presence_of_element_located((By.ID, "element-id")))
        
        print("测试执行完成")
        
    except Exception as e:
        print(f"测试执行失败: {e}")
        raise
    finally:
        # 关闭浏览器
        driver.quit()

if __name__ == "__main__":
    test_main()`
  },

  // 验证脚本语法
  validatePythonSyntax: (code: string): { valid: boolean, errors: string[] } => {
    const errors: string[] = []
    
    // 基础语法检查
    if (!code.trim()) {
      errors.push('脚本内容不能为空')
      return { valid: false, errors }
    }
    
    // 检查必要的导入
    if (!code.includes('from selenium import webdriver')) {
      errors.push('缺少必要的导入: from selenium import webdriver')
    }
    
    // 检查WebDriver初始化
    if (!code.includes('webdriver.')) {
      errors.push('未找到WebDriver初始化代码')
    }
    
    // 检查driver.quit()
    if (!code.includes('driver.quit()')) {
      errors.push('建议在finally块中添加driver.quit()以确保浏览器正确关闭')
    }
    
    return { valid: errors.length === 0, errors }
  },

  // 格式化执行时间
  formatDuration: (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}小时${minutes}分钟`
    }
  },

  // 获取状态颜色
  getStatusColor: (status: string): string => {
    const colors: { [key: string]: string } = {
      'running': 'warning',
      'success': 'success',
      'failed': 'danger',
      'stopped': 'info',
      '就绪': 'success',
      '草稿': 'info',
      '运行中': 'warning',
      '失败': 'danger'
    }
    return colors[status] || 'info'
  }
}
