using SqlSugar;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.DTOs;
using System.Linq.Expressions;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 需求文档仓储实现
/// 功能: 管理需求文档的数据访问操作
/// 支持: CRUD操作、搜索、状态管理、版本控制
/// </summary>
public class RequirementDocumentRepository : BaseRepository<RequirementDocument>, IRequirementDocumentRepository
{
    public RequirementDocumentRepository(ISqlSugarClient db, ILogger<RequirementDocumentRepository> logger) : base(db, logger)
    {
    }

    /// <summary>
    /// 根据项目获取需求文档列表
    /// </summary>
    public async Task<List<RequirementDocument>> GetRequirementDocumentsByProjectAsync(int projectId)
    {
        try
        {
            return await _db.Queryable<RequirementDocument>()
                .Where(x => x.ProjectId == projectId && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目获取需求文档列表失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 根据标题搜索需求文档
    /// </summary>
    public async Task<List<RequirementDocument>> GetByTitleAsync(string title)
    {
        try
        {
            return await _db.Queryable<RequirementDocument>()
                .Where(x => x.Title.Contains(title) && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据标题搜索需求文档失败，标题: {Title}", title);
            throw;
        }
    }

    /// <summary>
    /// 根据状态获取需求文档列表
    /// </summary>
    public async Task<List<RequirementDocument>> GetByStatusAsync(string status)
    {
        try
        {
            return await _db.Queryable<RequirementDocument>()
                .Where(x => x.Status == status && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取需求文档列表失败，状态: {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// 根据生成方式获取需求文档列表
    /// </summary>
    public async Task<List<RequirementDocument>> GetByGeneratedByAsync(string generatedBy)
    {
        try
        {
            return await _db.Queryable<RequirementDocument>()
                .Where(x => x.GeneratedBy == generatedBy && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据生成方式获取需求文档列表失败，生成方式: {GeneratedBy}", generatedBy);
            throw;
        }
    }

    /// <summary>
    /// 更新需求文档状态
    /// </summary>
    public async Task<bool> UpdateStatusAsync(int documentId, string status, int? updatedBy = null)
    {
        try
        {
            var updateColumns = new
            {
                Status = status,
                UpdatedTime = DateTime.Now,
                UpdatedBy = updatedBy
            };

            var result = await _db.Updateable<RequirementDocument>()
                .SetColumns(it => new RequirementDocument
                {
                    Status = updateColumns.Status,
                    UpdatedTime = updateColumns.UpdatedTime,
                    UpdatedBy = updateColumns.UpdatedBy
                })
                .Where(x => x.Id == documentId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新需求文档状态成功: {DocumentId}, 状态: {Status}", documentId, status);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新需求文档状态失败: {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// 更新需求文档内容
    /// </summary>
    public async Task<bool> UpdateContentAsync(int documentId, string content, int? updatedBy = null)
    {
        try
        {
            var updateColumns = new
            {
                Content = content,
                UpdatedTime = DateTime.Now,
                UpdatedBy = updatedBy
            };

            var result = await _db.Updateable<RequirementDocument>()
                .SetColumns(it => new RequirementDocument
                {
                    Content = updateColumns.Content,
                    UpdatedTime = updateColumns.UpdatedTime,
                    UpdatedBy = updateColumns.UpdatedBy
                })
                .Where(x => x.Id == documentId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新需求文档内容成功: {DocumentId}", documentId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新需求文档内容失败: {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// 搜索需求文档
    /// </summary>
    public async Task<ProjectManagement.Core.Interfaces.PagedResult<RequirementDocument>> SearchDocumentsAsync(
        string keyword,
        int? projectId = null,
        string? status = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<RequirementDocument>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x => x.Title.Contains(keyword) ||
                                        x.Content.Contains(keyword) ||
                                        (x.FunctionalRequirements != null && x.FunctionalRequirements.Contains(keyword)) ||
                                        (x.NonFunctionalRequirements != null && x.NonFunctionalRequirements.Contains(keyword)));
            }

            // 项目筛选
            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            // 状态筛选
            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(x => x.Status == status);
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var items = await query
                .OrderByDescending(x => x.CreatedTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new ProjectManagement.Core.Interfaces.PagedResult<RequirementDocument>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索需求文档失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    /// <summary>
    /// 获取需求文档统计信息
    /// </summary>
    public async Task<RequirementDocumentStatistics> GetDocumentStatisticsAsync(int? projectId = null)
    {
        try
        {
            var query = _db.Queryable<RequirementDocument>()
                .Where(x => !x.IsDeleted);

            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            var totalCount = await query.CountAsync();
            var draftCount = await query.Where(x => x.Status == "Draft").CountAsync();
            var reviewCount = await query.Where(x => x.Status == "Review").CountAsync();
            var approvedCount = await query.Where(x => x.Status == "Approved").CountAsync();
            var rejectedCount = await query.Where(x => x.Status == "Rejected").CountAsync();
            var aiGeneratedCount = await query.Where(x => x.GeneratedBy == "AI").CountAsync();

            return new RequirementDocumentStatistics
            {
                TotalDocuments = totalCount,
                DraftDocuments = draftCount,
                ReviewDocuments = reviewCount,
                ApprovedDocuments = approvedCount,
                AIGeneratedDocuments = aiGeneratedCount,
                ManualDocuments = totalCount - aiGeneratedCount,
                DocumentsByStatus = new Dictionary<string, int>
                {
                    ["Draft"] = draftCount,
                    ["Review"] = reviewCount,
                    ["Approved"] = approvedCount,
                    ["Rejected"] = rejectedCount
                },
                DocumentsByGeneratedBy = new Dictionary<string, int>
                {
                    ["AI"] = aiGeneratedCount,
                    ["Manual"] = totalCount - aiGeneratedCount
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求文档统计信息失败");
            throw;
        }
    }
}
