using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 向量数据库服务实现
    /// </summary>
    public class VectorDatabaseService : IVectorDatabaseService
    {
        private readonly ILogger<VectorDatabaseService> _logger;
        private readonly IAIService _aiService;
        private readonly string _databasePath;
        private readonly Dictionary<string, ProjectVector> _vectorStore;
        private readonly object _lockObject = new();

        // 向量维度（使用OpenAI text-embedding-ada-002的维度）
        private const int VectorDimensions = 1536;

        public VectorDatabaseService(ILogger<VectorDatabaseService> logger, IAIService aiService)
        {
            _logger = logger;
            _aiService = aiService;
            _databasePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                       "ProjectManagementAI", "VectorDatabase");
            _vectorStore = new Dictionary<string, ProjectVector>();
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("初始化向量数据库");

                // 创建数据库目录
                Directory.CreateDirectory(_databasePath);

                // 加载现有向量数据
                await LoadExistingVectorsAsync();

                _logger.LogInformation("向量数据库初始化完成，加载了 {Count} 个向量", _vectorStore.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向量数据库初始化失败");
                throw;
            }
        }

        public async Task<string> VectorizeAndStoreProjectAsync(object project)
        {
            try
            {
                var projectId = Guid.NewGuid().ToString();
                var projectName = ((dynamic)project).Name?.ToString() ?? "Unknown";
                var description = ((dynamic)project).Description?.ToString() ?? "";

                // 构建用于向量化的文本
                var textToVectorize = BuildProjectText(projectName, description, "", "");

                // 生成向量
                var vector = await GenerateEmbeddingAsync(textToVectorize);

                // 创建项目向量对象
                var projectVector = new ProjectVector
                {
                    Id = projectId,
                    ProjectName = projectName,
                    ProjectType = "Unknown",
                    Description = description,
                    TechnologyStack = "",
                    Vector = vector,
                    Metadata = new Dictionary<string, object>
                    {
                        { "Source", "MockProject" },
                        { "OriginalId", ((dynamic)project).Id?.ToString() ?? "" }
                    },
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // 存储向量
                lock (_lockObject)
                {
                    _vectorStore[projectId] = projectVector;
                }

                // 持久化到磁盘
                await SaveVectorAsync(projectVector);

                _logger.LogInformation("项目 {ProjectName} 向量化完成，ID: {ProjectId}", projectName, projectId);
                return projectId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "项目向量化失败");
                throw;
            }
        }

        public async Task<string> VectorizeAndStoreLocalProjectAsync(LocalProject localProject)
        {
            try
            {
                var projectId = Guid.NewGuid().ToString();

                // 构建用于向量化的文本
                var technologyStack = string.Join(", ", localProject.TechnologyStack);
                var textToVectorize = BuildProjectText(
                    localProject.Name,
                    localProject.Description,
                    localProject.Type.ToString(),
                    technologyStack
                );

                // 生成向量
                var vector = await GenerateEmbeddingAsync(textToVectorize);

                // 创建项目向量对象
                var projectVector = new ProjectVector
                {
                    Id = projectId,
                    ProjectName = localProject.Name,
                    ProjectType = localProject.Type.ToString(),
                    Description = localProject.Description,
                    TechnologyStack = technologyStack,
                    Vector = vector,
                    Metadata = new Dictionary<string, object>
                    {
                        { "Source", "LocalProject" },
                        { "Path", localProject.Path },
                        { "Language", localProject.Language },
                        { "Framework", localProject.Framework },
                        { "CodeLines", localProject.Statistics.CodeLines },
                        { "ComplexityScore", localProject.ComplexityScore },
                        { "QualityScore", localProject.QualityScore },
                        { "LastModified", localProject.LastModified }
                    },
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // 存储向量
                lock (_lockObject)
                {
                    _vectorStore[projectId] = projectVector;
                }

                // 持久化到磁盘
                await SaveVectorAsync(projectVector);

                _logger.LogInformation("本地项目 {ProjectName} 向量化完成，ID: {ProjectId}", localProject.Name, projectId);
                return projectId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "本地项目向量化失败");
                throw;
            }
        }

        public async Task<IEnumerable<VectorSearchResult>> SearchSimilarProjectsAsync(string queryText, int topK = 10, double threshold = 0.7)
        {
            try
            {
                _logger.LogInformation("搜索相似项目，查询: {Query}", queryText);

                // 将查询文本向量化
                var queryVector = await GenerateEmbeddingAsync(queryText);

                // 计算相似度并排序
                var results = new List<VectorSearchResult>();

                lock (_lockObject)
                {
                    foreach (var kvp in _vectorStore)
                    {
                        var similarity = CalculateCosineSimilarity(queryVector, kvp.Value.Vector);
                        
                        if (similarity >= threshold)
                        {
                            results.Add(new VectorSearchResult
                            {
                                ProjectId = kvp.Value.Id,
                                ProjectName = kvp.Value.ProjectName,
                                ProjectType = kvp.Value.ProjectType,
                                Description = kvp.Value.Description,
                                SimilarityScore = similarity,
                                Metadata = kvp.Value.Metadata
                            });
                        }
                    }
                }

                // 按相似度降序排序并返回前K个结果
                var topResults = results
                    .OrderByDescending(r => r.SimilarityScore)
                    .Take(topK)
                    .ToList();

                _logger.LogInformation("找到 {Count} 个相似项目", topResults.Count);
                return topResults;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索相似项目失败");
                throw;
            }
        }

        public async Task<IEnumerable<VectorSearchResult>> FindSimilarProjectsByIdAsync(string projectId, int topK = 10, double threshold = 0.7)
        {
            try
            {
                ProjectVector? targetProject;
                lock (_lockObject)
                {
                    if (!_vectorStore.TryGetValue(projectId, out targetProject))
                    {
                        _logger.LogWarning("项目 {ProjectId} 不存在", projectId);
                        return Enumerable.Empty<VectorSearchResult>();
                    }
                }

                // 使用目标项目的描述作为查询
                var queryText = $"{targetProject.ProjectName} {targetProject.Description} {targetProject.TechnologyStack}";
                var results = await SearchSimilarProjectsAsync(queryText, topK + 1, threshold);

                // 排除自身
                return results.Where(r => r.ProjectId != projectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "基于项目ID搜索相似项目失败");
                throw;
            }
        }

        public async Task<ProjectVector?> GetProjectVectorAsync(string projectId)
        {
            await Task.CompletedTask;
            
            lock (_lockObject)
            {
                return _vectorStore.TryGetValue(projectId, out var vector) ? vector : null;
            }
        }

        public async Task UpdateProjectVectorAsync(string projectId, object project)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_vectorStore.ContainsKey(projectId))
                    {
                        _logger.LogWarning("项目 {ProjectId} 不存在，无法更新", projectId);
                        return;
                    }
                }

                // 重新向量化并更新
                await DeleteProjectVectorAsync(projectId);
                await VectorizeAndStoreProjectAsync(project);

                _logger.LogInformation("项目 {ProjectId} 向量更新完成", projectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新项目向量失败");
                throw;
            }
        }

        public async Task DeleteProjectVectorAsync(string projectId)
        {
            try
            {
                lock (_lockObject)
                {
                    _vectorStore.Remove(projectId);
                }

                // 删除磁盘文件
                var filePath = Path.Combine(_databasePath, $"{projectId}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                _logger.LogInformation("项目 {ProjectId} 向量删除完成", projectId);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除项目向量失败");
                throw;
            }
        }

        public async Task<VectorDatabaseStats> GetDatabaseStatsAsync()
        {
            await Task.CompletedTask;

            lock (_lockObject)
            {
                var stats = new VectorDatabaseStats
                {
                    TotalVectors = _vectorStore.Count,
                    VectorDimensions = VectorDimensions,
                    LastUpdated = DateTime.UtcNow
                };

                // 计算项目类型分布
                foreach (var vector in _vectorStore.Values)
                {
                    var projectType = vector.ProjectType;
                    if (!stats.ProjectTypeDistribution.ContainsKey(projectType))
                        stats.ProjectTypeDistribution[projectType] = 0;
                    stats.ProjectTypeDistribution[projectType]++;
                }

                // 计算技术栈分布
                foreach (var vector in _vectorStore.Values)
                {
                    var technologies = vector.TechnologyStack.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var tech in technologies)
                    {
                        var trimmedTech = tech.Trim();
                        if (!string.IsNullOrEmpty(trimmedTech))
                        {
                            if (!stats.TechnologyDistribution.ContainsKey(trimmedTech))
                                stats.TechnologyDistribution[trimmedTech] = 0;
                            stats.TechnologyDistribution[trimmedTech]++;
                        }
                    }
                }

                // 计算数据库大小
                try
                {
                    var directoryInfo = new DirectoryInfo(_databasePath);
                    if (directoryInfo.Exists)
                    {
                        stats.DatabaseSizeBytes = directoryInfo.GetFiles("*.json")
                            .Sum(file => file.Length);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "计算数据库大小失败");
                }

                return stats;
            }
        }

        public async Task<BatchVectorizationResult> BatchVectorizeProjectsAsync(IEnumerable<object> projects)
        {
            var result = new BatchVectorizationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("开始批量向量化项目");

                foreach (var project in projects)
                {
                    try
                    {
                        var vectorId = await VectorizeAndStoreProjectAsync(project);
                        result.VectorIds.Add(vectorId);
                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.FailureCount++;
                        result.Errors.Add($"项目向量化失败: {ex.Message}");
                        _logger.LogWarning(ex, "单个项目向量化失败");
                    }
                }

                result.Duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("批量向量化完成，成功: {Success}，失败: {Failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量向量化失败");
                throw;
            }
        }

        public async Task<ClusterAnalysisResult> PerformClusterAnalysisAsync(int clusterCount = 5)
        {
            try
            {
                _logger.LogInformation("开始执行聚类分析，聚类数: {ClusterCount}", clusterCount);

                // 简化的K-means聚类实现
                var vectors = new List<ProjectVector>();
                lock (_lockObject)
                {
                    vectors.AddRange(_vectorStore.Values);
                }

                if (vectors.Count < clusterCount)
                {
                    clusterCount = Math.Max(1, vectors.Count);
                }

                var clusters = await PerformKMeansClusteringAsync(vectors, clusterCount);

                var result = new ClusterAnalysisResult
                {
                    ClusterCount = clusterCount,
                    Clusters = clusters,
                    SilhouetteScore = CalculateSilhouetteScore(vectors, clusters),
                    ClusterMetrics = new Dictionary<string, object>
                    {
                        { "TotalProjects", vectors.Count },
                        { "AverageClusterSize", vectors.Count / (double)clusterCount }
                    }
                };

                _logger.LogInformation("聚类分析完成，轮廓系数: {Score:F3}", result.SilhouetteScore);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "聚类分析失败");
                throw;
            }
        }

        #region Private Methods

        private string BuildProjectText(string name, string description, string projectType, string technologyStack)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"项目名称: {name}");
            sb.AppendLine($"项目描述: {description}");
            if (!string.IsNullOrEmpty(projectType))
                sb.AppendLine($"项目类型: {projectType}");
            if (!string.IsNullOrEmpty(technologyStack))
                sb.AppendLine($"技术栈: {technologyStack}");
            
            return sb.ToString();
        }

        private async Task<float[]> GenerateEmbeddingAsync(string text)
        {
            try
            {
                // 这里应该调用真实的AI服务生成向量
                // 目前使用模拟向量
                await Task.Delay(10); // 模拟API调用延迟

                var random = new Random(text.GetHashCode()); // 使用文本哈希作为种子确保一致性
                var vector = new float[VectorDimensions];
                
                for (int i = 0; i < VectorDimensions; i++)
                {
                    vector[i] = (float)(random.NextDouble() * 2 - 1); // -1 到 1 之间的随机数
                }

                // 归一化向量
                var magnitude = Math.Sqrt(vector.Sum(x => x * x));
                for (int i = 0; i < vector.Length; i++)
                {
                    vector[i] = (float)(vector[i] / magnitude);
                }

                return vector;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成向量失败");
                throw;
            }
        }

        private double CalculateCosineSimilarity(float[] vector1, float[] vector2)
        {
            if (vector1.Length != vector2.Length)
                throw new ArgumentException("向量维度不匹配");

            double dotProduct = 0;
            double magnitude1 = 0;
            double magnitude2 = 0;

            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                magnitude1 += vector1[i] * vector1[i];
                magnitude2 += vector2[i] * vector2[i];
            }

            magnitude1 = Math.Sqrt(magnitude1);
            magnitude2 = Math.Sqrt(magnitude2);

            if (magnitude1 == 0 || magnitude2 == 0)
                return 0;

            return dotProduct / (magnitude1 * magnitude2);
        }

        private async Task SaveVectorAsync(ProjectVector vector)
        {
            try
            {
                var filePath = Path.Combine(_databasePath, $"{vector.Id}.json");
                var json = JsonSerializer.Serialize(vector, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                await File.WriteAllTextAsync(filePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "保存向量到磁盘失败");
            }
        }

        private async Task LoadExistingVectorsAsync()
        {
            try
            {
                if (!Directory.Exists(_databasePath))
                    return;

                var files = Directory.GetFiles(_databasePath, "*.json");
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var vector = JsonSerializer.Deserialize<ProjectVector>(json);
                        if (vector != null)
                        {
                            _vectorStore[vector.Id] = vector;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "加载向量文件失败: {File}", file);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "加载现有向量失败");
            }
        }

        private async Task<List<ProjectCluster>> PerformKMeansClusteringAsync(List<ProjectVector> vectors, int clusterCount)
        {
            await Task.CompletedTask;

            // 简化的K-means实现
            var clusters = new List<ProjectCluster>();
            var random = new Random();

            // 初始化聚类中心
            for (int i = 0; i < clusterCount; i++)
            {
                var centroid = new float[VectorDimensions];
                for (int j = 0; j < VectorDimensions; j++)
                {
                    centroid[j] = (float)(random.NextDouble() * 2 - 1);
                }

                clusters.Add(new ProjectCluster
                {
                    ClusterId = i,
                    ClusterName = $"聚类 {i + 1}",
                    Centroid = centroid,
                    ProjectIds = new List<string>()
                });
            }

            // 简单分配：将每个项目分配给最近的聚类
            foreach (var vector in vectors)
            {
                var bestCluster = clusters
                    .OrderByDescending(c => CalculateCosineSimilarity(vector.Vector, c.Centroid))
                    .First();
                
                bestCluster.ProjectIds.Add(vector.Id);
            }

            // 计算聚类特征
            foreach (var cluster in clusters)
            {
                var clusterVectors = cluster.ProjectIds
                    .Select(id => vectors.First(v => v.Id == id))
                    .ToList();

                var projectTypes = clusterVectors
                    .GroupBy(v => v.ProjectType)
                    .OrderByDescending(g => g.Count())
                    .Select(g => g.Key)
                    .ToList();

                cluster.Characteristics = new Dictionary<string, object>
                {
                    { "ProjectCount", cluster.ProjectIds.Count },
                    { "DominantProjectType", projectTypes.FirstOrDefault() ?? "Unknown" },
                    { "ProjectTypes", projectTypes }
                };

                if (projectTypes.Any())
                {
                    cluster.ClusterName = $"{projectTypes.First()} 项目群";
                }
            }

            return clusters;
        }

        private double CalculateSilhouetteScore(List<ProjectVector> vectors, List<ProjectCluster> clusters)
        {
            // 简化的轮廓系数计算
            if (clusters.Count <= 1) return 0;

            var scores = new List<double>();
            
            foreach (var vector in vectors)
            {
                var ownCluster = clusters.First(c => c.ProjectIds.Contains(vector.Id));
                var ownClusterVectors = ownCluster.ProjectIds
                    .Where(id => id != vector.Id)
                    .Select(id => vectors.First(v => v.Id == id))
                    .ToList();

                if (!ownClusterVectors.Any()) continue;

                // 计算簇内平均距离
                var intraDistance = ownClusterVectors
                    .Average(v => 1 - CalculateCosineSimilarity(vector.Vector, v.Vector));

                // 计算到最近其他簇的平均距离
                var otherClusters = clusters.Where(c => c.ClusterId != ownCluster.ClusterId);
                var minInterDistance = otherClusters
                    .Select(c => c.ProjectIds
                        .Select(id => vectors.First(v => v.Id == id))
                        .Average(v => 1 - CalculateCosineSimilarity(vector.Vector, v.Vector)))
                    .Min();

                var silhouette = (minInterDistance - intraDistance) / Math.Max(intraDistance, minInterDistance);
                scores.Add(silhouette);
            }

            return scores.Any() ? scores.Average() : 0;
        }

        #endregion
    }
}
