using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// Prompt分类仓储接口
/// </summary>
public interface IPromptCategoryRepository : IRepository<PromptCategory>
{
    /// <summary>
    /// 获取所有启用的分类（包含层级结构）
    /// </summary>
    Task<List<PromptCategory>> GetEnabledCategoriesAsync();

    /// <summary>
    /// 获取根分类列表
    /// </summary>
    Task<List<PromptCategory>> GetRootCategoriesAsync();

    /// <summary>
    /// 获取子分类列表
    /// </summary>
    Task<List<PromptCategory>> GetChildCategoriesAsync(int parentId);

    /// <summary>
    /// 获取分类树形结构
    /// </summary>
    Task<List<PromptCategory>> GetCategoryTreeAsync();

    /// <summary>
    /// 更新分类模板数量
    /// </summary>
    Task UpdateTemplateCountAsync(int categoryId, int count);

    /// <summary>
    /// 获取分类路径
    /// </summary>
    Task<List<PromptCategory>> GetCategoryPathAsync(int categoryId);
}
