#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端项目编译UI界面
支持Vue、React、Angular、TypeScript等前端项目的编译检测
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
from pathlib import Path
from datetime import datetime
import json

from frontend_build_checker import FrontendBuildChecker

class FrontendBuildUI:
    """前端编译UI界面"""

    def __init__(self, parent=None, api_client=None):
        """初始化界面"""
        if parent is None:
            self.root = tk.Tk()
            self.root.title("编译前端项目")
            self.root.geometry("1000x700")
            self.is_embedded = False
        elif isinstance(parent, (tk.Frame, ttk.Frame)):
            # 嵌入式模式
            self.root = parent
            self.is_embedded = True
        else:
            self.root = tk.Toplevel(parent)
            self.root.title("编译前端项目")
            self.root.geometry("1000x700")
            self.is_embedded = False

        # API客户端
        self.api_client = api_client

        # 初始化变量
        self.project_path = tk.StringVar()
        self.auto_detection = tk.BooleanVar(value=False)
        self.smart_mode = tk.BooleanVar(value=True)  # 默认使用智能模式
        self.build_history = []
        self.frontend_checker = None

        # 自动刷新相关变量
        self.auto_refresh = tk.BooleanVar(value=False)
        self.auto_refresh_thread = None
        self.auto_refresh_interval = tk.IntVar(value=10)  # 默认10秒

        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """设置界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 项目设置区域
        self.create_project_section(main_frame)

        # 编译控制区域
        self.create_build_control_section(main_frame)

        # 结果显示区域
        self.create_results_section(main_frame)

        # 状态栏
        self.create_status_bar(main_frame)

    def create_project_section(self, parent):
        """创建项目设置区域"""
        project_frame = ttk.LabelFrame(parent, text="前端项目设置", padding=10)
        project_frame.pack(fill=tk.X, pady=(0, 10))

        # 项目路径
        ttk.Label(project_frame, text="项目路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        path_entry = ttk.Entry(project_frame, textvariable=self.project_path, width=60)
        path_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 5))
        
        ttk.Button(project_frame, text="浏览...", command=self.browse_project_path).grid(row=0, column=2)

        # 项目信息
        info_frame = ttk.Frame(project_frame)
        info_frame.grid(row=1, column=0, columnspan=3, sticky=tk.EW, pady=(10, 0))

        self.project_info_label = ttk.Label(info_frame, text="请选择前端项目路径", foreground="gray")
        self.project_info_label.pack(side=tk.LEFT)

        # 配置列权重
        project_frame.columnconfigure(1, weight=1)

        # 绑定路径变化事件
        self.project_path.trace('w', self.on_project_path_changed)

    def create_build_control_section(self, parent):
        """创建编译控制区域"""
        control_frame = ttk.LabelFrame(parent, text="编译控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        self.build_button = ttk.Button(button_frame, text="🔨 检测编译状态", command=self.check_build_status)
        self.build_button.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(button_frame, text="🏗️ 执行构建", command=self.run_build).pack(side=tk.LEFT, padx=(0, 5))

        # API相关按钮
        ttk.Button(button_frame, text="🔄 从API获取错误", command=self.load_errors_from_api).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🧹 清空结果", command=self.clear_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 保存报告", command=self.save_report).pack(side=tk.LEFT, padx=(0, 5))

        # 选项区域
        options_frame = ttk.Frame(control_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Checkbutton(options_frame, text="智能检测模式（自动避开开发服务器）", 
                       variable=self.smart_mode).pack(side=tk.LEFT)

        ttk.Checkbutton(options_frame, text="自动检测", 
                       variable=self.auto_detection,
                       command=self.toggle_auto_detection).pack(side=tk.LEFT, padx=(20, 0))

        # 进度条
        self.progress_bar = ttk.Progressbar(options_frame, mode='indeterminate', length=200)

        self.auto_status_label = ttk.Label(options_frame, text="", foreground="green")
        self.auto_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # API自动刷新选项
        if self.api_client:
            api_options_frame = ttk.Frame(control_frame)
            api_options_frame.pack(fill=tk.X, pady=(5, 0))

            ttk.Checkbutton(api_options_frame, text="自动从API刷新错误", variable=self.auto_refresh,
                           command=self.toggle_auto_refresh).pack(side=tk.LEFT)

            ttk.Label(api_options_frame, text="间隔:").pack(side=tk.LEFT, padx=(10, 5))

            refresh_spinbox = ttk.Spinbox(api_options_frame, from_=5, to=120, width=8,
                                        textvariable=self.auto_refresh_interval)
            refresh_spinbox.pack(side=tk.LEFT)

            ttk.Label(api_options_frame, text="秒").pack(side=tk.LEFT, padx=(5, 10))

            self.auto_refresh_status_label = ttk.Label(api_options_frame, text="", foreground="green")
            self.auto_refresh_status_label.pack(side=tk.LEFT, padx=(10, 0))

    def create_results_section(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="检测结果", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建Notebook用于不同类型的结果显示
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)

        # 1. 错误详情选项卡
        errors_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(errors_frame, text="❌ 错误详情")
        self.create_errors_display(errors_frame)

        # 2. 警告详情选项卡
        warnings_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(warnings_frame, text="⚠️ 警告详情")
        self.create_warnings_display(warnings_frame)

    def create_errors_display(self, parent):
        """创建错误显示"""
        # 错误列表
        columns = ('文件', '行号', '列号', '错误代码', '错误信息')
        self.errors_tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

        # 设置列标题和宽度
        self.errors_tree.heading('文件', text='文件')
        self.errors_tree.heading('行号', text='行号')
        self.errors_tree.heading('列号', text='列号')
        self.errors_tree.heading('错误代码', text='错误代码')
        self.errors_tree.heading('错误信息', text='错误信息')

        self.errors_tree.column('文件', width=200)
        self.errors_tree.column('行号', width=60)
        self.errors_tree.column('列号', width=60)
        self.errors_tree.column('错误代码', width=100)
        self.errors_tree.column('错误信息', width=400)

        # 滚动条
        errors_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.errors_tree.yview)
        self.errors_tree.configure(yscrollcommand=errors_scroll.set)

        # 布局
        self.errors_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        errors_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_warnings_display(self, parent):
        """创建警告显示"""
        # 警告列表
        columns = ('文件', '行号', '列号', '警告代码', '警告信息')
        self.warnings_tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

        # 设置列标题和宽度
        self.warnings_tree.heading('文件', text='文件')
        self.warnings_tree.heading('行号', text='行号')
        self.warnings_tree.heading('列号', text='列号')
        self.warnings_tree.heading('警告代码', text='警告代码')
        self.warnings_tree.heading('警告信息', text='警告信息')

        self.warnings_tree.column('文件', width=200)
        self.warnings_tree.column('行号', width=60)
        self.warnings_tree.column('列号', width=60)
        self.warnings_tree.column('警告代码', width=100)
        self.warnings_tree.column('警告信息', width=400)

        # 滚动条
        warnings_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.warnings_tree.yview)
        self.warnings_tree.configure(yscrollcommand=warnings_scroll.set)

        # 布局
        self.warnings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        warnings_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)

        self.status_label = ttk.Label(status_frame, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.build_time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
        self.build_time_label.pack(side=tk.RIGHT, padx=(5, 0))

    def browse_project_path(self):
        """浏览项目路径"""
        initial_dir = self.project_path.get() or str(Path.cwd())
        
        directory = filedialog.askdirectory(
            title="选择前端项目目录",
            initialdir=initial_dir
        )
        
        if directory:
            self.project_path.set(directory)

    def on_project_path_changed(self, *args):
        """项目路径变化事件"""
        path = self.project_path.get().strip()
        if not path:
            self.project_info_label.config(text="请选择前端项目路径", foreground="gray")
            return

        project_path = Path(path)
        if not project_path.exists():
            self.project_info_label.config(text="路径不存在", foreground="red")
            return

        package_json = project_path / "package.json"
        if not package_json.exists():
            self.project_info_label.config(text="不是有效的前端项目（缺少package.json）", foreground="red")
            return

        try:
            # 创建检测器并获取项目信息
            self.frontend_checker = FrontendBuildChecker(path)
            project_type = self.frontend_checker.project_type
            
            self.project_info_label.config(
                text=f"项目类型: {project_type.upper()}", 
                foreground="green"
            )
        except Exception as e:
            self.project_info_label.config(text=f"项目分析失败: {e}", foreground="red")

    def check_build_status(self):
        """检测编译状态"""
        if not self.validate_project():
            return

        # 显示进度
        self.status_label.config(text="🔍 正在检测编译状态...")
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 10))
        self.progress_bar.start(10)
        self.build_button.config(state="disabled")
        self.root.update()

        def check_thread():
            try:
                # 选择检测模式
                check_mode = "smart" if self.smart_mode.get() else "check"
                result = self.frontend_checker.check_build_status(check_mode)
                
                # 更新UI
                self.root.after(0, lambda: self.handle_check_result(result))
                
            except Exception as e:
                self.root.after(0, lambda: self.handle_check_error(str(e)))
            finally:
                self.root.after(0, self.enable_build_button)

        threading.Thread(target=check_thread, daemon=True).start()

    def run_build(self):
        """执行构建"""
        if not self.validate_project():
            return

        # 显示进度
        self.status_label.config(text="🏗️ 正在执行构建...")
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 10))
        self.progress_bar.start(10)
        self.build_button.config(state="disabled")
        self.root.update()

        def build_thread():
            try:
                result = self.frontend_checker.check_build_status("build")
                
                # 更新UI
                self.root.after(0, lambda: self.handle_check_result(result))
                
            except Exception as e:
                self.root.after(0, lambda: self.handle_check_error(str(e)))
            finally:
                self.root.after(0, self.enable_build_button)

        threading.Thread(target=build_thread, daemon=True).start()

    def validate_project(self):
        """验证项目设置"""
        path = self.project_path.get().strip()
        if not path:
            messagebox.showwarning("警告", "请先选择项目路径")
            return False

        if not self.frontend_checker:
            messagebox.showerror("错误", "项目检测器未初始化")
            return False

        return True

    def handle_check_result(self, result):
        """处理检测结果"""
        build_time = result.get('build_time', 0)
        self.build_time_label.config(text=f"检测时间: {build_time:.2f}s")

        if result['success']:
            self.status_label.config(text="✅ 检测成功")
        else:
            error_count = result.get('error_count', 0)
            warning_count = result.get('warning_count', 0)
            self.status_label.config(text=f"❌ 检测完成: {error_count}个错误, {warning_count}个警告")

        # 更新显示
        self.update_errors_display(result.get('errors', []))
        self.update_warnings_display(result.get('warnings', []))

        # 保存到历史
        self.build_history.append({
            'timestamp': datetime.now(),
            'result': result
        })

    def handle_check_error(self, error_message):
        """处理检测错误"""
        self.status_label.config(text=f"❌ 检测异常: {error_message}")
        messagebox.showerror("检测异常", f"检测过程中发生异常:\n{error_message}")

    def enable_build_button(self):
        """重新启用构建按钮"""
        self.build_button.config(state="normal")
        self.progress_bar.stop()
        self.progress_bar.pack_forget()

    def update_errors_display(self, errors):
        """更新错误显示"""
        # 清空现有项目
        for item in self.errors_tree.get_children():
            self.errors_tree.delete(item)

        # 添加错误
        for error in errors:
            self.errors_tree.insert('', tk.END, values=(
                Path(error.get('file', '')).name,
                error.get('line', ''),
                error.get('column', ''),
                error.get('code', ''),
                error.get('message', '')[:100] + '...' if len(error.get('message', '')) > 100 else error.get('message', '')
            ))

    def update_warnings_display(self, warnings):
        """更新警告显示"""
        # 清空现有项目
        for item in self.warnings_tree.get_children():
            self.warnings_tree.delete(item)

        # 添加警告
        for warning in warnings:
            self.warnings_tree.insert('', tk.END, values=(
                Path(warning.get('file', '')).name,
                warning.get('line', ''),
                warning.get('column', ''),
                warning.get('code', ''),
                warning.get('message', '')[:100] + '...' if len(warning.get('message', '')) > 100 else warning.get('message', '')
            ))

    def clear_results(self):
        """清空结果"""
        if messagebox.askyesno("确认", "确定要清空所有检测结果吗？"):
            for item in self.errors_tree.get_children():
                self.errors_tree.delete(item)

            for item in self.warnings_tree.get_children():
                self.warnings_tree.delete(item)

            self.status_label.config(text="结果已清空")

    def save_report(self):
        """保存检测报告"""
        if not self.build_history:
            messagebox.showwarning("警告", "没有检测结果可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存检测报告",
            defaultextension=".txt",
            filetypes=[
                ("Text Files", "*.txt"),
                ("JSON Files", "*.json"),
                ("All Files", "*.*")
            ]
        )

        if file_path:
            try:
                latest_result = self.build_history[-1]['result']
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"前端项目检测报告\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"项目路径: {self.project_path.get()}\n")
                    f.write(f"项目类型: {self.frontend_checker.project_type}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    f.write(f"检测结果: {'成功' if latest_result['success'] else '失败'}\n")
                    f.write(f"错误数量: {latest_result.get('error_count', 0)}\n")
                    f.write(f"警告数量: {latest_result.get('warning_count', 0)}\n")
                    f.write(f"检测时间: {latest_result.get('build_time', 0):.2f}秒\n\n")
                    
                    if latest_result.get('errors'):
                        f.write("错误详情:\n")
                        for i, error in enumerate(latest_result['errors'], 1):
                            f.write(f"{i}. {error.get('file', '')}({error.get('line', '')},{error.get('column', '')}): {error.get('message', '')}\n")
                        f.write("\n")
                    
                    if latest_result.get('warnings'):
                        f.write("警告详情:\n")
                        for i, warning in enumerate(latest_result['warnings'], 1):
                            f.write(f"{i}. {warning.get('file', '')}({warning.get('line', '')},{warning.get('column', '')}): {warning.get('message', '')}\n")

                messagebox.showinfo("成功", f"检测报告已保存到: {file_path}")
                self.status_label.config(text="检测报告已保存")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def toggle_auto_detection(self):
        """切换自动检测"""
        if self.auto_detection.get():
            if not self.project_path.get().strip():
                messagebox.showwarning("警告", "请先设置项目路径")
                self.auto_detection.set(False)
                return
            
            self.auto_status_label.config(text="🔄 自动检测已开启", foreground="green")
            messagebox.showinfo("自动检测", "自动检测已开启")
        else:
            self.auto_status_label.config(text="⏸️ 自动检测已关闭", foreground="orange")

    def load_settings(self):
        """加载设置"""
        # 设置默认前端项目路径
        default_paths = [
            r"D:\Projects\ProjectManagement\Frontend",
            r"D:\Projects\ProjectManagement\Web"
        ]

        for default_path in default_paths:
            if Path(default_path).exists() and (Path(default_path) / "package.json").exists():
                self.project_path.set(default_path)
                break

    def save_settings(self):
        """保存设置"""
        pass

    # API相关方法
    def load_errors_from_api(self):
        """从API获取前端编译错误"""
        if not self.api_client:
            messagebox.showwarning("警告", "API客户端未初始化")
            return

        def load_thread():
            try:
                self.status_label.config(text="🔄 正在从API获取前端错误...")

                # 获取前端编译错误
                result = self.api_client.get_compilation_errors(
                    project_type="Frontend",
                    page_size=1000
                )

                if result and 'items' in result:
                    errors = result['items']
                    self.root.after(0, lambda: self.display_api_errors(errors))
                    self.root.after(0, lambda: self.status_label.config(text=f"✅ 从API获取到 {len(errors)} 个前端错误"))
                else:
                    self.root.after(0, lambda: self.status_label.config(text="⚠️ API返回数据格式错误"))

            except Exception as e:
                error_msg = f"❌ 从API获取前端错误失败: {str(e)}"
                self.root.after(0, lambda: self.status_label.config(text=error_msg))
                print(error_msg)

        threading.Thread(target=load_thread, daemon=True).start()

    def display_api_errors(self, errors):
        """显示从API获取的前端错误"""
        try:
            # 清空现有结果
            self.clear_results()

            # 分类错误和警告
            error_list = []
            warning_list = []

            for error in errors:
                severity = error.get('severity', 'Error')
                error_info = {
                    'file': error.get('filePath', ''),
                    'line': error.get('lineNumber', 0),
                    'column': error.get('columnNumber', 0),
                    'message': error.get('message', ''),
                    'code': error.get('code', ''),
                    'severity': severity,
                    'time': error.get('compilationTime', ''),
                    'source': 'API'
                }

                if severity.lower() == 'error':
                    error_list.append(error_info)
                else:
                    warning_list.append(error_info)

            # 显示错误
            self.display_errors_in_tree(error_list)
            self.display_warnings_in_tree(warning_list)

            # 更新统计
            self.update_error_stats(len(error_list), len(warning_list))

        except Exception as e:
            print(f"❌ 显示API前端错误失败: {e}")

    def display_errors_in_tree(self, errors):
        """在错误树中显示错误"""
        try:
            for error in errors:
                file_name = Path(error['file']).name if error['file'] else ''
                self.errors_tree.insert('', 'end', values=(
                    file_name,
                    error['line'],
                    error['column'],
                    error['code'],
                    error['message']
                ))
        except Exception as e:
            print(f"❌ 显示错误到树控件失败: {e}")

    def display_warnings_in_tree(self, warnings):
        """在警告树中显示警告"""
        try:
            for warning in warnings:
                file_name = Path(warning['file']).name if warning['file'] else ''
                self.warnings_tree.insert('', 'end', values=(
                    file_name,
                    warning['line'],
                    warning['column'],
                    warning['code'],
                    warning['message']
                ))
        except Exception as e:
            print(f"❌ 显示警告到树控件失败: {e}")

    def update_error_stats(self, error_count, warning_count):
        """更新错误统计"""
        try:
            stats_text = f"错误: {error_count}, 警告: {warning_count}"
            if hasattr(self, 'stats_label'):
                self.stats_label.config(text=stats_text)
        except Exception as e:
            print(f"❌ 更新错误统计失败: {e}")

    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if not self.api_client:
            messagebox.showwarning("警告", "API客户端未初始化")
            self.auto_refresh.set(False)
            return

        if self.auto_refresh.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """开始自动刷新前端错误"""
        if not self.api_client:
            return

        self.auto_refresh_status_label.config(text="🔄 自动刷新中...", foreground="green")

        def refresh_loop():
            while self.auto_refresh.get():
                try:
                    # 从API获取前端错误
                    result = self.api_client.get_compilation_errors(
                        project_type="Frontend",
                        page_size=1000
                    )

                    if result and 'items' in result:
                        errors = result['items']
                        self.root.after(0, lambda: self.display_api_errors(errors))

                    # 等待指定间隔
                    for i in range(self.auto_refresh_interval.get()):
                        if not self.auto_refresh.get():
                            break
                        time.sleep(1)

                except Exception as e:
                    print(f"❌ 前端自动刷新失败: {e}")
                    self.root.after(0, self.stop_auto_refresh)
                    break

        self.auto_refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        self.auto_refresh_thread.start()

    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh.set(False)
        if hasattr(self, 'auto_refresh_status_label'):
            self.auto_refresh_status_label.config(text="", foreground="black")
        if self.auto_refresh_thread:
            self.auto_refresh_thread = None

    def run(self):
        """运行界面"""
        if not self.is_embedded:
            try:
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
                self.root.mainloop()
            except KeyboardInterrupt:
                print("程序被用户中断")

    def on_closing(self):
        """关闭事件"""
        # 停止自动刷新
        if hasattr(self, 'auto_refresh') and self.auto_refresh.get():
            self.stop_auto_refresh()

        self.save_settings()
        self.root.destroy()

    def get_current_errors(self):
        """获取当前错误列表

        Returns:
            list: 当前错误信息列表，每个错误包含file, line, column, code, message等字段
        """
        try:
            errors = []

            # 遍历错误TreeView中的所有项目
            for item in self.errors_tree.get_children():
                values = self.errors_tree.item(item, 'values')
                if len(values) >= 5:  # 确保有足够的列
                    # 列顺序: ('文件', '行号', '列号', '错误代码', '错误信息')
                    error_info = {
                        'file': values[0],
                        'line': values[1],
                        'column': values[2],
                        'code': values[3],
                        'message': values[4]
                    }
                    errors.append(error_info)

            return errors

        except Exception as e:
            print(f"❌ 获取当前前端错误失败: {e}")
            return []

def main():
    """主函数"""
    try:
        app = FrontendBuildUI()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
