using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Prompt模板实体 - 用于管理AI提示词模板
/// 功能: 存储和管理各种AI任务的提示词模板
/// 支持: 模板分类、版本控制、参数化、效果统计
/// </summary>
[SugarTable("PromptTemplates", "AI提示词模板管理表")]
public class PromptTemplate : BaseEntity
{
    /// <summary>
    /// 模板名称，用于识别和显示
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "模板名称")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述，说明用途和使用场景
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "模板描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 模板分类ID，关联PromptCategories表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "模板分类ID")]
    public int CategoryId { get; set; }

    /// <summary>
    /// 模板内容，支持参数占位符
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "模板内容")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 模板参数定义，JSON格式存储参数配置
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "模板参数定义")]
    public string? Parameters { get; set; }

    /// <summary>
    /// 模板类型: System=系统模板, User=用户自定义, Shared=共享模板
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "模板类型")]
    public string TemplateType { get; set; } = "System";

    /// <summary>
    /// 适用的AI任务类型: RequirementAnalysis, CodeGeneration, Testing, Optimization等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "适用的AI任务类型")]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 适用的AI提供商，多个用逗号分隔
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "适用的AI提供商")]
    public string? SupportedProviders { get; set; }

    /// <summary>
    /// 模板版本号
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "模板版本号")]
    public string TemplateVersion { get; set; } = "1.0";

    /// <summary>
    /// 是否为默认模板
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否为默认模板")]
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 使用次数统计
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "使用次数统计")]
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 平均评分（1-5分）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "平均评分")]
    public decimal? AverageRating { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "最后使用时间")]
    public DateTime? LastUsedTime { get; set; }

    /// <summary>
    /// 模板标签，用于搜索和分类
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "模板标签")]
    public string? Tags { get; set; }

    /// <summary>
    /// 模板分类 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptCategory? Category { get; set; }

    /// <summary>
    /// 使用统计记录 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PromptUsageStats> UsageStats { get; set; } = new();

    /// <summary>
    /// 用户评价记录 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PromptRating> Ratings { get; set; } = new();
}
