import { aiProvider } from './aiProvider'
import type { PlaywrightTest, PlaywrightExecutionResult } from './playwrightTest'

// AI测试分析服务
export class AITestAnalysisService {
  
  // 1. 智能测试失败分析
  static async analyzeTestFailure(
    test: PlaywrightTest, 
    executionResult: PlaywrightExecutionResult
  ): Promise<TestFailureAnalysis> {
    try {
      const prompt = `
请分析以下Playwright测试失败的原因并提供修复建议：

测试名称: ${test.name}
测试代码:
\`\`\`javascript
${test.code}
\`\`\`

执行结果:
- 状态: ${executionResult.status}
- 错误信息: ${executionResult.errorMessage || '无'}
- 执行时长: ${executionResult.duration}ms
- 浏览器: ${executionResult.browser}

执行日志:
${executionResult.logs.map(log => `[${log.level}] ${log.message}`).join('\n')}

请提供：
1. 失败原因分析（根本原因）
2. 具体修复建议
3. 优化后的测试代码
4. 预防措施建议
5. 相关最佳实践

以JSON格式返回分析结果。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseFailureAnalysis(response)
    } catch (error) {
      console.error('AI测试失败分析失败:', error)
      throw error
    }
  }

  // 2. 测试覆盖率分析和建议
  static async analyzeCoverage(
    tests: PlaywrightTest[], 
    projectStructure: any
  ): Promise<CoverageAnalysis> {
    try {
      const prompt = `
基于以下测试用例和项目结构，分析测试覆盖率并提供改进建议：

现有测试用例:
${tests.map(test => ({
  name: test.name,
  category: test.category,
  description: test.description
})).map(t => `- ${t.name} (${t.category}): ${t.description}`).join('\n')}

项目结构:
${JSON.stringify(projectStructure, null, 2)}

请分析：
1. 当前测试覆盖的功能模块
2. 缺失的测试覆盖区域
3. 测试类型分布是否合理
4. 建议新增的测试用例
5. 测试优先级建议

以JSON格式返回分析结果。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseCoverageAnalysis(response)
    } catch (error) {
      console.error('测试覆盖率分析失败:', error)
      throw error
    }
  }

  // 3. 测试性能分析
  static async analyzePerformance(
    executionResults: PlaywrightExecutionResult[]
  ): Promise<PerformanceAnalysis> {
    try {
      const prompt = `
分析以下测试执行性能数据，提供优化建议：

执行结果统计:
${executionResults.map(result => ({
  testName: result.testId,
  duration: result.duration,
  status: result.status,
  browser: result.browser,
  retries: result.stats?.flakyTests || 0
})).map(r => `- 测试${r.testName}: ${r.duration}ms (${r.status}, ${r.browser})`).join('\n')}

总体统计:
- 总测试数: ${executionResults.length}
- 平均执行时间: ${executionResults.reduce((sum, r) => sum + (r.duration || 0), 0) / executionResults.length}ms
- 成功率: ${(executionResults.filter(r => r.status === 'passed').length / executionResults.length * 100).toFixed(1)}%

请分析：
1. 性能瓶颈识别
2. 慢测试优化建议
3. 并行执行优化
4. 资源使用优化
5. 稳定性改进建议

以JSON格式返回分析结果。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parsePerformanceAnalysis(response)
    } catch (error) {
      console.error('测试性能分析失败:', error)
      throw error
    }
  }

  // 4. 智能测试维护建议
  static async generateMaintenanceSuggestions(
    tests: PlaywrightTest[],
    executionHistory: PlaywrightExecutionResult[]
  ): Promise<MaintenanceSuggestions> {
    try {
      const prompt = `
基于测试用例和执行历史，提供测试维护建议：

测试用例概况:
${tests.map(test => ({
  name: test.name,
  createdTime: test.createdTime,
  updatedTime: test.updatedTime,
  status: test.status
})).map(t => `- ${t.name}: 创建于${t.createdTime}, 更新于${t.updatedTime}, 状态${t.status}`).join('\n')}

执行历史分析:
- 总执行次数: ${executionHistory.length}
- 失败率较高的测试: ${this.getHighFailureRateTests(executionHistory)}
- 长期未执行的测试: ${this.getLongUnusedTests(tests, executionHistory)}

请提供：
1. 需要重构的测试识别
2. 可以删除的过时测试
3. 需要更新的测试用例
4. 测试数据维护建议
5. 测试环境优化建议

以JSON格式返回建议。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseMaintenanceSuggestions(response)
    } catch (error) {
      console.error('生成维护建议失败:', error)
      throw error
    }
  }

  // 5. 测试报告智能总结
  static async generateTestReport(
    tests: PlaywrightTest[],
    executionResults: PlaywrightExecutionResult[],
    timeRange: { start: Date, end: Date }
  ): Promise<IntelligentTestReport> {
    try {
      const prompt = `
基于以下数据生成智能测试报告：

时间范围: ${timeRange.start.toISOString()} 到 ${timeRange.end.toISOString()}

测试概况:
- 总测试数: ${tests.length}
- 执行次数: ${executionResults.length}
- 成功率: ${(executionResults.filter(r => r.status === 'passed').length / executionResults.length * 100).toFixed(1)}%

测试分类分布:
${this.getTestCategoryDistribution(tests)}

关键指标:
- 平均执行时间: ${executionResults.reduce((sum, r) => sum + (r.duration || 0), 0) / executionResults.length}ms
- 最慢测试: ${Math.max(...executionResults.map(r => r.duration || 0))}ms
- 失败测试数: ${executionResults.filter(r => r.status === 'failed').length}

请生成包含以下内容的测试报告：
1. 执行摘要
2. 质量评估
3. 风险识别
4. 改进建议
5. 下一步行动计划

以JSON格式返回报告内容。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseTestReport(response)
    } catch (error) {
      console.error('生成测试报告失败:', error)
      throw error
    }
  }

  // 辅助方法：解析失败分析结果
  private static parseFailureAnalysis(aiResponse: string): TestFailureAnalysis {
    try {
      const analysis = JSON.parse(aiResponse)
      return {
        rootCause: analysis.rootCause || '未知原因',
        fixSuggestions: analysis.fixSuggestions || [],
        optimizedCode: analysis.optimizedCode || '',
        preventionMeasures: analysis.preventionMeasures || [],
        bestPractices: analysis.bestPractices || [],
        confidence: analysis.confidence || 0.8
      }
    } catch (error) {
      console.error('解析失败分析结果失败:', error)
      return {
        rootCause: '解析AI响应失败',
        fixSuggestions: ['请手动检查测试代码和执行环境'],
        optimizedCode: '',
        preventionMeasures: [],
        bestPractices: [],
        confidence: 0.1
      }
    }
  }

  // 辅助方法：解析覆盖率分析结果
  private static parseCoverageAnalysis(aiResponse: string): CoverageAnalysis {
    try {
      const analysis = JSON.parse(aiResponse)
      return {
        coveredModules: analysis.coveredModules || [],
        missingCoverage: analysis.missingCoverage || [],
        testTypeDistribution: analysis.testTypeDistribution || {},
        suggestedTests: analysis.suggestedTests || [],
        priorityRecommendations: analysis.priorityRecommendations || []
      }
    } catch (error) {
      console.error('解析覆盖率分析结果失败:', error)
      return {
        coveredModules: [],
        missingCoverage: [],
        testTypeDistribution: {},
        suggestedTests: [],
        priorityRecommendations: []
      }
    }
  }

  // 辅助方法：解析性能分析结果
  private static parsePerformanceAnalysis(aiResponse: string): PerformanceAnalysis {
    try {
      const analysis = JSON.parse(aiResponse)
      return {
        bottlenecks: analysis.bottlenecks || [],
        slowTests: analysis.slowTests || [],
        parallelizationSuggestions: analysis.parallelizationSuggestions || [],
        resourceOptimization: analysis.resourceOptimization || [],
        stabilityImprovements: analysis.stabilityImprovements || []
      }
    } catch (error) {
      console.error('解析性能分析结果失败:', error)
      return {
        bottlenecks: [],
        slowTests: [],
        parallelizationSuggestions: [],
        resourceOptimization: [],
        stabilityImprovements: []
      }
    }
  }

  // 辅助方法：解析维护建议
  private static parseMaintenanceSuggestions(aiResponse: string): MaintenanceSuggestions {
    try {
      const suggestions = JSON.parse(aiResponse)
      return {
        testsToRefactor: suggestions.testsToRefactor || [],
        testsToDelete: suggestions.testsToDelete || [],
        testsToUpdate: suggestions.testsToUpdate || [],
        dataMaintenanceActions: suggestions.dataMaintenanceActions || [],
        environmentOptimizations: suggestions.environmentOptimizations || []
      }
    } catch (error) {
      console.error('解析维护建议失败:', error)
      return {
        testsToRefactor: [],
        testsToDelete: [],
        testsToUpdate: [],
        dataMaintenanceActions: [],
        environmentOptimizations: []
      }
    }
  }

  // 辅助方法：解析测试报告
  private static parseTestReport(aiResponse: string): IntelligentTestReport {
    try {
      const report = JSON.parse(aiResponse)
      return {
        executionSummary: report.executionSummary || '',
        qualityAssessment: report.qualityAssessment || '',
        riskIdentification: report.riskIdentification || [],
        improvementSuggestions: report.improvementSuggestions || [],
        actionPlan: report.actionPlan || []
      }
    } catch (error) {
      console.error('解析测试报告失败:', error)
      return {
        executionSummary: '报告生成失败',
        qualityAssessment: '无法评估',
        riskIdentification: [],
        improvementSuggestions: [],
        actionPlan: []
      }
    }
  }

  // 辅助方法：获取高失败率测试
  private static getHighFailureRateTests(executionHistory: PlaywrightExecutionResult[]): string {
    const testFailures = new Map<number, { total: number, failed: number }>()
    
    executionHistory.forEach(result => {
      const current = testFailures.get(result.testId) || { total: 0, failed: 0 }
      current.total++
      if (result.status === 'failed') {
        current.failed++
      }
      testFailures.set(result.testId, current)
    })

    const highFailureTests = Array.from(testFailures.entries())
      .filter(([_, stats]) => stats.failed / stats.total > 0.3)
      .map(([testId, stats]) => `测试${testId}(失败率${(stats.failed / stats.total * 100).toFixed(1)}%)`)

    return highFailureTests.join(', ') || '无'
  }

  // 辅助方法：获取长期未使用的测试
  private static getLongUnusedTests(tests: PlaywrightTest[], executionHistory: PlaywrightExecutionResult[]): string {
    const executedTestIds = new Set(executionHistory.map(r => r.testId))
    const unusedTests = tests
      .filter(test => !executedTestIds.has(test.id))
      .map(test => test.name)

    return unusedTests.join(', ') || '无'
  }

  // 辅助方法：获取测试分类分布
  private static getTestCategoryDistribution(tests: PlaywrightTest[]): string {
    const distribution = tests.reduce((acc, test) => {
      acc[test.category] = (acc[test.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(distribution)
      .map(([category, count]) => `${category}: ${count}`)
      .join(', ')
  }
}

// 类型定义
export interface TestFailureAnalysis {
  rootCause: string
  fixSuggestions: string[]
  optimizedCode: string
  preventionMeasures: string[]
  bestPractices: string[]
  confidence: number
}

export interface CoverageAnalysis {
  coveredModules: string[]
  missingCoverage: string[]
  testTypeDistribution: Record<string, number>
  suggestedTests: Array<{
    name: string
    description: string
    priority: 'high' | 'medium' | 'low'
    category: string
  }>
  priorityRecommendations: string[]
}

export interface PerformanceAnalysis {
  bottlenecks: Array<{
    testId: number
    issue: string
    impact: string
    suggestion: string
  }>
  slowTests: Array<{
    testId: number
    duration: number
    optimizationSuggestion: string
  }>
  parallelizationSuggestions: string[]
  resourceOptimization: string[]
  stabilityImprovements: string[]
}

export interface MaintenanceSuggestions {
  testsToRefactor: Array<{
    testId: number
    reason: string
    priority: 'high' | 'medium' | 'low'
  }>
  testsToDelete: Array<{
    testId: number
    reason: string
  }>
  testsToUpdate: Array<{
    testId: number
    updateType: string
    description: string
  }>
  dataMaintenanceActions: string[]
  environmentOptimizations: string[]
}

export interface IntelligentTestReport {
  executionSummary: string
  qualityAssessment: string
  riskIdentification: string[]
  improvementSuggestions: string[]
  actionPlan: string[]
}

// 导出默认服务
export default AITestAnalysisService