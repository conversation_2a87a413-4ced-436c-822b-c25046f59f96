-- =============================================
-- 项目开发自动化综合视图
-- 功能: 整合项目、编码任务、开发步骤和UI自动化模板的关联信息
-- 创建时间: 2025-01-02
-- =============================================

USE ProjectManagementAI;
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_ProjectDevelopmentAutomation')
BEGIN
    DROP VIEW V_ProjectDevelopmentAutomation;
    PRINT '已删除现有视图 V_ProjectDevelopmentAutomation';
END
GO

-- 创建项目开发自动化综合视图
CREATE VIEW V_ProjectDevelopmentAutomation AS
SELECT 
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,
    p.Progress AS ProjectProgress,
    
    -- 编码任务信息
    ct.Id AS CodingTaskId,
    ct.TaskName AS CodingTaskName,
    ct.Description AS CodingTaskDescription,
    ct.Status AS CodingTaskStatus,
    ct.Priority AS CodingTaskPriority,
    ct.EstimatedHours AS CodingTaskEstimatedHours,
    ct.ActualHours AS CodingTaskActualHours,
    ct.StartDate AS CodingTaskStartDate,
    ct.DueDate AS CodingTaskDueDate,
    ct.CompletedDate AS CodingTaskCompletedDate,
    
    -- 编码任务步骤信息
    cts.Id AS CodingTaskStepId,
    cts.OrderIndex AS CodingTaskStepOrder,
    cts.Status AS CodingTaskStepStatus,
    cts.StartTime AS CodingTaskStepStartTime,
    cts.CompletedTime AS CodingTaskStepCompletedTime,
    cts.ActualHours AS CodingTaskStepActualHours,
    
    -- 开发步骤信息
    ds.Id AS DevelopmentStepId,
    ds.StepName,
    ds.StepDescription,
    ds.StepType,
    ds.Priority AS StepPriority,
    ds.EstimatedHours AS StepEstimatedHours,
    ds.ActualHours AS StepActualHours,
    ds.TechnologyStack,
    ds.FileType,
    ds.FilePath,
    ds.ComponentType,
    ds.AIPrompt,
    ds.AIGeneratedCode,
    ds.AIProvider,
    ds.AIModel,
    ds.Status AS StepStatus,
    ds.Progress AS StepProgress,
    ds.StartTime AS StepStartTime,
    ds.EndTime AS StepEndTime,
    ds.CompletedTime AS StepCompletedTime,
    ds.ExecutionResult,
    ds.ErrorMessage,
    ds.OutputPath,
    ds.GeneratedFiles,
    ds.StepOrder,
    ds.StepGroup,
    ds.StepLevel,
    ds.ParentStepId,
    
    -- 步骤模板序列关联信息
    stsa.Id AS AssociationId,
    stsa.AppliedTime,
    stsa.Status AS ExecutionStatus,
    stsa.Progress AS ExecutionProgress,
    stsa.ExecutionStartTime,
    stsa.ExecutionEndTime,
    stsa.ExecutionResult AS AssociationExecutionResult,
    stsa.ErrorMessage AS AssociationErrorMessage,
    stsa.Notes AS ExecutionLog,
    
    -- UI自动化模板序列信息
    uts.Id AS TemplateSequenceId,
    uts.Name AS TemplateSequenceName,
    uts.Description AS TemplateSequenceDescription,
    uts.Category AS TemplateSequenceCategory,
    uts.Tags AS TemplateSequenceTags,
    uts.Notes AS TemplateSequenceNotes,
    uts.UsageCount AS TemplateSequenceUsageCount,
    uts.LastUsedTime AS TemplateSequenceLastUsedTime,
    uts.IsActive AS TemplateSequenceIsActive,
    uts.SequenceJson,
    uts.SourceCode,
    uts.CodeLanguage,
    uts.CategoryId AS TemplateSequenceCategoryId,
    
    -- UI自动化模板步骤信息
    utst.Id AS TemplateStepId,
    utst.StepOrder AS TemplateStepOrder,
    utst.ActionType,
    utst.Description AS TemplateStepDescription,
    utst.Parameters AS TemplateStepParameters,
    utst.TimeoutSeconds,
    utst.RetryCount,
    utst.MaxRetries,
    utst.IsActive AS TemplateStepIsActive,
    utst.LogicType,
    utst.ConditionExpression,
    utst.JumpToStepId,
    utst.LoopCount,
    utst.GroupId,
    
    -- 自定义UI自动化模板信息
    cut.Id AS CustomTemplateId,
    cut.Name AS CustomTemplateName,
    cut.Description AS CustomTemplateDescription,
    cut.Category AS CustomTemplateCategory,
    cut.FilePath AS CustomTemplateFilePath,
    cut.Confidence AS CustomTemplateConfidence,
    cut.Tags AS CustomTemplateTags,
    cut.Notes AS CustomTemplateNotes,
    cut.UsageCount AS CustomTemplateUsageCount,
    cut.LastUsedTime AS CustomTemplateLastUsedTime,
    cut.CategoryId AS CustomTemplateCategoryId,
    
    -- 分类信息
    tc.Name AS TemplateCategoryName,
    tc.Description AS TemplateCategoryDescription,
    tc.ParentId AS ParentCategoryId,
    tc.SortOrder AS CategorySortOrder,
    tc.Icon AS CategoryIcon,
    tc.Color AS CategoryColor,
    tc.IsSystem AS CategoryIsSystem,
    tc.IsEnabled AS CategoryIsEnabled,
    
    -- 统计信息
    CASE 
        WHEN ct.DueDate IS NOT NULL AND ct.DueDate < GETDATE() AND ct.Status != 'Completed' 
        THEN 1 ELSE 0 
    END AS IsOverdue,
    
    CASE 
        WHEN ct.DueDate IS NOT NULL AND ct.Status != 'Completed'
        THEN DATEDIFF(DAY, GETDATE(), ct.DueDate)
        ELSE NULL 
    END AS RemainingDays,
    
    CASE 
        WHEN ct.EstimatedHours IS NOT NULL AND ct.EstimatedHours > 0 AND ct.ActualHours IS NOT NULL
        THEN ROUND(ct.ActualHours / ct.EstimatedHours * 100, 2)
        ELSE NULL 
    END AS HourUtilization,
    
    -- 时间戳
    p.CreatedTime AS ProjectCreatedTime,
    ct.CreatedTime AS CodingTaskCreatedTime,
    ds.CreatedTime AS StepCreatedTime,
    stsa.CreatedTime AS AssociationCreatedTime,
    uts.CreatedTime AS TemplateSequenceCreatedTime

FROM Projects p
    LEFT JOIN CodingTasks ct ON p.Id = ct.ProjectId AND ct.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ct.Id = cts.CodingTaskId AND cts.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON cts.DevelopmentStepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId AND stsa.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id AND uts.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSteps utst ON uts.Id = utst.SequenceId AND utst.IsDeleted = 0
    LEFT JOIN CustomUIAutoMationTemplates cut ON utst.TemplateId = cut.Id AND cut.IsDeleted = 0
    LEFT JOIN CustomTemplateCategories tc ON COALESCE(uts.CategoryId, cut.CategoryId) = tc.Id AND tc.IsDeleted = 0

WHERE p.IsDeleted = 0;

GO

PRINT '✅ 视图 V_ProjectDevelopmentAutomation 创建成功！';
PRINT '';
PRINT '视图功能说明：';
PRINT '1. 整合项目、编码任务、开发步骤和UI自动化模板的完整关联信息';
PRINT '2. 提供项目开发进度和自动化执行状态的统一视图';
PRINT '3. 包含时间统计、进度计算和状态分析';
PRINT '4. 支持按项目、任务、步骤等多维度查询';
PRINT '';
PRINT '使用示例：';
PRINT '-- 查询特定项目的所有自动化信息';
PRINT 'SELECT * FROM V_ProjectDevelopmentAutomation WHERE ProjectId = 1;';
PRINT '';
PRINT '-- 查询正在执行的自动化任务';
PRINT 'SELECT * FROM V_ProjectDevelopmentAutomation WHERE ExecutionStatus = ''Running'';';
PRINT '';
PRINT '-- 查询逾期的编码任务';
PRINT 'SELECT * FROM V_ProjectDevelopmentAutomation WHERE IsOverdue = 1;';
GO
