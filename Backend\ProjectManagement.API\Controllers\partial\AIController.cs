﻿namespace ProjectManagement.API.Controllers
{
    // 测试生成相关扩展
    public partial class AIController
    {
        #region 测试生成辅助方法

        /// <summary>
        /// 获取项目信息用于测试生成
        /// </summary>
        private async Task<ProjectInfoForTestGeneration> GetProjectInfoForTestGeneration(int projectId)
        {
            try
            {
                // 获取项目基本信息
                var project = await _projectRepository.GetByIdAsync(projectId);
                if (project == null)
                {
                    throw new ArgumentException($"项目不存在: {projectId}");
                }

                // 获取项目的需求文档
                var requirements = await _requirementRepository.GetListAsync(r => r.ProjectId == projectId);
                var latestRequirement = requirements.OrderByDescending(r => r.CreatedTime).FirstOrDefault();

                // 构建项目信息
                var projectInfo = new ProjectInfoForTestGeneration
                {
                    ProjectId = projectId,
                    ProjectName = project.Name,
                    Specification = latestRequirement?.Content ?? "暂无需求规格说明",
                    Architecture = latestRequirement?.NonFunctionalRequirements ?? "暂无架构描述",
                    TechnologyStack = project.TechnologyStack ?? "暂无技术栈信息",
                    DatabaseSchema = "暂无数据库架构信息" // 可以从ER图中获取
                };

                _logger.LogInformation("成功获取项目信息: ProjectId={ProjectId}, ProjectName={ProjectName}",
                    projectId, project.Name);

                return projectInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取项目信息失败: ProjectId={ProjectId}", projectId);
                throw;
            }
        }

        /// <summary>
        /// 获取代码内容用于测试生成
        /// </summary>
        private async Task<string> GetCodeContentForTesting(int projectId, int? codeGenerationTaskId)
        {
            try
            {
                var codeContent = new System.Text.StringBuilder();

                if (codeGenerationTaskId.HasValue)
                {
                    // 从代码生成任务中获取生成的代码
                    try
                    {
                        // 这里可以扩展为从代码生成服务获取代码
                        // 目前返回基本的代码结构信息
                        codeContent.AppendLine("// 从代码生成任务获取的代码");
                        codeContent.AppendLine($"// 代码生成任务ID: {codeGenerationTaskId.Value}");
                        codeContent.AppendLine("// 包含控制器、服务、仓储等核心代码文件");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "从代码生成任务获取代码失败: TaskId={TaskId}", codeGenerationTaskId.Value);
                    }
                }

                // 获取项目基本信息作为代码上下文
                var project = await _projectRepository.GetByIdAsync(projectId);
                if (project != null)
                {
                    codeContent.AppendLine($"// 项目: {project.Name}");
                    codeContent.AppendLine($"// 技术栈: {project.TechnologyStack ?? "未指定"}");
                    codeContent.AppendLine($"// 项目描述: {project.Description ?? "无描述"}");

                    // 添加基本的代码结构
                    codeContent.AppendLine();
                    codeContent.AppendLine("// 项目代码结构示例:");
                    codeContent.AppendLine("// Controllers/ - API控制器");
                    codeContent.AppendLine("// Services/ - 业务逻辑服务");
                    codeContent.AppendLine("// Repositories/ - 数据访问层");
                    codeContent.AppendLine("// Models/ - 数据模型");
                    codeContent.AppendLine("// DTOs/ - 数据传输对象");
                }

                var result = codeContent.ToString();
                if (string.IsNullOrWhiteSpace(result))
                {
                    result = "// 暂无可用的代码内容";
                }

                _logger.LogInformation("成功获取代码内容: ProjectId={ProjectId}, 内容长度={Length}",
                    projectId, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取代码内容失败: ProjectId={ProjectId}, TaskId={TaskId}", projectId, codeGenerationTaskId);
                return "// 无法获取代码内容";
            }
        }

        /// <summary>
        /// 保存生成的测试用例
        /// </summary>
        private async Task<List<SavedTestCase>> SaveGeneratedTestCases(int projectId, string testCasesJson, string testType, string testFramework)
        {
            var savedTests = new List<SavedTestCase>();

            try
            {
                // 解析AI生成的测试用例JSON
                var testCases = System.Text.Json.JsonSerializer.Deserialize<List<GeneratedTestCase>>(testCasesJson);

                if (testCases != null && testCases.Any())
                {
                    // 获取 SqlSugar 客户端来直接操作 TestTask
                    var db = _serviceProvider.GetRequiredService<SqlSugar.ISqlSugarClient>();

                    foreach (var testCase in testCases)
                    {
                        try
                        {
                            // 创建 TestTask 实体
                            var testTask = new ProjectManagement.Core.Entities.TestTask
                            {
                                ProjectId = projectId,
                                TestName = testCase.Name ?? "未命名测试",
                                TestType = testType,
                                TestFramework = testFramework,
                                TestCode = testCase.Code ?? "",
                                Status = "Pending",
                                CreatedAt = DateTime.UtcNow
                            };

                            // 保存到数据库
                            var savedId = await db.Insertable(testTask).ExecuteReturnIdentityAsync();

                            // 添加到返回列表
                            savedTests.Add(new SavedTestCase
                            {
                                Id = savedId,
                                Name = testTask.TestName,
                                Description = testCase.Description ?? "",
                                Code = testTask.TestCode ?? "",
                                TestType = testType,
                                Framework = testFramework
                            });

                            _logger.LogDebug("保存测试用例成功: TestId={TestId}, Name={Name}",
                                savedId, testTask.TestName);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "保存单个测试用例失败: {TestName}", testCase.Name);

                            // 即使单个保存失败，也添加到列表中（使用临时ID）
                            savedTests.Add(new SavedTestCase
                            {
                                Id = -(savedTests.Count + 1), // 负数表示临时ID
                                Name = testCase.Name ?? "未命名测试",
                                Description = testCase.Description ?? "",
                                Code = testCase.Code ?? "",
                                TestType = testType,
                                Framework = testFramework
                            });
                        }
                    }
                }

                _logger.LogInformation("保存测试用例完成: ProjectId={ProjectId}, 总数={Total}, 成功={Success}",
                    projectId, testCases?.Count ?? 0, savedTests.Count(t => t.Id > 0));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存测试用例失败: ProjectId={ProjectId}", projectId);

                // 即使保存失败，也返回一个默认的测试用例
                savedTests.Add(new SavedTestCase
                {
                    Id = -1,
                    Name = "AI生成的测试用例",
                    Description = "测试用例保存失败，但AI生成成功",
                    Code = testCasesJson,
                    TestType = testType,
                    Framework = testFramework
                });
            }

            return savedTests;
        }

        /// <summary>
        /// 获取用户的AI配置
        /// </summary>
        private async Task<ProjectManagement.Core.DTOs.AI.AIModelConfig> GetAIConfigForUser(int userId, string? preferredModel = null)
        {
            try
            {
                var aiConfig = await ProjectManagement.API.Helper.AIConfigurationHelper.GetUserAIConfigurationAsync(
                    _aiConfigRepository,
                    _logger,
                    userId,
                    null, // aiProviderConfigId
                    4000, // defaultMaxTokens
                    0.7f, // defaultTemperature
                    _encryptionService);

                if (aiConfig == null)
                {
                    // 返回默认配置
                    return new ProjectManagement.Core.DTOs.AI.AIModelConfig
                    {
                        Provider = "DeepSeek",
                        Model = preferredModel ?? "deepseek-chat",
                        ApiKey = "",
                        Endpoint = "https://api.deepseek.com",
                        MaxTokens = 4000,
                        Temperature = 0.7f
                    };
                }

                return aiConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户AI配置失败: UserId={UserId}", userId);

                // 返回默认配置
                return new ProjectManagement.Core.DTOs.AI.AIModelConfig
                {
                    Provider = "DeepSeek",
                    Model = preferredModel ?? "deepseek-chat",
                    ApiKey = "",
                    Endpoint = "https://api.deepseek.com",
                    MaxTokens = 4000,
                    Temperature = 0.7f
                };
            }
        }

        #endregion

        #region 测试生成相关数据模型

        /// <summary>
        /// 项目测试生成信息
        /// </summary>
        public class ProjectInfoForTestGeneration
        {
            public int ProjectId { get; set; }
            public string ProjectName { get; set; } = string.Empty;
            public string? Specification { get; set; }
            public string? Architecture { get; set; }
            public string? TechnologyStack { get; set; }
            public string? DatabaseSchema { get; set; }
        }

        /// <summary>
        /// AI生成的测试用例
        /// </summary>
        public class GeneratedTestCase
        {
            public string? Name { get; set; }
            public string? Description { get; set; }
            public string? Code { get; set; }
            public string? Priority { get; set; }
            public List<string>? Tags { get; set; }
            public string? Category { get; set; }
        }

        /// <summary>
        /// 保存的测试用例
        /// </summary>
        public class SavedTestCase
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Code { get; set; } = string.Empty;
            public string TestType { get; set; } = string.Empty;
            public string Framework { get; set; } = string.Empty;
        }

        /// <summary>
        /// 测试生成结果DTO
        /// </summary>
        public class TestGenerationResultDto
        {
            public bool Success { get; set; }
            public string Message { get; set; } = string.Empty;
            public List<TestCaseDto> TestCases { get; set; } = new();
            public string? GeneratedContent { get; set; }
        }

        /// <summary>
        /// 测试用例DTO
        /// </summary>
        public class TestCaseDto
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Code { get; set; } = string.Empty;
            public string TestType { get; set; } = string.Empty;
            public string Framework { get; set; } = string.Empty;
            public string? Priority { get; set; }
            public List<string>? Tags { get; set; }
            public DateTime? CreatedTime { get; set; }
        }

        #endregion

    }
}
