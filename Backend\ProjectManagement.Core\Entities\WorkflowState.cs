using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 工作流程状态实体 - 对应DatabaseSchema.sql中的WorkflowStates表
/// 功能: 跟踪项目在AI驱动开发流程中的当前阶段
/// 支持: 阶段状态跟踪、流程数据存储、时间记录
/// </summary>
[SugarTable("WorkflowStates", "项目工作流管理表")]
public class WorkflowState
{
    /// <summary>
    /// 工作流状态唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "工作流状态唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 当前阶段: RequirementGathering(需求收集), DocumentGeneration(文档生成), DiagramGeneration(图表生成), CodeGeneration(代码生成), Testing(测试), Deployment(部署), IssueHandling(问题处理)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "当前阶段")]
    public string CurrentStage { get; set; } = string.Empty;

    /// <summary>
    /// 阶段状态: Pending(待开始), InProgress(进行中), Completed(已完成), Failed(失败)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "阶段状态")]
    public string StageStatus { get; set; } = "InProgress";

    /// <summary>
    /// 阶段相关数据，JSON格式存储
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "阶段相关数据，JSON格式存储")]
    public string? StageData { get; set; }

    /// <summary>
    /// 阶段开始时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "阶段开始时间")]
    public DateTime? StartedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 阶段完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "阶段完成时间")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }
}
