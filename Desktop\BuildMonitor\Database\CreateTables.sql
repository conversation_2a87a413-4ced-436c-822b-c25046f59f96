-- =============================================
-- 编译错误表 (CompilationErrors)
-- 用于存储前端和后端的编译错误信息
-- =============================================
use ProjectManagementAI
CREATE TABLE CompilationErrors (
    -- 主键
    Id INT IDENTITY(1,1) NOT NULL,

    -- 关联字段
    ProjectId INT NULL,                    -- 关联项目ID

    -- 编译信息
    ProjectType NVARCHAR(50) NOT NULL,     -- 项目类型：Backend/Frontend
    CompilationSessionId NVARCHAR(100) NULL, -- 编译会话ID，用于批量关联

    -- 错误详情
    Severity NVARCHAR(20) NOT NULL,        -- 严重程度：Error/Warning/Info
    Code NVARCHAR(50) NOT NULL,            -- 错误代码 (如CS0103, TS2304)
    Message NVARCHAR(MAX) NOT NULL,        -- 错误信息
    FilePath NVARCHAR(500) NOT NULL,       -- 文件路径
    LineNumber INT NOT NULL DEFAULT 0,     -- 行号
    ColumnNumber INT NOT NULL DEFAULT 0,   -- 列号

    -- 项目信息
    ProjectName NVARCHAR(200) NULL,        -- 项目名称
    ProjectPath NVARCHAR(500) NULL,        -- 项目路径

    -- 编译上下文
    CompilerVersion NVARCHAR(100) NULL,    -- 编译器版本
    TargetFramework NVARCHAR(100) NULL,    -- 目标框架
    BuildConfiguration NVARCHAR(50) NULL,  -- 编译配置 (Debug/Release)

    -- 时间信息
    CompilationTime DATETIME2 NOT NULL DEFAULT GETDATE(), -- 编译时间

    -- BaseEntity 审计字段
    CreatedTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime DATETIME2 NULL,
    CreatedBy INT NULL,
    UpdatedBy INT NULL,
    IsDeleted BIT NOT NULL DEFAULT 0,
    DeletedTime DATETIME2 NULL,
    DeletedBy INT NULL,
    Version INT NOT NULL DEFAULT 1,
    Remarks NVARCHAR(500) NULL,

    -- 约束
    CONSTRAINT PK_CompilationErrors PRIMARY KEY (Id),
    CONSTRAINT FK_CompilationErrors_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT CK_CompilationErrors_ProjectType CHECK (ProjectType IN ('Backend', 'Frontend')),
    CONSTRAINT CK_CompilationErrors_Severity CHECK (Severity IN ('Error', 'Warning', 'Info', 'Hidden'))
);

-- 创建索引
CREATE NONCLUSTERED INDEX IX_CompilationErrors_ProjectId ON CompilationErrors(ProjectId);
CREATE NONCLUSTERED INDEX IX_CompilationErrors_ProjectType ON CompilationErrors(ProjectType);
CREATE NONCLUSTERED INDEX IX_CompilationErrors_Severity ON CompilationErrors(Severity);
CREATE NONCLUSTERED INDEX IX_CompilationErrors_CompilationTime ON CompilationErrors(CompilationTime);
CREATE NONCLUSTERED INDEX IX_CompilationErrors_CompilationSessionId ON CompilationErrors(CompilationSessionId);
CREATE NONCLUSTERED INDEX IX_CompilationErrors_IsDeleted ON CompilationErrors(IsDeleted);
