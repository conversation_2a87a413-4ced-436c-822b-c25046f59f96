using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.AI.Interfaces;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Visual Studio 编译错误处理服务
/// 功能: 获取VS编译错误、记录到数据库、请求AI修复
/// </summary>
public class VSBuildErrorService
{
    private readonly ILogger<VSBuildErrorService> _logger;
    private readonly IRepository<VSBuildError> _buildErrorRepository;
    private readonly IRepository<VSBuildSession> _buildSessionRepository;
    private readonly IAIService _aiService;

    public VSBuildErrorService(
        ILogger<VSBuildErrorService> logger,
        IRepository<VSBuildError> buildErrorRepository,
        IRepository<VSBuildSession> buildSessionRepository,
        IAIService aiService)
    {
        _logger = logger;
        _buildErrorRepository = buildErrorRepository;
        _buildSessionRepository = buildSessionRepository;
        _aiService = aiService;
    }

    /// <summary>
    /// 监控Visual Studio编译输出并记录错误
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <param name="projectId">项目ID</param>
    /// <param name="buildConfiguration">编译配置</param>
    /// <param name="buildPlatform">编译平台</param>
    /// <returns>编译会话ID</returns>
    public async Task<string> MonitorBuildAsync(string projectPath, int? projectId = null, 
        string buildConfiguration = "Debug", string buildPlatform = "AnyCPU")
    {
        var sessionId = Guid.NewGuid().ToString();
        _logger.LogInformation("开始监控VS编译，会话ID: {SessionId}", sessionId);

        try
        {
            // 创建编译会话记录
            var buildSession = new VSBuildSession
            {
                ProjectId = projectId,
                SessionId = sessionId,
                BuildConfiguration = buildConfiguration,
                BuildPlatform = buildPlatform,
                StartTime = DateTime.Now,
                BuildResult = "Running"
            };

            await _buildSessionRepository.AddAsync(buildSession);

            // 执行编译并捕获输出
            var buildOutput = await ExecuteBuildAsync(projectPath, buildConfiguration, buildPlatform);
            
            // 解析编译输出中的错误
            var errors = ParseBuildErrors(buildOutput, sessionId);
            
            // 记录错误到数据库
            foreach (var error in errors)
            {
                error.ProjectId = projectId;
                await _buildErrorRepository.AddAsync(error);
            }

            // 更新编译会话统计
            await UpdateBuildSessionAsync(sessionId, errors, buildOutput.Contains("Build succeeded"));

            _logger.LogInformation("编译监控完成，会话ID: {SessionId}, 错误数: {ErrorCount}", 
                sessionId, errors.Count);

            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "监控VS编译失败，会话ID: {SessionId}", sessionId);
            throw;
        }
    }

    /// <summary>
    /// 为编译错误请求AI修复建议
    /// </summary>
    /// <param name="sessionId">编译会话ID</param>
    /// <returns>AI修复建议</returns>
    public async Task<string> RequestAIFixAsync(string sessionId)
    {
        _logger.LogInformation("为编译会话请求AI修复，会话ID: {SessionId}", sessionId);

        try
        {
            // 获取编译会话和错误
            var buildSession = await _buildSessionRepository.GetFirstOrDefaultAsync(x => x.SessionId == sessionId);
            if (buildSession is null)
            {
                throw new ArgumentException($"找不到编译会话: {sessionId}");
            }

            var errors = await _buildErrorRepository.GetListAsync(x => x.BuildSessionId == sessionId);
            if (!errors.Any())
            {
                return "没有找到需要修复的编译错误。";
            }

            // 构建AI修复提示词
            var prompt = BuildAIFixPrompt(errors);
            
            // 请求AI修复建议
            var aiResponse = await _aiService.GenerateTextAsync(prompt, config: null);

            // 更新编译会话
            buildSession.AIFixRequested = true;
            buildSession.AIFixResponse = aiResponse;
            buildSession.UpdatedTime = DateTime.Now;
            await _buildSessionRepository.UpdateAsync(buildSession);

            _logger.LogInformation("AI修复建议生成完成，会话ID: {SessionId}", sessionId);
            return aiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求AI修复失败，会话ID: {SessionId}", sessionId);
            throw;
        }
    }

    /// <summary>
    /// 执行项目编译
    /// </summary>
    private static async Task<string> ExecuteBuildAsync(string projectPath, string configuration, string platform)
    {
        var startInfo = new ProcessStartInfo
        {
            FileName = "dotnet",
            Arguments = $"build \"{projectPath}\" --configuration {configuration} --verbosity normal",
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = new Process { StartInfo = startInfo };
        process.Start();

        var output = await process.StandardOutput.ReadToEndAsync();
        var error = await process.StandardError.ReadToEndAsync();
        
        await process.WaitForExitAsync();

        return output + Environment.NewLine + error;
    }

    /// <summary>
    /// 解析编译输出中的错误信息
    /// </summary>
    private static List<VSBuildError> ParseBuildErrors(string buildOutput, string sessionId)
    {
        var errors = new List<VSBuildError>();
        var lines = buildOutput.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        // 正则表达式匹配编译错误格式
        // 例如: C:\path\file.cs(10,5): error CS0103: The name 'variable' does not exist
        var errorPattern = @"^(.+?)\((\d+),(\d+)\):\s*(error|warning)\s+([A-Z]+\d+):\s*(.+)$";
        var regex = new Regex(errorPattern, RegexOptions.IgnoreCase);

        foreach (var line in lines)
        {
            var match = regex.Match(line.Trim());
            if (match.Success)
            {
                var error = new VSBuildError
                {
                    BuildSessionId = sessionId,
                    FilePath = match.Groups[1].Value,
                    LineNumber = int.Parse(match.Groups[2].Value),
                    ColumnNumber = int.Parse(match.Groups[3].Value),
                    Severity = match.Groups[4].Value,
                    ErrorCode = match.Groups[5].Value,
                    ErrorMessage = match.Groups[6].Value,
                    BuildTime = DateTime.Now
                };

                errors.Add(error);
            }
        }

        return errors;
    }

    /// <summary>
    /// 更新编译会话统计信息
    /// </summary>
    private async Task UpdateBuildSessionAsync(string sessionId, List<VSBuildError> errors, bool buildSucceeded)
    {
        var buildSession = await _buildSessionRepository.GetFirstOrDefaultAsync(x => x.SessionId == sessionId);
        if (buildSession != null)
        {
            buildSession.EndTime = DateTime.Now;
            buildSession.BuildResult = buildSucceeded ? "Success" : "Failed";
            buildSession.TotalErrors = errors.Count(e => e.Severity.Equals("error", StringComparison.OrdinalIgnoreCase));
            buildSession.TotalWarnings = errors.Count(e => e.Severity.Equals("warning", StringComparison.OrdinalIgnoreCase));
            buildSession.UpdatedTime = DateTime.Now;

            await _buildSessionRepository.UpdateAsync(buildSession);
        }
    }

    /// <summary>
    /// 构建AI修复提示词
    /// </summary>
    private static string BuildAIFixPrompt(List<VSBuildError> errors)
    {
        var prompt = @"你是一个专业的C#开发专家。以下是Visual Studio编译时遇到的错误，请分析这些错误并提供修复建议：

编译错误列表：
";

        foreach (var error in errors.Take(10)) // 限制最多10个错误避免提示词过长
        {
            prompt += $@"
错误 {errors.IndexOf(error) + 1}:
- 文件: {error.FilePath}
- 位置: 第{error.LineNumber}行，第{error.ColumnNumber}列
- 错误代码: {error.ErrorCode}
- 错误信息: {error.ErrorMessage}
- 严重程度: {error.Severity}
";
        }

        prompt += @"

请为每个错误提供：
1. 错误原因分析
2. 具体的修复建议
3. 如果可能，提供修复后的代码示例
4. 预防类似错误的建议

请用中文回答，并按照错误编号顺序组织回答。";

        return prompt;
    }

    /// <summary>
    /// 获取编译会话详情
    /// </summary>
    public async Task<VSBuildSession?> GetBuildSessionAsync(string sessionId)
    {
        return await _buildSessionRepository.GetFirstOrDefaultAsync(x => x.SessionId == sessionId);
    }

    /// <summary>
    /// 获取编译会话的错误列表
    /// </summary>
    public async Task<List<VSBuildError>> GetBuildErrorsAsync(string sessionId)
    {
        return await _buildErrorRepository.GetListAsync(x => x.BuildSessionId == sessionId);
    }

    /// <summary>
    /// 标记错误为已解决
    /// </summary>
    public async Task MarkErrorAsResolvedAsync(int errorId)
    {
        var error = await _buildErrorRepository.GetByIdAsync(errorId);
        if (error != null)
        {
            error.IsResolved = true;
            error.ResolvedAt = DateTime.Now;
            await _buildErrorRepository.UpdateAsync(error);

            // 更新会话统计
            var session = await _buildSessionRepository.GetFirstOrDefaultAsync(x => x.SessionId == error.BuildSessionId);
            if (session != null)
            {
                var resolvedCount = await _buildErrorRepository.CountAsync(x => 
                    x.BuildSessionId == error.BuildSessionId && x.IsResolved);
                session.ResolvedErrors = resolvedCount;
                await _buildSessionRepository.UpdateAsync(session);
            }
        }
    }
}
