#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发步骤管理UI - 重构后的主界面
管理和执行开发步骤任务
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from typing import Dict, List, Optional
import json
from datetime import datetime, timedelta
from pathlib import Path

# 导入Copilot聊天记录相关模块
from vscode_dom_controller import VSCodeDOMController
from copilot_chat_controller import CopilotChatController

# 导入重构后的模块
from ui_components.project_task_manager import ProjectTaskManager
from ui_components.step_manager import StepManager
from ui_components.execution_manager import ExecutionManager
from ui_components.error_manager import ErrorManager
from ui_components.copilot_manager import CopilotManager
from ui_components.template_manager import TemplateManager
from ui_components.auto_task_ui import AutoTaskUI


class DevelopmentStepsUI:
    """开发步骤管理UI类 - 重构版本"""

    def __init__(self, parent, api_client, automation_manager):
        """
        初始化开发步骤管理UI

        Args:
            parent: 父容器
            api_client: API客户端
            automation_manager: 自动化管理器
        """
        self.parent = parent
        self.api_client = api_client
        self.automation_manager = automation_manager

        # 工作模式：'development' 或 'coding_task'
        self.work_mode = 'coding_task'  # 默认使用编码任务模式

        # 设置UI引用（用于获取自定义prompt设置）
        self.settings_ui = None
        self.csharp_error_ui = None
        self.frontend_build_ui = None

        # 当前开发步骤状态管理
        self.current_developing_step = None  # 当前正在开发的步骤对象
        self.current_developing_step_id = None  # 当前正在开发的步骤ID
        self.development_start_time = None  # 开发开始时间
        self.development_task_id = None  # 当前开发步骤所属的任务ID

        # 初始化各个管理器
        self.project_task_manager = ProjectTaskManager(self, api_client)
        self.step_manager = StepManager(self, api_client)
        self.execution_manager = ExecutionManager(self, api_client, automation_manager)
        self.error_manager = ErrorManager(self, api_client)
        self.copilot_manager = CopilotManager(self)
        self.template_manager = TemplateManager(self, api_client)

        # 自动化任务UI将在create_interface中初始化
        self.auto_task_ui = None

        # 创建界面
        self.create_interface()

        # 加载初始数据
        self.project_task_manager.refresh_projects()

    # 回调方法 - 项目任务管理器事件
    def on_project_selected(self, project):
        """项目选中回调"""
        # 清空开发步骤数据
        self.step_manager.clear_development_steps_display()
        
        # 预加载项目模板
        self.template_manager.preload_project_templates(project)
        
        # 自动刷新项目的编译错误
        if hasattr(self.error_manager, 'auto_refresh_project_compilation_errors'):
            self.error_manager.auto_refresh_project_compilation_errors()

    def on_project_cleared(self):
        """项目清空回调"""
        self.step_manager.clear_development_steps_display()

    def on_task_selected(self, task):
        """编码任务选中回调"""
        # 清空开发步骤数据
        self.step_manager.clear_development_steps_display()
        
        # 刷新该任务的步骤
        self.step_manager.refresh_development_steps(
            self.project_task_manager.get_selected_project(),
            task
        )

    def on_task_cleared(self):
        """编码任务清空回调"""
        if self.work_mode == 'coding_task':
            self.step_manager.clear_development_steps_display()

    # 回调方法 - 步骤管理器事件
    def on_step_selected(self, step):
        """步骤选中回调"""
        # 更新模板序列显示
        self.template_manager.update_template_sequences_display(step)

        # 预下载参考图片
        self.template_manager.predownload_reference_images(step)

        # 自动刷新后端编译错误
        self.error_manager.auto_refresh_backend_errors()

        # 自动刷新前端编译错误
        self.error_manager.auto_refresh_frontend_errors()

        # 检查是否有InProgress状态的步骤，并更新按钮状态
        self.update_button_states(step)

    # 属性访问方法（保持兼容性）
    @property
    def selected_project(self):
        return self.project_task_manager.get_selected_project()

    @property
    def selected_task(self):
        return self.project_task_manager.get_selected_task()

    @property
    def selected_step(self):
        return self.step_manager.get_selected_step()

    def refresh_development_steps(self):
        """刷新开发步骤（保持兼容性）"""
        self.step_manager.refresh_development_steps(
            self.project_task_manager.get_selected_project(),
            self.project_task_manager.get_selected_task()
        )
        # 刷新后更新按钮状态
        if hasattr(self, 'selected_step') and self.selected_step:
            self.update_button_states(self.selected_step)

        # 刷新后尝试恢复当前开发步骤（如果没有记录的话）
        if not self.current_developing_step:
            self._recover_current_developing_step()

    # 委托方法 - 转发到相应的管理器
    def refresh_data(self):
        """刷新数据"""
        self.project_task_manager.refresh_projects()

    def execute_selected_step(self):
        """执行选中的步骤"""
        self.execution_manager.execute_selected_step(self.selected_step)

    def stop_execution(self):
        """停止执行"""
        self.execution_manager.stop_execution()

    def start_loop_detection(self):
        """开始循环检测"""
        self.execution_manager.start_loop_detection()

    def stop_loop_detection(self):
        """停止循环检测"""
        self.execution_manager.stop_loop_detection()

    def update_selected_step_status(self):
        """更新选中步骤的状态"""
        selected_step = self.selected_step
        if not selected_step:
            messagebox.showwarning("警告", "请先选择一个步骤")
            return

        new_status = self.update_status_var.get()
        if not new_status:
            messagebox.showwarning("警告", "请选择要更新的状态")
            return

        # 确认更新
        step_name = selected_step.get('stepName', '未知步骤')
        current_status = selected_step.get('status', 'Unknown')

        if messagebox.askyesno("确认更新",
                              f"确定要将步骤 '{step_name}' 的状态从 '{current_status}' 更新为 '{new_status}' 吗？"):
            self.perform_status_update(selected_step, new_status)

    def has_inprogress_steps(self):
        """检查是否有InProgress状态的步骤"""
        if hasattr(self.step_manager, 'development_steps'):
            for step in self.step_manager.development_steps:
                if step.get('status') == 'InProgress':
                    return True
        return False

    def perform_status_update(self, step, new_status):
        """执行状态更新"""
        try:
            step_id = step.get('id')
            task_id = self.selected_task.get('id') if self.selected_task else None

            if not step_id or not task_id:
                messagebox.showerror("错误", "缺少必要的步骤ID或任务ID")
                return

            # 确保UI选中状态与要更新的步骤一致
            if hasattr(self, 'step_manager') and self.step_manager:
                current_selected = self.step_manager.selected_step
                if not current_selected or current_selected.get('id') != step_id:
                    print(f"🔄 自动选中要更新的步骤: {step.get('stepName', '未知')}")
                    self.step_manager.select_step_by_id(step_id)

            # 调用API更新状态
            result = self.api_client.update_coding_task_step_flags(
                task_id=task_id,
                step_id=step_id,
                status=new_status,
                is_finish_coding=(new_status == 'Completed')
            )

            if result and result.get('success'):
                # 根据状态更新相应的进度
                progress_value = self._get_progress_by_status(new_status)
                print(f"📊 状态更新为{new_status}，同时更新开发步骤进度为{progress_value}%")

                progress_result = self.api_client.update_development_step(
                    step_id=step_id,
                    status=new_status,
                    progress=progress_value
                )

                if progress_result:
                    print(f"✅ 开发步骤状态和进度已更新: {new_status} ({progress_value}%)")
                else:
                    print(f"⚠️ 开发步骤进度更新失败，但状态更新成功")

                # 刷新步骤列表以获取最新状态
                print("🔄 状态更新成功，刷新步骤列表以获取最新状态...")
                self.refresh_development_steps()

                # 等待刷新完成后更新当前开发步骤的状态
                import time
                time.sleep(1)  # 给刷新一点时间
                self._update_current_developing_step_status_after_refresh(step, new_status)

        except Exception as e:
            print(f"❌ 更新步骤状态异常: {e}")

    def _update_current_developing_step_status_after_refresh(self, step, new_status):
        """刷新后更新当前开发步骤的状态数据"""
        try:
            step_id = step.get('id')
            if step_id and hasattr(self, 'step_manager') and self.step_manager:
                # 从刷新后的步骤列表中找到最新的步骤数据
                updated_step = self.step_manager.find_step_by_id(step_id)
                if updated_step:
                    print(f"🔄 更新当前开发步骤数据: {updated_step.get('stepName', '未知')} - 状态: {updated_step.get('status', 'Unknown')}")
                    # 如果这是当前开发步骤，更新记录
                    if self.current_developing_step and self.current_developing_step.get('id') == step_id:
                        self.current_developing_step = updated_step
                        print(f"✅ 当前开发步骤数据已更新")
        except Exception as e:
            print(f"❌ 更新当前开发步骤数据异常: {e}")

    def perform_status_update_by_id(self, step_id, new_status):
        """根据步骤ID执行状态更新（不依赖UI选中状态）"""
        try:
            # 查找步骤
            step = None
            if hasattr(self, 'step_manager') and self.step_manager:
                step = self.step_manager.find_step_by_id(step_id)

            if not step:
                print(f"❌ 未找到步骤ID {step_id} 对应的步骤")
                return False

            print(f"🔄 根据ID更新步骤状态: {step.get('stepName', '未知')} -> {new_status}")

            # 调用原有的状态更新方法
            self.perform_status_update(step, new_status)
            return True

        except Exception as e:
            print(f"❌ 根据ID更新步骤状态异常: {e}")
            return False

    def _get_progress_by_status(self, status):
        """根据状态获取相应的进度值"""
        status_progress_map = {
            'NotStarted': 0,
            'Pending': 0,
            'InProgress': 50,
            'Completed': 100,
            'Failed': 0,
            'Blocked': 0
        }
        return status_progress_map.get(status, 0)

    def set_current_developing_step(self, step, task_id=None):
        """设置当前正在开发的步骤"""
        try:
            if step:
                self.current_developing_step = step
                self.current_developing_step_id = step.get('id')
                self.development_task_id = task_id or (self.selected_task.get('id') if self.selected_task else None)

                import time
                self.development_start_time = time.time()

                step_name = step.get('stepName', '未知步骤')
                print(f"📝 设置当前开发步骤: {step_name} (ID: {self.current_developing_step_id})")
                print(f"📝 开发任务ID: {self.development_task_id}")
                print(f"📝 开发开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.development_start_time))}")
            else:
                print(f"📝 清除当前开发步骤")
                self.current_developing_step = None
                self.current_developing_step_id = None
                self.development_start_time = None
                self.development_task_id = None

        except Exception as e:
            print(f"❌ 设置当前开发步骤异常: {e}")

    def get_current_developing_step(self):
        """获取当前正在开发的步骤"""
        return self.current_developing_step

    def get_current_developing_step_id(self):
        """获取当前正在开发的步骤ID"""
        return self.current_developing_step_id

    def is_step_currently_developing(self, step_id):
        """检查指定步骤是否为当前正在开发的步骤"""
        return self.current_developing_step_id == step_id

    def update_current_developing_step_status(self, new_status):
        """更新当前正在开发步骤的状态"""
        try:
            if not self.current_developing_step:
                print("⚠️ 没有当前正在开发的步骤，尝试自动恢复...")
                # 尝试从InProgress状态的步骤中恢复当前开发步骤
                if self._recover_current_developing_step():
                    print("✅ 已自动恢复当前开发步骤")
                else:
                    print("❌ 无法恢复当前开发步骤")
                    return False

            step_name = self.current_developing_step.get('stepName', '未知步骤')
            print(f"🔄 更新当前开发步骤状态: {step_name} -> {new_status}")

            # 调用状态更新方法
            self.perform_status_update(self.current_developing_step, new_status)

            # 如果状态更新为完成，清除当前开发步骤
            if new_status == 'Completed':
                print(f"✅ 步骤 '{step_name}' 开发完成，清除当前开发步骤记录")
                self.set_current_developing_step(None)

            return True

        except Exception as e:
            print(f"❌ 更新当前开发步骤状态异常: {e}")
            return False

    def _recover_current_developing_step(self):
        """尝试从InProgress状态的步骤中恢复当前开发步骤"""
        try:
            if hasattr(self, 'step_manager') and self.step_manager:
                steps = self.step_manager.development_steps
                inprogress_steps = [step for step in steps if step.get('status') == 'InProgress']

                if len(inprogress_steps) == 1:
                    # 如果只有一个InProgress步骤，将其设为当前开发步骤
                    step = inprogress_steps[0]
                    task_id = self.selected_task.get('id') if self.selected_task else None
                    self.set_current_developing_step(step, task_id)
                    print(f"🔄 已恢复当前开发步骤: {step.get('stepName', '未知')}")
                    return True
                elif len(inprogress_steps) > 1:
                    print(f"⚠️ 发现多个InProgress步骤({len(inprogress_steps)}个)，无法自动恢复")
                    for i, step in enumerate(inprogress_steps):
                        print(f"  {i+1}. {step.get('stepName', '未知')} (ID: {step.get('id')})")
                    return False
                else:
                    print("ℹ️ 没有找到InProgress状态的步骤")
                    return False
            return False
        except Exception as e:
            print(f"❌ 恢复当前开发步骤异常: {e}")
            return False

    def get_fresh_step_status(self, step_id):
        """获取步骤的最新状态（从API）"""
        try:
            if not step_id:
                return None

            task_id = self.selected_task.get('id') if self.selected_task else None
            if not task_id:
                return None

            # 调用API获取最新的编码任务步骤信息
            result = self.api_client.get_coding_task_steps(task_id)
            if result and 'data' in result:
                steps = result['data']
                for step in steps:
                    if step.get('id') == step_id:
                        print(f"🔄 从API获取到最新步骤状态: {step.get('stepName', '未知')} - {step.get('status', 'Unknown')}")
                        return step
            return None
        except Exception as e:
            print(f"❌ 获取最新步骤状态异常: {e}")
            return None

    def force_refresh_current_step_status(self):
        """强制刷新当前选中步骤的状态"""
        try:
            if hasattr(self, 'step_manager') and self.step_manager and self.step_manager.selected_step:
                step = self.step_manager.selected_step
                step_id = step.get('id')
                step_name = step.get('stepName', '未知')
                old_status = step.get('status', 'Unknown')

                print(f"🔄 强制刷新步骤状态: {step_name} (当前状态: {old_status})")

                # 获取最新状态
                fresh_step = self.get_fresh_step_status(step_id)
                if fresh_step:
                    new_status = fresh_step.get('status', 'Unknown')
                    print(f"🔄 API返回最新状态: {new_status}")

                    if new_status != old_status:
                        print(f"✅ 状态已更新: {old_status} -> {new_status}")
                        # 更新步骤管理器中的数据
                        if hasattr(self.step_manager, 'development_steps'):
                            for i, s in enumerate(self.step_manager.development_steps):
                                if s.get('id') == step_id:
                                    self.step_manager.development_steps[i] = fresh_step
                                    print(f"✅ 已更新步骤列表中的数据")
                                    break

                        # 更新选中步骤
                        self.step_manager.selected_step = fresh_step

                        # 更新当前开发步骤（如果是同一个）
                        if self.current_developing_step and self.current_developing_step.get('id') == step_id:
                            self.current_developing_step = fresh_step
                            print(f"✅ 已更新当前开发步骤数据")

                        return fresh_step
                    else:
                        print(f"ℹ️ 状态无变化: {new_status}")
                        return step
                else:
                    print("⚠️ 无法获取最新状态")
                    return step
            else:
                print("⚠️ 没有选中的步骤")
                return None
        except Exception as e:
            print(f"❌ 强制刷新步骤状态异常: {e}")
            return None

    def _update_task_step_status_via_api(self, task_id, step_id, status, is_finish_coding):
        """通过API更新任务步骤状态（用作Copilot回调函数）

        Args:
            task_id: 任务ID
            step_id: 步骤ID
            status: 新状态
            is_finish_coding: 是否完成编码

        Returns:
            bool: 更新是否成功
        """
        try:
            print(f"🔄 通过API更新任务步骤状态: 任务ID={task_id}, 步骤ID={step_id}, 状态={status}")

            # 更新编码任务步骤状态
            result = self.api_client.update_coding_task_step_flags(
                task_id=task_id,
                step_id=step_id,
                status=status,
                is_finish_coding=is_finish_coding
            )

            if result and result.get('success'):
                print(f"✅ 编码任务步骤状态更新成功")

                # 同时更新开发步骤的进度
                progress_value = self._get_progress_by_status(status)
                print(f"📊 同步更新开发步骤进度为{progress_value}%")

                progress_result = self.api_client.update_development_step(
                    step_id=step_id,
                    status=status,
                    progress=progress_value
                )

                if progress_result:
                    print(f"✅ 开发步骤状态和进度已同步更新: {status} ({progress_value}%)")
                else:
                    print(f"⚠️ 开发步骤进度更新失败，但编码任务状态更新成功")

                # 刷新步骤列表
                self.refresh_development_steps()
                return True
            else:
                print(f"❌ 编码任务步骤状态更新失败: {result}")
                return False

        except Exception as e:
            print(f"❌ 通过API更新任务步骤状态异常: {e}")
            return False

    def update_button_states(self, selected_step):
        """根据步骤状态更新按钮状态"""
        try:
            has_inprogress = self.has_inprogress_steps()
            selected_step_status = selected_step.get('status') if selected_step else None

            # 如果有InProgress状态的步骤，且当前选中的步骤不是InProgress状态，则禁用执行相关按钮
            if has_inprogress and selected_step_status != 'InProgress':
                # 禁用执行按钮
                if hasattr(self, 'execute_button'):
                    self.execute_button.config(state="disabled")
            else:
                # 启用执行按钮
                if hasattr(self, 'execute_button'):
                    self.execute_button.config(state="normal")

            # 更新状态按钮始终启用
            if hasattr(self, 'update_status_button'):
                self.update_status_button.config(state="normal")

            # 更新状态提示
            if has_inprogress and selected_step_status != 'InProgress':
                inprogress_steps = [step.get('stepName', '未知') for step in self.step_manager.development_steps
                                  if step.get('status') == 'InProgress']
                if hasattr(self, 'status_label'):
                    self.status_label.config(text=f"有InProgress步骤: {', '.join(inprogress_steps[:2])}")
            else:
                if hasattr(self, 'status_label'):
                    self.status_label.config(text="就绪")

        except Exception as e:
            print(f"❌ 更新按钮状态异常: {e}")

    def on_update_status_changed(self, event=None):
        """状态更新下拉框选择变化事件"""
        # 重新检查按钮状态
        if hasattr(self, 'selected_step') and self.selected_step:
            self.update_button_states(self.selected_step)

    def update_execute_button_text(self):
        """更新执行按钮文本"""
        self.execution_manager.update_execute_button_text()

    def clear_template_sequences_display(self):
        """清空模板序列显示"""
        self.template_manager.clear_template_sequences_display()

    def set_csharp_error_ui(self, csharp_error_ui):
        """设置C#编译错误UI引用"""
        self.csharp_error_ui = csharp_error_ui
        print(f"✅ C#编译错误UI引用已设置: {type(csharp_error_ui)}")

    def set_settings_ui(self, settings_ui):
        """设置设置UI引用"""
        self.settings_ui = settings_ui
        print("✅ 设置UI引用已设置")

    def get_compilation_errors_for_copilot(self):
        """获取编译错误信息供Copilot处理

        Returns:
            str: 格式化的错误信息，如果没有错误返回None
        """
        try:
            error_messages = []

            # 获取后端编译错误
            backend_errors = self._get_backend_compilation_errors()
            if backend_errors:
                error_messages.append("=== C# 后端编译错误 ===")
                backend_text = f"后端编译错误（共{len(backend_errors)}个）：\n"
                for i, error in enumerate(backend_errors, 1):
                    backend_text += f"{i}. 文件: {error['file']}\n"
                    backend_text += f"   行号: {error['line']}\n"
                    backend_text += f"   错误代码: {error['code']}\n"
                    backend_text += f"   错误信息: {error['message']}\n\n"
                error_messages.append(backend_text)

            # 获取前端编译错误
            frontend_errors = self._get_frontend_compilation_errors()
            if frontend_errors:
                error_messages.append("=== 前端编译错误 ===")
                frontend_text = f"前端编译错误（共{len(frontend_errors)}个）：\n"
                for i, error in enumerate(frontend_errors, 1):
                    frontend_text += f"{i}. 文件: {error['file']}\n"
                    frontend_text += f"   行号: {error['line']}\n"
                    frontend_text += f"   错误代码: {error['code']}\n"
                    frontend_text += f"   错误信息: {error['message']}\n\n"
                error_messages.append(frontend_text)

            if error_messages:
                full_message = "\n\n".join(error_messages)
                print(f"🔍 获取到编译错误信息: {len(full_message)} 字符")
                return full_message
            else:
                print("ℹ️ 未发现编译错误")
                return None

        except Exception as e:
            print(f"❌ 获取编译错误信息失败: {e}")
            return None

    def _get_backend_compilation_errors(self):
        """获取后端C#编译错误"""
        try:
            # 通过error_manager获取后端错误
            if hasattr(self.error_manager, 'backend_error_tree'):
                errors = []
                for item in self.error_manager.backend_error_tree.get_children():
                    values = self.error_manager.backend_error_tree.item(item)['values']
                    if len(values) >= 5 and values[0].upper() == 'ERROR':  # 只获取错误，不获取警告
                        errors.append({
                            'severity': values[0],
                            'file': values[1],
                            'line': values[2],
                            'code': values[3],
                            'message': values[4]
                        })

                return errors if errors else None

            return None

        except Exception as e:
            print(f"❌ 获取后端编译错误失败: {e}")
            return None

    def _get_compilation_errors_for_copilot(self):
        """为Copilot获取编译错误信息（格式化为文本）"""
        try:
            # 获取后端和前端错误
            backend_errors = self._get_backend_compilation_errors()
            frontend_errors = self._get_frontend_compilation_errors()

            if not backend_errors and not frontend_errors:
                return None

            # 收集错误信息内容
            error_content = []

            # 添加后端错误
            if backend_errors:
                error_content.append(f"后端编译错误（共{len(backend_errors)}个）：")
                for i, error in enumerate(backend_errors[:10], 1):  # 限制最多10个错误
                    error_content.append(f"{i}. 文件: {error['file']}")
                    error_content.append(f"   行号: {error['line']}")
                    error_content.append(f"   错误代码: {error['code']}")
                    error_content.append(f"   错误信息: {error['message']}")
                    error_content.append("")  # 空行分隔

            # 添加前端错误
            if frontend_errors:
                error_content.append(f"前端编译错误（共{len(frontend_errors)}个）：")
                for i, error in enumerate(frontend_errors[:10], 1):  # 限制最多10个错误
                    error_content.append(f"{i}. 文件: {error['file']}")
                    error_content.append(f"   行号: {error['line']}")
                    error_content.append(f"   错误代码: {error['code']}")
                    error_content.append(f"   错误信息: {error['message']}")
                    error_content.append("")  # 空行分隔

            # 检查是否有实际的错误内容
            if not error_content:
                return None

            # 组合最终的错误信息（不包含"发现编译错误，请帮助修复："前缀，这个前缀由调用方添加）
            error_text = "\n".join(error_content)
            return error_text

        except Exception as e:
            print(f"❌ 获取编译错误信息失败: {e}")
            return None

    def _get_frontend_compilation_errors(self):
        """获取前端编译错误"""
        try:
            # 通过error_manager获取前端错误
            if hasattr(self.error_manager, 'frontend_error_tree'):
                errors = []
                for item in self.error_manager.frontend_error_tree.get_children():
                    values = self.error_manager.frontend_error_tree.item(item)['values']
                    if len(values) >= 5 and values[0].upper() == 'ERROR':  # 只获取错误，不获取警告
                        errors.append({
                            'severity': values[0],
                            'file': values[1],
                            'line': values[2],
                            'code': values[3],
                            'message': values[4]
                        })

                return errors if errors else None

            return None

        except Exception as e:
            print(f"❌ 获取前端编译错误失败: {e}")
            return None

    def get_custom_prompt_content(self) -> str:
        """获取自定义Prompt内容"""
        try:
            if self.settings_ui and hasattr(self.settings_ui, 'get_custom_prompt_content'):
                content = self.settings_ui.get_custom_prompt_content()
                return content
            else:
                # 如果没有设置UI引用，直接从配置文件读取
                from pathlib import Path
                import json

                config_path = Path(__file__).parent / "config.json"
                if config_path.exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        enabled = config.get('enable_custom_prompt', False)
                        content = config.get('custom_prompt', '')
                        if enabled:
                            return content
                return ""
        except Exception as e:
            print(f"❌ 获取自定义Prompt内容失败: {e}")
            return ""

    def manual_login(self):
        """手动登录"""
        # 创建登录对话框
        login_window = tk.Toplevel(self.parent)
        login_window.title("API登录")
        login_window.geometry("400x200")
        login_window.transient(self.parent)
        login_window.grab_set()

        # 居中显示
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (200 // 2)
        login_window.geometry(f"400x200+{x}+{y}")

        # 创建表单
        main_frame = ttk.Frame(login_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        username_var = tk.StringVar(value="admin")  # 默认用户名
        username_entry = ttk.Entry(main_frame, textvariable=username_var, width=30)
        username_entry.grid(row=0, column=1, pady=5, padx=(10, 0))

        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        password_var = tk.StringVar(value="123456")  # 默认密码
        password_entry = ttk.Entry(main_frame, textvariable=password_var, show="*", width=30)
        password_entry.grid(row=1, column=1, pady=5, padx=(10, 0))

        # 状态标签
        status_var = tk.StringVar(value="请输入用户名和密码")
        status_label = ttk.Label(main_frame, textvariable=status_var)
        status_label.grid(row=2, column=0, columnspan=2, pady=10)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)

        def do_login():
            username = username_var.get().strip()
            password = password_var.get().strip()

            if not username or not password:
                status_var.set("请输入用户名和密码")
                return

            status_var.set("正在登录...")
            login_window.update()

            try:
                if self.api_client.login(username, password):
                    status_var.set("登录成功！")
                    login_window.after(1000, login_window.destroy)
                    # 登录成功后刷新项目列表
                    self.refresh_data()
                else:
                    status_var.set("登录失败，请检查用户名和密码")
            except Exception as e:
                status_var.set(f"登录异常: {e}")

        def on_enter(event):
            do_login()

        # 绑定回车键
        username_entry.bind('<Return>', on_enter)
        password_entry.bind('<Return>', on_enter)

        # 按钮
        ttk.Button(button_frame, text="登录", command=do_login).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=login_window.destroy).pack(side=tk.LEFT, padx=5)

        # 焦点设置
        username_entry.focus_set()

    def get_all_execution_history(self):
        """获取所有执行历史数据"""
        def load_all_history():
            try:
                self.status_label.config(text="加载所有执行历史中...")
                self.parent.update()

                result = self.api_client.get_all_step_execution_history(page=1, page_size=100)
                if result and 'items' in result:
                    all_history = result['items']
                    self.parent.after(0, lambda: self.show_all_execution_history(all_history))
                else:
                    self.parent.after(0, lambda: self.status_label.config(text="无执行历史数据"))

            except Exception as e:
                error_msg = f"加载执行历史失败: {e}"
                self.parent.after(0, lambda: self.status_label.config(text=error_msg))

        threading.Thread(target=load_all_history, daemon=True).start()

    def show_all_execution_history(self, history_data):
        """显示所有执行历史数据"""
        # 创建新窗口显示所有执行历史
        history_window = tk.Toplevel(self.parent)
        history_window.title("所有执行历史")
        history_window.geometry("1000x600")
        history_window.transient(self.parent)
        history_window.grab_set()

        # 创建Treeview显示历史数据
        columns = ('step_name', 'execution_id', 'start_time', 'status', 'duration', 'executor', 'ai_provider')
        history_tree = ttk.Treeview(history_window, columns=columns, show='headings')

        # 设置列标题
        history_tree.heading('step_name', text='步骤名称')
        history_tree.heading('execution_id', text='执行ID')
        history_tree.heading('start_time', text='开始时间')
        history_tree.heading('status', text='状态')
        history_tree.heading('duration', text='耗时(秒)')
        history_tree.heading('executor', text='执行器')
        history_tree.heading('ai_provider', text='AI提供商')

        # 设置列宽
        history_tree.column('step_name', width=150)
        history_tree.column('execution_id', width=100)
        history_tree.column('start_time', width=120)
        history_tree.column('status', width=80)
        history_tree.column('duration', width=80)
        history_tree.column('executor', width=120)
        history_tree.column('ai_provider', width=100)

        # 滚动条
        tree_scroll = ttk.Scrollbar(history_window, orient=tk.VERTICAL, command=history_tree.yview)
        history_tree.configure(yscrollcommand=tree_scroll.set)

        # 布局
        history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # 添加数据
        for history in history_data:
            # 计算执行时长
            duration = history.get('executionDuration', 0)
            if not duration and history.get('executionEndTime'):
                try:
                    start_time = datetime.fromisoformat(history.get('executionStartTime', '').replace('Z', '+00:00'))
                    end_time = datetime.fromisoformat(history.get('executionEndTime', '').replace('Z', '+00:00'))
                    duration = int((end_time - start_time).total_seconds())
                except:
                    duration = 0

            # 格式化开始时间
            start_time_str = history.get('executionStartTime', '')
            if start_time_str:
                try:
                    start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass

            # 获取步骤名称（需要从步骤ID查找）
            step_name = f"步骤ID: {history.get('stepId', '')}"

            history_tree.insert('', tk.END, values=(
                step_name,
                history.get('executionId', '')[:8] + '...',
                start_time_str,
                history.get('executionStatus', ''),
                duration,
                history.get('executorType', ''),
                history.get('aiProvider', '')
            ))

        self.status_label.config(text=f"已加载 {len(history_data)} 条执行历史记录")

    def show_dom_settings(self):
        """显示DOM设置对话框"""
        # 这个方法暂时为空，可以后续实现设置界面
        messagebox.showinfo("DOM设置",
                           f"当前DOM监控配置：\n\n"
                           f"监控间隔: 1.0秒\n"
                           f"自适应间隔: 启用\n\n"
                           f"可在config.json中修改这些设置")

    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 顶部工具栏
        self.create_toolbar(main_frame)

        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建分栏布局
        self.create_paned_layout(content_frame)
        
        # 设置各个管理器的UI组件引用
        self.setup_manager_ui_components()

    def setup_manager_ui_components(self):
        """设置各个管理器的UI组件引用"""
        # 设置项目任务管理器的UI组件
        self.project_task_manager.set_ui_components(
            self.project_combo, self.task_combo, 
            self.project_var, self.task_var, self.status_label
        )
        
        # 设置步骤管理器的UI组件
        if hasattr(self, 'steps_tree') and hasattr(self, 'details_text'):
            self.step_manager.set_ui_components(
                self.steps_tree, self.details_text, self.status_label,
                getattr(self, 'detection_status_label', None), 
                self.status_var, self.status_combo
            )
        
        # 设置执行管理器的UI组件
        if hasattr(self, 'execute_btn'):
            self.execution_manager.set_ui_components(
                self.execute_btn,
                self.start_monitor_btn,
                self.stop_monitor_btn,
                self.loop_detection_status_label,
                getattr(self, 'script_code_text', None),
                getattr(self, 'script_status_label', None),
                getattr(self, 'script_name_label', None),
                getattr(self, 'script_language_label', None),
                getattr(self, 'script_category_label', None),
                getattr(self, 'history_tree', None),
                getattr(self, 'history_count_label', None),
                self.status_label, self.execution_mode,
                getattr(self, 'error_send_delay_var', None)
            )

        # 设置Copilot管理器的UI组件
        if hasattr(self, 'copilot_text'):
            self.copilot_manager.set_ui_components(
                self.copilot_text, self.connect_btn, self.disconnect_btn, self.refresh_btn,
                self.auto_refresh_var, self.connection_status_label, self.record_count_label
            )

        # 设置错误管理器的UI组件
        if hasattr(self, 'backend_error_tree') and hasattr(self, 'frontend_error_tree'):
            self.error_manager.set_ui_components(
                self.backend_error_tree, self.frontend_error_tree,
                self.backend_error_count_var, self.frontend_error_count_var,
                self.backend_send_to_ai_button, self.frontend_send_to_ai_button,
                self.backend_error_count_label, self.frontend_error_count_label,
                self.backend_compile_status_label, self.frontend_compile_status_label,
                self.backend_last_update_label, self.frontend_last_update_label,
                self.status_label, self.backend_auto_refresh_var, self.backend_refresh_interval_var,
                self.frontend_auto_refresh_var, self.frontend_refresh_interval_var
            )

    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_container = ttk.Frame(parent)
        toolbar_container.pack(fill=tk.X, pady=(0, 10))

        # 第一行工具栏
        toolbar1 = ttk.Frame(toolbar_container)
        toolbar1.pack(fill=tk.X, pady=(0, 2))

        # 项目和任务选择
        ttk.Label(toolbar1, text="项目:").pack(side=tk.LEFT, padx=(0, 5))
        self.project_var = tk.StringVar()
        self.project_combo = ttk.Combobox(toolbar1, textvariable=self.project_var, width=25, state='readonly')
        self.project_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(toolbar1, text="任务:").pack(side=tk.LEFT, padx=(0, 5))
        self.task_var = tk.StringVar()
        self.task_combo = ttk.Combobox(toolbar1, textvariable=self.task_var, width=25, state='readonly')
        self.task_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 基础操作按钮
        ttk.Button(toolbar1, text="🔄 刷新", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar1, text="🔑 登录", command=self.manual_login).pack(side=tk.LEFT, padx=(0, 5))

        # 执行模式选择
        self.execution_mode = tk.StringVar(value="dom")  # 默认使用DOM操作
        execution_mode_frame = ttk.Frame(toolbar1)
        execution_mode_frame.pack(side=tk.LEFT, padx=(10, 5))

        ttk.Label(execution_mode_frame, text="执行模式:").pack(side=tk.LEFT)
        self.dom_radio = ttk.Radiobutton(execution_mode_frame, text="🤖DOM操作", 
                                       variable=self.execution_mode, value="dom", 
                                       command=self.update_execute_button_text)
        self.dom_radio.pack(side=tk.LEFT, padx=(5, 0))
        self.script_radio = ttk.Radiobutton(execution_mode_frame, text="📜脚本执行", 
                                          variable=self.execution_mode, value="script", 
                                          command=self.update_execute_button_text)
        self.script_radio.pack(side=tk.LEFT, padx=(5, 0))

        # 第二行工具栏
        toolbar2 = ttk.Frame(toolbar_container)
        toolbar2.pack(fill=tk.X, pady=(2, 0))

        # 执行按钮
        self.execute_btn = ttk.Button(toolbar2, text="🤖 DOM操作执行", command=self.execute_selected_step)
        self.execute_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 停止执行按钮 - 已隐藏
        # ttk.Button(toolbar2, text="⏹️ 停止执行", command=self.stop_execution).pack(side=tk.LEFT, padx=(0, 5))

        # DOM监控相关按钮 - 已隐藏
        # ttk.Button(toolbar2, text="⚙️ DOM设置", command=self.show_dom_settings).pack(side=tk.LEFT, padx=(5, 5))

        # 循环检测控制按钮
        self.start_monitor_btn = ttk.Button(toolbar2, text="🔄 开启循环检测", command=self.start_loop_detection)
        self.start_monitor_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_monitor_btn = ttk.Button(toolbar2, text="⏹️ 停止循环检测", command=self.stop_loop_detection, state="disabled")
        self.stop_monitor_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 循环检测状态标签
        self.loop_detection_status_label = ttk.Label(toolbar2, text="🔴 未启动", foreground="gray")
        self.loop_detection_status_label.pack(side=tk.LEFT, padx=(5, 10))

        # 错误发送延迟设置（从配置文件读取默认值）
        ttk.Label(toolbar2, text="错误发送延迟:").pack(side=tk.LEFT, padx=(5, 2))
        default_delay = self._get_error_send_delay_from_config()
        self.error_send_delay_var = tk.StringVar(value=str(default_delay))
        self.error_send_delay_entry = ttk.Entry(toolbar2, textvariable=self.error_send_delay_var, width=5)
        self.error_send_delay_entry.pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(toolbar2, text="秒").pack(side=tk.LEFT, padx=(0, 10))

        # 绑定延迟设置变化事件
        self.error_send_delay_var.trace('w', self._on_error_send_delay_changed)

        # 分隔符
        ttk.Separator(toolbar2, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # 状态筛选
        ttk.Label(toolbar2, text="筛选状态:").pack(side=tk.LEFT, padx=(10, 5))
        self.status_var = tk.StringVar()
        self.status_combo = ttk.Combobox(toolbar2, textvariable=self.status_var, width=15, state='readonly')
        self.status_combo['values'] = ('全部', 'Pending', 'InProgress', 'Completed', 'Failed', 'Blocked')
        self.status_combo.set('全部')
        self.status_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 分隔符
        ttk.Separator(toolbar2, orient='vertical').pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # 状态更新
        ttk.Label(toolbar2, text="更新状态:").pack(side=tk.LEFT, padx=(10, 5))
        self.update_status_var = tk.StringVar()
        self.update_status_combo = ttk.Combobox(toolbar2, textvariable=self.update_status_var, width=15, state='readonly')
        self.update_status_combo['values'] = ('Pending', 'InProgress', 'Completed', 'Failed', 'Blocked')
        self.update_status_combo.set('Completed')
        self.update_status_combo.pack(side=tk.LEFT, padx=(0, 5))

        # 更新状态按钮
        self.update_status_button = ttk.Button(toolbar2, text="✅ 更新状态", command=self.update_selected_step_status)
        self.update_status_button.pack(side=tk.LEFT, padx=(0, 10))

        # 绑定状态更新下拉框的选择变化事件
        self.update_status_combo.bind('<<ComboboxSelected>>', self.on_update_status_changed)

        # 右侧状态标签
        self.status_label = ttk.Label(toolbar2, text="就绪")
        self.status_label.pack(side=tk.RIGHT)

    def _get_error_send_delay_from_config(self):
        """从配置文件读取错误发送延迟设置"""
        try:
            import json
            from pathlib import Path

            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'compilation_errors' in config:
                        delay = config['compilation_errors'].get('error_send_delay', 10)
                        print(f"📋 从配置文件读取错误发送延迟: {delay}秒")
                        return delay
            print("⚠️ 配置文件中未找到错误发送延迟配置，使用默认值10秒")
            return 10
        except Exception as e:
            print(f"❌ 读取错误发送延迟配置失败: {e}，使用默认值10秒")
            return 10

    def _on_error_send_delay_changed(self, *args):
        """错误发送延迟设置变化时的回调"""
        try:
            delay = float(self.error_send_delay_var.get())
            # 更新Copilot脚本中的延迟设置
            if hasattr(self, 'copilot_script') and self.copilot_script:
                self.copilot_script.update_error_send_delay(delay)
                print(f"✅ 错误发送延迟已更新为: {delay}秒")
        except (ValueError, TypeError):
            # 输入无效时不做处理
            pass



    def create_paned_layout(self, parent):
        """创建分栏布局"""
        # 创建水平分栏
        h_paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        h_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧：开发步骤列表
        left_frame = ttk.LabelFrame(h_paned, text="开发步骤列表", padding="5")
        h_paned.add(left_frame, weight=3)

        # 右侧：详情和执行历史
        right_frame = ttk.Frame(h_paned)
        h_paned.add(right_frame, weight=1)

        # 创建开发步骤列表
        self.create_steps_list(left_frame)

        # 创建右侧选项卡
        self.create_right_notebook(right_frame)

    def create_steps_list(self, parent):
        """创建开发步骤列表"""
        # 创建Treeview
        columns = ('name', 'type', 'status', 'priority', 'progress', 'technology')
        self.steps_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        # 设置列标题
        self.steps_tree.heading('name', text='步骤名称')
        self.steps_tree.heading('type', text='类型')
        self.steps_tree.heading('status', text='状态')
        self.steps_tree.heading('priority', text='优先级')
        self.steps_tree.heading('progress', text='进度')
        self.steps_tree.heading('technology', text='技术栈')

        # 设置列宽
        self.steps_tree.column('name', width=300)  # 增加步骤名称列宽
        self.steps_tree.column('type', width=120)
        self.steps_tree.column('status', width=100)
        self.steps_tree.column('priority', width=80)
        self.steps_tree.column('progress', width=80)
        self.steps_tree.column('technology', width=150)

        # 滚动条
        tree_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=tree_scroll.set)

        # 布局
        self.steps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_right_notebook(self, parent):
        """创建右侧选项卡"""
        self.right_notebook = ttk.Notebook(parent)
        self.right_notebook.pack(fill=tk.BOTH, expand=True)

        # 步骤详情选项卡
        details_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(details_frame, text="📋 步骤详情")

        # 步骤详情文本框
        self.details_text = tk.Text(details_frame, wrap=tk.WORD, state=tk.DISABLED, font=('Consolas', 10))
        details_scroll = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scroll.set)

        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 模板序列选项卡（暂时隐藏）
        # template_frame = ttk.Frame(self.right_notebook)
        # self.right_notebook.add(template_frame, text="🔗 模板序列")
        # self.create_template_sequences_panel(template_frame)

        # Copilot聊天记录选项卡（隐藏）
        # copilot_frame = ttk.Frame(self.right_notebook)
        # self.right_notebook.add(copilot_frame, text="💬 聊天记录")
        # self.create_copilot_chat_panel(copilot_frame)

        # 执行脚本选项卡（隐藏）
        # script_frame = ttk.Frame(self.right_notebook)
        # self.right_notebook.add(script_frame, text="📜 执行脚本")
        # self.create_script_execution_panel(script_frame)

        # 自动化任务选项卡
        auto_task_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(auto_task_frame, text="🤖 自动化任务")
        self.create_auto_task_panel(auto_task_frame)

        # 执行历史选项卡
        history_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(history_frame, text="📊 执行历史")
        self.create_execution_history(history_frame)

    def create_auto_task_panel(self, parent):
        """创建自动化任务面板"""
        try:
            # 初始化自动化任务UI
            self.auto_task_ui = AutoTaskUI(parent, self, self.api_client)
            print("✅ 自动化任务UI已初始化")
        except Exception as e:
            print(f"❌ 初始化自动化任务UI失败: {e}")
            # 创建错误提示标签
            error_label = ttk.Label(parent, text=f"自动化任务UI初始化失败: {e}")
            error_label.pack(pady=20)

        # 后端编译错误选项卡
        backend_errors_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(backend_errors_frame, text="🔧 后端编译错误")
        self.create_backend_errors_panel(backend_errors_frame)

        # 前端编译错误选项卡
        frontend_errors_frame = ttk.Frame(self.right_notebook)
        self.right_notebook.add(frontend_errors_frame, text="🌐 前端编译错误")
        self.create_frontend_errors_panel(frontend_errors_frame)

    def create_copilot_chat_panel(self, parent):
        """创建Copilot聊天面板"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 连接按钮
        self.connect_btn = ttk.Button(toolbar, text="🔗 连接Copilot", command=self.connect_copilot)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.disconnect_btn = ttk.Button(toolbar, text="🔌 断开连接", command=self.disconnect_copilot, state="disabled")
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 刷新按钮
        self.refresh_btn = ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_copilot_records, state="disabled")
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 自动刷新选项
        self.auto_refresh_var = tk.BooleanVar()
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.auto_refresh_var,
                       command=self.toggle_copilot_auto_refresh).pack(side=tk.LEFT, padx=(10, 0))

        # 状态信息
        status_frame = ttk.Frame(toolbar)
        status_frame.pack(side=tk.RIGHT)

        self.connection_status_label = ttk.Label(status_frame, text="🔴 未连接", foreground="gray")
        self.connection_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.record_count_label = ttk.Label(status_frame, text="记录数: 0")
        self.record_count_label.pack(side=tk.RIGHT, padx=(0, 10))

        # 聊天记录显示
        self.copilot_text = scrolledtext.ScrolledText(main_frame, height=20, width=80,
                                                     font=('Consolas', 10), wrap=tk.WORD, state=tk.DISABLED)
        self.copilot_text.pack(fill=tk.BOTH, expand=True)

    def create_script_execution_panel(self, parent):
        """创建脚本执行面板"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 脚本信息区域
        info_frame = ttk.LabelFrame(main_frame, text="脚本信息", padding=5)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # 脚本信息显示
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill=tk.X)

        ttk.Label(info_grid, text="名称:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.script_name_label = ttk.Label(info_grid, text="未选择脚本")
        self.script_name_label.grid(row=0, column=1, sticky="w")

        ttk.Label(info_grid, text="语言:").grid(row=1, column=0, sticky="w", padx=(0, 5))
        self.script_language_label = ttk.Label(info_grid, text="-")
        self.script_language_label.grid(row=1, column=1, sticky="w")

        ttk.Label(info_grid, text="分类:").grid(row=2, column=0, sticky="w", padx=(0, 5))
        self.script_category_label = ttk.Label(info_grid, text="-")
        self.script_category_label.grid(row=2, column=1, sticky="w")

        # 脚本代码显示
        code_frame = ttk.LabelFrame(main_frame, text="脚本代码", padding=5)
        code_frame.pack(fill=tk.BOTH, expand=True)

        # 脚本代码文本框
        self.script_code_text = scrolledtext.ScrolledText(code_frame, height=20, width=80,
                                                         font=('Consolas', 10), wrap=tk.NONE)
        self.script_code_text.pack(fill=tk.BOTH, expand=True)

        # 脚本执行状态标签
        status_frame = ttk.Frame(code_frame)
        status_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(status_frame, text="执行状态:").pack(side=tk.LEFT)
        self.script_status_label = ttk.Label(status_frame, text="就绪", foreground="green")
        self.script_status_label.pack(side=tk.LEFT, padx=(5, 0))

    def create_execution_history(self, parent):
        """创建执行历史列表"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar, text="🔄 刷新历史", command=self.refresh_execution_history).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="🗑️ 清空历史", command=self.clear_execution_history).pack(side=tk.LEFT)

        # 历史记录数量标签
        self.history_count_label = ttk.Label(toolbar, text="历史记录: 0")
        self.history_count_label.pack(side=tk.RIGHT)

        # 创建Treeview
        columns = ('execution_id', 'start_time', 'status', 'duration', 'executor')
        self.history_tree = ttk.Treeview(main_frame, columns=columns, show='headings')

        # 设置列标题
        self.history_tree.heading('execution_id', text='执行ID')
        self.history_tree.heading('start_time', text='开始时间')
        self.history_tree.heading('status', text='状态')
        self.history_tree.heading('duration', text='耗时(秒)')
        self.history_tree.heading('executor', text='执行器')

        # 设置列宽
        self.history_tree.column('execution_id', width=100)
        self.history_tree.column('start_time', width=120)
        self.history_tree.column('status', width=80)
        self.history_tree.column('duration', width=80)
        self.history_tree.column('executor', width=120)

        # 滚动条
        history_scroll = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scroll.set)

        # 布局
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_backend_errors_panel(self, parent):
        """创建后端编译错误面板"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 刷新按钮
        ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_backend_errors).pack(side=tk.LEFT, padx=(0, 5))

        # 清空按钮
        ttk.Button(toolbar, text="🗑️ 清空", command=self.clear_backend_errors).pack(side=tk.LEFT, padx=(0, 5))

        # 自动刷新选项
        self.backend_auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.backend_auto_refresh_var,
                       command=self.toggle_backend_auto_refresh).pack(side=tk.LEFT, padx=(10, 0))

        # 刷新间隔设置
        ttk.Label(toolbar, text="间隔(秒):").pack(side=tk.LEFT, padx=(10, 5))
        self.backend_refresh_interval_var = tk.StringVar(value="30")
        interval_combo = ttk.Combobox(toolbar, textvariable=self.backend_refresh_interval_var,
                                    width=8, values=('10', '30', '60', '120'))
        interval_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 发送给AI按钮
        self.backend_send_to_ai_button = ttk.Button(toolbar, text="🤖 发送给AI修复",
                                                   command=self.send_backend_errors_to_ai, state="disabled")
        self.backend_send_to_ai_button.pack(side=tk.LEFT, padx=(10, 0))

        # 错误条数输入
        ttk.Label(toolbar, text="条数:").pack(side=tk.LEFT, padx=(5, 2))
        self.backend_error_count_var = tk.StringVar(value="5")
        count_entry = ttk.Entry(toolbar, textvariable=self.backend_error_count_var, width=5)
        count_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 状态信息
        status_frame = ttk.Frame(toolbar)
        status_frame.pack(side=tk.RIGHT)

        self.backend_compile_status_label = ttk.Label(status_frame, text="未检查", foreground="gray")
        self.backend_compile_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.backend_error_count_label = ttk.Label(status_frame, text="错误: 0 | 警告: 0")
        self.backend_error_count_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.backend_last_update_label = ttk.Label(status_frame, text="更新: 未检查")
        self.backend_last_update_label.pack(side=tk.RIGHT, padx=(0, 10))

        # 错误列表
        # 创建Treeview
        columns = ('severity', 'file', 'line', 'code', 'message', 'time')
        self.backend_error_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        self.backend_error_tree.heading('severity', text='严重程度')
        self.backend_error_tree.heading('file', text='文件')
        self.backend_error_tree.heading('line', text='行号')
        self.backend_error_tree.heading('code', text='错误代码')
        self.backend_error_tree.heading('message', text='错误信息')
        self.backend_error_tree.heading('time', text='时间')

        # 设置列宽
        self.backend_error_tree.column('severity', width=80)
        self.backend_error_tree.column('file', width=150)
        self.backend_error_tree.column('line', width=60)
        self.backend_error_tree.column('code', width=80)
        self.backend_error_tree.column('message', width=300)
        self.backend_error_tree.column('time', width=80)

        # 滚动条
        backend_scroll = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.backend_error_tree.yview)
        self.backend_error_tree.configure(yscrollcommand=backend_scroll.set)

        # 布局
        self.backend_error_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        backend_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.backend_error_tree.bind('<Double-1>', self.on_backend_error_double_click)

    def create_frontend_errors_panel(self, parent):
        """创建前端编译错误面板"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 刷新按钮
        ttk.Button(toolbar, text="🔄 刷新", command=self.refresh_frontend_errors).pack(side=tk.LEFT, padx=(0, 5))

        # 清空按钮
        ttk.Button(toolbar, text="🗑️ 清空", command=self.clear_frontend_errors).pack(side=tk.LEFT, padx=(0, 5))

        # 自动刷新选项
        self.frontend_auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.frontend_auto_refresh_var,
                       command=self.toggle_frontend_auto_refresh).pack(side=tk.LEFT, padx=(10, 0))

        # 刷新间隔设置
        ttk.Label(toolbar, text="间隔(秒):").pack(side=tk.LEFT, padx=(10, 5))
        self.frontend_refresh_interval_var = tk.StringVar(value="30")
        interval_combo = ttk.Combobox(toolbar, textvariable=self.frontend_refresh_interval_var,
                                    width=8, values=('10', '30', '60', '120'))
        interval_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 发送给AI按钮
        self.frontend_send_to_ai_button = ttk.Button(toolbar, text="🤖 发送给AI修复",
                                                    command=self.send_frontend_errors_to_ai, state="disabled")
        self.frontend_send_to_ai_button.pack(side=tk.LEFT, padx=(10, 0))

        # 错误条数输入
        ttk.Label(toolbar, text="条数:").pack(side=tk.LEFT, padx=(5, 2))
        self.frontend_error_count_var = tk.StringVar(value="5")
        count_entry = ttk.Entry(toolbar, textvariable=self.frontend_error_count_var, width=5)
        count_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 状态信息
        status_frame = ttk.Frame(toolbar)
        status_frame.pack(side=tk.RIGHT)

        self.frontend_compile_status_label = ttk.Label(status_frame, text="未检查", foreground="gray")
        self.frontend_compile_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.frontend_error_count_label = ttk.Label(status_frame, text="错误: 0 | 警告: 0")
        self.frontend_error_count_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.frontend_last_update_label = ttk.Label(status_frame, text="更新: 未检查")
        self.frontend_last_update_label.pack(side=tk.RIGHT, padx=(0, 10))

        # 错误列表
        # 创建Treeview
        columns = ('severity', 'file', 'line', 'code', 'message', 'time')
        self.frontend_error_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        self.frontend_error_tree.heading('severity', text='严重程度')
        self.frontend_error_tree.heading('file', text='文件')
        self.frontend_error_tree.heading('line', text='行号')
        self.frontend_error_tree.heading('code', text='错误代码')
        self.frontend_error_tree.heading('message', text='错误信息')
        self.frontend_error_tree.heading('time', text='时间')

        # 设置列宽
        self.frontend_error_tree.column('severity', width=80)
        self.frontend_error_tree.column('file', width=150)
        self.frontend_error_tree.column('line', width=60)
        self.frontend_error_tree.column('code', width=80)
        self.frontend_error_tree.column('message', width=300)
        self.frontend_error_tree.column('time', width=80)

        # 滚动条
        frontend_scroll = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.frontend_error_tree.yview)
        self.frontend_error_tree.configure(yscrollcommand=frontend_scroll.set)

        # 布局
        self.frontend_error_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        frontend_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.frontend_error_tree.bind('<Double-1>', self.on_frontend_error_double_click)

    # 委托方法 - Copilot相关
    def connect_copilot(self):
        """连接Copilot"""
        self.copilot_manager.connect_copilot()

    def disconnect_copilot(self):
        """断开Copilot连接"""
        self.copilot_manager.disconnect_copilot()

    def refresh_copilot_records(self):
        """刷新Copilot聊天记录"""
        self.copilot_manager.refresh_copilot_records()

    def toggle_copilot_auto_refresh(self):
        """切换Copilot自动刷新"""
        self.copilot_manager.toggle_copilot_auto_refresh()

    # 委托方法 - 执行历史相关
    def refresh_execution_history(self):
        """刷新执行历史"""
        print("刷新执行历史功能待实现")

    def clear_execution_history(self):
        """清空执行历史"""
        print("清空执行历史功能待实现")

    # 委托方法 - 后端编译错误相关
    def refresh_backend_errors(self):
        """刷新后端编译错误"""
        self.error_manager.refresh_backend_errors()

    def clear_backend_errors(self):
        """清空后端编译错误"""
        self.error_manager.clear_backend_errors()

    def toggle_backend_auto_refresh(self):
        """切换后端自动刷新"""
        self.error_manager.toggle_auto_refresh()

    def send_backend_errors_to_ai(self):
        """发送后端错误给AI修复"""
        self.error_manager.send_errors_to_ai()

    # 委托方法 - 前端编译错误相关
    def refresh_frontend_errors(self):
        """刷新前端编译错误"""
        self.error_manager.refresh_frontend_errors()

    def clear_frontend_errors(self):
        """清空前端编译错误"""
        self.error_manager.clear_frontend_errors()

    def toggle_frontend_auto_refresh(self):
        """切换前端自动刷新"""
        self.error_manager.toggle_frontend_auto_refresh()

    def send_frontend_errors_to_ai(self):
        """发送前端错误给AI修复"""
        self.error_manager.send_frontend_errors_to_ai()

    # 委托方法 - 错误双击事件
    def on_backend_error_double_click(self, event):
        """后端错误双击事件"""
        self.error_manager.on_backend_error_double_click(event)

    def on_frontend_error_double_click(self, event):
        """前端错误双击事件"""
        self.error_manager.on_frontend_error_double_click(event)

    def create_template_sequences_panel(self, parent):
        """创建模板序列面板"""
        # 主容器
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 序列数量标签
        self.sequence_count_label = ttk.Label(toolbar, text="序列数量: 0")
        self.sequence_count_label.pack(side=tk.LEFT)

        # 刷新按钮
        ttk.Button(toolbar, text="🔄 刷新序列",
                  command=self.refresh_template_sequences).pack(side=tk.RIGHT)

        # 模板序列流程显示区域
        # 创建带滚动条的Canvas
        self.flow_canvas = tk.Canvas(main_frame, bg='white')
        flow_scrollbar_v = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.flow_canvas.yview)
        flow_scrollbar_h = ttk.Scrollbar(main_frame, orient=tk.HORIZONTAL, command=self.flow_canvas.xview)

        self.flow_frame = ttk.Frame(self.flow_canvas)
        self.flow_canvas_window = self.flow_canvas.create_window((0, 0), window=self.flow_frame, anchor="nw")

        self.flow_canvas.configure(yscrollcommand=flow_scrollbar_v.set, xscrollcommand=flow_scrollbar_h.set)

        # 布局
        self.flow_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        flow_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        flow_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 设置模板管理器的UI组件
        self.template_manager.set_ui_components(
            self.flow_canvas, self.flow_frame, self.flow_canvas_window, self.sequence_count_label
        )

        # 显示初始消息
        self.template_manager.show_empty_flow_message()

    def refresh_template_sequences(self):
        """刷新模板序列"""
        if self.selected_step:
            self.template_manager.refresh_template_sequences(self.selected_step)
        else:
            self.template_manager.show_empty_flow_message()

    def download_project_files_before_execution(self, step_id: int, step_name: str):
        """执行步骤前下载项目文件"""
        try:
            print(f"🔄 开始下载项目文件 - 步骤ID: {step_id}, 步骤名称: {step_name}")

            # 获取下载路径配置
            config_path = Path(__file__).parent / "config.json"
            download_path = ""
            print(f"📁 配置文件路径: {config_path}")

            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        download_path = config.get('download_path', '')
                        print(f"📋 从配置文件读取到下载路径: '{download_path}'")
                except Exception as e:
                    print(f"❌ 读取配置文件失败: {e}")

            # 如果没有配置下载路径，跳过下载
            if not download_path:
                print("⚠️ 未配置下载路径，跳过文件下载")
                return

            # 确保下载目录存在
            download_dir = Path(download_path)
            print(f"📂 检查下载目录是否存在: {download_dir}")
            if not download_dir.exists():
                print(f"❌ 下载目录不存在: {download_path}")
                print(f"🔧 尝试创建下载目录...")
                try:
                    download_dir.mkdir(parents=True, exist_ok=True)
                    print(f"✅ 下载目录创建成功: {download_path}")
                except Exception as e:
                    print(f"❌ 创建下载目录失败: {e}")
                    return

            # 获取当前项目ID
            print(f"🔍 检查选中的项目: {self.selected_project}")
            if not self.selected_project:
                print("⚠️ 未选择项目，跳过文件下载")
                return

            project_id = self.selected_project.get('id')
            if not project_id:
                print("❌ 无法获取项目ID，跳过文件下载")
                print(f"🔍 项目数据: {self.selected_project}")
                return

            print(f"🎯 开始下载项目 {project_id} 的文件到: {download_path}")

            # 确保API客户端已认证
            print(f"🔐 检查API认证状态: {self.api_client.is_authenticated}")
            if not self.api_client.is_authenticated:
                print("🔑 尝试API登录...")
                if not self.api_client.login("admin", "password"):
                    print("❌ API认证失败，跳过文件下载")
                    return
                else:
                    print("✅ API认证成功")

            # 根据配置决定是否清除之前下载的文件
            clear_old_files = config.get('clear_old_files', True)
            if clear_old_files:
                print(f"🧹 清除之前下载的项目文件...")
                try:
                    import glob
                    import shutil

                    # 查找所有以 project_{project_id}_ 开头的目录
                    pattern = str(download_dir / f"project_{project_id}_*")
                    old_dirs = glob.glob(pattern)

                    for old_dir in old_dirs:
                        try:
                            shutil.rmtree(old_dir)
                            print(f"🗑️ 删除旧目录: {old_dir}")
                        except Exception as e:
                            print(f"⚠️ 删除旧目录失败: {old_dir}, 错误: {e}")

                    if old_dirs:
                        print(f"✅ 清除完成，删除了 {len(old_dirs)} 个旧目录")
                    else:
                        print(f"ℹ️ 没有找到需要清除的旧文件")

                except Exception as e:
                    print(f"⚠️ 清除旧文件时出错: {e}")
            else:
                print(f"ℹ️ 配置为不清除旧文件，保留历史下载")

            # 创建项目专用目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_dir = download_dir / f"project_{project_id}_{timestamp}"
            print(f"📁 创建项目专用目录: {project_dir}")
            project_dir.mkdir(exist_ok=True)
            print(f"✅ 项目目录创建成功: {project_dir}")

            download_count = 0

            # 从配置文件读取下载API配置
            download_apis = config.get('download_apis', [])
            if not download_apis:
                print("⚠️ 配置文件中未找到下载API配置，使用默认配置")
                # 默认配置作为后备
                download_apis = [
                    {
                        "name": "需求文档",
                        "endpoint": "/api/projects/{project_id}/download-requirements",
                        "filename": "需求文档_{project_id}.md",
                        "description": "项目需求文档"
                    },
                    {
                        "name": "ER图",
                        "endpoint": "/api/projects/{project_id}/download-er-diagrams",
                        "filename": "ER图_{project_id}.mermaid",
                        "description": "数据库ER图"
                    },
                    {
                        "name": "上下文图",
                        "endpoint": "/api/projects/{project_id}/download-context-diagrams",
                        "filename": "上下文图_{project_id}.mermaid",
                        "description": "系统上下文图"
                    },
                    {
                        "name": "原型图",
                        "endpoint": "/api/projects/{project_id}/download-prototypes",
                        "filename": "原型图_{project_id}.mermaid",
                        "description": "界面原型图"
                    }
                ]

            print(f"📋 找到 {len(download_apis)} 个下载API配置")

            # 循环下载所有配置的文件
            for api_config in download_apis:
                try:
                    name = api_config.get('name', '未知文件')
                    endpoint = api_config.get('endpoint', '')
                    filename = api_config.get('filename', f'file_{project_id}')
                    description = api_config.get('description', name)

                    # 替换占位符
                    endpoint = endpoint.replace('{project_id}', str(project_id))
                    filename = filename.replace('{project_id}', str(project_id))

                    print(f"📄 开始下载{name}...")

                    api_url = f"{self.api_client.base_url}{endpoint}"
                    print(f"🌐 API请求URL: {api_url}")

                    response = self.api_client.session.get(
                        api_url,
                        headers={'Authorization': f'Bearer {self.api_client.token}'},
                        timeout=30
                    )
                    print(f"📡 {name} API响应状态码: {response.status_code}")

                    if response.status_code == 200:
                        file_path = project_dir / filename
                        print(f"💾 保存{name}到: {file_path}")
                        with open(file_path, 'wb') as f:
                            f.write(response.content)
                        download_count += 1
                        print(f"✅ 下载{name}成功: {file_path}")
                    else:
                        print(f"❌ {name}下载失败: HTTP {response.status_code}")
                        if response.text:
                            print(f"📄 响应内容: {response.text[:200]}")

                except Exception as e:
                    print(f"❌ {name}下载失败: {e}")
                    import traceback
                    print(f"🔍 详细错误信息: {traceback.format_exc()}")

            if download_count > 0:
                print(f"📥 文件下载完成！成功下载 {download_count} 个文件到: {project_dir}")
                # 使用parent.after来安全更新UI
                if hasattr(self, 'parent') and self.parent:
                    self.parent.after(0, lambda: self.status_label.config(text=f"已下载 {download_count} 个项目文件"))
            else:
                print("⚠️ 没有文件被下载")

        except Exception as e:
            print(f"❌ 下载项目文件失败: {e}")
