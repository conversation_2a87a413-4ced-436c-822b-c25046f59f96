using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// Selenium测试脚本实体
    /// </summary>
    [SugarTable("SeleniumScripts")]
    public class SeleniumScript : BaseEntity
    {
        /// <summary>
        /// 脚本名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "脚本名称不能为空")]
        [StringLength(100, ErrorMessage = "脚本名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 脚本描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 脚本分类
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "分类不能为空")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Python代码
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? Code { get; set; }

        /// <summary>
        /// 配置信息(JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? ConfigJson { get; set; }

        /// <summary>
        /// 标签(JSON数组格式)
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        public string? TagsJson { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Priority { get; set; } = "medium";

        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Status { get; set; } = "draft";

        /// <summary>
        /// 项目ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ProjectId { get; set; }

        /// <summary>
        /// 最后执行时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastExecutedTime { get; set; }

        /// <summary>
        /// 执行次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ExecutionCount { get; set; } = 0;

        /// <summary>
        /// 成功次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SuccessCount { get; set; } = 0;

        /// <summary>
        /// 平均执行时长(秒)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AvgDuration { get; set; }

        // 导航属性
        /// <summary>
        /// 关联项目
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Project? Project { get; set; }

        /// <summary>
        /// 执行记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SeleniumExecution>? Executions { get; set; }
    }

    /// <summary>
    /// Selenium执行记录实体
    /// </summary>
    [SugarTable("SeleniumExecutions")]
    public class SeleniumExecution : BaseEntity
    {
        /// <summary>
        /// 执行ID(GUID)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required]
        public string ExecutionId { get; set; } = string.Empty;

        /// <summary>
        /// 脚本ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required]
        public int ScriptId { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Status { get; set; } = "running";

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行时长(秒)
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? Duration { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 执行配置(JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? ConfigJson { get; set; }

        /// <summary>
        /// 执行统计(JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? StatsJson { get; set; }

        /// <summary>
        /// 截图路径(JSON数组格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? ScreenshotsJson { get; set; }

        /// <summary>
        /// 执行环境信息
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Environment { get; set; }

        // 导航属性
        /// <summary>
        /// 关联脚本
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public SeleniumScript? Script { get; set; }

        /// <summary>
        /// 执行日志
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SeleniumExecutionLog>? Logs { get; set; }
    }

    /// <summary>
    /// Selenium执行日志实体
    /// </summary>
    [SugarTable("SeleniumExecutionLogs")]
    public class SeleniumExecutionLog : BaseEntity
    {
        /// <summary>
        /// 执行记录ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required]
        public int ExecutionId { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Level { get; set; } = "info";

        /// <summary>
        /// 日志消息
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = false)]
        [Required]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 执行步骤
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        public string? Step { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 额外数据(JSON格式)
        /// </summary>
        [SugarColumn(ColumnDataType = "NTEXT", IsNullable = true)]
        public string? ExtraData { get; set; }

        // 导航属性
        /// <summary>
        /// 关联执行记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public SeleniumExecution? Execution { get; set; }
    }
}
