import api from './api'

export interface Prototype {
  id: number
  projectId: number
  requirementDocumentId?: number
  prototypeName: string
  prototypeType: string
  mermaidDefinition: string
  description?: string
  targetUsers?: string
  pageModules?: string
  interactionFlows?: string
  uiComponents?: string
  deviceType?: string
  fidelityLevel?: string
  prototypeVersion: number
  createdBy: number
  createdTime: string
  updatedBy?: number
  updatedTime?: string
}

export interface CreatePrototypeRequest {
  projectId: number
  requirementDocumentId?: number
  prototypeName: string
  prototypeType: string
  mermaidDefinition: string
  description?: string
  targetUsers?: string
  pageModules?: string
  interactionFlows?: string
  uiComponents?: string
  deviceType?: string
  fidelityLevel?: string
}

export interface UpdatePrototypeRequest {
  prototypeName?: string
  prototypeType?: string
  mermaidDefinition?: string
  description?: string
  targetUsers?: string
  pageModules?: string
  interactionFlows?: string
  uiComponents?: string
  deviceType?: string
  fidelityLevel?: string
}

export interface GeneratePrototypeRequest {
  projectId: number
  requirementDocumentId?: number
  prototypeType: string
  deviceType: string
  fidelityLevel: string
  targetUsers?: string
  requirements: string
  aiProviderConfigId?: number
}

export interface GeneratePrototypeResponse {
  taskId: string
  message: string
}

export class PrototypeService {
  /**
   * 获取项目的原型图列表
   */
  static async getPrototypes(projectId: number): Promise<Prototype[]> {
    try {
      const response = await api.get(`/api/prototypes/project/${projectId}`)
      return response.data || []
    } catch (error) {
      console.error('获取原型图列表失败:', error)
      throw error
    }
  }

  /**
   * 获取单个原型图详情
   */
  static async getPrototype(id: number): Promise<Prototype> {
    try {
      const response = await api.get(`/api/prototypes/${id}`)
      return response.data
    } catch (error) {
      console.error('获取原型图详情失败:', error)
      throw error
    }
  }

  /**
   * 创建原型图
   */
  static async createPrototype(data: CreatePrototypeRequest): Promise<Prototype> {
    try {
      const response = await api.post('/api/prototypes', data)
      return response.data
    } catch (error) {
      console.error('创建原型图失败:', error)
      throw error
    }
  }

  /**
   * 更新原型图
   */
  static async updatePrototype(id: number, data: UpdatePrototypeRequest): Promise<Prototype> {
    try {
      const response = await api.put(`/api/prototypes/${id}`, data)
      return response.data
    } catch (error) {
      console.error('更新原型图失败:', error)
      throw error
    }
  }

  /**
   * 删除原型图
   */
  static async deletePrototype(id: number): Promise<void> {
    try {
      await api.delete(`/api/prototypes/${id}`)
    } catch (error) {
      console.error('删除原型图失败:', error)
      throw error
    }
  }

  /**
   * AI生成原型图
   */
  static async generatePrototype(data: GeneratePrototypeRequest): Promise<GeneratePrototypeResponse> {
    try {
      const response = await api.post('/api/ai/generate-prototype', data)
      return response.data
    } catch (error) {
      console.error('AI生成原型图失败:', error)
      throw error
    }
  }

  /**
   * 获取原型图生成任务状态
   */
  static async getGenerationTaskStatus(taskId: string): Promise<any> {
    try {
      const response = await api.get(`/api/ai/tasks/${taskId}/status`)
      return response.data
    } catch (error) {
      console.error('获取任务状态失败:', error)
      throw error
    }
  }

  /**
   * 复制原型图
   */
  static async duplicatePrototype(id: number, newName?: string): Promise<Prototype> {
    try {
      const original = await this.getPrototype(id)
      const duplicateData: CreatePrototypeRequest = {
        projectId: original.projectId,
        requirementDocumentId: original.requirementDocumentId,
        prototypeName: newName || `${original.prototypeName} (副本)`,
        prototypeType: original.prototypeType,
        mermaidDefinition: original.mermaidDefinition,
        description: original.description,
        targetUsers: original.targetUsers,
        pageModules: original.pageModules,
        interactionFlows: original.interactionFlows,
        uiComponents: original.uiComponents,
        deviceType: original.deviceType,
        fidelityLevel: original.fidelityLevel
      }
      return await this.createPrototype(duplicateData)
    } catch (error) {
      console.error('复制原型图失败:', error)
      throw error
    }
  }

  /**
   * 导出原型图
   */
  static async exportPrototype(id: number, format: 'svg' | 'png' | 'pdf' = 'svg'): Promise<Blob> {
    try {
      const response = await api.get(`/api/prototypes/${id}/export`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('导出原型图失败:', error)
      throw error
    }
  }

  /**
   * 验证Mermaid语法
   */
  static async validateMermaid(mermaidDefinition: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const response = await api.post('/api/prototypes/validate-mermaid', {
        mermaidDefinition
      })
      return response.data
    } catch (error) {
      console.error('验证Mermaid语法失败:', error)
      return { valid: false, error: '验证失败' }
    }
  }

  /**
   * 获取原型图模板
   */
  static async getPrototypeTemplates(prototypeType?: string): Promise<any[]> {
    try {
      const response = await api.get('/api/prototypes/templates', {
        params: { prototypeType }
      })
      return response.data || []
    } catch (error) {
      console.error('获取原型图模板失败:', error)
      return []
    }
  }

  /**
   * 获取原型图统计信息
   */
  static async getPrototypeStats(projectId: number): Promise<any> {
    try {
      const response = await api.get(`/api/prototypes/project/${projectId}/stats`)
      return response.data
    } catch (error) {
      console.error('获取原型图统计失败:', error)
      return {
        total: 0,
        byType: {},
        byDevice: {},
        byFidelity: {}
      }
    }
  }
}

export default PrototypeService
