{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ProjectManagementAI;Integrated Security=true;TrustServerCertificate=true", "AlternativeConnection": "Server=YOUR_SERVER;Database=ProjectManagementAI;User ID=YOUR_USER;Password=YOUR_PASSWORD;TrustServerCertificate=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "SqlSugar": "Information"}}, "BuildSettings": {"ProjectId": 1, "BackendProjectPath": "D:\\Projects\\ProjectManagement\\Backend\\ProjectManagement.API\\ProjectManagement.API.csproj", "FrontendProjectPath": "D:\\Projects\\ProjectManagement\\Frontend", "AutoCompileInterval": 30, "MaxErrorsToDisplay": 100, "EnableAutoCompile": false, "CompileTimeoutSeconds": 300}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSqlLogging": true, "AutoCreateTables": false}, "UISettings": {"RefreshInterval": 5, "ShowWarnings": true, "ShowInfoMessages": false, "MaxDisplayRows": 1000}}