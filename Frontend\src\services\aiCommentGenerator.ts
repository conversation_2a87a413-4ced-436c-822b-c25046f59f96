/**
 * AI中文注释生成服务
 * 调用后端AI服务为SQL脚本生成中文注释
 */

import { AIApiService } from './api'

export interface CommentGenerationOptions {
  projectId?: number
  databaseType?: string
  businessDomain?: string
}

export class AICommentGenerator {
  /**
   * 调用后端AI服务为SQL脚本生成中文注释
   */
  static async generateSQLComments(
    sqlScript: string,
    options: CommentGenerationOptions = {}
  ): Promise<string> {
    try {
      // 显式设置5分钟超时
      const response = await AIApiService.post<string>('/api/ai/generate-sql-comments', {
        sqlScript: sqlScript,
        projectId: options.projectId,
        databaseType: options.databaseType,
        businessDomain: options.businessDomain || '项目管理系统'
      }, {
        timeout: 300000 // 5分钟超时
      })

      return response
    } catch (error) {
      console.error('调用AI生成SQL注释失败:', error)
      throw new Error('AI注释生成失败')
    }
  }
}

/**
 * 便捷函数：为SQL脚本添加中文注释
 */
export async function enhanceSQLWithComments(
  sqlScript: string,
  options: CommentGenerationOptions = {}
): Promise<string> {
  try {
    return await AICommentGenerator.generateSQLComments(sqlScript, options)
  } catch (error) {
    console.error('增强SQL注释失败:', error)
    return sqlScript // 返回原始SQL脚本
  }
}
