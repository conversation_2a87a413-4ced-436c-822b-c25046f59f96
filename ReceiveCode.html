<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Code 邮箱验证码接收工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .logo p {
            color: #666;
            font-size: 16px;
        }

        .email-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }

        .email-status h3 {
            color: #495057;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .email-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            background: white;
        }

        .email-item {
            padding: 12px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .email-item:hover {
            background-color: #f8f9fa;
        }

        .email-item:last-child {
            border-bottom: none;
        }

        .email-subject {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .email-from {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .email-time {
            font-size: 11px;
            color: #999;
        }

        .verification-code {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 6px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin: 8px 0;
            display: inline-block;
        }

        .verification-form {
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .email-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .email-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .refresh-btn {
            width: 100%;
            padding: 12px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-bottom: 15px;
        }

        .refresh-btn:hover {
            background: #138496;
        }

        .refresh-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .email-generator {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .email-generator h3 {
            color: #495057;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .generator-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .generate-btn {
            flex: 1;
            padding: 10px;
            background: #6f42c1;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .generate-btn:hover {
            background: #5a32a3;
        }

        .use-btn {
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .use-btn:hover {
            background: #218838;
        }

        .generated-email {
            background: white;
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #495057;
            word-break: break-all;
            margin-bottom: 10px;
        }

        .email-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .option-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .option-group label {
            font-size: 12px;
            color: #666;
            margin-bottom: 0;
        }

        .option-group select {
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 8px;
            transition: background-color 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 15px 0;
            font-size: 14px;
        }

        .auto-refresh input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.connected {
            background: #28a745;
        }

        .status-indicator.disconnected {
            background: #dc3545;
        }

        .message {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }

            .email-item {
                padding: 10px;
            }

            .email-subject {
                font-size: 14px;
            }

            .generator-controls {
                flex-direction: column;
            }

            .email-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Augment Code</h1>
            <p>邮箱验证码接收工具</p>
        </div>

        <div class="verification-form">
            <div class="email-generator">
                <h3>📧 临时邮箱生成器</h3>

                <div class="email-options">
                    <div class="option-group">
                        <label>邮箱类型</label>
                        <select id="emailType">
                            <option value="temp">临时邮箱</option>
                            <option value="disposable">一次性邮箱</option>
                            <option value="anonymous">匿名邮箱</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>域名</label>
                        <select id="emailDomain">
                            <option value="tempmail.org">tempmail.org</option>
                            <option value="10minutemail.com">10minutemail.com</option>
                            <option value="guerrillamail.com">guerrillamail.com</option>
                            <option value="mailinator.com">mailinator.com</option>
                            <option value="yopmail.com">yopmail.com</option>
                        </select>
                    </div>
                </div>

                <div class="generator-controls">
                    <button type="button" class="generate-btn" id="generateEmailBtn">
                        🎲 生成新邮箱
                    </button>
                    <button type="button" class="use-btn" id="useGeneratedBtn" disabled>
                        ✅ 使用此邮箱
                    </button>
                </div>

                <div class="generated-email" id="generatedEmail" style="display: none;">
                    <!-- 生成的邮箱地址将显示在这里 -->
                </div>
            </div>

            <div class="form-group">
                <label for="emailAddress">监控邮箱地址</label>
                <input
                    type="email"
                    id="emailAddress"
                    class="email-input"
                    placeholder="请输入您的邮箱地址或使用上方生成器"
                    required
                >
            </div>

            <button type="button" class="refresh-btn" id="refreshBtn">
                <span id="refreshText">开始监控邮箱</span>
            </button>

            <div class="auto-refresh">
                <input type="checkbox" id="autoRefresh" checked>
                <label for="autoRefresh">自动刷新 (每30秒)</label>
                <span class="status-indicator disconnected" id="statusIndicator"></span>
                <span id="statusText">未连接</span>
            </div>

            <div class="email-status">
                <h3>
                    <span id="emailCount">邮件列表 (0)</span>
                    <span style="float: right; font-size: 12px; color: #999;" id="lastUpdate">未更新</span>
                </h3>
                <div class="email-list" id="emailList">
                    <div style="text-align: center; padding: 40px; color: #999;">
                        请输入邮箱地址并开始监控
                    </div>
                </div>
            </div>
        </div>

        <div class="message" id="message" style="display: none;"></div>
    </div>

    <script>
        class EmailVerificationCodeReceiver {
            constructor() {
                this.emailInput = document.getElementById('emailAddress');
                this.refreshBtn = document.getElementById('refreshBtn');
                this.refreshText = document.getElementById('refreshText');
                this.autoRefreshCheckbox = document.getElementById('autoRefresh');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.statusText = document.getElementById('statusText');
                this.emailList = document.getElementById('emailList');
                this.emailCount = document.getElementById('emailCount');
                this.lastUpdate = document.getElementById('lastUpdate');
                this.messageElement = document.getElementById('message');

                // 邮箱生成器相关元素
                this.generateEmailBtn = document.getElementById('generateEmailBtn');
                this.useGeneratedBtn = document.getElementById('useGeneratedBtn');
                this.generatedEmailDiv = document.getElementById('generatedEmail');
                this.emailTypeSelect = document.getElementById('emailType');
                this.emailDomainSelect = document.getElementById('emailDomain');

                this.isMonitoring = false;
                this.autoRefreshTimer = null;
                this.emails = [];
                this.refreshInterval = 30000; // 30秒
                this.currentGeneratedEmail = null;

                this.initEventListeners();
            }

            initEventListeners() {
                // 刷新按钮事件
                this.refreshBtn.addEventListener('click', () => this.toggleMonitoring());

                // 自动刷新复选框事件
                this.autoRefreshCheckbox.addEventListener('change', () => this.handleAutoRefreshChange());

                // 邮箱输入验证
                this.emailInput.addEventListener('input', () => this.validateEmail());

                // 邮箱生成器事件
                this.generateEmailBtn.addEventListener('click', () => this.generateEmail());
                this.useGeneratedBtn.addEventListener('click', () => this.useGeneratedEmail());

                // 页面关闭时清理定时器
                window.addEventListener('beforeunload', () => this.stopMonitoring());
            }

            validateEmail() {
                const email = this.emailInput.value.trim();
                const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
                this.refreshBtn.disabled = !isValid;
                return isValid;
            }

            async toggleMonitoring() {
                if (!this.isMonitoring) {
                    await this.startMonitoring();
                } else {
                    this.stopMonitoring();
                }
            }

            async startMonitoring() {
                if (!this.validateEmail()) {
                    this.showMessage('请输入有效的邮箱地址', 'error');
                    return;
                }

                this.isMonitoring = true;
                this.refreshBtn.disabled = true;
                this.refreshText.innerHTML = '<span class="loading"></span>正在连接...';
                this.updateStatus('connecting', '连接中...');

                try {
                    await this.fetchEmails();
                    this.updateStatus('connected', '已连接');
                    this.refreshText.textContent = '停止监控';
                    this.refreshBtn.disabled = false;

                    if (this.autoRefreshCheckbox.checked) {
                        this.startAutoRefresh();
                    }

                    this.showMessage('开始监控邮箱，等待验证码邮件...', 'success');
                } catch (error) {
                    this.stopMonitoring();
                    this.showMessage('连接失败，请检查邮箱地址', 'error');
                }
            }

            stopMonitoring() {
                this.isMonitoring = false;
                this.stopAutoRefresh();
                this.updateStatus('disconnected', '未连接');
                this.refreshText.textContent = '开始监控邮箱';
                this.refreshBtn.disabled = false;
            }

            startAutoRefresh() {
                this.stopAutoRefresh();
                this.autoRefreshTimer = setInterval(() => {
                    if (this.isMonitoring) {
                        this.fetchEmails();
                    }
                }, this.refreshInterval);
            }

            stopAutoRefresh() {
                if (this.autoRefreshTimer) {
                    clearInterval(this.autoRefreshTimer);
                    this.autoRefreshTimer = null;
                }
            }

            handleAutoRefreshChange() {
                if (this.isMonitoring) {
                    if (this.autoRefreshCheckbox.checked) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                }
            }

            updateStatus(status, text) {
                this.statusIndicator.className = `status-indicator ${status}`;
                this.statusText.textContent = text;
            }

            generateEmail() {
                const emailType = this.emailTypeSelect.value;
                const domain = this.emailDomainSelect.value;

                // 生成随机用户名
                const username = this.generateRandomUsername(emailType);
                const email = `${username}@${domain}`;

                this.currentGeneratedEmail = email;
                this.displayGeneratedEmail(email);
                this.useGeneratedBtn.disabled = false;

                this.showMessage(`已生成临时邮箱: ${email}`, 'success');
            }

            generateRandomUsername(type) {
                const adjectives = ['quick', 'bright', 'cool', 'smart', 'fast', 'nice', 'good', 'best', 'new', 'temp'];
                const nouns = ['user', 'mail', 'test', 'demo', 'temp', 'code', 'dev', 'app', 'web', 'net'];
                const numbers = Math.floor(Math.random() * 9999).toString().padStart(4, '0');

                let username;

                switch (type) {
                    case 'temp':
                        username = `temp_${numbers}`;
                        break;
                    case 'disposable':
                        const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
                        const noun = nouns[Math.floor(Math.random() * nouns.length)];
                        username = `${adj}${noun}${numbers}`;
                        break;
                    case 'anonymous':
                        username = `anon_${Date.now().toString().slice(-6)}`;
                        break;
                    default:
                        username = `user_${numbers}`;
                }

                return username;
            }

            generateEmail() {
                const emailType = this.emailTypeSelect.value;
                const domain = this.emailDomainSelect.value;

                // 生成随机用户名
                const username = this.generateRandomUsername(emailType);
                const email = `${username}@${domain}`;

                this.currentGeneratedEmail = email;
                this.displayGeneratedEmail(email);
                this.useGeneratedBtn.disabled = false;

                this.showMessage(`已生成临时邮箱: ${email}`, 'success');
            }

            generateRandomUsername(type) {
                const adjectives = ['quick', 'bright', 'cool', 'smart', 'fast', 'nice', 'good', 'best', 'new', 'temp'];
                const nouns = ['user', 'mail', 'test', 'demo', 'temp', 'code', 'dev', 'app', 'web', 'net'];
                const numbers = Math.floor(Math.random() * 9999).toString().padStart(4, '0');

                let username;

                switch (type) {
                    case 'temp':
                        username = `temp_${numbers}`;
                        break;
                    case 'disposable':
                        const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
                        const noun = nouns[Math.floor(Math.random() * nouns.length)];
                        username = `${adj}${noun}${numbers}`;
                        break;
                    case 'anonymous':
                        username = `anon_${Date.now().toString().slice(-6)}`;
                        break;
                    default:
                        username = `user_${numbers}`;
                }

                return username;
            }

            displayGeneratedEmail(email) {
                this.generatedEmailDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${email}</span>
                        <button class="copy-btn" onclick="copyToClipboard('${email}')">复制</button>
                    </div>
                `;
                this.generatedEmailDiv.style.display = 'block';
            }

            useGeneratedEmail() {
                if (this.currentGeneratedEmail) {
                    this.emailInput.value = this.currentGeneratedEmail;
                    this.validateEmail();
                    this.showMessage('已设置生成的邮箱地址', 'success');

                    // 滚动到邮箱输入框
                    this.emailInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    this.emailInput.focus();
                }
            }

            displayGeneratedEmail(email) {
                this.generatedEmailDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${email}</span>
                        <button class="copy-btn" onclick="copyToClipboard('${email}')">复制</button>
                    </div>
                `;
                this.generatedEmailDiv.style.display = 'block';
            }

            useGeneratedEmail() {
                if (this.currentGeneratedEmail) {
                    this.emailInput.value = this.currentGeneratedEmail;
                    this.validateEmail();
                    this.showMessage('已设置生成的邮箱地址', 'success');

                    // 滚动到邮箱输入框
                    this.emailInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    this.emailInput.focus();
                }
            }

            async fetchEmails() {
                try {
                    // 模拟获取邮件的API调用
                    const emails = await this.simulateEmailFetch();
                    this.emails = emails;
                    this.renderEmails();
                    this.updateLastUpdateTime();
                } catch (error) {
                    this.showMessage('获取邮件失败', 'error');
                }
            }

            async simulateEmailFetch() {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000));

                const currentEmail = this.emailInput.value;
                const isGeneratedEmail = currentEmail && (
                    currentEmail.includes('tempmail.org') ||
                    currentEmail.includes('10minutemail.com') ||
                    currentEmail.includes('guerrillamail.com') ||
                    currentEmail.includes('mailinator.com') ||
                    currentEmail.includes('yopmail.com')
                );

                // 模拟邮件数据
                const mockEmails = [
                    {
                        id: 1,
                        from: '<EMAIL>',
                        subject: 'Augment Code 验证码',
                        content: '您的验证码是：123456，请在10分钟内使用。',
                        time: new Date(Date.now() - 2 * 60 * 1000),
                        verificationCode: '123456',
                        to: currentEmail
                    },
                    {
                        id: 2,
                        from: '<EMAIL>',
                        subject: '欢迎使用 Augment Code',
                        content: '感谢您注册 Augment Code，开始您的AI编程之旅...',
                        time: new Date(Date.now() - 10 * 60 * 1000),
                        verificationCode: null,
                        to: currentEmail
                    },
                    {
                        id: 3,
                        from: '<EMAIL>',
                        subject: 'Augment Code 验证码',
                        content: '您的验证码是：789012，请在10分钟内使用。',
                        time: new Date(Date.now() - 15 * 60 * 1000),
                        verificationCode: '789012',
                        to: currentEmail
                    }
                ];

                // 如果是生成的临时邮箱，增加收到新验证码的概率
                const newEmailProbability = isGeneratedEmail ? 0.8 : 0.3;

                // 随机添加新的验证码邮件
                if (Math.random() > (1 - newEmailProbability)) {
                    const newCode = Math.floor(100000 + Math.random() * 900000).toString();
                    mockEmails.unshift({
                        id: Date.now(),
                        from: '<EMAIL>',
                        subject: 'Augment Code 验证码',
                        content: `您的验证码是：${newCode}，请在10分钟内使用。有效期10分钟。`,
                        time: new Date(),
                        verificationCode: newCode,
                        to: currentEmail
                    });
                }

                // 如果是临时邮箱，可能还会收到其他服务的邮件
                if (isGeneratedEmail && Math.random() > 0.6) {
                    const otherServices = [
                        { from: '<EMAIL>', subject: 'GitHub 验证码', code: Math.floor(100000 + Math.random() * 900000).toString() },
                        { from: '<EMAIL>', subject: 'Discord 验证码', code: Math.floor(100000 + Math.random() * 900000).toString() },
                        { from: '<EMAIL>', subject: 'Google 验证码', code: Math.floor(100000 + Math.random() * 900000).toString() }
                    ];

                    const service = otherServices[Math.floor(Math.random() * otherServices.length)];
                    mockEmails.push({
                        id: Date.now() + 1,
                        from: service.from,
                        subject: service.subject,
                        content: `您的验证码是：${service.code}`,
                        time: new Date(Date.now() - Math.random() * 30 * 60 * 1000),
                        verificationCode: service.code,
                        to: currentEmail
                    });
                }

                return mockEmails;
            }

            renderEmails() {
                this.emailCount.textContent = `邮件列表 (${this.emails.length})`;

                if (this.emails.length === 0) {
                    this.emailList.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #999;">
                            暂无邮件
                        </div>
                    `;
                    return;
                }

                this.emailList.innerHTML = this.emails.map(email => {
                    const timeStr = this.formatTime(email.time);
                    const isAugmentCode = email.from.includes('augmentcode.com');
                    const verificationCodeHtml = email.verificationCode ?
                        `<div style="margin-top: 8px;">
                            <span class="verification-code">${email.verificationCode}</span>
                            <button class="copy-btn" onclick="copyToClipboard('${email.verificationCode}')">复制</button>
                        </div>` : '';

                    const emailItemClass = isAugmentCode ? 'email-item' : 'email-item' + ' style="opacity: 0.8;"';
                    const priorityIndicator = isAugmentCode ? '🔥 ' : '';

                    return `
                        <div class="${emailItemClass}" onclick="selectEmail(${email.id})">
                            <div class="email-subject">${priorityIndicator}${email.subject}</div>
                            <div class="email-from">来自: ${email.from}</div>
                            <div class="email-from" style="font-size: 11px;">收件人: ${email.to || '当前邮箱'}</div>
                            <div class="email-time">${timeStr}</div>
                            ${verificationCodeHtml}
                        </div>
                    `;
                }).join('');
            }

            formatTime(date) {
                const now = new Date();
                const diff = now - date;
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);

                if (minutes < 1) return '刚刚';
                if (minutes < 60) return `${minutes}分钟前`;
                if (hours < 24) return `${hours}小时前`;
                if (days < 7) return `${days}天前`;

                return date.toLocaleDateString('zh-CN');
            }

            updateLastUpdateTime() {
                const now = new Date();
                this.lastUpdate.textContent = `最后更新: ${now.toLocaleTimeString('zh-CN')}`;
            }

            showMessage(text, type) {
                this.messageElement.textContent = text;
                this.messageElement.className = `message ${type}`;
                this.messageElement.style.display = 'block';

                // 3秒后自动隐藏消息（除了成功消息）
                if (type !== 'success') {
                    setTimeout(() => {
                        this.messageElement.style.display = 'none';
                    }, 3000);
                }
            }
        }

        // 全局函数
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功提示
                const message = document.getElementById('message');
                message.textContent = `验证码 ${text} 已复制到剪贴板`;
                message.className = 'message success';
                message.style.display = 'block';

                setTimeout(() => {
                    message.style.display = 'none';
                }, 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const message = document.getElementById('message');
                message.textContent = `验证码 ${text} 已复制到剪贴板`;
                message.className = 'message success';
                message.style.display = 'block';

                setTimeout(() => {
                    message.style.display = 'none';
                }, 2000);
            });
        }

        function selectEmail(emailId) {
            // 可以在这里添加选择邮件的逻辑
            console.log('选择邮件:', emailId);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new EmailVerificationCodeReceiver();
        });
    </script>
</body>
</html>
