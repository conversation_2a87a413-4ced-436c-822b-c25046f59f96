using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 用户管理控制器
/// 功能: 处理用户信息的查询、更新等操作
/// 支持: 用户资料管理、权限查询、用户搜索
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("GeneralApi")]
[Produces("application/json")]
public class UsersController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<UsersController> _logger;

    public UsersController(
        IUserRepository userRepository,
        ILogger<UsersController> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="search">搜索关键词</param>
    /// <returns>用户列表</returns>
    [HttpGet]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<PagedResultDto<UserInfoDto>>> GetUsers(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null)
    {
        try
        {
            _logger.LogInformation("获取用户列表，页码: {PageNumber}, 页大小: {PageSize}", pageNumber, pageSize);

            var users = await _userRepository.SearchUsersAsync(search ?? "", pageNumber, pageSize);

            var result = new PagedResultDto<UserInfoDto>
            {
                Items = users.Items.Select(u => new UserInfoDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    RealName = u.RealName,
                    Role = u.Role,
                    Avatar = u.Avatar,
                    CreatedAt = u.CreatedTime,
                    LastLoginAt = u.LastLoginAt
                }).ToList(),
                TotalCount = users.TotalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户列表时发生错误");
            return StatusCode(500, new { message = "获取用户列表失败" });
        }
    }

    /// <summary>
    /// 根据ID获取用户详细信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户详细信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserInfoDto>> GetUser(int id)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("获取用户详情: {UserId}, 请求用户: {CurrentUserId}", id, currentUserId);

            // 检查权限：只能查看自己的信息或管理员可以查看所有用户
            if (id != currentUserId && !IsAdmin())
            {
                return StatusCode(403, new { message = "无权查看其他用户信息" });
            }

            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var result = new UserInfoDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                RealName = user.RealName,
                Role = user.Role,
                Status = user.Status,
                Avatar = user.Avatar,
                CreatedAt = user.CreatedTime,
                LastLoginAt = user.LastLoginAt
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户详情时发生错误: {UserId}", id);
            return StatusCode(500, new { message = "获取用户详情失败" });
        }
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<UserInfoDto>> UpdateUser(int id, [FromBody] UpdateUserRequestDto request)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            _logger.LogInformation("更新用户信息: {UserId}, 操作用户: {CurrentUserId}", id, currentUserId);

            // 检查权限：只能更新自己的信息或管理员可以更新所有用户
            if (id != currentUserId && !IsAdmin())
            {
                return StatusCode(403, new { message = "无权修改其他用户信息" });
            }

            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            // 更新用户信息
            if (!string.IsNullOrEmpty(request.RealName))
                user.RealName = request.RealName;

            if (!string.IsNullOrEmpty(request.Phone))
                user.Phone = request.Phone;

            if (!string.IsNullOrEmpty(request.Avatar))
                user.Avatar = request.Avatar;

            // 只有管理员可以修改角色和状态
            if (IsAdmin())
            {
                if (!string.IsNullOrEmpty(request.Role))
                {
                    // 验证角色值是否有效
                    var validRoles = new[] { "User", "ProjectManager", "Developer", "Tester", "ProductManager", "Admin", "SuperAdmin" };
                    if (validRoles.Contains(request.Role))
                    {
                        user.Role = request.Role;
                        _logger.LogInformation("管理员 {AdminId} 将用户 {UserId} 的角色更改为 {Role}", currentUserId, id, request.Role);
                    }
                    else
                    {
                        return BadRequest(new { message = "无效的用户角色" });
                    }
                }

                if (request.Status.HasValue)
                {
                    // 验证状态值是否有效
                    var validStatuses = new[] { 1, 2, 3, 4, 5 };
                    if (validStatuses.Contains(request.Status.Value))
                    {
                        user.Status = request.Status.Value;
                        _logger.LogInformation("管理员 {AdminId} 将用户 {UserId} 的状态更改为 {Status}", currentUserId, id, request.Status.Value);
                    }
                    else
                    {
                        return BadRequest(new { message = "无效的用户状态" });
                    }
                }
            }

            user.UpdatedTime = DateTime.UtcNow;
            user.UpdatedBy = currentUserId;

            var updateResult = await _userRepository.UpdateAsync(user);

            if (!updateResult)
            {
                return StatusCode(500, new { message = "更新用户信息失败" });
            }

            _logger.LogInformation("用户信息更新成功: {UserId}", id);

            var result = new UserInfoDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                RealName = user.RealName,
                Role = user.Role,
                Status = user.Status,
                Avatar = user.Avatar,
                CreatedAt = user.CreatedTime,
                LastLoginAt = user.LastLoginAt
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户信息时发生错误: {UserId}", id);
            return StatusCode(500, new { message = "更新用户信息失败" });
        }
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="request">修改密码请求</param>
    /// <returns>修改结果</returns>
    [HttpPost("change-password")]
    public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户修改密码: {UserId}", userId);

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            // 验证当前密码
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
            {
                return BadRequest(new { message = "当前密码错误" });
            }

            // 更新密码
            var newPasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            await _userRepository.UpdatePasswordAsync(userId, newPasswordHash, "");

            _logger.LogInformation("用户密码修改成功: {UserId}", userId);

            return Ok(new { message = "密码修改成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码时发生错误");
            return StatusCode(500, new { message = "修改密码失败" });
        }
    }

    /// <summary>
    /// 获取用户统计信息
    /// </summary>
    /// <returns>用户统计信息</returns>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<UserStatisticsDto>> GetUserStatistics()
    {
        try
        {
            _logger.LogInformation("获取用户统计信息");

            var totalUsers = await _userRepository.CountAsync();
            var activeUsers = await _userRepository.GetActiveUserCountAsync(30);
            var newUsers = await _userRepository.GetRegistrationCountAsync(
                DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);

            var statistics = new UserStatisticsDto
            {
                TotalUsers = totalUsers,
                ActiveUsers = activeUsers,
                NewUsersThisMonth = newUsers,
                InactiveUsers = totalUsers - activeUsers
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户统计信息时发生错误");
            return StatusCode(500, new { message = "获取统计信息失败" });
        }
    }

    /// <summary>
    /// 上传用户头像
    /// </summary>
    /// <param name="file">头像文件</param>
    /// <returns>上传结果</returns>
    [HttpPost("upload-avatar")]
    public async Task<ActionResult<object>> UploadAvatar(IFormFile file)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户上传头像: {UserId}", userId);

            if (file == null || file.Length == 0)
            {
                return BadRequest(new { message = "请选择头像文件" });
            }

            // 验证文件类型
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/webp" };
            if (!allowedTypes.Contains(file.ContentType.ToLower()))
            {
                return BadRequest(new { message = "只支持 JPG、JPEG、PNG、WEBP 格式的图片" });
            }

            // 验证文件大小（2MB）
            if (file.Length > 2 * 1024 * 1024)
            {
                return BadRequest(new { message = "头像文件大小不能超过 2MB" });
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            // 删除旧头像文件
            if (!string.IsNullOrEmpty(user.Avatar))
            {
                await DeleteOldAvatarFile(user.Avatar);
            }

            // 生成文件名
            var fileName = $"{userId}_{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
            var uploadPath = Path.Combine("uploads", "avatars");
            var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", uploadPath);

            // 确保目录存在
            Directory.CreateDirectory(fullPath);

            // 保存文件
            var filePath = Path.Combine(fullPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // 更新用户头像路径
            var relativePath = Path.Combine(uploadPath, fileName).Replace("\\", "/");
            user.Avatar = relativePath;
            user.UpdatedTime = DateTime.UtcNow;
            user.UpdatedBy = userId;

            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("用户头像上传成功: {UserId}, 文件路径: {FilePath}", userId, relativePath);

            return Ok(new
            {
                success = true,
                message = "头像上传成功",
                data = new
                {
                    avatar = relativePath,
                    user = new UserInfoDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        RealName = user.RealName,
                        Role = user.Role,
                        Status = user.Status,
                        Avatar = user.Avatar,
                        CreatedAt = user.CreatedTime,
                        LastLoginAt = user.LastLoginAt
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传头像时发生错误");
            return StatusCode(500, new { message = "上传头像失败" });
        }
    }

    /// <summary>
    /// 删除旧头像文件
    /// </summary>
    /// <param name="avatarPath">头像路径</param>
    private Task DeleteOldAvatarFile(string avatarPath)
    {
        try
        {
            if (string.IsNullOrEmpty(avatarPath)) return Task.CompletedTask;

            var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", avatarPath);
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
                _logger.LogInformation("删除旧头像文件: {FilePath}", fullPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "删除旧头像文件失败: {AvatarPath}", avatarPath);
            // 不抛出异常，因为这不是关键操作
        }
        return Task.CompletedTask;
    }

    #region 私有方法

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }

    /// <summary>
    /// 检查当前用户是否为管理员
    /// </summary>
    private bool IsAdmin()
    {
        return User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
    }

    #endregion
}

/// <summary>
/// 更新用户请求DTO
/// </summary>
public class UpdateUserRequestDto
{
    /// <summary>
    /// 真实姓名
    /// </summary>
    public string? RealName { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 用户角色（仅管理员可修改）
    /// </summary>
    public string? Role { get; set; }

    /// <summary>
    /// 用户状态（仅管理员可修改）
    /// </summary>
    public int? Status { get; set; }
}

/// <summary>
/// 修改密码请求DTO
/// </summary>
public class ChangePasswordRequestDto
{
    /// <summary>
    /// 当前密码
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// 新密码
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// 确认新密码
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;
}

/// <summary>
/// 用户统计信息DTO
/// </summary>
public class UserStatisticsDto
{
    /// <summary>
    /// 总用户数
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// 活跃用户数
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// 非活跃用户数
    /// </summary>
    public int InactiveUsers { get; set; }

    /// <summary>
    /// 本月新用户数
    /// </summary>
    public int NewUsersThisMonth { get; set; }
}
