using ProjectManagement.Core.Entities;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt构建服务接口
/// </summary>
public interface IPromptBuilderService
{
    /// <summary>
    /// 构建提示词
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="parameters">参数字典</param>
    /// <param name="userId">用户ID</param>
    /// <param name="projectId">项目ID</param>
    /// <returns>构建后的提示词</returns>
    Task<string> BuildPromptAsync(int templateId, Dictionary<string, object> parameters, int userId, int? projectId = null);

    /// <summary>
    /// 根据任务类型构建提示词
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="parameters">参数字典</param>
    /// <param name="userId">用户ID</param>
    /// <param name="projectId">项目ID</param>
    /// <returns>构建后的提示词</returns>
    Task<string> BuildPromptByTaskTypeAsync(string taskType, Dictionary<string, object> parameters, int userId, int? projectId = null);

    /// <summary>
    /// 验证模板参数
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="parameters">参数字典</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, List<string> Errors)> ValidateParametersAsync(int templateId, Dictionary<string, object> parameters);

    /// <summary>
    /// 获取模板参数定义
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <returns>参数定义</returns>
    Task<Dictionary<string, object>?> GetTemplateParametersAsync(int templateId);

    /// <summary>
    /// 预览构建结果
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="parameters">参数字典</param>
    /// <returns>预览结果</returns>
    Task<string> PreviewPromptAsync(int templateId, Dictionary<string, object> parameters);

    /// <summary>
    /// 获取推荐模板
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="userId">用户ID</param>
    /// <param name="projectId">项目ID</param>
    /// <returns>推荐的模板列表</returns>
    Task<List<PromptTemplate>> GetRecommendedTemplatesAsync(string taskType, int userId, int? projectId = null);

    /// <summary>
    /// 优化提示词
    /// </summary>
    /// <param name="originalPrompt">原始提示词</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="aiProvider">AI提供商</param>
    /// <returns>优化后的提示词</returns>
    Task<string> OptimizePromptAsync(string originalPrompt, string taskType, string aiProvider);

    /// <summary>
    /// 分析提示词质量
    /// </summary>
    /// <param name="prompt">提示词内容</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>质量分析结果</returns>
    Task<object> AnalyzePromptQualityAsync(string prompt, string taskType);
}
