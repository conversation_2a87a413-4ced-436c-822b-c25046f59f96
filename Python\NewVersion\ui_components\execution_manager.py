#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行管理器
负责步骤执行和自动化操作的管理
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from typing import Dict, List, Optional
import json
import os
from datetime import datetime


class ExecutionManager:
    """执行管理器类"""
    
    def __init__(self, parent_ui, api_client, automation_manager):
        """
        初始化执行管理器
        
        Args:
            parent_ui: 父UI对象
            api_client: API客户端
            automation_manager: 自动化管理器
        """
        self.parent_ui = parent_ui
        self.api_client = api_client
        self.automation_manager = automation_manager
        
        # 执行历史
        self.execution_history = []
        
        # 脚本执行控制
        self.is_script_running = False
        self.script_execution_thread = None
        self.script_stop_requested = False
        
        # 循环检测控制
        self.is_loop_detection_running = False
        self.loop_detection_thread = None
        self.dom_script_instance = None

        # DOM监控设置
        self.dom_monitor_interval = 1.0    # 默认监控间隔1秒
        self.dom_adaptive_interval = True  # 默认启用自适应间隔

        # DOM操作相关
        self.dom_controller = None
        self.chat_controller = None
        
        # 执行模式
        self.execution_mode = tk.StringVar(value="dom")  # 默认使用DOM操作
        
        # UI组件引用
        self.execute_btn = None
        self.start_monitor_btn = None
        self.stop_monitor_btn = None
        self.loop_detection_status_label = None
        self.script_code_text = None
        self.script_status_label = None
        self.script_name_label = None
        self.script_language_label = None
        self.script_category_label = None
        self.history_tree = None
        self.history_count_label = None
        self.status_label = None
    
    def set_ui_components(self, execute_btn, start_monitor_btn, stop_monitor_btn,
                         loop_detection_status_label, script_code_text, script_status_label,
                         script_name_label, script_language_label, script_category_label,
                         history_tree, history_count_label, status_label, execution_mode,
                         error_send_delay_var=None):
        """设置UI组件引用"""
        self.execute_btn = execute_btn
        self.start_monitor_btn = start_monitor_btn
        self.stop_monitor_btn = stop_monitor_btn
        self.loop_detection_status_label = loop_detection_status_label
        self.script_code_text = script_code_text
        self.script_status_label = script_status_label
        self.script_name_label = script_name_label
        self.script_language_label = script_language_label
        self.script_category_label = script_category_label
        self.history_tree = history_tree
        self.history_count_label = history_count_label
        self.status_label = status_label
        self.execution_mode = execution_mode
        self.error_send_delay_var = error_send_delay_var
    
    def execute_selected_step(self, selected_step):
        """执行选中的步骤"""
        if not selected_step:
            messagebox.showwarning("警告", "请先选择一个步骤")
            return

        # 注释掉InProgress状态检查，允许执行任何步骤
        # if hasattr(self.parent_ui, 'has_inprogress_steps') and self.parent_ui.has_inprogress_steps():
        #     selected_step_status = selected_step.get('status')
        #     if selected_step_status != 'InProgress':
        #         print("⚠️ 列表中有InProgress状态的开发步骤，不能进行其他步骤的开发")
        #         # 更新状态标签显示提示信息
        #         if hasattr(self, 'status_label') and self.status_label:
        #             self.status_label.config(text="⚠️ 有InProgress步骤，只能执行进行中的步骤")
        #         return

        execution_mode = self.execution_mode.get()

        if execution_mode == "dom":
            self.execute_step_with_dom_vscode(selected_step)
        elif execution_mode == "script":
            self.execute_step_with_script(selected_step)
        else:
            messagebox.showerror("错误", f"未知的执行模式: {execution_mode}")

    def execute_step_with_dom_vscode(self, selected_step):
        """使用DOM操作VSCode执行步骤"""
        try:
            # 检查是否已有脚本在运行
            if self.is_script_running:
                return

            step_name = selected_step.get('stepName', '')
            step_id = selected_step.get('id')
            print(f"🤖 开始使用DOM操作VSCode执行步骤: {step_name}")

            # 获取步骤的AI Prompt
            step_ai_prompt = self.get_step_ai_prompt(selected_step)

            # 显示确认对话框（10秒超时自动确认）
            confirm_message = f"确定要使用DOM操作VSCode执行步骤 '{step_name}' 吗？\n\n"
            confirm_message += f"将发送以下内容到Copilot:\n{step_ai_prompt[:200]}{'...' if len(step_ai_prompt) > 200 else ''}\n\n"
            confirm_message += "💡 注意：UI界面将自动最小化以避免干扰输入操作"

            if not self._show_confirmation_dialog_with_timeout("确认执行", confirm_message, 10):
                return

            # 1. 设置当前开发步骤并更新状态为InProgress
            try:
                # 记录当前正在开发的步骤
                if hasattr(self.parent_ui, 'set_current_developing_step'):
                    task_id = getattr(self.parent_ui, 'selected_task', {}).get('id') if hasattr(self.parent_ui, 'selected_task') else None
                    self.parent_ui.set_current_developing_step(selected_step, task_id)

                # 更新步骤状态为InProgress
                if hasattr(self.parent_ui, 'perform_status_update'):
                    print(f"📝 将步骤 '{step_name}' 状态设置为InProgress")
                    self.parent_ui.perform_status_update(selected_step, 'InProgress')
                    print(f"✅ 步骤状态已更新为InProgress")
            except Exception as e:
                print(f"⚠️ 更新步骤状态失败: {e}")
                # 继续执行，不因为状态更新失败而中断

            # 2. 预下载项目文件（如果需要）
            if hasattr(self.parent_ui, 'download_project_files_before_execution'):
                print(f"📥 步骤执行前，先下载项目文件...")
                self.status_label.config(text=f"正在下载项目文件...")
                self.parent_ui.download_project_files_before_execution(step_id, step_name)
                print(f"✅ 项目文件下载完成，开始执行DOM操作")

            # 3. 设置执行状态
            self.is_script_running = True
            self.script_stop_requested = False

            # 4. 更新UI状态
            try:
                if hasattr(self, 'execute_btn') and self.execute_btn and self.execute_btn.winfo_exists():
                    self.execute_btn.config(state="disabled", text="🤖 DOM操作执行中...")
            except Exception as e:
                print(f"⚠️ 更新执行按钮失败: {e}")

            try:
                if hasattr(self, 'script_status_label') and self.script_status_label and self.script_status_label.winfo_exists():
                    self.script_status_label.config(text="正在执行DOM操作...", foreground="blue")
            except Exception as e:
                print(f"⚠️ 更新状态标签失败: {e}")

            try:
                if hasattr(self, 'status_label') and self.status_label and self.status_label.winfo_exists():
                    self.status_label.config(text=f"正在使用DOM操作VSCode执行: {step_name}")
            except Exception as e:
                print(f"⚠️ 更新状态标签失败: {e}")

            # 在后台线程中执行DOM操作
            def run_dom_operation():
                try:
                    # 检查是否请求停止
                    if self.script_stop_requested:
                        def safe_reset_1():
                            try:
                                self.reset_script_execution_state()
                            except Exception as e:
                                print(f"❌ 停止时重置状态异常: {e}")
                        self.parent_ui.parent.after(0, safe_reset_1)
                        return

                    # 导入DOM操作模块
                    import sys
                    from pathlib import Path
                    dom_path = Path(__file__).parent.parent / "dom"
                    if str(dom_path) not in sys.path:
                        sys.path.append(str(dom_path))

                    from copilot_vscode_script import CopilotVsCodeScript

                    # 1. 创建DOM操作脚本实例（传递父UI引用）
                    dom_script = CopilotVsCodeScript(parent_ui=self.parent_ui)

                    # 2. 连接到VSCode
                    if not dom_script.connect():
                        self.parent_ui.parent.after(0, lambda: messagebox.showerror("连接失败", "无法连接到VSCode，请确保VSCode已启动调试模式"))
                        def safe_reset_2():
                            try:
                                self.reset_script_execution_state()
                            except Exception as e:
                                print(f"❌ 连接失败时重置状态异常: {e}")
                        self.parent_ui.parent.after(0, safe_reset_2)
                        return

                    # 3. 准备完整的消息（包含自定义Prompt）
                    reference_images = self.get_step_reference_images()
                    reference_images_str = ""
                    if reference_images:
                        try:
                            import json
                            reference_images_str = json.dumps(reference_images)
                        except:
                            reference_images_str = str(reference_images)

                    complete_message = self.prepare_copilot_message(step_ai_prompt, reference_images_str)

                    # 4. 执行DOM操作流程
                    success = self.execute_dom_workflow(dom_script, complete_message)

                    # 断开连接
                    dom_script.disconnect()

                    # 更新UI
                    if success:
                        self.parent_ui.parent.after(0, lambda: self.script_status_label.config(text="DOM操作成功", foreground="green") if self.script_status_label else None)
                        self.parent_ui.parent.after(0, lambda: self.status_label.config(text=f"步骤 '{step_name}' DOM操作执行成功"))
                        print(f"✅ 步骤 '{step_name}' DOM操作执行成功")
                    else:
                        self.parent_ui.parent.after(0, lambda: self.script_status_label.config(text="DOM操作失败", foreground="red") if self.script_status_label else None)
                        self.parent_ui.parent.after(0, lambda: self.status_label.config(text=f"步骤 '{step_name}' DOM操作执行失败"))
                        self.parent_ui.parent.after(0, lambda: messagebox.showerror("执行失败", f"步骤 '{step_name}' DOM操作执行失败"))

                except ImportError as e:
                    error_msg = f"DOM模块导入失败: {e}"
                    print(error_msg)
                    self.parent_ui.parent.after(0, lambda: messagebox.showerror("模块错误", f"DOM操作模块不可用:\n{error_msg}"))
                    def safe_reset_3():
                        try:
                            self.reset_script_execution_state()
                        except Exception as e:
                            print(f"❌ 导入错误时重置状态异常: {e}")
                    self.parent_ui.parent.after(0, safe_reset_3)
                except Exception as e:
                    error_msg = f"DOM操作异常: {e}"
                    print(error_msg)
                    self.parent_ui.parent.after(0, lambda: self.script_status_label.config(text="DOM操作异常", foreground="red") if self.script_status_label else None)
                    self.parent_ui.parent.after(0, lambda: self.status_label.config(text=f"步骤 '{step_name}' DOM操作异常"))
                    self.parent_ui.parent.after(0, lambda: messagebox.showerror("执行异常", f"DOM操作执行异常:\n{error_msg}"))
                finally:
                    # 重置执行状态
                    def safe_reset():
                        try:
                            self.reset_script_execution_state()
                        except Exception as e:
                            print(f"❌ 安全重置执行状态异常: {e}")

                    self.parent_ui.parent.after(0, safe_reset)

            # 启动后台线程
            threading.Thread(target=run_dom_operation, daemon=True).start()

        except Exception as e:
            print(f"启动DOM操作失败: {e}")
            messagebox.showerror("启动失败", f"启动DOM操作失败: {e}")
            self.reset_script_execution_state()

    def execute_step_with_script(self, selected_step):
        """使用脚本执行步骤"""
        if not selected_step:
            messagebox.showwarning("警告", "请先选择一个步骤")
            return

        step_name = selected_step.get('stepName', '未知步骤')
        step_id = selected_step.get('id')

        # 获取步骤的脚本
        script = self.get_step_script(selected_step)
        
        if not script:
            messagebox.showerror("错误", f"步骤 '{step_name}' 没有关联的执行脚本")
            return

        # 显示确认对话框（10秒超时自动确认）
        if not self._show_confirmation_dialog_with_timeout("确认执行", f"确定要执行步骤 '{step_name}' 的脚本吗？", 10):
            return

        # 将当前步骤状态设置为InProgress
        try:
            if hasattr(self.parent_ui, 'perform_status_update'):
                print(f"📝 将步骤 '{step_name}' 状态设置为InProgress")
                self.parent_ui.perform_status_update(selected_step, 'InProgress')
                print(f"✅ 步骤状态已更新为InProgress")
        except Exception as e:
            print(f"⚠️ 更新步骤状态失败: {e}")
            # 继续执行，不因为状态更新失败而中断

        # 执行脚本
        self.execute_script_directly(script)

    def get_step_script(self, step):
        """获取步骤的执行脚本"""
        try:
            # 首先尝试获取步骤自身的脚本
            script_content = step.get('executionScript')
            if script_content:
                return script_content

            # 如果步骤没有脚本，尝试获取父步骤的脚本
            parent_step_id = step.get('parentStepId')
            if parent_step_id:
                parent_script = self.get_parent_step_script(parent_step_id)
                if parent_script:
                    return parent_script

            # 如果都没有，返回默认脚本模板
            return self.get_default_script_template(step)

        except Exception as e:
            print(f"获取步骤脚本失败: {e}")
            return None

    def get_parent_step_script(self, parent_step_id):
        """获取父步骤的脚本"""
        try:
            # 这里应该调用API获取父步骤信息
            # 暂时返回None，实际实现时需要调用相应的API
            return None
        except Exception as e:
            print(f"获取父步骤脚本失败: {e}")
            return None

    def get_default_script_template(self, step):
        """获取默认脚本模板"""
        step_name = step.get('stepName', '未知步骤')
        step_type = step.get('stepType', '未知类型')
        
        template = f"""# 步骤执行脚本
# 步骤名称: {step_name}
# 步骤类型: {step_type}
# 自动生成的默认脚本模板

print("开始执行步骤: {step_name}")

# TODO: 在这里添加具体的执行逻辑

print("步骤执行完成: {step_name}")
"""
        return template

    def execute_script_directly(self, script):
        """直接执行脚本"""
        if self.is_script_running:
            return

        try:
            self.is_script_running = True
            self.script_stop_requested = False
            
            # 更新UI状态
            self.script_status_label.config(text="🔄 执行中...", foreground="blue")
            self.execute_btn.config(state="disabled")

            def run_script():
                try:
                    # 这里应该实现实际的脚本执行逻辑
                    # 可以使用exec()执行Python脚本，或调用外部脚本执行器
                    
                    # 模拟脚本执行
                    lines = script.split('\n')
                    for i, line in enumerate(lines):
                        if self.script_stop_requested:
                            break
                        
                        # 模拟执行每一行
                        time.sleep(0.1)  # 模拟执行时间
                        
                        # 更新进度（在实际实现中可以显示执行进度）
                        progress = (i + 1) / len(lines) * 100
                        self.parent_ui.parent.after(0, lambda p=progress: 
                            self.script_status_label.config(text=f"🔄 执行中... {p:.1f}%"))

                    if not self.script_stop_requested:
                        self.parent_ui.parent.after(0, lambda: 
                            self.script_status_label.config(text="✅ 执行完成", foreground="green"))
                        self.parent_ui.parent.after(0, lambda: 
                            messagebox.showinfo("成功", "脚本执行完成！"))
                    else:
                        self.parent_ui.parent.after(0, lambda: 
                            self.script_status_label.config(text="⏹️ 已停止", foreground="orange"))

                except Exception as e:
                    error_msg = f"脚本执行异常: {str(e)}"
                    self.parent_ui.parent.after(0, lambda: 
                        self.script_status_label.config(text="❌ 执行失败", foreground="red"))
                    self.parent_ui.parent.after(0, lambda: 
                        messagebox.showerror("异常", error_msg))

                finally:
                    self.parent_ui.parent.after(0, self.reset_script_execution_state)

            # 在后台线程中执行脚本
            self.script_execution_thread = threading.Thread(target=run_script, daemon=True)
            self.script_execution_thread.start()

        except Exception as e:
            self.reset_script_execution_state()
            messagebox.showerror("异常", f"启动脚本执行时发生异常：{str(e)}")

    def stop_execution(self):
        """停止执行（包括自动化执行、脚本执行和循环检测）"""
        stopped_something = False

        # 停止自动化执行
        if hasattr(self.automation_manager, 'is_executing') and self.automation_manager.is_executing:
            self.automation_manager.stop_execution()
            print("🛑 自动化执行已停止")
            stopped_something = True

        # 停止脚本执行
        if self.is_script_running:
            self.stop_script_execution()
            print("🛑 脚本执行已停止")
            stopped_something = True

        # 停止循环检测
        if self.is_loop_detection_running:
            self.stop_loop_detection()
            print("🛑 循环检测已停止")
            stopped_something = True

        if stopped_something:
            messagebox.showinfo("提示", "执行已停止")
        else:
            messagebox.showinfo("提示", "当前没有正在执行的任务")

    def stop_script_execution(self):
        """停止脚本执行"""
        if not self.is_script_running:
            return

        self.script_stop_requested = True
        try:
            if hasattr(self, 'script_status_label') and self.script_status_label and self.script_status_label.winfo_exists():
                self.script_status_label.config(text="⏹️ 正在停止...", foreground="orange")
        except Exception as e:
            print(f"⚠️ 更新停止状态标签失败: {e}")

        # 等待脚本线程结束
        if self.script_execution_thread and self.script_execution_thread.is_alive():
            # 给脚本一些时间自然结束
            def force_reset():
                if self.is_script_running:
                    self.reset_script_execution_state()
                    messagebox.showinfo("停止", "脚本执行已强制停止")

            # 3秒后强制重置状态
            self.parent_ui.parent.after(3000, force_reset)

    def reset_script_execution_state(self):
        """重置脚本执行状态"""
        try:
            # 重置执行状态变量
            self.is_script_running = False
            self.script_stop_requested = False
            self.script_execution_thread = None

            print("🔄 重置脚本执行状态...")

            # 恢复UI状态
            self._safe_update_execute_button()
            self._safe_update_status_label()

            print("✅ 脚本执行状态重置完成")

        except Exception as e:
            print(f"❌ 重置脚本执行状态异常: {e}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")

    def _safe_update_execute_button(self):
        """安全地更新执行按钮状态"""
        try:
            if hasattr(self, 'execute_btn') and self.execute_btn and self.execute_btn.winfo_exists():
                self.execute_btn.config(state="normal")
                print("✅ 执行按钮状态已恢复")
            else:
                print("ℹ️ 执行按钮不存在或已销毁，跳过状态恢复")
        except Exception as e:
            print(f"⚠️ 恢复执行按钮状态失败: {e}")

    def _safe_update_status_label(self):
        """安全地更新状态标签"""
        try:
            if hasattr(self, 'script_status_label') and self.script_status_label and self.script_status_label.winfo_exists():
                current_text = self.script_status_label.cget("text")
                if current_text not in ["✅ 执行完成", "❌ 执行失败"]:
                    self.script_status_label.config(text="就绪", foreground="green")
                    print("✅ 状态标签已恢复")
                else:
                    print(f"ℹ️ 状态标签保持当前状态: {current_text}")
            else:
                print("ℹ️ 状态标签不存在或已销毁，跳过状态恢复")
        except Exception as e:
            print(f"⚠️ 恢复状态标签失败: {e}")

    def minimize_ui_for_dom_operation(self):
        """为DOM操作最小化UI"""
        try:
            # 获取主窗口
            main_window = self._get_main_window()
            if main_window:
                def minimize_ui():
                    try:
                        main_window.iconify()  # 最小化窗口
                        print("✅ UI已最小化，准备进行DOM操作")
                    except Exception as e:
                        print(f"❌ 最小化UI失败: {e}")

                # 延迟最小化，给用户一点时间看到确认
                self.parent_ui.parent.after(1000, minimize_ui)
            else:
                print("⚠️ 无法找到主窗口，跳过最小化")

        except Exception as e:
            print(f"❌ 最小化UI异常: {e}")

    def _get_main_window(self):
        """获取主窗口"""
        try:
            current = self.parent_ui.parent
            while current.master:
                current = current.master
            return current
        except:
            return None

    def get_step_ai_prompt(self, step):
        """获取步骤的AI Prompt（原始内容，不包含自定义Prompt）"""
        if not step:
            return "请帮我完成这个自动化任务"

        # 尝试多种可能的字段名
        step_ai_prompt = (step.get('aiPrompt', '') or
                         step.get('AIPrompt', '') or
                         step.get('ai_prompt', '') or
                         step.get('description', '') or
                         step.get('stepName', ''))

        if not step_ai_prompt:
            step_ai_prompt = "请帮我完成这个自动化任务"

        return step_ai_prompt

    def prepare_copilot_message(self, ai_prompt: str, reference_images: str = "") -> str:
        """准备发送给Copilot的消息"""
        message = ai_prompt

        # 检查是否启用了自定义Prompt功能，如果启用则追加自定义内容
        custom_prompt_content = ""
        if hasattr(self.parent_ui, 'get_custom_prompt_content'):
            custom_prompt_content = self.parent_ui.get_custom_prompt_content()

        if custom_prompt_content:
            print(f"📝 追加自定义Prompt: {custom_prompt_content[:100]}...")
            message += f"\n\n=== 自定义要求 ===\n{custom_prompt_content}"

        # 如果有参考图片，添加图片信息到消息中
        if reference_images:
            try:
                import json
                images = json.loads(reference_images)
                if images:
                    message += "\n\n参考图片："
                    for img in images:
                        message += f"\n- {img}"
            except:
                # 如果不是JSON格式，直接添加
                message += f"\n\n参考图片: {reference_images}"

        print(f"🔍 最终发送的消息长度: {len(message)} 字符")
        print(f"🔍 消息预览: {message[:200]}...")
        return message

    def get_dom_script_for_step(self, step):
        """获取步骤的DOM脚本"""
        # 这里应该根据步骤类型和内容获取相应的DOM脚本
        # 暂时返回一个示例脚本
        return "console.log('DOM操作脚本执行');"

    def execute_dom_workflow(self, dom_script, ai_prompt):
        """执行DOM操作工作流"""
        try:
            print(f"🚀 开始执行DOM操作工作流")

            # 检查是否请求停止
            if self.script_stop_requested:
                return False

            # 从配置文件读取监控参数
            monitor_interval = self._get_config_value('element_detection.monitor_interval', 1.0)
            adaptive_mode = self._get_config_value('element_detection.adaptive_interval', True)

            dom_script.start_auto_click_monitor(
                monitor_interval=monitor_interval,
                adaptive_interval=adaptive_mode
            )
            print(f"⚙️ 监控间隔: {monitor_interval}秒，自适应模式: {'启用' if adaptive_mode else '禁用'}")

            # 步骤1: 最小化UI界面，避免拦截输入
            print("📝 步骤1: 最小化UI界面")
            self.minimize_ui_for_dom_operation()

            # 步骤2: 打开Copilot聊天
            print("📝 步骤2: 打开Copilot聊天")
            dom_script.open_colipot_chat()
            dom_script.wait(1)

            if self.script_stop_requested:
                return False

            # 步骤3: 确认会话状态（继续当前会话）
            print("📝 步骤3: 确认会话状态")
            dom_script.confirmIsNewSession(False)
            dom_script.wait(1)

            if self.script_stop_requested:
                return False

            # 步骤4: 清空输入框
            print("📝 步骤4: 清空输入框")
            dom_script.clearText()
            dom_script.wait(0.5)

            if self.script_stop_requested:
                return False

            # 步骤5: 输入完整消息
            print(f"📝 步骤5: 输入完整消息: {ai_prompt[:100]}...")
            dom_script.inputTextAdvanced(ai_prompt)  # 使用高级输入方法
            dom_script.wait(1)

            if self.script_stop_requested:
                return False

            # 步骤6: 检查是否需要添加参考图片
            reference_images = self.get_step_reference_images()
            if reference_images:
                print(f"📝 步骤6: 添加 {len(reference_images)} 张参考图片")
                successful_images = 0

                for i, image_path in enumerate(reference_images, 1):
                    if self.script_stop_requested:
                        break

                    print(f"📷 正在添加第{i}张图片: {image_path}")
                    if os.path.exists(image_path):
                        print(f"✅ 图片文件存在: {image_path}")
                        success = dom_script.copy_and_paste_image(image_path)
                        if success:
                            successful_images += 1
                            print(f"✅ 第{i}张图片添加成功")
                        else:
                            print(f"❌ 第{i}张图片添加失败")
                    else:
                        print(f"❌ 图片文件不存在: {image_path}")
                        print(f"💡 请检查文件是否已下载到reference_images目录")

                    dom_script.wait(0.5)

                print(f"📊 图片添加完成: 成功{successful_images}张，总共{len(reference_images)}张")
            else:
                print("📝 步骤5: 无参考图片需要添加")

            if self.script_stop_requested:
                return False

            # 步骤7: 发送消息
            print("📝 步骤7: 发送消息")
            dom_script.clickMsgBtn()
            dom_script.wait(1)

            print("✅ DOM操作工作流执行完成")
            return True

        except Exception as e:
            print(f"❌ DOM操作工作流执行失败: {e}")
            return False

        finally:
            # 停止自动点击监控
            try:
                # 设置标志，表示刚刚执行了开发步骤，暂时不要发送编译错误
                if hasattr(dom_script, 'set_just_executed_step'):
                    dom_script.set_just_executed_step(True)
                    print("🚫 已设置标志：暂时不发送编译错误，优先处理开发步骤")

                dom_script.stop_auto_click_monitor()
                print("🛑 自动点击监控已停止")
            except Exception as e:
                print(f"⚠️ 停止监控时出现异常: {e}")

    def get_step_reference_images(self):
        """获取步骤的参考图片"""
        # 这里应该实现获取步骤参考图片的逻辑
        # 暂时返回空列表
        return []

    def _get_config_value(self, key, default_value):
        """从配置文件读取配置值"""
        try:
            from pathlib import Path
            import json

            config_path = Path(__file__).parent.parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 支持嵌套键，如 'element_detection.monitor_interval'
                keys = key.split('.')
                value = config
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default_value
                return value
            else:
                return default_value
        except Exception as e:
            print(f"读取配置失败: {e}")
            return default_value

    def _show_confirmation_dialog_with_timeout(self, title: str, message: str, timeout_seconds: int = 10) -> bool:
        """显示带超时的确认对话框"""
        result = [None]  # None表示未响应，True/False表示用户选择
        dialog_window = [None]  # 存储对话框窗口引用

        def show_dialog():
            try:
                import tkinter as tk

                # 创建自定义对话框窗口
                dialog = tk.Toplevel()
                dialog.title(title)
                dialog.geometry("500x300")
                dialog.resizable(False, False)

                # 居中显示
                dialog.transient(self.parent_ui.parent)
                dialog.grab_set()

                # 计算居中位置
                dialog.update_idletasks()
                x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
                y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
                dialog.geometry(f"+{x}+{y}")

                dialog_window[0] = dialog

                # 消息内容
                message_frame = tk.Frame(dialog, padx=20, pady=20)
                message_frame.pack(fill=tk.BOTH, expand=True)

                # 消息文本
                text_widget = tk.Text(message_frame, wrap=tk.WORD, height=8, width=50)
                text_widget.insert(tk.END, message)
                text_widget.config(state=tk.DISABLED)
                text_widget.pack(pady=(0, 10))

                # 倒计时标签
                countdown_label = tk.Label(message_frame, text=f"{timeout_seconds}秒后自动确认...",
                                         font=("Arial", 10), fg="orange")
                countdown_label.pack(pady=(10, 0))

                # 按钮框架
                button_frame = tk.Frame(dialog)
                button_frame.pack(pady=(0, 20))

                def on_yes():
                    result[0] = True
                    dialog.destroy()

                def on_no():
                    result[0] = False
                    dialog.destroy()

                tk.Button(button_frame, text="是", command=on_yes,
                         width=8, font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 10))
                tk.Button(button_frame, text="否", command=on_no,
                         width=8, font=("Arial", 10)).pack(side=tk.LEFT)

                # 倒计时逻辑
                countdown = [timeout_seconds]

                def update_countdown():
                    if result[0] is not None:  # 用户已选择
                        return

                    if countdown[0] > 0:
                        countdown_label.config(text=f"{countdown[0]}秒后自动确认...")
                        countdown[0] -= 1
                        dialog.after(1000, update_countdown)
                    else:
                        # 超时自动确认
                        result[0] = True
                        print(f"⏰ {timeout_seconds}秒超时，自动确认执行")
                        dialog.destroy()

                # 开始倒计时
                update_countdown()

            except Exception as e:
                print(f"❌ 显示确认对话框异常: {e}")
                result[0] = True  # 异常时默认确认
                if dialog_window[0]:
                    dialog_window[0].destroy()

        # 在主线程中执行
        if hasattr(self.parent_ui, 'parent'):
            self.parent_ui.parent.after(0, show_dialog)

            # 等待用户响应或超时
            wait_time = 0
            max_wait = timeout_seconds + 5  # 给倒计时留余量
            while wait_time < max_wait and result[0] is None:
                time.sleep(0.1)
                wait_time += 0.1

            final_result = result[0] if result[0] is not None else True
            return final_result

        return True  # 如果无法显示对话框，默认确认

    def start_loop_detection(self):
        """开启循环检测"""
        if self.is_loop_detection_running:
            messagebox.showwarning("警告", "循环检测已在运行中")
            return

        try:
            # 导入DOM操作模块
            import sys
            from pathlib import Path
            dom_path = Path(__file__).parent.parent / "dom"
            if str(dom_path) not in sys.path:
                sys.path.append(str(dom_path))

            from copilot_vscode_script import CopilotVsCodeScript

            # 创建DOM操作脚本实例（传递父UI引用）
            self.dom_script_instance = CopilotVsCodeScript(parent_ui=self.parent_ui)

            # 连接到VSCode
            if not self.dom_script_instance.connect():
                messagebox.showerror("连接失败", "无法连接到VSCode，请确保VSCode已启动调试模式")
                self.dom_script_instance = None
                return

            # 从配置文件读取监控参数
            monitor_interval = self._get_config_value('element_detection.monitor_interval', 1.0)
            adaptive_mode = self._get_config_value('element_detection.adaptive_interval', True)

            # 获取错误发送延迟参数
            error_send_delay = 10  # 默认10秒
            if hasattr(self, 'error_send_delay_var') and self.error_send_delay_var:
                try:
                    error_send_delay = int(self.error_send_delay_var.get())
                    if error_send_delay < 1:
                        error_send_delay = 10
                except (ValueError, AttributeError):
                    error_send_delay = 10

            # 设置错误获取回调函数
            if hasattr(self.parent_ui, '_get_compilation_errors_for_copilot'):
                self.dom_script_instance.set_error_callback(self.parent_ui._get_compilation_errors_for_copilot)
                print("✅ 错误获取回调函数已设置")

            # 设置任务信息获取回调函数
            if hasattr(self.parent_ui, '_get_current_task_info_for_copilot'):
                self.dom_script_instance.set_task_info_callback(self.parent_ui._get_current_task_info_for_copilot)

            # 设置API更新回调函数
            if hasattr(self.parent_ui, '_update_task_step_status_via_api'):
                self.dom_script_instance.set_api_update_callback(self.parent_ui._update_task_step_status_via_api)

            # 设置错误发送延迟参数
            if hasattr(self.dom_script_instance, 'set_error_send_delay'):
                self.dom_script_instance.set_error_send_delay(error_send_delay)
                print(f"✅ 错误发送延迟已设置为: {error_send_delay}秒")

            # 启动循环检测
            success = self.dom_script_instance.start_auto_click_monitor(
                monitor_interval=monitor_interval,
                adaptive_interval=adaptive_mode
            )

            if success:
                self.is_loop_detection_running = True
                self.start_monitor_btn.config(state="disabled")
                self.stop_monitor_btn.config(state="normal")
                self.loop_detection_status_label.config(text="🟢 运行中", foreground="green")
                self.status_label.config(text=f"✅ 循环检测已启动（间隔: {monitor_interval}秒）")
                print(f"✅ 循环检测已启动，监控间隔: {monitor_interval}秒，自适应模式: {'启用' if adaptive_mode else '禁用'}")
            else:
                messagebox.showwarning("启动失败", "循环检测启动失败，可能没有启用的自动点击元素")
                self.dom_script_instance.disconnect()
                self.dom_script_instance = None

        except ImportError as e:
            error_msg = f"DOM模块导入失败: {e}"
            print(error_msg)
            messagebox.showerror("模块错误", f"DOM操作模块不可用:\n{error_msg}")
        except Exception as e:
            error_msg = f"启动循环检测失败: {e}"
            print(error_msg)
            messagebox.showerror("启动失败", error_msg)
            if self.dom_script_instance:
                self.dom_script_instance.disconnect()
                self.dom_script_instance = None

    def stop_loop_detection(self):
        """停止循环检测"""
        if not self.is_loop_detection_running:
            messagebox.showwarning("警告", "循环检测未在运行")
            return

        try:
            if self.dom_script_instance:
                # 停止监控
                self.dom_script_instance.stop_auto_click_monitor()

                # 断开连接
                self.dom_script_instance.disconnect()
                self.dom_script_instance = None

            self.is_loop_detection_running = False
            self.start_monitor_btn.config(state="normal")
            self.stop_monitor_btn.config(state="disabled")
            self.loop_detection_status_label.config(text="🔴 已停止", foreground="red")
            self.status_label.config(text="🛑 循环检测已停止")
            print("🛑 循环检测已停止")

        except Exception as e:
            error_msg = f"停止循环检测失败: {e}"
            print(error_msg)
            messagebox.showerror("停止失败", error_msg)

            # 强制重置状态
            self.is_loop_detection_running = False
            self.start_monitor_btn.config(state="normal")
            self.stop_monitor_btn.config(state="disabled")
            self.loop_detection_status_label.config(text="🔴 错误", foreground="red")
            self.dom_script_instance = None

    def _reset_loop_detection_ui(self):
        """重置循环检测UI状态"""
        self.start_monitor_btn.config(state="normal")
        self.stop_monitor_btn.config(state="disabled")
        self.loop_detection_status_label.config(text="🔴 未启动", foreground="gray")

    def update_execute_button_text(self):
        """更新执行按钮文本"""
        mode = self.execution_mode.get()
        if mode == "dom":
            self.execute_btn.config(text="🤖 DOM操作执行")
        elif mode == "script":
            self.execute_btn.config(text="📜 脚本执行")

    def show_empty_script_message(self):
        """显示空脚本消息"""
        if self.script_code_text:
            self.script_code_text.delete(1.0, tk.END)
            self.script_code_text.insert(tk.END, "请选择一个开发步骤以查看执行脚本")
        
        if self.script_name_label:
            self.script_name_label.config(text="未选择脚本")
        if self.script_language_label:
            self.script_language_label.config(text="-")
        if self.script_category_label:
            self.script_category_label.config(text="-")
