using SqlSugar;
using ProjectManagement.Core.Enums;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 项目实体 - 对应DatabaseSchema.sql中的Projects表
/// 功能: 存储软件开发项目的基本信息、状态、进度等
/// 支持: 项目生命周期管理、多状态跟踪、优先级管理
/// </summary>
[SugarTable("Projects", "项目信息管理表")]
public class Project : BaseEntity
{
    /// <summary>
    /// 项目名称，用于显示和识别
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "项目名称，用于显示和识别")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目详细描述，包括目标、范围等
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "项目详细描述，包括目标、范围等")]
    public string? Description { get; set; }

    /// <summary>
    /// 项目编号，业务唯一标识（如：PROJ-2024-001）
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "项目编号，业务唯一标识")]
    public string ProjectCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目负责人ID，关联Users表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "项目负责人ID，关联Users表")]
    public int OwnerId { get; set; }

    /// <summary>
    /// 项目状态: Planning(规划中), InProgress(进行中), Testing(测试中), Deployed(已部署), Completed(已完成)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "项目状态")]
    public string Status { get; set; } = "Planning";

    /// <summary>
    /// 项目优先级: Low(低), Medium(中), High(高), Critical(紧急)
    /// </summary>
    [SugarColumn(Length = 10, IsNullable = true, ColumnDescription = "项目优先级")]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// 项目计划开始日期
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "项目计划开始日期")]
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 项目计划结束日期
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "项目计划结束日期")]
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 项目进度百分比 (0-100)
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "项目进度百分比")]
    public int Progress { get; set; } = 0;

    /// <summary>
    /// 预估工时（小时）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "预估工时（小时）")]
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 实际工时（小时）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "实际工时（小时）")]
    public decimal? ActualHours { get; set; }

    /// <summary>
    /// 项目预算
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "项目预算")]
    public decimal? Budget { get; set; }

    /// <summary>
    /// 技术栈信息（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "技术栈信息")]
    public string? TechnologyStack { get; set; }

    /// <summary>
    /// 项目负责人 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Owner { get; set; }

    /// <summary>
    /// 项目需求对话记录 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<RequirementConversation> RequirementConversations { get; set; } = new();

    /// <summary>
    /// 项目需求文档 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<RequirementDocument> RequirementDocuments { get; set; } = new();

    /// <summary>
    /// 项目ER图 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ERDiagram> ERDiagrams { get; set; } = new();

    /// <summary>
    /// 项目上下文图 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ContextDiagram> ContextDiagrams { get; set; } = new();

    /// <summary>
    /// 项目代码生成任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<CodeGenerationTask> CodeGenerationTasks { get; set; } = new();

    /// <summary>
    /// 项目测试任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<TestTask> TestTasks { get; set; } = new();

    /// <summary>
    /// 项目部署任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<DeploymentTask> DeploymentTasks { get; set; } = new();

    /// <summary>
    /// 项目问题列表 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<Issue> Issues { get; set; } = new();

    /// <summary>
    /// 项目工作流程状态 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<WorkflowState> WorkflowStates { get; set; } = new();

    /// <summary>
    /// 项目系统日志 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<SystemLog> SystemLogs { get; set; } = new();
}
