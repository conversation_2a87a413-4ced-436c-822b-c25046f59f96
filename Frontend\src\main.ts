import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 导入全局样式
import '@/styles/index.scss'

// 开发环境下验证图标
if (import.meta.env.DEV) {
  import('@/utils/iconValidator')
}

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  // 这里可以添加错误上报逻辑
}

// 初始化认证状态
const initApp = async () => {
  const authStore = useAuthStore()

  console.log('应用初始化开始，当前认证状态:', {
    hasToken: !!authStore.token,
    hasRefreshToken: !!authStore.refreshToken,
    hasUser: !!authStore.user,
    isAuthenticated: authStore.isAuthenticated
  })

  try {
    // 如果有token，尝试初始化认证状态
    if (authStore.token) {
      console.log('检测到token，开始初始化认证状态...')
      await authStore.initializeAuth()
      console.log('认证状态初始化完成:', {
        hasUser: !!authStore.user,
        isAuthenticated: authStore.isAuthenticated
      })
    } else {
      console.log('未检测到token，跳过认证初始化')
    }
  } catch (error) {
    console.error('认证初始化失败:', error)
  } finally {
    // 挂载应用
    console.log('挂载应用到DOM')
    app.mount('#app')
  }
}

// 启动应用
initApp()
