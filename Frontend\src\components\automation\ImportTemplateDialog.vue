<template>
  <el-dialog
    v-model="visible"
    title="导入模板"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-content">
      <el-upload
        ref="uploadRef"
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :show-file-list="false"
        accept=".zip,.json"
        drag
      >
        <div class="upload-placeholder">
          <el-icon class="upload-icon"><Upload /></el-icon>
          <div class="upload-text">
            <p>点击或拖拽文件到此处上传</p>
            <p class="upload-tip">支持 ZIP 压缩包或 JSON 配置文件</p>
          </div>
        </div>
      </el-upload>

      <div v-if="importResult" class="import-result">
        <el-alert
          :type="importResult.success ? 'success' : 'error'"
          :title="importResult.success ? '导入成功' : '导入失败'"
          :description="getResultDescription()"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const uploadRef = ref()
const importResult = ref<{
  success: boolean
  imported: number
  errors?: string[]
} | null>(null)

const authStore = useAuthStore()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadAction = computed(() => '/api/custom-templates/import')

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${authStore.token}`
}))

// 方法
const beforeUpload = (file: File) => {
  const isValidType = file.type === 'application/zip' || 
                     file.type === 'application/json' ||
                     file.name.endsWith('.zip') ||
                     file.name.endsWith('.json')
  
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 ZIP 或 JSON 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  // 重置结果
  importResult.value = null
  
  return true
}

const handleUploadSuccess = (response: any) => {
  if (response.success) {
    importResult.value = {
      success: true,
      imported: response.data.imported,
      errors: response.data.errors
    }
    ElMessage.success(`导入成功，共导入 ${response.data.imported} 个模板`)
    emit('success')
  } else {
    importResult.value = {
      success: false,
      imported: 0,
      errors: [response.message || '导入失败']
    }
    ElMessage.error(response.message || '导入失败')
  }
}

const handleUploadError = () => {
  importResult.value = {
    success: false,
    imported: 0,
    errors: ['网络错误，导入失败']
  }
  ElMessage.error('导入失败')
}

const getResultDescription = () => {
  if (!importResult.value) return ''
  
  if (importResult.value.success) {
    let desc = `成功导入 ${importResult.value.imported} 个模板`
    if (importResult.value.errors && importResult.value.errors.length > 0) {
      desc += `，${importResult.value.errors.length} 个模板导入失败`
    }
    return desc
  } else {
    return importResult.value.errors?.join('; ') || '导入失败'
  }
}

const handleClose = () => {
  visible.value = false
  importResult.value = null
  uploadRef.value?.clearFiles()
}
</script>

<style scoped lang="scss">
.import-content {
  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #8c939d;

    .upload-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .upload-text {
      text-align: center;

      p {
        margin: 0;
        line-height: 1.5;
      }

      .upload-tip {
        font-size: 12px;
        color: #c0c4cc;
        margin-top: 8px;
      }
    }
  }

  .import-result {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}
</style>
