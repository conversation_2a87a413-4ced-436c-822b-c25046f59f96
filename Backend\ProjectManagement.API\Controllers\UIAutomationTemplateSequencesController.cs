using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.API.Helper;
using SqlSugar;
using System.Text.Json;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UIAutomationTemplateSequencesController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<UIAutomationTemplateSequencesController> _logger;

        public UIAutomationTemplateSequencesController(
            ISqlSugarClient db,
            ILogger<UIAutomationTemplateSequencesController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有序列
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UIAutoMationTemplateSequence>>> GetSequences()
        {
            try
            {
                var sequences = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(s => s.IsActive && !s.IsDeleted)
                    .OrderByDescending(s => s.CreatedTime)
                    .ToListAsync();

                // 手动加载步骤数据
                foreach (var sequence in sequences)
                {
                    sequence.Steps = await _db.Queryable<UIAutoMationTemplateStep>()
                        .Where(step => step.SequenceId == sequence.Id && step.IsActive)
                        .OrderBy(step => step.StepOrder)
                        .ToListAsync();
                }

                return Ok(sequences);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取序列列表失败");
                return StatusCode(500, new { message = "获取序列列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取序列
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<UIAutoMationTemplateSequence>> GetSequence(int id)
        {
            try
            {
                var sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(s => s.Id == id && s.IsActive && !s.IsDeleted)
                    .FirstAsync();

                if (sequence == null)
                {
                    return NotFound(new { message = $"未找到ID为{id}的序列" });
                }

                // 手动加载步骤数据
                sequence.Steps = await _db.Queryable<UIAutoMationTemplateStep>()
                    .Where(step => step.SequenceId == sequence.Id && step.IsActive)
                    .OrderBy(step => step.StepOrder)
                    .ToListAsync();

                return Ok(sequence);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取序列详情失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取序列详情失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 创建新序列
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<UIAutoMationTemplateSequence>> CreateSequence(CreateUIAutomationSequenceRequest request)
        {
            try
            {
                // 验证请求数据
                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return BadRequest(new { message = "序列名称不能为空" });
                }

                // 检查名称是否重复
                var existingSequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .FirstAsync(s => s.Name == request.Name && s.IsActive && !s.IsDeleted);

                if (existingSequence != null)
                {
                    return Conflict(new { message = $"序列名称 '{request.Name}' 已存在" });
                }

                // 创建序列实体
                var sequence = new UIAutoMationTemplateSequence
                {
                    Name = request.Name,
                    Description = request.Description,
                    Category = request.Category ?? "代码生成",
                    Tags = request.Tags,
                    Notes = request.Notes,
                    SourceCode = request.SourceCode,
                    CodeLanguage = request.CodeLanguage ?? "javascript",
                    ExtendedProperties = request.ExtendedProperties,
                    IsActive = true,
                    CreatedBy = UserHelper.GetCurrentUserId(User),
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    Remarks = "通过代码编辑器创建"
                };

                // 开始事务
                await _db.Ado.BeginTranAsync();

                try
                {
                    // 插入序列
                    var sequenceId = await _db.Insertable(sequence).ExecuteReturnIdentityAsync();
                    sequence.Id = sequenceId;

                    // 添加步骤
                    if (request.Steps != null && request.Steps.Any())
                    {
                        var steps = request.Steps.Select(stepRequest => new UIAutoMationTemplateStep
                        {
                            SequenceId = sequenceId,
                            StepOrder = stepRequest.StepOrder,
                            ActionType = stepRequest.ActionType,
                            LogicType = stepRequest.LogicType,
                            Description = stepRequest.Description,
                            Parameters = JsonSerializer.Serialize(stepRequest.Parameters),
                            TimeoutSeconds = stepRequest.TimeoutSeconds,
                            MaxRetries = stepRequest.MaxRetries,
                            IsActive = stepRequest.IsActive,
                            ConditionExpression = stepRequest.ConditionExpression,
                            JumpToStepId = stepRequest.JumpToStepId,
                            LoopCount = stepRequest.LoopCount,
                            LoopVariable = stepRequest.LoopVariable,
                            GroupId = stepRequest.GroupId,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now
                        }).ToList();

                        await _db.Insertable(steps).ExecuteCommandAsync();
                    }

                    await _db.Ado.CommitTranAsync();

                    _logger.LogInformation("成功创建序列: {Name}, ID: {Id}", sequence.Name, sequence.Id);

                    return CreatedAtAction(
                        nameof(GetSequence),
                        new { id = sequence.Id },
                        sequence);
                }
                catch
                {
                    await _db.Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建序列失败: {Name}", request.Name);
                return StatusCode(500, new { message = "创建序列失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新序列
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSequence(int id, UpdateUIAutomationSequenceRequest request)
        {
            try
            {
                var sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .FirstAsync(s => s.Id == id && s.IsActive && !s.IsDeleted);

                if (sequence == null)
                {
                    return NotFound(new { message = $"未找到ID为{id}的序列" });
                }

                // 如果更新名称，检查名称是否与其他序列重复
                if (!string.IsNullOrWhiteSpace(request.Name) && request.Name != sequence.Name)
                {
                    var existingSequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                        .FirstAsync(s => s.Name == request.Name && s.Id != id && s.IsActive && !s.IsDeleted);

                    if (existingSequence != null)
                    {
                        return Conflict(new { message = $"序列名称 '{request.Name}' 已存在" });
                    }
                }

                // 更新基本信息
                sequence.Name = request.Name ?? sequence.Name;
                sequence.Description = request.Description ?? sequence.Description;
                sequence.Category = request.Category ?? sequence.Category;
                sequence.Tags = request.Tags ?? sequence.Tags;
                sequence.Notes = request.Notes ?? sequence.Notes;
                sequence.SourceCode = request.SourceCode ?? sequence.SourceCode;
                sequence.CodeLanguage = request.CodeLanguage ?? sequence.CodeLanguage;
                sequence.ExtendedProperties = request.ExtendedProperties ?? sequence.ExtendedProperties;
                sequence.UpdatedTime = DateTime.Now;
                sequence.UpdatedBy = null; // TODO: 从当前用户上下文获取用户ID

                await _db.Updateable(sequence).ExecuteCommandAsync();

                _logger.LogInformation("成功更新序列: {Name}, ID: {Id}", sequence.Name, sequence.Id);

                return Ok(sequence);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新序列失败，ID: {Id}", id);
                return StatusCode(500, new { message = "更新序列失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除序列（软删除）
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSequence(int id)
        {
            try
            {
                var sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .FirstAsync(s => s.Id == id && s.IsActive && !s.IsDeleted);

                if (sequence == null)
                {
                    return NotFound(new { message = $"未找到ID为{id}的序列" });
                }

                sequence.IsDeleted = true;
                sequence.UpdatedTime = DateTime.Now;
                sequence.UpdatedBy = null; // TODO: 从当前用户上下文获取用户ID
                sequence.DeletedTime = DateTime.Now;
                sequence.DeletedBy = null; // TODO: 从当前用户上下文获取用户ID

                await _db.Updateable(sequence).ExecuteCommandAsync();

                _logger.LogInformation("成功删除序列: {Name}, ID: {Id}", sequence.Name, sequence.Id);

                return Ok(new { message = "序列删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除序列失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除序列失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据代码语言获取序列
        /// </summary>
        [HttpGet("by-language/{language}")]
        public async Task<ActionResult<IEnumerable<UIAutoMationTemplateSequence>>> GetSequencesByLanguage(string language)
        {
            try
            {
                var sequences = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(s => s.IsActive && !s.IsDeleted && s.Category.Contains(language))
                    .OrderByDescending(s => s.CreatedTime)
                    .ToListAsync();

                return Ok(sequences);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据语言获取序列失败: {Language}", language);
                return StatusCode(500, new { message = "获取序列失败", error = ex.Message });
            }
        }
    }

    // 请求模型
    public class CreateUIAutomationSequenceRequest
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public string? SourceCode { get; set; }
        public string? CodeLanguage { get; set; }
        public string? ExtendedProperties { get; set; }
        public List<CreateUIAutomationStepRequest>? Steps { get; set; }
    }

    public class CreateUIAutomationStepRequest
    {
        public int StepOrder { get; set; }
        public string ActionType { get; set; } = string.Empty;
        public string? LogicType { get; set; }
        public string? Description { get; set; }
        public object? Parameters { get; set; }
        public int TimeoutSeconds { get; set; } = 5;
        public int MaxRetries { get; set; } = 3;
        public bool IsActive { get; set; } = true;
        public string? ConditionExpression { get; set; }
        public int? JumpToStepId { get; set; }
        public int? LoopCount { get; set; }
        public string? LoopVariable { get; set; }
        public string? GroupId { get; set; }
    }

    public class UpdateUIAutomationSequenceRequest
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public string? SourceCode { get; set; }
        public string? CodeLanguage { get; set; }
        public string? ExtendedProperties { get; set; }
    }
}
