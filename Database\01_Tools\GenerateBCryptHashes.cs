// =====================================================
// BCrypt密码哈希生成工具
// 用于生成正确的BCrypt密码哈希值
// 解决 BCrypt.Net.SaltParseException: Invalid salt version 错误
// =====================================================

using System;

class Program
{
    static void Main()
    {
        Console.WriteLine("BCrypt密码哈希生成工具");
        Console.WriteLine("=====================================================");
        Console.WriteLine();

        // 生成测试密码的哈希值
        string testPassword = "password";
        Console.WriteLine($"为测试密码生成BCrypt哈希: {testPassword}");
        Console.WriteLine();

        // 生成5个不同的哈希值（每个用户一个）
        string[] users = { "admin", "developer1", "pm1", "tester1", "product1" };

        for (int i = 0; i < users.Length; i++)
        {
            string hash = BCrypt.Net.BCrypt.HashPassword(testPassword);
            Console.WriteLine($"{users[i]}: {hash}");
        }

        Console.WriteLine();
        Console.WriteLine("验证哈希值:");
        string verifyHash = BCrypt.Net.BCrypt.HashPassword(testPassword);
        bool isValid = BCrypt.Net.BCrypt.Verify(testPassword, verifyHash);
        Console.WriteLine($"生成的哈希: {verifyHash}");
        Console.WriteLine($"验证结果: {isValid}");

        Console.WriteLine();
        Console.WriteLine("生成强密码的哈希值:");
        string strongPassword = "Admin123!";
        string strongHash = BCrypt.Net.BCrypt.HashPassword(strongPassword);
        Console.WriteLine($"强密码 '{strongPassword}' 的哈希: {strongHash}");

        Console.WriteLine();
        Console.WriteLine("使用说明:");
        Console.WriteLine("1. 复制上面生成的哈希值");
        Console.WriteLine("2. 在数据库中更新对应用户的PasswordHash字段");
        Console.WriteLine("3. 或者运行 FixBCryptHashes.sql 脚本自动修复");
        Console.WriteLine();
        Console.WriteLine("注意: 每次运行都会生成不同的哈希值，这是正常的！");
        Console.WriteLine("BCrypt会自动生成随机盐值，所以相同密码的哈希值每次都不同。");
    }
}

// 已知有效的BCrypt哈希值示例（密码: "password"）:
// $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi
