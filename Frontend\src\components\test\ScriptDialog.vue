<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑脚本' : '新建脚本'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="脚本名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入脚本名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%">
              <el-option label="UI测试" value="ui" />
              <el-option label="API测试" value="api" />
              <el-option label="集成测试" value="integration" />
              <el-option label="性能测试" value="performance" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入脚本描述"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-tag
          v-for="tag in formData.tags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          style="margin-right: 8px;"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          style="width: 100px;"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
      </el-form-item>

      <el-form-item label="优先级">
        <el-radio-group v-model="formData.priority">
          <el-radio label="low">低</el-radio>
          <el-radio label="medium">中</el-radio>
          <el-radio label="high">高</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="超时时间">
        <el-input-number
          v-model="formData.timeout"
          :min="1"
          :max="300"
          controls-position="right"
          style="width: 200px;"
        />
        <span style="margin-left: 8px; color: #909399;">秒</span>
      </el-form-item>

      <el-form-item label="浏览器">
        <el-select v-model="formData.browser" style="width: 200px;">
          <el-option label="Chrome" value="chrome" />
          <el-option label="Firefox" value="firefox" />
          <el-option label="Safari" value="safari" />
          <el-option label="Edge" value="edge" />
        </el-select>
      </el-form-item>

      <el-form-item label="无头模式">
        <el-switch v-model="formData.headless" />
        <span style="margin-left: 8px; color: #909399;">启用后浏览器将在后台运行</span>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  script?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  script: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [script: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const inputRef = ref()
const saving = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')

const formData = reactive({
  id: 0,
  name: '',
  description: '',
  category: 'ui',
  tags: [] as string[],
  priority: 'medium',
  timeout: 30,
  browser: 'chrome',
  headless: false,
  code: '',
  config: {}
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => props.script && props.script.id > 0)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入脚本名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 监听器
watch(() => props.script, (newScript) => {
  if (newScript) {
    Object.assign(formData, {
      id: newScript.id || 0,
      name: newScript.name || '',
      description: newScript.description || '',
      category: newScript.category || 'ui',
      tags: newScript.tags || [],
      priority: newScript.priority || 'medium',
      timeout: newScript.timeout || 30,
      browser: newScript.browser || 'chrome',
      headless: newScript.headless || false,
      code: newScript.code || '',
      config: newScript.config || {}
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      id: 0,
      name: '',
      description: '',
      category: 'ui',
      tags: [],
      priority: 'medium',
      timeout: 30,
      browser: 'chrome',
      headless: false,
      code: '',
      config: {}
    })
  }
}, { immediate: true })

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 构建脚本对象
    const script = {
      ...formData,
      updatedTime: new Date(),
      status: isEdit.value ? (formData as any).status || '就绪' : '草稿'
    }

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    emit('save', script)
    saving.value = false

  } catch (error) {
    console.error('表单验证失败:', error)
    saving.value = false
  }
}

const removeTag = (tag: string) => {
  const index = formData.tags.indexOf(tag)
  if (index > -1) {
    formData.tags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 0 20px;
}
</style>