using SqlSugar;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Enums;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 项目仓储实现
/// </summary>
public class ProjectRepository : BaseRepository<Project>, IProjectRepository
{
    public ProjectRepository(ISqlSugarClient db, ILogger<ProjectRepository> logger) : base(db, logger)
    {
    }

    public async Task<Project?> GetByProjectCodeAsync(string projectCode)
    {
        try
        {
            return await _db.Queryable<Project>()
                .Where(x => x.ProjectCode == projectCode && !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目编号获取项目失败，项目编号: {ProjectCode}", projectCode);
            throw;
        }
    }

    public async Task<bool> IsProjectCodeExistsAsync(string projectCode, int? excludeProjectId = null)
    {
        try
        {
            var query = _db.Queryable<Project>()
                .Where(x => x.ProjectCode == projectCode && !x.IsDeleted);

            if (excludeProjectId.HasValue)
            {
                query = query.Where(x => x.Id != excludeProjectId.Value);
            }

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查项目编号是否存在失败，项目编号: {ProjectCode}", projectCode);
            throw;
        }
    }

    /// <summary>
    /// 生成唯一的项目编号（线程安全）
    /// </summary>
    /// <returns>唯一的项目编号</returns>
    public async Task<string> GenerateUniqueProjectCodeAsync()
    {
        var currentYear = DateTime.Now.Year;
        var yearPrefix = $"PROJ-{currentYear}-";
        var maxRetries = 50; // 增加重试次数

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // 简化的方法：直接查询所有当年的项目代码（包括已删除的）
                var existingProjects = await _db.Queryable<Project>()
                    .Where(x => x.ProjectCode.StartsWith(yearPrefix))
                    .Select(x => x.ProjectCode)
                    .ToListAsync();

                // 提取所有序号
                var existingSequences = new HashSet<int>();
                foreach (var code in existingProjects)
                {
                    if (code.Length > yearPrefix.Length)
                    {
                        var sequencePart = code.Substring(yearPrefix.Length);

                        // 如果有时间戳后缀，只取前面的序号部分
                        var dashIndex = sequencePart.IndexOf('-');
                        if (dashIndex > 0)
                        {
                            sequencePart = sequencePart.Substring(0, dashIndex);
                        }

                        if (int.TryParse(sequencePart, out var sequence))
                        {
                            existingSequences.Add(sequence);
                        }
                    }
                }

                // 找到第一个未使用的序号
                int newSequence = 1;
                while (existingSequences.Contains(newSequence))
                {
                    newSequence++;
                }

                var projectCode = $"{yearPrefix}{newSequence:D3}";

                // 最终检查：确保这个代码真的不存在
                var finalCheck = await _db.Queryable<Project>()
                    .Where(x => x.ProjectCode == projectCode && !x.IsDeleted)
                    .AnyAsync();

                if (!finalCheck)
                {
                    _logger.LogInformation("生成项目编号成功: {ProjectCode} (尝试第 {Attempt} 次)", projectCode, attempt);
                    return projectCode;
                }

                // 如果还是冲突，添加时间戳和随机数
                var timestamp = DateTime.Now.ToString("HHmmss");
                var random = new Random().Next(100, 999);
                var timestampCode = $"{yearPrefix}{newSequence:D3}-{timestamp}-{random}";

                var timestampCheck = await _db.Queryable<Project>()
                    .Where(x => x.ProjectCode == timestampCode && !x.IsDeleted)
                    .AnyAsync();

                if (!timestampCheck)
                {
                    _logger.LogWarning("项目编号冲突，使用时间戳版本: {ProjectCode} (尝试第 {Attempt} 次)", timestampCode, attempt);
                    return timestampCode;
                }

                _logger.LogWarning("项目编号生成冲突，重试第 {Attempt} 次", attempt);

                // 短暂延迟避免高并发冲突
                await Task.Delay(50 * attempt); // 递增延迟
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成项目编号时发生错误，尝试第 {Attempt} 次", attempt);

                if (attempt == maxRetries)
                {
                    // 最后一次尝试失败，使用 GUID 备用方案
                    var fallbackCode = $"PROJ-{currentYear}-999-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
                    _logger.LogError("项目编号生成失败，使用备用编号: {ProjectCode}", fallbackCode);
                    return fallbackCode;
                }

                await Task.Delay(100 * attempt);
            }
        }

        // 理论上不应该到达这里，但作为最终保险
        var emergencyCode = $"PROJ-{currentYear}-999-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";
        _logger.LogError("项目编号生成超出最大重试次数，使用紧急备用编号: {ProjectCode}", emergencyCode);
        return emergencyCode;
    }

    public async Task<List<Project>> GetProjectsByManagerAsync(int projectManagerId)
    {
        try
        {
            // 使用OwnerId作为项目经理ID
            return await _db.Queryable<Project>()
                .Where(x => x.OwnerId == projectManagerId && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目经理获取项目列表失败，项目经理ID: {ProjectManagerId}", projectManagerId);
            throw;
        }
    }

    public async Task<List<Project>> GetProjectsByClientAsync(int clientId)
    {
        try
        {
            // 暂时返回空列表，因为Project实体中没有ClientId字段
            _logger.LogWarning("GetProjectsByClientAsync方法暂未实现，因为Project实体缺少ClientId字段");
            return new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据客户获取项目列表失败，客户ID: {ClientId}", clientId);
            throw;
        }
    }

    public async Task<List<Project>> GetProjectsByStatusAsync(ProjectStatus status)
    {
        try
        {
            var statusString = status.ToString();
            return await _db.Queryable<Project>()
                .Where(x => x.Status == statusString && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取项目列表失败，状态: {Status}", status);
            throw;
        }
    }

    public async Task<List<Project>> GetProjectsByTypeAsync(ProjectType type)
    {
        try
        {
            // 暂时返回空列表，因为Project实体中没有Type字段
            _logger.LogWarning("GetProjectsByTypeAsync方法暂未实现，因为Project实体缺少Type字段");
            return new List<Project>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据类型获取项目列表失败，类型: {Type}", type);
            throw;
        }
    }

    public async Task<List<Project>> GetProjectsByPriorityAsync(Priority priority)
    {
        try
        {
            var priorityString = priority.ToString();
            return await _db.Queryable<Project>()
                .Where(x => x.Priority == priorityString && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据优先级获取项目列表失败，优先级: {Priority}", priority);
            throw;
        }
    }

    public async Task<List<Project>> GetUserProjectsAsync(int userId)
    {
        try
        {
            // 获取用户作为项目负责人的项目
            return await _db.Queryable<Project>()
                .Where(x => x.OwnerId == userId && !x.IsDeleted)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户项目列表失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<List<Project>> GetUpcomingDeadlineProjectsAsync(int days = 7)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(days);
            return await _db.Queryable<Project>()
                .Where(x => !x.IsDeleted &&
                           x.Status == "InProgress" &&
                           x.EndDate.HasValue &&
                           x.EndDate.Value <= cutoffDate &&
                           x.EndDate.Value >= DateTime.Now)
                .OrderBy(x => x.EndDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取即将到期项目失败，天数: {Days}", days);
            throw;
        }
    }

    public async Task<List<Project>> GetOverdueProjectsAsync()
    {
        try
        {
            return await _db.Queryable<Project>()
                .Where(x => !x.IsDeleted &&
                           x.Status == "InProgress" &&
                           x.EndDate.HasValue &&
                           x.EndDate.Value < DateTime.Now)
                .OrderBy(x => x.EndDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取延期项目失败");
            throw;
        }
    }

    public async Task<bool> UpdateProgressAsync(int projectId, int progress)
    {
        try
        {
            var result = await _db.Updateable<Project>()
                .SetColumns(x => new Project
                {
                    Progress = progress,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == projectId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新项目进度成功，项目ID: {ProjectId}, 进度: {Progress}%", projectId, progress);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目进度失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<bool> UpdateStatusAsync(int projectId, ProjectStatus status, int? updatedBy = null)
    {
        try
        {
            var statusString = status.ToString();
            var result = await _db.Updateable<Project>()
                .SetColumns(x => new Project
                {
                    Status = statusString,
                    UpdatedTime = DateTime.UtcNow,
                    UpdatedBy = updatedBy
                })
                .Where(x => x.Id == projectId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新项目状态成功，项目ID: {ProjectId}, 状态: {Status}", projectId, status);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目状态失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<bool> UpdateActualStartDateAsync(int projectId, DateTime actualStartDate)
    {
        try
        {
            // 暂时使用StartDate字段
            var result = await _db.Updateable<Project>()
                .SetColumns(x => new Project
                {
                    StartDate = actualStartDate,
                    UpdatedTime = DateTime.UtcNow
                })
                .Where(x => x.Id == projectId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新项目实际开始时间成功，项目ID: {ProjectId}", projectId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目实际开始时间失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<bool> UpdateActualEndDateAsync(int projectId, DateTime actualEndDate)
    {
        try
        {
            // 暂时使用EndDate字段
            var result = await _db.Updateable<Project>()
                .SetColumns(x => new Project
                {
                    EndDate = actualEndDate,
                    UpdatedTime = DateTime.UtcNow
                })
                .Where(x => x.Id == projectId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新项目实际结束时间成功，项目ID: {ProjectId}", projectId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目实际结束时间失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<bool> UpdateActualCostAsync(int projectId, decimal actualCost)
    {
        try
        {
            // 暂时使用Budget字段
            var result = await _db.Updateable<Project>()
                .SetColumns(x => new Project
                {
                    Budget = actualCost,
                    UpdatedTime = DateTime.UtcNow
                })
                .Where(x => x.Id == projectId && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("更新项目实际成本成功，项目ID: {ProjectId}", projectId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目实际成本失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<PagedResult<Project>> SearchProjectsAsync(
        string keyword,
        ProjectStatus? status = null,
        ProjectType? type = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<Project>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x =>
                    x.Name.Contains(keyword) ||
                    x.ProjectCode.Contains(keyword) ||
                    (x.Description != null && x.Description.Contains(keyword)));
            }

            // 状态过滤
            if (status.HasValue)
            {
                var statusString = status.Value.ToString();
                query = query.Where(x => x.Status == statusString);
            }

            // 类型过滤 - 暂时跳过，因为Project实体没有Type字段
            if (type.HasValue)
            {
                _logger.LogWarning("项目类型过滤暂未实现，因为Project实体缺少Type字段");
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(x => x.CreatedTime)
                .ToPageListAsync(pageIndex, pageSize);

            return new PagedResult<Project>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索项目失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<ProjectStatistics> GetProjectStatisticsAsync()
    {
        try
        {
            var projects = await _db.Queryable<Project>()
                .Where(x => !x.IsDeleted)
                .ToListAsync();

            var statistics = new ProjectStatistics
            {
                TotalProjects = projects.Count,
                InProgressProjects = projects.Count(x => x.Status == "InProgress"),
                CompletedProjects = projects.Count(x => x.Status == "Completed"),
                OverdueProjects = projects.Count(x =>
                    x.Status == "InProgress" &&
                    x.EndDate.HasValue &&
                    x.EndDate.Value < DateTime.Now),
                AverageProgress = projects.Count > 0 ? (decimal)projects.Average(x => x.Progress) : 0
            };

            // 按状态分组 - 需要转换为枚举
            var statusGroups = projects.GroupBy(x => x.Status).ToList();
            statistics.ProjectsByStatus = new Dictionary<ProjectStatus, int>();
            foreach (var group in statusGroups)
            {
                if (Enum.TryParse<ProjectStatus>(group.Key, out var statusEnum))
                {
                    statistics.ProjectsByStatus[statusEnum] = group.Count();
                }
            }

            // 按类型分组 - 暂时为空，因为Project实体没有Type字段
            statistics.ProjectsByType = new Dictionary<ProjectType, int>();

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目统计信息失败");
            throw;
        }
    }

    public async Task<BudgetStatistics> GetBudgetStatisticsAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var projects = await _db.Queryable<Project>()
                .Where(x => !x.IsDeleted &&
                           x.CreatedTime >= startDate &&
                           x.CreatedTime <= endDate)
                .ToListAsync();

            var projectsWithBudget = projects.Where(x => x.Budget.HasValue).ToList();
            // 暂时使用Budget作为实际成本，因为Project实体没有ActualCost字段
            var projectsWithCost = projectsWithBudget;

            var statistics = new BudgetStatistics
            {
                TotalBudget = projectsWithBudget.Sum(x => x.Budget ?? 0),
                TotalActualCost = projectsWithCost.Sum(x => x.Budget ?? 0), // 暂时使用Budget
                OverBudgetProjects = 0, // 无法计算，因为没有实际成本字段
                AverageProjectBudget = projectsWithBudget.Count > 0 ?
                    projectsWithBudget.Average(x => x.Budget ?? 0) : 0,
                AverageProjectCost = projectsWithCost.Count > 0 ?
                    projectsWithCost.Average(x => x.Budget ?? 0) : 0 // 暂时使用Budget
            };

            // 计算预算利用率
            if (statistics.TotalBudget > 0)
            {
                statistics.BudgetUtilization = statistics.TotalActualCost / statistics.TotalBudget * 100;
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取预算统计信息失败");
            throw;
        }
    }

    public async Task<PagedResult<Project>> GetUserProjectsAsync(int userId, int pageNumber, int pageSize, string? status = null, string? search = null)
    {
        try
        {
            var query = _db.Queryable<Project>()
                .Where(x => x.OwnerId == userId && !x.IsDeleted);

            // 状态过滤
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(x => x.Status == status);
            }

            // 搜索过滤
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(x => x.Name.Contains(search) ||
                                        x.ProjectCode.Contains(search) ||
                                        (x.Description != null && x.Description.Contains(search)));
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(x => x.CreatedTime)
                .ToPageListAsync(pageNumber, pageSize);

            return new PagedResult<Project>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户项目分页列表失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<ProjectManagement.Core.DTOs.ProjectStatisticsDto> GetUserProjectStatisticsAsync(int userId)
    {
        try
        {
            var projects = await _db.Queryable<Project>()
                .Where(x => x.OwnerId == userId && !x.IsDeleted)
                .ToListAsync();

            var statistics = new ProjectManagement.Core.DTOs.ProjectStatisticsDto
            {
                TotalProjects = projects.Count,
                ActiveProjects = projects.Count(x => x.Status == "InProgress"),
                CompletedProjects = projects.Count(x => x.Status == "Completed"),
                PausedProjects = projects.Count(x => x.Status == "Paused"),
                AverageCompletionDays = 0, // 需要实际开始和结束日期来计算
                TotalBudget = projects.Where(x => x.Budget.HasValue).Sum(x => x.Budget ?? 0),
                UsedBudget = 0 // 需要实际成本字段来计算
            };

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户项目统计信息失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<int> SoftDeleteProjectDevelopmentStepsAsync(int projectId, int deletedBy)
    {
        try
        {
            // 软删除项目下的所有开发步骤（包括子步骤）
            var result = await _db.Updateable<DevelopmentStep>()
                .SetColumns(s => new DevelopmentStep
                {
                    IsDeleted = true,
                    DeletedTime = DateTime.Now,
                    DeletedBy = deletedBy
                })
                .Where(s => s.ProjectId == projectId && !s.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("软删除项目开发步骤成功，项目ID: {ProjectId}, 删除步骤数量: {Count}", projectId, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "软删除项目开发步骤失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }
}
