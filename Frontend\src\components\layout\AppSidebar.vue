<template>
  <div class="app-sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo">
      <router-link to="/dashboard" class="logo-link">
        <el-icon class="logo-icon" size="24">
          <Platform />
        </el-icon>
        <span v-if="!collapsed" class="logo-text">AI开发平台</span>
      </router-link>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="true"
      :router="true"
      class="sidebar-menu"
      background-color="var(--el-bg-color)"
      text-color="var(--el-text-color-primary)"
      active-text-color="var(--el-color-primary)"
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <el-menu-item
          v-if="!route.children || route.children.length === 0"
          :index="route.path"
          :disabled="route.disabled"
        >
          <el-icon v-if="route.meta?.icon">
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>
            <span>{{ route.meta?.title }}</span>
          </template>
        </el-menu-item>

        <el-sub-menu
          v-else
          :index="route.path"
          :disabled="route.disabled"
        >
          <template #title>
            <el-icon v-if="route.meta?.icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span>{{ route.meta?.title }}</span>
          </template>

          <el-menu-item
            v-for="child in route.children"
            :key="child.path"
            :index="child.path"
            :disabled="child.disabled"
          >
            <el-icon v-if="child.meta?.icon">
              <component :is="child.meta.icon" />
            </el-icon>
            <template #title>
              <span>{{ child.meta?.title }}</span>
            </template>
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>

    <!-- 折叠按钮 -->
    <div class="sidebar-toggle">
      <el-button
        type="text"
        @click="$emit('toggle')"
        class="toggle-button"
      >
        <el-icon>
          <Fold v-if="!collapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { Platform, Fold, Expand } from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()
defineEmits<{
  toggle: []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const { path } = route
  // 处理动态路由，返回基础路径
  if (path.includes('/projects/') && path !== '/projects') {
    return '/projects'
  }
  if (path.includes('/requirements/') && path !== '/requirements') {
    return '/requirements'
  }
  if (path.includes('/design/') && path !== '/design') {
    return '/design'
  }
  if (path.includes('/development/') && path !== '/development') {
    return '/development'
  }
  // 处理prompt相关路由
  if (path.startsWith('/prompt-')) {
    return '/prompt-templates'
  }
  // 处理管理员路由
  if (path.startsWith('/admin/')) {
    return path
  }
  return path
})

// 过滤后的菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()

  // 获取所有路由并构建菜单树
  const routeMap = new Map()

  // 首先收集所有有效路由
  routes.forEach(route => {
    // 过滤条件：
    // 1. 不在菜单中隐藏
    // 2. 需要认证
    // 3. 有标题
    // 4. 不是管理员专用路由（单独处理）
    if (route.meta?.hideInMenu) return
    if (!route.meta?.requiresAuth) return
    if (!route.meta?.title) return
    if (route.path.startsWith('/admin/') || route.meta?.permission === 'admin') return

    // 检查权限
    if (route.meta?.permission && !authStore.hasPermission(route.meta.permission as string)) {
      return
    }

    // 处理路由层级
    const pathSegments = route.path.split('/').filter(Boolean)

    if (pathSegments.length === 1) {
      // 一级路由
      if (!routeMap.has(route.path)) {
        routeMap.set(route.path, {
          path: route.path,
          name: route.name,
          meta: route.meta,
          children: [],
          disabled: false
        })
      }
    } else if (pathSegments.length === 2) {
      // 二级路由
      const parentPath = `/${pathSegments[0]}`

      // 确保父路由存在
      if (!routeMap.has(parentPath)) {
        // 查找父路由定义
        const parentRoute = routes.find(r => r.path === parentPath)
        if (parentRoute && parentRoute.meta?.title) {
          routeMap.set(parentPath, {
            path: parentPath,
            name: parentRoute.name,
            meta: parentRoute.meta,
            children: [],
            disabled: false
          })
        }
      }

      // 添加子路由
      const parentMenu = routeMap.get(parentPath)
      if (parentMenu) {
        parentMenu.children.push({
          path: route.path,
          name: route.name,
          meta: route.meta,
          children: [],
          disabled: false
        })
      }
    }
  })

  const baseRoutes = Array.from(routeMap.values())

  // 定义菜单顺序
  const menuOrder = [
    '/dashboard',
    '/projects',
    '/requirements',
    '/design',
    '/development',
    '/code-generation',
    '/automation',
    '/testing',
    '/deployment',
    '/prompt-templates',
    '/issues'
  ]

  // 按照预定义顺序排序菜单
  baseRoutes.sort((a, b) => {
    const indexA = menuOrder.indexOf(a.path)
    const indexB = menuOrder.indexOf(b.path)

    // 如果都在预定义列表中，按照列表顺序排序
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB
    }

    // 如果只有一个在预定义列表中，预定义的排在前面
    if (indexA !== -1) return -1
    if (indexB !== -1) return 1

    // 如果都不在预定义列表中，按照路径字母顺序排序
    return a.path.localeCompare(b.path)
  })

  // 管理员菜单 - 包含所有需要admin权限的路由
  const adminRoutes = routes.filter(route => {
    return (route.path.startsWith('/admin/') || route.meta?.permission === 'admin') &&
           !route.meta?.hideInMenu &&
           route.meta?.requiresAuth &&
           route.meta?.title &&
           authStore.hasPermission('admin')
  })

  console.log('管理员菜单检查:', {
    hasAdminPermission: authStore.hasPermission('admin'),
    adminRoutes: adminRoutes.length,
    currentUser: authStore.user,
    currentUserRole: authStore.user?.role,
    allAdminRoutes: routes.filter(route => route.path.startsWith('/admin/') || route.meta?.permission === 'admin'),
    filteredAdminRoutes: adminRoutes.map(r => ({ path: r.path, title: r.meta?.title }))
  })

  // 如果有管理员权限，添加管理菜单
  if (authStore.hasPermission('admin') && adminRoutes.length > 0) {
    const adminMenu = {
      path: '/admin',
      name: 'Admin',
      meta: {
        title: '系统管理',
        icon: 'Setting'
      },
      children: adminRoutes.map(route => ({
        path: route.path,
        name: route.name,
        meta: route.meta,
        children: [],
        disabled: false
      })),
      disabled: false
    }

    baseRoutes.push(adminMenu)
  }

  return baseRoutes
})


</script>

<style lang="scss" scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--el-text-color-primary);
    font-weight: 600;

    .logo-icon {
      color: var(--el-color-primary);
      margin-right: 12px;
    }

    .logo-text {
      font-size: 18px;
      white-space: nowrap;
    }
  }
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 2px;
  }

  :deep(.el-menu-item) {
    height: 48px;
    line-height: 48px;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    &.is-active {
      background-color: var(--el-color-primary-light-9);
      border-right: 3px solid var(--el-color-primary);
    }
  }

  :deep(.el-sub-menu__title) {
    height: 48px;
    line-height: 48px;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.sidebar-toggle {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid var(--el-border-color-lighter);

  .toggle-button {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

// 折叠状态样式
.app-sidebar.collapsed {
  .sidebar-menu {
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      padding: 0 20px;
      justify-content: center;
    }
  }
}
</style>
