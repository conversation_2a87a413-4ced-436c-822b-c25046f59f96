namespace ProjectManagement.Core.Enums;

/// <summary>
/// 用户角色枚举 - 对应DatabaseSchema.sql中的用户角色定义
/// </summary>
public enum UserRole
{
    /// <summary>
    /// 普通用户
    /// </summary>
    User = 1,

    /// <summary>
    /// 项目经理
    /// </summary>
    ProjectManager = 2,

    /// <summary>
    /// 开发人员
    /// </summary>
    Developer = 3,

    /// <summary>
    /// 测试人员
    /// </summary>
    Tester = 4,

    /// <summary>
    /// 产品经理
    /// </summary>
    ProductManager = 5,

    /// <summary>
    /// 管理员
    /// </summary>
    Administrator = 10,

    /// <summary>
    /// 超级管理员
    /// </summary>
    SuperAdmin = 99
}

/// <summary>
/// 用户状态枚举 - 对应DatabaseSchema.sql中的用户状态定义
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active = 1,

    /// <summary>
    /// 非活跃
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// 锁定
    /// </summary>
    Locked = 3,

    /// <summary>
    /// 待验证
    /// </summary>
    PendingVerification = 4,

    /// <summary>
    /// 已删除
    /// </summary>
    Deleted = 5
}

/// <summary>
/// 项目状态枚举 - 对应DatabaseSchema.sql中的项目状态定义
/// </summary>
public enum ProjectStatus
{
    /// <summary>
    /// 规划中
    /// </summary>
    Planning = 1,

    /// <summary>
    /// 进行中
    /// </summary>
    InProgress = 2,

    /// <summary>
    /// 测试中
    /// </summary>
    Testing = 3,

    /// <summary>
    /// 已部署
    /// </summary>
    Deployed = 4,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 5
}

/// <summary>
/// 项目类型枚举 - 项目分类定义
/// </summary>
public enum ProjectType
{
    /// <summary>
    /// Web应用
    /// </summary>
    WebApplication = 1,

    /// <summary>
    /// 移动应用
    /// </summary>
    MobileApplication = 2,

    /// <summary>
    /// 桌面应用
    /// </summary>
    DesktopApplication = 3,

    /// <summary>
    /// API服务
    /// </summary>
    APIService = 4,

    /// <summary>
    /// 微服务
    /// </summary>
    Microservice = 5,

    /// <summary>
    /// 数据分析
    /// </summary>
    DataAnalytics = 6,

    /// <summary>
    /// 机器学习
    /// </summary>
    MachineLearning = 7,

    /// <summary>
    /// 其他
    /// </summary>
    Other = 99
}

/// <summary>
/// 优先级枚举 - 通用优先级定义
/// </summary>
public enum Priority
{
    /// <summary>
    /// 低
    /// </summary>
    Low = 1,

    /// <summary>
    /// 中
    /// </summary>
    Medium = 2,

    /// <summary>
    /// 高
    /// </summary>
    High = 3,

    /// <summary>
    /// 紧急
    /// </summary>
    Critical = 4
}

/// <summary>
/// 消息类型枚举 - 需求对话消息类型
/// </summary>
public enum MessageType
{
    /// <summary>
    /// 需求收集
    /// </summary>
    Requirement = 1,

    /// <summary>
    /// 澄清问题
    /// </summary>
    Clarification = 2,

    /// <summary>
    /// 确认需求
    /// </summary>
    Confirmation = 3
}

/// <summary>
/// 文档状态枚举 - 需求文档状态
/// </summary>
public enum DocumentStatus
{
    /// <summary>
    /// 草稿
    /// </summary>
    Draft = 1,

    /// <summary>
    /// 审核中
    /// </summary>
    Review = 2,

    /// <summary>
    /// 已批准
    /// </summary>
    Approved = 3,

    /// <summary>
    /// 已拒绝
    /// </summary>
    Rejected = 4
}

/// <summary>
/// 生成方式枚举 - 文档生成方式
/// </summary>
public enum GeneratedBy
{
    /// <summary>
    /// AI生成
    /// </summary>
    AI = 1,

    /// <summary>
    /// 手动创建
    /// </summary>
    Manual = 2,

    /// <summary>
    /// 混合方式
    /// </summary>
    Hybrid = 3
}

/// <summary>
/// 代码类型枚举 - 代码生成任务类型
/// </summary>
public enum CodeType
{
    /// <summary>
    /// 前端
    /// </summary>
    Frontend = 1,

    /// <summary>
    /// 后端
    /// </summary>
    Backend = 2,

    /// <summary>
    /// 数据库
    /// </summary>
    Database = 3,

    /// <summary>
    /// 接口
    /// </summary>
    API = 4,

    /// <summary>
    /// 模型
    /// </summary>
    Model = 5
}

/// <summary>
/// 任务状态枚举 - 通用任务状态
/// </summary>
public enum TaskStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending = 1,

    /// <summary>
    /// 进行中
    /// </summary>
    InProgress = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 4
}

/// <summary>
/// 测试类型枚举 - 测试任务类型
/// </summary>
public enum TestType
{
    /// <summary>
    /// 单元测试
    /// </summary>
    Unit = 1,

    /// <summary>
    /// 集成测试
    /// </summary>
    Integration = 2,

    /// <summary>
    /// 端到端测试
    /// </summary>
    E2E = 3,

    /// <summary>
    /// 性能测试
    /// </summary>
    Performance = 4
}

/// <summary>
/// 测试状态枚举 - 测试执行状态
/// </summary>
public enum TestStatus
{
    /// <summary>
    /// 待执行
    /// </summary>
    Pending = 1,

    /// <summary>
    /// 执行中
    /// </summary>
    Running = 2,

    /// <summary>
    /// 通过
    /// </summary>
    Passed = 3,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 4
}

/// <summary>
/// 部署环境枚举 - 部署环境类型
/// </summary>
public enum DeploymentEnvironment
{
    /// <summary>
    /// 开发环境
    /// </summary>
    Development = 1,

    /// <summary>
    /// 测试环境
    /// </summary>
    Staging = 2,

    /// <summary>
    /// 生产环境
    /// </summary>
    Production = 3
}

/// <summary>
/// 部署类型枚举 - 部署平台类型
/// </summary>
public enum DeploymentType
{
    /// <summary>
    /// Docker容器
    /// </summary>
    Docker = 1,

    /// <summary>
    /// IIS服务器
    /// </summary>
    IIS = 2,

    /// <summary>
    /// Azure云平台
    /// </summary>
    Azure = 3,

    /// <summary>
    /// AWS云平台
    /// </summary>
    AWS = 4,

    /// <summary>
    /// Kubernetes容器编排
    /// </summary>
    Kubernetes = 5
}

/// <summary>
/// 问题类型枚举 - Issue类型
/// </summary>
public enum IssueType
{
    /// <summary>
    /// 缺陷
    /// </summary>
    Bug = 1,

    /// <summary>
    /// 功能请求
    /// </summary>
    Feature = 2,

    /// <summary>
    /// 改进
    /// </summary>
    Enhancement = 3,

    /// <summary>
    /// 任务
    /// </summary>
    Task = 4
}

/// <summary>
/// 问题状态枚举 - Issue状态
/// </summary>
public enum IssueStatus
{
    /// <summary>
    /// 开放
    /// </summary>
    Open = 1,

    /// <summary>
    /// 处理中
    /// </summary>
    InProgress = 2,

    /// <summary>
    /// 已解决
    /// </summary>
    Resolved = 3,

    /// <summary>
    /// 已关闭
    /// </summary>
    Closed = 4
}

/// <summary>
/// AI模型类型枚举 - AI模型用途类型
/// </summary>
public enum AIModelType
{
    /// <summary>
    /// 需求分析
    /// </summary>
    RequirementAnalysis = 1,

    /// <summary>
    /// 代码生成
    /// </summary>
    CodeGeneration = 2,

    /// <summary>
    /// 测试生成
    /// </summary>
    Testing = 3,

    /// <summary>
    /// 调试
    /// </summary>
    Debugging = 4,

    /// <summary>
    /// 文档生成
    /// </summary>
    DocumentGeneration = 5
}

/// <summary>
/// 工作流阶段枚举 - 项目工作流阶段
/// </summary>
public enum WorkflowStage
{
    /// <summary>
    /// 需求收集
    /// </summary>
    RequirementGathering = 1,

    /// <summary>
    /// 文档生成
    /// </summary>
    DocumentGeneration = 2,

    /// <summary>
    /// 图表生成
    /// </summary>
    DiagramGeneration = 3,

    /// <summary>
    /// 代码生成
    /// </summary>
    CodeGeneration = 4,

    /// <summary>
    /// 测试
    /// </summary>
    Testing = 5,

    /// <summary>
    /// 部署
    /// </summary>
    Deployment = 6,

    /// <summary>
    /// 问题处理
    /// </summary>
    IssueHandling = 7
}

/// <summary>
/// 阶段状态枚举 - 工作流阶段状态
/// </summary>
public enum StageStatus
{
    /// <summary>
    /// 待开始
    /// </summary>
    Pending = 1,

    /// <summary>
    /// 进行中
    /// </summary>
    InProgress = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 4
}

/// <summary>
/// 日志级别枚举 - 系统日志级别
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 调试
    /// </summary>
    Debug = 1,

    /// <summary>
    /// 信息
    /// </summary>
    Info = 2,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 3,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 4
}
