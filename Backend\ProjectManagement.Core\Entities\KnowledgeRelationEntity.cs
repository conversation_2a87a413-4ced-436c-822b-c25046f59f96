using SqlSugar;
using System.Text.Json;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 知识关系表
    /// </summary>
    [SugarTable("KnowledgeRelations")]
    public class KnowledgeRelationEntity : BaseEntity
    {
        /// <summary>
        /// 源实体ID
        /// </summary>
        [SugarColumn(ColumnDescription = "源实体ID", IsNullable = false)]
        public int FromEntityId { get; set; }

        /// <summary>
        /// 目标实体ID
        /// </summary>
        [SugarColumn(ColumnDescription = "目标实体ID", IsNullable = false)]
        public int ToEntityId { get; set; }

        /// <summary>
        /// 关系类型 (UsesTechnology, WorksOn, ResponsibleFor, HasSkill, etc.)
        /// </summary>
        [SugarColumn(ColumnDescription = "关系类型", Length = 50, IsNullable = false)]
        public string RelationType { get; set; } = string.Empty;

        /// <summary>
        /// 关系权重
        /// </summary>
        [SugarColumn(ColumnDescription = "关系权重", IsNullable = false)]
        public float Weight { get; set; } = 1.0f;

        /// <summary>
        /// 关系属性（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDescription = "关系属性", ColumnDataType = "TEXT", IsNullable = true)]
        public string? PropertiesJson { get; set; }

        /// <summary>
        /// 关系属性（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, object> Properties
        {
            get
            {
                if (string.IsNullOrEmpty(PropertiesJson))
                    return new Dictionary<string, object>();

                try
                {
                    return JsonSerializer.Deserialize<Dictionary<string, object>>(PropertiesJson) 
                           ?? new Dictionary<string, object>();
                }
                catch
                {
                    return new Dictionary<string, object>();
                }
            }
            set
            {
                PropertiesJson = JsonSerializer.Serialize(value);
            }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnDescription = "更新时间", IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 源实体导航属性
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public KnowledgeEntityEntity? FromEntity { get; set; }

        /// <summary>
        /// 目标实体导航属性
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public KnowledgeEntityEntity? ToEntity { get; set; }
    }
}
