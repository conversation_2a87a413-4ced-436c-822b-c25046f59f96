using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// Playwright脚本仓储实现
    /// </summary>
    public class PlaywrightScriptRepository : BaseRepository<PlaywrightScript>, IPlaywrightScriptRepository
    {
        public PlaywrightScriptRepository(ISqlSugarClient db, ILogger<BaseRepository<PlaywrightScript>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 根据分类获取脚本列表
        /// </summary>
        public async Task<(List<PlaywrightScript> items, int total)> GetByCategory(string category, int pageIndex = 1, int pageSize = 20)
        {
            var query = _db.Queryable<PlaywrightScript>()
                .Where(x => x.Category == category && !x.IsDeleted)
                .OrderByDescending(x => x.UpdatedTime);

            var total = await query.CountAsync();
            var items = await query.ToPageListAsync(pageIndex, pageSize);

            return (items, total);
        }

        /// <summary>
        /// 根据项目ID获取脚本列表
        /// </summary>
        public async Task<(List<PlaywrightScript> items, int total)> GetByProjectId(int projectId, int pageIndex = 1, int pageSize = 20)
        {
            var query = _db.Queryable<PlaywrightScript>()
                .Where(x => x.ProjectId == projectId && !x.IsDeleted)
                .OrderByDescending(x => x.UpdatedTime);

            var total = await query.CountAsync();
            var items = await query.ToPageListAsync(pageIndex, pageSize);

            return (items, total);
        }

        /// <summary>
        /// 根据浏览器类型获取脚本列表
        /// </summary>
        public async Task<(List<PlaywrightScript> items, int total)> GetByBrowser(string browser, int pageIndex = 1, int pageSize = 20)
        {
            var query = _db.Queryable<PlaywrightScript>()
                .Where(x => x.Browser == browser && !x.IsDeleted)
                .OrderByDescending(x => x.UpdatedTime);

            var total = await query.CountAsync();
            var items = await query.ToPageListAsync(pageIndex, pageSize);

            return (items, total);
        }

        /// <summary>
        /// 搜索脚本
        /// </summary>
        public async Task<(List<PlaywrightScript> items, int total)> Search(
            string? keyword = null,
            string? category = null,
            string? browser = null,
            int? projectId = null,
            int pageIndex = 1,
            int pageSize = 20)
        {
            var query = _db.Queryable<PlaywrightScript>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(x => x.Name.Contains(keyword) || 
                                        (x.Description != null && x.Description.Contains(keyword)));
            }

            // 分类筛选
            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(x => x.Category == category);
            }

            // 浏览器筛选
            if (!string.IsNullOrEmpty(browser))
            {
                query = query.Where(x => x.Browser == browser);
            }

            // 项目筛选
            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            query = query.OrderByDescending(x => x.UpdatedTime);

            var total = await query.CountAsync();
            var items = await query.ToPageListAsync(pageIndex, pageSize);

            return (items, total);
        }

        /// <summary>
        /// 更新执行统计
        /// </summary>
        public async Task UpdateExecutionStats(int scriptId, bool isSuccess, int? duration = null)
        {
            var script = await GetByIdAsync(scriptId);
            if (script == null) return;

            script.ExecutionCount++;
            script.LastExecutedTime = DateTime.Now;

            if (isSuccess)
            {
                script.SuccessCount++;
            }

            if (duration.HasValue)
            {
                // 计算平均执行时长
                if (script.AvgDuration.HasValue)
                {
                    script.AvgDuration = (script.AvgDuration.Value + duration.Value) / 2;
                }
                else
                {
                    script.AvgDuration = duration.Value;
                }
            }

            await UpdateAsync(script);
        }

        /// <summary>
        /// 获取脚本统计信息
        /// </summary>
        public async Task<object> GetStatistics(int? projectId = null)
        {
            var query = _db.Queryable<PlaywrightScript>()
                .Where(x => !x.IsDeleted);

            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            var totalCount = await query.CountAsync();
            var categoryStats = await query
                .GroupBy(x => x.Category)
                .Select(g => new { Category = g.Category, Count = SqlFunc.AggregateCount(g.Category) })
                .ToListAsync();

            var browserStats = await query
                .GroupBy(x => x.Browser)
                .Select(g => new { Browser = g.Browser, Count = SqlFunc.AggregateCount(g.Browser) })
                .ToListAsync();

            var statusStats = await query
                .GroupBy(x => x.Status)
                .Select(g => new { Status = g.Status, Count = SqlFunc.AggregateCount(g.Status) })
                .ToListAsync();

            var recentExecutions = await query
                .Where(x => x.LastExecutedTime.HasValue)
                .OrderByDescending(x => x.LastExecutedTime)
                .Take(10)
                .Select(x => new { 
                    x.Id, 
                    x.Name, 
                    x.LastExecutedTime, 
                    x.ExecutionCount, 
                    x.SuccessCount 
                })
                .ToListAsync();

            return new
            {
                TotalCount = totalCount,
                CategoryStats = categoryStats,
                BrowserStats = browserStats,
                StatusStats = statusStats,
                RecentExecutions = recentExecutions,
                SuccessRate = totalCount > 0 ? 
                    Math.Round((double)query.Sum(x => x.SuccessCount) / query.Sum(x => x.ExecutionCount) * 100, 2) : 0
            };
        }
    }
}
