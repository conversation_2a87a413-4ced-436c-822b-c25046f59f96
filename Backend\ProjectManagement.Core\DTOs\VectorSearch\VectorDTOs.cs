using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.Core.DTOs.VectorSearch
{
    /// <summary>
    /// 待索引文档
    /// </summary>
    public class DocumentToIndex
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // requirement, design, code, meeting
        public string ProjectId { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 文档向量
    /// </summary>
    public class DocumentVector
    {
        public int Id { get; set; }
        public string DocumentId { get; set; } = string.Empty;
        public int ChunkIndex { get; set; }
        public string Content { get; set; } = string.Empty;
        public float[] Embedding { get; set; } = Array.Empty<float>();
        public float SimilarityScore { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 搜索选项
    /// </summary>
    public class SearchOptions
    {
        public int TopK { get; set; } = 5;
        public float SimilarityThreshold { get; set; } = 0.7f;
        public Dictionary<string, object> Filters { get; set; } = new();
        public float RerankingWeight { get; set; } = 0.5f;
    }

    /// <summary>
    /// 搜索结果
    /// </summary>
    public class SearchResult
    {
        public string DocumentId { get; set; } = string.Empty;
        public int ChunkIndex { get; set; }
        public string Content { get; set; } = string.Empty;
        public float SimilarityScore { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<string> Highlights { get; set; } = new();
    }

    /// <summary>
    /// RAG选项
    /// </summary>
    public class RAGOptions
    {
        public int RetrievalTopK { get; set; } = 5;
        public float SimilarityThreshold { get; set; } = 0.7f;
        public int MaxContextLength { get; set; } = 4000;
        public string PromptTemplate { get; set; } = string.Empty;
        public Dictionary<string, object> Filters { get; set; } = new();
        public AIModelConfig? AIConfig { get; set; }
    }

    /// <summary>
    /// RAG响应
    /// </summary>
    public class RAGResponse
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string GeneratedText { get; set; } = string.Empty;
        public List<SourceDocument> SourceDocuments { get; set; } = new();
        public string Context { get; set; } = string.Empty;
    }

    /// <summary>
    /// 源文档
    /// </summary>
    public class SourceDocument
    {
        public string DocumentId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public float SimilarityScore { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
