<UserControl x:Class="ProjectManagementAI.WPF.Views.VectorOperationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.Resources>
        <!-- 向量化操作卡片样式 -->
        <Style x:Key="VectorOperationCard" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth4"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 操作按钮样式 -->
        <Style x:Key="VectorOperationButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Margin" Value="5,8"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="160"/>
            <Setter Property="MinHeight" Value="56"/>
            <Setter Property="Height" Value="Auto"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 结果显示样式 -->
        <Style x:Key="ResultTextBlock" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource VectorOperationCard}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="VectorPolyline" Width="32" Height="32" 
                                               VerticalAlignment="Center" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="向量化操作中心" FontSize="24" FontWeight="Bold" 
                                  VerticalAlignment="Center" Margin="10,0"/>
                    </StackPanel>
                    <TextBlock Text="批量处理、智能分析、AI优化 - 让项目管理更高效" 
                              FontSize="14" Foreground="{StaticResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 本地项目加载面板 -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource VectorOperationCard}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <materialDesign:PackIcon Kind="FolderOpen" Width="24" Height="24"
                                               VerticalAlignment="Center" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="本地项目加载" FontSize="18" FontWeight="Bold"
                                  VerticalAlignment="Center" Margin="10,0"/>
                    </StackPanel>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" Text="{Binding ProjectPath}"
                                materialDesign:HintAssist.Hint="选择项目根目录路径"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Margin="0,0,10,0"/>

                        <Button Grid.Column="1" Content="浏览"
                                Command="{Binding BrowseProjectPathCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0" Padding="20,10"/>

                        <Button Grid.Column="2" Content="扫描项目"
                                Command="{Binding ScanLocalProjectsCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{StaticResource PrimaryHueMidBrush}"
                                Padding="20,10"/>
                    </Grid>

                    <!-- 扫描结果显示 -->
                    <StackPanel Margin="0,15,0,0" Visibility="{Binding HasLocalProjects, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding LocalProjectsStatus}" FontWeight="Medium" Margin="0,0,0,10"/>

                        <ScrollViewer MaxHeight="150" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding LocalProjects}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{StaticResource MaterialDesignCardBackground}"
                                               Margin="0,2" Padding="10,5" CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Grid.Column="0" Kind="Application"
                                                                       Width="16" Height="16" VerticalAlignment="Center"
                                                                       Foreground="{StaticResource PrimaryHueMidBrush}" Margin="0,0,8,0"/>

                                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                    <TextBlock Text="{Binding Name}" FontWeight="Medium" FontSize="12"/>
                                                    <TextBlock Text="{Binding Type}" FontSize="10"
                                                              Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                                </StackPanel>

                                                <TextBlock Grid.Column="2" Text="{Binding Language}" FontSize="10"
                                                          VerticalAlignment="Center" Margin="10,0"
                                                          Foreground="{StaticResource MaterialDesignBodyLight}"/>

                                                <TextBlock Grid.Column="3" Text="{Binding Statistics.CodeLines, StringFormat='{}{0:N0} 行'}"
                                                          FontSize="10" VerticalAlignment="Center"
                                                          Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <Button Content="使用本地项目进行分析"
                                Command="{Binding UseLocalProjectsCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{StaticResource SecondaryHueMidBrush}"
                                Margin="0,10,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- 向量数据库面板 -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource VectorOperationCard}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <materialDesign:PackIcon Kind="Database" Width="24" Height="24"
                                               VerticalAlignment="Center" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="向量数据库" FontSize="18" FontWeight="Bold"
                                  VerticalAlignment="Center" Margin="10,0"/>
                    </StackPanel>

                    <!-- 数据库状态 -->
                    <Border Background="{StaticResource MaterialDesignCardBackground}"
                           Padding="15,10" CornerRadius="8" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="数据库状态:" FontWeight="Medium" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding VectorDatabaseStatus}"
                                      Foreground="{StaticResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>

                    <!-- 操作按钮 -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" Content="初始化数据库"
                                Command="{Binding InitializeVectorDatabaseCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,5,0" Padding="10,8"/>

                        <Button Grid.Column="1" Content="构建向量库"
                                Command="{Binding BuildVectorDatabaseCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{StaticResource PrimaryHueMidBrush}"
                                Margin="5,0,5,0" Padding="10,8"/>

                        <Button Grid.Column="2" Content="向量搜索"
                                Command="{Binding SearchVectorDatabaseCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{StaticResource SecondaryHueMidBrush}"
                                Margin="5,0,5,0" Padding="10,8"/>

                        <Button Grid.Column="3" Content="聚类分析"
                                Command="{Binding ClusterAnalysisCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{StaticResource SecondaryHueDarkBrush}"
                                Margin="5,0,0,0" Padding="10,8"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- 操作面板 -->
            <materialDesign:Card Grid.Row="3" Style="{StaticResource VectorOperationCard}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 批量操作 -->
                    <StackPanel Grid.Column="0" Margin="10">
                        <TextBlock Text="批量操作" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding BatchCreateProjectsCommand}"
                                Background="{StaticResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="批量创建项目" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding BatchUpdateProjectsCommand}"
                                Background="{StaticResource SecondaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Update" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="批量更新项目" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding BatchGenerateStepsCommand}"
                                Background="{StaticResource PrimaryHueDarkBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="StepForward" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="批量生成步骤" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- AI分析 -->
                    <StackPanel Grid.Column="1" Margin="10">
                        <TextBlock Text="AI智能分析" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding VectorAnalyzeCommand}"
                                Background="{StaticResource SecondaryHueDarkBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="向量化分析" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding FindSimilarProjectsCommand}"
                                Background="{StaticResource PrimaryHueLightBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Compare" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="相似性分析" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding PredictRisksCommand}"
                                Background="{StaticResource SecondaryHueLightBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="风险预测" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- 优化建议 -->
                    <StackPanel Grid.Column="2" Margin="10">
                        <TextBlock Text="智能优化" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding OptimizeProjectsCommand}"
                                Background="{StaticResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="AutoFix" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="项目优化" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding OptimizeResourcesCommand}"
                                Background="{StaticResource SecondaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="资源优化" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource VectorOperationButton}"
                                Command="{Binding BatchExecuteAutomationCommand}"
                                Background="{StaticResource PrimaryHueDarkBrush}">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="Robot" Width="20" Height="20"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="批量自动化" VerticalAlignment="Center"
                                          TextWrapping="NoWrap" FontWeight="Medium"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- 结果显示区域 -->
            <materialDesign:Card Grid.Row="4" Style="{StaticResource VectorOperationCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 结果标题 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <materialDesign:PackIcon Kind="ChartBox" Width="24" Height="24" 
                                               VerticalAlignment="Center" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="操作结果" FontSize="18" FontWeight="Bold" 
                                  VerticalAlignment="Center" Margin="10,0"/>
                        <ProgressBar IsIndeterminate="{Binding IsLoading}" 
                                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    Width="100" Height="4" Margin="20,0"/>
                    </StackPanel>

                    <!-- 结果内容 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- 状态信息 -->
                            <TextBlock Text="{Binding StatusMessage}" 
                                      Style="{StaticResource ResultTextBlock}"
                                      FontWeight="Bold" FontSize="14"/>

                            <!-- 操作结果 -->
                            <ItemsControl ItemsSource="{Binding OperationResults}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <materialDesign:Card Margin="5" Padding="10">
                                            <StackPanel>
                                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding Description}" Style="{StaticResource ResultTextBlock}"/>
                                                <TextBlock Text="{Binding Timestamp}" Style="{StaticResource ResultTextBlock}"
                                                          Foreground="{StaticResource MaterialDesignBodyLight}"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>

                            <!-- 详细结果展示 -->
                            <Expander Header="详细结果" Margin="0,10,0,0" 
                                     IsExpanded="{Binding ShowDetailedResults}">
                                <TextBox Text="{Binding DetailedResults}" 
                                        IsReadOnly="True" 
                                        TextWrapping="Wrap" 
                                        MinHeight="200"
                                        VerticalScrollBarVisibility="Auto"
                                        FontFamily="Consolas"/>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </ScrollViewer>
</UserControl>
