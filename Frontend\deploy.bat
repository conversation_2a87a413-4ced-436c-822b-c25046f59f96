@echo off
echo 开始构建前端项目...
cd /d "D:\Projects\ProjectManagement\Frontend"

echo 正在构建...
call npm run build

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建成功！
echo 构建文件位于: D:\Projects\ProjectManagement\Frontend\dist\

echo.
echo 📋 IIS部署说明:
echo 1. 将 dist 目录中的所有文件复制到 D:\AI\Frontend\ 目录
echo 2. 确保 web.config 文件也被复制（用于处理Vue Router的History模式）
echo 3. 在IIS中确保已安装URL重写模块
echo 4. 前端访问地址: http://localhost:62573/Frontend/
echo.

pause
