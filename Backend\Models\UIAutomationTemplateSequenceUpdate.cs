using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProjectManagementAI.Models
{
    /// <summary>
    /// UI自动化模板序列实体 - 更新版本
    /// 添加了 SourceCode 和 CodeLanguage 字段用于保存原生代码
    /// </summary>
    [Table("UIAutomationTemplateSequences")]
    public class UIAutomationTemplateSequence : BaseEntity
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 序列描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        [StringLength(50)]
        public string? Category { get; set; }

        /// <summary>
        /// 标签（JSON格式）
        /// </summary>
        public string? Tags { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 原生代码 - 新增字段
        /// 用于保存用户在代码编辑器中编写的原始代码
        /// </summary>
        [Column(TypeName = "NVARCHAR(MAX)")]
        public string? SourceCode { get; set; }

        /// <summary>
        /// 代码语言 - 新增字段
        /// 支持的语言：javascript, python, typescript 等
        /// </summary>
        [StringLength(50)]
        [Column(TypeName = "NVARCHAR(50)")]
        public string? CodeLanguage { get; set; } = "javascript";

        /// <summary>
        /// 扩展属性（JSON格式）
        /// 用于存储其他元数据，如编辑器设置等
        /// </summary>
        public string? ExtendedProperties { get; set; }

        /// <summary>
        /// 序列步骤集合
        /// </summary>
        public virtual ICollection<UIAutomationTemplateStep> Steps { get; set; } = new List<UIAutomationTemplateStep>();
    }

    /// <summary>
    /// UI自动化模板步骤实体
    /// </summary>
    [Table("UIAutomationTemplateSteps")]
    public class UIAutomationTemplateStep : BaseEntity
    {
        /// <summary>
        /// 所属序列ID
        /// </summary>
        [Required]
        public int SequenceId { get; set; }

        /// <summary>
        /// 步骤顺序
        /// </summary>
        public int StepOrder { get; set; }

        /// <summary>
        /// 动作类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 逻辑类型
        /// </summary>
        [StringLength(50)]
        public string? LogicType { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 参数（JSON格式）
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 5;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转到步骤ID
        /// </summary>
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数
        /// </summary>
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量
        /// </summary>
        [StringLength(50)]
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组ID
        /// </summary>
        [StringLength(50)]
        public string? GroupId { get; set; }

        /// <summary>
        /// 所属序列
        /// </summary>
        [ForeignKey("SequenceId")]
        public virtual UIAutomationTemplateSequence? Sequence { get; set; }
    }

    /// <summary>
    /// 基础实体类
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(50)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(50)]
        public string? UpdatedBy { get; set; }
    }
}
