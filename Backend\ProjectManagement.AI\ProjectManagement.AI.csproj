<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Configuration\**" />
    <Compile Remove="Extensions\**" />
    <Compile Remove="Providers\Alibaba\**" />
    <Compile Remove="Providers\AzureOpenAI\**" />
    <Compile Remove="Providers\Baidu\**" />
    <Compile Remove="Providers\Claude\**" />
    <Compile Remove="Providers\DeepSeek\**" />
    <Compile Remove="Providers\Local\**" />
    <Compile Remove="Providers\OpenAI\**" />
    <EmbeddedResource Remove="Configuration\**" />
    <EmbeddedResource Remove="Extensions\**" />
    <EmbeddedResource Remove="Providers\Alibaba\**" />
    <EmbeddedResource Remove="Providers\AzureOpenAI\**" />
    <EmbeddedResource Remove="Providers\Baidu\**" />
    <EmbeddedResource Remove="Providers\Claude\**" />
    <EmbeddedResource Remove="Providers\DeepSeek\**" />
    <EmbeddedResource Remove="Providers\Local\**" />
    <EmbeddedResource Remove="Providers\OpenAI\**" />
    <None Remove="Configuration\**" />
    <None Remove="Extensions\**" />
    <None Remove="Providers\Alibaba\**" />
    <None Remove="Providers\AzureOpenAI\**" />
    <None Remove="Providers\Baidu\**" />
    <None Remove="Providers\Claude\**" />
    <None Remove="Providers\DeepSeek\**" />
    <None Remove="Providers\Local\**" />
    <None Remove="Providers\OpenAI\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="1.0.0-beta.12" />
    <PackageReference Include="OpenAI" Version="1.10.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectManagement.Core\ProjectManagement.Core.csproj" />
    <ProjectReference Include="..\ProjectManagement.Data\ProjectManagement.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Interfaces\" />
    <Folder Include="Providers\" />
    <Folder Include="Services\" />
    <Folder Include="Models\" />
  </ItemGroup>

</Project>
