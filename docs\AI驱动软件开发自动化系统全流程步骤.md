# AI驱动软件开发自动化系统 - 用户操作手册

## 📖 系统简介

本系统是一个AI驱动的软件开发自动化平台，帮助您从项目需求到代码生成的全流程自动化开发。系统支持多种AI模型，可以自动生成需求文档、数据库设计、代码文件，并提供编译监控和错误修复功能。

## 🚀 快速开始

### 系统访问
- **Web管理界面**: https://localhost:61136 (后端API)
- **前端界面**: 通过IIS部署的前端应用
- **编译监控器**: 桌面WPF应用 (D:\Projects\ProjectManagement\Desktop\BuildMonitor)
- **UI自动化工具**: Python应用 (D:\Projects\ProjectManagement\Python\NewVersion)

---

## 第一步：项目创建与配置
**目的**: 建立项目基础框架，配置AI模型和开发环境，为后续的自动化开发流程做好准备。

### 1.1 创建新项目
1. 打开Web管理界面
2. 导航到 **项目管理** → **项目列表**
3. 点击 **新建项目** 按钮
4. 填写项目基本信息：
   - 项目名称
   - 项目描述
   - 技术栈选择
   - 预计工期
5. 点击 **保存** 完成项目创建

### 1.2 配置AI模型
1. 进入 **系统设置** → **AI配置**
2. 选择您偏好的AI提供商：
   - Azure OpenAI GPT-4
   - DeepSeek
   - 其他支持的模型
3. 配置API密钥和端点
4. 测试连接确保配置正确
5. 保存配置

---

## 第二步：AI需求分析
**目的**: 通过AI智能分析用户需求，自动生成结构化的需求文档，确保项目需求清晰完整且符合开发标准。

### 2.1 开始需求收集
1. 选择您的项目
2. 进入 **需求管理** → **需求分析**
3. 点击 **开始AI需求分析**
4. 在对话框中详细描述您的项目需求：
   ```
   示例：我需要开发一个在线图书管理系统，包括：
   - 用户注册登录功能
   - 图书信息管理（增删改查）
   - 图书借阅和归还
   - 用户借阅历史查询
   - 管理员后台管理
   ```

### 2.2 AI需求分析过程
1. AI会自动分析您的需求描述
2. 生成结构化的需求文档，包括：
   - 功能需求列表
   - 非功能需求
   - 用户故事
   - 验收标准
   - 技术栈建议
3. 您可以与AI继续对话完善需求
4. 确认需求后点击 **采纳并保存**

### 2.3 查看需求文档
1. 在 **需求管理** → **需求文档** 中查看生成的文档
2. 可以导出为PDF或Word格式
3. 支持版本管理和修改历史

---

## 第三步：系统设计
**目的**: 基于需求文档自动生成数据库ER图和系统架构图，为代码开发提供清晰的技术设计蓝图。

### 3.1 生成ER图
1. 在需求文档页面点击 **生成ER图**
2. 选择AI模型（建议使用GPT-4）
3. AI会基于需求自动生成数据库ER图
4. 系统会自动修复语法错误
5. 在 **设计管理** → **ER图** 中查看和编辑

### 3.2 生成系统架构图
1. 点击 **生成Context图**
2. AI会分析系统边界和组件关系
3. 生成系统上下文图
4. 在 **设计管理** → **架构图** 中查看

### 3.3 审查和调整设计
1. 仔细检查生成的ER图和架构图
2. 如有需要，可以手动编辑Mermaid代码
3. 保存最终版本的设计文档

---

## 第四步：开发步骤分解
**目的**: 将复杂的开发任务智能分解为具体的开发步骤，创建可执行的编码任务，为自动化代码生成做好准备。

### 4.1 AI自动分解开发步骤
1. 进入 **开发管理** → **开发步骤**
2. 选择项目后点击 **AI分解步骤**
3. AI会基于需求和设计生成详细的开发步骤
4. 步骤包括：
   - 前端组件开发
   - 后端API开发
   - 数据库脚本
   - 配置文件等

### 4.2 创建编码任务
1. 进入 **开发管理** → **编码任务**
2. 点击 **新建任务**
3. 选择要包含的开发步骤
4. 设置任务优先级和预估工时
5. 分配给开发人员（可选）

### 4.3 管理任务执行
1. 在编码任务列表中查看所有任务
2. 点击任务可查看详细的步骤列表
3. 更新任务和步骤的执行状态
4. 跟踪整体项目进度

---

## 第五步：Python自动化代码生成
**目的**: 通过Python UI自动化工具操作VSCode Copilot，实现批量自动化代码生成，大幅提升开发效率。

### 5.1 启动Python UI自动化工具
1. 运行Python自动化应用：
   ```bash
   cd D:\Projects\ProjectManagement\Python\NewVersion
   python development_steps_ui.py
   ```
2. 在界面中选择您的项目
3. 选择相应的编码任务
4. 加载开发步骤列表

### 5.2 配置VSCode Copilot环境
1. 确保VSCode已启动并开启调试模式：
   - 在VSCode中按 `Ctrl+Shift+P`
   - 输入 "Developer: Reload With Extensions Disabled"
   - 或者启动时添加 `--remote-debugging-port=9222` 参数
2. 确保VSCode Copilot插件已安装并登录
3. 打开您的项目文件夹

### 5.3 选择代码生成步骤
1. 在Python UI界面的步骤列表中，选择需要生成代码的步骤
2. 查看步骤详情，确认包含：
   - **AI提示词**: 描述要生成的代码功能
   - **文件路径**: 代码文件的保存位置
   - **技术栈**: 如Vue、C#、SQL等
   - **参考图片**: 界面设计参考（如有）

### 5.4 执行自动化代码生成
1. 选中要执行的开发步骤
2. 点击 **执行选择步骤** 按钮
3. 系统会弹出确认对话框，确认后开始执行
4. Python程序会自动：
   - 连接到VSCode调试端口
   - 打开Copilot Chat面板
   - 发送AI提示词到Copilot
   - 等待Copilot生成代码
   - 监控生成过程

### 5.5 监控代码生成过程
1. 在Python界面中查看执行日志
2. 观察VSCode中Copilot的回复
3. 系统会自动检测Copilot是否正在工作
4. 等待代码生成完成

### 5.6 处理生成的代码
1. **复制代码**: 从Copilot回复中复制生成的代码
2. **创建文件**: 根据步骤中的文件路径创建相应文件
3. **保存代码**: 将代码保存到指定位置
4. **代码审查**: 检查生成的代码质量和正确性

### 5.7 批量代码生成
1. 可以选择多个开发步骤进行批量执行
2. 系统会按顺序逐个执行每个步骤
3. 每个步骤完成后会更新执行状态
4. 可以在执行过程中查看进度和日志

### 5.8 处理特殊情况
1. **图片参考**: 如果步骤包含参考图片，系统会：
   - 自动下载参考图片到本地
   - 将图片粘贴到Copilot对话中
   - 结合图片和文字描述生成代码

2. **复杂提示词**: 对于复杂的AI提示词，系统会：
   - 自动添加项目背景信息
   - 包含相关的技术栈要求
   - 提供详细的功能描述

3. **错误处理**: 如果生成过程出错：
   - 查看Python控制台的错误信息
   - 检查VSCode Copilot连接状态
   - 重试执行或手动调整提示词

---

## 第六步：编译监控与错误处理
**目的**: 实时监控代码编译状态，自动检测编译错误并通过AI提供修复建议，确保代码质量和项目稳定性。

### 6.1 启动编译监控器
1. 运行桌面应用：`D:\Projects\ProjectManagement\Desktop\BuildMonitor\BuildMonitor.exe`
2. 在项目下拉框中选择您的项目
3. 配置项目路径：
   - 后端项目路径：如 `D:\Projects\YourProject\Backend\Backend.csproj`
   - 前端项目路径：如 `D:\Projects\YourProject\Frontend`
4. 点击 **保存配置**

### 6.2 手动编译测试
1. 点击 **编译后端** 按钮测试C#项目编译
2. 点击 **编译前端** 按钮测试前端项目编译
3. 查看编译结果和错误信息
4. 在错误列表中查看详细的错误描述

### 6.3 开启自动编译
1. 勾选 **启用自动编译**
2. 设置编译间隔（建议5-10分钟）
3. 系统会定期自动编译并检测错误
4. 编译完成后会自动刷新错误列表

### 6.4 AI错误修复
1. 当发现编译错误时，点击 **发送给AI修复** 按钮
2. 系统会将错误信息发送给AI分析
3. AI会提供详细的修复建议
4. 根据建议修改代码并重新编译
5. 可以在 **后端编译错误** 和 **前端编译错误** 选项卡中查看修复历史

---

## 第七步：UI自动化与VSCode操作
**目的**: 通过Python UI自动化工具实现VSCode的自动化操作，包括DOM元素操作、图像识别和循环监控，提升开发工具的自动化程度。

### 7.1 Python UI自动化工具功能
Python UI自动化工具 (`D:\Projects\ProjectManagement\Python\NewVersion`) 主要用于：
- **VSCode Copilot自动化**: 自动发送代码生成请求
- **DOM元素操作**: 自动点击、输入、检测页面元素
- **图像识别**: 基于模板图片进行界面操作
- **循环监控**: 持续监控特定元素状态

### 7.2 配置VSCode自动化环境
1. 确保VSCode已启动并开启调试模式：
   ```bash
   # 方法1：命令行启动VSCode
   code --remote-debugging-port=9222

   # 方法2：在VSCode中按Ctrl+Shift+P，输入"Developer: Reload With Extensions Disabled"
   ```
2. 确保Copilot插件已安装并登录
3. 打开目标项目文件夹

### 7.3 执行UI自动化脚本
1. 在Python UI界面中选择包含UI操作的开发步骤
2. 查看步骤的自动化配置：
   - **JavaScript脚本**: DOM操作逻辑
   - **图片模板**: 界面元素识别
   - **操作参数**: 点击坐标、等待时间等
3. 点击 **执行选择步骤** 开始自动化
4. 系统会自动执行：
   - 图像识别和点击
   - DOM元素操作
   - 文本输入和按键操作
   - 等待和检测操作

### 7.4 循环检测与监控
1. 点击 **开启循环检测** 启动持续监控
2. 配置检测参数：
   - 检测间隔时间
   - 目标元素选择器
   - 触发条件设置
3. 系统会持续检测指定元素的出现
4. 当检测到目标元素时自动执行预设操作
5. 可以随时点击 **停止循环检测** 停止监控

### 7.5 VSCode Copilot集成操作
1. **自动发送消息**: 将AI提示词自动发送到Copilot Chat
2. **图片粘贴**: 自动将参考图片粘贴到对话中
3. **状态检测**: 检测Copilot是否正在工作
4. **回复监控**: 等待并获取Copilot的回复内容
5. **会话管理**: 清理对话历史，开始新会话

---

## 第八步：测试与部署
**目的**: 执行自动化测试验证系统功能，完成项目部署上线，确保系统在生产环境中正常运行。

### 8.1 Selenium自动化测试
1. 进入 **测试管理** → **Selenium测试**
2. 创建新的测试脚本或使用AI生成
3. 配置测试环境：
   - 浏览器类型
   - 测试URL
   - 窗口大小等
4. 执行测试并查看结果报告

### 8.2 部署准备
1. 确保所有编译错误已修复
2. 运行完整的测试套件
3. 准备生产环境配置文件
4. 备份当前数据库

### 8.3 IIS部署
1. 将前端文件复制到IIS网站目录
2. 将后端文件发布到IIS应用程序目录
3. 配置数据库连接字符串
4. 测试部署后的应用功能

---

## 第九步：系统监控与维护
**目的**: 持续监控系统运行状态，跟踪问题和性能指标，通过AI辅助进行系统优化和维护，保障系统长期稳定运行。

### 9.1 查看系统日志
1. 进入 **系统管理** → **系统日志**
2. 查看应用运行日志和错误记录
3. 分析系统性能和用户行为
4. 设置日志告警规则

### 9.2 问题跟踪管理
1. 在 **问题管理** → **问题列表** 中查看系统问题
2. 系统会自动检测和报告问题
3. 对于复杂问题，可以请求AI分析
4. 跟踪问题解决进度和效果

### 9.3 性能优化
1. 定期检查系统性能指标
2. 优化数据库查询和索引
3. 调整AI模型配置以提高响应速度
4. 根据使用情况扩展系统资源

---

## 💡 使用技巧与最佳实践

### AI模型选择建议
- **需求分析**: 推荐使用GPT-4，理解能力更强
- **代码生成**: 可以使用DeepSeek，代码质量较好
- **错误修复**: GPT-4对错误分析更准确
- **文档生成**: 根据内容复杂度选择合适模型

### 提高AI代码生成质量的技巧
1. **详细描述需求**: 在AI提示词中提供越详细的功能描述，Copilot生成的代码越准确
2. **分步骤生成**: 不要一次性生成整个模块，按功能点逐步生成代码
3. **提供上下文**: 在提示词中包含项目背景、技术栈、编码规范等信息
4. **使用参考图片**: 对于UI组件，提供界面设计图片可以大大提高生成质量
5. **优化提示词**: 根据生成结果不断优化AI提示词模板

### 编译监控最佳实践
1. **设置合理的编译间隔**: 建议5-10分钟，避免过于频繁
2. **及时处理编译错误**: 发现错误后立即使用AI修复功能
3. **定期清理错误记录**: 避免数据库中积累过多历史错误
4. **备份重要配置**: 定期备份项目配置和AI设置

### UI自动化注意事项
1. **确保图片模板清晰**: 模糊的图片会影响识别准确率
2. **适当设置等待时间**: 给页面加载留出足够时间
3. **处理异常情况**: 准备好异常处理和重试机制
4. **定期更新脚本**: 当目标应用界面变化时及时更新脚本

---

## 🔧 常见问题解决

### Q1: VSCode Copilot生成的代码质量不理想怎么办？
**解决方案**:
1. 检查AI提示词是否足够详细和准确
2. 确保提供了足够的项目上下文信息
3. 尝试分解复杂功能为更小的代码片段
4. 使用参考图片辅助界面代码生成
5. 手动调整和优化生成的代码

### Q2: 编译监控器无法检测到错误？
**解决方案**:
1. 检查项目路径配置是否正确
2. 确认项目可以正常编译
3. 查看编译输出日志
4. 重启编译监控器

### Q3: Python UI自动化脚本执行失败？
**解决方案**:
1. 检查VSCode是否已启动调试模式 (`--remote-debugging-port=9222`)
2. 确认图片模板文件是否存在且清晰
3. 检查目标应用界面是否发生变化
4. 调整脚本中的等待时间和重试次数
5. 查看Python控制台的详细错误日志
6. 确认Copilot插件已正确安装和登录

### Q4: 系统响应速度慢？
**解决方案**:
1. 检查网络连接和AI API响应时间
2. 优化数据库查询
3. 清理系统日志和临时文件
4. 考虑升级硬件配置

---

## 📞 技术支持

### 系统文件位置
- **后端项目**: `D:\Projects\ProjectManagement\Backend`
- **前端项目**: `D:\Projects\ProjectManagement\Frontend`
- **数据库脚本**: `D:\Projects\ProjectManagement\Database`
- **编译监控器**: `D:\Projects\ProjectManagement\Desktop\BuildMonitor`
- **UI自动化工具**: `D:\Projects\ProjectManagement\Python\NewVersion`

### 配置文件位置
- **后端配置**: `Backend\appsettings.json`
- **前端配置**: `Frontend\src\config`
- **数据库连接**: 在后端配置文件中
- **AI模型配置**: 通过Web界面管理

### 日志文件位置
- **应用日志**: 系统自动记录到数据库
- **编译日志**: 编译监控器界面查看
- **UI自动化日志**: Python控制台输出

---

## 🎯 总结

通过以上九个步骤，您可以完整地使用AI驱动软件开发自动化系统来开发项目：

1. **项目创建与配置** - 建立项目基础和AI模型配置
2. **AI需求分析** - 智能需求收集和结构化分析
3. **系统设计** - 自动生成ER图和系统架构图
4. **开发步骤分解** - AI智能分解开发任务和工作流
5. **Python自动化代码生成** - 通过VSCode Copilot自动生成代码
6. **编译监控与错误处理** - 实时编译监控和AI错误修复
7. **UI自动化与VSCode操作** - Python自动化工具操作VSCode
8. **测试与部署** - 完整的测试和部署流程
9. **系统监控与维护** - 持续监控和系统优化

这个系统的核心特色是通过Python程序自动操作VSCode Copilot来生成代码，大大提升了开发效率。系统将AI能力与自动化工具完美结合，让您可以专注于业务逻辑设计，而将重复性的编码工作交给AI和自动化工具完成。

**关键提醒**: 代码生成主要通过Python UI自动化工具操作VSCode Copilot实现，而不是直接的AI API调用。这种方式可以充分利用Copilot的代码生成能力，同时保持与开发环境的紧密集成。
