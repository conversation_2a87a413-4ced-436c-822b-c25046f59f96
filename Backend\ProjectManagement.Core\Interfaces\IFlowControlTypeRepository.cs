using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 流程控制类型仓储接口
    /// </summary>
    public interface IFlowControlTypeRepository : IRepository<FlowControlType>
    {
        /// <summary>
        /// 分页查询流程控制类型
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResultDto<FlowControlType>> GetPagedAsync(FlowControlTypeQueryDto query);

        /// <summary>
        /// 根据值获取流程控制类型
        /// </summary>
        /// <param name="value">流程控制类型值</param>
        /// <returns>流程控制类型</returns>
        Task<FlowControlType?> GetByValueAsync(string value);

        /// <summary>
        /// 根据执行类型获取流程控制类型
        /// </summary>
        /// <param name="executionType">执行类型</param>
        /// <returns>流程控制类型列表</returns>
        Task<List<FlowControlType>> GetByExecutionTypeAsync(string executionType);

        /// <summary>
        /// 获取启用的流程控制类型
        /// </summary>
        /// <returns>流程控制类型列表</returns>
        Task<List<FlowControlType>> GetActiveAsync();

        /// <summary>
        /// 获取启用的流程控制类型（按执行类型分组）
        /// </summary>
        /// <returns>按执行类型分组的流程控制类型</returns>
        Task<Dictionary<string, List<FlowControlType>>> GetActiveByExecutionTypeAsync();

        /// <summary>
        /// 检查流程控制类型值是否已存在
        /// </summary>
        /// <param name="value">流程控制类型值</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsValueAsync(string value, int? excludeId = null);

        /// <summary>
        /// 获取最大排序顺序
        /// </summary>
        /// <param name="executionType">执行类型</param>
        /// <returns>最大排序顺序</returns>
        Task<int> GetMaxSortOrderAsync(string? executionType = null);

        /// <summary>
        /// 批量更新排序顺序
        /// </summary>
        /// <param name="sortOrders">排序信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders);

        /// <summary>
        /// 启用/禁用流程控制类型
        /// </summary>
        /// <param name="id">流程控制类型ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>是否成功</returns>
        Task<bool> SetActiveStatusAsync(int id, bool isActive);

        /// <summary>
        /// 获取内置流程控制类型
        /// </summary>
        /// <returns>内置流程控制类型列表</returns>
        Task<List<FlowControlType>> GetBuiltInAsync();

        /// <summary>
        /// 获取自定义流程控制类型
        /// </summary>
        /// <returns>自定义流程控制类型列表</returns>
        Task<List<FlowControlType>> GetCustomAsync();

        /// <summary>
        /// 获取需要目标步骤的流程控制类型
        /// </summary>
        /// <returns>需要目标步骤的流程控制类型列表</returns>
        Task<List<FlowControlType>> GetRequiresTargetAsync();

        /// <summary>
        /// 获取可以嵌套的流程控制类型
        /// </summary>
        /// <returns>可以嵌套的流程控制类型列表</returns>
        Task<List<FlowControlType>> GetCanNestAsync();
    }
}
