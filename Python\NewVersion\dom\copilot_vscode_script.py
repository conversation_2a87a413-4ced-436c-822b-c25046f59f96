#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
我的VSCode自动化脚本
简洁的基础模板，DOM操作由用户自定义
"""

import sys
import os
import time
import subprocess

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vscode_dom_automation import VSCodeDOMAutomation


class CopilotVsCodeScript:
    """我的VSCode自动化脚本类"""
    
    def __init__(self, parent_ui=None):
        self.vscode = VSCodeDOMAutomation()
        self.is_connected = False

        # 存储父UI引用
        self.parent_ui = parent_ui
        if parent_ui:
            self.log("✅ 父UI引用已设置")
        else:
            self.log("⚠️ 未提供父UI引用，某些功能可能不可用")

        # 加载元素检测配置
        self.config = self._load_config()
        self.log("✅ 已加载元素检测配置")

        # 跟踪已加载的JS文件
        self._loaded_js_files = set()

        # 回调函数
        self.error_callback = None
        self.step_execution_callback = None
        self.api_update_callback = None
        self.task_info_callback = None

        # 错误发送延迟设置（从配置文件读取）
        self.error_send_delay = self._get_error_send_delay_from_config()
        self.copilot_stop_time = None  # Copilot停止工作的时间

        # 将配置传递给JavaScript
        self._sync_config_to_js()

    def set_parent_ui(self, parent_ui):
        """设置父UI引用"""
        self.parent_ui = parent_ui
        if parent_ui:
            self.log("✅ 父UI引用已更新")
        else:
            self.log("⚠️ 父UI引用已清空")

    def _get_error_send_delay_from_config(self):
        """从配置文件读取错误发送延迟设置"""
        try:
            config = self.config
            if config and 'compilation_errors' in config:
                delay = config['compilation_errors'].get('error_send_delay', 10)
                self.log(f"📋 从配置文件读取错误发送延迟: {delay}秒")
                return delay
            else:
                self.log("⚠️ 配置文件中未找到compilation_errors配置，使用默认值10秒")
                return 10
        except Exception as e:
            self.log(f"❌ 读取错误发送延迟配置失败: {e}，使用默认值10秒")
            return 10

    def update_error_send_delay(self, delay):
        """更新错误发送延迟设置"""
        try:
            self.error_send_delay = float(delay)
            self.log(f"✅ 错误发送延迟已更新为: {self.error_send_delay}秒")
        except (ValueError, TypeError) as e:
            self.log(f"❌ 更新错误发送延迟失败: {e}")

    def get_error_send_delay(self):
        """获取当前错误发送延迟设置"""
        return self.error_send_delay

    def _load_config(self):
        """加载配置文件"""
        import json
        from pathlib import Path

        try:
            config_path = Path(__file__).parent.parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.log(f"⚠️ 配置文件不存在: {config_path}")
                return self._get_default_config()
        except Exception as e:
            self.log(f"❌ 加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self):
        """获取默认配置"""
        return {
            "element_detection": {
                "max_wait_time": 30,
                "check_interval": 1,
                "common_selectors": {},
                "auto_click_elements": [],
                "detection_classes": {}
            }
        }

    def _sync_config_to_js(self):
        """将配置同步到JavaScript端"""
        try:
            # 如果还没有连接，跳过同步
            if not self.is_connected:
                return

            # 获取检测配置
            detection_config = self.config.get("element_detection", {})

            # 确保JS文件已加载
            if "js_scripts/copilot_operations.js" not in self._loaded_js_files:
                js_code = self.load_js_file("js_scripts/copilot_operations.js")
                if js_code:
                    self.execute_js(js_code)
                    self._loaded_js_files.add("js_scripts/copilot_operations.js")

            # 设置配置
            import json
            config_json = json.dumps(detection_config)

            set_config_js = f"ElementDetectionConfig.setConfig({config_json});"
            result = self.execute_js(set_config_js)

            if result is not None:
                self.log("✅ 配置已同步到JavaScript")
            else:
                self.log("⚠️ 配置同步到JavaScript失败")

        except Exception as e:
            self.log(f"❌ 配置同步异常: {e}")

    def get_enabled_auto_click_elements(self):
        """获取启用的自动点击元素"""
        element_detection = self.config.get("element_detection", {})
        auto_click_elements = element_detection.get("auto_click_elements", [])
        return [elem for elem in auto_click_elements if elem.get("enabled", False)]

    def connect(self) -> bool:
        """连接到VSCode"""
        print("🔗 连接到VSCode...")

        # 先尝试连接到正确的VSCode标签页
        if self.connect_to_vscode_tab():
            self.is_connected = True
            print("✅ 连接成功！")

            # 连接成功后同步配置到JavaScript
            self._sync_config_to_js()

            return True
        else:
            print("❌ 连接失败！请确保VSCode已启动调试模式")
            return False

    def connect_to_vscode_tab(self) -> bool:
        """连接到正确的VSCode标签页"""
        import requests
        import websocket

        try:
            # 获取所有标签页
            response = requests.get("http://localhost:9222/json")
            tabs = response.json()

            # 查找VSCode主界面标签页
            vscode_tab = None
            for tab in tabs:
                title = tab.get('title', '').lower()
                url = tab.get('url', '').lower()
                # 查找包含VSCode标题且URL是workbench的标签页
                if ('visual studio code' in title or 'vscode' in title) and 'workbench.html' in url:
                    vscode_tab = tab
                    break

            if not vscode_tab:
                print("❌ 未找到VSCode主界面标签页")
                return False

            # 连接到VSCode标签页
            ws_url = vscode_tab['webSocketDebuggerUrl']
            self.vscode.ws = websocket.create_connection(ws_url)
            self.vscode.is_connected = True

            print(f"✅ 已连接到VSCode标签页: {vscode_tab.get('title', 'Unknown')}")
            return True

        except Exception as e:
            print(f"❌ 连接VSCode标签页失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.is_connected:
            self.vscode.disconnect()
            self.is_connected = False
            print("🔌 已断开连接")
    
    def execute_js(self, js_code: str):
        """执行JavaScript代码的便捷方法"""
        result = self.vscode.execute_javascript(js_code)
        if result.get("success"):
            return result.get("result")
        else:
            print(f"❌ JavaScript执行失败: {result.get('error')}")
            return None

    def load_js_file(self, js_file_path: str) -> str:
        """从JS文件加载JavaScript代码"""
        try:
            # 如果是相对路径，则相对于当前脚本目录
            if not os.path.isabs(js_file_path):
                script_dir = os.path.dirname(os.path.abspath(__file__))
                js_file_path = os.path.join(script_dir, js_file_path)

            with open(js_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ 读取JS文件失败: {e}")
            return ""

    def execute_js_file(self, js_file_path: str):
        """执行JS文件中的代码"""
        js_code = self.load_js_file(js_file_path)
        if js_code:
            return self.execute_js(js_code)
        return None

    def call_js_function(self, js_file_path: str, function_name: str, *args):
        """调用JS文件中的特定函数"""
        # 检查是否需要加载JS文件
        if js_file_path not in self._loaded_js_files:
            js_code = self.load_js_file(js_file_path)
            if not js_code:
                return None

            # 加载JS文件到浏览器环境
            load_result = self.execute_js(js_code)
            if load_result is None:
                self.log(f"❌ 加载JS文件失败: {js_file_path}")
                return None

            self._loaded_js_files.add(js_file_path)
            self.log(f"✅ 已加载JS文件: {js_file_path}")

        # 改进的参数处理
        processed_args = []
        for arg in args:
            if isinstance(arg, bool):
                # 布尔值转换为JavaScript的true/false
                processed_args.append('true' if arg else 'false')
            elif isinstance(arg, str):
                # 字符串加引号
                processed_args.append(f'"{arg}"')
            elif arg is None:
                # None转换为null
                processed_args.append('null')
            elif isinstance(arg, (int, float)):
                # 数字直接转换
                processed_args.append(str(arg))
            else:
                # 其他类型转换为字符串
                processed_args.append(f'"{str(arg)}"')

        args_str = ', '.join(processed_args)


        # 只调用函数，不重新加载JS代码
        call_code = f"{function_name}({args_str});"

        return self.execute_js(call_code)
    
    def wait(self, seconds: float = 1):
        """等待指定秒数"""
        time.sleep(seconds)
    
    def log(self, message: str):
        """打印日志"""
        print(f"📝 {message}")
    
    # ========================================
    # 在这里写您的自定义方法
    # ========================================
    


    def open_colipot_chat(self):
        """打开Copilot聊天"""

        result = self.call_js_function("js_scripts/copilot_operations.js", "openCopilitIcon")
        if result and result.get('success'):
            self.log("✅ Copilot图标已点击")
        else:
            error_msg = result.get('error', '未知错误') if result else '函数调用失败'
            self.log(f"❌ {error_msg}")
        return True
    
    def confirmIsNewSession(self,is_new_session=False):
        """自定义方法2 - 在这里写您的逻辑"""
        self.call_js_function("js_scripts/copilot_operations.js", "confirmIsNewSession",is_new_session)
        # 在这里添加您的DOM操作代码
        
    def focusTxtInput(self):
        """设置输入框焦点（带重试机制）"""
        max_retries = 3
        for attempt in range(max_retries):
            self.log(f"正在设置输入框焦点（第{attempt + 1}次尝试）...")

            result = self.call_js_function("js_scripts/copilot_operations.js", "focusTxtInput")
            if result and result.get("success"):
                self.log("✅ 输入框焦点设置成功")
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '函数调用失败'
                self.log(f"⚠️ 第{attempt + 1}次尝试失败: {error_msg}")

                if attempt < max_retries - 1:
                    self.log("等待0.5秒后重试...")
                    self.wait(0.5)

        self.log("❌ 设置输入框焦点失败，已达到最大重试次数")
        return False
    def inputText(self, text):
        """使用Python键盘操作直接输入文本"""
        import pyautogui

        self.log(f"输入文本: {text}")

        try:
            # 直接输入文本
            pyautogui.write(text, interval=0.01)  # interval控制输入速度

            self.log("✅ 文本输入完成")
            return True

        except Exception as e:
            self.log(f"❌ 文本输入失败: {e}")
            return False

    def inputTextJS(self, text):
        """备用方法：使用JavaScript DOM操作输入文本"""
        self.call_js_function("js_scripts/copilot_operations.js", "inputText", text)
        return True

    def inputTextAdvanced(self, text):
        """高级文本输入方法：支持中文和特殊字符"""
        import pyautogui
        import pyperclip

        self.log(f"高级输入文本: {text}")

        try:
            # 先尝试切换到VSCode窗口
            self.switch_to_vscode()
            self.wait(0.5)

            # 使用JavaScript设置输入框焦点
            self.log("正在设置输入框焦点...")
            focus_result = self.focusTxtInput()
            if focus_result:
                self.log("✅ 输入框焦点设置成功")
            else:
                self.log("⚠️ 输入框焦点设置失败，继续尝试输入")

            self.wait(0.3)  # 等待焦点设置完成

            # 方法1：对于包含中文或特殊字符的文本，使用剪贴板
            if any(ord(char) > 127 for char in text):  # 检测非ASCII字符
                self.log("检测到中文或特殊字符，使用剪贴板方法...")

                # 保存当前剪贴板内容
                original_clipboard = pyperclip.paste()

                # 复制文本到剪贴板
                pyperclip.copy(text)
                self.wait(0.1)

                # 粘贴
                pyautogui.hotkey('ctrl', 'v')

                # 恢复原剪贴板内容
                self.wait(0.2)
                pyperclip.copy(original_clipboard)

            else:
                # 方法2：对于纯英文，直接输入
                self.log("使用直接输入方法...")
                pyautogui.write(text, interval=0.01)

            self.log("✅ 高级文本输入完成")
            return True

        except Exception as e:
            self.log(f"❌ 高级文本输入失败: {e}")
            return False

    def switch_to_vscode(self):
        """切换到VSCode窗口"""
        import pyautogui

        self.log("正在切换到VSCode窗口...")

        try:
            # 方法1：尝试使用Windows API查找VSCode窗口
            try:
                import win32gui

                def find_vscode_window():
                    """查找VSCode窗口"""
                    vscode_windows = []

                    def enum_windows_callback(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if 'Visual Studio Code' in window_title or 'VSCode' in window_title:
                                windows.append((hwnd, window_title))
                        return True

                    win32gui.EnumWindows(enum_windows_callback, vscode_windows)
                    return vscode_windows

                vscode_windows = find_vscode_window()
                if vscode_windows:
                    # 激活第一个找到的VSCode窗口
                    hwnd, title = vscode_windows[0]
                    win32gui.SetForegroundWindow(hwnd)
                    self.log(f"✅ 已切换到VSCode窗口: {title}")
                    return True
                else:
                    self.log("未找到VSCode窗口，尝试Alt+Tab方法")

            except ImportError:
                self.log("win32gui不可用，使用Alt+Tab方法")
            except Exception as e:
                self.log(f"Windows API方法失败: {e}，使用Alt+Tab方法")

            # 方法2：使用Alt+Tab切换窗口（备用方法）
            pyautogui.hotkey('alt', 'tab')
            self.wait(0.5)

            self.log("✅ 已尝试切换到VSCode（Alt+Tab方法）")
            return True

        except Exception as e:
            self.log(f"❌ 切换到VSCode失败: {e}")
            return False
    def clickMsgBtn(self):
        """点击发送按钮"""
        result = self.call_js_function("js_scripts/copilot_operations.js", "clickMsgBtn")
        if result and result.get("success"):
            self.log("✅ 发送按钮点击成功")
            return True
        else:
            error_msg = result.get('error', '未知错误') if result else '函数调用失败'
            self.log(f"❌ 点击发送按钮失败: {error_msg}")
            return False
    def copy_and_paste_image(self, image_path):
        """更简单的复制粘贴方法"""
        import pyautogui

        if not os.path.exists(image_path):
            self.log(f"❌ 图片文件不存在: {image_path}")
            return False

        try:
            # 使用更纯净的图片复制方法
            ps_command = f'''
            Add-Type -AssemblyName System.Windows.Forms
            Add-Type -AssemblyName System.Drawing
            $image = [System.Drawing.Image]::FromFile("{image_path}")
            [System.Windows.Forms.Clipboard]::SetImage($image)
            $image.Dispose()
            '''

            self.log("复制图片到剪贴板...")
            subprocess.run(["powershell", "-Command", ps_command], check=True)

            # 等待剪贴板操作完成
            self.wait(0.5)

            # 直接按Ctrl+V
            self.log("执行Ctrl+V...")
            pyautogui.hotkey('ctrl', 'v')

            self.log("✅ 图片粘贴完成")
            return True

        except Exception as e:
            self.log(f"❌ 操作失败: {e}")
            return False
    def clearText(self):
        """用Python键盘操作清空文本"""
        import pyautogui

        self.log("使用键盘清空文本...")

        try:
            # 方法1: Ctrl+A 全选然后 Delete
            pyautogui.hotkey('ctrl', 'a')  # 全选
            self.wait(0.2)
            pyautogui.press('delete')      # 删除

            self.log("✅ 文本已清空")
            return True

        except Exception as e:
            self.log(f"❌ 清空文本失败: {e}")
            return False

    def clearTextAlternative(self):
        """备用清空方法"""
        import pyautogui

        self.log("使用备用方法清空文本...")

        try:
            # 方法2: Ctrl+A 全选然后 Backspace
            pyautogui.hotkey('ctrl', 'a')  # 全选
            self.wait(0.2)
            pyautogui.press('backspace')   # 退格删除

            self.log("✅ 文本已清空（备用方法）")
            return True

        except Exception as e:
            self.log(f"❌ 清空文本失败: {e}")
            return False

    def wait_and_click_element(self, selector, max_wait_time=30, check_interval=1):
        """循环检测元素并点击

        Args:
            selector: CSS选择器或多个选择器的列表
            max_wait_time: 最大等待时间（秒）
            check_interval: 检测间隔（秒）

        Returns:
            bool: 是否成功找到并点击元素
        """
        import time

        self.log(f"开始循环检测元素: {selector}")
        self.log(f"最大等待时间: {max_wait_time}秒，检测间隔: {check_interval}秒")

        start_time = time.time()
        attempt = 0

        # 确保selector是列表格式
        if isinstance(selector, str):
            selectors = [selector]
        else:
            selectors = selector

        while time.time() - start_time < max_wait_time:
            attempt += 1
            self.log(f"第{attempt}次检测...")

            try:
                # 检测元素是否存在
                result = self.call_js_function("js_scripts/copilot_operations.js", "detectAndClickElement", selectors)

                if result and result.get("success"):
                    clicked_selector = result.get("selector", "未知")
                    self.log(f"✅ 元素检测成功并已点击: {clicked_selector}")
                    return True
                else:
                    self.log(f"⏳ 第{attempt}次检测未找到元素，继续等待...")

            except Exception as e:
                self.log(f"❌ 检测过程中出现异常: {e}")

            # 等待指定间隔
            time.sleep(check_interval)

        self.log(f"❌ 超时：在{max_wait_time}秒内未检测到目标元素")
        return False

    def wait_for_element_appear(self, selector, max_wait_time=30, check_interval=1):
        """循环检测元素出现（不点击）

        Args:
            selector: CSS选择器或多个选择器的列表
            max_wait_time: 最大等待时间（秒）
            check_interval: 检测间隔（秒）

        Returns:
            bool: 是否找到元素
        """
        import time

        self.log(f"开始循环检测元素出现: {selector}")

        start_time = time.time()
        attempt = 0

        # 确保selector是列表格式
        if isinstance(selector, str):
            selectors = [selector]
        else:
            selectors = selector

        while time.time() - start_time < max_wait_time:
            attempt += 1
            self.log(f"第{attempt}次检测...")

            try:
                # 检测元素是否存在
                result = self.call_js_function("js_scripts/copilot_operations.js", "detectElement", selectors)

                if result and result.get("success"):
                    found_selector = result.get("selector", "未知")
                    self.log(f"✅ 元素检测成功: {found_selector}")
                    return True
                else:
                    self.log(f"⏳ 第{attempt}次检测未找到元素，继续等待...")

            except Exception as e:
                self.log(f"❌ 检测过程中出现异常: {e}")

            # 等待指定间隔
            time.sleep(check_interval)

        self.log(f"❌ 超时：在{max_wait_time}秒内未检测到目标元素")
        return False

    def wait_for_element_disappear(self, selector, max_wait_time=30, check_interval=1):
        """循环检测元素消失

        Args:
            selector: CSS选择器或多个选择器的列表
            max_wait_time: 最大等待时间（秒）
            check_interval: 检测间隔（秒）

        Returns:
            bool: 元素是否已消失
        """
        import time

        self.log(f"开始循环检测元素消失: {selector}")

        start_time = time.time()
        attempt = 0

        # 确保selector是列表格式
        if isinstance(selector, str):
            selectors = [selector]
        else:
            selectors = selector

        while time.time() - start_time < max_wait_time:
            attempt += 1
            self.log(f"第{attempt}次检测...")

            try:
                # 检测元素是否存在
                result = self.call_js_function("js_scripts/copilot_operations.js", "detectElement", selectors)

                if not result or not result.get("success"):
                    self.log(f"✅ 元素已消失")
                    return True
                else:
                    found_selector = result.get("selector", "未知")
                    self.log(f"⏳ 第{attempt}次检测元素仍存在: {found_selector}，继续等待...")

            except Exception as e:
                self.log(f"❌ 检测过程中出现异常: {e}")

            # 等待指定间隔
            time.sleep(check_interval)

        self.log(f"❌ 超时：在{max_wait_time}秒内元素未消失")
        return False

    def wait_and_click_by_name(self, selector_name: str, max_wait_time: int = None, check_interval: float = None):
        """使用配置中定义的选择器名称等待并点击元素

        Args:
            selector_name: 配置中定义的选择器名称
            max_wait_time: 最大等待时间（秒），None则使用配置默认值
            check_interval: 检测间隔（秒），None则使用配置默认值

        Returns:
            bool: 是否成功找到并点击元素
        """
        # 从配置获取选择器
        selector = self.config.get_common_selector(selector_name)
        if not selector:
            self.log(f"❌ 未找到选择器配置: {selector_name}")
            return False

        # 使用配置的默认值
        if max_wait_time is None:
            max_wait_time = self.config.get_max_wait_time()
        if check_interval is None:
            check_interval = self.config.get_check_interval()

        self.log(f"使用配置选择器 '{selector_name}': {selector}")
        return self.wait_and_click_element(selector, max_wait_time, check_interval)

    def wait_for_copilot_element(self, element_name: str, max_wait_time: int = None, check_interval: float = None):
        """等待Copilot相关元素出现

        Args:
            element_name: Copilot元素名称（如 'send_button', 'input_area'）
            max_wait_time: 最大等待时间（秒）
            check_interval: 检测间隔（秒）

        Returns:
            bool: 是否找到元素
        """
        copilot_selectors = self.config.get_copilot_selectors()
        selector = copilot_selectors.get(element_name)

        if not selector:
            self.log(f"❌ 未找到Copilot元素配置: {element_name}")
            return False

        # 使用配置的默认值
        if max_wait_time is None:
            max_wait_time = self.config.get_max_wait_time()
        if check_interval is None:
            check_interval = self.config.get_check_interval()

        self.log(f"等待Copilot元素 '{element_name}': {selector}")
        return self.wait_for_element_appear(selector, max_wait_time, check_interval)

    def auto_handle_dialogs(self):
        """自动处理启用的对话框"""
        enabled_elements = self.config.get_enabled_auto_click_elements()

        if not enabled_elements:
            self.log("ℹ️ 没有启用的自动点击元素")
            return False

        self.log(f"🤖 开始自动处理 {len(enabled_elements)} 个对话框...")

        handled_count = 0
        for element in enabled_elements:
            name = element.get("name", "未知")
            selector = element.get("selector", "")
            max_wait_time = element.get("max_wait_time", 5)
            description = element.get("description", "")

            self.log(f"检测 '{name}': {description}")

            if self.wait_and_click_element(selector, max_wait_time, 0.5):
                self.log(f"✅ 已处理: {name}")
                handled_count += 1
            else:
                self.log(f"ℹ️ 未发现: {name}")

        self.log(f"🎯 自动处理完成，共处理 {handled_count} 个对话框")
        return handled_count > 0

    def detect_element_by_config(self, config_key: str, element_name: str):
        """使用JavaScript配置检测元素

        Args:
            config_key: 配置键名 ('common_selectors', 'copilot_elements', 'ui_states')
            element_name: 元素名称

        Returns:
            bool: 是否找到元素
        """
        try:
            result = self.call_js_function(
                "js_scripts/copilot_operations.js",
                "detectElementByConfig",
                config_key,
                element_name
            )

            if result and result.get("success"):
                self.log(f"✅ 找到配置元素: {config_key}.{element_name}")
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '函数调用失败'
                self.log(f"❌ 未找到配置元素: {config_key}.{element_name} - {error_msg}")
                return False

        except Exception as e:
            self.log(f"❌ 配置检测异常: {e}")
            return False

    def click_element_by_config(self, config_key: str, element_name: str):
        """使用JavaScript配置检测并点击元素

        Args:
            config_key: 配置键名
            element_name: 元素名称

        Returns:
            bool: 是否成功点击
        """
        try:
            result = self.call_js_function(
                "js_scripts/copilot_operations.js",
                "detectAndClickByConfig",
                config_key,
                element_name
            )

            if result and result.get("success"):
                self.log(f"✅ 成功点击配置元素: {config_key}.{element_name}")
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '函数调用失败'
                self.log(f"❌ 点击配置元素失败: {config_key}.{element_name} - {error_msg}")
                return False

        except Exception as e:
            self.log(f"❌ 配置点击异常: {e}")
            return False

    def auto_handle_dialogs_js(self):
        """使用JavaScript自动处理对话框"""
        try:
            result = self.call_js_function(
                "js_scripts/copilot_operations.js",
                "autoHandleDialogs"
            )

            if result:
                handled_count = result.get("handledCount", 0)
                total_count = result.get("totalCount", 0)
                self.log(f"🎯 JavaScript自动处理完成: {handled_count}/{total_count}")
                return result.get("success", False)
            else:
                self.log("❌ JavaScript自动处理失败")
                return False

        except Exception as e:
            self.log(f"❌ JavaScript自动处理异常: {e}")
            return False

    def start_auto_click_monitor(self, monitor_interval: float = 2.0, max_duration: float = None,
                                adaptive_interval: bool = False):
        """启动自动点击监控（后台循环）

        Args:
            monitor_interval: 监控间隔（秒）
            max_duration: 最大监控时长（秒），None表示无限制
            adaptive_interval: 是否启用自适应间隔调整

        Returns:
            bool: 是否成功启动监控
        """
        import threading
        import time

        enabled_elements = self.get_enabled_auto_click_elements()
        if not enabled_elements:
            self.log("ℹ️ 没有启用的自动点击元素，跳过监控")
            return False

        self.log(f"🚀 启动自动点击监控，间隔: {monitor_interval}秒")
        if adaptive_interval:
            self.log("🧠 启用自适应间隔调整")
        self.log(f"📋 监控元素: {[elem['name'] for elem in enabled_elements]}")

        self._auto_click_running = True
        self._dynamic_interval = monitor_interval  # 初始化动态间隔
        self._just_executed_step = False  # 初始化开发步骤执行标志
        self._step_execution_time = None  # 初始化步骤执行时间
        self._auto_click_thread = threading.Thread(
            target=self._auto_click_monitor_loop,
            args=(monitor_interval, max_duration, adaptive_interval),
            daemon=True
        )
        self._auto_click_thread.start()

        return True

    def set_monitor_interval(self, new_interval: float):
        """动态调整监控间隔

        Args:
            new_interval: 新的监控间隔（秒）
        """
        if hasattr(self, '_auto_click_running') and self._auto_click_running:
            self._dynamic_interval = new_interval
            self.log(f"🔧 动态设置监控间隔为 {new_interval}秒")
        else:
            self.log("⚠️ 监控未运行，无法调整间隔")

    def stop_auto_click_monitor(self):
        """停止自动点击监控"""
        if hasattr(self, '_auto_click_running'):
            self._auto_click_running = False
            self.log("🛑 自动点击监控已停止")

    def set_just_executed_step(self, value: bool):
        """设置刚刚执行开发步骤的标志

        Args:
            value: True表示刚刚执行了开发步骤，暂时不发送编译错误
        """
        self._just_executed_step = value
        if value:
            import time
            self._step_execution_time = time.time()
            self.log("🚫 已设置标志：刚刚执行了开发步骤，暂时不发送编译错误")
        else:
            self.log("✅ 已清除标志：可以正常发送编译错误")

    def _auto_click_monitor_loop(self, monitor_interval: float, max_duration: float = None,
                                adaptive_interval: bool = False):
        """自动点击监控循环（内部方法）

        Args:
            monitor_interval: 基础监控间隔（秒）
            max_duration: 最大监控时长（秒），None表示无限制
            adaptive_interval: 是否启用自适应间隔调整
        """
        import time

        start_time = time.time()
        cycle_count = 0
        total_handled = 0
        current_interval = monitor_interval
        consecutive_no_action = 0  # 连续无操作次数
        last_copilot_working = None  # 上次Copilot工作状态
        last_refresh_cycle = 0  # 上次刷新步骤数据的循环次数

        try:
            while self._auto_click_running:
                cycle_count += 1
                self.log(f"🔄 第{cycle_count}次监控循环（间隔: {current_interval:.1f}秒）...")

                # 每10个循环刷新一次步骤数据，确保状态是最新的
                if cycle_count - last_refresh_cycle >= 10:
                    self.log("🔄 定期刷新步骤数据以确保状态最新...")
                    if hasattr(self, 'parent_ui') and self.parent_ui and hasattr(self.parent_ui, 'refresh_development_steps'):
                        try:
                            self.parent_ui.refresh_development_steps()
                            last_refresh_cycle = cycle_count
                            self.log("✅ 步骤数据已刷新")
                        except Exception as e:
                            self.log(f"⚠️ 刷新步骤数据失败: {e}")
                    else:
                        self.log("⚠️ 无法刷新步骤数据")

                # 检查是否超过最大时长
                if max_duration and (time.time() - start_time) > max_duration:
                    self.log(f"⏰ 达到最大监控时长 {max_duration}秒，停止监控")
                    break

                # 检查Copilot工作状态
                copilot_working = self._check_copilot_working_status()
                self.log(f"📊 Copilot当前状态: {'工作中' if copilot_working else '停止'}, 上次状态: {'工作中' if last_copilot_working else '停止' if last_copilot_working is not None else '未知'}")

                # 如果Copilot从工作状态变为停止状态，记录停止时间
                if last_copilot_working is True and copilot_working is False:
                    self.log("🔍 检测到Copilot停止工作，记录停止时间...")
                    import time
                    self.copilot_stop_time = time.time()
                    self._handle_copilot_stopped()
                # 如果Copilot持续停止工作，检查是否需要发送编译错误
                elif copilot_working is False and self.copilot_stop_time is not None:
                    import time
                    current_time = time.time()
                    time_since_stop = current_time - self.copilot_stop_time

                    self.log(f"⏱️ Copilot已停止工作 {time_since_stop:.1f}秒，延迟设置: {self.error_send_delay}秒")

                    # 如果停止工作时间超过设定的延迟时间，检查并发送编译错误
                    if time_since_stop >= self.error_send_delay:
                        self.log(f"🔍 Copilot已停止工作 {time_since_stop:.1f}秒，超过延迟时间")
                        # 检查并发送编译错误
                        self.log("🤖 开始检查并发送编译错误...")
                        success = self._check_and_send_compilation_errors()
                        if success:
                            self.log("✅ 编译错误检查和发送完成")
                        else:
                            self.log("ℹ️ 未发现编译错误或发送失败")
                        # 重置停止时间，避免重复检查
                        self.copilot_stop_time = None
                # 如果Copilot一直处于停止状态且没有记录停止时间，初始化停止时间
                elif copilot_working is False and self.copilot_stop_time is None and last_copilot_working is not None:
                    self.log("🔍 Copilot处于停止状态，初始化停止时间记录...")
                    import time
                    self.copilot_stop_time = time.time()
                # 如果Copilot重新开始工作，清除停止时间
                elif copilot_working is True and self.copilot_stop_time is not None:
                    self.log("✅ Copilot重新开始工作，清除停止时间记录")
                    self.copilot_stop_time = None

                last_copilot_working = copilot_working

                # 执行自动处理（使用JavaScript端）
                handled = self.auto_handle_dialogs_js()
                if handled:
                    total_handled += 1
                    consecutive_no_action = 0  # 重置无操作计数

                    # 如果有操作，缩短间隔以便快速响应
                    if adaptive_interval:
                        current_interval = max(0.5, monitor_interval * 0.5)
                        self.log(f"📈 检测到操作，缩短间隔至 {current_interval:.1f}秒")
                else:
                    consecutive_no_action += 1

                    # 如果连续无操作，逐渐延长间隔以节省资源
                    if adaptive_interval and consecutive_no_action >= 3:
                        current_interval = min(monitor_interval * 2, 10.0)  # 最大不超过10秒
                        if consecutive_no_action == 3:  # 只在第一次延长时记录
                            self.log(f"📉 连续无操作，延长间隔至 {current_interval:.1f}秒")

                # 检查是否有动态间隔调整请求
                if hasattr(self, '_dynamic_interval') and self._dynamic_interval != current_interval:
                    current_interval = self._dynamic_interval
                    self.log(f"🔧 动态调整间隔至 {current_interval:.1f}秒")

                # 等待下一次检测
                time.sleep(current_interval)

        except Exception as e:
            self.log(f"❌ 自动点击监控异常: {e}")
        finally:
            self._auto_click_running = False
            self.log(f"📊 监控结束统计: 共{cycle_count}次循环，处理{total_handled}次对话框")

    def _check_copilot_working_status(self):
        """检查Copilot工作状态

        Returns:
            bool: True表示正在工作，False表示未工作，None表示检测失败
        """
        try:
            self.log("🔍 开始检查Copilot工作状态...")

            # 检查连接状态
            if not self.is_connected:
                self.log("❌ 未连接到VSCode，无法检查Copilot状态")
                return None

            # 调用JavaScript函数
            result = self.call_js_function("js_scripts/copilot_operations.js", "isWorking")
            self.log(f"📊 isWorking函数返回结果: {result}")

            if result and isinstance(result, dict):
                success = result.get('success', False)
                self.log(f"✅ Copilot工作状态检查完成，结果: {success}")
                return success
            else:
                self.log(f"⚠️ isWorking函数返回了意外的结果格式: {type(result)}")
                return None
        except Exception as e:
            self.log(f"❌ 检查Copilot工作状态失败: {e}")
            import traceback
            self.log(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _handle_copilot_stopped(self):
        """处理Copilot停止工作的情况（已禁用自动编译错误检测）"""
        try:
            self.log("ℹ️ Copilot已停止工作，自动编译错误检测已禁用")
            return

        except Exception as e:
            self.log(f"❌ 处理Copilot停止工作异常: {e}")
    def _get_latest_copilot_messages(self, limit=3):
        """获取最新的Copilot消息

        Args:
            limit: 获取消息数量限制

        Returns:
            list: 最新消息列表
        """
        try:
            # 调用JavaScript获取最新消息
            result = self.call_js_function("js_scripts/copilot_operations.js", "getLatestMessages", limit)

            if result and result.get('success'):
                return result.get('messages', [])
            else:
                self.log(f"⚠️ 获取最新消息失败: {result.get('error', '未知错误') if result else '无响应'}")
                return []

        except Exception as e:
            self.log(f"❌ 获取最新消息异常: {e}")
            return []

    def _update_task_completion_status_directly(self):
        """直接更新任务完成状态（通过API调用）"""
        try:
            # 获取当前任务和步骤信息
            task_info = self._get_current_task_info()

            if not task_info:
                self.log("⚠️ 无法获取当前任务信息")
                return

            task_id = task_info.get('taskId')
            step_id = task_info.get('stepId')

            if not task_id or not step_id:
                self.log("⚠️ 任务ID或步骤ID缺失")
                return

            # 通过回调函数调用API更新
            if hasattr(self, 'api_update_callback') and callable(self.api_update_callback):
                self.log(f"🔄 正在更新任务状态 (任务ID: {task_id}, 步骤ID: {step_id})")

                success = self.api_update_callback(task_id, step_id, 'Completed', True)

                if success:
                    self.log(f"✅ 任务状态更新成功！")

                    # 发送成功确认消息给Copilot
                    success_message = f"""✅ 任务状态更新成功！

📋 更新详情：
- 任务: {task_info.get('taskName', '未知任务')}
- 步骤: {task_info.get('stepName', '未知步骤')}
- 状态: 已完成 (Completed)
- 编码完成标志: 是 (True)
- 进度: 100%

🎉 恭喜！该开发步骤已标记为完成，进度已同步更新为100%。"""

                    self._send_error_to_copilot(success_message)
                else:
                    self.log("❌ 任务状态更新失败")

                    # 发送失败消息给Copilot
                    error_message = f"""❌ 任务状态更新失败

📋 任务信息：
- 任务ID: {task_id}
- 步骤ID: {step_id}
- 任务: {task_info.get('taskName', '未知任务')}
- 步骤: {task_info.get('stepName', '未知步骤')}

请检查网络连接或联系管理员。"""

                    self._send_error_to_copilot(error_message)
            else:
                self.log("⚠️ 未设置API更新回调函数")

                # 发送提示消息给Copilot
                info_message = f"""ℹ️ 编码已完成确认，但无法自动更新状态

📋 任务信息：
- 任务ID: {task_id}
- 步骤ID: {step_id}
- 任务: {task_info.get('taskName', '未知任务')}
- 步骤: {task_info.get('stepName', '未知步骤')}

请手动在系统中将该步骤标记为完成。"""

                self._send_error_to_copilot(info_message)

        except Exception as e:
            self.log(f"❌ 直接更新任务完成状态异常: {e}")

    def _update_task_completion_status(self):
        """更新任务完成状态（保留原方法以兼容）"""
        self._update_task_completion_status_directly()

    def _get_current_task_info(self):
        """获取当前任务信息

        Returns:
            dict: 包含taskId和stepId的字典，如果获取失败返回None
        """
        try:
            # 通过回调函数获取当前任务信息
            if hasattr(self, 'task_info_callback') and callable(self.task_info_callback):
                return self.task_info_callback()
            else:
                self.log("⚠️ 未设置任务信息获取回调函数")
                return None

        except Exception as e:
            self.log(f"❌ 获取当前任务信息失败: {e}")
            return None

    def set_task_info_callback(self, callback):
        """设置任务信息获取回调函数

        Args:
            callback: 回调函数，应返回包含taskId和stepId的字典
        """
        self.task_info_callback = callback
        self.log("✅ 任务信息获取回调函数已设置")

    def set_api_update_callback(self, callback):
        """设置API更新回调函数

        Args:
            callback: 回调函数，参数为(task_id, step_id, status, is_finish_coding)，返回bool表示是否成功
        """
        self.api_update_callback = callback
        self.log("✅ API更新回调函数已设置")

    def set_error_callback(self, callback):
        """设置错误获取回调函数

        Args:
            callback: 回调函数，应返回编译错误信息字符串
        """
        self.error_callback = callback
        self.log("✅ 错误获取回调函数已设置")

    def set_step_execution_callback(self, callback):
        """设置步骤执行回调函数

        Args:
            callback: 回调函数，用于执行开发步骤
        """
        self.step_execution_callback = callback
        self.log("✅ 步骤执行回调函数已设置")

    def set_error_send_delay(self, delay_seconds):
        """设置错误发送延迟时间

        Args:
            delay_seconds: 延迟时间（秒）
        """
        self.error_send_delay = max(1, int(delay_seconds))  # 最少1秒
        self.log(f"✅ 错误发送延迟已设置为: {self.error_send_delay}秒")



    def _send_error_to_copilot(self, error_message):
        """发送错误信息给Copilot

        Args:
            error_message (str): 错误信息

        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查错误信息是否有效
            if not error_message or not error_message.strip():
                self.log("⚠️ 错误信息为空，跳过发送")
                return False

            # 构建完整的提示信息
            full_message = f"发现编译错误，请帮助修复：\n\n{error_message}"
            self.log(f"📝 准备发送消息长度: {len(full_message)} 字符")

            # 截断过长的消息
            if len(full_message) > 8000:
                full_message = full_message[:8000] + "\n\n... (消息过长，已截断)"
                self.log(f"⚠️ 消息过长，已截断至 {len(full_message)} 字符")

            # 转义特殊字符以避免JavaScript解析错误
            escaped_message = self._escape_message_for_js(full_message)
            self.log(f"🔧 消息转义后长度: {len(escaped_message)} 字符")

            # 第一步：输入文本（使用高级输入方法）
            self.log("📝 第一步：输入错误信息...")
            input_success = self.inputTextAdvanced(full_message)  # 使用原始消息，不需要转义
            self.log(f"📊 inputTextAdvanced返回: {input_success}")

            if input_success:
                # 等待一下确保输入完成
                import time
                time.sleep(1.0)  # 增加等待时间

                # 第二步：点击发送按钮
                self.log("📤 第二步：点击发送按钮...")
                send_success = self.clickMsgBtn()
                self.log(f"📊 clickMsgBtn返回: {send_success}")

                if send_success:
                    self.log("✅ 错误信息已发送给Copilot")
                    return True
                else:
                    self.log("❌ 点击发送按钮失败")
                    return False
            else:
                self.log("❌ 输入文本失败")
                return False

        except Exception as e:
            self.log(f"❌ 发送错误信息异常: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return False

    def _escape_message_for_js(self, message):
        """转义消息中的特殊字符以避免JavaScript解析错误"""
        try:
            # 替换可能导致JavaScript解析错误的字符
            escaped = message

            # 转义反斜杠（必须最先处理）
            escaped = escaped.replace('\\', '\\\\')

            # 转义引号
            escaped = escaped.replace('"', '\\"')
            escaped = escaped.replace("'", "\\'")

            # 转义换行符
            escaped = escaped.replace('\n', '\\n')
            escaped = escaped.replace('\r', '\\r')
            escaped = escaped.replace('\t', '\\t')

            # 移除或替换其他可能有问题的字符
            escaped = escaped.replace('\x00', '')  # 空字符
            escaped = escaped.replace('\x08', '')  # 退格符
            escaped = escaped.replace('\x0c', '')  # 换页符

            return escaped

        except Exception as e:
            self.log(f"⚠️ 消息转义失败: {e}")
            # 如果转义失败，返回简化的消息
            return "发现编译错误，请检查代码并修复相关问题。"

    def _check_and_send_compilation_errors(self):
        """检查Python UI界面中的前后端编译错误选项卡是否有错误，如果有就发送给Copilot"""
        try:
            self.log("🔍 开始检查编译错误...")

            # 获取编译错误信息
            error_message = self._get_compilation_errors()

            if error_message and error_message.strip():
                self.log(f"✅ 发现编译错误，准备发送给Copilot")
                # 发送错误信息给Copilot
                success = self._send_error_to_copilot(error_message)
                if success:
                    self.log("✅ 编译错误已成功发送给Copilot")
                else:
                    self.log("❌ 发送编译错误给Copilot失败")
                return success
            else:
                self.log("ℹ️ 未发现编译错误")
                # 当Copilot未工作且未发现编译错误时，说明当前步骤可能已完成
                self.log("🎯 Copilot未工作且无编译错误，尝试自动完成当前步骤并进行下一步...")
                self._auto_complete_current_step_and_next()
                return False

        except Exception as e:
            self.log(f"❌ 检查编译错误异常: {e}")
            import traceback
            self.log(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _auto_complete_current_step_and_next(self):
        """自动完成当前步骤并进行下一步开发"""
        try:
            self.log("🎯 开始自动完成当前步骤...")

            # 获取当前正在开发的步骤
            current_step = self._get_current_developing_step()
            if not current_step:
                self.log("⚠️ 未找到当前开发的步骤")
                return False

            step_name = current_step.get('stepName', '未知步骤')
            step_status = current_step.get('status', 'Unknown')

            self.log(f"📋 当前步骤: {step_name}, 状态: {step_status}")

            # 如果当前步骤已完成，直接跳转到下一个待处理步骤
            if step_status == 'Completed':
                self.log(f"ℹ️ 当前步骤状态为 {step_status}，已完成，尝试跳转到下一个待处理步骤")
                # 直接开始下一个步骤
                return self._auto_start_next_step()

            # 如果当前步骤不是InProgress状态，且不是Completed状态，不进行自动完成
            if step_status != 'InProgress':
                self.log(f"ℹ️ 当前步骤状态为 {step_status}，不需要自动完成")
                return False

            # 更新当前开发步骤状态为Completed
            success = self._update_current_developing_step_status('Completed')
            if success:
                self.log(f"✅ 步骤 '{step_name}' 已自动标记为完成")

                # 等待一小段时间让UI更新
                import time
                time.sleep(1)

                # 自动选择并开始下一个步骤
                self._auto_start_next_step()
                return True
            else:
                self.log(f"❌ 更新步骤状态失败")
                return False

        except Exception as e:
            self.log(f"❌ 自动完成当前步骤异常: {e}")
            import traceback
            self.log(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _get_current_developing_step(self):
        """获取当前正在开发的步骤（优先使用记录的开发步骤，其次使用UI选中步骤）"""
        try:
            # 优先使用记录的当前开发步骤
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'get_current_developing_step'):
                    developing_step = self.parent_ui.get_current_developing_step()
                    if developing_step:
                        # 检查步骤状态是否是最新的，如果状态看起来不对，强制刷新
                        step_status = developing_step.get('status', 'Unknown')
                        self.log(f"✅ 找到当前开发步骤: {developing_step.get('stepName', '未知')}")
                        self.log(f"📋 当前步骤状态: {step_status}")

                        # 如果状态是NotStarted但我们期望是InProgress，尝试获取最新状态
                        if step_status in ['NotStarted', 'Pending'] and hasattr(self.parent_ui, 'get_fresh_step_status'):
                            step_id = developing_step.get('id')
                            self.log(f"🔄 步骤状态可能不是最新的，从API获取最新状态... (步骤ID: {step_id})")
                            fresh_step = self.parent_ui.get_fresh_step_status(step_id)
                            if fresh_step:
                                fresh_status = fresh_step.get('status', 'Unknown')
                                self.log(f"🔄 API返回的最新状态: {fresh_status}")
                                if fresh_status != step_status:
                                    self.log(f"✅ 状态已更新: {step_status} -> {fresh_status}")
                                    # 更新当前开发步骤的数据
                                    if hasattr(self.parent_ui, 'current_developing_step') and self.parent_ui.current_developing_step:
                                        if self.parent_ui.current_developing_step.get('id') == step_id:
                                            self.parent_ui.current_developing_step = fresh_step
                                    return fresh_step
                                else:
                                    self.log(f"ℹ️ API状态与本地状态一致: {fresh_status}")
                            else:
                                self.log("⚠️ 无法从API获取最新状态")

                        return developing_step
                    else:
                        self.log("ℹ️ 没有记录的当前开发步骤，尝试使用UI选中步骤")

                # 如果没有记录的开发步骤，尝试恢复
                if hasattr(self.parent_ui, '_recover_current_developing_step'):
                    self.log("🔄 尝试恢复当前开发步骤...")
                    if self.parent_ui._recover_current_developing_step():
                        developing_step = self.parent_ui.get_current_developing_step()
                        if developing_step:
                            self.log(f"✅ 已恢复当前开发步骤: {developing_step.get('stepName', '未知')}")
                            return developing_step

                # 如果恢复失败，回退到UI选中步骤
                if hasattr(self.parent_ui, 'step_manager') and self.parent_ui.step_manager:
                    selected_step = self.parent_ui.step_manager.selected_step
                    if selected_step:
                        step_name = selected_step.get('stepName', '未知')
                        step_status = selected_step.get('status', 'Unknown')
                        step_id = selected_step.get('id')

                        self.log(f"✅ 使用UI选中步骤: {step_name}")
                        self.log(f"📋 当前步骤: {step_name}, 状态: {step_status}")

                        # 对于UI选中步骤，强制刷新状态
                        if hasattr(self.parent_ui, 'force_refresh_current_step_status'):
                            self.log(f"🔄 强制刷新UI选中步骤状态... (步骤ID: {step_id})")
                            fresh_step = self.parent_ui.force_refresh_current_step_status()
                            if fresh_step:
                                fresh_status = fresh_step.get('status', 'Unknown')
                                if fresh_status != step_status:
                                    self.log(f"✅ UI选中步骤状态已强制更新: {step_status} -> {fresh_status}")
                                    return fresh_step
                                else:
                                    self.log(f"ℹ️ UI选中步骤状态确认无变化: {fresh_status}")
                            else:
                                self.log("⚠️ 强制刷新失败")

                        return selected_step
                    else:
                        self.log("⚠️ 步骤管理器中没有选中的步骤")
                else:
                    self.log("⚠️ 无法访问步骤管理器")
            else:
                self.log("⚠️ 无法访问父UI对象")
            return None
        except Exception as e:
            self.log(f"❌ 获取当前开发步骤异常: {e}")
            return None

    def _get_current_selected_step(self):
        """获取当前选中的步骤（保留原方法以兼容性）"""
        return self._get_current_developing_step()

    def _update_current_developing_step_status(self, new_status):
        """更新当前开发步骤的状态"""
        try:
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'update_current_developing_step_status'):
                    return self.parent_ui.update_current_developing_step_status(new_status)
                else:
                    self.log("⚠️ 父UI不支持更新当前开发步骤状态，回退到传统方法")
                    # 回退到传统的选中步骤更新方法
                    current_step = self._get_current_developing_step()
                    if current_step:
                        return self._update_step_status_to_completed(current_step)
            return False
        except Exception as e:
            self.log(f"❌ 更新当前开发步骤状态异常: {e}")
            return False

    def _update_step_status_to_completed(self, step):
        """更新编码任务步骤状态为Completed"""
        try:
            if not step or 'id' not in step:
                self.log("❌ 步骤信息无效")
                return False

            step_id = step['id']

            # 获取当前任务ID
            current_task = self._get_current_task()
            if not current_task:
                self.log("❌ 无法获取当前任务信息")
                return False

            task_id = current_task.get('id')
            self.log(f"📋 当前任务ID: {task_id}, 步骤ID: {step_id}")

            # 通过parent_ui的API客户端更新编码任务步骤状态
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'api_client') and self.parent_ui.api_client:
                    # 使用编码任务步骤状态更新API
                    result = self.parent_ui.api_client.update_coding_task_step_flags(
                        task_id=task_id,
                        step_id=step_id,
                        status='Completed',
                        is_finish_coding=True
                    )

                    if result and result.get('success', False):
                        self.log(f"✅ 编码任务步骤状态已更新为Completed")

                        # 同时更新开发步骤的进度为100%
                        try:
                            progress_result = self.parent_ui.api_client.update_development_step(
                                step_id=step_id,
                                status='Completed',
                                progress=100
                            )
                            if progress_result:
                                self.log(f"✅ 开发步骤进度已同步更新为100%")
                            else:
                                self.log(f"⚠️ 开发步骤进度更新失败，但编码任务状态更新成功")
                        except Exception as progress_error:
                            self.log(f"⚠️ 更新开发步骤进度异常: {progress_error}")

                        # 刷新步骤列表
                        try:
                            if hasattr(self.parent_ui, 'step_manager') and self.parent_ui.step_manager:
                                self.parent_ui.step_manager.refresh_steps()
                                self.log("✅ 步骤列表已刷新")
                            else:
                                self.log("⚠️ 无法访问步骤管理器，跳过刷新")
                        except Exception as refresh_error:
                            self.log(f"⚠️ 刷新步骤列表失败: {refresh_error}，但状态更新成功")

                        return True
                    else:
                        self.log(f"❌ API更新编码任务步骤状态失败: {result}")
                        return False

            self.log("❌ 无法访问API客户端")
            return False

        except Exception as e:
            self.log(f"❌ 更新编码任务步骤状态异常: {e}")
            return False

    def _get_current_task(self):
        """获取当前选中的任务"""
        try:
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'project_task_manager') and self.parent_ui.project_task_manager:
                    selected_task = self.parent_ui.project_task_manager.selected_task
                    if selected_task:
                        self.log(f"✅ 找到当前选中任务: {selected_task.get('taskName', '未知')}")
                        return selected_task
                    else:
                        self.log("⚠️ 项目任务管理器中没有选中的任务")
                else:
                    self.log("⚠️ 无法访问项目任务管理器")
            else:
                self.log("⚠️ 无法访问父UI对象")
            return None
        except Exception as e:
            self.log(f"❌ 获取当前任务异常: {e}")
            return None

    def _auto_start_next_step(self):
        """自动开始下一个步骤"""
        try:
            self.log("🚀 尝试自动开始下一个步骤...")

            # 等待步骤列表刷新
            import time
            time.sleep(2)

            # 获取下一个待处理的步骤
            next_step = self._get_next_pending_step()
            if not next_step:
                self.log("ℹ️ 没有找到下一个待处理的步骤")
                return False

            step_name = next_step.get('stepName', '未知步骤')
            self.log(f"🎯 找到下一个步骤: {step_name}")

            # 选中下一个步骤
            if self._select_step_in_ui(next_step):
                # 等待UI更新
                time.sleep(1)

                # 自动执行下一个步骤
                if self._auto_execute_step():
                    self.log("✅ 下一个步骤已自动开始执行")
                    return True
                else:
                    self.log("⚠️ 自动执行下一个步骤失败")
                    return False
            else:
                self.log("⚠️ 选中下一个步骤失败")
                return False

        except Exception as e:
            self.log(f"❌ 自动开始下一个步骤异常: {e}")
            return False

    def _get_next_pending_step(self):
        """获取下一个待处理的步骤"""
        try:
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'step_manager') and self.parent_ui.step_manager:
                    steps = self.parent_ui.step_manager.development_steps
                    self.log(f"🔍 查找下一个待处理步骤，总步骤数: {len(steps)}")

                    # 查找第一个Pending或NotStarted状态的步骤
                    for i, step in enumerate(steps):
                        status = step.get('status', '')
                        step_name = step.get('stepName', '未知')
                        self.log(f"  步骤 {i+1}: {step_name}, 状态: {status}")

                        if status in ['Pending', 'NotStarted']:
                            self.log(f"✅ 找到下一个待处理步骤: {step_name}")
                            return step

                    self.log("⚠️ 没有找到待处理的步骤")
                else:
                    self.log("⚠️ 无法访问步骤管理器")
            else:
                self.log("⚠️ 无法访问父UI对象")
            return None
        except Exception as e:
            self.log(f"❌ 获取下一个待处理步骤异常: {e}")
            return None

    def _select_step_in_ui(self, step):
        """在UI中选中指定步骤"""
        try:
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'step_manager') and self.parent_ui.step_manager:
                    # 通过step_manager选中步骤
                    self.parent_ui.step_manager.select_step_by_id(step.get('id'))
                    self.log(f"✅ 已在UI中选中步骤: {step.get('stepName', '未知')}")
                    return True
            return False
        except Exception as e:
            self.log(f"❌ 在UI中选中步骤异常: {e}")
            return False

    def _auto_execute_step(self):
        """自动执行当前选中的步骤"""
        try:
            if hasattr(self, 'parent_ui') and self.parent_ui:
                if hasattr(self.parent_ui, 'execute_selected_step'):
                    # 调用父UI的执行方法
                    self.parent_ui.parent.after(0, self.parent_ui.execute_selected_step)
                    self.log("🚀 已触发自动执行下一个步骤")
                    return True
            return False
        except Exception as e:
            self.log(f"❌ 自动执行步骤异常: {e}")
            return False

    def _check_and_send_compilation_errors_with_delay(self):
        """在延迟后检查并发送编译错误"""
        try:
            self.log("🔍 延迟检查编译错误...")

            # 等待一小段时间，确保UI状态稳定
            import time
            time.sleep(2)

            # 检查并发送编译错误
            return self._check_and_send_compilation_errors()

        except Exception as e:
            self.log(f"❌ 延迟检查编译错误异常: {e}")
            import traceback
            self.log(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _get_compilation_errors(self):
        """获取编译错误信息"""
        try:
            self.log("🔍 开始获取编译错误信息...")

            # 方法1: 通过回调函数获取
            if hasattr(self, 'error_callback') and callable(self.error_callback):
                self.log("🔍 方法1: 通过回调函数获取编译错误...")
                try:
                    error_message = self.error_callback()
                    if error_message and error_message.strip():
                        self.log(f"✅ 通过回调函数获取到编译错误: {len(error_message)} 字符")
                        return error_message
                    else:
                        self.log("ℹ️ 回调函数返回空错误信息")
                except Exception as e:
                    self.log(f"⚠️ 回调函数调用失败: {e}")

            # 方法2: 直接从UI获取
            self.log("🔍 方法2: 直接从UI获取编译错误...")
            error_message = self._get_compilation_errors_from_ui()
            if error_message and error_message.strip():
                self.log(f"✅ 从UI获取到编译错误: {len(error_message)} 字符")
                return error_message

            self.log("ℹ️ 未获取到编译错误信息")
            return None

        except Exception as e:
            self.log(f"❌ 获取编译错误异常: {e}")
            import traceback
            self.log(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _get_compilation_errors_from_ui(self):
        """直接从Python UI界面的错误tab获取编译错误信息"""
        try:
            self.log("🔍 直接从Python UI界面获取编译错误...")

            # 查找CSharpErrorCaptureUI实例
            error_message = self._find_csharp_error_ui()

            if error_message:
                self.log("✅ 从UI界面获取到编译错误信息")
                return error_message

            self.log("ℹ️ UI界面中未发现编译错误")
            return None

        except Exception as e:
            self.log(f"❌ 从UI获取编译错误失败: {e}")
            return None

    def _find_csharp_error_ui(self):
        """简化版：直接从Tkinter根窗口查找错误信息"""
        try:
            import tkinter as tk

            self.log("🔍 使用简化方法：直接从Tkinter根窗口查找...")

            # 获取所有Tkinter根窗口
            if tk._default_root is None:
                self.log("❌ 没有找到Tkinter根窗口")
                return None

            self.log(f"🔍 找到Tkinter根窗口: {tk._default_root}")

            # 递归查找所有Treeview控件
            treeviews = []
            self._find_treeviews_recursive(tk._default_root, treeviews)

            # 检查每个Treeview是否包含错误信息
            all_errors = []
            for i, treeview in enumerate(treeviews):
                try:
                    children = treeview.get_children()

                    if len(children) > 0:
                            # 提取错误信息
                        for item in children:
                                item_values = treeview.item(item)['values']
                                if len(item_values) >= 4:
                                    # 尝试不同的格式
                                    if 'ERROR' in str(item_values[0]).upper():
                                        # 格式1: (severity, file, line, code, message, ...)
                                        all_errors.append({
                                            'severity': item_values[0],
                                            'file': item_values[1],
                                            'line': item_values[2],
                                            'code': item_values[3],
                                            'message': item_values[4] if len(item_values) > 4 else ''
                                        })
                                    elif len(item_values) > 1 and 'ERROR' in str(item_values[1]).upper():
                                        # 格式2: (time, severity, file, line, code, message)
                                        all_errors.append({
                                            'severity': item_values[1],
                                            'file': item_values[2] if len(item_values) > 2 else '',
                                            'line': item_values[3] if len(item_values) > 3 else '',
                                            'code': item_values[4] if len(item_values) > 4 else '',
                                            'message': item_values[5] if len(item_values) > 5 else ''
                                        })

                except Exception as e:
                    self.log(f"⚠️ 检查Treeview {i+1} 时出错: {e}")

            if all_errors:
                # 获取条数限制设置
                error_limit = self._get_error_count_limit()

                # 限制错误数量
                if error_limit > 0 and len(all_errors) > error_limit:
                    limited_errors = all_errors[:error_limit]
                    self.log(f"⚠️ 错误数量 {len(all_errors)} 超过限制 {error_limit}，只发送前 {error_limit} 个")
                else:
                    limited_errors = all_errors
                    self.log(f"✅ 错误数量 {len(all_errors)} 在限制范围内")

                # 格式化错误信息
                error_text = f"发现编译错误，请帮助修复（共{len(limited_errors)}个错误）：\n\n"
                for i, error in enumerate(limited_errors, 1):
                    error_text += f"错误 {i}:\n"
                    error_text += f"  文件: {error['file']}\n"
                    error_text += f"  行号: {error['line']}\n"
                    error_text += f"  错误代码: {error['code']}\n"
                    error_text += f"  错误信息: {error['message']}\n\n"

                self.log(f"✅ 从Treeview控件获取到 {len(all_errors)} 个编译错误，发送 {len(limited_errors)} 个")
                return error_text

            self.log("ℹ️ 没有在Treeview控件中找到错误信息")
            return None

        except Exception as e:
            self.log(f"❌ 简化查找方法失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_error_count_limit(self):
        """从配置文件获取错误条数限制设置"""
        try:
            import json
            import os

            # 获取配置文件路径
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 获取编译错误配置
                compilation_errors = config.get('compilation_errors', {})
                max_errors = compilation_errors.get('max_errors_to_send', 5)

                self.log(f"📊 从配置文件获取到错误条数限制: {max_errors}")
                return max_errors
            else:
                self.log(f"⚠️ 配置文件不存在: {config_path}")

        except Exception as e:
            self.log(f"❌ 读取配置文件失败: {e}")

        # 如果读取配置失败，返回默认值
        self.log("📊 使用默认错误条数限制: 5")
        return 5

    def _find_treeviews_recursive(self, widget, treeviews):
        """递归查找所有Treeview控件"""
        try:
            import tkinter as tk
            from tkinter import ttk

            # 检查当前控件是否是Treeview
            if isinstance(widget, ttk.Treeview):
                treeviews.append(widget)

            # 递归检查子控件
            try:
                children = widget.winfo_children()
                for child in children:
                    self._find_treeviews_recursive(child, treeviews)
            except:
                pass

        except Exception:
            pass

    def _extract_errors_from_error_manager(self, error_manager):
        """从ErrorManager实例中提取错误信息"""
        try:
            all_errors = []

            # 提取后端错误
            if error_manager.backend_error_tree is not None:
                backend_children = error_manager.backend_error_tree.get_children()
                self.log(f"🔍 backend_error_tree中有 {len(backend_children)} 个错误项")

                for item in backend_children:
                    values = error_manager.backend_error_tree.item(item)['values']
                    if len(values) >= 5:
                        # ErrorManager格式: (severity, file_name, line, code, message, timestamp)
                        if values[0].upper() == 'ERROR':  # 只获取错误，不获取警告
                            all_errors.append({
                                'type': '后端',
                                'severity': values[0],
                                'file': values[1],
                                'line': values[2],
                                'code': values[3],
                                'message': values[4]
                            })

            # 提取前端错误
            if error_manager.frontend_error_tree is not None:
                frontend_children = error_manager.frontend_error_tree.get_children()
                self.log(f"🔍 frontend_error_tree中有 {len(frontend_children)} 个错误项")

                for item in frontend_children:
                    values = error_manager.frontend_error_tree.item(item)['values']
                    if len(values) >= 5:
                        # ErrorManager格式: (severity, file_name, line, code, message, timestamp)
                        if values[0].upper() == 'ERROR':  # 只获取错误，不获取警告
                            all_errors.append({
                                'type': '前端',
                                'severity': values[0],
                                'file': values[1],
                                'line': values[2],
                                'code': values[3],
                                'message': values[4]
                            })

            if all_errors:
                # 格式化错误信息
                error_text = "发现编译错误，请帮助修复：\n\n"
                for i, error in enumerate(all_errors, 1):
                    error_text += f"错误 {i} ({error['type']}):\n"
                    error_text += f"  文件: {error['file']}\n"
                    error_text += f"  行号: {error['line']}\n"
                    error_text += f"  错误代码: {error['code']}\n"
                    error_text += f"  错误信息: {error['message']}\n\n"

                self.log(f"✅ 从ErrorManager获取到 {len(all_errors)} 个编译错误")
                return error_text

            return None

        except Exception as e:
            self.log(f"❌ 从ErrorManager提取错误失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_errors_from_ui_directly(self):
        """直接从UI控件获取错误信息"""
        try:
            self.log("🔍 方法1: 通过tkinter查找主窗口...")
            # 方法1: 通过tkinter查找主窗口
            import tkinter as tk

            self.log(f"🔍 tkinter默认根窗口: {tk._default_root}")

            # 获取所有tkinter窗口
            if tk._default_root:
                children = tk._default_root.winfo_children()
                self.log(f"🔍 找到 {len(children)} 个子窗口")

                for i, widget in enumerate(children):
                    self.log(f"🔍 窗口 {i}: {widget.__class__.__name__}")
                    if hasattr(widget, 'winfo_class'):
                        self.log(f"  - 窗口类: {widget.winfo_class()}")

                    # 查找错误管理器
                    error_manager = self._find_error_manager_in_widget(widget)
                    if error_manager:
                        self.log("✅ 找到错误管理器!")
                        return self._extract_errors_from_treeview(error_manager)
            else:
                self.log("❌ tkinter默认根窗口不存在")

            self.log("🔍 方法2: 通过模块查找...")
            # 方法2: 通过模块查找ErrorManager实例
            import sys
            found_instances = []

            # 创建模块列表的副本以避免字典大小变化错误
            modules_list = list(sys.modules.items())

            # 查找所有模块中的ErrorManager实例
            for module_name, module in modules_list:
                try:
                    # 检查模块的所有属性
                    for attr_name in dir(module):
                        if not attr_name.startswith('_'):
                            try:
                                attr_value = getattr(module, attr_name)
                                # 检查是否是ErrorManager实例
                                if hasattr(attr_value, 'backend_error_tree') and hasattr(attr_value, 'frontend_error_tree'):
                                    found_instances.append((module_name, attr_name, attr_value))
                                    self.log(f"🔍 找到ErrorManager实例: {module_name}.{attr_name}")

                                    # 检查错误树状态
                                    backend_tree = attr_value.backend_error_tree
                                    frontend_tree = attr_value.frontend_error_tree

                                    self.log(f"🔍 backend_error_tree: {type(backend_tree)} (None: {backend_tree is None})")
                                    self.log(f"🔍 frontend_error_tree: {type(frontend_tree)} (None: {frontend_tree is None})")

                                    # 如果错误树不为None，尝试提取错误
                                    if backend_tree is not None or frontend_tree is not None:
                                        self.log("✅ 找到有效的错误树!")
                                        return self._extract_errors_from_treeview(attr_value)
                            except:
                                pass
                except:
                    pass

            if found_instances:
                self.log(f"🔍 总共找到 {len(found_instances)} 个ErrorManager实例，但错误树都为None")
            else:
                self.log("❌ 未找到ErrorManager实例")

            # 方法3: 查找主UI实例
            self.log("🔍 方法3: 查找主UI实例...")
            for module_name, module in modules_list:
                try:
                    for attr_name in dir(module):
                        if not attr_name.startswith('_'):
                            try:
                                attr_value = getattr(module, attr_name)
                                # 查找包含development_steps的对象
                                if hasattr(attr_value, 'development_steps') and hasattr(attr_value.development_steps, 'error_manager'):
                                    self.log(f"🔍 找到主UI实例: {module_name}.{attr_name}")
                                    error_manager = attr_value.development_steps.error_manager

                                    backend_tree = error_manager.backend_error_tree
                                    frontend_tree = error_manager.frontend_error_tree

                                    self.log(f"🔍 通过主UI找到backend_error_tree: {type(backend_tree)} (None: {backend_tree is None})")
                                    self.log(f"🔍 通过主UI找到frontend_error_tree: {type(frontend_tree)} (None: {frontend_tree is None})")

                                    if backend_tree is not None or frontend_tree is not None:
                                        self.log("✅ 通过主UI找到有效的错误树!")
                                        return self._extract_errors_from_treeview(error_manager)
                            except:
                                pass
                except:
                    pass

            self.log("🔍 方法3: 通过全局变量查找...")
            # 方法3: 查找全局变量
            import builtins
            if hasattr(builtins, '__dict__'):
                for name, obj in builtins.__dict__.items():
                    if hasattr(obj, 'error_manager'):
                        self.log(f"🔍 在全局变量中找到: {name}")
                        return self._extract_errors_from_treeview(obj.error_manager)

            return None

        except Exception as e:
            self.log(f"❌ 直接访问UI失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return None

    def _find_error_manager_in_widget(self, widget):
        """在widget中查找错误管理器"""
        try:
            # 递归查找包含error_manager的对象
            if hasattr(widget, 'error_manager'):
                self.log(f"✅ 在 {widget.__class__.__name__} 中找到error_manager")
                return widget.error_manager

            # 检查widget的所有属性
            for attr_name in dir(widget):
                if not attr_name.startswith('_'):
                    try:
                        attr_value = getattr(widget, attr_name)
                        if hasattr(attr_value, 'backend_error_tree') or hasattr(attr_value, 'frontend_error_tree'):
                            self.log(f"✅ 在 {widget.__class__.__name__}.{attr_name} 中找到错误树")
                            return attr_value
                    except:
                        pass

            # 查找子控件
            try:
                children = widget.winfo_children()
                for child in children:
                    result = self._find_error_manager_in_widget(child)
                    if result:
                        return result
            except:
                pass

            return None
        except Exception as e:
            self.log(f"⚠️ 查找错误管理器时出错: {e}")
            return None

    def _extract_errors_from_treeview(self, error_manager):
        """从TreeView控件中提取错误信息"""
        try:
            error_messages = []

            # 获取后端编译错误
            if hasattr(error_manager, 'backend_error_tree'):
                backend_errors = []
                for item in error_manager.backend_error_tree.get_children():
                    values = error_manager.backend_error_tree.item(item)['values']
                    if len(values) >= 5 and values[0].upper() == 'ERROR':
                        backend_errors.append({
                            'file': values[1],
                            'line': values[2],
                            'code': values[3],
                            'message': values[4]
                        })

                if backend_errors:
                    error_text = "=== C# 后端编译错误 ===\n\n"
                    for i, error in enumerate(backend_errors, 1):
                        error_text += f"错误 {i}:\n"
                        error_text += f"  文件: {error['file']}\n"
                        error_text += f"  行号: {error['line']}\n"
                        error_text += f"  错误代码: {error['code']}\n"
                        error_text += f"  错误信息: {error['message']}\n\n"
                    error_messages.append(error_text)
                    self.log(f"✅ 从UI获取到 {len(backend_errors)} 个后端编译错误")

            # 获取前端编译错误
            if hasattr(error_manager, 'frontend_error_tree'):
                frontend_errors = []
                for item in error_manager.frontend_error_tree.get_children():
                    values = error_manager.frontend_error_tree.item(item)['values']
                    if len(values) >= 5 and values[0].upper() == 'ERROR':
                        frontend_errors.append({
                            'file': values[1],
                            'line': values[2],
                            'code': values[3],
                            'message': values[4]
                        })

                if frontend_errors:
                    error_text = "=== 前端编译错误 ===\n\n"
                    for i, error in enumerate(frontend_errors, 1):
                        error_text += f"错误 {i}:\n"
                        error_text += f"  文件: {error['file']}\n"
                        error_text += f"  行号: {error['line']}\n"
                        error_text += f"  错误代码: {error['code']}\n"
                        error_text += f"  错误信息: {error['message']}\n\n"
                    error_messages.append(error_text)
                    self.log(f"✅ 从UI获取到 {len(frontend_errors)} 个前端编译错误")

            if error_messages:
                return "\n".join(error_messages)

            return None

        except Exception as e:
            self.log(f"❌ 从TreeView提取错误失败: {e}")
            return None







    def run_workflow_with_auto_monitor(self, workflow_func, monitor_interval: float = 2.0):
        """运行工作流程并同时启动自动监控

        Args:
            workflow_func: 要执行的工作流程函数
            monitor_interval: 监控间隔（秒）

        Returns:
            工作流程的返回值
        """
        try:
            # 启动自动监控
            self.start_auto_click_monitor(monitor_interval)

            # 执行工作流程
            self.log("🎯 开始执行工作流程...")
            result = workflow_func()

            return result

        finally:
            # 停止自动监控
            self.stop_auto_click_monitor()

    def run_my_workflow(self):
        """运行我的工作流"""
        self.log("开始执行我的工作流...")
        
        try:
            # 步骤1
            self.open_colipot_chat()
            self.wait(1)
            
            # 步骤2
            self.my_custom_method_2()
            self.wait(1)
            
            # 步骤3
            self.my_custom_method_3()
            
            self.log("✅ 工作流执行完成！")
            
        except Exception as e:
            self.log(f"❌ 工作流执行出错: {e}")


def main():
    """主函数"""
    print("🚀 我的VSCode自动化脚本")
    print("="*50)
    
    # 创建脚本实例
    script = CopilotVsCodeScript()
    
    try:
        # 连接到VSCode
        if not script.connect():
            return
        
        # 运行您的工作流
        script.open_colipot_chat()
        script.wait(1)
        script.confirmIsNewSession(False)
        script.wait(1)
        script.clearText()
        script.wait(1)
        script.inputTextAdvanced('请实现图片的html排版')
        script.wait(1)
        script.copy_and_paste_image(r'D:\Projects\ProjectManagement\Python\NewVersion\dom\image.png')
        script.wait(1)
        script.clickMsgBtn()
 
        
    finally:
        # 断开连接
        script.disconnect()


if __name__ == "__main__":
    main()
