<template>
  <div class="provider-distribution-chart">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  data: any[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.data) return

  const option = {
    title: {
      text: 'AI提供商使用分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      data: props.data.map(item => item.name)
    },
    series: [
      {
        name: '使用量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getColor(index)
          }
        }))
      }
    ]
  }

  chartInstance.setOption(option)
}

const getColor = (index: number) => {
  const colors = [
    '#409EFF',
    '#67C23A', 
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#C0C4CC',
    '#606266',
    '#303133'
  ]
  return colors[index % colors.length]
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  window.addEventListener('resize', resizeChart)
})

watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && chartContainer.value && !chartInstance) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style scoped>
.provider-distribution-chart {
  width: 100%;
  height: 400px;
}

.loading-container {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
