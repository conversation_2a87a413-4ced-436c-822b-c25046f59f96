@echo off
echo ========================================
echo 创建IIS部署包
echo ========================================

cd /d "%~dp0"

set PUBLISH_DIR=ProjectManagement.API\bin\Release\net8.0\publish
set PACKAGE_NAME=ProjectManagement-API-Deploy-%date:~0,4%%date:~5,2%%date:~8,2%.zip

echo 检查发布目录...
if not exist "%PUBLISH_DIR%" (
    echo ❌ 发布目录不存在，请先运行发布命令
    echo 运行: dotnet publish -c Release
    pause
    exit /b 1
)

echo 正在创建部署包...

:: 使用PowerShell创建ZIP文件
powershell -Command "Compress-Archive -Path '%PUBLISH_DIR%\*' -DestinationPath '%PACKAGE_NAME%' -Force"

if %ERRORLEVEL% EQ 0 (
    echo ✅ 部署包创建成功: %PACKAGE_NAME%
    echo.
    echo 📦 包含文件:
    powershell -Command "Get-ChildItem '%PUBLISH_DIR%' | Select-Object Name, Length | Format-Table -AutoSize"
    echo.
    echo 📋 下一步操作:
    echo 1. 将 %PACKAGE_NAME% 传输到远程IIS服务器
    echo 2. 在远程服务器上解压到IIS网站目录
    echo 3. 按照 IIS部署说明.md 进行配置
    echo 4. 重启IIS应用程序池
) else (
    echo ❌ 创建部署包失败
)

echo.
pause
