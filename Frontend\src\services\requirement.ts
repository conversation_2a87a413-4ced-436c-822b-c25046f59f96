import { ApiService, AIApiService } from './api'
import type {
  RequirementConversation,
  RequirementDocument,
  PagedResult
} from '@/types'

export class RequirementService {
  /**
   * 获取需求对话列表
   */
  static async getConversations(projectId: number): Promise<RequirementConversation[]> {
    return ApiService.get<RequirementConversation[]>(`/api/requirements/projects/${projectId}/conversations`)
  }

  /**
   * 创建需求对话
   */
  static async createConversation(projectId: number, title: string): Promise<RequirementConversation> {
    return ApiService.post<RequirementConversation>(`/api/requirements/projects/${projectId}/conversations`, {
      title
    })
  }

  /**
   * 发送对话消息（使用AI专用API，超时时间更长）
   */
  static async sendMessage(conversationId: string, message: string, projectId?: number, aiProviderConfigId?: number): Promise<{
    messageId: string
    conversationId: string
    userMessage: string
    aiResponse: string
    timestamp: string
  }> {
    return AIApiService.post(`/api/requirements/conversations/${conversationId}/messages`, {
      message,
      projectId,
      AIProviderConfigId: aiProviderConfigId
    })
  }

  /**
   * 获取对话历史
   */
  static async getConversationHistory(conversationId: string): Promise<{
    userMessage: string
    aiResponse: string
    timestamp: string
  }[]> {
    return ApiService.get(`/api/requirements/conversations/${conversationId}/messages`)
  }

  /**
   * 分析需求并生成规格书（使用AI专用API）
   */
  static async analyzeRequirements(data: {
    projectId: number
    requirements: string
    conversationId?: string
  }): Promise<{
    taskId: string
    taskType: string
    status: string
    message: string
    estimatedDuration: string
  }> {
    return AIApiService.post('/api/ai/analyze-requirements', data)
  }

  /**
   * 获取AI任务状态
   */
  static async getTaskStatus(taskId: string): Promise<{
    taskId: string
    status: string
    progress: number
    message: string
    startTime: string
    estimatedCompletion?: string
  }> {
    return ApiService.get(`/api/ai/tasks/${taskId}/status`)
  }

  /**
   * 获取AI任务结果
   */
  static async getTaskResult(taskId: string): Promise<{
    taskId: string
    status: string
    result: any
    completedAt: string
  }> {
    return ApiService.get(`/api/ai/tasks/${taskId}/result`)
  }

  /**
   * 获取所有需求文档列表
   */
  static async getAllRequirementDocuments(pageNumber: number = 1, pageSize: number = 50, projectId?: number, status?: string): Promise<PagedResult<RequirementDocument>> {
    const params = new URLSearchParams({
      pageNumber: pageNumber.toString(),
      pageSize: pageSize.toString()
    })

    if (projectId) {
      params.append('projectId', projectId.toString())
    }

    if (status) {
      params.append('status', status)
    }

    return ApiService.get<PagedResult<RequirementDocument>>(`/api/requirements/documents?${params.toString()}`)
  }

  /**
   * 获取需求文档列表（按项目）
   */
  static async getRequirementDocuments(projectId: number, pageNumber: number = 1, pageSize: number = 50): Promise<PagedResult<RequirementDocument>> {
    return ApiService.get<PagedResult<RequirementDocument>>(`/api/requirements/projects/${projectId}/documents?pageNumber=${pageNumber}&pageSize=${pageSize}`)
  }

  /**
   * 获取需求文档详情
   */
  static async getRequirementDocument(documentId: number): Promise<RequirementDocument> {
    return ApiService.get<RequirementDocument>(`/api/requirements/${documentId}`)
  }

  /**
   * 更新需求文档
   */
  static async updateRequirementDocument(id: number, data: {
    title?: string
    content?: string
    status?: string
    functionalRequirements?: string
    nonFunctionalRequirements?: string
    userStories?: string
    acceptanceCriteria?: string
  }): Promise<RequirementDocument> {
    return ApiService.put<RequirementDocument>(`/api/requirements/${id}`, data)
  }

  /**
   * 创建需求文档
   */
  static async createRequirementDocument(data: {
    projectId: number
    title: string
    content: string
    functionalRequirements?: string
    nonFunctionalRequirements?: string
    userStories?: string
    acceptanceCriteria?: string
  }): Promise<RequirementDocument> {
    return ApiService.post<RequirementDocument>('/api/requirements', data)
  }

  /**
   * 从AI对话保存需求文档
   */
  static async saveRequirementFromChat(data: {
    projectId: number
    title: string
    aiContent: string
    userMessage: string
    conversationId: string
    functionalRequirements?: string
    nonFunctionalRequirements?: string
    userStories?: string
    acceptanceCriteria?: string
    aiProviderConfigId?: number
  }): Promise<RequirementDocument> {
    return ApiService.post<RequirementDocument>('/api/requirements/save-from-chat', {
      ...data,
      AIProviderConfigId: data.aiProviderConfigId
    })
  }



  /**
   * 删除需求文档
   */
  static async deleteRequirementDocument(documentId: number): Promise<void> {
    return ApiService.delete<void>(`/api/requirements/${documentId}`)
  }

  /**
   * 生成ER图
   */
  static async generateERDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    specification: string
  }): Promise<{
    taskId: string
    taskType: string
    status: string
    message: string
  }> {
    return ApiService.post('/api/ai/generate-er-diagram', data)
  }

  /**
   * 生成Context图
   */
  static async generateContextDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    specification: string
  }): Promise<{
    taskId: string
    taskType: string
    status: string
    message: string
  }> {
    return ApiService.post('/api/ai/generate-context-diagram', data)
  }

  /**
   * 获取AI模型列表
   */
  static async getAvailableModels(): Promise<{
    provider: string
    model: string
    description: string
    isAvailable: boolean
  }[]> {
    return ApiService.get('/api/ai/models')
  }

  /**
   * 获取AI使用统计
   */
  static async getUsageStatistics(): Promise<{
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    totalTokensUsed: number
    estimatedCost: number
    lastUsed: string
  }> {
    return ApiService.get('/api/ai/usage-statistics')
  }

  /**
   * 导出需求文档
   */
  static async exportRequirementDocument(documentId: number, format: 'pdf' | 'word' | 'markdown'): Promise<void> {
    return ApiService.download(`/api/requirements/${documentId}/export?format=${format}`,
      `requirement-document-${documentId}.${format}`)
  }

  /**
   * 批量导入需求
   */
  static async importRequirements(projectId: number, file: File): Promise<{
    importedCount: number
    errors: string[]
  }> {
    return ApiService.upload(`/api/requirements/projects/${projectId}/import`, file)
  }

  /**
   * 需求优先级排序
   */
  static async prioritizeRequirements(projectId: number, requirements: {
    id: string
    priority: number
  }[]): Promise<void> {
    return ApiService.post(`/api/requirements/projects/${projectId}/prioritize`, {
      requirements
    })
  }

  /**
   * 需求影响分析
   */
  static async analyzeRequirementImpact(requirementId: string): Promise<{
    affectedComponents: string[]
    estimatedEffort: number
    riskLevel: string
    recommendations: string[]
  }> {
    return ApiService.get(`/api/requirements/${requirementId}/impact-analysis`)
  }

  /**
   * 需求可行性评估
   */
  static async assessFeasibility(requirements: string): Promise<{
    feasibilityScore: number
    complexityScore: number
    confidenceScore: number
    risks: {
      description: string
      impact: string
      probability: string
      mitigation: string
    }[]
    recommendations: string[]
  }> {
    return ApiService.post('/api/ai/assess-feasibility', {
      requirements
    })
  }


}
