// 开发步骤相关类型定义

export interface DevelopmentStep {
  id: number
  stepName: string
  stepDescription?: string
  stepType: StepType
  priority: Priority
  status: StepStatus
  progress: number
  estimatedHours?: number
  actualHours?: number
  technologyStack?: string
  fileType?: string
  filePath?: string
  componentType?: ComponentType
  aiPrompt?: string
  stepOrder: number
  stepGroup?: string
  stepLevel: number
  parentStepId?: number
  projectId: number
  requirementDocumentId?: number
  referenceImages?: string // JSON格式的图片路径数组
  startTime?: string
  endTime?: string
  completedTime?: string
  createdTime: string
  children?: DevelopmentStep[]
  hasChildren?: boolean
  templateSequences?: StepTemplateSequence[]
}

export interface StepTemplateSequence {
  id: number
  name: string
  description?: string
  category?: string
  appliedTime: string
  stepCount: number
  status?: string
  progress?: number
  executionStartTime?: string
  executionEndTime?: string
  executionResult?: string
  errorMessage?: string
}

export interface StepDependency {
  id: number
  stepId: number
  dependsOnStepId: number
  dependencyType: DependencyType
  isRequired: boolean
  description?: string
}

export interface StepExecutionHistory {
  id: number
  stepId: number
  executionId: string
  executionStartTime: string
  executionEndTime?: string
  executionDuration?: number
  executionStatus: ExecutionStatus
  executionResult?: ExecutionResult
  aiProvider?: string
  aiModel?: string
  promptUsed?: string
  generatedCode?: string
  outputFiles?: string
  errorMessage?: string
  executionLog?: string
  executorType: ExecutorType
}

// 枚举类型
export type StepType = 'Development' | 'Testing' | 'Documentation' | 'Deployment' | 'Review'

export type Priority = 'Low' | 'Medium' | 'High' | 'Critical'

export type StepStatus = 'Pending' | 'InProgress' | 'Completed' | 'Failed' | 'Blocked'

export type ComponentType = 'Frontend' | 'Backend' | 'Database' | 'API'

export type DependencyType = 'Sequential' | 'Parallel' | 'Conditional' | 'Optional'

export type ExecutionStatus = 'Running' | 'Completed' | 'Failed' | 'Cancelled'

export type ExecutionResult = 'Success' | 'Failed' | 'Partial' | 'Timeout'

export type ExecutorType = 'Manual' | 'Automated' | 'VSCode' | 'API'

// 分解选项
export interface DecompositionOptions {
  technologyStack?: string
  granularity?: 'Coarse' | 'Medium' | 'Fine'
  includeTestSteps?: boolean
  includeDocumentationSteps?: boolean
  autoAnalyzeDependencies?: boolean
  maxStepCount?: number
  aiProvider?: number | string // 支持配置ID(number)或提供商名称(string)
  aiModel?: string
}

// 分解请求
export interface DecomposeRequirementRequest extends DecompositionOptions {
  // 继承分解选项的所有属性
}

export interface DecomposeContentRequest extends DecompositionOptions {
  requirementContent: string
}

// 分解结果
export interface DecompositionResult {
  success: boolean
  message?: string
  data?: {
    steps: DevelopmentStep[]
    dependencies: StepDependency[]
    statistics: DecompositionStatistics
    aiAnalysis?: string
  }
  isPreview?: boolean // 是否为预览模式（未保存到数据库）
}

export interface DecompositionStatistics {
  totalSteps: number
  developmentSteps: number
  testSteps: number
  documentationSteps: number
  dependencyCount: number
  estimatedTotalHours: number
  maxDepth: number
  technologyStacks: string[]
}

// 项目步骤统计
export interface ProjectStepStatistics {
  projectId: number
  projectName: string
  totalSteps: number
  pendingSteps: number
  inProgressSteps: number
  completedSteps: number
  failedSteps: number
  blockedSteps: number
  averageProgress: number
  totalEstimatedHours: number
  totalActualHours: number
  technologyStackCount: number
  firstStepCreated?: string
  lastStepUpdated?: string
}

// 步骤执行进度
export interface StepExecutionProgress {
  projectId: number
  totalSteps: number
  pendingSteps: number
  inProgressSteps: number
  completedSteps: number
  failedSteps: number
  blockedSteps: number
  overallProgress: number
  progressByType: StepTypeProgress[]
}

export interface StepTypeProgress {
  stepType: string
  total: number
  completed: number
  progress: number
}

// 步骤复杂度分析
export interface StepComplexityAnalysis {
  stepId: number
  stepName: string
  complexityScore: number // 1-10
  estimatedHours: number
  complexityFactors?: ComplexityFactor[]
  recommendations?: ComplexityRecommendation[]
  riskLevel: 'Low' | 'Medium' | 'High'
  analyzedAt?: string
}

export interface ComplexityFactor {
  name: string
  description: string
  score: number // 1-10
  impact: 'Low' | 'Medium' | 'High'
}

export interface ComplexityRecommendation {
  title: string
  description: string
  priority?: 'Low' | 'Medium' | 'High'
}

// 分页结果
export interface PagedStepsResult {
  items: DevelopmentStep[]
  totalCount: number
  pageIndex: number
  pageSize: number
  totalPages: number
}

// 步骤更新数据
export interface StepUpdateData {
  stepName?: string
  stepDescription?: string
  priority?: Priority
  status?: StepStatus
  estimatedHours?: number
  actualHours?: number
  technologyStack?: string
  componentType?: string
  filePath?: string
  aiPrompt?: string
  progress?: number
  referenceImages?: string // JSON格式的图片路径数组
}

// 步骤排序信息
export interface StepOrderInfo {
  stepId: number
  order: number
  parentStepId?: number
}

// 依赖验证结果
export interface DependencyValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  circularDependencies: CircularDependency[]
}

export interface CircularDependency {
  stepIds: number[]
  stepNames: string[]
  description: string
}

// 步骤筛选条件
export interface StepFilterOptions {
  status?: StepStatus
  priority?: Priority
  stepType?: StepType
  technologyStack?: string
  componentType?: ComponentType
  keyword?: string
  parentOnly?: boolean  // 是否只获取父级步骤
}

// 步骤排序选项
export interface StepSortOptions {
  field: 'stepOrder' | 'priority' | 'estimatedHours' | 'createdTime' | 'progress'
  direction: 'asc' | 'desc'
}

// 批量操作选项
export interface BatchOperationOptions {
  stepIds: number[]
  operation: 'updateStatus' | 'updatePriority' | 'delete' | 'reorder'
  value?: string | number
}

// 步骤模板
export interface StepTemplate {
  id: number
  name: string
  description: string
  stepType: StepType
  technologyStack: string
  componentType: ComponentType
  estimatedHours: number
  aiPrompt: string
  tags: string[]
}

// 技术栈配置
export interface TechnologyStackConfig {
  name: string
  displayName: string
  description: string
  defaultStepTypes: StepType[]
  defaultComponentTypes: ComponentType[]
  fileExtensions: string[]
  frameworks: string[]
  tools: string[]
}

// 常用的技术栈配置
export const TECHNOLOGY_STACKS: TechnologyStackConfig[] = [
  {
    name: 'vue',
    displayName: 'Vue.js',
    description: 'Vue.js + TypeScript + Element Plus',
    defaultStepTypes: ['Development', 'Testing'],
    defaultComponentTypes: ['Frontend'],
    fileExtensions: ['.vue', '.ts', '.js'],
    frameworks: ['Vue 3', 'TypeScript', 'Element Plus', 'Vite'],
    tools: ['ESLint', 'Prettier', 'Jest']
  },
  {
    name: 'dotnet',
    displayName: '.NET Core',
    description: 'C# .NET Core Web API',
    defaultStepTypes: ['Development', 'Testing'],
    defaultComponentTypes: ['Backend', 'API'],
    fileExtensions: ['.cs', '.csproj'],
    frameworks: ['.NET 8', 'ASP.NET Core', 'Entity Framework'],
    tools: ['xUnit', 'Swagger', 'AutoMapper']
  },
  {
    name: 'python',
    displayName: 'Python',
    description: 'Python FastAPI/Django',
    defaultStepTypes: ['Development', 'Testing'],
    defaultComponentTypes: ['Backend', 'API'],
    fileExtensions: ['.py'],
    frameworks: ['FastAPI', 'Django', 'SQLAlchemy'],
    tools: ['pytest', 'black', 'mypy']
  },
  {
    name: 'database',
    displayName: 'Database',
    description: 'SQL Server/MySQL/PostgreSQL',
    defaultStepTypes: ['Development'],
    defaultComponentTypes: ['Database'],
    fileExtensions: ['.sql'],
    frameworks: ['SQL Server', 'MySQL', 'PostgreSQL'],
    tools: ['SSMS', 'DataGrip', 'pgAdmin']
  }
]

// 步骤类型配置
export const STEP_TYPE_CONFIG = {
  Development: {
    color: '#1890ff',
    icon: '💻',
    description: '开发任务'
  },
  Testing: {
    color: '#52c41a',
    icon: '🧪',
    description: '测试任务'
  },
  Documentation: {
    color: '#722ed1',
    icon: '📄',
    description: '文档任务'
  },
  Deployment: {
    color: '#fa8c16',
    icon: '🚀',
    description: '部署任务'
  },
  Review: {
    color: '#eb2f96',
    icon: '🔍',
    description: '审查任务'
  }
}

// 优先级配置
export const PRIORITY_CONFIG = {
  Low: {
    color: '#52c41a',
    icon: '⬇️',
    description: '低优先级'
  },
  Medium: {
    color: '#1890ff',
    icon: '➡️',
    description: '中优先级'
  },
  High: {
    color: '#fa8c16',
    icon: '⬆️',
    description: '高优先级'
  },
  Critical: {
    color: '#f5222d',
    icon: '🔥',
    description: '紧急'
  }
}

// 状态配置
export const STATUS_CONFIG = {
  Pending: {
    color: '#d9d9d9',
    icon: '⏳',
    description: '待处理'
  },
  InProgress: {
    color: '#1890ff',
    icon: '🔄',
    description: '进行中'
  },
  Completed: {
    color: '#52c41a',
    icon: '✅',
    description: '已完成'
  },
  Failed: {
    color: '#f5222d',
    icon: '❌',
    description: '失败'
  },
  Blocked: {
    color: '#fa8c16',
    icon: '🚫',
    description: '阻塞'
  }
}
