using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Models;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 编译错误API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CompilationErrorsController : ControllerBase
    {
        private readonly ICompilationErrorService _compilationErrorService;
        private readonly ILogger<CompilationErrorsController> _logger;

        public CompilationErrorsController(
            ICompilationErrorService compilationErrorService,
            ILogger<CompilationErrorsController> logger)
        {
            _compilationErrorService = compilationErrorService;
            _logger = logger;
        }

        /// <summary>
        /// 获取编译错误列表
        /// </summary>
        /// <param name="projectType">项目类型 (Backend/Frontend)</param>
        /// <param name="severity">严重程度 (Error/Warning/Info)</param>
        /// <param name="projectId">项目ID</param>
        /// <param name="sessionId">编译会话ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="errorsOnly">是否只获取错误</param>
        /// <returns>编译错误列表</returns>
        [HttpGet]
        public async Task<ActionResult<Core.Interfaces.PagedResult<CompilationErrorDto>>> GetCompilationErrors(
            [FromQuery] string? projectType = null,
            [FromQuery] string? severity = null,
            [FromQuery] int? projectId = null,
            [FromQuery] string? sessionId = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] bool errorsOnly = false)
        {
            try
            {
                var query = new CompilationErrorQueryDto
                {
                    ProjectType = projectType,
                    Severity = severity,
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    Page = page,
                    PageSize = Math.Min(pageSize, 200), // 限制最大页面大小
                    ErrorsOnly = errorsOnly
                };

                var result = await _compilationErrorService.GetCompilationErrorsAsync(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取编译错误列表失败");
                return StatusCode(500, new { message = "获取编译错误列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取编译错误详情
        /// </summary>
        /// <param name="id">错误ID</param>
        /// <returns>编译错误详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<CompilationErrorDto>> GetCompilationError(int id)
        {
            try
            {
                var error = await _compilationErrorService.GetCompilationErrorByIdAsync(id);
                if (error == null)
                {
                    return NotFound(new { message = "编译错误不存在" });
                }

                return Ok(error);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取编译错误详情失败，ID: {Id}", id);
                return StatusCode(500, new { message = "获取编译错误详情失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取编译错误统计信息
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="projectId">项目ID</param>
        /// <returns>统计信息</returns>
        [HttpGet("stats")]
        public async Task<ActionResult<CompilationErrorStatsDto>> GetCompilationErrorStats(
            [FromQuery] string? projectType = null,
            [FromQuery] int? projectId = null)
        {
            try
            {
                var stats = await _compilationErrorService.GetCompilationErrorStatsAsync(projectType, projectId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取编译错误统计失败");
                return StatusCode(500, new { message = "获取编译错误统计失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清空编译错误
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="projectId">项目ID</param>
        /// <param name="sessionId">会话ID</param>
        /// <returns>清空的错误数量</returns>
        [HttpDelete("clear")]
        public async Task<ActionResult<object>> ClearCompilationErrors(
            [FromQuery] string? projectType = null,
            [FromQuery] int? projectId = null,
            [FromQuery] string? sessionId = null)
        {
            try
            {
                var count = await _compilationErrorService.ClearCompilationErrorsAsync(projectType, projectId, sessionId);
                return Ok(new { message = "编译错误已清空", clearedCount = count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空编译错误失败");
                return StatusCode(500, new { message = "清空编译错误失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 批量添加编译错误
        /// </summary>
        /// <param name="errors">错误列表</param>
        /// <returns>添加的错误数量</returns>
        [HttpPost("batch")]
        public async Task<ActionResult<object>> AddCompilationErrors([FromBody] List<CompilationErrorDto> errors)
        {
            try
            {
                if (errors == null || !errors.Any())
                {
                    return BadRequest(new { message = "错误列表不能为空" });
                }

                var count = await _compilationErrorService.AddCompilationErrorsAsync(errors);
                return Ok(new { message = "编译错误已添加", addedCount = count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量添加编译错误失败");
                return StatusCode(500, new { message = "批量添加编译错误失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 删除编译错误
        /// </summary>
        /// <param name="id">错误ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<object>> DeleteCompilationError(int id)
        {
            try
            {
                var success = await _compilationErrorService.DeleteCompilationErrorAsync(id);
                if (!success)
                {
                    return NotFound(new { message = "编译错误不存在" });
                }

                return Ok(new { message = "编译错误已删除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除编译错误失败，ID: {Id}", id);
                return StatusCode(500, new { message = "删除编译错误失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取最新的编译会话ID列表
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="limit">返回数量限制</param>
        /// <returns>会话ID列表</returns>
        [HttpGet("sessions")]
        public async Task<ActionResult<List<string>>> GetRecentCompilationSessions(
            [FromQuery] string? projectType = null,
            [FromQuery] int limit = 10)
        {
            try
            {
                var sessions = await _compilationErrorService.GetRecentCompilationSessionsAsync(projectType, Math.Min(limit, 50));
                return Ok(sessions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取编译会话列表失败");
                return StatusCode(500, new { message = "获取编译会话列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 兼容Python客户端的简化API - 获取所有编译错误
        /// </summary>
        /// <returns>所有编译错误</returns>
        [HttpGet("all")]
        public async Task<ActionResult<List<CompilationErrorDto>>> GetAllCompilationErrors()
        {
            try
            {
                var query = new CompilationErrorQueryDto
                {
                    Page = 1,
                    PageSize = 1000 // 返回大量数据，适合Python客户端
                };

                var result = await _compilationErrorService.GetCompilationErrorsAsync(query);
                return Ok(result.Items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有编译错误失败");
                return StatusCode(500, new { message = "获取所有编译错误失败", error = ex.Message });
            }
        }
    }
}
