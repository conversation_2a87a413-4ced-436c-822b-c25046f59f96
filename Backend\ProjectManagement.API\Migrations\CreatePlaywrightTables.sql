-- Playwright测试相关表创建脚本
-- 执行时间：2024-12-19

-- 1. 创建PlaywrightScripts表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightScripts' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PlaywrightScripts] (
        [Id] int IDENTITY(1,1) NOT NULL,
        [Name] nvarchar(100) NOT NULL,
        [Description] nvarchar(500) NULL,
        [Category] nvarchar(50) NOT NULL DEFAULT 'e2e',
        [Browser] nvarchar(20) NOT NULL DEFAULT 'chromium',
        [Code] ntext NULL,
        [ConfigJson] ntext NULL,
        [TagsJson] nvarchar(1000) NULL,
        [Priority] nvarchar(20) NOT NULL DEFAULT 'medium',
        [Status] nvarchar(20) NOT NULL DEFAULT '草稿',
        [ProjectId] int NULL,
        [LastExecutedTime] datetime NULL,
        [ExecutionCount] int NOT NULL DEFAULT 0,
        [SuccessCount] int NOT NULL DEFAULT 0,
        [AvgDuration] int NULL,
        [CreatedBy] int NULL,
        [CreatedTime] datetime NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] int NULL,
        [UpdatedTime] datetime NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedBy] int NULL,
        [DeletedTime] datetime NULL,
        [Version] int NOT NULL DEFAULT 1,
        [Remarks] nvarchar(500) NULL,
        CONSTRAINT [PK_PlaywrightScripts] PRIMARY KEY CLUSTERED ([Id] ASC)
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_PlaywrightScripts_Category] ON [dbo].[PlaywrightScripts] ([Category]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightScripts_Browser] ON [dbo].[PlaywrightScripts] ([Browser]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightScripts_Status] ON [dbo].[PlaywrightScripts] ([Status]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightScripts_ProjectId] ON [dbo].[PlaywrightScripts] ([ProjectId]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightScripts_IsDeleted] ON [dbo].[PlaywrightScripts] ([IsDeleted]);
    
    PRINT 'PlaywrightScripts表创建成功';
END
ELSE
BEGIN
    PRINT 'PlaywrightScripts表已存在';
END

-- 2. 创建PlaywrightExecutions表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightExecutions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PlaywrightExecutions] (
        [Id] int IDENTITY(1,1) NOT NULL,
        [ExecutionId] nvarchar(50) NOT NULL,
        [ScriptId] int NOT NULL,
        [Status] nvarchar(20) NOT NULL DEFAULT 'running',
        [Browser] nvarchar(20) NOT NULL DEFAULT 'chromium',
        [StartTime] datetime NOT NULL,
        [EndTime] datetime NULL,
        [Duration] int NULL,
        [ErrorMessage] ntext NULL,
        [ConfigJson] ntext NULL,
        [StatsJson] ntext NULL,
        [ScreenshotsJson] ntext NULL,
        [VideoPath] nvarchar(500) NULL,
        [Environment] nvarchar(500) NULL,
        [CreatedBy] int NULL,
        [CreatedTime] datetime NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] int NULL,
        [UpdatedTime] datetime NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedBy] int NULL,
        [DeletedTime] datetime NULL,
        [Version] int NOT NULL DEFAULT 1,
        [Remarks] nvarchar(500) NULL,
        CONSTRAINT [PK_PlaywrightExecutions] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_PlaywrightExecutions_PlaywrightScripts] FOREIGN KEY ([ScriptId]) REFERENCES [dbo].[PlaywrightScripts] ([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutions_ExecutionId] ON [dbo].[PlaywrightExecutions] ([ExecutionId]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutions_ScriptId] ON [dbo].[PlaywrightExecutions] ([ScriptId]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutions_Status] ON [dbo].[PlaywrightExecutions] ([Status]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutions_StartTime] ON [dbo].[PlaywrightExecutions] ([StartTime]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutions_IsDeleted] ON [dbo].[PlaywrightExecutions] ([IsDeleted]);
    
    PRINT 'PlaywrightExecutions表创建成功';
END
ELSE
BEGIN
    PRINT 'PlaywrightExecutions表已存在';
END

-- 3. 创建PlaywrightExecutionLogs表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightExecutionLogs' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[PlaywrightExecutionLogs] (
        [Id] int IDENTITY(1,1) NOT NULL,
        [ExecutionId] int NOT NULL,
        [Level] nvarchar(20) NOT NULL DEFAULT 'info',
        [Message] ntext NOT NULL,
        [Step] nvarchar(200) NULL,
        [Timestamp] datetime NOT NULL,
        [ExtraData] ntext NULL,
        [CreatedBy] int NULL,
        [CreatedTime] datetime NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] int NULL,
        [UpdatedTime] datetime NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedBy] int NULL,
        [DeletedTime] datetime NULL,
        [Version] int NOT NULL DEFAULT 1,
        [Remarks] nvarchar(500) NULL,
        CONSTRAINT [PK_PlaywrightExecutionLogs] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_PlaywrightExecutionLogs_PlaywrightExecutions] FOREIGN KEY ([ExecutionId]) REFERENCES [dbo].[PlaywrightExecutions] ([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutionLogs_ExecutionId] ON [dbo].[PlaywrightExecutionLogs] ([ExecutionId]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutionLogs_Level] ON [dbo].[PlaywrightExecutionLogs] ([Level]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutionLogs_Timestamp] ON [dbo].[PlaywrightExecutionLogs] ([Timestamp]);
    CREATE NONCLUSTERED INDEX [IX_PlaywrightExecutionLogs_IsDeleted] ON [dbo].[PlaywrightExecutionLogs] ([IsDeleted]);
    
    PRINT 'PlaywrightExecutionLogs表创建成功';
END
ELSE
BEGIN
    PRINT 'PlaywrightExecutionLogs表已存在';
END

-- 4. 插入一些示例数据
IF NOT EXISTS (SELECT * FROM [dbo].[PlaywrightScripts] WHERE [Name] = '示例登录测试')
BEGIN
    INSERT INTO [dbo].[PlaywrightScripts] (
        [Name], [Description], [Category], [Browser], [Code], [ConfigJson], [TagsJson], [Priority], [Status], [CreatedBy]
    ) VALUES (
        '示例登录测试',
        '演示用户登录流程的E2E测试',
        'e2e',
        'chromium',
        'import { test, expect } from ''@playwright/test'';

test(''用户登录测试'', async ({ page }) => {
  // 访问登录页面
  await page.goto(''/login'');
  
  // 填写用户名
  await page.fill(''[name="username"]'', ''testuser'');
  
  // 填写密码
  await page.fill(''[name="password"]'', ''password123'');
  
  // 点击登录按钮
  await page.click(''button[type="submit"]'');
  
  // 验证登录成功
  await expect(page.locator(''.dashboard'')).toBeVisible();
});',
        '{"headless": false, "viewport": {"width": 1920, "height": 1080}, "timeout": 30000}',
        '["登录", "认证", "E2E"]',
        'high',
        '就绪',
        1
    );
    
    PRINT '示例数据插入成功';
END

PRINT '所有Playwright相关表创建完成！';
