using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 用户实体 - 对应DatabaseSchema.sql中的Users表
/// 功能: 存储系统中所有用户的基本信息、权限、状态等
/// 支持: 多角色权限管理、双因子认证、用户偏好设置
/// </summary>
[SugarTable("Users", "系统用户信息管理表")]
public class User : BaseEntity
{
    /// <summary>
    /// 用户登录名，唯一标识
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "用户登录名，唯一标识")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 用户邮箱地址，用于登录和通知
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "用户邮箱地址，用于登录和通知")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 密码哈希值，使用安全算法加密
    /// </summary>
    [SugarColumn(Length = 255, IsNullable = false, ColumnDescription = "密码哈希值，使用安全算法加密")]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// 密码盐值，增强密码安全性
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "密码盐值，增强密码安全性")]
    public string Salt { get; set; } = string.Empty;

    /// <summary>
    /// 用户真实姓名
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "用户真实姓名")]
    public string? RealName { get; set; }

    /// <summary>
    /// 手机号码，用于通知和验证
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "手机号码，用于通知和验证")]
    public string? Phone { get; set; }

    /// <summary>
    /// 用户头像URL地址
    /// </summary>
    [SugarColumn(Length = 255, IsNullable = true, ColumnDescription = "用户头像URL地址")]
    public string? Avatar { get; set; }

    /// <summary>
    /// 用户角色: User=普通用户, ProjectManager=项目经理, Developer=开发人员, Tester=测试人员, ProductManager=产品经理, Admin=管理员, SuperAdmin=超级管理员
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "用户角色")]
    public string Role { get; set; } = "User";

    /// <summary>
    /// 用户状态: 1=活跃, 2=非活跃, 3=锁定, 4=待验证, 5=已删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "用户状态")]
    public int Status { get; set; } = 1;

    /// <summary>
    /// 最后登录时间，用于统计活跃度
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "最后登录时间，用于统计活跃度")]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 最后登录IP地址，安全审计
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "最后登录IP地址，安全审计")]
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// 邮箱是否已验证，0=未验证, 1=已验证
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "邮箱是否已验证")]
    public bool EmailVerified { get; set; } = false;

    /// <summary>
    /// 邮箱验证时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "邮箱验证时间")]
    public DateTime? EmailVerifiedTime { get; set; }

    /// <summary>
    /// 是否启用双因子认证，0=未启用, 1=已启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否启用双因子认证")]
    public bool TwoFactorEnabled { get; set; } = false;

    /// <summary>
    /// 双因子认证密钥，用于TOTP算法
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "双因子认证密钥，用于TOTP算法")]
    public string? TwoFactorSecret { get; set; }

    /// <summary>
    /// 用户偏好设置，JSON格式存储（主题、语言、通知设置等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "用户偏好设置，JSON格式存储")]
    public string? Preferences { get; set; }

    /// <summary>
    /// 刷新令牌，用于JWT认证
    /// </summary>
    [SugarColumn(Length = 255, IsNullable = true, ColumnDescription = "刷新令牌，用于JWT认证")]
    public string? RefreshToken { get; set; }

    /// <summary>
    /// 刷新令牌过期时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "刷新令牌过期时间")]
    public DateTime? RefreshTokenExpiryTime { get; set; }

    /// <summary>
    /// 最后登录时间（JWT认证使用）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "最后登录时间（JWT认证使用）")]
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 用户拥有的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<Project> OwnedProjects { get; set; } = new();

    /// <summary>
    /// 用户参与的需求对话 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<RequirementConversation> RequirementConversations { get; set; } = new();

    /// <summary>
    /// 用户分配的问题 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<Issue> AssignedIssues { get; set; } = new();

    /// <summary>
    /// 用户报告的问题 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<Issue> ReportedIssues { get; set; } = new();

    /// <summary>
    /// 用户相关的系统日志 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<SystemLog> SystemLogs { get; set; } = new();
}
