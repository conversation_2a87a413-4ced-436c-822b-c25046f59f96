using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// 模拟AI提供商 - 用于开发和测试
/// </summary>
public class MockAIProvider : IAIProvider
{
    private readonly ILogger<MockAIProvider> _logger;
    private readonly AIUsageStats _usageStats;

    public string Name => "Mock";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "mock-gpt-4", "mock-gpt-3.5-turbo" };

    public MockAIProvider(ILogger<MockAIProvider> logger)
    {
        _logger = logger;
        _usageStats = new AIUsageStats();
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Mock AI开始生成文本，模型: {Model}", config.Model);

            // 更新提供商名称引用
            var providerName = Name;

            // 模拟AI处理时间
            await Task.Delay(Random.Shared.Next(1000, 3000));

            string response;

            // 根据提示词类型生成不同的响应
            if (prompt.Contains("请分析以下软件需求") && prompt.Contains("JSON格式"))
            {
                // 需求分析响应
                response = GenerateRequirementAnalysisResponse(prompt);
            }
            else if (prompt.Contains("生成详细的软件需求规格书"))
            {
                // 需求规格书响应
                response = GenerateSpecificationResponse();
            }
            else if (prompt.Contains("生成数据库ER图的Mermaid代码"))
            {
                // ER图响应
                response = GenerateERDiagramResponse();
            }
            else if (prompt.Contains("生成系统上下文图的Mermaid代码"))
            {
                // Context图响应
                response = GenerateContextDiagramResponse();
            }
            else if (prompt.Contains("请生成一个Mermaid格式的") && (prompt.Contains("线框图") || prompt.Contains("原型图") || prompt.Contains("流程图")))
            {
                // 原型图响应
                response = GeneratePrototypeResponse(prompt);
            }
            else
            {
                // 通用对话响应
                response = GenerateConversationResponse(prompt);
            }

            UpdateUsageStats(startTime, true);

            _logger.LogInformation("Mock AI文本生成完成，响应长度: {Length}", response.Length);
            return response;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Mock AI文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Mock AI开始获取向量嵌入");

            // 模拟处理时间
            await Task.Delay(500);

            // 生成模拟的1536维向量
            var embedding = new float[1536];
            var random = new Random();
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2 - 1); // -1 到 1 之间的随机数
            }

            UpdateUsageStats(startTime, true);

            _logger.LogInformation("Mock AI向量嵌入获取完成，维度: {Dimension}", embedding.Length);
            return embedding;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Mock AI向量嵌入获取失败");
            throw;
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        await Task.Delay(100); // 模拟网络延迟
        IsAvailable = true;
        _logger.LogInformation("Mock AI健康检查完成，状态: 正常");
        return IsAvailable;
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    #region 私有方法

    /// <summary>
    /// 生成需求分析响应
    /// </summary>
    private string GenerateRequirementAnalysisResponse(string prompt)
    {
        // 从提示词中提取需求描述
        var requirementsStart = prompt.IndexOf("需求描述：") + 5;
        var requirementsEnd = prompt.IndexOf("请按照以下JSON格式");
        var requirements = requirementsStart > 4 && requirementsEnd > requirementsStart
            ? prompt.Substring(requirementsStart, requirementsEnd - requirementsStart).Trim()
            : "项目管理系统";

        // 根据需求内容生成相应的分析结果
        var analysisResult = new
        {
            projectName = ExtractProjectName(requirements),
            projectDescription = $"基于需求分析，这是一个{ExtractProjectType(requirements)}项目",
            functionalRequirements = GenerateFunctionalRequirements(requirements),
            nonFunctionalRequirements = GenerateNonFunctionalRequirements(),
            userStories = GenerateUserStories(requirements),
            acceptanceCriteria = GenerateAcceptanceCriteria(),
            techStack = GenerateTechStack(),
            risks = GenerateRisks(),
            workloadEstimation = GenerateWorkloadEstimation(),
            feasibilityScore = Random.Shared.Next(7, 10),
            complexityScore = Random.Shared.Next(5, 8),
            confidenceScore = 0.85 + Random.Shared.NextDouble() * 0.1
        };

        return JsonSerializer.Serialize(analysisResult, new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        });
    }

    /// <summary>
    /// 生成对话响应
    /// </summary>
    private string GenerateConversationResponse(string prompt)
    {
        var responses = new[]
        {
            "我理解您的需求。请问您能提供更多关于用户角色和主要功能的详细信息吗？",
            "这是一个很有趣的项目想法。您希望这个系统支持多少用户同时使用？",
            "根据您的描述，我建议采用微服务架构。您对技术栈有什么特殊要求吗？",
            "为了更好地分析您的需求，请问您的目标用户群体是什么？",
            "我需要了解更多关于数据存储和安全性要求的信息。",
            "这个功能听起来很实用。您预期的项目上线时间是什么时候？"
        };

        return responses[Random.Shared.Next(responses.Length)];
    }

    /// <summary>
    /// 生成需求规格书响应
    /// </summary>
    private string GenerateSpecificationResponse()
    {
        return @"# 软件需求规格书

## 1. 项目概述
本项目旨在开发一个现代化的管理系统，提供高效的业务流程管理和数据分析功能。

## 2. 功能需求详细说明
### 2.1 用户管理
- 用户注册和登录
- 角色权限管理
- 用户信息维护

### 2.2 数据管理
- 数据录入和编辑
- 数据查询和筛选
- 数据导入导出

## 3. 非功能需求
- 性能要求：系统响应时间不超过3秒
- 安全要求：数据传输加密，用户认证
- 可用性要求：系统可用性达到99.9%

## 4. 用户界面需求
- 响应式设计，支持多设备访问
- 直观的用户界面，易于操作
- 支持主题切换

## 5. 数据需求
- 用户数据安全存储
- 数据备份和恢复机制
- 数据访问日志记录

## 6. 系统架构建议
- 前端：Vue.js + Element Plus
- 后端：ASP.NET Core + C#
- 数据库：SQL Server
- 部署：Docker + Kubernetes

## 7. 技术约束
- 支持主流浏览器
- 兼容移动设备
- 遵循RESTful API设计规范

## 8. 验收标准
- 所有功能模块正常运行
- 性能指标达到要求
- 安全测试通过
- 用户验收测试通过";
    }

    /// <summary>
    /// 生成ER图响应
    /// </summary>
    private string GenerateERDiagramResponse()
    {
        return @"```mermaid
erDiagram
    USER ||--o{ PROJECT : owns
    USER ||--o{ REQUIREMENT : creates
    PROJECT ||--o{ REQUIREMENT : contains
    PROJECT ||--o{ CODE_GENERATION : has
    REQUIREMENT ||--o{ REQUIREMENT_DOCUMENT : generates

    USER {
        int id PK
        string username
        string email
        string real_name
        string phone
        string avatar
        int role
        int status
        datetime created_time
        datetime updated_time
    }

    PROJECT {
        int id PK
        string name
        string description
        string status
        string priority
        int progress
        datetime start_date
        datetime end_date
        decimal budget
        string technology_stack
        int owner_id FK
        datetime created_at
        datetime updated_at
    }

    REQUIREMENT {
        int id PK
        int project_id FK
        string title
        string description
        string type
        string priority
        string status
        int created_by FK
        datetime created_at
        datetime updated_at
    }

    REQUIREMENT_DOCUMENT {
        int id PK
        int project_id FK
        int requirement_id FK
        string title
        text content
        text functional_requirements
        text non_functional_requirements
        string version
        string status
        datetime created_at
        datetime updated_at
    }

    CODE_GENERATION {
        int id PK
        int project_id FK
        string task_name
        string code_type
        string technology
        string status
        text generated_code
        string file_path
        datetime created_at
        datetime completed_at
    }
```";
    }

    /// <summary>
    /// 生成Context图响应
    /// </summary>
    private string GenerateContextDiagramResponse()
    {
        return @"```mermaid
graph TB
    subgraph ""外部实体""
        User[""👤 用户""]
        Admin[""👨‍💼 管理员""]
        ExtAPI[""🌐 外部API""]
        EmailSys[""📧 邮件系统""]
    end

    subgraph ""系统边界""
        subgraph ""前端层""
            WebUI[""💻 Web界面""]
            MobileUI[""📱 移动端""]
        end

        subgraph ""应用层""
            AuthSvc[""🔐 认证服务""]
            ProjectSvc[""📋 项目服务""]
            RequirementSvc[""📝 需求服务""]
            AISvc[""🤖 AI服务""]
        end

        subgraph ""数据层""
            Database[""🗄️ 数据库""]
            FileStorage[""📁 文件存储""]
            Cache[""⚡ 缓存""]
        end
    end

    User --> WebUI
    User --> MobileUI
    Admin --> WebUI

    WebUI --> AuthSvc
    WebUI --> ProjectSvc
    WebUI --> RequirementSvc

    MobileUI --> AuthSvc
    MobileUI --> ProjectSvc

    AuthSvc --> Database
    ProjectSvc --> Database
    RequirementSvc --> Database
    RequirementSvc --> AISvc

    AISvc --> ExtAPI
    AuthSvc --> EmailSys

    ProjectSvc --> Cache
    RequirementSvc --> FileStorage
```";
    }

    /// <summary>
    /// 提取项目名称
    /// </summary>
    private string ExtractProjectName(string requirements)
    {
        if (requirements.Contains("电商") || requirements.Contains("商城"))
            return "电商管理系统";
        if (requirements.Contains("教育") || requirements.Contains("学习"))
            return "在线教育平台";
        if (requirements.Contains("项目管理"))
            return "项目管理系统";
        if (requirements.Contains("CRM") || requirements.Contains("客户"))
            return "客户关系管理系统";

        return "业务管理系统";
    }

    /// <summary>
    /// 提取项目类型
    /// </summary>
    private string ExtractProjectType(string requirements)
    {
        if (requirements.Contains("网站") || requirements.Contains("Web"))
            return "Web应用";
        if (requirements.Contains("APP") || requirements.Contains("移动"))
            return "移动应用";
        if (requirements.Contains("管理系统") || requirements.Contains("平台"))
            return "管理平台";

        return "软件系统";
    }

    /// <summary>
    /// 生成功能需求
    /// </summary>
    private object[] GenerateFunctionalRequirements(string requirements)
    {
        var baseRequirements = new[]
        {
            new { id = "FR001", title = "用户管理", description = "用户注册、登录、权限管理", priority = "High", category = "用户管理" },
            new { id = "FR002", title = "数据管理", description = "数据的增删改查操作", priority = "High", category = "数据管理" },
            new { id = "FR003", title = "报表统计", description = "生成各类统计报表", priority = "Medium", category = "报表分析" }
        };

        if (requirements.Contains("电商"))
        {
            return baseRequirements.Concat(new[]
            {
                new { id = "FR004", title = "商品管理", description = "商品信息维护和分类管理", priority = "High", category = "商品管理" },
                new { id = "FR005", title = "订单处理", description = "订单创建、支付、发货流程", priority = "High", category = "订单管理" }
            }).ToArray();
        }

        return baseRequirements;
    }

    /// <summary>
    /// 生成非功能需求
    /// </summary>
    private object[] GenerateNonFunctionalRequirements()
    {
        return new[]
        {
            new { type = "性能", description = "系统响应时间不超过3秒", metrics = "响应时间 < 3s" },
            new { type = "安全", description = "数据传输加密，用户认证", metrics = "SSL/TLS加密，JWT认证" },
            new { type = "可用性", description = "系统可用性达到99.9%", metrics = "年度停机时间 < 8.76小时" }
        };
    }

    /// <summary>
    /// 生成用户故事
    /// </summary>
    private object[] GenerateUserStories(string requirements)
    {
        return new[]
        {
            new { role = "普通用户", goal = "能够快速完成日常操作", benefit = "提高工作效率" },
            new { role = "管理员", goal = "能够监控系统运行状态", benefit = "确保系统稳定运行" },
            new { role = "业务人员", goal = "能够生成业务报表", benefit = "支持业务决策" }
        };
    }

    /// <summary>
    /// 生成验收标准
    /// </summary>
    private string[] GenerateAcceptanceCriteria()
    {
        return new[]
        {
            "所有功能模块正常运行",
            "性能指标达到要求",
            "安全测试通过",
            "用户验收测试通过"
        };
    }

    /// <summary>
    /// 生成技术栈
    /// </summary>
    private object GenerateTechStack()
    {
        return new
        {
            frontend = new[] { "Vue 3", "TypeScript", "Element Plus", "Vite" },
            backend = new[] { "ASP.NET Core", "C#", "SqlSugar", "JWT" },
            database = new[] { "SQL Server", "Redis" },
            other = new[] { "Docker", "Nginx", "Azure" }
        };
    }

    /// <summary>
    /// 生成风险评估
    /// </summary>
    private object[] GenerateRisks()
    {
        return new[]
        {
            new { description = "技术选型风险", impact = "Medium", probability = "Low", mitigation = "充分的技术调研和原型验证" },
            new { description = "需求变更风险", impact = "High", probability = "Medium", mitigation = "采用敏捷开发方法，定期沟通" },
            new { description = "性能风险", impact = "Medium", probability = "Low", mitigation = "性能测试和优化" }
        };
    }

    /// <summary>
    /// 生成工作量估算
    /// </summary>
    private object GenerateWorkloadEstimation()
    {
        return new
        {
            totalHours = Random.Shared.Next(200, 800),
            phases = new[]
            {
                new { name = "需求分析", hours = Random.Shared.Next(40, 80), description = "需求收集和分析" },
                new { name = "系统设计", hours = Random.Shared.Next(60, 120), description = "架构设计和详细设计" },
                new { name = "开发实现", hours = Random.Shared.Next(200, 400), description = "编码和单元测试" },
                new { name = "测试验收", hours = Random.Shared.Next(80, 160), description = "系统测试和用户验收" }
            }
        };
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        _usageStats.TotalTokens += Random.Shared.Next(100, 1000);
        _usageStats.TotalCost += (decimal)(Random.Shared.NextDouble() * 0.1);
        _usageStats.LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 生成原型图的模拟响应
    /// </summary>
    private string GeneratePrototypeResponse(string prompt)
    {
        if (prompt.Contains("线框图") || prompt.Contains("原型图"))
        {
            return @"```mermaid
graph TD
    A[页面标题] --> B[导航栏]
    B --> C[主内容区]
    C --> D[侧边栏]
    C --> E[内容列表]
    E --> F[列表项1]
    E --> G[列表项2]
    E --> H[列表项3]
    D --> I[筛选器]
    D --> J[操作按钮]

    style A fill:#e3f2fd
    style B fill:#f1f8e9
    style C fill:#fce4ec
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
    style I fill:#fff8e1
    style J fill:#e0f2f1
```

这是一个基本的页面原型图，展示了页面的主要布局和组件结构。";
        }
        else if (prompt.Contains("流程图"))
        {
            return @"```mermaid
flowchart TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[处理结果]
    D --> E
    E --> F{是否继续}
    F -->|是| B
    F -->|否| G[结束]

    style A fill:#e8f5e8
    style G fill:#ffebee
    style B fill:#fff3e0
    style F fill:#fff3e0
    style C fill:#e3f2fd
    style D fill:#e3f2fd
    style E fill:#f3e5f5
```

这是一个基本的业务流程图，展示了操作的执行流程和决策点。";
        }

        return "我已经为您生成了相应的Mermaid图表代码，您可以复制使用。";
    }

    #endregion
}
