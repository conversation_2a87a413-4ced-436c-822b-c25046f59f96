<template>
  <div class="create-project">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" type="text" class="back-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回项目列表
        </el-button>
        <h1 class="page-title">创建新项目</h1>
        <p class="page-subtitle">填写项目基本信息，开始您的AI驱动开发之旅</p>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="form-container">
      <el-card class="form-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          label-position="left"
          size="large"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>

            <el-form-item label="项目名称" prop="name" required>
              <el-input
                v-model="form.name"
                placeholder="请输入项目名称"
                maxlength="200"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-form-item label="项目类型" prop="projectType" required>
              <el-select
                v-model="form.projectType"
                placeholder="请选择项目类型"
                style="width: 100%"
              >
                <el-option label="新项目开发" value="NEW_PROJECT">
                  <div class="option-content">
                    <span class="option-icon">🆕</span>
                    <span class="option-text">新项目开发</span>
                    <span class="option-desc">从零开始的全新项目</span>
                  </div>
                </el-option>
                <el-option label="功能增强" value="FEATURE_ENHANCEMENT">
                  <div class="option-content">
                    <span class="option-icon">⚡</span>
                    <span class="option-text">功能增强</span>
                    <span class="option-desc">为现有系统添加新功能</span>
                  </div>
                </el-option>
                <el-option label="维护项目" value="MAINTENANCE">
                  <div class="option-content">
                    <span class="option-icon">🔧</span>
                    <span class="option-text">维护项目</span>
                    <span class="option-desc">Bug修复和系统维护</span>
                  </div>
                </el-option>
                <el-option label="重构项目" value="REFACTORING">
                  <div class="option-content">
                    <span class="option-icon">🔄</span>
                    <span class="option-text">重构项目</span>
                    <span class="option-desc">代码重构和架构优化</span>
                  </div>
                </el-option>
                <el-option label="迁移项目" value="MIGRATION">
                  <div class="option-content">
                    <span class="option-icon">📦</span>
                    <span class="option-text">迁移项目</span>
                    <span class="option-desc">技术栈或平台迁移</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 现有项目关联 - 仅在非新项目时显示 -->
            <el-form-item
              v-if="form.projectType && form.projectType !== 'NEW_PROJECT'"
              label="关联现有项目"
              prop="parentProjectId"
              required
            >
              <el-select
                v-model="form.parentProjectId"
                placeholder="请选择要关联的现有项目"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchExistingProjects"
                :loading="loadingProjects"
                @change="handleParentProjectChange"
              >
                <el-option
                  v-for="project in existingProjects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                >
                  <div class="project-option">
                    <div class="project-info">
                      <span class="project-name">{{ project.name }}</span>
                      <el-tag :type="getProjectTypeTagType(project.projectType)" size="small">
                        {{ getProjectTypeText(project.projectType) }}
                      </el-tag>
                    </div>
                    <div class="project-meta">
                      <span class="tech-stack">{{ project.technologyStack }}</span>
                      <span class="status">{{ getStatusText(project.status) }}</span>
                    </div>
                  </div>
                </el-option>
              </el-select>

              <!-- 选中项目的详细信息 -->
              <div v-if="selectedParentProject" class="parent-project-info">
                <el-card shadow="never" class="info-card">
                  <div class="project-summary">
                    <h4>{{ selectedParentProject.name }}</h4>
                    <p class="description">{{ selectedParentProject.description }}</p>
                    <div class="project-details">
                      <div class="detail-item">
                        <span class="label">技术栈：</span>
                        <span class="value">{{ selectedParentProject.technologyStack }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">当前状态：</span>
                        <el-tag :type="getStatusType(selectedParentProject.status)" size="small">
                          {{ getStatusText(selectedParentProject.status) }}
                        </el-tag>
                      </div>
                      <div class="detail-item">
                        <span class="label">进度：</span>
                        <el-progress :percentage="selectedParentProject.progress" :stroke-width="6" />
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-form-item>

            <el-form-item label="项目描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                :placeholder="getDescriptionPlaceholder()"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目优先级" prop="priority">
                  <el-select v-model="form.priority" placeholder="请选择优先级">
                    <el-option
                      v-for="option in priorityOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="技术栈" prop="technologyStack">
                  <el-select
                    v-model="form.technologyStack"
                    placeholder="请选择主要技术栈"
                    clearable
                    :loading="loadingTechnologyOptions"
                  >
                    <el-option
                      v-for="tech in technologyOptions"
                      :key="tech.value"
                      :label="tech.label"
                      :value="tech.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 时间规划 -->
          <div class="form-section">
            <h3 class="section-title">时间规划</h3>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间" prop="startDate">
                  <el-date-picker
                    v-model="form.startDate"
                    type="date"
                    placeholder="选择开始时间"
                    style="width: 100%"
                    :disabled-date="disabledStartDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间" prop="endDate">
                  <el-date-picker
                    v-model="form.endDate"
                    type="date"
                    placeholder="选择结束时间"
                    style="width: 100%"
                    :disabled-date="disabledEndDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="预估工时" prop="estimatedHours">
              <el-input-number
                v-model="form.estimatedHours"
                :min="1"
                :max="10000"
                :step="1"
                :precision="0"
                controls
                controls-position="right"
                placeholder="预估工时（小时）"
                style="width: 100%"
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>请根据项目复杂度合理估算所需工时</span>
              </div>
            </el-form-item>
          </div>

          <!-- 预算管理 -->
          <div class="form-section">
            <h3 class="section-title">预算管理</h3>

            <el-form-item label="项目预算" prop="budget">
              <el-input-number
                v-model="form.budget"
                :min="0"
                :step="1000"
                :precision="0"
                controls
                controls-position="right"
                placeholder="项目预算（元）"
                style="width: 100%"
                :formatter="(value: any) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value: any) => value.replace(/¥\s?|(,*)/g, '')"
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>包括开发成本、第三方服务费用等</span>
              </div>
            </el-form-item>
          </div>

          <!-- AI配置 -->
          <div class="form-section">
            <h3 class="section-title">AI配置</h3>

            <el-form-item label="启用AI功能">
              <el-checkbox-group v-model="form.aiFeatures">
                <el-checkbox label="requirement" border>
                  <div class="feature-item">
                    <div class="feature-title">智能需求分析</div>
                    <div class="feature-desc">使用AI自动分析和整理需求文档</div>
                  </div>
                </el-checkbox>
                <el-checkbox label="design" border>
                  <div class="feature-item">
                    <div class="feature-title">自动设计生成</div>
                    <div class="feature-desc">生成ER图、上下文图等设计文档</div>
                  </div>
                </el-checkbox>
                <el-checkbox label="code" border>
                  <div class="feature-item">
                    <div class="feature-title">智能代码生成</div>
                    <div class="feature-desc">根据需求自动生成高质量代码</div>
                  </div>
                </el-checkbox>
                <el-checkbox label="test" border>
                  <div class="feature-item">
                    <div class="feature-title">自动化测试</div>
                    <div class="feature-desc">生成测试用例并执行自动化测试</div>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- 表单操作 -->
          <div class="form-actions">
            <el-button @click="resetForm" size="large">
              重置
            </el-button>
            <el-button @click="saveDraft" size="large">
              保存草稿
            </el-button>
            <el-button
              @click="submitForm"
              type="primary"
              size="large"
              :loading="submitting"
            >
              创建项目
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 项目模板选择对话框 -->
    <el-dialog v-model="templateDialogVisible" title="选择项目模板" width="800px">
      <div class="template-grid">
        <div
          v-for="template in projectTemplates"
          :key="template.id"
          class="template-card"
          :class="{ active: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <div class="template-icon">
            <el-icon size="32">
              <component :is="template.icon" />
            </el-icon>
          </div>
          <h4>{{ template.name }}</h4>
          <p>{{ template.description }}</p>
          <div class="template-tags">
            <el-tag
              v-for="tag in template.tags"
              :key="tag"
              size="small"
              type="info"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button @click="skipTemplate" type="default">跳过模板</el-button>
          <el-button
            @click="applyTemplate"
            type="primary"
            :disabled="!selectedTemplate"
          >
            应用模板
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { ProjectService } from '@/services/project'
import { SystemParameterService } from '@/services/systemParameter'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { CreateProjectRequest } from '@/types'

// 图标导入
import {
  ArrowLeft,
  InfoFilled,
  Monitor,
  ShoppingCart,
  Management,
  Document,
  Platform
} from '@element-plus/icons-vue'

const router = useRouter()
const projectStore = useProjectStore()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式数据
const submitting = ref(false)
const templateDialogVisible = ref(false)
const selectedTemplate = ref<any>(null)
const loadingProjects = ref(false)
const existingProjects = ref<any[]>([])
const selectedParentProject = ref<any>(null)

// 表单数据
const form = reactive<CreateProjectRequest & { aiFeatures: string[], projectType: string, parentProjectId?: number }>({
  name: '',
  description: '',
  projectType: '',
  parentProjectId: undefined,
  priority: 'Medium',
  startDate: '',
  endDate: '',
  estimatedHours: undefined,
  budget: undefined,
  technologyStack: '',
  aiFeatures: ['requirement', 'design', 'code']
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 200, message: '项目名称长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  projectType: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  parentProjectId: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (form.projectType && form.projectType !== 'NEW_PROJECT' && !value) {
          callback(new Error('请选择要关联的现有项目'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  description: [
    { max: 1000, message: '项目描述不能超过 1000 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择项目优先级', trigger: 'change' }
  ],
  estimatedHours: [
    { type: 'number', min: 1, max: 10000, message: '预估工时必须在 1 到 10000 小时之间', trigger: 'blur' }
  ],
  budget: [
    { type: 'number', min: 0, message: '预算不能为负数', trigger: 'blur' }
  ]
}

// 选项数据
const priorityOptions = computed(() => ProjectService.getPriorityOptions())

const technologyOptions = ref<Array<{ label: string; value: string }>>([])
const loadingTechnologyOptions = ref(false)

// 项目模板
const projectTemplates = ref([
  {
    id: 1,
    name: '企业管理系统',
    description: '包含用户管理、权限控制、数据统计等功能的企业级管理系统',
    icon: 'Management',
    tags: ['后台管理', '权限系统', '数据统计'],
    config: {
      technologyStack: 'Vue.js,ASP.NET Core,SQL Server',
      estimatedHours: 200,
      aiFeatures: ['requirement', 'design', 'code', 'test']
    }
  },
  {
    id: 2,
    name: '电商平台',
    description: '完整的电商解决方案，包含商品管理、订单处理、支付集成',
    icon: 'ShoppingCart',
    tags: ['电商', '支付', '订单管理'],
    config: {
      technologyStack: 'React,Node.js,MongoDB',
      estimatedHours: 300,
      aiFeatures: ['requirement', 'design', 'code']
    }
  },
  {
    id: 3,
    name: '移动应用',
    description: '跨平台移动应用，支持iOS和Android',
    icon: 'Mobile',
    tags: ['移动端', '跨平台', 'API'],
    config: {
      technologyStack: 'Flutter,Firebase,Firestore',
      estimatedHours: 150,
      aiFeatures: ['requirement', 'design', 'code']
    }
  },
  {
    id: 4,
    name: '内容管理系统',
    description: '灵活的内容管理和发布平台',
    icon: 'Document',
    tags: ['CMS', '内容管理', '发布系统'],
    config: {
      technologyStack: 'Vue.js,ASP.NET Core,SQL Server',
      estimatedHours: 120,
      aiFeatures: ['requirement', 'design', 'code']
    }
  },
  {
    id: 5,
    name: '数据分析平台',
    description: '数据可视化和分析工具',
    icon: 'Platform',
    tags: ['数据分析', '可视化', '报表'],
    config: {
      technologyStack: 'React,Node.js,PostgreSQL',
      estimatedHours: 180,
      aiFeatures: ['requirement', 'design', 'code']
    }
  },
  {
    id: 6,
    name: '监控系统',
    description: '系统监控和运维管理平台',
    icon: 'Monitor',
    tags: ['监控', '运维', '告警'],
    config: {
      technologyStack: 'Angular,Spring Boot,MySQL',
      estimatedHours: 160,
      aiFeatures: ['requirement', 'design', 'code']
    }
  }
])

// 日期验证
const disabledStartDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择昨天之前的日期
}

const disabledEndDate = (time: Date) => {
  if (!form.startDate) return false
  const startTime = new Date(form.startDate).getTime()
  return time.getTime() < startTime // 结束时间不能早于开始时间
}

// 表单操作
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  form.aiFeatures = ['requirement', 'design', 'code']
}

const saveDraft = async () => {
  try {
    // TODO: 实现保存草稿功能
    ElMessage.success('草稿保存成功')
  } catch (error) {
    ElMessage.error('草稿保存失败')
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 准备提交数据
    const submitData: CreateProjectRequest = {
      name: form.name,
      description: form.description,
      priority: form.priority,
      startDate: form.startDate,
      endDate: form.endDate,
      estimatedHours: form.estimatedHours,
      budget: form.budget,
      technologyStack: form.technologyStack
    }

    // 创建项目
    const project = await projectStore.createProject(submitData)

    ElMessage.success('项目创建成功')

    // 跳转到项目详情页
    router.push(`/projects/${project.id}`)

  } catch (error) {
    console.error('Create project failed:', error)
    ElMessage.error('项目创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 模板操作
const selectTemplate = (template: any) => {
  selectedTemplate.value = template
}

const applyTemplate = () => {
  if (!selectedTemplate.value) return

  const config = selectedTemplate.value.config

  // 应用模板配置
  form.technologyStack = config.technologyStack
  form.estimatedHours = config.estimatedHours
  form.aiFeatures = config.aiFeatures

  // 如果没有填写项目名称，使用模板名称
  if (!form.name) {
    form.name = `${selectedTemplate.value.name} - ${new Date().toLocaleDateString()}`
  }

  templateDialogVisible.value = false
  selectedTemplate.value = null

  ElMessage.success('模板应用成功')
}

const skipTemplate = () => {
  templateDialogVisible.value = false
  selectedTemplate.value = null
}

// 现有项目相关方法
const searchExistingProjects = async (query: string) => {
  if (!query) {
    existingProjects.value = []
    return
  }

  try {
    loadingProjects.value = true
    // 使用现有的 fetchProjects 方法并过滤
    await projectStore.fetchProjects()
    const allProjects = projectStore.projects
    existingProjects.value = allProjects.filter((p: any) =>
      p.status !== 'Archived' &&
      p.name.toLowerCase().includes(query.toLowerCase())
    )
  } catch (error) {
    console.error('搜索项目失败:', error)
    ElMessage.error('搜索项目失败')
  } finally {
    loadingProjects.value = false
  }
}

const handleParentProjectChange = async (projectId: number) => {
  if (!projectId) {
    selectedParentProject.value = null
    return
  }

  try {
    // 从已加载的项目列表中查找
    const project = existingProjects.value.find(p => p.id === projectId)
    if (project) {
      selectedParentProject.value = project

      // 自动填充相关信息
      if (!form.technologyStack) {
        form.technologyStack = project.technologyStack || ''
      }

      // 根据项目类型提供建议的描述模板
      if (!form.description) {
        form.description = getDescriptionTemplate(form.projectType, project.name)
      }
    }

  } catch (error) {
    console.error('获取项目详情失败:', error)
    ElMessage.error('获取项目详情失败')
  }
}

const getDescriptionTemplate = (projectType: string, parentProjectName: string) => {
  const templates = {
    FEATURE_ENHANCEMENT: `为 ${parentProjectName} 添加新功能：\n\n功能目标：\n- \n\n预期效果：\n- \n\n影响范围：\n- `,
    MAINTENANCE: `${parentProjectName} 维护项目：\n\n维护内容：\n- Bug修复\n- 性能优化\n- 安全更新\n\n预期改进：\n- `,
    REFACTORING: `${parentProjectName} 重构项目：\n\n重构目标：\n- 代码结构优化\n- 性能提升\n- 可维护性改进\n\n重构范围：\n- `,
    MIGRATION: `${parentProjectName} 迁移项目：\n\n迁移内容：\n- \n\n目标平台/技术：\n- \n\n迁移计划：\n- `
  }
  return templates[projectType as keyof typeof templates] || ''
}

const getDescriptionPlaceholder = () => {
  if (!form.projectType) return '请输入项目描述，包括项目目标、功能范围等'

  const placeholders = {
    NEW_PROJECT: '请输入项目描述，包括项目目标、功能范围、技术要求等',
    FEATURE_ENHANCEMENT: '请描述要添加的新功能，包括功能目标、预期效果、影响范围等',
    MAINTENANCE: '请描述维护内容，包括要修复的问题、优化的方面等',
    REFACTORING: '请描述重构目标，包括要改进的代码结构、性能目标等',
    MIGRATION: '请描述迁移计划，包括迁移内容、目标平台、迁移策略等'
  }

  return placeholders[form.projectType as keyof typeof placeholders] || '请输入项目描述'
}

// 项目类型相关方法
const getProjectTypeIcon = (projectType: string) => {
  const iconMap: Record<string, string> = {
    NEW_PROJECT: '🆕',
    FEATURE_ENHANCEMENT: '⚡',
    MAINTENANCE: '🔧',
    REFACTORING: '🔄',
    MIGRATION: '📦'
  }
  return iconMap[projectType] || '📋'
}

const getProjectTypeText = (projectType: string) => {
  const textMap: Record<string, string> = {
    NEW_PROJECT: '新项目',
    FEATURE_ENHANCEMENT: '功能增强',
    MAINTENANCE: '维护项目',
    REFACTORING: '重构项目',
    MIGRATION: '迁移项目'
  }
  return textMap[projectType] || projectType
}

const getProjectTypeTagType = (projectType: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    NEW_PROJECT: 'success',
    FEATURE_ENHANCEMENT: 'primary',
    MAINTENANCE: 'warning',
    REFACTORING: 'info',
    MIGRATION: 'danger'
  }
  return typeMap[projectType] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    Planning: '规划中',
    InProgress: '进行中',
    Completed: '已完成',
    OnHold: '暂停',
    Cancelled: '已取消',
    Archived: '已归档'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    Planning: 'info',
    InProgress: 'primary',
    Completed: 'success',
    OnHold: 'warning',
    Cancelled: 'danger',
    Archived: 'info'
  }
  return typeMap[status] || 'info'
}

// 加载技术栈选项
const loadTechnologyOptions = async () => {
  try {
    loadingTechnologyOptions.value = true
    const options = await SystemParameterService.getTechnologyStackOptions()
    technologyOptions.value = options.map(option => ({
      label: option.label,
      value: option.value
    }))
  } catch (error) {
    console.error('加载技术栈选项失败:', error)
    ElMessage.error('加载技术栈选项失败')
    // 使用默认选项作为后备
    technologyOptions.value = [
      { label: 'Vue.js + ASP.NET Core', value: 'Vue.js,ASP.NET Core,SQL Server' },
      { label: 'React + Node.js', value: 'React,Node.js,MongoDB' },
      { label: 'Angular + Spring Boot', value: 'Angular,Spring Boot,MySQL' },
      { label: 'Flutter + Firebase', value: 'Flutter,Firebase,Firestore' },
      { label: 'React Native + Express', value: 'React Native,Express,PostgreSQL' },
      { label: '微信小程序', value: '微信小程序,Node.js,MySQL' },
      { label: '自定义技术栈', value: 'Custom' }
    ]
  } finally {
    loadingTechnologyOptions.value = false
  }
}

// 组件挂载时加载技术栈选项
onMounted(() => {
  loadTechnologyOptions()
  // 可以根据需要决定是否自动显示模板选择
  // templateDialogVisible.value = true
})
</script>

<style lang="scss" scoped>
.create-project {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;

  .page-header {
    margin-bottom: 32px;

    .back-btn {
      margin-bottom: 16px;
      color: #606266;

      &:hover {
        color: #409eff;
      }
    }

    .page-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: #303133;
    }

    .page-subtitle {
      margin: 0;
      color: #606266;
      font-size: 16px;
    }
  }

  .form-container {
    .form-card {
      border-radius: 12px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .form-section {
        margin-bottom: 32px;
        padding-bottom: 24px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .section-title {
          margin: 0 0 20px 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;

          &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #409eff;
            margin-right: 12px;
            border-radius: 2px;
          }
        }

        .form-tip {
          display: flex;
          align-items: center;
          margin-top: 8px;
          color: #909399;
          font-size: 14px;

          .el-icon {
            margin-right: 6px;
            color: #409eff;
          }
        }

        // AI功能选择样式
        .el-checkbox-group {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;

          .el-checkbox {
            margin: 0;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }

            &.is-checked {
              border-color: #409eff;
              background-color: rgba(64, 158, 255, 0.05);
            }

            .feature-item {
              margin-left: 8px;

              .feature-title {
                font-weight: 500;
                color: #303133;
                margin-bottom: 4px;
              }

              .feature-desc {
                font-size: 12px;
                color: #909399;
                line-height: 1.4;
              }
            }
          }
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        padding-top: 24px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }

  // 模板选择对话框样式
  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    max-height: 400px;
    overflow-y: auto;

    .template-card {
      padding: 20px;
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
      }

      &.active {
        border-color: #409eff;
        background-color: rgba(64, 158, 255, 0.05);
      }

      .template-icon {
        margin-bottom: 12px;
        color: #409eff;
      }

      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      p {
        margin: 0 0 12px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }

      .template-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        justify-content: center;

        .el-tag {
          font-size: 12px;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .create-project {
    padding: 16px;

    .form-container {
      .form-card {
        :deep(.el-form) {
          .el-form-item {
            .el-form-item__label {
              text-align: left;
            }
          }

          .el-row {
            .el-col {
              margin-bottom: 16px;
            }
          }
        }

        .form-section {
          .el-checkbox-group {
            grid-template-columns: 1fr;
          }
        }

        .form-actions {
          flex-direction: column;

          .el-button {
            width: 100%;
          }
        }
      }
    }

    .template-grid {
      grid-template-columns: 1fr;
    }
  }
}

// Element Plus 组件样式覆盖
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;

  .option-icon {
    font-size: 16px;
  }

  .option-text {
    font-weight: 500;
  }

  .option-desc {
    color: #909399;
    font-size: 12px;
    margin-left: auto;
  }
}

.project-option {
  .project-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;

    .project-name {
      font-weight: 500;
      color: #303133;
    }
  }

  .project-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;

    .tech-stack {
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.parent-project-info {
  margin-top: 12px;

  .info-card {
    border: 1px solid #e4e7ed;

    .project-summary {
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
      }

      .description {
        color: #606266;
        font-size: 14px;
        margin-bottom: 12px;
        line-height: 1.5;
      }

      .project-details {
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .label {
            font-weight: 500;
            color: #606266;
            min-width: 80px;
          }

          .value {
            color: #303133;
          }
        }
      }
    }
  }
}
</style>
