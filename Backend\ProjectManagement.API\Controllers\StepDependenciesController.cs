using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Services;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 步骤依赖关系管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class StepDependenciesController : ControllerBase
{
    private readonly IRequirementDecompositionService _decompositionService;
    private readonly ILogger<StepDependenciesController> _logger;

    public StepDependenciesController(
        IRequirementDecompositionService decompositionService,
        ILogger<StepDependenciesController> logger)
    {
        _decompositionService = decompositionService;
        _logger = logger;
    }

    /// <summary>
    /// 添加步骤依赖关系
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> AddDependency([FromBody] AddDependencyRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var dependency = await _decompositionService.AddStepDependencyAsync(
                request.StepId,
                request.DependsOnStepId,
                request.DependencyType ?? "Sequential",
                request.IsRequired,
                userId);

            return Ok(new
            {
                success = true,
                data = new
                {
                    id = dependency.Id,
                    stepId = dependency.StepId,
                    dependsOnStepId = dependency.DependsOnStepId,
                    dependencyType = dependency.DependencyType,
                    isRequired = dependency.IsRequired,
                    description = dependency.Description,
                    createdTime = dependency.CreatedTime
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加步骤依赖关系失败");
            return StatusCode(500, new { success = false, message = "添加依赖关系失败" });
        }
    }

    /// <summary>
    /// 删除步骤依赖关系
    /// </summary>
    [HttpDelete("{dependencyId}")]
    public async Task<IActionResult> RemoveDependency(int dependencyId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var success = await _decompositionService.RemoveStepDependencyAsync(dependencyId, userId);

            if (success)
            {
                return Ok(new { success = true, message = "依赖关系删除成功" });
            }
            else
            {
                return NotFound(new { success = false, message = "依赖关系不存在" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除步骤依赖关系失败，DependencyId: {DependencyId}", dependencyId);
            return StatusCode(500, new { success = false, message = "删除依赖关系失败" });
        }
    }

    /// <summary>
    /// 自动分析并建立依赖关系
    /// </summary>
    [HttpPost("auto-analyze/{projectId}")]
    public async Task<IActionResult> AutoAnalyzeDependencies(int projectId)
    {
        try
        {
            var userId = GetCurrentUserId();
            var count = await _decompositionService.AutoAnalyzeDependenciesAsync(projectId, userId);

            return Ok(new
            {
                success = true,
                message = $"自动分析完成，建立了 {count} 个依赖关系",
                data = new { dependencyCount = count }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动分析依赖关系失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "自动分析依赖关系失败" });
        }
    }

    /// <summary>
    /// 验证依赖关系
    /// </summary>
    [HttpGet("validate/{projectId}")]
    public async Task<IActionResult> ValidateDependencies(int projectId)
    {
        try
        {
            var result = await _decompositionService.ValidateDependenciesAsync(projectId);

            return Ok(new
            {
                success = true,
                data = new
                {
                    isValid = result.IsValid,
                    errors = result.Errors,
                    warnings = result.Warnings,
                    circularDependencies = result.CircularDependencies?.Select(cd => new
                    {
                        stepIds = cd.StepIds,
                        stepNames = cd.StepNames,
                        description = cd.Description
                    })
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证依赖关系失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "验证依赖关系失败" });
        }
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return userIdClaim != null ? int.Parse(userIdClaim.Value) : 0;
    }
}

/// <summary>
/// 添加依赖关系请求
/// </summary>
public class AddDependencyRequest
{
    public int StepId { get; set; }
    public int DependsOnStepId { get; set; }
    public string? DependencyType { get; set; }
    public bool IsRequired { get; set; } = true;
    public string? Description { get; set; }
}
