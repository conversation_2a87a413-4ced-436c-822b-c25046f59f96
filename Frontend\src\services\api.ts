import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 全局状态管理
let isShowingLoginDialog = false // 防止重复弹出登录对话框
let loginDialogPromise: Promise<any> | null = null // 保存对话框Promise
let refreshingPromise: Promise<any> | null = null // 正在刷新token的Promise，防止并发刷新

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: import.meta.env.VITE_API_TIMEOUT || 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  // 开发环境下忽略SSL证书验证
  ...(import.meta.env.DEV && {
    httpsAgent: false,
    withCredentials: false

  })
})

// 创建AI专用的axios实例，超时时间更长
const aiApiTimeout = 300000 // 直接设置5分钟超时，确保生效
console.log('AI API Timeout配置:', {
  envValue: import.meta.env.VITE_AI_API_TIMEOUT,
  hardcodedValue: aiApiTimeout,
  finalTimeout: aiApiTimeout
})

const aiApi: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: aiApiTimeout, // AI请求5分钟超时
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  // 开发环境下忽略SSL证书验证
  ...(import.meta.env.DEV && {
    httpsAgent: false,
    withCredentials: false
  })
})

// 通用请求拦截器函数
const createRequestInterceptor = (apiName: string) => (config: any) => {
  // 添加认证token
  const authStore = useAuthStore()
  if (authStore.token) {
    config.headers = config.headers || {}
    config.headers.Authorization = `Bearer ${authStore.token}`
  }

  // 添加请求ID用于追踪
  config.headers = config.headers || {}
  config.headers['X-Request-ID'] = generateRequestId()

  console.log(`[${apiName} Request] ${config.method?.toUpperCase()} ${config.url}`, config.data)
  return config
}

// 通用错误处理函数
const createRequestErrorHandler = (apiName: string) => (error: any) => {
  console.error(`[${apiName} Request Error]`, error)
  return Promise.reject(error)
}

// 为普通API添加拦截器
api.interceptors.request.use(
  createRequestInterceptor('API'),
  createRequestErrorHandler('API')
)

// 为AI API添加拦截器
aiApi.interceptors.request.use(
  createRequestInterceptor('AI API'),
  createRequestErrorHandler('AI API')
)

// 通用响应成功处理函数
const createResponseSuccessHandler = (apiName: string) => (response: AxiosResponse) => {
  return response
}

// 通用响应错误处理函数
const createResponseErrorHandler = (apiName: string) => async (error: any) => {
  console.error(`[${apiName} Response Error]`, error)

  const { response, code, message } = error

  // 处理超时错误
  if (code === 'ECONNABORTED' && message.includes('timeout')) {
    if (apiName === 'AI API') {
      ElMessage.error('AI服务响应超时，请稍后重试或检查网络连接')
    } else {
      ElMessage.error('请求超时，请稍后重试')
    }
    return Promise.reject(error)
  }

  // 处理网络连接错误
  if (!response) {
    if (code === 'ERR_NETWORK' || message.includes('Network Error')) {
      ElMessage.error('网络连接失败，请检查后端服务是否启动')
    } else if (code === 'ECONNREFUSED' || message.includes('ECONNREFUSED')) {
      ElMessage.error('连接被拒绝，请检查API服务器地址和端口')
    } else if (message.includes('SSL') || message.includes('certificate')) {
      ElMessage.error('SSL证书验证失败，请检查HTTPS配置')
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
    return Promise.reject(error)
  }

  const { status, data } = response

  switch (status) {
    case 401:
      // 未授权，尝试刷新token
      const authStore = useAuthStore()
      const originalRequest = error.config

      // 如果有refresh token，尝试刷新
      if (authStore.refreshToken && !refreshingPromise) {
        try {
          console.log('Token过期，尝试自动刷新...')

          // 防止并发刷新
          refreshingPromise = authStore.refreshTokenAction()
          await refreshingPromise

          console.log('Token刷新成功，重试原请求')

          // 刷新成功，重试原请求
          if (originalRequest && !originalRequest._retry) {
            originalRequest._retry = true
            // 更新Authorization头
            originalRequest.headers.Authorization = `Bearer ${authStore.token}`

            // 根据API类型选择正确的axios实例重试
            const axiosInstance = apiName === 'AI API' ? aiApi : api
            return axiosInstance.request(originalRequest)
          }
        } catch (refreshError) {
          console.error('Token刷新失败:', refreshError)
          // 刷新失败，执行登出流程
          await authStore.logout()

          // 显示登录过期提示（但不在应用初始化阶段显示）
          const isInitializing = !(document.querySelector('#app') as any)?.__vue_app__
          if (router.currentRoute.value.path !== '/login' && !isShowingLoginDialog && !isInitializing) {
            isShowingLoginDialog = true

            if (!loginDialogPromise) {
              loginDialogPromise = ElMessageBox.alert('登录已过期，请重新登录', '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              }).then(() => {
                router.push('/login')
              }).finally(() => {
                isShowingLoginDialog = false
                loginDialogPromise = null
              })
            }

            await loginDialogPromise
          } else if (isInitializing) {
            console.log('应用初始化阶段，跳过登录过期提示')
          }
        } finally {
          refreshingPromise = null
        }
      } else {
        // 没有refresh token或正在刷新中，直接登出
        await authStore.logout()

        if (router.currentRoute.value.path !== '/login' && !isShowingLoginDialog) {
          isShowingLoginDialog = true

          if (!loginDialogPromise) {
            loginDialogPromise = ElMessageBox.alert('登录已过期，请重新登录', '提示', {
              confirmButtonText: '确定',
              type: 'warning'
            }).then(() => {
              router.push('/login')
            }).finally(() => {
              isShowingLoginDialog = false
              loginDialogPromise = null
            })
          }

          await loginDialogPromise
        }
      }
      break

    case 403:
      ElMessage.error('权限不足，无法访问该资源')
      break

    case 404:
      // 检查是否是因为taskId为undefined导致的404
      if (error.config?.url?.includes('/tasks/undefined/')) {
        ElMessage.error('任务ID无效，请重新生成任务')
      } else {
        ElMessage.error('请求的资源不存在')
      }
      break

    case 422:
      // 验证错误
      if (data?.errors) {
        const errorMessages = Object.values(data.errors).flat()
        ElMessage.error(errorMessages.join('; '))
      } else {
        ElMessage.error(data?.message || '请求参数验证失败')
      }
      break

    case 429:
      ElMessage.error('请求过于频繁，请稍后再试')
      break

    case 500:
      ElMessage.error(data?.message || '服务器内部错误')
      break

    default:
      ElMessage.error(data?.message || `请求失败 (${status})`)
  }

  return Promise.reject(error)
}

// 为普通API添加响应拦截器
api.interceptors.response.use(
  createResponseSuccessHandler('API'),
  createResponseErrorHandler('API')
)

// 为AI API添加响应拦截器
aiApi.interceptors.response.use(
  createResponseSuccessHandler('AI API'),
  createResponseErrorHandler('AI API')
)

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
}

// API响应包装器
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// 通用API请求方法
export class ApiService {
  // GET请求
  static async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.get<T>(url, config)
    return response.data
  }

  // POST请求
  static async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.post<T>(url, data, config)
    return response.data
  }

  // PUT请求
  static async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.put<T>(url, data, config)
    return response.data
  }

  // PATCH请求
  static async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.patch<T>(url, data, config)
    return response.data
  }

  // DELETE请求
  static async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await api.delete<T>(url, config)
    return response.data
  }

  // 文件上传
  static async upload<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    }

    const response = await api.post<T>(url, formData, config)
    return response.data
  }

  // 文件下载
  static async download(url: string, filename?: string): Promise<void> {
    const response = await api.get(url, {
      responseType: 'blob'
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// AI专用API服务
export class AIApiService {
  // AI POST请求（使用更长的超时时间）
  static async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await aiApi.post<T>(url, data, config)
    return response.data
  }

  // AI GET请求
  static async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await aiApi.get<T>(url, config)
    return response.data
  }

  // AI PUT请求
  static async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await aiApi.put<T>(url, data, config)
    return response.data
  }

  // AI DELETE请求
  static async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await aiApi.delete<T>(url, config)
    return response.data
  }
}

export { aiApi }
export default api
