using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// Ollama本地AI服务提供商
/// </summary>
public class OllamaProvider : IAIProvider
{
    private readonly ILogger<OllamaProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly OllamaConfig _config;
    private readonly AIUsageStats _usageStats;

    public string Name => "Ollama";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "llama2", "codellama", "mistral", "qwen", "deepseek-coder" };

    public OllamaProvider(
        ILogger<OllamaProvider> logger,
        HttpClient httpClient,
        IOptions<OllamaConfig> config)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config.Value;
        _usageStats = new AIUsageStats();

        // 配置HttpClient
        _httpClient.BaseAddress = new Uri(_config.Endpoint);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Ollama开始生成文本，模型: {Model}", config.Model);

            // Ollama API格式
            var requestBody = new
            {
                model = GetOllamaModel(config.Model),
                prompt = prompt,
                stream = false,
                options = new
                {
                    temperature = config.Temperature,
                    top_p = config.TopP,
                    num_predict = config.MaxTokens
                }
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("api/generate", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Ollama API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"Ollama API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var generatedText = responseData
                .GetProperty("response")
                .GetString() ?? string.Empty;

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("Ollama文本生成完成，长度: {Length}", generatedText.Length);
            return generatedText;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Ollama文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Ollama开始获取向量嵌入");

            var requestBody = new
            {
                model = "nomic-embed-text",
                prompt = text
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("api/embeddings", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Ollama嵌入API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);

                // 如果嵌入失败，返回模拟数据
                return GenerateMockEmbedding();
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var embeddingArray = responseData
                .GetProperty("embedding")
                .EnumerateArray()
                .Select(x => x.GetSingle())
                .ToArray();

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("Ollama向量嵌入获取完成，维度: {Dimension}", embeddingArray.Length);
            return embeddingArray;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Ollama向量嵌入获取失败，返回模拟数据");
            return GenerateMockEmbedding();
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            // 检查Ollama服务是否可用
            var response = await _httpClient.GetAsync("api/tags");
            IsAvailable = response.IsSuccessStatusCode;

            _logger.LogInformation("Ollama健康检查完成，状态: {Status}", IsAvailable ? "正常" : "异常");
            return IsAvailable;
        }
        catch (Exception ex)
        {
            IsAvailable = false;
            _logger.LogError(ex, "Ollama健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    /// <summary>
    /// 获取可用模型列表
    /// </summary>
    public async Task<List<string>> GetAvailableModelsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/tags");
            if (!response.IsSuccessStatusCode)
            {
                return SupportedModels;
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var models = responseData
                .GetProperty("models")
                .EnumerateArray()
                .Select(model => model.GetProperty("name").GetString() ?? "")
                .Where(name => !string.IsNullOrEmpty(name))
                .ToList();

            return models.Any() ? models : SupportedModels;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Ollama模型列表失败");
            return SupportedModels;
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取Ollama模型名称
    /// </summary>
    private string GetOllamaModel(string model)
    {
        return model.ToLower() switch
        {
            "llama2" => "llama2",
            "codellama" => "codellama",
            "mistral" => "mistral",
            "qwen" => "qwen",
            "deepseek-coder" => "deepseek-coder",
            _ => "llama2"
        };
    }

    /// <summary>
    /// 生成模拟嵌入向量
    /// </summary>
    private float[] GenerateMockEmbedding()
    {
        var embedding = new float[768]; // Ollama通常使用768维
        var random = new Random();
        for (int i = 0; i < embedding.Length; i++)
        {
            embedding[i] = (float)(random.NextDouble() * 2 - 1);
        }
        return embedding;
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success, JsonElement? responseData = null)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        // 更新平均响应时间
        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        // Ollama通常不返回Token使用量，使用估算
        if (success)
        {
            _usageStats.TotalTokens += Random.Shared.Next(50, 500);
        }

        _usageStats.LastUpdated = DateTime.Now;
    }

    #endregion
}

/// <summary>
/// Ollama配置
/// </summary>
public class OllamaConfig
{
    /// <summary>
    /// API端点
    /// </summary>
    public string Endpoint { get; set; } = "http://localhost:11434/";

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300; // 5分钟超时

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}
