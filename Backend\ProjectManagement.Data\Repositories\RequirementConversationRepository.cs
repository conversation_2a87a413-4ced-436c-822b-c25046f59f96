using SqlSugar;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Models;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 需求对话仓储实现
/// </summary>
public class RequirementConversationRepository : IRequirementConversationRepository
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<RequirementConversationRepository> _logger;

    public RequirementConversationRepository(ISqlSugarClient db, ILogger<RequirementConversationRepository> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 根据会话ID获取对话历史（按时间排序，用于构建AI上下文）
    /// </summary>
    public async Task<List<RequirementConversation>> GetConversationHistoryAsync(string conversationId, int limit = 10)
    {
        try
        {
            return await _db.Queryable<RequirementConversation>()
                .Where(x => x.ConversationId == conversationId)
                .OrderByDescending(x => x.Timestamp)
                .Take(limit)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话历史失败，会话ID: {ConversationId}", conversationId);
            throw;
        }
    }

    /// <summary>
    /// 根据项目和用户获取最近的对话历史
    /// </summary>
    public async Task<List<RequirementConversation>> GetRecentConversationsAsync(int projectId, int userId, int limit = 5)
    {
        try
        {
            return await _db.Queryable<RequirementConversation>()
                .Where(x => x.ProjectId == projectId && x.UserId == userId)
                .OrderByDescending(x => x.Timestamp)
                .Take(limit)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最近对话失败，项目ID: {ProjectId}, 用户ID: {UserId}", projectId, userId);
            throw;
        }
    }

    /// <summary>
    /// 创建对话记录
    /// </summary>
    public async Task<RequirementConversation> CreateConversationAsync(RequirementConversation conversation)
    {
        try
        {
            conversation.Timestamp = DateTime.UtcNow;
            var result = await _db.Insertable(conversation).ExecuteReturnEntityAsync();
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建对话记录失败: {ConversationId}", conversation.ConversationId);
            throw;
        }
    }

    /// <summary>
    /// 更新对话记录
    /// </summary>
    public async Task<RequirementConversation> UpdateConversationAsync(RequirementConversation conversation)
    {
        try
        {
            await _db.Updateable(conversation).ExecuteCommandAsync();
            return conversation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新对话记录失败: {ConversationId}", conversation.ConversationId);
            throw;
        }
    }

    /// <summary>
    /// 获取对话的token统计信息
    /// </summary>
    public async Task<ConversationTokenStats> GetConversationTokenStatsAsync(string conversationId)
    {
        try
        {
            var conversations = await _db.Queryable<RequirementConversation>()
                .Where(x => x.ConversationId == conversationId)
                .ToListAsync();

            var totalUserTokens = conversations.Sum(x => EstimateTokenCount(x.UserMessage));
            var totalAiTokens = conversations.Sum(x => EstimateTokenCount(x.AIResponse ?? ""));

            return new ConversationTokenStats
            {
                ConversationId = conversationId,
                TotalMessages = conversations.Count,
                EstimatedUserTokens = totalUserTokens,
                EstimatedAiTokens = totalAiTokens,
                EstimatedTotalTokens = totalUserTokens + totalAiTokens
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取对话token统计失败: {ConversationId}", conversationId);
            throw;
        }
    }

    /// <summary>
    /// 简单的token估算（1个中文字符约等于2个token，1个英文单词约等于1.3个token）
    /// </summary>
    private int EstimateTokenCount(string text)
    {
        if (string.IsNullOrEmpty(text)) return 0;

        var chineseCharCount = text.Count(c => c >= 0x4e00 && c <= 0x9fff);
        var englishWordCount = text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
        var otherCharCount = text.Length - chineseCharCount;

        return (int)(chineseCharCount * 2 + englishWordCount * 1.3 + otherCharCount * 0.5);
    }

    public async Task<List<RequirementConversation>> GetConversationsByProjectAsync(int projectId)
    {
        try
        {
            return await _db.Queryable<RequirementConversation>()
                .Where(x => x.ProjectId == projectId)
                .OrderBy(x => x.Timestamp)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目获取需求对话失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<RequirementConversation>> GetConversationsByUserAsync(int userId)
    {
        try
        {
            return await _db.Queryable<RequirementConversation>()
                .Where(x => x.UserId == userId)
                .OrderBy(x => x.Timestamp)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据用户获取需求对话失败，用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<RequirementConversation?> GetLatestConversationAsync(int projectId, int userId)
    {
        try
        {
            return await _db.Queryable<RequirementConversation>()
                .Where(x => x.ProjectId == projectId && x.UserId == userId)
                .OrderByDescending(x => x.Timestamp)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最新需求对话失败，项目ID: {ProjectId}, 用户ID: {UserId}", projectId, userId);
            throw;
        }
    }

    public async Task<ProjectManagement.Core.Interfaces.PagedResult<RequirementConversation>> SearchConversationsAsync(
        string keyword,
        int? projectId = null,
        int? userId = null,
        string? messageType = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<RequirementConversation>();

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x =>
                    x.UserMessage.Contains(keyword) ||
                    (x.AIResponse != null && x.AIResponse.Contains(keyword)));
            }

            // 项目过滤
            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            // 用户过滤
            if (userId.HasValue)
            {
                query = query.Where(x => x.UserId == userId.Value);
            }

            // 消息类型过滤
            if (!string.IsNullOrWhiteSpace(messageType))
            {
                query = query.Where(x => x.MessageType == messageType);
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(x => x.Timestamp)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new ProjectManagement.Core.Interfaces.PagedResult<RequirementConversation>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索需求对话失败");
            throw;
        }
    }

    public async Task<RequirementConversationStatistics> GetConversationStatisticsAsync(int? projectId = null)
    {
        try
        {
            var query = _db.Queryable<RequirementConversation>();

            if (projectId.HasValue)
            {
                query = query.Where(x => x.ProjectId == projectId.Value);
            }

            var totalConversations = await query.CountAsync();
            var todayConversations = await query
                .Where(x => x.Timestamp >= DateTime.Today)
                .CountAsync();

            var messageTypeStats = await query
                .GroupBy(x => x.MessageType)
                .Select(x => new { MessageType = x.MessageType, Count = SqlFunc.AggregateCount(x.Id) })
                .ToListAsync();

            return new RequirementConversationStatistics
            {
                TotalConversations = totalConversations,
                TodayConversations = todayConversations,
                MessageTypeBreakdown = messageTypeStats.ToDictionary(x => x.MessageType, x => x.Count)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求对话统计失败");
            throw;
        }
    }
}
