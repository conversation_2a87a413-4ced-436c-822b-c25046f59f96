using ProjectManagementAI.WPF.Services;

namespace ProjectManagementAI.WPF.Tests.TestHelpers
{
    /// <summary>
    /// 测试数据工厂 - 提供各种测试数据的创建方法
    /// </summary>
    public static class TestDataFactory
    {
        #region 项目模板数据

        /// <summary>
        /// 创建标准项目模板列表
        /// </summary>
        public static List<ProjectTemplate> CreateStandardProjectTemplates()
        {
            return new List<ProjectTemplate>
            {
                CreateNewProjectTemplate(),
                CreateFeatureEnhancementTemplate(),
                CreateMaintenanceTemplate(),
                CreateRefactoringTemplate(),
                CreateMigrationTemplate()
            };
        }

        /// <summary>
        /// 创建新项目模板
        /// </summary>
        public static ProjectTemplate CreateNewProjectTemplate()
        {
            return new ProjectTemplate
            {
                Name = "电商平台",
                Description = "全新的电商平台项目，包含用户管理、商品管理、订单处理等核心功能",
                ProjectType = "NEW_PROJECT",
                TechnologyStack = "Vue.js, ASP.NET Core, SQL Server",
                CustomProperties = new Dictionary<string, object>
                {
                    { "EstimatedHours", 500 },
                    { "TeamSize", 5 },
                    { "Priority", "High" },
                    { "Budget", 100000 }
                }
            };
        }

        /// <summary>
        /// 创建功能增强模板
        /// </summary>
        public static ProjectTemplate CreateFeatureEnhancementTemplate()
        {
            return new ProjectTemplate
            {
                Name = "支付功能增强",
                Description = "为现有电商平台添加多种支付方式支持",
                ProjectType = "FEATURE_ENHANCEMENT",
                TechnologyStack = "Vue.js, ASP.NET Core",
                ParentProjectId = 1,
                CustomProperties = new Dictionary<string, object>
                {
                    { "EstimatedHours", 120 },
                    { "TeamSize", 2 },
                    { "Priority", "Medium" }
                }
            };
        }

        /// <summary>
        /// 创建维护项目模板
        /// </summary>
        public static ProjectTemplate CreateMaintenanceTemplate()
        {
            return new ProjectTemplate
            {
                Name = "系统维护优化",
                Description = "修复已知Bug，优化系统性能，更新安全补丁",
                ProjectType = "MAINTENANCE",
                TechnologyStack = "Vue.js, ASP.NET Core, SQL Server",
                ParentProjectId = 1,
                CustomProperties = new Dictionary<string, object>
                {
                    { "EstimatedHours", 80 },
                    { "TeamSize", 3 },
                    { "Priority", "High" }
                }
            };
        }

        /// <summary>
        /// 创建重构项目模板
        /// </summary>
        public static ProjectTemplate CreateRefactoringTemplate()
        {
            return new ProjectTemplate
            {
                Name = "架构重构",
                Description = "重构现有代码架构，提升代码质量和可维护性",
                ProjectType = "REFACTORING",
                TechnologyStack = "Vue.js, ASP.NET Core",
                ParentProjectId = 1,
                CustomProperties = new Dictionary<string, object>
                {
                    { "EstimatedHours", 200 },
                    { "TeamSize", 4 },
                    { "Priority", "Medium" }
                }
            };
        }

        /// <summary>
        /// 创建迁移项目模板
        /// </summary>
        public static ProjectTemplate CreateMigrationTemplate()
        {
            return new ProjectTemplate
            {
                Name = "云平台迁移",
                Description = "将现有系统迁移到云平台，提升系统可扩展性",
                ProjectType = "MIGRATION",
                TechnologyStack = "Vue.js, ASP.NET Core, Azure",
                ParentProjectId = 1,
                CustomProperties = new Dictionary<string, object>
                {
                    { "EstimatedHours", 300 },
                    { "TeamSize", 6 },
                    { "Priority", "Low" }
                }
            };
        }

        #endregion

        #region 批量操作结果数据

        /// <summary>
        /// 创建成功的批量操作结果
        /// </summary>
        public static BatchOperationResult CreateSuccessfulBatchResult(int totalCount)
        {
            return new BatchOperationResult
            {
                TotalCount = totalCount,
                SuccessCount = totalCount,
                FailureCount = 0,
                Errors = new List<string>(),
                Warnings = new List<string>(),
                Duration = TimeSpan.FromSeconds(totalCount * 0.5),
                Metadata = new Dictionary<string, object>
                {
                    { "StartTime", DateTime.Now.AddSeconds(-totalCount * 0.5) },
                    { "EndTime", DateTime.Now },
                    { "ProcessedBy", "TestUser" }
                }
            };
        }

        /// <summary>
        /// 创建部分失败的批量操作结果
        /// </summary>
        public static BatchOperationResult CreatePartialFailureBatchResult(int totalCount, int failureCount)
        {
            var successCount = totalCount - failureCount;
            var errors = Enumerable.Range(1, failureCount)
                .Select(i => $"项目 {i} 创建失败: 模拟错误")
                .ToList();

            return new BatchOperationResult
            {
                TotalCount = totalCount,
                SuccessCount = successCount,
                FailureCount = failureCount,
                Errors = errors,
                Warnings = new List<string> { "部分项目创建失败，请检查错误信息" },
                Duration = TimeSpan.FromSeconds(totalCount * 0.8),
                Metadata = new Dictionary<string, object>
                {
                    { "PartialFailure", true },
                    { "FailureRate", (double)failureCount / totalCount }
                }
            };
        }

        #endregion

        #region 优化结果数据

        /// <summary>
        /// 创建优化结果
        /// </summary>
        public static OptimizationResult CreateOptimizationResult()
        {
            return new OptimizationResult
            {
                OverallScore = 0.85,
                Suggestions = new List<OptimizationSuggestion>
                {
                    new()
                    {
                        ProjectId = 1,
                        ProjectName = "电商平台",
                        Category = "性能优化",
                        Title = "数据库查询优化",
                        Description = "优化商品查询的SQL语句，预计可提升30%的查询性能",
                        Impact = 0.8,
                        Confidence = 0.9,
                        ActionType = "CodeOptimization"
                    },
                    new()
                    {
                        ProjectId = 2,
                        ProjectName = "用户管理系统",
                        Category = "架构优化",
                        Title = "缓存策略改进",
                        Description = "引入Redis缓存，减少数据库访问频率",
                        Impact = 0.7,
                        Confidence = 0.85,
                        ActionType = "ArchitectureImprovement"
                    }
                },
                Metrics = new Dictionary<string, double>
                {
                    { "PerformanceScore", 0.82 },
                    { "MaintainabilityScore", 0.88 },
                    { "SecurityScore", 0.85 },
                    { "ScalabilityScore", 0.80 }
                }
            };
        }

        #endregion

        #region 向量分析结果数据

        /// <summary>
        /// 创建向量分析结果
        /// </summary>
        public static VectorAnalysisResult CreateVectorAnalysisResult()
        {
            return new VectorAnalysisResult
            {
                ProjectAnalyses = new List<ProjectAnalysis>
                {
                    new()
                    {
                        ProjectId = 1,
                        ProjectName = "电商平台",
                        ComplexityScore = 0.75,
                        RiskScore = 0.45,
                        QualityScore = 0.88,
                        DetailedMetrics = new Dictionary<string, double>
                        {
                            { "CodeComplexity", 0.7 },
                            { "TestCoverage", 0.85 },
                            { "Documentation", 0.6 },
                            { "Performance", 0.8 }
                        },
                        Recommendations = new List<string>
                        {
                            "增加单元测试覆盖率",
                            "完善API文档",
                            "优化数据库查询性能"
                        }
                    },
                    new()
                    {
                        ProjectId = 2,
                        ProjectName = "用户管理系统",
                        ComplexityScore = 0.55,
                        RiskScore = 0.25,
                        QualityScore = 0.92,
                        DetailedMetrics = new Dictionary<string, double>
                        {
                            { "CodeComplexity", 0.5 },
                            { "TestCoverage", 0.95 },
                            { "Documentation", 0.9 },
                            { "Performance", 0.85 }
                        },
                        Recommendations = new List<string>
                        {
                            "保持当前的高质量标准",
                            "考虑重构部分复杂逻辑"
                        }
                    }
                },
                GlobalMetrics = new Dictionary<string, double>
                {
                    { "AverageComplexity", 0.65 },
                    { "AverageRisk", 0.35 },
                    { "AverageQuality", 0.90 },
                    { "TotalProjects", 2 }
                },
                Insights = new List<string>
                {
                    "整体项目质量较高，平均质量分数为0.90",
                    "项目复杂度适中，风险可控",
                    "建议重点关注文档完善和性能优化"
                }
            };
        }

        #endregion

        #region 相似性结果数据

        /// <summary>
        /// 创建相似性结果列表
        /// </summary>
        public static List<SimilarityResult> CreateSimilarityResults()
        {
            return new List<SimilarityResult>
            {
                new()
                {
                    ProjectId = 2,
                    ProjectName = "在线商城",
                    SimilarityScore = 0.92,
                    SimilarityFactors = new List<string>
                    {
                        "相同的技术栈 (Vue.js + ASP.NET Core)",
                        "相似的业务领域 (电商)",
                        "相近的项目规模"
                    }
                },
                new()
                {
                    ProjectId = 3,
                    ProjectName = "库存管理系统",
                    SimilarityScore = 0.78,
                    SimilarityFactors = new List<string>
                    {
                        "相同的后端技术 (ASP.NET Core)",
                        "相似的数据结构",
                        "类似的用户权限管理"
                    }
                }
            };
        }

        #endregion

        #region 风险预测数据

        /// <summary>
        /// 创建风险预测结果
        /// </summary>
        public static List<RiskPrediction> CreateRiskPredictions()
        {
            return new List<RiskPrediction>
            {
                new()
                {
                    ProjectId = 1,
                    ProjectName = "大型企业系统",
                    RiskScore = 0.75,
                    RiskFactors = new List<RiskFactor>
                    {
                        new()
                        {
                            Category = "技术风险",
                            Description = "使用了较新的技术栈，团队经验不足",
                            Impact = 0.8,
                            Probability = 0.6
                        },
                        new()
                        {
                            Category = "进度风险",
                            Description = "项目规模较大，时间估算可能不准确",
                            Impact = 0.9,
                            Probability = 0.7
                        }
                    },
                    Recommendations = new List<string>
                    {
                        "增加技术培训和知识分享",
                        "细化项目计划，增加缓冲时间",
                        "建立定期的风险评估机制"
                    }
                },
                new()
                {
                    ProjectId = 2,
                    ProjectName = "简单工具系统",
                    RiskScore = 0.25,
                    RiskFactors = new List<RiskFactor>
                    {
                        new()
                        {
                            Category = "需求风险",
                            Description = "需求相对简单，变更可能性较小",
                            Impact = 0.3,
                            Probability = 0.4
                        }
                    },
                    Recommendations = new List<string>
                    {
                        "保持当前的开发节奏",
                        "定期与用户确认需求"
                    }
                }
            };
        }

        #endregion

        #region 自动化任务数据

        /// <summary>
        /// 创建自动化任务列表
        /// </summary>
        public static List<AutomationTask> CreateAutomationTasks()
        {
            return new List<AutomationTask>
            {
                new()
                {
                    TaskType = "CodeGeneration",
                    ProjectId = 1,
                    Priority = 1,
                    Parameters = new Dictionary<string, object>
                    {
                        { "TemplateType", "CRUD" },
                        { "EntityName", "Product" },
                        { "IncludeTests", true }
                    }
                },
                new()
                {
                    TaskType = "DocumentGeneration",
                    ProjectId = 1,
                    Priority = 2,
                    Parameters = new Dictionary<string, object>
                    {
                        { "DocumentType", "API" },
                        { "Format", "Swagger" },
                        { "IncludeExamples", true }
                    }
                },
                new()
                {
                    TaskType = "TestGeneration",
                    ProjectId = 2,
                    Priority = 3,
                    Parameters = new Dictionary<string, object>
                    {
                        { "TestType", "Unit" },
                        { "Coverage", "80%" },
                        { "Framework", "xUnit" }
                    }
                }
            };
        }

        #endregion

        #region 资源分配数据

        /// <summary>
        /// 创建资源分配结果
        /// </summary>
        public static ResourceAllocationResult CreateResourceAllocationResult()
        {
            return new ResourceAllocationResult
            {
                EfficiencyScore = 0.88,
                Allocations = new List<ResourceAllocation>
                {
                    new()
                    {
                        ProjectId = 1,
                        ProjectName = "电商平台",
                        ResourceRequirements = new Dictionary<string, double>
                        {
                            { "前端开发", 2.0 },
                            { "后端开发", 2.0 },
                            { "测试", 1.0 },
                            { "UI设计", 0.5 }
                        },
                        RecommendedTeamMembers = new List<string>
                        {
                            "张三 (前端专家)",
                            "李四 (后端专家)",
                            "王五 (全栈开发)",
                            "赵六 (测试工程师)"
                        },
                        RecommendedStartDate = DateTime.Now.AddDays(7),
                        RecommendedEndDate = DateTime.Now.AddDays(67)
                    }
                },
                Recommendations = new List<string>
                {
                    "建议优先安排有电商项目经验的开发人员",
                    "考虑在项目中期增加一名UI设计师",
                    "建议设置每周的进度检查点"
                }
            };
        }

        #endregion
    }
}
