using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using ProjectManagementAI.WPF.Services;
using ProjectManagementAI.WPF.Views;
using ProjectManagementAI.WPF.ViewModels;

namespace ProjectManagementAI.WPF.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;
        private readonly IProjectService _projectService;
        private readonly IAIService _aiService;
        private readonly IVectorOperationService _vectorOperationService;
        private readonly IDataAnalysisService _dataAnalysisService;
        private readonly IKnowledgeGraphService _knowledgeGraphService;

        // ViewModels for different views
        private readonly VectorOperationViewModel _vectorOperationViewModel;

        [ObservableProperty]
        private object? _currentView;

        [ObservableProperty]
        private int _selectedNavigationIndex;

        [ObservableProperty]
        private string _statusMessage = "就绪";

        [ObservableProperty]
        private string _connectionStatusText = "已连接";

        [ObservableProperty]
        private bool _connectionStatus = true;

        [ObservableProperty]
        private bool _isLoading;

        public MainViewModel(
            ILogger<MainViewModel> logger,
            IProjectService projectService,
            IAIService aiService,
            IVectorOperationService vectorOperationService,
            IDataAnalysisService dataAnalysisService,
            IKnowledgeGraphService knowledgeGraphService,
            VectorOperationViewModel vectorOperationViewModel)
        {
            _logger = logger;
            _projectService = projectService;
            _aiService = aiService;
            _vectorOperationService = vectorOperationService;
            _dataAnalysisService = dataAnalysisService;
            _knowledgeGraphService = knowledgeGraphService;
            _vectorOperationViewModel = vectorOperationViewModel;

            // 初始化命令
            InitializeCommands();

            // 设置默认视图
            CurrentView = CreatePlaceholderView("仪表板");
        }

        private void InitializeCommands()
        {
            SyncDataCommand = new AsyncRelayCommand(SyncDataAsync);
            OpenSettingsCommand = new RelayCommand(OpenSettings);
            ShowAboutCommand = new RelayCommand(ShowAbout);
            BatchCreateProjectsCommand = new AsyncRelayCommand(BatchCreateProjectsAsync);
            BatchAnalyzeCommand = new AsyncRelayCommand(BatchAnalyzeAsync);
            VectorOptimizeCommand = new AsyncRelayCommand(VectorOptimizeAsync);
        }

        #region Commands

        public IAsyncRelayCommand? SyncDataCommand { get; private set; }
        public IRelayCommand? OpenSettingsCommand { get; private set; }
        public IRelayCommand? ShowAboutCommand { get; private set; }
        public IAsyncRelayCommand? BatchCreateProjectsCommand { get; private set; }
        public IAsyncRelayCommand? BatchAnalyzeCommand { get; private set; }
        public IAsyncRelayCommand? VectorOptimizeCommand { get; private set; }

        #endregion

        #region Navigation

        partial void OnSelectedNavigationIndexChanged(int value)
        {
            NavigateToView(value);
        }

        private void NavigateToView(int index)
        {
            try
            {
                CurrentView = index switch
                {
                    0 => CreatePlaceholderView("仪表板"),
                    1 => CreateVectorOperationView(),
                    2 => CreatePlaceholderView("数据分析"),
                    3 => CreatePlaceholderView("知识图谱"),
                    _ => CreatePlaceholderView("仪表板")
                };

                StatusMessage = $"已切换到: {GetViewName(index)}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到视图失败: {Index}", index);
                StatusMessage = "导航失败";
            }
        }

        private object CreatePlaceholderView(string viewName)
        {
            return new System.Windows.Controls.TextBlock
            {
                Text = $"{viewName} - 开发中...",
                FontSize = 24,
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                VerticalAlignment = System.Windows.VerticalAlignment.Center,
                Foreground = System.Windows.Media.Brushes.Gray
            };
        }

        private object CreateVectorOperationView()
        {
            var view = new VectorOperationView();
            view.DataContext = _vectorOperationViewModel;
            return view;
        }

        private string GetViewName(int index)
        {
            return index switch
            {
                0 => "仪表板",
                1 => "向量化操作",
                2 => "数据分析",
                3 => "知识图谱",
                _ => "未知视图"
            };
        }

        #endregion

        #region Command Implementations

        private async Task SyncDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在同步数据...";

                await _projectService.SyncWithServerAsync();

                StatusMessage = "数据同步完成";
                ConnectionStatus = true;
                ConnectionStatusText = "已连接";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据同步失败");
                StatusMessage = "数据同步失败";
                ConnectionStatus = false;
                ConnectionStatusText = "连接失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OpenSettings()
        {
            try
            {
                // TODO: 创建设置窗口
                System.Windows.MessageBox.Show("设置功能开发中...", "提示", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开设置窗口失败");
                System.Windows.MessageBox.Show("打开设置失败", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void ShowAbout()
        {
            try
            {
                // TODO: 创建关于窗口
                System.Windows.MessageBox.Show("AI项目管理系统 v1.0\n基于WPF和Material Design的智能项目管理工具", "关于", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开关于窗口失败");
                System.Windows.MessageBox.Show("打开关于窗口失败", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 批量创建项目 - 向量化操作的核心功能
        /// </summary>
        private async Task BatchCreateProjectsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行批量创建...";

                // TODO: 创建批量创建对话框
                // 暂时使用示例数据
                var projectTemplates = new List<ProjectTemplate>
                {
                    new() { Name = "示例项目1", ProjectType = "NEW_PROJECT", TechnologyStack = "Vue.js" },
                    new() { Name = "示例项目2", ProjectType = "FEATURE_ENHANCEMENT", TechnologyStack = "React" }
                };

                // 使用向量化操作服务批量创建
                var results = await _vectorOperationService.BatchCreateProjectsAsync(projectTemplates);

                StatusMessage = $"批量创建完成，成功创建 {results.SuccessCount} 个项目";

                // 刷新项目列表
                await RefreshCurrentViewAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量创建项目失败");
                StatusMessage = "批量创建失败";
                System.Windows.MessageBox.Show($"批量创建失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 批量分析 - AI驱动的向量化分析
        /// </summary>
        private async Task BatchAnalyzeAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行AI批量分析...";

                // 获取所有项目进行分析
                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList(); // 转换为List以获取Count

                // 使用AI服务进行向量化分析
                var analysisResults = await _aiService.BatchAnalyzeProjectsAsync(projectList);

                // TODO: 创建分析结果窗口
                // 暂时显示消息框
                System.Windows.MessageBox.Show($"分析完成！共分析了 {projectList.Count} 个项目", "分析结果", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                StatusMessage = $"批量分析完成，分析了 {projectList.Count} 个项目";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量分析失败");
                StatusMessage = "批量分析失败";
                System.Windows.MessageBox.Show($"批量分析失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 向量优化 - 基于机器学习的智能优化
        /// </summary>
        private async Task VectorOptimizeAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行智能优化...";

                // 使用向量化操作服务进行智能优化
                var optimizationResults = await _vectorOperationService.OptimizeProjectsAsync();
                
                // TODO: 创建优化结果窗口
                // 暂时显示消息框
                System.Windows.MessageBox.Show($"智能优化完成！总体评分: {optimizationResults.OverallScore:P0}", "优化结果", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                
                StatusMessage = "智能优化完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "智能优化失败");
                StatusMessage = "智能优化失败";
                System.Windows.MessageBox.Show($"智能优化失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Public Methods

        public async Task InitializeAsync()
        {
            try
            {
                StatusMessage = "正在初始化...";
                
                // 检查服务器连接
                await CheckServerConnectionAsync();
                
                // 加载初始数据
                await LoadInitialDataAsync();
                
                StatusMessage = "初始化完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化失败");
                StatusMessage = "初始化失败";
            }
        }

        public void Cleanup()
        {
            try
            {
                // 清理资源
                _logger.LogInformation("应用程序正在关闭，清理资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理资源失败");
            }
        }

        #endregion

        #region Private Methods

        private async Task CheckServerConnectionAsync()
        {
            try
            {
                var isConnected = await _projectService.CheckConnectionAsync();
                ConnectionStatus = isConnected;
                ConnectionStatusText = isConnected ? "已连接" : "连接失败";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查服务器连接失败");
                ConnectionStatus = false;
                ConnectionStatusText = "连接失败";
            }
        }

        private async Task LoadInitialDataAsync()
        {
            try
            {
                // 加载基础数据
                await _projectService.LoadCacheDataAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载初始数据失败");
                throw;
            }
        }

        private async Task RefreshCurrentViewAsync()
        {
            try
            {
                // TODO: 实现视图刷新逻辑
                // 根据当前视图刷新数据
                await Task.Delay(100); // 模拟刷新操作
                StatusMessage = "视图已刷新";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新当前视图失败");
            }
        }

        #endregion
    }
}
