# JavaScript文件使用指南

## 文件结构

```
js_scripts/
├── copilot_operations.js    # Copilot相关操作
├── vscode_operations.js     # VSCode界面操作
└── editor_operations.js     # 编辑器操作
```

## 使用方法

### 1. 调用JS文件中的函数

```python
# 调用单个函数
result = self.call_js_function('js_scripts/vscode_operations.js', 'getVSCodeStatus')

# 调用带参数的函数
result = self.call_js_function('js_scripts/copilot_operations.js', 'sendMessageToCopilot', '你好')

# 调用多参数函数
result = self.call_js_function('js_scripts/editor_operations.js', 'replaceText', '旧文本', '新文本')
```

### 2. 执行整个JS文件

```python
# 执行整个JS文件（定义所有函数）
self.execute_js_file('js_scripts/vscode_operations.js')

# 然后可以调用其中的函数
result = self.execute_js('getVSCodeStatus()')
```

### 3. 加载JS文件内容并自定义

```python
# 加载JS文件内容
js_code = self.load_js_file('js_scripts/vscode_operations.js')

# 添加自定义代码
custom_code = js_code + """
// 自定义代码
const status = getVSCodeStatus();
console.log('状态:', status);
return status;
"""

# 执行自定义代码
result = self.execute_js(custom_code)
```

## 可用的JavaScript函数

### copilot_operations.js

| 函数名 | 参数 | 功能 | 返回值 |
|--------|------|------|--------|
| `sendMessageToCopilot(message)` | message: 字符串 | 发送消息到Copilot | `{success: bool, message: string}` |
| `getChatMessages()` | 无 | 获取聊天消息列表 | 消息数组 |
| `isCopilotResponding()` | 无 | 检查Copilot是否正在响应 | boolean |
| `clearChatHistory()` | 无 | 清空聊天历史 | `{success: bool, message: string}` |
| `startNewChat()` | 无 | 开始新的聊天会话 | `{success: bool, message: string}` |

### vscode_operations.js

| 函数名 | 参数 | 功能 | 返回值 |
|--------|------|------|--------|
| `getVSCodeStatus()` | 无 | 获取VSCode状态信息 | 状态对象 |
| `getActiveEditorContent(maxLength)` | maxLength: 数字 | 获取当前编辑器内容 | `{success: bool, content: string}` |
| `clickButtonByText(buttonText)` | buttonText: 字符串 | 点击包含指定文本的按钮 | `{success: bool, text: string}` |
| `getOpenFiles()` | 无 | 获取已打开的文件列表 | 文件数组 |
| `switchToFile(fileName)` | fileName: 字符串 | 切换到指定文件 | `{success: bool, title: string}` |
| `saveCurrentFile()` | 无 | 保存当前文件 | `{success: bool, message: string}` |
| `openCommandPalette()` | 无 | 打开命令面板 | `{success: bool, message: string}` |
| `findInFile(searchText)` | searchText: 字符串 | 在文件中查找 | `{success: bool, message: string}` |
| `toggleSidebar()` | 无 | 切换侧边栏 | `{success: bool, message: string}` |
| `togglePanel()` | 无 | 切换面板 | `{success: bool, message: string}` |

### editor_operations.js

| 函数名 | 参数 | 功能 | 返回值 |
|--------|------|------|--------|
| `insertTextAtCursor(text)` | text: 字符串 | 在光标处插入文本 | `{success: bool, message: string}` |
| `selectAllText()` | 无 | 选择所有文本 | `{success: bool, message: string}` |
| `copySelectedText()` | 无 | 复制选中文本 | `{success: bool, message: string}` |
| `pasteText()` | 无 | 粘贴文本 | `{success: bool, message: string}` |
| `undoLastAction()` | 无 | 撤销操作 | `{success: bool, message: string}` |
| `redoLastAction()` | 无 | 重做操作 | `{success: bool, message: string}` |
| `formatCode()` | 无 | 格式化代码 | `{success: bool, message: string}` |
| `toggleComment()` | 无 | 切换注释 | `{success: bool, message: string}` |
| `goToLine(lineNumber)` | lineNumber: 数字 | 跳转到指定行 | `{success: bool, message: string}` |
| `getCursorPosition()` | 无 | 获取光标位置 | `{success: bool, line: number, column: number}` |
| `replaceText(searchText, replaceText)` | searchText, replaceText: 字符串 | 替换文本 | `{success: bool, message: string}` |

## 使用示例

### 示例1：Copilot自动化

```python
def copilot_workflow(self):
    # 发送消息
    result = self.call_js_function('js_scripts/copilot_operations.js', 'sendMessageToCopilot', '请解释这段代码')
    
    # 等待响应
    self.wait(3)
    
    # 检查是否还在响应
    responding = self.call_js_function('js_scripts/copilot_operations.js', 'isCopilotResponding')
    if responding:
        self.log("Copilot正在响应...")
    
    # 获取聊天消息
    messages = self.call_js_function('js_scripts/copilot_operations.js', 'getChatMessages')
    self.log(f"共有 {len(messages)} 条消息")
```

### 示例2：编辑器操作

```python
def editor_workflow(self):
    # 获取当前编辑器内容
    content = self.call_js_function('js_scripts/vscode_operations.js', 'getActiveEditorContent', 1000)
    
    # 选择所有文本
    self.call_js_function('js_scripts/editor_operations.js', 'selectAllText')
    
    # 复制文本
    self.call_js_function('js_scripts/editor_operations.js', 'copySelectedText')
    
    # 格式化代码
    self.call_js_function('js_scripts/editor_operations.js', 'formatCode')
    
    # 保存文件
    self.call_js_function('js_scripts/vscode_operations.js', 'saveCurrentFile')
```

### 示例3：界面操作

```python
def ui_workflow(self):
    # 获取VSCode状态
    status = self.call_js_function('js_scripts/vscode_operations.js', 'getVSCodeStatus')
    self.log(f"当前状态: {status}")
    
    # 获取打开的文件
    files = self.call_js_function('js_scripts/vscode_operations.js', 'getOpenFiles')
    for file in files:
        self.log(f"文件: {file['title']} {'(活动)' if file['isActive'] else ''}")
    
    # 切换侧边栏
    self.call_js_function('js_scripts/vscode_operations.js', 'toggleSidebar')
```

## 创建自定义JS文件

您可以创建自己的JS文件：

1. 在 `js_scripts/` 目录下创建新的 `.js` 文件
2. 定义您需要的函数
3. 在Python中调用这些函数

### 自定义JS文件模板

```javascript
// my_custom_operations.js

function myCustomFunction(param1, param2) {
    try {
        // 您的DOM操作代码
        const element = document.querySelector('.my-selector');
        if (element) {
            element.click();
            return { success: true, message: "操作成功" };
        }
        return { success: false, error: "元素未找到" };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

function anotherFunction() {
    // 另一个函数
    return { success: true, data: "some data" };
}
```

### 在Python中使用

```python
# 调用自定义函数
result = self.call_js_function('js_scripts/my_custom_operations.js', 'myCustomFunction', 'param1', 'param2')
```

## 调试技巧

1. **在JS函数中添加console.log()** 来调试
2. **返回详细的错误信息** 便于排查问题
3. **使用try-catch** 包装可能出错的代码
4. **在浏览器开发者工具中测试** JS代码
