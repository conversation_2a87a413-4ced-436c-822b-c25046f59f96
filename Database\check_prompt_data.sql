-- 检查提示词相关表的数据
USE ProjectManagement;

-- 检查分类表数据
SELECT '=== PromptCategories 分类数据 ===' as Info;
SELECT Id, Name, Description, TemplateCount, IsEnabled, CreatedTime 
FROM PromptCategories 
WHERE IsDeleted = 0
ORDER BY SortOrder, Name;

-- 检查模板表数据
SELECT '=== PromptTemplates 模板数据 ===' as Info;
SELECT Id, Name, CategoryId, TemplateType, TaskType, IsEnabled, UsageCount, CreatedTime
FROM PromptTemplates 
WHERE IsDeleted = 0
ORDER BY CategoryId, Name;

-- 检查每个分类下的模板数量
SELECT '=== 每个分类的模板数量统计 ===' as Info;
SELECT 
    c.Id as CategoryId,
    c.Name as CategoryName,
    c.TemplateCount as StoredCount,
    COUNT(t.Id) as ActualCount
FROM PromptCategories c
LEFT JOIN PromptTemplates t ON c.Id = t.CategoryId AND t.IsDeleted = 0 AND t.IsEnabled = 1
WHERE c.IsDeleted = 0 AND c.IsEnabled = 1
GROUP BY c.Id, c.Name, c.TemplateCount
ORDER BY c.SortOrder, c.Name;
