#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright安装脚本
自动安装Playwright及其浏览器驱动
"""

import subprocess
import sys
import os


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ {description}成功")
            if result.stdout:
                print(f"输出: {result.stdout}")
        else:
            print(f"❌ {description}失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ {description}异常: {e}")
        return False
    
    return True


def install_playwright():
    """安装Playwright"""
    print("🎭 Playwright安装程序")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Playwright需要Python 3.8或更高版本")
        return False
    
    # 1. 安装Playwright包
    if not run_command("pip install playwright", "安装Playwright包"):
        return False
    
    # 2. 安装浏览器驱动
    print("\n🌐 安装浏览器驱动...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    if not run_command("playwright install", "安装所有浏览器驱动"):
        # 如果全部安装失败，尝试只安装Chromium
        print("\n⚠️ 尝试只安装Chromium浏览器...")
        if not run_command("playwright install chromium", "安装Chromium浏览器"):
            return False
    
    # 3. 验证安装
    print("\n🔍 验证安装...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # 检查可用的浏览器
            browsers = []
            
            try:
                browser = p.chromium.launch(headless=True)
                browsers.append("Chromium")
                browser.close()
            except:
                pass
            
            try:
                browser = p.firefox.launch(headless=True)
                browsers.append("Firefox")
                browser.close()
            except:
                pass
            
            try:
                browser = p.webkit.launch(headless=True)
                browsers.append("WebKit")
                browser.close()
            except:
                pass
            
            if browsers:
                print(f"✅ 可用的浏览器: {', '.join(browsers)}")
                return True
            else:
                print("❌ 没有可用的浏览器")
                return False
                
    except ImportError:
        print("❌ Playwright导入失败")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("开始安装Playwright...")
    
    if install_playwright():
        print("\n🎉 Playwright安装成功！")
        print("\n📝 使用说明:")
        print("1. 重启Python程序")
        print("2. 在主界面选择'🎭 浏览器错误监控'选项卡")
        print("3. 点击'🚀 启动监控'开始监控浏览器错误")
        print("4. 输入URL并点击'🌐 导航'访问页面")
        print("5. 查看F12控制台错误和运行时错误")
        
        input("\n按回车键退出...")
    else:
        print("\n❌ Playwright安装失败！")
        print("\n🔧 手动安装步骤:")
        print("1. 打开命令提示符或PowerShell")
        print("2. 运行: pip install playwright")
        print("3. 运行: playwright install")
        print("4. 如果网络问题，可以只安装Chromium: playwright install chromium")
        
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
