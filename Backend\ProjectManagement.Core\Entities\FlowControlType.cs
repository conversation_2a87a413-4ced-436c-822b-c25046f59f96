using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 流程控制类型实体
    /// </summary>
    [SugarTable("FlowControlTypes", "流程控制类型表")]
    public class FlowControlType : BaseEntity
    {
        /// <summary>
        /// 流程控制类型值
        /// </summary>
        [SugarColumn(ColumnDescription = "流程控制类型值", Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "流程控制类型值不能为空")]
        [StringLength(50, ErrorMessage = "流程控制类型值长度不能超过50个字符")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [SugarColumn(ColumnDescription = "显示名称", Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "显示名称不能为空")]
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 流程控制描述
        /// </summary>
        [SugarColumn(ColumnDescription = "流程控制描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "流程控制描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [SugarColumn(ColumnDescription = "图标名称", Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [SugarColumn(ColumnDescription = "颜色标识", Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "排序顺序", IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用", IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否内置类型
        /// </summary>
        [SugarColumn(ColumnDescription = "是否内置类型", IsNullable = false)]
        public bool IsBuiltIn { get; set; } = false;

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        [SugarColumn(ColumnDescription = "参数架构JSON", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? ParameterSchema { get; set; }

        /// <summary>
        /// 执行类型(condition/loop/branch/jump/exit)
        /// </summary>
        [SugarColumn(ColumnDescription = "执行类型", Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "执行类型长度不能超过50个字符")]
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 是否需要目标步骤
        /// </summary>
        [SugarColumn(ColumnDescription = "是否需要目标步骤", IsNullable = false)]
        public bool RequiresTarget { get; set; } = false;

        /// <summary>
        /// 是否可以嵌套
        /// </summary>
        [SugarColumn(ColumnDescription = "是否可以嵌套", IsNullable = false)]
        public bool CanNest { get; set; } = false;
    }

    /// <summary>
    /// 流程控制执行类型枚举
    /// </summary>
    public static class FlowControlExecutionTypes
    {
        /// <summary>
        /// 条件判断
        /// </summary>
        public const string Condition = "condition";

        /// <summary>
        /// 循环控制
        /// </summary>
        public const string Loop = "loop";

        /// <summary>
        /// 分支执行
        /// </summary>
        public const string Branch = "branch";

        /// <summary>
        /// 跳转控制
        /// </summary>
        public const string Jump = "jump";

        /// <summary>
        /// 退出控制
        /// </summary>
        public const string Exit = "exit";

        /// <summary>
        /// 获取所有执行类型
        /// </summary>
        public static readonly string[] All = { Condition, Loop, Branch, Jump, Exit };

        /// <summary>
        /// 验证执行类型是否有效
        /// </summary>
        public static bool IsValid(string executionType)
        {
            return All.Contains(executionType);
        }
    }
}
