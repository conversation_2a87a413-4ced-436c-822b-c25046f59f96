using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 自定义模板分类管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CustomTemplateCategoryController : ControllerBase
    {
        private readonly ICustomTemplateCategoryRepository _categoryRepository;
        private readonly ILogger<CustomTemplateCategoryController> _logger;

        public CustomTemplateCategoryController(
            ICustomTemplateCategoryRepository categoryRepository,
            ILogger<CustomTemplateCategoryController> logger)
        {
            _categoryRepository = categoryRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<CustomTemplateCategoryDto>>> GetCategories()
        {
            try
            {
                var categories = await _categoryRepository.GetEnabledCategoriesAsync();
                var result = categories.Select(MapToDto).ToList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类列表失败");
                return StatusCode(500, "获取分类列表失败");
            }
        }

        /// <summary>
        /// 获取分类树结构
        /// </summary>
        [HttpGet("tree")]
        public async Task<ActionResult<List<CategoryTreeNodeDto>>> GetCategoryTree()
        {
            try
            {
                var categories = await _categoryRepository.GetCategoryTreeAsync();
                var result = categories.Select(MapToTreeNode).ToList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类树失败");
                return StatusCode(500, "获取分类树失败");
            }
        }

        /// <summary>
        /// 根据ID获取分类
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<CustomTemplateCategoryDto>> GetCategory(int id)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound("分类不存在");
                }

                return Ok(MapToDto(category));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类详情失败，ID: {Id}", id);
                return StatusCode(500, "获取分类详情失败");
            }
        }

        /// <summary>
        /// 创建分类
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<CustomTemplateCategoryDto>> CreateCategory([FromBody] CreateCustomTemplateCategoryDto dto)
        {
            try
            {
                // 检查名称是否已存在
                if (await _categoryRepository.ExistsNameAsync(dto.Name))
                {
                    return BadRequest("分类名称已存在");
                }

                // 检查父分类是否存在
                if (dto.ParentId.HasValue)
                {
                    var parentCategory = await _categoryRepository.GetByIdAsync(dto.ParentId.Value);
                    if (parentCategory == null)
                    {
                        return BadRequest("父分类不存在");
                    }
                }

                var category = new CustomTemplateCategory
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    ParentId = dto.ParentId,
                    Icon = dto.Icon,
                    Color = dto.Color,
                    SortOrder = dto.SortOrder,
                    IsEnabled = dto.IsEnabled,
                    CreatedBy = GetCurrentUserId(),
                    CreatedTime = DateTime.Now
                };

                var result = await _categoryRepository.AddAsync(category);
                return Ok(MapToDto(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建分类失败");
                return StatusCode(500, "创建分类失败");
            }
        }

        /// <summary>
        /// 更新分类
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<CustomTemplateCategoryDto>> UpdateCategory(int id, [FromBody] UpdateCustomTemplateCategoryDto dto)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound("分类不存在");
                }

                // 系统分类不允许修改名称
                if (category.IsSystem && category.Name != dto.Name)
                {
                    return BadRequest("系统分类不允许修改名称");
                }

                // 检查名称是否已存在
                if (await _categoryRepository.ExistsNameAsync(dto.Name, id))
                {
                    return BadRequest("分类名称已存在");
                }

                // 检查父分类是否存在
                if (dto.ParentId.HasValue)
                {
                    var parentCategory = await _categoryRepository.GetByIdAsync(dto.ParentId.Value);
                    if (parentCategory == null)
                    {
                        return BadRequest("父分类不存在");
                    }

                    // 检查是否会形成循环引用
                    if (dto.ParentId == id)
                    {
                        return BadRequest("不能将分类设置为自己的子分类");
                    }
                }

                category.Name = dto.Name;
                category.Description = dto.Description;
                category.ParentId = dto.ParentId;
                category.Icon = dto.Icon;
                category.Color = dto.Color;
                category.SortOrder = dto.SortOrder;
                category.IsEnabled = dto.IsEnabled;
                category.UpdatedBy = GetCurrentUserId();

                await _categoryRepository.UpdateAsync(category);
                return Ok(MapToDto(category));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新分类失败，ID: {Id}", id);
                return StatusCode(500, "更新分类失败");
            }
        }

        /// <summary>
        /// 删除分类
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteCategory(int id)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound("分类不存在");
                }

                // 系统分类不允许删除
                if (category.IsSystem)
                {
                    return BadRequest("系统分类不允许删除");
                }

                // 检查是否有子分类
                if (await _categoryRepository.HasChildrenAsync(id))
                {
                    return BadRequest("该分类下还有子分类，无法删除");
                }

                // 检查是否有模板
                if (await _categoryRepository.HasTemplatesAsync(id))
                {
                    return BadRequest("该分类下还有模板，无法删除");
                }

                await _categoryRepository.SoftDeleteAsync(id, GetCurrentUserId());
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除分类失败，ID: {Id}", id);
                return StatusCode(500, "删除分类失败");
            }
        }

        /// <summary>
        /// 获取分类统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<List<CategoryStatisticsDto>>> GetCategoryStatistics()
        {
            try
            {
                var statistics = await _categoryRepository.GetCategoryStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类统计信息失败");
                return StatusCode(500, "获取分类统计信息失败");
            }
        }

        /// <summary>
        /// 移动分类
        /// </summary>
        [HttpPost("{id}/move")]
        public async Task<ActionResult> MoveCategory(int id, [FromBody] int? newParentId)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound("分类不存在");
                }

                // 系统分类不允许移动
                if (category.IsSystem)
                {
                    return BadRequest("系统分类不允许移动");
                }

                var success = await _categoryRepository.MoveCategoryAsync(id, newParentId);
                if (!success)
                {
                    return BadRequest("移动失败，可能会形成循环引用");
                }

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动分类失败，ID: {Id}", id);
                return StatusCode(500, "移动分类失败");
            }
        }

        /// <summary>
        /// 启用/禁用分类
        /// </summary>
        [HttpPost("{id}/toggle-enabled")]
        public async Task<ActionResult> ToggleEnabled(int id, [FromBody] bool enabled)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound("分类不存在");
                }

                await _categoryRepository.SetEnabledAsync(id, enabled);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换分类状态失败，ID: {Id}", id);
                return StatusCode(500, "切换分类状态失败");
            }
        }

        /// <summary>
        /// 批量更新分类排序
        /// </summary>
        [HttpPost("batch-sort")]
        public async Task<ActionResult> BatchUpdateSortOrder([FromBody] List<(int CategoryId, int SortOrder)> sortOrders)
        {
            try
            {
                var success = await _categoryRepository.UpdateSortOrderAsync(sortOrders);
                if (!success)
                {
                    return BadRequest("批量更新排序失败");
                }

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新分类排序失败");
                return StatusCode(500, "批量更新分类排序失败");
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 1; // 默认为1
        }

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static CustomTemplateCategoryDto MapToDto(CustomTemplateCategory category)
        {
            return new CustomTemplateCategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                ParentId = category.ParentId,
                Icon = category.Icon,
                Color = category.Color,
                SortOrder = category.SortOrder,
                IsSystem = category.IsSystem,
                IsEnabled = category.IsEnabled,
                TemplateCount = category.TemplateCount,
                Children = category.Children?.Select(MapToDto).ToList() ?? new List<CustomTemplateCategoryDto>(),
                CreatedTime = category.CreatedTime,
                UpdatedTime = category.UpdatedTime,
                CreatedBy = category.CreatedBy
            };
        }

        /// <summary>
        /// 映射到树节点DTO
        /// </summary>
        private static CategoryTreeNodeDto MapToTreeNode(CustomTemplateCategory category)
        {
            return new CategoryTreeNodeDto
            {
                Id = category.Id,
                Name = category.Name,
                Icon = category.Icon,
                Color = category.Color,
                TemplateCount = category.TemplateCount,
                IsSystem = category.IsSystem,
                Children = category.Children?.Select(MapToTreeNode).ToList() ?? new List<CategoryTreeNodeDto>()
            };
        }

        #endregion
    }
}
