using SqlSugar;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 编码任务仓储实现
/// </summary>
public class CodingTaskRepository : BaseRepository<CodingTask>, ICodingTaskRepository
{
    public CodingTaskRepository(ISqlSugarClient db, ILogger<CodingTaskRepository> logger) : base(db, logger)
    {
    }

    /// <summary>
    /// 根据项目ID获取编码任务列表
    /// </summary>
    public async Task<(List<CodingTask> items, int totalCount)> GetByProjectIdAsync(
        int projectId,
        int pageIndex = 1,
        int pageSize = 20,
        string? status = null,
        string? priority = null,
        int? assignedTo = null,
        string? searchKeyword = null)
    {
        try
        {
            var query = _db.Queryable<CodingTask>()
                .Where(t => t.ProjectId == projectId && !t.IsDeleted);

            // 状态筛选
            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            // 优先级筛选
            if (!string.IsNullOrEmpty(priority))
            {
                query = query.Where(t => t.Priority == priority);
            }

            // 分配人筛选
            if (assignedTo.HasValue)
            {
                query = query.Where(t => t.AssignedTo == assignedTo.Value);
            }

            // 搜索关键词
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                query = query.Where(t => t.TaskName.Contains(searchKeyword) || 
                                        (t.Description != null && t.Description.Contains(searchKeyword)));
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var items = await query
                .OrderByDescending(t => t.CreatedTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 为每个任务获取关联的步骤数量
            foreach (var task in items)
            {
                task.StepCount = await GetRelatedStepsCountAsync(task.Id);
            }

            _logger.LogInformation("获取项目 {ProjectId} 的编码任务列表，共 {TotalCount} 个任务", projectId, totalCount);
            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目编码任务列表失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 根据项目ID获取所有编码任务（不分页）
    /// </summary>
    public async Task<List<CodingTask>> GetAllByProjectIdAsync(int projectId)
    {
        try
        {
            var tasks = await _db.Queryable<CodingTask>()
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .OrderByDescending(t => t.CreatedTime)
                .ToListAsync();

            _logger.LogInformation("获取项目 {ProjectId} 的所有编码任务，共 {Count} 个", projectId, tasks.Count);
            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目所有编码任务失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 获取编码任务统计信息
    /// </summary>
    public async Task<CodingTaskStatistics> GetStatisticsAsync(int projectId)
    {
        try
        {
            var tasks = await _db.Queryable<CodingTask>()
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .ToListAsync();

            var statistics = new CodingTaskStatistics
            {
                TotalTasks = tasks.Count,
                InProgressTasks = tasks.Count(t => t.Status == "InProgress"),
                CompletedTasks = tasks.Count(t => t.Status == "Completed"),
                BlockedTasks = tasks.Count(t => t.Status == "Blocked"),
                NotStartedTasks = tasks.Count(t => t.Status == "NotStarted"),
                HighPriorityTasks = tasks.Count(t => t.Priority == "High")
            };

            // 计算即将到期的任务数
            var upcomingDueTasks = await GetUpcomingDueTasksAsync(projectId, 7);
            statistics.UpcomingDueTasks = upcomingDueTasks.Count;

            // 计算平均完成时间
            var completedTasks = tasks.Where(t => t.Status == "Completed" && 
                                                 t.ActualStartTime.HasValue && 
                                                 t.ActualEndTime.HasValue).ToList();
            if (completedTasks.Any())
            {
                var totalHours = completedTasks.Sum(t => 
                    (t.ActualEndTime!.Value - t.ActualStartTime!.Value).TotalHours);
                statistics.AverageCompletionHours = totalHours / completedTasks.Count;
            }

            _logger.LogInformation("获取项目 {ProjectId} 编码任务统计信息成功", projectId);
            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编码任务统计信息失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 根据分配人获取编码任务
    /// </summary>
    public async Task<List<CodingTask>> GetByAssignedUserAsync(int assignedTo, string? status = null)
    {
        try
        {
            var query = _db.Queryable<CodingTask>()
                .Where(t => t.AssignedTo == assignedTo && !t.IsDeleted);

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            var tasks = await query
                .OrderByDescending(t => t.CreatedTime)
                .ToListAsync();

            _logger.LogInformation("获取用户 {UserId} 的编码任务，共 {Count} 个", assignedTo, tasks.Count);
            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户编码任务失败，用户ID: {UserId}", assignedTo);
            throw;
        }
    }

    /// <summary>
    /// 获取即将到期的编码任务
    /// </summary>
    public async Task<List<CodingTask>> GetUpcomingDueTasksAsync(int projectId, int days = 7)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(days);
            
            var tasks = await _db.Queryable<CodingTask>()
                .Where(t => t.ProjectId == projectId && 
                           !t.IsDeleted && 
                           t.Status != "Completed" && 
                           t.Status != "Cancelled" &&
                           t.DueDate.HasValue && 
                           t.DueDate.Value <= cutoffDate)
                .OrderBy(t => t.DueDate)
                .ToListAsync();

            _logger.LogInformation("获取项目 {ProjectId} 即将到期的任务，共 {Count} 个", projectId, tasks.Count);
            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取即将到期任务失败，项目ID: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 批量更新任务状态
    /// </summary>
    public async Task<int> BatchUpdateStatusAsync(List<int> taskIds, string status, int updatedBy)
    {
        try
        {
            var result = await _db.Updateable<CodingTask>()
                .SetColumns(t => new CodingTask
                {
                    Status = status,
                    UpdatedTime = DateTime.Now,
                    UpdatedBy = updatedBy
                })
                .Where(t => taskIds.Contains(t.Id) && !t.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("批量更新 {Count} 个任务状态为 {Status}", result, status);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新任务状态失败");
            throw;
        }
    }

    /// <summary>
    /// 获取任务关联的开发步骤数量
    /// </summary>
    public async Task<int> GetRelatedStepsCountAsync(int taskId)
    {
        try
        {
            // 这里需要根据实际的关联表结构来实现
            // 假设有一个CodingTaskStep关联表
            var count = await _db.Queryable<CodingTaskStep>()
                .Where(cts => cts.CodingTaskId == taskId && !cts.IsDeleted)
                .CountAsync();

            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务关联步骤数量失败，任务ID: {TaskId}", taskId);
            return 0;
        }
    }

    /// <summary>
    /// 添加开发步骤到编码任务
    /// </summary>
    public async Task<int> AddStepsToTaskAsync(int taskId, List<int> stepIds)
    {
        try
        {
            var existingSteps = await _db.Queryable<CodingTaskStep>()
                .Where(cts => cts.CodingTaskId == taskId && stepIds.Contains(cts.DevelopmentStepId))
                .Select(cts => cts.DevelopmentStepId)
                .ToListAsync();

            var newStepIds = stepIds.Except(existingSteps).ToList();
            
            if (!newStepIds.Any())
            {
                return 0;
            }

            var taskSteps = newStepIds.Select(stepId => new CodingTaskStep
            {
                CodingTaskId = taskId,
                DevelopmentStepId = stepId,
                CreatedTime = DateTime.Now
            }).ToList();

            await _db.Insertable(taskSteps).ExecuteCommandAsync();

            _logger.LogInformation("为任务 {TaskId} 添加了 {Count} 个开发步骤", taskId, newStepIds.Count);
            return newStepIds.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加步骤到任务失败，任务ID: {TaskId}", taskId);
            throw;
        }
    }

    /// <summary>
    /// 从编码任务中移除开发步骤
    /// </summary>
    public async Task<int> RemoveStepsFromTaskAsync(int taskId, List<int> stepIds)
    {
        try
        {
            var result = await _db.Updateable<CodingTaskStep>()
                .SetColumns(cts => new CodingTaskStep { IsDeleted = true })
                .Where(cts => cts.CodingTaskId == taskId && stepIds.Contains(cts.DevelopmentStepId))
                .ExecuteCommandAsync();

            _logger.LogInformation("从任务 {TaskId} 移除了 {Count} 个开发步骤", taskId, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从任务移除步骤失败，任务ID: {TaskId}", taskId);
            throw;
        }
    }

    /// <summary>
    /// 获取任务关联的开发步骤（包含CodingTaskSteps执行状态信息）
    /// </summary>
    public async Task<List<object>> GetTaskStepsAsync(int taskId)
    {
        try
        {
            // 联合查询获取CodingTaskSteps和DevelopmentSteps的完整信息
            var taskStepsWithDetails = await _db.Queryable<CodingTaskStep>()
                .LeftJoin<DevelopmentStep>((cts, ds) => cts.DevelopmentStepId == ds.Id)
                .Where((cts, ds) => cts.CodingTaskId == taskId && !cts.IsDeleted && !ds.IsDeleted)
                .Select((cts, ds) => new
                {
                    // CodingTaskSteps 的执行状态信息
                    TaskStepId = cts.Id,
                    CodingTaskId = cts.CodingTaskId,
                    DevelopmentStepId = cts.DevelopmentStepId,
                    OrderIndex = cts.OrderIndex,
                    Status = cts.Status,
                    StartTime = cts.StartTime,
                    CompletedTime = cts.CompletedTime,
                    ActualHours = cts.ActualHours,
                    Notes = cts.Notes,
                    ExecutionResult = cts.ExecutionResult,
                    IsFinishCoding = cts.IsFinishCoding,
                    IsFixError = cts.IsFixError,
                    TaskStepCreatedTime = cts.CreatedTime,
                    TaskStepUpdatedTime = cts.UpdatedTime,
                    TaskStepCreatedBy = cts.CreatedBy,
                    TaskStepUpdatedBy = cts.UpdatedBy,

                    // DevelopmentSteps 的基本信息
                    Id = ds.Id,
                    StepName = ds.StepName,
                    StepDescription = ds.StepDescription,
                    StepType = ds.StepType,
                    Priority = ds.Priority,
                    StepStatus = ds.Status, // 区分于CodingTaskStep的Status
                    Progress = ds.Progress,
                    EstimatedHours = ds.EstimatedHours,
                    TechnologyStack = ds.TechnologyStack,
                    ComponentType = ds.ComponentType,
                    FilePath = ds.FilePath,
                    AIPrompt = ds.AIPrompt,
                    StepOrder = ds.StepOrder,
                    StepLevel = ds.StepLevel,
                    ParentStepId = ds.ParentStepId,
                    ReferenceImages = ds.ReferenceImages,
                    ProjectId = ds.ProjectId,
                    RequirementDocumentId = ds.RequirementDocumentId,
                    StepCreatedTime = ds.CreatedTime,
                    StepUpdatedTime = ds.UpdatedTime
                })
                .OrderBy("cts.OrderIndex, ds.StepOrder, cts.CreatedTime")
                .ToListAsync();

            if (!taskStepsWithDetails.Any())
            {
                _logger.LogInformation("任务 {TaskId} 没有关联的开发步骤", taskId);
                return new List<object>();
            }

            _logger.LogInformation("获取任务 {TaskId} 关联的开发步骤，共 {Count} 个（包含执行状态信息）",
                taskId, taskStepsWithDetails.Count);

            // 转换为匿名对象列表返回
            var result = taskStepsWithDetails.Cast<object>().ToList();
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务关联步骤失败，任务ID: {TaskId}", taskId);
            throw;
        }
    }

    /// <summary>
    /// 递归获取指定步骤的所有子步骤
    /// </summary>
    private async Task<List<DevelopmentStep>> GetAllChildStepsRecursiveAsync(int parentStepId)
    {
        var result = new List<DevelopmentStep>();

        // 获取直接子步骤
        var directChildren = await _db.Queryable<DevelopmentStep>()
            .Where(ds => ds.ParentStepId == parentStepId && !ds.IsDeleted)
            .ToListAsync();

        foreach (var child in directChildren)
        {
            result.Add(child);

            // 递归获取子步骤的子步骤
            var grandChildren = await GetAllChildStepsRecursiveAsync(child.Id);
            result.AddRange(grandChildren);
        }

        return result;
    }

    /// <summary>
    /// 获取特定的编码任务步骤
    /// </summary>
    public async Task<CodingTaskStep?> GetTaskStepAsync(int taskId, int stepId)
    {
        try
        {
            var taskStep = await _db.Queryable<CodingTaskStep>()
                .Where(cts => cts.CodingTaskId == taskId && cts.DevelopmentStepId == stepId && !cts.IsDeleted)
                .FirstAsync();

            return taskStep;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编码任务步骤失败，任务ID: {TaskId}, 步骤ID: {StepId}", taskId, stepId);
            return null;
        }
    }

    /// <summary>
    /// 更新编码任务步骤
    /// </summary>
    public async Task<bool> UpdateTaskStepAsync(CodingTaskStep taskStep)
    {
        try
        {
            var result = await _db.Updateable(taskStep).ExecuteCommandAsync();
            _logger.LogInformation("更新编码任务步骤成功，任务步骤ID: {TaskStepId}", taskStep.Id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新编码任务步骤失败，任务步骤ID: {TaskStepId}", taskStep.Id);
            throw;
        }
    }

    /// <summary>
    /// 获取任务的第一个步骤
    /// </summary>
    public async Task<CodingTaskStep?> GetFirstTaskStepAsync(int taskId)
    {
        try
        {
            var firstStep = await _db.Queryable<CodingTaskStep>()
                .Where(cts => cts.CodingTaskId == taskId && !cts.IsDeleted)
                .OrderBy(cts => cts.OrderIndex)
                .FirstAsync();

            return firstStep;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务第一个步骤失败，任务ID: {TaskId}", taskId);
            return null;
        }
    }

    /// <summary>
    /// 开始任务的第一个步骤
    /// </summary>
    public async Task<bool> StartFirstTaskStepAsync(int taskId, int userId)
    {
        try
        {
            var firstStep = await GetFirstTaskStepAsync(taskId);
            if (firstStep == null)
            {
                _logger.LogWarning("任务 {TaskId} 没有找到第一个步骤", taskId);
                return false;
            }

            // 只有当步骤状态为NotStarted时才更新
            if (firstStep.Status == "NotStarted")
            {
                firstStep.Status = "InProgress";
                firstStep.StartTime = DateTime.Now;
                firstStep.UpdatedTime = DateTime.Now;
                firstStep.UpdatedBy = userId;

                var result = await UpdateTaskStepAsync(firstStep);
                if (result)
                {
                    _logger.LogInformation("任务 {TaskId} 的第一个步骤 {StepId} 已开始", taskId, firstStep.DevelopmentStepId);
                }
                return result;
            }

            _logger.LogInformation("任务 {TaskId} 的第一个步骤 {StepId} 状态为 {Status}，无需更新",
                taskId, firstStep.DevelopmentStepId, firstStep.Status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始任务第一个步骤失败，任务ID: {TaskId}", taskId);
            throw;
        }
    }
}
