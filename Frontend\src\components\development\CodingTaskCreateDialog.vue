<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑编码任务' : '创建编码任务'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="form.taskName"
              placeholder="请输入任务名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="高" value="High" />
              <el-option label="中" value="Medium" />
              <el-option label="低" value="Low" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分配给">
            <el-select
              v-model="form.assignedTo"
              placeholder="选择负责人"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.realName || user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预估工时">
            <el-input-number
              v-model="form.estimatedHours"
              :min="0"
              :max="1000"
              placeholder="小时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="选择开始日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期">
            <el-date-picker
              v-model="form.dueDate"
              type="date"
              placeholder="选择截止日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="技术栈">
        <el-input
          v-model="form.technologyStack"
          placeholder="如：Vue3, TypeScript, Element Plus"
          maxlength="200"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="添加标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="任务备注信息"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { userService } from '@/services/user'
import { codingTaskService } from '@/services/codingTask'
import type { User } from '@/types/user'

// Props
interface Props {
  modelValue: boolean
  projectId?: number
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const users = ref<User[]>([])
const availableTags = ref<string[]>([
  '前端开发', '后端开发', '数据库', 'API开发', 'UI组件',
  '业务逻辑', '数据处理', '权限管理', '性能优化', '测试'
])

// 表单数据
const form = reactive({
  taskName: '',
  description: '',
  priority: 'Medium',
  assignedTo: undefined as number | undefined,
  estimatedHours: undefined as number | undefined,
  startDate: undefined as Date | undefined,
  dueDate: undefined as Date | undefined,
  technologyStack: '',
  tags: [] as string[],
  notes: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.task)

// 表单验证规则
const rules: FormRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 100, message: '任务名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入任务描述', trigger: 'blur' },
    { min: 5, max: 500, message: '描述长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 方法定义
const resetForm = () => {
  Object.assign(form, {
    taskName: '',
    description: '',
    priority: 'Medium',
    assignedTo: undefined,
    estimatedHours: undefined,
    startDate: undefined,
    dueDate: undefined,
    technologyStack: '',
    tags: [],
    notes: ''
  })
}

// 监听器
watch(() => props.task, (newTask) => {
  if (newTask) {
    Object.assign(form, {
      taskName: newTask.taskName || '',
      description: newTask.description || '',
      priority: newTask.priority || 'Medium',
      assignedTo: newTask.assignedTo,
      estimatedHours: newTask.estimatedHours,
      startDate: newTask.startDate ? new Date(newTask.startDate) : undefined,
      dueDate: newTask.dueDate ? new Date(newTask.dueDate) : undefined,
      technologyStack: newTask.technologyStack || '',
      tags: newTask.tags ? (typeof newTask.tags === 'string' ? newTask.tags.split(',').filter(Boolean) : newTask.tags) : [],
      notes: newTask.notes || ''
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadUsers()
})

// 方法
const loadUsers = async () => {
  try {
    const result = await userService.getUsers({ pageNumber: 1, pageSize: 100 })
    users.value = result.items as User[]
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 如果加载失败，使用空数组
    users.value = []
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (!props.projectId) {
      ElMessage.error('项目信息不完整')
      return
    }

    submitting.value = true

    // 准备任务数据
    const taskData = {
      projectId: props.projectId,
      taskName: form.taskName,
      description: form.description,
      priority: form.priority,
      assignedTo: form.assignedTo,
      estimatedHours: form.estimatedHours,
      startDate: form.startDate?.toISOString(),
      dueDate: form.dueDate?.toISOString(),
      technologyStack: form.technologyStack,
      tags: form.tags.length > 0 ? form.tags.join(',') : undefined,
      notes: form.notes,
      status: 'NotStarted' // 新创建的任务默认状态为未开始
    }

    if (isEdit.value && props.task?.id) {
      // 更新任务
      await codingTaskService.updateTask(props.task.id, taskData)
      ElMessage.success('编码任务更新成功')
    } else {
      // 创建任务
      await codingTaskService.createTask(taskData)
      ElMessage.success('编码任务创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存编码任务失败:', error)
    ElMessage.error('保存编码任务失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
