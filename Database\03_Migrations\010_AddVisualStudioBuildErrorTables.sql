-- =============================================
-- Visual Studio 编译错误管理表
-- 创建时间: 2024-12-26
-- 功能: 记录VS编译错误、AI修复建议和修复结果
-- =============================================

-- 1. Visual Studio 编译错误记录表
CREATE TABLE VSBuildErrors (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NULL,
    BuildSessionId nvarchar(50) NOT NULL,
    ErrorCode nvarchar(20) NULL,
    ErrorMessage nvarchar(max) NOT NULL,
    FilePath nvarchar(500) NULL,
    LineNumber int NULL,
    ColumnNumber int NULL,
    Severity nvarchar(20) NOT NULL DEFAULT 'Error',
    BuildConfiguration nvarchar(50) NULL,
    BuildPlatform nvarchar(50) NULL,
    BuildTime datetime2 NOT NULL DEFAULT GETDATE(),
    IsResolved bit NOT NULL DEFAULT 0,
    ResolvedAt datetime2 NULL,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_VSBuildErrors PRIMARY KEY (Id),
    CONSTRAINT FK_VSBuildErrors_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_VSBuildErrors_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_VSBuildErrors_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_VSBuildErrors_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_VSBuildErrors_Severity CHECK (Severity IN ('Error', 'Warning', 'Info'))
);
GO

-- 2. 编译会话统计表
CREATE TABLE VSBuildSessions (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NULL,
    SessionId nvarchar(50) NOT NULL,
    BuildConfiguration nvarchar(50) NULL,
    BuildPlatform nvarchar(50) NULL,
    StartTime datetime2 NOT NULL,
    EndTime datetime2 NULL,
    BuildResult nvarchar(20) NOT NULL,
    TotalErrors int NOT NULL DEFAULT 0,
    TotalWarnings int NOT NULL DEFAULT 0,
    ResolvedErrors int NOT NULL DEFAULT 0,
    AIFixRequested bit NOT NULL DEFAULT 0,
    AIFixResponse nvarchar(max) NULL,
    AIFixApplied bit NOT NULL DEFAULT 0,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_VSBuildSessions PRIMARY KEY (Id),
    CONSTRAINT FK_VSBuildSessions_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_VSBuildSessions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_VSBuildSessions_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_VSBuildSessions_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_VSBuildSessions_BuildResult CHECK (BuildResult IN ('Success', 'Failed', 'Cancelled')),
    CONSTRAINT UQ_VSBuildSessions_SessionId UNIQUE (SessionId)
);
GO

-- 创建索引
CREATE INDEX IX_VSBuildErrors_ProjectId ON VSBuildErrors(ProjectId);
CREATE INDEX IX_VSBuildErrors_BuildSessionId ON VSBuildErrors(BuildSessionId);
CREATE INDEX IX_VSBuildErrors_ErrorCode ON VSBuildErrors(ErrorCode);
CREATE INDEX IX_VSBuildErrors_FilePath ON VSBuildErrors(FilePath);
CREATE INDEX IX_VSBuildErrors_BuildTime ON VSBuildErrors(BuildTime);
CREATE INDEX IX_VSBuildErrors_IsResolved ON VSBuildErrors(IsResolved);



CREATE INDEX IX_VSBuildSessions_ProjectId ON VSBuildSessions(ProjectId);
CREATE INDEX IX_VSBuildSessions_SessionId ON VSBuildSessions(SessionId);
CREATE INDEX IX_VSBuildSessions_StartTime ON VSBuildSessions(StartTime);
CREATE INDEX IX_VSBuildSessions_BuildResult ON VSBuildSessions(BuildResult);

-- 添加表和列注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Visual Studio编译错误记录表', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'关联的项目ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'ProjectId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译会话ID，用于关联同一次编译的多个错误', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'BuildSessionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误代码，如CS0103', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'ErrorCode';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误消息内容', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'ErrorMessage';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'出错文件路径', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'FilePath';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误行号', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'LineNumber';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误列号', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'ColumnNumber';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'严重程度：Error, Warning, Info', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'Severity';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译配置：Debug, Release等', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'BuildConfiguration';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译平台：x86, x64, AnyCPU等', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'BuildPlatform';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'BuildTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'是否已解决', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'IsResolved';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'解决时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'ResolvedAt';

-- VSBuildErrors BaseEntity字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'CreatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'UpdatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'CreatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'UpdatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'软删除标记', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'IsDeleted';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'DeletedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'DeletedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'版本号', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'Version';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildErrors', @level2type = N'Column', @level2name = N'Remarks';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译会话统计表', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'关联的项目ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'ProjectId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译会话ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'SessionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译配置', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'BuildConfiguration';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译平台', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'BuildPlatform';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译开始时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'StartTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译结束时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'EndTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'编译结果：Success, Failed, Cancelled', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'BuildResult';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误总数', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'TotalErrors';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'警告总数', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'TotalWarnings';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'已解决错误数', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'ResolvedErrors';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'是否已请求AI修复', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'AIFixRequested';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'AI修复响应内容', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'AIFixResponse';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'是否已应用AI修复', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'AIFixApplied';

-- VSBuildSessions BaseEntity字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'CreatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'UpdatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'CreatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'UpdatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'软删除标记', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'IsDeleted';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除时间', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'DeletedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除人ID', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'DeletedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'版本号', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'Version';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'Schema', @level0name = N'dbo', @level1type = N'Table', @level1name = N'VSBuildSessions', @level2type = N'Column', @level2name = N'Remarks';

PRINT 'Visual Studio 编译错误管理表创建完成！';
PRINT '包含以下表：';
PRINT '1. VSBuildErrors - VS编译错误记录表';
PRINT '2. VSBuildSessions - 编译会话统计表';
