using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using ProjectManagementAI.WPF.Services;

namespace ProjectManagementAI.WPF.ViewModels
{
    /// <summary>
    /// 向量化操作视图模型
    /// </summary>
    public partial class VectorOperationViewModel : ObservableObject
    {
        private readonly ILogger<VectorOperationViewModel> _logger;
        private readonly IVectorOperationService _vectorOperationService;
        private readonly IProjectService _projectService;
        private readonly IAIService _aiService;
        private readonly ILocalProjectService _localProjectService;
        private readonly IVectorDatabaseService _vectorDatabaseService;

        [ObservableProperty]
        private string _statusMessage = "就绪";

        [ObservableProperty]
        private bool _isLoading;

        [ObservableProperty]
        private bool _showDetailedResults;

        [ObservableProperty]
        private string _detailedResults = string.Empty;

        [ObservableProperty]
        private ObservableCollection<OperationResult> _operationResults = new();

        [ObservableProperty]
        private string _projectPath = string.Empty;

        [ObservableProperty]
        private ObservableCollection<LocalProject> _localProjects = new();

        [ObservableProperty]
        private bool _hasLocalProjects;

        [ObservableProperty]
        private string _localProjectsStatus = string.Empty;

        [ObservableProperty]
        private string _vectorDatabaseStatus = "向量数据库未初始化";

        [ObservableProperty]
        private bool _isVectorDatabaseReady;

        public VectorOperationViewModel(
            ILogger<VectorOperationViewModel> logger,
            IVectorOperationService vectorOperationService,
            IProjectService projectService,
            IAIService aiService,
            ILocalProjectService localProjectService,
            IVectorDatabaseService vectorDatabaseService)
        {
            _logger = logger;
            _vectorOperationService = vectorOperationService;
            _projectService = projectService;
            _aiService = aiService;
            _localProjectService = localProjectService;
            _vectorDatabaseService = vectorDatabaseService;

            InitializeCommands();
            _ = InitializeVectorDatabaseAsync();
        }

        private void InitializeCommands()
        {
            BatchCreateProjectsCommand = new AsyncRelayCommand(BatchCreateProjectsAsync);
            BatchUpdateProjectsCommand = new AsyncRelayCommand(BatchUpdateProjectsAsync);
            BatchGenerateStepsCommand = new AsyncRelayCommand(BatchGenerateStepsAsync);
            VectorAnalyzeCommand = new AsyncRelayCommand(VectorAnalyzeAsync);
            FindSimilarProjectsCommand = new AsyncRelayCommand(FindSimilarProjectsAsync);
            PredictRisksCommand = new AsyncRelayCommand(PredictRisksAsync);
            OptimizeProjectsCommand = new AsyncRelayCommand(OptimizeProjectsAsync);
            OptimizeResourcesCommand = new AsyncRelayCommand(OptimizeResourcesAsync);
            BatchExecuteAutomationCommand = new AsyncRelayCommand(BatchExecuteAutomationAsync);

            // 本地项目相关命令
            BrowseProjectPathCommand = new RelayCommand(BrowseProjectPath);
            ScanLocalProjectsCommand = new AsyncRelayCommand(ScanLocalProjectsAsync);
            UseLocalProjectsCommand = new AsyncRelayCommand(UseLocalProjectsAsync);

            // 向量数据库相关命令
            InitializeVectorDatabaseCommand = new AsyncRelayCommand(InitializeVectorDatabaseAsync);
            BuildVectorDatabaseCommand = new AsyncRelayCommand(BuildVectorDatabaseAsync);
            SearchVectorDatabaseCommand = new AsyncRelayCommand(SearchVectorDatabaseAsync);
            ClusterAnalysisCommand = new AsyncRelayCommand(PerformClusterAnalysisAsync);
        }

        #region Commands

        public IAsyncRelayCommand? BatchCreateProjectsCommand { get; private set; }
        public IAsyncRelayCommand? BatchUpdateProjectsCommand { get; private set; }
        public IAsyncRelayCommand? BatchGenerateStepsCommand { get; private set; }
        public IAsyncRelayCommand? VectorAnalyzeCommand { get; private set; }
        public IAsyncRelayCommand? FindSimilarProjectsCommand { get; private set; }
        public IAsyncRelayCommand? PredictRisksCommand { get; private set; }
        public IAsyncRelayCommand? OptimizeProjectsCommand { get; private set; }
        public IAsyncRelayCommand? OptimizeResourcesCommand { get; private set; }
        public IAsyncRelayCommand? BatchExecuteAutomationCommand { get; private set; }

        // 本地项目相关命令
        public IRelayCommand? BrowseProjectPathCommand { get; private set; }
        public IAsyncRelayCommand? ScanLocalProjectsCommand { get; private set; }
        public IAsyncRelayCommand? UseLocalProjectsCommand { get; private set; }

        // 向量数据库相关命令
        public IAsyncRelayCommand? InitializeVectorDatabaseCommand { get; private set; }
        public IAsyncRelayCommand? BuildVectorDatabaseCommand { get; private set; }
        public IAsyncRelayCommand? SearchVectorDatabaseCommand { get; private set; }
        public IAsyncRelayCommand? ClusterAnalysisCommand { get; private set; }

        #endregion

        #region Command Implementations

        /// <summary>
        /// 批量创建项目
        /// </summary>
        private async Task BatchCreateProjectsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在批量创建项目...";

                // 示例项目模板
                var templates = new List<ProjectTemplate>
                {
                    new() { Name = "AI聊天机器人", ProjectType = "AI_APPLICATION", TechnologyStack = "Python, FastAPI, OpenAI" },
                    new() { Name = "数据分析平台", ProjectType = "DATA_PLATFORM", TechnologyStack = "Python, Pandas, Streamlit" },
                    new() { Name = "移动端应用", ProjectType = "MOBILE_APP", TechnologyStack = "React Native, TypeScript" }
                };

                var result = await _vectorOperationService.BatchCreateProjectsAsync(templates);
                
                AddOperationResult("批量创建项目", 
                    $"成功创建 {result.SuccessCount} 个项目，失败 {result.FailureCount} 个，耗时 {result.Duration.TotalSeconds:F2} 秒");

                DetailedResults = $"详细结果:\n成功: {result.SuccessCount}\n失败: {result.FailureCount}\n" +
                                 $"错误信息: {string.Join("\n", result.Errors)}";
                ShowDetailedResults = true;

                StatusMessage = "批量创建项目完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量创建项目失败");
                StatusMessage = "批量创建项目失败";
                AddOperationResult("批量创建项目", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 批量更新项目
        /// </summary>
        private async Task BatchUpdateProjectsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在批量更新项目...";

                // 获取所有项目进行更新
                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var updates = projectList.Select(p => new ProjectUpdate
                {
                    ProjectId = GetProjectId(p),
                    Updates = new Dictionary<string, object>
                    {
                        { "LastAnalyzed", DateTime.Now },
                        { "Status", "Analyzed" }
                    }
                }).ToList();

                var result = await _vectorOperationService.BatchUpdateProjectsAsync(updates);
                
                AddOperationResult("批量更新项目", 
                    $"成功更新 {result.SuccessCount} 个项目，失败 {result.FailureCount} 个");

                StatusMessage = "批量更新项目完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新项目失败");
                StatusMessage = "批量更新项目失败";
                AddOperationResult("批量更新项目", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 批量生成开发步骤
        /// </summary>
        private async Task BatchGenerateStepsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在批量生成开发步骤...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var projectIds = projectList.Select(p => GetProjectId(p)).ToList();

                var result = await _vectorOperationService.BatchGenerateStepsAsync(projectIds);
                
                AddOperationResult("批量生成步骤", 
                    $"为 {result.SuccessCount} 个项目生成了开发步骤");

                StatusMessage = "批量生成开发步骤完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量生成开发步骤失败");
                StatusMessage = "批量生成开发步骤失败";
                AddOperationResult("批量生成步骤", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 向量化分析
        /// </summary>
        private async Task VectorAnalyzeAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行向量化分析...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var projectIds = projectList.Select(p => GetProjectId(p)).ToList();

                var result = await _vectorOperationService.VectorAnalyzeAsync(projectIds);
                
                var avgComplexity = result.GlobalMetrics.GetValueOrDefault("AverageComplexity", 0);
                var avgRisk = result.GlobalMetrics.GetValueOrDefault("AverageRisk", 0);
                
                AddOperationResult("向量化分析", 
                    $"分析了 {result.ProjectAnalyses.Count} 个项目，平均复杂度: {avgComplexity:F2}，平均风险: {avgRisk:F2}");

                DetailedResults = $"分析结果:\n" +
                                 $"项目数量: {result.ProjectAnalyses.Count}\n" +
                                 $"平均复杂度: {avgComplexity:F2}\n" +
                                 $"平均风险: {avgRisk:F2}\n" +
                                 $"洞察: {string.Join("\n", result.Insights)}";
                ShowDetailedResults = true;

                StatusMessage = "向量化分析完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向量化分析失败");
                StatusMessage = "向量化分析失败";
                AddOperationResult("向量化分析", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 相似性分析
        /// </summary>
        private async Task FindSimilarProjectsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行相似性分析...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                
                if (projectList.Any())
                {
                    var referenceProjectId = GetProjectId(projectList.First());
                    var similarProjects = await _vectorOperationService.FindSimilarProjectsAsync(referenceProjectId, 0.6);
                    var similarProjectsList = similarProjects.ToList();

                    AddOperationResult("相似性分析",
                        $"找到 {similarProjectsList.Count} 个相似项目");

                    DetailedResults = $"相似项目:\n" +
                                     string.Join("\n", similarProjectsList.Select(s =>
                                         $"- {s.ProjectName} (相似度: {s.SimilarityScore:P0})"));
                    ShowDetailedResults = true;
                }

                StatusMessage = "相似性分析完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "相似性分析失败");
                StatusMessage = "相似性分析失败";
                AddOperationResult("相似性分析", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 风险预测
        /// </summary>
        private async Task PredictRisksAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行风险预测...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var projectIds = projectList.Select(p => GetProjectId(p)).ToList();

                var riskPredictions = await _vectorOperationService.PredictProjectRisksAsync(projectIds);
                var riskPredictionsList = riskPredictions.ToList();
                var highRiskProjects = riskPredictionsList.Where(r => r.RiskScore > 0.7).ToList();

                AddOperationResult("风险预测",
                    $"分析了 {riskPredictionsList.Count} 个项目，发现 {highRiskProjects.Count} 个高风险项目");

                DetailedResults = $"高风险项目:\n" +
                                 string.Join("\n", highRiskProjects.Select(r => 
                                     $"- {r.ProjectName} (风险评分: {r.RiskScore:P0})"));
                ShowDetailedResults = true;

                StatusMessage = "风险预测完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "风险预测失败");
                StatusMessage = "风险预测失败";
                AddOperationResult("风险预测", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 项目优化
        /// </summary>
        private async Task OptimizeProjectsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行项目优化...";

                var result = await _vectorOperationService.OptimizeProjectsAsync();
                
                AddOperationResult("项目优化", 
                    $"生成了 {result.Suggestions.Count} 条优化建议，总体评分: {result.OverallScore:P0}");

                DetailedResults = $"优化建议:\n" +
                                 string.Join("\n", result.Suggestions.Take(5).Select(s => 
                                     $"- {s.Title}: {s.Description} (影响度: {s.Impact:P0})"));
                ShowDetailedResults = true;

                StatusMessage = "项目优化完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "项目优化失败");
                StatusMessage = "项目优化失败";
                AddOperationResult("项目优化", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 资源优化
        /// </summary>
        private async Task OptimizeResourcesAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行资源优化...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var projectIds = projectList.Select(p => GetProjectId(p)).ToList();

                var result = await _vectorOperationService.OptimizeResourceAllocationAsync(projectIds);
                
                AddOperationResult("资源优化", 
                    $"为 {result.Allocations.Count} 个项目优化了资源分配，效率评分: {result.EfficiencyScore:P0}");

                DetailedResults = $"资源分配建议:\n" +
                                 string.Join("\n", result.Recommendations.Take(5));
                ShowDetailedResults = true;

                StatusMessage = "资源优化完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "资源优化失败");
                StatusMessage = "资源优化失败";
                AddOperationResult("资源优化", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 批量自动化执行
        /// </summary>
        private async Task BatchExecuteAutomationAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在执行批量自动化任务...";

                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();
                var automationTasks = projectList.Select(p => new AutomationTask
                {
                    TaskType = "CodeGeneration",
                    ProjectId = GetProjectId(p),
                    Parameters = new Dictionary<string, object>
                    {
                        { "Language", "C#" },
                        { "Framework", "ASP.NET Core" }
                    },
                    Priority = 1
                }).ToList();

                var result = await _vectorOperationService.BatchExecuteAutomationAsync(automationTasks);
                
                AddOperationResult("批量自动化", 
                    $"执行了 {result.SuccessCount} 个自动化任务，失败 {result.FailureCount} 个");

                StatusMessage = "批量自动化执行完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量自动化执行失败");
                StatusMessage = "批量自动化执行失败";
                AddOperationResult("批量自动化", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Helper Methods

        private void AddOperationResult(string title, string description)
        {
            OperationResults.Insert(0, new OperationResult
            {
                Title = title,
                Description = description,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            // 保持最多20条记录
            while (OperationResults.Count > 20)
            {
                OperationResults.RemoveAt(OperationResults.Count - 1);
            }
        }

        private int GetProjectId(object project)
        {
            return ((dynamic)project).Id;
        }

        #endregion

        #region Local Project Methods

        /// <summary>
        /// 浏览项目路径
        /// </summary>
        private void BrowseProjectPath()
        {
            try
            {
                // 使用OpenFileDialog作为文件夹选择的替代方案
                var dialog = new OpenFileDialog
                {
                    Title = "选择项目根目录中的任意文件",
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "选择此文件夹",
                    Filter = "所有文件|*.*"
                };

                if (!string.IsNullOrEmpty(ProjectPath))
                {
                    dialog.InitialDirectory = ProjectPath;
                }

                if (dialog.ShowDialog() == true)
                {
                    var selectedPath = System.IO.Path.GetDirectoryName(dialog.FileName);
                    if (!string.IsNullOrEmpty(selectedPath))
                    {
                        ProjectPath = selectedPath;
                        StatusMessage = $"已选择路径: {ProjectPath}";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "浏览项目路径失败");
                StatusMessage = "浏览路径失败，请手动输入路径";
            }
        }

        /// <summary>
        /// 扫描本地项目
        /// </summary>
        private async Task ScanLocalProjectsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(ProjectPath))
                {
                    StatusMessage = "请先选择项目路径";
                    return;
                }

                IsLoading = true;
                StatusMessage = "正在扫描本地项目...";
                LocalProjects.Clear();

                var projects = await _localProjectService.ScanProjectsAsync(ProjectPath);
                var projectList = projects.ToList();

                foreach (var project in projectList)
                {
                    LocalProjects.Add(project);
                }

                HasLocalProjects = LocalProjects.Any();
                LocalProjectsStatus = $"发现 {LocalProjects.Count} 个项目";

                if (HasLocalProjects)
                {
                    var totalLines = LocalProjects.Sum(p => p.Statistics.CodeLines);
                    var languages = LocalProjects.Select(p => p.Language).Distinct().Count();
                    LocalProjectsStatus += $"，共 {totalLines:N0} 行代码，涉及 {languages} 种编程语言";
                }

                StatusMessage = HasLocalProjects ? "项目扫描完成" : "未发现有效项目";

                AddOperationResult("本地项目扫描", LocalProjectsStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描本地项目失败");
                StatusMessage = "扫描项目失败";
                AddOperationResult("本地项目扫描", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 使用本地项目进行分析
        /// </summary>
        private async Task UseLocalProjectsAsync()
        {
            try
            {
                if (!HasLocalProjects)
                {
                    StatusMessage = "没有可用的本地项目";
                    return;
                }

                IsLoading = true;
                StatusMessage = "正在分析本地项目...";

                // 执行向量化分析
                await AnalyzeLocalProjectsAsync();

                StatusMessage = "本地项目分析完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用本地项目分析失败");
                StatusMessage = "本地项目分析失败";
                AddOperationResult("本地项目分析", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 分析本地项目
        /// </summary>
        private async Task AnalyzeLocalProjectsAsync()
        {
            var analysisResults = new List<string>();

            // 项目类型分布
            var typeGroups = LocalProjects.GroupBy(p => p.Type).ToList();
            analysisResults.Add($"项目类型分布:");
            foreach (var group in typeGroups)
            {
                analysisResults.Add($"  - {group.Key}: {group.Count()} 个项目");
            }

            // 编程语言分布
            var languageGroups = LocalProjects.GroupBy(p => p.Language).ToList();
            analysisResults.Add($"\n编程语言分布:");
            foreach (var group in languageGroups.OrderByDescending(g => g.Count()))
            {
                var totalLines = group.Sum(p => p.Statistics.CodeLines);
                analysisResults.Add($"  - {group.Key}: {group.Count()} 个项目，{totalLines:N0} 行代码");
            }

            // 复杂度分析
            var avgComplexity = LocalProjects.Average(p => p.ComplexityScore);
            var highComplexityProjects = LocalProjects.Where(p => p.ComplexityScore > 0.7).ToList();
            analysisResults.Add($"\n复杂度分析:");
            analysisResults.Add($"  - 平均复杂度: {avgComplexity:P0}");
            analysisResults.Add($"  - 高复杂度项目: {highComplexityProjects.Count} 个");

            // 质量分析
            var avgQuality = LocalProjects.Average(p => p.QualityScore);
            var lowQualityProjects = LocalProjects.Where(p => p.QualityScore < 0.6).ToList();
            analysisResults.Add($"\n质量分析:");
            analysisResults.Add($"  - 平均质量评分: {avgQuality:P0}");
            analysisResults.Add($"  - 需要改进的项目: {lowQualityProjects.Count} 个");

            // 技术栈分析
            var allTechnologies = LocalProjects.SelectMany(p => p.TechnologyStack).Distinct().ToList();
            analysisResults.Add($"\n技术栈分析:");
            analysisResults.Add($"  - 使用的技术: {string.Join(", ", allTechnologies.Take(10))}");
            if (allTechnologies.Count > 10)
                analysisResults.Add($"  - 等共 {allTechnologies.Count} 种技术");

            // 问题和建议汇总
            var allIssues = LocalProjects.SelectMany(p => p.Issues).GroupBy(i => i).ToList();
            var allRecommendations = LocalProjects.SelectMany(p => p.Recommendations).GroupBy(r => r).ToList();

            analysisResults.Add($"\n常见问题:");
            foreach (var issue in allIssues.OrderByDescending(g => g.Count()).Take(5))
            {
                analysisResults.Add($"  - {issue.Key} ({issue.Count()} 个项目)");
            }

            analysisResults.Add($"\n改进建议:");
            foreach (var recommendation in allRecommendations.OrderByDescending(g => g.Count()).Take(5))
            {
                analysisResults.Add($"  - {recommendation.Key} ({recommendation.Count()} 个项目)");
            }

            DetailedResults = string.Join("\n", analysisResults);
            ShowDetailedResults = true;

            AddOperationResult("本地项目分析",
                $"分析了 {LocalProjects.Count} 个本地项目，平均复杂度 {avgComplexity:P0}，平均质量 {avgQuality:P0}");
        }

        #endregion

        #region Vector Database Methods

        /// <summary>
        /// 初始化向量数据库
        /// </summary>
        private async Task InitializeVectorDatabaseAsync()
        {
            try
            {
                IsLoading = true;
                VectorDatabaseStatus = "正在初始化向量数据库...";

                await _vectorDatabaseService.InitializeAsync();

                var stats = await _vectorDatabaseService.GetDatabaseStatsAsync();
                VectorDatabaseStatus = $"向量数据库已就绪 - {stats.TotalVectors} 个向量，{stats.VectorDimensions} 维";
                IsVectorDatabaseReady = true;

                AddOperationResult("向量数据库初始化", VectorDatabaseStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向量数据库初始化失败");
                VectorDatabaseStatus = "向量数据库初始化失败";
                IsVectorDatabaseReady = false;
                AddOperationResult("向量数据库初始化", $"失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 构建向量数据库
        /// </summary>
        private async Task BuildVectorDatabaseAsync()
        {
            try
            {
                if (!IsVectorDatabaseReady)
                {
                    StatusMessage = "请先初始化向量数据库";
                    return;
                }

                IsLoading = true;
                StatusMessage = "正在构建向量数据库...";

                // 向量化模拟项目数据
                var projects = await _projectService.GetAllProjectsAsync();
                var projectList = projects.ToList();

                var batchResult = await _vectorDatabaseService.BatchVectorizeProjectsAsync(projectList);

                // 向量化本地项目数据
                if (HasLocalProjects)
                {
                    foreach (var localProject in LocalProjects)
                    {
                        try
                        {
                            await _vectorDatabaseService.VectorizeAndStoreLocalProjectAsync(localProject);
                            batchResult.SuccessCount++;
                        }
                        catch (Exception ex)
                        {
                            batchResult.FailureCount++;
                            batchResult.Errors.Add($"本地项目 {localProject.Name} 向量化失败: {ex.Message}");
                        }
                    }
                }

                var stats = await _vectorDatabaseService.GetDatabaseStatsAsync();
                VectorDatabaseStatus = $"向量数据库已更新 - {stats.TotalVectors} 个向量";

                AddOperationResult("构建向量数据库",
                    $"成功向量化 {batchResult.SuccessCount} 个项目，失败 {batchResult.FailureCount} 个，耗时 {batchResult.Duration.TotalSeconds:F2} 秒");

                StatusMessage = "向量数据库构建完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建向量数据库失败");
                StatusMessage = "构建向量数据库失败";
                AddOperationResult("构建向量数据库", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 搜索向量数据库
        /// </summary>
        private async Task SearchVectorDatabaseAsync()
        {
            try
            {
                if (!IsVectorDatabaseReady)
                {
                    StatusMessage = "请先初始化向量数据库";
                    return;
                }

                IsLoading = true;
                StatusMessage = "正在搜索向量数据库...";

                // 示例搜索查询
                var searchQueries = new[]
                {
                    "AI人工智能机器学习项目",
                    "Web前端React Vue项目",
                    "后端API微服务项目",
                    "数据分析Python项目",
                    "移动应用开发项目"
                };

                var allResults = new List<VectorSearchResult>();

                foreach (var query in searchQueries)
                {
                    var results = await _vectorDatabaseService.SearchSimilarProjectsAsync(query, 3, 0.6);
                    allResults.AddRange(results);
                }

                // 去重并按相似度排序
                var uniqueResults = allResults
                    .GroupBy(r => r.ProjectId)
                    .Select(g => g.OrderByDescending(r => r.SimilarityScore).First())
                    .OrderByDescending(r => r.SimilarityScore)
                    .Take(10)
                    .ToList();

                AddOperationResult("向量数据库搜索",
                    $"执行了 {searchQueries.Length} 个查询，找到 {uniqueResults.Count} 个相关项目");

                DetailedResults = $"向量搜索结果:\n" +
                                 string.Join("\n", uniqueResults.Select(r =>
                                     $"- {r.ProjectName} ({r.ProjectType}) - 相似度: {r.SimilarityScore:P1}\n  {r.Description}"));
                ShowDetailedResults = true;

                StatusMessage = "向量数据库搜索完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索向量数据库失败");
                StatusMessage = "搜索向量数据库失败";
                AddOperationResult("向量数据库搜索", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 执行聚类分析
        /// </summary>
        private async Task PerformClusterAnalysisAsync()
        {
            try
            {
                if (!IsVectorDatabaseReady)
                {
                    StatusMessage = "请先初始化向量数据库";
                    return;
                }

                IsLoading = true;
                StatusMessage = "正在执行聚类分析...";

                var clusterResult = await _vectorDatabaseService.PerformClusterAnalysisAsync(5);

                AddOperationResult("聚类分析",
                    $"生成了 {clusterResult.ClusterCount} 个聚类，轮廓系数: {clusterResult.SilhouetteScore:F3}");

                var clusterDetails = new List<string>();
                clusterDetails.Add($"聚类分析结果 (轮廓系数: {clusterResult.SilhouetteScore:F3}):");

                foreach (var cluster in clusterResult.Clusters)
                {
                    clusterDetails.Add($"\n{cluster.ClusterName}:");
                    clusterDetails.Add($"  项目数量: {cluster.ProjectIds.Count}");

                    if (cluster.Characteristics.TryGetValue("DominantProjectType", out var dominantType))
                        clusterDetails.Add($"  主要类型: {dominantType}");

                    if (cluster.Characteristics.TryGetValue("ProjectTypes", out var types) && types is List<string> typeList)
                        clusterDetails.Add($"  包含类型: {string.Join(", ", typeList.Take(3))}");
                }

                DetailedResults = string.Join("\n", clusterDetails);
                ShowDetailedResults = true;

                StatusMessage = "聚类分析完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "聚类分析失败");
                StatusMessage = "聚类分析失败";
                AddOperationResult("聚类分析", $"操作失败: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion
    }

    /// <summary>
    /// 操作结果
    /// </summary>
    public class OperationResult
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Timestamp { get; set; } = string.Empty;
    }
}
