"""
自动化任务管理器
实现自动状态更新和任务队列管理
"""

import time
import threading
from tkinter import messagebox
from typing import Optional, List, Dict, Any, Callable


class AutoTaskManager:
    """自动化任务管理器"""
    
    def __init__(self, parent_ui, api_client):
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # 自动化控制
        self.auto_mode_enabled = False
        self.auto_thread = None
        self.stop_auto_mode = False
        
        # 配置参数
        self.config = {
            'check_interval': 10,  # 检查间隔（秒）
            'completion_timeout': 300,  # 完成超时（秒）
            'max_retries': 3,  # 最大重试次数
            'auto_next_task': True,  # 自动进行下一个任务
            'auto_status_update': True,  # 自动状态更新
        }
        
        # 任务队列
        self.task_queue = []
        self.current_task_index = 0
        
        # 回调函数
        self.on_task_completed = None
        self.on_task_failed = None
        self.on_queue_completed = None
        
    def enable_auto_mode(self):
        """启用自动模式"""
        if self.auto_mode_enabled:
            print("⚠️ 自动模式已启用，跳过重复启用")
            return
            
        self.auto_mode_enabled = True
        self.stop_auto_mode = False
        
        # 启动自动化线程
        self.auto_thread = threading.Thread(target=self._auto_mode_worker, daemon=True)
        self.auto_thread.start()
        
        print("🤖 自动化任务管理器已启用")
        
    def disable_auto_mode(self):
        """禁用自动模式"""
        if not self.auto_mode_enabled:
            return
            
        self.auto_mode_enabled = False
        self.stop_auto_mode = True
        
        if self.auto_thread and self.auto_thread.is_alive():
            self.auto_thread.join(timeout=5)
            
        print("🛑 自动化任务管理器已禁用")
        
    def set_task_queue(self, tasks: List[Dict[str, Any]]):
        """设置任务队列"""
        self.task_queue = tasks.copy()
        self.current_task_index = 0
        print(f"📋 任务队列已设置，共 {len(tasks)} 个任务")
        
    def add_task_to_queue(self, task: Dict[str, Any]):
        """添加任务到队列"""
        self.task_queue.append(task)
        print(f"➕ 任务已添加到队列: {task.get('stepName', '未知任务')}")
        
    def get_current_task(self) -> Optional[Dict[str, Any]]:
        """获取当前任务"""
        if 0 <= self.current_task_index < len(self.task_queue):
            return self.task_queue[self.current_task_index]
        return None
        
    def get_next_task(self) -> Optional[Dict[str, Any]]:
        """获取下一个任务"""
        next_index = self.current_task_index + 1
        if next_index < len(self.task_queue):
            return self.task_queue[next_index]
        return None
        
    def move_to_next_task(self) -> bool:
        """移动到下一个任务"""
        if self.current_task_index + 1 < len(self.task_queue):
            self.current_task_index += 1
            return True
        return False
        
    def _auto_mode_worker(self):
        """自动模式工作线程"""
        print("🚀 自动化工作线程已启动")
        
        while self.auto_mode_enabled and not self.stop_auto_mode:
            try:
                # 检查当前任务状态
                self._check_and_process_current_task()
                
                # 等待下次检查
                time.sleep(self.config['check_interval'])
                
            except Exception as e:
                print(f"❌ 自动化工作线程异常: {e}")
                time.sleep(5)  # 异常后等待5秒再继续
                
        print("🛑 自动化工作线程已停止")
        
    def _check_and_process_current_task(self):
        """检查并处理当前任务"""
        current_task = self.get_current_task()
        if not current_task:
            print("ℹ️ 任务队列为空或已完成")
            self._handle_queue_completed()
            return
            
        task_status = current_task.get('status', 'Unknown')
        task_name = current_task.get('stepName', '未知任务')
        
        print(f"🔍 检查任务状态: {task_name} - {task_status}")
        
        if task_status == 'InProgress':
            # 检查任务是否完成
            if self._is_task_completed(current_task):
                print(f"✅ 任务已完成: {task_name}")
                self._handle_task_completed(current_task)
            else:
                print(f"⏳ 任务仍在进行中: {task_name}")
                
        elif task_status in ['Pending', 'NotStarted']:
            # 检查是否有其他InProgress状态的任务
            if self._has_inprogress_tasks():
                print(f"⚠️ 列表中有InProgress状态的开发步骤，不能进行其他步骤的开发")
                return

            # 自动开始任务
            if self.config['auto_next_task']:
                print(f"🚀 自动开始任务: {task_name} (状态: {task_status})")
                self._start_task(current_task)
            else:
                print(f"⏸️ 任务等待手动开始: {task_name} (状态: {task_status})")
                
        elif task_status == 'Completed':
            # 当前任务已完成，智能跳过到下一个待处理任务
            print(f"📋 当前任务已完成，智能跳过到下一个待处理任务...")
            self._skip_to_next_pending_task()

        elif task_status == 'Failed':
            print(f"❌ 任务失败: {task_name}")
            self._handle_task_failed(current_task)

        else:
            print(f"⚠️ 未知任务状态: {task_status}")

    def _skip_to_next_pending_task(self):
        """智能跳过已完成任务，移动到下一个待处理任务"""
        try:
            original_index = self.current_task_index
            skipped_count = 0

            # 循环查找下一个待处理任务
            while self.current_task_index < len(self.task_queue):
                if self.move_to_next_task():
                    skipped_count += 1
                    next_task = self.get_current_task()
                    if next_task:
                        next_status = next_task.get('status', 'Unknown')
                        next_name = next_task.get('stepName', '未知任务')

                        print(f"🔍 检查任务: {next_name} - 状态: {next_status}")

                        if next_status in ['Pending', 'NotStarted']:
                            # 找到待处理任务
                            print(f"✅ 找到下一个待处理任务: {next_name} (跳过了{skipped_count}个已完成任务)")

                            # 立即开始这个任务
                            if self.config['auto_next_task']:
                                if not self._has_inprogress_tasks():
                                    print(f"🚀 立即开始任务: {next_name}")
                                    self._start_task(next_task)
                                else:
                                    print(f"⚠️ 有其他InProgress状态的任务，暂停开始: {next_name}")
                            return
                        elif next_status == 'Completed':
                            # 继续跳过已完成任务
                            print(f"⏭️ 跳过已完成任务: {next_name}")
                            continue
                        elif next_status == 'InProgress':
                            # 找到进行中任务，停止在这里
                            print(f"🔄 找到进行中任务: {next_name}")
                            return
                else:
                    # 没有更多任务了
                    break

            # 如果到这里，说明没有找到待处理任务
            print(f"🎉 所有任务已完成 (跳过了{skipped_count}个已完成任务)")
            self._handle_queue_completed()

        except Exception as e:
            print(f"❌ 智能跳过任务异常: {e}")
            # 恢复到原始位置
            self.current_task_index = original_index

    def _is_task_completed(self, task: Dict[str, Any]) -> bool:
        """检查任务是否完成"""
        try:
            print(f"🔍 检查任务完成状态: {task.get('stepName', '未知任务')}")

            # 方法1: 检查编译错误
            has_errors = self._has_compilation_errors()
            print(f"   编译错误检查: {'有错误' if has_errors else '无错误'}")

            if has_errors:
                # 检查Copilot状态
                copilot_working = self._is_copilot_working()
                print(f"   Copilot状态检查: {'工作中' if copilot_working else '空闲'}")

                if not copilot_working:
                    # 检查是否开启了循环检测功能
                    loop_detection_running = self._is_loop_detection_running()
                    if loop_detection_running:
                        print(f"   编译错误检查: 有错误，Copilot未在工作，循环检测已开启")
                        print(f"   🤖 循环检测将在Copilot停止工作指定时间后自动发送编译错误")

                        # 获取错误发送延迟设置
                        delay_info = self._get_error_send_delay_info()
                        if delay_info:
                            print(f"   ⏱️ 错误发送延迟设置: {delay_info}")
                    else:
                        print(f"   编译错误检查: 有错误，Copilot未在工作，但循环检测未开启，不会自动发送")
                        print(f"   💡 提示: 请开启'🔄 循环检测'功能以自动发送编译错误给Copilot")
                else:
                    print(f"   编译错误检查: 有错误，Copilot正在工作中，等待Copilot完成")

                print(f"⏳ 任务仍在进行中: {task.get('stepName', '未知任务')}")
                return False
            else:
                # 方法2: 检查Copilot状态
                copilot_working = self._is_copilot_working()
                print(f"   Copilot状态检查: {'工作中' if copilot_working else '空闲'}")

                if not copilot_working:
                    # 方法3: 检查任务运行时间（避免过于频繁的询问）
                    if self._should_ask_completion_confirmation(task):
                        # 方法4: 询问用户确认（10秒自动确认）
                        return self._ask_user_completion_confirmation_with_timeout(task)
                    else:
                        print("   跳过用户确认（时间间隔不足）")

            return False

        except Exception as e:
            print(f"❌ 检查任务完成状态异常: {e}")
            return False

    def _should_ask_completion_confirmation(self, task: Dict[str, Any]) -> bool:
        """判断是否应该询问完成确认（避免过于频繁）"""
        task_id = task.get('id')
        current_time = time.time()

        # 检查上次询问时间
        if not hasattr(self, '_last_ask_time'):
            self._last_ask_time = {}

        last_ask = self._last_ask_time.get(task_id, 0)
        min_interval = 60  # 最少间隔60秒

        if current_time - last_ask >= min_interval:
            self._last_ask_time[task_id] = current_time
            return True

        return False
            
    def _has_compilation_errors(self) -> bool:
        """检查是否有编译错误"""
        try:
            # 通过父UI获取编译错误信息
            if hasattr(self.parent_ui, 'error_manager'):
                print("   正在获取编译错误信息...")

                # 使用正确的方法名
                backend_errors = self.parent_ui.error_manager.get_backend_compilation_errors()
                frontend_errors = self.parent_ui.error_manager.get_frontend_compilation_errors()

                print(f"   获取到后端错误数量: {len(backend_errors) if backend_errors else 0}")
                print(f"   获取到前端错误数量: {len(frontend_errors) if frontend_errors else 0}")

                # 过滤出ERROR级别的错误（不包括WARNING）
                backend_error_count = sum(1 for e in backend_errors if e.get('severity') == 'ERROR') if backend_errors else 0
                frontend_error_count = sum(1 for e in frontend_errors if e.get('severity') == 'ERROR') if frontend_errors else 0

                total_errors = backend_error_count + frontend_error_count
                print(f"   后端ERROR级别错误: {backend_error_count}, 前端ERROR级别错误: {frontend_error_count}, 总计: {total_errors}")

                return total_errors > 0
            else:
                print("   error_manager不存在")

        except Exception as e:
            print(f"❌ 检查编译错误异常: {e}")
            import traceback
            print(f"   详细错误信息: {traceback.format_exc()}")

        return False
        
    def _is_copilot_working(self) -> bool:
        """检查Copilot是否正在工作"""
        try:
            print("   正在检查Copilot工作状态...")

            # 通过DOM脚本检查Copilot状态
            if hasattr(self.parent_ui, 'execution_manager'):
                execution_manager = self.parent_ui.execution_manager
                print(f"   execution_manager存在: {execution_manager is not None}")

                if hasattr(execution_manager, 'dom_script_instance') and execution_manager.dom_script_instance:
                    print("   DOM脚本实例存在，正在检查状态...")
                    # 使用正确的方法调用
                    result = execution_manager.dom_script_instance._check_copilot_working_status()
                    print(f"   Copilot状态检查结果: {result}")
                    return result is True
                else:
                    print("   DOM脚本实例不存在或未初始化")
            else:
                print("   execution_manager不存在")

        except Exception as e:
            print(f"❌ 检查Copilot状态异常: {e}")
            import traceback
            print(f"   详细错误信息: {traceback.format_exc()}")

        return False
        
    def _ask_user_completion_confirmation(self, task: Dict[str, Any]) -> bool:
        """询问用户确认任务是否完成"""
        task_name = task.get('stepName', '未知任务')
        
        # 在主线程中显示确认对话框
        result = [False]  # 使用列表来存储结果，以便在lambda中修改
        
        def ask_confirmation():
            try:
                answer = messagebox.askyesno(
                    "任务完成确认",
                    f"任务 '{task_name}' 是否已完成？\n\n"
                    f"• 没有发现编译错误\n"
                    f"• Copilot已停止工作\n\n"
                    f"点击'是'将自动更新状态并进行下一个任务"
                )
                result[0] = answer
                print(f"📝 用户确认结果: {answer}")
            except Exception as e:
                print(f"❌ 显示确认对话框异常: {e}")
                result[0] = False

        # 在主线程中执行
        if hasattr(self.parent_ui, 'parent'):
            self.parent_ui.parent.after(0, ask_confirmation)

            # 等待用户响应（最多等待30秒）
            wait_time = 0
            while wait_time < 30 and result[0] is False:
                time.sleep(0.5)
                wait_time += 0.5

            print(f"⏰ 等待结束，最终结果: {result[0]}")

        return result[0]

    def _ask_user_completion_confirmation_with_timeout(self, task: Dict[str, Any]) -> bool:
        """询问用户确认任务是否完成（10秒超时自动确认）"""
        task_name = task.get('stepName', '未知任务')

        # 在主线程中显示确认对话框
        result = [None]  # None表示未响应，True/False表示用户选择
        dialog_window = [None]  # 存储对话框窗口引用

        def ask_confirmation():
            try:
                import tkinter as tk
                from tkinter import messagebox

                # 创建自定义对话框窗口
                dialog = tk.Toplevel()
                dialog.title("任务完成确认")
                dialog.geometry("400x200")
                dialog.resizable(False, False)

                # 居中显示
                dialog.transient(self.parent_ui.parent)
                dialog.grab_set()

                # 计算居中位置
                dialog.update_idletasks()
                x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
                y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
                dialog.geometry(f"+{x}+{y}")

                dialog_window[0] = dialog

                # 消息内容
                message_frame = tk.Frame(dialog, padx=20, pady=20)
                message_frame.pack(fill=tk.BOTH, expand=True)

                tk.Label(message_frame, text=f"任务 '{task_name}' 是否已完成？",
                        font=("Arial", 12, "bold")).pack(pady=(0, 10))

                tk.Label(message_frame, text="• 没有发现编译错误",
                        font=("Arial", 10)).pack(anchor=tk.W)
                tk.Label(message_frame, text="• Copilot已停止工作",
                        font=("Arial", 10)).pack(anchor=tk.W)

                # 倒计时标签
                countdown_label = tk.Label(message_frame, text="10秒后自动确认...",
                                         font=("Arial", 10), fg="orange")
                countdown_label.pack(pady=(10, 0))

                # 按钮框架
                button_frame = tk.Frame(dialog)
                button_frame.pack(pady=(0, 20))

                def on_yes():
                    result[0] = True
                    dialog.destroy()

                def on_no():
                    result[0] = False
                    dialog.destroy()

                tk.Button(button_frame, text="是", command=on_yes,
                         width=8, font=("Arial", 10)).pack(side=tk.LEFT, padx=(0, 10))
                tk.Button(button_frame, text="否", command=on_no,
                         width=8, font=("Arial", 10)).pack(side=tk.LEFT)

                # 倒计时逻辑
                countdown = [10]  # 10秒倒计时

                def update_countdown():
                    if result[0] is not None:  # 用户已选择
                        return

                    if countdown[0] > 0:
                        countdown_label.config(text=f"{countdown[0]}秒后自动确认...")
                        countdown[0] -= 1
                        dialog.after(1000, update_countdown)
                    else:
                        # 超时自动确认
                        result[0] = True
                        print(f"⏰ 10秒超时，自动确认任务完成: {task_name}")
                        dialog.destroy()

                # 开始倒计时
                update_countdown()

            except Exception as e:
                print(f"❌ 显示确认对话框异常: {e}")
                result[0] = True  # 异常时默认确认
                if dialog_window[0]:
                    dialog_window[0].destroy()

        # 在主线程中执行
        if hasattr(self.parent_ui, 'parent'):
            self.parent_ui.parent.after(0, ask_confirmation)

            # 等待用户响应或超时（最多等待15秒，给倒计时留余量）
            wait_time = 0
            while wait_time < 15 and result[0] is None:
                time.sleep(0.1)
                wait_time += 0.1

            final_result = result[0] if result[0] is not None else True
            print(f"📝 用户确认结果: {final_result}")
            return final_result

        return True  # 如果无法显示对话框，默认确认

    def _start_task(self, task: Dict[str, Any]):
        """开始任务"""
        try:
            # 通过父UI开始任务
            if hasattr(self.parent_ui, 'execution_manager'):
                self.parent_ui.execution_manager.execute_selected_step(task)
                print(f"🚀 任务已自动开始: {task.get('stepName', '未知任务')}")

                # 立即更新本地任务队列中的状态，确保下次循环能检测到正确状态
                current_task = self.get_current_task()
                if current_task and current_task.get('id') == task.get('id'):
                    current_task['status'] = 'InProgress'
                    print(f"🔄 本地任务队列状态已更新为InProgress")

        except Exception as e:
            print(f"❌ 自动开始任务失败: {e}")

    def _has_inprogress_tasks(self) -> bool:
        """检查是否有InProgress状态的任务"""
        try:
            if hasattr(self.parent_ui, 'has_inprogress_steps'):
                return self.parent_ui.has_inprogress_steps()
            return False
        except Exception as e:
            print(f"❌ 检查InProgress任务状态异常: {e}")
            return False

    def _is_loop_detection_running(self) -> bool:
        """检查循环检测是否正在运行"""
        try:
            if hasattr(self.parent_ui, 'execution_manager'):
                execution_manager = self.parent_ui.execution_manager
                if hasattr(execution_manager, 'is_loop_detection_running'):
                    return execution_manager.is_loop_detection_running
            return False
        except Exception as e:
            print(f"❌ 检查循环检测状态异常: {e}")
            return False

    def _get_error_send_delay_info(self) -> str:
        """获取错误发送延迟设置信息"""
        try:
            if hasattr(self.parent_ui, 'error_send_delay_var'):
                delay_var = self.parent_ui.error_send_delay_var
                if delay_var:
                    delay_value = delay_var.get()
                    return f"{delay_value}秒"
            return "10秒(默认)"
        except Exception as e:
            print(f"❌ 获取错误发送延迟设置异常: {e}")
            return "未知"

    def _handle_task_completed(self, task: Dict[str, Any]):
        """处理任务完成"""
        try:
            # 自动更新状态为完成
            if self.config['auto_status_update']:
                if hasattr(self.parent_ui, 'perform_status_update'):
                    self.parent_ui.perform_status_update(task, 'Completed')
                    print(f"✅ 任务状态已自动更新为完成: {task.get('stepName', '未知任务')}")

                    # 额外更新开发步骤进度为100%（如果perform_status_update没有处理）
                    self._ensure_step_progress_updated(task)

            # 立即更新本地任务队列中的状态和进度，确保下次循环能检测到
            current_task = self.get_current_task()
            if current_task and current_task.get('id') == task.get('id'):
                current_task['status'] = 'Completed'
                current_task['progress'] = 100  # 设置进度为100%
                print(f"🔄 本地任务队列状态已更新为Completed，进度设置为100%")

                # 立即触发下一次检查，不等待定时器
                print(f"⚡ 立即触发下一个任务检查...")
                threading.Timer(1.0, self._check_and_process_current_task).start()

            # 调用完成回调
            if self.on_task_completed:
                self.on_task_completed(task)

        except Exception as e:
            print(f"❌ 处理任务完成异常: {e}")

    def _ensure_step_progress_updated(self, task: Dict[str, Any]):
        """确保开发步骤进度已根据状态更新"""
        try:
            step_id = task.get('id')
            status = task.get('status', 'Unknown')

            if step_id and hasattr(self.parent_ui, 'api_client'):
                # 根据状态设置相应的进度
                progress_value = self._get_progress_by_status(status)
                print(f"🔄 确保步骤进度更新为{progress_value}%: {task.get('stepName', '未知任务')} (状态: {status})")

                result = self.parent_ui.api_client.update_development_step(
                    step_id=step_id,
                    progress=progress_value
                )

                if result:
                    print(f"✅ 步骤进度确认更新为{progress_value}%")
                else:
                    print(f"⚠️ 步骤进度更新确认失败")
        except Exception as e:
            print(f"❌ 确保步骤进度更新异常: {e}")

    def _get_progress_by_status(self, status):
        """根据状态获取相应的进度值"""
        status_progress_map = {
            'NotStarted': 0,
            'Pending': 0,
            'InProgress': 50,
            'Completed': 100,
            'Failed': 0,
            'Blocked': 0
        }
        return status_progress_map.get(status, 0)

    def _handle_task_failed(self, task: Dict[str, Any]):
        """处理任务失败"""
        try:
            # 调用失败回调
            if self.on_task_failed:
                self.on_task_failed(task)
                
            # 根据配置决定是否继续下一个任务
            if self.config.get('continue_on_failure', False):
                self.move_to_next_task()
            else:
                print("❌ 任务失败，自动模式暂停")
                self.disable_auto_mode()
                
        except Exception as e:
            print(f"❌ 处理任务失败异常: {e}")
            
    def _handle_queue_completed(self):
        """处理队列完成"""
        try:
            print("🎉 所有任务已完成")
            
            # 调用队列完成回调
            if self.on_queue_completed:
                self.on_queue_completed()
                
            # 禁用自动模式
            self.disable_auto_mode()
            
        except Exception as e:
            print(f"❌ 处理队列完成异常: {e}")
            
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        current_task = self.get_current_task()
        return {
            'auto_mode_enabled': self.auto_mode_enabled,
            'task_queue_length': len(self.task_queue),
            'current_task_index': self.current_task_index,
            'current_task': current_task.get('stepName', '无') if current_task else '无',
            'remaining_tasks': len(self.task_queue) - self.current_task_index - 1 if current_task else 0
        }
