using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;
using System.Security.Claims;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 问题管理控制器
/// 功能: 处理问题的CRUD操作、状态管理、分配管理等
/// 支持: 问题搜索、统计、批量操作
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("GeneralApi")]
[Produces("application/json")]
public class IssuesController : ControllerBase
{
    private readonly ILogger<IssuesController> _logger;
    private readonly IIssueRepository _issueRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IUserRepository _userRepository;

    public IssuesController(
        ILogger<IssuesController> logger,
        IIssueRepository issueRepository,
        IProjectRepository projectRepository,
        IUserRepository userRepository)
    {
        _logger = logger;
        _issueRepository = issueRepository;
        _projectRepository = projectRepository;
        _userRepository = userRepository;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }

    /// <summary>
    /// 获取问题列表（支持搜索和筛选）
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<object>> GetIssues(
        [FromQuery] string? keyword = null,
        [FromQuery] int? projectId = null,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? issueType = null,
        [FromQuery] int? assignedTo = null,
        [FromQuery] int pageIndex = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var result = await _issueRepository.SearchIssuesAsync(
                keyword, projectId, status, priority, issueType, assignedTo, pageIndex, pageSize);

            return Ok(new
            {
                success = true,
                data = new
                {
                    items = result.Items.Select(MapToDto),
                    totalCount = result.TotalCount,
                    pageIndex = result.PageIndex,
                    pageSize = result.PageSize,
                    totalPages = (int)Math.Ceiling((double)result.TotalCount / pageSize)
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取问题列表失败");
            return StatusCode(500, new { success = false, message = "获取问题列表失败" });
        }
    }

    /// <summary>
    /// 根据ID获取问题详情
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<object>> GetIssue(int id)
    {
        try
        {
            var issue = await _issueRepository.GetByIdAsync(id);
            if (issue == null)
            {
                return NotFound(new { success = false, message = "问题不存在" });
            }

            return Ok(new { success = true, data = MapToDto(issue) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取问题详情失败，IssueId: {IssueId}", id);
            return StatusCode(500, new { success = false, message = "获取问题详情失败" });
        }
    }

    /// <summary>
    /// 创建新问题
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<object>> CreateIssue([FromBody] CreateIssueRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 创建问题: {Title}", userId, request.Title);

            // 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                return BadRequest(new { success = false, message = "项目不存在" });
            }

            // 验证分配用户是否存在（如果指定）
            if (request.AssignedTo.HasValue)
            {
                var assignedUser = await _userRepository.GetByIdAsync(request.AssignedTo.Value);
                if (assignedUser == null)
                {
                    return BadRequest(new { success = false, message = "指定的分配用户不存在" });
                }
            }

            var issue = new Issue
            {
                ProjectId = request.ProjectId,
                Title = request.Title,
                Description = request.Description,
                IssueType = request.IssueType,
                Priority = request.Priority ?? "Medium",
                Status = "Open",
                AssignedTo = request.AssignedTo,
                ReportedBy = userId,
                Labels = request.Labels
            };

            var createdIssue = await _issueRepository.AddAsync(issue);
            return Ok(new { success = true, data = MapToDto(createdIssue), message = "问题创建成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建问题失败");
            return StatusCode(500, new { success = false, message = "创建问题失败" });
        }
    }

    /// <summary>
    /// 更新问题
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<object>> UpdateIssue(int id, [FromBody] UpdateIssueRequest request)
    {
        try
        {
            var issue = await _issueRepository.GetByIdAsync(id);
            if (issue == null)
            {
                return NotFound(new { success = false, message = "问题不存在" });
            }

            // 验证分配用户是否存在（如果指定）
            if (request.AssignedTo.HasValue)
            {
                var assignedUser = await _userRepository.GetByIdAsync(request.AssignedTo.Value);
                if (assignedUser == null)
                {
                    return BadRequest(new { success = false, message = "指定的分配用户不存在" });
                }
            }

            // 更新字段
            if (!string.IsNullOrWhiteSpace(request.Title))
                issue.Title = request.Title;
            if (!string.IsNullOrWhiteSpace(request.Description))
                issue.Description = request.Description;
            if (!string.IsNullOrWhiteSpace(request.IssueType))
                issue.IssueType = request.IssueType;
            if (!string.IsNullOrWhiteSpace(request.Priority))
                issue.Priority = request.Priority;
            if (!string.IsNullOrWhiteSpace(request.Status))
            {
                issue.Status = request.Status;
                if (request.Status == "Resolved" || request.Status == "Closed")
                {
                    issue.ResolvedAt = DateTime.Now;
                }
            }
            if (request.AssignedTo != issue.AssignedTo)
                issue.AssignedTo = request.AssignedTo;
            if (request.Labels != null)
                issue.Labels = request.Labels;

            issue.UpdatedTime = DateTime.Now;

            var success = await _issueRepository.UpdateAsync(issue);
            if (!success)
            {
                return StatusCode(500, new { success = false, message = "更新问题失败" });
            }

            return Ok(new { success = true, data = MapToDto(issue), message = "问题更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新问题失败，IssueId: {IssueId}", id);
            return StatusCode(500, new { success = false, message = "更新问题失败" });
        }
    }

    /// <summary>
    /// 删除问题
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<object>> DeleteIssue(int id)
    {
        try
        {
            var issue = await _issueRepository.GetByIdAsync(id);
            if (issue == null)
            {
                return NotFound(new { success = false, message = "问题不存在" });
            }

            var success = await _issueRepository.DeleteAsync(id);
            if (!success)
            {
                return StatusCode(500, new { success = false, message = "删除问题失败" });
            }

            return Ok(new { success = true, message = "问题删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除问题失败，IssueId: {IssueId}", id);
            return StatusCode(500, new { success = false, message = "删除问题失败" });
        }
    }

    /// <summary>
    /// 获取问题统计信息
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetIssueStatistics([FromQuery] int? projectId = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var statistics = await _issueRepository.GetIssueStatisticsAsync(projectId, userId);

            return Ok(new { success = true, data = statistics });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取问题统计信息失败");
            return StatusCode(500, new { success = false, message = "获取统计信息失败" });
        }
    }

    /// <summary>
    /// 更新问题状态
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<ActionResult<object>> UpdateIssueStatus(int id, [FromBody] UpdateIssueStatusRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var success = await _issueRepository.UpdateIssueStatusAsync(id, request.Status, userId);

            if (!success)
            {
                return NotFound(new { success = false, message = "问题不存在或更新失败" });
            }

            return Ok(new { success = true, message = "问题状态更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新问题状态失败，IssueId: {IssueId}", id);
            return StatusCode(500, new { success = false, message = "更新问题状态失败" });
        }
    }

    /// <summary>
    /// 分配问题给用户
    /// </summary>
    [HttpPatch("{id}/assign")]
    public async Task<ActionResult<object>> AssignIssue(int id, [FromBody] AssignIssueRequest request)
    {
        try
        {
            // 验证分配用户是否存在（如果指定）
            if (request.AssignedTo.HasValue)
            {
                var assignedUser = await _userRepository.GetByIdAsync(request.AssignedTo.Value);
                if (assignedUser == null)
                {
                    return BadRequest(new { success = false, message = "指定的分配用户不存在" });
                }
            }

            var success = await _issueRepository.AssignIssueAsync(id, request.AssignedTo);

            if (!success)
            {
                return NotFound(new { success = false, message = "问题不存在或分配失败" });
            }

            return Ok(new { success = true, message = "问题分配成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配问题失败，IssueId: {IssueId}", id);
            return StatusCode(500, new { success = false, message = "分配问题失败" });
        }
    }

    /// <summary>
    /// 根据项目ID获取问题列表
    /// </summary>
    [HttpGet("project/{projectId}")]
    public async Task<ActionResult<object>> GetIssuesByProject(int projectId)
    {
        try
        {
            var issues = await _issueRepository.GetByProjectIdAsync(projectId);
            return Ok(new { success = true, data = issues.Select(MapToDto) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目ID获取问题列表失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "获取问题列表失败" });
        }
    }

    /// <summary>
    /// 获取分配给当前用户的问题
    /// </summary>
    [HttpGet("assigned-to-me")]
    public async Task<ActionResult<object>> GetAssignedIssues()
    {
        try
        {
            var userId = GetCurrentUserId();
            var issues = await _issueRepository.GetByAssignedUserIdAsync(userId);
            return Ok(new { success = true, data = issues.Select(MapToDto) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分配给当前用户的问题失败");
            return StatusCode(500, new { success = false, message = "获取问题列表失败" });
        }
    }

    /// <summary>
    /// 获取当前用户报告的问题
    /// </summary>
    [HttpGet("reported-by-me")]
    public async Task<ActionResult<object>> GetReportedIssues()
    {
        try
        {
            var userId = GetCurrentUserId();
            var issues = await _issueRepository.GetByReportedUserIdAsync(userId);
            return Ok(new { success = true, data = issues.Select(MapToDto) });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户报告的问题失败");
            return StatusCode(500, new { success = false, message = "获取问题列表失败" });
        }
    }

    /// <summary>
    /// 将Issue实体映射为DTO
    /// </summary>
    private object MapToDto(Issue issue)
    {
        return new
        {
            id = issue.Id,
            projectId = issue.ProjectId,
            projectName = issue.Project?.Name,
            title = issue.Title,
            description = issue.Description,
            issueType = issue.IssueType,
            priority = issue.Priority,
            status = issue.Status,
            assignedTo = issue.AssignedTo,
            assignedToUser = issue.AssignedToUser != null ? new
            {
                id = issue.AssignedToUser.Id,
                username = issue.AssignedToUser.Username,
                realName = issue.AssignedToUser.RealName
            } : null,
            reportedBy = issue.ReportedBy,
            reportedByUser = issue.ReportedByUser != null ? new
            {
                id = issue.ReportedByUser.Id,
                username = issue.ReportedByUser.Username,
                realName = issue.ReportedByUser.RealName
            } : null,
            labels = issue.Labels?.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(l => l.Trim()).ToArray(),
            createdAt = issue.CreatedTime,
            updatedAt = issue.UpdatedTime,
            resolvedAt = issue.ResolvedAt
        };
    }
}

/// <summary>
/// 创建问题请求
/// </summary>
public class CreateIssueRequest
{
    public int ProjectId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IssueType { get; set; } = "Bug";
    public string? Priority { get; set; }
    public int? AssignedTo { get; set; }
    public string? Labels { get; set; }
}

/// <summary>
/// 更新问题请求
/// </summary>
public class UpdateIssueRequest
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string? IssueType { get; set; }
    public string? Priority { get; set; }
    public string? Status { get; set; }
    public int? AssignedTo { get; set; }
    public string? Labels { get; set; }
}

/// <summary>
/// 更新问题状态请求
/// </summary>
public class UpdateIssueStatusRequest
{
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 分配问题请求
/// </summary>
public class AssignIssueRequest
{
    public int? AssignedTo { get; set; }
}
