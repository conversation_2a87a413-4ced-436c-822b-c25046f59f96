using SqlSugar;
using ProjectManagement.Core.DTOs.VectorSearch;
using ProjectManagement.Core.Entities;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 文档向量仓储实现
    /// </summary>
    public class DocumentVectorRepository : IDocumentVectorRepository
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DocumentVectorRepository> _logger;

        public DocumentVectorRepository(ISqlSugarClient db, ILogger<DocumentVectorRepository> logger)
        {
            _db = db;
            _logger = logger;
        }

        public async Task<DocumentVector> CreateAsync(DocumentVector documentVector)
        {
            var vectorData = new DocumentVectorEntity
            {
                DocumentId = documentVector.DocumentId,
                ChunkIndex = documentVector.ChunkIndex,
                Content = documentVector.Content,
                Embedding = documentVector.Embedding,
                Metadata = documentVector.Metadata,
                CreatedTime = DateTime.Now
            };

            var id = await _db.Insertable(vectorData).ExecuteReturnIdentityAsync();
            documentVector.Id = id;

            _logger.LogInformation("创建文档向量成功: DocumentId={DocumentId}, ChunkIndex={ChunkIndex}",
                documentVector.DocumentId, documentVector.ChunkIndex);

            return documentVector;
        }

        public async Task<DocumentVector?> GetByIdAsync(int id)
        {
            var vectorData = await _db.Queryable<DocumentVectorEntity>()
                .Where(dv => dv.Id == id)
                .FirstAsync();

            return vectorData == null ? null : MapToModel(vectorData);
        }

        public async Task<List<DocumentVector>> GetByDocumentIdAsync(string documentId)
        {
            var vectors = await _db.Queryable<DocumentVectorEntity>()
                .Where(dv => dv.DocumentId == documentId)
                .OrderBy(dv => dv.ChunkIndex)
                .ToListAsync();

            return vectors.Select(MapToModel).ToList();
        }

        public async Task<List<DocumentVector>> FindSimilarAsync(
            float[] queryEmbedding,
            int topK,
            float similarityThreshold = 0.7f,
            Dictionary<string, object>? filters = null)
        {
            // 注意：这是一个简化的实现
            // 在实际生产环境中，应该使用专门的向量数据库（如Pinecone、Weaviate等）
            // 或者使用支持向量搜索的数据库扩展（如pgvector for PostgreSQL）

            var allVectors = await _db.Queryable<DocumentVectorEntity>().ToListAsync();
            var results = new List<DocumentVector>();

            foreach (var vectorEntity in allVectors)
            {
                var vector = MapToModel(vectorEntity);

                // 计算余弦相似度
                var similarity = CalculateCosineSimilarity(queryEmbedding, vector.Embedding);

                if (similarity >= similarityThreshold)
                {
                    vector.SimilarityScore = similarity;
                    results.Add(vector);
                }
            }

            // 应用过滤器
            if (filters != null)
            {
                results = ApplyFilters(results, filters);
            }

            return results
                .OrderByDescending(r => r.SimilarityScore)
                .Take(topK)
                .ToList();
        }

        public async Task<DocumentVector> UpdateAsync(DocumentVector documentVector)
        {
            var vectorData = new DocumentVectorEntity
            {
                Id = documentVector.Id,
                DocumentId = documentVector.DocumentId,
                ChunkIndex = documentVector.ChunkIndex,
                Content = documentVector.Content,
                Embedding = documentVector.Embedding,
                Metadata = documentVector.Metadata
            };

            await _db.Updateable(vectorData)
                .IgnoreColumns(dv => dv.CreatedTime)
                .ExecuteCommandAsync();

            return documentVector;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var result = await _db.Deleteable<DocumentVectorEntity>()
                .Where(dv => dv.Id == id)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除文档向量: Id={Id}, 影响行数={RowsAffected}", id, result);
            return result > 0;
        }

        public async Task<bool> DeleteByDocumentIdAsync(string documentId)
        {
            var result = await _db.Deleteable<DocumentVectorEntity>()
                .Where(dv => dv.DocumentId == documentId)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除文档向量: DocumentId={DocumentId}, 影响行数={RowsAffected}",
                documentId, result);
            return result > 0;
        }

        public async Task<List<DocumentVector>> SearchByMetadataAsync(Dictionary<string, object> filters)
        {
            var allVectors = await _db.Queryable<DocumentVectorEntity>().ToListAsync();
            var models = allVectors.Select(MapToModel).ToList();
            return ApplyFilters(models, filters);
        }

        /// <summary>
        /// 计算余弦相似度
        /// </summary>
        private float CalculateCosineSimilarity(float[] vector1, float[] vector2)
        {
            if (vector1.Length != vector2.Length)
                return 0f;

            float dotProduct = 0f;
            float magnitude1 = 0f;
            float magnitude2 = 0f;

            for (int i = 0; i < vector1.Length; i++)
            {
                dotProduct += vector1[i] * vector2[i];
                magnitude1 += vector1[i] * vector1[i];
                magnitude2 += vector2[i] * vector2[i];
            }

            magnitude1 = (float)Math.Sqrt(magnitude1);
            magnitude2 = (float)Math.Sqrt(magnitude2);

            if (magnitude1 == 0f || magnitude2 == 0f)
                return 0f;

            return dotProduct / (magnitude1 * magnitude2);
        }

        /// <summary>
        /// 应用过滤器
        /// </summary>
        private List<DocumentVector> ApplyFilters(List<DocumentVector> vectors, Dictionary<string, object> filters)
        {
            var filtered = vectors.AsEnumerable();

            foreach (var filter in filters)
            {
                filtered = filtered.Where(v => 
                {
                    if (v.Metadata.TryGetValue(filter.Key, out var value))
                    {
                        return value?.ToString() == filter.Value?.ToString();
                    }
                    return false;
                });
            }

            return filtered.ToList();
        }

        /// <summary>
        /// 将Entity映射为Model
        /// </summary>
        private DocumentVector MapToModel(DocumentVectorEntity entity)
        {
            return new DocumentVector
            {
                Id = entity.Id,
                DocumentId = entity.DocumentId,
                ChunkIndex = entity.ChunkIndex,
                Content = entity.Content,
                Embedding = entity.Embedding,
                SimilarityScore = 0f, // 默认值，在搜索时会被设置
                Metadata = entity.Metadata,
                CreatedAt = entity.CreatedTime
            };
        }
    }
}
