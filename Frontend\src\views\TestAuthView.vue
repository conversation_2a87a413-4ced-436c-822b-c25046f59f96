<template>
  <div class="test-auth">
    <el-card>
      <template #header>
        <h3>认证状态测试</h3>
      </template>
      
      <div class="auth-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="Token">
            {{ authStore.token ? '✓ 存在' : '✗ 不存在' }}
          </el-descriptions-item>
          <el-descriptions-item label="User">
            {{ authStore.user ? '✓ 存在' : '✗ 不存在' }}
          </el-descriptions-item>
          <el-descriptions-item label="IsAuthenticated">
            {{ authStore.isAuthenticated ? '✓ 是' : '✗ 否' }}
          </el-descriptions-item>
          <el-descriptions-item label="Current Route">
            {{ $route.path }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="authStore.user" class="user-details">
          <h4>用户信息</h4>
          <pre>{{ JSON.stringify(authStore.user, null, 2) }}</pre>
        </div>
        
        <div class="test-actions">
          <el-button @click="testDashboard" type="primary">跳转到Dashboard</el-button>
          <el-button @click="testLogin" type="default">跳转到登录页</el-button>
          <el-button @click="logout" type="danger">退出登录</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const testDashboard = async () => {
  try {
    await router.push('/dashboard')
    ElMessage.success('跳转成功')
  } catch (error) {
    console.error('Navigation failed:', error)
    ElMessage.error('跳转失败')
  }
}

const testLogin = async () => {
  try {
    await router.push('/login')
    ElMessage.success('跳转成功')
  } catch (error) {
    console.error('Navigation failed:', error)
    ElMessage.error('跳转失败')
  }
}

const logout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('退出成功')
    await router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    ElMessage.error('退出失败')
  }
}
</script>

<style scoped>
.test-auth {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.auth-info {
  margin-top: 20px;
}

.user-details {
  margin: 20px 0;
}

.user-details pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow: auto;
}

.test-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}
</style>
