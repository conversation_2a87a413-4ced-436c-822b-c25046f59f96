using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// UI自动化模板执行日志实体
    /// </summary>
    [SugarTable("UIAutoMationTemplateExecutionLogs", "UI自动化模板执行日志表")]
    public class UIAutoMationTemplateExecutionLog : BaseEntity
    {
        /// <summary>
        /// 序列ID（可选）
        /// </summary>
        [SugarColumn(ColumnDescription = "序列ID", IsNullable = true)]
        public int? SequenceId { get; set; }

        /// <summary>
        /// 模板ID（可选）
        /// </summary>
        [SugarColumn(ColumnDescription = "模板ID", IsNullable = true)]
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤ID（可选）
        /// </summary>
        [SugarColumn(ColumnDescription = "步骤ID", IsNullable = true)]
        public int? StepId { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        [SugarColumn(ColumnDescription = "执行类型", Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "执行类型不能为空")]
        public string ExecutionType { get; set; } = string.Empty;

        /// <summary>
        /// 执行状态
        /// </summary>
        [SugarColumn(ColumnDescription = "执行状态", Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "执行状态不能为空")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnDescription = "开始时间", IsNullable = false)]
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(ColumnDescription = "结束时间", IsNullable = true)]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行耗时（毫秒）
        /// </summary>
        [SugarColumn(ColumnDescription = "执行耗时", IsNullable = true)]
        public int? Duration { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        [SugarColumn(ColumnDescription = "执行结果", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnDescription = "错误信息", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 截图路径
        /// </summary>
        [SugarColumn(ColumnDescription = "截图路径", Length = 500, IsNullable = true)]
        public string? ScreenshotPath { get; set; }

        /// <summary>
        /// 执行者
        /// </summary>
        [SugarColumn(ColumnDescription = "执行者", Length = 100, IsNullable = true)]
        public string? ExecutedBy { get; set; }

        /// <summary>
        /// 关联序列（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public UIAutoMationTemplateSequence? Sequence { get; set; }

        /// <summary>
        /// 关联模板（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CustomUIAutoMationTemplate? Template { get; set; }

        /// <summary>
        /// 关联步骤（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public UIAutoMationTemplateStep? Step { get; set; }
    }

    /// <summary>
    /// 执行类型枚举
    /// </summary>
    public static class ExecutionTypes
    {
        public const string Template = "Template";
        public const string Sequence = "Sequence";
        public const string Step = "Step";

        /// <summary>
        /// 获取所有执行类型
        /// </summary>
        public static readonly string[] All = { Template, Sequence, Step };

        /// <summary>
        /// 验证执行类型是否有效
        /// </summary>
        public static bool IsValid(string executionType)
        {
            return All.Contains(executionType);
        }
    }

    /// <summary>
    /// 执行状态枚举
    /// </summary>
    public static class ExecutionStatuses
    {
        public const string Started = "Started";
        public const string Running = "Running";
        public const string Completed = "Completed";
        public const string Failed = "Failed";
        public const string Cancelled = "Cancelled";

        /// <summary>
        /// 获取所有执行状态
        /// </summary>
        public static readonly string[] All = { Started, Running, Completed, Failed, Cancelled };

        /// <summary>
        /// 验证执行状态是否有效
        /// </summary>
        public static bool IsValid(string status)
        {
            return All.Contains(status);
        }
    }
}
