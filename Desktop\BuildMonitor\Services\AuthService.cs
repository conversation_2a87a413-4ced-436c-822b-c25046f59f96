using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using BuildMonitor.Models;

namespace BuildMonitor.Services
{
    /// <summary>
    /// 认证服务 - 调用后端API进行用户认证
    /// </summary>
    public class AuthService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public AuthService(string? baseUrl = null)
        {
            _httpClient = new HttpClient();
            _baseUrl = (baseUrl ?? GetApiBaseUrlFromConfig()).TrimEnd('/');

            // 设置超时时间
            var timeout = GetTimeoutFromConfig();
            _httpClient.Timeout = TimeSpan.FromSeconds(timeout);
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="rememberMe">记住我</param>
        /// <returns>登录响应</returns>
        public async Task<LoginResponse?> LoginAsync(string username, string password, bool rememberMe = false)
        {
            try
            {
                var loginRequest = new LoginRequest
                {
                    Username = username,
                    Password = password,
                    RememberMe = rememberMe
                };

                var json = JsonConvert.SerializeObject(loginRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/login", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<LoginResponse>(responseContent);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(errorContent);
                    throw new AuthenticationException(errorResponse?.Message ?? "登录失败");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new AuthenticationException($"网络连接失败: {ex.Message}");
            }
            catch (TaskCanceledException ex)
            {
                throw new AuthenticationException($"请求超时: {ex.Message}");
            }
            catch (JsonException ex)
            {
                throw new AuthenticationException($"响应数据格式错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <param name="accessToken">访问令牌</param>
        /// <returns>用户信息</returns>
        public async Task<UserInfo?> GetCurrentUserAsync(string accessToken)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/me");
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<UserInfo>(responseContent);
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <param name="accessToken">访问令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> LogoutAsync(string accessToken)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/logout", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        /// <summary>
        /// 从配置文件获取API基础URL
        /// </summary>
        private static string GetApiBaseUrlFromConfig()
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    dynamic config = JsonConvert.DeserializeObject(json);

                    string baseUrl = config?.ApiSettings?.BaseUrl;
                    if (!string.IsNullOrEmpty(baseUrl))
                        return baseUrl;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to read API base URL from config: {ex.Message}");
            }

            // 默认值
            return "https://localhost:61136";
        }

        /// <summary>
        /// 从配置文件获取超时时间
        /// </summary>
        private static int GetTimeoutFromConfig()
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    dynamic config = JsonConvert.DeserializeObject(json);

                    int timeout = config?.ApiSettings?.Timeout ?? 30;
                    return timeout;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to read timeout from config: {ex.Message}");
            }

            // 默认值：30秒
            return 30;
        }
    }

    /// <summary>
    /// 登录请求
    /// </summary>
    public class LoginRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// 登录响应
    /// </summary>
    public class LoginResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public int ExpiresIn { get; set; }
        public string TokenType { get; set; } = "Bearer";
        public UserInfo User { get; set; } = new();
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInfo
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string RealName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int Status { get; set; }
        public string? Avatar { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// 错误响应
    /// </summary>
    public class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 认证异常
    /// </summary>
    public class AuthenticationException : Exception
    {
        public AuthenticationException(string message) : base(message) { }
        public AuthenticationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
