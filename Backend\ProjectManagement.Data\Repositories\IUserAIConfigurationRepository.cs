using ProjectManagement.Core.Entities;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 用户AI配置仓储接口
/// 功能: 管理用户个人AI配置的数据访问操作
/// 支持: CRUD操作、用户配置查询、使用统计更新
/// </summary>
public interface IUserAIConfigurationRepository
{
    /// <summary>
    /// 根据ID获取用户AI配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>用户AI配置</returns>
    Task<UserAIConfiguration?> GetByIdAsync(int id);

    /// <summary>
    /// 获取用户的所有AI配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户AI配置列表</returns>
    Task<List<UserAIConfiguration>> GetByUserIdAsync(int userId);

    /// <summary>
    /// 获取用户指定类型的AI配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="modelType">模型类型</param>
    /// <returns>用户AI配置列表</returns>
    Task<List<UserAIConfiguration>> GetByUserIdAndTypeAsync(int userId, string modelType);

    /// <summary>
    /// 获取用户指定提供商的AI配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="providerName">提供商名称</param>
    /// <returns>用户AI配置列表</returns>
    Task<List<UserAIConfiguration>> GetByUserIdAndProviderAsync(int userId, string providerName);

    /// <summary>
    /// 获取用户的默认AI配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="modelType">模型类型（可选）</param>
    /// <returns>默认AI配置</returns>
    Task<UserAIConfiguration?> GetDefaultConfigurationAsync(int userId, string? modelType = null);

    /// <summary>
    /// 获取用户活跃的AI配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>活跃的AI配置列表</returns>
    Task<List<UserAIConfiguration>> GetActiveConfigurationsAsync(int userId);

    /// <summary>
    /// 创建用户AI配置
    /// </summary>
    /// <param name="configuration">用户AI配置</param>
    /// <returns>创建的配置</returns>
    Task<UserAIConfiguration> CreateAsync(UserAIConfiguration configuration);

    /// <summary>
    /// 更新用户AI配置
    /// </summary>
    /// <param name="configuration">用户AI配置</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateAsync(UserAIConfiguration configuration);

    /// <summary>
    /// 删除用户AI配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(int id);

    /// <summary>
    /// 设置默认配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="configurationId">配置ID</param>
    /// <param name="modelType">模型类型</param>
    /// <returns>是否设置成功</returns>
    Task<bool> SetDefaultConfigurationAsync(int userId, int configurationId, string modelType);

    /// <summary>
    /// 更新使用统计
    /// </summary>
    /// <param name="configurationId">配置ID</param>
    /// <param name="tokensUsed">使用的令牌数</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="cost">成本</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateUsageStatisticsAsync(int configurationId, int tokensUsed, bool isSuccess, decimal cost = 0);

    /// <summary>
    /// 重置每日统计
    /// </summary>
    /// <param name="userId">用户ID（可选，为空则重置所有用户）</param>
    /// <returns>是否重置成功</returns>
    Task<bool> ResetDailyStatisticsAsync(int? userId = null);

    /// <summary>
    /// 重置每月统计
    /// </summary>
    /// <param name="userId">用户ID（可选，为空则重置所有用户）</param>
    /// <returns>是否重置成功</returns>
    Task<bool> ResetMonthlyStatisticsAsync(int? userId = null);

    /// <summary>
    /// 检查用户是否有指定类型的配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="modelType">模型类型</param>
    /// <returns>是否存在配置</returns>
    Task<bool> HasConfigurationAsync(int userId, string modelType);

    /// <summary>
    /// 获取用户的使用统计
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="startDate">开始日期（可选）</param>
    /// <param name="endDate">结束日期（可选）</param>
    /// <returns>使用统计信息</returns>
    Task<UserAIUsageStatistics> GetUsageStatisticsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取超出限制的配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>超出限制的配置列表</returns>
    Task<List<UserAIConfiguration>> GetOverLimitConfigurationsAsync(int userId);

    /// <summary>
    /// 批量更新配置状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="isActive">是否活跃</param>
    /// <param name="configurationIds">配置ID列表（可选，为空则更新用户所有配置）</param>
    /// <returns>是否更新成功</returns>
    Task<bool> BatchUpdateStatusAsync(int userId, bool isActive, List<int>? configurationIds = null);
}

/// <summary>
/// 用户AI使用统计
/// </summary>
public class UserAIUsageStatistics
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// 总请求次数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求次数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求次数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 总令牌使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 总成本
    /// </summary>
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 当月成本
    /// </summary>
    public decimal CurrentMonthCost { get; set; }

    /// <summary>
    /// 当日令牌使用量
    /// </summary>
    public int CurrentDayTokens { get; set; }

    /// <summary>
    /// 当月令牌使用量
    /// </summary>
    public long CurrentMonthTokens { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 按提供商分组的统计
    /// </summary>
    public Dictionary<string, ProviderUsageStatistics> ProviderStatistics { get; set; } = new();

    /// <summary>
    /// 按模型类型分组的统计
    /// </summary>
    public Dictionary<string, ModelTypeUsageStatistics> ModelTypeStatistics { get; set; } = new();
}

/// <summary>
/// 提供商使用统计
/// </summary>
public class ProviderUsageStatistics
{
    /// <summary>
    /// 提供商名称
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// 请求次数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// 令牌使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }
}

/// <summary>
/// 模型类型使用统计
/// </summary>
public class ModelTypeUsageStatistics
{
    /// <summary>
    /// 模型类型
    /// </summary>
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// 请求次数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// 令牌使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }
}
