{"api_timeout": 30, "api_retries": 3, "log_level": "INFO", "log_path": "./logs", "max_concurrent": 1, "cache_size": 100, "download_path": "D:\\GitProjects\\ProjectManagementAI\\Python\\NewVersion\\Test", "clear_old_files": true, "enable_custom_prompt": true, "custom_prompt": "在D:\\GitProjects\\ProjectManagementAI\\Python\\NewVersion\\Test目录开发，开发之前请仔细阅读D:\\GitProjects\\ProjectManagementAI\\Python\\NewVersion\\Test的项目需求文档,阅读完之后请开始开发，不要询问我是否要开始开发", "window_geometry": "1400x880+260+90", "current_tab": "🚀 开发步骤", "api_settings": {"base_url": "https://localhost:61136/", "timeout": 30, "is_authenticated": true}, "compilation_errors": {"max_errors_to_send": 5, "backend_error_limit": 5, "frontend_error_limit": 5, "include_warnings": false, "auto_send_to_copilot": false, "error_send_delay": 10, "description": "编译错误发送配置"}, "element_detection": {"monitor_interval": 2, "adaptive_interval": true, "auto_click_elements": [{"name": "保留按钮", "selector": "#workbench\\.parts\\.auxiliarybar .chat-editing-session-actions .monaco-button.default-colors.monaco-text-button[aria-label*='保留']", "enabled": true, "max_wait_time": 3, "description": "自动点击保留按钮"}, {"name": "继续按钮1", "selector": "#workbench\\.parts\\.auxiliarybar .chat-buttons-container .monaco-button.monaco-text-button", "enabled": true, "max_wait_time": 3, "description": "自动点击继续按钮"}, {"name": "继续按钮2", "selector": "#workbench\\.parts\\.auxiliarybar .chat-confirmation-buttons-container .monaco-button.monaco-text-button:not(.secondary)", "enabled": true, "max_wait_time": 3, "description": "自动点击继续按钮（不含secondary的最后一个）"}]}, "download_apis": [{"name": "需求文档", "endpoint": "/api/projects/{project_id}/download-requirements", "filename": "需求文档_{project_id}.md", "description": "项目需求文档"}, {"name": "ER图", "endpoint": "/api/projects/{project_id}/download-er-diagrams", "filename": "ER图_{project_id}.mermaid", "description": "数据库ER图"}, {"name": "上下文图", "endpoint": "/api/projects/{project_id}/download-context-diagrams", "filename": "上下文图_{project_id}.mermaid", "description": "系统上下文图"}, {"name": "原型图", "endpoint": "/api/projects/{project_id}/download-prototypes", "filename": "原型图_{project_id}.mermaid", "description": "界面原型图"}], "api_url": "https://localhost:61136"}