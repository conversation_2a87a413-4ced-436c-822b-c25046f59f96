namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt分析服务接口
/// </summary>
public interface IPromptAnalyticsService
{
    /// <summary>
    /// 获取模板使用统计
    /// </summary>
    Task<TemplateUsageAnalytics> GetTemplateUsageAnalyticsAsync(int templateId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取用户使用统计
    /// </summary>
    Task<UserUsageAnalytics> GetUserUsageAnalyticsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取整体使用统计
    /// </summary>
    Task<OverallUsageAnalytics> GetOverallUsageAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取热门模板排行
    /// </summary>
    Task<List<TemplateRanking>> GetPopularTemplatesRankingAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取AI提供商使用统计
    /// </summary>
    Task<List<ProviderUsageStats>> GetProviderUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取任务类型使用统计
    /// </summary>
    Task<List<TaskTypeUsageStats>> GetTaskTypeUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取模板效果分析
    /// </summary>
    Task<TemplateEffectivenessAnalysis> GetTemplateEffectivenessAsync(int templateId);

    /// <summary>
    /// 比较模板效果
    /// </summary>
    Task<TemplateComparisonResult> CompareTemplatesAsync(int templateId1, int templateId2);

    /// <summary>
    /// 获取优化建议
    /// </summary>
    Task<List<OptimizationSuggestion>> GetOptimizationSuggestionsAsync(int templateId);

    /// <summary>
    /// 执行A/B测试分析
    /// </summary>
    Task<ABTestResult> AnalyzeABTestAsync(int templateAId, int templateBId, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 获取成本分析
    /// </summary>
    Task<CostAnalysis> GetCostAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 获取性能分析
    /// </summary>
    Task<PerformanceAnalysis> GetPerformanceAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null);

    /// <summary>
    /// 生成分析报告
    /// </summary>
    Task<AnalyticsReport> GenerateReportAsync(ReportType reportType, ReportParameters parameters);
}

/// <summary>
/// 模板使用分析结果
/// </summary>
public class TemplateUsageAnalytics
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public int TotalUsage { get; set; }
    public int UniqueUsers { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public decimal TotalCost { get; set; }
    public double AverageRating { get; set; }
    public List<DailyUsage> DailyUsage { get; set; } = new();
    public List<HourlyUsage> HourlyUsage { get; set; } = new();
    public Dictionary<string, int> ProviderDistribution { get; set; } = new();
    public Dictionary<string, int> ErrorTypes { get; set; } = new();
}

/// <summary>
/// 用户使用分析结果
/// </summary>
public class UserUsageAnalytics
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int TotalUsage { get; set; }
    public int UniqueTemplates { get; set; }
    public string MostUsedTaskType { get; set; } = string.Empty;
    public string PreferredProvider { get; set; } = string.Empty;
    public double AverageSessionDuration { get; set; }
    public decimal TotalCost { get; set; }
    public List<TemplateUsageSummary> TopTemplates { get; set; } = new();
    public List<DailyUsage> DailyActivity { get; set; } = new();
}

/// <summary>
/// 整体使用分析结果
/// </summary>
public class OverallUsageAnalytics
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalTemplates { get; set; }
    public int TotalUsage { get; set; }
    public double OverallSuccessRate { get; set; }
    public decimal TotalCost { get; set; }
    public double AverageResponseTime { get; set; }
    public List<DailyUsage> DailyTrends { get; set; } = new();
    public Dictionary<string, int> TaskTypeDistribution { get; set; } = new();
    public Dictionary<string, int> ProviderDistribution { get; set; } = new();
    public List<TopUser> TopUsers { get; set; } = new();
}

/// <summary>
/// 模板排行
/// </summary>
public class TemplateRanking
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public string TaskType { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public double SuccessRate { get; set; }
    public double AverageRating { get; set; }
    public int Rank { get; set; }
    public double TrendScore { get; set; }
}

/// <summary>
/// 提供商使用统计
/// </summary>
public class ProviderUsageStats
{
    public string Provider { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public decimal TotalCost { get; set; }
    public decimal AverageCost { get; set; }
    public List<string> PopularModels { get; set; } = new();
}

/// <summary>
/// 任务类型使用统计
/// </summary>
public class TaskTypeUsageStats
{
    public string TaskType { get; set; } = string.Empty;
    public string TaskTypeName { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public int TemplateCount { get; set; }
    public double SuccessRate { get; set; }
    public double AverageRating { get; set; }
    public List<TemplateUsageSummary> TopTemplates { get; set; } = new();
}

/// <summary>
/// 模板效果分析
/// </summary>
public class TemplateEffectivenessAnalysis
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public double EffectivenessScore { get; set; }
    public double QualityScore { get; set; }
    public double UsabilityScore { get; set; }
    public double PerformanceScore { get; set; }
    public double CostEfficiencyScore { get; set; }
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
    public Dictionary<string, double> MetricTrends { get; set; } = new();
}

/// <summary>
/// 模板比较结果
/// </summary>
public class TemplateComparisonResult
{
    public TemplateComparisonItem TemplateA { get; set; } = new();
    public TemplateComparisonItem TemplateB { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
    public List<MetricComparison> MetricComparisons { get; set; } = new();
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// 模板比较项
/// </summary>
public class TemplateComparisonItem
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public TemplateComparisonMetrics Metrics { get; set; } = new();
}

/// <summary>
/// 比较摘要
/// </summary>
public class ComparisonSummary
{
    public string Winner { get; set; } = string.Empty;
    public double ConfidenceLevel { get; set; }
    public List<string> KeyDifferences { get; set; } = new();
}

/// <summary>
/// 指标比较
/// </summary>
public class MetricComparison
{
    public string MetricName { get; set; } = string.Empty;
    public double ValueA { get; set; }
    public double ValueB { get; set; }
    public double Difference { get; set; }
    public double DifferencePercentage { get; set; }
    public double PercentageChange { get; set; }
    public string Winner { get; set; } = string.Empty;
    public bool IsSignificant { get; set; }
}

/// <summary>
/// 优化建议
/// </summary>
public class OptimizationSuggestion
{
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public double ImpactScore { get; set; }
    public double ImplementationDifficulty { get; set; }
    public List<string> ActionItems { get; set; } = new();
    public string ExpectedOutcome { get; set; } = string.Empty;
}

/// <summary>
/// A/B测试结果
/// </summary>
public class ABTestResult
{
    public string TestName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public TemplateTestResult TemplateA { get; set; } = new();
    public TemplateTestResult TemplateB { get; set; } = new();
    public TestStatistics Statistics { get; set; } = new();
    public string Conclusion { get; set; } = string.Empty;
    public bool IsStatisticallySignificant { get; set; }
    public double ConfidenceLevel { get; set; }
}

/// <summary>
/// 模板测试结果
/// </summary>
public class TemplateTestResult
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public int SampleSize { get; set; }
    public double SuccessRate { get; set; }
    public double AverageRating { get; set; }
    public double AverageResponseTime { get; set; }
    public decimal AverageCost { get; set; }
}

/// <summary>
/// 测试统计
/// </summary>
public class TestStatistics
{
    public double PValue { get; set; }
    public double EffectSize { get; set; }
    public double PowerAnalysis { get; set; }
    public string StatisticalTest { get; set; } = string.Empty;
}

/// <summary>
/// 成本分析
/// </summary>
public class CostAnalysis
{
    public decimal TotalCost { get; set; }
    public decimal AverageCostPerRequest { get; set; }
    public Dictionary<string, decimal> CostByProvider { get; set; } = new();
    public Dictionary<string, decimal> CostByTaskType { get; set; } = new();
    public List<DailyCost> DailyCosts { get; set; } = new();
    public List<CostOptimizationSuggestion> OptimizationSuggestions { get; set; } = new();
}

/// <summary>
/// 性能分析
/// </summary>
public class PerformanceAnalysis
{
    public double AverageResponseTime { get; set; }
    public double MedianResponseTime { get; set; }
    public double P95ResponseTime { get; set; }
    public double P99ResponseTime { get; set; }
    public Dictionary<string, double> ResponseTimeByProvider { get; set; } = new();
    public Dictionary<string, double> ResponseTimeByTaskType { get; set; } = new();
    public List<PerformanceBottleneck> Bottlenecks { get; set; } = new();
}

/// <summary>
/// 分析报告
/// </summary>
public class AnalyticsReport
{
    public string ReportId { get; set; } = string.Empty;
    public ReportType Type { get; set; }
    public DateTime GeneratedAt { get; set; }
    public ReportParameters Parameters { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public List<string> KeyInsights { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 报告类型
/// </summary>
public enum ReportType
{
    Usage,
    Performance,
    Cost,
    Quality,
    Comparison,
    Optimization
}

/// <summary>
/// 报告参数
/// </summary>
public class ReportParameters
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public List<int> TemplateIds { get; set; } = new();
    public List<int> UserIds { get; set; } = new();
    public List<string> TaskTypes { get; set; } = new();
    public List<string> Providers { get; set; } = new();
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

// 辅助类
public class DailyUsage
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
    public double SuccessRate { get; set; }
}

public class HourlyUsage
{
    public int Hour { get; set; }
    public int Count { get; set; }
}

public class TemplateUsageSummary
{
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public int UsageCount { get; set; }
    public double SuccessRate { get; set; }
}

public class TopUser
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int UsageCount { get; set; }
}

public class DailyCost
{
    public DateTime Date { get; set; }
    public decimal Cost { get; set; }
}

public class CostOptimizationSuggestion
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PotentialSavings { get; set; }
}

public class PerformanceBottleneck
{
    public string Component { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double ImpactScore { get; set; }
}

/// <summary>
/// 模板比较指标
/// </summary>
public class TemplateComparisonMetrics
{
    public int UsageCount { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageRating { get; set; }
    public decimal AverageCost { get; set; }
    public int UniqueUsers { get; set; }
}
