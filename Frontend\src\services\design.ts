import { ApiService } from './api'
import type { ERDiagram, ContextDiagram } from '@/types'

/**
 * 设计生成服务
 */
export class DesignService {
  /**
   * 获取项目的ER图列表
   */
  static async getERDiagrams(projectId: number): Promise<ERDiagram[]> {
    return ApiService.get(`/api/projects/${projectId}/er-diagrams`)
  }

  /**
   * 获取ER图详情
   */
  static async getERDiagram(id: number): Promise<ERDiagram> {
    return ApiService.get(`/api/er-diagrams/${id}`)
  }

  /**
   * 创建ER图
   */
  static async createERDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    diagramName: string
    mermaidDefinition: string
    description?: string
  }): Promise<ERDiagram> {
    return ApiService.post('/api/er-diagrams', data)
  }

  /**
   * 更新ER图
   */
  static async updateERDiagram(id: number, data: Partial<ERDiagram>): Promise<ERDiagram> {
    return ApiService.put(`/api/er-diagrams/${id}`, data)
  }

  /**
   * 删除ER图
   */
  static async deleteERDiagram(id: number): Promise<void> {
    return ApiService.delete(`/api/er-diagrams/${id}`)
  }

  /**
   * 获取项目的Context图列表
   */
  static async getContextDiagrams(projectId: number): Promise<ContextDiagram[]> {
    return ApiService.get(`/api/projects/${projectId}/context-diagrams`)
  }

  /**
   * 获取Context图详情
   */
  static async getContextDiagram(id: number): Promise<ContextDiagram> {
    return ApiService.get(`/api/context-diagrams/${id}`)
  }

  /**
   * 创建Context图
   */
  static async createContextDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    diagramName: string
    mermaidDefinition: string
    externalEntities?: string
    systemBoundary?: string
    dataFlows?: string
  }): Promise<ContextDiagram> {
    return ApiService.post('/api/context-diagrams', data)
  }

  /**
   * 更新Context图
   */
  static async updateContextDiagram(id: number, data: Partial<ContextDiagram>): Promise<ContextDiagram> {
    return ApiService.put(`/api/context-diagrams/${id}`, data)
  }

  /**
   * 删除Context图
   */
  static async deleteContextDiagram(id: number): Promise<void> {
    return ApiService.delete(`/api/context-diagrams/${id}`)
  }

  /**
   * 生成ER图
   */
  static async generateERDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    databaseType?: string
    diagramFormat?: string
    aiProviderConfigId?: number
  }): Promise<{
    taskId: string
    taskType: string
    status: string
    message: string
  }> {
    return ApiService.post('/api/ai/generate-er-diagram', data)
  }

  /**
   * 生成Context图
   */
  static async generateContextDiagram(data: {
    projectId: number
    requirementDocumentId?: number
    diagramFormat?: string
    includeExternalSystems?: boolean
    aiProviderConfigId?: number
  }): Promise<{
    taskId: string
    taskType: string
    status: string
    message: string
  }> {
    return ApiService.post('/api/ai/generate-context-diagram', data)
  }

  /**
   * 获取AI任务状态
   */
  static async getTaskStatus(taskId: string): Promise<{
    taskId: string
    taskType: string
    status: string
    progress: number
    result?: any
    error?: string
  }> {
    return ApiService.get(`/api/ai/tasks/${taskId}/status`)
  }

  /**
   * 导出图表
   */
  static async exportDiagram(type: 'er' | 'context', id: number, format: 'svg' | 'png' | 'pdf'): Promise<Blob> {
    const response = await fetch(`/api/${type}-diagrams/${id}/export?format=${format}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      throw new Error('导出失败')
    }
    
    return response.blob()
  }
}
