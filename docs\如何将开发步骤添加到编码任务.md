# 如何将开发步骤添加到编码任务中

## 概述

编码任务和开发步骤是项目管理系统中的两个重要概念：
- **开发步骤**：具体的开发任务，如"创建用户实体类"、"实现登录接口"等
- **编码任务**：由多个开发步骤组成的更大的工作单元，如"用户管理模块开发"

通过将开发步骤关联到编码任务，可以更好地组织和跟踪项目进度。

## 功能特性

### 1. 多对多关系
- 一个编码任务可以包含多个开发步骤
- 一个开发步骤可以被多个编码任务引用
- 通过`CodingTaskSteps`关联表管理关系

### 2. 步骤管理
- 添加步骤到编码任务
- 从编码任务中移除步骤
- 查看任务关联的所有步骤
- 步骤执行状态跟踪

### 3. 进度统计
- 自动计算任务完成进度
- 统计已完成/进行中/未开始的步骤数量
- 可视化进度展示

## 使用方法

### 方法一：通过编码任务列表页面

1. **打开编码任务页面**
   - 导航到 `开发管理 > 编码任务`
   - 选择项目查看任务列表

2. **添加步骤到任务**
   - 在任务列表中找到目标任务
   - 点击任务操作菜单中的"添加步骤"按钮
   - 在弹出的对话框中选择要添加的开发步骤
   - 点击"确定"完成添加

3. **查看任务步骤**
   - 点击任务名称查看详情
   - 在详情页面的"关联开发步骤"部分查看所有步骤
   - 可以看到每个步骤的状态、优先级等信息

### 方法二：通过任务详情页面

1. **打开任务详情**
   - 在任务列表中点击任务名称
   - 或点击"查看"按钮

2. **管理关联步骤**
   - 在"关联开发步骤"部分点击"添加步骤"按钮
   - 选择要添加的步骤并确认
   - 对于已关联的步骤，可以点击"移除"按钮取消关联

3. **查看进度统计**
   - 在详情页面查看"进度统计"部分
   - 显示总步骤数、已完成数、进行中数和完成率

### 方法三：通过开发步骤页面

1. **打开开发步骤页面**
   - 导航到 `开发管理 > 开发步骤`
   - 选择项目查看步骤列表

2. **从步骤页面跳转**
   - 在编码任务详情中点击"在步骤中查看"
   - 会跳转到开发步骤页面并自动筛选该任务的步骤

## API接口

### 1. 添加步骤到编码任务
```http
POST /api/CodingTask/{taskId}/steps
Content-Type: application/json

{
  "stepIds": [1, 2, 3, 4]
}
```

### 2. 从编码任务移除步骤
```http
DELETE /api/CodingTask/{taskId}/steps
Content-Type: application/json

{
  "stepIds": [1, 2]
}
```

### 3. 获取任务关联的步骤
```http
GET /api/CodingTask/{taskId}/steps
```

### 4. 获取任务统计信息
```http
GET /api/CodingTask/project/{projectId}/statistics
```

## 数据库结构

### CodingTaskSteps 关联表
```sql
CREATE TABLE CodingTaskSteps (
    Id int IDENTITY(1,1) NOT NULL,
    CodingTaskId int NOT NULL,           -- 编码任务ID
    DevelopmentStepId int NOT NULL,      -- 开发步骤ID
    OrderIndex int NOT NULL DEFAULT 0,   -- 步骤顺序
    Status nvarchar(50) NOT NULL,        -- 步骤状态
    StartTime datetime2 NULL,            -- 开始时间
    CompletedTime datetime2 NULL,        -- 完成时间
    ActualHours decimal(10,2) NULL,      -- 实际工时
    ExecutionResult nvarchar(2000) NULL, -- 执行结果
    Notes nvarchar(1000) NULL,           -- 备注
    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL,
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL
);
```

## 业务规则

### 1. 关联规则
- 同一个编码任务中不能重复添加相同的开发步骤
- 删除编码任务时，相关的步骤关联会被软删除
- 删除开发步骤时，相关的任务关联会被软删除

### 2. 状态管理
- 步骤状态独立于任务状态
- 任务进度根据关联步骤的完成情况自动计算
- 支持步骤级别的时间跟踪和工时记录

### 3. 权限控制
- 只有任务负责人和项目管理员可以添加/移除步骤
- 所有项目成员都可以查看任务关联的步骤
- 步骤执行状态更新需要相应权限

## 最佳实践

### 1. 任务规划
- 在创建编码任务时，先规划好需要的开发步骤
- 将相关的步骤组织到同一个编码任务中
- 合理设置步骤的优先级和预估工时

### 2. 进度跟踪
- 定期更新步骤的执行状态
- 记录实际工时和执行结果
- 利用进度统计功能监控任务完成情况

### 3. 团队协作
- 明确每个步骤的负责人
- 在步骤备注中记录重要信息
- 利用任务详情页面进行团队沟通

## 注意事项

1. **数据一致性**：删除操作使用软删除，确保数据完整性
2. **性能考虑**：大量步骤关联时注意分页查询
3. **权限验证**：所有操作都会验证用户权限
4. **错误处理**：API调用失败时会显示友好的错误提示

## 故障排除

### 常见问题

1. **无法添加步骤**
   - 检查是否有足够的权限
   - 确认步骤是否已经在任务中
   - 验证网络连接和API服务状态

2. **步骤列表为空**
   - 确认项目中是否有开发步骤
   - 检查筛选条件是否过于严格
   - 验证数据库连接和数据完整性

3. **进度统计不准确**
   - 刷新页面重新加载数据
   - 检查步骤状态是否正确更新
   - 联系管理员检查数据一致性

通过以上功能和方法，您可以有效地管理编码任务和开发步骤的关联关系，提高项目开发的组织性和可追踪性。
