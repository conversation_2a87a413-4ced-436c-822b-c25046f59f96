using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 需求对话记录实体 - 对应DatabaseSchema.sql中的RequirementConversations表
/// 功能: 记录用户与AI系统的需求收集对话过程
/// 支持: 对话历史追踪、需求澄清过程、AI交互记录
/// </summary>
[SugarTable("RequirementConversations", "AI需求对话管理表")]
public class RequirementConversation
{
    /// <summary>
    /// 对话记录唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "对话记录唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID，关联Projects表（可为空，用于支持无项目的通用对话）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "所属项目ID，关联Projects表（可为空）")]
    public int? ProjectId { get; set; }

    /// <summary>
    /// 用户ID，关联Users表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "用户ID，关联Users表")]
    public int UserId { get; set; }

    /// <summary>
    /// 对话会话ID，用于标识同一次对话会话
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "对话会话ID，用于标识同一次对话会话")]
    public string? ConversationId { get; set; }

    /// <summary>
    /// 用户输入的消息内容（需求描述、问题等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "用户输入的消息内容")]
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// AI系统的回复内容（分析结果、澄清问题等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI系统的回复内容")]
    public string? AIResponse { get; set; }

    /// <summary>
    /// 消息类型: Requirement(需求收集), Clarification(澄清问题), Confirmation(确认需求)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "消息类型")]
    public string MessageType { get; set; } = "Requirement";

    /// <summary>
    /// 对话发生的时间戳
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "对话发生的时间戳")]
    public DateTime? Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的用户 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }
}
