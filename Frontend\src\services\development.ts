import { ApiService, AIApiService } from './api'
import type {
  DevelopmentStep,
  StepDependency,
  StepExecutionHistory,
  DecomposeRequirementRequest,
  DecomposeContentRequest,
  DecompositionResult,
  PagedStepsResult,
  ProjectStepStatistics,
  StepExecutionProgress,
  StepComplexityAnalysis,
  StepUpdateData,
  StepOrderInfo,
  DependencyValidationResult,
  StepFilterOptions,
  BatchOperationOptions
} from '@/types/development'

/**
 * 需求分解和开发步骤管理服务
 */
export class DevelopmentService {

  // ==================== 需求分解 ====================

  /**
   * 分解需求文档为开发步骤
   */
  static async decomposeRequirement(
    requirementDocumentId: number,
    options: DecomposeRequirementRequest
  ): Promise<DecompositionResult> {
    try {
      const response = await AIApiService.post<DecompositionResult>(
        `/api/RequirementDecomposition/decompose/${requirementDocumentId}`,
        options
      )
      return response
    } catch (error) {
      console.error('分解需求文档失败:', error)
      throw error
    }
  }

  /**
   * 基于项目信息分解
   */
  static async decomposeProject(
    projectId: number,
    options: DecomposeRequirementRequest
  ): Promise<DecompositionResult> {
    try {
      const response = await AIApiService.post<DecompositionResult>(
        `/api/RequirementDecomposition/decompose-project/${projectId}`,
        options
      )
      return response
    } catch (error) {
      console.error('分解项目失败:', error)
      throw error
    }
  }

  /**
   * 基于需求内容直接分解
   */
  static async decomposeRequirementContent(
    projectId: number,
    options: DecomposeContentRequest
  ): Promise<DecompositionResult> {
    try {
      const response = await AIApiService.post<DecompositionResult>(
        `/api/RequirementDecomposition/decompose-content/${projectId}`,
        options
      )
      return response
    } catch (error) {
      console.error('分解需求内容失败:', error)
      throw error
    }
  }

  /**
   * 确认并保存分解的步骤到数据库
   */
  static async confirmSteps(request: {
    projectId: number
    requirementDocumentId?: number
    steps: Array<{
      stepName: string
      stepDescription: string
      stepType: string
      priority: string
      estimatedHours?: number
      technologyStack?: string
      fileType?: string
      filePath?: string
      componentType?: string
      aiPrompt?: string
      stepOrder: number
      stepGroup?: string
      stepLevel: number
      parentStepName?: string
    }>
    dependencies: Array<{
      stepName: string
      dependsOnStepName: string
      dependencyType: string
    }>
  }): Promise<DecompositionResult> {
    try {
      const response = await AIApiService.post<DecompositionResult>(
        '/api/RequirementDecomposition/confirm-steps',
        request
      )
      return response
    } catch (error) {
      console.error('确认保存步骤失败:', error)
      throw error
    }
  }

  // ==================== 开发步骤查询 ====================

  /**
   * 获取项目的开发步骤（分页）
   */
  static async getProjectSteps(
    projectId: number,
    pageIndex: number = 1,
    pageSize: number = 20,
    filters?: StepFilterOptions
  ): Promise<PagedStepsResult> {
    try {
      const params = new URLSearchParams({
        pageIndex: pageIndex.toString(),
        pageSize: pageSize.toString()
      })

      if (filters?.status) params.append('status', filters.status)
      if (filters?.priority) params.append('priority', filters.priority)
      if (filters?.stepType) params.append('stepType', filters.stepType)
      if (filters?.keyword) params.append('search', filters.keyword)
      if (filters?.parentOnly) params.append('parentOnly', filters.parentOnly.toString())

      console.log('getProjectSteps filters:', filters)
      console.log('getProjectSteps params:', params.toString())

      const response = await ApiService.get<{success: boolean, data: PagedStepsResult}>(
        `/api/RequirementDecomposition/steps/${projectId}?${params.toString()}`
      )

      // 后端返回的数据结构是 {success: true, data: {...}}
      if (response.success && response.data) {
        return response.data
      } else {
        return {
          items: [],
          totalCount: 0,
          pageIndex: pageIndex,
          pageSize: pageSize,
          totalPages: 0
        }
      }
    } catch (error) {
      console.error('获取项目开发步骤失败:', error)
      throw error
    }
  }

  /**
   * 获取项目步骤树结构
   */
  static async getProjectStepTree(projectId: number): Promise<DevelopmentStep[]> {
    try {
      console.log('Calling API: /api/RequirementDecomposition/steps/' + projectId + '/tree')
      const response = await ApiService.get<{success: boolean, data: DevelopmentStep[]}>(
        `/api/RequirementDecomposition/steps/${projectId}/tree`
      )
      console.log('API response:', response)

      // 后端返回的数据结构是 {success: true, data: [...]}
      if (response.success && response.data) {
        console.log('Returning steps:', response.data.length)
        return response.data
      } else {
        console.warn('API response success is false or no data:', response)
        return []
      }
    } catch (error) {
      console.error('获取项目步骤树失败:', error)
      throw error
    }
  }

  /**
   * 获取可执行的步骤
   */
  static async getExecutableSteps(
    projectId: number,
    stepType?: string,
    priority?: string
  ): Promise<DevelopmentStep[]> {
    try {
      const params = new URLSearchParams()
      if (stepType) params.append('stepType', stepType)
      if (priority) params.append('priority', priority)

      const response = await ApiService.get<{success: boolean, data: DevelopmentStep[]}>(
        `/api/RequirementDecomposition/steps/${projectId}/executable?${params.toString()}`
      )
      return response.success && response.data ? response.data : []
    } catch (error) {
      console.error('获取可执行步骤失败:', error)
      throw error
    }
  }

  /**
   * 获取单个开发步骤详情
   */
  static async getStepById(stepId: number): Promise<DevelopmentStep> {
    try {
      const response = await ApiService.get<{success: boolean, data: DevelopmentStep}>(`/api/DevelopmentSteps/${stepId}`)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error('获取步骤详情失败')
    } catch (error) {
      console.error('获取开发步骤详情失败:', error)
      throw error
    }
  }

  /**
   * 搜索开发步骤
   */
  static async searchSteps(
    projectId: number,
    keyword: string,
    pageIndex: number = 1,
    pageSize: number = 20
  ): Promise<PagedStepsResult> {
    try {
      const params = new URLSearchParams({
        keyword,
        pageIndex: pageIndex.toString(),
        pageSize: pageSize.toString()
      })

      const response = await ApiService.get<{success: boolean, data: PagedStepsResult}>(
        `/api/DevelopmentSteps/search/${projectId}?${params.toString()}`
      )
      return response.success && response.data ? response.data : {
        items: [],
        totalCount: 0,
        pageIndex: pageIndex,
        pageSize: pageSize,
        totalPages: 0
      }
    } catch (error) {
      console.error('搜索开发步骤失败:', error)
      throw error
    }
  }

  // ==================== 开发步骤管理 ====================

  /**
   * 创建自定义开发步骤
   */
  static async createStep(step: Partial<DevelopmentStep>): Promise<DevelopmentStep> {
    try {
      const response = await ApiService.post('/api/DevelopmentSteps', step)
      return response as DevelopmentStep
    } catch (error) {
      console.error('创建开发步骤失败:', error)
      throw error
    }
  }

  /**
   * 更新开发步骤
   */
  static async updateStep(stepId: number, updateData: StepUpdateData): Promise<DevelopmentStep> {
    try {
      const response = await ApiService.put(`/api/DevelopmentSteps/${stepId}`, updateData)
      return response as DevelopmentStep
    } catch (error) {
      console.error('更新开发步骤失败:', error)
      throw error
    }
  }

  /**
   * 删除开发步骤
   */
  static async deleteStep(stepId: number, deleteChildSteps: boolean = false): Promise<boolean> {
    try {
      const response = await ApiService.delete(
        `/api/DevelopmentSteps/${stepId}?deleteChildSteps=${deleteChildSteps}`
      )
      return (response as any).success
    } catch (error) {
      console.error('删除开发步骤失败:', error)
      throw error
    }
  }

  /**
   * 移动步骤到新的父步骤下
   */
  static async moveStep(stepId: number, newParentStepId?: number): Promise<boolean> {
    try {
      const response = await ApiService.put(`/api/DevelopmentSteps/${stepId}/move`, {
        newParentStepId
      })
      return (response as any).success
    } catch (error) {
      console.error('移动开发步骤失败:', error)
      throw error
    }
  }

  /**
   * 重新排序步骤
   */
  static async reorderSteps(stepOrders: StepOrderInfo[]): Promise<boolean> {
    try {
      const response = await ApiService.put('/api/DevelopmentSteps/reorder', {
        stepOrders
      })
      return (response as any).success
    } catch (error) {
      console.error('重新排序步骤失败:', error)
      throw error
    }
  }

  /**
   * 批量操作步骤
   */
  static async batchOperateSteps(options: BatchOperationOptions): Promise<boolean> {
    try {
      const response = await ApiService.post('/api/DevelopmentSteps/batch', options)
      return (response as any).success
    } catch (error) {
      console.error('批量操作步骤失败:', error)
      throw error
    }
  }

  // ==================== 依赖关系管理 ====================

  /**
   * 获取步骤的依赖关系
   */
  static async getStepDependencies(stepId: number): Promise<StepDependency[]> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/${stepId}/dependencies`)
      return (response as StepDependency[]) || []
    } catch (error) {
      console.error('获取步骤依赖关系失败:', error)
      throw error
    }
  }

  /**
   * 获取依赖当前步骤的其他步骤
   */
  static async getStepDependents(stepId: number): Promise<StepDependency[]> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/${stepId}/dependents`)
      return (response as StepDependency[]) || []
    } catch (error) {
      console.error('获取步骤被依赖关系失败:', error)
      throw error
    }
  }

  /**
   * 添加步骤依赖关系
   */
  static async addStepDependency(dependency: {
    stepId: number
    dependsOnStepId: number
    dependencyType: string
    isRequired: boolean
    description?: string
  }): Promise<StepDependency> {
    try {
      const response = await ApiService.post('/api/StepDependencies', dependency)
      return (response as any).data
    } catch (error) {
      console.error('添加步骤依赖关系失败:', error)
      throw error
    }
  }

  /**
   * 移除步骤依赖关系
   */
  static async removeStepDependency(dependencyId: number): Promise<boolean> {
    try {
      const response = await ApiService.delete(`/api/StepDependencies/${dependencyId}`)
      return (response as any).success
    } catch (error) {
      console.error('移除步骤依赖关系失败:', error)
      throw error
    }
  }

  /**
   * 自动分析并建立步骤依赖关系
   */
  static async autoAnalyzeDependencies(projectId: number): Promise<number> {
    try {
      const response = await AIApiService.post(`/api/DevelopmentSteps/auto-dependencies/${projectId}`)
      return (response as number) || 0
    } catch (error) {
      console.error('自动分析依赖关系失败:', error)
      throw error
    }
  }

  /**
   * 验证依赖关系
   */
  static async validateDependencies(projectId: number): Promise<DependencyValidationResult> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/validate-dependencies/${projectId}`)
      return (response as DependencyValidationResult) || { isValid: false, errors: [], warnings: [], circularDependencies: [] }
    } catch (error) {
      console.error('验证依赖关系失败:', error)
      throw error
    }
  }

  // ==================== 执行管理 ====================

  /**
   * 开始执行步骤
   */
  static async startStepExecution(stepId: number, executorType: string = 'Manual'): Promise<StepExecutionHistory> {
    try {
      const response = await ApiService.post(`/api/DevelopmentSteps/${stepId}/execute`, {
        executorType
      })
      return response as StepExecutionHistory
    } catch (error) {
      console.error('开始执行步骤失败:', error)
      throw error
    }
  }

  /**
   * 完成步骤执行
   */
  static async completeStepExecution(
    executionId: string,
    result: string,
    generatedCode?: string,
    outputFiles?: string
  ): Promise<boolean> {
    try {
      const response = await ApiService.put(`/api/StepExecutions/${executionId}/complete`, {
        result,
        generatedCode,
        outputFiles
      })
      return (response as any).success
    } catch (error) {
      console.error('完成步骤执行失败:', error)
      throw error
    }
  }

  /**
   * 标记步骤执行失败
   */
  static async failStepExecution(executionId: string, errorMessage: string): Promise<boolean> {
    try {
      const response = await ApiService.put(`/api/StepExecutions/${executionId}/fail`, {
        errorMessage
      })
      return (response as any).success
    } catch (error) {
      console.error('标记步骤执行失败失败:', error)
      throw error
    }
  }

  /**
   * 获取步骤执行历史
   */
  static async getStepExecutionHistory(stepId: number): Promise<StepExecutionHistory[]> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/${stepId}/executions`)
      return (response as StepExecutionHistory[]) || []
    } catch (error) {
      console.error('获取步骤执行历史失败:', error)
      throw error
    }
  }

  // ==================== 统计分析 ====================

  /**
   * 获取项目步骤统计信息
   */
  static async getProjectStepStatistics(projectId: number): Promise<ProjectStepStatistics> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/statistics/${projectId}`)
      return (response as ProjectStepStatistics) || {
        projectId: projectId,
        projectName: '',
        totalSteps: 0,
        pendingSteps: 0,
        inProgressSteps: 0,
        completedSteps: 0,
        failedSteps: 0,
        blockedSteps: 0,
        overallProgress: 0,
        progressByType: [],
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取项目步骤统计失败:', error)
      throw error
    }
  }

  /**
   * 获取步骤执行进度
   */
  static async getStepExecutionProgress(projectId: number): Promise<StepExecutionProgress> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/progress/${projectId}`)
      return (response as StepExecutionProgress) || {
        projectId,
        totalSteps: 0,
        pendingSteps: 0,
        inProgressSteps: 0,
        completedSteps: 0,
        failedSteps: 0,
        completionPercentage: 0,
        estimatedCompletionDate: null,
        averageExecutionTime: 0
      }
    } catch (error) {
      console.error('获取步骤执行进度失败:', error)
      throw error
    }
  }

  /**
   * 分析步骤复杂度
   */
  static async analyzeStepComplexity(stepId: number): Promise<StepComplexityAnalysis> {
    try {
      // 复杂度分析可能涉及AI，使用AI专用API服务
      const response = await AIApiService.get(`/api/DevelopmentSteps/${stepId}/complexity`)
      return (response as StepComplexityAnalysis) || {
        stepId,
        stepName: '',
        complexityScore: 0,
        estimatedHours: 0,
        riskLevel: 'Low',
        factors: []
      }
    } catch (error) {
      console.error('分析步骤复杂度失败:', error)
      throw error
    }
  }

  /**
   * 获取步骤复杂度分析结果
   */
  static async getStepComplexityAnalysis(stepId: number): Promise<StepComplexityAnalysis> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/${stepId}/complexity-analysis`)
      return (response as StepComplexityAnalysis) || {
        stepId,
        stepName: '',
        complexityScore: 0,
        estimatedHours: 0,
        riskLevel: 'Low',
        factors: []
      }
    } catch (error) {
      console.error('获取步骤复杂度分析失败:', error)
      throw error
    }
  }

  // ==================== 模板序列应用 ====================

  /**
   * 应用模板序列到开发步骤
   */
  static async applyTemplateSequenceToSteps(sequenceId: number, stepIds: number[]): Promise<void> {
    try {
      await ApiService.post(`/api/DevelopmentSteps/apply-template-sequence`, {
        sequenceId,
        stepIds
      })
    } catch (error) {
      console.error('应用模板序列到步骤失败:', error)
      throw error
    }
  }

  /**
   * 获取步骤关联的模板序列信息
   */
  static async getStepTemplateSequences(stepId: number): Promise<any[]> {
    try {
      const response = await ApiService.get(`/api/DevelopmentSteps/${stepId}/template-sequences`)
      return (response as any[]) || []
    } catch (error) {
      console.error('获取步骤模板序列信息失败:', error)
      throw error
    }
  }

  /**
   * 移除步骤的模板序列关联
   */
  static async removeStepTemplateSequence(stepId: number, sequenceId: number): Promise<void> {
    try {
      await ApiService.delete(`/api/DevelopmentSteps/${stepId}/template-sequences/${sequenceId}`)
    } catch (error) {
      console.error('移除步骤模板序列关联失败:', error)
      throw error
    }
  }

  // ==================== AI分解功能 ====================

  /**
   * AI分解开发步骤
   */
  static async decomposeStep(stepId: number, request: {
    decompositionOptions: {
      granularity: string
      maxSubSteps: number
      technologyPreference?: string
      includeTestSteps: boolean
      includeDocumentationSteps: boolean
      customRequirements?: string
    }
    aiProviderId?: number
  }): Promise<{
    success: boolean
    message: string
    data?: {
      originalStepId: number
      childSteps: DevelopmentStep[]
      decompositionAnalysis: string
    }
    isPreview?: boolean
  }> {
    try {
      // 使用AI专用API服务，支持更长的超时时间（5分钟）
      const response = await AIApiService.post(`/api/DevelopmentSteps/${stepId}/decompose`, request) as any
      return {
        success: true,
        message: response.message || '步骤分解成功',
        data: response.data,
        isPreview: response.isPreview
      }
    } catch (error: any) {
      console.error('AI分解步骤失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'AI分解步骤失败'
      }
    }
  }

  /**
   * 确认并保存步骤分解结果
   */
  static async confirmStepDecomposition(stepId: number, request: {
    childSteps: Array<{
      stepName: string
      stepDescription: string
      stepType?: string
      priority?: string
      estimatedHours?: number
      technologyStack?: string
      fileType?: string
      filePath?: string
      componentType?: string
      aiPrompt?: string
    }>
  }): Promise<{
    success: boolean
    message: string
    data?: {
      originalStepId: number
      childSteps: DevelopmentStep[]
    }
    isPreview?: boolean
  }> {
    try {
      const response = await AIApiService.post(`/api/DevelopmentSteps/${stepId}/confirm-decompose`, request) as any
      return {
        success: true,
        message: response.message || '步骤分解保存成功',
        data: response.data,
        isPreview: response.isPreview
      }
    } catch (error: any) {
      console.error('确认保存步骤分解失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '确认保存步骤分解失败'
      }
    }
  }

  /**
   * 创建自定义子步骤
   */
  static async createCustomSubSteps(parentStepId: number, request: {
    subSteps: any[]
  }): Promise<{
    success: boolean
    message: string
    data?: {
      parentStepId: number
      childSteps: DevelopmentStep[]
    }
  }> {
    try {
      const response = await ApiService.post(`/api/DevelopmentSteps/${parentStepId}/custom-decompose`, request) as any
      return {
        success: true,
        message: response.message || '自定义细分成功',
        data: response.data
      }
    } catch (error: any) {
      console.error('创建自定义子步骤失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '创建自定义子步骤失败'
      }
    }
  }

  // ==================== 图片上传功能 ====================

  /**
   * 上传步骤参考图片
   */
  static async uploadStepImage(stepId: number, file: File): Promise<{ success: boolean; data: { filePath: string } }> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await ApiService.post(`/api/DevelopmentSteps/${stepId}/upload-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response as { success: boolean; data: { filePath: string } }
    } catch (error) {
      console.error('上传步骤图片失败:', error)
      throw error
    }
  }

  /**
   * 删除步骤参考图片
   */
  static async deleteStepImage(stepId: number, imagePath: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await ApiService.delete(`/api/DevelopmentSteps/${stepId}/delete-image?imagePath=${encodeURIComponent(imagePath)}`)
      return response as { success: boolean; message: string }
    } catch (error) {
      console.error('删除步骤图片失败:', error)
      throw error
    }
  }
}

// 导出服务类和实例
export const developmentService = DevelopmentService
export default DevelopmentService
