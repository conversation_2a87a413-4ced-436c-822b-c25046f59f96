using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 模板序列仓储接口
    /// </summary>
    public interface ITemplateSequenceRepository : IRepository<UIAutoMationTemplateSequence>
    {
        /// <summary>
        /// 分页查询序列
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResultDto<UIAutoMationTemplateSequence>> GetPagedAsync(TemplateSequenceQueryDto query);

        /// <summary>
        /// 获取序列详情（包含步骤）
        /// </summary>
        /// <param name="id">序列ID</param>
        /// <returns>序列详情</returns>
        Task<UIAutoMationTemplateSequence?> GetWithStepsAsync(int id);

        /// <summary>
        /// 根据分类获取序列
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>序列列表</returns>
        Task<List<UIAutoMationTemplateSequence>> GetByCategoryAsync(string category);

        /// <summary>
        /// 根据标签获取序列
        /// </summary>
        /// <param name="tags">标签列表</param>
        /// <returns>序列列表</returns>
        Task<List<UIAutoMationTemplateSequence>> GetByTagsAsync(List<string> tags);

        /// <summary>
        /// 搜索序列
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <returns>序列列表</returns>
        Task<List<UIAutoMationTemplateSequence>> SearchAsync(string keyword);

        /// <summary>
        /// 获取启用的序列
        /// </summary>
        /// <returns>序列列表</returns>
        Task<List<UIAutoMationTemplateSequence>> GetActiveAsync();

        /// <summary>
        /// 增加使用次数
        /// </summary>
        /// <param name="id">序列ID</param>
        /// <returns>是否成功</returns>
        Task<bool> IncrementUsageCountAsync(int id);

        /// <summary>
        /// 更新最后使用时间
        /// </summary>
        /// <param name="id">序列ID</param>
        /// <param name="lastUsedTime">最后使用时间</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateLastUsedTimeAsync(int id, DateTime lastUsedTime);

        /// <summary>
        /// 获取最常用的序列
        /// </summary>
        /// <param name="count">数量</param>
        /// <returns>序列列表</returns>
        Task<List<UIAutoMationTemplateSequence>> GetMostUsedAsync(int count = 10);

        /// <summary>
        /// 检查序列名称是否存在
        /// </summary>
        /// <param name="name">序列名称</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsNameAsync(string name, int? excludeId = null);

        /// <summary>
        /// 根据名称获取序列
        /// </summary>
        /// <param name="name">序列名称</param>
        /// <returns>序列实体</returns>
        Task<UIAutoMationTemplateSequence?> GetByNameAsync(string name);

        /// <summary>
        /// 批量删除序列
        /// </summary>
        /// <param name="ids">序列ID列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchDeleteAsync(List<int> ids);

        /// <summary>
        /// 启用/禁用序列
        /// </summary>
        /// <param name="id">序列ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>是否成功</returns>
        Task<bool> SetActiveStatusAsync(int id, bool isActive);
    }
}
