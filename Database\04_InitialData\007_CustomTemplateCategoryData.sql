-- =============================================
-- 自定义模板分类初始数据脚本
-- 版本: 1.0
-- 创建时间: 2024-06-23
-- 描述: 插入默认的模板分类数据，包括系统预定义分类
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '开始插入自定义模板分类初始数据...';

-- =============================================
-- 1. 插入系统预定义分类
-- =============================================

-- 检查是否已存在数据
IF NOT EXISTS (SELECT 1 FROM CustomTemplateCategories WHERE Name = '按钮')
BEGIN
    PRINT '插入系统预定义分类...';

    -- 插入主要分类
    INSERT INTO CustomTemplateCategories (Name, Description, Icon, Color, SortOrder, IsSystem, IsEnabled, CreatedTime) VALUES
    ('按钮', '各种按钮元素，包括普通按钮、图标按钮等', 'Operation', '#409EFF', 1, 1, 1, GETDATE()),
    ('菜单', '菜单项和下拉菜单元素', 'Menu', '#67C23A', 2, 1, 1, GETDATE()),
    ('对话框', '对话框、弹窗和模态框元素', 'ChatDotRound', '#E6A23C', 3, 1, 1, GETDATE()),
    ('输入框', '文本输入框、表单控件等', 'Edit', '#909399', 4, 1, 1, GETDATE()),
    ('图标', '图标和小图片元素', 'Picture', '#F56C6C', 5, 1, 1, GETDATE()),
    ('文本', '文本标签和内容元素', 'Document', '#606266', 6, 1, 1, GETDATE()),
    ('状态', '状态指示器、进度条等', 'DataLine', '#13CE66', 7, 1, 1, GETDATE()),
    ('工具栏', '工具栏和操作栏元素', 'Tools', '#FF9500', 8, 1, 1, GETDATE()),
    ('面板', '面板、容器和布局元素', 'Box', '#5856D6', 9, 1, 1, GETDATE()),
    ('其他', '其他类型的UI元素', 'Link', '#8E8E93', 99, 1, 1, GETDATE());

    PRINT '✅ 系统预定义分类插入成功';
END
ELSE
BEGIN
    PRINT '⚠️ 系统预定义分类已存在，跳过插入';
END

-- =============================================
-- 2. 插入CopilotChat专用分类
-- =============================================

-- 检查是否已存在CopilotChat分类
IF NOT EXISTS (SELECT 1 FROM CustomTemplateCategories WHERE Name = 'CopilotChat自动化')
BEGIN
    PRINT '插入CopilotChat专用分类...';

    -- 插入CopilotChat主分类
    INSERT INTO CustomTemplateCategories (Name, Description, Icon, Color, SortOrder, IsSystem, IsEnabled, CreatedTime) VALUES
    ('CopilotChat自动化', 'VSCode CopilotChat相关的自动化模板', 'Cpu', '#0078D4', 10, 1, 1, GETDATE());

    -- 获取CopilotChat主分类ID
    DECLARE @CopilotChatCategoryId int = SCOPE_IDENTITY();

    -- 插入CopilotChat子分类
    INSERT INTO CustomTemplateCategories (Name, Description, ParentId, Icon, Color, SortOrder, IsSystem, IsEnabled, CreatedTime) VALUES
    ('Chat界面', 'CopilotChat聊天界面相关元素', @CopilotChatCategoryId, 'ChatDotRound', '#0078D4', 1, 1, 1, GETDATE()),
    ('代码生成', 'CopilotChat代码生成相关元素', @CopilotChatCategoryId, 'Lightning', '#0078D4', 2, 1, 1, GETDATE()),
    ('文档生成', 'CopilotChat文档生成相关元素', @CopilotChatCategoryId, 'Reading', '#0078D4', 3, 1, 1, GETDATE()),
    ('设置配置', 'CopilotChat设置和配置相关元素', @CopilotChatCategoryId, 'Setting', '#0078D4', 4, 1, 1, GETDATE());

    PRINT '✅ CopilotChat专用分类插入成功';
END
ELSE
BEGIN
    PRINT '⚠️ CopilotChat专用分类已存在，跳过插入';
END

-- =============================================
-- 3. 插入用户自定义分类示例
-- =============================================

-- 检查是否已存在用户自定义分类
IF NOT EXISTS (SELECT 1 FROM CustomTemplateCategories WHERE Name = '我的模板')
BEGIN
    PRINT '插入用户自定义分类示例...';

    INSERT INTO CustomTemplateCategories (Name, Description, Icon, Color, SortOrder, IsSystem, IsEnabled, CreatedTime) VALUES
    ('我的模板', '用户自定义的模板分类', 'User', '#722ED1', 50, 0, 1, GETDATE()),
    ('常用操作', '经常使用的操作模板', 'Star', '#FA8C16', 51, 0, 1, GETDATE()),
    ('测试模板', '用于测试的模板分类', 'MagicStick', '#52C41A', 52, 0, 1, GETDATE());

    PRINT '✅ 用户自定义分类示例插入成功';
END
ELSE
BEGIN
    PRINT '⚠️ 用户自定义分类示例已存在，跳过插入';
END

PRINT '✅ 自定义模板分类初始数据插入完成！';
