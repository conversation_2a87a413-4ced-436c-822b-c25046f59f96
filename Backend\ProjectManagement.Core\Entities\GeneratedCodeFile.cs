using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 生成的代码文件实体 - 对应DatabaseSchema.sql中的GeneratedCodeFiles表
/// 功能: 存储AI生成的具体代码文件内容
/// 支持: 多文件类型、版本管理、文件组织、代码检索
/// </summary>
[SugarTable("GeneratedCodeFiles", "代码文件存储管理表")]
public class GeneratedCodeFile
{
    /// <summary>
    /// 代码文件唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "代码文件唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 关联的代码生成任务ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的代码生成任务ID")]
    public int CodeGenerationTaskId { get; set; }

    /// <summary>
    /// 文件名称（如：UserController.cs, UserList.vue）
    /// </summary>
    [SugarColumn(Length = 255, IsNullable = false, ColumnDescription = "文件名称")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件完整路径（如：/Controllers/UserController.cs）
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = false, ColumnDescription = "文件完整路径")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件完整内容（实际的代码内容）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "文件完整内容")]
    public string FileContent { get; set; } = string.Empty;

    /// <summary>
    /// 文件扩展名类型: cs, vue, sql, json, js, ts, html, css等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "文件扩展名类型")]
    public string? FileType { get; set; }

    /// <summary>
    /// 编程语言: CSharp, JavaScript, SQL, TypeScript, HTML, CSS等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编程语言")]
    public string? Language { get; set; }

    /// <summary>
    /// 文件生成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "文件生成时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的代码生成任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public CodeGenerationTask? CodeGenerationTask { get; set; }
}
