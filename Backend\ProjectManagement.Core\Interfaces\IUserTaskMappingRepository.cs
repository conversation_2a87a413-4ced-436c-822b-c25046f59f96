using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 用户任务映射仓储接口
/// 功能: 定义用户任务映射的数据访问操作
/// 支持: CRUD操作、用户配置查询、任务类型查询、默认配置管理
/// </summary>
public interface IUserTaskMappingRepository
{
    /// <summary>
    /// 根据ID获取用户任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <returns>用户任务映射实体</returns>
    Task<UserTaskMapping?> GetByIdAsync(int id);

    /// <summary>
    /// 根据用户ID获取所有任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户任务映射列表</returns>
    Task<List<UserTaskMapping>> GetByUserIdAsync(int userId);

    /// <summary>
    /// 根据用户ID和任务类型获取任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>用户任务映射列表</returns>
    Task<List<UserTaskMapping>> GetByUserIdAndTaskTypeAsync(int userId, string taskType);

    /// <summary>
    /// 根据用户ID、任务类型和提供商获取任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="providerName">提供商名称</param>
    /// <returns>用户任务映射实体</returns>
    Task<UserTaskMapping?> GetByUserTaskProviderAsync(int userId, string taskType, string providerName);

    /// <summary>
    /// 获取用户指定任务类型的默认配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>默认任务映射配置</returns>
    Task<UserTaskMapping?> GetDefaultByUserAndTaskTypeAsync(int userId, string taskType);

    /// <summary>
    /// 获取用户指定任务类型的最高优先级配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>最高优先级的任务映射配置</returns>
    Task<UserTaskMapping?> GetHighestPriorityByUserAndTaskTypeAsync(int userId, string taskType);

    /// <summary>
    /// 根据提供商名称获取所有使用该提供商的任务映射
    /// </summary>
    /// <param name="providerName">提供商名称</param>
    /// <returns>任务映射列表</returns>
    Task<List<UserTaskMapping>> GetByProviderNameAsync(string providerName);

    /// <summary>
    /// 获取所有任务映射（分页）
    /// </summary>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页的任务映射列表</returns>
    Task<(List<UserTaskMapping> Items, int TotalCount)> GetPagedAsync(int pageIndex, int pageSize);

    /// <summary>
    /// 创建用户任务映射
    /// </summary>
    /// <param name="mapping">任务映射实体</param>
    /// <returns>创建的任务映射实体</returns>
    Task<UserTaskMapping> CreateAsync(UserTaskMapping mapping);

    /// <summary>
    /// 批量创建用户任务映射
    /// </summary>
    /// <param name="mappings">任务映射实体列表</param>
    /// <returns>创建的任务映射实体列表</returns>
    Task<List<UserTaskMapping>> CreateBatchAsync(List<UserTaskMapping> mappings);

    /// <summary>
    /// 更新用户任务映射
    /// </summary>
    /// <param name="mapping">任务映射实体</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateAsync(UserTaskMapping mapping);

    /// <summary>
    /// 批量更新用户任务映射
    /// </summary>
    /// <param name="mappings">任务映射实体列表</param>
    /// <returns>更新成功的数量</returns>
    Task<int> UpdateBatchAsync(List<UserTaskMapping> mappings);

    /// <summary>
    /// 删除用户任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(int id);

    /// <summary>
    /// 删除用户的所有任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>删除的数量</returns>
    Task<int> DeleteByUserIdAsync(int userId);

    /// <summary>
    /// 删除指定任务类型的所有映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>删除的数量</returns>
    Task<int> DeleteByUserAndTaskTypeAsync(int userId, string taskType);

    /// <summary>
    /// 启用/禁用任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <param name="isActive">是否启用</param>
    /// <returns>是否操作成功</returns>
    Task<bool> ToggleActiveAsync(int id, bool isActive);

    /// <summary>
    /// 设置默认配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="mappingId">要设置为默认的映射ID</param>
    /// <returns>是否设置成功</returns>
    Task<bool> SetDefaultAsync(int userId, string taskType, int mappingId);

    /// <summary>
    /// 清除指定任务类型的默认配置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="excludeMappingId">排除的映射ID（可选）</param>
    /// <returns>清除的数量</returns>
    Task<int> ClearDefaultAsync(int userId, string taskType, int? excludeMappingId = null);

    /// <summary>
    /// 检查用户是否已配置指定任务类型的映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <returns>是否已配置</returns>
    Task<bool> HasMappingForTaskTypeAsync(int userId, string taskType);

    /// <summary>
    /// 获取用户任务映射统计信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>统计信息</returns>
    Task<object> GetUserMappingStatsAsync(int userId);

    /// <summary>
    /// 获取系统任务映射统计信息
    /// </summary>
    /// <returns>系统统计信息</returns>
    Task<object> GetSystemMappingStatsAsync();

    /// <summary>
    /// 验证任务映射配置是否有效
    /// </summary>
    /// <param name="mapping">任务映射实体</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, List<string> Errors)> ValidateMappingAsync(UserTaskMapping mapping);

    /// <summary>
    /// 检查是否存在重复的任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="providerName">提供商名称</param>
    /// <param name="excludeId">排除的映射ID（用于更新时检查）</param>
    /// <returns>是否存在重复</returns>
    Task<bool> ExistsDuplicateAsync(int userId, string taskType, string providerName, int? excludeId = null);
}
