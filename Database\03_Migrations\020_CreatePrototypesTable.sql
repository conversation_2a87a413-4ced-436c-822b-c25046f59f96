-- 创建原型图表
-- 用于存储UI原型图设计，包括线框图、交互流程图等

USE [ProjectManagementAI]
GO

-- 检查表是否已存在
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Prototypes](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [ProjectId] [int] NOT NULL,
        [RequirementDocumentId] [int] NULL,
        [PrototypeName] [nvarchar](200) NOT NULL,
        [PrototypeType] [nvarchar](50) NOT NULL DEFAULT 'Wireframe',
        [MermaidDefinition] [nvarchar](max) NOT NULL,
        [Description] [nvarchar](max) NULL,
        [TargetUsers] [nvarchar](200) NULL,
        [PageModules] [nvarchar](max) NULL,
        [InteractionFlows] [nvarchar](max) NULL,
        [UIComponents] [nvarchar](max) NULL,
        [DeviceType] [nvarchar](50) NULL DEFAULT 'Desktop',
        [FidelityLevel] [nvarchar](50) NULL DEFAULT 'Low',
        [PrototypeVersion] [nvarchar](20) NOT NULL DEFAULT '1.0',
        [CreatedBy] [int] NULL,
        [CreatedTime] [datetime2](7) NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] [int] NULL,
        [UpdatedTime] [datetime2](7) NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0,
        [DeletedTime] [datetime2](7) NULL,
        [DeletedBy] [int] NULL,
        [Version] [int] NOT NULL DEFAULT 1,
        [Remarks] [nvarchar](500) NULL,
        CONSTRAINT [PK_Prototypes] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_Prototypes_Projects] FOREIGN KEY([ProjectId]) REFERENCES [dbo].[Projects] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_Prototypes_RequirementDocuments] FOREIGN KEY([RequirementDocumentId]) REFERENCES [dbo].[RequirementDocuments] ([Id]) ON DELETE SET NULL,
        CONSTRAINT [FK_Prototypes_Users_CreatedBy] FOREIGN KEY([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_Prototypes_Users_UpdatedBy] FOREIGN KEY([UpdatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_Prototypes_Users_DeletedBy] FOREIGN KEY([DeletedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [CK_Prototypes_PrototypeType] CHECK ([PrototypeType] IN ('Wireframe', 'UserFlow', 'ComponentDiagram', 'InteractionFlow')),
        CONSTRAINT [CK_Prototypes_DeviceType] CHECK ([DeviceType] IN ('Desktop', 'Mobile', 'Tablet', 'Responsive')),
        CONSTRAINT [CK_Prototypes_FidelityLevel] CHECK ([FidelityLevel] IN ('Low', 'Medium', 'High'))
    )

    PRINT '原型图表 [Prototypes] 创建成功'
END
ELSE
BEGIN
    PRINT '原型图表 [Prototypes] 已存在，跳过创建'
END
GO

-- 添加索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_ProjectId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_ProjectId] ON [dbo].[Prototypes]([ProjectId])
    PRINT '索引 [IX_Prototypes_ProjectId] 创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_RequirementDocumentId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_RequirementDocumentId] ON [dbo].[Prototypes]([RequirementDocumentId])
    PRINT '索引 [IX_Prototypes_RequirementDocumentId] 创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_PrototypeType')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_PrototypeType] ON [dbo].[Prototypes]([PrototypeType])
    PRINT '索引 [IX_Prototypes_PrototypeType] 创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_CreatedTime')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_CreatedTime] ON [dbo].[Prototypes]([CreatedTime] DESC)
    PRINT '索引 [IX_Prototypes_CreatedTime] 创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_IsDeleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_IsDeleted] ON [dbo].[Prototypes]([IsDeleted])
    PRINT '索引 [IX_Prototypes_IsDeleted] 创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Prototypes]') AND name = N'IX_Prototypes_CreatedBy')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Prototypes_CreatedBy] ON [dbo].[Prototypes]([CreatedBy])
    PRINT '索引 [IX_Prototypes_CreatedBy] 创建成功'
END

-- 添加扩展属性（中文注释）
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[Prototypes]') AND minor_id = 0)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name=N'MS_Description', 
        @value=N'原型图管理表 - 存储UI原型图设计，包括线框图、用户流程图、组件关系图、交互流程图等', 
        @level0type=N'SCHEMA', @level0name=N'dbo', 
        @level1type=N'TABLE', @level1name=N'Prototypes'
END

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原型图ID，主键自增', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Id'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'所属项目ID，关联Projects表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'ProjectId'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联的需求文档ID，可为空', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'RequirementDocumentId'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原型图名称（如：用户登录页面原型、商品列表线框图）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'PrototypeName'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原型图类型：Wireframe(线框图)、UserFlow(用户流程图)、ComponentDiagram(组件关系图)、InteractionFlow(交互流程图)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'PrototypeType'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Mermaid格式的原型图定义代码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'MermaidDefinition'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原型图描述说明', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Description'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'目标用户群体（如：管理员、普通用户、访客）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'TargetUsers'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'页面/功能模块信息，JSON格式存储', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'PageModules'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'交互流程信息，JSON格式存储（用户操作路径、页面跳转等）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'InteractionFlows'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'UI组件信息，JSON格式存储（按钮、表单、列表等组件详情）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'UIComponents'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'设备类型：Desktop(桌面端)、Mobile(移动端)、Tablet(平板端)、Responsive(响应式)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'DeviceType'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'保真度级别：Low(低保真)、Medium(中保真)、High(高保真)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'FidelityLevel'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原型图版本号，用于版本控制', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'PrototypeVersion'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建者用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'CreatedBy'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'CreatedTime'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新者用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'UpdatedBy'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最后更新时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'UpdatedTime'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'软删除标记，0=未删除，1=已删除', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'IsDeleted'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'删除时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'DeletedTime'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'删除者用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'DeletedBy'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'版本号，用于乐观锁并发控制', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Version'
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注信息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'Prototypes', @level2type=N'COLUMN', @level2name=N'Remarks'

PRINT '原型图表创建完成，包含所有索引和中文注释'
GO
