{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ProjectManagementAI;Persist Security Info=True;User ID=sa;Password=**********;TrustServerCertificate=true;MultipleActiveResultSets=true;", "AlternativeConnection": "Server=************;Database=ProjectManagementAI;Persist Security Info=True;User ID=test_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "SqlSugar": "Information"}}, "BuildSettings": {"ProjectId": 1, "BackendProjectPath": "", "FrontendProjectPath": "", "AutoCompileInterval": 30, "MaxErrorsToDisplay": 100, "EnableAutoCompile": true, "CompileTimeoutSeconds": 300}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSqlLogging": true, "AutoCreateTables": false}, "UISettings": {"RefreshInterval": 5, "ShowWarnings": true, "ShowInfoMessages": false, "MaxDisplayRows": 1000}}