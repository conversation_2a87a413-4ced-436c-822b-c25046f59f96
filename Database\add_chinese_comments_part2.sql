-- =============================================
-- 项目管理AI系统数据库字段中文注释脚本 - 第二部分
-- 为剩余表的字段添加中文描述注释
-- 版本: 1.0
-- 创建日期: 2024-12-19
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 7. RequirementDocuments表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'需求文档唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'需求文档标题，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'Title';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'需求文档完整内容（支持全文搜索）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'Content';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'功能性需求详细描述（支持全文搜索）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'FunctionalRequirements';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'非功能性需求详细描述（支持全文搜索）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'NonFunctionalRequirements';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户故事集合，JSON格式存储',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'UserStories';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'验收标准详细说明',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'AcceptanceCriteria';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文档版本号（如：1.0, 1.1, 2.0），长度20字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'DocumentVersion';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文档状态：Draft=草稿, Review=审核中, Approved=已批准, Rejected=已拒绝',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'生成方式：AI=AI生成, Manual=手动创建, Hybrid=混合方式',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments',
    @level2type = N'Column', @level2name = N'GeneratedBy';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'需求文档管理表，存储AI生成的需求规格书文档，支持版本控制、状态管理、全文搜索、多格式需求',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementDocuments';
GO

-- =============================================
-- 8. ERDiagrams表字段注释
-- =============================================

-- 添加表说明
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'实体关系图管理表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'Id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'所属项目ID，关联Projects表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'ProjectId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'关联的需求文档ID，可为空', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'RequirementDocumentId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'ER图名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'DiagramName';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Mermaid格式的ER图定义代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'MermaidDefinition';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'ER图描述说明', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'Description';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'ER图版本号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'DiagramVersion';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'CreatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'UpdatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'CreatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'UpdatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'软删除标记', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'IsDeleted';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'DeletedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'DeletedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'版本号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'Version';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ERDiagrams', @level2type = N'COLUMN', @level2name = N'Remarks';
GO


-- =============================================
-- 9. ContextDiagrams表字段注释
-- =============================================

-- 添加表说明
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'系统上下文图管理表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'Id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'所属项目ID，关联Projects表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'ProjectId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'关联的需求文档ID，可为空', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'RequirementDocumentId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'上下文图名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'DiagramName';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Mermaid格式的上下文图定义代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'MermaidDefinition';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'外部实体信息，JSON格式存储', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'ExternalEntities';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'系统边界定义，JSON格式存储', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'SystemBoundary';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'数据流信息，JSON格式存储', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'DataFlows';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'上下文图版本号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'DiagramVersion';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'CreatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'UpdatedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'CreatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'更新人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'UpdatedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'软删除标记', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'IsDeleted';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'DeletedTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'删除人ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'DeletedBy';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'版本号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'Version';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'ContextDiagrams', @level2type = N'COLUMN', @level2name = N'Remarks';
GO

-- =============================================
-- 10. CodeGenerationTasks表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'代码生成任务唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'关联的需求文档ID，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'RequirementDocumentId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'代码生成任务名称（如：用户管理API生成），长度200字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'TaskName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'代码类型：Frontend=前端, Backend=后端, Database=数据库, API=接口, Model=模型',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'CodeType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'使用的技术栈：Vue, CSharp, SqlServer, React, Python等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'Technology';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'任务状态：Pending=待处理, InProgress=进行中, Completed=已完成, Failed=失败',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI生成的代码内容',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'GeneratedCode';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'生成代码的文件路径，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'FilePath';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'错误信息（任务失败时记录）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'ErrorMessage';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'任务创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'任务完成时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks',
    @level2type = N'Column', @level2name = N'CompletedAt';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI代码生成任务管理表，管理AI代码生成任务的执行状态和结果，支持多技术栈、任务状态跟踪、错误处理、批量生成',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'CodeGenerationTasks';
GO
