using System;
using System.Threading;
using System.Threading.Tasks;
using BuildMonitor.Configuration;
using BuildMonitor.Models;
using BuildMonitor.Services;
using System.Collections.Generic;
using System.IO;

namespace BuildMonitor.Services
{
    /// <summary>
    /// 自动编译服务 - 定时自动编译项目
    /// </summary>
    public class AutoCompileService
    {
        private readonly CompilerService _compilerService;
        private readonly DatabaseService _databaseService;
        private readonly AppSettings _appSettings;
        private Timer? _timer;
        private bool _isRunning = false;
        private bool _isCompiling = false;
        private DateTime _lastCompileTime = DateTime.MinValue;

        /// <summary>
        /// 编译完成事件 - 通知主窗口刷新错误列表
        /// </summary>
        public event EventHandler<CompilationCompletedEventArgs>? CompilationCompleted;

        public AutoCompileService(CompilerService compilerService, DatabaseService databaseService, AppSettings appSettings)
        {
            _compilerService = compilerService;
            _databaseService = databaseService;
            _appSettings = appSettings;
        }

        /// <summary>
        /// 启动自动编译
        /// </summary>
        public void Start()
        {
            System.Diagnostics.Debug.WriteLine($"🔍 开始启动自动编译服务，当前运行状态: {_isRunning}");

            if (_isRunning)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 自动编译服务已经在运行中");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"🔍 检查配置 - EnableAutoCompile: {_appSettings.Build.EnableAutoCompile}");
            System.Diagnostics.Debug.WriteLine($"🔍 配置详情 - 间隔: {_appSettings.Build.AutoCompileInterval}秒, 后端路径: {_appSettings.Build.BackendProjectPath}");
            System.Diagnostics.Debug.WriteLine($"🔍 配置详情 - 前端路径: {_appSettings.Build.FrontendProjectPath}");

            if (!_appSettings.Build.EnableAutoCompile)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 自动编译功能已禁用");
                return;
            }

            var interval = TimeSpan.FromSeconds(_appSettings.Build.AutoCompileInterval);
            System.Diagnostics.Debug.WriteLine($"🚀 启动自动编译服务，间隔时间: {interval.TotalSeconds} 秒");

            try
            {
                _timer = new Timer(OnTimerElapsed, null, TimeSpan.Zero, interval);
                _isRunning = true;
                System.Diagnostics.Debug.WriteLine("✅ 自动编译服务已启动，定时器已创建");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 启动自动编译服务失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ 异常详情: {ex}");
                _isRunning = false;
                throw;
            }
        }

        /// <summary>
        /// 停止自动编译
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 自动编译服务未运行");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🛑 正在停止自动编译服务...");

            _timer?.Dispose();
            _timer = null;
            _isRunning = false;

            System.Diagnostics.Debug.WriteLine("✅ 自动编译服务已停止");
        }

        /// <summary>
        /// 重启自动编译服务（用于配置更改后）
        /// </summary>
        public void Restart()
        {
            System.Diagnostics.Debug.WriteLine("🔄 重启自动编译服务...");
            Stop();
            Start();
        }

        /// <summary>
        /// 更新自动编译间隔时间
        /// </summary>
        public void UpdateInterval(int intervalSeconds)
        {
            if (intervalSeconds <= 0)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 间隔时间必须大于0秒");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"🔧 更新自动编译间隔时间: {intervalSeconds} 秒");

            if (_isRunning)
            {
                Restart();
            }
        }

        /// <summary>
        /// 获取自动编译状态
        /// </summary>
        public AutoCompileStatus GetStatus()
        {
            var status = new AutoCompileStatus
            {
                IsRunning = _isRunning,
                IsCompiling = _isCompiling,
                LastCompileTime = _lastCompileTime,
                IntervalSeconds = _appSettings.Build.AutoCompileInterval,
                EnableAutoCompile = _appSettings.Build.EnableAutoCompile,
                BackendProjectPath = _appSettings.Build.BackendProjectPath ?? "",
                FrontendProjectPath = _appSettings.Build.FrontendProjectPath ?? ""
            };

            System.Diagnostics.Debug.WriteLine($"🔍 GetStatus调用 - IsRunning: {status.IsRunning}, IsCompiling: {status.IsCompiling}");
            return status;
        }

        /// <summary>
        /// 手动触发一次编译
        /// </summary>
        public async Task TriggerManualCompileAsync()
        {
            if (_isCompiling)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 编译正在进行中，请稍后再试");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔨 手动触发编译...");
            await PerformCompileAsync();
        }

        /// <summary>
        /// 定时器回调方法
        /// </summary>
        private async void OnTimerElapsed(object? state)
        {
            System.Diagnostics.Debug.WriteLine($"⏰ 定时器触发 - {DateTime.Now:HH:mm:ss}");

            if (_isCompiling)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 上次编译尚未完成，跳过本次编译");
                return;
            }

            try
            {
                await PerformCompileAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 定时器编译任务异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ 异常详情: {ex}");
            }
        }

        /// <summary>
        /// 执行编译操作
        /// </summary>
        private async Task PerformCompileAsync()
        {
            if (_isCompiling)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ 编译已在进行中，跳过重复执行");
                return;
            }

            _isCompiling = true;
            _lastCompileTime = DateTime.Now;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔨 开始自动编译 - {_lastCompileTime:yyyy-MM-dd HH:mm:ss}");

                var allErrors = new List<CompilationError>();

                // 编译后端项目
                if (!string.IsNullOrEmpty(_appSettings.Build.BackendProjectPath) && File.Exists(_appSettings.Build.BackendProjectPath))
                {
                    try
                    {
                        await _databaseService.ClearErrorsAsync("Backend", _appSettings.Build.ProjectId);
                        System.Diagnostics.Debug.WriteLine($"🔧 编译后端项目: {_appSettings.Build.BackendProjectPath}");
                        var backendErrors = await _compilerService.CompileCSharpProjectAsync(_appSettings.Build.BackendProjectPath, _appSettings.Build.ProjectId);

                        System.Diagnostics.Debug.WriteLine($"📊 后端编译返回 {backendErrors.Count} 个问题");
                        if (backendErrors.Count > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"   前3个错误:");
                            foreach (var error in backendErrors.Take(3))
                            {
                                System.Diagnostics.Debug.WriteLine($"   - {error.Severity}: {error.Message}");
                            }
                        }

                        // 立即保存后端错误到数据库
                        if (backendErrors.Count > 0)
                        {
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"💾 保存 {backendErrors.Count} 个后端编译问题到数据库");
                                await _databaseService.SaveCompilationBackendErrorsAsync(backendErrors);
                                System.Diagnostics.Debug.WriteLine($"✅ 后端编译问题已成功保存到数据库");
                            }
                            catch (Exception saveEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ 保存后端编译问题到数据库失败: {saveEx.Message}");
                            }
                        }

                        allErrors.AddRange(backendErrors);
                        System.Diagnostics.Debug.WriteLine($"📊 添加后端错误后，总错误数: {allErrors.Count}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 后端编译异常: {ex.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 后端项目路径未配置或文件不存在 - 路径: '{_appSettings.Build.BackendProjectPath}'");
                }

                // 编译前端项目
                if (!string.IsNullOrEmpty(_appSettings.Build.FrontendProjectPath) && Directory.Exists(_appSettings.Build.FrontendProjectPath))
                {
                    try
                    {
                        await _databaseService.ClearErrorsAsync("Frontend", _appSettings.Build.ProjectId);
                        System.Diagnostics.Debug.WriteLine($"🔧 编译前端项目: {_appSettings.Build.FrontendProjectPath}");
                        var frontendErrors = await _compilerService.CompileFrontendProjectAsync(_appSettings.Build.FrontendProjectPath, _appSettings.Build.ProjectId);

                        // 立即保存前端错误到数据库
                        if (frontendErrors.Count > 0)
                        {
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"💾 保存 {frontendErrors.Count} 个前端编译问题到数据库");
                                await _databaseService.SaveCompilationFroentendErrorsAsync(frontendErrors);
                                System.Diagnostics.Debug.WriteLine($"✅ 前端编译问题已成功保存到数据库");
                            }
                            catch (Exception saveEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ 保存前端编译问题到数据库失败: {saveEx.Message}");
                            }
                        }

                        allErrors.AddRange(frontendErrors);
                        System.Diagnostics.Debug.WriteLine($"📊 前端编译完成，发现 {frontendErrors.Count} 个问题");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 前端编译异常: {ex.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 前端项目路径未配置或目录不存在 - 路径: '{_appSettings.Build.FrontendProjectPath}'");
                }

                System.Diagnostics.Debug.WriteLine($"✅ 自动编译完成 - 总共发现 {allErrors.Count} 个问题");

                // 触发编译完成事件，通知主窗口刷新错误列表
                var hasBackendErrors = allErrors.Any(e => e.ProjectType == "Backend");
                var hasFrontendErrors = allErrors.Any(e => e.ProjectType == "Frontend");

                CompilationCompleted?.Invoke(this, new CompilationCompletedEventArgs
                {
                    ErrorCount = allErrors.Count,
                    CompilationTime = _lastCompileTime,
                    HasBackendErrors = hasBackendErrors,
                    HasFrontendErrors = hasFrontendErrors
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 自动编译失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ 异常详情: {ex}");
            }
            finally
            {
                _isCompiling = false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Stop();
        }
    }

    /// <summary>
    /// 自动编译状态信息
    /// </summary>
    public class AutoCompileStatus
    {
        public bool IsRunning { get; set; }
        public bool IsCompiling { get; set; }
        public DateTime LastCompileTime { get; set; }
        public int IntervalSeconds { get; set; }
        public bool EnableAutoCompile { get; set; }
        public string BackendProjectPath { get; set; }
        public string FrontendProjectPath { get; set; }
    }

    /// <summary>
    /// 编译完成事件参数
    /// </summary>
    public class CompilationCompletedEventArgs : EventArgs
    {
        public int ErrorCount { get; set; }
        public DateTime CompilationTime { get; set; }
        public bool HasBackendErrors { get; set; }
        public bool HasFrontendErrors { get; set; }
    }
}
