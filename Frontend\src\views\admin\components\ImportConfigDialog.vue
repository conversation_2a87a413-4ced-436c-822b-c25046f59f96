<template>
  <el-dialog
    v-model="visible"
    title="导入AI配置"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-config">
      <!-- 文件上传 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="config-upload"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".json"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将配置文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 JSON 格式的配置文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 配置预览 -->
      <div v-if="configPreview" class="preview-section">
        <h3 class="section-title">配置预览</h3>
        
        <!-- 基本信息 -->
        <el-descriptions :column="2" border>
          <el-descriptions-item label="默认提供商">
            {{ configPreview.defaultProvider }}
          </el-descriptions-item>
          <el-descriptions-item label="提供商数量">
            {{ Object.keys(configPreview.providers || {}).length }}
          </el-descriptions-item>
          <el-descriptions-item label="任务映射">
            {{ Object.keys(configPreview.taskMapping || {}).length }} 个任务
          </el-descriptions-item>
          <el-descriptions-item label="默认模型">
            {{ configPreview.defaultConfig?.model || '未设置' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 提供商列表 -->
        <div class="providers-preview">
          <h4>包含的提供商:</h4>
          <div class="provider-list">
            <el-tag
              v-for="(config, name) in configPreview.providers"
              :key="name"
              :type="config.enabled ? 'success' : 'info'"
              class="provider-tag"
            >
              {{ name }} {{ config.enabled ? '(启用)' : '(禁用)' }}
            </el-tag>
          </div>
        </div>

        <!-- 导入选项 -->
        <div class="import-options">
          <h4>导入选项:</h4>
          <el-checkbox-group v-model="importOptions">
            <el-checkbox label="providers">提供商配置</el-checkbox>
            <el-checkbox label="defaultConfig">默认配置</el-checkbox>
            <el-checkbox label="taskMapping">任务映射</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 冲突处理 -->
        <div class="conflict-handling">
          <h4>冲突处理:</h4>
          <el-radio-group v-model="conflictStrategy">
            <el-radio label="merge">合并配置（保留现有，添加新的）</el-radio>
            <el-radio label="replace">替换配置（覆盖现有配置）</el-radio>
            <el-radio label="skip">跳过冲突（仅导入不冲突的配置）</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-section">
        <h3 class="section-title">验证结果</h3>
        
        <el-alert
          :type="validationResult.isValid ? 'success' : 'error'"
          :title="validationResult.isValid ? '配置验证通过' : '配置验证失败'"
          show-icon
          :closable="false"
        />

        <div v-if="validationResult.errors.length > 0" class="validation-errors">
          <h4>错误:</h4>
          <ul>
            <li v-for="error in validationResult.errors" :key="error" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>

        <div v-if="validationResult.warnings.length > 0" class="validation-warnings">
          <h4>警告:</h4>
          <ul>
            <li v-for="warning in validationResult.warnings" :key="warning" class="warning-item">
              {{ warning }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <h3 class="section-title">导入结果</h3>
        
        <el-alert
          :type="importResult.success ? 'success' : 'error'"
          :title="importResult.success ? '导入成功' : '导入失败'"
          :description="importResult.message"
          show-icon
          :closable="false"
        />

        <div v-if="importResult.importedProviders && importResult.importedProviders.length > 0" class="imported-providers">
          <h4>已导入的提供商:</h4>
          <div class="provider-list">
            <el-tag
              v-for="provider in importResult.importedProviders"
              :key="provider"
              type="success"
              class="provider-tag"
            >
              {{ provider }}
            </el-tag>
          </div>
        </div>

        <div v-if="importResult.errors && importResult.errors.length > 0" class="import-errors">
          <h4>导入错误:</h4>
          <ul>
            <li v-for="error in importResult.errors" :key="error" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ importResult?.success ? '完成' : '取消' }}
        </el-button>
        <el-button 
          v-if="configPreview && !importResult"
          @click="validateConfiguration" 
          :loading="validating"
        >
          <el-icon><CircleCheck /></el-icon>
          验证配置
        </el-button>
        <el-button 
          v-if="configPreview && !importResult"
          type="primary" 
          @click="importConfiguration" 
          :loading="importing"
          :disabled="Boolean(validationResult && !validationResult.isValid)"
        >
          <el-icon><Upload /></el-icon>
          导入配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadInstance, UploadFile } from 'element-plus'
import { AIProviderService, type AIConfiguration } from '@/services/aiProvider'
import { UploadFilled, CircleCheck, Upload } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'imported'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const uploadRef = ref<UploadInstance>()
const configPreview = ref<AIConfiguration | null>(null)
const validationResult = ref<{
  isValid: boolean
  errors: string[]
  warnings: string[]
} | null>(null)
const importResult = ref<{
  success: boolean
  message: string
  importedProviders?: string[]
  errors?: string[]
} | null>(null)

const importOptions = ref(['providers', 'defaultConfig', 'taskMapping'])
const conflictStrategy = ref('merge')
const validating = ref(false)
const importing = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target?.result as string
      const config = JSON.parse(content) as AIConfiguration
      
      // 基本验证
      if (!config.defaultProvider || !config.providers) {
        throw new Error('配置文件格式不正确')
      }
      
      configPreview.value = config
      validationResult.value = null
      importResult.value = null
      
      ElMessage.success('配置文件解析成功')
    } catch (error: any) {
      console.error('解析配置文件失败:', error)
      ElMessage.error('配置文件格式错误: ' + error.message)
      configPreview.value = null
    }
  }
  
  reader.readAsText(file.raw)
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个配置文件')
}

const validateConfiguration = async () => {
  if (!configPreview.value) return
  
  try {
    validating.value = true
    
    // 这里应该调用后端API验证配置
    // 暂时使用前端简单验证
    const errors: string[] = []
    const warnings: string[] = []
    
    // 验证默认提供商是否存在
    if (!configPreview.value.providers[configPreview.value.defaultProvider]) {
      errors.push(`默认提供商 "${configPreview.value.defaultProvider}" 不存在于提供商列表中`)
    }
    
    // 验证提供商配置
    Object.entries(configPreview.value.providers).forEach(([name, config]) => {
      if (config.enabled && !config.apiKey && name !== 'Mock' && name !== 'Ollama') {
        warnings.push(`提供商 "${name}" 已启用但未配置API密钥`)
      }
    })
    
    // 验证任务映射
    Object.entries(configPreview.value.taskMapping || {}).forEach(([task, provider]) => {
      if (!configPreview.value!.providers[provider]) {
        errors.push(`任务 "${task}" 映射的提供商 "${provider}" 不存在`)
      }
    })
    
    validationResult.value = {
      isValid: errors.length === 0,
      errors,
      warnings
    }
    
    if (errors.length === 0) {
      ElMessage.success('配置验证通过')
    } else {
      ElMessage.error('配置验证失败')
    }
  } catch (error: any) {
    console.error('验证配置失败:', error)
    ElMessage.error('验证配置失败')
  } finally {
    validating.value = false
  }
}

const importConfiguration = async () => {
  if (!configPreview.value) return
  
  try {
    importing.value = true
    
    // 构建导入数据
    const importData = {
      config: configPreview.value,
      options: importOptions.value,
      conflictStrategy: conflictStrategy.value
    }
    
    // 创建临时文件进行上传
    const blob = new Blob([JSON.stringify(importData, null, 2)], { type: 'application/json' })
    const file = new File([blob], 'import-config.json', { type: 'application/json' })
    
    const result = await AIProviderService.importConfiguration(file)
    importResult.value = result
    
    if (result.success) {
      ElMessage.success('配置导入成功')
      emit('imported')
    } else {
      ElMessage.error('配置导入失败: ' + result.message)
    }
  } catch (error: any) {
    console.error('导入配置失败:', error)
    ElMessage.error('导入配置失败')
    importResult.value = {
      success: false,
      message: '导入配置失败: ' + error.message
    }
  } finally {
    importing.value = false
  }
}

const handleClose = () => {
  visible.value = false
  
  // 重置状态
  configPreview.value = null
  validationResult.value = null
  importResult.value = null
  importOptions.value = ['providers', 'defaultConfig', 'taskMapping']
  conflictStrategy.value = 'merge'
  
  // 清空上传组件
  uploadRef.value?.clearFiles()
}
</script>

<style lang="scss" scoped>
.import-config {
  .upload-section {
    margin-bottom: 24px;
    
    .config-upload {
      width: 100%;
    }
  }
  
  .preview-section,
  .validation-section,
  .import-result {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }
  
  .providers-preview,
  .import-options,
  .conflict-handling {
    margin-top: 16px;
    
    h4 {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }
    
    .provider-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .provider-tag {
        font-size: 12px;
      }
    }
  }
  
  .validation-errors,
  .validation-warnings,
  .import-errors {
    margin-top: 12px;
    
    h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      .error-item {
        color: var(--el-color-danger);
        margin-bottom: 4px;
      }
      
      .warning-item {
        color: var(--el-color-warning);
        margin-bottom: 4px;
      }
    }
  }
  
  .imported-providers {
    margin-top: 12px;
    
    h4 {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
