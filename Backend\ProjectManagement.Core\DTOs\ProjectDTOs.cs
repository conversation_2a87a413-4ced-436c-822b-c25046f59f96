namespace ProjectManagement.Core.DTOs;

/// <summary>
/// 项目摘要DTO
/// </summary>
public class ProjectSummaryDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目编号
    /// </summary>
    public string ProjectCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public string Priority { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public int Progress { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 项目所有者姓名
    /// </summary>
    public string OwnerName { get; set; } = string.Empty;
}

/// <summary>
/// 项目详情DTO
/// </summary>
public class ProjectDetailDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目编号
    /// </summary>
    public string ProjectCode { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public string Priority { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public int Progress { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 预估工时
    /// </summary>
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 实际工时
    /// </summary>
    public decimal? ActualHours { get; set; }

    /// <summary>
    /// 预算
    /// </summary>
    public decimal? Budget { get; set; }

    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechnologyStack { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 项目所有者ID
    /// </summary>
    public int OwnerId { get; set; }

    /// <summary>
    /// 项目所有者姓名
    /// </summary>
    public string OwnerName { get; set; } = string.Empty;
}

/// <summary>
/// 创建项目请求DTO
/// </summary>
public class CreateProjectRequestDto
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string? Priority { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 预估工时
    /// </summary>
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 预算
    /// </summary>
    public decimal? Budget { get; set; }

    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechnologyStack { get; set; }
}

/// <summary>
/// 更新项目请求DTO
/// </summary>
public class UpdateProjectRequestDto
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string? Priority { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 预估工时
    /// </summary>
    public decimal? EstimatedHours { get; set; }

    /// <summary>
    /// 实际工时
    /// </summary>
    public decimal? ActualHours { get; set; }

    /// <summary>
    /// 预算
    /// </summary>
    public decimal? Budget { get; set; }

    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechnologyStack { get; set; }
}

/// <summary>
/// 更新项目状态请求DTO
/// </summary>
public class UpdateProjectStatusRequestDto
{
    /// <summary>
    /// 新状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比
    /// </summary>
    public int? Progress { get; set; }
}

/// <summary>
/// 项目统计信息DTO
/// </summary>
public class ProjectStatisticsDto
{
    /// <summary>
    /// 总项目数
    /// </summary>
    public int TotalProjects { get; set; }

    /// <summary>
    /// 活跃项目数
    /// </summary>
    public int ActiveProjects { get; set; }

    /// <summary>
    /// 已完成项目数
    /// </summary>
    public int CompletedProjects { get; set; }

    /// <summary>
    /// 暂停项目数
    /// </summary>
    public int PausedProjects { get; set; }

    /// <summary>
    /// 平均完成时间（天）
    /// </summary>
    public double AverageCompletionDays { get; set; }

    /// <summary>
    /// 总预算
    /// </summary>
    public decimal TotalBudget { get; set; }

    /// <summary>
    /// 已使用预算
    /// </summary>
    public decimal UsedBudget { get; set; }
}

/// <summary>
/// 基础分页查询DTO
/// </summary>
public class PageQueryDto
{
    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 每页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? SortBy { get; set; }

    /// <summary>
    /// 排序方向（asc/desc）
    /// </summary>
    public string? SortDirection { get; set; } = "desc";
}

/// <summary>
/// 分页结果DTO
/// </summary>
public class PagedResultDto<T>
{
    /// <summary>
    /// 数据项
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// 每页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;
}
