graph TD
    A[用户描述需求] --> B[AI语言理解]
    B --> C{需求是否清晰?}
    C -->|否| D[AI提出澄清问题]
    D --> E[用户补充说明]
    E --> B
    C -->|是| F[提取功能需求]
    F --> G[提取非功能需求]
    G --> H[生成用户故事]
    H --> I[定义验收标准]
    I --> J[生成需求规格书]
    J --> K[需求确认]
    K --> L{用户确认?}
    L -->|否| M[修改需求]
    M --> F
    L -->|是| N[需求锁定]
    N --> O[进入下一阶段]
    
    %% 样式定义
    classDef userStyle fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef aiStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef outputStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef decisionStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    %% 应用样式
    class A,E userStyle
    class B,D aiStyle
    class F,G,H,I,M processStyle
    class J,N,O outputStyle
    class C,L decisionStyle
