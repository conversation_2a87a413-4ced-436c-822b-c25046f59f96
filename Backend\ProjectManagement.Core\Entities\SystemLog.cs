using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 系统日志实体 - 对应DatabaseSchema.sql中的SystemLogs表
/// 功能: 记录系统运行过程中的各种日志信息
/// 支持: 多级别日志、组件分类、异常记录、数据追踪
/// </summary>
[SugarTable("SystemLogs", "系统运行日志管理表")]
public class SystemLog
{
    /// <summary>
    /// 日志记录唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "日志记录唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 关联的项目ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的项目ID，可为空")]
    public int? ProjectId { get; set; }

    /// <summary>
    /// 关联的用户ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的用户ID，可为空")]
    public int? UserId { get; set; }

    /// <summary>
    /// 日志级别: Info(信息), Warning(警告), Error(错误), Debug(调试), BuildError(编译错误)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "日志级别")]
    public string LogLevel { get; set; } = string.Empty;

    /// <summary>
    /// 组件名称: AI Service(AI服务), Code Generator(代码生成器), Test Runner(测试运行器)等
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "组件名称")]
    public string? Component { get; set; }

    /// <summary>
    /// 日志消息内容
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "日志消息内容")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息（如果有）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "异常信息")]
    public string? Exception { get; set; }

    /// <summary>
    /// 附加数据，JSON格式存储
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "附加数据，JSON格式存储")]
    public string? AdditionalData { get; set; }

    /// <summary>
    /// 日志创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "日志创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的用户 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }
}
