-- =============================================
-- 迁移脚本: 添加自动化任务队列表
-- 版本: 005
-- 创建时间: 2025-06-22
-- 描述: 创建AutomationTasks表，用于统一的任务调度和分发系统
-- =============================================

USE ProjectManagementAI;
GO

-- 检查表是否已存在
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND type in (N'U'))
BEGIN
    PRINT '开始创建AutomationTasks表...';

    -- =============================================
    -- 创建自动化任务队列表
    -- =============================================
    CREATE TABLE AutomationTasks (
        Id int IDENTITY(1,1) NOT NULL,
        ProjectId int NOT NULL,
        SourceType varchar(50) NOT NULL,
        SourceId int NULL,
        TaskType varchar(50) NOT NULL,
        TaskName nvarchar(200) NOT NULL,
        Description nvarchar(1000) NULL,
        TaskData nvarchar(max) NULL,
        Status varchar(20) NOT NULL DEFAULT 'Pending',
        Priority varchar(20) NOT NULL DEFAULT 'Medium',
        AssignedTo varchar(100) NULL,
        StartedTime datetime2 NULL,
        CompletedTime datetime2 NULL,
        Result nvarchar(max) NULL,
        ErrorMessage nvarchar(max) NULL,
        RetryCount int NOT NULL DEFAULT 0,
        MaxRetries int NOT NULL DEFAULT 3,
        TimeoutMinutes int NULL,
        Dependencies nvarchar(max) NULL,
        Tags nvarchar(500) NULL,

        -- BaseEntity字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_AutomationTasks PRIMARY KEY (Id),
        CONSTRAINT FK_AutomationTasks_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),

        -- 状态约束
        CONSTRAINT CK_AutomationTasks_Status CHECK (Status IN ('Pending', 'Assigned', 'InProgress', 'Completed', 'Failed', 'Cancelled')),

        -- 优先级约束
        CONSTRAINT CK_AutomationTasks_Priority CHECK (Priority IN ('Low', 'Medium', 'High', 'Critical')),

        -- 任务类型约束
        CONSTRAINT CK_AutomationTasks_TaskType CHECK (TaskType IN ('CodeGeneration', 'FileOperation', 'GitOperation', 'VSCodeOperation', 'BuildOperation', 'TestExecution', 'DatabaseOperation', 'DeploymentOperation')),

        -- 来源类型约束
        CONSTRAINT CK_AutomationTasks_SourceType CHECK (SourceType IN ('DevelopmentStep', 'CodeGenerationTask', 'TestTask', 'DeploymentTask', 'Manual')),

        -- 重试次数约束
        CONSTRAINT CK_AutomationTasks_RetryCount CHECK (RetryCount >= 0 AND RetryCount <= MaxRetries),
        CONSTRAINT CK_AutomationTasks_MaxRetries CHECK (MaxRetries >= 0 AND MaxRetries <= 10),

        -- 超时时间约束
        CONSTRAINT CK_AutomationTasks_TimeoutMinutes CHECK (TimeoutMinutes IS NULL OR TimeoutMinutes > 0)
    );

    PRINT 'AutomationTasks表创建成功';
END
ELSE
BEGIN
    PRINT 'AutomationTasks表已存在，跳过创建';
END
GO

-- =============================================
-- 创建索引
-- =============================================
PRINT '开始创建索引...';

-- 项目ID索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_ProjectId')
    CREATE INDEX IX_AutomationTasks_ProjectId ON AutomationTasks(ProjectId);

-- 状态索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_Status')
    CREATE INDEX IX_AutomationTasks_Status ON AutomationTasks(Status);

-- 优先级索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_Priority')
    CREATE INDEX IX_AutomationTasks_Priority ON AutomationTasks(Priority);

-- 任务类型索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_TaskType')
    CREATE INDEX IX_AutomationTasks_TaskType ON AutomationTasks(TaskType);

-- 分配客户端索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_AssignedTo')
    CREATE INDEX IX_AutomationTasks_AssignedTo ON AutomationTasks(AssignedTo);

-- 来源类型和ID复合索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_Source')
    CREATE INDEX IX_AutomationTasks_Source ON AutomationTasks(SourceType, SourceId);

-- 创建时间索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_CreatedTime')
    CREATE INDEX IX_AutomationTasks_CreatedTime ON AutomationTasks(CreatedTime);

-- 软删除索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_IsDeleted')
    CREATE INDEX IX_AutomationTasks_IsDeleted ON AutomationTasks(IsDeleted);

-- 任务队列查询复合索引（状态+优先级+创建时间）
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[AutomationTasks]') AND name = N'IX_AutomationTasks_Queue')
    CREATE INDEX IX_AutomationTasks_Queue ON AutomationTasks(Status, Priority, CreatedTime) WHERE IsDeleted = 0;

PRINT '索引创建完成';
GO

-- =============================================
-- 添加扩展属性（表和字段注释）
-- =============================================
PRINT '开始添加扩展属性...';

-- 表注释
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('AutomationTasks') AND minor_id = 0)
    EXEC sys.sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'自动化任务队列表 - 统一的任务调度和分发系统，支持本地自动化客户端任务管理',
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = N'AutomationTasks';

-- 字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务唯一标识ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'所属项目ID，关联Projects表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'ProjectId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务来源类型: DevelopmentStep, CodeGenerationTask, TestTask, DeploymentTask, Manual', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'SourceType';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务来源记录ID，关联到具体的源表记录', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'SourceId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务类型: CodeGeneration, FileOperation, GitOperation, VSCodeOperation, BuildOperation, TestExecution, DatabaseOperation, DeploymentOperation', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'TaskType';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'TaskName';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Description';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务详细数据，JSON格式存储', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'TaskData';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务状态: Pending, Assigned, InProgress, Completed, Failed, Cancelled', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务优先级: Low, Medium, High, Critical', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Priority';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'分配给的客户端标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'AssignedTo';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务开始时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'StartedTime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务完成时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'CompletedTime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务执行结果，JSON格式存储', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Result';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'错误信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'ErrorMessage';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'重试次数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'RetryCount';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'最大重试次数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'MaxRetries';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务超时时间（分钟）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'TimeoutMinutes';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务依赖，JSON格式存储依赖的任务ID列表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Dependencies';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'任务标签，用于分类和筛选', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'AutomationTasks', @level2type = N'COLUMN', @level2name = N'Tags';

PRINT '扩展属性添加完成';
GO

-- =============================================
-- 添加审计字段外键约束
-- =============================================
PRINT '开始添加审计字段外键约束...';

-- 创建人外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AutomationTasks_CreatedBy')
    ALTER TABLE AutomationTasks
    ADD CONSTRAINT FK_AutomationTasks_CreatedBy
        FOREIGN KEY (CreatedBy) REFERENCES Users(Id);

-- 更新人外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AutomationTasks_UpdatedBy')
    ALTER TABLE AutomationTasks
    ADD CONSTRAINT FK_AutomationTasks_UpdatedBy
        FOREIGN KEY (UpdatedBy) REFERENCES Users(Id);

-- 删除人外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AutomationTasks_DeletedBy')
    ALTER TABLE AutomationTasks
    ADD CONSTRAINT FK_AutomationTasks_DeletedBy
        FOREIGN KEY (DeletedBy) REFERENCES Users(Id);

PRINT '审计字段外键约束添加完成';
GO

PRINT '==============================================';
PRINT 'AutomationTasks表迁移完成！';
PRINT '==============================================';
