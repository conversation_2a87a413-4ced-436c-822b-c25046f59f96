using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using ProjectManagement.Data.Configuration;
using ProjectManagement.API.Middleware;
using ProjectManagement.API.Hubs;
using Serilog;
using System.Text;
using System.Reflection;
using FluentValidation.AspNetCore;
using MediatR;
using AutoMapper;
using Microsoft.AspNetCore.RateLimiting;
using System.Threading.RateLimiting;
using SqlSugar;
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.HttpOverrides;

namespace ProjectManagement.API;

/// <summary>
/// AI驱动软件开发自动化系统 - API应用程序入口点
/// 功能: 配置服务、中间件、认证、授权、数据库、AI服务等
/// 支持: JWT认证、SignalR实时通信、Swagger文档、限流、CORS等
/// </summary>
public class Program
{
    public static void Main(string[] args)
    {
        // 配置Serilog日志
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                .Build())
            .CreateLogger();

        try
        {
            Log.Information("启动AI驱动软件开发自动化系统API服务");

            var builder = WebApplication.CreateBuilder(args);

            // 使用Serilog作为日志提供程序
            builder.Host.UseSerilog();

            // 配置服务
            ConfigureServices(builder.Services, builder.Configuration);

            var app = builder.Build();

            // 配置HTTP请求管道
            ConfigurePipeline(app);

            app.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 配置依赖注入服务
    /// </summary>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 配置ForwardedHeaders以支持IIS子应用程序
        services.Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
        });

        // 基础服务配置
        services.AddControllers()
            .AddFluentValidation(fv => fv.RegisterValidatorsFromAssemblyContaining<Program>())
            .AddJsonOptions(options =>
            {
                // 配置JSON序列化选项
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.WriteIndented = true;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
            });

        services.AddEndpointsApiExplorer();

        // 配置请求超时
        services.Configure<IISServerOptions>(options =>
        {
            options.MaxRequestBodySize = 100 * 1024 * 1024; // 100MB
        });

        // 配置Kestrel服务器选项
        services.Configure<Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestBodySize = 100 * 1024 * 1024; // 100MB
            options.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(5); // 5分钟
            options.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(5); // 5分钟
        });

        // Swagger配置
        ConfigureSwagger(services);

        // 数据库配置
        ConfigureDatabase(services, configuration);

        // 认证和授权配置
        ConfigureAuthentication(services, configuration);

        // CORS配置
        ConfigureCors(services, configuration);

        // 限流配置
        ConfigureRateLimiting(services, configuration);

        // SignalR配置
        services.AddSignalR();

        // Redis缓存配置
        ConfigureRedis(services, configuration);

        // AutoMapper配置
        services.AddAutoMapper(typeof(Program));

        // MediatR配置
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        // 健康检查
        services.AddHealthChecks();
            // .AddSqlServer(configuration.GetConnectionString("DefaultConnection")!) // 需要安装 AspNetCore.HealthChecks.SqlServer 包
            // .AddRedis(configuration.GetConnectionString("RedisConnection")!); // 需要安装 AspNetCore.HealthChecks.Redis 包

        // 应用程序服务注册
        RegisterApplicationServices(services, configuration);
    }

    /// <summary>
    /// 配置Swagger文档
    /// </summary>
    private static void ConfigureSwagger(IServiceCollection services)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "AI驱动软件开发自动化系统 API",
                Version = "v1.0",
                Description = "提供完整的AI驱动软件开发自动化功能，包括需求分析、代码生成、测试、部署等",
                Contact = new OpenApiContact
                {
                    Name = "开发团队",
                    Email = "<EMAIL>"
                }
            });

            // JWT认证配置
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT授权头使用Bearer方案。示例: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });

            // 包含XML注释
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }
        });
    }

    /// <summary>
    /// 配置数据库服务
    /// </summary>
    private static void ConfigureDatabase(IServiceCollection services, IConfiguration configuration)
    {
        // SqlSugar配置 - 使用Scoped避免连接冲突
        services.AddScoped<ISqlSugarClient>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<Program>>();
            return SqlSugarConfig.CreateSqlSugarClient(configuration, logger);
        });

        // 数据访问层服务注册
        services.AddScoped<ProjectManagement.Core.Interfaces.IUserRepository, ProjectManagement.Data.Repositories.UserRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IProjectRepository, ProjectManagement.Data.Repositories.ProjectRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IUserTaskMappingRepository, ProjectManagement.Data.Repositories.UserTaskMappingRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRequirementConversationRepository, ProjectManagement.Data.Repositories.RequirementConversationRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRequirementDocumentRepository, ProjectManagement.Data.Repositories.RequirementDocumentRepository>();
        services.AddScoped<ProjectManagement.Data.Repositories.IAIModelConfigurationRepository, ProjectManagement.Data.Repositories.AIModelConfigurationRepository>();
        services.AddScoped<ProjectManagement.Data.Repositories.IUserAIConfigurationRepository, ProjectManagement.Data.Repositories.UserAIConfigurationRepository>();

        // Prompt管理相关仓储注册
        services.AddScoped<ProjectManagement.Data.Repositories.IPromptTemplateRepository, ProjectManagement.Data.Repositories.PromptTemplateRepository>();
        services.AddScoped<ProjectManagement.Data.Repositories.IPromptCategoryRepository, ProjectManagement.Data.Repositories.PromptCategoryRepository>();

        // 通用仓储注册（用于Prompt相关实体）
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.PromptUsageStats>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.PromptUsageStats>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.PromptRating>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.PromptRating>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.UserPromptPreference>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.UserPromptPreference>>();

        // 开发步骤相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.IDevelopmentStepRepository, ProjectManagement.Data.Repositories.DevelopmentStepRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.StepDependency>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.StepDependency>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.StepExecutionHistory>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.StepExecutionHistory>>();

        // 自动化任务相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.IAutomationTaskRepository, ProjectManagement.Data.Repositories.AutomationTaskRepository>();

        // 编码任务相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.ICodingTaskRepository, ProjectManagement.Data.Repositories.CodingTaskRepository>();

        // UI自动化模板相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.ICustomTemplateRepository, ProjectManagement.Data.Repositories.CustomTemplateRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.ITemplateSequenceRepository, ProjectManagement.Data.Repositories.TemplateSequenceRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.ITemplateStepRepository, ProjectManagement.Data.Repositories.TemplateStepRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IExecutionLogRepository, ProjectManagement.Data.Repositories.ExecutionLogRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IUIActionTypeRepository, ProjectManagement.Data.Repositories.UIActionTypeRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IFlowControlTypeRepository, ProjectManagement.Data.Repositories.FlowControlTypeRepository>();
        services.AddScoped<ProjectManagement.Data.Repositories.ICustomTemplateCategoryRepository, ProjectManagement.Data.Repositories.CustomTemplateCategoryRepository>();

        // 通用仓储注册（用于核心实体）
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.Project>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.Project>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.User>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.User>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.RequirementDocument>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.RequirementDocument>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.ERDiagram>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.ERDiagram>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.ContextDiagram>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.ContextDiagram>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.Prototype>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.Prototype>>();

        // VS编译错误相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.VSBuildError>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.VSBuildError>>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.VSBuildSession>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.VSBuildSession>>();

        // 问题管理相关仓储注册
        services.AddScoped<ProjectManagement.Data.Repositories.IIssueRepository, ProjectManagement.Data.Repositories.IssueRepository>();
        services.AddScoped<ProjectManagement.Core.Interfaces.IRepository<ProjectManagement.Core.Entities.IssueResolution>, ProjectManagement.Data.Repositories.BaseRepository<ProjectManagement.Core.Entities.IssueResolution>>();

        // Playwright测试相关仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.IPlaywrightScriptRepository, ProjectManagement.Data.Repositories.PlaywrightScriptRepository>();
    }

    /// <summary>
    /// 配置JWT认证
    /// </summary>
    private static void ConfigureAuthentication(IServiceCollection services, IConfiguration configuration)
    {
        var jwtSettings = configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"];
        var key = Encoding.ASCII.GetBytes(secretKey!);

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.RequireHttpsMetadata = false;
            options.SaveToken = true;
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = jwtSettings["Issuer"],
                ValidateAudience = true,
                ValidAudience = jwtSettings["Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            // SignalR JWT支持
            options.Events = new JwtBearerEvents
            {
                OnMessageReceived = context =>
                {
                    var accessToken = context.Request.Query["access_token"];
                    var path = context.HttpContext.Request.Path;
                    if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
                    {
                        context.Token = accessToken;
                    }
                    return Task.CompletedTask;
                }
            };
        });

        services.AddAuthorization();
    }

    /// <summary>
    /// 配置CORS跨域
    /// </summary>
    private static void ConfigureCors(IServiceCollection services, IConfiguration configuration)
    {
        var corsSettings = configuration.GetSection("CORS");
        var allowedOrigins = corsSettings.GetSection("AllowedOrigins").Get<string[]>() ?? Array.Empty<string>();

        services.AddCors(options =>
        {
            options.AddPolicy("DefaultPolicy", policy =>
            {
                policy.WithOrigins(allowedOrigins)
                      .AllowAnyMethod()
                      .AllowAnyHeader()
                      .AllowCredentials();
            });
        });
    }

    /// <summary>
    /// 配置限流
    /// </summary>
    private static void ConfigureRateLimiting(IServiceCollection services, IConfiguration configuration)
    {
        var rateLimitSettings = configuration.GetSection("RateLimiting");

        services.AddRateLimiter(options =>
        {
            // 通用API限流
            options.AddFixedWindowLimiter("GeneralApi", limiterOptions =>
            {
                limiterOptions.PermitLimit = rateLimitSettings.GetValue<int>("GeneralRules:PermitLimit");
                limiterOptions.Window = TimeSpan.Parse(rateLimitSettings["GeneralRules:Window"]!);
                limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
                limiterOptions.QueueLimit = rateLimitSettings.GetValue<int>("GeneralRules:QueueLimit");
            });

            // AI API限流（更严格）
            options.AddFixedWindowLimiter("AIApi", limiterOptions =>
            {
                limiterOptions.PermitLimit = rateLimitSettings.GetValue<int>("AIApiRules:PermitLimit");
                limiterOptions.Window = TimeSpan.Parse(rateLimitSettings["AIApiRules:Window"]!);
                limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
                limiterOptions.QueueLimit = rateLimitSettings.GetValue<int>("AIApiRules:QueueLimit");
            });
        });
    }

    /// <summary>
    /// 配置Redis缓存
    /// </summary>
    private static void ConfigureRedis(IServiceCollection services, IConfiguration configuration)
    {
        var redisConnection = configuration.GetConnectionString("RedisConnection");
        if (!string.IsNullOrEmpty(redisConnection))
        {
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnection;
            });
        }
    }

    /// <summary>
    /// 注册应用程序服务
    /// </summary>
    private static void RegisterApplicationServices(IServiceCollection services, IConfiguration configuration)
    {
        // AI配置
        services.Configure<ProjectManagement.AI.Models.AIConfiguration>(
            configuration.GetSection("AI"));
        services.Configure<ProjectManagement.AI.Providers.AzureOpenAIConfig>(
            configuration.GetSection("AI:Providers:Azure"));
        services.Configure<ProjectManagement.AI.Providers.OpenAIConfig>(
            configuration.GetSection("AI:Providers:OpenAI"));
        services.Configure<ProjectManagement.AI.Providers.DeepSeekConfig>(
            configuration.GetSection("AI:Providers:DeepSeek"));
        services.Configure<ProjectManagement.AI.Providers.ClaudeConfig>(
            configuration.GetSection("AI:Providers:Claude"));
        services.Configure<ProjectManagement.AI.Providers.OllamaConfig>(
            configuration.GetSection("AI:Providers:Ollama"));

        // AI提供商注册
        services.AddHttpClient<ProjectManagement.AI.Providers.AzureOpenAIProvider>();
        services.AddHttpClient<ProjectManagement.AI.Providers.OpenAIProvider>();
        services.AddHttpClient<ProjectManagement.AI.Providers.DeepSeekProvider>();
        services.AddHttpClient<ProjectManagement.AI.Providers.ClaudeProvider>();
        services.AddHttpClient<ProjectManagement.AI.Providers.OllamaProvider>();

        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.MockAIProvider>();
        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.AzureOpenAIProvider>();
        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.OpenAIProvider>();
        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.DeepSeekProvider>();
        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.ClaudeProvider>();
        services.AddSingleton<ProjectManagement.AI.Interfaces.IAIProvider, ProjectManagement.AI.Providers.OllamaProvider>();

        // AI服务注册
        services.AddScoped<ProjectManagement.AI.Interfaces.IAIService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<ProjectManagement.AI.Services.AIService>>();
            var config = provider.GetRequiredService<IOptions<ProjectManagement.AI.Models.AIConfiguration>>();
            var aiProviders = provider.GetServices<ProjectManagement.AI.Interfaces.IAIProvider>();
            var promptBuilderService = provider.GetService<ProjectManagement.AI.Services.IPromptBuilderService>();
            var promptTemplateService = provider.GetService<ProjectManagement.AI.Services.IPromptTemplateService>();
            var projectRepository = provider.GetService<ProjectManagement.Core.Interfaces.IProjectRepository>();
            var userTaskMappingRepository = provider.GetService<ProjectManagement.Core.Interfaces.IUserTaskMappingRepository>();
            var userAIConfigurationRepository = provider.GetService<ProjectManagement.Data.Repositories.IUserAIConfigurationRepository>();

            return new ProjectManagement.AI.Services.AIService(
                logger, config, aiProviders, promptBuilderService, promptTemplateService, projectRepository,
                userTaskMappingRepository, userAIConfigurationRepository);
        });
        services.AddScoped<ProjectManagement.AI.Services.ConversationContextService>();

        // 任务状态管理服务注册
        services.AddSingleton<ProjectManagement.AI.Services.ITaskStatusService, ProjectManagement.AI.Services.InMemoryTaskStatusService>();

        // Prompt工程服务注册
        services.AddScoped<ProjectManagement.AI.Services.IPromptTemplateService, ProjectManagement.AI.Services.PromptTemplateService>();
        services.AddScoped<ProjectManagement.AI.Services.IPromptBuilderService, ProjectManagement.AI.Services.PromptBuilderService>();
        services.AddScoped<ProjectManagement.AI.Services.IPromptAnalyticsService, ProjectManagement.AI.Services.PromptAnalyticsService>();

        // 需求分解服务注册
        services.AddScoped<ProjectManagement.Core.Services.IRequirementDecompositionService, ProjectManagement.AI.Services.RequirementDecompositionService>();

        // 开发步骤服务注册
        services.AddScoped<ProjectManagement.Core.Services.IDevelopmentStepService, ProjectManagement.AI.Services.DevelopmentStepService>();

        // 自动化任务服务注册
        services.AddScoped<ProjectManagement.AI.Services.AutomationTaskGeneratorService>();

        // VS编译错误服务注册
        services.AddScoped<ProjectManagement.AI.Services.VSBuildErrorService>();

        // 编译错误服务注册
        services.AddScoped<ProjectManagement.Core.Interfaces.ICompilationErrorService, ProjectManagement.Core.Services.CompilationErrorService>();

        // 高级AI服务注册
        RegisterAdvancedAIServices(services);

        // Repository注册已在ConfigureDatabase方法中完成

        // 系统参数仓储注册
        services.AddScoped<ProjectManagement.Core.Interfaces.ISystemParameterRepository, ProjectManagement.Data.Repositories.SystemParameterRepository>();

        // 加密服务注册
        services.AddScoped<ProjectManagement.Core.Services.IEncryptionService, ProjectManagement.Core.Services.AesEncryptionService>();

        // 业务服务注册
        // services.AddScoped<ProjectManagement.Core.Services.IProjectService, ProjectManagement.Core.Services.ProjectService>();

        // 其他服务将在后续添加
    }

    /// <summary>
    /// 配置HTTP请求管道
    /// </summary>
    private static void ConfigurePipeline(WebApplication app)
    {
        // 处理转发头信息（用于反向代理和IIS子应用程序）
        app.UseForwardedHeaders();

        // 配置PathBase以支持IIS子应用程序部署
        if (!app.Environment.IsDevelopment())
        {
            // 生产环境：配置PathBase以支持IIS子应用程序部署
            app.UsePathBase("/backend");
        }

        // Swagger配置 - 根据Features配置决定是否启用
        var enableSwagger = app.Configuration.GetValue<bool>("Features:EnableSwagger", true);
        if (app.Environment.IsDevelopment() || enableSwagger)
        {
            app.UseSwagger(c =>
            {
                c.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
                {
                    // 动态设置服务器URL以适应不同的部署环境
                    var pathBase = httpReq.PathBase.Value ?? "";
                    var scheme = httpReq.Scheme;
                    var host = httpReq.Host.Value;
                    swaggerDoc.Servers = new List<Microsoft.OpenApi.Models.OpenApiServer>
                    {
                        new Microsoft.OpenApi.Models.OpenApiServer { Url = $"{scheme}://{host}{pathBase}" }
                    };
                });
            });
            app.UseSwaggerUI(c =>
            {
                // 使用相对路径，让IIS自动处理路径基础
                c.SwaggerEndpoint("./v1/swagger.json", "ProjectManagement API v1");
                c.RoutePrefix = "swagger"; // 设置Swagger UI路径为 /swagger
                c.DisplayRequestDuration();
                c.EnableDeepLinking();
                c.EnableFilter();
                c.ShowExtensions();
            });
        }

        // 全局异常处理中间件
        app.UseMiddleware<ExceptionHandlingMiddleware>();

        // HTTPS重定向 - 仅在开发环境或配置启用时使用
        if (app.Environment.IsDevelopment() || app.Configuration.GetValue<bool>("Features:EnableHttpsRedirection", false))
        {
            app.UseHttpsRedirection();
        }

        // 静态文件
        app.UseStaticFiles();

        // 路由
        app.UseRouting();

        // CORS
        app.UseCors("DefaultPolicy");

        // 限流
        app.UseRateLimiter();

        // 认证和授权
        app.UseAuthentication();
        app.UseAuthorization();

        // 根路径重定向到测试页面
        app.MapGet("/", () => Results.Redirect("./test.html"));

        // 健康检查
        app.MapHealthChecks("/health");

        // SignalR Hub映射
        app.MapHub<ProjectHub>("/hubs/project");
        app.MapHub<AIProcessingHub>("/hubs/ai-processing");

        // 控制器映射
        app.MapControllers();
    }

    /// <summary>
    /// 注册高级AI服务
    /// </summary>
    private static void RegisterAdvancedAIServices(IServiceCollection services)
    {
        // Repository注册
        services.AddScoped<ProjectManagement.Data.Repositories.IKnowledgeEntityRepository, ProjectManagement.Data.Repositories.KnowledgeEntityRepository>();
        services.AddScoped<ProjectManagement.Data.Repositories.IKnowledgeRelationRepository, ProjectManagement.Data.Repositories.KnowledgeRelationRepository>();
        // 使用SqlSugar版本的DocumentVectorRepository
        services.AddScoped<ProjectManagement.Data.Repositories.IDocumentVectorRepository, ProjectManagement.Data.Repositories.DocumentVectorRepository>();

        // LangChain服务
        services.AddScoped<ProjectManagement.AI.Services.LangChainService>();

        // 知识图谱服务
        services.AddScoped<ProjectManagement.AI.Services.KnowledgeGraphService>();

        // 向量搜索服务
        services.AddScoped<ProjectManagement.AI.Services.VectorSearchService>();

        // Agent编排服务
        services.AddScoped<ProjectManagement.AI.Services.AgentOrchestrationService>();
    }
}
