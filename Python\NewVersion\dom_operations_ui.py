#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOM操作UI界面
集成VSCode DOM自动化功能到主界面
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import time
from pathlib import Path

# 添加dom目录到路径
dom_path = Path(__file__).parent / "dom"
sys.path.append(str(dom_path))

try:
    from copilot_vscode_script import CopilotVsCodeScript
    DOM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ DOM模块导入失败: {e}")
    DOM_AVAILABLE = False


class DOMOperationsUI:
    """DOM操作UI界面类"""
    
    def __init__(self, parent, api_client, main_ui=None):
        """
        初始化DOM操作UI

        Args:
            parent: 父容器
            api_client: API客户端实例
            main_ui: 主UI实例（用于获取编译错误）
        """
        self.parent = parent
        self.api_client = api_client
        self.main_ui = main_ui
        self.dom_script = None
        self.is_connected = False

        # 获取主窗口引用
        self.main_window = self._get_main_window()

        # 创建界面
        self.create_interface()

        print("DOM操作UI初始化完成")

    def _get_main_window(self):
        """获取主窗口引用"""
        widget = self.parent
        while widget:
            if isinstance(widget, tk.Tk):
                return widget
            widget = widget.master
        return self.parent  # 如果找不到，返回parent
    
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="VSCode DOM自动化操作", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 创建选项卡
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """创建选项卡"""
        # 创建Notebook
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 连接管理选项卡
        self.create_connection_tab()
        
        # Copilot操作选项卡
        self.create_copilot_tab()
        
        # 自定义脚本选项卡
        self.create_script_tab()
        
        # 日志选项卡
        self.create_log_tab()
    
    def create_connection_tab(self):
        """创建连接管理选项卡"""
        conn_frame = ttk.Frame(self.notebook)
        self.notebook.add(conn_frame, text="🔗 连接管理")
        
        # 连接状态区域
        status_frame = ttk.LabelFrame(conn_frame, text="连接状态", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态显示
        self.connection_status_label = ttk.Label(status_frame, text="未连接", style='Error.TLabel')
        self.connection_status_label.pack(pady=5)
        
        # 连接按钮
        button_frame = ttk.Frame(status_frame)
        button_frame.pack(pady=10)
        
        self.connect_btn = ttk.Button(button_frame, text="连接VSCode", command=self.connect_vscode)
        self.connect_btn.pack(side=tk.LEFT, padx=5)
        
        self.disconnect_btn = ttk.Button(button_frame, text="断开连接", command=self.disconnect_vscode, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=5)

        # 恢复UI界面按钮
        self.restore_ui_btn = ttk.Button(button_frame, text="恢复UI界面", command=self.restore_ui_interface)
        self.restore_ui_btn.pack(side=tk.LEFT, padx=5)

        # 启动VSCode调试模式按钮
        self.start_debug_btn = ttk.Button(button_frame, text="启动VSCode调试模式", command=self.start_vscode_debug)
        self.start_debug_btn.pack(side=tk.LEFT, padx=5)
        
        # 连接信息
        info_frame = ttk.LabelFrame(conn_frame, text="连接信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = """
VSCode DOM自动化连接说明：

1. 启动VSCode调试模式：
   - 点击"启动VSCode调试模式"按钮
   - 或手动运行：code --remote-debugging-port=9222

2. 在VSCode中打开Copilot Chat面板：
   - 按 Ctrl+Shift+P 打开命令面板
   - 输入 "Copilot: Open Chat" 并执行

3. 点击"连接VSCode"按钮建立连接

4. 连接成功后可以使用各种DOM操作功能

注意：确保VSCode已安装Copilot插件并已登录
        """
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(fill=tk.BOTH, expand=True)
    
    def create_copilot_tab(self):
        """创建Copilot操作选项卡"""
        copilot_frame = ttk.Frame(self.notebook)
        self.notebook.add(copilot_frame, text="🤖 Copilot操作")
        
        # 快速操作区域
        quick_frame = ttk.LabelFrame(copilot_frame, text="快速操作", padding=10)
        quick_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 按钮行1
        btn_row1 = ttk.Frame(quick_frame)
        btn_row1.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_row1, text="打开Copilot聊天", command=self.open_copilot_chat).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_row1, text="开始新会话", command=lambda: self.confirm_new_session(True)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_row1, text="继续当前会话", command=lambda: self.confirm_new_session(False)).pack(side=tk.LEFT, padx=5)
        
        # 按钮行2
        btn_row2 = ttk.Frame(quick_frame)
        btn_row2.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_row2, text="聚焦输入框", command=self.focus_input).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_row2, text="清空文本", command=self.clear_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_row2, text="发送消息", command=self.send_message).pack(side=tk.LEFT, padx=5)
        
        # 消息输入区域
        message_frame = ttk.LabelFrame(copilot_frame, text="消息输入", padding=10)
        message_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 文本输入框
        self.message_text = scrolledtext.ScrolledText(message_frame, height=6, wrap=tk.WORD)
        self.message_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 输入和发送按钮
        input_btn_frame = ttk.Frame(message_frame)
        input_btn_frame.pack(fill=tk.X)
        
        ttk.Button(input_btn_frame, text="输入文本", command=self.input_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(input_btn_frame, text="输入并发送", command=self.input_and_send).pack(side=tk.LEFT, padx=5)
        
        # 图片操作
        image_frame = ttk.Frame(input_btn_frame)
        image_frame.pack(side=tk.RIGHT)
        
        ttk.Button(image_frame, text="选择图片", command=self.select_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(image_frame, text="粘贴图片", command=self.paste_image).pack(side=tk.LEFT, padx=5)
        
        # 图片路径显示
        self.image_path_var = tk.StringVar()
        self.image_path_label = ttk.Label(message_frame, textvariable=self.image_path_var, foreground='blue')
        self.image_path_label.pack(fill=tk.X, pady=(5, 0))
    
    def create_script_tab(self):
        """创建自定义脚本选项卡"""
        script_frame = ttk.Frame(self.notebook)
        self.notebook.add(script_frame, text="📝 自定义脚本")
        
        # JavaScript代码输入
        js_frame = ttk.LabelFrame(script_frame, text="JavaScript代码", padding=10)
        js_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.js_text = scrolledtext.ScrolledText(js_frame, height=15, wrap=tk.WORD)
        self.js_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 预设一些示例代码
        example_js = """// 示例：获取VSCode状态
return {
    title: document.title,
    url: window.location.href,
    copilotPanel: !!document.querySelector('#workbench\\.panel\\.chat'),
    monacoEditors: document.querySelectorAll('.monaco-editor').length,
    timestamp: new Date().toISOString()
};"""
        self.js_text.insert(tk.END, example_js)
        
        # 执行按钮
        js_btn_frame = ttk.Frame(js_frame)
        js_btn_frame.pack(fill=tk.X)
        
        ttk.Button(js_btn_frame, text="执行JavaScript", command=self.execute_javascript).pack(side=tk.LEFT, padx=5)
        ttk.Button(js_btn_frame, text="清空代码", command=lambda: self.js_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=5)
        
        # 常用脚本按钮
        common_frame = ttk.Frame(js_btn_frame)
        common_frame.pack(side=tk.RIGHT)
        
        ttk.Button(common_frame, text="获取VSCode状态", command=self.load_vscode_status_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(common_frame, text="查找Copilot元素", command=self.load_copilot_elements_script).pack(side=tk.LEFT, padx=2)
    
    def create_log_tab(self):
        """创建日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📋 日志")
        
        # 日志显示区域
        log_display_frame = ttk.LabelFrame(log_frame, text="操作日志", padding=10)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_display_frame, height=20, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_display_frame)
        log_btn_frame.pack(fill=tk.X)
        
        ttk.Button(log_btn_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_btn_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_btn_frame, text="自动滚动", command=self.toggle_auto_scroll).pack(side=tk.LEFT, padx=5)
        
        self.auto_scroll = True
    
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        
        # 根据日志级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")
        
        self.log_text.config(state=tk.DISABLED)
        
        # 自动滚动到底部
        if self.auto_scroll:
            self.log_text.see(tk.END)
    
    # DOM操作方法
    def connect_vscode(self):
        """连接到VSCode"""
        if not DOM_AVAILABLE:
            messagebox.showerror("错误", "DOM模块不可用，请检查依赖")
            return
        
        def connect_thread():
            try:
                self.log_message("正在连接到VSCode...")
                # 注意：这里没有parent_ui引用，某些功能可能不可用
                self.dom_script = CopilotVsCodeScript()
                
                if self.dom_script.connect():
                    self.is_connected = True
                    self.connection_status_label.config(text="已连接", style='Success.TLabel')
                    self.connect_btn.config(state=tk.DISABLED)
                    self.disconnect_btn.config(state=tk.NORMAL)
                    self.log_message("VSCode连接成功", "SUCCESS")

                    self.log_message("✅ VSCode连接成功，编译错误检测功能已启用", "SUCCESS")
                else:
                    self.log_message("VSCode连接失败", "ERROR")
                    messagebox.showerror("连接失败", "无法连接到VSCode，请确保VSCode已启动调试模式")
            except Exception as e:
                self.log_message(f"连接异常: {e}", "ERROR")
                messagebox.showerror("连接异常", f"连接过程中发生异常: {e}")
        
        threading.Thread(target=connect_thread, daemon=True).start()

    def restore_ui_interface(self):
        """恢复UI界面到前台"""
        try:
            self.main_window.deiconify()
            self.main_window.lift()
            self.main_window.focus_force()
            self.log_message("UI界面已恢复到前台", "SUCCESS")
        except Exception as e:
            self.log_message(f"恢复UI界面失败: {e}", "ERROR")

    def disconnect_vscode(self):
        """断开VSCode连接"""
        if self.dom_script and self.is_connected:
            try:
                self.dom_script.disconnect()
                self.is_connected = False
                self.connection_status_label.config(text="未连接", style='Error.TLabel')
                self.connect_btn.config(state=tk.NORMAL)
                self.disconnect_btn.config(state=tk.DISABLED)
                self.log_message("VSCode连接已断开", "SUCCESS")
            except Exception as e:
                self.log_message(f"断开连接异常: {e}", "ERROR")

    def start_vscode_debug(self):
        """启动VSCode调试模式"""
        try:
            import subprocess
            bat_file = Path(__file__).parent / "dom" / "调试模式启动vscdoe.bat"
            if bat_file.exists():
                subprocess.Popen([str(bat_file)], shell=True)
                self.log_message("正在启动VSCode调试模式...", "INFO")
                messagebox.showinfo("提示", "正在启动VSCode调试模式，请稍等片刻后点击连接")
            else:
                # 手动启动命令
                subprocess.Popen(['code', '--remote-debugging-port=9222'], shell=True)
                self.log_message("使用命令启动VSCode调试模式", "INFO")
        except Exception as e:
            self.log_message(f"启动VSCode调试模式失败: {e}", "ERROR")
            messagebox.showerror("启动失败", f"无法启动VSCode调试模式: {e}")

    def check_connection(self):
        """检查连接状态"""
        if not self.is_connected or not self.dom_script:
            messagebox.showwarning("未连接", "请先连接到VSCode")
            return False
        return True

    def open_copilot_chat(self):
        """打开Copilot聊天"""
        if not self.check_connection():
            return

        def operation_thread():
            try:
                self.log_message("正在打开Copilot聊天...")
                result = self.dom_script.open_colipot_chat()
                if result:
                    self.log_message("Copilot聊天已打开", "SUCCESS")
                else:
                    self.log_message("打开Copilot聊天失败", "ERROR")
            except Exception as e:
                self.log_message(f"打开Copilot聊天异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def confirm_new_session(self, is_new: bool):
        """确认是否新会话"""
        if not self.check_connection():
            return

        def operation_thread():
            try:
                action = "开始新会话" if is_new else "继续当前会话"
                self.log_message(f"正在{action}...")
                self.dom_script.confirmIsNewSession(is_new)
                self.log_message(f"{action}完成", "SUCCESS")
            except Exception as e:
                self.log_message(f"{action}异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def focus_input(self):
        """聚焦输入框"""
        if not self.check_connection():
            return

        def operation_thread():
            try:
                self.log_message("正在聚焦输入框...")
                self.log_message("💡 请确保VSCode窗口在前台可见", "INFO")
                result = self.dom_script.focusTxtInput()
                if result:
                    self.log_message("输入框已聚焦", "SUCCESS")
                else:
                    self.log_message("聚焦输入框失败", "ERROR")
            except Exception as e:
                self.log_message(f"聚焦输入框异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def clear_text(self):
        """清空文本"""
        if not self.check_connection():
            return

        def operation_thread():
            try:
                self.log_message("正在清空文本...")
                result = self.dom_script.clearText()
                if result:
                    self.log_message("文本已清空", "SUCCESS")
                else:
                    self.log_message("清空文本失败", "ERROR")
            except Exception as e:
                self.log_message(f"清空文本异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def input_text(self):
        """输入文本"""
        if not self.check_connection():
            return

        text = self.message_text.get(1.0, tk.END).strip()
        if not text:
            messagebox.showwarning("警告", "请输入要发送的文本")
            return

        def operation_thread():
            try:
                self.log_message(f"正在输入文本: {text[:50]}...")
                self.log_message("💡 UI界面将在2秒后自动最小化", "INFO")
                self.log_message("💡 系统将自动切换到VSCode并执行输入", "INFO")

                # 等待2秒让用户看到提示
                time.sleep(2.0)

                # 将UI界面放置后台，避免拦截输入
                def minimize_ui():
                    try:
                        self.main_window.iconify()
                        self.log_message("UI界面已放置后台，正在切换到VSCode", "INFO")
                    except Exception as e:
                        self.log_message(f"放置后台失败: {e}", "ERROR")

                self.main_window.after(0, minimize_ui)
                time.sleep(0.5)  # 短暂等待确保最小化完成

                # 自动输入文本（包含切换到VSCode的逻辑）
                result = self.dom_script.inputTextAdvanced(text)

                if result:
                    self.log_message("文本输入完成", "SUCCESS")
                else:
                    self.log_message("文本输入失败", "ERROR")

                self.log_message("💡 UI界面保持在后台，可手动恢复", "INFO")

            except Exception as e:
                self.log_message(f"文本输入异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def send_message(self):
        """发送消息"""
        if not self.check_connection():
            return

        def operation_thread():
            try:
                self.log_message("正在发送消息...")
                self.log_message("💡 UI界面将在2秒后自动最小化并发送", "INFO")

                # 等待2秒让用户看到提示
                time.sleep(2.0)

                # 将UI界面放置后台
                def minimize_ui():
                    try:
                        self.main_window.iconify()
                        self.log_message("UI界面已放置后台，正在发送", "INFO")
                    except Exception as e:
                        self.log_message(f"放置后台失败: {e}", "ERROR")

                self.main_window.after(0, minimize_ui)
                time.sleep(0.5)  # 短暂等待确保最小化完成

                result = self.dom_script.clickMsgBtn()
                if result:
                    self.log_message("消息已发送", "SUCCESS")
                else:
                    self.log_message("发送消息失败", "ERROR")

                self.log_message("💡 UI界面保持在后台，可手动恢复", "INFO")

            except Exception as e:
                self.log_message(f"发送消息异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def input_and_send(self):
        """输入文本并发送"""
        if not self.check_connection():
            return

        text = self.message_text.get(1.0, tk.END).strip()
        if not text:
            messagebox.showwarning("警告", "请输入要发送的文本")
            return

        def operation_thread():
            try:
                self.log_message(f"正在输入并发送: {text[:50]}...")
                self.log_message("💡 UI界面将在2秒后自动最小化", "INFO")
                self.log_message("💡 系统将自动切换到VSCode并执行操作", "INFO")

                # 等待2秒让用户看到提示
                time.sleep(2.0)

                # 将UI界面放置后台，避免拦截输入
                def minimize_ui():
                    try:
                        self.main_window.iconify()
                        self.log_message("UI界面已放置后台，正在切换到VSCode", "INFO")
                    except Exception as e:
                        self.log_message(f"放置后台失败: {e}", "ERROR")

                def clear_input():
                    try:
                        self.message_text.delete(1.0, tk.END)
                    except:
                        pass

                self.main_window.after(0, minimize_ui)
                time.sleep(0.5)  # 短暂等待确保最小化完成

                # 清空文本（使用键盘操作）
                self.dom_script.clearText()
                time.sleep(0.3)

                # 自动输入文本（包含切换到VSCode的逻辑）
                result1 = self.dom_script.inputTextAdvanced(text)
                time.sleep(0.5)

                # 发送消息
                result2 = self.dom_script.clickMsgBtn()

                if result1 and result2:
                    self.log_message("文本输入并发送完成", "SUCCESS")
                    # 清空输入框
                    self.main_window.after(0, clear_input)
                else:
                    self.log_message("输入并发送失败", "ERROR")

                self.log_message("💡 UI界面保持在后台，可手动恢复", "INFO")

            except Exception as e:
                self.log_message(f"输入并发送异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def select_image(self):
        """选择图片文件"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.image_path_var.set(f"已选择图片: {file_path}")
            self.selected_image_path = file_path
            self.log_message(f"已选择图片: {file_path}", "INFO")

    def paste_image(self):
        """粘贴图片"""
        if not self.check_connection():
            return

        if not hasattr(self, 'selected_image_path') or not self.selected_image_path:
            messagebox.showwarning("警告", "请先选择图片文件")
            return

        def operation_thread():
            try:
                self.log_message(f"正在粘贴图片: {self.selected_image_path}")
                result = self.dom_script.copy_and_paste_image(self.selected_image_path)
                if result:
                    self.log_message("图片粘贴完成", "SUCCESS")
                else:
                    self.log_message("图片粘贴失败", "ERROR")
            except Exception as e:
                self.log_message(f"图片粘贴异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def execute_javascript(self):
        """执行JavaScript代码"""
        if not self.check_connection():
            return

        js_code = self.js_text.get(1.0, tk.END).strip()
        if not js_code:
            messagebox.showwarning("警告", "请输入JavaScript代码")
            return

        def operation_thread():
            try:
                self.log_message("正在执行JavaScript代码...")
                result = self.dom_script.execute_js(js_code)

                if result is not None:
                    self.log_message("JavaScript执行成功", "SUCCESS")
                    self.log_message(f"执行结果: {result}", "INFO")

                    # 在弹窗中显示结果
                    result_window = tk.Toplevel(self.parent)
                    result_window.title("JavaScript执行结果")
                    result_window.geometry("600x400")

                    result_text = scrolledtext.ScrolledText(result_window, wrap=tk.WORD)
                    result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

                    import json
                    try:
                        formatted_result = json.dumps(result, indent=2, ensure_ascii=False)
                    except:
                        formatted_result = str(result)

                    result_text.insert(tk.END, formatted_result)
                    result_text.config(state=tk.DISABLED)
                else:
                    self.log_message("JavaScript执行失败", "ERROR")
            except Exception as e:
                self.log_message(f"JavaScript执行异常: {e}", "ERROR")

        threading.Thread(target=operation_thread, daemon=True).start()

    def load_vscode_status_script(self):
        """加载VSCode状态检查脚本"""
        script = """// 获取VSCode状态信息
return {
    title: document.title,
    url: window.location.href,
    copilotPanel: !!document.querySelector('#workbench\\.panel\\.chat'),
    copilotPanelVisible: (() => {
        const panel = document.querySelector('#workbench\\.parts\\.auxiliarybar');
        if (panel) {
            const parent = panel.parentElement;
            const style = parent.getAttribute('style') || '';
            const widthMatch = style.match(/width:\\s*(\\d+)px/);
            return widthMatch ? parseInt(widthMatch[1]) > 0 : false;
        }
        return false;
    })(),
    monacoEditors: document.querySelectorAll('.monaco-editor').length,
    textareas: document.querySelectorAll('textarea').length,
    chatMessages: document.querySelectorAll('.monaco-list-row').length,
    activeEditor: !!document.querySelector('.monaco-editor.focused'),
    sidebarVisible: !!document.querySelector('.sidebar.visible'),
    timestamp: new Date().toISOString()
};"""

        self.js_text.delete(1.0, tk.END)
        self.js_text.insert(tk.END, script)
        self.log_message("已加载VSCode状态检查脚本", "INFO")

    def load_copilot_elements_script(self):
        """加载Copilot元素查找脚本"""
        script = """// 查找Copilot相关元素
const elements = {
    copilotIcon: document.querySelector('.action-label.codicon.codicon-copilot'),
    copilotPanel: document.querySelector('#workbench\\.parts\\.auxiliarybar'),
    chatInput: document.querySelector('textarea[data-mprt="7"].inputarea'),
    sendButton: document.querySelector('a.action-label.codicon.codicon-send'),
    newSessionButton: document.querySelector('.action-label.codicon.codicon-plus'),
    chatMessages: document.querySelectorAll('.monaco-list-row')
};

const result = {};
for (const [key, element] of Object.entries(elements)) {
    if (element) {
        if (key === 'chatMessages') {
            result[key] = {
                found: true,
                count: element.length,
                elements: Array.from(element).slice(0, 3).map(el => ({
                    text: el.textContent?.substring(0, 100) || '',
                    className: el.className
                }))
            };
        } else {
            result[key] = {
                found: true,
                tagName: element.tagName,
                className: element.className,
                id: element.id || 'no id',
                textContent: element.textContent?.substring(0, 50) || 'no text',
                visible: element.offsetParent !== null
            };
        }
    } else {
        result[key] = { found: false };
    }
}

return result;"""

        self.js_text.delete(1.0, tk.END)
        self.js_text.insert(tk.END, script)
        self.log_message("已加载Copilot元素查找脚本", "INFO")

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("日志已清空", "INFO")

    def save_log(self):
        """保存日志"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".log",
                filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if file_path:
                log_content = self.log_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"日志已保存到: {file_path}", "SUCCESS")
        except Exception as e:
            self.log_message(f"保存日志失败: {e}", "ERROR")

    def toggle_auto_scroll(self):
        """切换自动滚动"""
        self.auto_scroll = not self.auto_scroll
        status = "开启" if self.auto_scroll else "关闭"
        self.log_message(f"自动滚动已{status}", "INFO")


def main():
    """测试DOM操作UI"""
    root = tk.Tk()
    root.title("DOM操作UI测试")
    root.geometry("1000x700")

    # 创建一个模拟的API客户端
    class MockAPIClient:
        pass

    api_client = MockAPIClient()

    # 创建DOM操作UI
    dom_ui = DOMOperationsUI(root, api_client)

    root.mainloop()


if __name__ == "__main__":
    main()
