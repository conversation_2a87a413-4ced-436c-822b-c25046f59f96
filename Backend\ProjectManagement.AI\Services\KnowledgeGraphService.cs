using Microsoft.Extensions.Logging;
using ProjectManagement.Core.DTOs.KnowledgeGraph;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// 知识图谱服务 - 管理项目相关的实体关系和知识推理
    /// </summary>
    public class KnowledgeGraphService
    {
        private readonly ILogger<KnowledgeGraphService> _logger;
        private readonly IKnowledgeEntityRepository _entityRepository;
        private readonly IKnowledgeRelationRepository _relationRepository;

        public KnowledgeGraphService(
            ILogger<KnowledgeGraphService> logger,
            IKnowledgeEntityRepository entityRepository,
            IKnowledgeRelationRepository relationRepository)
        {
            _logger = logger;
            _entityRepository = entityRepository;
            _relationRepository = relationRepository;
        }

        /// <summary>
        /// 创建知识实体
        /// </summary>
        public async Task<KnowledgeEntity> CreateEntityAsync(string type, string name, Dictionary<string, object> properties)
        {
            _logger.LogInformation("创建知识实体: {Type} - {Name}", type, name);

            var entity = new KnowledgeEntity
            {
                Type = type,
                Name = name,
                Properties = properties,
                CreatedAt = DateTime.UtcNow
            };

            return await _entityRepository.CreateAsync(entity);
        }

        /// <summary>
        /// 创建实体关系
        /// </summary>
        public async Task<KnowledgeRelation> CreateRelationAsync(int fromEntityId, int toEntityId, string relationType, float weight = 1.0f)
        {
            _logger.LogInformation("创建实体关系: {FromId} -> {ToId} ({RelationType})", fromEntityId, toEntityId, relationType);

            var relation = new KnowledgeRelation
            {
                FromEntityId = fromEntityId,
                ToEntityId = toEntityId,
                RelationType = relationType,
                Weight = weight,
                CreatedAt = DateTime.UtcNow
            };

            return await _relationRepository.CreateAsync(relation);
        }

        /// <summary>
        /// 查找相关项目
        /// </summary>
        public async Task<List<ProjectSimilarity>> FindSimilarProjectsAsync(int projectId, int topK = 5)
        {
            _logger.LogInformation("查找相似项目: {ProjectId}", projectId);

            var projectEntity = await _entityRepository.GetByTypeAndReferenceIdAsync("Project", projectId.ToString());
            if (projectEntity == null)
            {
                return new List<ProjectSimilarity>();
            }

            // 基于技术栈相似性
            var techSimilarities = await FindProjectsBySimilarTechnology(projectEntity, topK);
            
            // 基于团队成员相似性
            var teamSimilarities = await FindProjectsBySimilarTeam(projectEntity, topK);
            
            // 基于领域相似性
            var domainSimilarities = await FindProjectsBySimilarDomain(projectEntity, topK);

            // 合并和排序结果
            var allSimilarities = new List<ProjectSimilarity>();
            allSimilarities.AddRange(techSimilarities);
            allSimilarities.AddRange(teamSimilarities);
            allSimilarities.AddRange(domainSimilarities);

            return allSimilarities
                .GroupBy(p => p.ProjectId)
                .Select(g => new ProjectSimilarity
                {
                    ProjectId = g.Key,
                    ProjectName = g.First().ProjectName,
                    SimilarityScore = g.Sum(p => p.SimilarityScore) / g.Count(),
                    SimilarityReasons = g.SelectMany(p => p.SimilarityReasons).Distinct().ToList()
                })
                .OrderByDescending(p => p.SimilarityScore)
                .Take(topK)
                .ToList();
        }

        /// <summary>
        /// 获取专家推荐
        /// </summary>
        public async Task<List<ExpertRecommendation>> GetExpertRecommendationsAsync(string skillRequired, string problemDomain, int topK = 3)
        {
            _logger.LogInformation("获取专家推荐: {Skill} in {Domain}", skillRequired, problemDomain);

            var recommendations = new List<ExpertRecommendation>();

            // 查找具有相关技能的人员
            var skillEntities = await _entityRepository.GetByTypeAndNameAsync("Skill", skillRequired);
            
            foreach (var skillEntity in skillEntities)
            {
                // 查找掌握该技能的人员
                var personRelations = await _relationRepository.GetByToEntityAndTypeAsync(skillEntity.Id, "HasSkill");
                
                foreach (var relation in personRelations)
                {
                    var person = await _entityRepository.GetByIdAsync(relation.FromEntityId);
                    if (person?.Type == "Person")
                    {
                        var recommendation = new ExpertRecommendation
                        {
                            PersonId = person.ReferenceId,
                            PersonName = person.Name,
                            SkillLevel = relation.Weight,
                            RecommendationScore = await CalculateExpertScore(person, skillRequired, problemDomain),
                            Reasons = await GetExpertRecommendationReasons(person, skillRequired, problemDomain)
                        };

                        recommendations.Add(recommendation);
                    }
                }
            }

            return recommendations
                .OrderByDescending(r => r.RecommendationScore)
                .Take(topK)
                .ToList();
        }

        /// <summary>
        /// 获取最佳实践推荐
        /// </summary>
        public async Task<List<BestPractice>> GetBestPracticesAsync(string technology, string context)
        {
            _logger.LogInformation("获取最佳实践: {Technology} in {Context}", technology, context);

            var practices = new List<BestPractice>();

            // 查找技术相关的最佳实践
            var techEntities = await _entityRepository.GetByTypeAndNameAsync("Technology", technology);
            var techEntity = techEntities.FirstOrDefault();
            if (techEntity != null)
            {
                var practiceRelations = await _relationRepository.GetByFromEntityAndTypeAsync(techEntity.Id, "HasBestPractice");
                
                foreach (var relation in practiceRelations)
                {
                    var practiceEntity = await _entityRepository.GetByIdAsync(relation.ToEntityId);
                    if (practiceEntity?.Type == "BestPractice")
                    {
                        var practice = new BestPractice
                        {
                            Title = practiceEntity.Name,
                            Description = practiceEntity.Properties.GetValueOrDefault("description")?.ToString() ?? "",
                            Category = practiceEntity.Properties.GetValueOrDefault("category")?.ToString() ?? "",
                            Priority = relation.Weight,
                            Source = practiceEntity.Properties.GetValueOrDefault("source")?.ToString() ?? "",
                            Examples = await GetPracticeExamples(practiceEntity.Id)
                        };

                        practices.Add(practice);
                    }
                }
            }

            return practices.OrderByDescending(p => p.Priority).ToList();
        }

        /// <summary>
        /// 构建项目知识图谱
        /// </summary>
        public async Task BuildProjectKnowledgeGraphAsync(int projectId, ProjectKnowledgeData data)
        {
            _logger.LogInformation("构建项目知识图谱: {ProjectId}", projectId);

            // 创建项目实体
            var projectEntity = await CreateEntityAsync("Project", data.ProjectName, new Dictionary<string, object>
            {
                ["description"] = data.Description,
                ["status"] = data.Status,
                ["priority"] = data.Priority,
                ["referenceId"] = projectId.ToString()
            });

            // 创建技术栈实体和关系
            foreach (var tech in data.Technologies)
            {
                var techEntity = await GetOrCreateTechnologyEntity(tech);
                await CreateRelationAsync(projectEntity.Id, techEntity.Id, "UsesTechnology", 1.0f);
            }

            // 创建团队成员实体和关系
            foreach (var member in data.TeamMembers)
            {
                var memberEntity = await GetOrCreatePersonEntity(member);
                await CreateRelationAsync(memberEntity.Id, projectEntity.Id, "WorksOn", 1.0f);
            }

            // 创建模块实体和关系
            foreach (var module in data.Modules)
            {
                var moduleEntity = await CreateEntityAsync("Module", module.Name, new Dictionary<string, object>
                {
                    ["description"] = module.Description,
                    ["status"] = module.Status
                });
                await CreateRelationAsync(projectEntity.Id, moduleEntity.Id, "Contains", 1.0f);

                // 模块负责人关系
                if (!string.IsNullOrEmpty(module.Owner))
                {
                    var ownerEntity = await GetOrCreatePersonEntity(module.Owner);
                    await CreateRelationAsync(ownerEntity.Id, moduleEntity.Id, "ResponsibleFor", 1.0f);
                }
            }
        }

        /// <summary>
        /// 获取或创建技术实体
        /// </summary>
        private async Task<KnowledgeEntity> GetOrCreateTechnologyEntity(string technology)
        {
            var entities = await _entityRepository.GetByTypeAndNameAsync("Technology", technology);
            var existing = entities.FirstOrDefault();
            if (existing != null)
            {
                return existing;
            }

            return await CreateEntityAsync("Technology", technology, new Dictionary<string, object>
            {
                ["category"] = InferTechnologyCategory(technology),
                ["maturity"] = "stable"
            });
        }

        /// <summary>
        /// 获取或创建人员实体
        /// </summary>
        private async Task<KnowledgeEntity> GetOrCreatePersonEntity(string personName)
        {
            var entities = await _entityRepository.GetByTypeAndNameAsync("Person", personName);
            var existing = entities.FirstOrDefault();
            if (existing != null)
            {
                return existing;
            }

            return await CreateEntityAsync("Person", personName, new Dictionary<string, object>
            {
                ["role"] = "Developer"
            });
        }

        /// <summary>
        /// 推断技术分类
        /// </summary>
        private string InferTechnologyCategory(string technology)
        {
            var tech = technology.ToLower();
            
            if (tech.Contains("vue") || tech.Contains("react") || tech.Contains("angular") || tech.Contains("javascript"))
                return "Frontend";
            
            if (tech.Contains("net") || tech.Contains("java") || tech.Contains("python") || tech.Contains("node"))
                return "Backend";
            
            if (tech.Contains("sql") || tech.Contains("mongo") || tech.Contains("redis"))
                return "Database";
            
            if (tech.Contains("docker") || tech.Contains("kubernetes") || tech.Contains("aws") || tech.Contains("azure"))
                return "Infrastructure";
            
            return "Other";
        }

        /// <summary>
        /// 基于技术栈查找相似项目
        /// </summary>
        private async Task<List<ProjectSimilarity>> FindProjectsBySimilarTechnology(KnowledgeEntity projectEntity, int topK)
        {
            var similarities = new List<ProjectSimilarity>();
            
            // 获取项目使用的技术
            var techRelations = await _relationRepository.GetByFromEntityAndTypeAsync(projectEntity.Id, "UsesTechnology");
            var projectTechs = new HashSet<int>(techRelations.Select(r => r.ToEntityId));

            // 查找使用相似技术的其他项目
            var allProjects = await _entityRepository.GetByTypeAsync("Project");
            
            foreach (var otherProject in allProjects.Where(p => p.Id != projectEntity.Id))
            {
                var otherTechRelations = await _relationRepository.GetByFromEntityAndTypeAsync(otherProject.Id, "UsesTechnology");
                var otherTechs = new HashSet<int>(otherTechRelations.Select(r => r.ToEntityId));

                var commonTechs = projectTechs.Intersect(otherTechs).Count();
                var totalTechs = projectTechs.Union(otherTechs).Count();

                if (commonTechs > 0 && totalTechs > 0)
                {
                    var similarity = (float)commonTechs / totalTechs;
                    similarities.Add(new ProjectSimilarity
                    {
                        ProjectId = int.Parse(otherProject.ReferenceId),
                        ProjectName = otherProject.Name,
                        SimilarityScore = similarity,
                        SimilarityReasons = new List<string> { $"共享 {commonTechs} 个技术栈" }
                    });
                }
            }

            return similarities.OrderByDescending(s => s.SimilarityScore).Take(topK).ToList();
        }

        /// <summary>
        /// 基于团队成员查找相似项目
        /// </summary>
        private async Task<List<ProjectSimilarity>> FindProjectsBySimilarTeam(KnowledgeEntity projectEntity, int topK)
        {
            // 实现基于团队成员的相似性计算
            // 这里简化实现，实际可以更复杂
            return new List<ProjectSimilarity>();
        }

        /// <summary>
        /// 基于领域查找相似项目
        /// </summary>
        private async Task<List<ProjectSimilarity>> FindProjectsBySimilarDomain(KnowledgeEntity projectEntity, int topK)
        {
            // 实现基于项目领域的相似性计算
            // 这里简化实现，实际可以更复杂
            return new List<ProjectSimilarity>();
        }

        /// <summary>
        /// 计算专家推荐分数
        /// </summary>
        private async Task<float> CalculateExpertScore(KnowledgeEntity person, string skillRequired, string problemDomain)
        {
            // 实现专家评分算法
            // 考虑技能匹配度、经验、项目成功率等因素
            return 0.8f; // 简化实现
        }

        /// <summary>
        /// 获取专家推荐理由
        /// </summary>
        private async Task<List<string>> GetExpertRecommendationReasons(KnowledgeEntity person, string skillRequired, string problemDomain)
        {
            // 实现推荐理由生成
            return new List<string> { $"在 {skillRequired} 方面有丰富经验" };
        }

        /// <summary>
        /// 获取最佳实践示例
        /// </summary>
        private async Task<List<string>> GetPracticeExamples(int practiceEntityId)
        {
            // 实现最佳实践示例获取
            return new List<string>();
        }
    }
}
