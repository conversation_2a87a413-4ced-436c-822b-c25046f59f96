using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// UI自动化模板序列实体
    /// </summary>
    [SugarTable("UIAutoMationTemplateSequences", "UI自动化模板序列表")]
    public class UIAutoMationTemplateSequence : BaseEntity
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        [SugarColumn(ColumnDescription = "序列名称", Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "序列名称不能为空")]
        [StringLength(100, ErrorMessage = "序列名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 序列描述
        /// </summary>
        [SugarColumn(ColumnDescription = "序列描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "序列描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 序列分类（兼容性字段，逐步迁移到CategoryId）
        /// </summary>
        [SugarColumn(ColumnDescription = "序列分类", Length = 50, IsNullable = true)]
        public string? Category { get; set; }

        /// <summary>
        /// 分类ID（关联CustomTemplateCategories表）
        /// </summary>
        [SugarColumn(ColumnDescription = "分类ID", IsNullable = true)]
        public int? CategoryId { get; set; }

        /// <summary>
        /// 标签（JSON格式存储）
        /// </summary>
        [SugarColumn(ColumnDescription = "标签", Length = 500, IsNullable = true)]
        public string? Tags { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(ColumnDescription = "备注信息", Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        [SugarColumn(ColumnDescription = "使用次数", IsNullable = false)]
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        [SugarColumn(ColumnDescription = "最后使用时间", IsNullable = true)]
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用", IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 序列步骤JSON（完整的序列配置，包含所有步骤信息）
        /// </summary>
        [SugarColumn(ColumnDescription = "序列步骤JSON", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? SequenceJson { get; set; }

        /// <summary>
        /// 原生代码 - 用于保存用户在代码编辑器中编写的原始代码
        /// </summary>
        [SugarColumn(ColumnDescription = "原生代码", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? SourceCode { get; set; }

        /// <summary>
        /// 代码语言 - 支持的语言：javascript, python, typescript 等
        /// </summary>
        [SugarColumn(ColumnDescription = "代码语言", Length = 50, IsNullable = true)]
        public string? CodeLanguage { get; set; } = "javascript";

        /// <summary>
        /// 扩展属性（JSON格式）- 用于存储其他元数据，如编辑器设置等
        /// </summary>
        [SugarColumn(ColumnDescription = "扩展属性", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? ExtendedProperties { get; set; }

        /// <summary>
        /// 分类信息（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CustomTemplateCategory? CategoryInfo { get; set; }

        /// <summary>
        /// 执行步骤（导航属性）
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(UIAutoMationTemplateStep.SequenceId))]
        public List<UIAutoMationTemplateStep> Steps { get; set; } = new List<UIAutoMationTemplateStep>();

        /// <summary>
        /// 标签列表（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<string> TagList
        {
            get
            {
                if (string.IsNullOrEmpty(Tags))
                    return new List<string>();

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<List<string>>(Tags) ?? new List<string>();
                }
                catch
                {
                    return new List<string>();
                }
            }
            set
            {
                Tags = value?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(value) : null;
            }
        }
    }
}
