<template>
  <div class="requirement-chat">
    <!-- 页面头部 -->
    <!-- <div class="chat-header">
      <div class="header-left">
        <el-button type="text" @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1 class="page-title">AI需求对话</h1>
        <p class="page-subtitle">与AI助手对话，智能分析您的项目需求</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="startAnalysis" :loading="analyzing">
          <el-icon><MagicStick /></el-icon>
          开始需求分析
        </el-button>
      </div>
    </div> -->

    <!-- 对话区域 -->
    <div class="chat-container">
      <!-- 对话历史 -->
      <div class="chat-messages" ref="messagesContainer">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="ai-avatar">
            <el-icon size="32"><Robot /></el-icon>
          </div>
          <div class="message-content">
            <h3>👋 您好！我是AI需求分析助手</h3>
            <p>我可以帮助您：</p>
            <ul>
              <li>📝 理解和分析您的项目需求</li>
              <li>🎯 识别功能和非功能需求</li>
              <li>📊 评估项目可行性和复杂度</li>
              <li>📋 生成详细的需求规格书</li>
              <li>🗺️ 推荐技术架构方案</li>
            </ul>
            <p>请描述您的项目需求，我会为您提供专业的分析建议。</p>
          </div>
        </div>

        <!-- 对话消息 -->
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="message-item"
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
        >
          <div class="message-avatar">
            <el-avatar v-if="message.type === 'user'" :src="userAvatar" size="small">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div v-else class="ai-avatar">
              <el-icon size="20"><ChatDotRound /></el-icon>
            </div>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">{{ message.type === 'user' ? '您' : 'AI助手' }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>

            <!-- AI消息的操作按钮 -->
            <div v-if="message.type === 'ai'" class="message-actions">
              <el-button type="text" size="small" @click="copyMessage(message.content)">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button type="text" size="small" @click="likeMessage(message)">
                <el-icon><StarFilled /></el-icon>
                有用
              </el-button>
              <el-button type="text" size="small" @click="dislikeMessage(message)">
                <el-icon><Close /></el-icon>
                无用
              </el-button>
              <el-button
                type="primary"
                size="small"
                text
                @click="saveAsRequirement(message, index)"
                :loading="savingRequirement"
              >
                <el-icon><Document /></el-icon>
                保存为需求
              </el-button>
            </div>
          </div>
        </div>

        <!-- AI正在输入 -->
        <div v-if="aiTyping" class="message-item ai-message">
          <div class="message-avatar">
            <div class="ai-avatar">
              <el-icon size="20"><ChatDotRound /></el-icon>
            </div>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
              AI正在思考中...
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <el-input
            v-model="currentMessage"
            type="textarea"
            :rows="2"
            placeholder="请描述您的项目需求，例如：理解和分析您的项目需求..."
            :disabled="aiTyping"
            @keydown.ctrl.enter="sendMessage"
            @keydown.meta.enter="sendMessage"
          />
          <div class="input-actions">
            <div class="input-tips">
              <el-icon><InfoFilled /></el-icon>
              按 Ctrl+Enter 发送消息
            </div>
            <div class="input-buttons">
              <el-button @click="clearChat" :disabled="aiTyping">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
              <el-button
                type="primary"
                @click="sendMessage"
                :loading="aiTyping"
                :disabled="!currentMessage.trim()"
              >
                <el-icon><Promotion /></el-icon>
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 需求分析结果对话框 -->
    <el-dialog
      v-model="analysisDialogVisible"
      title="需求分析结果"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="analysisResult" class="analysis-result">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="分析概览" name="overview">
            <div class="analysis-overview">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-statistic title="可行性评分" :value="analysisResult.feasibilityScore" suffix="/10">
                    <template #prefix>
                      <el-icon><TrendCharts /></el-icon>
                    </template>
                  </el-statistic>
                </el-col>
                <el-col :span="8">
                  <el-statistic title="复杂度评分" :value="analysisResult.complexityScore" suffix="/10">
                    <template #prefix>
                      <el-icon><DataAnalysis /></el-icon>
                    </template>
                  </el-statistic>
                </el-col>
                <el-col :span="8">
                  <el-statistic title="置信度" :value="Math.round(analysisResult.confidenceScore * 100)" suffix="%">
                    <template #prefix>
                      <el-icon><CircleCheck /></el-icon>
                    </template>
                  </el-statistic>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <el-tab-pane label="功能需求" name="functional">
            <div class="functional-requirements">
              <el-table :data="analysisResult.functionalRequirements" style="width: 100%">
                <el-table-column prop="id" label="编号" width="80" />
                <el-table-column prop="title" label="功能名称" width="200" />
                <el-table-column prop="description" label="功能描述" />
                <el-table-column prop="priority" label="优先级" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="技术建议" name="tech">
            <div class="tech-stack">
              <h4>推荐技术栈</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="前端技术">
                  <el-tag v-for="tech in analysisResult.techStack?.frontend" :key="tech" class="mr-1">
                    {{ tech }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="后端技术">
                  <el-tag v-for="tech in analysisResult.techStack?.backend" :key="tech" class="mr-1">
                    {{ tech }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="数据库">
                  <el-tag v-for="tech in analysisResult.techStack?.database" :key="tech" class="mr-1">
                    {{ tech }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="其他技术">
                  <el-tag v-for="tech in analysisResult.techStack?.other" :key="tech" class="mr-1">
                    {{ tech }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>

          <el-tab-pane label="风险评估" name="risks">
            <div class="risk-assessment">
              <el-table :data="analysisResult.risks" style="width: 100%">
                <el-table-column prop="description" label="风险描述" />
                <el-table-column prop="impact" label="影响程度" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getRiskType(row.impact)">{{ row.impact }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="probability" label="发生概率" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getRiskType(row.probability)">{{ row.probability }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="mitigation" label="缓解措施" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="analysisDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="generateSpecification">
            <el-icon><Document /></el-icon>
            生成需求规格书
          </el-button>
          <el-button type="success" @click="generateDiagrams">
            <el-icon><PictureRounded /></el-icon>
            生成设计图
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 保存需求对话框 -->
    <el-dialog
      v-model="saveRequirementDialogVisible"
      title="保存为需求文档"
      width="90%"
      :close-on-click-modal="false"
      :modal="true"
      :append-to-body="true"
      class="requirement-save-dialog"
      top="5vh"
    >
      <el-form :model="requirementForm" label-width="100px">
        <el-form-item label="需求标题" required>
          <el-input
            v-model="requirementForm.title"
            placeholder="请输入需求文档标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="用户需求">
          <el-input
            v-model="requirementForm.userMessage"
            type="textarea"
            :rows="4"
            placeholder="用户原始需求描述"
          />
        </el-form-item>

        <el-form-item label="AI分析内容">
          <el-input
            v-model="requirementForm.aiContent"
            type="textarea"
            :rows="6"
            placeholder="AI分析和建议内容"
          />
        </el-form-item>

        <el-form-item label="功能性需求">
          <div class="form-item-with-ai">
            <el-input
              v-model="requirementForm.functionalRequirements"
              type="textarea"
              :rows="6"
              placeholder="可选：提取或补充功能性需求"
            />
            <el-button
              type="primary"
              size="small"
              :loading="generatingFunctional"
              @click="() => generateFunctionalRequirements()"
              class="ai-generate-btn"
            >
              <el-icon><MagicStick /></el-icon>
              AI生成
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="非功能性需求">
          <div class="form-item-with-ai">
            <el-input
              v-model="requirementForm.nonFunctionalRequirements"
              type="textarea"
              :rows="6"
              placeholder="可选：提取或补充非功能性需求"
            />
            <el-button
              type="primary"
              size="small"
              :loading="generatingNonFunctional"
              @click="() => generateNonFunctionalRequirements()"
              class="ai-generate-btn"
            >
              <el-icon><MagicStick /></el-icon>
              AI生成
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="用户故事">
          <div class="form-item-with-ai">
            <el-input
              v-model="requirementForm.userStories"
              type="textarea"
              :rows="5"
              placeholder="可选：用户故事描述"
            />
            <el-button
              type="primary"
              size="small"
              :loading="generatingUserStories"
              @click="() => generateUserStories()"
              class="ai-generate-btn"
            >
              <el-icon><MagicStick /></el-icon>
              AI生成
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="验收标准">
          <div class="form-item-with-ai">
            <el-input
              v-model="requirementForm.acceptanceCriteria"
              type="textarea"
              :rows="5"
              placeholder="可选：验收标准和测试要求"
            />
            <el-button
              type="primary"
              size="small"
              :loading="generatingAcceptance"
              @click="() => generateAcceptanceCriteria()"
              class="ai-generate-btn"
            >
              <el-icon><MagicStick /></el-icon>
              AI生成
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveRequirementDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmSaveRequirement"
            :loading="savingRequirement"
            :disabled="!requirementForm.title.trim()"
          >
            保存需求文档
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { RequirementService } from '@/services/requirement'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElTagType } from '@/types/element-plus'
import dayjs from 'dayjs'
import {
  ArrowLeft,
  MagicStick,
  User,
  ChatDotRound,
  StarFilled,
  CopyDocument,
  Close,
  InfoFilled,
  Delete,
  Promotion,
  TrendCharts,
  DataAnalysis,
  CircleCheck,
  Document,
  PictureRounded
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const projectId = ref(parseInt(route.params.projectId as string))
const aiProviderConfigId = ref(route.query.aiProvider ? parseInt(route.query.aiProvider as string) : undefined)
const currentMessage = ref('')
const messages = ref<Array<{
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}>>([])
const aiTyping = ref(false)
const analyzing = ref(false)
const messagesContainer = ref<HTMLElement>()
const conversationId = ref(`project-${projectId.value}-${Date.now()}`)
const loadingHistory = ref(false)

// 分析结果相关
const analysisDialogVisible = ref(false)
const analysisResult = ref<any>(null)
const activeTab = ref('overview')

// 保存需求相关
const saveRequirementDialogVisible = ref(false)
const savingRequirement = ref(false)
const requirementForm = ref({
  title: '',
  userMessage: '',
  aiContent: '',
  functionalRequirements: '',
  nonFunctionalRequirements: '',
  userStories: '',
  acceptanceCriteria: ''
})
const currentSavingMessageIndex = ref(-1)

// AI生成相关的加载状态
const generatingFunctional = ref(false)
const generatingNonFunctional = ref(false)
const generatingUserStories = ref(false)
const generatingAcceptance = ref(false)

// 计算属性
const userAvatar = computed(() => authStore.user?.avatar || '')



// 方法
const formatTime = (time: Date) => {
  return dayjs(time).format('HH:mm')
}

const formatMessage = (content: string) => {
  // 简单的Markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      // 使用 setTimeout 确保 DOM 完全更新后再滚动
      setTimeout(() => {
        messagesContainer.value!.scrollTop = messagesContainer.value!.scrollHeight
      }, 100)
    }
  })
}

// 加载对话历史
const loadConversationHistory = async () => {
  try {
    loadingHistory.value = true
    const history = await RequirementService.getConversationHistory(conversationId.value)

    // 将历史对话转换为消息格式
    const historyMessages: Array<{
      type: 'user' | 'ai'
      content: string
      timestamp: Date
    }> = []

    history.forEach(item => {
      // 添加用户消息
      historyMessages.push({
        type: 'user',
        content: item.userMessage,
        timestamp: new Date(item.timestamp)
      })

      // 添加AI回复（如果存在）
      if (item.aiResponse) {
        historyMessages.push({
          type: 'ai',
          content: item.aiResponse,
          timestamp: new Date(item.timestamp)
        })
      }
    })

    messages.value = historyMessages
    scrollToBottom()
  } catch (error: any) {
    console.error('加载对话历史失败:', error)
    // 不显示错误消息，静默失败
  } finally {
    loadingHistory.value = false
  }
}

const sendMessage = async () => {
  if (!currentMessage.value.trim() || aiTyping.value) return

  const userMessage = currentMessage.value.trim()
  currentMessage.value = ''

  // 添加用户消息
  messages.value.push({
    type: 'user',
    content: userMessage,
    timestamp: new Date()
  })

  scrollToBottom()

  // 显示AI正在输入
  aiTyping.value = true

  try {
    // 调用AI服务，传递对话ID、项目ID和AI提供商配置ID
    const response = await RequirementService.sendMessage(
      conversationId.value,
      userMessage,
      projectId.value,
      aiProviderConfigId.value
    )

    // 添加AI回复
    messages.value.push({
      type: 'ai',
      content: response.aiResponse,
      timestamp: new Date()
    })

    // 立即滚动到底部
    scrollToBottom()
  } catch (error: any) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请稍后重试')

    // 添加错误消息
    messages.value.push({
      type: 'ai',
      content: '抱歉，我暂时无法回复您的消息，请稍后重试。',
      timestamp: new Date()
    })

    // 立即滚动到底部
    scrollToBottom()
  } finally {
    aiTyping.value = false
    scrollToBottom()
  }
}



const clearChat = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空对话历史吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    messages.value = []
  } catch (error) {
    // 用户取消
  }
}

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const likeMessage = (_message: any) => {
  ElMessage.success('感谢您的反馈！')
  // TODO: 发送反馈到后端
}

const dislikeMessage = (_message: any) => {
  ElMessage.info('感谢您的反馈，我们会持续改进')
  // TODO: 发送反馈到后端
}

// 保存为需求文档
const saveAsRequirement = (message: any, messageIndex: number) => {
  // 找到对应的用户消息
  let userMessage = ''
  if (messageIndex > 0) {
    const previousMessage = messages.value[messageIndex - 1]
    if (previousMessage && previousMessage.type === 'user') {
      userMessage = previousMessage.content
    }
  }

  // 填充表单数据
  requirementForm.value = {
    title: `需求文档 - ${dayjs().format('YYYY-MM-DD HH:mm')}`,
    userMessage: userMessage,
    aiContent: message.content,
    functionalRequirements: '',
    nonFunctionalRequirements: '',
    userStories: '',
    acceptanceCriteria: ''
  }

  currentSavingMessageIndex.value = messageIndex
  saveRequirementDialogVisible.value = true
}

// 确认保存需求文档
const confirmSaveRequirement = async () => {
  if (!requirementForm.value.title.trim()) {
    ElMessage.warning('请输入需求文档标题')
    return
  }

  savingRequirement.value = true

  try {
    // 如果各类需求内容为空，自动生成
    await autoGenerateRequirementSections()

    const savedDocument = await RequirementService.saveRequirementFromChat({
      projectId: projectId.value,
      title: requirementForm.value.title,
      aiContent: requirementForm.value.aiContent,
      userMessage: requirementForm.value.userMessage,
      conversationId: conversationId.value,
      functionalRequirements: requirementForm.value.functionalRequirements || undefined,
      nonFunctionalRequirements: requirementForm.value.nonFunctionalRequirements || undefined,
      userStories: requirementForm.value.userStories || undefined,
      acceptanceCriteria: requirementForm.value.acceptanceCriteria || undefined,
      aiProviderConfigId: aiProviderConfigId.value || undefined
    })

    ElMessage.success('需求文档保存成功！')
    saveRequirementDialogVisible.value = false

    // 询问是否跳转到需求文档详情页
    try {
      await ElMessageBox.confirm(
        '需求文档已保存成功，是否查看文档详情？',
        '保存成功',
        {
          confirmButtonText: '查看详情',
          cancelButtonText: '继续对话',
          type: 'success'
        }
      )

      // 跳转到需求文档详情页
      router.push(`/requirements/${savedDocument.id}`)
    } catch (error) {
      // 用户选择继续对话，不做任何操作
    }

  } catch (error: any) {
    console.error('保存需求文档失败:', error)
    ElMessage.error('保存需求文档失败，请稍后重试')
  } finally {
    savingRequirement.value = false
  }
}

// 自动生成需求各个部分
const autoGenerateRequirementSections = async () => {
  if (!requirementForm.value.userMessage && !requirementForm.value.aiContent) {
    return
  }

  const sectionsToGenerate = []

  // 检查哪些部分需要生成
  if (!requirementForm.value.functionalRequirements.trim()) {
    sectionsToGenerate.push('functional')
  }
  if (!requirementForm.value.nonFunctionalRequirements.trim()) {
    sectionsToGenerate.push('nonFunctional')
  }
  if (!requirementForm.value.userStories.trim()) {
    sectionsToGenerate.push('userStories')
  }
  if (!requirementForm.value.acceptanceCriteria.trim()) {
    sectionsToGenerate.push('acceptance')
  }

  if (sectionsToGenerate.length === 0) {
    return
  }

  ElMessage.info('正在自动生成需求文档各部分内容...')

  try {
    // 按顺序生成，因为验收标准依赖于功能性需求和用户故事
    if (sectionsToGenerate.includes('functional')) {
      await generateFunctionalRequirements(false)
    }

    if (sectionsToGenerate.includes('nonFunctional')) {
      await generateNonFunctionalRequirements(false)
    }

    if (sectionsToGenerate.includes('userStories')) {
      await generateUserStories(false)
    }

    if (sectionsToGenerate.includes('acceptance')) {
      await generateAcceptanceCriteria(false)
    }

    ElMessage.success('需求文档各部分内容生成完成！')
  } catch (error) {
    console.error('自动生成需求内容失败:', error)
    ElMessage.error('自动生成需求内容失败，请手动生成')
  }
}

const startAnalysis = async () => {
  if (messages.value.length === 0) {
    ElMessage.warning('请先与AI助手对话，描述您的项目需求')
    return
  }

  // 收集所有用户消息作为需求描述
  const requirements = messages.value
    .filter(msg => msg.type === 'user')
    .map(msg => msg.content)
    .join('\n\n')

  if (!requirements.trim()) {
    ElMessage.warning('请先描述您的项目需求')
    return
  }

  analyzing.value = true

  try {
    // 启动需求分析
    const response = await RequirementService.analyzeRequirements({
      projectId: projectId.value,
      requirements
    })

    ElMessage.success('需求分析任务已启动，正在使用AI进行智能分析...')

    // 轮询任务状态
    const pollTaskStatus = async () => {
      try {
        const status = await RequirementService.getTaskStatus(response.taskId)

        if (status.status === 'Completed') {
          // 获取分析结果
          const result = await RequirementService.getTaskResult(response.taskId)
          analysisResult.value = result.result
          analysisDialogVisible.value = true
          analyzing.value = false
          ElMessage.success('需求分析完成！')
        } else if (status.status === 'Failed') {
          analyzing.value = false
          ElMessage.error('需求分析失败，请重试')
        } else {
          // 继续轮询
          setTimeout(pollTaskStatus, 2000)
        }
      } catch (error) {
        analyzing.value = false
        ElMessage.error('获取分析状态失败')
      }
    }

    // 开始轮询
    setTimeout(pollTaskStatus, 2000)

  } catch (error: any) {
    analyzing.value = false
    console.error('需求分析失败:', error)
    ElMessage.error('启动需求分析失败，请稍后重试')
  }
}

const getPriorityType = (priority: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success'
  }
  return typeMap[priority] || 'info'
}

const getRiskType = (level: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success'
  }
  return typeMap[level] || 'info'
}

const generateSpecification = () => {
  ElMessage.info('需求规格书生成功能开发中...')
  // TODO: 实现需求规格书生成
}

const generateDiagrams = () => {
  ElMessage.info('设计图生成功能开发中...')
  // TODO: 实现ER图和Context图生成
}

// AI生成各类需求的方法
const generateFunctionalRequirements = async (showMessage = true) => {
  if (!requirementForm.value.userMessage && !requirementForm.value.aiContent) {
    if (showMessage) {
      ElMessage.warning('请先填写用户需求或AI分析内容')
    }
    return
  }

  generatingFunctional.value = true

  try {
    const context = `
用户需求：${requirementForm.value.userMessage}

AI分析内容：${requirementForm.value.aiContent}

请基于以上内容，生成详细的功能性需求列表。要求：
1. 列出所有核心功能模块
2. 每个功能要具体明确
3. 使用清晰的编号和分类
4. 避免技术实现细节，专注于功能描述
5. 格式清晰，便于理解

请直接返回功能性需求内容，不需要额外说明。`

    const response = await RequirementService.sendMessage(
      `generate-functional-${Date.now()}`,
      context,
      projectId.value,
      aiProviderConfigId.value
    )

    requirementForm.value.functionalRequirements = response.aiResponse
    if (showMessage) {
      ElMessage.success('功能性需求生成成功！')
    }
  } catch (error: any) {
    console.error('生成功能性需求失败:', error)
    if (showMessage) {
      ElMessage.error('生成功能性需求失败，请稍后重试')
    }
  } finally {
    generatingFunctional.value = false
  }
}

const generateNonFunctionalRequirements = async (showMessage = true) => {
  if (!requirementForm.value.userMessage && !requirementForm.value.aiContent) {
    if (showMessage) {
      ElMessage.warning('请先填写用户需求或AI分析内容')
    }
    return
  }

  generatingNonFunctional.value = true

  try {
    const context = `
用户需求：${requirementForm.value.userMessage}

AI分析内容：${requirementForm.value.aiContent}

请基于以上内容，生成详细的非功能性需求。要求：
1. 性能需求（响应时间、并发用户数等）
2. 安全性需求（认证、授权、数据保护等）
3. 可用性需求（系统可用性、容错性等）
4. 兼容性需求（浏览器、设备、操作系统等）
5. 可维护性需求（代码质量、文档等）
6. 可扩展性需求（用户增长、功能扩展等）

请直接返回非功能性需求内容，不需要额外说明。`

    const response = await RequirementService.sendMessage(
      `generate-nonfunctional-${Date.now()}`,
      context,
      projectId.value,
      aiProviderConfigId.value
    )

    requirementForm.value.nonFunctionalRequirements = response.aiResponse
    if (showMessage) {
      ElMessage.success('非功能性需求生成成功！')
    }
  } catch (error: any) {
    console.error('生成非功能性需求失败:', error)
    if (showMessage) {
      ElMessage.error('生成非功能性需求失败，请稍后重试')
    }
  } finally {
    generatingNonFunctional.value = false
  }
}

const generateUserStories = async (showMessage = true) => {
  if (!requirementForm.value.userMessage && !requirementForm.value.aiContent) {
    if (showMessage) {
      ElMessage.warning('请先填写用户需求或AI分析内容')
    }
    return
  }

  generatingUserStories.value = true

  try {
    const context = `
用户需求：${requirementForm.value.userMessage}

AI分析内容：${requirementForm.value.aiContent}

请基于以上内容，生成用户故事（User Stories）。要求：
1. 使用标准格式："作为[用户角色]，我希望[功能描述]，以便[价值/目标]"
2. 识别不同的用户角色（如管理员、普通用户、访客等）
3. 每个故事要独立、可测试
4. 按优先级排序
5. 包含主要业务流程的关键故事

请直接返回用户故事内容，不需要额外说明。`

    const response = await RequirementService.sendMessage(
      `generate-userstories-${Date.now()}`,
      context,
      projectId.value,
      aiProviderConfigId.value
    )

    requirementForm.value.userStories = response.aiResponse
    if (showMessage) {
      ElMessage.success('用户故事生成成功！')
    }
  } catch (error: any) {
    console.error('生成用户故事失败:', error)
    if (showMessage) {
      ElMessage.error('生成用户故事失败，请稍后重试')
    }
  } finally {
    generatingUserStories.value = false
  }
}

const generateAcceptanceCriteria = async (showMessage = true) => {
  if (!requirementForm.value.userMessage && !requirementForm.value.aiContent) {
    if (showMessage) {
      ElMessage.warning('请先填写用户需求或AI分析内容')
    }
    return
  }

  generatingAcceptance.value = true

  try {
    const context = `
用户需求：${requirementForm.value.userMessage}

AI分析内容：${requirementForm.value.aiContent}

功能性需求：${requirementForm.value.functionalRequirements}

用户故事：${requirementForm.value.userStories}

请基于以上内容，生成详细的验收标准。要求：
1. 针对每个主要功能制定验收标准
2. 使用Given-When-Then格式或清晰的测试场景
3. 包含正常流程和异常流程的测试
4. 明确输入条件和预期输出
5. 可量化、可验证的标准
6. 包含用户界面和用户体验的验收标准

请直接返回验收标准内容，不需要额外说明。`

    const response = await RequirementService.sendMessage(
      `generate-acceptance-${Date.now()}`,
      context,
      projectId.value,
      aiProviderConfigId.value
    )

    requirementForm.value.acceptanceCriteria = response.aiResponse
    if (showMessage) {
      ElMessage.success('验收标准生成成功！')
    }
  } catch (error: any) {
    console.error('生成验收标准失败:', error)
    if (showMessage) {
      ElMessage.error('生成验收标准失败，请稍后重试')
    }
  } finally {
    generatingAcceptance.value = false
  }
}

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  scrollToBottom()
}, { deep: true })

// 组件挂载时的初始化
onMounted(() => {
  console.log('需求对话页面已加载，项目ID:', projectId.value)
  console.log('AI提供商配置ID:', aiProviderConfigId.value)
  // 加载对话历史
  loadConversationHistory()
})
</script>

<style lang="scss" scoped>
.requirement-chat {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);

  .header-left {
    .page-title {
      margin: 8px 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .page-subtitle {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1000px; // 增加最大宽度
  width: 75%; // 设置为屏幕宽度的75%
  margin: 0 auto;
  padding: 0 20px 200px 20px; // 为固定输入框留出空间
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 3px;
  }
}

.welcome-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;

  .ai-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 16px;
    flex-shrink: 0;
  }

  .message-content {
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 12px 0;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 8px 0;
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin: 8px 0;
        color: var(--el-text-color-regular);
        line-height: 1.6;
      }
    }
  }
}

.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;

  &.user-message {
    flex-direction: row-reverse;

    .message-content {
      background: var(--el-color-primary);
      color: white;
      margin-left: 60px;
      margin-right: 16px;

      .message-header .sender-name {
        color: rgba(255, 255, 255, 0.9);
      }

      .message-header .message-time {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  &.ai-message {
    .message-content {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-lighter);
      margin-right: 60px;
      margin-left: 16px;
    }
  }

  .message-avatar {
    flex-shrink: 0;

    .ai-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
  }

  .message-content {
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 70%;
    word-wrap: break-word;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .sender-name {
        font-weight: 500;
        font-size: 12px;
        color: var(--el-text-color-regular);
      }

      .message-time {
        font-size: 11px;
        color: var(--el-text-color-placeholder);
      }
    }

    .message-text {
      line-height: 1.6;

      :deep(strong) {
        font-weight: 600;
      }

      :deep(em) {
        font-style: italic;
      }
    }

    .message-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;

      .el-button {
        padding: 4px 8px;
        height: auto;
        font-size: 12px;
      }
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--el-color-primary);
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  width: 75%; // 与聊天容器宽度一致
  max-width: 1000px; // 与聊天容器最大宽度一致
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .input-container {
    width: 100%;
    margin: 0;
    background: var(--el-bg-color);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      border-color: var(--el-border-color);
    }

    &:focus-within {
      border-color: var(--el-color-primary);
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        border: none;
        box-shadow: none;
        background: transparent;
        resize: none;
        font-size: 14px;
        line-height: 1.6;
        padding: 8px 0;

        &:focus {
          box-shadow: none;
        }

        &::placeholder {
          color: var(--el-text-color-placeholder);
          font-size: 14px;
        }
      }
    }

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid var(--el-border-color-extra-light);

      .input-tips {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }

      .input-buttons {
        display: flex;
        gap: 8px;

        .el-button {
          border-radius: 8px;
          font-size: 13px;
          padding: 8px 16px;

          &:not(.el-button--primary) {
            background: var(--el-fill-color-light);
            border-color: var(--el-border-color-light);

            &:hover {
              background: var(--el-fill-color);
              border-color: var(--el-border-color);
            }
          }
        }
      }
    }
  }
}



.analysis-result {
  .analysis-overview {
    padding: 20px 0;
  }

  .functional-requirements,
  .tech-stack,
  .risk-assessment {
    .mr-1 {
      margin-right: 8px;
      margin-bottom: 4px;
    }

    h4 {
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

// 保存需求对话框样式
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px 30px;

    .el-form {
      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__content {
          max-width: none;
          flex: 1;
        }

        .el-textarea {
          width: 100%;

          .el-textarea__inner {
            width: 100%;
            min-width: 100%;
            resize: vertical;
          }
        }
      }
    }
  }
}

// AI生成按钮样式
.form-item-with-ai {
  position: relative;
  width: 100%;

  .ai-generate-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    border-radius: 6px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    min-height: 24px;
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);

    &:hover {
      background: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }

    .el-icon {
      margin-right: 2px;
      font-size: 12px;
    }
  }

  :deep(.el-textarea) {
    width: 100%;

    .el-textarea__inner {
      padding-right: 80px; // 为按钮留出空间
      width: 100%;
      min-width: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    gap: 16px;

    .header-right {
      align-self: stretch;
    }
  }

  .chat-container {
    padding: 0 12px;
  }

  .message-item {
    .message-content {
      max-width: 85%;
    }

    &.user-message .message-content {
      margin-left: 40px;
    }

    &.ai-message .message-content {
      margin-right: 40px;
    }
  }



  .chat-container {
    width: 95%; // 移动端使用更大的宽度
    padding: 0 12px 180px 12px; // 移动端减少底部空间
  }

  .chat-input {
    width: 95%; // 移动端使用更大的宽度
    padding: 16px 12px;

    .input-container {
      max-width: none;
      margin: 0;
      padding: 12px;
      border-radius: 8px;

      .input-actions {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .input-tips {
          justify-content: center;
        }

        .input-buttons {
          justify-content: center;

          .el-button {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
  }
}

// 需求保存对话框样式 - 使用全局样式覆盖
.requirement-save-dialog {
  .el-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2000 !important;
  }

  .el-dialog {
    position: fixed !important;
    top: 5vh !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    margin: 0 !important;
    width: 90% !important;
    max-width: 1200px !important;
    height: 90vh !important;
    max-height: 90vh !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  .el-dialog__header {
    flex-shrink: 0 !important;
    padding: 20px 20px 10px 20px !important;
    border-bottom: 1px solid var(--el-border-color-lighter) !important;
    margin: 0 !important;
  }

  .el-dialog__body {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 20px !important;
    margin: 0 !important;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-bg-color-page);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-light);
      border-radius: 3px;
    }
  }

  .el-dialog__footer {
    flex-shrink: 0 !important;
    padding: 10px 20px 20px 20px !important;
    border-top: 1px solid var(--el-border-color-lighter) !important;
    background: var(--el-bg-color) !important;
    margin: 0 !important;
  }

  // 表单项优化
  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item__label {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .el-textarea {
      .el-textarea__inner {
        min-height: 80px;
        resize: vertical;
      }
    }
  }

  // 对话框底部按钮
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .requirement-save-dialog {
    .el-dialog {
      width: 95% !important;
      top: 2vh !important;
      height: 96vh !important;
      max-height: 96vh !important;
    }

    .el-dialog__body {
      padding: 16px !important;
    }

    .el-dialog__header,
    .el-dialog__footer {
      padding: 16px !important;
    }

    .el-form {
      .el-form-item__label {
        font-size: 14px;
      }

      .el-textarea .el-textarea__inner {
        min-height: 60px;
        font-size: 14px;
      }
    }
  }
}
</style>

<!-- 全局样式，确保对话框固定定位 -->
<style>
.requirement-save-dialog .el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
}

.requirement-save-dialog .el-dialog {
  position: fixed !important;
  top: 5vh !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  margin: 0 !important;
  width: 90% !important;
  max-width: 1200px !important;
  height: 90vh !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.requirement-save-dialog .el-dialog__header {
  flex-shrink: 0 !important;
  padding: 20px 20px 10px 20px !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  margin: 0 !important;
}

.requirement-save-dialog .el-dialog__body {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 20px !important;
  margin: 0 !important;
}

.requirement-save-dialog .el-dialog__footer {
  flex-shrink: 0 !important;
  padding: 10px 20px 20px 20px !important;
  border-top: 1px solid var(--el-border-color-lighter) !important;
  background: var(--el-bg-color) !important;
  margin: 0 !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .requirement-save-dialog .el-dialog {
    width: 95% !important;
    top: 2vh !important;
    height: 96vh !important;
    max-height: 96vh !important;
  }

  .requirement-save-dialog .el-dialog__body,
  .requirement-save-dialog .el-dialog__header,
  .requirement-save-dialog .el-dialog__footer {
    padding: 16px !important;
  }
}
</style>
