-- 更新PromptTemplates表的TaskType约束，添加PrototypeGeneration类型
-- 用于支持原型图生成功能

USE [ProjectManagementAI]
GO

-- 检查约束是否存在并删除旧约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE [dbo].[PromptTemplates] DROP CONSTRAINT [CK_PromptTemplates_TaskType]
    PRINT '已删除旧的TaskType约束'
END

-- 创建新的TaskType约束，包含PrototypeGeneration类型
ALTER TABLE [dbo].[PromptTemplates] ADD CONSTRAINT [CK_PromptTemplates_TaskType]
CHECK ([TaskType] IN (
    'RequirementAnalysis',      -- 需求分析
    'CodeGeneration',          -- 代码生成
    'Testing',                 -- 测试生成
    'Debugging',               -- 调试辅助
    'Documentation',           -- 文档生成
    'Review',                  -- 代码审查
    'ERDiagramGeneration',     -- ER图生成
    'ContextDiagramGeneration', -- 上下文图生成
    'DesignGeneration',        -- 设计生成
    'ArchitectureAnalysis',    -- 架构分析
    'RequirementDecomposition', -- 需求分解
    'DependencyAnalysis',      -- 依赖分析
    'ComplexityAnalysis',      -- 复杂度分析
    'SQLCommentGeneration',    -- SQL注释生成
    'StepDecomposition',       -- 步骤分解
    'PrototypeGeneration'      -- 原型图生成
))

PRINT '已创建新的TaskType约束，包含PrototypeGeneration类型'

-- 验证约束是否创建成功
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    PRINT '✓ TaskType约束创建成功'
END
ELSE
BEGIN
    PRINT '✗ TaskType约束创建失败'
END

PRINT 'PromptTemplates表TaskType约束更新完成'
GO
