-- =============================================
-- 项目模板使用统计视图
-- 视图名称: vw_ProjectTemplateUsageStats
-- 功能: 统计分析项目中UI模板序列的使用情况
-- 创建日期: 2024-12-25
-- 版本: 1.0
-- =============================================

USE ProjectManagementAI;
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectTemplateUsageStats')
BEGIN
    DROP VIEW vw_ProjectTemplateUsageStats;
    PRINT '已删除现有视图 vw_ProjectTemplateUsageStats';
END
GO

-- 创建统计分析视图
CREATE VIEW vw_ProjectTemplateUsageStats
AS
SELECT
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,
    owner.RealName AS ProjectOwner,

    -- 步骤统计
    (
        SELECT COUNT(*)
        FROM DevelopmentSteps ds
        WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
    ) AS TotalSteps,

    (
        SELECT COUNT(DISTINCT stsa.StepId)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS StepsWithTemplates,

    -- 模板序列统计
    (
        SELECT COUNT(DISTINCT stsa.SequenceId)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS UniqueSequencesUsed,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS TotalAssociations,

    -- 执行状态统计
    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Completed'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS CompletedAssociations,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Running'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS RunningAssociations,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Failed'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS FailedAssociations,

    -- 分类统计（简化）
    (
        SELECT COUNT(DISTINCT uts.Category)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
        AND uts.IsDeleted = 0
    ) AS CategoryCount,

    -- 最常用的模板序列
    (
        SELECT TOP 1 uts.Name
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
        AND uts.IsDeleted = 0
        GROUP BY uts.Id, uts.Name
        ORDER BY COUNT(*) DESC
    ) AS MostUsedSequence,

    -- 平均执行进度
    (
        SELECT AVG(CAST(stsa.Progress AS FLOAT))
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS AverageProgress,

    -- 成功率
    CASE
        WHEN (
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) > 0
        THEN CAST((
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.Status = 'Completed'
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) AS FLOAT) * 100.0 / (
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        )
        ELSE 0
    END AS SuccessRate,

    -- 模板覆盖率
    CASE
        WHEN (
            SELECT COUNT(*)
            FROM DevelopmentSteps ds
            WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
        ) > 0
        THEN CAST((
            SELECT COUNT(DISTINCT stsa.StepId)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) AS FLOAT) * 100.0 / (
            SELECT COUNT(*)
            FROM DevelopmentSteps ds
            WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
        )
        ELSE 0
    END AS TemplateCoverageRate,

    -- 最近应用时间
    (
        SELECT MAX(stsa.AppliedTime)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS LastAppliedTime,

    -- 项目创建时间
    p.CreatedTime AS ProjectCreatedTime

FROM Projects p
    LEFT JOIN Users owner ON p.OwnerId = owner.Id
WHERE p.IsDeleted = 0;

GO

-- 添加视图注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'项目模板使用统计视图 - 提供项目中UI模板序列使用情况的统计分析，包括覆盖率、成功率、分类分布等指标',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'vw_ProjectTemplateUsageStats';

GO

-- 验证视图创建
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectTemplateUsageStats')
BEGIN
    PRINT '✅ 视图 vw_ProjectTemplateUsageStats 创建成功！';
    PRINT '';
    PRINT '📊 统计分析查询示例：';
    PRINT '';
    PRINT '-- 1. 查看所有项目的模板使用统计';
    PRINT 'SELECT ProjectName, TotalSteps, StepsWithTemplates, UniqueSequencesUsed,';
    PRINT '       ROUND(TemplateCoverageRate, 2) as CoverageRate,';
    PRINT '       ROUND(SuccessRate, 2) as SuccessRate';
    PRINT 'FROM vw_ProjectTemplateUsageStats';
    PRINT 'ORDER BY TemplateCoverageRate DESC;';
    PRINT '';
    PRINT '-- 2. 查找模板覆盖率最高的项目';
    PRINT 'SELECT TOP 5 ProjectName, TemplateCoverageRate, TotalSteps, StepsWithTemplates';
    PRINT 'FROM vw_ProjectTemplateUsageStats';
    PRINT 'WHERE TotalSteps > 0';
    PRINT 'ORDER BY TemplateCoverageRate DESC;';
    PRINT '';
    PRINT '-- 3. 查看执行成功率统计';
    PRINT 'SELECT ProjectName, TotalAssociations, CompletedAssociations,';
    PRINT '       RunningAssociations, FailedAssociations,';
    PRINT '       ROUND(SuccessRate, 2) as SuccessRate';
    PRINT 'FROM vw_ProjectTemplateUsageStats';
    PRINT 'WHERE TotalAssociations > 0';
    PRINT 'ORDER BY SuccessRate DESC;';
    PRINT '';
    PRINT '-- 4. 分析模板分类使用情况';
    PRINT 'SELECT ProjectName, CategoryBreakdown, MostUsedSequence';
    PRINT 'FROM vw_ProjectTemplateUsageStats';
    PRINT 'WHERE CategoryBreakdown IS NOT NULL;';
    PRINT '';
    PRINT '-- 5. 查看项目模板使用活跃度';
    PRINT 'SELECT ProjectName, LastAppliedTime,';
    PRINT '       DATEDIFF(DAY, LastAppliedTime, GETDATE()) as DaysSinceLastUse,';
    PRINT '       TotalAssociations, RunningAssociations';
    PRINT 'FROM vw_ProjectTemplateUsageStats';
    PRINT 'WHERE LastAppliedTime IS NOT NULL';
    PRINT 'ORDER BY LastAppliedTime DESC;';

END
ELSE
BEGIN
    PRINT '❌ 视图创建失败！';
END

GO

PRINT '';
PRINT '脚本执行完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
