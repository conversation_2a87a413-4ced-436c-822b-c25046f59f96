using SqlSugar;
using ProjectManagement.Core.DTOs.KnowledgeGraph;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 知识关系仓储实现
    /// </summary>
    public class KnowledgeRelationRepository : IKnowledgeRelationRepository
    {
        private readonly ISqlSugarClient _db;

        public KnowledgeRelationRepository(ISqlSugarClient db)
        {
            _db = db;
        }

        public async Task<KnowledgeRelation> CreateAsync(KnowledgeRelation relation)
        {
            var relationData = new KnowledgeRelationEntity
            {
                FromEntityId = relation.FromEntityId,
                ToEntityId = relation.ToEntityId,
                RelationType = relation.RelationType,
                Weight = relation.Weight,
                Properties = relation.Properties,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var id = await _db.Insertable(relationData).ExecuteReturnIdentityAsync();
            relation.Id = id;
            return relation;
        }

        public async Task<KnowledgeRelation?> GetByIdAsync(int id)
        {
            var relationData = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.Id == id)
                .FirstAsync();

            return relationData == null ? null : MapToModel(relationData);
        }

        public async Task<List<KnowledgeRelation>> GetByFromEntityAsync(int fromEntityId)
        {
            var relations = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.FromEntityId == fromEntityId)
                .ToListAsync();

            return relations.Select(MapToModel).ToList();
        }

        public async Task<List<KnowledgeRelation>> GetByToEntityAsync(int toEntityId)
        {
            var relations = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.ToEntityId == toEntityId)
                .ToListAsync();

            return relations.Select(MapToModel).ToList();
        }

        public async Task<List<KnowledgeRelation>> GetByFromEntityAndTypeAsync(int fromEntityId, string relationType)
        {
            var relations = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.FromEntityId == fromEntityId && r.RelationType == relationType)
                .ToListAsync();

            return relations.Select(MapToModel).ToList();
        }

        public async Task<List<KnowledgeRelation>> GetByToEntityAndTypeAsync(int toEntityId, string relationType)
        {
            var relations = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.ToEntityId == toEntityId && r.RelationType == relationType)
                .ToListAsync();

            return relations.Select(MapToModel).ToList();
        }

        public async Task<List<KnowledgeRelation>> GetByTypeAsync(string relationType)
        {
            var relations = await _db.Queryable<KnowledgeRelationEntity>()
                .Where(r => r.RelationType == relationType)
                .ToListAsync();

            return relations.Select(MapToModel).ToList();
        }

        public async Task<KnowledgeRelation> UpdateAsync(KnowledgeRelation relation)
        {
            var relationData = new KnowledgeRelationEntity
            {
                Id = relation.Id,
                FromEntityId = relation.FromEntityId,
                ToEntityId = relation.ToEntityId,
                RelationType = relation.RelationType,
                Weight = relation.Weight,
                Properties = relation.Properties,
                UpdatedAt = DateTime.UtcNow
            };

            await _db.Updateable(relationData)
                .IgnoreColumns(r => r.CreatedAt)
                .ExecuteCommandAsync();

            return relation;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var result = await _db.Deleteable<KnowledgeRelationEntity>()
                .Where(r => r.Id == id)
                .ExecuteCommandAsync();

            return result > 0;
        }

        public async Task<bool> DeleteByEntitiesAsync(int fromEntityId, int toEntityId, string? relationType = null)
        {
            var queryable = _db.Deleteable<KnowledgeRelationEntity>()
                .Where(r => r.FromEntityId == fromEntityId && r.ToEntityId == toEntityId);

            if (!string.IsNullOrEmpty(relationType))
            {
                queryable = queryable.Where(r => r.RelationType == relationType);
            }

            var result = await queryable.ExecuteCommandAsync();
            return result > 0;
        }

        /// <summary>
        /// 将Entity映射为Model
        /// </summary>
        private KnowledgeRelation MapToModel(KnowledgeRelationEntity relation)
        {
            return new KnowledgeRelation
            {
                Id = relation.Id,
                FromEntityId = relation.FromEntityId,
                ToEntityId = relation.ToEntityId,
                RelationType = relation.RelationType,
                Weight = relation.Weight,
                Properties = relation.Properties,
                CreatedAt = relation.CreatedAt,
                UpdatedAt = relation.UpdatedAt
            };
        }
    }
}
