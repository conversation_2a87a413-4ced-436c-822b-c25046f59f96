import { aiProvider } from './aiProvider'
import { ApiService } from './api'
import { PromptService } from './promptService'
import type { PlaywrightTest, PlaywrightConfig } from './playwrightTest'

// AI测试生成服务
export class AITestGenerationService {
  
  // 1. 基于需求文档生成测试用例
  static async generateTestsFromRequirement(requirementId: number, projectId: number): Promise<PlaywrightTest[]> {
    try {
      // 获取需求文档内容
      const requirement = await this.getRequirementContent(requirementId)

      // 从数据库获取提示词模板
      const template = await PromptService.getTemplateByTaskType('Testing', 'Playwright需求测试生成')

      let prompt: string
      if (template) {
        // 使用数据库中的模板，替换参数
        prompt = this.replaceTemplateParameters(template.content, {
          requirementContent: requirement.content,
          projectName: requirement.projectName || '项目',
          baseUrl: 'http://localhost:3000',
          browsers: 'chromium, firefox, webkit'
        })

        // 记录模板使用
        await PromptService.recordTemplateUsage(template.id)
      } else {
        // 使用默认提示词作为后备
        prompt = `
基于以下需求文档，生成完整的Playwright自动化测试用例：

需求内容：
${requirement.content}

请生成以下类型的测试：
1. 功能测试 - 验证核心业务功能
2. 用户界面测试 - 验证UI交互和显示
3. 边界条件测试 - 验证异常情况处理
4. 用户体验测试 - 验证用户操作流程

每个测试用例应包含：
- 测试名称和描述
- 详细的测试步骤
- 预期结果验证
- 错误处理验证

请以JSON格式返回测试用例数组。
`
      }

      const response = await aiProvider.generateContent(prompt, 'test-generation')
      return this.parseTestCases(response, projectId, 'requirement')
    } catch (error) {
      console.error('生成需求测试用例失败:', error)
      throw error
    }
  }

  // 2. 基于页面结构生成测试用例
  static async generateTestsFromPageStructure(pageUrl: string, projectId: number): Promise<PlaywrightTest[]> {
    try {
      // 分析页面结构
      const pageStructure = await this.analyzePage(pageUrl)
      
      // 从数据库获取提示词模板
      const template = await PromptService.getTemplateByTaskType('Testing', 'Playwright页面测试生成')

      let prompt: string
      if (template) {
        // 使用数据库中的模板，替换参数
        prompt = this.replaceTemplateParameters(template.content, {
          pageUrl: pageUrl,
          pageTitle: pageStructure.title || '页面',
          pageElements: pageStructure.elements?.join(', ') || '未知',
          pageStructure: JSON.stringify(pageStructure, null, 2)
        })

        // 记录模板使用
        await PromptService.recordTemplateUsage(template.id)
      } else {
        // 使用默认提示词作为后备
        prompt = `
基于以下页面结构分析，生成Playwright自动化测试用例：

页面URL: ${pageUrl}
页面结构：
${JSON.stringify(pageStructure, null, 2)}

请生成以下测试：
1. 页面加载测试 - 验证页面正常加载
2. 元素存在性测试 - 验证关键元素存在
3. 表单交互测试 - 验证表单填写和提交
4. 导航测试 - 验证页面跳转和路由
5. 响应式测试 - 验证不同屏幕尺寸下的显示

每个测试用例应包含具体的选择器和操作步骤。
`
      }

      const response = await aiProvider.generateContent(prompt, 'test-generation')
      return this.parseTestCases(response, projectId, 'page')
    } catch (error) {
      console.error('生成页面测试用例失败:', error)
      throw error
    }
  }

  // 3. 基于业务流程生成端到端测试
  static async generateE2ETestsFromBusinessFlow(businessFlow: any, projectId: number): Promise<PlaywrightTest[]> {
    try {
      const prompt = `
基于以下业务流程，生成端到端(E2E)测试用例：

业务流程：
${JSON.stringify(businessFlow, null, 2)}

请生成完整的用户旅程测试，包括：
1. 用户注册/登录流程
2. 核心业务操作流程
3. 数据创建、编辑、删除流程
4. 权限验证流程
5. 异常情况处理流程

每个E2E测试应该模拟真实用户的完整操作路径。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseTestCases(response, projectId, 'e2e')
    } catch (error) {
      console.error('生成E2E测试用例失败:', error)
      throw error
    }
  }

  // 4. 智能测试用例优化
  static async optimizeTestCases(testCases: PlaywrightTest[]): Promise<PlaywrightTest[]> {
    try {
      const prompt = `
请优化以下Playwright测试用例，提高测试效率和覆盖率：

现有测试用例：
${JSON.stringify(testCases.map(t => ({ name: t.name, code: t.code })), null, 2)}

优化要求：
1. 消除重复的测试逻辑
2. 提高测试稳定性（添加等待和重试机制）
3. 增强错误处理
4. 优化选择器策略
5. 添加数据驱动测试
6. 提高测试可维护性

返回优化后的测试用例。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseOptimizedTestCases(response, testCases)
    } catch (error) {
      console.error('优化测试用例失败:', error)
      throw error
    }
  }

  // 5. 基于失败日志生成修复建议
  static async generateFixSuggestions(failedTest: PlaywrightTest, errorLog: string): Promise<string> {
    try {
      const prompt = `
以下Playwright测试执行失败，请分析错误原因并提供修复建议：

测试名称: ${failedTest.name}
测试代码:
${failedTest.code}

错误日志:
${errorLog}

请提供：
1. 错误原因分析
2. 具体修复建议
3. 优化后的测试代码
4. 预防类似问题的建议
`

      const response = await aiProvider.generateContent(prompt)
      return response
    } catch (error) {
      console.error('生成修复建议失败:', error)
      throw error
    }
  }

  // 辅助方法：获取需求内容
  private static async getRequirementContent(requirementId: number) {
    try {
      return await ApiService.get(`/api/requirements/${requirementId}`)
    } catch (error: any) {
      if (error.status === 401) {
        throw new Error('请先登录系统才能访问需求数据')
      }
      if (error.status === 404) {
        throw new Error('需求文档不存在')
      }
      throw new Error('获取需求内容失败: ' + (error.message || '未知错误'))
    }
  }

  // 辅助方法：替换模板参数
  private static replaceTemplateParameters(template: string, parameters: Record<string, string>): string {
    let result = template
    for (const [key, value] of Object.entries(parameters)) {
      const placeholder = `{${key}}`
      result = result.replace(new RegExp(placeholder, 'g'), value || '')
    }
    return result
  }

  // 辅助方法：分析页面结构
  private static async analyzePage(pageUrl: string) {
    try {
      // 这里可以集成页面分析工具，如Puppeteer或Playwright来分析页面结构
      return await ApiService.post('/api/page-analysis', { url: pageUrl })
    } catch (error: any) {
      if (error.status === 401) {
        throw new Error('请先登录系统才能使用页面分析功能')
      }
      throw new Error('页面分析失败: ' + (error.message || '未知错误'))
    }
  }

  // 辅助方法：解析测试用例
  private static parseTestCases(aiResponse: string, projectId: number, category: string): PlaywrightTest[] {
    try {
      const testCases = JSON.parse(aiResponse)
      return testCases.map((testCase: any, index: number) => ({
        id: Date.now() + index,
        name: testCase.name || `AI生成测试_${index + 1}`,
        description: testCase.description || '',
        category: category,
        browser: 'chromium',
        code: testCase.code || this.generateDefaultTestCode(testCase),
        config: this.getDefaultConfig(),
        tags: ['AI生成', category],
        priority: testCase.priority || 'medium',
        status: '草稿',
        createdTime: new Date(),
        updatedTime: new Date(),
        projectId: projectId
      }))
    } catch (error) {
      console.error('解析AI响应失败:', error)
      return []
    }
  }

  // 辅助方法：解析优化后的测试用例
  private static parseOptimizedTestCases(aiResponse: string, originalTests: PlaywrightTest[]): PlaywrightTest[] {
    try {
      const optimizedCases = JSON.parse(aiResponse)
      return originalTests.map((test, index) => ({
        ...test,
        code: optimizedCases[index]?.code || test.code,
        description: optimizedCases[index]?.description || test.description,
        updatedTime: new Date()
      }))
    } catch (error) {
      console.error('解析优化响应失败:', error)
      return originalTests
    }
  }

  // 生成默认测试代码
  private static generateDefaultTestCode(testCase: any): string {
    return `import { test, expect } from '@playwright/test';

test.describe('${testCase.name || 'AI生成测试'}', () => {
  test('${testCase.testName || '基础测试'}', async ({ page }) => {
    // ${testCase.description || '测试描述'}
    
    ${testCase.steps?.map((step: string, index: number) => 
      `// 步骤 ${index + 1}: ${step}`
    ).join('\n    ') || '// 在这里添加测试步骤'}
    
    // 验证结果
    ${testCase.assertions?.map((assertion: string) => 
      `// ${assertion}`
    ).join('\n    ') || '// 在这里添加断言'}
  });
});`
  }

  // 获取默认配置
  private static getDefaultConfig(): PlaywrightConfig {
    return {
      headless: false,
      viewport: { width: 1920, height: 1080 },
      timeout: 30000,
      baseURL: 'http://localhost:3000',
      browsers: ['chromium'],
      device: '',
      userAgent: '',
      parallel: false,
      workers: 1,
      retries: 1,
      globalTimeout: 300000,
      reporters: ['html', 'line'],
      screenshot: 'only-on-failure',
      video: 'retain-on-failure',
      trace: 'retain-on-failure',
      ignoreHTTPSErrors: false,
      javaScriptEnabled: true,
      waitForNetworkIdle: false,
      launchOptions: ''
    }
  }
}

// 测试生成配置
export interface TestGenerationConfig {
  projectId: number
  sourceType: 'requirement' | 'page' | 'business-flow' | 'api'
  sourceId?: number
  testTypes: string[]
  browsers: string[]
  priority: 'low' | 'medium' | 'high'
  generateE2E: boolean
  generateAPI: boolean
  generatePerformance: boolean
}

// 批量生成测试用例
export async function batchGenerateTests(config: TestGenerationConfig): Promise<PlaywrightTest[]> {
  const allTests: PlaywrightTest[] = []

  try {
    switch (config.sourceType) {
      case 'requirement':
        if (config.sourceId) {
          const requirementTests = await AITestGenerationService.generateTestsFromRequirement(
            config.sourceId, 
            config.projectId
          )
          allTests.push(...requirementTests)
        }
        break

      case 'page':
        // 基于项目页面生成测试
        const projectPages = await getProjectPages(config.projectId)
        for (const page of projectPages) {
          const pageTests = await AITestGenerationService.generateTestsFromPageStructure(
            page.url, 
            config.projectId
          )
          allTests.push(...pageTests)
        }
        break

      case 'business-flow':
        const businessFlows = await getBusinessFlows(config.projectId)
        for (const flow of businessFlows) {
          const flowTests = await AITestGenerationService.generateE2ETestsFromBusinessFlow(
            flow, 
            config.projectId
          )
          allTests.push(...flowTests)
        }
        break
    }

    // 优化生成的测试用例
    const optimizedTests = await AITestGenerationService.optimizeTestCases(allTests)
    
    return optimizedTests
  } catch (error) {
    console.error('批量生成测试失败:', error)
    throw error
  }
}

// 后端AI测试生成服务
export class BackendAITestGenerationService {

  // 异步生成测试用例（推荐）
  static async generateTestsAsync(config: TestGenerationConfig): Promise<{ taskId: string, conversationId: string }> {
    try {
      const response = await ApiService.post('/api/ai/generate-tests', {
        projectId: config.projectId,
        codeGenerationTaskId: config.codeGenerationTaskId,
        testType: config.testTypes?.[0] || 'E2E',
        testFramework: 'Playwright',
        coverageTarget: 80,
        preferredModel: 'deepseek-chat'
      })

      return {
        taskId: response.taskId,
        conversationId: response.conversationId
      }
    } catch (error: any) {
      console.error('后端AI测试生成失败:', error)
      throw new Error('后端AI测试生成失败: ' + (error.message || '未知错误'))
    }
  }

  // 同步生成测试用例（用于测试）
  static async generateTestsSync(config: TestGenerationConfig): Promise<PlaywrightTest[]> {
    try {
      const response = await ApiService.post('/api/ai/generate-tests-sync', {
        projectId: config.projectId,
        codeGenerationTaskId: config.codeGenerationTaskId,
        testType: config.testTypes?.[0] || 'E2E',
        testFramework: 'Playwright',
        coverageTarget: 80,
        preferredModel: 'deepseek-chat'
      })

      if (!response.success) {
        throw new Error(response.message)
      }

      // 转换为PlaywrightTest格式
      return response.testCases.map((testCase: any) => ({
        id: testCase.id,
        name: testCase.name,
        description: testCase.description,
        code: testCase.code,
        category: 'e2e',
        priority: testCase.priority || 'medium',
        tags: testCase.tags || [],
        status: 'draft',
        projectId: config.projectId,
        createdTime: testCase.createdTime || new Date().toISOString(),
        updatedTime: new Date().toISOString()
      }))
    } catch (error: any) {
      console.error('后端同步AI测试生成失败:', error)
      throw new Error('后端同步AI测试生成失败: ' + (error.message || '未知错误'))
    }
  }

  // 检查任务状态
  static async checkTaskStatus(taskId: string): Promise<any> {
    try {
      return await ApiService.get(`/api/ai/task-status/${taskId}`)
    } catch (error: any) {
      console.error('检查任务状态失败:', error)
      throw new Error('检查任务状态失败: ' + (error.message || '未知错误'))
    }
  }
}

// 辅助函数
async function getProjectPages(projectId: number) {
  try {
    return await ApiService.get(`/api/projects/${projectId}/pages`)
  } catch (error: any) {
    if (error.status === 401) {
      throw new Error('请先登录系统才能访问项目页面数据')
    }
    if (error.status === 404) {
      throw new Error('项目不存在或没有页面数据')
    }
    throw new Error('获取项目页面失败: ' + (error.message || '未知错误'))
  }
}

async function getBusinessFlows(projectId: number) {
  try {
    return await ApiService.get(`/api/projects/${projectId}/business-flows`)
  } catch (error: any) {
    if (error.status === 401) {
      throw new Error('请先登录系统才能访问业务流程数据')
    }
    if (error.status === 404) {
      throw new Error('项目不存在或没有业务流程数据')
    }
    throw new Error('获取业务流程失败: ' + (error.message || '未知错误'))
  }
}