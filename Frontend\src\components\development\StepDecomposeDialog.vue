<template>
  <el-dialog
    v-model="dialogVisible"
    title="AI分解开发步骤"
    width="70%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="step" class="decompose-container">
      <!-- 原步骤信息 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>原步骤信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="步骤名称">{{ step.stepName }}</el-descriptions-item>
          <el-descriptions-item label="步骤类型">{{ step.stepType }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ step.priority }}</el-descriptions-item>
          <el-descriptions-item label="技术栈">{{ step.technologyStack || '未指定' }}</el-descriptions-item>
          <el-descriptions-item label="预估工时">{{ step.estimatedHours || 0 }} 小时</el-descriptions-item>
          <el-descriptions-item label="当前层级">第 {{ step.stepLevel }} 层</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div class="step-description">{{ step.stepDescription || '无描述' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 分解选项 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>分解选项</span>
        </template>
        <el-form :model="decompositionOptions" label-width="120px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="分解粒度">
                <el-select v-model="decompositionOptions.granularity" placeholder="选择分解粒度">
                  <el-option label="细粒度" value="Fine" />
                  <el-option label="中等粒度" value="Medium" />
                  <el-option label="粗粒度" value="Coarse" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大子步骤数">
                <el-input-number 
                  v-model="decompositionOptions.maxSubSteps" 
                  :min="2" 
                  :max="20" 
                  placeholder="最大子步骤数量"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="技术栈偏好">
                <el-input 
                  v-model="decompositionOptions.technologyPreference" 
                  placeholder="指定技术栈偏好（可选）"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选项">
                <el-checkbox v-model="decompositionOptions.includeTestSteps">包含测试步骤</el-checkbox>
                <el-checkbox v-model="decompositionOptions.includeDocumentationSteps">包含文档步骤</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="自定义要求">
            <el-input
              v-model="decompositionOptions.customRequirements"
              type="textarea"
              :rows="3"
              placeholder="输入自定义分解要求（可选）"
            />
          </el-form-item>

          <!-- AI供应商选择 -->
          <el-form-item label="AI模型" required>
            <el-select
              v-model="selectedAIProviderId"
              placeholder="选择AI模型"
              style="width: 100%"
              :loading="loadingAIProviders"
            >
              <el-option
                v-for="provider in aiProviders"
                :key="provider.id"
                :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
                :value="provider.id"
                :disabled="!provider.isActive"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ provider.modelName }}</span>
                  <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 分解结果 -->
      <el-card v-if="decompositionResult" shadow="never" class="mb-4">
        <template #header>
          <span>分解结果</span>
        </template>
        
        <!-- AI分析 -->
        <div v-if="decompositionResult.decompositionAnalysis" class="mb-4">
          <h4>AI分析</h4>
          <el-alert
            :title="decompositionResult.decompositionAnalysis"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 子步骤列表 -->
        <div v-if="decompositionResult.childSteps && decompositionResult.childSteps.length > 0">
          <div class="steps-header">
            <h4>生成的子步骤 ({{ decompositionResult.childSteps.length }} 个)</h4>
            <div class="steps-actions" v-if="isPreviewMode">
              <el-alert
                title="预览模式"
                description="这些子步骤尚未保存到数据库，请确认后保存"
                type="warning"
                :closable="false"
                style="margin-bottom: 16px;"
              />
              <div class="action-buttons">
                <el-button
                  type="primary"
                  :loading="confirmingSave"
                  @click="confirmAndSaveSteps"
                  size="default"
                >
                  <el-icon><Check /></el-icon>
                  确认并保存子步骤
                </el-button>
                <el-button @click="regenerateSteps" size="default">
                  <el-icon><Refresh /></el-icon>
                  重新生成
                </el-button>
              </div>
            </div>
            <div v-else-if="!isPreviewMode && decompositionResult.childSteps">
              <el-alert
                title="已保存"
                description="子步骤已成功保存到数据库"
                type="success"
                :closable="false"
                style="margin-bottom: 16px;"
              />
            </div>
          </div>
          <el-table :data="decompositionResult.childSteps" border>
            <el-table-column prop="stepName" label="步骤名称" min-width="200" />
            <el-table-column prop="stepDescription" label="描述" min-width="250" show-overflow-tooltip />
            <el-table-column prop="stepType" label="类型" width="100" />
            <el-table-column prop="priority" label="优先级" width="80" />
            <el-table-column prop="estimatedHours" label="预估工时" width="100">
              <template #default="{ row }">
                {{ row.estimatedHours || 0 }} 小时
              </template>
            </el-table-column>
            <el-table-column prop="technologyStack" label="技术栈" width="120" show-overflow-tooltip />
            <el-table-column label="操作" width="160" v-if="isPreviewMode">
              <template #default="{ $index }">
                <el-button
                  type="primary"
                  size="small"
                  @click="editStep($index)"
                  :icon="Edit"
                  style="margin-right: 8px;"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="removeStep($index)"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="decomposing">取消</el-button>
        <el-button 
          type="primary" 
          @click="startDecomposition"
          :loading="decomposing"
          :disabled="!step"
        >
          {{ decomposing ? '分解中...' : '开始分解' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 编辑步骤对话框 -->
  <el-dialog
    v-model="showEditDialog"
    title="编辑子步骤"
    width="60%"
    :before-close="handleEditClose"
  >
    <el-form
      ref="editFormRef"
      :model="editingStepData"
      :rules="editFormRules"
      label-width="120px"
    >
      <el-form-item label="步骤名称" prop="stepName">
        <el-input v-model="editingStepData.stepName" placeholder="请输入步骤名称" />
      </el-form-item>

      <el-form-item label="步骤描述" prop="stepDescription">
        <el-input
          v-model="editingStepData.stepDescription"
          type="textarea"
          :rows="3"
          placeholder="请输入步骤描述"
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="步骤类型" prop="stepType">
            <el-select v-model="editingStepData.stepType" placeholder="选择步骤类型">
              <el-option label="开发" value="Development" />
              <el-option label="测试" value="Testing" />
              <el-option label="文档" value="Documentation" />
              <el-option label="部署" value="Deployment" />
              <el-option label="配置" value="Configuration" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="editingStepData.priority" placeholder="选择优先级">
              <el-option label="高" value="High" />
              <el-option label="中" value="Medium" />
              <el-option label="低" value="Low" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="预估工时" prop="estimatedHours">
            <el-input-number
              v-model="editingStepData.estimatedHours"
              :min="0"
              :step="0.5"
              placeholder="预估工时（小时）"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="技术栈" prop="technologyStack">
            <el-input v-model="editingStepData.technologyStack" placeholder="技术栈" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="文件类型" prop="fileType">
            <el-input v-model="editingStepData.fileType" placeholder="文件类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件路径" prop="filePath">
            <el-input v-model="editingStepData.filePath" placeholder="文件路径" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="组件类型" prop="componentType">
        <el-input v-model="editingStepData.componentType" placeholder="组件类型" />
      </el-form-item>

      <el-form-item label="AI提示词" prop="aiPrompt">
        <el-input
          v-model="editingStepData.aiPrompt"
          type="textarea"
          :rows="2"
          placeholder="AI提示词"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleEditClose">取消</el-button>
        <el-button type="primary" @click="saveStepEdit" :loading="savingEdit">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Refresh, Delete, Edit } from '@element-plus/icons-vue'
import type { DevelopmentStep } from '@/types/development'
import { DevelopmentService } from '@/services/development'
import { AIProviderService } from '@/services/aiProvider'

// Props
interface Props {
  modelValue: boolean
  step?: DevelopmentStep
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  step: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'decompose-success': [result: any]
}>()

// 响应式数据
const decomposing = ref(false)
const confirmingSave = ref(false)
const decompositionResult = ref<any>(null)
const isPreviewMode = ref(false)
const showEditDialog = ref(false)
const savingEdit = ref(false)
const editingStepIndex = ref(-1)
const editFormRef = ref()

// AI供应商相关数据
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)
const selectedAIProviderId = ref<number | undefined>(undefined)

// 编辑表单数据
const editingStepData = ref({
  stepName: '',
  stepDescription: '',
  stepType: '',
  priority: '',
  estimatedHours: 0,
  technologyStack: '',
  fileType: '',
  filePath: '',
  componentType: '',
  aiPrompt: ''
})

// 分解选项
const decompositionOptions = ref({
  granularity: 'Medium',
  maxSubSteps: 8,
  technologyPreference: '',
  includeTestSteps: true,
  includeDocumentationSteps: false,
  customRequirements: ''
})

// 编辑表单验证规则
const editFormRules = {
  stepName: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' },
    { min: 2, max: 100, message: '步骤名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  stepDescription: [
    { required: true, message: '请输入步骤描述', trigger: 'blur' },
    { min: 5, max: 500, message: '步骤描述长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  stepType: [
    { required: true, message: '请选择步骤类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听步骤变化，重置状态
watch(() => props.step, () => {
  decompositionResult.value = null
  // 根据步骤信息设置默认技术栈偏好
  if (props.step?.technologyStack) {
    decompositionOptions.value.technologyPreference = props.step.technologyStack
  }
})

// 方法
const handleClose = () => {
  if (!decomposing.value) {
    dialogVisible.value = false
  }
}

const startDecomposition = async () => {
  if (!props.step) {
    ElMessage.error('步骤信息不完整')
    return
  }

  if (!selectedAIProviderId.value) {
    ElMessage.error('请选择AI模型')
    return
  }

  decomposing.value = true
  decompositionResult.value = null

  try {
    const result = await DevelopmentService.decomposeStep(props.step.id, {
      decompositionOptions: decompositionOptions.value,
      aiProviderId: selectedAIProviderId.value
    })

    if (result.success) {
      decompositionResult.value = result.data
      isPreviewMode.value = result.isPreview || false

      if (result.isPreview) {
        ElMessage.success(`步骤分解成功，生成 ${result.data?.childSteps?.length || 0} 个子步骤（预览模式）`)
      } else {
        ElMessage.success(`步骤分解成功，生成 ${result.data?.childSteps?.length || 0} 个子步骤`)
        emit('decompose-success', result.data)
      }
    } else {
      ElMessage.error(result.message || '步骤分解失败')
    }
  } catch (error: any) {
    console.error('步骤分解失败:', error)
    ElMessage.error(error.message || '步骤分解失败')
  } finally {
    decomposing.value = false
  }
}

const confirmAndSaveSteps = async () => {
  if (!decompositionResult.value?.childSteps || !props.step) return

  try {
    confirmingSave.value = true

    // 转换子步骤数据格式
    const childSteps = decompositionResult.value.childSteps.map((step: any) => ({
      stepName: step.stepName,
      stepDescription: step.stepDescription,
      stepType: step.stepType,
      priority: step.priority,
      estimatedHours: step.estimatedHours,
      technologyStack: step.technologyStack,
      fileType: step.fileType,
      filePath: step.filePath,
      componentType: step.componentType,
      aiPrompt: step.aiPrompt
    }))

    const result = await DevelopmentService.confirmStepDecomposition(props.step.id, {
      childSteps
    })

    if (result.success) {
      // 更新结果为已保存状态
      decompositionResult.value = result.data
      isPreviewMode.value = false
      ElMessage.success('子步骤已成功保存到数据库！')
      emit('decompose-success', result.data)
    } else {
      ElMessage.error(result.message || '保存子步骤失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存子步骤失败')
  } finally {
    confirmingSave.value = false
  }
}

const regenerateSteps = () => {
  // 重置状态，重新分解
  decompositionResult.value = null
  isPreviewMode.value = false
  startDecomposition()
}

const removeStep = (index: number) => {
  if (!decompositionResult.value?.childSteps) return

  // 删除指定索引的步骤
  decompositionResult.value.childSteps.splice(index, 1)

  // 如果没有步骤了，提示用户
  if (decompositionResult.value.childSteps.length === 0) {
    ElMessage.warning('所有子步骤已删除，请重新生成或关闭对话框')
  } else {
    ElMessage.success('步骤已删除')
  }
}

const editStep = (index: number) => {
  if (!decompositionResult.value?.childSteps?.[index]) return

  const step = decompositionResult.value.childSteps[index]
  editingStepIndex.value = index

  // 填充编辑表单
  editingStepData.value = {
    stepName: step.stepName || '',
    stepDescription: step.stepDescription || '',
    stepType: step.stepType || '',
    priority: step.priority || '',
    estimatedHours: step.estimatedHours || 0,
    technologyStack: step.technologyStack || '',
    fileType: step.fileType || '',
    filePath: step.filePath || '',
    componentType: step.componentType || '',
    aiPrompt: step.aiPrompt || ''
  }

  showEditDialog.value = true
}

const handleEditClose = () => {
  if (!savingEdit.value) {
    showEditDialog.value = false
    editingStepIndex.value = -1
  }
}

const saveStepEdit = async () => {
  if (!editFormRef.value) return

  try {
    // 验证表单
    await editFormRef.value.validate()

    savingEdit.value = true

    // 更新步骤数据
    if (decompositionResult.value?.childSteps?.[editingStepIndex.value]) {
      const step = decompositionResult.value.childSteps[editingStepIndex.value]
      Object.assign(step, editingStepData.value)

      ElMessage.success('步骤编辑成功')
      showEditDialog.value = false
      editingStepIndex.value = -1
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    savingEdit.value = false
  }
}

// 加载AI供应商列表
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const response = await AIProviderService.getModelConfigurations()
    aiProviders.value = response || []
    // 如果有可用的AI供应商，默认选择第一个激活的
    const activeProvider = aiProviders.value.find(p => p.isActive)
    if (activeProvider && !selectedAIProviderId.value) {
      selectedAIProviderId.value = activeProvider.id
    }
  } catch (error) {
    console.error('加载AI供应商失败:', error)
    ElMessage.error('加载AI供应商失败')
  } finally {
    loadingAIProviders.value = false
  }
}

// 组件挂载时加载AI供应商
onMounted(() => {
  loadAIProviders()
})
</script>

<style scoped>
.decompose-container {
  max-height: 70vh;
  overflow-y: auto;
}

.step-description {
  white-space: pre-wrap;
  word-break: break-word;
}

.dialog-footer {
  text-align: right;
}

.mb-4 {
  margin-bottom: 16px;
}

.el-checkbox {
  margin-right: 16px;
}

.steps-header {
  margin-bottom: 16px;
}

.steps-actions {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}
</style>
