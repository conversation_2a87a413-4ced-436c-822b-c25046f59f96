using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using System.Linq;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 开发步骤仓储实现
/// </summary>
public class DevelopmentStepRepository : BaseRepository<DevelopmentStep>, IDevelopmentStepRepository
{
    public DevelopmentStepRepository(ISqlSugarClient db, ILogger<DevelopmentStepRepository> logger)
        : base(db, logger)
    {
    }

    #region 项目相关查询

    public async Task<List<DevelopmentStep>> GetByProjectIdAsync(int projectId, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ProjectId == projectId);

            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            return await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据项目ID获取开发步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<PagedResult<DevelopmentStep>> GetByProjectIdPagedAsync(
        int projectId,
        int pageIndex,
        int pageSize,
        string? status = null,
        string? priority = null,
        string? stepType = null)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ProjectId == projectId && !x.IsDeleted);

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(x => x.Status == status);
            }

            if (!string.IsNullOrEmpty(priority))
            {
                query = query.Where(x => x.Priority == priority);
            }

            if (!string.IsNullOrEmpty(stepType))
            {
                query = query.Where(x => x.StepType == stepType);
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<DevelopmentStep>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取项目开发步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    #endregion

    #region 需求文档相关查询

    public async Task<List<DevelopmentStep>> GetByRequirementDocumentIdAsync(int requirementDocumentId, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.RequirementDocumentId == requirementDocumentId);

            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            return await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据需求文档ID获取开发步骤失败，RequirementDocumentId: {RequirementDocumentId}", requirementDocumentId);
            throw;
        }
    }

    #endregion

    #region 层级结构查询

    public async Task<List<DevelopmentStep>> GetTopLevelStepsAsync(int projectId, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ProjectId == projectId && x.ParentStepId == null);

            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            return await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取顶级步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<DevelopmentStep>> GetChildStepsAsync(int parentStepId, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ParentStepId == parentStepId);

            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            return await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取子步骤失败，ParentStepId: {ParentStepId}", parentStepId);
            throw;
        }
    }

    public async Task<DevelopmentStep?> GetStepWithHierarchyAsync(int stepId, bool includeDeleted = false)
    {
        try
        {
            var step = await GetByIdAsync(stepId);
            if (step == null) return null;

            // 递归加载子步骤
            await LoadChildStepsRecursive(step, includeDeleted);

            return step;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取步骤层级结构失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<List<DevelopmentStep>> GetProjectStepTreeAsync(int projectId, bool includeDeleted = false)
    {
        try
        {
            var topLevelSteps = await GetTopLevelStepsAsync(projectId, includeDeleted);

            // 为每个顶级步骤加载子步骤
            foreach (var step in topLevelSteps)
            {
                await LoadChildStepsRecursive(step, includeDeleted);
            }

            return topLevelSteps;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目步骤树失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 递归加载子步骤
    /// </summary>
    private async Task LoadChildStepsRecursive(DevelopmentStep parentStep, bool includeDeleted)
    {
        var childSteps = await GetChildStepsAsync(parentStep.Id, includeDeleted);
        parentStep.ChildSteps = childSteps;

        foreach (var childStep in childSteps)
        {
            await LoadChildStepsRecursive(childStep, includeDeleted);
        }
    }

    #endregion

    #region 状态和进度查询

    public async Task<List<DevelopmentStep>> GetByStatusAsync(int projectId, string status, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ProjectId == projectId && x.Status == status);

            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            return await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据状态获取步骤失败，ProjectId: {ProjectId}, Status: {Status}", projectId, status);
            throw;
        }
    }

    public async Task<List<DevelopmentStep>> GetExecutableStepsAsync(int projectId)
    {
        try
        {
            // 获取所有待处理的步骤
            var pendingSteps = await GetByStatusAsync(projectId, "Pending");
            var executableSteps = new List<DevelopmentStep>();

            foreach (var step in pendingSteps)
            {
                // 检查依赖是否满足
                var dependencies = await GetStepDependenciesAsync(step.Id);
                var canExecute = true;

                foreach (var dependency in dependencies)
                {
                    var dependsOnStep = await GetByIdAsync(dependency.DependsOnStepId);
                    if (dependsOnStep != null && !dependency.IsSatisfied())
                    {
                        canExecute = false;
                        break;
                    }
                }

                if (canExecute)
                {
                    executableSteps.Add(step);
                }
            }

            return executableSteps;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可执行步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<List<DevelopmentStep>> GetBlockedStepsAsync(int projectId)
    {
        try
        {
            var pendingSteps = await GetByStatusAsync(projectId, "Pending");
            var executableSteps = await GetExecutableStepsAsync(projectId);

            // 被阻塞的步骤 = 待处理步骤 - 可执行步骤
            return pendingSteps.Except(executableSteps).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取被阻塞步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    #endregion

    #region 依赖关系查询

    public async Task<List<StepDependency>> GetStepDependenciesAsync(int stepId)
    {
        try
        {
            return await _db.Queryable<StepDependency>()
                .Where(x => x.StepId == stepId && !x.IsDeleted)
                .Includes(x => x.DependsOnStep)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取步骤依赖失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<List<StepDependency>> GetStepDependentsAsync(int stepId)
    {
        try
        {
            return await _db.Queryable<StepDependency>()
                .Where(x => x.DependsOnStepId == stepId && !x.IsDeleted)
                .Includes(x => x.Step)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取步骤被依赖关系失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<bool> HasCircularDependencyAsync(int projectId)
    {
        try
        {
            var allDependencies = await _db.Queryable<StepDependency>()
                .LeftJoin<DevelopmentStep>((d, s) => d.StepId == s.Id)
                .Where((d, s) => s.ProjectId == projectId && !d.IsDeleted && !s.IsDeleted)
                .Select(d => d)
                .ToListAsync();

            // 使用深度优先搜索检测循环依赖
            var visited = new HashSet<int>();
            var recursionStack = new HashSet<int>();

            var allStepIds = allDependencies.Select(d => d.StepId).Union(allDependencies.Select(d => d.DependsOnStepId)).Distinct();

            foreach (var stepId in allStepIds)
            {
                if (!visited.Contains(stepId))
                {
                    if (HasCyclicDependencyDFS(stepId, allDependencies, visited, recursionStack))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查循环依赖失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 深度优先搜索检测循环依赖
    /// </summary>
    private bool HasCyclicDependencyDFS(int stepId, List<StepDependency> dependencies, HashSet<int> visited, HashSet<int> recursionStack)
    {
        visited.Add(stepId);
        recursionStack.Add(stepId);

        var stepDependencies = dependencies.Where(d => d.StepId == stepId);

        foreach (var dependency in stepDependencies)
        {
            var dependsOnId = dependency.DependsOnStepId;

            if (recursionStack.Contains(dependsOnId))
                return true;

            if (!visited.Contains(dependsOnId) && HasCyclicDependencyDFS(dependsOnId, dependencies, visited, recursionStack))
                return true;
        }

        recursionStack.Remove(stepId);
        return false;
    }

    #endregion

    #region 执行历史查询

    public async Task<PagedResult<StepExecutionHistory>> GetStepExecutionHistoryAsync(int stepId, int pageIndex = 1, int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<StepExecutionHistory>()
                .Where(x => x.StepId == stepId && !x.IsDeleted);

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(x => x.ExecutionStartTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<StepExecutionHistory>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取步骤执行历史失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<StepExecutionHistory?> GetLatestExecutionHistoryAsync(int stepId)
    {
        try
        {
            return await _db.Queryable<StepExecutionHistory>()
                .Where(x => x.StepId == stepId && !x.IsDeleted)
                .OrderByDescending(x => x.ExecutionStartTime)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最新执行历史失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    #endregion

    #region 统计查询

    public async Task<ProjectStepStatistics> GetProjectStepStatisticsAsync(int projectId)
    {
        try
        {
            var result = await _db.Queryable<DevelopmentStep>()
                .LeftJoin<Project>((ds, p) => ds.ProjectId == p.Id)
                .Where((ds, p) => ds.ProjectId == projectId && !ds.IsDeleted)
                .GroupBy((ds, p) => new { p.Id, p.Name })
                .Select((ds, p) => new ProjectStepStatistics
                {
                    ProjectId = p.Id,
                    ProjectName = p.Name,
                    TotalSteps = SqlFunc.AggregateCount(ds.Id),
                    PendingSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Pending", 1, 0)),
                    InProgressSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "InProgress", 1, 0)),
                    CompletedSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Completed", 1, 0)),
                    FailedSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Failed", 1, 0)),
                    BlockedSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Blocked", 1, 0)),
                    AverageProgress = SqlFunc.AggregateAvg(ds.Progress),
                    TotalEstimatedHours = SqlFunc.AggregateSum(SqlFunc.IsNull(ds.EstimatedHours, 0)) ?? 0,
                    TotalActualHours = SqlFunc.AggregateSum(SqlFunc.IsNull(ds.ActualHours, 0)) ?? 0,
                    TechnologyStackCount = SqlFunc.AggregateDistinctCount(ds.TechnologyStack),
                    FirstStepCreated = SqlFunc.AggregateMin(ds.CreatedTime),
                    LastStepUpdated = SqlFunc.AggregateMax(ds.UpdatedTime)
                })
                .FirstAsync();

            return result ?? new ProjectStepStatistics { ProjectId = projectId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目步骤统计失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<UserStepStatistics> GetUserStepStatisticsAsync(int userId, int? projectId = null)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .LeftJoin<User>((ds, u) => ds.CreatedBy == u.Id)
                .Where((ds, u) => ds.CreatedBy == userId && !ds.IsDeleted);

            if (projectId.HasValue)
            {
                query = query.Where((ds, u) => ds.ProjectId == projectId.Value);
            }

            var result = await query
                .GroupBy((ds, u) => new { u.Id, u.Username })
                .Select((ds, u) => new UserStepStatistics
                {
                    UserId = u.Id,
                    UserName = u.Username,
                    TotalSteps = SqlFunc.AggregateCount(ds.Id),
                    CompletedSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Completed", 1, 0)),
                    InProgressSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "InProgress", 1, 0)),
                    FailedSteps = SqlFunc.AggregateSum(SqlFunc.IIF(ds.Status == "Failed", 1, 0)),
                    TotalHours = SqlFunc.AggregateSum(SqlFunc.IsNull(ds.ActualHours, 0)) ?? 0,
                    ProjectCount = SqlFunc.AggregateDistinctCount(ds.ProjectId)
                })
                .FirstAsync();

            if (result != null && result.TotalSteps > 0)
            {
                result.CompletionRate = (double)result.CompletedSteps / result.TotalSteps * 100;
            }

            return result ?? new UserStepStatistics { UserId = userId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户步骤统计失败，UserId: {UserId}", userId);
            throw;
        }
    }

    #endregion

    #region 批量操作

    public async Task<int> BatchUpdateStatusAsync(List<int> stepIds, string status, int? updatedBy = null)
    {
        try
        {
            return await _db.Updateable<DevelopmentStep>()
                .SetColumns(x => new DevelopmentStep
                {
                    Status = status,
                    UpdatedTime = DateTime.Now,
                    UpdatedBy = updatedBy
                })
                .Where(x => stepIds.Contains(x.Id) && !x.IsDeleted)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新步骤状态失败，StepIds: {StepIds}, Status: {Status}", string.Join(",", stepIds), status);
            throw;
        }
    }

    public async Task<int> BatchUpdatePriorityAsync(List<int> stepIds, string priority, int? updatedBy = null)
    {
        try
        {
            return await _db.Updateable<DevelopmentStep>()
                .SetColumns(x => new DevelopmentStep
                {
                    Priority = priority,
                    UpdatedTime = DateTime.Now,
                    UpdatedBy = updatedBy
                })
                .Where(x => stepIds.Contains(x.Id) && !x.IsDeleted)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新步骤优先级失败，StepIds: {StepIds}, Priority: {Priority}", string.Join(",", stepIds), priority);
            throw;
        }
    }

    public async Task<int> ReorderStepsAsync(Dictionary<int, int> stepOrders, int? updatedBy = null)
    {
        try
        {
            var updateCount = 0;
            foreach (var kvp in stepOrders)
            {
                var count = await _db.Updateable<DevelopmentStep>()
                    .SetColumns(x => new DevelopmentStep
                    {
                        StepOrder = kvp.Value,
                        UpdatedTime = DateTime.Now,
                        UpdatedBy = updatedBy
                    })
                    .Where(x => x.Id == kvp.Key && !x.IsDeleted)
                    .ExecuteCommandAsync();
                updateCount += count;
            }
            return updateCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新排序步骤失败");
            throw;
        }
    }

    #endregion

    #region 搜索功能

    public async Task<PagedResult<DevelopmentStep>> SearchStepsAsync(int projectId, string keyword, int pageIndex = 1, int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(x => x.ProjectId == projectId && !x.IsDeleted);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(x =>
                    x.StepName.Contains(keyword) ||
                    x.StepDescription.Contains(keyword) ||
                    x.TechnologyStack.Contains(keyword) ||
                    x.ComponentType.Contains(keyword));
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(x => new { x.StepOrder, x.CreatedTime })
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<DevelopmentStep>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索开发步骤失败，ProjectId: {ProjectId}, Keyword: {Keyword}", projectId, keyword);
            throw;
        }
    }

    #endregion
}