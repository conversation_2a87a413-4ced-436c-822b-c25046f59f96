import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { AuthService } from '@/services/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types'

// JWT token解析工具
function parseJwt(token: string) {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    }).join(''))
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('解析JWT token失败:', error)
    return null
  }
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refreshToken'))
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 定时器引用
  let tokenCheckTimer: NodeJS.Timeout | null = null

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => {
    if (!user.value) return false
    const role = user.value.role
    return role === 'Admin' || role === 'SuperAdmin'
  })
  const userRole = computed(() => {
    if (!user.value) return ''

    const role = user.value.role
    const roleMap: Record<string, string> = {
      'User': '普通用户',
      'ProjectManager': '项目经理',
      'Developer': '开发人员',
      'Tester': '测试人员',
      'ProductManager': '产品经理',
      'Admin': '管理员',
      'SuperAdmin': '超级管理员'
    }

    return roleMap[role] || '未知角色'
  })

  // 操作方法
  const login = async (request: LoginRequest) => {
    try {
      loading.value = true
      const response = await AuthService.login(request)

      // 保存认证信息
      token.value = response.accessToken
      refreshToken.value = response.refreshToken
      user.value = response.user

      // 持久化存储
      localStorage.setItem('token', response.accessToken)
      localStorage.setItem('refreshToken', response.refreshToken)
      localStorage.setItem('user', JSON.stringify(response.user))

      // 启动token检查定时器
      startTokenCheck()

      return response
    } finally {
      loading.value = false
    }
  }

  const register = async (request: RegisterRequest) => {
    try {
      loading.value = true
      await AuthService.register(request)
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await AuthService.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
      // 如果logout请求失败，仍然清除本地状态
    } finally {
      // 清除本地状态
      logoutLocal()
    }
  }

  // 只清除本地状态的登出方法（不调用后端接口）
  const logoutLocal = () => {
    // 停止token检查定时器
    stopTokenCheck()

    // 清除本地状态
    token.value = null
    refreshToken.value = null
    user.value = null

    // 清除持久化存储
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
  }

  const refreshTokenAction = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await AuthService.refreshToken(refreshToken.value)

      // 更新认证信息
      token.value = response.accessToken
      refreshToken.value = response.refreshToken
      user.value = response.user

      // 更新持久化存储
      localStorage.setItem('token', response.accessToken)
      localStorage.setItem('refreshToken', response.refreshToken)
      localStorage.setItem('user', JSON.stringify(response.user))

      // 重新启动token检查定时器
      startTokenCheck()

      return response
    } catch (error) {
      // 刷新失败，清除认证信息，使用logoutLocal避免循环
      logoutLocal()
      throw error
    }
  }

  // 检查token是否即将过期
  const isTokenExpiringSoon = (): boolean => {
    if (!token.value) return false

    const payload = parseJwt(token.value)
    if (!payload || !payload.exp) return false

    const currentTime = Math.floor(Date.now() / 1000)
    const expirationTime = payload.exp
    const timeUntilExpiry = expirationTime - currentTime

    // 如果token在5分钟内过期，认为即将过期
    return timeUntilExpiry <= 300
  }

  // 检查token是否已过期
  const isTokenExpired = (): boolean => {
    if (!token.value) return true

    const payload = parseJwt(token.value)
    if (!payload || !payload.exp) return true

    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp <= currentTime
  }

  // 自动检查并刷新token
  const checkAndRefreshToken = async () => {
    if (!token.value || !refreshToken.value) return

    if (isTokenExpired()) {
      console.log('Token已过期，尝试刷新...')
      try {
        await refreshTokenAction()
        console.log('Token自动刷新成功')
      } catch (error) {
        console.error('Token自动刷新失败:', error)
      }
    } else if (isTokenExpiringSoon()) {
      console.log('Token即将过期，提前刷新...')
      try {
        await refreshTokenAction()
        console.log('Token提前刷新成功')
      } catch (error) {
        console.error('Token提前刷新失败:', error)
      }
    }
  }

  // 启动token检查定时器
  const startTokenCheck = () => {
    // 清除现有定时器
    if (tokenCheckTimer) {
      clearInterval(tokenCheckTimer)
    }

    // 每分钟检查一次token状态
    tokenCheckTimer = setInterval(checkAndRefreshToken, 60000)
  }

  // 停止token检查定时器
  const stopTokenCheck = () => {
    if (tokenCheckTimer) {
      clearInterval(tokenCheckTimer)
      tokenCheckTimer = null
    }
  }

  const getCurrentUser = async () => {
    if (!token.value) return null

    try {
      loading.value = true
      const currentUser = await AuthService.getCurrentUser()
      user.value = currentUser
      localStorage.setItem('user', JSON.stringify(currentUser))
      return currentUser
    } catch (error) {
      console.error('Get current user error:', error)
      // 获取用户信息失败，可能token已过期，使用logoutLocal避免循环
      logoutLocal()
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (userData: Partial<User>) => {
    try {
      loading.value = true
      const updatedUser = await AuthService.updateProfile(userData)
      user.value = updatedUser
      localStorage.setItem('user', JSON.stringify(updatedUser))
      return updatedUser
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    try {
      loading.value = true
      await AuthService.changePassword(data)
    } finally {
      loading.value = false
    }
  }

  // 初始化用户信息
  const initializeAuth = async () => {
    const storedUser = localStorage.getItem('user')
    if (storedUser && token.value) {
      try {
        loading.value = true
        user.value = JSON.parse(storedUser)

        // 检查token是否已过期
        if (isTokenExpired()) {
          console.log('存储的token已过期，尝试刷新...')
          if (refreshToken.value) {
            await refreshTokenAction()
          } else {
            logoutLocal()
            return
          }
        } else {
          // Token未过期，但需要验证token的有效性
          console.log('Token未过期，验证token有效性...')
          try {
            // 调用后端验证token并获取最新用户信息
            const currentUser = await AuthService.getCurrentUser()
            user.value = currentUser
            localStorage.setItem('user', JSON.stringify(currentUser))
            console.log('Token验证成功，用户信息已更新')
            startTokenCheck()
          } catch (error) {
            console.log('Token验证失败，尝试刷新token...')
            if (refreshToken.value) {
              try {
                await refreshTokenAction()
                console.log('Token刷新成功')
              } catch (refreshError) {
                console.error('Token刷新失败:', refreshError)
                logoutLocal()
              }
            } else {
              logoutLocal()
            }
          }
        }
      } catch (error) {
        console.error('Initialize auth error:', error)
        logoutLocal()
      } finally {
        loading.value = false
      }
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false

    const role = user.value.role
    console.log('检查权限:', { role, permission, user: user.value })

    // 超级管理员拥有所有权限
    if (role === 'SuperAdmin') {
      console.log('SuperAdmin用户，拥有所有权限')
      return true
    }

    // 根据角色检查具体权限
    const rolePermissions: Record<string, string[]> = {
      'User': ['view_own_projects'], // 普通用户
      'ProjectManager': ['view_projects', 'create_project', 'edit_project', 'manage_team'], // 项目经理
      'Developer': ['view_projects', 'edit_code', 'run_tests'], // 开发人员
      'Tester': ['view_projects', 'create_tests', 'run_tests'], // 测试人员
      'ProductManager': ['view_projects', 'create_project', 'edit_requirements'], // 产品经理
      'Admin': ['admin', 'view_projects', 'create_project', 'edit_project', 'manage_team', 'manage_users'], // 管理员
      'SuperAdmin': ['admin', 'view_projects', 'create_project', 'edit_project', 'manage_team', 'manage_users', 'system_admin'] // 超级管理员（备用检查）
    }

    const userPermissions = rolePermissions[role] || []
    const hasPermissionResult = userPermissions.includes(permission)
    console.log('权限检查结果:', { userPermissions, hasPermissionResult, role, permission })

    return hasPermissionResult
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    loading,

    // 计算属性
    isAuthenticated,
    isAdmin,
    userRole,

    // 操作方法
    login,
    register,
    logout,
    logoutLocal,
    refreshTokenAction,
    getCurrentUser,
    updateProfile,
    changePassword,
    initializeAuth,
    hasPermission,

    // Token管理方法
    isTokenExpired,
    isTokenExpiringSoon,
    checkAndRefreshToken,
    startTokenCheck,
    stopTokenCheck
  }
})
