<template>
  <div ref="editorContainer" class="monaco-editor-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as monaco from 'monaco-editor'

// 配置 Monaco Editor Workers - 使用内联Worker以确保智能提示正常工作
self.MonacoEnvironment = {
  getWorker: function (moduleId, label) {
    // 为不同的语言服务创建适当的Worker
    if (label === 'json') {
      return new Worker(URL.createObjectURL(new Blob([`
        importScripts('https://unpkg.com/monaco-editor@latest/min/vs/language/json/json.worker.js');
      `], { type: 'application/javascript' })))
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new Worker(URL.createObjectURL(new Blob([`
        importScripts('https://unpkg.com/monaco-editor@latest/min/vs/language/css/css.worker.js');
      `], { type: 'application/javascript' })))
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new Worker(URL.createObjectURL(new Blob([`
        importScripts('https://unpkg.com/monaco-editor@latest/min/vs/language/html/html.worker.js');
      `], { type: 'application/javascript' })))
    }
    if (label === 'typescript' || label === 'javascript') {
      return new Worker(URL.createObjectURL(new Blob([`
        importScripts('https://unpkg.com/monaco-editor@latest/min/vs/language/typescript/ts.worker.js');
      `], { type: 'application/javascript' })))
    }
    // 默认编辑器Worker
    return new Worker(URL.createObjectURL(new Blob([`
      importScripts('https://unpkg.com/monaco-editor@latest/min/vs/editor/editor.worker.js');
    `], { type: 'application/javascript' })))
  }
}

// Props
interface Props {
  modelValue: string
  language?: string
  theme?: string
  options?: monaco.editor.IStandaloneEditorConstructionOptions
  height?: string | number
  width?: string | number
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  language: 'javascript',
  theme: 'vs-dark',
  height: '400px',
  width: '100%',
  readonly: false,
  options: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
  'focus': []
  'blur': []
}>()

// 响应式数据
const editorContainer = ref<HTMLElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 默认编辑器选项
const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
  automaticLayout: true,
  fontSize: 14,
  lineNumbers: 'on',
  roundedSelection: false,
  scrollBeyondLastLine: false,
  readOnly: props.readonly,
  theme: props.theme,
  language: props.language,
  minimap: {
    enabled: true
  },
  scrollbar: {
    vertical: 'visible',
    horizontal: 'visible',
    useShadows: false,
    verticalHasArrows: true,
    horizontalHasArrows: true
  },
  wordWrap: 'on',
  contextmenu: true,
  selectOnLineNumbers: true,
  glyphMargin: true,
  folding: true,
  foldingStrategy: 'indentation',
  showFoldingControls: 'always',
  unfoldOnClickAfterEndOfLine: false,
  bracketPairColorization: {
    enabled: true
  },
  guides: {
    bracketPairs: true,
    indentation: true
  },
  suggest: {
    showKeywords: true,
    showSnippets: true,
    showFunctions: true,
    showVariables: true,
    showClasses: true,
    showProperties: true,
    showMethods: true,
    showEvents: true,
    showOperators: true,
    showUnits: true,
    showValues: true,
    showConstants: true,
    showEnums: true,
    showEnumMembers: true,
    showModules: true,
    showStructs: true,
    showTypeParameters: true,
    showFolders: true,
    showFiles: true,
    showReferences: true,
    showWords: true,
    showColors: true,
    showFields: true,
    showConstructors: true,
    showUsers: true,
    showIssues: true,
    insertMode: 'replace',
    filterGraceful: true,
    snippetsPreventQuickSuggestions: false,
    localityBonus: true,
    shareSuggestSelections: true,
    showInlineDetails: true,
    // maxVisibleSuggestions: 12 // 此属性在新版本Monaco Editor中已移除
  },
  quickSuggestions: {
    other: true,
    comments: true,
    strings: true
  },
  quickSuggestionsDelay: 10,
  suggestOnTriggerCharacters: true,
  acceptSuggestionOnEnter: 'on' as const,
  acceptSuggestionOnCommitCharacter: true,
  wordBasedSuggestions: 'currentDocument' as const,
  tabCompletion: 'on' as const,
  suggestSelection: 'first' as const,
  parameterHints: {
    enabled: true,
    cycle: true
  },
  hover: {
    enabled: true,
    delay: 300,
    sticky: true
  }
}

// 标记是否已注册
let isAutomationAPIRegistered = false

// 注册自动化API的智能提示
const registerAutomationAPI = () => {
  if (isAutomationAPIRegistered) return

  // 为 JavaScript 添加自动化 API 提示
  monaco.languages.registerCompletionItemProvider('javascript', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position)
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn
      }

      const suggestions = [
        {
          label: 'click',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'click(\'${1:#selector}\', { timeout: ${2:5000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '基于选择器点击元素（CSS选择器、UIA控件等）',
          range: range
        },
        {
          label: 'clickImage',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'clickImage(\'${1:template_name}\', { confidence: ${2:0.8} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '基于图像识别点击目标（图片模板匹配）',
          range: range
        },
        {
          label: 'input',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'input(\'${1:#selector}\', \'${2:text}\', { clear: ${3:true} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '在输入框中输入文本',
          range: range
        },
        {
          label: 'type',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'type(\'${1:text}\', { delay: ${2:100} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '模拟键盘输入文本（逐字符输入）',
          range: range
        },
        {
          label: 'inputText',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'inputText(\'${1:#selector}\', \'${2:要输入的文字}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '在指定输入框中输入文字',
          range: range
        },
        {
          label: 'clearInput',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'clearInput(\'${1:#selector}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '清空输入框内容',
          range: range
        },
        {
          label: 'selectText',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'selectText(\'${1:#selector}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '选中输入框中的所有文本',
          range: range
        },
        {
          label: 'wait',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'wait(\'${1:#selector}\', { visible: ${2:true}, timeout: ${3:10000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '等待元素出现或条件满足',
          range: range
        },
        {
          label: 'waitTime',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'waitTime(${1:2000});',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '等待指定时间（毫秒）',
          range: range
        },
        {
          label: 'waitUntil',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'waitUntil(() => ${1:condition}, { timeout: ${2:10000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '等待直到条件为真',
          range: range
        },
        {
          label: 'screenshot',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'screenshot(\'${1:filename.png}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '截取屏幕截图',
          range: range
        },
        {
          label: 'screenshotElement',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'screenshotElement(\'${1:#selector}\', \'${2:filename.png}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '截取指定元素的截图',
          range: range
        },
        {
          label: 'screenshotRegion',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'screenshotRegion({ x: ${1:0}, y: ${2:0}, width: ${3:800}, height: ${4:600} }, \'${5:filename.png}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '截取指定区域的截图',
          range: range
        },
        {
          label: 'verify',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'verify(\'${1:#selector}\', { ${2:text: \'expected\'} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '验证元素状态',
          range: range
        },
        {
          label: 'exists',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'exists(\'${1:#selector}\')',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '检查元素是否存在',
          range: range
        },
        {
          label: 'keyPress',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'keyPress(\'${1:Enter}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '按下指定按键（Enter, Tab, Escape, Space等）',
          range: range
        },
        {
          label: 'keyCombo',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'keyCombo(\'${1:Ctrl+C}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '按下组合键（Ctrl+C, Alt+Tab, Ctrl+Shift+N等）',
          range: range
        },
        {
          label: 'keyDown',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'keyDown(\'${1:Shift}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '按下并保持按键',
          range: range
        },
        {
          label: 'keyUp',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'keyUp(\'${1:Shift}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '释放按键',
          range: range
        },
        {
          label: 'loop',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'loop(${1:5}, [\n  ${2:// 循环执行的步骤}\n]);',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '循环执行步骤',
          range: range
        },
        {
          label: 'imageExists',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'imageExists(\'${1:template_name}\', { confidence: ${2:0.8}, timeout: ${3:5000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '检查图片模板是否存在',
          range: range
        },
        {
          label: 'waitForImage',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'waitForImage(\'${1:template_name}\', { confidence: ${2:0.8}, timeout: ${3:10000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '等待图片出现',
          range: range
        },
        {
          label: 'ifImageExists',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (imageExists(\'${1:template_name}\')) {\n  ${2:// 图片存在时执行}\n  click(\'${3:target_template}\');\n} else {\n  ${4:// 图片不存在时执行}\n  console.log(\'图片未找到\');\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '条件判断：如果图片存在则执行',
          range: range
        },
        {
          label: 'drag',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'drag(\'${1:#source}\', \'${2:#target}\', { duration: ${3:1000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '从源元素拖拽到目标元素',
          range: range
        },
        {
          label: 'dragToPosition',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'dragToPosition(\'${1:#element}\', { x: ${2:100}, y: ${3:200} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '将元素拖拽到指定坐标',
          range: range
        },
        {
          label: 'dragImage',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'dragImage(\'${1:source_template}\', \'${2:target_template}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '通过图像识别进行拖拽操作',
          range: range
        },
        {
          label: 'scroll',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'scroll(\'${1:#element}\', { direction: \'${2:down}\', distance: ${3:300} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '滚动元素（up, down, left, right）',
          range: range
        },
        {
          label: 'mouseMove',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'mouseMove({ x: ${1:100}, y: ${2:200} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '移动鼠标到指定坐标',
          range: range
        },
        {
          label: 'rightClick',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'rightClick(\'${1:#selector}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '右键点击元素',
          range: range
        },
        {
          label: 'doubleClick',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'doubleClick(\'${1:#selector}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '双击元素',
          range: range
        },
        {
          label: 'imageCondition',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '// 图像条件判断模板\nconst result = imageExists(\'${1:condition_template}\');\nif (${2:result}) {\n  // 条件满足时执行\n  click(\'${3:target_template}\');\n  console.log(\'条件满足，执行点击操作\');\n} else {\n  // 条件不满足时执行\n  console.log(\'条件不满足，跳过操作\');\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '完整的图像条件判断模板',
          range: range
        },
        {
          label: 'pasteImages',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'pasteImages(\'${1:referenceImages}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '将参考图片粘贴到文本输入框中',
          range: range
        },
        {
          label: 'confirmDialog',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'confirmDialog(\'${1:确认要继续执行吗？}\', \'${2:确认操作}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '显示确认对话框，询问用户是否继续',
          range: range
        },
        {
          label: 'userConfirm',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'if (userConfirm(\'${1:是否继续执行下一步？}\')) {\n  ${2:// 用户确认后执行的操作}\n  console.log(\'用户确认继续\');\n} else {\n  ${3:// 用户取消时执行的操作}\n  console.log(\'用户取消操作\');\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '用户确认条件判断，返回true/false',
          range: range
        }
      ]

      return { suggestions }
    }
  })

  // 为 TypeScript 添加相同的自动化 API 提示
  monaco.languages.registerCompletionItemProvider('typescript', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position)
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn
      }

      const suggestions = [
        {
          label: 'click',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'click(\'${1:#selector}\', { timeout: ${2:5000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '基于选择器点击元素（CSS选择器、UIA控件等）',
          range: range
        },
        {
          label: 'clickImage',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'clickImage(\'${1:template_name}\', { confidence: ${2:0.8} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '基于图像识别点击目标（图片模板匹配）',
          range: range
        },
        {
          label: 'imageExists',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'imageExists(\'${1:template_name}\', { confidence: ${2:0.8}, timeout: ${3:5000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '检查图片模板是否存在',
          range: range
        },
        {
          label: 'input',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'input(\'${1:#selector}\', \'${2:text}\', { clear: ${3:true} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '在输入框中输入文本',
          range: range
        },
        {
          label: 'wait',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'wait(\'${1:#selector}\', { visible: ${2:true}, timeout: ${3:10000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '等待元素出现或条件满足',
          range: range
        },
        {
          label: 'screenshot',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'screenshot(\'${1:filename.png}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '截取屏幕截图',
          range: range
        },
        {
          label: 'keyPress',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'keyPress(\'${1:Enter}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '按下指定按键（Enter, Tab, Escape, Space等）',
          range: range
        },
        {
          label: 'drag',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'drag(\'${1:#source}\', \'${2:#target}\', { duration: ${3:1000} });',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '从源元素拖拽到目标元素',
          range: range
        },
        {
          label: 'pasteImages',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'pasteImages(\'${1:referenceImages}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '将参考图片粘贴到文本输入框中',
          range: range
        },
        {
          label: 'confirmDialog',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'confirmDialog(\'${1:确认要继续执行吗？}\', \'${2:确认操作}\');',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '显示确认对话框，询问用户是否继续',
          range: range
        },
        {
          label: 'userConfirm',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'if (userConfirm(\'${1:是否继续执行下一步？}\')) {\n  ${2:// 用户确认后执行的操作}\n  console.log(\'用户确认继续\');\n} else {\n  ${3:// 用户取消时执行的操作}\n  console.log(\'用户取消操作\');\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '用户确认条件判断，返回true/false',
          range: range
        }
      ]

      return { suggestions }
    }
  })

  isAutomationAPIRegistered = true
}

// 初始化编辑器
const initEditor = () => {
  if (!editorContainer.value) return

  // 注册自动化 API
  registerAutomationAPI()

  // 合并选项，确保智能提示功能完全启用
  const editorOptions = {
    ...defaultOptions,
    ...props.options,
    value: props.modelValue,
    language: props.language,
    theme: props.theme,
    // 强制启用所有智能提示功能
    quickSuggestions: {
      other: true,
      comments: true,
      strings: true
    },
    quickSuggestionsDelay: 10,
    suggestOnTriggerCharacters: true,
    acceptSuggestionOnEnter: 'on' as const,
    acceptSuggestionOnCommitCharacter: true,
    tabCompletion: 'on' as const,
    wordBasedSuggestions: 'currentDocument' as const,
    suggestSelection: 'first' as const,
    // 确保参数提示启用
    parameterHints: {
      enabled: true,
      cycle: true
    },
    // 确保悬停提示启用
    hover: {
      enabled: true,
      delay: 300,
      sticky: true
    }
  }

  // 创建编辑器
  editor = monaco.editor.create(editorContainer.value, editorOptions)

  // 确保主题正确应用
  monaco.editor.setTheme(props.theme)

  // 监听内容变化
  editor.onDidChangeModelContent(() => {
    const value = editor?.getValue() || ''
    emit('update:modelValue', value)
    emit('change', value)
  })

  // 监听焦点事件
  editor.onDidFocusEditorText(() => {
    emit('focus')
  })

  editor.onDidBlurEditorText(() => {
    emit('blur')
  })

  // 添加多个快捷键来触发智能提示
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Space, () => {
    editor?.trigger('keyboard', 'editor.action.triggerSuggest', {})
  })

  // 添加 Ctrl+I 触发参数提示
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI, () => {
    editor?.trigger('keyboard', 'editor.action.triggerParameterHints', {})
  })

  // 添加 Alt+Space 也可以触发智能提示（备用快捷键）
  editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.Space, () => {
    editor?.trigger('keyboard', 'editor.action.triggerSuggest', {})
  })

  // 设置容器大小
  updateSize()

  // 确保智能提示在编辑器准备好后立即可用
  nextTick(() => {
    if (editor) {
      // 强制刷新语言服务
      const model = editor.getModel()
      if (model) {
        monaco.editor.setModelLanguage(model, props.language)

        // 强制触发一次智能提示以确保服务正常工作
        setTimeout(() => {
          if (editor && !editor.hasTextFocus()) {
            editor.focus()
          }
          // 延迟触发智能提示，确保语言服务已完全加载
          setTimeout(() => {
            editor?.trigger('api', 'editor.action.triggerSuggest', {})
          }, 100)
        }, 200)
      }
    }
  })
}

// 更新编辑器大小
const updateSize = () => {
  if (!editor || !editorContainer.value) return

  const container = editorContainer.value
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width

  container.style.height = height
  container.style.width = width

  // 触发编辑器重新布局
  nextTick(() => {
    editor?.layout()
  })
}

// 设置编辑器值
const setValue = (value: string) => {
  if (editor && editor.getValue() !== value) {
    editor.setValue(value)
  }
}

// 获取编辑器值
const getValue = (): string => {
  return editor?.getValue() || ''
}

// 设置语言
const setLanguage = (language: string) => {
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, language)
    }
  }
}

// 设置主题
const setTheme = (theme: string) => {
  if (editor) {
    monaco.editor.setTheme(theme)
  }
}

// 格式化代码
const formatCode = () => {
  if (editor) {
    editor.getAction('editor.action.formatDocument')?.run()
  }
}

// 插入文本
const insertText = (text: string) => {
  if (editor) {
    const selection = editor.getSelection()
    const range = new monaco.Range(
      selection?.startLineNumber || 1,
      selection?.startColumn || 1,
      selection?.endLineNumber || 1,
      selection?.endColumn || 1
    )
    editor.executeEdits('insert-text', [
      {
        range,
        text,
        forceMoveMarkers: true
      }
    ])
  }
}

// 获取选中文本
const getSelectedText = (): string => {
  if (editor) {
    const selection = editor.getSelection()
    if (selection) {
      return editor.getModel()?.getValueInRange(selection) || ''
    }
  }
  return ''
}

// 手动触发智能提示
const triggerSuggest = () => {
  if (editor) {
    editor.trigger('api', 'editor.action.triggerSuggest', {})
  }
}

// 手动触发参数提示
const triggerParameterHints = () => {
  if (editor) {
    editor.trigger('api', 'editor.action.triggerParameterHints', {})
  }
}

// 重新注册智能提示（用于调试）
const reregisterSuggestions = () => {
  registerAutomationAPI()
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, props.language)
    }
  }
}

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    setValue(newValue)
  }
})

watch(() => props.language, (newLanguage) => {
  setLanguage(newLanguage)
})

watch(() => props.theme, (newTheme) => {
  setTheme(newTheme)
})

watch([() => props.height, () => props.width], () => {
  updateSize()
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 暴露方法给父组件
defineExpose({
  setValue,
  getValue,
  setLanguage,
  setTheme,
  formatCode,
  insertText,
  getSelectedText,
  triggerSuggest,
  triggerParameterHints,
  reregisterSuggestions,
  getEditor: () => editor
})
</script>

<style scoped>
.monaco-editor-container {
  width: 100%;
  height: 400px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

/* 深色主题适配 */
:deep(.monaco-editor) {
  --vscode-editor-background: var(--el-bg-color);
  --vscode-editor-foreground: var(--el-text-color-primary);
}

/* 确保编辑器填满容器 */
:deep(.monaco-editor .overflow-guard) {
  border-radius: 4px;
}
</style>
