using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 编码任务控制器
/// 功能: 管理编码任务的CRUD操作、任务分配、进度跟踪等
/// 支持: 任务创建、更新、删除、查询、统计、步骤关联
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CodingTaskController : ControllerBase
{
    private readonly ICodingTaskRepository _codingTaskRepository;
    private readonly ILogger<CodingTaskController> _logger;

    public CodingTaskController(
        ICodingTaskRepository codingTaskRepository,
        ILogger<CodingTaskController> logger)
    {
        _codingTaskRepository = codingTaskRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }

    /// <summary>
    /// 获取项目的编码任务列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <param name="assignedTo">分配人筛选</param>
    /// <param name="searchKeyword">搜索关键词</param>
    /// <returns>编码任务列表</returns>
    [HttpGet("project/{projectId}")]
    public async Task<ActionResult<object>> GetProjectTasks(
        int projectId,
        [FromQuery] int pageIndex = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] int? assignedTo = null,
        [FromQuery] string? searchKeyword = null)
    {
        try
        {
            _logger.LogInformation("获取项目 {ProjectId} 的编码任务列表", projectId);

            var (items, totalCount) = await _codingTaskRepository.GetByProjectIdAsync(
                projectId, pageIndex, pageSize, status, priority, assignedTo, searchKeyword);

            var result = new
            {
                success = true,
                data = new
                {
                    items,
                    totalCount,
                    pageIndex,
                    pageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                }
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目编码任务列表失败，项目ID: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "获取编码任务列表失败" });
        }
    }

    /// <summary>
    /// 获取编码任务详情
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>编码任务详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<object>> GetTask(int id)
    {
        try
        {
            var task = await _codingTaskRepository.GetByIdAsync(id);
            if (task == null || task.IsDeleted)
            {
                return NotFound(new { success = false, message = "编码任务不存在" });
            }

            // 获取关联的开发步骤
            var steps = await _codingTaskRepository.GetTaskStepsAsync(id);
            task.StepCount = steps.Count;

            return Ok(new { success = true, data = task });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编码任务详情失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "获取编码任务详情失败" });
        }
    }

    /// <summary>
    /// 创建编码任务
    /// </summary>
    /// <param name="task">编码任务信息</param>
    /// <returns>创建的编码任务</returns>
    [HttpPost]
    public async Task<ActionResult<object>> CreateTask([FromBody] CodingTask task)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 创建编码任务: {TaskName}", userId, task.TaskName);

            // 设置创建信息
            task.CreatedTime = DateTime.Now;
            task.CreatedBy = userId;
            task.UpdatedTime = DateTime.Now;
            task.UpdatedBy = userId;

            var createdTask = await _codingTaskRepository.AddAsync(task);

            return Ok(new { success = true, data = createdTask, message = "编码任务创建成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建编码任务失败");
            return StatusCode(500, new { success = false, message = "创建编码任务失败" });
        }
    }

    /// <summary>
    /// 更新编码任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="task">更新的任务信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<object>> UpdateTask(int id, [FromBody] CodingTask task)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 更新编码任务 {TaskId}", userId, id);

            var existingTask = await _codingTaskRepository.GetByIdAsync(id);
            if (existingTask == null || existingTask.IsDeleted)
            {
                return NotFound(new { success = false, message = "编码任务不存在" });
            }

            // 更新任务信息
            task.Id = id;
            task.UpdatedTime = DateTime.Now;
            task.UpdatedBy = userId;
            task.CreatedTime = existingTask.CreatedTime;
            task.CreatedBy = existingTask.CreatedBy;

            var updatedTask = await _codingTaskRepository.UpdateAsync(task);

            return Ok(new { success = true, data = updatedTask, message = "编码任务更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新编码任务失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "更新编码任务失败" });
        }
    }

    /// <summary>
    /// 删除编码任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<object>> DeleteTask(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 删除编码任务 {TaskId}", userId, id);

            var task = await _codingTaskRepository.GetByIdAsync(id);
            if (task == null || task.IsDeleted)
            {
                return NotFound(new { success = false, message = "编码任务不存在" });
            }

            // 软删除
            task.IsDeleted = true;
            task.UpdatedTime = DateTime.Now;
            task.UpdatedBy = userId;

            await _codingTaskRepository.UpdateAsync(task);

            return Ok(new { success = true, message = "编码任务删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除编码任务失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "删除编码任务失败" });
        }
    }

    /// <summary>
    /// 获取项目编码任务统计信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    [HttpGet("project/{projectId}/statistics")]
    public async Task<ActionResult<object>> GetProjectStatistics(int projectId)
    {
        try
        {
            _logger.LogInformation("获取项目 {ProjectId} 编码任务统计信息", projectId);

            var statistics = await _codingTaskRepository.GetStatisticsAsync(projectId);

            return Ok(new { success = true, data = statistics });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目编码任务统计信息失败，项目ID: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "获取统计信息失败" });
        }
    }

    /// <summary>
    /// 获取用户分配的编码任务
    /// </summary>
    /// <param name="userId">用户ID，不传则获取当前用户的任务</param>
    /// <param name="status">状态筛选</param>
    /// <returns>用户的编码任务列表</returns>
    [HttpGet("user/{userId?}")]
    public async Task<ActionResult<object>> GetUserTasks(int? userId = null, [FromQuery] string? status = null)
    {
        try
        {
            var targetUserId = userId ?? GetCurrentUserId();
            _logger.LogInformation("获取用户 {UserId} 的编码任务", targetUserId);

            var tasks = await _codingTaskRepository.GetByAssignedUserAsync(targetUserId, status);

            return Ok(new { success = true, data = tasks });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户编码任务失败，用户ID: {UserId}", userId);
            return StatusCode(500, new { success = false, message = "获取用户编码任务失败" });
        }
    }

    /// <summary>
    /// 获取即将到期的编码任务
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="days">天数，默认7天</param>
    /// <returns>即将到期的任务列表</returns>
    [HttpGet("project/{projectId}/upcoming-due")]
    public async Task<ActionResult<object>> GetUpcomingDueTasks(int projectId, [FromQuery] int days = 7)
    {
        try
        {
            _logger.LogInformation("获取项目 {ProjectId} 即将到期的编码任务", projectId);

            var tasks = await _codingTaskRepository.GetUpcomingDueTasksAsync(projectId, days);

            return Ok(new { success = true, data = tasks });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取即将到期任务失败，项目ID: {ProjectId}", projectId);
            return StatusCode(500, new { success = false, message = "获取即将到期任务失败" });
        }
    }

    /// <summary>
    /// 批量更新任务状态
    /// </summary>
    /// <param name="request">批量更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("batch-status")]
    public async Task<ActionResult<object>> BatchUpdateStatus([FromBody] CodingTaskBatchUpdateStatusRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 批量更新任务状态", userId);

            var updatedCount = await _codingTaskRepository.BatchUpdateStatusAsync(
                request.TaskIds, request.Status, userId);

            return Ok(new {
                success = true,
                data = new { updatedCount },
                message = $"成功更新 {updatedCount} 个任务状态"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新任务状态失败");
            return StatusCode(500, new { success = false, message = "批量更新任务状态失败" });
        }
    }

    /// <summary>
    /// 获取任务关联的开发步骤
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>关联的开发步骤列表</returns>
    [HttpGet("{id}/steps")]
    public async Task<ActionResult<object>> GetTaskSteps(int id)
    {
        try
        {
            _logger.LogInformation("获取任务 {TaskId} 关联的开发步骤", id);

            var steps = await _codingTaskRepository.GetTaskStepsAsync(id);

            return Ok(new { success = true, data = steps });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务关联步骤失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "获取任务关联步骤失败" });
        }
    }

    /// <summary>
    /// 添加开发步骤到编码任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="request">添加步骤请求</param>
    /// <returns>添加结果</returns>
    [HttpPost("{id}/steps")]
    public async Task<ActionResult<object>> AddStepsToTask(int id, [FromBody] CodingTaskAddStepsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 为任务 {TaskId} 添加开发步骤", userId, id);

            var addedCount = await _codingTaskRepository.AddStepsToTaskAsync(id, request.StepIds);

            return Ok(new {
                success = true,
                data = new { addedCount },
                message = $"成功添加 {addedCount} 个开发步骤"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加步骤到任务失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "添加步骤到任务失败" });
        }
    }

    /// <summary>
    /// 从编码任务中移除开发步骤
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="request">移除步骤请求</param>
    /// <returns>移除结果</returns>
    [HttpDelete("{id}/steps")]
    public async Task<ActionResult<object>> RemoveStepsFromTask(int id, [FromBody] CodingTaskRemoveStepsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 从任务 {TaskId} 移除开发步骤", userId, id);

            var removedCount = await _codingTaskRepository.RemoveStepsFromTaskAsync(id, request.StepIds);

            return Ok(new {
                success = true,
                data = new { removedCount },
                message = $"成功移除 {removedCount} 个开发步骤"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从任务移除步骤失败，任务ID: {TaskId}", id);
            return StatusCode(500, new { success = false, message = "从任务移除步骤失败" });
        }
    }

    /// <summary>
    /// 更新编码任务步骤状态标志
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="stepId">步骤ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPatch("{taskId}/steps/{stepId}/flags")]
    public async Task<ActionResult<object>> UpdateTaskStepFlags(int taskId, int stepId, [FromBody] UpdateCodingTaskStepFlagsRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 更新编码任务 {TaskId} 步骤 {StepId} 的状态标志", userId, taskId, stepId);

            // 查找CodingTaskStep记录
            var taskStep = await _codingTaskRepository.GetTaskStepAsync(taskId, stepId);
            var task= await _codingTaskRepository.GetByIdAsync(taskId);
            if (taskStep == null|| task==null)
            {
                return NotFound(new { success = false, message = "编码任务步骤不存在" });
            }

            // 更新标志字段
            if (request.IsFinishCoding.HasValue)
            {
                taskStep.IsFinishCoding = request.IsFinishCoding.Value;
            }

            if (request.IsFixError.HasValue)
            {
                taskStep.IsFixError = request.IsFixError.Value;
            }

            if (!string.IsNullOrEmpty(request.Status))
            {
                taskStep.Status = request.Status;
                task.Status = request.Status;

            }

            // 设置更新信息
            taskStep.UpdatedTime = DateTime.Now;
            taskStep.UpdatedBy = userId;
            task.UpdatedTime = DateTime.Now;
            task.UpdatedBy = userId;
            await _codingTaskRepository.UpdateTaskStepAsync(taskStep);
            await _codingTaskRepository.UpdateAsync(task);
            return Ok(new {
                success = true,
                data = new {
                    taskStepId = taskStep.Id,
                    taskId = taskStep.CodingTaskId,
                    stepId = taskStep.DevelopmentStepId,
                    status = taskStep.Status,
                    isFinishCoding = taskStep.IsFinishCoding,
                    isFixError = taskStep.IsFixError
                },
                message = "编码任务步骤状态标志更新成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新编码任务步骤状态标志失败");
            return StatusCode(500, new { success = false, message = "更新编码任务步骤状态标志失败" });
        }
    }

    /// <summary>
    /// 开始自动化操作 - 更新项目中所有任务状态
    /// </summary>
    /// <param name="request">开始自动化操作请求</param>
    /// <returns>更新结果</returns>
    [HttpPost("start-automation")]
    public async Task<ActionResult<object>> StartAutomation([FromBody] StartAutomationRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 开始自动化操作，项目ID: {ProjectId}, 当前任务ID: {CurrentTaskId}",
                userId, request.ProjectId, request.CurrentTaskId);

            // 验证当前任务是否存在且属于指定项目
            var currentTask = await _codingTaskRepository.GetByIdAsync(request.CurrentTaskId);
            if (currentTask == null)
            {
                return NotFound(new { success = false, message = "当前任务不存在" });
            }

            if (currentTask.ProjectId != request.ProjectId)
            {
                return BadRequest(new { success = false, message = "任务不属于指定项目" });
            }

            // 获取项目中的所有任务
            var allTasks = await _codingTaskRepository.GetAllByProjectIdAsync(request.ProjectId);

            var updateResults = new List<object>();
            var updatedCount = 0;

            foreach (var task in allTasks)
            {
                var oldStatus = task.Status;
                var newStatus = task.Id == request.CurrentTaskId ? "InProgress" : "Blocked";

                // 只更新状态发生变化的任务
                if (oldStatus != newStatus)
                {
                    task.Status = newStatus;
                    task.UpdatedTime = DateTime.Now;
                    task.UpdatedBy = userId;

                    // 如果是当前任务且还没有开始时间，设置开始时间
                    if (task.Id == request.CurrentTaskId && !task.ActualStartTime.HasValue)
                    {
                        task.ActualStartTime = DateTime.Now;
                    }

                    await _codingTaskRepository.UpdateAsync(task);
                    updatedCount++;

                    // 如果是当前任务，同时开始第一个步骤
                    if (task.Id == request.CurrentTaskId)
                    {
                        try
                        {
                            var stepStarted = await _codingTaskRepository.StartFirstTaskStepAsync(task.Id, userId);
                            _logger.LogInformation("任务 {TaskId} 第一个步骤开始状态: {Started}", task.Id, stepStarted);
                        }
                        catch (Exception stepEx)
                        {
                            _logger.LogError(stepEx, "开始任务 {TaskId} 第一个步骤失败", task.Id);
                            // 不影响主流程，继续执行
                        }
                    }

                    updateResults.Add(new
                    {
                        taskId = task.Id,
                        taskName = task.TaskName,
                        oldStatus = oldStatus,
                        newStatus = newStatus,
                        isCurrent = task.Id == request.CurrentTaskId
                    });
                }
            }

            return Ok(new
            {
                success = true,
                data = new
                {
                    projectId = request.ProjectId,
                    currentTaskId = request.CurrentTaskId,
                    updatedCount = updatedCount,
                    totalTasks = allTasks.Count,
                    updateResults = updateResults
                },
                message = $"自动化操作已开始，当前任务设为进行中，其他 {updatedCount - 1} 个任务设为暂停状态"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始自动化操作失败");
            return StatusCode(500, new { success = false, message = "开始自动化操作失败" });
        }
    }
}

/// <summary>
/// 编码任务批量更新状态请求
/// </summary>
public class CodingTaskBatchUpdateStatusRequest
{
    /// <summary>
    /// 任务ID列表
    /// </summary>
    public List<int> TaskIds { get; set; } = new();

    /// <summary>
    /// 新状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 编码任务添加步骤请求
/// </summary>
public class CodingTaskAddStepsRequest
{
    /// <summary>
    /// 步骤ID列表
    /// </summary>
    public List<int> StepIds { get; set; } = new();
}

/// <summary>
/// 编码任务移除步骤请求
/// </summary>
public class CodingTaskRemoveStepsRequest
{
    /// <summary>
    /// 步骤ID列表
    /// </summary>
    public List<int> StepIds { get; set; } = new();
}

/// <summary>
/// 更新编码任务状态标志请求
/// </summary>
public class UpdateCodingTaskFlagsRequest
{
    /// <summary>
    /// 是否完成编码
    /// </summary>
    public bool? IsFinishCoding { get; set; }

    /// <summary>
    /// 是否修复错误
    /// </summary>
    public bool? IsFixError { get; set; }
}

/// <summary>
/// 开始自动化操作请求
/// </summary>
public class StartAutomationRequest
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 当前执行的任务ID
    /// </summary>
    public int CurrentTaskId { get; set; }
}

/// <summary>
/// 更新编码任务步骤状态标志请求
/// </summary>
public class UpdateCodingTaskStepFlagsRequest
{
    /// <summary>
    /// 是否完成编码
    /// </summary>
    public bool? IsFinishCoding { get; set; }

    /// <summary>
    /// 是否修复错误
    /// </summary>
    public bool? IsFixError { get; set; }

    /// <summary>
    /// 步骤状态
    /// </summary>
    public string? Status { get; set; }
}
