#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOM自动化配置
"""

import json
import os
from pathlib import Path


class DOMAutomationConfig:
    """DOM自动化配置管理器"""
    
    def __init__(self, config_file: str = "dom_automation_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_default_config()
        self._load_config()
    
    def _load_default_config(self) -> dict:
        """加载默认配置"""
        return {
            "debug_port": 9222,
            "connection_timeout": 30,
            "response_timeout": 30,
            "check_interval": 1.0,
            "max_retries": 3,
            "chrome_options": [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu",
                "--disable-extensions"
            ],
            "vscode": {
                "startup_command": "code --remote-debugging-port=9222 --disable-web-security --remote-allow-origins=*",
                "wait_time": 5
            },
            "selectors": {
                "copilot_input": [
                    'textarea.inputarea.monaco-mouse-cursor-text',
                    'textarea[data-mprt="7"]',
                    'textarea[role="textbox"][aria-roledescription="编辑器"]',
                    '.chat-input-container textarea',
                    '.interactive-input-editor textarea',
                    '[data-testid="chat-input"]',
                    '.copilot-chat-input',
                    'textarea[placeholder*="Ask Copilot"]',
                    'textarea[placeholder*="Type a message"]',
                    '.monaco-inputbox textarea'
                ],
                "send_button": [
                    'a.action-label.codicon.codicon-send',
                    'a[aria-label*="发送"]',
                    'a[aria-label*="Send"]',
                    '.chat-execute-toolbar a.codicon-send',
                    '[data-testid="send-button"]',
                    '.copilot-send-button',
                    'button[title*="Send"]',
                    'button[aria-label*="Send"]'
                ],
                "message_container": [
                    '.monaco-scrollable-element',
                    '.chat-list-container',
                    '.interactive-list',
                    '.chat-tree-container'
                ],
                "message_row": [
                    '.monaco-list-row',
                    '.monaco-tl-row',
                    '.interactive-item-container',
                    '.chat-request-wrapper',
                    '.chat-response-wrapper'
                ]
            },
            "message_detection": {
                "user_indicators": [
                    "interactive-request",
                    "chat-request-wrapper",
                    "你:",
                    "User:"
                ],
                "assistant_indicators": [
                    "interactive-response",
                    "chat-response-wrapper",
                    "GitHub Copilot"
                ],
                "error_keywords": [
                    "错误", "Error", "服务繁忙", "Service busy",
                    "服务器繁忙", "Server busy", "超时", "Timeout",
                    "网络错误", "Network error", "请稍后再试", "Please try again"
                ],
                "thinking_keywords": [
                    "正在思考", "Thinking", "正在生成", "Generating",
                    "正在处理", "Processing"
                ]
            },
            "logging": {
                "enabled": True,
                "level": "INFO",
                "file": "dom_automation.log",
                "max_size": "10MB",
                "backup_count": 5
            }
        }
    
    def _load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(self.config, file_config)
                print(f"✅ 配置已从文件加载: {self.config_file}")
            else:
                self._save_config()
                print(f"✅ 已创建默认配置文件: {self.config_file}")
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")
            print("使用默认配置")
    
    def _merge_config(self, base_config: dict, new_config: dict):
        """合并配置"""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ 保存配置文件失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self._save_config()
    
    def get_debug_port(self) -> int:
        """获取调试端口"""
        return self.get("debug_port", 9222)
    
    def get_connection_timeout(self) -> int:
        """获取连接超时时间"""
        return self.get("connection_timeout", 30)
    
    def get_response_timeout(self) -> int:
        """获取响应超时时间"""
        return self.get("response_timeout", 30)
    
    def get_check_interval(self) -> float:
        """获取检查间隔"""
        return self.get("check_interval", 1.0)
    
    def get_chrome_options(self) -> list:
        """获取Chrome选项"""
        return self.get("chrome_options", [])
    
    def get_copilot_input_selectors(self) -> list:
        """获取Copilot输入框选择器"""
        return self.get("selectors.copilot_input", [])
    
    def get_send_button_selectors(self) -> list:
        """获取发送按钮选择器"""
        return self.get("selectors.send_button", [])
    
    def get_message_container_selectors(self) -> list:
        """获取消息容器选择器"""
        return self.get("selectors.message_container", [])
    
    def get_message_row_selectors(self) -> list:
        """获取消息行选择器"""
        return self.get("selectors.message_row", [])
    
    def get_user_indicators(self) -> list:
        """获取用户消息指示器"""
        return self.get("message_detection.user_indicators", [])
    
    def get_assistant_indicators(self) -> list:
        """获取助手消息指示器"""
        return self.get("message_detection.assistant_indicators", [])
    
    def get_error_keywords(self) -> list:
        """获取错误关键词"""
        return self.get("message_detection.error_keywords", [])
    
    def get_thinking_keywords(self) -> list:
        """获取思考关键词"""
        return self.get("message_detection.thinking_keywords", [])
    
    def is_logging_enabled(self) -> bool:
        """是否启用日志"""
        return self.get("logging.enabled", True)
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get("logging.level", "INFO")
    
    def get_log_file(self) -> str:
        """获取日志文件"""
        return self.get("logging.file", "dom_automation.log")


# 全局配置实例
dom_config = DOMAutomationConfig()
