-- ================================================
-- 迁移脚本: 018_AddLogicTypeAndSequenceJsonFields
-- 描述: 为UIAutoMationTemplateSteps表添加LogicType字段，为UIAutoMationTemplateSequences表添加SequenceJson字段
-- 创建时间: 2025-06-27
-- ================================================

USE [ProjectManagementAI]
GO

-- 检查是否已经执行过此迁移
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'UIAutoMationTemplateSteps' 
               AND COLUMN_NAME = 'LogicType')
BEGIN
    PRINT '开始执行迁移: 添加LogicType和SequenceJson字段...'
    
    -- 1. 为 UIAutoMationTemplateSteps 表添加 LogicType 字段
    PRINT '添加 UIAutoMationTemplateSteps.LogicType 字段...'
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [LogicType] NVARCHAR(50) NULL;
    
    -- 添加字段注释
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'逻辑语句类型（可选，用于条件判断、循环等逻辑控制）', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps', 
        @level2type = N'COLUMN', @level2name = N'LogicType';
    
    PRINT 'UIAutoMationTemplateSteps.LogicType 字段添加完成'
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSteps.LogicType 字段已存在，跳过添加'
END

-- 2. 为 UIAutoMationTemplateSequences 表添加 SequenceJson 字段
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
               AND COLUMN_NAME = 'SequenceJson')
BEGIN
    PRINT '添加 UIAutoMationTemplateSequences.SequenceJson 字段...'
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    ADD [SequenceJson] NVARCHAR(MAX) NULL;
    
    -- 添加字段注释
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'序列步骤JSON（完整的序列配置，包含所有步骤信息）', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'SequenceJson';
    
    PRINT 'UIAutoMationTemplateSequences.SequenceJson 字段添加完成'
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSequences.SequenceJson 字段已存在，跳过添加'
END

-- 3. 验证字段添加成功
PRINT '验证字段添加结果...'
SELECT 
    'UIAutoMationTemplateSteps' AS TableName, 
    'LogicType' AS ColumnName,
    CASE WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_NAME = 'UIAutoMationTemplateSteps' 
                     AND COLUMN_NAME = 'LogicType') 
         THEN '已添加' ELSE '未添加' END AS Status
UNION ALL
SELECT 
    'UIAutoMationTemplateSequences' AS TableName, 
    'SequenceJson' AS ColumnName,
    CASE WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
                     AND COLUMN_NAME = 'SequenceJson') 
         THEN '已添加' ELSE '未添加' END AS Status;

-- 4. 为现有数据生成初始JSON（可选）
PRINT '开始为现有序列生成初始JSON数据...'

-- 更新现有序列的SequenceJson字段
UPDATE s
SET SequenceJson = (
    SELECT 
        s.Name,
        s.Description,
        s.Category,
        CASE 
            WHEN s.Tags IS NULL OR s.Tags = '' THEN JSON_QUERY('[]')
            ELSE JSON_QUERY('["' + REPLACE(s.Tags, ',', '","') + '"]')
        END AS Tags,
        s.Notes,
        s.IsActive,
        (
            SELECT 
                step.StepOrder,
                step.ActionType,
                step.LogicType,
                step.Description,
                JSON_QUERY(CASE 
                    WHEN step.Parameters IS NULL OR step.Parameters = '' 
                    THEN '{}' 
                    ELSE step.Parameters 
                END) AS Parameters,
                step.TimeoutSeconds,
                step.MaxRetries,
                step.IsActive,
                step.ConditionExpression,
                step.JumpToStepId,
                step.LoopCount,
                step.LoopVariable,
                step.GroupId
            FROM UIAutoMationTemplateSteps step
            WHERE step.SequenceId = s.Id 
                AND step.IsDeleted = 0
            ORDER BY step.StepOrder
            FOR JSON PATH
        ) AS Steps
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
)
FROM UIAutoMationTemplateSequences s
WHERE s.IsDeleted = 0 
    AND s.SequenceJson IS NULL;

PRINT '现有序列JSON数据生成完成'

-- 5. 显示迁移完成信息
PRINT '========================================='
PRINT '迁移 018_AddLogicTypeAndSequenceJsonFields 执行完成!'
PRINT '已添加字段:'
PRINT '  - UIAutoMationTemplateSteps.LogicType (NVARCHAR(50))'
PRINT '  - UIAutoMationTemplateSequences.SequenceJson (NVARCHAR(MAX))'
PRINT '已为现有序列生成初始JSON数据'
PRINT '========================================='

GO
