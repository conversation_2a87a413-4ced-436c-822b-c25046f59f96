-- =============================================
-- 添加SQL扩展属性注释生成模板
-- 创建时间: 2024-12-19
-- 描述: 生成SQL Server扩展属性注释而不是普通注释
-- =============================================

USE [ProjectManagementAI]
GO
-- =============================================
-- 更新TaskType约束，添加SQLCommentGeneration类型
-- =============================================

USE [ProjectManagementAI]
GO

-- 首先检查约束是否存在
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    -- 删除现有约束
    ALTER TABLE PromptTemplates DROP CONSTRAINT CK_PromptTemplates_TaskType;
    PRINT '已删除旧的TaskType约束';
    
    -- 创建新的约束，包含SQLCommentGeneration类型
    ALTER TABLE PromptTemplates
    ADD CONSTRAINT CK_PromptTemplates_TaskType
    CHECK (TaskType IN (
        'RequirementAnalysis',      -- 需求分析
        'CodeGeneration',          -- 代码生成
        'Testing',                 -- 测试生成
        'Debugging',               -- 调试辅助
        'Documentation',           -- 文档生成
        'Review',                  -- 代码审查
        'ERDiagramGeneration',     -- ER图生成
        'ContextDiagramGeneration', -- 上下文图生成
        'DesignGeneration',        -- 设计生成
        'ArchitectureAnalysis',    -- 架构分析
        'RequirementDecomposition', -- 需求分解
        'DependencyAnalysis',      -- 依赖分析
        'ComplexityAnalysis',      -- 复杂度分析
        'SQLCommentGeneration'     -- SQL注释生成
    ));
    
    PRINT '已更新TaskType约束，添加SQLCommentGeneration类型';
END
ELSE
BEGIN
    PRINT 'TaskType约束不存在，无需更新';
END

-- 验证约束是否更新成功
SELECT name, definition 
FROM sys.check_constraints 
WHERE name = 'CK_PromptTemplates_TaskType';

PRINT '约束更新完成，现在可以执行添加SQL扩展属性注释生成模板的脚本';
GO
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'SQL扩展属性注释生成模板')
BEGIN
    -- 检查并添加数据库设计分类
    DECLARE @DatabaseCategoryId int = NULL;
    
    -- 尝试获取数据库设计分类ID
    SELECT @DatabaseCategoryId = Id FROM PromptCategories WHERE Name = '数据库设计';
    
    -- 如果分类不存在，则创建
    IF @DatabaseCategoryId IS NULL
    BEGIN
        INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, IsEnabled, CreatedTime)
        VALUES ('数据库设计', '用于数据库设计、SQL脚本生成和优化的提示词模板', 'database', '#2f54eb', 9, 1, GETDATE());
        
        SET @DatabaseCategoryId = SCOPE_IDENTITY();
        PRINT '已创建数据库设计分类';
    END
    ELSE
    BEGIN
        PRINT '数据库设计分类已存在';
    END
    
    PRINT '开始添加SQL扩展属性注释生成模板...';

    -- 添加新的SQL扩展属性注释生成模板
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, IsDefault, Tags, CreatedTime
    )
    VALUES (
        'SQL扩展属性注释生成模板',
        '为SQL脚本生成SQL Server扩展属性注释（sp_addextendedproperty），注释会保存到数据库元数据中',
        @DatabaseCategoryId,
        N'请为以下SQL脚本添加SQL Server扩展属性注释（sp_addextendedproperty）。

## 任务要求
1. **表注释**：使用sp_addextendedproperty为每个表添加MS_Description扩展属性
2. **字段注释**：使用sp_addextendedproperty为每个字段添加MS_Description扩展属性
3. **注释格式**：使用标准的SQL Server扩展属性语法
4. **注释内容**：准确、详细、专业的中文描述
5. **智能推断**：根据字段名称和类型智能推断业务含义
6. **完整输出**：包含原始CREATE TABLE语句和所有扩展属性注释

## 项目上下文
- 项目ID：{{projectId}}
- 数据库类型：{{databaseType}}
- 业务领域：{{businessDomain}}

## 扩展属性注释格式规范

### 表注释格式：
```sql
-- 表注释
EXEC sp_addextendedproperty
    @name = N''''MS_Description'''', @value = N''''表的详细业务描述'''',
    @level0type = N''''Schema'''', @level0name = N''''dbo'''',
    @level1type = N''''Table'''', @level1name = N''''表名'''';
GO
```

### 字段注释格式：
```sql
-- 字段注释
EXEC sp_addextendedproperty
    @name = N''''MS_Description'''', @value = N''''字段的详细业务描述'''',
    @level0type = N''''Schema'''', @level0name = N''''dbo'''',
    @level1type = N''''Table'''', @level1name = N''''表名'''',
    @level2type = N''''Column'''', @level2name = N''''字段名'''';
GO
```

## 注释内容要求
- **表注释**：说明表的业务用途、数据特征、在系统中的作用、与其他表的关系
- **字段注释**：说明字段的业务含义、数据格式、约束规则、取值范围、业务规则
- **专业术语**：使用准确的业务术语和技术术语
- **详细描述**：提供足够的信息帮助开发者和DBA理解表结构

## 示例输出格式：
```sql
CREATE TABLE [Users] (
    [Id] INT IDENTITY(1,1) NOT NULL,
    [Username] NVARCHAR(50) NOT NULL,
    [Email] NVARCHAR(100) NULL,
    PRIMARY KEY ([Id])
);
GO

-- 表注释
EXEC sp_addextendedproperty
    @name = N''''MS_Description'''', @value = N''''系统用户信息管理表，存储所有用户的基本信息、权限、状态等'''',
    @level0type = N''''Schema'''', @level0name = N''''dbo'''',
    @level1type = N''''Table'''', @level1name = N''''Users'''';
GO

-- 字段注释
EXEC sp_addextendedproperty
    @name = N''''MS_Description'''', @value = N''''用户唯一标识符，自增主键'''',
    @level0type = N''''Schema'''', @level0name = N''''dbo'''',
    @level1type = N''''Table'''', @level1name = N''''Users'''',
    @level2type = N''''Column'''', @level2name = N''''Id'''';
GO
```

## 待处理的SQL脚本
{{sqlScript}}

请返回包含原始CREATE TABLE语句和完整扩展属性注释的SQL脚本：',
        N'[
            {
                "name": "sqlScript",
                "type": "string",
                "required": true,
                "description": "需要添加扩展属性注释的SQL脚本"
            },
            {
                "name": "projectId",
                "type": "number",
                "required": false,
                "description": "项目ID，用于提供业务上下文"
            },
            {
                "name": "databaseType",
                "type": "string",
                "required": false,
                "default": "SqlServer",
                "options": ["SqlServer"],
                "description": "数据库类型，扩展属性注释仅支持SQL Server"
            },
            {
                "name": "businessDomain",
                "type": "string",
                "required": false,
                "default": "项目管理系统",
                "description": "业务领域，用于更准确的注释生成"
            }
        ]',
        'System',
        'SQLCommentGeneration',
        'SQL',
        'SQL Server',
        'Medium',
        2000,
        0, -- 不设为默认，让用户选择
        'SQL扩展属性,数据库注释,元数据,sp_addextendedproperty,MS_Description',
        GETDATE()
    );

    PRINT '已成功添加SQL扩展属性注释生成模板';
END
ELSE
BEGIN
    PRINT 'SQL扩展属性注释生成模板已存在';
END

-- 同时更新原有的标准模板，将其设为非默认
UPDATE PromptTemplates
SET IsDefault = 0
WHERE TaskType = 'SQLCommentGeneration' AND Name = 'SQL注释生成标准模板';

-- 显示所有SQL注释生成模板
SELECT
    Name,
    Description,
    TaskType,
    IsDefault,
    Tags
FROM PromptTemplates
WHERE TaskType = 'SQLCommentGeneration'
ORDER BY IsDefault DESC, Name;

PRINT '==============================================';
PRINT 'SQL扩展属性注释生成模板添加完成！';
PRINT '现在AI将生成SQL Server扩展属性注释格式';