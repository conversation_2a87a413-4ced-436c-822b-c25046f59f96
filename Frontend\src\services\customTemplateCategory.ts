/**
 * 自定义模板分类服务
 */
import api from './api'

// 分类接口
export interface CustomTemplateCategory {
  id: number
  name: string
  description?: string
  parentId?: number
  parentName?: string
  icon?: string
  color?: string
  sortOrder: number
  isSystem: boolean
  isEnabled: boolean
  templateCount: number
  children: CustomTemplateCategory[]
  createdTime: string
  updatedTime?: string
  createdBy?: number
}

// 创建分类DTO
export interface CreateCustomTemplateCategoryDto {
  name: string
  description?: string
  parentId?: number
  icon?: string
  color?: string
  sortOrder: number
  isEnabled: boolean
}

// 更新分类DTO
export interface UpdateCustomTemplateCategoryDto {
  name: string
  description?: string
  parentId?: number
  icon?: string
  color?: string
  sortOrder: number
  isEnabled: boolean
}

// 分类树节点
export interface CategoryTreeNode {
  id: number
  name: string
  icon?: string
  color?: string
  templateCount: number
  isSystem: boolean
  children: CategoryTreeNode[]
}

// 分类统计
export interface CategoryStatistics {
  categoryId: number
  categoryName: string
  templateCount: number
  sequenceCount: number
  totalUsageCount: number
  lastUsedTime?: string
}

/**
 * 自定义模板分类服务类
 */
export default class CustomTemplateCategoryService {

  /**
   * 获取所有分类
   */
  static async getCategories(): Promise<CustomTemplateCategory[]> {
    const response = await api.get('/api/CustomTemplateCategory')
    return response.data
  }

  /**
   * 获取分类树结构
   */
  static async getCategoryTree(): Promise<CategoryTreeNode[]> {
    const response = await api.get('/api/CustomTemplateCategory/tree')
    return response.data
  }

  /**
   * 根据ID获取分类
   */
  static async getCategory(id: number): Promise<CustomTemplateCategory> {
    const response = await api.get(`/api/CustomTemplateCategory/${id}`)
    return response.data
  }

  /**
   * 创建分类
   */
  static async createCategory(dto: CreateCustomTemplateCategoryDto): Promise<CustomTemplateCategory> {
    const response = await api.post('/api/CustomTemplateCategory', dto)
    return response.data
  }

  /**
   * 更新分类
   */
  static async updateCategory(id: number, dto: UpdateCustomTemplateCategoryDto): Promise<CustomTemplateCategory> {
    const response = await api.put(`/api/CustomTemplateCategory/${id}`, dto)
    return response.data
  }

  /**
   * 删除分类
   */
  static async deleteCategory(id: number): Promise<void> {
    await api.delete(`/api/CustomTemplateCategory/${id}`)
  }

  /**
   * 获取分类统计信息
   */
  static async getCategoryStatistics(): Promise<CategoryStatistics[]> {
    const response = await api.get('/api/CustomTemplateCategory/statistics')
    return response.data
  }

  /**
   * 移动分类
   */
  static async moveCategory(id: number, newParentId?: number): Promise<void> {
    await api.post(`/api/CustomTemplateCategory/${id}/move`, newParentId)
  }

  /**
   * 启用/禁用分类
   */
  static async toggleEnabled(id: number, enabled: boolean): Promise<void> {
    await api.post(`/api/CustomTemplateCategory/${id}/toggle-enabled`, enabled)
  }

  /**
   * 批量更新分类排序
   */
  static async batchUpdateSortOrder(sortOrders: Array<{ categoryId: number, sortOrder: number }>): Promise<void> {
    await api.post('/api/CustomTemplateCategory/batch-sort', sortOrders)
  }

  /**
   * 获取预定义的分类图标列表
   */
  static getPredefinedIcons(): Array<{ icon: string, name: string }> {
    return [
      { icon: 'Operation', name: '按钮' },
      { icon: 'Menu', name: '菜单' },
      { icon: 'ChatDotRound', name: '对话框' },
      { icon: 'Edit', name: '输入框' },
      { icon: 'Picture', name: '图标' },
      { icon: 'Document', name: '文本' },
      { icon: 'DataLine', name: '状态' },
      { icon: 'Setting', name: '工具栏' },
      { icon: 'Box', name: '面板' },
      { icon: 'Cpu', name: 'AI自动化' },
      { icon: 'Flash', name: '快速操作' },
      { icon: 'Reading', name: '文档' },
      { icon: 'Setting', name: '设置' },
      { icon: 'User', name: '用户' },
      { icon: 'Star', name: '收藏' },
      { icon: 'MagicStick', name: '测试' },
      { icon: 'Link', name: '其他' }
    ]
  }

  /**
   * 获取预定义的分类颜色列表
   */
  static getPredefinedColors(): Array<{ color: string, name: string }> {
    return [
      { color: '#409EFF', name: '蓝色' },
      { color: '#67C23A', name: '绿色' },
      { color: '#E6A23C', name: '橙色' },
      { color: '#F56C6C', name: '红色' },
      { color: '#909399', name: '灰色' },
      { color: '#606266', name: '深灰' },
      { color: '#13CE66', name: '成功绿' },
      { color: '#FF9500', name: '警告橙' },
      { color: '#5856D6', name: '紫色' },
      { color: '#8E8E93', name: '浅灰' },
      { color: '#0078D4', name: '微软蓝' },
      { color: '#722ED1', name: '深紫' },
      { color: '#FA8C16', name: '活力橙' },
      { color: '#52C41A', name: '自然绿' },
      { color: '#1890FF', name: '科技蓝' },
      { color: '#EB2F96', name: '品红' }
    ]
  }

  /**
   * 验证分类名称
   */
  static validateCategoryName(name: string): { valid: boolean, message?: string } {
    if (!name || name.trim().length === 0) {
      return { valid: false, message: '分类名称不能为空' }
    }

    if (name.length > 100) {
      return { valid: false, message: '分类名称长度不能超过100个字符' }
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      return { valid: false, message: '分类名称不能包含特殊字符 < > : " / \\ | ? *' }
    }

    return { valid: true }
  }

  /**
   * 构建分类路径（面包屑）
   */
  static buildCategoryPath(category: CustomTemplateCategory, allCategories: CustomTemplateCategory[]): string[] {
    const path: string[] = []
    let current: CustomTemplateCategory | undefined = category

    while (current) {
      path.unshift(current.name)
      current = current.parentId ? allCategories.find(c => c.id === current!.parentId) : undefined
    }

    return path
  }

  /**
   * 检查分类是否可以删除
   */
  static canDeleteCategory(category: CustomTemplateCategory): { canDelete: boolean, reason?: string } {
    if (category.isSystem) {
      return { canDelete: false, reason: '系统分类不能删除' }
    }

    if (category.children && category.children.length > 0) {
      return { canDelete: false, reason: '该分类下还有子分类，请先删除子分类' }
    }

    if (category.templateCount > 0) {
      return { canDelete: false, reason: '该分类下还有模板，请先移动或删除模板' }
    }

    return { canDelete: true }
  }

  /**
   * 获取分类的显示名称（包含层级缩进）
   */
  static getCategoryDisplayName(category: CustomTemplateCategory, level: number = 0): string {
    const indent = '　'.repeat(level) // 使用全角空格缩进
    return `${indent}${category.name}`
  }
}
