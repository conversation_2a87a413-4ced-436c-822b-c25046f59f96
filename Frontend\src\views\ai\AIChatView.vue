<template>
  <div class="ai-chat-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <el-icon class="title-icon"><ChatDotRound /></el-icon>
            AI智能助手
          </h1>
          <p class="subtitle">通过对话创建项目、需求文档、设计图表等，让AI成为您的开发伙伴</p>
        </div>
        <div class="header-actions">
          <el-button-group>
            <el-button 
              :type="currentMode === 'general' ? 'primary' : 'default'"
              @click="switchMode('general')"
            >
              <el-icon><Service /></el-icon>
              通用对话
            </el-button>
            <el-button 
              :type="currentMode === 'project' ? 'primary' : 'default'"
              @click="switchMode('project')"
            >
              <el-icon><Folder /></el-icon>
              项目助手
            </el-button>
            <el-button 
              :type="currentMode === 'requirement' ? 'primary' : 'default'"
              @click="switchMode('requirement')"
            >
              <el-icon><Document /></el-icon>
              需求分析
            </el-button>
            <el-button 
              :type="currentMode === 'design' ? 'primary' : 'default'"
              @click="switchMode('design')"
            >
              <el-icon><PictureRounded /></el-icon>
              设计生成
            </el-button>
            <el-button 
              :type="currentMode === 'development' ? 'primary' : 'default'"
              @click="switchMode('development')"
            >
              <el-icon><Operation /></el-icon>
              开发管理
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：对话区域 -->
      <div class="chat-section">
        <el-card class="chat-card" shadow="never">
          <!-- 对话头部 -->
          <template #header>
            <div class="chat-header">
              <div class="mode-info">
                <el-icon class="mode-icon" :color="getModeColor(currentMode)">
                  <component :is="getModeIcon(currentMode)" />
                </el-icon>
                <div>
                  <h3>{{ getModeTitle(currentMode) }}</h3>
                  <p class="mode-desc">{{ getModeDescription(currentMode) }}</p>
                </div>
              </div>
              <div class="chat-actions">
                <div class="conversation-info">
                  <el-text size="small" type="info">
                    对话ID: {{ currentConversationId.substring(0, 8) }}...
                  </el-text>
                </div>
                <div class="vector-context-switch">
                  <el-switch
                    v-model="enableVectorContext"
                    size="small"
                    active-text="向量增强"
                    inactive-text="基础模式"
                    :active-value="true"
                    :inactive-value="false"
                  />
                </div>
                <el-button size="small" @click="clearConversation">
                  <el-icon><Delete /></el-icon>
                  清空对话
                </el-button>
                <el-button size="small" @click="exportConversation">
                  <el-icon><Download /></el-icon>
                  导出对话
                </el-button>
              </div>
            </div>
          </template>

          <!-- 对话消息区域 -->
          <div class="messages-container" ref="messagesContainer">
            <!-- 加载历史对话状态 -->
            <div v-if="isLoadingHistory" class="loading-history">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>正在加载对话历史...</span>
            </div>

            <!-- 欢迎消息 -->
            <div v-else-if="messages.length === 0" class="welcome-section">
              <div class="welcome-avatar">
                <el-icon size="48" :color="getModeColor(currentMode)">
                  <component :is="getModeIcon(currentMode)" />
                </el-icon>
              </div>
              <div class="welcome-content">
                <h2>{{ getModeWelcome(currentMode) }}</h2>
                <p>{{ getModeDescription(currentMode) }}</p>
                
                <!-- 快速开始建议 -->
                <div class="quick-suggestions">
                  <h4>💡 您可以尝试这样说：</h4>
                  <div class="suggestion-list">
                    <el-tag 
                      v-for="suggestion in getModeSuggestions(currentMode)"
                      :key="suggestion"
                      class="suggestion-tag"
                      @click="sendSuggestion(suggestion)"
                      effect="plain"
                    >
                      {{ suggestion }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 对话消息列表 -->
            <div v-for="(message, index) in messages" :key="index" class="message-item">
              <div :class="['message', message.type]">
                <div class="message-avatar">
                  <el-avatar v-if="message.type === 'user'" :size="32">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <el-avatar v-else :size="32" :style="{ backgroundColor: getModeColor(currentMode) }">
                    <el-icon><Service /></el-icon>
                  </el-avatar>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender">{{ message.type === 'user' ? '您' : 'AI助手' }}</span>
                    <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  <div class="message-body" v-html="formatMessage(message.content)"></div>
                  
                  <!-- AI消息的操作按钮 -->
                  <div v-if="message.type === 'ai' && message.actions" class="message-actions">
                    <el-button 
                      v-for="action in message.actions"
                      :key="action.type"
                      :type="action.type === 'primary' ? 'primary' : 'default'"
                      size="small"
                      @click="handleMessageAction(action, message)"
                    >
                      <el-icon><component :is="action.icon" /></el-icon>
                      {{ action.label }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI正在输入指示器 -->
            <div v-if="aiTyping" class="typing-indicator">
              <div class="message ai">
                <div class="message-avatar">
                  <el-avatar :size="32" :style="{ backgroundColor: getModeColor(currentMode) }">
                    <el-icon><Service /></el-icon>
                  </el-avatar>
                </div>
                <div class="message-content">
                  <div class="typing-animation">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <div class="input-container">
              <el-input
                v-model="currentMessage"
                type="textarea"
                :rows="3"
                :placeholder="getInputPlaceholder(currentMode)"
                @keydown.ctrl.enter="sendMessage"
                @keydown.meta.enter="sendMessage"
                class="message-input"
              />
              <div class="input-actions">
                <div class="input-tips">
                  <el-text size="small" type="info">
                    按 Ctrl+Enter 发送消息
                  </el-text>
                </div>
                <el-button 
                  type="primary" 
                  @click="sendMessage"
                  :loading="aiTyping"
                  :disabled="!currentMessage.trim()"
                >
                  <el-icon><Promotion /></el-icon>
                  发送
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：上下文和工具面板 -->
      <div class="context-section">
        <!-- 当前上下文 -->
        <el-card class="context-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>当前上下文</span>
            </div>
          </template>
          
          <div class="context-content">
            <!-- 项目选择 -->
            <div v-if="currentMode !== 'general'" class="context-item">
              <label>当前项目：</label>
              <el-select 
                v-model="selectedProjectId" 
                placeholder="选择项目"
                @change="onProjectChange"
                style="width: 100%"
              >
                <el-option
                  v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </el-select>
            </div>

            <!-- AI提供商选择 -->
            <div class="context-item">
              <label>AI提供商：</label>
              <el-select
                v-model="selectedAIProvider"
                placeholder="选择AI提供商"
                style="width: 100%"
              >
                <el-option
                  v-for="provider in aiProviders"
                  :key="provider.id"
                  :label="provider.modelName"
                  :value="provider.id"
                />
              </el-select>
            </div>

            <!-- 高级AI功能选择 -->
            <div class="context-item">
              <label>AI增强功能：</label>
              <el-select
                v-model="selectedAIFeature"
                placeholder="选择AI功能"
                @change="onAIFeatureChange"
                style="width: 100%"
                clearable
              >
                <el-option-group label="🔗 推理链分析">
                  <el-option label="项目全面分析" value="chain-project-analysis" />
                  <el-option label="技术方案评估" value="chain-tech-evaluation" />
                  <el-option label="风险评估分析" value="chain-risk-assessment" />
                  <el-option label="需求深度分析" value="chain-requirement-analysis" />
                </el-option-group>
                <el-option-group label="🤖 专业Agent">
                  <el-option label="项目管理专家" value="agent-project-manager" />
                  <el-option label="系统架构师" value="agent-architect" />
                  <el-option label="开发工程师" value="agent-developer" />
                  <el-option label="测试工程师" value="agent-tester" />
                  <el-option label="业务分析师" value="agent-business-analyst" />
                </el-option-group>
                <el-option-group label="👥 多专家协作">
                  <el-option label="专家团队协作" value="collaboration-consensus" />
                  <el-option label="专家观点辩论" value="collaboration-debate" />
                  <el-option label="顺序深度分析" value="collaboration-sequential" />
                </el-option-group>
                <el-option-group label="🔍 智能搜索">
                  <el-option label="文档智能搜索" value="search-semantic" />
                  <el-option label="知识增强问答" value="search-rag" />
                  <el-option label="相似项目推荐" value="knowledge-similar-projects" />
                  <el-option label="专家技能推荐" value="knowledge-expert-recommend" />
                </el-option-group>
              </el-select>
            </div>

            <!-- AI功能说明 -->
            <div v-if="selectedAIFeature" class="context-item">
              <div class="ai-feature-info">
                <div class="feature-title">
                  <el-icon><Magic /></el-icon>
                  <span>{{ getAIFeatureTitle(selectedAIFeature) }}</span>
                </div>
                <div class="feature-description">
                  {{ getAIFeatureDescription(selectedAIFeature) }}
                </div>
                <div class="feature-tips">
                  <el-text type="info" size="small">
                    💡 {{ getAIFeatureTips(selectedAIFeature) }}
                  </el-text>
                </div>
              </div>
            </div>

            <!-- Prompt分类选择 -->
            <div class="context-item">
              <label>Prompt分类：</label>
              <el-select
                v-model="selectedPromptCategory"
                placeholder="选择Prompt分类"
                style="width: 100%"
                @change="onPromptCategoryChange"
              >
                <el-option
                  v-for="category in promptCategories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                >
                  <div class="category-option">
                    <div class="category-name">
                      <el-icon v-if="category.icon" style="margin-right: 4px;">
                        <component :is="category.icon" />
                      </el-icon>
                      {{ category.name }}
                    </div>
                    <div class="category-desc">{{ category.description }}</div>
                    <el-tag size="small" type="info">{{ category.templateCount }}个模板</el-tag>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- Prompt模板选择 -->
            <div class="context-item">
              <label>Prompt模板：</label>
              <el-select
                v-model="selectedPromptTemplate"
                placeholder="选择Prompt模板"
                style="width: 100%"
                @change="onPromptTemplateChange"
                :disabled="!selectedPromptCategory"
              >
                <el-option
                  v-for="template in promptTemplates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                >
                  <div class="template-option">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-desc">{{ template.description }}</div>
                    <div class="template-tags">
                      <el-tag size="small" type="success">{{ template.taskType }}</el-tag>
                      <el-tag v-if="template.isDefault" size="small" type="warning">默认</el-tag>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- Prompt内容预览按钮 -->
            <div v-if="selectedTemplateInfo" class="context-item">
              <div class="template-preview-trigger">
                <label>Prompt模板预览：</label>
                <div class="preview-actions">
                  <el-button
                    size="small"
                    type="primary"
                    @click="openTemplatePreviewDialog"
                    icon="View"
                  >
                    查看详情
                  </el-button>
                  <el-button
                    size="small"
                    type="success"
                    @click="copyTemplateContent"
                    icon="CopyDocument"
                  >
                    复制
                  </el-button>
                </div>
              </div>

              <!-- 简要信息 -->
              <div class="template-summary">
                <div class="summary-tags">
                  <el-tag size="small" type="info">{{ selectedTemplateInfo.categoryName }}</el-tag>
                  <el-tag size="small" type="success">{{ selectedTemplateInfo.taskType }}</el-tag>
                  <el-tag v-if="selectedTemplateInfo.isDefault" size="small" type="warning">默认</el-tag>
                  <el-tag v-if="getVariableCount(selectedTemplateInfo.content) > 0" size="small" type="primary">
                    {{ getVariableCount(selectedTemplateInfo.content) }}个变量
                  </el-tag>
                </div>
                <div class="summary-description">
                  {{ selectedTemplateInfo.description }}
                </div>
              </div>
            </div>

            <!-- 模板信息显示 -->
            <div v-if="selectedTemplateInfo" class="template-info">
              <h4>{{ selectedTemplateInfo.name }}</h4>
              <p class="template-description">{{ selectedTemplateInfo.description }}</p>
              <div class="template-meta">
                <el-tag size="small" :type="getTaskTypeColor(selectedTemplateInfo.taskType)">
                  {{ selectedTemplateInfo.taskType }}
                </el-tag>
                <el-tag size="small" type="info">使用{{ selectedTemplateInfo.usageCount }}次</el-tag>
                <el-tag v-if="selectedTemplateInfo.isDefault" size="small" type="success">默认</el-tag>
              </div>
            </div>

            <!-- 项目信息显示 -->
            <div v-if="selectedProject" class="project-info">
              <h4>{{ selectedProject.name }}</h4>
              <p class="project-desc">{{ selectedProject.description }}</p>
              <div class="project-meta">
                <el-tag size="small">{{ selectedProject.status }}</el-tag>
                <el-tag size="small" type="info">{{ selectedProject.priority }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 快速操作 -->
        <el-card class="tools-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Tools /></el-icon>
              <span>快速操作</span>
            </div>
          </template>
          
          <div class="tools-content">
            <div class="tool-buttons">
              <el-button 
                v-for="tool in getModeTools(currentMode)"
                :key="tool.key"
                :type="(tool as any).type || 'default'"
                size="small"
                @click="useTool(tool)"
                class="tool-button"
              >
                <el-icon><component :is="tool.icon" /></el-icon>
                {{ tool.label }}
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 对话历史 -->
        <el-card class="history-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Clock /></el-icon>
              <span>最近对话</span>
            </div>
          </template>
          
          <div class="history-content">
            <div v-if="conversationHistory.length === 0" class="empty-history">
              <el-text type="info">暂无对话历史</el-text>
            </div>
            <div v-else class="history-list">
              <div 
                v-for="conversation in conversationHistory"
                :key="conversation.id"
                class="history-item"
                @click="loadConversation(conversation)"
              >
                <div class="history-title">{{ conversation.title }}</div>
                <div class="history-meta">
                  <el-text size="small" type="info">
                    {{ formatTime(conversation.lastMessageTime) }}
                  </el-text>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- Prompt模板预览弹窗 -->
    <el-dialog
      v-model="showTemplatePreviewDialog"
      title="Prompt模板详情预览"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="template-preview-dialog"
    >
      <div v-if="selectedTemplateInfo" class="dialog-content">
        <!-- 模板基本信息 -->
        <div class="template-header-info">
          <div class="template-title">
            <h3>{{ selectedTemplateInfo.name }}</h3>
            <div class="template-meta-tags">
              <el-tag size="small" type="info">{{ selectedTemplateInfo.categoryName }}</el-tag>
              <el-tag size="small" type="success">{{ selectedTemplateInfo.taskType }}</el-tag>
              <el-tag v-if="selectedTemplateInfo.isDefault" size="small" type="warning">默认模板</el-tag>
              <el-tag size="small" type="primary">使用{{ selectedTemplateInfo.usageCount }}次</el-tag>
              <el-tag v-if="getVariableCount(selectedTemplateInfo.content) > 0" size="small" type="danger">
                {{ getVariableCount(selectedTemplateInfo.content) }}个变量
              </el-tag>
            </div>
          </div>
          <div class="template-description">
            <strong>描述：</strong>{{ selectedTemplateInfo.description }}
          </div>
        </div>

        <!-- 内容预览区域 -->
        <div class="template-content-area">
          <el-tabs v-model="activePreviewTab" class="preview-tabs">
            <!-- 原始模板 -->
            <el-tab-pane label="原始模板" name="original">
              <div class="tab-content">
                <div class="content-header">
                  <span>包含变量的原始模板内容</span>
                  <div class="header-actions">
                    <el-button size="small" @click="copyTemplateContent" icon="CopyDocument">
                      复制原始内容
                    </el-button>
                  </div>
                </div>
                <div class="template-content-display">
                  <div
                    class="highlighted-content"
                    v-html="highlightVariables(selectedTemplateInfo.content)"
                  ></div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 格式化预览 -->
            <el-tab-pane label="格式化预览" name="formatted">
              <div class="tab-content">
                <div class="content-header">
                  <span>参数替换后的实际效果</span>
                  <div class="header-actions">
                    <el-button size="small" @click="refreshPreview" icon="Refresh">
                      刷新预览
                    </el-button>
                    <el-button size="small" @click="copyFormattedContent" icon="CopyDocument">
                      复制预览内容
                    </el-button>
                  </div>
                </div>
                <div class="formatted-content-display">
                  <pre class="formatted-text">{{ formattedPreview || '请选择项目和AI提供商后查看格式化预览' }}</pre>
                </div>
              </div>
            </el-tab-pane>

            <!-- 变量参考 -->
            <el-tab-pane label="变量参考" name="variables">
              <div class="tab-content">
                <div class="content-header">
                  <span>常用变量参考手册</span>
                  <div class="header-actions">
                    <el-text type="info" size="small">
                      💡 点击变量名可复制到剪贴板
                    </el-text>
                  </div>
                </div>
                <div class="variables-reference-content">
                  <div class="variable-categories">
                    <div class="variable-category">
                      <h4>项目相关变量</h4>
                      <div class="variable-grid">
                        <div
                          v-for="variable in projectVariables"
                          :key="variable.name"
                          class="variable-item"
                          @click="copyVariableToClipboard(variable.name)"
                        >
                          <div class="variable-name">{{ variable.name }}</div>
                          <div class="variable-desc">{{ variable.description }}</div>
                        </div>
                      </div>
                    </div>

                    <div class="variable-category">
                      <h4>用户交互变量</h4>
                      <div class="variable-grid">
                        <div
                          v-for="variable in userVariables"
                          :key="variable.name"
                          class="variable-item"
                          @click="copyVariableToClipboard(variable.name)"
                        >
                          <div class="variable-name">{{ variable.name }}</div>
                          <div class="variable-desc">{{ variable.description }}</div>
                        </div>
                      </div>
                    </div>

                    <div class="variable-category">
                      <h4>时间日期变量</h4>
                      <div class="variable-grid">
                        <div
                          v-for="variable in timeVariables"
                          :key="variable.name"
                          class="variable-item"
                          @click="copyVariableToClipboard(variable.name)"
                        >
                          <div class="variable-name">{{ variable.name }}</div>
                          <div class="variable-desc">{{ variable.description }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 模板标签 -->
        <div v-if="selectedTemplateInfo.tags" class="template-tags-section">
          <strong>标签：</strong>
          <el-tag
            v-for="tag in selectedTemplateInfo.tags.split(',')"
            :key="tag.trim()"
            size="small"
            class="tag-item"
          >
            {{ tag.trim() }}
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showTemplatePreviewDialog = false">关闭</el-button>
          <el-button type="primary" @click="useTemplate">
            使用此模板
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound, Service, Folder, Document, PictureRounded, Operation,
  Delete, Download, User, InfoFilled, Tools, Clock, Promotion, Loading
} from '@element-plus/icons-vue'
import { ProjectService } from '@/services/project'
import { AIProviderService } from '@/services/aiProvider'
import { AIDialogServiceFactory, GeneralAIService, type AIDialogContext } from '@/services/aiDialogService'
import { PromptService, type PromptTemplate as ServicePromptTemplate, type PromptCategory } from '@/services/promptService'
import dayjs from 'dayjs'

// 定义类型
interface Message {
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  actions?: Array<{
    type: string
    label: string
    icon: string
    data?: any
  }>
  metadata?: {
    type?: string
    [key: string]: any
  }
}

interface Project {
  id: number
  name: string
  description: string
  status: string
  priority: string
}

interface AIProvider {
  id: number
  userId: number
  modelName: string
  apiEndpoint?: string
  apiKey?: string
  modelParameters?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 使用服务中的PromptTemplate类型
type PromptTemplate = ServicePromptTemplate

interface ConversationHistory {
  id: string
  title: string
  mode: string
  lastMessageTime: Date
}

// 响应式数据
const router = useRouter()
const messagesContainer = ref<HTMLElement>()
const currentMode = ref<string>('general')
const currentMessage = ref('')
const aiTyping = ref(false)
const messages = ref<Message[]>([])
const selectedProjectId = ref<number>()
const selectedAIProvider = ref<number>()
const selectedPromptCategory = ref<number>()
const selectedPromptTemplate = ref<number>()
const showTemplatePreviewDialog = ref(false)
const activePreviewTab = ref('original')
const selectedAIFeature = ref<string>('')
const formattedPreview = ref('')
const projects = ref<Project[]>([])
const aiProviders = ref<AIProvider[]>([])
const promptCategories = ref<PromptCategory[]>([])
const promptTemplates = ref<PromptTemplate[]>([])
const conversationHistory = ref<ConversationHistory[]>([])

// 对话管理相关
const currentConversationId = ref<string>('')
const conversationStartTime = ref<Date>(new Date())
const isLoadingHistory = ref(false)
const enableVectorContext = ref(true) // 是否启用向量上下文

// 服务实例
const promptService = new PromptService()

// 计算属性
const selectedProject = computed(() => {
  return projects.value.find(p => p.id === selectedProjectId.value)
})

const selectedTemplateInfo = computed(() => {
  return promptTemplates.value.find(t => t.id === selectedPromptTemplate.value)
})

// 模式配置
const modeConfig = {
  general: {
    title: '通用对话',
    description: '与AI进行自由对话，获取各种帮助和建议',
    welcome: '👋 您好！我是您的AI助手',
    icon: 'Service',
    color: '#409eff',
    placeholder: '请输入您想要咨询的问题...',
    suggestions: [
      '帮我分析一下软件开发的最佳实践',
      '如何提高团队协作效率？',
      '推荐一些前端开发工具',
      '解释一下微服务架构的优缺点'
    ],
    tools: [
      { key: 'help', label: '使用帮助', icon: 'QuestionFilled' },
      { key: 'examples', label: '示例对话', icon: 'ChatLineRound' }
    ]
  },
  project: {
    title: '项目助手',
    description: '帮助您创建和管理项目，制定项目计划',
    welcome: '🚀 项目助手为您服务',
    icon: 'Folder',
    color: '#67c23a',
    placeholder: '描述您想要创建的项目，或询问项目管理相关问题...',
    suggestions: [
      '我想创建一个电商网站项目',
      '帮我制定项目开发计划',
      '如何评估项目风险？',
      '项目进度管理的最佳实践'
    ],
    tools: [
      { key: 'project-analysis', label: '项目分析', icon: 'TrendCharts', type: 'primary' },
      { key: 'project-template', label: '项目模板', icon: 'Document' },
      { key: 'project-consultation', label: '项目咨询', icon: 'ChatDotRound' }
    ]
  },
  requirement: {
    title: '需求分析',
    description: '智能分析需求，生成规范的需求文档',
    welcome: '📋 需求分析专家为您服务',
    icon: 'Document',
    color: '#e6a23c',
    placeholder: '描述您的需求，我将帮您分析并生成需求文档...',
    suggestions: [
      '我需要一个用户管理系统',
      '帮我分析电商平台的核心功能',
      '生成移动应用的需求文档',
      '如何编写用户故事？'
    ],
    tools: [
      { key: 'create-requirement', label: '创建需求', icon: 'Plus', type: 'primary' },
      { key: 'requirement-template', label: '需求模板', icon: 'Document' },
      { key: 'user-story', label: '用户故事', icon: 'User' }
    ]
  },
  design: {
    title: '设计生成',
    description: '生成ER图、原型图、架构图等设计文档',
    welcome: '🎨 设计专家为您服务',
    icon: 'PictureRounded',
    color: '#f56c6c',
    placeholder: '描述您需要的设计图表，我将为您生成...',
    suggestions: [
      '为用户管理系统生成ER图',
      '设计电商网站的原型图',
      '创建系统架构图',
      '生成数据库设计文档'
    ],
    tools: [
      { key: 'create-er', label: '生成ER图', icon: 'Connection', type: 'primary' },
      { key: 'create-prototype', label: '生成原型', icon: 'Picture' },
      { key: 'create-context', label: '上下文图', icon: 'Share' }
    ]
  },
  development: {
    title: '开发管理',
    description: '分解开发任务，生成代码，管理开发流程',
    welcome: '⚙️ 开发专家为您服务',
    icon: 'Operation',
    color: '#909399',
    placeholder: '描述您的开发需求，我将帮您分解任务和生成代码...',
    suggestions: [
      '将需求分解为开发步骤',
      '生成用户登录功能的代码',
      '如何设计API接口？',
      '代码审查的要点是什么？'
    ],
    tools: [
      { key: 'decompose-requirement', label: '需求分解', icon: 'Operation', type: 'primary' },
      { key: 'generate-code', label: '代码生成', icon: 'DocumentCopy' },
      { key: 'create-task', label: '创建任务', icon: 'Plus' }
    ]
  }
}

// 方法
const getModeColor = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.color || '#409eff'
}

const getModeIcon = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.icon || 'Service'
}

const getModeTitle = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.title || '通用对话'
}

const getModeDescription = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.description || ''
}

const getModeWelcome = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.welcome || '您好！'
}

const getInputPlaceholder = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.placeholder || '请输入消息...'
}

const getModeSuggestions = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.suggestions || []
}

const getModeTools = (mode: string) => {
  return modeConfig[mode as keyof typeof modeConfig]?.tools || []
}

// 生成新的对话ID
const generateConversationId = (): string => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${currentMode.value}-${timestamp}-${random}`
}

// 初始化对话
const initializeConversation = () => {
  currentConversationId.value = generateConversationId()
  conversationStartTime.value = new Date()
  messages.value = []
  console.log('初始化新对话:', currentConversationId.value)
}

// 切换模式
const switchMode = async (mode: string) => {
  if (currentMode.value === mode) return

  currentMode.value = mode
  currentMessage.value = ''

  // 初始化新的对话会话
  initializeConversation()

  // 重新加载对应模式的Prompt模板
  await loadPromptTemplates()

  ElMessage.success(`已切换到${getModeTitle(mode)}模式`)
}

// 发送建议消息
const sendSuggestion = (suggestion: string) => {
  currentMessage.value = suggestion
  sendMessage()
}

// 调用高级AI服务
const callAdvancedAIService = async (message: string) => {
  const feature = selectedAIFeature.value

  if (feature.startsWith('chain-')) {
    return await callLangChainService(message, feature)
  } else if (feature.startsWith('agent-')) {
    return await callAgentService(message, feature)
  } else if (feature.startsWith('collaboration-')) {
    return await callCollaborationService(message, feature)
  } else if (feature.startsWith('search-')) {
    return await callSearchService(message, feature)
  } else if (feature.startsWith('knowledge-')) {
    return await callKnowledgeService(message, feature)
  }

  throw new Error(`未知的AI功能: ${feature}`)
}

// 调用推理链服务
const callLangChainService = async (message: string, feature: string) => {
  const chainType = feature.replace('chain-', '')

  const requestData = {
    chainType: chainType,
    projectId: selectedProjectId.value || '',
    userMessage: message,
    variables: {
      projectName: getSelectedProjectName(),
      projectDescription: getSelectedProjectDescription()
    },
    aiProviderConfigId: selectedAIProvider.value
  }

  const response = await fetch('/api/langchain/execute', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })

  if (!response.ok) {
    throw new Error(`推理链服务错误: ${response.status}`)
  }

  const data = await response.json()

  if (!data.success) {
    throw new Error(data.errorMessage || '推理链执行失败')
  }

  return {
    content: formatChainResult(data),
    metadata: {
      type: 'langchain',
      chainType: chainType,
      steps: data.steps,
      duration: data.duration
    }
  }
}

// 调用Agent服务
const callAgentService = async (message: string, feature: string) => {
  const agentType = feature.replace('agent-', '')

  const requestData = {
    agentType: agentType,
    taskType: 'analysis',
    taskDescription: '用户咨询',
    context: getProjectContext(),
    userMessage: message,
    aiConfig: {
      aiProviderConfigId: selectedAIProvider.value
    }
  }

  const response = await fetch('/api/agent/execute', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })

  if (!response.ok) {
    throw new Error(`Agent服务错误: ${response.status}`)
  }

  const data = await response.json()

  if (!data.success) {
    throw new Error(data.errorMessage || 'Agent执行失败')
  }

  return {
    content: `## ${getAIFeatureTitle(feature)}的专业建议\n\n${data.output}`,
    metadata: {
      type: 'agent',
      agentType: agentType,
      duration: data.duration
    }
  }
}

// 调用协作服务
const callCollaborationService = async (message: string, feature: string) => {
  const collaborationType = feature.replace('collaboration-', '')

  const requestData = {
    collaborationType: collaborationType,
    taskDescription: '多专家协作分析',
    context: getProjectContext(),
    userMessage: message,
    participantAgents: ['project-manager', 'architect', 'developer'],
    aiConfig: {
      aiProviderConfigId: selectedAIProvider.value
    }
  }

  const response = await fetch('/api/agent/collaborate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })

  if (!response.ok) {
    throw new Error(`协作服务错误: ${response.status}`)
  }

  const data = await response.json()

  if (!data.success) {
    throw new Error(data.errorMessage || '多专家协作失败')
  }

  return {
    content: formatCollaborationResult(data),
    metadata: {
      type: 'collaboration',
      collaborationType: collaborationType,
      agentResults: data.agentResults,
      duration: data.duration
    }
  }
}

// 调用搜索服务
const callSearchService = async (message: string, feature: string) => {
  if (feature === 'search-semantic') {
    const requestData = {
      query: message,
      topK: 5,
      similarityThreshold: 0.7,
      filters: {
        projectId: selectedProjectId.value
      }
    }

    const response = await fetch('/api/vectorsearch/search/semantic', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`语义搜索错误: ${response.status}`)
    }

    const data = await response.json()

    return {
      content: formatSearchResults(data),
      metadata: {
        type: 'search',
        searchType: 'semantic',
        resultCount: data.length
      }
    }
  } else if (feature === 'search-rag') {
    const requestData = {
      query: message,
      retrievalTopK: 5,
      similarityThreshold: 0.7,
      maxContextLength: 4000,
      filters: {
        projectId: selectedProjectId.value
      },
      aiConfig: {
        aiProviderConfigId: selectedAIProvider.value
      }
    }

    const response = await fetch('/api/vectorsearch/rag/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`RAG服务错误: ${response.status}`)
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.errorMessage || 'RAG生成失败')
    }

    return {
      content: data.generatedText,
      metadata: {
        type: 'rag',
        sourceDocuments: data.sourceDocuments,
        context: data.context
      }
    }
  }

  throw new Error(`未知的搜索功能: ${feature}`)
}

// 调用知识服务
const callKnowledgeService = async (message: string, feature: string) => {
  if (feature === 'knowledge-similar-projects') {
    if (!selectedProjectId.value) {
      throw new Error('请先选择一个项目')
    }

    const response = await fetch(`/api/knowledgegraph/projects/${selectedProjectId.value}/similar?topK=5`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`相似项目推荐错误: ${response.status}`)
    }

    const data = await response.json()

    return {
      content: formatSimilarProjects(data),
      metadata: {
        type: 'knowledge',
        knowledgeType: 'similar-projects',
        projectCount: data.length
      }
    }
  } else if (feature === 'knowledge-expert-recommend') {
    const requestData = {
      skillRequired: message,
      problemDomain: currentMode.value,
      topK: 3
    }

    const response = await fetch('/api/knowledgegraph/experts/recommend', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`专家推荐错误: ${response.status}`)
    }

    const data = await response.json()

    return {
      content: formatExpertRecommendations(data),
      metadata: {
        type: 'knowledge',
        knowledgeType: 'expert-recommend',
        expertCount: data.length
      }
    }
  }

  throw new Error(`未知的知识功能: ${feature}`)
}

// 辅助方法
const getSelectedProjectName = () => {
  return selectedProject.value?.name || '未选择项目'
}

const getSelectedProjectDescription = () => {
  return selectedProject.value?.description || '暂无描述'
}

const getProjectContext = () => {
  if (!selectedProject.value) {
    return '当前未选择项目'
  }

  return `项目名称：${selectedProject.value.name}
项目描述：${selectedProject.value.description || '暂无描述'}
项目状态：${selectedProject.value.status || '进行中'}
项目优先级：${selectedProject.value.priority || '中等'}`
}

// 格式化推理链结果
const formatChainResult = (data: any) => {
  let result = `# ${data.chainName}\n\n`

  if (data.steps && data.steps.length > 0) {
    result += '## 分析过程\n\n'
    data.steps.forEach((step: any, index: number) => {
      result += `### ${index + 1}. ${step.stepName}\n\n`
      result += `${step.output}\n\n`
    })
  }

  result += `## 总结\n\n${data.finalOutput}\n\n`
  result += `*分析耗时：${Math.round(data.duration / 1000)}秒*`

  return result
}

// 格式化协作结果
const formatCollaborationResult = (data: any) => {
  let result = `# 多专家协作分析结果\n\n`

  if (data.agentResults && data.agentResults.length > 0) {
    data.agentResults.forEach((agentResult: any) => {
      const agentName = getAIFeatureTitle(`agent-${agentResult.agentType}`)
      result += `## ${agentName}的观点\n\n`
      result += `${agentResult.output}\n\n`
    })
  }

  result += `## 综合建议\n\n${data.finalOutput}\n\n`
  result += `*协作耗时：${Math.round(data.duration / 1000)}秒*`

  return result
}

// 格式化搜索结果
const formatSearchResults = (data: any[]) => {
  if (!data || data.length === 0) {
    return '未找到相关文档。'
  }

  let result = `# 搜索结果\n\n找到 ${data.length} 个相关文档：\n\n`

  data.forEach((item: any, index: number) => {
    result += `## ${index + 1}. 文档片段\n\n`
    result += `**相似度：** ${(item.similarityScore * 100).toFixed(1)}%\n\n`
    result += `**内容：**\n${item.content}\n\n`

    if (item.highlights && item.highlights.length > 0) {
      result += `**关键片段：**\n`
      item.highlights.forEach((highlight: string) => {
        result += `- ${highlight}\n`
      })
      result += '\n'
    }
  })

  return result
}

// 格式化相似项目
const formatSimilarProjects = (data: any[]) => {
  if (!data || data.length === 0) {
    return '未找到相似的项目。'
  }

  let result = `# 相似项目推荐\n\n找到 ${data.length} 个相似项目：\n\n`

  data.forEach((project: any, index: number) => {
    result += `## ${index + 1}. ${project.projectName}\n\n`
    result += `**相似度：** ${(project.similarityScore * 100).toFixed(1)}%\n\n`

    if (project.similarityReasons && project.similarityReasons.length > 0) {
      result += `**相似原因：**\n`
      project.similarityReasons.forEach((reason: string) => {
        result += `- ${reason}\n`
      })
      result += '\n'
    }
  })

  return result
}

// 格式化专家推荐
const formatExpertRecommendations = (data: any[]) => {
  if (!data || data.length === 0) {
    return '未找到合适的专家。'
  }

  let result = `# 专家推荐\n\n找到 ${data.length} 位合适的专家：\n\n`

  data.forEach((expert: any, index: number) => {
    result += `## ${index + 1}. ${expert.personName}\n\n`
    result += `**推荐分数：** ${(expert.recommendationScore * 100).toFixed(1)}%\n\n`
    result += `**技能水平：** ${(expert.skillLevel * 100).toFixed(1)}%\n\n`

    if (expert.reasons && expert.reasons.length > 0) {
      result += `**推荐理由：**\n`
      expert.reasons.forEach((reason: string) => {
        result += `- ${reason}\n`
      })
      result += '\n'
    }
  })

  return result
}

// 发送消息（支持流式响应）
const sendMessage = async () => {
  const message = currentMessage.value.trim()
  if (!message || aiTyping.value) return

  // 添加用户消息
  const userMessage: Message = {
    type: 'user',
    content: message,
    timestamp: new Date()
  }
  messages.value.push(userMessage)
  currentMessage.value = ''

  await nextTick()
  scrollToBottom()

  // 创建AI消息占位符
  const aiMessage: Message = {
    type: 'ai',
    content: '',
    timestamp: new Date(),
    actions: []
  }
  messages.value.push(aiMessage)

  // 显示AI正在输入
  aiTyping.value = true

  try {
    // 检查是否使用高级AI功能
    if (selectedAIFeature.value) {
      const response = await callAdvancedAIService(message)
      aiMessage.content = response.content
      aiMessage.metadata = response.metadata
      aiTyping.value = false

      await nextTick()
      scrollToBottom()
    } else {
      // 使用原有的AI服务
      const context: AIDialogContext = {
        projectId: selectedProjectId.value,
        projectName: selectedProject.value?.name,
        taskType: currentMode.value,
        aiProviderConfigId: selectedAIProvider.value,
        promptTemplateId: selectedPromptTemplate.value
      }

      // 获取对应的AI服务
      const aiService = AIDialogServiceFactory.getService(getTaskTypeFromMode(currentMode.value))

      // 使用当前对话ID，如果没有则初始化新对话
      if (!currentConversationId.value) {
        initializeConversation()
      }

      // 尝试使用流式响应
      if (aiService instanceof GeneralAIService && typeof (aiService as any).sendStreamMessage === 'function') {
        await (aiService as any).sendStreamMessage(
          currentConversationId.value,
          message,
          context,
          // onChunk - 接收到新的文本块
          (chunk: string) => {
            aiMessage.content += chunk
            // 实时滚动到底部
            nextTick(() => {
              scrollToBottom()
            })
          },
          // onComplete - 消息接收完成
          (fullResponse: string, actions?: any[]) => {
            aiMessage.content = fullResponse
            if (actions && actions.length > 0) {
              aiMessage.actions = actions
            } else {
              aiMessage.actions = parseMessageActions(fullResponse, currentMode.value)
            }
            aiTyping.value = false
            nextTick(() => {
              scrollToBottom()
            })
          },
          // onError - 发生错误
          (error: string) => {
            console.error('流式消息发送失败:', error)
            aiMessage.content = '抱歉，我现在无法回复您的消息，请稍后重试。'
            aiTyping.value = false
          }
        )
      } else {
        // 使用传统响应
        const response = await aiService.sendMessage(currentConversationId.value, message, context)

        aiMessage.content = response.aiResponse
        aiMessage.actions = parseMessageActions(response.aiResponse, currentMode.value)
        aiTyping.value = false

        await nextTick()
        scrollToBottom()
      }
    }

  } catch (error: any) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请稍后重试')

    aiMessage.content = '抱歉，我暂时无法回复您的消息，请稍后重试。'
    aiTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}

// 将模式转换为任务类型
const getTaskTypeFromMode = (mode: string): string => {
  const modeMap: Record<string, string> = {
    'general': 'General',
    'project': 'ProjectManagement',
    'requirement': 'RequirementAnalysis',
    'design': 'DesignGeneration',
    'development': 'CodeGeneration'
  }
  return modeMap[mode] || 'General'
}

// 解析消息中的操作按钮
const parseMessageActions = (content: string, mode: string): Array<any> => {
  const actions: Array<any> = []

  // 根据模式和内容添加相应的操作按钮
  if (mode === 'project' && content.includes('项目')) {
    actions.push({
      type: 'primary',
      label: '创建项目',
      icon: 'Plus',
      data: { action: 'create-project' }
    })
  }

  if (mode === 'requirement' && content.includes('需求')) {
    actions.push({
      type: 'primary',
      label: '保存需求文档',
      icon: 'Document',
      data: { action: 'save-requirement' }
    })
  }

  if (mode === 'design' && (content.includes('ER图') || content.includes('数据库'))) {
    actions.push({
      type: 'primary',
      label: '生成ER图',
      icon: 'Connection',
      data: { action: 'generate-er' }
    })
  }

  if (mode === 'design' && content.includes('原型')) {
    actions.push({
      type: 'primary',
      label: '生成原型图',
      icon: 'Picture',
      data: { action: 'generate-prototype' }
    })
  }

  if (mode === 'development' && content.includes('步骤')) {
    actions.push({
      type: 'primary',
      label: '分解开发步骤',
      icon: 'Operation',
      data: { action: 'decompose-steps' }
    })
  }

  return actions
}

// 处理消息操作
const handleMessageAction = async (action: any, message: Message) => {
  try {
    switch (action.data?.action) {
      case 'save-requirement':
        await handleSaveRequirement()
        break
      case 'generate-er':
        await handleGenerateER()
        break
      case 'generate-prototype':
        await handleGeneratePrototype()
        break
      case 'decompose-steps':
        await handleDecomposeSteps()
        break
      default:
        ElMessage.info('功能开发中...')
    }
  } catch (error: any) {
    console.error('执行操作失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}



// 处理保存需求文档
const handleSaveRequirement = async () => {
  if (!selectedProjectId.value) {
    ElMessage.warning('请先选择项目')
    return
  }

  // 跳转到需求管理页面，并传递AI聊天上下文
  const query = {
    projectId: selectedProjectId.value,
    fromAIChat: 'true',
    conversationId: currentConversationId.value,
    mode: currentMode.value
  }

  router.push({
    path: '/requirements',
    query
  })
  ElMessage.success('正在跳转到需求管理页面...')
}

// 处理生成ER图
const handleGenerateER = async () => {
  if (!selectedProjectId.value) {
    ElMessage.warning('请先选择项目')
    return
  }

  // 跳转到ER图设计页面，并传递AI聊天上下文
  const query = {
    fromAIChat: 'true',
    conversationId: currentConversationId.value,
    mode: currentMode.value
  }

  router.push({
    path: `/design/${selectedProjectId.value}/er-diagram`,
    query
  })
  ElMessage.success('正在跳转到ER图设计页面...')
}

// 处理生成原型图
const handleGeneratePrototype = async () => {
  if (!selectedProjectId.value) {
    ElMessage.warning('请先选择项目')
    return
  }

  // 跳转到原型设计页面，并传递AI聊天上下文
  const query = {
    fromAIChat: 'true',
    conversationId: currentConversationId.value,
    mode: currentMode.value
  }

  router.push({
    path: `/design/${selectedProjectId.value}/prototype`,
    query
  })
  ElMessage.success('正在跳转到原型设计页面...')
}

// 处理分解开发步骤
const handleDecomposeSteps = async () => {
  if (!selectedProjectId.value) {
    ElMessage.warning('请先选择项目')
    return
  }

  // 跳转到需求分解页面，并传递AI聊天上下文
  const query = {
    projectId: selectedProjectId.value,
    fromAIChat: 'true',
    conversationId: currentConversationId.value,
    mode: currentMode.value
  }

  router.push({
    path: '/development/decompose',
    query
  })
  ElMessage.success('正在跳转到需求分解页面...')
}

// 使用工具
const useTool = (tool: any) => {
  switch (tool.key) {
    case 'project-analysis':
      ElMessage.info('项目分析功能：AI将帮助您分析项目需求、技术选型、风险评估等')
      break
    case 'project-template':
      ElMessage.info('项目模板功能开发中...')
      break
    case 'project-consultation':
      ElMessage.info('项目咨询功能：AI将为您提供项目管理建议和最佳实践')
      break
    case 'create-requirement':
      if (selectedProjectId.value) {
        const query = {
          projectId: selectedProjectId.value,
          fromAIChat: 'true',
          conversationId: currentConversationId.value,
          mode: currentMode.value
        }
        router.push({ path: '/requirements', query })
      } else {
        ElMessage.warning('请先选择项目')
      }
      break
    case 'create-er':
      if (selectedProjectId.value) {
        const query = {
          fromAIChat: 'true',
          conversationId: currentConversationId.value,
          mode: currentMode.value
        }
        router.push({ path: `/design/${selectedProjectId.value}/er-diagram`, query })
      } else {
        ElMessage.warning('请先选择项目')
      }
      break
    case 'create-prototype':
      if (selectedProjectId.value) {
        const query = {
          fromAIChat: 'true',
          conversationId: currentConversationId.value,
          mode: currentMode.value
        }
        router.push({ path: `/design/${selectedProjectId.value}/prototype`, query })
      } else {
        ElMessage.warning('请先选择项目')
      }
      break
    case 'decompose-requirement':
      const decomposeQuery = {
        projectId: selectedProjectId.value,
        fromAIChat: 'true',
        conversationId: currentConversationId.value,
        mode: currentMode.value
      }
      router.push({ path: '/development/decompose', query: decomposeQuery })
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 项目变化处理
const onProjectChange = (projectId: number) => {
  const project = projects.value.find(p => p.id === projectId)
  if (project) {
    ElMessage.success(`已选择项目：${project.name}`)
  }
}

// Prompt分类变化处理
const onPromptCategoryChange = async (categoryId: number) => {
  const category = promptCategories.value.find(c => c.id === categoryId)
  if (category) {
    ElMessage.success(`已选择分类：${category.name}`)
    // 清空当前选择的模板
    selectedPromptTemplate.value = undefined
    // 加载该分类下的模板
    await loadPromptTemplatesByCategory(categoryId)
  }
}

// Prompt模板变化处理
const onPromptTemplateChange = (templateId: number) => {
  const template = promptTemplates.value.find(t => t.id === templateId)
  if (template) {
    ElMessage.success(`已选择模板：${template.name}`)
    // 自动刷新预览
    nextTick(() => {
      refreshPreview()
    })
  }
}

// 复制模板内容
const copyTemplateContent = async () => {
  if (selectedTemplateInfo.value?.content) {
    try {
      await navigator.clipboard.writeText(selectedTemplateInfo.value.content)
      ElMessage.success('模板内容已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请手动复制')
    }
  } else {
    ElMessage.warning('没有可复制的内容')
  }
}

// 格式化Prompt预览
const formatPromptPreview = (template: string): string => {
  if (!template) return ''

  // 获取当前上下文信息
  const selectedProject = projects.value.find(p => p.id === selectedProjectId.value)
  const selectedProvider = aiProviders.value.find(p => p.id === selectedAIProvider.value)

  // 构建详细的项目上下文
  const projectContext = selectedProject ? `
项目名称：${selectedProject.name}
项目描述：${selectedProject.description || '暂无描述'}
项目状态：${selectedProject.status || '进行中'}
项目优先级：${selectedProject.priority || '中等'}
创建时间：${(selectedProject as any).createdAt ? new Date((selectedProject as any).createdAt).toLocaleDateString('zh-CN') : '未知'}
项目负责人：${(selectedProject as any).ownerName || '未指定'}
团队成员：${(selectedProject as any).teamMembers || '待补充'}
技术栈：${(selectedProject as any).techStack || '待确定'}
项目阶段：${(selectedProject as any).phase || '开发阶段'}
预计完成时间：${(selectedProject as any).expectedEndDate ? new Date((selectedProject as any).expectedEndDate).toLocaleDateString('zh-CN') : '待确定'}
` : `
项目信息：请先选择一个项目以获取详细的项目上下文信息。
当前可用项目：${projects.value.map(p => p.name).join('、') || '暂无项目'}
`

  // 构建替换参数
  const context = {
    // 项目相关
    projectName: selectedProject?.name || '[请选择项目]',
    projectDescription: selectedProject?.description || '[项目描述]',
    projectId: selectedProjectId.value || '[项目ID]',
    projectContext: projectContext.trim(),
    projectStatus: selectedProject?.status || '[项目状态]',
    projectPriority: selectedProject?.priority || '[项目优先级]',
    projectOwner: (selectedProject as any)?.ownerName || '[项目负责人]',
    projectTeam: (selectedProject as any)?.teamMembers || '[团队成员]',
    techStack: (selectedProject as any)?.techStack || '[技术栈]',

    // AI提供商相关
    aiProvider: selectedProvider?.modelName || '[请选择AI提供商]',
    modelName: selectedProvider?.modelName || '[模型名称]',

    // 用户相关
    userMessage: '[用户输入的消息]',
    userName: '[用户名称]',

    // 时间相关
    currentDate: new Date().toLocaleDateString('zh-CN'),
    currentTime: new Date().toLocaleTimeString('zh-CN'),
    currentDateTime: new Date().toLocaleString('zh-CN'),

    // 任务相关
    taskType: getTaskTypeFromMode(currentMode.value),
    conversationId: '[对话ID]',

    // 常用占位符
    input: '[用户输入]',
    context: '[上下文信息]',
    requirements: '[需求描述]',
    specifications: '[规格说明]'
  }

  // 替换模板中的变量
  let formatted = template

  // 替换 {变量名} 格式的占位符
  formatted = formatted.replace(/\{(\w+)\}/g, (match, key) => {
    const value = context[key as keyof typeof context]
    return value ? String(value) : match
  })

  // 替换 {{变量名}} 格式的占位符
  formatted = formatted.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    const value = context[key as keyof typeof context]
    return value ? String(value) : match
  })

  // 替换 $变量名 格式的占位符
  formatted = formatted.replace(/\$(\w+)/g, (match, key) => {
    const value = context[key as keyof typeof context]
    return value ? String(value) : match
  })

  return formatted
}

// 刷新预览
const refreshPreview = () => {
  if (selectedTemplateInfo.value?.content) {
    formattedPreview.value = formatPromptPreview(selectedTemplateInfo.value.content)
    ElMessage.success('预览已刷新')
  } else {
    formattedPreview.value = ''
    ElMessage.warning('没有可预览的模板内容')
  }
}

// 统计模板中的变量数量
const getVariableCount = (content: string): number => {
  if (!content) return 0

  const patterns = [
    /\{(\w+)\}/g,      // {变量名}
    /\{\{(\w+)\}\}/g,  // {{变量名}}
    /\$(\w+)/g         // $变量名
  ]

  const variables = new Set<string>()

  patterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      variables.add(match[1])
    }
  })

  return variables.size
}

// 高亮显示模板中的变量
const highlightVariables = (content: string): string => {
  if (!content) return ''

  let highlighted = content

  // 转义HTML特殊字符
  highlighted = highlighted
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')

  // 高亮 {变量名} 格式
  highlighted = highlighted.replace(/\{(\w+)\}/g, '<span class="variable-highlight">{$1}</span>')

  // 高亮 {{变量名}} 格式
  highlighted = highlighted.replace(/\{\{(\w+)\}\}/g, '<span class="variable-highlight">{{$1}}</span>')

  // 高亮 $变量名 格式
  highlighted = highlighted.replace(/\$(\w+)/g, '<span class="variable-highlight">$$$1</span>')

  // 保持换行
  highlighted = highlighted.replace(/\n/g, '<br>')

  return highlighted
}

// 定义常用变量
const projectVariables = ref([
  { name: '{projectName}', description: '项目名称' },
  { name: '{projectDescription}', description: '项目描述' },
  { name: '{projectContext}', description: '完整项目上下文信息' },
  { name: '{projectStatus}', description: '项目状态' },
  { name: '{projectPriority}', description: '项目优先级' },
  { name: '{projectOwner}', description: '项目负责人' },
  { name: '{projectTeam}', description: '团队成员' },
  { name: '{techStack}', description: '技术栈' }
])

const userVariables = ref([
  { name: '{userMessage}', description: '用户输入的消息' },
  { name: '{userName}', description: '用户名称' },
  { name: '{input}', description: '用户输入' },
  { name: '{context}', description: '上下文信息' },
  { name: '{requirements}', description: '需求描述' },
  { name: '{specifications}', description: '规格说明' }
])

const timeVariables = ref([
  { name: '{currentDate}', description: '当前日期' },
  { name: '{currentTime}', description: '当前时间' },
  { name: '{currentDateTime}', description: '当前日期时间' },
  { name: '{taskType}', description: '任务类型' },
  { name: '{conversationId}', description: '对话ID' }
])

// 复制变量到剪贴板
const copyVariableToClipboard = async (variableName: string) => {
  try {
    await navigator.clipboard.writeText(variableName)
    ElMessage.success(`已复制变量：${variableName}`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 打开模板预览弹窗
const openTemplatePreviewDialog = () => {
  if (selectedTemplateInfo.value?.content) {
    refreshPreview()
    showTemplatePreviewDialog.value = true
    activePreviewTab.value = 'original'
  } else {
    ElMessage.warning('请先选择一个模板')
  }
}

// 复制格式化内容
const copyFormattedContent = async () => {
  if (formattedPreview.value) {
    try {
      await navigator.clipboard.writeText(formattedPreview.value)
      ElMessage.success('格式化内容已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请手动复制')
    }
  } else {
    ElMessage.warning('没有可复制的格式化内容')
  }
}

// 使用模板
const useTemplate = () => {
  if (selectedTemplateInfo.value) {
    // 将格式化后的内容填入消息输入框
    if (formattedPreview.value) {
      currentMessage.value = formattedPreview.value
      showTemplatePreviewDialog.value = false
      ElMessage.success('模板内容已填入消息框')

      // 聚焦到消息输入框
      nextTick(() => {
        const messageInput = document.querySelector('.message-input textarea') as HTMLTextAreaElement
        if (messageInput) {
          messageInput.focus()
        }
      })
    } else {
      ElMessage.warning('请先刷新预览以获取格式化内容')
    }
  }
}

// AI功能变化处理
const onAIFeatureChange = (feature: string) => {
  if (feature) {
    ElMessage.info(`已选择AI功能：${getAIFeatureTitle(feature)}`)
  }
}

// 获取AI功能标题
const getAIFeatureTitle = (feature: string): string => {
  const featureMap: Record<string, string> = {
    'chain-project-analysis': '项目全面分析',
    'chain-tech-evaluation': '技术方案评估',
    'chain-risk-assessment': '风险评估分析',
    'chain-requirement-analysis': '需求深度分析',
    'agent-project-manager': '项目管理专家',
    'agent-architect': '系统架构师',
    'agent-developer': '开发工程师',
    'agent-tester': '测试工程师',
    'agent-business-analyst': '业务分析师',
    'collaboration-consensus': '专家团队协作',
    'collaboration-debate': '专家观点辩论',
    'collaboration-sequential': '顺序深度分析',
    'search-semantic': '文档智能搜索',
    'search-rag': '知识增强问答',
    'knowledge-similar-projects': '相似项目推荐',
    'knowledge-expert-recommend': '专家技能推荐'
  }
  return featureMap[feature] || feature
}

// 获取AI功能描述
const getAIFeatureDescription = (feature: string): string => {
  const descriptionMap: Record<string, string> = {
    'chain-project-analysis': '通过多步骤推理链对项目进行全面分析，包括需求分析、技术选型、架构设计和风险评估',
    'chain-tech-evaluation': '深入评估技术方案的可行性，包括技术调研和优劣分析',
    'chain-risk-assessment': '系统性识别和评估项目风险，制定应对策略',
    'chain-requirement-analysis': '深度分析用户需求，从收集到规格化的完整流程',
    'agent-project-manager': '专业项目管理专家，擅长项目规划、进度管理和风险控制',
    'agent-architect': '资深系统架构师，专注于架构设计、技术选型和性能优化',
    'agent-developer': '经验丰富的开发工程师，提供技术实现和问题解决方案',
    'agent-tester': '专业测试工程师，负责质量保证和测试策略制定',
    'agent-business-analyst': '业务分析专家，专长需求分析和业务流程设计',
    'collaboration-consensus': '多个专业Agent协作分析，寻求专家共识',
    'collaboration-debate': '不同专家Agent进行观点辩论，提供多角度分析',
    'collaboration-sequential': '专家Agent按顺序深度分析，逐步深入问题',
    'search-semantic': '基于语义理解的智能文档搜索',
    'search-rag': '检索增强生成，结合相关文档提供准确回答',
    'knowledge-similar-projects': '基于知识图谱推荐相似项目和经验',
    'knowledge-expert-recommend': '根据技能需求推荐合适的专家'
  }
  return descriptionMap[feature] || '高级AI功能'
}

// 获取AI功能使用提示
const getAIFeatureTips = (feature: string): string => {
  const tipsMap: Record<string, string> = {
    'chain-project-analysis': '适合项目启动阶段的全面分析，需要提供详细的项目背景信息',
    'chain-tech-evaluation': '适合技术选型阶段，请描述具体的技术需求和约束条件',
    'chain-risk-assessment': '适合项目规划阶段，请提供项目的基本信息和关注点',
    'chain-requirement-analysis': '适合需求梳理阶段，请提供原始需求描述',
    'agent-project-manager': '请描述项目管理相关的问题或需要制定的计划',
    'agent-architect': '请描述技术架构相关的需求或问题',
    'agent-developer': '请描述具体的技术问题或开发需求',
    'agent-tester': '请描述测试相关的问题或质量保证需求',
    'agent-business-analyst': '请描述业务需求或流程相关的问题',
    'collaboration-consensus': '适合复杂问题的多角度分析，会调用多个专家Agent',
    'collaboration-debate': '适合有争议的问题，会展示不同专家的观点',
    'collaboration-sequential': '适合需要深度分析的复杂问题',
    'search-semantic': '输入关键词或问题，系统会智能搜索相关文档',
    'search-rag': '提出问题，系统会检索相关文档并生成准确回答',
    'knowledge-similar-projects': '会根据当前项目推荐相似的项目经验',
    'knowledge-expert-recommend': '描述需要的技能，系统会推荐合适的专家'
  }
  return tipsMap[feature] || '请根据功能特点合理使用'
}

// 获取任务类型颜色
const getTaskTypeColor = (taskType: string) => {
  const colorMap: Record<string, any> = {
    'General': 'primary',
    'ProjectManagement': 'success',
    'RequirementAnalysis': 'warning',
    'DesignGeneration': 'danger',
    'CodeGeneration': 'info'
  }
  return colorMap[taskType] || ''
}

// 清空对话
const clearConversation = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '确认操作', {
      type: 'warning'
    })

    // 初始化新的对话会话
    initializeConversation()
    ElMessage.success('对话已清空')
  } catch {
    // 用户取消
  }
}

// 导出对话
const exportConversation = () => {
  if (messages.value.length === 0) {
    ElMessage.warning('暂无对话内容可导出')
    return
  }

  const content = messages.value.map(msg => {
    const sender = msg.type === 'user' ? '用户' : 'AI助手'
    const time = formatTime(msg.timestamp)
    return `[${time}] ${sender}: ${msg.content}`
  }).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `AI对话记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('对话记录已导出')
}

// 加载对话历史
const loadConversationHistory = async (conversationId: string) => {
  if (!conversationId) return

  try {
    isLoadingHistory.value = true

    // 构建API URL，包含向量上下文参数
    const url = new URL(`/api/ai/conversation/${conversationId}/history`, window.location.origin)
    if (enableVectorContext.value) {
      url.searchParams.set('includeVectorContext', 'true')
    }

    // 调用后端API获取对话历史
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    })

    if (!response.ok) {
      throw new Error(`获取对话历史失败: ${response.status}`)
    }

    const historyResponse = await response.json()

    // 将历史对话转换为消息格式
    const historyMessages: Message[] = []

    if (historyResponse?.messages && Array.isArray(historyResponse.messages)) {
      historyResponse.messages.forEach((item: any) => {
        // 添加用户消息
        if (item.userMessage) {
          historyMessages.push({
            type: 'user',
            content: item.userMessage,
            timestamp: new Date(item.timestamp)
          })
        }

        // 添加AI回复
        if (item.aiResponse) {
          historyMessages.push({
            type: 'ai',
            content: item.aiResponse,
            timestamp: new Date(item.timestamp),
            actions: parseMessageActions(item.aiResponse, currentMode.value)
          })
        }
      })
    }

    // 如果有向量上下文，显示相关信息
    if (historyResponse?.vectorContext && historyResponse.vectorContext.length > 0) {
      console.log(`加载了 ${historyResponse.vectorContext.length} 个向量上下文项`)

      // 可以选择在UI中显示向量上下文信息
      ElMessage.success(`已加载对话历史，包含 ${historyResponse.vectorContext.length} 个相关文档上下文`)
    }

    // 更新消息列表
    messages.value = historyMessages

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    console.log(`加载了 ${historyMessages.length} 条历史消息`)
  } catch (error: any) {
    console.error('加载对话历史失败:', error)
    ElMessage.warning('加载对话历史失败，将开始新的对话')
  } finally {
    isLoadingHistory.value = false
  }
}

// 加载对话
const loadConversation = async (conversation: ConversationHistory) => {
  try {
    // 设置对话ID和模式
    currentConversationId.value = conversation.id
    currentMode.value = conversation.mode || 'general'

    // 加载历史消息
    await loadConversationHistory(conversation.id)

    ElMessage.success(`已加载对话: ${conversation.title}`)
  } catch (error: any) {
    console.error('加载对话失败:', error)
    ElMessage.error('加载对话失败')

    // 如果加载失败，初始化新对话
    initializeConversation()
  }
}

// 格式化时间
const formatTime = (time: Date) => {
  return dayjs(time).format('HH:mm:ss')
}

// 格式化消息内容
const formatMessage = (content: string) => {
  // 简单的markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 加载数据
const loadProjects = async () => {
  try {
    const response = await ProjectService.getProjects({ pageNumber: 1, pageSize: 100 })
    projects.value = (response.items || []).map(item => ({
      id: item.id,
      name: item.name,
      description: item.description || '',
      status: item.status || '',
      priority: item.priority || ''
    }))
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

const loadAIProviders = async () => {
  try {
    const response = await AIProviderService.getModelConfigurations()
    aiProviders.value = response || []

    // 默认选择第一个可用的AI提供商
    if (aiProviders.value.length > 0) {
      selectedAIProvider.value = aiProviders.value[0].id
    }
  } catch (error) {
    console.error('加载AI提供商失败:', error)
  }
}

// 加载Prompt分类
const loadPromptCategories = async () => {
  try {
    const categories = await promptService.getCategories()

    // 按排序顺序和模板数量排序
    categories.sort((a, b) => {
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder
      }
      return (b.templateCount || 0) - (a.templateCount || 0)
    })

    promptCategories.value = categories

    // 默认选择第一个分类
    if (promptCategories.value.length > 0) {
      selectedPromptCategory.value = promptCategories.value[0].id
      await loadPromptTemplatesByCategory(promptCategories.value[0].id)
    }
  } catch (error) {
    console.error('加载Prompt分类失败:', error)
    ElMessage.warning('加载Prompt分类失败')
  }
}

// 根据分类加载Prompt模板
const loadPromptTemplatesByCategory = async (categoryId: number) => {
  try {
    const templates = await promptService.getTemplatesByCategory(categoryId)

    // 按使用次数和是否默认排序
    templates.sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1
      if (!a.isDefault && b.isDefault) return 1
      return (b.usageCount || 0) - (a.usageCount || 0)
    })

    promptTemplates.value = templates

    // 默认选择第一个模板
    if (promptTemplates.value.length > 0) {
      selectedPromptTemplate.value = promptTemplates.value[0].id
    } else {
      selectedPromptTemplate.value = undefined
    }
  } catch (error) {
    console.error('加载Prompt模板失败:', error)
    ElMessage.warning('加载Prompt模板失败')
    promptTemplates.value = []
    selectedPromptTemplate.value = undefined
  }
}

const loadPromptTemplates = async () => {
  try {
    // 根据当前模式加载对应的Prompt模板
    const taskType = getTaskTypeFromMode(currentMode.value)

    // 首先尝试按任务类型获取模板
    let templates = await promptService.getTemplatesByTaskType(taskType)

    // 如果没有找到对应任务类型的模板，获取所有模板并过滤
    if (!templates || templates.length === 0) {
      const allTemplates = await promptService.getTemplates()
      templates = allTemplates.filter(t =>
        t.taskType === taskType ||
        t.taskType === 'General' ||
        t.categoryName?.includes('AI对话')
      )
    }

    // 按使用次数和是否默认排序
    templates.sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1
      if (!a.isDefault && b.isDefault) return 1
      return (b.usageCount || 0) - (a.usageCount || 0)
    })

    promptTemplates.value = templates

    // 默认选择第一个模板
    if (promptTemplates.value.length > 0) {
      selectedPromptTemplate.value = promptTemplates.value[0].id
    }
  } catch (error) {
    console.error('加载Prompt模板失败:', error)
    ElMessage.warning('加载Prompt模板失败，将使用默认模板')

    // 如果加载失败，创建一个简单的默认模板
    promptTemplates.value = [{
      id: 0,
      name: getDefaultTemplateName(currentMode.value),
      description: getDefaultTemplateDescription(currentMode.value),
      content: getDefaultTemplateContent(currentMode.value),
      taskType: getTaskTypeFromMode(currentMode.value),
      categoryId: 1,
      categoryName: 'AI对话',
      templateType: 'System',
      templateVersion: '1.0',
      isDefault: true,
      isEnabled: true,
      usageCount: 0,
      createdTime: new Date().toISOString(),
      tags: '默认模板'
    }]

    if (promptTemplates.value.length > 0) {
      selectedPromptTemplate.value = promptTemplates.value[0].id
    }
  }
}

// 获取默认模板信息的辅助方法
const getDefaultTemplateName = (mode: string): string => {
  const names: Record<string, string> = {
    general: '通用AI助手',
    project: '项目管理专家',
    requirement: '需求分析师',
    design: '系统设计专家',
    development: '开发导师'
  }
  return names[mode] || '通用AI助手'
}

const getDefaultTemplateDescription = (mode: string): string => {
  const descriptions: Record<string, string> = {
    general: '通用的AI助手对话模板，适用于各种技术咨询',
    project: '专门用于项目管理的AI助手模板',
    requirement: '专门用于需求分析的AI助手模板',
    design: '专门用于系统设计的AI助手模板',
    development: '专门用于代码开发的AI助手模板'
  }
  return descriptions[mode] || '通用的AI助手对话模板'
}

const getDefaultTemplateContent = (mode: string): string => {
  const contents: Record<string, string> = {
    general: '你是一个专业的AI技术助手，请回答用户的问题：{userMessage}',
    project: '你是一个资深的软件项目管理专家，请提供项目管理建议：{userMessage}',
    requirement: '你是一个专业的需求分析师，请帮助分析和整理需求：{userMessage}',
    design: '你是一个资深的系统架构师，请提供设计建议：{userMessage}',
    development: '你是一个经验丰富的开发导师，请提供开发指导：{userMessage}'
  }
  return contents[mode] || contents.general
}

// 监听项目和AI提供商变化，自动刷新预览
watch([selectedProjectId, selectedAIProvider], () => {
  if (selectedTemplateInfo.value?.content) {
    nextTick(() => {
      refreshPreview()
    })
  }
})

// 监听模式变化，重新加载模板
watch(currentMode, () => {
  if (selectedPromptCategory.value) {
    loadPromptTemplatesByCategory(selectedPromptCategory.value)
  }
})

// 生命周期
onMounted(() => {
  // 检查是否从其他页面返回，如果是则恢复对话状态
  const route = router.currentRoute.value
  const returnFrom = route.query.returnFrom as string
  const conversationId = route.query.conversationId as string
  const mode = route.query.mode as string
  const projectId = route.query.projectId as string

  if (returnFrom && conversationId && mode) {
    // 恢复对话状态
    currentConversationId.value = conversationId
    currentMode.value = mode

    if (projectId) {
      selectedProjectId.value = parseInt(projectId)
    }

    ElMessage.success(`已从${getReturnFromLabel(returnFrom)}返回，对话状态已恢复`)
  } else {
    // 初始化新对话
    initializeConversation()
  }

  // 加载基础数据
  loadProjects()
  loadAIProviders()
  loadPromptCategories() // 改为加载分类，分类加载完成后会自动加载第一个分类的模板
  loadTestConversationHistory() // 加载测试对话历史

  // 添加一些测试消息来验证滚动条（开发时使用）
  // addTestMessages()
})

// 获取返回来源的标签
const getReturnFromLabel = (returnFrom: string): string => {
  const labels: Record<string, string> = {
    'requirements': '需求管理',
    'design': '设计生成',
    'development': '开发管理'
  }
  return labels[returnFrom] || returnFrom
}

// 加载测试对话历史（用于测试滚动条）
const loadTestConversationHistory = () => {
  const testHistory: ConversationHistory[] = []
  const modes = ['general', 'project', 'requirement', 'design', 'development']
  for (let i = 1; i <= 15; i++) {
    testHistory.push({
      id: i.toString(),
      title: `对话 ${i} - ${i % 3 === 0 ? '项目管理讨论' : i % 3 === 1 ? '需求分析会议' : '技术方案设计'}`,
      lastMessageTime: new Date(Date.now() - i * 3600000), // i小时前
      projectId: i % 3 + 1,
      userId: 1,
      mode: modes[i % modes.length]
    } as ConversationHistory)
  }
  conversationHistory.value = testHistory
}

// 添加测试消息的方法（用于测试滚动条）
const addTestMessages = () => {
  for (let i = 1; i <= 20; i++) {
    messages.value.push({
      type: i % 2 === 0 ? 'user' : 'ai',
      content: `这是测试消息 ${i}。这是一条比较长的消息，用来测试滚动条是否能够正常工作。当消息内容超过容器高度时，应该显示滚动条让用户可以查看所有消息内容。`,
      timestamp: new Date(),
      actions: i % 3 === 0 ? [
        { label: '项目分析', icon: 'project-analysis', type: 'primary' },
        { label: '生成ER图', icon: 'generate-er', type: 'success' }
      ] : undefined
    })
  }
}

// 监听模式变化
watch(currentMode, () => {
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
.ai-chat-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.page-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 20px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
  }

  .title-section {
    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      display: flex;
      align-items: center;
      gap: 12px;

      .title-icon {
        color: var(--el-color-primary);
      }
    }

    .subtitle {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .header-actions {
    .el-button-group {
      .el-button {
        padding: 8px 16px;

        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 120px); /* 确保容器有足够高度 */
}

.chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;

  .chat-card {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    min-height: 600px; /* 设置最小高度 */

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0;
      min-height: 0; /* 确保flex子元素可以收缩 */
    }
  }
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .mode-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .mode-icon {
      font-size: 24px;
    }

    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
    }

    .mode-desc {
      margin: 0;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  .chat-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .conversation-info {
      margin-right: auto;
    }

    .vector-context-switch {
      display: flex;
      align-items: center;

      .el-switch {
        --el-switch-on-color: #67c23a;
        --el-switch-off-color: #dcdfe6;
      }
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  min-height: 0; /* 确保flex子元素可以收缩 */
  max-height: calc(100vh - 300px); /* 设置最大高度 */

  &::-webkit-scrollbar {
    width: 8px; /* 增加滚动条宽度使其更明显 */
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 4px;
    border: 1px solid var(--el-bg-color-page);

    &:hover {
      background: var(--el-border-color);
    }
  }

  /* 确保在Firefox中也显示滚动条 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-light) var(--el-bg-color-page);
}

.loading-history {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: var(--el-text-color-regular);
  font-size: 14px;

  .el-icon {
    font-size: 16px;
  }
}

.welcome-section {
  text-align: center;
  padding: 40px 20px;

  .welcome-avatar {
    margin-bottom: 20px;
  }

  .welcome-content {
    h2 {
      margin: 0 0 12px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 0 0 30px 0;
      color: var(--el-text-color-regular);
    }
  }

  .quick-suggestions {
    text-align: left;
    max-width: 600px;
    margin: 0 auto;

    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .suggestion-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .suggestion-tag {
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

.message-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.message {
  display: flex;
  gap: 12px;

  &.user {
    flex-direction: row-reverse;

    .message-content {
      background: var(--el-color-primary);
      color: white;
      border-radius: 18px 18px 4px 18px;
    }
  }

  &.ai {
    .message-content {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);
      border-radius: 18px 18px 18px 4px;
    }
  }

  .message-avatar {
    flex-shrink: 0;
  }

  .message-content {
    max-width: 70%;
    padding: 12px 16px;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      .sender {
        font-size: 12px;
        font-weight: 600;
        opacity: 0.8;
      }

      .timestamp {
        font-size: 11px;
        opacity: 0.6;
      }
    }

    .message-body {
      line-height: 1.5;
      word-wrap: break-word;

      :deep(code) {
        background: rgba(0, 0, 0, 0.1);
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
      }

      :deep(strong) {
        font-weight: 600;
      }

      :deep(em) {
        font-style: italic;
      }
    }

    .message-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
}

.typing-indicator {
  .typing-animation {
    display: flex;
    gap: 4px;
    padding: 12px 16px;

    span {
      width: 6px;
      height: 6px;
      background: var(--el-color-primary);
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-section {
  border-top: 1px solid var(--el-border-color-light);
  padding: 16px 20px;

  .input-container {
    .message-input {
      margin-bottom: 12px;

      :deep(.el-textarea__inner) {
        resize: none;
        border-radius: 8px;
      }
    }

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .input-tips {
        font-size: 12px;
      }
    }
  }
}

.context-section {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100vh - 200px); /* 设置整体高度 */
  overflow-y: auto; /* 整体滚动 */

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 3px;

    &:hover {
      background: var(--el-border-color);
    }
  }

  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-light) var(--el-bg-color-page);

  .el-card {
    flex-shrink: 0; /* 防止卡片被压缩 */

    :deep(.el-card__header) {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        font-size: 14px;
      }
    }

    :deep(.el-card__body) {
      padding: 16px;
    }

    /* 为特定卡片设置最大高度和内部滚动 */
    &.context-card {
      .context-content {
        max-height: 400px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--el-border-color-lighter);
          border-radius: 2px;
        }
      }
    }

    &.tools-card {
      .tools-content {
        max-height: 300px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--el-border-color-lighter);
          border-radius: 2px;
        }
      }
    }

    &.history-card {
      .history-content {
        max-height: 350px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--el-border-color-lighter);
          border-radius: 2px;
        }
      }
    }
  }
}

.context-content {
  .context-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      margin-bottom: 6px;
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-regular);
    }
  }

  .project-info {
    margin-top: 16px;
    padding: 12px;
    background: var(--el-bg-color-page);
    border-radius: 6px;

    h4 {
      margin: 0 0 6px 0;
      font-size: 14px;
      font-weight: 600;
    }

    .project-desc {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }

    .project-meta {
      display: flex;
      gap: 6px;
    }
  }

  .template-info {
    margin-top: 16px;
    padding: 12px;
    background: var(--el-bg-color-page);
    border-radius: 6px;
    border-left: 3px solid var(--el-color-primary);

    h4 {
      margin: 0 0 6px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-color-primary);
    }

    .template-description {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: var(--el-text-color-regular);
      line-height: 1.4;
    }

    .template-meta {
      display: flex;
      gap: 6px;
    }
  }
}

// Prompt分类选择样式
:deep(.category-option) {
  .category-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }

  .category-desc {
    font-size: 12px;
    color: var(--el-text-color-regular);
    line-height: 1.3;
    margin-bottom: 4px;
  }
}

// Prompt模板选择样式
:deep(.template-option) {
  .template-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }

  .template-desc {
    font-size: 12px;
    color: var(--el-text-color-regular);
    line-height: 1.3;
    margin-bottom: 4px;
  }

  .template-tags {
    display: flex;
    gap: 4px;
    margin-top: 4px;
  }
}

// Prompt内容预览样式
.template-preview {
  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    label {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .template-actions {
      display: flex;
      gap: 8px;
    }
  }

  .template-content {
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid var(--el-border-color-light);

    .template-info-tags {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      flex-wrap: wrap;

      .usage-count {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-left: auto;
      }
    }

    .template-description {
      margin-bottom: 16px;
      padding: 12px;
      background: var(--el-bg-color);
      border-radius: 6px;
      border-left: 4px solid var(--el-color-primary);
      font-size: 14px;
      line-height: 1.5;

      strong {
        color: var(--el-text-color-primary);
        margin-right: 8px;
      }
    }

    .template-content-area {
      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        strong {
          color: var(--el-text-color-primary);
        }
      }

      .content-section {
        margin-bottom: 20px;

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          padding: 8px 12px;
          background: var(--el-bg-color);
          border-radius: 6px;
          border-left: 4px solid var(--el-color-primary);

          span {
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .variable-count {
            margin-left: auto;
          }
        }

        .template-content-display {
          border: 1px solid var(--el-border-color);
          border-radius: 6px;
          background: var(--el-bg-color);

          .highlighted-content {
            padding: 12px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: var(--el-text-color-primary);
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 200px;
            overflow-y: auto;

            .variable-highlight {
              background: var(--el-color-warning-light-8);
              color: var(--el-color-warning-dark-2);
              padding: 2px 4px;
              border-radius: 3px;
              font-weight: 600;
              border: 1px solid var(--el-color-warning-light-5);
            }

            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-track {
              background: transparent;
            }

            &::-webkit-scrollbar-thumb {
              background: var(--el-border-color-lighter);
              border-radius: 2px;
            }
          }
        }
      }

      .template-textarea {
        :deep(.el-textarea__inner) {
          background: var(--el-bg-color);
          border: 1px solid var(--el-border-color);
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.6;
          resize: vertical;
        }

        &.formatted-preview {
          :deep(.el-textarea__inner) {
            background: var(--el-color-success-light-9);
            border: 1px solid var(--el-color-success-light-5);
            color: var(--el-color-success-dark-2);
          }
        }
      }
    }

    .template-tags-section {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);

      strong {
        color: var(--el-text-color-primary);
        margin-right: 8px;
      }

      .tag-item {
        display: inline-block;
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 6px;
        margin-bottom: 4px;
      }
    }

    .variables-reference {
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);

      .reference-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        strong {
          color: var(--el-text-color-primary);
        }
      }

      .reference-content {
        .variable-categories {
          .variable-category {
            margin-bottom: 16px;

            h5 {
              margin: 0 0 8px 0;
              font-size: 13px;
              font-weight: 600;
              color: var(--el-text-color-regular);
            }

            .variable-list {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;

              .variable-tag {
                cursor: pointer;
                transition: all 0.2s;
                user-select: none;

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  background: var(--el-color-primary-light-7);
                  border-color: var(--el-color-primary-light-3);
                }

                &:active {
                  transform: translateY(0);
                }
              }
            }
          }
        }

        .reference-tip {
          margin-top: 12px;
          padding: 8px 12px;
          background: var(--el-color-info-light-9);
          border-radius: 6px;
          border-left: 4px solid var(--el-color-info);
        }
      }
    }
  }
}

.tools-content {
  /* 内容区域滚动样式已在上面的.context-section中定义 */
  .tool-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .tool-button {
      justify-content: flex-start;
      width: 100%;

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}

.history-content {
  /* 滚动样式已在上面的.context-section中定义 */
  .empty-history {
    text-align: center;
    padding: 20px;
    color: var(--el-text-color-placeholder);
  }

  .history-list {
    .history-item {
      padding: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 8px;
      border: 1px solid transparent;

      &:hover {
        background: var(--el-bg-color-page);
        border-color: var(--el-border-color-light);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .history-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        color: var(--el-text-color-primary);
        line-height: 1.4;

        /* 文本溢出处理 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .history-meta {
        font-size: 12px;
        color: var(--el-text-color-regular);
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    // 简化的预览触发器样式
    .template-preview-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      label {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }

    .template-summary {
      .summary-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 8px;
      }

      .summary-description {
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
        background: var(--el-bg-color-page);
        padding: 8px 12px;
        border-radius: 6px;
        border-left: 3px solid var(--el-color-primary);
      }
    }

    // AI功能信息样式
    .ai-feature-info {
      background: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-5);
      border-radius: 8px;
      padding: 12px;

      .feature-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: var(--el-color-primary);
        margin-bottom: 8px;

        .el-icon {
          font-size: 16px;
        }
      }

      .feature-description {
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .feature-tips {
        font-size: 12px;

        .el-text {
          background: var(--el-color-info-light-9);
          padding: 4px 8px;
          border-radius: 4px;
          border-left: 3px solid var(--el-color-info);
        }
      }
    }
  }
}

// 弹窗样式
:deep(.template-preview-dialog) {
  .el-dialog__header {
    background: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .dialog-content {
    .template-header-info {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .template-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        h3 {
          margin: 0;
          color: var(--el-text-color-primary);
        }

        .template-meta-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
      }

      .template-description {
        font-size: 14px;
        color: var(--el-text-color-regular);
        background: var(--el-bg-color-page);
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid var(--el-color-info);

        strong {
          color: var(--el-text-color-primary);
        }
      }
    }

    .template-content-area {
      .preview-tabs {
        .tab-content {
          .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            background: var(--el-bg-color-page);
            border-radius: 6px;

            .header-actions {
              display: flex;
              gap: 8px;
              align-items: center;
            }
          }

          .template-content-display {
            border: 1px solid var(--el-border-color);
            border-radius: 8px;
            background: var(--el-bg-color);
            max-height: 400px;
            overflow-y: auto;

            .highlighted-content {
              padding: 16px;
              font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
              font-size: 14px;
              line-height: 1.6;
              color: var(--el-text-color-primary);
              white-space: pre-wrap;
              word-wrap: break-word;

              .variable-highlight {
                background: var(--el-color-warning-light-8);
                color: var(--el-color-warning-dark-2);
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: 600;
                border: 1px solid var(--el-color-warning-light-5);
              }
            }
          }

          .formatted-content-display {
            border: 1px solid var(--el-color-success-light-5);
            border-radius: 8px;
            background: var(--el-color-success-light-9);
            max-height: 400px;
            overflow-y: auto;

            .formatted-text {
              padding: 16px;
              margin: 0;
              font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
              font-size: 14px;
              line-height: 1.6;
              color: var(--el-color-success-dark-2);
              white-space: pre-wrap;
              word-wrap: break-word;
            }
          }

          .variables-reference-content {
            .variable-categories {
              .variable-category {
                margin-bottom: 24px;

                h4 {
                  margin: 0 0 12px 0;
                  color: var(--el-text-color-primary);
                  font-size: 16px;
                  border-bottom: 2px solid var(--el-color-primary);
                  padding-bottom: 8px;
                }

                .variable-grid {
                  display: grid;
                  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                  gap: 12px;

                  .variable-item {
                    padding: 12px;
                    border: 1px solid var(--el-border-color-light);
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s;
                    background: var(--el-bg-color);

                    &:hover {
                      border-color: var(--el-color-primary);
                      background: var(--el-color-primary-light-9);
                      transform: translateY(-2px);
                      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }

                    .variable-name {
                      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                      font-weight: 600;
                      color: var(--el-color-primary);
                      margin-bottom: 4px;
                    }

                    .variable-desc {
                      font-size: 12px;
                      color: var(--el-text-color-regular);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .template-tags-section {
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-lighter);

      strong {
        color: var(--el-text-color-primary);
        margin-right: 8px;
      }

      .tag-item {
        margin-right: 8px;
        margin-bottom: 6px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .context-section {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 16px;
  }

  .context-section {
    width: 100%;
    order: -1;
  }

  .page-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    .header-actions {
      width: 100%;
      overflow-x: auto;

      .el-button-group {
        display: flex;
        white-space: nowrap;

        .el-button {
          flex-shrink: 0;
        }
      }
    }
  }

  .message {
    .message-content {
      max-width: 85%;
    }
  }
}
</style>
