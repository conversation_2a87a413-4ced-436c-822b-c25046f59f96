using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Enums;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 项目仓储接口
/// </summary>
public interface IProjectRepository : IRepository<Project>
{
    /// <summary>
    /// 根据项目编号获取项目
    /// </summary>
    /// <param name="projectCode">项目编号</param>
    /// <returns>项目对象</returns>
    Task<Project?> GetByProjectCodeAsync(string projectCode);

    /// <summary>
    /// 检查项目编号是否存在
    /// </summary>
    /// <param name="projectCode">项目编号</param>
    /// <param name="excludeProjectId">排除的项目ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsProjectCodeExistsAsync(string projectCode, int? excludeProjectId = null);

    /// <summary>
    /// 生成唯一的项目编号（线程安全）
    /// </summary>
    /// <returns>唯一的项目编号</returns>
    Task<string> GenerateUniqueProjectCodeAsync();

    /// <summary>
    /// 根据项目经理获取项目列表
    /// </summary>
    /// <param name="projectManagerId">项目经理ID</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetProjectsByManagerAsync(int projectManagerId);

    /// <summary>
    /// 根据客户获取项目列表
    /// </summary>
    /// <param name="clientId">客户ID</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetProjectsByClientAsync(int clientId);

    /// <summary>
    /// 根据状态获取项目列表
    /// </summary>
    /// <param name="status">项目状态</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetProjectsByStatusAsync(ProjectStatus status);

    /// <summary>
    /// 根据类型获取项目列表
    /// </summary>
    /// <param name="type">项目类型</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetProjectsByTypeAsync(ProjectType type);

    /// <summary>
    /// 根据优先级获取项目列表
    /// </summary>
    /// <param name="priority">优先级</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetProjectsByPriorityAsync(Priority priority);

    /// <summary>
    /// 获取用户参与的项目列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetUserProjectsAsync(int userId);

    /// <summary>
    /// 获取用户项目分页列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="status">状态过滤</param>
    /// <param name="search">搜索关键词</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<Project>> GetUserProjectsAsync(int userId, int pageNumber, int pageSize, string? status = null, string? search = null);

    /// <summary>
    /// 获取用户项目统计信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>项目统计信息</returns>
    Task<ProjectManagement.Core.DTOs.ProjectStatisticsDto> GetUserProjectStatisticsAsync(int userId);

    /// <summary>
    /// 获取即将到期的项目
    /// </summary>
    /// <param name="days">天数</param>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetUpcomingDeadlineProjectsAsync(int days = 7);

    /// <summary>
    /// 获取延期的项目
    /// </summary>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetOverdueProjectsAsync();

    /// <summary>
    /// 更新项目进度
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="progress">进度百分比</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateProgressAsync(int projectId, int progress);

    /// <summary>
    /// 软删除项目下的所有开发步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="deletedBy">删除人ID</param>
    /// <returns>删除的步骤数量</returns>
    Task<int> SoftDeleteProjectDevelopmentStepsAsync(int projectId, int deletedBy);

    /// <summary>
    /// 更新项目状态
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="status">项目状态</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateStatusAsync(int projectId, ProjectStatus status, int? updatedBy = null);

    /// <summary>
    /// 更新项目实际开始时间
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="actualStartDate">实际开始时间</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateActualStartDateAsync(int projectId, DateTime actualStartDate);

    /// <summary>
    /// 更新项目实际结束时间
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="actualEndDate">实际结束时间</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateActualEndDateAsync(int projectId, DateTime actualEndDate);

    /// <summary>
    /// 更新项目实际成本
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="actualCost">实际成本</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateActualCostAsync(int projectId, decimal actualCost);

    /// <summary>
    /// 搜索项目
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="status">项目状态（可选）</param>
    /// <param name="type">项目类型（可选）</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<Project>> SearchProjectsAsync(
        string keyword,
        ProjectStatus? status = null,
        ProjectType? type = null,
        int pageIndex = 1,
        int pageSize = 20);

    /// <summary>
    /// 获取项目统计信息
    /// </summary>
    /// <returns>项目统计</returns>
    Task<ProjectStatistics> GetProjectStatisticsAsync();

    /// <summary>
    /// 获取项目预算统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>预算统计</returns>
    Task<BudgetStatistics> GetBudgetStatisticsAsync(DateTime startDate, DateTime endDate);
}

/// <summary>
/// 项目统计信息
/// </summary>
public class ProjectStatistics
{
    /// <summary>
    /// 总项目数
    /// </summary>
    public int TotalProjects { get; set; }

    /// <summary>
    /// 进行中的项目数
    /// </summary>
    public int InProgressProjects { get; set; }

    /// <summary>
    /// 已完成的项目数
    /// </summary>
    public int CompletedProjects { get; set; }

    /// <summary>
    /// 延期的项目数
    /// </summary>
    public int OverdueProjects { get; set; }

    /// <summary>
    /// 平均完成率
    /// </summary>
    public decimal AverageProgress { get; set; }

    /// <summary>
    /// 按状态分组的项目数
    /// </summary>
    public Dictionary<ProjectStatus, int> ProjectsByStatus { get; set; } = new();

    /// <summary>
    /// 按类型分组的项目数
    /// </summary>
    public Dictionary<ProjectType, int> ProjectsByType { get; set; } = new();
}

/// <summary>
/// 预算统计信息
/// </summary>
public class BudgetStatistics
{
    /// <summary>
    /// 总预算
    /// </summary>
    public decimal TotalBudget { get; set; }

    /// <summary>
    /// 总实际成本
    /// </summary>
    public decimal TotalActualCost { get; set; }

    /// <summary>
    /// 预算利用率
    /// </summary>
    public decimal BudgetUtilization { get; set; }

    /// <summary>
    /// 超预算项目数
    /// </summary>
    public int OverBudgetProjects { get; set; }

    /// <summary>
    /// 平均项目预算
    /// </summary>
    public decimal AverageProjectBudget { get; set; }

    /// <summary>
    /// 平均项目成本
    /// </summary>
    public decimal AverageProjectCost { get; set; }
}
