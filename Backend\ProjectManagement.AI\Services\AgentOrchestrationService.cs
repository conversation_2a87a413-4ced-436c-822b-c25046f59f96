using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.Core.DTOs.Agent;
using ProjectManagement.Core.DTOs.LangChain;
using ProjectManagement.Core.DTOs.VectorSearch;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// Agent编排服务 - 管理多个专业AI Agent的协作
    /// </summary>
    public class AgentOrchestrationService
    {
        private readonly ILogger<AgentOrchestrationService> _logger;
        private readonly IAIService _aiService;
        private readonly LangChainService _langChainService;
        private readonly VectorSearchService _vectorSearchService;
        private readonly Dictionary<string, AgentDefinition> _agents;

        public AgentOrchestrationService(
            ILogger<AgentOrchestrationService> logger,
            IAIService aiService,
            LangChainService langChainService,
            VectorSearchService vectorSearchService)
        {
            _logger = logger;
            _aiService = aiService;
            _langChainService = langChainService;
            _vectorSearchService = vectorSearchService;
            _agents = InitializeAgents();
        }

        /// <summary>
        /// 执行Agent任务
        /// </summary>
        public async Task<AgentExecutionResult> ExecuteAgentTaskAsync(AgentTaskRequest request)
        {
            _logger.LogInformation("执行Agent任务: {TaskType} by {AgentType}", request.TaskType, request.AgentType);

            var result = new AgentExecutionResult
            {
                TaskId = Guid.NewGuid().ToString(),
                AgentType = request.AgentType,
                TaskType = request.TaskType,
                StartTime = DateTime.UtcNow
            };

            try
            {
                var agent = GetAgent(request.AgentType);
                if (agent == null)
                {
                    throw new ArgumentException($"未找到Agent类型: {request.AgentType}");
                }

                // 根据任务类型选择执行策略
                switch (request.TaskType.ToLower())
                {
                    case "analysis":
                        result.Output = await ExecuteAnalysisTask(agent, request);
                        break;
                    case "recommendation":
                        result.Output = await ExecuteRecommendationTask(agent, request);
                        break;
                    case "planning":
                        result.Output = await ExecutePlanningTask(agent, request);
                        break;
                    case "review":
                        result.Output = await ExecuteReviewTask(agent, request);
                        break;
                    case "collaboration":
                        result.Output = await ExecuteCollaborationTask(request);
                        break;
                    default:
                        result.Output = await ExecuteGeneralTask(agent, request);
                        break;
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Agent任务执行失败: {TaskType}", request.TaskType);
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// 多Agent协作
        /// </summary>
        public async Task<CollaborationResult> ExecuteCollaborationAsync(CollaborationRequest request)
        {
            _logger.LogInformation("执行多Agent协作: {CollaborationType}", request.CollaborationType);

            var result = new CollaborationResult
            {
                CollaborationId = Guid.NewGuid().ToString(),
                CollaborationType = request.CollaborationType,
                StartTime = DateTime.UtcNow,
                AgentResults = new List<AgentExecutionResult>()
            };

            try
            {
                switch (request.CollaborationType.ToLower())
                {
                    case "sequential":
                        await ExecuteSequentialCollaboration(request, result);
                        break;
                    case "parallel":
                        await ExecuteParallelCollaboration(request, result);
                        break;
                    case "debate":
                        await ExecuteDebateCollaboration(request, result);
                        break;
                    case "consensus":
                        await ExecuteConsensusCollaboration(request, result);
                        break;
                    default:
                        throw new ArgumentException($"未支持的协作类型: {request.CollaborationType}");
                }

                result.Success = result.AgentResults.All(r => r.Success);
                result.FinalOutput = SynthesizeCollaborationResults(result.AgentResults, request.CollaborationType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "多Agent协作失败: {CollaborationType}", request.CollaborationType);
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// 获取可用的Agent列表
        /// </summary>
        public List<AgentInfo> GetAvailableAgents()
        {
            return _agents.Values.Select(agent => new AgentInfo
            {
                Type = agent.Type,
                Name = agent.Name,
                Description = agent.Description,
                Capabilities = agent.Capabilities,
                Specialties = agent.Specialties
            }).ToList();
        }

        /// <summary>
        /// 推荐最适合的Agent
        /// </summary>
        public AgentRecommendation RecommendAgent(string taskDescription, string context)
        {
            _logger.LogInformation("推荐Agent: {TaskDescription}", taskDescription);

            var scores = new Dictionary<string, float>();

            foreach (var agent in _agents.Values)
            {
                var score = CalculateAgentSuitability(agent, taskDescription, context);
                scores[agent.Type] = score;
            }

            var bestAgent = scores.OrderByDescending(s => s.Value).First();

            return new AgentRecommendation
            {
                RecommendedAgentType = bestAgent.Key,
                ConfidenceScore = bestAgent.Value,
                Reasoning = GenerateRecommendationReasoning(bestAgent.Key, taskDescription),
                AlternativeAgents = scores
                    .Where(s => s.Key != bestAgent.Key)
                    .OrderByDescending(s => s.Value)
                    .Take(2)
                    .ToDictionary(s => s.Key, s => s.Value)
            };
        }

        /// <summary>
        /// 初始化Agent定义
        /// </summary>
        private Dictionary<string, AgentDefinition> InitializeAgents()
        {
            return new Dictionary<string, AgentDefinition>
            {
                ["project-manager"] = new AgentDefinition
                {
                    Type = "project-manager",
                    Name = "项目管理专家",
                    Description = "专业的项目管理AI助手，擅长项目规划、进度管理、风险控制",
                    Capabilities = new List<string> { "项目规划", "进度跟踪", "风险管理", "资源分配", "团队协调" },
                    Specialties = new List<string> { "敏捷开发", "瀑布模型", "项目监控", "质量管理" },
                    SystemPrompt = @"你是一位资深的项目管理专家，具有丰富的软件项目管理经验。
你的专长包括：
- 项目规划和时间管理
- 风险评估和控制策略  
- 团队协作和资源分配
- 项目进度跟踪和质量管理
- 敏捷开发和DevOps实践

请基于项目管理最佳实践，为用户提供专业的建议和解决方案。"
                },

                ["architect"] = new AgentDefinition
                {
                    Type = "architect",
                    Name = "系统架构师",
                    Description = "专业的系统架构设计专家，擅长技术选型、架构设计、性能优化",
                    Capabilities = new List<string> { "架构设计", "技术选型", "性能优化", "扩展性设计", "安全架构" },
                    Specialties = new List<string> { "微服务架构", "分布式系统", "云原生", "高并发设计" },
                    SystemPrompt = @"你是一位资深的系统架构师，在大型软件系统设计方面有丰富经验。
你的专长包括：
- 系统架构设计和技术选型
- 微服务和分布式系统架构
- 高并发高可用系统设计
- 云原生架构和容器化
- 系统性能优化和扩展性设计

请基于架构设计最佳实践，提供专业的技术架构建议。"
                },

                ["developer"] = new AgentDefinition
                {
                    Type = "developer",
                    Name = "开发工程师",
                    Description = "专业的软件开发专家，擅长编码实现、技术问题解决、代码优化",
                    Capabilities = new List<string> { "代码开发", "技术实现", "问题调试", "代码优化", "技术选型" },
                    Specialties = new List<string> { "前端开发", "后端开发", "全栈开发", "移动开发" },
                    SystemPrompt = @"你是一位经验丰富的软件开发工程师，精通多种编程语言和技术栈。
你的专长包括：
- 前端和后端开发技术
- 代码质量和性能优化
- 问题诊断和调试技巧
- 开发工具和最佳实践
- 新技术学习和应用

请基于软件开发最佳实践，提供专业的技术实现建议。"
                },

                ["tester"] = new AgentDefinition
                {
                    Type = "tester",
                    Name = "测试工程师",
                    Description = "专业的软件测试专家，擅长测试策略、质量保证、自动化测试",
                    Capabilities = new List<string> { "测试策略", "质量保证", "自动化测试", "性能测试", "安全测试" },
                    Specialties = new List<string> { "功能测试", "接口测试", "UI测试", "压力测试" },
                    SystemPrompt = @"你是一位专业的软件测试工程师，在软件质量保证方面有丰富经验。
你的专长包括：
- 测试策略制定和测试计划
- 功能测试和非功能测试
- 自动化测试框架和工具
- 性能测试和安全测试
- 缺陷管理和质量度量

请基于软件测试最佳实践，提供专业的质量保证建议。"
                },

                ["business-analyst"] = new AgentDefinition
                {
                    Type = "business-analyst",
                    Name = "业务分析师",
                    Description = "专业的业务分析专家，擅长需求分析、业务流程设计、用户体验优化",
                    Capabilities = new List<string> { "需求分析", "业务建模", "流程设计", "用户研究", "产品规划" },
                    Specialties = new List<string> { "需求工程", "业务流程", "用户体验", "产品设计" },
                    SystemPrompt = @"你是一位专业的业务分析师，在需求分析和业务流程设计方面有丰富经验。
你的专长包括：
- 业务需求分析和建模
- 用户故事和用例设计
- 业务流程优化和设计
- 用户体验研究和改进
- 产品功能规划和优先级

请基于业务分析最佳实践，提供专业的需求分析建议。"
                }
            };
        }

        /// <summary>
        /// 获取Agent定义
        /// </summary>
        private AgentDefinition? GetAgent(string agentType)
        {
            return _agents.TryGetValue(agentType, out var agent) ? agent : null;
        }

        /// <summary>
        /// 执行分析任务
        /// </summary>
        private async Task<string> ExecuteAnalysisTask(AgentDefinition agent, AgentTaskRequest request)
        {
            var prompt = $@"{agent.SystemPrompt}

分析任务：
{request.TaskDescription}

项目上下文：
{request.Context}

用户问题：
{request.UserMessage}

请提供详细的分析报告，包括：
1. 问题识别和分析
2. 影响因素评估
3. 解决方案建议
4. 风险和注意事项";

            return await _aiService.GenerateTextAsync(prompt, request.AIConfig);
        }

        /// <summary>
        /// 执行推荐任务
        /// </summary>
        private async Task<string> ExecuteRecommendationTask(AgentDefinition agent, AgentTaskRequest request)
        {
            // 使用RAG增强推荐
            var ragResponse = await _vectorSearchService.GenerateWithRAGAsync(request.UserMessage, new RAGOptions
            {
                RetrievalTopK = 5,
                SimilarityThreshold = 0.7f,
                PromptTemplate = $@"{agent.SystemPrompt}

基于以下相关文档和项目经验：
{{context}}

用户需求：
{{query}}

请提供专业的推荐方案，包括：
1. 推荐的解决方案
2. 方案的优势和特点
3. 实施建议和步骤
4. 潜在风险和应对措施"
            });

            return ragResponse.GeneratedText;
        }

        /// <summary>
        /// 执行规划任务
        /// </summary>
        private async Task<string> ExecutePlanningTask(AgentDefinition agent, AgentTaskRequest request)
        {
            // 使用推理链进行规划
            var chainContext = new ChainContext();
            chainContext.AddVariable("projectContext", request.Context);
            chainContext.AddVariable("userMessage", request.UserMessage);
            chainContext.AddVariable("agentType", agent.Type);

            var planningChain = CreatePlanningChain(agent);
            var chainResult = await _langChainService.ExecuteChainAsync(planningChain, chainContext, request.AIConfig);

            return chainResult.FinalOutput;
        }

        /// <summary>
        /// 执行评审任务
        /// </summary>
        private async Task<string> ExecuteReviewTask(AgentDefinition agent, AgentTaskRequest request)
        {
            var prompt = $@"{agent.SystemPrompt}

评审任务：
{request.TaskDescription}

评审内容：
{request.UserMessage}

项目上下文：
{request.Context}

请从{agent.Name}的专业角度进行评审，包括：
1. 内容质量评估
2. 专业性和准确性检查
3. 改进建议和优化方案
4. 风险识别和预防措施";

            return await _aiService.GenerateTextAsync(prompt, request.AIConfig);
        }

        /// <summary>
        /// 执行协作任务
        /// </summary>
        private async Task<string> ExecuteCollaborationTask(AgentTaskRequest request)
        {
            var collaborationRequest = new CollaborationRequest
            {
                CollaborationType = "consensus",
                TaskDescription = request.TaskDescription,
                Context = request.Context,
                UserMessage = request.UserMessage,
                ParticipantAgents = new List<string> { "project-manager", "architect", "developer" },
                AIConfig = request.AIConfig
            };

            var result = await ExecuteCollaborationAsync(collaborationRequest);
            return result.FinalOutput;
        }

        /// <summary>
        /// 执行通用任务
        /// </summary>
        private async Task<string> ExecuteGeneralTask(AgentDefinition agent, AgentTaskRequest request)
        {
            var prompt = $@"{agent.SystemPrompt}

任务描述：
{request.TaskDescription}

项目上下文：
{request.Context}

用户问题：
{request.UserMessage}

请基于你的专业知识提供帮助和建议。";

            return await _aiService.GenerateTextAsync(prompt, request.AIConfig);
        }

        /// <summary>
        /// 执行顺序协作
        /// </summary>
        private async Task ExecuteSequentialCollaboration(CollaborationRequest request, CollaborationResult result)
        {
            var context = request.Context;
            
            foreach (var agentType in request.ParticipantAgents)
            {
                var agentTask = new AgentTaskRequest
                {
                    AgentType = agentType,
                    TaskType = "analysis",
                    TaskDescription = request.TaskDescription,
                    Context = context,
                    UserMessage = request.UserMessage,
                    AIConfig = request.AIConfig
                };

                var agentResult = await ExecuteAgentTaskAsync(agentTask);
                result.AgentResults.Add(agentResult);

                // 将前一个Agent的结果添加到上下文中
                context += $"\n\n--- {GetAgent(agentType)?.Name}的分析结果 ---\n{agentResult.Output}";
            }
        }

        /// <summary>
        /// 执行并行协作
        /// </summary>
        private async Task ExecuteParallelCollaboration(CollaborationRequest request, CollaborationResult result)
        {
            var tasks = request.ParticipantAgents.Select(async agentType =>
            {
                var agentTask = new AgentTaskRequest
                {
                    AgentType = agentType,
                    TaskType = "analysis",
                    TaskDescription = request.TaskDescription,
                    Context = request.Context,
                    UserMessage = request.UserMessage,
                    AIConfig = request.AIConfig
                };

                return await ExecuteAgentTaskAsync(agentTask);
            });

            var agentResults = await Task.WhenAll(tasks);
            result.AgentResults.AddRange(agentResults);
        }

        /// <summary>
        /// 执行辩论协作
        /// </summary>
        private async Task ExecuteDebateCollaboration(CollaborationRequest request, CollaborationResult result)
        {
            // 实现Agent间的辩论机制
            await ExecuteParallelCollaboration(request, result);
        }

        /// <summary>
        /// 执行共识协作
        /// </summary>
        private async Task ExecuteConsensusCollaboration(CollaborationRequest request, CollaborationResult result)
        {
            // 先并行执行，然后寻求共识
            await ExecuteParallelCollaboration(request, result);
        }

        /// <summary>
        /// 综合协作结果
        /// </summary>
        private string SynthesizeCollaborationResults(List<AgentExecutionResult> agentResults, string collaborationType)
        {
            var synthesis = "## 多专家协作分析结果\n\n";

            foreach (var result in agentResults)
            {
                var agent = GetAgent(result.AgentType);
                synthesis += $"### {agent?.Name}的观点\n{result.Output}\n\n";
            }

            synthesis += "## 综合建议\n基于以上多位专家的分析，建议采取综合性的解决方案，充分考虑各个角度的专业意见。";

            return synthesis;
        }

        /// <summary>
        /// 计算Agent适用性分数
        /// </summary>
        private float CalculateAgentSuitability(AgentDefinition agent, string taskDescription, string context)
        {
            var score = 0f;
            var taskLower = taskDescription.ToLower();

            // 基于能力匹配
            foreach (var capability in agent.Capabilities)
            {
                if (taskLower.Contains(capability.ToLower()))
                {
                    score += 0.3f;
                }
            }

            // 基于专长匹配
            foreach (var specialty in agent.Specialties)
            {
                if (taskLower.Contains(specialty.ToLower()))
                {
                    score += 0.2f;
                }
            }

            return Math.Min(score, 1.0f);
        }

        /// <summary>
        /// 生成推荐理由
        /// </summary>
        private string GenerateRecommendationReasoning(string agentType, string taskDescription)
        {
            var agent = GetAgent(agentType);
            return $"推荐{agent?.Name}是因为该任务与其专长领域高度匹配，特别是在{string.Join("、", agent?.Capabilities ?? new List<string>())}方面。";
        }

        /// <summary>
        /// 创建规划推理链
        /// </summary>
        private ChainDefinition CreatePlanningChain(AgentDefinition agent)
        {
            return new ChainDefinition
            {
                Name = $"{agent.Name}规划链",
                Description = $"基于{agent.Name}的专业视角进行规划",
                Steps = new List<ChainStep>
                {
                    new ChainStep
                    {
                        Name = "现状分析",
                        Type = "analysis",
                        PromptTemplate = $@"{agent.SystemPrompt}

请分析当前项目状况：
项目上下文：{{projectContext}}
用户需求：{{userMessage}}

从{agent.Name}的角度分析：
1. 当前状况评估
2. 主要挑战识别
3. 关键成功因素
4. 资源需求分析"
                    },
                    new ChainStep
                    {
                        Name = "方案设计",
                        Type = "design",
                        PromptTemplate = $@"基于现状分析：
{{step_现状分析}}

请设计解决方案：
1. 总体策略和方法
2. 具体实施步骤
3. 时间安排和里程碑
4. 资源配置建议"
                    },
                    new ChainStep
                    {
                        Name = "风险评估",
                        Type = "assessment",
                        PromptTemplate = $@"基于方案设计：
{{step_方案设计}}

请评估风险和制定应对措施：
1. 潜在风险识别
2. 风险影响评估
3. 预防和应对策略
4. 监控和调整机制"
                    }
                }
            };
        }
    }
}
