using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 自定义模板DTO
    /// </summary>
    public class CustomTemplateDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 模板分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 匹配置信度
        /// </summary>
        public decimal Confidence { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 区域图片文件路径
        /// </summary>
        public string? RegionFilePath { get; set; }

        /// <summary>
        /// 区域图片描述
        /// </summary>
        public string? RegionDescription { get; set; }

        /// <summary>
        /// 区域图片匹配置信度
        /// </summary>
        public decimal? RegionConfidence { get; set; }

        /// <summary>
        /// 区域边界扩展像素
        /// </summary>
        public int? RegionExpand { get; set; }

        /// <summary>
        /// 是否启用区域匹配
        /// </summary>
        public bool UseRegionMatching { get; set; }
    }

    /// <summary>
    /// 创建自定义模板DTO
    /// </summary>
    public class CreateCustomTemplateDto
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [Required(ErrorMessage = "模板名称不能为空")]
        [StringLength(100, ErrorMessage = "模板名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [StringLength(500, ErrorMessage = "模板描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 模板分类
        /// </summary>
        [Required(ErrorMessage = "模板分类不能为空")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 模板文件路径
        /// </summary>
        [Required(ErrorMessage = "模板文件路径不能为空")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 匹配置信度
        /// </summary>
        [Range(0.1, 1.0, ErrorMessage = "置信度必须在0.1到1.0之间")]
        public decimal Confidence { get; set; } = 0.7m;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 区域图片文件路径（可选）
        /// </summary>
        public string? RegionFilePath { get; set; }

        /// <summary>
        /// 区域图片描述
        /// </summary>
        [StringLength(200, ErrorMessage = "区域图片描述长度不能超过200个字符")]
        public string? RegionDescription { get; set; }

        /// <summary>
        /// 区域图片匹配置信度
        /// </summary>
        [Range(0.1, 1.0, ErrorMessage = "区域置信度必须在0.1到1.0之间")]
        public decimal? RegionConfidence { get; set; } = 0.7m;

        /// <summary>
        /// 区域边界扩展像素
        /// </summary>
        [Range(0, 200, ErrorMessage = "区域扩展像素必须在0到200之间")]
        public int? RegionExpand { get; set; } = 10;

        /// <summary>
        /// 是否启用区域匹配
        /// </summary>
        public bool UseRegionMatching { get; set; } = false;
    }

    /// <summary>
    /// 更新自定义模板DTO
    /// </summary>
    public class UpdateCustomTemplateDto
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [StringLength(100, ErrorMessage = "模板名称长度不能超过100个字符")]
        public string? Name { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        [StringLength(500, ErrorMessage = "模板描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 模板分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 模板文件路径
        /// </summary>
        public string? FilePath { get; set; }

        /// <summary>
        /// 匹配置信度
        /// </summary>
        [Range(0.1, 1.0, ErrorMessage = "置信度必须在0.1到1.0之间")]
        public decimal? Confidence { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 区域图片文件路径（可选）
        /// </summary>
        public string? RegionFilePath { get; set; }

        /// <summary>
        /// 区域图片描述
        /// </summary>
        [StringLength(200, ErrorMessage = "区域图片描述长度不能超过200个字符")]
        public string? RegionDescription { get; set; }

        /// <summary>
        /// 区域图片匹配置信度
        /// </summary>
        [Range(0.1, 1.0, ErrorMessage = "区域置信度必须在0.1到1.0之间")]
        public decimal? RegionConfidence { get; set; }

        /// <summary>
        /// 区域边界扩展像素
        /// </summary>
        [Range(0, 200, ErrorMessage = "区域扩展像素必须在0到200之间")]
        public int? RegionExpand { get; set; }

        /// <summary>
        /// 是否启用区域匹配
        /// </summary>
        public bool? UseRegionMatching { get; set; }
    }

    /// <summary>
    /// 模板查询DTO
    /// </summary>
    public class CustomTemplateQueryDto : PageQueryDto
    {
        /// <summary>
        /// 关键词搜索
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 分类过滤
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 创建者过滤
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 创建时间范围开始
        /// </summary>
        public DateTime? CreatedTimeStart { get; set; }

        /// <summary>
        /// 创建时间范围结束
        /// </summary>
        public DateTime? CreatedTimeEnd { get; set; }
    }

    /// <summary>
    /// 模板测试结果DTO
    /// </summary>
    public class TemplateTestResultDto
    {
        /// <summary>
        /// 是否测试成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 找到的位置信息
        /// </summary>
        public object? Location { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? Error { get; set; }

        /// <summary>
        /// 测试耗时（毫秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 截图路径
        /// </summary>
        public string? ScreenshotPath { get; set; }
    }

    /// <summary>
    /// 模板统计信息DTO
    /// </summary>
    public class TemplateStatisticsDto
    {
        /// <summary>
        /// 总模板数
        /// </summary>
        public int TotalTemplates { get; set; }

        /// <summary>
        /// 总序列数
        /// </summary>
        public int TotalSequences { get; set; }

        /// <summary>
        /// 总执行次数
        /// </summary>
        public int TotalExecutions { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 分类统计
        /// </summary>
        public Dictionary<string, int> CategoryStats { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 最近执行记录
        /// </summary>
        public List<ExecutionLogDto> RecentExecutions { get; set; } = new List<ExecutionLogDto>();

        /// <summary>
        /// 最常用模板
        /// </summary>
        public List<CustomTemplateDto> MostUsedTemplates { get; set; } = new List<CustomTemplateDto>();

        /// <summary>
        /// 最常用序列
        /// </summary>
        public List<TemplateSequenceDto> MostUsedSequences { get; set; } = new List<TemplateSequenceDto>();
    }
}
