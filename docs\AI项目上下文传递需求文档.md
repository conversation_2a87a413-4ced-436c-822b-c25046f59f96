# AI项目上下文传递需求文档

## 需求概述

在项目管理AI系统中，当用户创建需求文档并开始AI对话时，AI助手需要了解当前项目的基本信息，以便提供更准确、更有针对性的建议和回复。

## 问题分析

### 当前问题
1. **AI缺少项目上下文**：AI对话时不知道当前是什么项目，无法提供针对性建议
2. **回复通用化**：AI回复过于通用，缺乏项目特定的指导
3. **用户体验不佳**：用户需要重复描述项目背景信息

### 期望效果
1. **智能上下文感知**：AI能够自动获取项目信息并在对话中体现
2. **个性化建议**：基于项目类型、技术栈、预算等提供定制化建议
3. **连贯性对话**：AI能够结合项目背景进行连贯的需求分析

## 解决方案

### 1. 后端实现

#### 1.1 更新DTO结构
在`SendRequirementMessageRequestDto`中添加项目ID字段：

```csharp
public class SendRequirementMessageRequestDto
{
    public string Message { get; set; } = string.Empty;
    public int? ProjectId { get; set; }  // 新增：项目ID
    public string MessageType { get; set; } = "Text";
    public Dictionary<string, object>? Metadata { get; set; }
}
```

#### 1.2 控制器改进
在`RequirementsController`中：

```csharp
[HttpPost("conversations/{conversationId}/messages")]
public async Task<ActionResult<RequirementMessageResponseDto>> SendRequirementMessage(
    string conversationId, 
    [FromBody] SendRequirementMessageRequestDto request)
{
    // 获取项目信息
    Project? projectInfo = null;
    if (request.ProjectId.HasValue)
    {
        projectInfo = await _projectRepository.GetByIdAsync(request.ProjectId.Value);
    }

    // 构建包含项目上下文的提示词
    var prompt = BuildRequirementChatPrompt(request.Message, projectInfo);
    
    // 调用AI服务
    var aiResponse = await _aiService.GenerateTextAsync(prompt, config);
    
    // 返回结果
}
```

#### 1.3 智能提示词构建
增强`BuildRequirementChatPrompt`方法：

```csharp
private string BuildRequirementChatPrompt(string userMessage, Project? projectInfo = null)
{
    var projectContext = "";
    if (projectInfo != null)
    {
        projectContext = $@"
当前项目信息：
- 项目名称：{projectInfo.Name}
- 项目编号：{projectInfo.ProjectCode}
- 项目描述：{projectInfo.Description ?? "暂无描述"}
- 项目状态：{projectInfo.Status}
- 项目优先级：{projectInfo.Priority}
- 技术栈：{projectInfo.TechnologyStack ?? "暂未确定"}
- 预估工时：{projectInfo.EstimatedHours?.ToString() ?? "暂未确定"}小时
- 项目预算：{projectInfo.Budget?.ToString("C") ?? "暂未确定"}

请结合以上项目信息来理解用户需求，并提供针对性的建议。";
    }

    return $@"你是一个专业的软件需求分析师和产品经理。{projectContext}

用户消息：{userMessage}

请根据用户的消息，以友好、专业的方式回复，并：
1. 理解用户的核心需求
2. 结合项目背景提出相关的澄清问题
3. 给出初步的建议或方向
4. 保持对话的连贯性

回复要求：
- 语言简洁明了，避免过于技术化的术语
- 体现专业性但保持易懂
- 引导用户提供更多有用信息
- 回复长度控制在200字以内";
}
```

### 2. 前端实现

#### 2.1 服务层更新
在`RequirementService`中更新`sendMessage`方法：

```typescript
static async sendMessage(
  conversationId: string, 
  message: string, 
  projectId?: number
): Promise<{
  messageId: string
  conversationId: string
  userMessage: string
  aiResponse: string
  timestamp: string
}> {
  return AIApiService.post(`/api/requirements/conversations/${conversationId}/messages`, {
    message,
    projectId
  })
}
```

#### 2.2 组件层改进
在`RequirementChatView.vue`中传递项目ID：

```typescript
const sendMessage = async () => {
  // 调用AI服务，传递项目ID以获取项目上下文
  const response = await RequirementService.sendMessage(
    'default', 
    userMessage, 
    projectId.value
  )
  
  // 处理AI回复
}
```

## 实现效果

### 1. AI回复示例

**无项目上下文时：**
```
感谢您的需求描述。我理解您的想法，请问您能提供更多关于目标用户和主要功能的详细信息吗？
```

**有项目上下文时：**
```
针对您的项目「在线教育平台」，我理解您想要添加用户管理功能。
考虑到项目使用Vue.js + .NET技术栈，建议实现以下功能：
1. 学生/教师角色管理
2. 课程权限控制
3. 学习进度跟踪

请问您希望优先实现哪个用户角色的管理功能？
```

### 2. 技术优势

1. **上下文感知**：AI能够理解项目背景，提供相关建议
2. **个性化回复**：基于项目信息定制回复内容
3. **提高效率**：减少用户重复描述项目信息的时间
4. **专业指导**：结合技术栈和预算提供可行性建议

## 测试验证

### 1. 功能测试
- [ ] 验证项目ID正确传递到后端
- [ ] 验证项目信息正确获取
- [ ] 验证AI提示词包含项目上下文
- [ ] 验证AI回复体现项目相关性

### 2. 边界测试
- [ ] 项目ID为空时的处理
- [ ] 项目不存在时的处理
- [ ] 项目信息不完整时的处理

### 3. 性能测试
- [ ] 项目信息查询性能
- [ ] AI响应时间影响
- [ ] 并发请求处理

## 后续优化

1. **缓存机制**：缓存项目信息减少数据库查询
2. **上下文扩展**：包含需求文档历史、团队成员信息等
3. **智能推荐**：基于项目类型推荐最佳实践
4. **多轮对话**：维护对话历史中的项目上下文

## 总结

通过在AI对话中传递项目上下文信息，系统能够提供更加智能、个性化的需求分析服务，显著提升用户体验和工作效率。这个改进为后续的AI功能增强奠定了重要基础。
