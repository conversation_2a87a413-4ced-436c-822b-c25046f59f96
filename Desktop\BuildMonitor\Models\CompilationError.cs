using System;
using SqlSugar;

namespace BuildMonitor.Models
{
    /// <summary>
    /// 编译错误模型
    /// </summary>
    [SugarTable("CompilationErrors")]
    public class CompilationError
    {
        /// <summary>
        /// 错误ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ProjectId { get; set; }

        /// <summary>
        /// 项目类型 (Backend/Frontend)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string ProjectType { get; set; } = string.Empty;

        /// <summary>
        /// 编译会话ID
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? CompilationSessionId { get; set; }

        /// <summary>
        /// 严重程度 (Error/Warning/Info/Hidden)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Severity { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnDataType = "NVARCHAR(MAX)", IsNullable = false)]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 行号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LineNumber { get; set; }

        /// <summary>
        /// 列号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ColumnNumber { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? ProjectPath { get; set; }

        /// <summary>
        /// 编译器版本
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? CompilerVersion { get; set; }

        /// <summary>
        /// 目标框架
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        public string? TargetFramework { get; set; }

        /// <summary>
        /// 编译配置 (Debug/Release)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? BuildConfiguration { get; set; }

        /// <summary>
        /// 编译时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CompilationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DeletedTime { get; set; }

        /// <summary>
        /// 删除者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DeletedBy { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Version { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Remarks { get; set; }
    }
}
