using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 自定义UI自动化模板控制器
    /// </summary>
    [ApiController]
    [Route("api/custom-templates")]
    [Authorize]
    public class CustomTemplateController : ControllerBase
    {
        private readonly ICustomTemplateRepository _templateRepository;
        private readonly ILogger<CustomTemplateController> _logger;

        public CustomTemplateController(
            ICustomTemplateRepository templateRepository,
            ILogger<CustomTemplateController> logger)
        {
            _templateRepository = templateRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取模板列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResultDto<CustomTemplateDto>>> GetTemplates([FromQuery] CustomTemplateQueryDto query)
        {
            try
            {
                var result = await _templateRepository.GetPagedAsync(query);

                var dtoResult = new PagedResultDto<CustomTemplateDto>
                {
                    Items = result.Items.Select(MapToDto).ToList(),
                    TotalCount = result.TotalCount,
                    PageNumber = result.PageNumber,
                    PageSize = result.PageSize
                };

                return Ok(dtoResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模板列表失败");
                return StatusCode(500, "获取模板列表失败");
            }
        }

        /// <summary>
        /// 获取模板详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<CustomTemplateDto>> GetTemplate(int id)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(id);
                if (template == null || template.IsDeleted)
                {
                    return NotFound("模板不存在");
                }

                return Ok(MapToDto(template));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模板详情失败，ID: {Id}", id);
                return StatusCode(500, "获取模板详情失败");
            }
        }

        /// <summary>
        /// 创建模板
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<CustomTemplateDto>> CreateTemplate([FromBody] CreateCustomTemplateDto dto)
        {
            try
            {
                // 检查名称是否已存在
                if (await _templateRepository.ExistsNameAsync(dto.Name))
                {
                    return BadRequest("模板名称已存在");
                }

                var template = new CustomUIAutoMationTemplate
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    Category = dto.Category,
                    FilePath = dto.FilePath,
                    Confidence = dto.Confidence,
                    TagList = dto.Tags,
                    Notes = dto.Notes,
                    // 区域图片相关字段
                    RegionFilePath = dto.RegionFilePath,
                    RegionDescription = dto.RegionDescription,
                    RegionConfidence = dto.RegionConfidence,
                    RegionExpand = dto.RegionExpand,
                    UseRegionMatching = dto.UseRegionMatching,
                    CreatedBy = GetCurrentUserId(),
                    CreatedTime = DateTime.Now
                };

                var result = await _templateRepository.AddAsync(template);
                template = result;

                _logger.LogInformation("创建模板成功，ID: {Id}, 名称: {Name}", template.Id, dto.Name);
                return CreatedAtAction(nameof(GetTemplate), new { id = template.Id }, MapToDto(template));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建模板失败");
                return StatusCode(500, "创建模板失败");
            }
        }

        /// <summary>
        /// 更新模板
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<CustomTemplateDto>> UpdateTemplate(int id, [FromBody] UpdateCustomTemplateDto dto)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(id);
                if (template == null || template.IsDeleted)
                {
                    return NotFound("模板不存在");
                }

                // 检查名称是否已存在（排除当前模板）
                if (!string.IsNullOrEmpty(dto.Name) && dto.Name != template.Name)
                {
                    if (await _templateRepository.ExistsNameAsync(dto.Name, id))
                    {
                        return BadRequest("模板名称已存在");
                    }
                }

                // 更新字段
                if (!string.IsNullOrEmpty(dto.Name))
                    template.Name = dto.Name;
                if (dto.Description != null)
                    template.Description = dto.Description;
                if (!string.IsNullOrEmpty(dto.Category))
                    template.Category = dto.Category;
                if (!string.IsNullOrEmpty(dto.FilePath))
                    template.FilePath = dto.FilePath;
                if (dto.Confidence.HasValue)
                    template.Confidence = dto.Confidence.Value;
                if (dto.Tags != null)
                    template.TagList = dto.Tags;
                if (dto.Notes != null)
                    template.Notes = dto.Notes;

                // 更新区域图片相关字段
                if (dto.RegionFilePath != null)
                    template.RegionFilePath = dto.RegionFilePath;
                if (dto.RegionDescription != null)
                    template.RegionDescription = dto.RegionDescription;
                if (dto.RegionConfidence.HasValue)
                    template.RegionConfidence = dto.RegionConfidence.Value;
                if (dto.RegionExpand.HasValue)
                    template.RegionExpand = dto.RegionExpand.Value;
                if (dto.UseRegionMatching.HasValue)
                    template.UseRegionMatching = dto.UseRegionMatching.Value;

                template.UpdatedTime = DateTime.Now;

                await _templateRepository.UpdateAsync(template);

                _logger.LogInformation("更新模板成功，ID: {Id}", id);
                return Ok(MapToDto(template));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模板失败，ID: {Id}", id);
                return StatusCode(500, "更新模板失败");
            }
        }

        /// <summary>
        /// 删除模板
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteTemplate(int id)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(id);
                if (template == null || template.IsDeleted)
                {
                    return NotFound("模板不存在");
                }

                template.IsDeleted = true;
                template.UpdatedTime = DateTime.Now;
                await _templateRepository.UpdateAsync(template);

                _logger.LogInformation("删除模板成功，ID: {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除模板失败，ID: {Id}", id);
                return StatusCode(500, "删除模板失败");
            }
        }

        /// <summary>
        /// 测试模板
        /// </summary>
        [HttpPost("{id}/test")]
        public async Task<ActionResult<TemplateTestResultDto>> TestTemplate(int id)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(id);
                if (template == null || template.IsDeleted)
                {
                    return NotFound("模板不存在");
                }

                // 这里应该调用实际的模板测试服务
                // 暂时返回模拟结果
                var result = new TemplateTestResultDto
                {
                    Success = true,
                    Location = new { X = 100, Y = 200, Width = 50, Height = 30 },
                    Duration = 150
                };

                // 增加使用次数
                await _templateRepository.IncrementUsageCountAsync(id);
                await _templateRepository.UpdateLastUsedTimeAsync(id, DateTime.Now);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试模板失败，ID: {Id}", id);
                return StatusCode(500, "测试模板失败");
            }
        }

        /// <summary>
        /// 获取分类列表
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<List<string>>> GetCategories()
        {
            try
            {
                var categories = await _templateRepository.GetCategoriesAsync();
                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类列表失败");
                return StatusCode(500, "获取分类列表失败");
            }
        }

        /// <summary>
        /// 获取标签列表
        /// </summary>
        [HttpGet("tags")]
        public async Task<ActionResult<List<string>>> GetTags()
        {
            try
            {
                var tags = await _templateRepository.GetTagsAsync();
                return Ok(tags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取标签列表失败");
                return StatusCode(500, "获取标签列表失败");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<TemplateStatisticsDto>> GetStatistics()
        {
            try
            {
                var statistics = await _templateRepository.GetStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计信息失败");
                return StatusCode(500, "获取统计信息失败");
            }
        }

        /// <summary>
        /// 上传模板图片
        /// </summary>
        [HttpPost("upload-image")]
        public async Task<ActionResult<object>> UploadImage(IFormFile file, [FromQuery] string? category = null, [FromQuery] int? templateId = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("请选择文件");
                }

                // 验证文件类型
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    return BadRequest("只支持 JPG、JPEG、PNG 格式的图片");
                }

                // 验证文件大小（2MB）
                if (file.Length > 2 * 1024 * 1024)
                {
                    return BadRequest("文件大小不能超过 2MB");
                }

                // 处理分类路径
                var categoryPath = string.IsNullOrEmpty(category) ? "default" : SanitizeCategoryName(category);

                // 如果是编辑现有模板，先删除旧文件
                if (templateId.HasValue)
                {
                    await DeleteOldTemplateImage(templateId.Value);
                }

                // 生成文件名
                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                var uploadPath = Path.Combine("uploads", "templates", categoryPath);
                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", uploadPath);

                // 确保目录存在
                Directory.CreateDirectory(fullPath);

                // 保存文件
                var filePath = Path.Combine(fullPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var relativePath = Path.Combine(uploadPath, fileName).Replace("\\", "/");

                return Ok(new { success = true, data = new { filePath = relativePath } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传图片失败");
                return StatusCode(500, "上传图片失败");
            }
        }

        /// <summary>
        /// 获取模板图片
        /// </summary>
        [HttpGet("image/{*filePath}")]
        [AllowAnonymous] // 允许匿名访问图片
        public IActionResult GetTemplateImage(string filePath)
        {
            try
            {
                // 安全检查：防止路径遍历攻击
                if (filePath.Contains("..") || Path.IsPathRooted(filePath))
                {
                    return BadRequest("无效的文件路径");
                }

                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", filePath);

                if (!System.IO.File.Exists(fullPath))
                {
                    return NotFound("文件不存在");
                }

                var contentType = GetContentType(filePath);
                var fileBytes = System.IO.File.ReadAllBytes(fullPath);

                return File(fileBytes, contentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取图片失败: {FilePath}", filePath);
                return StatusCode(500, "获取图片失败");
            }
        }

        /// <summary>
        /// 删除模板的旧图片文件
        /// </summary>
        private async Task DeleteOldTemplateImage(int templateId)
        {
            try
            {
                var template = await _templateRepository.GetByIdAsync(templateId);
                if (template != null && !string.IsNullOrEmpty(template.FilePath))
                {
                    var oldFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", template.FilePath);
                    if (System.IO.File.Exists(oldFilePath))
                    {
                        System.IO.File.Delete(oldFilePath);
                        _logger.LogInformation("删除旧图片文件: {FilePath}", template.FilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "删除旧图片文件失败，模板ID: {TemplateId}", templateId);
                // 不抛出异常，允许上传继续进行
            }
        }

        /// <summary>
        /// 清理分类名称，确保可以作为文件夹名称
        /// </summary>
        private static string SanitizeCategoryName(string categoryName)
        {
            if (string.IsNullOrWhiteSpace(categoryName))
                return "default";

            // 移除或替换不安全的字符
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = categoryName;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // 移除额外的不安全字符
            sanitized = sanitized.Replace(".", "_")
                                 .Replace(" ", "_")
                                 .Replace("/", "_")
                                 .Replace("\\", "_");

            // 限制长度
            if (sanitized.Length > 50)
            {
                sanitized = sanitized.Substring(0, 50);
            }

            return string.IsNullOrWhiteSpace(sanitized) ? "default" : sanitized;
        }

        /// <summary>
        /// 清理未使用的模板图片文件
        /// </summary>
        [HttpPost("cleanup-unused-images")]
        public async Task<ActionResult<object>> CleanupUnusedImages()
        {
            try
            {
                var templatesPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "templates");
                if (!Directory.Exists(templatesPath))
                {
                    return Ok(new { success = true, message = "模板目录不存在", deletedCount = 0 });
                }

                // 获取所有模板使用的文件路径
                var usedFiles = await _templateRepository.GetAllAsync();
                var usedFilePaths = usedFiles
                    .Where(t => !string.IsNullOrEmpty(t.FilePath))
                    .Select(t => Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", t.FilePath))
                    .ToHashSet(StringComparer.OrdinalIgnoreCase);

                // 扫描所有图片文件
                var allImageFiles = Directory.GetFiles(templatesPath, "*.*", SearchOption.AllDirectories)
                    .Where(f => IsImageFile(f))
                    .ToList();

                // 找出未使用的文件
                var unusedFiles = allImageFiles.Where(f => !usedFilePaths.Contains(f)).ToList();

                // 删除未使用的文件
                var deletedCount = 0;
                foreach (var file in unusedFiles)
                {
                    try
                    {
                        System.IO.File.Delete(file);
                        deletedCount++;
                        _logger.LogInformation("删除未使用的图片文件: {FilePath}", file);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "删除文件失败: {FilePath}", file);
                    }
                }

                return Ok(new {
                    success = true,
                    message = $"清理完成，删除了 {deletedCount} 个未使用的文件",
                    deletedCount = deletedCount,
                    totalFiles = allImageFiles.Count,
                    usedFiles = usedFilePaths.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理未使用图片失败");
                return StatusCode(500, "清理失败");
            }
        }

        /// <summary>
        /// 检查文件是否为图片文件
        /// </summary>
        private static bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension is ".png" or ".jpg" or ".jpeg" or ".gif" or ".bmp" or ".webp";
        }

        /// <summary>
        /// 根据文件扩展名获取Content-Type
        /// </summary>
        private static string GetContentType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".png" => "image/png",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId))
            {
                return userId;
            }
            return null;
        }

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static CustomTemplateDto MapToDto(CustomUIAutoMationTemplate entity)
        {
            return new CustomTemplateDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                Category = entity.Category,
                FilePath = entity.FilePath,
                Confidence = entity.Confidence,
                Tags = entity.TagList,
                Notes = entity.Notes,
                UsageCount = entity.UsageCount,
                LastUsedTime = entity.LastUsedTime,
                CreatedTime = entity.CreatedTime,
                UpdatedTime = entity.UpdatedTime,
                CreatedBy = entity.CreatedBy,
                IsDeleted = entity.IsDeleted,
                // 区域图片相关字段
                RegionFilePath = entity.RegionFilePath,
                RegionDescription = entity.RegionDescription,
                RegionConfidence = entity.RegionConfidence,
                RegionExpand = entity.RegionExpand,
                UseRegionMatching = entity.UseRegionMatching
            };
        }
    }
}
