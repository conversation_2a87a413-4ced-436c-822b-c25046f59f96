using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Visual Studio 编译会话实体
/// 功能: 记录VS编译会话的统计信息
/// 支持: 编译结果统计、AI修复效果跟踪
/// </summary>
[SugarTable("VSBuildSessions", "编译会话统计表")]
public class VSBuildSession : BaseEntity
{
    /// <summary>
    /// 关联的项目ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的项目ID")]
    public int? ProjectId { get; set; }

    /// <summary>
    /// 编译会话ID
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "编译会话ID")]
    [Required]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 编译配置：Debug, Release等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编译配置")]
    public string? BuildConfiguration { get; set; }

    /// <summary>
    /// 编译平台：x86, x64, AnyCPU等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编译平台")]
    public string? BuildPlatform { get; set; }

    /// <summary>
    /// 编译开始时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "编译开始时间")]
    public DateTime StartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 编译结束时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "编译结束时间")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 编译结果：Success, Failed, Cancelled
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "编译结果")]
    public string BuildResult { get; set; } = "Failed";

    /// <summary>
    /// 错误总数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "错误总数")]
    public int TotalErrors { get; set; } = 0;

    /// <summary>
    /// 警告总数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "警告总数")]
    public int TotalWarnings { get; set; } = 0;

    /// <summary>
    /// 已解决错误数
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "已解决错误数")]
    public int ResolvedErrors { get; set; } = 0;

    /// <summary>
    /// 是否已请求AI修复
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否已请求AI修复")]
    public bool AIFixRequested { get; set; } = false;

    /// <summary>
    /// AI修复响应内容
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI修复响应内容")]
    public string? AIFixResponse { get; set; }

    /// <summary>
    /// 是否已应用AI修复
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否已应用AI修复")]
    public bool AIFixApplied { get; set; } = false;

    /// <summary>
    /// 编译持续时间（毫秒）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public long? DurationMs => EndTime.HasValue ? (long)(EndTime.Value - StartTime).TotalMilliseconds : null;



    /// <summary>
    /// 错误解决率
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public decimal? ErrorResolutionRate => TotalErrors > 0 ? (decimal)ResolvedErrors / TotalErrors * 100 : null;

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的编译错误 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<VSBuildError> BuildErrors { get; set; } = new();
}
