using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt分析服务实现
/// </summary>
public class PromptAnalyticsService : IPromptAnalyticsService
{
    private readonly IPromptTemplateRepository _templateRepository;
    private readonly IRepository<PromptUsageStats> _usageStatsRepository;
    private readonly IRepository<PromptRating> _ratingRepository;
    private readonly IRepository<User> _userRepository;
    private readonly ILogger<PromptAnalyticsService> _logger;

    public PromptAnalyticsService(
        IPromptTemplateRepository templateRepository,
        IRepository<PromptUsageStats> usageStatsRepository,
        IRepository<PromptRating> ratingRepository,
        IRepository<User> userRepository,
        ILogger<PromptAnalyticsService> logger)
    {
        _templateRepository = templateRepository;
        _usageStatsRepository = usageStatsRepository;
        _ratingRepository = ratingRepository;
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取模板使用统计
    /// </summary>
    public async Task<TemplateUsageAnalytics> GetTemplateUsageAnalyticsAsync(int templateId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取模板使用统计，TemplateId: {TemplateId}", templateId);

            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            var usageStats = await GetUsageStatsInRange(templateId, startDate, endDate);
            var ratings = await _ratingRepository.GetListAsync(r => r.TemplateId == templateId);

            var analytics = new TemplateUsageAnalytics
            {
                TemplateId = templateId,
                TemplateName = template.Name,
                TotalUsage = usageStats.Count,
                UniqueUsers = usageStats.Select(u => u.UserId).Distinct().Count(),
                SuccessRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0,
                AverageResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
                TotalCost = usageStats.Where(u => u.Cost.HasValue).Sum(u => u.Cost) ?? 0,
                AverageRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) : 0,
                DailyUsage = CalculateDailyUsage(usageStats),
                HourlyUsage = CalculateHourlyUsage(usageStats),
                ProviderDistribution = usageStats.GroupBy(u => u.AIProvider).ToDictionary(g => g.Key, g => g.Count()),
                ErrorTypes = usageStats.Where(u => !u.IsSuccess && !string.IsNullOrEmpty(u.ErrorMessage))
                    .GroupBy(u => ExtractErrorType(u.ErrorMessage!))
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板使用统计失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户使用统计
    /// </summary>
    public async Task<UserUsageAnalytics> GetUserUsageAnalyticsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取用户使用统计，UserId: {UserId}", userId);

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException($"用户不存在，ID: {userId}");

            var usageStats = await GetUserUsageStatsInRange(userId, startDate, endDate);

            var analytics = new UserUsageAnalytics
            {
                UserId = userId,
                UserName = user.RealName ?? user.Username,
                TotalUsage = usageStats.Count,
                UniqueTemplates = usageStats.Select(u => u.TemplateId).Distinct().Count(),
                MostUsedTaskType = GetMostUsedTaskType(usageStats),
                PreferredProvider = GetPreferredProvider(usageStats),
                AverageSessionDuration = CalculateAverageSessionDuration(usageStats),
                TotalCost = usageStats.Where(u => u.Cost.HasValue).Sum(u => u.Cost) ?? 0,
                TopTemplates = GetTopTemplatesForUser(usageStats),
                DailyActivity = CalculateDailyUsage(usageStats)
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户使用统计失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取整体使用统计
    /// </summary>
    public async Task<OverallUsageAnalytics> GetOverallUsageAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取整体使用统计");

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);
            var allUsers = await _userRepository.GetAllAsync();
            var allTemplates = await _templateRepository.GetAllAsync();

            var activeUserIds = allUsageStats.Select(u => u.UserId).Distinct().ToList();

            var analytics = new OverallUsageAnalytics
            {
                TotalUsers = allUsers.Count,
                ActiveUsers = activeUserIds.Count,
                TotalTemplates = allTemplates.Count,
                TotalUsage = allUsageStats.Count,
                OverallSuccessRate = allUsageStats.Count > 0 ? (double)allUsageStats.Count(u => u.IsSuccess) / allUsageStats.Count : 0,
                TotalCost = allUsageStats.Where(u => u.Cost.HasValue).Sum(u => u.Cost) ?? 0,
                AverageResponseTime = allUsageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
                DailyTrends = CalculateDailyUsage(allUsageStats),
                TaskTypeDistribution = GetTaskTypeDistribution(allUsageStats),
                ProviderDistribution = allUsageStats.GroupBy(u => u.AIProvider).ToDictionary(g => g.Key, g => g.Count()),
                TopUsers = GetTopUsers(allUsageStats, allUsers)
            };

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取整体使用统计失败");
            throw;
        }
    }

    /// <summary>
    /// 获取热门模板排行
    /// </summary>
    public async Task<List<TemplateRanking>> GetPopularTemplatesRankingAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取热门模板排行，Count: {Count}", count);

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);
            var allTemplates = await _templateRepository.GetAllAsync();
            var allRatings = await _ratingRepository.GetAllAsync();

            var templateStats = allUsageStats
                .GroupBy(u => u.TemplateId)
                .Select(g => new
                {
                    TemplateId = g.Key,
                    UsageCount = g.Count(),
                    SuccessRate = (double)g.Count(u => u.IsSuccess) / g.Count(),
                    TrendScore = CalculateTrendScore(g.ToList())
                })
                .ToList();

            var rankings = templateStats
                .Join(allTemplates, ts => ts.TemplateId, t => t.Id, (ts, t) => new TemplateRanking
                {
                    TemplateId = t.Id,
                    TemplateName = t.Name,
                    TaskType = t.TaskType,
                    UsageCount = ts.UsageCount,
                    SuccessRate = ts.SuccessRate,
                    AverageRating = allRatings.Where(r => r.TemplateId == t.Id).Average(r => (double?)r.OverallRating) ?? 0,
                    TrendScore = ts.TrendScore
                })
                .OrderByDescending(r => r.UsageCount * r.SuccessRate * (1 + r.TrendScore))
                .Take(count)
                .Select((r, index) => { r.Rank = index + 1; return r; })
                .ToList();

            return rankings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门模板排行失败");
            throw;
        }
    }

    /// <summary>
    /// 获取AI提供商使用统计
    /// </summary>
    public async Task<List<ProviderUsageStats>> GetProviderUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取AI提供商使用统计");

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);

            var providerStats = allUsageStats
                .GroupBy(u => u.AIProvider)
                .Select(g => new ProviderUsageStats
                {
                    Provider = g.Key,
                    UsageCount = g.Count(),
                    SuccessRate = (double)g.Count(u => u.IsSuccess) / g.Count(),
                    AverageResponseTime = g.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
                    TotalCost = g.Where(u => u.Cost.HasValue).Sum(u => u.Cost) ?? 0,
                    AverageCost = g.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0,
                    PopularModels = g.GroupBy(u => u.AIModel)
                        .OrderByDescending(mg => mg.Count())
                        .Take(3)
                        .Select(mg => mg.Key)
                        .ToList()
                })
                .OrderByDescending(p => p.UsageCount)
                .ToList();

            return providerStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商使用统计失败");
            throw;
        }
    }

    /// <summary>
    /// 获取任务类型使用统计
    /// </summary>
    public async Task<List<TaskTypeUsageStats>> GetTaskTypeUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取任务类型使用统计");

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);
            var allTemplates = await _templateRepository.GetAllAsync();
            var allRatings = await _ratingRepository.GetAllAsync();

            var taskTypeStats = allUsageStats
                .Join(allTemplates, u => u.TemplateId, t => t.Id, (u, t) => new { Usage = u, Template = t })
                .GroupBy(x => x.Template.TaskType)
                .Select(g => new TaskTypeUsageStats
                {
                    TaskType = g.Key,
                    TaskTypeName = GetTaskTypeName(g.Key),
                    UsageCount = g.Count(),
                    TemplateCount = g.Select(x => x.Template.Id).Distinct().Count(),
                    SuccessRate = (double)g.Count(x => x.Usage.IsSuccess) / g.Count(),
                    AverageRating = allRatings
                        .Where(r => g.Select(x => x.Template.Id).Contains(r.TemplateId))
                        .Average(r => (double?)r.OverallRating) ?? 0,
                    TopTemplates = g.GroupBy(x => x.Template)
                        .Select(tg => new TemplateUsageSummary
                        {
                            TemplateId = tg.Key.Id,
                            TemplateName = tg.Key.Name,
                            UsageCount = tg.Count(),
                            SuccessRate = (double)tg.Count(x => x.Usage.IsSuccess) / tg.Count()
                        })
                        .OrderByDescending(t => t.UsageCount)
                        .Take(3)
                        .ToList()
                })
                .OrderByDescending(t => t.UsageCount)
                .ToList();

            return taskTypeStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务类型使用统计失败");
            throw;
        }
    }

    /// <summary>
    /// 获取模板效果分析
    /// </summary>
    public async Task<TemplateEffectivenessAnalysis> GetTemplateEffectivenessAsync(int templateId)
    {
        try
        {
            _logger.LogInformation("获取模板效果分析，TemplateId: {TemplateId}", templateId);

            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            var usageStats = await _usageStatsRepository.GetListAsync(u => u.TemplateId == templateId);
            var ratings = await _ratingRepository.GetListAsync(r => r.TemplateId == templateId);

            var analysis = new TemplateEffectivenessAnalysis
            {
                TemplateId = templateId,
                TemplateName = template.Name,
                EffectivenessScore = CalculateEffectivenessScore(usageStats, ratings),
                QualityScore = CalculateQualityScore(ratings),
                UsabilityScore = CalculateUsabilityScore(ratings),
                PerformanceScore = CalculatePerformanceScore(usageStats),
                CostEfficiencyScore = CalculateCostEfficiencyScore(usageStats),
                Strengths = IdentifyStrengths(usageStats, ratings),
                Weaknesses = IdentifyWeaknesses(usageStats, ratings),
                ImprovementAreas = IdentifyImprovementAreas(usageStats, ratings),
                MetricTrends = CalculateMetricTrends(usageStats)
            };

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板效果分析失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 比较模板效果
    /// </summary>
    public async Task<TemplateComparisonResult> CompareTemplatesAsync(int templateId1, int templateId2)
    {
        try
        {
            _logger.LogInformation("比较模板效果，TemplateId1: {TemplateId1}, TemplateId2: {TemplateId2}", templateId1, templateId2);

            var template1 = await _templateRepository.GetByIdAsync(templateId1);
            var template2 = await _templateRepository.GetByIdAsync(templateId2);

            if (template1 == null || template2 == null)
                throw new ArgumentException("模板不存在");

            var usageStats1 = await _usageStatsRepository.GetListAsync(u => u.TemplateId == templateId1);
            var usageStats2 = await _usageStatsRepository.GetListAsync(u => u.TemplateId == templateId2);

            var ratings1 = await _ratingRepository.GetListAsync(r => r.TemplateId == templateId1);
            var ratings2 = await _ratingRepository.GetListAsync(r => r.TemplateId == templateId2);

            var metrics1 = CalculateComparisonMetrics(usageStats1, ratings1);
            var metrics2 = CalculateComparisonMetrics(usageStats2, ratings2);

            var comparison = new TemplateComparisonResult
            {
                TemplateA = new TemplateComparisonItem
                {
                    TemplateId = templateId1,
                    TemplateName = template1.Name,
                    Metrics = metrics1
                },
                TemplateB = new TemplateComparisonItem
                {
                    TemplateId = templateId2,
                    TemplateName = template2.Name,
                    Metrics = metrics2
                },
                MetricComparisons = CompareMetrics(metrics1, metrics2).Values.ToList(),
                Summary = GenerateComparisonSummary(metrics1, metrics2, template1.Name, template2.Name),
                Recommendation = GenerateRecommendation(metrics1, metrics2, template1.Name, template2.Name)
            };

            return comparison;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "比较模板效果失败");
            throw;
        }
    }

    /// <summary>
    /// 获取优化建议
    /// </summary>
    public async Task<List<OptimizationSuggestion>> GetOptimizationSuggestionsAsync(int templateId)
    {
        try
        {
            _logger.LogInformation("获取优化建议，TemplateId: {TemplateId}", templateId);

            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            var usageStats = await _usageStatsRepository.GetListAsync(u => u.TemplateId == templateId);
            var ratings = await _ratingRepository.GetListAsync(r => r.TemplateId == templateId);

            var suggestions = new List<OptimizationSuggestion>();

            // 分析成功率
            var successRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0;
            if (successRate < 0.8)
            {
                suggestions.Add(new OptimizationSuggestion
                {
                    Type = "Quality",
                    Title = "提升成功率",
                    Description = $"当前成功率为 {successRate:P2}，建议优化提示词内容以提高AI理解准确性",
                    Priority = "High",
                    ImpactScore = 0.9,
                    ImplementationDifficulty = 0.6,
                    ActionItems = new List<string>
                    {
                        "分析失败案例的共同特征",
                        "添加更明确的指令和约束",
                        "提供更多示例",
                        "优化参数定义"
                    },
                    ExpectedOutcome = "成功率提升至85%以上"
                });
            }

            // 分析响应时间
            var avgResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
            if (avgResponseTime > 5000)
            {
                suggestions.Add(new OptimizationSuggestion
                {
                    Type = "Performance",
                    Title = "优化响应时间",
                    Description = $"平均响应时间为 {avgResponseTime:F0}ms，建议优化提示词长度和复杂度",
                    Priority = "Medium",
                    ImpactScore = 0.7,
                    ImplementationDifficulty = 0.4,
                    ActionItems = new List<string>
                    {
                        "简化提示词内容",
                        "移除冗余信息",
                        "优化参数传递",
                        "考虑使用更快的AI模型"
                    },
                    ExpectedOutcome = "响应时间减少20-30%"
                });
            }

            // 分析用户评分
            var avgRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) : 0;
            if (avgRating < 4.0)
            {
                suggestions.Add(new OptimizationSuggestion
                {
                    Type = "UserExperience",
                    Title = "提升用户满意度",
                    Description = $"平均评分为 {avgRating:F1}/5，建议根据用户反馈优化模板",
                    Priority = "High",
                    ImpactScore = 0.8,
                    ImplementationDifficulty = 0.7,
                    ActionItems = new List<string>
                    {
                        "收集详细用户反馈",
                        "分析低评分原因",
                        "优化输出格式",
                        "增强模板易用性"
                    },
                    ExpectedOutcome = "用户评分提升至4.2以上"
                });
            }

            // 分析成本效率
            var avgCost = usageStats.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0;
            if (avgCost > 0.1m)
            {
                suggestions.Add(new OptimizationSuggestion
                {
                    Type = "Cost",
                    Title = "降低使用成本",
                    Description = $"平均成本为 ${avgCost:F3}，建议优化以降低Token使用量",
                    Priority = "Medium",
                    ImpactScore = 0.6,
                    ImplementationDifficulty = 0.5,
                    ActionItems = new List<string>
                    {
                        "精简提示词内容",
                        "优化输出格式要求",
                        "考虑使用成本更低的模型",
                        "实施智能缓存策略"
                    },
                    ExpectedOutcome = "成本降低15-25%"
                });
            }

            return suggestions.OrderByDescending(s => s.ImpactScore).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取优化建议失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 执行A/B测试分析
    /// </summary>
    public async Task<ABTestResult> AnalyzeABTestAsync(int templateAId, int templateBId, DateTime startDate, DateTime endDate)
    {
        try
        {
            _logger.LogInformation("执行A/B测试分析，TemplateA: {TemplateAId}, TemplateB: {TemplateBId}", templateAId, templateBId);

            var templateA = await _templateRepository.GetByIdAsync(templateAId);
            var templateB = await _templateRepository.GetByIdAsync(templateBId);

            if (templateA == null || templateB == null)
                throw new ArgumentException("模板不存在");

            var usageStatsA = await _usageStatsRepository.GetListAsync(u =>
                u.TemplateId == templateAId && u.UsedAt >= startDate && u.UsedAt <= endDate);
            var usageStatsB = await _usageStatsRepository.GetListAsync(u =>
                u.TemplateId == templateBId && u.UsedAt >= startDate && u.UsedAt <= endDate);

            var ratingsA = await _ratingRepository.GetListAsync(r =>
                r.TemplateId == templateAId && r.RatedAt >= startDate && r.RatedAt <= endDate);
            var ratingsB = await _ratingRepository.GetListAsync(r =>
                r.TemplateId == templateBId && r.RatedAt >= startDate && r.RatedAt <= endDate);

            var resultA = new TemplateTestResult
            {
                TemplateId = templateAId,
                TemplateName = templateA.Name,
                SampleSize = usageStatsA.Count,
                SuccessRate = usageStatsA.Count > 0 ? (double)usageStatsA.Count(u => u.IsSuccess) / usageStatsA.Count : 0,
                AverageRating = ratingsA.Count > 0 ? ratingsA.Average(r => r.OverallRating) : 0,
                AverageResponseTime = usageStatsA.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
                AverageCost = usageStatsA.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0
            };

            var resultB = new TemplateTestResult
            {
                TemplateId = templateBId,
                TemplateName = templateB.Name,
                SampleSize = usageStatsB.Count,
                SuccessRate = usageStatsB.Count > 0 ? (double)usageStatsB.Count(u => u.IsSuccess) / usageStatsB.Count : 0,
                AverageRating = ratingsB.Count > 0 ? ratingsB.Average(r => r.OverallRating) : 0,
                AverageResponseTime = usageStatsB.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
                AverageCost = usageStatsB.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0
            };

            var statistics = CalculateTestStatistics(resultA, resultB);

            var result = new ABTestResult
            {
                TestName = $"{templateA.Name} vs {templateB.Name}",
                StartDate = startDate,
                EndDate = endDate,
                TemplateA = resultA,
                TemplateB = resultB,
                Statistics = statistics,
                IsStatisticallySignificant = statistics.PValue < 0.05,
                ConfidenceLevel = 0.95,
                Conclusion = GenerateABTestConclusion(resultA, resultB, statistics)
            };

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "A/B测试分析失败");
            throw;
        }
    }

    /// <summary>
    /// 获取成本分析
    /// </summary>
    public async Task<CostAnalysis> GetCostAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取成本分析");

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);
            var costStats = allUsageStats.Where(u => u.Cost.HasValue).ToList();

            var analysis = new CostAnalysis
            {
                TotalCost = costStats.Sum(u => u.Cost!.Value),
                AverageCostPerRequest = costStats.Count > 0 ? costStats.Average(u => u.Cost!.Value) : 0,
                CostByProvider = costStats.GroupBy(u => u.AIProvider).ToDictionary(g => g.Key, g => g.Sum(u => u.Cost!.Value)),
                CostByTaskType = await GetCostByTaskType(costStats),
                DailyCosts = CalculateDailyCosts(costStats),
                OptimizationSuggestions = GenerateCostOptimizationSuggestions(costStats)
            };

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取成本分析失败");
            throw;
        }
    }

    /// <summary>
    /// 获取性能分析
    /// </summary>
    public async Task<PerformanceAnalysis> GetPerformanceAnalysisAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("获取性能分析");

            var allUsageStats = await GetAllUsageStatsInRange(startDate, endDate);
            var performanceStats = allUsageStats.Where(u => u.ResponseTimeMs.HasValue).ToList();

            if (!performanceStats.Any())
            {
                return new PerformanceAnalysis();
            }

            var responseTimes = performanceStats.Select(u => (double)u.ResponseTimeMs!.Value).OrderBy(t => t).ToList();

            var analysis = new PerformanceAnalysis
            {
                AverageResponseTime = responseTimes.Average(),
                MedianResponseTime = CalculateMedian(responseTimes),
                P95ResponseTime = CalculatePercentile(responseTimes, 0.95),
                P99ResponseTime = CalculatePercentile(responseTimes, 0.99),
                ResponseTimeByProvider = performanceStats.GroupBy(u => u.AIProvider)
                    .ToDictionary(g => g.Key, g => g.Average(u => u.ResponseTimeMs!.Value)),
                ResponseTimeByTaskType = await GetResponseTimeByTaskType(performanceStats),
                Bottlenecks = IdentifyPerformanceBottlenecks(performanceStats)
            };

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能分析失败");
            throw;
        }
    }

    /// <summary>
    /// 生成分析报告
    /// </summary>
    public async Task<AnalyticsReport> GenerateReportAsync(ReportType reportType, ReportParameters parameters)
    {
        try
        {
            _logger.LogInformation("生成分析报告，类型: {ReportType}", reportType);

            var report = new AnalyticsReport
            {
                ReportId = Guid.NewGuid().ToString(),
                Type = reportType,
                GeneratedAt = DateTime.Now,
                Parameters = parameters
            };

            switch (reportType)
            {
                case ReportType.Usage:
                    await GenerateUsageReport(report);
                    break;
                case ReportType.Performance:
                    await GeneratePerformanceReport(report);
                    break;
                case ReportType.Cost:
                    await GenerateCostReport(report);
                    break;
                case ReportType.Quality:
                    await GenerateQualityReport(report);
                    break;
                case ReportType.Comparison:
                    await GenerateComparisonReport(report);
                    break;
                case ReportType.Optimization:
                    await GenerateOptimizationReport(report);
                    break;
            }

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成分析报告失败，类型: {ReportType}", reportType);
            throw;
        }
    }

    #region 私有辅助方法

    private async Task<List<PromptUsageStats>> GetUsageStatsInRange(int templateId, DateTime? startDate, DateTime? endDate)
    {
        var query = await _usageStatsRepository.GetListAsync(u => u.TemplateId == templateId);

        if (startDate.HasValue)
            query = query.Where(u => u.UsedAt >= startDate.Value).ToList();

        if (endDate.HasValue)
            query = query.Where(u => u.UsedAt <= endDate.Value).ToList();

        return query;
    }

    private async Task<List<PromptUsageStats>> GetUserUsageStatsInRange(int userId, DateTime? startDate, DateTime? endDate)
    {
        var query = await _usageStatsRepository.GetListAsync(u => u.UserId == userId);

        if (startDate.HasValue)
            query = query.Where(u => u.UsedAt >= startDate.Value).ToList();

        if (endDate.HasValue)
            query = query.Where(u => u.UsedAt <= endDate.Value).ToList();

        return query;
    }

    private async Task<List<PromptUsageStats>> GetAllUsageStatsInRange(DateTime? startDate, DateTime? endDate)
    {
        var query = await _usageStatsRepository.GetAllAsync();

        if (startDate.HasValue)
            query = query.Where(u => u.UsedAt >= startDate.Value).ToList();

        if (endDate.HasValue)
            query = query.Where(u => u.UsedAt <= endDate.Value).ToList();

        return query;
    }

    private List<DailyUsage> CalculateDailyUsage(List<PromptUsageStats> usageStats)
    {
        return usageStats
            .GroupBy(u => u.UsedAt.Date)
            .Select(g => new DailyUsage
            {
                Date = g.Key,
                Count = g.Count(),
                SuccessRate = (double)g.Count(u => u.IsSuccess) / g.Count()
            })
            .OrderBy(d => d.Date)
            .ToList();
    }

    private List<HourlyUsage> CalculateHourlyUsage(List<PromptUsageStats> usageStats)
    {
        return usageStats
            .GroupBy(u => u.UsedAt.Hour)
            .Select(g => new HourlyUsage
            {
                Hour = g.Key,
                Count = g.Count()
            })
            .OrderBy(h => h.Hour)
            .ToList();
    }

    private string ExtractErrorType(string errorMessage)
    {
        // 简化的错误类型提取逻辑
        if (errorMessage.Contains("timeout", StringComparison.OrdinalIgnoreCase))
            return "Timeout";
        if (errorMessage.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
            return "RateLimit";
        if (errorMessage.Contains("authentication", StringComparison.OrdinalIgnoreCase))
            return "Authentication";
        if (errorMessage.Contains("quota", StringComparison.OrdinalIgnoreCase))
            return "QuotaExceeded";
        return "Other";
    }

    private string GetMostUsedTaskType(List<PromptUsageStats> usageStats)
    {
        // 需要关联模板表获取任务类型，这里简化处理
        return "RequirementAnalysis"; // 示例返回
    }

    private string GetPreferredProvider(List<PromptUsageStats> usageStats)
    {
        return usageStats
            .GroupBy(u => u.AIProvider)
            .OrderByDescending(g => g.Count())
            .FirstOrDefault()?.Key ?? "Unknown";
    }

    private double CalculateAverageSessionDuration(List<PromptUsageStats> usageStats)
    {
        // 简化的会话时长计算
        return usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
    }

    private List<TemplateUsageSummary> GetTopTemplatesForUser(List<PromptUsageStats> usageStats)
    {
        return usageStats
            .GroupBy(u => u.TemplateId)
            .Select(g => new TemplateUsageSummary
            {
                TemplateId = g.Key,
                TemplateName = $"Template {g.Key}", // 实际应该查询模板名称
                UsageCount = g.Count(),
                SuccessRate = (double)g.Count(u => u.IsSuccess) / g.Count()
            })
            .OrderByDescending(t => t.UsageCount)
            .Take(5)
            .ToList();
    }

    private Dictionary<string, int> GetTaskTypeDistribution(List<PromptUsageStats> usageStats)
    {
        // 简化实现，实际需要关联模板表
        return new Dictionary<string, int>
        {
            ["RequirementAnalysis"] = usageStats.Count / 3,
            ["CodeGeneration"] = usageStats.Count / 3,
            ["Testing"] = usageStats.Count / 3
        };
    }

    private List<TopUser> GetTopUsers(List<PromptUsageStats> usageStats, List<User> allUsers)
    {
        return usageStats
            .GroupBy(u => u.UserId)
            .Select(g => new TopUser
            {
                UserId = g.Key,
                UserName = allUsers.FirstOrDefault(u => u.Id == g.Key)?.RealName ?? "Unknown",
                UsageCount = g.Count()
            })
            .OrderByDescending(u => u.UsageCount)
            .Take(10)
            .ToList();
    }

    private double CalculateTrendScore(List<PromptUsageStats> usageStats)
    {
        // 简化的趋势分数计算
        var recentUsage = usageStats.Where(u => u.UsedAt >= DateTime.Now.AddDays(-7)).Count();
        var totalUsage = usageStats.Count;
        return totalUsage > 0 ? (double)recentUsage / totalUsage : 0;
    }

    private string GetTaskTypeName(string taskType)
    {
        return taskType switch
        {
            "RequirementAnalysis" => "需求分析",
            "CodeGeneration" => "代码生成",
            "Testing" => "测试生成",
            "Documentation" => "文档生成",
            "CodeReview" => "代码审查",
            "Optimization" => "性能优化",
            _ => taskType
        };
    }

    private double CalculateEffectivenessScore(List<PromptUsageStats> usageStats, List<PromptRating> ratings)
    {
        var successRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0;
        var avgRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) / 5.0 : 0;
        var usageWeight = Math.Min(usageStats.Count / 100.0, 1.0); // 使用量权重

        return (successRate * 0.4 + avgRating * 0.4 + usageWeight * 0.2) * 10;
    }

    private double CalculateQualityScore(List<PromptRating> ratings)
    {
        if (!ratings.Any()) return 0;

        var accuracyScore = ratings.Where(r => r.AccuracyRating.HasValue).Average(r => r.AccuracyRating) ?? 0;
        var usefulnessScore = ratings.Where(r => r.UsefulnessRating.HasValue).Average(r => r.UsefulnessRating) ?? 0;
        var overallScore = ratings.Average(r => r.OverallRating);

        return (accuracyScore + usefulnessScore + overallScore) / 3.0 * 2; // 转换为10分制
    }

    private double CalculateUsabilityScore(List<PromptRating> ratings)
    {
        if (!ratings.Any()) return 0;

        var easeOfUseScore = ratings.Where(r => r.EaseOfUseRating.HasValue).Average(r => r.EaseOfUseRating) ?? 0;
        var recommendationRate = ratings.Where(r => r.WouldRecommend.HasValue).Average(r => r.WouldRecommend.Value ? 5 : 1);

        return (easeOfUseScore + recommendationRate) / 2.0 * 2; // 转换为10分制
    }

    private double CalculatePerformanceScore(List<PromptUsageStats> usageStats)
    {
        if (!usageStats.Any()) return 0;

        var avgResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
        var successRate = (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count;

        // 响应时间评分：5秒以下为满分，超过10秒为0分
        var responseTimeScore = Math.Max(0, Math.Min(10, (10000 - avgResponseTime) / 1000));
        var successScore = successRate * 10;

        return (responseTimeScore + successScore) / 2;
    }

    private double CalculateCostEfficiencyScore(List<PromptUsageStats> usageStats)
    {
        if (!usageStats.Any()) return 0;

        var avgCost = usageStats.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0;
        var successRate = (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count;

        // 成本效率 = 成功率 / 平均成本
        var efficiency = avgCost > 0 ? successRate / (double)avgCost : successRate;

        return Math.Min(10, efficiency * 10); // 转换为10分制
    }

    private List<string> IdentifyStrengths(List<PromptUsageStats> usageStats, List<PromptRating> ratings)
    {
        var strengths = new List<string>();

        var successRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0;
        if (successRate > 0.9) strengths.Add("高成功率");

        var avgRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) : 0;
        if (avgRating > 4.5) strengths.Add("用户满意度高");

        var avgResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
        if (avgResponseTime < 3000) strengths.Add("响应速度快");

        if (usageStats.Count > 100) strengths.Add("使用量大");

        return strengths;
    }

    private List<string> IdentifyWeaknesses(List<PromptUsageStats> usageStats, List<PromptRating> ratings)
    {
        var weaknesses = new List<string>();

        var successRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0;
        if (successRate < 0.7) weaknesses.Add("成功率偏低");

        var avgRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) : 0;
        if (avgRating < 3.5) weaknesses.Add("用户满意度低");

        var avgResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
        if (avgResponseTime > 8000) weaknesses.Add("响应时间过长");

        var avgCost = usageStats.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0;
        if (avgCost > 0.2m) weaknesses.Add("使用成本较高");

        return weaknesses;
    }

    private List<string> IdentifyImprovementAreas(List<PromptUsageStats> usageStats, List<PromptRating> ratings)
    {
        var areas = new List<string>();

        var errorTypes = usageStats.Where(u => !u.IsSuccess && !string.IsNullOrEmpty(u.ErrorMessage))
            .GroupBy(u => ExtractErrorType(u.ErrorMessage!))
            .OrderByDescending(g => g.Count())
            .ToList();

        if (errorTypes.Any())
        {
            areas.Add($"减少{errorTypes.First().Key}错误");
        }

        var lowRatings = ratings.Where(r => r.OverallRating < 3).ToList();
        if (lowRatings.Any())
        {
            areas.Add("改善低评分用户体验");
        }

        areas.Add("优化提示词结构");
        areas.Add("增强错误处理");

        return areas;
    }

    private Dictionary<string, double> CalculateMetricTrends(List<PromptUsageStats> usageStats)
    {
        // 简化的趋势计算
        var trends = new Dictionary<string, double>();

        var recentStats = usageStats.Where(u => u.UsedAt >= DateTime.Now.AddDays(-7)).ToList();
        var olderStats = usageStats.Where(u => u.UsedAt < DateTime.Now.AddDays(-7) && u.UsedAt >= DateTime.Now.AddDays(-14)).ToList();

        if (olderStats.Any())
        {
            var recentSuccessRate = recentStats.Count > 0 ? (double)recentStats.Count(u => u.IsSuccess) / recentStats.Count : 0;
            var olderSuccessRate = (double)olderStats.Count(u => u.IsSuccess) / olderStats.Count;
            trends["SuccessRate"] = recentSuccessRate - olderSuccessRate;

            var recentAvgTime = recentStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
            var olderAvgTime = olderStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0;
            trends["ResponseTime"] = olderAvgTime - recentAvgTime; // 负值表示时间增加
        }

        return trends;
    }

    private TemplateComparisonMetrics CalculateComparisonMetrics(List<PromptUsageStats> usageStats, List<PromptRating> ratings)
    {
        return new TemplateComparisonMetrics
        {
            UsageCount = usageStats.Count,
            SuccessRate = usageStats.Count > 0 ? (double)usageStats.Count(u => u.IsSuccess) / usageStats.Count : 0,
            AverageResponseTime = usageStats.Where(u => u.ResponseTimeMs.HasValue).Average(u => u.ResponseTimeMs) ?? 0,
            AverageRating = ratings.Count > 0 ? ratings.Average(r => r.OverallRating) : 0,
            AverageCost = usageStats.Where(u => u.Cost.HasValue).Average(u => u.Cost) ?? 0,
            UniqueUsers = usageStats.Select(u => u.UserId).Distinct().Count()
        };
    }

    private Dictionary<string, MetricComparison> CompareMetrics(TemplateComparisonMetrics metrics1, TemplateComparisonMetrics metrics2)
    {
        var comparisons = new Dictionary<string, MetricComparison>();

        comparisons["UsageCount"] = new MetricComparison
        {
            MetricName = "使用次数",
            ValueA = metrics1.UsageCount,
            ValueB = metrics2.UsageCount,
            Difference = metrics2.UsageCount - metrics1.UsageCount,
            PercentageChange = metrics1.UsageCount > 0 ? (double)(metrics2.UsageCount - metrics1.UsageCount) / metrics1.UsageCount * 100 : 0,
            Winner = metrics2.UsageCount > metrics1.UsageCount ? "B" : metrics1.UsageCount > metrics2.UsageCount ? "A" : "Tie"
        };

        comparisons["SuccessRate"] = new MetricComparison
        {
            MetricName = "成功率",
            ValueA = metrics1.SuccessRate,
            ValueB = metrics2.SuccessRate,
            Difference = metrics2.SuccessRate - metrics1.SuccessRate,
            PercentageChange = metrics1.SuccessRate > 0 ? (metrics2.SuccessRate - metrics1.SuccessRate) / metrics1.SuccessRate * 100 : 0,
            Winner = metrics2.SuccessRate > metrics1.SuccessRate ? "B" : metrics1.SuccessRate > metrics2.SuccessRate ? "A" : "Tie"
        };

        comparisons["AverageResponseTime"] = new MetricComparison
        {
            MetricName = "平均响应时间",
            ValueA = metrics1.AverageResponseTime,
            ValueB = metrics2.AverageResponseTime,
            Difference = metrics2.AverageResponseTime - metrics1.AverageResponseTime,
            PercentageChange = metrics1.AverageResponseTime > 0 ? (metrics2.AverageResponseTime - metrics1.AverageResponseTime) / metrics1.AverageResponseTime * 100 : 0,
            Winner = metrics2.AverageResponseTime < metrics1.AverageResponseTime ? "B" : metrics1.AverageResponseTime < metrics2.AverageResponseTime ? "A" : "Tie"
        };

        comparisons["AverageRating"] = new MetricComparison
        {
            MetricName = "平均评分",
            ValueA = metrics1.AverageRating,
            ValueB = metrics2.AverageRating,
            Difference = metrics2.AverageRating - metrics1.AverageRating,
            PercentageChange = metrics1.AverageRating > 0 ? (metrics2.AverageRating - metrics1.AverageRating) / metrics1.AverageRating * 100 : 0,
            Winner = metrics2.AverageRating > metrics1.AverageRating ? "B" : metrics1.AverageRating > metrics2.AverageRating ? "A" : "Tie"
        };

        return comparisons;
    }

    private string GenerateComparisonSummary(TemplateComparisonMetrics metrics1, TemplateComparisonMetrics metrics2, string name1, string name2)
    {
        var summary = new List<string>();

        if (metrics2.SuccessRate > metrics1.SuccessRate)
            summary.Add($"{name2}的成功率比{name1}高{(metrics2.SuccessRate - metrics1.SuccessRate):P2}");
        else if (metrics1.SuccessRate > metrics2.SuccessRate)
            summary.Add($"{name1}的成功率比{name2}高{(metrics1.SuccessRate - metrics2.SuccessRate):P2}");

        if (metrics2.AverageResponseTime < metrics1.AverageResponseTime)
            summary.Add($"{name2}的响应时间比{name1}快{metrics1.AverageResponseTime - metrics2.AverageResponseTime:F0}ms");
        else if (metrics1.AverageResponseTime < metrics2.AverageResponseTime)
            summary.Add($"{name1}的响应时间比{name2}快{metrics2.AverageResponseTime - metrics1.AverageResponseTime:F0}ms");

        if (metrics2.AverageRating > metrics1.AverageRating)
            summary.Add($"{name2}的用户评分比{name1}高{metrics2.AverageRating - metrics1.AverageRating:F1}分");
        else if (metrics1.AverageRating > metrics2.AverageRating)
            summary.Add($"{name1}的用户评分比{name2}高{metrics1.AverageRating - metrics2.AverageRating:F1}分");

        return summary.Any() ? string.Join("；", summary) : "两个模板的表现相近";
    }

    private string GenerateRecommendation(TemplateComparisonMetrics metrics1, TemplateComparisonMetrics metrics2, string name1, string name2)
    {
        var score1 = CalculateOverallScore(metrics1);
        var score2 = CalculateOverallScore(metrics2);

        if (score2 > score1)
            return $"推荐使用{name2}，综合表现更优秀";
        else if (score1 > score2)
            return $"推荐使用{name1}，综合表现更优秀";
        else
            return "两个模板表现相近，可根据具体需求选择";
    }

    private double CalculateOverallScore(TemplateComparisonMetrics metrics)
    {
        var successScore = metrics.SuccessRate * 40;
        var ratingScore = metrics.AverageRating * 8; // 转换为40分制
        var responseTimeScore = Math.Max(0, Math.Min(20, (5000 - metrics.AverageResponseTime) / 250)); // 响应时间评分
        return successScore + ratingScore + responseTimeScore;
    }

    private TestStatistics CalculateTestStatistics(TemplateTestResult resultA, TemplateTestResult resultB)
    {
        // 简化的统计计算
        var pooledSuccessRate = (resultA.SuccessRate * resultA.SampleSize + resultB.SuccessRate * resultB.SampleSize) / (resultA.SampleSize + resultB.SampleSize);
        var pooledVariance = pooledSuccessRate * (1 - pooledSuccessRate);
        var standardError = Math.Sqrt(pooledVariance * (1.0 / resultA.SampleSize + 1.0 / resultB.SampleSize));

        var zScore = standardError > 0 ? (resultB.SuccessRate - resultA.SuccessRate) / standardError : 0;
        var pValue = 2 * (1 - NormalCDF(Math.Abs(zScore))); // 双尾检验

        var effectSize = Math.Abs(resultB.SuccessRate - resultA.SuccessRate);
        var powerAnalysis = CalculatePower(effectSize, resultA.SampleSize, resultB.SampleSize);

        return new TestStatistics
        {
            PValue = pValue,
            EffectSize = effectSize,
            PowerAnalysis = powerAnalysis,
            StatisticalTest = "Z-test for proportions"
        };
    }

    private double NormalCDF(double x)
    {
        // 简化的正态分布累积分布函数
        return 0.5 * (1 + Math.Sign(x) * Math.Sqrt(1 - Math.Exp(-2 * x * x / Math.PI)));
    }

    private double CalculatePower(double effectSize, int sampleSizeA, int sampleSizeB)
    {
        // 简化的功效计算
        var harmonicMean = 2.0 / (1.0 / sampleSizeA + 1.0 / sampleSizeB);
        var power = Math.Min(0.99, effectSize * Math.Sqrt(harmonicMean) * 2);
        return Math.Max(0.05, power);
    }

    private string GenerateABTestConclusion(TemplateTestResult resultA, TemplateTestResult resultB, TestStatistics statistics)
    {
        var conclusion = new List<string>();

        if (statistics.PValue < 0.05)
        {
            var winner = resultB.SuccessRate > resultA.SuccessRate ? resultB.TemplateName : resultA.TemplateName;
            conclusion.Add($"统计显著性检验表明{winner}表现更优（p < 0.05）");
        }
        else
        {
            conclusion.Add("两个模板之间没有统计显著性差异（p ≥ 0.05）");
        }

        if (statistics.EffectSize > 0.1)
            conclusion.Add("效应量较大，差异具有实际意义");
        else if (statistics.EffectSize > 0.05)
            conclusion.Add("效应量中等");
        else
            conclusion.Add("效应量较小");

        if (statistics.PowerAnalysis < 0.8)
            conclusion.Add("建议增加样本量以提高检验功效");

        return string.Join("；", conclusion);
    }

    private async Task<Dictionary<string, decimal>> GetCostByTaskType(List<PromptUsageStats> costStats)
    {
        // 简化实现，实际需要关联模板表获取任务类型
        var result = new Dictionary<string, decimal>();
        var totalCost = costStats.Sum(u => u.Cost!.Value);

        result["RequirementAnalysis"] = totalCost * 0.4m;
        result["CodeGeneration"] = totalCost * 0.3m;
        result["Testing"] = totalCost * 0.2m;
        result["Documentation"] = totalCost * 0.1m;

        return result;
    }

    private List<DailyCost> CalculateDailyCosts(List<PromptUsageStats> costStats)
    {
        return costStats
            .GroupBy(u => u.UsedAt.Date)
            .Select(g => new DailyCost
            {
                Date = g.Key,
                Cost = g.Sum(u => u.Cost!.Value)
            })
            .OrderBy(d => d.Date)
            .ToList();
    }

    private List<CostOptimizationSuggestion> GenerateCostOptimizationSuggestions(List<PromptUsageStats> costStats)
    {
        var suggestions = new List<CostOptimizationSuggestion>();
        var avgCost = costStats.Average(u => u.Cost!.Value);

        if (avgCost > 0.1m)
        {
            suggestions.Add(new CostOptimizationSuggestion
            {
                Title = "优化提示词长度",
                Description = "减少不必要的提示词内容，降低Token使用量",
                PotentialSavings = avgCost * 0.2m
            });
        }

        if (costStats.GroupBy(u => u.AIProvider).Count() > 1)
        {
            suggestions.Add(new CostOptimizationSuggestion
            {
                Title = "选择成本更低的AI提供商",
                Description = "对比不同提供商的成本效益，选择最优方案",
                PotentialSavings = avgCost * 0.15m
            });
        }

        suggestions.Add(new CostOptimizationSuggestion
        {
            Title = "实施智能缓存",
            Description = "对相似请求实施缓存机制，减少重复调用",
            PotentialSavings = avgCost * 0.25m
        });

        return suggestions;
    }

    private double CalculateMedian(List<double> values)
    {
        if (!values.Any()) return 0;

        var sorted = values.OrderBy(v => v).ToList();
        var count = sorted.Count;

        if (count % 2 == 0)
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        else
            return sorted[count / 2];
    }

    private double CalculatePercentile(List<double> values, double percentile)
    {
        if (!values.Any()) return 0;

        var sorted = values.OrderBy(v => v).ToList();
        var index = (int)Math.Ceiling(percentile * sorted.Count) - 1;
        index = Math.Max(0, Math.Min(sorted.Count - 1, index));

        return sorted[index];
    }

    private async Task<Dictionary<string, double>> GetResponseTimeByTaskType(List<PromptUsageStats> performanceStats)
    {
        // 简化实现，实际需要关联模板表获取任务类型
        var result = new Dictionary<string, double>();
        var avgTime = performanceStats.Average(u => u.ResponseTimeMs!.Value);

        result["RequirementAnalysis"] = avgTime * 1.2;
        result["CodeGeneration"] = avgTime * 1.5;
        result["Testing"] = avgTime * 0.8;
        result["Documentation"] = avgTime * 1.0;

        return result;
    }

    private List<PerformanceBottleneck> IdentifyPerformanceBottlenecks(List<PromptUsageStats> performanceStats)
    {
        var bottlenecks = new List<PerformanceBottleneck>();
        var avgResponseTime = performanceStats.Average(u => u.ResponseTimeMs!.Value);

        if (avgResponseTime > 8000)
        {
            bottlenecks.Add(new PerformanceBottleneck
            {
                Component = "AI模型响应",
                Description = "AI模型响应时间过长，可能需要优化提示词或更换更快的模型",
                ImpactScore = 0.9
            });
        }

        var slowProviders = performanceStats
            .GroupBy(u => u.AIProvider)
            .Where(g => g.Average(u => u.ResponseTimeMs!.Value) > avgResponseTime * 1.5)
            .ToList();

        foreach (var provider in slowProviders)
        {
            bottlenecks.Add(new PerformanceBottleneck
            {
                Component = $"AI提供商: {provider.Key}",
                Description = $"{provider.Key}的响应时间明显高于平均水平",
                ImpactScore = 0.7
            });
        }

        return bottlenecks;
    }

    private async Task GenerateUsageReport(AnalyticsReport report)
    {
        var analytics = await GetOverallUsageAnalyticsAsync(report.Parameters.StartDate, report.Parameters.EndDate);

        report.Summary = $"总使用量: {analytics.TotalUsage}, 活跃用户: {analytics.ActiveUsers}, 整体成功率: {analytics.OverallSuccessRate:P2}";
        report.Data["analytics"] = analytics;
        report.KeyInsights = new List<string>
        {
            $"系统共有{analytics.TotalUsers}个用户，其中{analytics.ActiveUsers}个活跃用户",
            $"总共使用了{analytics.TotalTemplates}个模板，产生{analytics.TotalUsage}次调用",
            $"整体成功率为{analytics.OverallSuccessRate:P2}，平均响应时间{analytics.AverageResponseTime:F0}ms"
        };
        report.Recommendations = new List<string>
        {
            "继续监控使用趋势，及时调整资源配置",
            "关注低活跃用户，提供更好的使用指导",
            "优化高频使用的模板以提升整体性能"
        };
    }

    private async Task GeneratePerformanceReport(AnalyticsReport report)
    {
        var analysis = await GetPerformanceAnalysisAsync(report.Parameters.StartDate, report.Parameters.EndDate);

        report.Summary = $"平均响应时间: {analysis.AverageResponseTime:F0}ms, P95: {analysis.P95ResponseTime:F0}ms, P99: {analysis.P99ResponseTime:F0}ms";
        report.Data["analysis"] = analysis;
        report.KeyInsights = new List<string>
        {
            $"平均响应时间为{analysis.AverageResponseTime:F0}ms，中位数为{analysis.MedianResponseTime:F0}ms",
            $"95%的请求在{analysis.P95ResponseTime:F0}ms内完成",
            $"发现{analysis.Bottlenecks.Count}个性能瓶颈"
        };
        report.Recommendations = analysis.Bottlenecks.Select(b => b.Description).ToList();
    }

    private async Task GenerateCostReport(AnalyticsReport report)
    {
        var analysis = await GetCostAnalysisAsync(report.Parameters.StartDate, report.Parameters.EndDate);

        report.Summary = $"总成本: ${analysis.TotalCost:F2}, 平均每次请求: ${analysis.AverageCostPerRequest:F3}";
        report.Data["analysis"] = analysis;
        report.KeyInsights = new List<string>
        {
            $"总成本为${analysis.TotalCost:F2}，平均每次请求成本${analysis.AverageCostPerRequest:F3}",
            $"成本最高的提供商: {analysis.CostByProvider.OrderByDescending(kv => kv.Value).FirstOrDefault().Key}",
            $"发现{analysis.OptimizationSuggestions.Count}个成本优化机会"
        };
        report.Recommendations = analysis.OptimizationSuggestions.Select(s => s.Description).ToList();
    }

    private async Task GenerateQualityReport(AnalyticsReport report)
    {
        // 简化实现，获取所有模板的质量分析
        var templates = await _templateRepository.GetAllAsync();
        var qualityScores = new List<double>();

        foreach (var template in templates.Take(10)) // 限制数量避免性能问题
        {
            try
            {
                var effectiveness = await GetTemplateEffectivenessAsync(template.Id);
                qualityScores.Add(effectiveness.QualityScore);
            }
            catch
            {
                // 忽略错误，继续处理其他模板
            }
        }

        var avgQuality = qualityScores.Any() ? qualityScores.Average() : 0;

        report.Summary = $"平均质量评分: {avgQuality:F1}/10, 分析了{qualityScores.Count}个模板";
        report.Data["qualityScores"] = qualityScores;
        report.KeyInsights = new List<string>
        {
            $"平均质量评分为{avgQuality:F1}分（满分10分）",
            $"最高质量评分: {(qualityScores.Any() ? qualityScores.Max() : 0):F1}分",
            $"最低质量评分: {(qualityScores.Any() ? qualityScores.Min() : 0):F1}分"
        };
        report.Recommendations = new List<string>
        {
            "重点优化低质量评分的模板",
            "分析高质量模板的成功因素",
            "建立质量评估标准和流程"
        };
    }

    private async Task GenerateComparisonReport(AnalyticsReport report)
    {
        var templateIds = report.Parameters.TemplateIds;
        if (templateIds.Count >= 2)
        {
            var comparison = await CompareTemplatesAsync(templateIds[0], templateIds[1]);

            report.Summary = comparison.Summary;
            report.Data["comparison"] = comparison;
            report.KeyInsights = new List<string>
            {
                comparison.Summary,
                $"模板A使用量: {comparison.TemplateA.Metrics.UsageCount}",
                $"模板B使用量: {comparison.TemplateB.Metrics.UsageCount}"
            };
            report.Recommendations = new List<string> { comparison.Recommendation };
        }
        else
        {
            report.Summary = "需要至少两个模板进行比较";
            report.KeyInsights = new List<string> { "请选择至少两个模板进行比较分析" };
            report.Recommendations = new List<string> { "选择更多模板进行对比分析" };
        }
    }

    private async Task GenerateOptimizationReport(AnalyticsReport report)
    {
        var allSuggestions = new List<OptimizationSuggestion>();
        var templateIds = report.Parameters.TemplateIds;

        foreach (var templateId in templateIds.Take(5)) // 限制数量
        {
            try
            {
                var suggestions = await GetOptimizationSuggestionsAsync(templateId);
                allSuggestions.AddRange(suggestions);
            }
            catch
            {
                // 忽略错误，继续处理其他模板
            }
        }

        var highPrioritySuggestions = allSuggestions.Where(s => s.Priority == "High").ToList();

        report.Summary = $"发现{allSuggestions.Count}个优化建议，其中{highPrioritySuggestions.Count}个高优先级";
        report.Data["suggestions"] = allSuggestions;
        report.KeyInsights = new List<string>
        {
            $"总共发现{allSuggestions.Count}个优化机会",
            $"高优先级建议: {highPrioritySuggestions.Count}个",
            $"平均影响评分: {(allSuggestions.Any() ? allSuggestions.Average(s => s.ImpactScore) : 0):F2}"
        };
        report.Recommendations = highPrioritySuggestions.Take(5).Select(s => s.Description).ToList();
    }

    #endregion
}
