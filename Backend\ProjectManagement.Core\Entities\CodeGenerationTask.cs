using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 代码生成任务实体 - 对应DatabaseSchema.sql中的CodeGenerationTasks表
/// 功能: 管理AI代码生成任务的执行状态和结果
/// 支持: 多技术栈、任务状态跟踪、错误处理、批量生成
/// </summary>
[SugarTable("CodeGenerationTasks", "AI代码生成任务管理表")]
public class CodeGenerationTask
{
    /// <summary>
    /// 代码生成任务唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "代码生成任务唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的需求文档ID，可为空")]
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 代码生成任务名称（如：用户管理API生成）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "代码生成任务名称")]
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 代码类型: Frontend(前端), Backend(后端), Database(数据库), API(接口), Model(模型)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "代码类型")]
    public string CodeType { get; set; } = string.Empty;

    /// <summary>
    /// 使用的技术栈: Vue, CSharp, SqlServer, React, Python等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "使用的技术栈")]
    public string? Technology { get; set; }

    /// <summary>
    /// 任务状态: Pending(待处理), InProgress(进行中), Completed(已完成), Failed(失败)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "任务状态")]
    public string Status { get; set; } = "Pending";

    /// <summary>
    /// AI生成的代码内容
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成的代码内容")]
    public string? GeneratedCode { get; set; }

    /// <summary>
    /// 生成代码的文件路径
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "生成代码的文件路径")]
    public string? FilePath { get; set; }

    /// <summary>
    /// 错误信息（任务失败时记录）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 任务创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 任务完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "任务完成时间")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的需求文档 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RequirementDocument? RequirementDocument { get; set; }

    /// <summary>
    /// 生成的代码文件 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<GeneratedCodeFile> GeneratedCodeFiles { get; set; } = new();

    /// <summary>
    /// 关联的测试任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<TestTask> TestTasks { get; set; } = new();
}
