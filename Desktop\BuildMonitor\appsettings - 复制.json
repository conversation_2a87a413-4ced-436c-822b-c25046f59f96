{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ProjectManagementAI;Persist Security Info=True;User ID=sa;Password=**********;TrustServerCertificate=true;MultipleActiveResultSets=false;", "AlternativeConnection": "Server=************;Database=ProjectManagementAI;Persist Security Info=True;User ID=test_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=false;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "SqlSugar": "Information"}}, "BuildSettings": {"BackendProjectPath": "D:\\Projects\\ProjectManagement\\Backend\\ProjectManagement.API\\ProjectManagement.API.csproj", "FrontendProjectPath": "D:\\Projects\\ProjectManagement\\Frontend", "AutoCompileInterval": 30, "MaxErrorsToDisplay": 100, "EnableAutoCompile": false, "CompileTimeoutSeconds": 300}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSqlLogging": true, "AutoCreateTables": false}, "UISettings": {"RefreshInterval": 5, "ShowWarnings": true, "ShowInfoMessages": false, "MaxDisplayRows": 1000}}