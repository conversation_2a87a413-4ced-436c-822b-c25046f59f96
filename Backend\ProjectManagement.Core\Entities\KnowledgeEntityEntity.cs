using SqlSugar;
using System.Text.Json;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 知识实体表
    /// </summary>
    [SugarTable("KnowledgeEntities")]
    public class KnowledgeEntityEntity : BaseEntity
    {
        /// <summary>
        /// 实体类型 (Project, Person, Technology, Module, Skill, BestPractice)
        /// </summary>
        [SugarColumn(ColumnDescription = "实体类型", Length = 50, IsNullable = false)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 实体名称
        /// </summary>
        [SugarColumn(ColumnDescription = "实体名称", Length = 200, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 关联的业务实体ID
        /// </summary>
        [SugarColumn(ColumnDescription = "关联的业务实体ID", Length = 50, IsNullable = true)]
        public string? ReferenceId { get; set; }

        /// <summary>
        /// 实体属性（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDescription = "实体属性", ColumnDataType = "TEXT", IsNullable = true)]
        public string? PropertiesJson { get; set; }

        /// <summary>
        /// 实体属性（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, object> Properties
        {
            get
            {
                if (string.IsNullOrEmpty(PropertiesJson))
                    return new Dictionary<string, object>();

                try
                {
                    return JsonSerializer.Deserialize<Dictionary<string, object>>(PropertiesJson) 
                           ?? new Dictionary<string, object>();
                }
                catch
                {
                    return new Dictionary<string, object>();
                }
            }
            set
            {
                PropertiesJson = JsonSerializer.Serialize(value);
            }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnDescription = "更新时间", IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
