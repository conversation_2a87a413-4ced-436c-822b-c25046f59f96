using ProjectManagement.Core.DTOs.VectorSearch;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 文档向量仓储接口
    /// </summary>
    public interface IDocumentVectorRepository
    {
        Task<DocumentVector> CreateAsync(DocumentVector documentVector);
        Task<DocumentVector?> GetByIdAsync(int id);
        Task<List<DocumentVector>> GetByDocumentIdAsync(string documentId);
        Task<List<DocumentVector>> FindSimilarAsync(
            float[] queryEmbedding, 
            int topK, 
            float similarityThreshold = 0.7f,
            Dictionary<string, object>? filters = null);
        Task<DocumentVector> UpdateAsync(DocumentVector documentVector);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByDocumentIdAsync(string documentId);
        Task<List<DocumentVector>> SearchByMetadataAsync(Dictionary<string, object> filters);
    }
}
