#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copilot管理器
负责VSCode Copilot聊天记录的管理和显示
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from typing import Dict, List, Optional
from datetime import datetime


class CopilotManager:
    """Copilot管理器类"""
    
    def __init__(self, parent_ui):
        """
        初始化Copilot管理器
        
        Args:
            parent_ui: 父UI对象
        """
        self.parent_ui = parent_ui
        
        # Copilot聊天记录相关
        self.dom_controller = None
        self.chat_controller = None
        self.is_copilot_connected = False
        self.copilot_auto_refresh = False
        self.copilot_refresh_interval = 10  # 10秒刷新间隔
        self.copilot_refresh_thread = None
        self.last_chat_records = []
        
        # UI组件引用
        self.copilot_text = None
        self.connect_btn = None
        self.disconnect_btn = None
        self.refresh_btn = None
        self.auto_refresh_var = None
        self.connection_status_label = None
        self.record_count_label = None
    
    def set_ui_components(self, copilot_text, connect_btn, disconnect_btn, refresh_btn,
                         auto_refresh_var, connection_status_label, record_count_label):
        """设置UI组件引用"""
        self.copilot_text = copilot_text
        self.connect_btn = connect_btn
        self.disconnect_btn = disconnect_btn
        self.refresh_btn = refresh_btn
        self.auto_refresh_var = auto_refresh_var
        self.connection_status_label = connection_status_label
        self.record_count_label = record_count_label
    
    def connect_copilot(self):
        """连接Copilot"""
        def connect_thread():
            try:
                self.parent_ui.parent.after(0, lambda: self.connection_status_label.config(text="🔄 连接中...", foreground="blue"))
                self.parent_ui.parent.after(0, lambda: self.connect_btn.config(state="disabled"))

                # 初始化DOM控制器
                try:
                    from vscode_dom_controller import VSCodeDOMController
                    self.dom_controller = VSCodeDOMController()
                    print("✅ VSCode DOM控制器初始化成功")
                except Exception as e:
                    print(f"❌ VSCode DOM控制器初始化失败: {e}")
                    self.parent_ui.parent.after(0, lambda: messagebox.showerror("错误", f"DOM控制器初始化失败: {e}"))
                    return

                # 初始化聊天控制器
                try:
                    from copilot_chat_controller import CopilotChatController
                    self.chat_controller = CopilotChatController(self.dom_controller)
                    print("✅ Copilot聊天控制器初始化成功")
                except Exception as e:
                    print(f"❌ Copilot聊天控制器初始化失败: {e}")
                    self.parent_ui.parent.after(0, lambda: messagebox.showerror("错误", f"聊天控制器初始化失败: {e}"))
                    return

                # 测试连接
                try:
                    test_result = self.chat_controller.test_connection()
                    if test_result:
                        self.is_copilot_connected = True
                        self.parent_ui.parent.after(0, self._on_connect_success)
                        print("✅ Copilot连接测试成功")
                    else:
                        self.parent_ui.parent.after(0, lambda: self._on_connect_failed("连接测试失败"))
                        print("❌ Copilot连接测试失败")
                except Exception as e:
                    self.parent_ui.parent.after(0, lambda: self._on_connect_failed(f"连接测试异常: {e}"))
                    print(f"❌ Copilot连接测试异常: {e}")

            except Exception as e:
                self.parent_ui.parent.after(0, lambda: self._on_connect_failed(f"连接过程异常: {e}"))
                print(f"❌ Copilot连接过程异常: {e}")

        threading.Thread(target=connect_thread, daemon=True).start()

    def _on_connect_success(self):
        """连接成功回调"""
        self.connection_status_label.config(text="🟢 已连接", foreground="green")
        self.connect_btn.config(state="disabled")
        self.disconnect_btn.config(state="normal")
        self.refresh_btn.config(state="normal")
        
        # 立即刷新一次聊天记录
        self.refresh_copilot_records()
        
        # 如果启用了自动刷新，开始自动刷新
        if self.auto_refresh_var.get():
            self.start_copilot_auto_refresh()
        
        messagebox.showinfo("成功", "Copilot连接成功！")

    def _on_connect_failed(self, error_msg):
        """连接失败回调"""
        self.connection_status_label.config(text="🔴 连接失败", foreground="red")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")
        self.refresh_btn.config(state="disabled")
        messagebox.showerror("连接失败", f"无法连接到Copilot: {error_msg}")

    def disconnect_copilot(self):
        """断开Copilot连接"""
        try:
            # 停止自动刷新
            self.stop_copilot_auto_refresh()
            
            # 重置连接状态
            self.is_copilot_connected = False
            self.dom_controller = None
            self.chat_controller = None
            
            # 更新UI状态
            self.connection_status_label.config(text="🔴 未连接", foreground="gray")
            self.connect_btn.config(state="normal")
            self.disconnect_btn.config(state="disabled")
            self.refresh_btn.config(state="disabled")
            
            # 清空显示
            self.clear_copilot_display()
            
            messagebox.showinfo("断开连接", "已断开Copilot连接")
            
        except Exception as e:
            messagebox.showerror("错误", f"断开连接时发生异常: {e}")

    def refresh_copilot_records(self):
        """刷新Copilot聊天记录"""
        if not self.is_copilot_connected or not self.chat_controller:
            messagebox.showwarning("警告", "请先连接Copilot")
            return

        def refresh_thread():
            try:
                self.parent_ui.parent.after(0, lambda: self.add_copilot_log("🔄 正在刷新聊天记录...", "info"))
                
                # 获取聊天记录
                records = self.chat_controller.get_chat_records()
                
                if records:
                    self.last_chat_records = records
                    self.parent_ui.parent.after(0, lambda: self.display_copilot_records(records))
                    self.parent_ui.parent.after(0, lambda: self.add_copilot_log(f"✅ 成功获取 {len(records)} 条聊天记录", "success"))
                else:
                    self.parent_ui.parent.after(0, lambda: self.add_copilot_log("ℹ️ 暂无聊天记录", "info"))
                
            except Exception as e:
                error_msg = f"刷新聊天记录失败: {e}"
                self.parent_ui.parent.after(0, lambda: self.add_copilot_log(f"❌ {error_msg}", "error"))
                print(f"❌ {error_msg}")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def toggle_copilot_auto_refresh(self):
        """切换Copilot自动刷新"""
        if self.auto_refresh_var.get():
            if self.is_copilot_connected:
                self.start_copilot_auto_refresh()
            self.add_copilot_log("🔄 自动刷新已启用", "info")
        else:
            self.stop_copilot_auto_refresh()
            self.add_copilot_log("⏸️ 自动刷新已停用", "info")

    def start_copilot_auto_refresh(self):
        """开始Copilot自动刷新"""
        if not self.is_copilot_connected:
            return

        def auto_refresh_loop():
            while self.copilot_auto_refresh and self.is_copilot_connected:
                time.sleep(self.copilot_refresh_interval)
                if self.copilot_auto_refresh and self.is_copilot_connected:
                    self.parent_ui.parent.after(0, self.refresh_copilot_records)

        self.copilot_auto_refresh = True
        self.copilot_refresh_thread = threading.Thread(target=auto_refresh_loop, daemon=True)
        self.copilot_refresh_thread.start()

    def stop_copilot_auto_refresh(self):
        """停止Copilot自动刷新"""
        self.copilot_auto_refresh = False

    def display_copilot_records(self, records):
        """显示Copilot聊天记录"""
        try:
            # 清空现有内容
            self.copilot_text.config(state=tk.NORMAL)
            self.copilot_text.delete(1.0, tk.END)

            if not records:
                self.copilot_text.insert(tk.END, "暂无聊天记录\n")
                self.record_count_label.config(text="记录数: 0")
                self.copilot_text.config(state=tk.DISABLED)
                return

            # 显示聊天记录
            for i, record in enumerate(records, 1):
                timestamp = record.get('timestamp', '未知时间')
                user_message = record.get('user_message', '')
                ai_response = record.get('ai_response', '')
                
                # 添加分隔线
                if i > 1:
                    self.copilot_text.insert(tk.END, "\n" + "="*50 + "\n\n")
                
                # 添加时间戳
                self.copilot_text.insert(tk.END, f"🕒 时间: {timestamp}\n\n")
                
                # 添加用户消息
                if user_message:
                    self.copilot_text.insert(tk.END, "👤 用户:\n")
                    self.copilot_text.insert(tk.END, f"{user_message}\n\n")
                
                # 添加AI回复
                if ai_response:
                    self.copilot_text.insert(tk.END, "🤖 Copilot:\n")
                    self.copilot_text.insert(tk.END, f"{ai_response}\n")

            # 更新记录数量
            self.record_count_label.config(text=f"记录数: {len(records)}")
            
            # 滚动到底部
            self.copilot_text.see(tk.END)
            self.copilot_text.config(state=tk.DISABLED)

        except Exception as e:
            self.add_copilot_log(f"❌ 显示聊天记录失败: {e}", "error")

    def add_copilot_log(self, message, tag="info"):
        """添加Copilot日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"
            
            self.copilot_text.config(state=tk.NORMAL)
            self.copilot_text.insert(tk.END, log_message)
            self.copilot_text.see(tk.END)
            self.copilot_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"添加Copilot日志失败: {e}")

    def clear_copilot_display(self):
        """清空Copilot显示"""
        try:
            self.copilot_text.config(state=tk.NORMAL)
            self.copilot_text.delete(1.0, tk.END)
            self.copilot_text.insert(tk.END, "请连接Copilot以查看聊天记录")
            self.copilot_text.config(state=tk.DISABLED)
            self.record_count_label.config(text="记录数: 0")
        except Exception as e:
            print(f"清空Copilot显示失败: {e}")

    def send_message_to_copilot(self, message):
        """发送消息给Copilot"""
        if not self.is_copilot_connected or not self.chat_controller:
            messagebox.showwarning("警告", "请先连接Copilot")
            return False

        try:
            # 发送消息
            success = self.chat_controller.send_message(message)
            
            if success:
                self.add_copilot_log(f"✅ 消息已发送: {message[:50]}...", "success")
                # 延迟刷新聊天记录以获取AI回复
                self.parent_ui.parent.after(3000, self.refresh_copilot_records)
                return True
            else:
                self.add_copilot_log("❌ 消息发送失败", "error")
                return False
                
        except Exception as e:
            error_msg = f"发送消息异常: {e}"
            self.add_copilot_log(f"❌ {error_msg}", "error")
            return False

    def get_last_chat_records(self):
        """获取最后的聊天记录"""
        return self.last_chat_records

    def is_connected(self):
        """检查是否已连接"""
        return self.is_copilot_connected

    def get_connection_status(self):
        """获取连接状态"""
        if self.is_copilot_connected:
            return "已连接"
        else:
            return "未连接"
