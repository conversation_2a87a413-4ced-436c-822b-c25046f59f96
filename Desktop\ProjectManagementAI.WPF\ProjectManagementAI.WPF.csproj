<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <!-- <ApplicationIcon>Assets\app.ico</ApplicationIcon> -->
    <AssemblyTitle>AI项目管理系统</AssemblyTitle>
    <AssemblyDescription>基于AI的智能项目管理桌面应用</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <!-- 暂时移除有兼容性问题的包 -->
    <!-- <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" /> -->
    <!-- <PackageReference Include="Microsoft.Toolkit.Win32.UI.Controls" Version="6.1.3" /> -->
  </ItemGroup>

  <!-- Backend项目引用已移除，Desktop项目独立运行 -->

  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Models\" />
    <Folder Include="Controls\" />
    <Folder Include="Behaviors\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\**\*" />
  </ItemGroup>

</Project>
