using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.API.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// Prompt分类控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PromptCategoryController : ControllerBase
{
    private readonly IPromptCategoryRepository _categoryRepository;
    private readonly IPromptTemplateRepository _templateRepository;
    private readonly ILogger<PromptCategoryController> _logger;

    public PromptCategoryController(
        IPromptCategoryRepository categoryRepository,
        IPromptTemplateRepository templateRepository,
        ILogger<PromptCategoryController> logger)
    {
        _categoryRepository = categoryRepository;
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有分类
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<PromptCategoryDto>>> GetCategories()
    {
        try
        {
            var categories = await _categoryRepository.GetEnabledCategoriesAsync();

            // 计算每个分类的模板数量
            await CalculateTemplateCountsAsync(categories);

            var result = categories.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类列表失败");
            return StatusCode(500, new { message = "获取分类列表失败" });
        }
    }

    /// <summary>
    /// 获取分类树形结构
    /// </summary>
    [HttpGet("tree")]
    public async Task<ActionResult<List<PromptCategoryDto>>> GetCategoryTree()
    {
        try
        {
            var categories = await _categoryRepository.GetCategoryTreeAsync();

            // 计算每个分类的模板数量
            await CalculateTemplateCountsAsync(categories);

            var result = categories.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类树形结构失败");
            return StatusCode(500, new { message = "获取分类树形结构失败" });
        }
    }

    /// <summary>
    /// 获取根分类
    /// </summary>
    [HttpGet("root")]
    public async Task<ActionResult<List<PromptCategoryDto>>> GetRootCategories()
    {
        try
        {
            var categories = await _categoryRepository.GetRootCategoriesAsync();
            var result = categories.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取根分类失败");
            return StatusCode(500, new { message = "获取根分类失败" });
        }
    }

    /// <summary>
    /// 获取子分类
    /// </summary>
    [HttpGet("{parentId}/children")]
    public async Task<ActionResult<List<PromptCategoryDto>>> GetChildCategories(int parentId)
    {
        try
        {
            var categories = await _categoryRepository.GetChildCategoriesAsync(parentId);
            var result = categories.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取子分类失败，ParentId: {ParentId}", parentId);
            return StatusCode(500, new { message = "获取子分类失败" });
        }
    }

    /// <summary>
    /// 根据ID获取分类
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<PromptCategoryDto>> GetCategory(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
                return NotFound(new { message = "分类不存在" });

            return Ok(MapToDto(category));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类失败，ID: {Id}", id);
            return StatusCode(500, new { message = "获取分类失败" });
        }
    }

    /// <summary>
    /// 获取分类路径
    /// </summary>
    [HttpGet("{id}/path")]
    public async Task<ActionResult<List<PromptCategoryDto>>> GetCategoryPath(int id)
    {
        try
        {
            var path = await _categoryRepository.GetCategoryPathAsync(id);
            var result = path.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类路径失败，ID: {Id}", id);
            return StatusCode(500, new { message = "获取分类路径失败" });
        }
    }

    /// <summary>
    /// 创建分类（仅管理员）
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<PromptCategoryDto>> CreateCategory([FromBody] CreatePromptCategoryDto request)
    {
        try
        {
            var category = new PromptCategory
            {
                Name = request.Name,
                Description = request.Description,
                ParentId = request.ParentId,
                Icon = request.Icon,
                Color = request.Color,
                SortOrder = request.SortOrder,
                IsEnabled = request.IsEnabled
            };

            var result = await _categoryRepository.AddAsync(category);
            return Ok(MapToDto(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建分类失败");
            return StatusCode(500, new { message = "创建分类失败" });
        }
    }

    /// <summary>
    /// 更新分类（仅管理员）
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<PromptCategoryDto>> UpdateCategory(int id, [FromBody] UpdatePromptCategoryDto request)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
                return NotFound(new { message = "分类不存在" });

            category.Name = request.Name;
            category.Description = request.Description;
            category.ParentId = request.ParentId;
            category.Icon = request.Icon;
            category.Color = request.Color;
            category.SortOrder = request.SortOrder;
            category.IsEnabled = request.IsEnabled;

            var result = await _categoryRepository.UpdateAsync(category);
            if (result)
            {
                // 重新获取更新后的实体
                var updatedCategory = await _categoryRepository.GetByIdAsync(id);
                return Ok(MapToDto(updatedCategory!));
            }
            else
            {
                return BadRequest(new { message = "更新分类失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新分类失败，ID: {Id}", id);
            return StatusCode(500, new { message = "更新分类失败" });
        }
    }

    /// <summary>
    /// 删除分类（仅管理员）
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult> DeleteCategory(int id)
    {
        try
        {
            var result = await _categoryRepository.DeleteAsync(id);
            if (result)
                return Ok(new { message = "分类删除成功" });
            else
                return BadRequest(new { message = "分类删除失败" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除分类失败，ID: {Id}", id);
            return StatusCode(500, new { message = "删除分类失败" });
        }
    }

    /// <summary>
    /// 计算分类的模板数量
    /// </summary>
    private async Task CalculateTemplateCountsAsync(List<PromptCategory> categories)
    {
        foreach (var category in categories)
        {
            // 获取该分类下的模板数量
            var templates = await _templateRepository.GetByCategoryIdAsync(category.Id);
            category.TemplateCount = templates.Count;

            // 递归计算子分类的模板数量
            if (category.Children?.Any() == true)
            {
                await CalculateTemplateCountsAsync(category.Children);
            }
        }
    }

    /// <summary>
    /// 映射实体到DTO
    /// </summary>
    private PromptCategoryDto MapToDto(PromptCategory category)
    {
        return new PromptCategoryDto
        {
            Id = category.Id,
            Name = category.Name,
            Description = category.Description,
            ParentId = category.ParentId,
            ParentName = category.Parent?.Name,
            Icon = category.Icon,
            Color = category.Color,
            SortOrder = category.SortOrder,
            IsEnabled = category.IsEnabled,
            TemplateCount = category.TemplateCount,
            Children = category.Children?.Select(MapToDto).ToList() ?? new List<PromptCategoryDto>()
        };
    }
}

/// <summary>
/// 创建分类请求DTO
/// </summary>
public class CreatePromptCategoryDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? ParentId { get; set; }
    public string? Icon { get; set; }
    public string? Color { get; set; }
    public int SortOrder { get; set; } = 0;
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 更新分类请求DTO
/// </summary>
public class UpdatePromptCategoryDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? ParentId { get; set; }
    public string? Icon { get; set; }
    public string? Color { get; set; }
    public int SortOrder { get; set; } = 0;
    public bool IsEnabled { get; set; } = true;
}
