namespace ProjectManagement.Core.DTOs.KnowledgeGraph
{
    /// <summary>
    /// 知识实体
    /// </summary>
    public class KnowledgeEntity
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty; // Project, Person, Technology, Module, Skill, BestPractice
        public string Name { get; set; } = string.Empty;
        public string ReferenceId { get; set; } = string.Empty; // 关联的业务实体ID
        public Dictionary<string, object> Properties { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 知识关系
    /// </summary>
    public class KnowledgeRelation
    {
        public int Id { get; set; }
        public int FromEntityId { get; set; }
        public int ToEntityId { get; set; }
        public string RelationType { get; set; } = string.Empty; // UsesTechnology, WorksOn, ResponsibleFor, HasSkill, etc.
        public float Weight { get; set; } = 1.0f;
        public Dictionary<string, object> Properties { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 项目相似性
    /// </summary>
    public class ProjectSimilarity
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public float SimilarityScore { get; set; }
        public List<string> SimilarityReasons { get; set; } = new();
    }

    /// <summary>
    /// 专家推荐
    /// </summary>
    public class ExpertRecommendation
    {
        public string PersonId { get; set; } = string.Empty;
        public string PersonName { get; set; } = string.Empty;
        public float SkillLevel { get; set; }
        public float RecommendationScore { get; set; }
        public List<string> Reasons { get; set; } = new();
    }

    /// <summary>
    /// 最佳实践
    /// </summary>
    public class BestPractice
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public float Priority { get; set; }
        public string Source { get; set; } = string.Empty;
        public List<string> Examples { get; set; } = new();
    }

    /// <summary>
    /// 项目知识数据
    /// </summary>
    public class ProjectKnowledgeData
    {
        public string ProjectName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public List<string> Technologies { get; set; } = new();
        public List<string> TeamMembers { get; set; } = new();
        public List<ModuleInfo> Modules { get; set; } = new();
    }

    /// <summary>
    /// 模块信息
    /// </summary>
    public class ModuleInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Owner { get; set; } = string.Empty;
    }
}
