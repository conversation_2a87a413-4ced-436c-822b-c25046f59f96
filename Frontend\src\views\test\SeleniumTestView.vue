<template>
  <div class="selenium-test-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-row :gutter="20" align="middle">
        <el-col :span="12">
          <h2>🔧 Selenium自动化测试框架</h2>
        </el-col>
        <el-col :span="12" class="text-right">
          <el-button type="primary" @click="createNewScript">
            <el-icon><Plus /></el-icon>
            新建脚本
          </el-button>
          <el-button type="success" @click="runSelectedScript" :disabled="!selectedScript">
            <el-icon><VideoPlay /></el-icon>
            运行脚本
          </el-button>
          <el-button type="warning" @click="stopExecution" :disabled="!isRunning">
            <el-icon><VideoPause /></el-icon>
            停止执行
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧：脚本列表 -->
      <el-col :span="6">
        <el-card class="script-list-card">
          <template #header>
            <div class="card-header">
              <span>📋 测试脚本</span>
              <el-button type="text" @click="refreshScripts">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索脚本..."
            prefix-icon="Search"
            class="search-input"
          />
          
          <!-- 脚本分类 -->
          <el-tabs v-model="activeCategory" class="script-tabs">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="UI测试" name="ui" />
            <el-tab-pane label="API测试" name="api" />
            <el-tab-pane label="集成测试" name="integration" />
          </el-tabs>
          
          <!-- 脚本列表 -->
          <div class="script-list">
            <div
              v-for="script in filteredScripts"
              :key="script.id"
              class="script-item"
              :class="{ active: selectedScript?.id === script.id }"
              @click="selectScript(script)"
            >
              <div class="script-info">
                <div class="script-name">{{ script.name }}</div>
                <div class="script-meta">
                  <el-tag :type="getStatusType(script.status)" size="small">
                    {{ script.status }}
                  </el-tag>
                  <span class="script-date">{{ formatDate(script.updatedTime) }}</span>
                </div>
              </div>
              <div class="script-actions">
                <el-button type="text" size="small" @click.stop="editScript(script)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="text" size="small" @click.stop="deleteScript(script)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 中间：脚本编辑器 -->
      <el-col :span="12">
        <el-card class="editor-card">
          <template #header>
            <div class="card-header">
              <span>📝 脚本编辑器</span>
              <div class="editor-actions">
                <el-button type="text" @click="saveScript" :disabled="!selectedScript">
                  <el-icon><Document /></el-icon>
                  保存
                </el-button>
                <el-button type="text" @click="formatCode" :disabled="!selectedScript">
                  <el-icon><Setting /></el-icon>
                  格式化
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-if="selectedScript" class="editor-content">
            <!-- 脚本基本信息 -->
            <el-form :model="selectedScript" label-width="80px" class="script-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="脚本名称">
                    <el-input v-model="selectedScript.name" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分类">
                    <el-select v-model="selectedScript.category" style="width: 100%">
                      <el-option label="UI测试" value="ui" />
                      <el-option label="API测试" value="api" />
                      <el-option label="集成测试" value="integration" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="描述">
                <el-input v-model="selectedScript.description" type="textarea" :rows="2" />
              </el-form-item>
            </el-form>
            
            <!-- 代码编辑器 -->
            <div class="code-editor">
              <el-tabs v-model="activeEditorTab">
                <el-tab-pane label="Python代码" name="python">
                  <div class="editor-toolbar">
                    <el-button-group>
                      <el-button size="small" @click="insertTemplate('basic')">基础模板</el-button>
                      <el-button size="small" @click="insertTemplate('login')">登录模板</el-button>
                      <el-button size="small" @click="insertTemplate('form')">表单模板</el-button>
                    </el-button-group>
                  </div>
                  <el-input
                    v-model="selectedScript.code"
                    type="textarea"
                    :rows="20"
                    placeholder="在这里编写您的Selenium测试代码..."
                    class="code-textarea"
                  />
                </el-tab-pane>
                <el-tab-pane label="配置" name="config">
                  <selenium-config v-model="selectedScript.config" />
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          
          <div v-else class="no-script-selected">
            <el-empty description="请选择一个脚本进行编辑" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：执行结果 -->
      <el-col :span="6">
        <el-card class="result-card">
          <template #header>
            <div class="card-header">
              <span>📊 执行结果</span>
              <el-button type="text" @click="clearResults">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </template>
          
          <!-- 执行状态 -->
          <div class="execution-status">
            <el-alert
              v-if="executionStatus"
              :title="executionStatus.title"
              :type="executionStatus.type"
              :description="executionStatus.description"
              show-icon
              :closable="false"
            />
          </div>
          
          <!-- 实时日志 -->
          <div class="log-container">
            <div class="log-header">
              <span>📝 执行日志</span>
              <el-switch
                v-model="autoScroll"
                active-text="自动滚动"
                size="small"
              />
            </div>
            <div ref="logContainer" class="log-content">
              <div
                v-for="(log, index) in executionLogs"
                :key="index"
                class="log-item"
                :class="log.level"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
          
          <!-- 执行统计 -->
          <div class="execution-stats" v-if="executionStats">
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="开始时间">
                {{ formatDateTime(executionStats.startTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="执行时长">
                {{ executionStats.duration }}
              </el-descriptions-item>
              <el-descriptions-item label="执行步骤">
                {{ executionStats.totalSteps }}
              </el-descriptions-item>
              <el-descriptions-item label="成功步骤">
                <span class="success-count">{{ executionStats.successSteps }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="失败步骤">
                <span class="error-count">{{ executionStats.failedSteps }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 脚本创建/编辑对话框 -->
    <script-dialog
      v-model="showScriptDialog"
      :script="editingScript"
      @save="handleScriptSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  VideoPlay,
  VideoPause,
  Refresh,
  Edit,
  Delete,
  Document,
  Setting,
  Search
} from '@element-plus/icons-vue'

// 导入组件
import ScriptDialog from '@/components/test/ScriptDialog.vue'
import SeleniumConfig from '@/components/test/SeleniumConfig.vue'

// 接口定义
interface TestScript {
  id: number
  name: string
  description: string
  category: string
  code: string
  config: any
  status: string
  updatedTime: Date
}

interface ExecutionLog {
  timestamp: Date
  level: string
  message: string
}

interface ExecutionStats {
  startTime: Date
  duration: string
  totalSteps: number
  successSteps: number
  failedSteps: number
}

// 响应式数据
const searchKeyword = ref('')
const activeCategory = ref('all')
const activeEditorTab = ref('python')
const selectedScript = ref<TestScript | null>(null)
const showScriptDialog = ref(false)
const editingScript = ref<TestScript | null>(null)
const isRunning = ref(false)
const autoScroll = ref(true)
const logContainer = ref<HTMLElement>()

const executionStatus = ref<{
  title: string
  type: 'error' | 'success' | 'primary' | 'warning' | 'info'
  description: string
} | null>(null)

const executionLogs = ref<ExecutionLog[]>([])
const executionStats = ref<ExecutionStats | null>(null)

// 测试脚本数据
const scripts = ref<TestScript[]>([
  {
    id: 1,
    name: '用户登录测试',
    description: '测试用户登录功能',
    category: 'ui',
    code: `# 用户登录测试脚本
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_user_login():
    driver = webdriver.Chrome()
    try:
        # 打开登录页面
        driver.get("https://example.com/login")
        
        # 输入用户名
        username_field = driver.find_element(By.NAME, "username")
        username_field.send_keys("testuser")
        
        # 输入密码
        password_field = driver.find_element(By.NAME, "password")
        password_field.send_keys("password123")
        
        # 点击登录按钮
        login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
        login_button.click()
        
        # 等待登录成功
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "dashboard"))
        )
        
        print("登录测试成功")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_user_login()`,
    config: {
      browser: 'chrome',
      timeout: 30,
      headless: false
    },
    status: '就绪',
    updatedTime: new Date()
  }
])

// 计算属性
const filteredScripts = computed(() => {
  let filtered = scripts.value

  // 按分类过滤
  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(script => script.category === activeCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(script =>
      script.name.toLowerCase().includes(keyword) ||
      script.description.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 方法
const refreshScripts = () => {
  ElMessage.success('脚本列表已刷新')
  // TODO: 从API加载脚本列表
}

const selectScript = (script: TestScript) => {
  selectedScript.value = script
}

const createNewScript = () => {
  editingScript.value = {
    id: 0,
    name: '',
    description: '',
    category: 'ui',
    code: '',
    config: {
      browser: 'chrome',
      timeout: 30,
      headless: false
    },
    status: '草稿',
    updatedTime: new Date()
  }
  showScriptDialog.value = true
}

const editScript = (script: TestScript) => {
  editingScript.value = { ...script }
  showScriptDialog.value = true
}

const deleteScript = async (script: TestScript) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除脚本 "${script.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = scripts.value.findIndex(s => s.id === script.id)
    if (index > -1) {
      scripts.value.splice(index, 1)
      if (selectedScript.value?.id === script.id) {
        selectedScript.value = null
      }
      ElMessage.success('脚本删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const saveScript = () => {
  if (!selectedScript.value) return
  
  // TODO: 保存到API
  ElMessage.success('脚本保存成功')
}

const formatCode = () => {
  if (!selectedScript.value) return
  
  // TODO: 实现代码格式化
  ElMessage.success('代码格式化完成')
}

const insertTemplate = (templateType: string) => {
  if (!selectedScript.value) return
  
  const templates = {
    basic: `# 基础Selenium测试模板
from selenium import webdriver
from selenium.webdriver.common.by import By

def test_basic():
    driver = webdriver.Chrome()
    try:
        driver.get("https://example.com")
        # 在这里添加您的测试代码
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_basic()`,
    
    login: `# 登录测试模板
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_login():
    driver = webdriver.Chrome()
    try:
        driver.get("https://example.com/login")
        
        # 输入用户名
        username = driver.find_element(By.NAME, "username")
        username.send_keys("your_username")
        
        # 输入密码
        password = driver.find_element(By.NAME, "password")
        password.send_keys("your_password")
        
        # 点击登录
        login_btn = driver.find_element(By.XPATH, "//button[@type='submit']")
        login_btn.click()
        
        # 验证登录成功
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "dashboard"))
        )
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_login()`,
    
    form: `# 表单测试模板
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select

def test_form():
    driver = webdriver.Chrome()
    try:
        driver.get("https://example.com/form")
        
        # 填写文本框
        name_field = driver.find_element(By.NAME, "name")
        name_field.send_keys("测试用户")
        
        # 选择下拉框
        select_element = Select(driver.find_element(By.NAME, "category"))
        select_element.select_by_visible_text("选项1")
        
        # 勾选复选框
        checkbox = driver.find_element(By.NAME, "agree")
        if not checkbox.is_selected():
            checkbox.click()
        
        # 提交表单
        submit_btn = driver.find_element(By.XPATH, "//button[@type='submit']")
        submit_btn.click()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_form()`
  }
  
  selectedScript.value.code = templates[templateType as keyof typeof templates] || ''
  ElMessage.success(`${templateType}模板已插入`)
}

const runSelectedScript = async () => {
  if (!selectedScript.value) return
  
  isRunning.value = true
  executionLogs.value = []
  executionStatus.value = {
    title: '正在执行测试',
    type: 'warning',
    description: `正在运行脚本: ${selectedScript.value.name}`
  }
  
  // 模拟执行过程
  const logs = [
    { level: 'info', message: '开始执行测试脚本...' },
    { level: 'info', message: '初始化WebDriver...' },
    { level: 'info', message: '打开浏览器...' },
    { level: 'info', message: '导航到目标页面...' },
    { level: 'success', message: '页面加载完成' },
    { level: 'info', message: '执行测试步骤...' },
    { level: 'success', message: '测试执行完成' }
  ]
  
  for (let i = 0; i < logs.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    executionLogs.value.push({
      timestamp: new Date(),
      ...logs[i]
    })
    
    if (autoScroll.value) {
      await nextTick()
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    }
  }
  
  isRunning.value = false
  executionStatus.value = {
    title: '执行完成',
    type: 'success',
    description: '测试脚本执行成功'
  }
  
  executionStats.value = {
    startTime: new Date(Date.now() - 7000),
    duration: '7秒',
    totalSteps: 5,
    successSteps: 5,
    failedSteps: 0
  }
}

const stopExecution = () => {
  isRunning.value = false
  executionStatus.value = {
    title: '执行已停止',
    type: 'info',
    description: '用户手动停止了测试执行'
  }
  ElMessage.warning('测试执行已停止')
}

const clearResults = () => {
  executionLogs.value = []
  executionStatus.value = null
  executionStats.value = null
  ElMessage.success('执行结果已清空')
}

const handleScriptSave = (script: TestScript) => {
  if (script.id === 0) {
    // 新建脚本
    script.id = Date.now()
    scripts.value.push(script)
    ElMessage.success('脚本创建成功')
  } else {
    // 更新脚本
    const index = scripts.value.findIndex(s => s.id === script.id)
    if (index > -1) {
      scripts.value[index] = script
      if (selectedScript.value?.id === script.id) {
        selectedScript.value = script
      }
      ElMessage.success('脚本更新成功')
    }
  }
  showScriptDialog.value = false
}

const getStatusType = (status: string): 'success' | 'warning' | 'danger' | 'info' => {
  const types: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    '就绪': 'success',
    '草稿': 'info',
    '运行中': 'warning',
    '失败': 'danger'
  }
  return types[status] || 'info'
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN')
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  // 初始化
})
</script>

<style lang="scss" scoped>
.selenium-test-container {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
  
  .text-right {
    text-align: right;
  }
}

.main-content {
  flex: 1;
  height: 0; // 重要：让flex子项能够正确计算高度
}

.script-list-card,
.editor-card,
.result-card {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  margin-bottom: 16px;
}

.script-tabs {
  margin-bottom: 16px;
  
  :deep(.el-tabs__header) {
    margin: 0 0 16px 0;
  }
}

.script-list {
  flex: 1;
  overflow-y: auto;
}

.script-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
  
  &.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }
  
  .script-info {
    flex: 1;
    
    .script-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .script-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .script-date {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .script-actions {
    display: flex;
    gap: 4px;
  }
}

.editor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.script-form {
  margin-bottom: 16px;
}

.code-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .editor-toolbar {
    margin-bottom: 8px;
  }
  
  .code-textarea {
    :deep(textarea) {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  :deep(.el-tabs__content) {
    flex: 1;
    height: 0;
  }
  
  :deep(.el-tab-pane) {
    height: 100%;
  }
}

.no-script-selected {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.execution-status {
  margin-bottom: 16px;
}

.log-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .log-content {
    flex: 1;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }
  
  .log-item {
    margin-bottom: 4px;
    
    .log-time {
      color: #909399;
      margin-right: 8px;
    }
    
    .log-message {
      color: #303133;
    }
    
    &.info .log-message {
      color: #409eff;
    }
    
    &.success .log-message {
      color: #67c23a;
    }
    
    &.warning .log-message {
      color: #e6a23c;
    }
    
    &.error .log-message {
      color: #f56c6c;
    }
  }
}

.execution-stats {
  .success-count {
    color: #67c23a;
    font-weight: bold;
  }
  
  .error-count {
    color: #f56c6c;
    font-weight: bold;
  }
}
</style>
