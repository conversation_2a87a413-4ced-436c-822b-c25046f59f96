using ProjectManagement.Core.DTOs.KnowledgeGraph;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 知识关系仓储接口
    /// </summary>
    public interface IKnowledgeRelationRepository
    {
        Task<KnowledgeRelation> CreateAsync(KnowledgeRelation relation);
        Task<KnowledgeRelation?> GetByIdAsync(int id);
        Task<List<KnowledgeRelation>> GetByFromEntityAsync(int fromEntityId);
        Task<List<KnowledgeRelation>> GetByToEntityAsync(int toEntityId);
        Task<List<KnowledgeRelation>> GetByFromEntityAndTypeAsync(int fromEntityId, string relationType);
        Task<List<KnowledgeRelation>> GetByToEntityAndTypeAsync(int toEntityId, string relationType);
        Task<List<KnowledgeRelation>> GetByTypeAsync(string relationType);
        Task<KnowledgeRelation> UpdateAsync(KnowledgeRelation relation);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByEntitiesAsync(int fromEntityId, int toEntityId, string? relationType = null);
    }
}
