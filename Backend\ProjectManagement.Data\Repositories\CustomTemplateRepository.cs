using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 自定义模板仓储实现
    /// </summary>
    public class CustomTemplateRepository : BaseRepository<CustomUIAutoMationTemplate>, ICustomTemplateRepository
    {
        public CustomTemplateRepository(ISqlSugarClient db, ILogger<BaseRepository<CustomUIAutoMationTemplate>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 分页查询模板
        /// </summary>
        public async Task<PagedResultDto<CustomUIAutoMationTemplate>> GetPagedAsync(CustomTemplateQueryDto query)
        {
            var queryable = _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                queryable = queryable.Where(x => x.Name.Contains(query.Keyword) ||
                                               x.Description!.Contains(query.Keyword));
            }

            // 分类过滤
            if (!string.IsNullOrEmpty(query.Category))
            {
                queryable = queryable.Where(x => x.Category == query.Category);
            }

            // 标签过滤
            if (query.Tags != null && query.Tags.Count > 0)
            {
                foreach (var tag in query.Tags)
                {
                    queryable = queryable.Where(x => x.Tags!.Contains($"\"{tag}\""));
                }
            }

            // 创建者过滤
            if (query.CreatedBy.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedBy == query.CreatedBy.Value);
            }

            // 创建时间范围过滤
            if (query.CreatedTimeStart.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedTime >= query.CreatedTimeStart.Value);
            }
            if (query.CreatedTimeEnd.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedTime <= query.CreatedTimeEnd.Value);
            }

            // 排序
            var sortBy = query.SortBy ?? "CreatedTime";
            var sortDirection = query.SortDirection ?? "desc";

            queryable = sortDirection.ToLower() == "asc"
                ? queryable.OrderBy(sortBy)
                : queryable.OrderBy($"{sortBy} desc");

            // 分页
            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .ToPageListAsync(query.Page, query.PageSize);

            return new PagedResultDto<CustomUIAutoMationTemplate>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 根据分类获取模板
        /// </summary>
        public async Task<List<CustomUIAutoMationTemplate>> GetByCategoryAsync(string category)
        {
            return await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted && x.Category == category)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 根据标签获取模板
        /// </summary>
        public async Task<List<CustomUIAutoMationTemplate>> GetByTagsAsync(List<string> tags)
        {
            var queryable = _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted);

            foreach (var tag in tags)
            {
                queryable = queryable.Where(x => x.Tags!.Contains($"\"{tag}\""));
            }

            return await queryable.OrderBy(x => x.Name).ToListAsync();
        }

        /// <summary>
        /// 搜索模板
        /// </summary>
        public async Task<List<CustomUIAutoMationTemplate>> SearchAsync(string keyword)
        {
            return await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted &&
                           (x.Name.Contains(keyword) || x.Description!.Contains(keyword)))
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        public async Task<List<string>> GetCategoriesAsync()
        {
            return await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted)
                .GroupBy(x => x.Category)
                .Select(x => x.Category)
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有标签
        /// </summary>
        public async Task<List<string>> GetTagsAsync()
        {
            var templates = await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted && !string.IsNullOrEmpty(x.Tags))
                .Select(x => x.Tags)
                .ToListAsync();

            var allTags = new HashSet<string>();
            foreach (var tagJson in templates)
            {
                try
                {
                    var tags = System.Text.Json.JsonSerializer.Deserialize<List<string>>(tagJson!);
                    if (tags != null)
                    {
                        foreach (var tag in tags)
                        {
                            allTags.Add(tag);
                        }
                    }
                }
                catch
                {
                    // 忽略JSON解析错误
                }
            }

            return allTags.OrderBy(x => x).ToList();
        }

        /// <summary>
        /// 增加使用次数
        /// </summary>
        public async Task<bool> IncrementUsageCountAsync(int id)
        {
            return await _db.Updateable<CustomUIAutoMationTemplate>()
                .SetColumns(x => x.UsageCount == x.UsageCount + 1)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 更新最后使用时间
        /// </summary>
        public async Task<bool> UpdateLastUsedTimeAsync(int id, DateTime lastUsedTime)
        {
            return await _db.Updateable<CustomUIAutoMationTemplate>()
                .SetColumns(x => x.LastUsedTime == lastUsedTime)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 获取最常用的模板
        /// </summary>
        public async Task<List<CustomUIAutoMationTemplate>> GetMostUsedAsync(int count = 10)
        {
            return await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.UsageCount, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 检查模板名称是否存在
        /// </summary>
        public async Task<bool> ExistsNameAsync(string name, int? excludeId = null)
        {
            var queryable = _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted && x.Name == name);

            if (excludeId.HasValue)
            {
                queryable = queryable.Where(x => x.Id != excludeId.Value);
            }

            return await queryable.AnyAsync();
        }

        /// <summary>
        /// 批量删除模板
        /// </summary>
        public async Task<bool> BatchDeleteAsync(List<int> ids)
        {
            return await _db.Updateable<CustomUIAutoMationTemplate>()
                .SetColumns(x => x.IsDeleted == true)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => ids.Contains(x.Id))
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 获取模板统计信息
        /// </summary>
        public async Task<TemplateStatisticsDto> GetStatisticsAsync()
        {
            var totalTemplates = await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted)
                .CountAsync();

            var totalSequences = await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted)
                .CountAsync();

            var totalExecutions = await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .CountAsync();

            var successfulExecutions = await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .Where(x => x.Status == "Completed")
                .CountAsync();

            var successRate = totalExecutions > 0 ? (decimal)successfulExecutions / totalExecutions : 0;

            var categoryStats = await _db.Queryable<CustomUIAutoMationTemplate>()
                .Where(x => !x.IsDeleted)
                .GroupBy(x => x.Category)
                .Select(x => new { Category = x.Category, Count = SqlFunc.AggregateCount(x.Id) })
                .ToListAsync();

            var recentExecutions = await _db.Queryable<UIAutoMationTemplateExecutionLog>()
                .OrderBy(x => x.StartTime, OrderByType.Desc)
                .Take(10)
                .ToListAsync();

            var mostUsedTemplates = await GetMostUsedAsync(5);

            var mostUsedSequences = await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.UsageCount, OrderByType.Desc)
                .Take(5)
                .ToListAsync();

            return new TemplateStatisticsDto
            {
                TotalTemplates = totalTemplates,
                TotalSequences = totalSequences,
                TotalExecutions = totalExecutions,
                SuccessRate = successRate,
                CategoryStats = categoryStats.ToDictionary(x => x.Category, x => x.Count),
                // 注意：这里需要转换为DTO，暂时留空，在Service层处理
                RecentExecutions = new List<ExecutionLogDto>(),
                MostUsedTemplates = new List<CustomTemplateDto>(),
                MostUsedSequences = new List<TemplateSequenceDto>()
            };
        }
    }
}
