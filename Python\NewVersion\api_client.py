#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端模块
与后端API进行通信
"""

import requests
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
import time
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用 SSL 警告（仅用于开发环境）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class APIClient:
    """API客户端类"""

    def __init__(self, base_url: str = "https://localhost:61136"):
        """
        初始化API客户端

        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.timeout = 30

        # 配置 HTTPS 和重试策略
        self.setup_session()

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'UI-Automation-Client/2.0'
        })

        # 认证信息
        self.token = None
        self.is_authenticated = False

        # 加载配置
        self.load_config()

        # 自动登录
        self.auto_login()

    def setup_session(self):
        """配置会话设置"""
        # 禁用 SSL 验证（仅用于开发环境的自签名证书）
        self.session.verify = False

        # 配置重试策略
        try:
            # 尝试使用新版本的参数名
            retry_strategy = Retry(
                total=3,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"],
                backoff_factor=1
            )
        except TypeError:
            # 如果失败，使用旧版本的参数名
            retry_strategy = Retry(
                total=3,
                status_forcelist=[429, 500, 502, 503, 504],
                method_whitelist=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"],
                backoff_factor=1
            )

        # 配置适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def load_config(self):
        """加载配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 加载 API 设置
                    api_settings = config.get('api_settings', {})
                    self.base_url = api_settings.get('base_url', self.base_url)
                    self.timeout = api_settings.get('timeout', self.timeout)

                    # 加载认证信息
                    self.username = config.get('username', '')
                    self.password = config.get('password', '')

                    # 加载其他设置
                    self.api_url = config.get('api_url', self.base_url)
                    self.api_timeout = config.get('api_timeout', self.timeout)
                    self.api_retries = config.get('api_retries', 3)
        except Exception as e:
            print(f"加载配置失败: {e}")
            # 设置默认值
            self.username = ''
            self.password = ''

    def auto_login(self):
        """自动登录"""
        if hasattr(self, 'username') and hasattr(self, 'password') and self.username and self.password:
            print(f"尝试自动登录用户: {self.username}")
            if self.login(self.username, self.password):
                print("✅ 自动登录成功")
            else:
                print("❌ 自动登录失败")

    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'base_url': self.base_url,
            'timeout': self.timeout,
            'is_authenticated': self.is_authenticated
        }

    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            # 尝试多个可能的健康检查端点
            endpoints = [
                "/api/health",
                "/health",
                "/api/status",
                "/status",
                "/"  # 根路径
            ]

            for endpoint in endpoints:
                try:
                    response = self.session.get(
                        f"{self.base_url}{endpoint}",
                        timeout=5
                    )
                    if response.status_code in [200, 404]:  # 404 也表示服务器在运行
                        return True
                except:
                    continue

            return False
        except Exception as e:
            print(f"连接测试失败: {e}")
            return False

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应数据或None
        """
        try:
            url = f"{self.base_url}{endpoint}"

            # 添加认证头
            if self.token:
                kwargs.setdefault('headers', {})['Authorization'] = f"Bearer {self.token}"

            # 设置超时
            kwargs.setdefault('timeout', self.timeout)

            # 发送请求
            response = self.session.request(method, url, **kwargs)

            # 检查响应状态
            if response.status_code == 401:
                self.is_authenticated = False
                self.token = None
                raise Exception("认证失败，请重新登录")

            response.raise_for_status()

            # 返回JSON数据
            if response.content:
                return response.json()
            else:
                return {}

        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            return None
        except Exception as e:
            print(f"请求处理错误: {e}")
            return None

    # 模板管理API
    def get_templates(self, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取模板列表"""
        params = {
            'pageNumber': page,
            'pageSize': page_size,
            **filters
        }
        return self._make_request('GET', '/api/custom-templates', params=params)

    def get_template(self, template_id: int) -> Optional[Dict]:
        """获取模板详情"""
        return self._make_request('GET', f'/api/custom-templates/{template_id}')

    def get_custom_template(self, template_id: int) -> Optional[Dict]:
        """获取自定义UI自动化模板详情"""
        return self._make_request('GET', f'/api/custom-templates/{template_id}')

    def create_template(self, template_data: Dict) -> Optional[Dict]:
        """创建模板"""
        return self._make_request('POST', '/api/custom-templates', json=template_data)

    def update_template(self, template_id: int, template_data: Dict) -> Optional[Dict]:
        """更新模板"""
        return self._make_request('PUT', f'/api/custom-templates/{template_id}', json=template_data)

    def delete_template(self, template_id: int) -> bool:
        """删除模板"""
        result = self._make_request('DELETE', f'/api/custom-templates/{template_id}')
        return result is not None

    # 序列管理API
    def get_sequences(self, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取序列列表"""
        params = {
            'pageNumber': page,
            'pageSize': page_size,
            **filters
        }
        return self._make_request('GET', '/api/template-sequences', params=params)

    def get_sequence(self, sequence_id: int) -> Optional[Dict]:
        """获取序列详情"""
        return self._make_request('GET', f'/api/template-sequences/{sequence_id}')

    def create_sequence(self, sequence_data: Dict) -> Optional[Dict]:
        """创建序列"""
        return self._make_request('POST', '/api/template-sequences', json=sequence_data)

    def update_sequence(self, sequence_id: int, sequence_data: Dict) -> Optional[Dict]:
        """更新序列"""
        return self._make_request('PUT', f'/api/template-sequences/{sequence_id}', json=sequence_data)

    def delete_sequence(self, sequence_id: int) -> bool:
        """删除序列"""
        result = self._make_request('DELETE', f'/api/template-sequences/{sequence_id}')
        return result is not None

    def execute_sequence(self, sequence_id: int, parameters: Dict = None) -> Optional[Dict]:
        """执行序列"""
        data = {'parameters': parameters or {}}
        return self._make_request('POST', f'/api/template-sequences/{sequence_id}/execute', json=data)

    def get_sequence_steps(self, sequence_id: int) -> Optional[List[Dict]]:
        """获取序列步骤"""
        return self._make_request('GET', f'/api/template-sequences/{sequence_id}/steps')

    def reorder_steps(self, sequence_id: int, step_ids: List[int]) -> bool:
        """调整步骤顺序"""
        data = {'stepIds': step_ids}
        result = self._make_request('PUT', f'/api/template-sequences/{sequence_id}/steps/reorder', json=data)
        return result is not None

    # 步骤管理API
    def get_step(self, step_id: int) -> Optional[Dict]:
        """获取步骤详情"""
        return self._make_request('GET', f'/api/template-steps/{step_id}')

    def create_step(self, step_data: Dict) -> Optional[Dict]:
        """创建步骤"""
        return self._make_request('POST', '/api/template-steps', json=step_data)

    def update_step(self, step_id: int, step_data: Dict) -> Optional[Dict]:
        """更新步骤"""
        return self._make_request('PUT', f'/api/template-steps/{step_id}', json=step_data)

    def delete_step(self, step_id: int) -> bool:
        """删除步骤"""
        result = self._make_request('DELETE', f'/api/template-steps/{step_id}')
        return result is not None

    # 执行日志API
    def get_execution_logs(self, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取执行日志"""
        params = {
            'pageNumber': page,
            'pageSize': page_size,
            **filters
        }
        return self._make_request('GET', '/api/template-execution-logs', params=params)

    def get_execution_log(self, log_id: int) -> Optional[Dict]:
        """获取执行日志详情"""
        return self._make_request('GET', f'/api/template-execution-logs/{log_id}')

    def get_execution_statistics(self, **filters) -> Optional[Dict]:
        """获取执行统计"""
        return self._make_request('GET', '/api/template-execution-logs/statistics', params=filters)

    # 认证API
    def login(self, username: str, password: str) -> bool:
        """用户登录"""
        try:
            data = {
                'username': username,
                'password': password
            }

            # 直接发送登录请求，不使用 _make_request 避免添加认证头
            url = f"{self.base_url}/api/Auth/login"
            response = self.session.post(url, json=data, timeout=self.timeout)

            print(f"登录请求: {url}")
            print(f"登录数据: {data}")
            print(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"登录响应: {result}")

                if result and ('token' in result or 'accessToken' in result):
                    # 支持两种 token 字段名
                    self.token = result.get('token') or result.get('accessToken')
                    self.is_authenticated = True
                    print("✅ 登录成功，已获取 token")
                    return True
                else:
                    print("❌ 响应中没有 token 或 accessToken")
                    return False
            else:
                print(f"❌ 登录失败，状态码: {response.status_code}")
                if response.content:
                    print(f"错误信息: {response.text}")
                return False

        except Exception as e:
            print(f"登录异常: {e}")
            return False

    def logout(self):
        """用户登出"""
        self.token = None
        self.is_authenticated = False

    # 工具方法
    def upload_file(self, file_path: str, endpoint: str) -> Optional[Dict]:
        """上传文件"""
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                # 移除Content-Type头，让requests自动设置
                headers = dict(self.session.headers)
                if 'Content-Type' in headers:
                    del headers['Content-Type']

                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    files=files,
                    headers=headers,
                    timeout=self.timeout
                )

                response.raise_for_status()
                return response.json() if response.content else {}

        except Exception as e:
            print(f"文件上传失败: {e}")
            return None

    def download_file(self, endpoint: str, save_path: str) -> bool:
        """下载文件"""
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                timeout=self.timeout,
                stream=True
            )

            response.raise_for_status()

            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return True

        except Exception as e:
            print(f"文件下载失败: {e}")
            return False

    # 开发步骤API
    def get_development_steps(self, project_id: int, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取项目的开发步骤列表"""
        if not project_id:
            print("错误: 必须提供项目ID")
            return None

        params = {
            'pageIndex': page,  # 注意：后端使用pageIndex而不是pageNumber
            'pageSize': page_size,
            **filters
        }
        result = self._make_request('GET', f'/api/RequirementDecomposition/steps/{project_id}', params=params)
        return result

    def get_development_step(self, step_id: int) -> Optional[Dict]:
        """获取开发步骤详情"""
        return self._make_request('GET', f'/api/DevelopmentSteps/{step_id}')

    def start_step_execution(self, step_id: int, executor_type: str = "Python_Automation") -> Optional[Dict]:
        """开始执行开发步骤"""
        data = {'executorType': executor_type}
        return self._make_request('POST', f'/api/DevelopmentSteps/{step_id}/execute', json=data)

    def update_development_step(self, step_id: int, **kwargs) -> Optional[Dict]:
        """更新开发步骤

        Args:
            step_id: 步骤ID
            **kwargs: 更新字段，支持：
                - stepName: 步骤名称
                - stepDescription: 步骤描述
                - priority: 优先级
                - status: 状态
                - estimatedHours: 预计工时
                - actualHours: 实际工时
                - technologyStack: 技术栈
                - componentType: 组件类型
                - filePath: 文件路径
                - aiPrompt: AI提示词
                - progress: 进度 (0-100)
                - referenceImages: 参考图片
        """
        # 过滤掉None值
        data = {k: v for k, v in kwargs.items() if v is not None}
        return self._make_request('PUT', f'/api/DevelopmentSteps/{step_id}', json=data)

    def complete_step_execution(self, execution_id: str, result: str, generated_code: str = None, output_files: str = None) -> bool:
        """完成步骤执行 - 注意：后端可能没有这个API"""
        # 由于后端没有独立的StepExecutions控制器，这里暂时返回True
        # 实际的完成逻辑可能需要通过其他方式实现
        print(f"警告：complete_step_execution API不存在，执行ID: {execution_id}")
        return True

    def get_step_execution_history(self, step_id: int) -> Optional[List[Dict]]:
        """获取步骤执行历史"""
        return self._make_request('GET', f'/api/DevelopmentSteps/{step_id}/executions')

    def get_all_step_execution_history(self, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取所有步骤执行历史（分页）- 注意：后端可能没有这个API，返回空结果"""
        # 由于后端没有统一的StepExecutions API，这里返回空结果
        # 实际的执行历史需要通过具体步骤ID获取
        print("警告：get_all_step_execution_history API不存在，返回空结果")
        return {'items': [], 'totalCount': 0, 'pageIndex': page, 'pageSize': page_size}

    def get_pending_step_executions(self, executor_type: str = "Python_Automation") -> Optional[List[Dict]]:
        """获取待处理的步骤执行任务"""
        params = {'executorType': executor_type, 'status': 'Running'}
        result = self.get_step_execution_history(**params)
        return result.get('items', []) if result else []

    def get_step_template_sequences(self, step_id: int) -> Optional[List[Dict]]:
        """获取步骤关联的模板序列"""
        return self._make_request('GET', f'/api/DevelopmentSteps/{step_id}/template-sequences')

    # 编码任务API
    def get_coding_tasks(self, project_id: int, page: int = 1, page_size: int = 20, **filters) -> Optional[Dict]:
        """获取项目的编码任务列表"""
        if not project_id:
            print("错误: 必须提供项目ID")
            return None

        params = {
            'pageIndex': page,
            'pageSize': page_size,
            **filters
        }
        result = self._make_request('GET', f'/api/CodingTask/project/{project_id}', params=params)
        return result

    def get_coding_task(self, task_id: int) -> Optional[Dict]:
        """获取编码任务详情"""
        return self._make_request('GET', f'/api/CodingTask/{task_id}')

    def update_coding_task(self, task_id: int, task_data: Dict) -> Optional[Dict]:
        """更新编码任务"""
        return self._make_request('PUT', f'/api/CodingTask/{task_id}', json=task_data)

    def start_coding_task(self, task_id: int) -> Optional[Dict]:
        """开始编码任务 - 更新ActualStartTime和状态"""
        from datetime import datetime

        print(f"🚀 开始编码任务，任务ID: {task_id}")

        # 先获取当前任务信息
        current_task = self.get_coding_task(task_id)
        if not current_task or not current_task.get('success'):
            print(f"❌ 获取任务信息失败: {current_task}")
            return None

        task_data = current_task['data']
        print(f"📋 当前任务数据: {task_data.get('taskName', '未知')}")

        # 更新开始时间和状态
        now = datetime.now()
        task_data['actualStartTime'] = now.isoformat()
        task_data['status'] = 'InProgress'

        print(f"⏰ 设置开始时间: {task_data['actualStartTime']}")
        print(f"📊 设置状态: {task_data['status']}")

        result = self.update_coding_task(task_id, task_data)
        print(f"✅ 更新结果: {result}")
        return result

    def complete_coding_task(self, task_id: int) -> Optional[Dict]:
        """完成编码任务 - 更新ActualEndTime和状态"""
        from datetime import datetime

        print(f"🏁 完成编码任务，任务ID: {task_id}")

        # 先获取当前任务信息
        current_task = self.get_coding_task(task_id)
        if not current_task or not current_task.get('success'):
            print(f"❌ 获取任务信息失败: {current_task}")
            return None

        task_data = current_task['data']
        print(f"📋 当前任务数据: {task_data.get('taskName', '未知')}")

        # 更新结束时间和状态
        now = datetime.now()
        task_data['actualEndTime'] = now.isoformat()
        task_data['status'] = 'Completed'
        task_data['completedDate'] = now.isoformat()

        print(f"⏰ 设置结束时间: {task_data['actualEndTime']}")
        print(f"📊 设置状态: {task_data['status']}")
        print(f"📅 设置完成日期: {task_data['completedDate']}")

        result = self.update_coding_task(task_id, task_data)
        print(f"✅ 更新结果: {result}")
        return result

    def get_coding_task_steps(self, task_id: int) -> Optional[List[Dict]]:
        """获取编码任务的步骤列表"""
        if not task_id:
            print("错误: 必须提供任务ID")
            return None

        result = self._make_request('GET', f'/api/CodingTask/{task_id}/steps')
        return result

    def start_automation_operation(self, project_id: int, current_task_id: int) -> Optional[Dict]:
        """开始自动化操作 - 更新项目中所有任务状态"""
        print(f"🚀 开始自动化操作，项目ID: {project_id}, 当前任务ID: {current_task_id}")

        data = {
            'projectId': project_id,
            'currentTaskId': current_task_id
        }

        result = self._make_request('POST', '/api/CodingTask/start-automation', json=data)

        if result and result.get('success'):
            data = result.get('data', {})
            print(f"✅ 自动化操作已开始")
            print(f"📊 项目ID: {data.get('projectId')}")
            print(f"📋 当前任务ID: {data.get('currentTaskId')}")
            print(f"🔄 更新任务数: {data.get('updatedCount')}")
            print(f"📈 总任务数: {data.get('totalTasks')}")

            # 显示更新详情
            update_results = data.get('updateResults', [])
            for update in update_results:
                status_icon = "🟢" if update.get('isCurrent') else "🟡"
                print(f"  {status_icon} {update.get('taskName')}: {update.get('oldStatus')} → {update.get('newStatus')}")
        else:
            error_msg = result.get('message', '未知错误') if result else '网络请求失败'
            print(f"❌ 开始自动化操作失败: {error_msg}")

        return result

    def update_coding_task_step_flags(self, task_id: int, step_id: int, is_finish_coding: bool = None, is_fix_error: bool = None, status: str = None) -> Optional[Dict]:
        """更新编码任务步骤状态标志"""
        print(f"🏷️ 更新编码任务步骤状态标志，任务ID: {task_id}, 步骤ID: {step_id}")

        data = {}
        if is_finish_coding is not None:
            data['isFinishCoding'] = is_finish_coding
        if is_fix_error is not None:
            data['isFixError'] = is_fix_error
        if status is not None:
            data['status'] = status

        if not data:
            print("⚠️ 没有提供要更新的标志")
            return None

        result = self._make_request('PATCH', f'/api/CodingTask/{task_id}/steps/{step_id}/flags', json=data)

        if result and result.get('success'):
            flags_data = result.get('data', {})
            print(f"✅ 步骤状态标志更新成功")
            print(f"📋 任务步骤ID: {flags_data.get('taskStepId')}")
            print(f"📋 任务ID: {flags_data.get('taskId')}")
            print(f"📋 步骤ID: {flags_data.get('stepId')}")
            print(f"📊 状态: {flags_data.get('status')}")
            print(f"✅ 完成编码: {flags_data.get('isFinishCoding')}")
            print(f"🔧 修复错误: {flags_data.get('isFixError')}")
        else:
            error_msg = result.get('message', '未知错误') if result else '网络请求失败'
            print(f"❌ 更新步骤状态标志失败: {error_msg}")

        return result

    def get_sequence_details(self, sequence_id: int) -> Optional[Dict]:
        """获取序列详情（包含步骤）"""
        return self._make_request('GET', f'/api/template-sequences/{sequence_id}')

    def execute_sequence_by_id(self, sequence_id: int, parameters: Dict = None) -> Optional[Dict]:
        """执行模板序列（通过序列ID）"""
        data = {'parameters': parameters or {}}
        return self._make_request('POST', f'/api/template-sequences/{sequence_id}/execute', json=data)

    def update_step_execution_status(self, execution_id: str, status: str, result: str = None, error_message: str = None) -> bool:
        """更新步骤执行状态 - 注意：后端可能没有这个API"""
        # 由于后端没有独立的StepExecutions控制器，这里暂时返回True
        print(f"警告：update_step_execution_status API不存在，执行ID: {execution_id}, 状态: {status}")
        return True

    def complete_step_execution_with_details(self, execution_id: str, result: str, generated_code: str = None,
                                           output_files: str = None, execution_log: str = None) -> bool:
        """完成步骤执行并提供详细信息 - 注意：后端可能没有这个API"""
        # 由于后端没有独立的StepExecutions控制器，这里暂时返回True
        print(f"警告：complete_step_execution_with_details API不存在，执行ID: {execution_id}, 结果: {result}")
        return True

    def get_automation_tasks(self, client_id: str, task_types: List[str] = None, max_count: int = 10) -> Optional[List[Dict]]:
        """获取自动化任务队列"""
        params = {
            'clientId': client_id,
            'maxCount': max_count
        }
        if task_types:
            params['taskTypes'] = task_types
        return self._make_request('GET', '/api/AutomationTasks/pending', params=params)

    def update_automation_task_status(self, task_id: int, status: str, result: str = None, error_message: str = None) -> bool:
        """更新自动化任务状态"""
        data = {
            'status': status,
            'result': result,
            'errorMessage': error_message
        }
        result = self._make_request('PUT', f'/api/AutomationTasks/{task_id}/status', json=data)
        return result is not None

    def upload_step_image(self, step_id: int, image_path: str) -> Optional[Dict]:
        """上传开发步骤图片"""
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                response = self.session.post(
                    f"{self.base_url}/api/DevelopmentSteps/{step_id}/upload-image",
                    files=files,
                    headers={'Authorization': f"Bearer {self.token}"} if self.token else {},
                    timeout=self.timeout
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"上传图片失败: {e}")
            return None

    # 编译错误API
    def get_compilation_errors(self, project_type: str = None, severity: str = None,
                             project_id: int = None, session_id: str = None,
                             page: int = 1, page_size: int = 50, errors_only: bool = False) -> Optional[Dict]:
        """获取编译错误列表"""
        params = {
            'page': page,
            'pageSize': page_size,
            'errorsOnly': errors_only
        }

        if project_type:
            params['projectType'] = project_type
        if severity:
            params['severity'] = severity
        if project_id:
            params['projectId'] = project_id
        if session_id:
            params['sessionId'] = session_id

        return self._make_request('GET', '/api/CompilationErrors', params=params)

    def get_all_compilation_errors(self) -> Optional[List[Dict]]:
        """获取所有编译错误（兼容Python客户端的简化API）"""
        return self._make_request('GET', '/api/CompilationErrors/all')

    def get_compilation_error_stats(self, project_type: str = None, project_id: int = None) -> Optional[Dict]:
        """获取编译错误统计信息"""
        params = {}
        if project_type:
            params['projectType'] = project_type
        if project_id:
            params['projectId'] = project_id

        return self._make_request('GET', '/api/CompilationErrors/stats', params=params)

    def add_compilation_errors(self, errors: List[Dict]) -> Optional[Dict]:
        """批量添加编译错误"""
        return self._make_request('POST', '/api/CompilationErrors/batch', json=errors)

    def clear_compilation_errors(self, project_type: str = None, project_id: int = None) -> Optional[Dict]:
        """清空编译错误"""
        params = {}
        if project_type:
            params['projectType'] = project_type
        if project_id:
            params['projectId'] = project_id

        return self._make_request('DELETE', '/api/CompilationErrors/clear', params=params)

    def get_recent_compilation_sessions(self, project_type: str = None, limit: int = 10) -> Optional[List[str]]:
        """获取最新的编译会话ID列表"""
        params = {'limit': limit}
        if project_type:
            params['projectType'] = project_type

        return self._make_request('GET', '/api/CompilationErrors/sessions', params=params)

    # 项目API
    def get_projects(self, page: int = 1, page_size: int = 20) -> Optional[Dict]:
        """获取项目列表"""
        params = {
            'pageNumber': page,
            'pageSize': page_size
        }
        return self._make_request('GET', '/api/Projects', params=params)
