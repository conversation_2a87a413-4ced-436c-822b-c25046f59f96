-- =============================================
-- 编码任务相关表创建和字段添加脚本
-- 版本: 1.0
-- 创建日期: 2025-06-27
-- 说明: 添加编码任务相关的表和字段
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始执行编码任务表创建和字段添加脚本...';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '==============================================';

-- =============================================
-- 1. 创建编码任务表 (CodingTasks)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CodingTasks')
BEGIN
    PRINT '创建编码任务表 (CodingTasks)...';
    
    CREATE TABLE CodingTasks (
        Id int IDENTITY(1,1) NOT NULL,
        ProjectId int NOT NULL,
        TaskName nvarchar(200) NOT NULL,
        Description nvarchar(1000) NULL,
        Status nvarchar(50) NOT NULL DEFAULT 'NotStarted',
        Priority nvarchar(20) NOT NULL DEFAULT 'Medium',
        AssignedTo int NULL,
        EstimatedHours decimal(10,2) NULL,
        ActualHours decimal(10,2) NULL,
        StartDate datetime2 NULL,
        DueDate datetime2 NULL,
        CompletedDate datetime2 NULL,
        ActualStartTime datetime2 NULL,
        ActualEndTime datetime2 NULL,
        TechnologyStack nvarchar(max) NULL,
        Tags nvarchar(500) NULL,
        Notes nvarchar(2000) NULL,
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_CodingTasks PRIMARY KEY (Id),
        CONSTRAINT FK_CodingTasks_Projects FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
        CONSTRAINT FK_CodingTasks_AssignedUser FOREIGN KEY (AssignedTo) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTasks_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTasks_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTasks_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id)
    );
    
    -- 创建索引
    CREATE INDEX IX_CodingTasks_ProjectId ON CodingTasks(ProjectId);
    CREATE INDEX IX_CodingTasks_Status ON CodingTasks(Status);
    CREATE INDEX IX_CodingTasks_Priority ON CodingTasks(Priority);
    CREATE INDEX IX_CodingTasks_AssignedTo ON CodingTasks(AssignedTo);
    CREATE INDEX IX_CodingTasks_DueDate ON CodingTasks(DueDate);
    CREATE INDEX IX_CodingTasks_CreatedTime ON CodingTasks(CreatedTime);
    CREATE INDEX IX_CodingTasks_IsDeleted ON CodingTasks(IsDeleted);
    
    PRINT '编码任务表创建完成';
END
ELSE
BEGIN
    PRINT '编码任务表已存在，检查并添加缺失字段...';
    
    -- 检查并添加缺失的字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTasks') AND name = 'ActualStartTime')
    BEGIN
        ALTER TABLE CodingTasks ADD ActualStartTime datetime2 NULL;
        PRINT '添加字段: ActualStartTime';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTasks') AND name = 'ActualEndTime')
    BEGIN
        ALTER TABLE CodingTasks ADD ActualEndTime datetime2 NULL;
        PRINT '添加字段: ActualEndTime';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTasks') AND name = 'DeletedTime')
    BEGIN
        ALTER TABLE CodingTasks ADD DeletedTime datetime2 NULL;
        PRINT '添加字段: DeletedTime';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTasks') AND name = 'DeletedBy')
    BEGIN
        ALTER TABLE CodingTasks ADD DeletedBy int NULL;
        PRINT '添加字段: DeletedBy';
        
        -- 添加外键约束
        IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_CodingTasks_DeletedBy')
        BEGIN
            ALTER TABLE CodingTasks ADD CONSTRAINT FK_CodingTasks_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id);
        END
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTasks') AND name = 'Remarks')
    BEGIN
        ALTER TABLE CodingTasks ADD Remarks nvarchar(500) NULL;
        PRINT '添加字段: Remarks';
    END
END

-- =============================================
-- 2. 创建编码任务步骤关联表 (CodingTaskSteps)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CodingTaskSteps')
BEGIN
    PRINT '创建编码任务步骤关联表 (CodingTaskSteps)...';
    
    CREATE TABLE CodingTaskSteps (
        Id int IDENTITY(1,1) NOT NULL,
        CodingTaskId int NOT NULL,
        DevelopmentStepId int NOT NULL,
        OrderIndex int NOT NULL DEFAULT 0,
        Status nvarchar(50) NOT NULL DEFAULT 'NotStarted',
        StartTime datetime2 NULL,
        CompletedTime datetime2 NULL,
        ActualHours decimal(10,2) NULL,
        ExecutionResult nvarchar(2000) NULL,
        Notes nvarchar(1000) NULL,
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_CodingTaskSteps PRIMARY KEY (Id),
        CONSTRAINT FK_CodingTaskSteps_CodingTask FOREIGN KEY (CodingTaskId) REFERENCES CodingTasks(Id),
        CONSTRAINT FK_CodingTaskSteps_DevelopmentStep FOREIGN KEY (DevelopmentStepId) REFERENCES DevelopmentSteps(Id),
        CONSTRAINT FK_CodingTaskSteps_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTaskSteps_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTaskSteps_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id)
    );
    
    -- 创建索引
    CREATE INDEX IX_CodingTaskSteps_CodingTaskId ON CodingTaskSteps(CodingTaskId);
    CREATE INDEX IX_CodingTaskSteps_DevelopmentStepId ON CodingTaskSteps(DevelopmentStepId);
    CREATE INDEX IX_CodingTaskSteps_Status ON CodingTaskSteps(Status);
    CREATE INDEX IX_CodingTaskSteps_OrderIndex ON CodingTaskSteps(OrderIndex);
    CREATE INDEX IX_CodingTaskSteps_IsDeleted ON CodingTaskSteps(IsDeleted);
    
    -- 创建唯一约束（同一个编码任务中不能有重复的开发步骤）
    CREATE UNIQUE INDEX IX_CodingTaskSteps_Unique ON CodingTaskSteps(CodingTaskId, DevelopmentStepId) WHERE IsDeleted = 0;
    
    PRINT '编码任务步骤关联表创建完成';
END
ELSE
BEGIN
    PRINT '编码任务步骤关联表已存在';
END

-- =============================================
-- 3. 创建编码任务执行日志表 (CodingTaskExecutionLogs)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CodingTaskExecutionLogs')
BEGIN
    PRINT '创建编码任务执行日志表 (CodingTaskExecutionLogs)...';
    
    CREATE TABLE CodingTaskExecutionLogs (
        Id int IDENTITY(1,1) NOT NULL,
        CodingTaskId int NOT NULL,
        ExecutionType nvarchar(50) NOT NULL,
        Content nvarchar(2000) NULL,
        Result nvarchar(2000) NULL,
        ErrorMessage nvarchar(2000) NULL,
        ExecutionTime datetime2 NOT NULL DEFAULT GETDATE(),
        ExecutedBy int NULL,
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_CodingTaskExecutionLogs PRIMARY KEY (Id),
        CONSTRAINT FK_CodingTaskExecutionLogs_CodingTask FOREIGN KEY (CodingTaskId) REFERENCES CodingTasks(Id),
        CONSTRAINT FK_CodingTaskExecutionLogs_ExecutedBy FOREIGN KEY (ExecutedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTaskExecutionLogs_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTaskExecutionLogs_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CodingTaskExecutionLogs_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id)
    );
    
    -- 创建索引
    CREATE INDEX IX_CodingTaskExecutionLogs_CodingTaskId ON CodingTaskExecutionLogs(CodingTaskId);
    CREATE INDEX IX_CodingTaskExecutionLogs_ExecutionType ON CodingTaskExecutionLogs(ExecutionType);
    CREATE INDEX IX_CodingTaskExecutionLogs_ExecutionTime ON CodingTaskExecutionLogs(ExecutionTime);
    CREATE INDEX IX_CodingTaskExecutionLogs_ExecutedBy ON CodingTaskExecutionLogs(ExecutedBy);
    CREATE INDEX IX_CodingTaskExecutionLogs_IsDeleted ON CodingTaskExecutionLogs(IsDeleted);
    
    PRINT '编码任务执行日志表创建完成';
END
ELSE
BEGIN
    PRINT '编码任务执行日志表已存在';
END

-- =============================================
-- 4. 插入一些示例数据（可选）
-- =============================================
PRINT '检查是否需要插入示例数据...';

-- 检查是否有项目数据
IF EXISTS (SELECT TOP 1 1 FROM Projects WHERE IsDeleted = 0)
BEGIN
    DECLARE @ProjectId INT = (SELECT TOP 1 Id FROM Projects WHERE IsDeleted = 0 ORDER BY Id);
    DECLARE @AdminUserId INT = (SELECT TOP 1 Id FROM Users WHERE Username = 'admin');
    
    -- 检查是否已有编码任务数据
    IF NOT EXISTS (SELECT TOP 1 1 FROM CodingTasks WHERE IsDeleted = 0)
    BEGIN
        PRINT '插入示例编码任务数据...';
        
        INSERT INTO CodingTasks (ProjectId, TaskName, Description, Status, Priority, AssignedTo, EstimatedHours, CreatedBy, CreatedTime)
        VALUES 
        (@ProjectId, '用户管理模块开发', '实现用户注册、登录、权限管理等功能', 'InProgress', 'High', @AdminUserId, 40, @AdminUserId, GETDATE()),
        (@ProjectId, '商品管理API开发', '开发商品增删改查相关接口', 'NotStarted', 'Medium', NULL, 24, @AdminUserId, GETDATE()),
        (@ProjectId, '订单处理系统', '实现订单创建、支付、发货等流程', 'NotStarted', 'High', NULL, 60, @AdminUserId, GETDATE());
        
        PRINT '示例编码任务数据插入完成';
    END
    ELSE
    BEGIN
        PRINT '编码任务数据已存在，跳过示例数据插入';
    END
END
ELSE
BEGIN
    PRINT '没有找到项目数据，跳过示例数据插入';
END

PRINT '==============================================';
PRINT '编码任务表创建和字段添加脚本执行完成！';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '==============================================';

GO
