-- =============================================
-- 添加开发步骤相关数据表
-- 功能: 支持需求分解成开发步骤的功能
-- 创建日期: 2025-06-21
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. 创建开发步骤主表 (DevelopmentSteps)
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DevelopmentSteps]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[DevelopmentSteps] (
        -- 主键和基础字段
        [Id] INT IDENTITY(1,1) NOT NULL,

        -- 关联字段
        [ProjectId] INT NOT NULL,
        [RequirementDocumentId] INT NULL,
        [ParentStepId] INT NULL,

        -- 步骤基本信息
        [StepName] NVARCHAR(200) NOT NULL,
        [StepDescription] NVARCHAR(MAX) NULL,
        [StepType] NVARCHAR(50) NOT NULL DEFAULT 'Development',
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        [EstimatedHours] DECIMAL(10,2) NULL,
        [ActualHours] DECIMAL(10,2) NULL,

        -- 技术相关
        [TechnologyStack] NVARCHAR(100) NULL,
        [FileType] NVARCHAR(50) NULL,
        [FilePath] NVARCHAR(500) NULL,
        [ComponentType] NVARCHAR(50) NULL,

        -- AI生成相关
        [AIPrompt] NVARCHAR(MAX) NULL,
        [AIGeneratedCode] NVARCHAR(MAX) NULL,
        [AIProvider] NVARCHAR(50) NULL,
        [AIModel] NVARCHAR(50) NULL,

        -- 状态管理
        [Status] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
        [Progress] INT NOT NULL DEFAULT 0,
        [StartTime] DATETIME2 NULL,
        [EndTime] DATETIME2 NULL,
        [CompletedTime] DATETIME2 NULL,

        -- 执行结果
        [ExecutionResult] NVARCHAR(20) NULL,
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [OutputPath] NVARCHAR(500) NULL,
        [GeneratedFiles] NVARCHAR(MAX) NULL,

        -- 排序和分组
        [StepOrder] INT NOT NULL DEFAULT 0,
        [StepGroup] NVARCHAR(100) NULL,
        [StepLevel] INT NOT NULL DEFAULT 1,

        -- BaseEntity 审计字段
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] DATETIME2 NULL,
        [CreatedBy] INT NULL,
        [UpdatedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [DeletedTime] DATETIME2 NULL,
        [DeletedBy] INT NULL,
        [Version] INT NOT NULL DEFAULT 1,
        [Remarks] NVARCHAR(500) NULL,

        -- 主键约束
        CONSTRAINT [PK_DevelopmentSteps] PRIMARY KEY CLUSTERED ([Id] ASC),

        -- 外键约束
        CONSTRAINT [FK_DevelopmentSteps_Projects] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[Projects] ([Id]),
        CONSTRAINT [FK_DevelopmentSteps_RequirementDocuments] FOREIGN KEY ([RequirementDocumentId]) REFERENCES [dbo].[RequirementDocuments] ([Id]),
        CONSTRAINT [FK_DevelopmentSteps_ParentStep] FOREIGN KEY ([ParentStepId]) REFERENCES [dbo].[DevelopmentSteps] ([Id]),
        CONSTRAINT [FK_DevelopmentSteps_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_DevelopmentSteps_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_DevelopmentSteps_DeletedBy] FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users] ([Id])
    );

    PRINT '✓ DevelopmentSteps 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠ DevelopmentSteps 表已存在，跳过创建';
END
GO

-- =============================================
-- 2. 创建步骤依赖关系表 (StepDependencies)
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[StepDependencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[StepDependencies] (
        -- 主键
        [Id] INT IDENTITY(1,1) NOT NULL,

        -- 依赖关系
        [StepId] INT NOT NULL,
        [DependsOnStepId] INT NOT NULL,
        [DependencyType] NVARCHAR(20) NOT NULL DEFAULT 'Sequential',
        [IsRequired] BIT NOT NULL DEFAULT 1,
        [Description] NVARCHAR(500) NULL,

        -- BaseEntity 审计字段
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] DATETIME2 NULL,
        [CreatedBy] INT NULL,
        [UpdatedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [DeletedTime] DATETIME2 NULL,
        [DeletedBy] INT NULL,
        [Version] INT NOT NULL DEFAULT 1,
        [Remarks] NVARCHAR(500) NULL,

        -- 主键约束
        CONSTRAINT [PK_StepDependencies] PRIMARY KEY CLUSTERED ([Id] ASC),

        -- 外键约束
        CONSTRAINT [FK_StepDependencies_Step] FOREIGN KEY ([StepId]) REFERENCES [dbo].[DevelopmentSteps] ([Id]),
        CONSTRAINT [FK_StepDependencies_DependsOnStep] FOREIGN KEY ([DependsOnStepId]) REFERENCES [dbo].[DevelopmentSteps] ([Id]),
        CONSTRAINT [FK_StepDependencies_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_StepDependencies_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_StepDependencies_DeletedBy] FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users] ([Id]),

        -- 唯一约束（防止重复依赖）
        CONSTRAINT [UK_StepDependencies_Unique] UNIQUE ([StepId], [DependsOnStepId])
    );

    PRINT '✓ StepDependencies 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠ StepDependencies 表已存在，跳过创建';
END
GO

-- =============================================
-- 3. 创建步骤执行历史表 (StepExecutionHistory)
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[StepExecutionHistory]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[StepExecutionHistory] (
        -- 主键
        [Id] INT IDENTITY(1,1) NOT NULL,

        -- 关联步骤
        [StepId] INT NOT NULL,
        [ExecutionId] NVARCHAR(50) NOT NULL,

        -- 执行信息
        [ExecutionStartTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ExecutionEndTime] DATETIME2 NULL,
        [ExecutionDuration] INT NULL,
        [ExecutionStatus] NVARCHAR(20) NOT NULL DEFAULT 'Running',
        [ExecutionResult] NVARCHAR(20) NULL,

        -- 执行详情
        [AIProvider] NVARCHAR(50) NULL,
        [AIModel] NVARCHAR(50) NULL,
        [PromptUsed] NVARCHAR(MAX) NULL,
        [GeneratedCode] NVARCHAR(MAX) NULL,
        [OutputFiles] NVARCHAR(MAX) NULL,
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [ExecutionLog] NVARCHAR(MAX) NULL,

        -- 执行环境
        [ExecutorType] NVARCHAR(20) NOT NULL DEFAULT 'Manual',
        [ExecutorInfo] NVARCHAR(500) NULL,
        [VSCodeVersion] NVARCHAR(50) NULL,
        [PluginVersion] NVARCHAR(50) NULL,

        -- BaseEntity 审计字段
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] DATETIME2 NULL,
        [CreatedBy] INT NULL,
        [UpdatedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [DeletedTime] DATETIME2 NULL,
        [DeletedBy] INT NULL,
        [Version] INT NOT NULL DEFAULT 1,
        [Remarks] NVARCHAR(500) NULL,

        -- 主键约束
        CONSTRAINT [PK_StepExecutionHistory] PRIMARY KEY CLUSTERED ([Id] ASC),

        -- 外键约束
        CONSTRAINT [FK_StepExecutionHistory_Step] FOREIGN KEY ([StepId]) REFERENCES [dbo].[DevelopmentSteps] ([Id]),
        CONSTRAINT [FK_StepExecutionHistory_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_StepExecutionHistory_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_StepExecutionHistory_DeletedBy] FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users] ([Id])
    );

    PRINT '✓ StepExecutionHistory 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠ StepExecutionHistory 表已存在，跳过创建';
END
GO

-- =============================================
-- 4. 创建索引优化查询性能
-- =============================================

-- DevelopmentSteps 表索引
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_ProjectId] ON [dbo].[DevelopmentSteps] ([ProjectId]) INCLUDE ([Status], [Priority], [StepOrder]);
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_RequirementDocumentId] ON [dbo].[DevelopmentSteps] ([RequirementDocumentId]) WHERE [RequirementDocumentId] IS NOT NULL;
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_Status] ON [dbo].[DevelopmentSteps] ([Status], [Priority]);
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_ParentStepId] ON [dbo].[DevelopmentSteps] ([ParentStepId]) WHERE [ParentStepId] IS NOT NULL;
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_StepOrder] ON [dbo].[DevelopmentSteps] ([ProjectId], [StepOrder]);
CREATE NONCLUSTERED INDEX [IX_DevelopmentSteps_IsDeleted] ON [dbo].[DevelopmentSteps] ([IsDeleted]) WHERE [IsDeleted] = 0;

-- StepDependencies 表索引
CREATE NONCLUSTERED INDEX [IX_StepDependencies_StepId] ON [dbo].[StepDependencies] ([StepId]);
CREATE NONCLUSTERED INDEX [IX_StepDependencies_DependsOnStepId] ON [dbo].[StepDependencies] ([DependsOnStepId]);
CREATE NONCLUSTERED INDEX [IX_StepDependencies_IsDeleted] ON [dbo].[StepDependencies] ([IsDeleted]) WHERE [IsDeleted] = 0;

-- StepExecutionHistory 表索引
CREATE NONCLUSTERED INDEX [IX_StepExecutionHistory_StepId] ON [dbo].[StepExecutionHistory] ([StepId], [ExecutionStartTime] DESC);
CREATE NONCLUSTERED INDEX [IX_StepExecutionHistory_ExecutionId] ON [dbo].[StepExecutionHistory] ([ExecutionId]);
CREATE NONCLUSTERED INDEX [IX_StepExecutionHistory_Status] ON [dbo].[StepExecutionHistory] ([ExecutionStatus]);

PRINT '✓ 所有索引创建成功';
GO

-- =============================================
-- 5. 创建视图便于数据查询
-- =============================================

-- 开发步骤概览视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_DevelopmentStepOverview]'))
    DROP VIEW [dbo].[vw_DevelopmentStepOverview];
GO

CREATE VIEW [dbo].[vw_DevelopmentStepOverview]
AS
SELECT
    ds.[Id],
    ds.[ProjectId],
    p.[Name] AS ProjectName,
    ds.[RequirementDocumentId],
    rd.[Title] AS RequirementTitle,
    ds.[StepName],
    ds.[StepDescription],
    ds.[StepType],
    ds.[Priority],
    ds.[Status],
    ds.[Progress],
    ds.[EstimatedHours],
    ds.[ActualHours],
    ds.[TechnologyStack],
    ds.[ComponentType],
    ds.[StepOrder],
    ds.[StepGroup],
    ds.[StepLevel],
    ds.[ParentStepId],
    parent.[StepName] AS ParentStepName,
    ds.[StartTime],
    ds.[EndTime],
    ds.[CompletedTime],
    ds.[CreatedTime],
    ds.[CreatedBy],
    creator.[Username] AS CreatorName,
    -- 依赖统计
    (SELECT COUNT(*) FROM [dbo].[StepDependencies] sd WHERE sd.[StepId] = ds.[Id] AND sd.[IsDeleted] = 0) AS DependencyCount,
    (SELECT COUNT(*) FROM [dbo].[StepDependencies] sd WHERE sd.[DependsOnStepId] = ds.[Id] AND sd.[IsDeleted] = 0) AS DependentCount,
    -- 子步骤统计
    (SELECT COUNT(*) FROM [dbo].[DevelopmentSteps] child WHERE child.[ParentStepId] = ds.[Id] AND child.[IsDeleted] = 0) AS ChildStepCount,
    -- 执行历史统计
    (SELECT COUNT(*) FROM [dbo].[StepExecutionHistory] seh WHERE seh.[StepId] = ds.[Id] AND seh.[IsDeleted] = 0) AS ExecutionCount
FROM [dbo].[DevelopmentSteps] ds
LEFT JOIN [dbo].[Projects] p ON ds.[ProjectId] = p.[Id]
LEFT JOIN [dbo].[RequirementDocuments] rd ON ds.[RequirementDocumentId] = rd.[Id]
LEFT JOIN [dbo].[DevelopmentSteps] parent ON ds.[ParentStepId] = parent.[Id]
LEFT JOIN [dbo].[Users] creator ON ds.[CreatedBy] = creator.[Id]
WHERE ds.[IsDeleted] = 0;
GO

-- 项目开发步骤统计视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_ProjectStepStatistics]'))
    DROP VIEW [dbo].[vw_ProjectStepStatistics];
GO

CREATE VIEW [dbo].[vw_ProjectStepStatistics]
AS
SELECT
    p.[Id] AS ProjectId,
    p.[Name] AS ProjectName,
    COUNT(ds.[Id]) AS TotalSteps,
    SUM(CASE WHEN ds.[Status] = 'Pending' THEN 1 ELSE 0 END) AS PendingSteps,
    SUM(CASE WHEN ds.[Status] = 'InProgress' THEN 1 ELSE 0 END) AS InProgressSteps,
    SUM(CASE WHEN ds.[Status] = 'Completed' THEN 1 ELSE 0 END) AS CompletedSteps,
    SUM(CASE WHEN ds.[Status] = 'Failed' THEN 1 ELSE 0 END) AS FailedSteps,
    SUM(CASE WHEN ds.[Status] = 'Blocked' THEN 1 ELSE 0 END) AS BlockedSteps,
    ROUND(AVG(CAST(ds.[Progress] AS FLOAT)), 2) AS AverageProgress,
    SUM(ISNULL(ds.[EstimatedHours], 0)) AS TotalEstimatedHours,
    SUM(ISNULL(ds.[ActualHours], 0)) AS TotalActualHours,
    COUNT(DISTINCT ds.[TechnologyStack]) AS TechnologyStackCount,
    MIN(ds.[CreatedTime]) AS FirstStepCreated,
    MAX(ds.[UpdatedTime]) AS LastStepUpdated
FROM [dbo].[Projects] p
LEFT JOIN [dbo].[DevelopmentSteps] ds ON p.[Id] = ds.[ProjectId] AND ds.[IsDeleted] = 0
GROUP BY p.[Id], p.[Name];
GO

PRINT '✓ 所有视图创建成功';
GO

-- =============================================
-- 6. 完成脚本
-- =============================================

PRINT '✓ 开发步骤相关表结构创建完成！';
PRINT '';
PRINT '包含以下3个新数据表：';
PRINT '1. DevelopmentSteps - 开发步骤主表';
PRINT '2. StepDependencies - 步骤依赖关系表';
PRINT '3. StepExecutionHistory - 步骤执行历史表';
PRINT '';
PRINT '包含以下2个视图：';
PRINT '1. vw_DevelopmentStepOverview - 开发步骤概览视图';
PRINT '2. vw_ProjectStepStatistics - 项目步骤统计视图';
PRINT '';
PRINT '✅ 数据库迁移脚本执行完成！';
GO