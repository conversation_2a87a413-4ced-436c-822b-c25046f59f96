using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 模板执行日志控制器
    /// </summary>
    [ApiController]
    [Route("api/template-execution-logs")]
    [Authorize]
    public class TemplateExecutionLogController : ControllerBase
    {
        private readonly IExecutionLogRepository _executionLogRepository;
        private readonly ILogger<TemplateExecutionLogController> _logger;

        public TemplateExecutionLogController(
            IExecutionLogRepository executionLogRepository,
            ILogger<TemplateExecutionLogController> logger)
        {
            _executionLogRepository = executionLogRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取执行日志列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResultDto<ExecutionLogDto>>> GetExecutionLogs([FromQuery] ExecutionLogQueryDto query)
        {
            try
            {
                var result = await _executionLogRepository.GetPagedAsync(query);

                var dtoResult = new PagedResultDto<ExecutionLogDto>
                {
                    Items = result.Items.Select(MapToDto).ToList(),
                    TotalCount = result.TotalCount,
                    PageNumber = result.PageNumber,
                    PageSize = result.PageSize
                };

                return Ok(dtoResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取执行日志列表失败");
                return StatusCode(500, "获取执行日志列表失败");
            }
        }

        /// <summary>
        /// 获取执行日志详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ExecutionLogDto>> GetExecutionLog(int id)
        {
            try
            {
                var log = await _executionLogRepository.GetByIdAsync(id);
                if (log == null || log.IsDeleted)
                {
                    return NotFound("执行日志不存在");
                }

                return Ok(MapToDto(log));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取执行日志详情失败，ID: {Id}", id);
                return StatusCode(500, "获取执行日志详情失败");
            }
        }

        /// <summary>
        /// 获取执行统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<ExecutionStatisticsDto>> GetExecutionStatistics([FromQuery] ExecutionStatisticsQueryDto query)
        {
            try
            {
                // 暂时返回模拟数据，后续实现具体统计逻辑
                var statistics = new ExecutionStatisticsDto
                {
                    TotalExecutions = 0,
                    SuccessfulExecutions = 0,
                    FailedExecutions = 0,
                    SuccessRate = 0,
                    AverageExecutionTime = 0,
                    RecentExecutions = new List<ExecutionLogDto>()
                };
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取执行统计信息失败");
                return StatusCode(500, "获取执行统计信息失败");
            }
        }

        #region 私有方法

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static ExecutionLogDto MapToDto(Core.Entities.UIAutoMationTemplateExecutionLog log)
        {
            return new ExecutionLogDto
            {
                Id = log.Id,
                SequenceId = log.SequenceId,
                TemplateId = log.TemplateId,
                StepId = log.StepId,
                ExecutionType = log.ExecutionType,
                Status = log.Status,
                StartTime = log.StartTime,
                EndTime = log.EndTime,
                Duration = log.Duration,
                Result = log.Result,
                ErrorMessage = log.ErrorMessage,
                ScreenshotPath = log.ScreenshotPath,
                ExecutedBy = log.ExecutedBy,
                CreatedTime = log.CreatedTime
            };
        }

        #endregion
    }

    /// <summary>
    /// 执行统计查询DTO
    /// </summary>
    public class ExecutionStatisticsQueryDto
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 序列ID
        /// </summary>
        public int? SequenceId { get; set; }

        /// <summary>
        /// 模板ID
        /// </summary>
        public int? TemplateId { get; set; }
    }

    /// <summary>
    /// 执行统计DTO
    /// </summary>
    public class ExecutionStatisticsDto
    {
        /// <summary>
        /// 总执行次数
        /// </summary>
        public int TotalExecutions { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessfulExecutions { get; set; }

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailedExecutions { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 平均执行时间
        /// </summary>
        public int AverageExecutionTime { get; set; }

        /// <summary>
        /// 最近执行记录
        /// </summary>
        public List<ExecutionLogDto> RecentExecutions { get; set; } = new List<ExecutionLogDto>();
    }
}
