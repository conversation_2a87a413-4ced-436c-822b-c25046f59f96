using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// UI操作类型仓储接口
    /// </summary>
    public interface IUIActionTypeRepository : IRepository<UIActionType>
    {
        /// <summary>
        /// 分页查询UI操作类型
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResultDto<UIActionType>> GetPagedAsync(UIActionTypeQueryDto query);

        /// <summary>
        /// 根据值获取UI操作类型
        /// </summary>
        /// <param name="value">UI操作类型值</param>
        /// <returns>UI操作类型</returns>
        Task<UIActionType?> GetByValueAsync(string value);

        /// <summary>
        /// 获取启用的UI操作类型
        /// </summary>
        /// <returns>UI操作类型列表</returns>
        Task<List<UIActionType>> GetActiveAsync();

        /// <summary>
        /// 检查UI操作类型值是否已存在
        /// </summary>
        /// <param name="value">UI操作类型值</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsValueAsync(string value, int? excludeId = null);

        /// <summary>
        /// 获取最大排序顺序
        /// </summary>
        /// <returns>最大排序顺序</returns>
        Task<int> GetMaxSortOrderAsync();

        /// <summary>
        /// 批量更新排序顺序
        /// </summary>
        /// <param name="sortOrders">排序信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders);

        /// <summary>
        /// 启用/禁用UI操作类型
        /// </summary>
        /// <param name="id">UI操作类型ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>是否成功</returns>
        Task<bool> SetActiveStatusAsync(int id, bool isActive);

        /// <summary>
        /// 获取内置UI操作类型
        /// </summary>
        /// <returns>内置UI操作类型列表</returns>
        Task<List<UIActionType>> GetBuiltInAsync();

        /// <summary>
        /// 获取自定义UI操作类型
        /// </summary>
        /// <returns>自定义UI操作类型列表</returns>
        Task<List<UIActionType>> GetCustomAsync();
    }
}
