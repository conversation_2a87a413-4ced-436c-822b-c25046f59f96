using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Text.Json;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 模板序列控制器
    /// </summary>
    [ApiController]
    [Route("api/template-sequences")]
    [Authorize]
    public class TemplateSequenceController : ControllerBase
    {
        private readonly ITemplateSequenceRepository _sequenceRepository;
        private readonly ITemplateStepRepository _stepRepository;
        private readonly IExecutionLogRepository _executionLogRepository;
        private readonly ILogger<TemplateSequenceController> _logger;

        public TemplateSequenceController(
            ITemplateSequenceRepository sequenceRepository,
            ITemplateStepRepository stepRepository,
            IExecutionLogRepository executionLogRepository,
            ILogger<TemplateSequenceController> logger)
        {
            _sequenceRepository = sequenceRepository;
            _stepRepository = stepRepository;
            _executionLogRepository = executionLogRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取序列列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResultDto<TemplateSequenceDto>>> GetSequences([FromQuery] TemplateSequenceQueryDto query)
        {
            try
            {
                var result = await _sequenceRepository.GetPagedAsync(query);

                var dtoResult = new PagedResultDto<TemplateSequenceDto>
                {
                    Items = result.Items.Select(MapToDto).ToList(),
                    TotalCount = result.TotalCount,
                    PageNumber = result.PageNumber,
                    PageSize = result.PageSize
                };

                return Ok(dtoResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取序列列表失败");
                return StatusCode(500, "获取序列列表失败");
            }
        }

        /// <summary>
        /// 获取序列详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<TemplateSequenceDto>> GetSequence(int id)
        {
            try
            {
                var sequence = await _sequenceRepository.GetWithStepsAsync(id);
                if (sequence == null || sequence.IsDeleted)
                {
                    return NotFound("序列不存在");
                }

                return Ok(MapToDto(sequence));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取序列详情失败，ID: {Id}", id);
                return StatusCode(500, "获取序列详情失败");
            }
        }

        /// <summary>
        /// 创建序列
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<TemplateSequenceDto>> CreateSequence([FromBody] CreateTemplateSequenceDto dto)
        {
            try
            {
                // 检查序列名称是否已存在
                var existingSequence = await _sequenceRepository.GetByNameAsync(dto.Name);
                if (existingSequence != null && !existingSequence.IsDeleted)
                {
                    return BadRequest($"序列名称 '{dto.Name}' 已存在，请使用其他名称");
                }

                var sequence = new UIAutoMationTemplateSequence
                {
                    Name = dto.Name,
                    Description = dto.Description,
                    Category = dto.Category,
                    Tags = string.Join(",", dto.Tags ?? new List<string>()),
                    Notes = dto.Notes,
                    IsActive = dto.IsActive,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    CreatedBy = GetCurrentUserId(),
                    IsDeleted = false,
                    UsageCount = 0
                };

                var result = await _sequenceRepository.AddAsync(sequence);

                // 如果有步骤，创建步骤
                if (dto.Steps?.Any() == true)
                {
                    foreach (var stepDto in dto.Steps)
                    {
                        var step = new UIAutoMationTemplateStep
                        {
                            SequenceId = result.Id,
                            TemplateId = stepDto.TemplateId,
                            StepOrder = stepDto.StepOrder,
                            ActionType = stepDto.ActionType,
                            Description = stepDto.Description,
                            Parameters = System.Text.Json.JsonSerializer.Serialize(stepDto.Parameters ?? new object()),
                            TimeoutSeconds = stepDto.TimeoutSeconds,
                            MaxRetries = stepDto.MaxRetries,
                            IsActive = stepDto.IsActive,
                            // 流程控制字段
                            ConditionExpression = stepDto.ConditionExpression,
                            JumpToStepId = stepDto.JumpToStepId,
                            LoopCount = stepDto.LoopCount,
                            LoopVariable = stepDto.LoopVariable,
                            GroupId = stepDto.GroupId,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                            IsDeleted = false
                        };

                        await _stepRepository.AddAsync(step);
                    }
                }

                // 生成序列JSON
                await UpdateSequenceJsonAsync(result.Id);

                _logger.LogInformation("创建序列成功，ID: {Id}, 名称: {Name}", result.Id, dto.Name);

                // 重新获取包含JSON的序列数据
                var createdSequence = await _sequenceRepository.GetWithStepsAsync(result.Id);
                return CreatedAtAction(nameof(GetSequence), new { id = result.Id }, MapToDto(createdSequence));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建序列失败");
                return StatusCode(500, "创建序列失败");
            }
        }

        /// <summary>
        /// 更新序列
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<TemplateSequenceDto>> UpdateSequence(int id, [FromBody] UpdateTemplateSequenceDto dto)
        {
            try
            {
                var sequence = await _sequenceRepository.GetByIdAsync(id);
                if (sequence == null || sequence.IsDeleted)
                {
                    return NotFound("序列不存在");
                }

                // 更新基本信息
                sequence.Name = dto.Name ?? sequence.Name;
                sequence.Description = dto.Description ?? sequence.Description;
                sequence.Category = dto.Category ?? sequence.Category;
                sequence.Tags = dto.Tags != null ? string.Join(",", dto.Tags) : sequence.Tags;
                sequence.Notes = dto.Notes ?? sequence.Notes;
                sequence.IsActive = dto.IsActive ?? sequence.IsActive;
                sequence.UpdatedTime = DateTime.Now;

                await _sequenceRepository.UpdateAsync(sequence);

                // 如果提供了步骤数据，更新步骤
                if (dto.Steps != null)
                {
                    await UpdateSequenceStepsAsync(id, dto.Steps);
                }

                // 更新序列JSON
                await UpdateSequenceJsonAsync(id);

                // 重新获取完整的序列信息（包含步骤和JSON）
                var updatedSequence = await _sequenceRepository.GetWithStepsAsync(id);

                _logger.LogInformation("更新序列成功，ID: {Id}, 名称: {Name}, 步骤数: {StepCount}",
                    id, dto.Name, dto.Steps?.Count ?? 0);
                return Ok(MapToDto(updatedSequence));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新序列失败，ID: {Id}", id);
                return StatusCode(500, "更新序列失败");
            }
        }

        /// <summary>
        /// 删除序列
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteSequence(int id)
        {
            try
            {
                var sequence = await _sequenceRepository.GetByIdAsync(id);
                if (sequence == null || sequence.IsDeleted)
                {
                    return NotFound("序列不存在");
                }

                // 软删除序列
                sequence.IsDeleted = true;
                sequence.UpdatedTime = DateTime.Now;
                await _sequenceRepository.UpdateAsync(sequence);

                // 软删除相关步骤
                var steps = await _stepRepository.GetBySequenceIdAsync(id);
                foreach (var step in steps)
                {
                    step.IsDeleted = true;
                    step.UpdatedTime = DateTime.Now;
                    await _stepRepository.UpdateAsync(step);
                }

                _logger.LogInformation("删除序列成功，ID: {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除序列失败，ID: {Id}", id);
                return StatusCode(500, "删除序列失败");
            }
        }

        /// <summary>
        /// 执行序列
        /// </summary>
        [HttpPost("{id}/execute")]
        public async Task<ActionResult<SequenceExecutionResultDto>> ExecuteSequence(int id, [FromBody] ExecuteSequenceDto dto)
        {
            try
            {
                var sequence = await _sequenceRepository.GetWithStepsAsync(id);
                if (sequence == null || sequence.IsDeleted)
                {
                    return NotFound("序列不存在");
                }

                if (!sequence.IsActive)
                {
                    return BadRequest("序列已禁用，无法执行");
                }

                if (sequence.Steps?.Any() != true)
                {
                    return BadRequest("序列没有配置步骤，无法执行");
                }

                // 创建执行日志
                var executionLog = new UIAutoMationTemplateExecutionLog
                {
                    SequenceId = id,
                    ExecutionType = "Sequence",
                    Status = "Started",
                    StartTime = DateTime.Now,
                    ExecutedBy = GetCurrentUserId()?.ToString() ?? "Unknown",
                    CreatedTime = DateTime.Now,
                    IsDeleted = false
                };

                var logResult = await _executionLogRepository.AddAsync(executionLog);
                var logId = logResult.Id;

                // 这里应该调用实际的序列执行服务
                // 暂时返回模拟结果
                _logger.LogInformation("序列执行已启动，序列ID: {SequenceId}, 执行ID: {ExecutionId}", id, logId);

                return Ok(new SequenceExecutionResultDto
                {
                    ExecutionId = logId,
                    Status = "Started",
                    StartTime = DateTime.Now,
                    Message = "序列执行已启动"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行序列失败，ID: {Id}", id);
                return StatusCode(500, "执行序列失败");
            }
        }

        /// <summary>
        /// 获取序列的步骤列表
        /// </summary>
        [HttpGet("{id}/steps")]
        public async Task<ActionResult<List<TemplateStepDto>>> GetSequenceSteps(int id)
        {
            try
            {
                var steps = await _stepRepository.GetBySequenceIdAsync(id);
                var stepDtos = steps.Select(MapStepToDto).ToList();

                return Ok(stepDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取序列步骤失败，序列ID: {SequenceId}", id);
                return StatusCode(500, "获取序列步骤失败");
            }
        }

        /// <summary>
        /// 调整步骤顺序
        /// </summary>
        [HttpPut("{id}/steps/reorder")]
        public async Task<ActionResult> ReorderSteps(int id, [FromBody] ReorderStepsDto dto)
        {
            try
            {
                await _stepRepository.ReorderStepsAsync(id, dto.StepIds);
                _logger.LogInformation("调整步骤顺序成功，序列ID: {SequenceId}", id);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调整步骤顺序失败，序列ID: {SequenceId}", id);
                return StatusCode(500, "调整步骤顺序失败");
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        /// <summary>
        /// 更新序列步骤（智能更新，避免重复插入）
        /// </summary>
        private async Task UpdateSequenceStepsAsync(int sequenceId, List<CreateTemplateStepDto> newSteps)
        {
            _logger.LogInformation("开始更新序列步骤，序列ID: {SequenceId}, 新步骤数量: {NewStepCount}", sequenceId, newSteps.Count);

            // 获取现有步骤
            var existingSteps = await _stepRepository.GetBySequenceIdAsync(sequenceId);
            var existingStepDict = existingSteps.ToDictionary(s => s.Id);

            _logger.LogInformation("现有步骤数量: {ExistingStepCount}, 步骤IDs: [{ExistingStepIds}]",
                existingSteps.Count, string.Join(", ", existingSteps.Select(s => s.Id)));

            // 处理前端传来的步骤（可能包含ID）
            var stepsToUpdate = new List<UIAutoMationTemplateStep>();
            var stepsToCreate = new List<CreateTemplateStepDto>();
            var existingStepIds = new HashSet<int>();

            foreach (var stepDto in newSteps)
            {
                // 检查步骤是否有ID（前端可能传递现有步骤的ID）
                var stepId = GetStepIdFromDto(stepDto);

                _logger.LogInformation("处理步骤: StepOrder={StepOrder}, 提取的ID={StepId}, ActionType={ActionType}",
                    stepDto.StepOrder, stepId, stepDto.ActionType);

                if (stepId.HasValue && existingStepDict.ContainsKey(stepId.Value))
                {
                    _logger.LogInformation("更新现有步骤: ID={StepId}, StepOrder={StepOrder}", stepId.Value, stepDto.StepOrder);

                    // 更新现有步骤
                    var existingStep = existingStepDict[stepId.Value];
                    existingStep.TemplateId = stepDto.TemplateId;
                    existingStep.StepOrder = stepDto.StepOrder;
                    existingStep.ActionType = stepDto.ActionType;
                    existingStep.Description = stepDto.Description;
                    existingStep.Parameters = System.Text.Json.JsonSerializer.Serialize(stepDto.Parameters ?? new object());
                    existingStep.TimeoutSeconds = stepDto.TimeoutSeconds;
                    existingStep.MaxRetries = stepDto.MaxRetries;
                    existingStep.IsActive = stepDto.IsActive;
                    // 更新流程控制字段
                    existingStep.ConditionExpression = stepDto.ConditionExpression;
                    existingStep.JumpToStepId = stepDto.JumpToStepId;
                    existingStep.LoopCount = stepDto.LoopCount;
                    existingStep.LoopVariable = stepDto.LoopVariable;
                    existingStep.GroupId = stepDto.GroupId;
                    existingStep.UpdatedTime = DateTime.Now;

                    stepsToUpdate.Add(existingStep);
                    existingStepIds.Add(stepId.Value);
                }
                else
                {
                    _logger.LogInformation("创建新步骤: StepOrder={StepOrder}, 原因: StepId={StepId}, 存在于字典={ExistsInDict}",
                        stepDto.StepOrder, stepId, stepId.HasValue ? existingStepDict.ContainsKey(stepId.Value) : false);

                    // 创建新步骤
                    stepsToCreate.Add(stepDto);
                }
            }

            _logger.LogInformation("处理结果: 更新步骤数={UpdateCount}, 创建步骤数={CreateCount}, 保留步骤IDs=[{RetainedIds}]",
                stepsToUpdate.Count, stepsToCreate.Count, string.Join(", ", existingStepIds));

            // 软删除不再需要的步骤
            var stepsToDelete = existingSteps.Where(s => !existingStepIds.Contains(s.Id)).ToList();
            _logger.LogInformation("需要删除的步骤数: {DeleteCount}, 删除步骤IDs: [{DeleteIds}]",
                stepsToDelete.Count, string.Join(", ", stepsToDelete.Select(s => s.Id)));

            foreach (var stepToDelete in stepsToDelete)
            {
                stepToDelete.IsDeleted = true;
                stepToDelete.UpdatedTime = DateTime.Now;
                await _stepRepository.UpdateAsync(stepToDelete);
            }

            // 更新现有步骤
            foreach (var stepToUpdate in stepsToUpdate)
            {
                await _stepRepository.UpdateAsync(stepToUpdate);
            }

            // 创建新步骤
            foreach (var stepDto in stepsToCreate)
            {
                var step = new UIAutoMationTemplateStep
                {
                    SequenceId = sequenceId,
                    TemplateId = stepDto.TemplateId,
                    StepOrder = stepDto.StepOrder,
                    ActionType = stepDto.ActionType,
                    Description = stepDto.Description,
                    Parameters = System.Text.Json.JsonSerializer.Serialize(stepDto.Parameters ?? new object()),
                    TimeoutSeconds = stepDto.TimeoutSeconds,
                    MaxRetries = stepDto.MaxRetries,
                    IsActive = stepDto.IsActive,
                    // 流程控制字段
                    ConditionExpression = stepDto.ConditionExpression,
                    JumpToStepId = stepDto.JumpToStepId,
                    LoopCount = stepDto.LoopCount,
                    LoopVariable = stepDto.LoopVariable,
                    GroupId = stepDto.GroupId,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    IsDeleted = false
                };

                await _stepRepository.AddAsync(step);
            }

            _logger.LogInformation("更新序列步骤完成，序列ID: {SequenceId}, 更新: {UpdateCount}, 创建: {CreateCount}, 删除: {DeleteCount}",
                sequenceId, stepsToUpdate.Count, stepsToCreate.Count, stepsToDelete.Count);

            // 更新序列的JSON数据
            await UpdateSequenceJsonAsync(sequenceId);
        }

        /// <summary>
        /// 从DTO中获取步骤ID（如果存在）
        /// </summary>
        private int? GetStepIdFromDto(CreateTemplateStepDto stepDto)
        {
            // 首先检查DTO的Id字段
            if (stepDto.Id.HasValue)
            {
                _logger.LogDebug("从DTO.Id字段获取步骤ID: {StepId}", stepDto.Id.Value);
                return stepDto.Id.Value;
            }

            // 兼容旧版本：检查参数中是否包含ID
            if (stepDto.Parameters?.ContainsKey("id") == true)
            {
                if (stepDto.Parameters["id"] is int id)
                {
                    _logger.LogDebug("从Parameters[id]获取步骤ID (int): {StepId}", id);
                    return id;
                }
                if (int.TryParse(stepDto.Parameters["id"]?.ToString(), out var parsedId))
                {
                    _logger.LogDebug("从Parameters[id]获取步骤ID (parsed): {StepId}", parsedId);
                    return parsedId;
                }
            }

            _logger.LogDebug("未找到步骤ID，StepOrder: {StepOrder}, ActionType: {ActionType}", stepDto.StepOrder, stepDto.ActionType);
            return null;
        }

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static TemplateSequenceDto MapToDto(UIAutoMationTemplateSequence sequence)
        {
            return new TemplateSequenceDto
            {
                Id = sequence.Id,
                Name = sequence.Name,
                Description = sequence.Description,
                Category = sequence.Category,
                Tags = string.IsNullOrEmpty(sequence.Tags) ? new List<string>() : sequence.Tags.Split(',').ToList(),
                Notes = sequence.Notes,
                UsageCount = sequence.UsageCount,
                LastUsedTime = sequence.LastUsedTime,
                IsActive = sequence.IsActive,
                Steps = sequence.Steps?.Select(MapStepToDto).ToList() ?? new List<TemplateStepDto>(),
                SequenceJson = sequence.SequenceJson, // 添加序列JSON字段
                CreatedTime = sequence.CreatedTime,
                UpdatedTime = sequence.UpdatedTime,
                CreatedBy = sequence.CreatedBy
            };
        }

        /// <summary>
        /// 映射步骤到DTO
        /// </summary>
        private static TemplateStepDto MapStepToDto(UIAutoMationTemplateStep step)
        {
            Dictionary<string, object> parameters;
            try
            {
                parameters = string.IsNullOrEmpty(step.Parameters)
                    ? new Dictionary<string, object>()
                    : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(step.Parameters) ?? new Dictionary<string, object>();
            }
            catch
            {
                parameters = new Dictionary<string, object>();
            }

            return new TemplateStepDto
            {
                Id = step.Id,
                SequenceId = step.SequenceId,
                TemplateId = step.TemplateId,
                StepOrder = step.StepOrder,
                ActionType = step.ActionType,
                Description = step.Description,
                Parameters = parameters,
                TimeoutSeconds = step.TimeoutSeconds,
                RetryCount = 0, // 当前重试次数，默认为0
                MaxRetries = step.MaxRetries,
                IsActive = step.IsActive,
                // 流程控制字段
                ConditionExpression = step.ConditionExpression,
                JumpToStepId = step.JumpToStepId,
                LoopCount = step.LoopCount,
                LoopVariable = step.LoopVariable,
                GroupId = step.GroupId,
                CreatedTime = step.CreatedTime,
                UpdatedTime = step.UpdatedTime
            };
        }

        /// <summary>
        /// 导出序列为JSON格式
        /// </summary>
        [HttpPost("export")]
        public async Task<ActionResult> ExportSequences([FromBody] ExportSequencesDto dto)
        {
            try
            {
                var sequences = new List<UIAutoMationTemplateSequence>();

                if (dto.SequenceIds?.Any() == true)
                {
                    // 导出指定的序列
                    foreach (var id in dto.SequenceIds)
                    {
                        var sequence = await _sequenceRepository.GetWithStepsAsync(id);
                        if (sequence != null && !sequence.IsDeleted)
                        {
                            sequences.Add(sequence);
                        }
                    }
                }
                else
                {
                    // 导出所有序列
                    var query = new TemplateSequenceQueryDto
                    {
                        Page = 1,
                        PageSize = int.MaxValue,
                        IsActive = true
                    };
                    var result = await _sequenceRepository.GetPagedAsync(query);
                    sequences = result.Items.ToList();

                    // 为每个序列加载步骤
                    foreach (var sequence in sequences)
                    {
                        sequence.Steps = await _stepRepository.GetBySequenceIdAsync(sequence.Id);
                    }
                }

                // 转换为导出格式
                var exportData = new
                {
                    ExportTime = DateTime.Now,
                    Version = "1.0",
                    TotalCount = sequences.Count,
                    Sequences = sequences.Select(s => new
                    {
                        s.Name,
                        s.Description,
                        s.Category,
                        Tags = string.IsNullOrEmpty(s.Tags) ? new List<string>() : s.Tags.Split(',').ToList(),
                        s.Notes,
                        s.IsActive,
                        Steps = s.Steps?.OrderBy(step => step.StepOrder).Select(step => new
                        {
                            step.StepOrder,
                            step.ActionType,
                            LogicType = step.LogicType, // 添加逻辑语句类型
                            step.Description,
                            Parameters = string.IsNullOrEmpty(step.Parameters)
                                ? new Dictionary<string, object>()
                                : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(step.Parameters),
                            step.TimeoutSeconds,
                            step.MaxRetries,
                            step.IsActive,
                            step.ConditionExpression,
                            step.JumpToStepId,
                            step.LoopCount,
                            step.LoopVariable,
                            step.GroupId
                        }).ToList()
                    }).ToList()
                };

                var json = System.Text.Json.JsonSerializer.Serialize(exportData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                var fileName = $"template_sequences_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var bytes = System.Text.Encoding.UTF8.GetBytes(json);

                return File(bytes, "application/json", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出序列失败");
                return StatusCode(500, "导出序列失败");
            }
        }

        /// <summary>
        /// 从JSON格式导入序列
        /// </summary>
        [HttpPost("import")]
        public async Task<ActionResult<ImportSequencesResultDto>> ImportSequences(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("请选择要导入的文件");
                }

                if (!file.FileName.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest("只支持JSON格式文件");
                }

                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream);
                var jsonContent = await reader.ReadToEndAsync();

                var importData = System.Text.Json.JsonSerializer.Deserialize<ImportSequencesDto>(jsonContent);
                if (importData?.Sequences == null)
                {
                    return BadRequest("文件格式不正确");
                }

                var result = new ImportSequencesResultDto
                {
                    TotalCount = importData.Sequences.Count,
                    SuccessCount = 0,
                    FailedCount = 0,
                    Errors = new List<string>()
                };

                var currentUserId = GetCurrentUserId();

                foreach (var sequenceData in importData.Sequences)
                {
                    try
                    {
                        // 检查序列名称是否已存在
                        var existingSequence = await _sequenceRepository.GetByNameAsync(sequenceData.Name);
                        if (existingSequence != null && !existingSequence.IsDeleted)
                        {
                            result.Errors.Add($"序列 '{sequenceData.Name}' 已存在，跳过导入");
                            result.FailedCount++;
                            continue;
                        }

                        // 创建序列
                        var sequence = new UIAutoMationTemplateSequence
                        {
                            Name = sequenceData.Name,
                            Description = sequenceData.Description,
                            Category = sequenceData.Category,
                            Tags = string.Join(",", sequenceData.Tags ?? new List<string>()),
                            Notes = sequenceData.Notes,
                            IsActive = sequenceData.IsActive,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                            CreatedBy = currentUserId,
                            IsDeleted = false,
                            UsageCount = 0
                        };

                        var createdSequence = await _sequenceRepository.AddAsync(sequence);

                        // 创建步骤
                        if (sequenceData.Steps?.Any() == true)
                        {
                            foreach (var stepData in sequenceData.Steps.OrderBy(s => s.StepOrder))
                            {
                                var step = new UIAutoMationTemplateStep
                                {
                                    SequenceId = createdSequence.Id,
                                    StepOrder = stepData.StepOrder,
                                    ActionType = stepData.ActionType,
                                    Description = stepData.Description,
                                    Parameters = System.Text.Json.JsonSerializer.Serialize(stepData.Parameters ?? new Dictionary<string, object>()),
                                    TimeoutSeconds = stepData.TimeoutSeconds,
                                    MaxRetries = stepData.MaxRetries,
                                    IsActive = stepData.IsActive,
                                    ConditionExpression = stepData.ConditionExpression,
                                    JumpToStepId = stepData.JumpToStepId,
                                    LoopCount = stepData.LoopCount,
                                    LoopVariable = stepData.LoopVariable,
                                    GroupId = stepData.GroupId,
                                    CreatedTime = DateTime.Now,
                                    UpdatedTime = DateTime.Now,
                                    IsDeleted = false
                                };

                                await _stepRepository.AddAsync(step);
                            }
                        }

                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "导入序列失败: {SequenceName}", sequenceData.Name);
                        result.Errors.Add($"导入序列 '{sequenceData.Name}' 失败: {ex.Message}");
                        result.FailedCount++;
                    }
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入序列失败");
                return StatusCode(500, "导入序列失败");
            }
        }

        /// <summary>
        /// 更新序列的JSON数据
        /// </summary>
        private async Task UpdateSequenceJsonAsync(int sequenceId)
        {
            try
            {
                // 获取序列及其步骤
                var sequence = await _sequenceRepository.GetWithStepsAsync(sequenceId);
                if (sequence == null) return;

                // 生成序列JSON
                var sequenceData = new
                {
                    sequence.Name,
                    sequence.Description,
                    sequence.Category,
                    Tags = string.IsNullOrEmpty(sequence.Tags) ? new List<string>() : sequence.Tags.Split(',').ToList(),
                    sequence.Notes,
                    sequence.IsActive,
                    Steps = sequence.Steps?.Where(s => !s.IsDeleted).OrderBy(step => step.StepOrder).Select(step => new
                    {
                        step.StepOrder,
                        step.ActionType,
                        LogicType = step.LogicType, // 添加逻辑语句类型
                        step.Description,
                        Parameters = string.IsNullOrEmpty(step.Parameters)
                            ? new Dictionary<string, object>()
                            : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(step.Parameters),
                        step.TimeoutSeconds,
                        step.MaxRetries,
                        step.IsActive,
                        step.ConditionExpression,
                        step.JumpToStepId,
                        step.LoopCount,
                        step.LoopVariable,
                        step.GroupId
                    }).ToList()
                };

                // 序列化为JSON
                var json = System.Text.Json.JsonSerializer.Serialize(sequenceData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                // 更新序列的JSON字段
                sequence.SequenceJson = json;
                sequence.UpdatedTime = DateTime.Now;
                await _sequenceRepository.UpdateAsync(sequence);

                _logger.LogInformation("已更新序列JSON，序列ID: {SequenceId}", sequenceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新序列JSON失败，序列ID: {SequenceId}", sequenceId);
            }
        }

        #endregion
    }
}
