<template>
  <el-card class="ai-assistant">
    <template #header>
      <div class="assistant-header">
        <span>🤖 AI测试助手</span>
        <el-button type="text" @click="toggleExpanded">
          <el-icon>
            <component :is="expanded ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
        </el-button>
      </div>
    </template>

    <div v-show="expanded" class="assistant-content">
      <!-- AI配置状态 -->
      <div v-if="!hasValidAIProvider" class="ai-config-warning">
        <el-alert
          :title="!isUserLoggedIn ? 'Please Login First' : 'AI配置未完成'"
          :type="!isUserLoggedIn ? 'error' : 'warning'"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="config-actions">
              <div v-if="!isUserLoggedIn">
                <p>请先登录系统才能使用AI功能</p>
                <el-button type="primary" size="small" @click="goToLogin">
                  <el-icon><User /></el-icon>
                  前往登录
                </el-button>
              </div>
              <div v-else>
                <p>请选择AI提供商以使用AI功能：</p>
                <el-select
                  v-model="selectedAIProvider"
                  placeholder="选择AI提供商"
                  style="width: 100%; margin: 8px 0;"
                  @change="onAIProviderChange"
                >
                  <el-option
                    v-for="provider in aiProviders"
                    :key="provider.id"
                    :label="`${provider.providerName || provider.modelName} - ${provider.modelName}`"
                    :value="provider.id"
                  />
                </el-select>
                <div class="action-buttons">
                  <el-button size="small" @click="loadAIProviders" :loading="loadingProviders">
                    <el-icon><Refresh /></el-icon>
                    刷新列表
                  </el-button>
                  <el-button size="small" type="primary" @click="createDefaultConfig" :loading="creatingConfig">
                    <el-icon><Plus /></el-icon>
                    创建默认配置
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <el-button-group>
          <el-button size="small" @click="analyzeCurrentTest" :loading="analyzing" :disabled="!hasValidAIProvider">
            <el-icon><Search /></el-icon>
            分析当前测试
          </el-button>
          <el-button size="small" @click="optimizeTest" :loading="optimizing" :disabled="!hasValidAIProvider">
            <el-icon><Tools /></el-icon>
            优化测试
          </el-button>
          <el-button size="small" @click="generateSimilar" :loading="generating" :disabled="!hasValidAIProvider">
            <el-icon><CopyDocument /></el-icon>
            生成相似测试
          </el-button>
        </el-button-group>
      </div>

      <!-- AI建议区域 -->
      <div class="suggestions-area" v-if="currentSuggestions.length > 0">
        <h4>💡 AI建议</h4>
        <div class="suggestions-list">
          <div 
            v-for="(suggestion, index) in currentSuggestions" 
            :key="index"
            class="suggestion-item"
            :class="suggestion.type"
          >
            <div class="suggestion-icon">
              <el-icon>
                <component :is="getSuggestionIcon(suggestion.type)" />
              </el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-description">{{ suggestion.description }}</div>
              <div class="suggestion-actions" v-if="suggestion.actions">
                <el-button 
                  v-for="action in suggestion.actions" 
                  :key="action.label"
                  size="small" 
                  type="text"
                  @click="executeSuggestionAction(action)"
                >
                  {{ action.label }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试质量评分 -->
      <div class="quality-score" v-if="qualityScore">
        <h4>📊 测试质量评分</h4>
        <div class="score-display">
          <el-progress 
            type="circle" 
            :percentage="qualityScore.overall" 
            :color="getScoreColor(qualityScore.overall)"
            :width="80"
          />
          <div class="score-details">
            <div class="score-item">
              <span>可读性:</span>
              <el-rate v-model="qualityScore.readability" disabled show-score />
            </div>
            <div class="score-item">
              <span>稳定性:</span>
              <el-rate v-model="qualityScore.stability" disabled show-score />
            </div>
            <div class="score-item">
              <span>覆盖率:</span>
              <el-rate v-model="qualityScore.coverage" disabled show-score />
            </div>
          </div>
        </div>
      </div>

      <!-- AI供应商选择 -->
      <div class="ai-provider-selection" v-if="aiProviders.length > 0">
        <h4>🔧 AI配置</h4>
        <el-select
          v-model="selectedAIProvider"
          placeholder="选择AI供应商"
          size="small"
          style="width: 100%"
          @change="onAIProviderChange"
        >
          <el-option
            v-for="provider in aiProviders"
            :key="provider.id"
            :label="provider.modelName"
            :value="provider.id"
          />
        </el-select>
      </div>

      <!-- 智能问答 -->
      <div class="ai-chat">
        <h4>💬 智能问答</h4>
        <div class="chat-messages" ref="chatMessagesContainer">
          <div 
            v-for="(message, index) in chatMessages" 
            :key="index"
            class="chat-message"
            :class="message.role"
          >
            <div class="message-avatar">
              <el-icon v-if="message.role === 'user'">
                <User />
              </el-icon>
              <el-icon v-else>
                <Avatar />
              </el-icon>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
        </div>
        
        <div class="chat-input">
          <el-input
            v-model="chatInput"
            placeholder="询问测试相关问题..."
            @keyup.enter="sendChatMessage"
            :disabled="chatLoading"
          >
            <template #append>
              <el-button @click="sendChatMessage" :loading="chatLoading">
                <el-icon><Promotion /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 测试模式推荐 -->
      <div class="test-patterns" v-if="recommendedPatterns.length > 0">
        <h4>🎯 推荐测试模式</h4>
        <div class="patterns-list">
          <el-tag 
            v-for="pattern in recommendedPatterns" 
            :key="pattern.name"
            class="pattern-tag"
            @click="applyTestPattern(pattern)"
            effect="plain"
          >
            {{ pattern.name }}
          </el-tag>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowUp,
  ArrowDown,
  Search,
  Tools,
  CopyDocument,
  WarningFilled,
  InfoFilled,
  SuccessFilled,
  User,
  Avatar,
  Promotion
} from '@element-plus/icons-vue'
import type { PlaywrightTest } from '@/services/playwrightTest'
import { AITestAnalysisService } from '@/services/aiTestAnalysis'
import { aiProvider, AIProviderService } from '@/services/aiProvider'
import { PromptService } from '@/services/promptService'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps<{
  currentTest: PlaywrightTest | null
  allTests: PlaywrightTest[]
  selectedAiProvider?: number
  aiProviders?: Array<{
    id: number
    userId: number
    providerName: string
    modelName: string
    modelType: string
    isActive: boolean
    isDefault: boolean
  }>
}>()

// 响应式数据
const expanded = ref(true)
const analyzing = ref(false)
const optimizing = ref(false)
const generating = ref(false)
const chatLoading = ref(false)

const currentSuggestions = ref<TestSuggestion[]>([])
const qualityScore = ref<TestQualityScore | null>(null)
const chatMessages = ref<ChatMessage[]>([])
const chatInput = ref('')
const chatMessagesContainer = ref<HTMLElement>()
const recommendedPatterns = ref<TestPattern[]>([])

// AI供应商相关 - 使用父组件传递的数据或本地数据
const aiProviders = computed(() => props.aiProviders || localAIProviders.value)
const selectedAIProvider = computed({
  get: () => props.selectedAiProvider || localSelectedAIProvider.value,
  set: (value) => {
    if (props.selectedAiProvider !== undefined) {
      // 如果父组件传递了选中的提供商，通过emit通知父组件
      emit('ai-provider-change', value)
    } else {
      // 否则更新本地状态
      localSelectedAIProvider.value = value
    }
  }
})

// 本地AI供应商数据（当父组件没有传递时使用）
const localAIProviders = ref<any[]>([])
const localSelectedAIProvider = ref<number>()
const loadingProviders = ref(false)
const creatingConfig = ref(false)

// 认证状态
const authStore = useAuthStore()

// 计算属性：检查是否有有效的AI提供商
const hasValidAIProvider = computed(() => {
  return aiProviders.value.length > 0 && selectedAIProvider.value
})

// 计算属性：检查用户是否已登录
const isUserLoggedIn = computed(() => {
  return authStore.isAuthenticated && authStore.token
})

// 接口定义
interface TestSuggestion {
  type: 'warning' | 'info' | 'success' | 'error'
  title: string
  description: string
  actions?: Array<{
    label: string
    action: string
    params?: any
  }>
}

interface TestQualityScore {
  overall: number
  readability: number
  stability: number
  coverage: number
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface TestPattern {
  name: string
  description: string
  code: string
  category: string
}

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const analyzeCurrentTest = async () => {
  if (!props.currentTest) {
    ElMessage.warning('请先选择一个测试')
    return
  }

  analyzing.value = true
  try {
    // 分析当前测试
    const analysis = await analyzeTestCode(props.currentTest.code)
    
    // 更新建议
    currentSuggestions.value = analysis.suggestions
    
    // 更新质量评分
    qualityScore.value = analysis.qualityScore
    
    // 更新推荐模式
    recommendedPatterns.value = analysis.recommendedPatterns
    
    ElMessage.success('测试分析完成')
  } catch (error) {
    console.error('分析测试失败:', error)
    ElMessage.error('分析测试失败')
  } finally {
    analyzing.value = false
  }
}

const optimizeTest = async () => {
  if (!props.currentTest) {
    ElMessage.warning('请先选择一个测试')
    return
  }

  optimizing.value = true
  try {
    const optimizedCode = await optimizeTestCode(props.currentTest.code)
    
    // 显示优化建议
    currentSuggestions.value = [{
      type: 'success',
      title: '测试优化完成',
      description: '已生成优化后的测试代码',
      actions: [{
        label: '应用优化',
        action: 'apply-optimization',
        params: { code: optimizedCode }
      }, {
        label: '查看对比',
        action: 'show-diff',
        params: { original: props.currentTest.code, optimized: optimizedCode }
      }]
    }]
    
    ElMessage.success('测试优化完成')
  } catch (error) {
    console.error('优化测试失败:', error)
    ElMessage.error('优化测试失败')
  } finally {
    optimizing.value = false
  }
}

const generateSimilar = async () => {
  if (!props.currentTest) {
    ElMessage.warning('请先选择一个测试')
    return
  }

  generating.value = true
  try {
    const similarTests = await generateSimilarTests(props.currentTest)
    
    currentSuggestions.value = [{
      type: 'info',
      title: `生成了 ${similarTests.length} 个相似测试`,
      description: '基于当前测试生成的相似测试用例',
      actions: [{
        label: '查看生成的测试',
        action: 'show-generated-tests',
        params: { tests: similarTests }
      }]
    }]
    
    ElMessage.success('相似测试生成完成')
  } catch (error) {
    console.error('生成相似测试失败:', error)
    ElMessage.error('生成相似测试失败')
  } finally {
    generating.value = false
  }
}

const sendChatMessage = async () => {
  if (!chatInput.value.trim()) return

  const userMessage: ChatMessage = {
    role: 'user',
    content: chatInput.value,
    timestamp: new Date()
  }
  
  chatMessages.value.push(userMessage)
  const question = chatInput.value
  chatInput.value = ''
  
  chatLoading.value = true
  try {
    const response = await askTestQuestion(question, props.currentTest)
    
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response,
      timestamp: new Date()
    }
    
    chatMessages.value.push(assistantMessage)
    
    // 滚动到底部
    await nextTick()
    if (chatMessagesContainer.value) {
      chatMessagesContainer.value.scrollTop = chatMessagesContainer.value.scrollHeight
    }
  } catch (error) {
    console.error('AI问答失败:', error)
    ElMessage.error('AI问答失败')
  } finally {
    chatLoading.value = false
  }
}

const executeSuggestionAction = (action: any) => {
  switch (action.action) {
    case 'apply-optimization':
      // 触发父组件更新测试代码
      emit('update-test-code', action.params.code)
      break
    case 'show-diff':
      // 显示代码对比
      emit('show-code-diff', action.params)
      break
    case 'show-generated-tests':
      // 显示生成的测试
      emit('show-generated-tests', action.params.tests)
      break
    case 'goto-ai-config':
      // 跳转到AI配置页面
      ElMessage.info('请前往AI聊天页面配置AI供应商')
      // 可以在这里添加路由跳转逻辑
      break
    default:
      console.log('执行动作:', action)
  }
}

const applyTestPattern = (pattern: TestPattern) => {
  emit('apply-test-pattern', pattern)
}

const getSuggestionIcon = (type: string) => {
  const icons = {
    warning: WarningFilled,
    info: InfoFilled,
    success: SuccessFilled,
    error: WarningFilled
  }
  return icons[type as keyof typeof icons] || InfoFilled
}

const getScoreColor = (score: number) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// AI供应商相关方法
const loadAIProviders = async () => {
  // 如果父组件已经传递了AI提供商数据，则不需要加载
  if (props.aiProviders && props.aiProviders.length > 0) {
    return
  }

  try {
    loadingProviders.value = true
    const response = await AIProviderService.getModelConfigurations()
    localAIProviders.value = response || []

    // 默认选择第一个可用的AI提供商
    if (localAIProviders.value.length > 0 && !localSelectedAIProvider.value) {
      localSelectedAIProvider.value = localAIProviders.value[0].id
    }

    if (localAIProviders.value.length === 0) {
      ElMessage.warning('未找到AI配置，请创建默认配置或手动添加')
    }
  } catch (error) {
    console.error('加载AI提供商失败:', error)
    ElMessage.error('加载AI供应商失败，请检查网络连接')
    // 如果加载失败，显示提示信息
    currentSuggestions.value = [{
      type: 'warning',
      title: 'AI配置缺失',
      description: '请先配置AI供应商才能使用AI测试助手功能',
      actions: [{
        label: '前往配置',
        action: 'goto-ai-config'
      }]
    }]
  } finally {
    loadingProviders.value = false
  }
}

const onAIProviderChange = (providerId: number) => {
  console.log('AI提供商选择变更:', providerId)
  selectedAIProvider.value = providerId
}

// 前往登录页面
const goToLogin = () => {
  window.location.href = '/login'
}

// 创建默认配置
const createDefaultConfig = async () => {
  try {
    creatingConfig.value = true

    // 调用后端API创建默认配置
    const success = await AIProviderService.createDefaultConfigurations()

    if (success) {
      ElMessage.success('默认AI配置创建成功！')
      // 重新加载AI提供商列表
      await loadAIProviders()
    } else {
      ElMessage.error('创建默认配置失败，请检查网络连接或联系管理员')
    }
  } catch (error: any) {
    console.error('创建默认配置失败:', error)
    ElMessage.error('创建默认配置失败: ' + (error.message || '未知错误'))
  } finally {
    creatingConfig.value = false
  }
}

// AI服务调用
const analyzeTestCode = async (code: string) => {
  if (!selectedAIProvider.value) {
    throw new Error('请先选择AI供应商')
  }

  try {
    // 从数据库获取测试分析提示词模板
    const template = await PromptService.getTemplateByTaskType('Testing', '测试代码分析')

    let prompt: string
    if (template) {
      // 使用数据库中的模板，替换参数
      prompt = template.content
        .replace(/{testCode}/g, code)
        .replace(/{language}/g, 'JavaScript')
        .replace(/{framework}/g, 'Playwright')
        .replace(/{testType}/g, 'E2E测试')

      // 记录模板使用
      await PromptService.recordTemplateUsage(template.id)
    } else {
      // 使用默认提示词作为后备
      prompt = `
分析以下Playwright测试代码，提供改进建议和质量评分：

\`\`\`javascript
${code}
\`\`\`

请提供：
1. 代码质量建议（可读性、稳定性、覆盖率）
2. 质量评分（0-100分）
3. 推荐的测试模式
4. 具体改进建议

以JSON格式返回分析结果。
`
    }

    const response = await aiProvider.generateContent(prompt, 'test-analysis')
    return JSON.parse(response)
  } catch (error: any) {
    console.error('AI分析失败:', error)

    // 检查是否是配置相关的错误
    if (error.message && error.message.includes('未找到可用的AI配置')) {
      // 显示配置错误提示
      ElMessage({
        type: 'warning',
        message: 'AI配置未完成',
        description: '请先配置AI提供商后再使用分析功能',
        duration: 5000,
        showClose: true
      })

      return {
        suggestions: [{
          type: 'warning',
          title: 'AI配置未完成',
          description: '请按以下步骤配置AI提供商：\n1. 访问 AI Chat 页面\n2. 在右侧面板选择AI提供商\n3. 或联系管理员配置默认AI服务'
        }],
        qualityScore: {
          overall: 0,
          readability: 0,
          stability: 0,
          coverage: 0
        },
        recommendedPatterns: []
      }
    }

    // 其他错误返回通用错误信息
    return {
      suggestions: [{
        type: 'error',
        title: 'AI分析失败',
        description: '当前AI服务不可用，请检查网络连接或稍后重试'
      }],
      qualityScore: {
        overall: 75,
        readability: 4,
        stability: 3,
        coverage: 4
      },
      recommendedPatterns: []
    }
  }
}

const optimizeTestCode = async (code: string) => {
  if (!selectedAIProvider.value) {
    throw new Error('请先选择AI供应商')
  }

  try {
    // 从数据库获取测试优化提示词模板
    const template = await PromptService.getTemplateByTaskType('Testing', '测试代码优化')

    let prompt: string
    if (template) {
      // 使用数据库中的模板，替换参数
      prompt = template.content
        .replace(/{originalCode}/g, code)
        .replace(/{language}/g, 'JavaScript')
        .replace(/{framework}/g, 'Playwright')
        .replace(/{optimizationGoals}/g, '提高稳定性、可维护性和执行效率')

      // 记录模板使用
      await PromptService.recordTemplateUsage(template.id)
    } else {
      // 使用默认提示词作为后备
      prompt = `
优化以下Playwright测试代码，提高其稳定性和可维护性：

\`\`\`javascript
${code}
\`\`\`

请优化：
1. 选择器策略
2. 等待机制
3. 错误处理
4. 代码结构
5. 性能优化

返回优化后的完整代码。
`
    }

    const response = await aiProvider.generateContent(prompt, 'code-optimization')
    return response
  } catch (error) {
    console.error('AI优化失败:', error)
    throw new Error('AI优化服务暂不可用，请检查配置或稍后重试')
  }
}

const generateSimilarTests = async (test: PlaywrightTest) => {
  if (!selectedAIProvider.value) {
    throw new Error('请先选择AI供应商')
  }

  const prompt = `
基于以下测试用例，生成3-5个相似的测试用例：

测试名称: ${test.name}
测试代码:
\`\`\`javascript
${test.code}
\`\`\`

生成的测试应该：
1. 测试相关功能的不同场景
2. 包含边界条件测试
3. 包含异常情况测试
4. 保持代码风格一致

以JSON数组格式返回测试用例。
`

  try {
    const response = await aiProvider.generateContent(prompt, 'test-generation')
    return JSON.parse(response)
  } catch (error) {
    console.error('AI生成失败:', error)
    // 返回模拟数据作为备用
    return [{
      name: '相似测试示例',
      description: 'AI生成服务暂不可用',
      code: '// AI生成服务暂不可用，请检查配置'
    }]
  }
}

const askTestQuestion = async (question: string, currentTest: PlaywrightTest | null) => {
  if (!selectedAIProvider.value) {
    throw new Error('请先选择AI供应商')
  }

  const context = currentTest ? `
当前测试上下文:
- 测试名称: ${currentTest.name}
- 测试描述: ${currentTest.description}
- 测试代码: ${currentTest.code.substring(0, 500)}...
` : ''

  const prompt = `
你是一个Playwright测试专家，请回答以下问题：

${context}

用户问题: ${question}

请提供详细、实用的回答。
`

  try {
    const response = await aiProvider.generateContent(prompt, 'test-qa')
    return response
  } catch (error) {
    console.error('AI问答失败:', error)
    return '抱歉，AI问答服务暂时不可用。请检查AI配置或稍后重试。'
  }
}

// 监听当前测试变化
watch(() => props.currentTest, (newTest, oldTest) => {
  // 只有在测试真正切换时才自动分析，避免新建测试时触发
  if (newTest && selectedAIProvider.value && oldTest && newTest.id !== oldTest.id) {
    // 检查是否是已保存的测试（不是刚创建的草稿）
    if (newTest.status !== '草稿' || newTest.code.length > 200) {
      analyzeCurrentTest()
    }
  }
})

// 组件挂载时加载AI供应商
onMounted(() => {
  loadAIProviders()
})

// Emits
const emit = defineEmits<{
  'update-test-code': [code: string]
  'show-code-diff': [params: any]
  'show-generated-tests': [tests: any[]]
  'apply-test-pattern': [pattern: TestPattern]
  'ai-provider-change': [providerId: number]
}>()
</script>

<style lang="scss" scoped>
.ai-assistant {
  height: 100%;
  
  .assistant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .assistant-content {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }
  
  .ai-config-warning {
    margin-bottom: 16px;

    .config-actions {
      p {
        margin: 0 0 8px 0;
        color: #606266;
      }

      .action-buttons {
        display: flex;
        gap: 8px;
        margin-top: 8px;

        .el-button {
          flex: 1;
        }
      }
    }
  }

  .quick-actions {
    margin-bottom: 16px;

    .el-button-group {
      width: 100%;

      .el-button {
        flex: 1;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
  
  .suggestions-area {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
    
    .suggestions-list {
      .suggestion-item {
        display: flex;
        padding: 12px;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        margin-bottom: 8px;
        
        &.warning {
          border-color: var(--el-color-warning);
          background-color: var(--el-color-warning-light-9);
        }
        
        &.info {
          border-color: var(--el-color-info);
          background-color: var(--el-color-info-light-9);
        }
        
        &.success {
          border-color: var(--el-color-success);
          background-color: var(--el-color-success-light-9);
        }
        
        .suggestion-icon {
          margin-right: 8px;
          
          .el-icon {
            font-size: 16px;
          }
        }
        
        .suggestion-content {
          flex: 1;
          
          .suggestion-title {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .suggestion-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-bottom: 8px;
          }
          
          .suggestion-actions {
            .el-button {
              padding: 0;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
  
  .quality-score {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }
    
    .score-display {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .score-details {
        flex: 1;
        
        .score-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 4px;
          font-size: 12px;
          
          span {
            min-width: 50px;
          }
        }
      }
    }
  }
  
  .ai-provider-selection {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }
  
  .ai-chat {
    margin-bottom: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }
    
    .chat-messages {
      height: 200px;
      overflow-y: auto;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      padding: 8px;
      margin-bottom: 8px;
      
      .chat-message {
        display: flex;
        margin-bottom: 12px;
        
        &.user {
          flex-direction: row-reverse;
          
          .message-content {
            background-color: var(--el-color-primary-light-9);
            margin-right: 8px;
          }
        }
        
        &.assistant {
          .message-content {
            background-color: var(--el-bg-color-page);
            margin-left: 8px;
          }
        }
        
        .message-avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: var(--el-color-primary);
          color: white;
          flex-shrink: 0;
          
          .el-icon {
            font-size: 12px;
          }
        }
        
        .message-content {
          max-width: 80%;
          padding: 8px 12px;
          border-radius: 12px;
          
          .message-text {
            font-size: 12px;
            line-height: 1.4;
          }
          
          .message-time {
            font-size: 10px;
            color: var(--el-text-color-secondary);
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .test-patterns {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }
    
    .patterns-list {
      .pattern-tag {
        margin: 2px 4px 2px 0;
        cursor: pointer;
        
        &:hover {
          background-color: var(--el-color-primary-light-8);
        }
      }
    }
  }
}
</style>