<template>
  <el-dialog
    v-model="visible"
    title="添加AI供应商"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="供应商名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入供应商名称（英文，如：CustomAI）"
          :disabled="saving"
        />
        <div class="form-tip">
          供应商名称用于系统内部标识，建议使用英文，创建后不可修改
        </div>
      </el-form-item>

      <el-form-item label="显示名称" prop="displayName">
        <el-input
          v-model="form.displayName"
          placeholder="请输入显示名称（如：自定义AI服务）"
          :disabled="saving"
        />
        <div class="form-tip">
          显示名称将在界面中展示给用户
        </div>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入供应商描述"
          :disabled="saving"
        />
      </el-form-item>

      <el-form-item label="API端点" prop="apiEndpoint">
        <el-input
          v-model="form.apiEndpoint"
          placeholder="请输入API端点URL（如：https://api.example.com/v1/）"
          :disabled="saving"
        />
        <div class="form-tip">
          API端点URL，必须以http://或https://开头
        </div>
      </el-form-item>

      <el-form-item label="API密钥" prop="apiKey">
        <el-input
          v-model="form.apiKey"
          type="password"
          placeholder="请输入API密钥"
          :disabled="saving"
          show-password
        />
        <div class="form-tip">
          API密钥将被加密存储
        </div>
      </el-form-item>

      <el-form-item label="模型名称" prop="modelName">
        <el-input
          v-model="form.modelName"
          placeholder="请输入默认模型名称（如：gpt-4）"
          :disabled="saving"
        />
        <div class="form-tip">
          默认使用的模型名称
        </div>
      </el-form-item>

      <el-form-item label="超时时间" prop="timeoutSeconds">
        <el-input-number
          v-model="form.timeoutSeconds"
          :min="10"
          :max="600"
          :step="10"
          :disabled="saving"
        />
        <span class="input-suffix">秒</span>
        <div class="form-tip">
          API请求超时时间，建议设置为60-300秒
        </div>
      </el-form-item>

      <el-form-item label="最大重试次数" prop="maxRetries">
        <el-input-number
          v-model="form.maxRetries"
          :min="0"
          :max="10"
          :step="1"
          :disabled="saving"
        />
        <span class="input-suffix">次</span>
        <div class="form-tip">
          请求失败时的最大重试次数
        </div>
      </el-form-item>

      <el-form-item label="启用状态">
        <el-switch
          v-model="form.enabled"
          :disabled="saving"
        />
        <div class="form-tip">
          是否立即启用此供应商
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="saving">
          取消
        </el-button>
        <el-button @click="testConnection" :loading="testing" :disabled="saving">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="saving">
          <el-icon><Check /></el-icon>
          确定添加
        </el-button>
      </div>
    </template>

    <!-- 测试结果显示 -->
    <el-alert
      v-if="testResult"
      :type="testResult.success ? 'success' : 'error'"
      :title="testResult.success ? '连接测试成功' : '连接测试失败'"
      :description="testResult.message"
      :closable="false"
      style="margin-top: 16px;"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Connection, Check } from '@element-plus/icons-vue'
import { AIProviderService, type CreateProviderRequest } from '@/services/aiProvider'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)
const testResult = ref<{
  success: boolean
  message: string
  responseTime?: number
} | null>(null)

const form = ref({
  name: '',
  displayName: '',
  description: '',
  apiEndpoint: '',
  apiKey: '',
  modelName: '',
  timeoutSeconds: 60,
  maxRetries: 3,
  enabled: true
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 50, message: '供应商名称长度在2-50个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '供应商名称必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 100, message: '显示名称长度在2-100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ],
  apiEndpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: 'API端点必须以http://或https://开头', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' },
    { min: 10, message: 'API密钥长度至少10个字符', trigger: 'blur' }
  ],
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 2, max: 100, message: '模型名称长度在2-100个字符', trigger: 'blur' }
  ],
  timeoutSeconds: [
    { required: true, message: '请设置超时时间', trigger: 'blur' },
    { type: 'number', min: 10, max: 600, message: '超时时间必须在10-600秒之间', trigger: 'blur' }
  ],
  maxRetries: [
    { required: true, message: '请设置最大重试次数', trigger: 'blur' },
    { type: 'number', min: 0, max: 10, message: '最大重试次数必须在0-10次之间', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开，重置表单
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  form.value = {
    name: '',
    displayName: '',
    description: '',
    apiEndpoint: '',
    apiKey: '',
    modelName: '',
    timeoutSeconds: 60,
    maxRetries: 3,
    enabled: true
  }
  testResult.value = null
  formRef.value?.clearValidate()
}

const handleClose = () => {
  if (saving.value) return
  visible.value = false
}

const testConnection = async () => {
  if (!formRef.value) return

  try {
    // 先验证必要字段
    const fieldsToValidate = ['apiEndpoint', 'apiKey']
    let isValid = true
    
    for (const field of fieldsToValidate) {
      try {
        await formRef.value.validateField(field)
      } catch {
        isValid = false
      }
    }

    if (!isValid) {
      ElMessage.warning('请先填写API端点和API密钥')
      return
    }

    testing.value = true
    testResult.value = null

    // TODO: 调用测试连接API
    // 这里暂时模拟测试结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟测试结果
    const success = Math.random() > 0.3 // 70%成功率
    testResult.value = {
      success,
      message: success ? '连接成功，API响应正常' : '连接失败，请检查API端点和密钥是否正确',
      responseTime: success ? Math.floor(Math.random() * 1000) + 200 : undefined
    }

    if (success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败')
    }
  } catch (error: any) {
    console.error('测试连接失败:', error)
    testResult.value = {
      success: false,
      message: '测试连接时发生错误: ' + error.message
    }
    ElMessage.error('测试连接失败')
  } finally {
    testing.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const isValid = await formRef.value.validate()
    if (!isValid) return

    saving.value = true

    // 构建创建请求
    const request: CreateProviderRequest = {
      name: form.value.name,
      displayName: form.value.displayName,
      description: form.value.description,
      apiEndpoint: form.value.apiEndpoint,
      apiKey: form.value.apiKey,
      modelName: form.value.modelName,
      timeoutSeconds: form.value.timeoutSeconds,
      maxRetries: form.value.maxRetries,
      enabled: form.value.enabled
    }

    // 调用创建供应商API
    await AIProviderService.createProvider(request)

    ElMessage.success('AI供应商添加成功')
    emit('success')
    visible.value = false
  } catch (error: any) {
    console.error('添加供应商失败:', error)
    ElMessage.error('添加供应商失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.input-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 8px;
}
</style>
