using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs.Agent;
using ProjectManagement.AI.Services;
using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// AI Agent编排API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AgentController : ControllerBase
    {
        private readonly AgentOrchestrationService _agentService;
        private readonly ILogger<AgentController> _logger;

        public AgentController(AgentOrchestrationService agentService, ILogger<AgentController> logger)
        {
            _agentService = agentService;
            _logger = logger;
        }

        /// <summary>
        /// 执行Agent任务
        /// </summary>
        [HttpPost("execute")]
        public async Task<ActionResult<AgentExecutionResult>> ExecuteAgentTask([FromBody] AgentTaskRequest request)
        {
            try
            {
                var result = await _agentService.ExecuteAgentTaskAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Agent任务执行失败: {AgentType} - {TaskType}", request.AgentType, request.TaskType);
                return StatusCode(500, new { error = "Agent任务执行失败", details = ex.Message });
            }
        }

        /// <summary>
        /// 多Agent协作
        /// </summary>
        [HttpPost("collaborate")]
        public async Task<ActionResult<CollaborationResult>> ExecuteCollaboration([FromBody] CollaborationRequest request)
        {
            try
            {
                var result = await _agentService.ExecuteCollaborationAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "多Agent协作失败: {CollaborationType}", request.CollaborationType);
                return StatusCode(500, new { error = "多Agent协作失败", details = ex.Message });
            }
        }

        /// <summary>
        /// 获取可用的Agent列表
        /// </summary>
        [HttpGet("available")]
        public ActionResult<List<AgentInfo>> GetAvailableAgents()
        {
            try
            {
                var agents = _agentService.GetAvailableAgents();
                return Ok(agents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用Agent列表失败");
                return StatusCode(500, new { error = "获取可用Agent列表失败" });
            }
        }

        /// <summary>
        /// 推荐最适合的Agent
        /// </summary>
        [HttpPost("recommend")]
        public ActionResult<AgentRecommendation> RecommendAgent([FromBody] AgentRecommendationRequest request)
        {
            try
            {
                var recommendation = _agentService.RecommendAgent(request.TaskDescription, request.Context);
                return Ok(recommendation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Agent推荐失败");
                return StatusCode(500, new { error = "Agent推荐失败" });
            }
        }

        /// <summary>
        /// 项目管理专家分析
        /// </summary>
        [HttpPost("project-manager/analyze")]
        public async Task<ActionResult<AgentExecutionResult>> ProjectManagerAnalyze([FromBody] ProjectAnalysisRequest request)
        {
            try
            {
                var agentRequest = new AgentTaskRequest
                {
                    AgentType = "project-manager",
                    TaskType = "analysis",
                    TaskDescription = "项目管理分析",
                    Context = request.ProjectContext,
                    UserMessage = request.Question,
                    AIConfig = request.AIConfig
                };

                var result = await _agentService.ExecuteAgentTaskAsync(agentRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "项目管理分析失败");
                return StatusCode(500, new { error = "项目管理分析失败" });
            }
        }

        /// <summary>
        /// 系统架构师设计
        /// </summary>
        [HttpPost("architect/design")]
        public async Task<ActionResult<AgentExecutionResult>> ArchitectDesign([FromBody] ArchitectureDesignRequest request)
        {
            try
            {
                var agentRequest = new AgentTaskRequest
                {
                    AgentType = "architect",
                    TaskType = "design",
                    TaskDescription = "系统架构设计",
                    Context = request.TechnicalContext,
                    UserMessage = request.Requirements,
                    AIConfig = request.AIConfig
                };

                var result = await _agentService.ExecuteAgentTaskAsync(agentRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统架构设计失败");
                return StatusCode(500, new { error = "系统架构设计失败" });
            }
        }

        /// <summary>
        /// 开发工程师技术支持
        /// </summary>
        [HttpPost("developer/support")]
        public async Task<ActionResult<AgentExecutionResult>> DeveloperSupport([FromBody] DeveloperSupportRequest request)
        {
            try
            {
                var agentRequest = new AgentTaskRequest
                {
                    AgentType = "developer",
                    TaskType = "recommendation",
                    TaskDescription = "技术支持",
                    Context = request.TechnicalContext,
                    UserMessage = request.Problem,
                    AIConfig = request.AIConfig
                };

                var result = await _agentService.ExecuteAgentTaskAsync(agentRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开发技术支持失败");
                return StatusCode(500, new { error = "开发技术支持失败" });
            }
        }

        /// <summary>
        /// 测试工程师质量评估
        /// </summary>
        [HttpPost("tester/assess")]
        public async Task<ActionResult<AgentExecutionResult>> TesterAssess([FromBody] QualityAssessmentRequest request)
        {
            try
            {
                var agentRequest = new AgentTaskRequest
                {
                    AgentType = "tester",
                    TaskType = "assessment",
                    TaskDescription = "质量评估",
                    Context = request.ProjectContext,
                    UserMessage = request.AssessmentTarget,
                    AIConfig = request.AIConfig
                };

                var result = await _agentService.ExecuteAgentTaskAsync(agentRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "质量评估失败");
                return StatusCode(500, new { error = "质量评估失败" });
            }
        }

        /// <summary>
        /// 业务分析师需求分析
        /// </summary>
        [HttpPost("business-analyst/analyze")]
        public async Task<ActionResult<AgentExecutionResult>> BusinessAnalystAnalyze([FromBody] RequirementAnalysisRequest request)
        {
            try
            {
                var agentRequest = new AgentTaskRequest
                {
                    AgentType = "business-analyst",
                    TaskType = "analysis",
                    TaskDescription = "需求分析",
                    Context = request.BusinessContext,
                    UserMessage = request.Requirements,
                    AIConfig = request.AIConfig
                };

                var result = await _agentService.ExecuteAgentTaskAsync(agentRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "需求分析失败");
                return StatusCode(500, new { error = "需求分析失败" });
            }
        }
    }

    // 请求DTO类
    public class AgentRecommendationRequest
    {
        public string TaskDescription { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
    }

    public class ProjectAnalysisRequest
    {
        public string ProjectContext { get; set; } = string.Empty;
        public string Question { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
    }

    public class ArchitectureDesignRequest
    {
        public string TechnicalContext { get; set; } = string.Empty;
        public string Requirements { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
    }

    public class DeveloperSupportRequest
    {
        public string TechnicalContext { get; set; } = string.Empty;
        public string Problem { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
    }

    public class QualityAssessmentRequest
    {
        public string ProjectContext { get; set; } = string.Empty;
        public string AssessmentTarget { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
    }

    public class RequirementAnalysisRequest
    {
        public string BusinessContext { get; set; } = string.Empty;
        public string Requirements { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
    }
}
