-- =============================================
-- 脚本名称: 007_AddFlowControlFieldsToTemplateSteps_Fixed.sql
-- 脚本描述: 修复版本 - 为UIAutoMationTemplateSteps表添加流程控制相关字段
-- 创建时间: 2025-06-24
-- 修复说明: 解决字段不存在时添加注释失败的问题
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '======================================================';
PRINT '开始执行修复版迁移脚本: 007_AddFlowControlFieldsToTemplateSteps_Fixed.sql';
PRINT '功能: 为UIAutoMationTemplateSteps表添加流程控制相关字段';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '======================================================';
PRINT '';

-- =============================================
-- 1. 前置条件检查
-- =============================================

-- 检查UIAutoMationTemplateSteps表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UIAutoMationTemplateSteps')
BEGIN
    PRINT '❌ 错误：UIAutoMationTemplateSteps表不存在，请先执行006_AddUICustomTemplateTables.sql';
    RETURN;
END

-- 显示当前表结构
PRINT '当前 UIAutoMationTemplateSteps 表的字段：';
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'UIAutoMationTemplateSteps'
ORDER BY ORDINAL_POSITION;

PRINT '✓ 前置条件检查通过';
GO

-- =============================================
-- 2. 添加流程控制相关字段
-- =============================================

PRINT '正在为UIAutoMationTemplateSteps表添加流程控制字段...';

-- 添加条件表达式字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'ConditionExpression')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [ConditionExpression] nvarchar(1000) NULL;

    PRINT '✓ 已添加 ConditionExpression 字段';
END
ELSE
BEGIN
    PRINT '⚠ ConditionExpression 字段已存在，跳过添加';
END

-- 添加跳转目标步骤ID字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'JumpToStepId')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [JumpToStepId] int NULL;

    PRINT '✓ 已添加 JumpToStepId 字段';
END
ELSE
BEGIN
    PRINT '⚠ JumpToStepId 字段已存在，跳过添加';
END

-- 添加循环次数字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopCount')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [LoopCount] int NULL;

    PRINT '✓ 已添加 LoopCount 字段';
END
ELSE
BEGIN
    PRINT '⚠ LoopCount 字段已存在，跳过添加';
END

-- 添加循环变量名字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopVariable')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [LoopVariable] nvarchar(50) NULL;

    PRINT '✓ 已添加 LoopVariable 字段';
END
ELSE
BEGIN
    PRINT '⚠ LoopVariable 字段已存在，跳过添加';
END

-- 添加分组标识字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'GroupId')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    ADD [GroupId] nvarchar(50) NULL;

    PRINT '✓ 已添加 GroupId 字段';
END
ELSE
BEGIN
    PRINT '⚠ GroupId 字段已存在，跳过添加';
END

-- 等待一下确保字段创建完成
WAITFOR DELAY '00:00:01';

-- =============================================
-- 3. 添加字段注释（只有字段存在时才添加）
-- =============================================

PRINT '正在添加字段注释...';

-- 条件表达式字段注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'ConditionExpression')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('UIAutoMationTemplateSteps') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'ConditionExpression'))
    BEGIN
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'条件表达式，用于条件判断类型的步骤，支持JavaScript表达式',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps',
            @level2type = N'COLUMN', @level2name = N'ConditionExpression';
        PRINT '✓ 已添加 ConditionExpression 字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠ ConditionExpression 字段注释已存在';
    END
END

-- 跳转目标步骤ID字段注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'JumpToStepId')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('UIAutoMationTemplateSteps') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'JumpToStepId'))
    BEGIN
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'跳转目标步骤ID，用于跳转类型的步骤',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps',
            @level2type = N'COLUMN', @level2name = N'JumpToStepId';
        PRINT '✓ 已添加 JumpToStepId 字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠ JumpToStepId 字段注释已存在';
    END
END

-- 循环次数字段注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopCount')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('UIAutoMationTemplateSteps') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopCount'))
    BEGIN
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'循环次数，用于循环类型的步骤，-1表示无限循环',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps',
            @level2type = N'COLUMN', @level2name = N'LoopCount';
        PRINT '✓ 已添加 LoopCount 字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠ LoopCount 字段注释已存在';
    END
END

-- 循环变量名字段注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopVariable')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('UIAutoMationTemplateSteps') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'LoopVariable'))
    BEGIN
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'循环变量名，用于循环类型的步骤',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps',
            @level2type = N'COLUMN', @level2name = N'LoopVariable';
        PRINT '✓ 已添加 LoopVariable 字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠ LoopVariable 字段注释已存在';
    END
END

-- 分组标识字段注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'GroupId')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('UIAutoMationTemplateSteps') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'GroupId'))
    BEGIN
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'分组标识，用于标识循环体、条件分支等逻辑分组',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSteps',
            @level2type = N'COLUMN', @level2name = N'GroupId';
        PRINT '✓ 已添加 GroupId 字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠ GroupId 字段注释已存在';
    END
END

-- =============================================
-- 4. 创建索引（可选）
-- =============================================

PRINT '正在创建相关索引...';

-- 为JumpToStepId创建索引
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'JumpToStepId')
   AND NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'IX_UIAutoMationTemplateSteps_JumpToStepId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_UIAutoMationTemplateSteps_JumpToStepId]
    ON [dbo].[UIAutoMationTemplateSteps] ([JumpToStepId])
    WHERE [JumpToStepId] IS NOT NULL;

    PRINT '✓ 已创建 JumpToStepId 索引';
END
ELSE
BEGIN
    PRINT '⚠ JumpToStepId 索引已存在或字段不存在，跳过创建';
END

-- 为GroupId创建索引
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'GroupId')
   AND NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('UIAutoMationTemplateSteps') AND name = 'IX_UIAutoMationTemplateSteps_GroupId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_UIAutoMationTemplateSteps_GroupId]
    ON [dbo].[UIAutoMationTemplateSteps] ([GroupId])
    WHERE [GroupId] IS NOT NULL;

    PRINT '✓ 已创建 GroupId 索引';
END
ELSE
BEGIN
    PRINT '⚠ GroupId 索引已存在或字段不存在，跳过创建';
END

PRINT '';
PRINT '✅ 修复版迁移脚本执行完成！';
PRINT '======================================================';
PRINT '已添加以下字段到 UIAutoMationTemplateSteps 表：';
PRINT '1. ConditionExpression - 条件表达式字段';
PRINT '2. JumpToStepId - 跳转目标步骤ID字段';
PRINT '3. LoopCount - 循环次数字段';
PRINT '4. LoopVariable - 循环变量名字段';
PRINT '5. GroupId - 分组标识字段';
PRINT '';
PRINT '新增功能特性：';
PRINT '- 支持条件判断执行：根据条件表达式决定是否执行步骤';
PRINT '- 支持循环执行：可以设置循环次数和循环变量';
PRINT '- 支持步骤跳转：可以跳转到指定的步骤';
PRINT '- 支持逻辑分组：通过GroupId标识相关的步骤组';
PRINT '';
PRINT '脚本执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
