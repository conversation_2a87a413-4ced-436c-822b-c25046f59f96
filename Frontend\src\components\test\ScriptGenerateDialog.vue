<template>
  <el-dialog
    v-model="visible"
    title="🚀 AI自动化脚本生成器"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="script-generate-dialog">
      <!-- 脚本类型选择 -->
      <el-card class="script-type-selection">
        <template #header>
          <span>📋 选择脚本类型</span>
        </template>
        
        <el-radio-group v-model="scriptConfig.scriptType" class="script-type-grid">
          <el-radio-button label="ci">
            <div class="script-option">
              <el-icon><Operation /></el-icon>
              <span>CI/CD脚本</span>
            </div>
          </el-radio-button>
          <el-radio-button label="docker">
            <div class="script-option">
              <el-icon><Grid /></el-icon>
              <span>Docker容器</span>
            </div>
          </el-radio-button>
          <el-radio-button label="data">
            <div class="script-option">
              <el-icon><Coin /></el-icon>
              <span>测试数据</span>
            </div>
          </el-radio-button>
          <el-radio-button label="monitoring">
            <div class="script-option">
              <el-icon><Monitor /></el-icon>
              <span>性能监控</span>
            </div>
          </el-radio-button>
          <el-radio-button label="environment">
            <div class="script-option">
              <el-icon><Setting /></el-icon>
              <span>环境管理</span>
            </div>
          </el-radio-button>
          <el-radio-button label="reporting">
            <div class="script-option">
              <el-icon><Document /></el-icon>
              <span>报告生成</span>
            </div>
          </el-radio-button>
          <el-radio-button label="load">
            <div class="script-option">
              <el-icon><TrendCharts /></el-icon>
              <span>负载测试</span>
            </div>
          </el-radio-button>
        </el-radio-group>
      </el-card>

      <!-- 配置选项 -->
      <el-card class="script-config">
        <template #header>
          <span>⚙️ 配置选项</span>
        </template>

        <el-form :model="scriptConfig" label-width="120px">
          <!-- CI/CD配置 -->
          <div v-if="scriptConfig.scriptType === 'ci'">
            <el-form-item label="CI平台">
              <el-select v-model="scriptConfig.platform" style="width: 100%">
                <el-option label="GitHub Actions" value="github" />
                <el-option label="GitLab CI" value="gitlab" />
                <el-option label="Jenkins" value="jenkins" />
                <el-option label="Azure DevOps" value="azure" />
              </el-select>
            </el-form-item>
          </div>

          <!-- Docker配置 -->
          <div v-if="scriptConfig.scriptType === 'docker'">
            <el-form-item label="目标环境">
              <el-select v-model="scriptConfig.environment" style="width: 100%">
                <el-option label="开发环境" value="development" />
                <el-option label="测试环境" value="staging" />
                <el-option label="生产环境" value="production" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 测试数据配置 -->
          <div v-if="scriptConfig.scriptType === 'data'">
            <el-form-item label="数据源类型">
              <el-select v-model="scriptConfig.dataSource" style="width: 100%">
                <el-option label="数据库" value="database" />
                <el-option label="API接口" value="api" />
                <el-option label="文件系统" value="file" />
                <el-option label="Mock数据" value="mock" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 环境管理配置 -->
          <div v-if="scriptConfig.scriptType === 'environment'">
            <el-form-item label="目标环境">
              <el-checkbox-group v-model="scriptConfig.environments">
                <el-checkbox label="development">开发环境</el-checkbox>
                <el-checkbox label="testing">测试环境</el-checkbox>
                <el-checkbox label="staging">预发布环境</el-checkbox>
                <el-checkbox label="production">生产环境</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- 报告生成配置 -->
          <div v-if="scriptConfig.scriptType === 'reporting'">
            <el-form-item label="报告类型">
              <el-checkbox-group v-model="scriptConfig.reportTypes">
                <el-checkbox label="html">HTML报告</el-checkbox>
                <el-checkbox label="pdf">PDF报告</el-checkbox>
                <el-checkbox label="json">JSON数据</el-checkbox>
                <el-checkbox label="excel">Excel表格</el-checkbox>
                <el-checkbox label="email">邮件报告</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <!-- 负载测试配置 -->
          <div v-if="scriptConfig.scriptType === 'load'">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="并发用户数">
                  <el-input-number 
                    v-model="scriptConfig.loadConfig.concurrentUsers" 
                    :min="1" 
                    :max="1000"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="持续时间">
                  <el-input v-model="scriptConfig.loadConfig.duration" placeholder="5m" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="目标RPS">
                  <el-input-number 
                    v-model="scriptConfig.loadConfig.targetRPS" 
                    :min="1" 
                    :max="10000"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 测试用例选择 -->
          <el-form-item label="包含测试">
            <div class="test-selection">
              <el-checkbox 
                v-model="selectAllTests" 
                @change="handleSelectAllTests"
                :indeterminate="isIndeterminate"
              >
                全选 ({{ selectedTests.length }}/{{ availableTests.length }})
              </el-checkbox>
              
              <div class="test-list">
                <el-checkbox-group v-model="selectedTests">
                  <div 
                    v-for="test in availableTests" 
                    :key="test.id"
                    class="test-checkbox-item"
                  >
                    <el-checkbox :label="test.id">
                      <div class="test-info">
                        <span class="test-name">{{ test.name }}</span>
                        <el-tag :type="getTestTypeColor(test.category)" size="small">
                          {{ test.category }}
                        </el-tag>
                      </div>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 生成预览 -->
      <el-card class="generation-preview" v-if="generationPreview">
        <template #header>
          <span>👀 生成预览</span>
        </template>
        
        <div class="preview-content">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="脚本类型">
              {{ getScriptTypeLabel(scriptConfig.scriptType) }}
            </el-descriptions-item>
            <el-descriptions-item label="包含测试">
              {{ selectedTests.length }} 个
            </el-descriptions-item>
            <el-descriptions-item label="预计文件数">
              {{ generationPreview.estimatedFiles }}
            </el-descriptions-item>
            <el-descriptions-item label="生成时间">
              {{ generationPreview.estimatedTime }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="file-list">
            <h4>将生成的文件：</h4>
            <ul>
              <li v-for="file in generationPreview.files" :key="file">
                <el-icon><Document /></el-icon>
                {{ file }}
              </li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="previewGeneration" :loading="previewing">预览生成</el-button>
        <el-button 
          type="primary" 
          @click="startGeneration" 
          :loading="generating"
          :disabled="!canGenerate"
        >
          <el-icon><MagicStick /></el-icon>
          开始生成
        </el-button>
      </div>
    </template>

    <!-- 生成进度对话框 -->
    <el-dialog
      v-model="showProgress"
      title="🚀 正在生成自动化脚本"
      width="600px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="generation-progress">
        <el-progress 
          :percentage="progressPercentage" 
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
        
        <div class="progress-logs">
          <div 
            v-for="(log, index) in progressLogs" 
            :key="index"
            class="progress-log"
          >
            <el-icon><InfoFilled /></el-icon>
            {{ log }}
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 结果展示对话框 -->
    <el-dialog
      v-model="showResult"
      title="✅ 脚本生成完成"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="generation-result" v-if="generationResult">
        <el-tabs v-model="activeResultTab">
          <el-tab-pane 
            v-for="(content, filename) in generationResult.files" 
            :key="filename"
            :label="filename"
            :name="filename"
          >
            <div class="file-content">
              <div class="file-header">
                <span>{{ filename }}</span>
                <el-button size="small" @click="copyToClipboard(content)">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-button>
              </div>
              <el-input
                :model-value="content"
                type="textarea"
                :rows="20"
                readonly
                class="code-textarea"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
        
        <div class="result-actions">
          <el-button @click="downloadAllFiles">
            <el-icon><Download /></el-icon>
            下载所有文件
          </el-button>
          <el-button type="primary" @click="applyToProject">
            <el-icon><Check /></el-icon>
            应用到项目
          </el-button>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Operation,
  Grid,
  Coin,
  Monitor,
  Setting,
  Document,
  TrendCharts,
  MagicStick,
  InfoFilled,
  CopyDocument,
  Download,
  Check
} from '@element-plus/icons-vue'
import type { PlaywrightTest } from '@/services/playwrightTest'
import { batchGenerateScripts, type ScriptGenerationConfig } from '@/services/aiScriptGenerator'

// Props & Emits
const props = defineProps<{
  modelValue: boolean
  availableTests: PlaywrightTest[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'generated': [result: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const scriptConfig = reactive<ScriptGenerationConfig>({
  tests: [],
  scriptType: 'ci',
  platform: 'github',
  environment: 'development',
  dataSource: 'database',
  environments: ['development'],
  reportTypes: ['html'],
  loadConfig: {
    concurrentUsers: 10,
    duration: '5m',
    targetRPS: 100
  }
})

const selectedTests = ref<number[]>([])
const selectAllTests = ref(false)
const generationPreview = ref<any>(null)
const previewing = ref(false)
const generating = ref(false)

const showProgress = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | undefined>()
const progressText = ref('')
const progressLogs = ref<string[]>([])

const showResult = ref(false)
const generationResult = ref<any>(null)
const activeResultTab = ref('')

// 计算属性
const isIndeterminate = computed(() => {
  const selected = selectedTests.value.length
  const total = props.availableTests.length
  return selected > 0 && selected < total
})

const canGenerate = computed(() => {
  return selectedTests.value.length > 0 && scriptConfig.scriptType
})

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  selectedTests.value = []
  selectAllTests.value = false
  generationPreview.value = null
  generationResult.value = null
}

const handleSelectAllTests = (checked: boolean) => {
  if (checked) {
    selectedTests.value = props.availableTests.map(test => test.id)
  } else {
    selectedTests.value = []
  }
}

const getTestTypeColor = (category: string): 'success' | 'warning' | 'danger' | 'info' => {
  const colors: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    'e2e': 'success',
    'component': 'warning',
    'api': 'info',
    'performance': 'danger'
  }
  return colors[category] || 'info'
}

const getScriptTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    'ci': 'CI/CD自动化脚本',
    'docker': 'Docker容器化脚本',
    'data': '测试数据管理脚本',
    'monitoring': '性能监控脚本',
    'environment': '环境管理脚本',
    'reporting': '报告生成脚本',
    'load': '负载测试脚本'
  }
  return labels[type] || type
}

const previewGeneration = async () => {
  previewing.value = true
  
  try {
    // 模拟预览生成计划
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const fileMap: Record<string, string[]> = {
      'ci': ['ci.yml', 'test-runner.sh', 'README.md'],
      'docker': ['Dockerfile', 'docker-compose.yml', 'start.sh', '.env'],
      'data': ['setup-data.sql', 'cleanup-data.sql', 'data-sync.js'],
      'monitoring': ['monitor.js', 'alerts.yml', 'dashboard.json'],
      'environment': ['env-switch.sh', 'config.json', 'health-check.js'],
      'reporting': ['report-gen.js', 'templates/', 'email-sender.js'],
      'load': ['load-test.js', 'k6-config.js', 'performance.yml']
    }
    
    generationPreview.value = {
      estimatedFiles: fileMap[scriptConfig.scriptType]?.length || 3,
      estimatedTime: '30-60秒',
      files: fileMap[scriptConfig.scriptType] || ['script.js', 'config.json', 'README.md']
    }
    
    ElMessage.success('生成预览完成')
  } catch (error) {
    console.error('预览生成失败:', error)
    ElMessage.error('预览生成失败')
  } finally {
    previewing.value = false
  }
}

const startGeneration = async () => {
  generating.value = true
  showProgress.value = true
  progressPercentage.value = 0
  progressLogs.value = []
  
  try {
    // 准备配置
    const selectedTestObjects = props.availableTests.filter(test => 
      selectedTests.value.includes(test.id)
    )
    
    const config: ScriptGenerationConfig = {
      ...scriptConfig,
      tests: selectedTestObjects
    }
    
    // 开始生成过程
    updateProgress(10, '正在分析测试用例...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateProgress(30, '正在调用AI生成脚本...')
    const result = await batchGenerateScripts(config)
    
    updateProgress(60, '正在优化生成的脚本...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateProgress(80, '正在验证脚本语法...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateProgress(100, '脚本生成完成！', 'success')
    
    // 处理生成结果
    generationResult.value = {
      files: formatGenerationResult(result, scriptConfig.scriptType),
      config: config,
      timestamp: new Date()
    }
    
    // 设置默认选中的标签页
    const fileNames = Object.keys(generationResult.value.files)
    if (fileNames.length > 0) {
      activeResultTab.value = fileNames[0]
    }
    
    emit('generated', generationResult.value)
    
    ElMessage.success('脚本生成完成')
    
    setTimeout(() => {
      showProgress.value = false
      showResult.value = true
    }, 1500)
    
  } catch (error) {
    console.error('生成脚本失败:', error)
    progressStatus.value = 'exception'
    progressText.value = '生成失败，请重试'
    ElMessage.error('生成脚本失败')
  } finally {
    generating.value = false
  }
}

const updateProgress = (percentage: number, text: string, status?: 'success' | 'exception') => {
  progressPercentage.value = percentage
  progressText.value = text
  progressStatus.value = status
  progressLogs.value.push(text)
}

const formatGenerationResult = (result: any, scriptType: string): Record<string, string> => {
  const files: Record<string, string> = {}
  
  switch (scriptType) {
    case 'ci':
      files['ci.yml'] = result.configFile || '# CI配置文件'
      files['README.md'] = result.documentation || '# 使用说明'
      break
    case 'docker':
      files['Dockerfile'] = result.dockerfile || '# Dockerfile'
      files['docker-compose.yml'] = result.dockerCompose || '# Docker Compose配置'
      files['start.sh'] = result.startupScript || '#!/bin/bash\n# 启动脚本'
      break
    case 'data':
      files['setup-data.sql'] = result.setupScript || '-- 数据准备脚本'
      files['cleanup-data.sql'] = result.cleanupScript || '-- 数据清理脚本'
      break
    case 'monitoring':
      files['monitor.js'] = result.monitoringScript || '// 监控脚本'
      files['alerts.yml'] = result.alertRules || '# 报警规则'
      break
    case 'environment':
      files['env-switch.sh'] = result.switchScript || '#!/bin/bash\n# 环境切换脚本'
      files['config.json'] = result.configManagement || '// 配置管理'
      break
    case 'reporting':
      files['report-gen.js'] = result.generationScript || '// 报告生成脚本'
      files['template.html'] = result.templateScript || '<!-- 报告模板 -->'
      break
    case 'load':
      files['load-test.js'] = result.k6Script || '// K6负载测试脚本'
      files['benchmark.js'] = result.benchmarkScript || '// 性能基准脚本'
      break
    default:
      files['script.js'] = JSON.stringify(result, null, 2)
  }
  
  return files
}

const copyToClipboard = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const downloadAllFiles = () => {
  if (!generationResult.value) return
  
  // 创建ZIP文件并下载
  const files = generationResult.value.files
  const timestamp = new Date().toISOString().slice(0, 10)
  const scriptType = getScriptTypeLabel(scriptConfig.scriptType)
  
  // 这里应该集成ZIP库来创建压缩包
  // 暂时使用简单的文本下载
  Object.entries(files).forEach(([filename, content]) => {
    const blob = new Blob([content as string], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${timestamp}-${filename}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  })
  
  ElMessage.success('文件下载完成')
}

const applyToProject = () => {
  // 应用脚本到项目
  ElMessage.success('脚本已应用到项目')
  showResult.value = false
  visible.value = false
}

// 监听选中测试变化
watch(selectedTests, (newSelected) => {
  selectAllTests.value = newSelected.length === props.availableTests.length
})
</script>

<style lang="scss" scoped>
.script-generate-dialog {
  .script-type-selection,
  .script-config,
  .generation-preview {
    margin-bottom: 16px;
    
    :deep(.el-card__header) {
      padding: 12px 16px;
      font-weight: 500;
    }
  }
  
  .script-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    width: 100%;
    
    :deep(.el-radio-button) {
      margin: 0;
      
      .el-radio-button__inner {
        width: 100%;
        padding: 12px 8px;
        text-align: center;
        border-radius: 6px;
      }
    }
    
    .script-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      
      .el-icon {
        font-size: 20px;
      }
      
      span {
        font-size: 12px;
      }
    }
  }
  
  .test-selection {
    .test-list {
      max-height: 200px;
      overflow-y: auto;
      margin-top: 8px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      padding: 8px;
      
      .test-checkbox-item {
        margin-bottom: 8px;
        
        .test-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          
          .test-name {
            flex: 1;
            margin-right: 8px;
          }
        }
      }
    }
  }
  
  .preview-content {
    .file-list {
      margin-top: 16px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
          
          .el-icon {
            margin-right: 8px;
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

.generation-progress {
  text-align: center;
  
  .progress-text {
    margin: 16px 0;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
  
  .progress-logs {
    max-height: 200px;
    overflow-y: auto;
    text-align: left;
    margin-top: 20px;
    
    .progress-log {
      display: flex;
      align-items: center;
      padding: 4px 0;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      
      .el-icon {
        margin-right: 8px;
        color: var(--el-color-primary);
      }
    }
  }
}

.generation-result {
  .file-content {
    .file-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 12px;
      background-color: var(--el-bg-color-page);
      border-radius: 6px;
      
      span {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
    
    .code-textarea {
      :deep(textarea) {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
  
  .result-actions {
    margin-top: 16px;
    text-align: center;
    
    .el-button {
      margin: 0 8px;
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 8px;
  }
}
</style>