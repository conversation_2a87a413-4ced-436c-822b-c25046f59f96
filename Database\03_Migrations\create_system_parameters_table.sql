-- =============================================
-- 创建系统参数表
-- 创建时间: 2025-07-09
-- 说明: 用于存储各种系统配置参数，如技术栈选项、优先级选项等
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始创建系统参数表...';

-- 创建系统参数表
CREATE TABLE SystemParameters (
    Id int IDENTITY(1,1) NOT NULL,
    
    -- 参数分类和标识
    Category nvarchar(50) NOT NULL,           -- 参数分类：TechnologyStack, Priority, Status, etc.
    ParameterKey nvarchar(100) NOT NULL,      -- 参数键名
    ParameterValue nvarchar(500) NOT NULL,    -- 参数值
    DisplayName nvarchar(200) NOT NULL,       -- 显示名称
    Description nvarchar(1000) NULL,          -- 参数描述
    
    -- 排序和状态
    SortOrder int NOT NULL DEFAULT 0,         -- 排序顺序
    IsActive bit NOT NULL DEFAULT 1,          -- 是否启用
    IsSystem bit NOT NULL DEFAULT 0,          -- 是否系统参数（不可删除）
    
    -- 扩展属性
    ExtendedProperties nvarchar(max) NULL,    -- JSON格式的扩展属性
    
    -- 基础字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_SystemParameters PRIMARY KEY (Id),
    CONSTRAINT UK_SystemParameters_Category_Key UNIQUE (Category, ParameterKey),
    CONSTRAINT CK_SystemParameters_Category CHECK (Category IN (
        'TechnologyStack',      -- 技术栈
        'Priority',            -- 优先级
        'ProjectStatus',       -- 项目状态
        'RequirementStatus',   -- 需求状态
        'StepType',           -- 步骤类型
        'ComponentType',      -- 组件类型
        'AIProvider',         -- AI提供商
        'DatabaseType',       -- 数据库类型
        'PrototypeType',      -- 原型类型
        'DeviceType',         -- 设备类型
        'FidelityLevel',      -- 保真度级别
        'MessageType',        -- 消息类型
        'TaskStatus',         -- 任务状态
        'UserRole',           -- 用户角色
        'FileExtension',      -- 文件扩展名
        'Framework',          -- 框架
        'Tool',              -- 工具
        'Other'              -- 其他
    ))
);

-- 创建索引
CREATE INDEX IX_SystemParameters_Category ON SystemParameters(Category, IsActive, SortOrder);
CREATE INDEX IX_SystemParameters_IsActive ON SystemParameters(IsActive, SortOrder);
CREATE INDEX IX_SystemParameters_CreatedTime ON SystemParameters(CreatedTime);

PRINT '系统参数表创建完成';

-- 插入技术栈参数
PRINT '插入技术栈参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem, ExtendedProperties) VALUES
('TechnologyStack', 'vue_dotnet_sqlserver', 'Vue.js,ASP.NET Core,SQL Server', 'Vue.js + ASP.NET Core', '前端使用Vue.js，后端使用ASP.NET Core，数据库使用SQL Server', 1, 1, 1, '{"frontend": "Vue.js", "backend": "ASP.NET Core", "database": "SQL Server", "type": "web"}'),
('TechnologyStack', 'react_nodejs_mongodb', 'React,Node.js,MongoDB', 'React + Node.js', '前端使用React，后端使用Node.js，数据库使用MongoDB', 2, 1, 1, '{"frontend": "React", "backend": "Node.js", "database": "MongoDB", "type": "web"}'),
('TechnologyStack', 'angular_springboot_mysql', 'Angular,Spring Boot,MySQL', 'Angular + Spring Boot', '前端使用Angular，后端使用Spring Boot，数据库使用MySQL', 3, 1, 1, '{"frontend": "Angular", "backend": "Spring Boot", "database": "MySQL", "type": "web"}'),
('TechnologyStack', 'flutter_firebase', 'Flutter,Firebase,Firestore', 'Flutter + Firebase', '移动端使用Flutter，后端使用Firebase，数据库使用Firestore', 4, 1, 1, '{"frontend": "Flutter", "backend": "Firebase", "database": "Firestore", "type": "mobile"}'),
('TechnologyStack', 'reactnative_express_postgresql', 'React Native,Express,PostgreSQL', 'React Native + Express', '移动端使用React Native，后端使用Express，数据库使用PostgreSQL', 5, 1, 1, '{"frontend": "React Native", "backend": "Express", "database": "PostgreSQL", "type": "mobile"}'),
('TechnologyStack', 'wechat_miniprogram', '微信小程序,Node.js,MySQL', '微信小程序', '微信小程序开发，后端使用Node.js，数据库使用MySQL', 6, 1, 1, '{"frontend": "微信小程序", "backend": "Node.js", "database": "MySQL", "type": "miniprogram"}'),
('TechnologyStack', 'custom', 'Custom', '自定义技术栈', '用户自定义的技术栈组合', 99, 1, 1, '{"type": "custom"}');

-- 插入优先级参数
PRINT '插入优先级参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem) VALUES
('Priority', 'low', 'Low', '低', '低优先级', 1, 1, 1),
('Priority', 'medium', 'Medium', '中', '中等优先级', 2, 1, 1),
('Priority', 'high', 'High', '高', '高优先级', 3, 1, 1),
('Priority', 'urgent', 'Urgent', '紧急', '紧急优先级', 4, 1, 1);

-- 插入项目状态参数
PRINT '插入项目状态参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem) VALUES
('ProjectStatus', 'planning', 'Planning', '规划中', '项目处于规划阶段', 1, 1, 1),
('ProjectStatus', 'in_progress', 'InProgress', '进行中', '项目正在进行中', 2, 1, 1),
('ProjectStatus', 'testing', 'Testing', '测试中', '项目处于测试阶段', 3, 1, 1),
('ProjectStatus', 'completed', 'Completed', '已完成', '项目已完成', 4, 1, 1),
('ProjectStatus', 'on_hold', 'OnHold', '暂停', '项目暂时暂停', 5, 1, 1),
('ProjectStatus', 'cancelled', 'Cancelled', '已取消', '项目已取消', 6, 1, 1);

-- 插入需求状态参数
PRINT '插入需求状态参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem) VALUES
('RequirementStatus', 'draft', 'Draft', '草稿', '需求文档草稿状态', 1, 1, 1),
('RequirementStatus', 'review', 'Review', '审核中', '需求文档审核中', 2, 1, 1),
('RequirementStatus', 'approved', 'Approved', '已批准', '需求文档已批准', 3, 1, 1),
('RequirementStatus', 'rejected', 'Rejected', '已拒绝', '需求文档已拒绝', 4, 1, 1),
('RequirementStatus', 'published', 'Published', '已发布', '需求文档已发布', 5, 1, 1);

-- 插入步骤类型参数
PRINT '插入步骤类型参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem) VALUES
('StepType', 'analysis', 'Analysis', '分析', '需求分析步骤', 1, 1, 1),
('StepType', 'design', 'Design', '设计', '系统设计步骤', 2, 1, 1),
('StepType', 'development', 'Development', '开发', '代码开发步骤', 3, 1, 1),
('StepType', 'testing', 'Testing', '测试', '测试步骤', 4, 1, 1),
('StepType', 'deployment', 'Deployment', '部署', '部署步骤', 5, 1, 1),
('StepType', 'documentation', 'Documentation', '文档', '文档编写步骤', 6, 1, 1);

-- 插入组件类型参数
PRINT '插入组件类型参数...';

INSERT INTO SystemParameters (Category, ParameterKey, ParameterValue, DisplayName, Description, SortOrder, IsActive, IsSystem) VALUES
('ComponentType', 'frontend', 'Frontend', '前端', '前端组件', 1, 1, 1),
('ComponentType', 'backend', 'Backend', '后端', '后端组件', 2, 1, 1),
('ComponentType', 'database', 'Database', '数据库', '数据库组件', 3, 1, 1),
('ComponentType', 'api', 'API', 'API', 'API接口组件', 4, 1, 1),
('ComponentType', 'service', 'Service', '服务', '服务组件', 5, 1, 1),
('ComponentType', 'infrastructure', 'Infrastructure', '基础设施', '基础设施组件', 6, 1, 1);

PRINT '系统参数表初始化完成！';

-- 查询插入的数据统计
SELECT 
    Category,
    COUNT(*) as ParameterCount
FROM SystemParameters 
WHERE IsDeleted = 0
GROUP BY Category
ORDER BY Category;

PRINT '参数统计查询完成';
GO
