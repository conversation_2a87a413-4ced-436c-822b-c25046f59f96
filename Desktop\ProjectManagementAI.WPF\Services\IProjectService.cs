using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 项目服务接口
    /// </summary>
    public interface IProjectService
    {
        /// <summary>
        /// 检查服务器连接
        /// </summary>
        Task<bool> CheckConnectionAsync();

        /// <summary>
        /// 与服务器同步数据
        /// </summary>
        Task SyncWithServerAsync();

        /// <summary>
        /// 加载缓存数据
        /// </summary>
        Task LoadCacheDataAsync();

        /// <summary>
        /// 获取所有项目
        /// </summary>
        Task<IEnumerable<object>> GetAllProjectsAsync();

        /// <summary>
        /// 根据ID列表获取项目
        /// </summary>
        Task<IEnumerable<object>> GetProjectsByIdsAsync(IEnumerable<int> ids);

        /// <summary>
        /// 根据ID获取单个项目
        /// </summary>
        Task<object> GetProjectByIdAsync(int id);

        /// <summary>
        /// 创建项目
        /// </summary>
        Task<object> CreateProjectAsync(object project);

        /// <summary>
        /// 更新项目
        /// </summary>
        Task<object> UpdateProjectAsync(object project);

        /// <summary>
        /// 删除项目
        /// </summary>
        Task<bool> DeleteProjectAsync(int id);
    }
}
