using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;
using System.Text.Json;

namespace ProjectManagement.AI.Services;

/// <summary>
/// AI服务实现类
/// 提供统一的AI服务接口，支持多模型切换和故障转移
/// </summary>
public class AIService : IAIService
{
    private readonly ILogger<AIService> _logger;
    private readonly AIConfiguration _config;
    private readonly Dictionary<string, IAIProvider> _providers;
    private readonly IAIProvider _defaultProvider;
    private readonly IPromptBuilderService? _promptBuilderService;
    private readonly IPromptTemplateService? _promptTemplateService;
    private readonly IProjectRepository? _projectRepository;
    private readonly IUserTaskMappingRepository? _userTaskMappingRepository;
    private readonly IUserAIConfigurationRepository? _userAIConfigurationRepository;

    public AIService(
        ILogger<AIService> logger,
        IOptions<AIConfiguration> config,
        IEnumerable<IAIProvider> providers,
        IPromptBuilderService? promptBuilderService = null,
        IPromptTemplateService? promptTemplateService = null,
        IProjectRepository? projectRepository = null,
        IUserTaskMappingRepository? userTaskMappingRepository = null,
        IUserAIConfigurationRepository? userAIConfigurationRepository = null)
    {
        _logger = logger;
        _config = config.Value;
        _providers = providers.ToDictionary(p => p.Name, p => p);
        _promptBuilderService = promptBuilderService;
        _promptTemplateService = promptTemplateService;
        _projectRepository = projectRepository;
        _userTaskMappingRepository = userTaskMappingRepository;
        _userAIConfigurationRepository = userAIConfigurationRepository;

        // 设置默认提供商
        _defaultProvider = _providers.GetValueOrDefault(_config.DefaultProvider)
                          ?? _providers.Values.FirstOrDefault()
                          ?? throw new InvalidOperationException("没有可用的AI提供商");

        _logger.LogInformation("AI服务初始化完成，默认提供商: {Provider}, Prompt工程: {PromptEnabled}",
            _defaultProvider.Name, _promptBuilderService != null ? "启用" : "禁用");
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateTextAsync(string prompt, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始生成文本，提供商: {Provider}, 模型: {Model}, 端点: {Endpoint}, API密钥长度: {ApiKeyLength}",
                provider.Name, modelConfig.Model, modelConfig.Endpoint, modelConfig.ApiKey?.Length ?? 0);

            var result = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("文本生成完成，长度: {Length}", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文本生成失败，提供商: {Provider}", provider.Name);

            // 尝试故障转移
            if (provider != _defaultProvider)
            {
                _logger.LogInformation("尝试使用默认提供商进行故障转移");
                return await _defaultProvider.GenerateAsync(prompt, GetDefaultConfig());
            }

            throw;
        }
    }

    /// <summary>
    /// 生成文本（指定提供商和模型）
    /// </summary>
    public async Task<string> GenerateTextAsync(string prompt, string? provider = null, string? model = null)
    {
        var aiProvider = GetProvider(provider);
        var modelConfig = GetDefaultConfig();

        if (!string.IsNullOrEmpty(model))
        {
            modelConfig.Model = model;
        }

        try
        {
            _logger.LogInformation("开始生成文本，提供商: {Provider}, 模型: {Model}",
                aiProvider.Name, modelConfig.Model);

            var result = await aiProvider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("文本生成完成，长度: {Length}", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文本生成失败，提供商: {Provider}", aiProvider.Name);

            // 尝试故障转移
            if (aiProvider != _defaultProvider)
            {
                _logger.LogInformation("尝试使用默认提供商进行故障转移");
                return await _defaultProvider.GenerateAsync(prompt, GetDefaultConfig());
            }

            throw;
        }
    }

    /// <summary>
    /// 分析需求
    /// </summary>
    public async Task<RequirementAnalysisResult> AnalyzeRequirementsAsync(string requirements, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始需求分析，需求长度: {Length}", requirements.Length);

            // 使用Prompt工程构建提示词，如果可用的话
            string prompt;
            if (_promptBuilderService != null)
            {
                try
                {
                    var parameters = new Dictionary<string, object>
                    {
                        ["requirements"] = requirements
                    };

                    // 使用默认用户ID 1，实际应用中应该传入真实的用户ID
                    prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("RequirementAnalysis", parameters, 1);
                    _logger.LogInformation("使用Prompt模板构建提示词成功");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "使用Prompt模板失败，回退到硬编码提示词");
                    prompt = BuildRequirementAnalysisPrompt(requirements);
                }
            }
            else
            {
                prompt = BuildRequirementAnalysisPrompt(requirements);
            }

            var response = await provider.GenerateAsync(prompt, modelConfig);

            // 记录模板使用情况（如果启用了Prompt工程）
            if (_promptTemplateService != null && _promptBuilderService != null)
            {
                try
                {
                    var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("RequirementAnalysis");
                    if (defaultTemplate != null)
                    {
                        await _promptTemplateService.RecordTemplateUsageAsync(
                            defaultTemplate.Id, 1, null, provider.Name, modelConfig.Model,
                            System.Text.Json.JsonSerializer.Serialize(new { requirements }),
                            prompt, response, null, null, null, true, null);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "记录模板使用失败");
                }
            }

            // 解析AI响应为结构化数据
            var analysisResult = ParseRequirementAnalysisResponse(response);
            analysisResult.AIModel = $"{provider.Name}:{modelConfig.Model}";
            analysisResult.AnalysisTime = DateTime.Now;

            _logger.LogInformation("需求分析完成，功能需求数量: {Count}",
                analysisResult.FunctionalRequirements.Count);

            return analysisResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "需求分析失败");
            throw;
        }
    }

    /// <summary>
    /// 生成需求规格书
    /// </summary>
    public async Task<string> GenerateSpecificationAsync(RequirementAnalysisResult analysisResult, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始生成需求规格书");

            var prompt = BuildSpecificationPrompt(analysisResult);
            var specification = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("需求规格书生成完成，长度: {Length}", specification.Length);
            return specification;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "需求规格书生成失败");
            throw;
        }
    }

    /// <summary>
    /// 生成ER图代码
    /// </summary>
    public async Task<string> GenerateERDiagramAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null)
    {
        // 如果提供了配置，直接使用配置中的提供商
        IAIProvider provider;
        AIModelConfig modelConfig;

        if (config != null)
        {
            provider = GetProvider(config.Provider);
            modelConfig = config;
        }
        else
        {
            // 根据首选模型选择提供商，如果没有则使用默认提供商
            provider = GetProviderByModel(preferredModel) ?? _defaultProvider;
            modelConfig = GetModelConfigByPreference(preferredModel);
        }

        try
        {
            _logger.LogInformation("开始生成ER图，提供商: {Provider}, 模型: {Model}",
                provider.Name, modelConfig.Model);

            var erDiagram = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("ER图生成完成，长度: {Length}", erDiagram.Length);
            return erDiagram;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ER图生成失败，提供商: {Provider}", provider.Name);

            // 如果使用的是用户配置，不进行故障转移，直接抛出异常
            if (config != null)
            {
                throw;
            }

            // 尝试故障转移到默认提供商
            if (provider != _defaultProvider)
            {
                _logger.LogInformation("尝试使用默认提供商进行故障转移");
                return await _defaultProvider.GenerateAsync(prompt, GetDefaultConfig());
            }

            throw;
        }
    }

    /// <summary>
    /// 生成Context图代码
    /// </summary>
    public async Task<string> GenerateContextDiagramAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null)
    {
        // 如果提供了配置，直接使用配置中的提供商
        IAIProvider provider;
        AIModelConfig modelConfig;

        if (config != null)
        {
            provider = GetProvider(config.Provider);
            modelConfig = config;
        }
        else
        {
            // 根据首选模型选择提供商，如果没有则使用默认提供商
            provider = GetProviderByModel(preferredModel) ?? _defaultProvider;
            modelConfig = GetModelConfigByPreference(preferredModel);
        }

        try
        {
            _logger.LogInformation("开始生成Context图，提供商: {Provider}, 模型: {Model}",
                provider.Name, modelConfig.Model);

            var contextDiagram = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("Context图生成完成，长度: {Length}", contextDiagram.Length);
            return contextDiagram;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Context图生成失败，提供商: {Provider}", provider.Name);

            // 如果使用的是用户配置，不进行故障转移，直接抛出异常
            if (config != null)
            {
                throw;
            }

            // 尝试故障转移到默认提供商
            if (provider != _defaultProvider)
            {
                _logger.LogInformation("尝试使用默认提供商进行故障转移");
                return await _defaultProvider.GenerateAsync(prompt, GetDefaultConfig());
            }

            throw;
        }
    }

    /// <summary>
    /// 生成原型图代码
    /// </summary>
    public async Task<string> GeneratePrototypeAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null)
    {
        // 如果提供了配置，直接使用配置中的提供商
        IAIProvider provider;
        AIModelConfig modelConfig;

        if (config != null)
        {
            provider = GetProvider(config.Provider);
            modelConfig = config;
        }
        else
        {
            // 根据首选模型选择提供商，如果没有则使用默认提供商
            provider = GetProviderByModel(preferredModel) ?? _defaultProvider;
            modelConfig = GetModelConfigByPreference(preferredModel);
        }

        try
        {
            _logger.LogInformation("开始生成原型图，提供商: {Provider}, 模型: {Model}",
                provider.Name, modelConfig.Model);

            var prototype = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("原型图生成完成，长度: {Length}", prototype.Length);
            return prototype;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "原型图生成失败，提供商: {Provider}", provider.Name);

            // 如果使用的是用户配置，不进行故障转移，直接抛出异常
            if (config != null)
            {
                throw;
            }

            // 尝试故障转移到默认提供商
            if (provider != _defaultProvider)
            {
                _logger.LogInformation("尝试使用默认提供商进行故障转移");
                return await _defaultProvider.GenerateAsync(prompt, GetDefaultConfig());
            }

            throw;
        }
    }

    /// <summary>
    /// 生成代码
    /// </summary>
    public async Task<CodeGenerationResult> GenerateCodeAsync(string specification, CodeType codeType, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始生成代码，类型: {CodeType}", codeType);

            // 使用Prompt工程构建提示词，如果可用的话
            string prompt;
            if (_promptBuilderService != null)
            {
                try
                {
                    var parameters = new Dictionary<string, object>
                    {
                        ["specification"] = specification,
                        ["codeType"] = codeType.ToString()
                    };

                    prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("CodeGeneration", parameters, 1);
                    _logger.LogInformation("使用Prompt模板构建代码生成提示词成功");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "使用Prompt模板失败，回退到硬编码提示词");
                    prompt = BuildCodeGenerationPrompt(specification, codeType);
                }
            }
            else
            {
                prompt = BuildCodeGenerationPrompt(specification, codeType);
            }

            var codeResponse = await provider.GenerateAsync(prompt, modelConfig);

            // 记录模板使用情况（如果启用了Prompt工程）
            if (_promptTemplateService != null && _promptBuilderService != null)
            {
                try
                {
                    var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("CodeGeneration");
                    if (defaultTemplate != null)
                    {
                        await _promptTemplateService.RecordTemplateUsageAsync(
                            defaultTemplate.Id, 1, null, provider.Name, modelConfig.Model,
                            System.Text.Json.JsonSerializer.Serialize(new { specification, codeType }),
                            prompt, codeResponse, null, null, null, true, null);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "记录模板使用失败");
                }
            }

            var result = ParseCodeGenerationResponse(codeResponse, codeType);

            _logger.LogInformation("代码生成完成，文件数量: {Count}", result.GeneratedFiles.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "代码生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            return await provider.GetEmbeddingsAsync(text, modelConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取向量嵌入失败");
            throw;
        }
    }

    /// <summary>
    /// 生成测试用例
    /// </summary>
    public async Task<string> GenerateTestCasesAsync(string specification, string codeContent, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始生成测试用例");

            // 使用Prompt工程构建提示词，如果可用的话
            string prompt="";
            if (_promptBuilderService != null)
            {
                try
                {
                    var parameters = new Dictionary<string, object>
                    {
                        ["specification"] = specification,
                        ["codeContent"] = codeContent
                    };

                    prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("Testing", parameters, 1);
                    _logger.LogInformation("使用Prompt模板构建测试用例生成提示词成功");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "使用Prompt模板失败，回退到硬编码提示词");
                    prompt = "";
                }
            }


            var testCases = await provider.GenerateAsync(prompt, modelConfig);

            // 记录模板使用情况（如果启用了Prompt工程）
            if (_promptTemplateService != null && _promptBuilderService != null)
            {
                try
                {
                    var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("Testing");
                    if (defaultTemplate != null)
                    {
                        await _promptTemplateService.RecordTemplateUsageAsync(
                            defaultTemplate.Id, 1, null, provider.Name, modelConfig.Model,
                            System.Text.Json.JsonSerializer.Serialize(new { specification, codeContent }),
                            prompt, testCases, null, null, null, true, null);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "记录模板使用失败");
                }
            }

            _logger.LogInformation("测试用例生成完成");
            return testCases;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试用例生成失败");
            throw;
        }
    }

    /// <summary>
    /// 代码质量分析
    /// </summary>
    public async Task<CodeQualityAnalysisResult> AnalyzeCodeQualityAsync(string codeContent, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始代码质量分析");

            var prompt = BuildCodeQualityAnalysisPrompt(codeContent);
            var response = await provider.GenerateAsync(prompt, modelConfig);

            var result = ParseCodeQualityAnalysisResponse(response);

            _logger.LogInformation("代码质量分析完成，质量评分: {Score}", result.QualityScore);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "代码质量分析失败");
            throw;
        }
    }

    /// <summary>
    /// 性能优化建议
    /// </summary>
    public async Task<string> GenerateOptimizationSuggestionsAsync(string performanceData, AIModelConfig? config = null)
    {
        var provider = GetProvider(config?.Provider);
        var modelConfig = config ?? GetDefaultConfig();

        try
        {
            _logger.LogInformation("开始生成性能优化建议");

            var prompt = BuildOptimizationPrompt(performanceData);
            var suggestions = await provider.GenerateAsync(prompt, modelConfig);

            _logger.LogInformation("性能优化建议生成完成");
            return suggestions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能优化建议生成失败");
            throw;
        }
    }

    /// <summary>
    /// AI分解开发步骤
    /// </summary>
    public async Task<StepDecompositionResult> DecomposeStepAsync(DevelopmentStep step, StepDecompositionOptions options, int userId = 0, AIModelConfig? config = null)
    {
        try
        {
            _logger.LogInformation("开始AI分解步骤，StepId: {StepId}, StepName: {StepName}, UserId: {UserId}", step.Id, step.StepName, userId);

            // 获取项目背景信息
            Project? project = null;
            if (_projectRepository != null && step.ProjectId > 0)
            {
                try
                {
                    project = await _projectRepository.GetByIdAsync(step.ProjectId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取项目信息失败，ProjectId: {ProjectId}", step.ProjectId);
                }
            }

            // 构建提示词
            string prompt = await BuildStepDecompositionPromptAsync(step, options, project);

            // 调用AI服务并获取响应
            string response = await CallAIServiceWithUserConfigAsync(prompt, userId, "StepDecomposition", step, options, config);

            // 解析响应
            var result = ParseStepDecompositionResponse(response);

            _logger.LogInformation("AI分解步骤完成，生成 {Count} 个子步骤", result.Steps.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI分解步骤失败，StepId: {StepId}", step.Id);
            return new StepDecompositionResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }



    #region 私有方法

    /// <summary>
    /// 获取AI提供商
    /// </summary>
    private IAIProvider GetProvider(string? providerName = null)
    {
        if (string.IsNullOrEmpty(providerName))
            return _defaultProvider;

        // 检查是否是已注册的提供商
        if (_providers.ContainsKey(providerName))
        {
            return _providers[providerName];
        }

        // 对于自定义供应商，如果不在已注册的提供商中，使用OpenAI提供商
        // 因为大多数自定义AI服务都兼容OpenAI API格式
        _logger.LogInformation("未找到提供商 {ProviderName}，使用OpenAI兼容模式", providerName);
        return _providers.GetValueOrDefault("OpenAI") ?? _defaultProvider;
    }

    /// <summary>
    /// 根据模型名称获取提供商
    /// </summary>
    private IAIProvider? GetProviderByModel(string? modelName)
    {
        if (string.IsNullOrEmpty(modelName))
            return null;

        // 根据模型名称推断提供商
        var providerName = modelName.ToLower() switch
        {
            var m when m.Contains("gpt") => "Azure",
            var m when m.Contains("deepseek") => "DeepSeek",
            var m when m.Contains("claude") => "Claude",
            var m when m.Contains("mock") => "Mock",
            _ => null
        };

        return providerName != null ? _providers.GetValueOrDefault(providerName) : null;
    }

    /// <summary>
    /// 根据首选模型获取配置
    /// </summary>
    private AIModelConfig GetModelConfigByPreference(string? preferredModel)
    {
        var defaultConfig = GetDefaultConfig();

        if (string.IsNullOrEmpty(preferredModel))
            return defaultConfig;

        // 根据首选模型调整配置
        return preferredModel.ToLower() switch
        {
            "gpt-4" => new AIModelConfig
            {
                Provider = "Azure",
                Model = "gpt-4",
                MaxTokens = 8000,
                Temperature = 0.3f // 图表生成使用较低的温度以确保一致性
            },
            "claude-3" => new AIModelConfig
            {
                Provider = "Claude",
                Model = "claude-3-opus",
                MaxTokens = 4000,
                Temperature = 0.3f
            },
            "deepseek" => new AIModelConfig
            {
                Provider = "DeepSeek",
                Model = "deepseek-coder",
                MaxTokens = 4000,
                Temperature = 0.2f // 代码生成使用更低的温度
            },
            _ => defaultConfig
        };
    }

    /// <summary>
    /// 获取默认配置
    /// </summary>
    private AIModelConfig GetDefaultConfig()
    {
        return _config.DefaultConfig ?? new AIModelConfig
        {
            Provider = _defaultProvider.Name,
            Model = "mock-gpt-4",
            MaxTokens = 4000,
            Temperature = 0.7f
        };
    }

    /// <summary>
    /// 构建需求分析提示词
    /// </summary>
    private string BuildRequirementAnalysisPrompt(string requirements)
    {
        return $@"
请分析以下软件需求，并以JSON格式返回结构化的分析结果：

需求描述：
{requirements}

请按照以下JSON格式返回分析结果：
{{
    ""projectName"": ""项目名称"",
    ""projectDescription"": ""项目描述"",
    ""functionalRequirements"": [
        {{
            ""id"": ""FR001"",
            ""title"": ""功能标题"",
            ""description"": ""功能描述"",
            ""priority"": ""High/Medium/Low"",
            ""category"": ""功能分类""
        }}
    ],
    ""nonFunctionalRequirements"": [
        {{
            ""type"": ""性能/安全/可用性等"",
            ""description"": ""具体要求"",
            ""metrics"": ""衡量标准""
        }}
    ],
    ""userStories"": [
        {{
            ""role"": ""用户角色"",
            ""goal"": ""目标"",
            ""benefit"": ""价值""
        }}
    ],
    ""acceptanceCriteria"": [""验收标准1"", ""验收标准2""],
    ""techStack"": {{
        ""frontend"": [""技术栈""],
        ""backend"": [""技术栈""],
        ""database"": [""数据库""],
        ""other"": [""其他技术""]
    }},
    ""risks"": [
        {{
            ""description"": ""风险描述"",
            ""impact"": ""High/Medium/Low"",
            ""probability"": ""High/Medium/Low"",
            ""mitigation"": ""缓解措施""
        }}
    ],
    ""workloadEstimation"": {{
        ""totalHours"": 0,
        ""phases"": [
            {{
                ""name"": ""阶段名称"",
                ""hours"": 0,
                ""description"": ""阶段描述""
            }}
        ]
    }},
    ""feasibilityScore"": 8,
    ""complexityScore"": 6,
    ""confidenceScore"": 0.85
}}

请确保返回的是有效的JSON格式，不要包含任何其他文本。";
    }

    /// <summary>
    /// 解析需求分析响应
    /// </summary>
    private RequirementAnalysisResult ParseRequirementAnalysisResponse(string response)
    {
        try
        {
            // 清理响应文本，提取JSON部分
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');

            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonText = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                return JsonSerializer.Deserialize<RequirementAnalysisResult>(jsonText, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new RequirementAnalysisResult();
            }

            // 如果解析失败，返回默认结果
            _logger.LogWarning("无法解析AI响应为JSON格式，返回默认结果");
            return CreateDefaultAnalysisResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析需求分析响应失败");
            return CreateDefaultAnalysisResult();
        }
    }

    /// <summary>
    /// 创建默认分析结果
    /// </summary>
    private RequirementAnalysisResult CreateDefaultAnalysisResult()
    {
        return new RequirementAnalysisResult
        {
            ProjectName = "未知项目",
            ProjectDescription = "需求分析失败，请重新尝试",
            FeasibilityScore = 5,
            ComplexityScore = 5,
            ConfidenceScore = 0.5f
        };
    }

    /// <summary>
    /// 构建规格书生成提示词
    /// </summary>
    private string BuildSpecificationPrompt(RequirementAnalysisResult analysisResult)
    {
        return $@"
基于以下需求分析结果，生成详细的软件需求规格书：

项目名称：{analysisResult.ProjectName}
项目描述：{analysisResult.ProjectDescription}

功能需求：
{string.Join("\n", analysisResult.FunctionalRequirements.Select(fr => $"- {fr}"))}

非功能需求：
{string.Join("\n", analysisResult.NonFunctionalRequirements.Select(nfr => $"- {nfr}"))}

请生成一份完整的软件需求规格书，包含以下章节：
1. 项目概述
2. 功能需求详细说明
3. 非功能需求
4. 用户界面需求
5. 数据需求
6. 系统架构建议
7. 技术约束
8. 验收标准

请使用Markdown格式输出。";
    }

    /// <summary>
    /// 构建ER图生成提示词
    /// </summary>
    private string BuildERDiagramPrompt(string specification)
    {
        return $@"
基于以下需求规格书，生成数据库ER图的Mermaid代码：

{specification}

请生成Mermaid格式的ER图代码，包含：
1. 主要实体（Entity）
2. 实体属性（Attributes）
3. 实体关系（Relationships）
4. 主键和外键约束

请只返回Mermaid代码，不要包含其他文本。格式示例：
```mermaid
erDiagram
    USER ||--o{{ PROJECT : owns
    USER {{
        int id PK
        string username
        string email
        datetime created_at
    }}
    PROJECT {{
        int id PK
        string name
        string description
        int owner_id FK
        datetime created_at
    }}
```";
    }

    /// <summary>
    /// 构建Context图生成提示词
    /// </summary>
    private string BuildContextDiagramPrompt(string specification)
    {
        return $@"
基于以下需求规格书，生成系统上下文图的Mermaid代码：

{specification}

请生成Mermaid格式的系统上下文图，包含：
1. 系统边界
2. 外部实体（用户、外部系统）
3. 数据流
4. 系统组件

请只返回Mermaid代码，不要包含其他文本。";
    }

    /// <summary>
    /// 构建代码生成提示词
    /// </summary>
    private string BuildCodeGenerationPrompt(string specification, CodeType codeType)
    {
        var techStack = codeType switch
        {
            CodeType.Frontend => "Vue 3 + TypeScript + Element Plus",
            CodeType.Backend => "ASP.NET Core + C# + SqlSugar",
            CodeType.Database => "SQL Server",
            _ => "通用代码"
        };

        return $@"
基于以下需求规格书，生成{techStack}代码：

{specification}

代码类型：{codeType}

请生成完整的、可运行的代码文件，包含：
1. 必要的依赖和导入
2. 完整的类/组件定义
3. 适当的注释
4. 错误处理
5. 最佳实践

请以JSON格式返回，包含文件路径和内容：
{{
    ""files"": [
        {{
            ""path"": ""文件路径"",
            ""content"": ""文件内容"",
            ""language"": ""编程语言""
        }}
    ],
    ""description"": ""代码说明"",
    ""instructions"": ""使用说明""
}}";
    }

    /// <summary>
    /// 解析代码生成响应
    /// </summary>
    private CodeGenerationResult ParseCodeGenerationResponse(string response, CodeType codeType)
    {
        try
        {
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');

            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonText = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<dynamic>(jsonText);

                // 解析为CodeGenerationResult
                return new CodeGenerationResult
                {
                    CodeType = codeType,
                    GeneratedFiles = new List<GeneratedFile>(),
                    Description = "代码生成完成",
                    Instructions = "请按照说明使用生成的代码"
                };
            }

            return new CodeGenerationResult { CodeType = codeType };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析代码生成响应失败");
            return new CodeGenerationResult { CodeType = codeType };
        }
    }

    /// <summary>
    /// 构建代码质量分析提示词
    /// </summary>
    private string BuildCodeQualityAnalysisPrompt(string codeContent)
    {
        return $@"
请分析以下代码的质量，并提供改进建议：

{codeContent}

请从以下维度进行分析：
1. 代码可读性
2. 性能优化
3. 安全性
4. 最佳实践
5. 潜在bug

请以JSON格式返回分析结果。";
    }

    /// <summary>
    /// 解析代码质量分析响应
    /// </summary>
    private CodeQualityAnalysisResult ParseCodeQualityAnalysisResponse(string response)
    {
        // 简化实现，实际应该解析JSON
        return new CodeQualityAnalysisResult
        {
            QualityScore = 8,
            Issues = new List<CodeIssue>(),
            Suggestions = new List<string> { "代码质量良好" }
        };
    }

    /// <summary>
    /// 构建优化建议提示词
    /// </summary>
    private string BuildOptimizationPrompt(string performanceData)
    {
        return $@"
基于以下性能数据，提供系统优化建议：

{performanceData}

请提供具体的优化建议，包括：
1. 性能瓶颈分析
2. 优化方案
3. 实施步骤
4. 预期效果";
    }

    /// <summary>
    /// 从模板构建提示词
    /// </summary>
    private string BuildPromptFromTemplate(string templateContent, DevelopmentStep step, StepDecompositionOptions options, Project? project)
    {
        var granularityDescription = options.Granularity switch
        {
            "Fine" => "细粒度分解，每个子步骤应该是具体的、可执行的小任务",
            "Medium" => "中等粒度分解，每个子步骤应该是相对独立的功能模块",
            "Coarse" => "粗粒度分解，每个子步骤应该是主要的开发阶段",
            _ => "中等粒度分解"
        };

        // 替换模板中的占位符
        var prompt = templateContent
            .Replace("{ProjectName}", project?.Name ?? "未知项目")
            .Replace("{ProjectDescription}", project?.Description ?? "无项目描述")
            .Replace("{ProjectStatus}", project?.Status ?? "未知")
            .Replace("{ProjectPriority}", project?.Priority ?? "Medium")
            .Replace("{ProjectTechnologyStack}", project?.TechnologyStack ?? "未指定")
            .Replace("{ProjectProgress}", project?.Progress.ToString() ?? "0")
            .Replace("{ProjectEstimatedHours}", project?.EstimatedHours?.ToString() ?? "0")
            .Replace("{ProjectBudget}", project?.Budget?.ToString("C") ?? "未设定")
            .Replace("{StepName}", step.StepName)
            .Replace("{StepDescription}", step.StepDescription ?? "无描述")
            .Replace("{StepType}", step.StepType)
            .Replace("{StepTechnologyStack}", step.TechnologyStack ?? "未指定")
            .Replace("{StepFilePath}", step.FilePath ?? "未指定")
            .Replace("{StepComponentType}", step.ComponentType ?? "未指定")
            .Replace("{StepPriority}", step.Priority)
            .Replace("{StepEstimatedHours}", step.EstimatedHours?.ToString() ?? "0")
            .Replace("{StepLevel}", step.StepLevel.ToString())
            .Replace("{GranularityDescription}", granularityDescription)
            .Replace("{MaxSubSteps}", options.MaxSubSteps.ToString())
            .Replace("{TechnologyPreference}", options.TechnologyPreference ?? "继承父步骤")
            .Replace("{IncludeTestSteps}", options.IncludeTestSteps ? "是" : "否")
            .Replace("{IncludeDocumentationSteps}", options.IncludeDocumentationSteps ? "是" : "否")
            .Replace("{CustomRequirements}", options.CustomRequirements ?? "无");

        return prompt;
    }

    /// <summary>
    /// 构建步骤分解提示词（异步版本，支持模板）
    /// </summary>
    private async Task<string> BuildStepDecompositionPromptAsync(DevelopmentStep step, StepDecompositionOptions options, Project? project = null)
    {
        // 使用Prompt工程构建提示词，如果可用的话
        if (_promptTemplateService != null)
        {
            try
            {
                var template = await _promptTemplateService.GetDefaultTemplateAsync("StepDecomposition");
                if (template != null)
                {
                    var prompt = BuildPromptFromTemplate(template.Content, step, options, project);
                    _logger.LogInformation("使用Prompt模板构建步骤分解提示词成功");
                    return prompt;
                }
                else
                {
                    _logger.LogWarning("未找到StepDecomposition类型的默认模板，使用硬编码提示词");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "使用Prompt模板失败，回退到硬编码提示词");
            }
        }
        return null;
    }


    /// <summary>
    /// 根据用户配置调用AI服务
    /// </summary>
    private async Task<string> CallAIServiceWithUserConfigAsync(
        string prompt,
        int userId,
        string taskType,
        DevelopmentStep step,
        StepDecompositionOptions options,
        AIModelConfig? config = null)
    {
        try
        {
            // 如果提供了配置，直接使用
            if (config != null)
            {
                var provider = GetProvider(config.Provider);
                var response = await provider.GenerateAsync(prompt, config);
                _logger.LogInformation("使用提供的配置完成AI步骤分解");
                return response;
            }

            // 如果用户ID有效，尝试获取用户配置
            if (userId > 0 && _userTaskMappingRepository != null && _userAIConfigurationRepository != null)
            {
                // 1. 获取用户任务映射配置
                var userMappings = await _userTaskMappingRepository.GetByUserIdAndTaskTypeAsync(userId, taskType);
                var userMapping = userMappings.FirstOrDefault(x => x.IsActive) ?? userMappings.FirstOrDefault();

                if (userMapping != null)
                {
                    // 2. 根据ProviderName获取用户的AI配置
                    var userAIConfigs = await _userAIConfigurationRepository.GetByUserIdAndProviderAsync(userId, userMapping.ProviderName);
                    var userAIConfig = userAIConfigs.FirstOrDefault(x => x.IsActive && x.ModelType == taskType)
                                      ?? userAIConfigs.FirstOrDefault(x => x.IsActive);

                    if (userAIConfig != null)
                    {
                        // 3. 使用用户的AI配置
                        var userConfig = new AIModelConfig
                        {
                            Provider = userAIConfig.ProviderName,
                            Model = userAIConfig.ModelName,
                            ApiKey = userAIConfig.ApiKey ?? string.Empty,
                            Endpoint = userAIConfig.ApiEndpoint ?? string.Empty,
                            MaxTokens = 4000, // 可以从配置中获取
                            Temperature = 0.3f // 步骤分解使用较低的温度
                        };

                        _logger.LogInformation("使用用户AI配置进行步骤分解: {Provider}, {Model}, 用户: {UserId}",
                            userAIConfig.ProviderName, userAIConfig.ModelName, userId);

                        var provider = GetProvider(userConfig.Provider);
                        var response = await provider.GenerateAsync(prompt, userConfig);

                        // 4. 更新使用统计
                        await _userAIConfigurationRepository.UpdateUsageStatisticsAsync(userAIConfig.Id, 4000, true);

                        return response;
                    }
                    else
                    {
                        _logger.LogWarning("用户没有配置AI提供商 {ProviderName}，使用默认配置", userMapping.ProviderName);
                    }
                }
                else
                {
                    _logger.LogInformation("用户 {UserId} 没有配置任务类型 {TaskType} 的映射，使用默认配置", userId, taskType);
                }
            }

            // 6. 使用默认配置
            var defaultProvider = GetProvider();
            var defaultConfig = GetDefaultConfig();
            var defaultResponse = await defaultProvider.GenerateAsync(prompt, defaultConfig);

            _logger.LogInformation("使用默认配置完成AI步骤分解");
            return defaultResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调用AI服务失败，用户: {UserId}, 任务类型: {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 解析步骤分解响应
    /// </summary>
    private StepDecompositionResult ParseStepDecompositionResponse(string response)
    {
        try
        {
            // 清理响应文本，提取JSON部分
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');

            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonText = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<StepDecompositionResult>(jsonText, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new StepDecompositionResult();

                return parsed;
            }

            // 如果解析失败，返回默认结果
            _logger.LogWarning("无法解析AI步骤分解响应为JSON格式");
            return new StepDecompositionResult
            {
                Success = false,
                ErrorMessage = "AI响应格式无效"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析步骤分解响应失败");
            return new StepDecompositionResult
            {
                Success = false,
                ErrorMessage = $"解析失败：{ex.Message}"
            };
        }
    }

    #endregion
}
