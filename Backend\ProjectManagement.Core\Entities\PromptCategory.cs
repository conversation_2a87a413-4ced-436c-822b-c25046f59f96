using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Prompt分类实体 - 用于管理提示词模板的分类
/// 功能: 组织和分类不同类型的提示词模板
/// 支持: 层级分类、图标管理、排序
/// </summary>
[SugarTable("PromptCategories", "AI提示词分类管理表")]
public class PromptCategory : BaseEntity
{
    /// <summary>
    /// 分类名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "分类名称")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 分类描述
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "分类描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 父分类ID，支持层级分类
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "父分类ID")]
    public int? ParentId { get; set; }

    /// <summary>
    /// 分类图标
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "分类图标")]
    public string? Icon { get; set; }

    /// <summary>
    /// 分类颜色
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "分类颜色")]
    public string? Color { get; set; }

    /// <summary>
    /// 排序权重，数值越小越靠前
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "排序权重")]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 模板数量统计
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "模板数量统计")]
    public int TemplateCount { get; set; } = 0;

    /// <summary>
    /// 父分类 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptCategory? Parent { get; set; }

    /// <summary>
    /// 子分类列表 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PromptCategory> Children { get; set; } = new();

    /// <summary>
    /// 该分类下的模板列表 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PromptTemplate> Templates { get; set; } = new();
}
