/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddStepsToTaskDialog: typeof import('./src/components/development/AddStepsToTaskDialog.vue')['default']
    AIDialog: typeof import('./src/components/common/AIDialog.vue')['default']
    AITestAssistant: typeof import('./src/components/test/AITestAssistant.vue')['default']
    AITestGenerateDialog: typeof import('./src/components/test/AITestGenerateDialog.vue')['default']
    AppBreadcrumb: typeof import('./src/components/layout/AppBreadcrumb.vue')['default']
    AppHeader: typeof import('./src/components/layout/AppHeader.vue')['default']
    AppSidebar: typeof import('./src/components/layout/AppSidebar.vue')['default']
    AppTabs: typeof import('./src/components/layout/AppTabs.vue')['default']
    AuthDebugger: typeof import('./src/components/AuthDebugger.vue')['default']
    CategoryFormDialog: typeof import('./src/components/automation/CategoryFormDialog.vue')['default']
    CodeSequenceCreator: typeof import('./src/components/automation/CodeSequenceCreator.vue')['default']
    CodeSequenceSaveDialog: typeof import('./src/components/automation/CodeSequenceSaveDialog.vue')['default']
    CodingTaskCreateDialog: typeof import('./src/components/development/CodingTaskCreateDialog.vue')['default']
    CodingTaskDetailDialog: typeof import('./src/components/development/CodingTaskDetailDialog.vue')['default']
    DevelopmentStepsPanel: typeof import('./src/components/development/DevelopmentStepsPanel.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElLoading: typeof import('element-plus/es')['ElLoading']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExecutionParametersDialog: typeof import('./src/components/automation/ExecutionParametersDialog.vue')['default']
    ImportTemplateDialog: typeof import('./src/components/automation/ImportTemplateDialog.vue')['default']
    MermaidSequenceCreator: typeof import('./src/components/automation/MermaidSequenceCreator.vue')['default']
    MonacoEditor: typeof import('./src/components/common/MonacoEditor.vue')['default']
    PlaywrightConfig: typeof import('./src/components/test/PlaywrightConfig.vue')['default']
    RequirementDecomposeDialog: typeof import('./src/components/development/RequirementDecomposeDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScriptDialog: typeof import('./src/components/test/ScriptDialog.vue')['default']
    ScriptGenerateDialog: typeof import('./src/components/test/ScriptGenerateDialog.vue')['default']
    SeleniumConfig: typeof import('./src/components/test/SeleniumConfig.vue')['default']
    SequenceDetailPanel: typeof import('./src/components/automation/SequenceDetailPanel.vue')['default']
    SequenceFlowChart: typeof import('./src/components/automation/SequenceFlowChart.vue')['default']
    SequenceFormDialog: typeof import('./src/components/automation/SequenceFormDialog.vue')['default']
    StepCard: typeof import('./src/components/development/StepCard.vue')['default']
    StepComplexityAnalysis: typeof import('./src/components/development/StepComplexityAnalysis.vue')['default']
    StepCreateDialog: typeof import('./src/components/development/StepCreateDialog.vue')['default']
    StepCustomDecomposeDialog: typeof import('./src/components/development/StepCustomDecomposeDialog.vue')['default']
    StepDecomposeDialog: typeof import('./src/components/development/StepDecomposeDialog.vue')['default']
    StepDependencyManager: typeof import('./src/components/development/StepDependencyManager.vue')['default']
    StepDetailDialog: typeof import('./src/components/development/StepDetailDialog.vue')['default']
    StepEditDialog: typeof import('./src/components/development/StepEditDialog.vue')['default']
    StepExecutionHistory: typeof import('./src/components/development/StepExecutionHistory.vue')['default']
    StepFormDialog: typeof import('./src/components/automation/StepFormDialog.vue')['default']
    TemplateFormDialog: typeof import('./src/components/automation/TemplateFormDialog.vue')['default']
    TemplateSequenceSelectionDialog: typeof import('./src/components/development/TemplateSequenceSelectionDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
