using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.Core.DTOs.LangChain;
using System.Text.Json;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// LangChain集成服务 - 提供链式推理和复杂工作流支持
    /// </summary>
    public class LangChainService
    {
        private readonly ILogger<LangChainService> _logger;
        private readonly IAIService _aiService;

        public LangChainService(ILogger<LangChainService> logger, IAIService aiService)
        {
            _logger = logger;
            _aiService = aiService;
        }

        /// <summary>
        /// 执行推理链
        /// </summary>
        public async Task<ChainExecutionResult> ExecuteChainAsync(ChainDefinition chain, ChainContext context, AIModelConfig? aiConfig = null)
        {
            _logger.LogInformation("开始执行推理链: {ChainName}", chain.Name);

            var result = new ChainExecutionResult
            {
                ChainName = chain.Name,
                StartTime = DateTime.UtcNow,
                Steps = new List<ChainStepResult>()
            };

            try
            {
                var chainContext = context.Clone();

                foreach (var step in chain.Steps)
                {
                    var stepResult = await ExecuteChainStepAsync(step, chainContext, aiConfig);
                    result.Steps.Add(stepResult);

                    if (!stepResult.Success)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"步骤 '{step.Name}' 执行失败: {stepResult.ErrorMessage}";
                        break;
                    }

                    // 将步骤结果添加到上下文中，供后续步骤使用
                    chainContext.AddStepResult(step.Name, stepResult.Output);
                }

                result.Success = result.Steps.All(s => s.Success);
                result.FinalOutput = result.Steps.LastOrDefault()?.Output ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推理链执行失败: {ChainName}", chain.Name);
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// 执行单个推理步骤
        /// </summary>
        private async Task<ChainStepResult> ExecuteChainStepAsync(ChainStep step, ChainContext context, AIModelConfig? aiConfig)
        {
            _logger.LogInformation("执行推理步骤: {StepName}", step.Name);

            var stepResult = new ChainStepResult
            {
                StepName = step.Name,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // 构建步骤的提示词，替换上下文变量
                var prompt = BuildStepPrompt(step, context);

                // 调用AI服务
                var aiResponse = await _aiService.GenerateTextAsync(prompt, aiConfig);

                stepResult.Success = true;
                stepResult.Output = aiResponse;
                stepResult.Metadata = new Dictionary<string, object>
                {
                    ["prompt_length"] = prompt.Length,
                    ["response_length"] = aiResponse.Length,
                    ["step_type"] = step.Type
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推理步骤执行失败: {StepName}", step.Name);
                stepResult.Success = false;
                stepResult.ErrorMessage = ex.Message;
            }
            finally
            {
                stepResult.EndTime = DateTime.UtcNow;
                stepResult.Duration = stepResult.EndTime - stepResult.StartTime;
            }

            return stepResult;
        }

        /// <summary>
        /// 构建步骤提示词
        /// </summary>
        private string BuildStepPrompt(ChainStep step, ChainContext context)
        {
            var prompt = step.PromptTemplate;

            // 替换上下文变量
            foreach (var variable in context.Variables)
            {
                prompt = prompt.Replace($"{{{variable.Key}}}", variable.Value?.ToString() ?? "");
            }

            // 替换前置步骤结果
            foreach (var stepResult in context.StepResults)
            {
                prompt = prompt.Replace($"{{step_{stepResult.Key}}}", stepResult.Value?.ToString() ?? "");
            }

            return prompt;
        }

        /// <summary>
        /// 获取预定义的推理链
        /// </summary>
        public ChainDefinition GetPredefinedChain(string chainType)
        {
            return chainType.ToLower() switch
            {
                "project-analysis" => GetProjectAnalysisChain(),
                "tech-evaluation" => GetTechEvaluationChain(),
                "risk-assessment" => GetRiskAssessmentChain(),
                "requirement-analysis" => GetRequirementAnalysisChain(),
                _ => throw new ArgumentException($"未知的推理链类型: {chainType}")
            };
        }

        /// <summary>
        /// 项目分析推理链
        /// </summary>
        private ChainDefinition GetProjectAnalysisChain()
        {
            return new ChainDefinition
            {
                Name = "项目分析链",
                Description = "全面分析项目的各个方面",
                Steps = new List<ChainStep>
                {
                    new ChainStep
                    {
                        Name = "需求分析",
                        Type = "analysis",
                        PromptTemplate = @"
作为需求分析专家，请分析以下项目信息：

项目名称：{projectName}
项目描述：{projectDescription}
用户需求：{userMessage}

请从以下角度进行需求分析：
1. 功能需求识别
2. 非功能需求识别
3. 约束条件分析
4. 优先级评估

请提供详细的需求分析报告。"
                    },
                    new ChainStep
                    {
                        Name = "技术选型",
                        Type = "recommendation",
                        PromptTemplate = @"
基于前面的需求分析结果：
{step_需求分析}

作为技术架构师，请推荐合适的技术栈：
1. 前端技术选择及理由
2. 后端技术选择及理由
3. 数据库选择及理由
4. 部署方案推荐

请考虑项目规模、团队技能、维护成本等因素。"
                    },
                    new ChainStep
                    {
                        Name = "架构设计",
                        Type = "design",
                        PromptTemplate = @"
基于需求分析：
{step_需求分析}

和技术选型：
{step_技术选型}

请设计系统架构：
1. 整体架构图描述
2. 模块划分和职责
3. 数据流设计
4. 接口设计原则

请提供清晰的架构设计方案。"
                    },
                    new ChainStep
                    {
                        Name = "风险评估",
                        Type = "assessment",
                        PromptTemplate = @"
综合前面的分析结果：
需求分析：{step_需求分析}
技术选型：{step_技术选型}
架构设计：{step_架构设计}

请进行项目风险评估：
1. 技术风险识别和应对
2. 进度风险评估
3. 资源风险分析
4. 质量风险控制

请提供风险评估报告和建议的应对措施。"
                    }
                }
            };
        }

        /// <summary>
        /// 技术评估推理链
        /// </summary>
        private ChainDefinition GetTechEvaluationChain()
        {
            return new ChainDefinition
            {
                Name = "技术评估链",
                Description = "评估技术方案的可行性和优劣",
                Steps = new List<ChainStep>
                {
                    new ChainStep
                    {
                        Name = "技术调研",
                        Type = "research",
                        PromptTemplate = @"
请对以下技术进行深入调研：
技术名称：{technology}
应用场景：{scenario}

调研内容：
1. 技术成熟度和稳定性
2. 社区活跃度和支持
3. 学习成本和上手难度
4. 性能特点和适用场景
5. 与现有技术栈的兼容性"
                    },
                    new ChainStep
                    {
                        Name = "优劣分析",
                        Type = "analysis",
                        PromptTemplate = @"
基于技术调研结果：
{step_技术调研}

请进行优劣分析：
1. 主要优势和亮点
2. 潜在问题和限制
3. 与竞品技术对比
4. 适用和不适用场景
5. 总体评价和建议"
                    }
                }
            };
        }

        /// <summary>
        /// 风险评估推理链
        /// </summary>
        private ChainDefinition GetRiskAssessmentChain()
        {
            return new ChainDefinition
            {
                Name = "风险评估链",
                Description = "全面评估项目风险",
                Steps = new List<ChainStep>
                {
                    new ChainStep
                    {
                        Name = "风险识别",
                        Type = "identification",
                        PromptTemplate = @"
项目信息：
{projectContext}

请识别项目可能面临的风险：
1. 技术风险
2. 进度风险
3. 资源风险
4. 质量风险
5. 外部风险

请详细列出每类风险的具体表现。"
                    },
                    new ChainStep
                    {
                        Name = "风险评估",
                        Type = "assessment",
                        PromptTemplate = @"
基于识别的风险：
{step_风险识别}

请对每个风险进行评估：
1. 发生概率（高/中/低）
2. 影响程度（高/中/低）
3. 风险等级（高/中/低）
4. 影响范围和后果

请提供风险评估矩阵。"
                    },
                    new ChainStep
                    {
                        Name = "应对策略",
                        Type = "strategy",
                        PromptTemplate = @"
基于风险评估：
{step_风险评估}

请制定风险应对策略：
1. 高风险项的应对措施
2. 中风险项的监控方案
3. 低风险项的关注要点
4. 应急预案和备选方案

请提供具体可执行的应对策略。"
                    }
                }
            };
        }

        /// <summary>
        /// 需求分析推理链
        /// </summary>
        private ChainDefinition GetRequirementAnalysisChain()
        {
            return new ChainDefinition
            {
                Name = "需求分析链",
                Description = "深入分析和整理用户需求",
                Steps = new List<ChainStep>
                {
                    new ChainStep
                    {
                        Name = "需求收集",
                        Type = "collection",
                        PromptTemplate = @"
用户原始需求：
{userMessage}

项目背景：
{projectContext}

请收集和整理需求信息：
1. 明确的功能需求
2. 隐含的功能需求
3. 非功能需求
4. 约束条件
5. 假设条件

请将模糊的需求具体化，将隐含的需求显性化。"
                    },
                    new ChainStep
                    {
                        Name = "需求分析",
                        Type = "analysis",
                        PromptTemplate = @"
整理后的需求：
{step_需求收集}

请进行深入的需求分析：
1. 需求的合理性分析
2. 需求之间的依赖关系
3. 需求的优先级排序
4. 需求的可行性评估
5. 潜在的需求冲突

请提供结构化的需求分析报告。"
                    },
                    new ChainStep
                    {
                        Name = "需求规格",
                        Type = "specification",
                        PromptTemplate = @"
需求分析结果：
{step_需求分析}

请编写需求规格说明：
1. 功能需求规格（用例图、用例描述）
2. 非功能需求规格（性能、安全、可用性等）
3. 接口需求规格
4. 数据需求规格
5. 验收标准定义

请提供详细的需求规格文档。"
                    }
                }
            };
        }
    }
}
