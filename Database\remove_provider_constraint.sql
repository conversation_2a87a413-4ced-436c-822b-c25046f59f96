-- =============================================
-- 删除UserAIConfigurations表的约束
-- 允许添加自定义AI供应商和模型类型
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始删除UserAIConfigurations表的约束...';

-- 删除ProviderName约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserAIConfigurations_ProviderName')
BEGIN
    PRINT '找到约束 CK_UserAIConfigurations_ProviderName，正在删除...';

    ALTER TABLE UserAIConfigurations
    DROP CONSTRAINT CK_UserAIConfigurations_ProviderName;

    PRINT '约束 CK_UserAIConfigurations_ProviderName 已成功删除';
END
ELSE
BEGIN
    PRINT '约束 CK_UserAIConfigurations_ProviderName 不存在，无需删除';
END

-- 删除ModelType约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserAIConfigurations_ModelType')
BEGIN
    PRINT '找到约束 CK_UserAIConfigurations_ModelType，正在删除...';

    ALTER TABLE UserAIConfigurations
    DROP CONSTRAINT CK_UserAIConfigurations_ModelType;

    PRINT '约束 CK_UserAIConfigurations_ModelType 已成功删除';
END
ELSE
BEGIN
    PRINT '约束 CK_UserAIConfigurations_ModelType 不存在，无需删除';
END
GO

-- 验证约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserAIConfigurations_ProviderName')
   AND NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserAIConfigurations_ModelType')
BEGIN
    PRINT '验证成功：所有约束已删除，现在可以添加自定义AI供应商和模型类型';
END
ELSE
BEGIN
    PRINT '警告：部分约束删除失败，请手动检查';
END
GO

-- 显示当前表的所有约束
PRINT '当前UserAIConfigurations表的约束列表：';
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition,
    cc.is_disabled AS IsDisabled
FROM sys.check_constraints cc
INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
WHERE t.name = 'UserAIConfigurations'
ORDER BY cc.name;
GO

PRINT '约束删除操作完成！';
PRINT '现在可以在UserAIConfigurations表中添加任意名称的AI供应商和模型类型';
