<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title">
            <el-icon><UserFilled /></el-icon>
            <span>用户管理</span>
          </div>
          <div class="page-description">
            管理系统中的所有用户账户
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="showCreateUserDialog">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="搜索用户名、邮箱或真实姓名"
              style="width: 300px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="角色">
            <el-select v-model="searchForm.role" placeholder="选择角色" clearable style="width: 150px">
              <el-option label="全部" value="" />
              <el-option label="普通用户" value="User" />
              <el-option label="项目经理" value="ProjectManager" />
              <el-option label="开发者" value="Developer" />
              <el-option label="测试员" value="Tester" />
              <el-option label="产品经理" value="ProductManager" />
              <el-option label="管理员" value="Admin" />
              <el-option label="超级管理员" value="SuperAdmin" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="2" />
              <el-option label="暂停" :value="3" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.activeUsers }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon new">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.newUsersThisMonth }}</div>
                <div class="stat-label">本月新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon inactive">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.inactiveUsers }}</div>
                <div class="stat-label">非活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="table-header">
            <span>用户列表</span>
            <div class="table-actions">
              <el-button size="small" @click="loadUsers">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="users"
          stripe
          class="user-table"
        >
          <el-table-column type="index" label="#" width="60" />
          
          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="40" :src="getAvatarUrl(row.avatar)">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
                <div class="user-details">
                  <div class="username">{{ row.username }}</div>
                  <div class="real-name">{{ row.realName || '未设置' }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="email" label="邮箱" min-width="180" />
          
          <el-table-column label="角色" width="120">
            <template #default="{ row }">
              <el-tag :type="getRoleTagType(row.role) || undefined">
                {{ getRoleText(row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status) || undefined">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="最后登录" width="160">
            <template #default="{ row }">
              {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewUser(row)">
                查看
              </el-button>
              <el-button type="warning" size="small" @click="editUser(row)">
                编辑
              </el-button>
              <el-dropdown @command="(command) => handleUserAction(command, row)">
                <el-button type="info" size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                    <el-dropdown-item command="toggleStatus">
                      {{ row.status === 1 ? '禁用' : '启用' }}账户
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除用户</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="isEditMode ? '编辑用户' : '新增用户'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            :disabled="isEditMode"
            placeholder="请输入用户名"
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input 
            v-model="userForm.email" 
            placeholder="请输入邮箱"
          />
        </el-form-item>
        
        <el-form-item v-if="!isEditMode" label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            type="password"
            show-password
            placeholder="请输入密码"
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input 
            v-model="userForm.realName" 
            placeholder="请输入真实姓名"
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="选择角色" style="width: 100%">
            <el-option label="普通用户" value="User" />
            <el-option label="项目经理" value="ProjectManager" />
            <el-option label="开发者" value="Developer" />
            <el-option label="测试员" value="Tester" />
            <el-option label="产品经理" value="ProductManager" />
            <el-option label="管理员" value="Admin" />
            <el-option label="超级管理员" value="SuperAdmin" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">禁用</el-radio>
            <el-radio :label="3">暂停</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitting"
          @click="submitUser"
        >
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { UserService } from '@/services/user'
import dayjs from 'dayjs'
import {
  UserFilled,
  Plus,
  Search,
  Refresh,
  Check,
  Close,
  ArrowDown
} from '@element-plus/icons-vue'
import type {
  User,
  UserQueryParams,
  UserStatisticsDto,
  UpdateUserRequestDto
} from '@/types/user'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const userDialogVisible = ref(false)
const isEditMode = ref(false)
const currentEditUser = ref<User | null>(null)

// 表单引用
const userFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  search: '',
  role: '',
  status: ''
})

// 用户表单
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  realName: '',
  role: 'User',
  status: 1
})

// 用户列表和分页
const users = ref<User[]>([])
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 统计信息
const statistics = ref<UserStatisticsDto>({
  totalUsers: 0,
  activeUsers: 0,
  newUsersThisMonth: 0,
  inactiveUsers: 0
})

// 表单验证规则
const userFormRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  realName: [
    { max: 50, message: '真实姓名不能超过50个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    'User': '普通用户',
    'ProjectManager': '项目经理',
    'Developer': '开发者',
    'Tester': '测试员',
    'ProductManager': '产品经理',
    'Admin': '管理员',
    'SuperAdmin': '超级管理员'
  }
  return roleMap[role] || role
}

const getRoleTagType = (role: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | '' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger' | ''> = {
    'SuperAdmin': 'danger',
    'Admin': 'warning',
    'ProjectManager': 'success',
    'Developer': 'primary',
    'Tester': 'info',
    'ProductManager': 'success',
    'User': ''
  }
  return typeMap[role] || ''
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    2: '禁用',
    3: '暂停',
    4: '待激活',
    5: '已删除'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: number): 'success' | 'primary' | 'warning' | 'info' | 'danger' | '' => {
  const typeMap: Record<number, 'success' | 'primary' | 'warning' | 'info' | 'danger' | ''> = {
    1: 'success',
    2: 'danger',
    3: 'warning',
    4: 'info',
    5: 'info'
  }
  return typeMap[status] || ''
}

const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getAvatarUrl = (avatar?: string) => {
  if (!avatar) return ''
  // 如果是完整URL，直接返回
  if (avatar.startsWith('http')) return avatar
  // 如果是相对路径，添加后端服务器地址
  return `https://localhost:61136/${avatar}`
}

const loadUsers = async () => {
  try {
    loading.value = true

    const params: UserQueryParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: searchForm.search || undefined
    }

    const result = await UserService.getUsers(params)

    users.value = result.items
    pagination.total = result.totalCount

  } catch (error: any) {
    console.error('Failed to load users:', error)
    ElMessage.error(error.message || '加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    statistics.value = await UserService.getUserStatistics()
  } catch (error: any) {
    console.error('Failed to load statistics:', error)
    ElMessage.error(error.message || '加载统计信息失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

const resetSearch = () => {
  searchForm.search = ''
  searchForm.role = ''
  searchForm.status = ''
  pagination.page = 1
  loadUsers()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUsers()
}

const showCreateUserDialog = () => {
  isEditMode.value = false
  currentEditUser.value = null
  resetUserForm()
  userDialogVisible.value = true
}

const resetUserForm = () => {
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.realName = ''
  userForm.role = 'User'
  userForm.status = 1
}

const viewUser = (user: User) => {
  ElMessage.info('查看用户详情功能开发中...')
}

const editUser = (user: User) => {
  isEditMode.value = true
  currentEditUser.value = user

  // 填充表单数据
  userForm.username = user.username
  userForm.email = user.email
  userForm.password = ''
  userForm.realName = user.realName || ''
  userForm.role = user.role
  userForm.status = user.status

  userDialogVisible.value = true
}

const submitUser = async () => {
  if (!userFormRef.value) return

  try {
    const valid = await userFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (isEditMode.value && currentEditUser.value) {
      // 编辑用户
      const updateData: UpdateUserRequestDto = {
        realName: userForm.realName,
        role: userForm.role,
        status: userForm.status
      }

      await UserService.updateUser(currentEditUser.value.id, updateData)
      ElMessage.success('用户信息更新成功')
    } else {
      // 新增用户 - 这里需要后端支持管理员创建用户的API
      ElMessage.info('新增用户功能需要后端API支持')
    }

    userDialogVisible.value = false
    loadUsers()
    loadStatistics()

  } catch (error: any) {
    console.error('Failed to submit user:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleUserAction = async (command: string, user: User) => {
  switch (command) {
    case 'resetPassword':
      await resetUserPassword(user)
      break
    case 'toggleStatus':
      await toggleUserStatus(user)
      break
    case 'delete':
      await deleteUser(user)
      break
  }
}

const resetUserPassword = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 "${user.username}" 的密码吗？`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('重置密码功能需要后端API支持')

  } catch {
    // 用户取消
  }
}

const toggleUserStatus = async (user: User) => {
  const action = user.status === 1 ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info(`${action}用户功能需要后端API支持`)

  } catch {
    // 用户取消
  }
}

const deleteUser = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '删除用户',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    ElMessage.info('删除用户功能需要后端API支持')

  } catch {
    // 用户取消
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 16px 24px;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
  }

  .header-left {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    .page-description {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.search-section,
.statistics-section,
.table-section {
  padding: 16px 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.statistics-section {
  .stat-card {
    :deep(.el-card__body) {
      padding: 20px;
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;

      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.active {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.new {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.inactive {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }

    .stat-info {
      flex: 1;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-table {
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-details {
      .username {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }

      .real-name {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 1200px) {
  .search-section,
  .statistics-section,
  .table-section {
    padding: 16px;
  }

  .page-header .header-content {
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .page-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .statistics-section {
    :deep(.el-col) {
      margin-bottom: 16px;
    }
  }

  .user-table {
    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }
}
</style>
