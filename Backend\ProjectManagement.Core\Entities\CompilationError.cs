using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 编译错误实体
    /// </summary>
    [SugarTable("CompilationErrors", "编译错误信息管理表")]
    public class CompilationError : BaseEntity
    {
        /// <summary>
        /// 关联项目ID
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// 项目类型 (Backend/Frontend)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "项目类型")]
        public string ProjectType { get; set; } = string.Empty;

        /// <summary>
        /// 编译会话ID
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "编译会话ID")]
        public string? CompilationSessionId { get; set; }

        /// <summary>
        /// 严重程度 (Error/Warning/Info/Hidden)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "严重程度")]
        public string Severity { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "错误代码")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "错误信息")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false, ColumnDescription = "文件路径")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 行号
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// 列号
        /// </summary>
        public int ColumnNumber { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "项目路径")]
        public string? ProjectPath { get; set; }

        /// <summary>
        /// 编译器版本
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "编译器版本")]
        public string? CompilerVersion { get; set; }

        /// <summary>
        /// 目标框架
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "目标框架")]
        public string? TargetFramework { get; set; }

        /// <summary>
        /// 编译配置 (Debug/Release)
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编译配置")]
        public string? BuildConfiguration { get; set; }

        /// <summary>
        /// 编译时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "编译时间")]
        public DateTime CompilationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 导航属性 - 关联项目
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public virtual Project? Project { get; set; }
    }
}
