#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板管理器
负责模板序列和参考图片的管理
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, List, Optional
import json
import os
import requests
from pathlib import Path


class TemplateManager:
    """模板管理器类"""
    
    def __init__(self, parent_ui, api_client):
        """
        初始化模板管理器
        
        Args:
            parent_ui: 父UI对象
            api_client: API客户端
        """
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # JavaScript执行器用于模板预下载
        self.js_executor = None
        try:
            from simple_js_executor import SimpleJSExecutor
            if hasattr(parent_ui, 'automation_manager'):
                self.js_executor = SimpleJSExecutor(parent_ui.automation_manager)
                print("✅ JavaScript执行器初始化成功")
        except Exception as e:
            print(f"⚠️ JavaScript执行器初始化失败: {e}")
            self.js_executor = None
        
        # UI组件引用
        self.flow_canvas = None
        self.flow_frame = None
        self.flow_canvas_window = None
        self.sequence_count_label = None
    
    def set_ui_components(self, flow_canvas, flow_frame, flow_canvas_window, sequence_count_label):
        """设置UI组件引用"""
        self.flow_canvas = flow_canvas
        self.flow_frame = flow_frame
        self.flow_canvas_window = flow_canvas_window
        self.sequence_count_label = sequence_count_label
        
        # 绑定事件
        if self.flow_frame:
            self.flow_frame.bind('<Configure>', self.on_flow_frame_configure)
        if self.flow_canvas:
            self.flow_canvas.bind('<Configure>', self.on_flow_canvas_configure)
            self.flow_canvas.bind('<MouseWheel>', self.on_flow_mousewheel)

    def update_template_sequences_display(self, selected_step):
        """更新模板序列显示"""
        if not selected_step:
            self.show_empty_flow_message()
            return

        def load_sequences():
            try:
                step_id = selected_step.get('id')
                sequences = self.get_step_template_sequences_with_steps(step_id)
                
                self.parent_ui.parent.after(0, lambda: self.display_template_sequences(sequences))
                
            except Exception as e:
                print(f"加载模板序列失败: {e}")
                self.parent_ui.parent.after(0, lambda: self.display_sequence_error(f"加载失败: {e}"))

        threading.Thread(target=load_sequences, daemon=True).start()

    def display_template_sequences(self, sequences):
        """显示模板序列"""
        try:
            # 检查UI组件是否已初始化
            if not self.flow_frame:
                print("⚠️ 模板序列UI组件未初始化，跳过显示")
                return

            # 清空现有内容
            for widget in self.flow_frame.winfo_children():
                widget.destroy()

            if not sequences:
                self.show_no_sequences_flow()
                return

            # 更新序列数量
            if self.sequence_count_label:
                self.sequence_count_label.config(text=f"序列数量: {len(sequences)}")

            # 创建序列流程
            self.create_sequences_flow(sequences)

        except Exception as e:
            print(f"显示模板序列失败: {e}")
            self.display_sequence_error(f"显示失败: {e}")

    def show_no_sequences_flow(self):
        """显示无序列流程"""
        if not self.flow_frame:
            return

        for widget in self.flow_frame.winfo_children():
            widget.destroy()

        no_seq_frame = ttk.Frame(self.flow_frame)
        no_seq_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        ttk.Label(no_seq_frame, text="该步骤暂无关联的模板序列",
                 font=('Arial', 12), foreground='gray').pack(pady=10)

        if self.sequence_count_label:
            self.sequence_count_label.config(text="序列数量: 0")

    def create_sequences_flow(self, sequences):
        """创建序列流程"""
        main_container = ttk.Frame(self.flow_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for i, sequence in enumerate(sequences):
            # 序列标题
            seq_frame = ttk.LabelFrame(main_container, text=f"序列 {i+1}: {sequence.get('sequenceName', '未知序列')}", 
                                     padding=10)
            seq_frame.pack(fill=tk.X, pady=(0, 10))

            # 序列信息
            info_frame = ttk.Frame(seq_frame)
            info_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(info_frame, text=f"描述: {sequence.get('description', '无描述')}").pack(anchor=tk.W)
            ttk.Label(info_frame, text=f"步骤数: {len(sequence.get('steps', []))}").pack(anchor=tk.W)

            # 步骤流程
            steps = sequence.get('steps', [])
            if steps:
                self.create_steps_flow(seq_frame, steps)

    def create_steps_flow(self, parent, steps):
        """创建步骤流程"""
        steps_container = ttk.Frame(parent)
        steps_container.pack(fill=tk.X, pady=(10, 0))

        # 创建水平滚动的步骤流程
        canvas = tk.Canvas(steps_container, height=200, bg='white')
        scrollbar = ttk.Scrollbar(steps_container, orient=tk.HORIZONTAL, command=canvas.xview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(xscrollcommand=scrollbar.set)

        # 添加步骤
        for i, step in enumerate(steps):
            step_frame = ttk.Frame(scrollable_frame)
            step_frame.pack(side=tk.LEFT, padx=5, pady=5)

            # 步骤卡片
            card = ttk.LabelFrame(step_frame, text=f"步骤 {step.get('stepOrder', i+1)}", padding=5)
            card.pack()

            # 步骤信息
            ttk.Label(card, text=step.get('stepName', '未知步骤'), font=('Arial', 10, 'bold')).pack()
            ttk.Label(card, text=f"动作: {step.get('actionType', '未知')}", font=('Arial', 8)).pack()

            # 模板信息
            template_info = step.get('templateInfo', {})
            if template_info:
                self.create_template_section(card, step, template_info)

            # 添加箭头（除了最后一个步骤）
            if i < len(steps) - 1:
                arrow_frame = ttk.Frame(scrollable_frame)
                arrow_frame.pack(side=tk.LEFT, padx=2)
                ttk.Label(arrow_frame, text="→", font=('Arial', 16)).pack(pady=50)

        canvas.pack(side=tk.TOP, fill=tk.X, expand=True)
        scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_template_section(self, parent, step, template_info):
        """创建模板部分"""
        template_frame = ttk.Frame(parent)
        template_frame.pack(fill=tk.X, pady=2)

        # 模板类型图标
        action_type = step.get('actionType', '')
        icon = self.get_action_type_icon(action_type)
        ttk.Label(template_frame, text=icon).pack(side=tk.LEFT)

        # 模板名称
        template_name = template_info.get('templateName', '未知模板')
        ttk.Label(template_frame, text=template_name, font=('Arial', 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 操作按钮
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=2)

        # 查看图片按钮
        if template_info.get('referenceImageUrl'):
            ttk.Button(btn_frame, text="🖼️", width=3,
                      command=lambda: self.view_template_image(template_info)).pack(side=tk.LEFT, padx=1)

        # 执行按钮
        ttk.Button(btn_frame, text="▶️", width=3,
                  command=lambda: self.execute_template_automation(step, template_info)).pack(side=tk.LEFT, padx=1)

    def get_action_type_icon(self, action_type):
        """获取动作类型图标"""
        icons = {
            'click': '👆',
            'input': '⌨️',
            'wait': '⏳',
            'scroll': '📜',
            'screenshot': '📸',
            'verify': '✅',
            'navigate': '🧭',
            'select': '🎯',
            'drag': '👋',
            'keyboard': '⌨️',
            'mouse': '🖱️'
        }
        return icons.get(action_type.lower(), '❓')

    def view_template_image(self, template_info):
        """查看模板图片"""
        try:
            image_url = template_info.get('referenceImageUrl')
            if not image_url:
                messagebox.showwarning("警告", "该模板没有参考图片")
                return

            # 下载并显示图片
            self.download_and_show_image(image_url, template_info.get('templateName', '模板图片'))

        except Exception as e:
            messagebox.showerror("错误", f"查看模板图片失败: {e}")

    def download_and_show_image(self, image_url, title):
        """下载并显示图片"""
        def download_and_show():
            try:
                # 创建临时目录
                temp_dir = Path(__file__).parent.parent / "temp_images"
                temp_dir.mkdir(exist_ok=True)

                # 下载图片
                response = requests.get(image_url, timeout=30)
                response.raise_for_status()

                # 保存图片
                file_extension = os.path.splitext(image_url)[1] or '.png'
                temp_file = temp_dir / f"template_{int(time.time())}{file_extension}"
                
                with open(temp_file, 'wb') as f:
                    f.write(response.content)

                # 在主线程中显示图片
                self.parent_ui.parent.after(0, lambda: self.show_image_viewer(str(temp_file), title))

            except Exception as e:
                self.parent_ui.parent.after(0, lambda: messagebox.showerror("错误", f"下载图片失败: {e}"))

        threading.Thread(target=download_and_show, daemon=True).start()

    def show_image_viewer(self, image_path, title):
        """显示图片查看器"""
        try:
            # 创建图片查看窗口
            viewer = tk.Toplevel(self.parent_ui.parent)
            viewer.title(title)
            viewer.geometry("800x600")

            # 加载并显示图片
            try:
                from PIL import Image, ImageTk
                
                # 打开图片
                image = Image.open(image_path)
                
                # 调整图片大小以适应窗口
                image.thumbnail((750, 550), Image.Resampling.LANCZOS)
                
                # 转换为Tkinter可用的格式
                photo = ImageTk.PhotoImage(image)
                
                # 创建标签显示图片
                label = tk.Label(viewer, image=photo)
                label.image = photo  # 保持引用
                label.pack(expand=True)
                
            except ImportError:
                # 如果没有PIL，显示提示信息
                ttk.Label(viewer, text=f"图片路径: {image_path}\n\n请安装Pillow库以查看图片预览").pack(expand=True)

        except Exception as e:
            messagebox.showerror("错误", f"显示图片失败: {e}")

    def execute_template_automation(self, step, template_info):
        """执行模板自动化"""
        try:
            step_name = step.get('stepName', '未知步骤')
            template_name = template_info.get('templateName', '未知模板')
            
            if messagebox.askyesno("确认执行", f"确定要执行模板自动化吗？\n\n步骤: {step_name}\n模板: {template_name}"):
                # 这里应该调用实际的模板自动化执行逻辑
                messagebox.showinfo("执行", f"模板自动化执行功能正在开发中...\n\n步骤: {step_name}\n模板: {template_name}")

        except Exception as e:
            messagebox.showerror("错误", f"执行模板自动化失败: {e}")

    def get_step_template_sequences_with_steps(self, step_id):
        """获取步骤的模板序列（包含步骤详情）"""
        try:
            # 调用API获取模板序列
            result = self.api_client.get_step_template_sequences(step_id)
            
            if not result or not result.get('success'):
                return []

            sequences = result.get('data', [])
            
            # 为每个序列获取详细的步骤信息
            for sequence in sequences:
                sequence_id = sequence.get('id')
                if sequence_id:
                    steps = self.get_sequence_steps(sequence_id)
                    sequence['steps'] = steps

            return sequences

        except Exception as e:
            print(f"获取步骤模板序列失败: {e}")
            return []

    def get_sequence_steps(self, sequence_id):
        """获取序列的步骤"""
        try:
            result = self.api_client.get_template_sequence_steps(sequence_id)
            
            if not result or not result.get('success'):
                return []

            steps = result.get('data', [])
            
            # 为每个步骤获取模板信息
            for step in steps:
                template_id = step.get('templateId')
                if template_id:
                    template_info = self.get_template_info(template_id)
                    step['templateInfo'] = template_info

            return steps

        except Exception as e:
            print(f"获取序列步骤失败: {e}")
            return []

    def get_template_info(self, template_id):
        """获取模板信息"""
        try:
            result = self.api_client.get_automation_template(template_id)
            
            if result and result.get('success'):
                return result.get('data', {})
            else:
                return {}

        except Exception as e:
            print(f"获取模板信息失败: {e}")
            return {}

    def predownload_reference_images(self, selected_step):
        """预下载参考图片"""
        if not selected_step:
            return

        def download_images():
            try:
                step_id = selected_step.get('id')
                step_name = selected_step.get('stepName', '未知步骤')
                
                print(f"🔄 开始预下载步骤 '{step_name}' 的参考图片...")

                # 获取步骤的参考图片
                reference_images = self.get_step_reference_images(step_id)
                
                if reference_images:
                    self._download_step_reference_images(selected_step, reference_images, step_name)

                # 获取步骤的模板序列并下载相关图片
                sequences = self.get_step_template_sequences_with_steps(step_id)
                
                for sequence in sequences:
                    steps = sequence.get('steps', [])
                    for step in steps:
                        template_info = step.get('templateInfo', {})
                        if template_info.get('referenceImageUrl'):
                            # 下载模板参考图片
                            pass  # 实现下载逻辑

                print(f"✅ 步骤 '{step_name}' 的参考图片预下载完成")

            except Exception as e:
                print(f"❌ 预下载参考图片失败: {e}")

        threading.Thread(target=download_images, daemon=True).start()

    def get_step_reference_images(self, step_id):
        """获取步骤的参考图片"""
        try:
            # 这里应该调用API获取步骤的参考图片
            # 暂时返回空列表
            return []
        except Exception as e:
            print(f"获取步骤参考图片失败: {e}")
            return []

    def _download_step_reference_images(self, step, reference_images, step_name):
        """下载步骤参考图片"""
        try:
            # 实现图片下载逻辑
            pass
        except Exception as e:
            print(f"下载步骤参考图片失败: {e}")

    def preload_project_templates(self, selected_project):
        """预加载项目模板"""
        if not selected_project:
            return

        def download_templates():
            try:
                project_id = selected_project.get('id')
                project_name = selected_project.get('name', '未知项目')
                
                print(f"🔄 开始预加载项目 '{project_name}' 的模板...")

                # 这里应该实现项目模板预加载逻辑
                # 可以获取项目的所有开发步骤，然后预下载相关模板

                print(f"✅ 项目 '{project_name}' 的模板预加载完成")

            except Exception as e:
                print(f"❌ 预加载项目模板失败: {e}")

        threading.Thread(target=download_templates, daemon=True).start()

    def on_flow_frame_configure(self, event):
        """流程框架配置变化"""
        if self.flow_canvas:
            self.flow_canvas.configure(scrollregion=self.flow_canvas.bbox("all"))

    def on_flow_canvas_configure(self, event):
        """流程画布配置变化"""
        if self.flow_canvas_window:
            canvas_width = event.width
            self.flow_canvas.itemconfig(self.flow_canvas_window, width=canvas_width)

    def on_flow_mousewheel(self, event):
        """鼠标滚轮事件"""
        if self.flow_canvas:
            self.flow_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def show_empty_flow_message(self):
        """显示空流程消息"""
        if not self.flow_frame:
            return

        for widget in self.flow_frame.winfo_children():
            widget.destroy()

        empty_label = ttk.Label(self.flow_frame, text="请选择一个开发步骤以查看关联的模板序列流程",
                               font=('Arial', 12), foreground='gray')
        empty_label.pack(pady=50)

    def display_sequence_error(self, error_msg):
        """显示序列错误"""
        if not self.flow_frame:
            return

        for widget in self.flow_frame.winfo_children():
            widget.destroy()

        error_label = ttk.Label(self.flow_frame, text=f"加载模板序列时出错：\n{error_msg}",
                               font=('Arial', 10), foreground='red')
        error_label.pack(pady=50)

    def clear_template_sequences_display(self):
        """清空模板序列显示"""
        self.show_empty_flow_message()
        if self.sequence_count_label:
            self.sequence_count_label.config(text="序列数量: 0")

    def refresh_template_sequences(self, selected_step):
        """刷新模板序列"""
        self.update_template_sequences_display(selected_step)
