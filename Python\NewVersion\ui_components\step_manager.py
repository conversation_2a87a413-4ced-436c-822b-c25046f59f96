#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤管理器
负责开发步骤的管理和显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Optional


class StepManager:
    """步骤管理器类"""
    
    def __init__(self, parent_ui, api_client):
        """
        初始化步骤管理器
        
        Args:
            parent_ui: 父UI对象
            api_client: API客户端
        """
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # 数据
        self.development_steps = []
        self.selected_step = None

        # 步骤ID映射（因为TreeView设置了show='headings'，不能使用#0列存储数据）
        self.item_to_step_id = {}  # TreeView item -> step_id 的映射

        # 工作模式：'development' 或 'coding_task'
        self.work_mode = 'coding_task'  # 默认使用编码任务模式
        
        # 步骤完成检测器
        self.step_completion_detector = None
        self.auto_detection_enabled = False
        
        # UI组件引用
        self.steps_tree = None
        self.details_text = None
        self.status_label = None
        self.detection_status_label = None
        self.status_var = None
        self.status_combo = None
    
    def set_ui_components(self, steps_tree, details_text, status_label, detection_status_label, status_var, status_combo):
        """设置UI组件引用"""
        self.steps_tree = steps_tree
        self.details_text = details_text
        self.status_label = status_label
        self.detection_status_label = detection_status_label
        self.status_var = status_var
        self.status_combo = status_combo
        
        # 绑定事件
        self.steps_tree.bind('<<TreeviewSelect>>', self.on_step_select)
        self.steps_tree.bind('<Double-1>', self.on_step_double_click)
        self.status_combo.bind('<<ComboboxSelected>>', self.on_status_changed)
    
    def refresh_development_steps(self, selected_project=None, selected_task=None):
        """刷新开发步骤列表"""
        if self.work_mode == 'coding_task':
            if not selected_task:
                print("⚠️ 警告：没有选择编码任务，无法加载步骤")
                self.status_label.config(text="请先选择编码任务")
                self.clear_development_steps_display()
                return
        else:
            if not selected_project:
                print("⚠️ 警告：没有选择项目，无法加载开发步骤")
                self.status_label.config(text="请先选择项目")
                self.clear_development_steps_display()
                return

        def load_steps():
            try:
                self.status_label.config(text="加载步骤中...")
                self.parent_ui.parent.update()

                if self.work_mode == 'coding_task':
                    # 编码任务模式：获取编码任务的步骤
                    task_id = selected_task.get('id')
                    print(f"🔍 获取编码任务步骤，任务ID: {task_id}")
                    result = self.api_client.get_coding_task_steps(task_id)

                    if result and 'data' in result:
                        self.development_steps = result['data']
                        print(f"✅ 从编码任务获取到 {len(self.development_steps)} 个步骤")

                        # 打印前几个步骤的详细信息
                        for i, step in enumerate(self.development_steps[:3]):
                            print(f"  步骤 {i+1}: ID={step.get('id')}, Name={step.get('stepName')}, Status={step.get('status')}")
                    else:
                        print(f"❌ 编码任务步骤API返回格式异常: {result}")
                        self.development_steps = []
                else:
                    # 项目模式：获取项目的开发步骤
                    project_id = selected_project.get('id')
                    print(f"🔍 获取项目开发步骤，项目ID: {project_id}")
                    result = self.api_client.get_development_steps(project_id)
                    if result and 'data' in result and 'items' in result['data']:
                        self.development_steps = result['data']['items']
                        print(f"✅ 从项目获取到 {len(self.development_steps)} 个开发步骤")

                        # 打印前几个步骤的详细信息
                        for i, step in enumerate(self.development_steps[:3]):
                            print(f"  步骤 {i+1}: ID={step.get('id')}, Name={step.get('stepName')}, Status={step.get('status')}")
                    else:
                        print(f"❌ 项目步骤API返回格式异常: {result}")
                        self.development_steps = []

                # 在主线程中更新UI
                self.parent_ui.parent.after(0, self.update_steps_list)

            except Exception as e:
                print(f"❌ 加载开发步骤异常: {e}")
                import traceback
                traceback.print_exc()
                self.parent_ui.parent.after(0, lambda: self.status_label.config(text=f"加载步骤失败: {e}"))

        threading.Thread(target=load_steps, daemon=True).start()

    def clear_development_steps_display(self):
        """清空开发步骤显示"""
        try:
            print("🧹 清空开发步骤显示...")
            
            # 清空步骤列表
            if self.steps_tree:
                for item in self.steps_tree.get_children():
                    self.steps_tree.delete(item)
                print("✅ 步骤列表已清空")
            
            # 清空步骤详情
            if self.details_text:
                self.details_text.config(state=tk.NORMAL)
                self.details_text.delete(1.0, tk.END)
                self.details_text.insert(tk.END, "请选择一个开发步骤以查看详情")
                self.details_text.config(state=tk.DISABLED)
                print("✅ 步骤详情已清空")
            
            # 重置选中的步骤
            self.selected_step = None
            self.development_steps = []
            
            # 更新状态标签
            if self.status_label:
                self.status_label.config(text="就绪")
            
            print("✅ 开发步骤显示已完全清空")
            
        except Exception as e:
            print(f"❌ 清空开发步骤显示失败: {e}")

    def update_steps_list(self):
        """更新步骤列表"""
        try:
            # 清空现有项目和映射
            for item in self.steps_tree.get_children():
                self.steps_tree.delete(item)
            self.item_to_step_id.clear()

            if not self.development_steps:
                print("⚠️ 警告：没有开发步骤数据")
                self.status_label.config(text="无步骤数据")
                return

            # 构建层级结构
            step_hierarchy = self.build_step_hierarchy(self.development_steps)

            # 添加步骤到树形控件
            self.add_steps_to_tree(step_hierarchy)

            # 应用状态筛选
            self.apply_status_filter()

            # 再次检查筛选后的项目数量
            visible_items_count = len([item for item in self.steps_tree.get_children()
                                     if self.steps_tree.item(item)['values']])
            print(f"🔍 状态筛选后可见项目数量: {visible_items_count}")

            self.status_label.config(text=f"已加载 {len(self.development_steps)} 个步骤")

        except Exception as e:
            print(f"❌ 更新步骤列表失败: {e}")
            import traceback
            traceback.print_exc()
            self.status_label.config(text=f"更新步骤列表失败: {e}")

    def build_step_hierarchy(self, steps):
        """构建步骤层级结构"""
        # 创建步骤字典，便于查找
        step_dict = {step.get('id'): step for step in steps}
        
        # 找出根步骤（没有父步骤的）
        root_steps = []
        for step in steps:
            parent_id = step.get('parentStepId')
            if not parent_id or parent_id not in step_dict:
                root_steps.append(step)
        
        # 为每个步骤添加子步骤列表
        for step in steps:
            step['children'] = []
        
        # 构建父子关系
        for step in steps:
            parent_id = step.get('parentStepId')
            if parent_id and parent_id in step_dict:
                step_dict[parent_id]['children'].append(step)
        
        return root_steps

    def add_steps_to_tree(self, steps, parent_item=''):
        """递归添加步骤到树形控件"""

        for step in steps:
            step_id = step.get('id', '')
            step_name = step.get('stepName', '未知步骤')
            step_type = step.get('stepType', '')
            status = step.get('status', 'Pending')
            priority = step.get('priority', 0)
            progress = f"{step.get('progress', 0)}%"
            technology = step.get('technologyStack', '')


            # 获取编译状态
            compile_status = self.get_step_compile_status(step)

            # 插入步骤项
            item = self.steps_tree.insert(parent_item, 'end',
                                        values=(step_name, step_type, status, priority, progress, technology, compile_status),
                                        tags=(status.lower(),))
            # 存储步骤ID到映射字典中
            self.item_to_step_id[item] = step_id
            # 递归添加子步骤
            if step.get('children'):
                self.add_steps_to_tree(step['children'], item)

    def get_step_compile_status(self, step):
        """获取步骤的编译状态"""
        # 这里可以实现缓存机制，避免重复检测
        # 目前返回默认状态，实际使用时可以调用检测器
        return "未检测"

    def apply_status_filter(self):
        """应用状态筛选"""
        filter_status = self.status_var.get()
        print(f"🔍 应用状态筛选: {filter_status}")

        if filter_status == '全部':
            print("  ✅ 显示全部步骤，不进行筛选")
            # 确保所有项目都是可见的
            self._show_all_items()
            return

        print(f"  🔍 筛选状态为 '{filter_status}' 的步骤")
        # 隐藏不匹配的项目
        hidden_count = 0
        visible_count = 0
        for item in self.steps_tree.get_children():
            if self._filter_item_recursive(item, filter_status):
                visible_count += 1
            else:
                hidden_count += 1

        print(f"  📊 筛选结果: 可见={visible_count}, 隐藏={hidden_count}")

    def _show_all_items(self):
        """显示所有项目"""
        def show_item_recursive(item):
            # 确保项目是可见的
            parent = self.steps_tree.parent(item)
            if parent and item not in self.steps_tree.get_children(parent):
                self.steps_tree.move(item, parent, 'end')
            elif not parent and item not in self.steps_tree.get_children():
                self.steps_tree.move(item, '', 'end')

            # 递归处理子项
            for child in self.steps_tree.get_children(item):
                show_item_recursive(child)

        # 获取所有项目（包括被detach的）
        all_items = list(self.steps_tree.get_children())
        for item in all_items:
            show_item_recursive(item)

    def _filter_item_recursive(self, item, filter_status):
        """递归筛选项目，返回是否可见"""
        values = self.steps_tree.item(item, 'values')
        item_visible = False

        if values and len(values) > 2:
            item_status = values[2]  # status列
            if item_status == filter_status:
                item_visible = True
                print(f"    ✅ 步骤匹配: {values[0]} (状态: {item_status})")
            else:
                print(f"    ❌ 步骤不匹配: {values[0]} (状态: {item_status}, 期望: {filter_status})")

        # 检查子项
        child_visible = False
        for child in self.steps_tree.get_children(item):
            if self._filter_item_recursive(child, filter_status):
                child_visible = True

        # 如果项目本身匹配或有匹配的子项，则显示
        should_show = item_visible or child_visible

        if not should_show:
            self.steps_tree.detach(item)

        return should_show

    def on_status_changed(self, event=None):
        """状态筛选变化"""
        self.apply_status_filter()

    def on_step_select(self, event):
        """步骤选择事件"""
        selection = self.steps_tree.selection()
        if selection:
            item = selection[0]
            # 从映射字典中获取步骤ID
            step_id = self.item_to_step_id.get(item)

            # 查找对应的步骤数据
            self.selected_step = self.find_step_by_id(int(step_id)) if step_id and str(step_id).isdigit() else None

            if self.selected_step:
                print(f"选中步骤: {self.selected_step.get('stepName', '未知')}")
                self.update_step_details()

                # 通知父UI步骤已选中
                if hasattr(self.parent_ui, 'on_step_selected'):
                    self.parent_ui.on_step_selected(self.selected_step)
            else:
                print(f"⚠️ 未找到步骤ID {step_id} 对应的步骤数据")

    def on_step_double_click(self, event):
        """步骤双击事件"""
        # 可以在这里实现双击执行步骤的逻辑
        if self.selected_step:
            print(f"双击执行步骤: {self.selected_step.get('stepName', '未知')}")

    def find_step_by_id(self, step_id):
        """根据ID查找步骤"""
        for step in self.development_steps:
            if step.get('id') == step_id:
                return step
        return None

    def update_step_details(self):
        """更新步骤详情显示"""
        if not self.selected_step:
            self.details_text.config(state=tk.NORMAL)
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, "请选择一个开发步骤以查看详情")
            self.details_text.config(state=tk.DISABLED)
            return

        # 构建详情文本
        details = []
        details.append(f"步骤名称: {self.selected_step.get('stepName', '未知')}")
        details.append(f"步骤类型: {self.selected_step.get('stepType', '未知')}")
        details.append(f"状态: {self.selected_step.get('status', 'Pending')}")
        details.append(f"优先级: {self.selected_step.get('priority', 0)}")
        details.append(f"进度: {self.selected_step.get('progress', 0)}%")
        details.append(f"技术栈: {self.selected_step.get('technologyStack', '未指定')}")
        details.append(f"预计工时: {self.selected_step.get('estimatedHours', 0)} 小时")
        details.append(f"实际工时: {self.selected_step.get('actualHours', 0)} 小时")
        
        if self.selected_step.get('description'):
            details.append(f"\n描述:\n{self.selected_step.get('description')}")
        
        if self.selected_step.get('requirements'):
            details.append(f"\n需求:\n{self.selected_step.get('requirements')}")
        
        if self.selected_step.get('acceptanceCriteria'):
            details.append(f"\n验收标准:\n{self.selected_step.get('acceptanceCriteria')}")

        # 更新文本框
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(tk.END, '\n'.join(details))
        self.details_text.config(state=tk.DISABLED)

    def get_selected_step(self):
        """获取选中的步骤"""
        return self.selected_step

    def set_work_mode(self, mode):
        """设置工作模式"""
        self.work_mode = mode

    def select_step_by_id(self, step_id):
        """根据步骤ID在UI中选中步骤"""
        try:
            if not step_id:
                return False

            # 查找对应的TreeView项
            for item in self.steps_tree.get_children():
                if self._find_item_by_step_id(item, step_id):
                    # 选中该项
                    self.steps_tree.selection_set(item)
                    self.steps_tree.focus(item)
                    self.steps_tree.see(item)  # 确保项目可见

                    # 触发选择事件
                    self.on_step_select(None)
                    return True

            print(f"⚠️ 未找到步骤ID {step_id} 对应的TreeView项")
            return False

        except Exception as e:
            print(f"❌ 选中步骤异常: {e}")
            return False

    def _find_item_by_step_id(self, item, target_step_id):
        """递归查找指定步骤ID的TreeView项"""
        try:
            # 检查当前项
            step_id = self.item_to_step_id.get(item)
            if step_id and int(step_id) == int(target_step_id):
                return item

            # 递归检查子项
            for child in self.steps_tree.get_children(item):
                result = self._find_item_by_step_id(child, target_step_id)
                if result:
                    return result

            return None

        except Exception as e:
            print(f"❌ 查找TreeView项异常: {e}")
            return None

    def refresh_steps(self):
        """刷新步骤列表"""
        try:
            # 获取当前选中的项目和任务
            if hasattr(self.parent_ui, 'project_task_manager'):
                selected_project = self.parent_ui.project_task_manager.selected_project
                selected_task = self.parent_ui.project_task_manager.selected_task
                self.refresh_development_steps(selected_project, selected_task)
            else:
                print("⚠️ 无法获取项目任务管理器，无法刷新步骤")
        except Exception as e:
            print(f"❌ 刷新步骤异常: {e}")
