-- 添加测试相关的AI提示词模板
USE ProjectManagementAI;

-- 检查是否存在测试生成分类，如果不存在则创建
DECLARE @TestCategoryId INT;
SELECT @TestCategoryId = Id FROM PromptCategories WHERE Name = '测试生成' AND IsDeleted = 0;

IF @TestCategoryId IS NULL
BEGIN
    INSERT INTO PromptCategories (Name, Description, Icon, SortOrder, IsEnabled, CreatedTime)
    VALUES ('测试生成', '用于生成各种类型的自动化测试用例', '🧪', 30, 1, GETDATE());
    
    SET @TestCategoryId = SCOPE_IDENTITY();
    PRINT '创建测试生成分类，ID: ' + CAST(@TestCategoryId AS VARCHAR(10));
END
ELSE
BEGIN
    PRINT '测试生成分类已存在，ID: ' + CAST(@TestCategoryId AS VARCHAR(10));
END

-- 添加测试相关的提示词模板
PRINT '开始添加测试提示词模板...';

-- 1. Playwright测试生成模板 - 基于需求
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'Playwright需求测试生成' AND IsDeleted = 0)
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, IsDefault, CreatedTime
    )
    VALUES (
        'Playwright需求测试生成',
        '基于需求文档生成完整的Playwright自动化测试用例',
        @TestCategoryId,
        N'基于以下需求文档，生成完整的Playwright自动化测试用例：

需求内容：
{requirementContent}

项目信息：
- 项目名称：{projectName}
- 基础URL：{baseUrl}
- 目标浏览器：{browsers}

请生成以下类型的测试：
1. **功能测试** - 验证核心业务功能
2. **用户界面测试** - 验证UI交互和显示
3. **边界条件测试** - 验证异常情况处理
4. **用户体验测试** - 验证用户操作流程

每个测试用例应包含：
- 清晰的测试名称和描述
- 详细的测试步骤（使用Playwright API）
- 预期结果验证（使用expect断言）
- 错误处理和边界条件验证
- 适当的等待和同步机制

请以JSON格式返回测试用例数组，格式如下：
```json
[
  {
    "name": "测试名称",
    "description": "测试描述",
    "category": "e2e",
    "priority": "high|medium|low",
    "code": "完整的Playwright测试代码",
    "tags": ["tag1", "tag2"]
  }
]
```',
        '{"requirementContent": "需求文档内容", "projectName": "项目名称", "baseUrl": "基础URL", "browsers": "目标浏览器"}',
        'System',
        'Testing',
        'JavaScript',
        'Playwright',
        'Medium',
        800,
        'playwright,需求测试,自动化测试,功能测试',
        1,
        1,
        GETDATE()
    );
    PRINT '✓ 添加Playwright需求测试生成模板';
END

-- 2. Playwright页面测试生成模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'Playwright页面测试生成' AND IsDeleted = 0)
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, IsDefault, CreatedTime
    )
    VALUES (
        'Playwright页面测试生成',
        '基于页面结构分析生成Playwright页面交互测试',
        @TestCategoryId,
        N'基于以下页面结构分析，生成Playwright自动化测试用例：

页面信息：
- 页面URL：{pageUrl}
- 页面标题：{pageTitle}
- 主要元素：{pageElements}

页面结构分析：
{pageStructure}

请生成以下类型的页面测试：
1. **页面加载测试** - 验证页面正确加载
2. **元素交互测试** - 验证按钮、链接、表单等交互
3. **导航测试** - 验证页面间导航功能
4. **响应式测试** - 验证不同屏幕尺寸下的显示
5. **性能测试** - 验证页面加载性能

测试要求：
- 使用合适的选择器策略（优先使用data-testid、role等）
- 包含适当的等待机制（waitForSelector、waitForLoadState等）
- 添加截图和视频记录用于调试
- 处理可能的异步操作和动态内容

请以JSON格式返回测试用例数组。',
        '{"pageUrl": "页面URL", "pageTitle": "页面标题", "pageElements": "主要元素列表", "pageStructure": "页面结构分析"}',
        'System',
        'Testing',
        'JavaScript',
        'Playwright',
        'Medium',
        600,
        'playwright,页面测试,UI测试,交互测试',
        1,
        0,
        GETDATE()
    );
    PRINT '✓ 添加Playwright页面测试生成模板';
END

-- 3. 测试代码分析模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '测试代码分析' AND IsDeleted = 0)
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, IsDefault, CreatedTime
    )
    VALUES (
        '测试代码分析',
        '分析现有测试代码，提供质量评估和改进建议',
        @TestCategoryId,
        N'请分析以下测试代码，提供详细的质量评估和改进建议：

测试代码：
```{language}
{testCode}
```

测试框架：{framework}
测试类型：{testType}

请从以下维度进行分析：

1. **代码质量评估**：
   - 可读性：代码结构是否清晰，命名是否规范
   - 可维护性：是否易于修改和扩展
   - 稳定性：是否有潜在的不稳定因素

2. **测试覆盖度分析**：
   - 功能覆盖：是否覆盖了主要功能点
   - 边界条件：是否测试了边界情况
   - 异常处理：是否包含错误处理测试

3. **最佳实践检查**：
   - 选择器策略是否合理
   - 等待机制是否适当
   - 断言是否充分和准确
   - 测试数据管理是否规范

4. **性能和效率**：
   - 测试执行效率
   - 资源使用情况
   - 并行执行可能性

请以JSON格式返回分析结果：
```json
{
  "qualityScore": {
    "overall": 85,
    "readability": 4,
    "maintainability": 4,
    "stability": 3
  },
  "suggestions": [
    {
      "type": "warning|info|error",
      "title": "建议标题",
      "description": "详细描述",
      "code": "示例代码（可选）"
    }
  ],
  "recommendedPatterns": [
    {
      "name": "模式名称",
      "description": "模式描述",
      "example": "示例代码"
    }
  ]
}
```',
        '{"testCode": "测试代码", "language": "编程语言", "framework": "测试框架", "testType": "测试类型"}',
        'System',
        'Testing',
        'JavaScript',
        'Playwright',
        'Hard',
        1000,
        '代码分析,测试质量,代码审查,最佳实践',
        1,
        1,
        GETDATE()
    );
    PRINT '✓ 添加测试代码分析模板';
END

-- 4. 测试优化建议模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '测试代码优化' AND IsDeleted = 0)
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, IsDefault, CreatedTime
    )
    VALUES (
        '测试代码优化',
        '优化现有测试代码，提高稳定性和可维护性',
        @TestCategoryId,
        N'请优化以下测试代码，提高其稳定性、可维护性和执行效率：

原始测试代码：
```{language}
{originalCode}
```

测试框架：{framework}
优化目标：{optimizationGoals}

请按照以下原则进行优化：

1. **稳定性优化**：
   - 改进选择器策略，使用更稳定的定位方法
   - 添加适当的等待机制，避免时序问题
   - 处理可能的异步操作和动态内容

2. **可维护性优化**：
   - 提取公共方法和页面对象
   - 改进代码结构和组织
   - 添加清晰的注释和文档

3. **性能优化**：
   - 减少不必要的等待时间
   - 优化测试数据准备和清理
   - 提高测试执行效率

4. **最佳实践应用**：
   - 遵循测试框架的最佳实践
   - 使用合适的断言方法
   - 实现适当的错误处理

请返回优化后的完整代码，并说明主要改进点。',
        '{"originalCode": "原始测试代码", "language": "编程语言", "framework": "测试框架", "optimizationGoals": "优化目标"}',
        'System',
        'Testing',
        'JavaScript',
        'Playwright',
        'Hard',
        800,
        '代码优化,测试改进,性能优化,最佳实践',
        1,
        0,
        GETDATE()
    );
    PRINT '✓ 添加测试代码优化模板';
END

-- 5. API测试生成模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'API测试生成' AND IsDeleted = 0)
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, IsDefault, CreatedTime
    )
    VALUES (
        'API测试生成',
        '基于API文档生成完整的API测试用例',
        @TestCategoryId,
        N'基于以下API文档，生成完整的API测试用例：

API信息：
- API名称：{apiName}
- 基础URL：{baseUrl}
- 认证方式：{authType}

API文档：
{apiDocumentation}

请生成以下类型的API测试：
1. **正常流程测试** - 验证API正常功能
2. **参数验证测试** - 验证必需参数和可选参数
3. **边界条件测试** - 验证参数边界值
4. **错误处理测试** - 验证各种错误情况
5. **性能测试** - 验证响应时间和并发处理

每个测试用例应包含：
- 请求方法和URL
- 请求头和参数
- 预期响应状态码和数据
- 响应时间验证
- 数据格式验证

请以Playwright API测试格式返回测试代码。',
        '{"apiName": "API名称", "baseUrl": "基础URL", "authType": "认证方式", "apiDocumentation": "API文档"}',
        'System',
        'Testing',
        'JavaScript',
        'Playwright',
        'Medium',
        700,
        'api测试,接口测试,自动化测试,playwright',
        1,
        0,
        GETDATE()
    );
    PRINT '✓ 添加API测试生成模板';
END

-- 更新分类的模板数量
UPDATE PromptCategories 
SET TemplateCount = (
    SELECT COUNT(*) 
    FROM PromptTemplates 
    WHERE CategoryId = @TestCategoryId AND IsDeleted = 0 AND IsEnabled = 1
)
WHERE Id = @TestCategoryId;

PRINT '✓ 更新分类模板数量';

-- 验证添加结果
SELECT '=== 测试相关提示词模板 ===' as Info;
SELECT Id, Name, TaskType, IsEnabled, IsDefault, UsageCount, CreatedTime
FROM PromptTemplates 
WHERE CategoryId = @TestCategoryId AND IsDeleted = 0
ORDER BY IsDefault DESC, Name;

PRINT '测试提示词模板添加完成！';
