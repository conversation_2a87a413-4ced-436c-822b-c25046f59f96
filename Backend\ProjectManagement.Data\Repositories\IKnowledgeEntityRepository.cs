using ProjectManagement.Core.DTOs.KnowledgeGraph;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 知识实体仓储接口
    /// </summary>
    public interface IKnowledgeEntityRepository
    {
        Task<KnowledgeEntity> CreateAsync(KnowledgeEntity entity);
        Task<KnowledgeEntity?> GetByIdAsync(int id);
        Task<List<KnowledgeEntity>> GetByTypeAsync(string type);
        Task<List<KnowledgeEntity>> GetByTypeAndNameAsync(string type, string name);
        Task<KnowledgeEntity?> GetByTypeAndReferenceIdAsync(string type, string referenceId);
        Task<KnowledgeEntity> UpdateAsync(KnowledgeEntity entity);
        Task<bool> DeleteAsync(int id);
        Task<List<KnowledgeEntity>> SearchAsync(string query, string? type = null);
    }
}
