// Element Plus 组件类型定义
export type ElTagType = 'success' | 'primary' | 'warning' | 'info' | 'danger'

// 状态颜色映射函数的返回类型
export const getStatusColor = (status: string): ElTagType => {
  const colors: Record<string, ElTagType> = {
    '成功': 'success',
    '失败': 'danger',
    '运行中': 'warning',
    '已完成': 'success',
    '进行中': 'warning',
    '未开始': 'info',
    '已取消': 'danger',
    '错误': 'danger',
    '正常': 'success',
    '警告': 'warning'
  }
  return colors[status] || 'info'
}

// 测试类型颜色映射函数
export const getTestTypeColor = (type: string): ElTagType => {
  const colors: Record<string, ElTagType> = {
    '单元测试': 'primary',
    '集成测试': 'success',
    '功能测试': 'info',
    '性能测试': 'warning',
    '性能': 'warning',
    'Selenium': 'primary',
    'API': 'success'
  }
  return colors[type] || 'info'
}

// 优先级颜色映射函数
export const getPriorityColor = (priority: string): ElTagType => {
  const colors: Record<string, ElTagType> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info',
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'info'
  }
  return colors[priority] || 'info'
}

// 项目状态颜色映射函数
export const getProjectStatusColor = (status: string): ElTagType => {
  const colors: Record<string, ElTagType> = {
    '进行中': 'primary',
    '已完成': 'success',
    '已暂停': 'warning',
    '已取消': 'danger',
    '计划中': 'info'
  }
  return colors[status] || 'info'
}
