using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.RateLimiting;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.DTOs.VectorSearch;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.API.DTOs;
using ProjectManagement.Data.Repositories;
using System.Security.Claims;
using System.Text.Json;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Text;
using ProjectManagement.API.Helper;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// AI服务控制器
/// 功能: 处理AI相关的请求，包括需求分析、代码生成、文档生成等
/// 支持: 多AI模型、任务队列、进度跟踪、结果缓存
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[EnableRateLimiting("AIApi")]
[Produces("application/json")]
public partial class AIController : ControllerBase
{
    private readonly ILogger<AIController> _logger;
    private readonly IAIService _aiService;
    private readonly ProjectManagement.Data.Repositories.IAIModelConfigurationRepository _aiConfigRepository;
    private readonly ProjectManagement.Data.Repositories.IUserAIConfigurationRepository _userAIConfigRepository;
    private readonly ProjectManagement.Core.Interfaces.IUserTaskMappingRepository _userTaskMappingRepository;
    private readonly ProjectManagement.AI.Services.IPromptBuilderService _promptBuilderService;
    private readonly ProjectManagement.AI.Services.IPromptTemplateService _promptTemplateService;
    private readonly ProjectManagement.AI.Services.ITaskStatusService _taskStatusService;
    private readonly ProjectManagement.Core.Interfaces.IRequirementConversationRepository _conversationRepository;
    private readonly IServiceProvider _serviceProvider;
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<RequirementDocument> _requirementRepository;
    private readonly ProjectManagement.AI.Services.ConversationContextService _conversationContextService;
    private readonly ProjectManagement.Data.Repositories.IPromptTemplateRepository _promptTemplateRepository;
    private readonly ProjectManagement.AI.Services.VectorSearchService _vectorSearchService;
    private readonly IConfiguration _configuration;
    private readonly ProjectManagement.Core.Services.IEncryptionService _encryptionService;

    public AIController(
        ILogger<AIController> logger,
        IAIService aiService,
        ProjectManagement.Data.Repositories.IAIModelConfigurationRepository aiConfigRepository,
        ProjectManagement.Data.Repositories.IUserAIConfigurationRepository userAIConfigRepository,
        ProjectManagement.Core.Interfaces.IUserTaskMappingRepository userTaskMappingRepository,
        ProjectManagement.AI.Services.IPromptBuilderService promptBuilderService,
        ProjectManagement.AI.Services.IPromptTemplateService promptTemplateService,
        ProjectManagement.AI.Services.ITaskStatusService taskStatusService,
        ProjectManagement.Core.Interfaces.IRequirementConversationRepository conversationRepository,
        IServiceProvider serviceProvider,
        IRepository<Project> projectRepository,
        IRepository<RequirementDocument> requirementRepository,
        ProjectManagement.AI.Services.ConversationContextService conversationContextService,
        ProjectManagement.Data.Repositories.IPromptTemplateRepository promptTemplateRepository,
        ProjectManagement.AI.Services.VectorSearchService vectorSearchService,
        IConfiguration configuration,
        ProjectManagement.Core.Services.IEncryptionService encryptionService
        )
    {
        _logger = logger;
        _aiService = aiService;
        _aiConfigRepository = aiConfigRepository;
        _userAIConfigRepository = userAIConfigRepository;
        _userTaskMappingRepository = userTaskMappingRepository;
        _promptBuilderService = promptBuilderService;
        _promptTemplateService = promptTemplateService;
        _taskStatusService = taskStatusService;
        _conversationRepository = conversationRepository;
        _serviceProvider = serviceProvider;
        _projectRepository = projectRepository;
        _requirementRepository = requirementRepository;
        _conversationContextService = conversationContextService;
        _promptTemplateRepository = promptTemplateRepository;
        _vectorSearchService = vectorSearchService;
        _configuration = configuration;
        _encryptionService = encryptionService;
    }

    /// <summary>
    /// 分析需求并生成需求规格书
    /// </summary>
    /// <param name="request">需求分析请求</param>
    /// <returns>需求分析任务ID</returns>
    [HttpPost("analyze-requirements")]
    public async Task<ActionResult<AITaskResponseDto>> AnalyzeRequirements([FromBody] RequirementAnalysisRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求需求分析: 项目 {ProjectId}", userId, request.ProjectId);

            // 1. 验证项目权限
            // TODO: 添加项目权限验证

            // 2. 创建AI任务
            var taskId = Guid.NewGuid().ToString();

            // 3. 启动后台任务进行需求分析
            _ = Task.Run(async () =>
            {
                try
                {
                    _logger.LogInformation("开始执行需求分析任务: {TaskId}", taskId);

                    // 调用AI服务进行需求分析
                    var analysisResult = await _aiService.AnalyzeRequirementsAsync(request.RequirementText);

                    // 生成需求规格书
                    var specification = await _aiService.GenerateSpecificationAsync(analysisResult);

                    _logger.LogInformation("需求分析任务完成: {TaskId}", taskId);

                    // TODO: 保存分析结果到数据库
                    // await _requirementRepository.SaveAnalysisResultAsync(request.ProjectId, analysisResult, specification);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "需求分析任务执行失败: {TaskId}", taskId);
                    // TODO: 更新任务状态为失败
                }
            });

            // 4. 返回任务ID供客户端跟踪进度
            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "RequirementAnalysis",
                Status = "Started",
                Message = "需求分析任务已启动，正在使用AI进行智能分析",
                EstimatedDuration = TimeSpan.FromMinutes(5)
            };

            _logger.LogInformation("需求分析任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建需求分析任务时发生错误");
            return StatusCode(500, new { message = "创建需求分析任务失败" });
        }
    }

    /// <summary>
    /// 生成ER图
    /// </summary>
    /// <param name="request">ER图生成请求</param>
    /// <returns>ER图生成任务ID</returns>
    [HttpPost("generate-er-diagram")]
    public async Task<ActionResult<AITaskResponseDto>> GenerateERDiagram([FromBody] ERDiagramGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求生成ER图: 项目 {ProjectId}", userId, request.ProjectId);

            // 1. 验证项目权限
            // TODO: 添加项目权限验证

            // 2. 创建任务并获取任务ID
            var taskId = await _taskStatusService.CreateTaskAsync("ERDiagramGeneration", userId, request.ProjectId, "ER图生成任务已启动");

            // 3. 预先记录对话ID，稍后在获取prompt后更新
            var conversationId = $"er-diagram-{taskId}";

            // 4. 启动后台任务进行ER图生成
            _ = Task.Run(async () =>
            {
                try
                {
                    // 更新任务状态为运行中
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 10, "开始生成ER图...");

                    // 使用Prompt工程构建ER图生成提示词
                    string prompt;
                    try
                    {
                        var parameters = new Dictionary<string, object>
                        {
                            ["projectId"] = request.ProjectId,
                            ["databaseType"] = request.DatabaseType ?? "SqlServer",
                            ["diagramFormat"] = request.DiagramFormat ?? "Mermaid",
                            ["requirementDocumentId"] = request.RequirementDocumentId ?? 0
                        };

                        prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("ERDiagramGeneration", parameters, userId, request.ProjectId);
                    }
                    catch (Exception ex)
                    {
                        prompt = "使用Prompt模板失败";
                    }

                    // 记录用户请求到对话表（包含实际的AI prompt）
                    try
                    {
                        await _conversationRepository.CreateConversationAsync(new RequirementConversation
                        {
                            ConversationId = conversationId,
                            ProjectId = request.ProjectId,
                            UserId = userId,
                            UserMessage = prompt, // 记录实际发送给AI的prompt
                            AIResponse = null, // 初始时AI还没有响应
                            MessageType = "ERDiagramGeneration",
                            Timestamp = DateTime.UtcNow
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "记录ER图生成请求到对话表失败: {ConversationId}", conversationId);
                    }

                    // 更新任务进度
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 30, "正在调用AI生成ER图...");

                    // 获取AI配置 - 必须使用指定的AI提供商配置ID
                    string generatedMermaid;

                    if (!request.AIProviderConfigId.HasValue)
                    {
                        _logger.LogWarning("未指定AI提供商配置ID，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "请选择AI提供商");
                        return;
                    }

                    // 使用Helper获取AI配置，ER图生成使用较低的温度以确保一致性
                    var config = await AIConfigurationHelper.GetUserAIConfigurationAsync(
                        _aiConfigRepository,
                        _logger,
                        userId,
                        request.AIProviderConfigId.Value,
                        4000, // maxTokens
                        0.3f, // temperature - ER图生成使用较低的温度
                        _encryptionService
                    );

                    if (config == null)
                    {
                        _logger.LogWarning("没有找到可用的AI配置，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "没有找到可用的AI配置");
                        return;
                    }

                    _logger.LogInformation("使用AI配置: 提供商={Provider}, 模型={Model}", config.Provider, config.Model);

                    generatedMermaid = await _aiService.GenerateERDiagramAsync(prompt, null, config);

                    // 更新使用统计
                    await AIConfigurationHelper.UpdateUsageStatisticsAsync(_aiConfigRepository, _logger, request.AIProviderConfigId.Value);

                    // 更新任务进度
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 70, "正在验证和处理生成结果...");

                    // 清理和验证生成的Mermaid语法
                    var cleanedMermaid = CleanMermaidResponse(generatedMermaid);
                    var validatedMermaid = ValidateMermaidSyntax(cleanedMermaid, "erDiagram");

                    _logger.LogInformation("原始Mermaid代码长度: {OriginalLength}, 修复后长度: {ValidatedLength}",
                        generatedMermaid?.Length ?? 0, validatedMermaid?.Length ?? 0);

                    // 创建ER图记录
                    var erDiagram = new
                    {
                        ProjectId = request.ProjectId,
                        RequirementDocumentId = request.RequirementDocumentId,
                        DiagramName = $"AI生成ER图 - {DateTime.Now:yyyy-MM-dd HH:mm}",
                        MermaidDefinition = validatedMermaid,
                        Description = $"基于{request.DatabaseType}数据库类型生成的ER图",
                        Version = "1.0",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // 记录模板使用情况
                    if (_promptTemplateService != null)
                    {
                        try
                        {
                            var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("ERDiagramGeneration");
                            if (defaultTemplate != null)
                            {
                                await _promptTemplateService.RecordTemplateUsageAsync(
                                    defaultTemplate.Id, userId, request.ProjectId,
                                    request.PreferredModel ?? "default", "ai-model",
                                    System.Text.Json.JsonSerializer.Serialize(new { request.DatabaseType, request.DiagramFormat }),
                                    prompt, generatedMermaid, null, null, null, true, null);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "记录ER图生成模板使用失败");
                        }
                    }

                    _logger.LogInformation("ER图生成任务完成: {TaskId}", taskId);

                    // TODO: 保存ER图到数据库
                    // await _erDiagramRepository.CreateAsync(erDiagram);

                    // 更新对话记录的AI响应
                    try
                    {
                        var conversations = await _conversationRepository.GetConversationHistoryAsync(conversationId, 1);
                        var conversation = conversations.FirstOrDefault();
                        if (conversation != null)
                        {
                            conversation.AIResponse = $"已成功生成ER图，包含 {CountEntitiesInMermaid(validatedMermaid)} 个实体。生成的Mermaid定义长度：{validatedMermaid.Length} 字符。";
                            await _conversationRepository.UpdateConversationAsync(conversation);
                            _logger.LogInformation("已更新对话记录的AI响应: {ConversationId}", conversationId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "更新对话记录AI响应失败: {ConversationId}", conversationId);
                    }

                    // 更新任务状态为完成
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Completed", 100, "ER图生成完成", erDiagram);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "ER图生成任务执行失败: {TaskId}", taskId);

                    // 更新对话记录的AI响应（失败情况）
                    try
                    {
                        var conversations = await _conversationRepository.GetConversationHistoryAsync(conversationId, 1);
                        var conversation = conversations.FirstOrDefault();
                        if (conversation != null)
                        {
                            conversation.AIResponse = $"ER图生成失败：{ex.Message}";
                            await _conversationRepository.UpdateConversationAsync(conversation);
                            _logger.LogInformation("已更新对话记录的AI响应（失败）: {ConversationId}", conversationId);
                        }
                    }
                    catch (Exception updateEx)
                    {
                        _logger.LogWarning(updateEx, "更新对话记录AI响应失败（失败情况）: {ConversationId}", conversationId);
                    }

                    // 更新任务状态为失败
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "ER图生成失败", null, ex.Message);
                }
            });

            // 5. 返回任务ID供客户端跟踪进度
            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "ERDiagramGeneration",
                Status = "Started",
                Message = "ER图生成任务已启动，正在使用AI智能生成数据库设计",
                EstimatedDuration = TimeSpan.FromMinutes(3)
            };

            _logger.LogInformation("ER图生成任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建ER图生成任务时发生错误");
            return StatusCode(500, new { message = "创建ER图生成任务失败" });
        }
    }

    /// <summary>
    /// 生成Context图
    /// </summary>
    /// <param name="request">Context图生成请求</param>
    /// <returns>Context图生成任务ID</returns>
    [HttpPost("generate-context-diagram")]
    public async Task<ActionResult<AITaskResponseDto>> GenerateContextDiagram([FromBody] ContextDiagramGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求生成Context图: 项目 {ProjectId}", userId, request.ProjectId);

            // 1. 验证项目权限
            // TODO: 添加项目权限验证

            // 2. 创建任务并获取任务ID
            var taskId = await _taskStatusService.CreateTaskAsync("ContextDiagramGeneration", userId, request.ProjectId, "上下文图生成任务已启动");

            // 3. 预先记录对话ID，稍后在获取prompt后更新
            var conversationId = $"context-diagram-{taskId}";

            // 4. 启动后台任务进行Context图生成
            _ = Task.Run(async () =>
            {
                try
                {
                    // 更新任务状态为运行中
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 10, "开始生成上下文图...");

                    _logger.LogInformation("开始执行Context图生成任务: {TaskId}", taskId);

                    // 使用Prompt工程构建Context图生成提示词
                    string prompt;
                    try
                    {
                        var parameters = new Dictionary<string, object>
                        {
                            ["projectId"] = request.ProjectId,
                            ["diagramFormat"] = request.DiagramFormat ?? "Mermaid",
                            ["includeExternalSystems"] = request.IncludeExternalSystems,
                            ["requirementDocumentId"] = request.RequirementDocumentId ?? 0
                        };

                        prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("ContextDiagramGeneration", parameters, userId, request.ProjectId);
                    }
                    catch (Exception ex)
                    {
                        prompt = "使用Prompt模板失败";
                    }

                    // 记录用户请求到对话表（包含实际的AI prompt）
                    try
                    {
                        await _conversationRepository.CreateConversationAsync(new RequirementConversation
                        {
                            ConversationId = conversationId,
                            ProjectId = request.ProjectId,
                            UserId = userId,
                            UserMessage = prompt, // 记录实际发送给AI的prompt
                            AIResponse = null, // 初始时AI还没有响应
                            MessageType = "ContextDiagramGeneration",
                            Timestamp = DateTime.UtcNow
                        });

                        _logger.LogInformation("已记录上下文图生成请求到对话表: {ConversationId}", conversationId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "记录上下文图生成请求到对话表失败: {ConversationId}", conversationId);
                    }

                    // 更新任务进度
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 30, "正在调用AI生成上下文图...");

                    // 获取AI配置 - 必须使用指定的AI提供商配置ID
                    string generatedMermaid;

                    if (!request.AIProviderConfigId.HasValue)
                    {
                        _logger.LogWarning("未指定AI提供商配置ID，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "请选择AI提供商");
                        return;
                    }

                    // 使用Helper获取AI配置，Context图生成使用较低的温度以确保一致性
                    var config = await AIConfigurationHelper.GetUserAIConfigurationAsync(
                        _aiConfigRepository,
                        _logger,
                        userId,
                        request.AIProviderConfigId.Value,
                        4000, // maxTokens
                        0.3f, // temperature - Context图生成使用较低的温度
                        _encryptionService
                    );

                    if (config == null)
                    {
                        _logger.LogWarning("没有找到可用的AI配置，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "没有找到可用的AI配置");
                        return;
                    }

                    _logger.LogInformation("使用AI配置生成Context图: {Provider}, {Model}, 用户: {UserId}",
                        config.Provider, config.Model, userId);

                    generatedMermaid = await _aiService.GenerateContextDiagramAsync(prompt, null, config);

                    // 更新使用统计
                    await AIConfigurationHelper.UpdateUsageStatisticsAsync(_aiConfigRepository, _logger, request.AIProviderConfigId.Value);

                    // 更新任务进度
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 70, "正在验证和处理生成结果...");

                    // 清理和验证生成的Mermaid语法
                    var cleanedMermaid = CleanMermaidResponse(generatedMermaid);
                    var validatedMermaid = ValidateMermaidSyntax(cleanedMermaid, "flowchart");

                    // 创建Context图记录
                    var contextDiagram = new
                    {
                        ProjectId = request.ProjectId,
                        RequirementDocumentId = request.RequirementDocumentId,
                        DiagramName = $"AI生成上下文图 - {DateTime.Now:yyyy-MM-dd HH:mm}",
                        MermaidDefinition = validatedMermaid,
                        ExternalEntities = ExtractExternalEntities(validatedMermaid),
                        SystemBoundary = ExtractSystemBoundary(validatedMermaid),
                        DataFlows = ExtractDataFlows(validatedMermaid),
                        Version = "1.0",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    // 记录模板使用情况
                    if (_promptTemplateService != null)
                    {
                        try
                        {
                            var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("ContextDiagramGeneration");
                            if (defaultTemplate != null)
                            {
                                await _promptTemplateService.RecordTemplateUsageAsync(
                                    defaultTemplate.Id, userId, request.ProjectId,
                                    request.PreferredModel ?? "default", "ai-model",
                                    System.Text.Json.JsonSerializer.Serialize(new { request.DiagramFormat, request.IncludeExternalSystems }),
                                    prompt, generatedMermaid, null, null, null, true, null);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "记录Context图生成模板使用失败");
                        }
                    }

                    _logger.LogInformation("Context图生成任务完成: {TaskId}", taskId);

                    // TODO: 保存Context图到数据库
                    // await _contextDiagramRepository.CreateAsync(contextDiagram);

                    // 更新对话记录的AI响应
                    try
                    {
                        var conversations = await _conversationRepository.GetConversationHistoryAsync(conversationId, 1);
                        var conversation = conversations.FirstOrDefault();
                        if (conversation != null)
                        {
                            var externalEntitiesCount = CountExternalEntitiesInMermaid(validatedMermaid);
                            conversation.AIResponse = $"已成功生成上下文图，包含 {externalEntitiesCount} 个外部实体。生成的Mermaid定义长度：{validatedMermaid.Length} 字符。";
                            await _conversationRepository.UpdateConversationAsync(conversation);
                            _logger.LogInformation("已更新对话记录的AI响应: {ConversationId}", conversationId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "更新对话记录AI响应失败: {ConversationId}", conversationId);
                    }

                    // 更新任务状态为完成
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Completed", 100, "上下文图生成完成", contextDiagram);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Context图生成任务执行失败: {TaskId}", taskId);

                    // 更新对话记录的AI响应（失败情况）
                    try
                    {
                        var conversations = await _conversationRepository.GetConversationHistoryAsync(conversationId, 1);
                        var conversation = conversations.FirstOrDefault();
                        if (conversation != null)
                        {
                            conversation.AIResponse = $"上下文图生成失败：{ex.Message}";
                            await _conversationRepository.UpdateConversationAsync(conversation);
                            _logger.LogInformation("已更新对话记录的AI响应（失败）: {ConversationId}", conversationId);
                        }
                    }
                    catch (Exception updateEx)
                    {
                        _logger.LogWarning(updateEx, "更新对话记录AI响应失败（失败情况）: {ConversationId}", conversationId);
                    }

                    // 更新任务状态为失败
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "上下文图生成失败", null, ex.Message);
                }
            });

            // 5. 返回任务ID供客户端跟踪进度
            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "ContextDiagramGeneration",
                Status = "Started",
                Message = "上下文图生成任务已启动，正在使用AI智能分析系统边界",
                EstimatedDuration = TimeSpan.FromMinutes(3)
            };

            _logger.LogInformation("Context图生成任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建Context图生成任务时发生错误");
            return StatusCode(500, new { message = "创建Context图生成任务失败" });
        }
    }

    /// <summary>
    /// 生成原型图
    /// </summary>
    /// <param name="request">原型图生成请求</param>
    /// <returns>原型图生成任务ID</returns>
    [HttpPost("generate-prototype")]
    public async Task<ActionResult<AITaskResponseDto>> GeneratePrototype([FromBody] PrototypeGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求生成原型图: 项目 {ProjectId}, 类型 {PrototypeType}",
                userId, request.ProjectId, request.PrototypeType);

            // 1. 验证项目权限
            // TODO: 添加项目权限验证

            // 2. 创建任务并获取任务ID
            var taskId = await _taskStatusService.CreateTaskAsync("PrototypeGeneration", userId, request.ProjectId, "原型图生成任务已启动");

            // 3. 获取项目信息
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "项目不存在");
                return BadRequest(new { message = "项目不存在" });
            }

            // 4. 在Task.Run之前获取所需的服务，避免ObjectDisposedException
            var prototypeRepository = _serviceProvider.GetRequiredService<IRepository<Prototype>>();

            // 5. 异步执行原型图生成
            _ = Task.Run(async () =>
            {
                try
                {
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 10, "正在分析项目需求...");

                    // 获取需求文档内容
                    string requirementContent = "";
                    if (request.RequirementDocumentId.HasValue)
                    {
                        var requirement = await _requirementRepository.GetByIdAsync(request.RequirementDocumentId.Value);
                        if (requirement != null)
                        {
                            requirementContent = requirement.Content ?? "";
                        }
                    }

                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 30, "正在构建AI提示词...");

                    // 构建AI提示词
                    var promptBuilder = new StringBuilder();
                    promptBuilder.AppendLine($"请为以下项目生成{GetPrototypeTypeDisplayName(request.PrototypeType)}：");
                    promptBuilder.AppendLine($"项目名称：{project.Name}");
                    promptBuilder.AppendLine($"项目描述：{project.Description}");

                    if (!string.IsNullOrEmpty(requirementContent))
                    {
                        promptBuilder.AppendLine($"需求内容：{requirementContent}");
                    }

                    promptBuilder.AppendLine($"设备类型：{request.DeviceType}");
                    promptBuilder.AppendLine($"保真度级别：{request.FidelityLevel}");

                    if (!string.IsNullOrEmpty(request.TargetUsers))
                    {
                        promptBuilder.AppendLine($"目标用户：{request.TargetUsers}");
                    }

                    promptBuilder.AppendLine();
                    promptBuilder.AppendLine(GetPrototypePromptTemplate(request.PrototypeType, request.DeviceType, request.FidelityLevel));

                    var prompt = promptBuilder.ToString();

                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 50, "正在调用AI生成原型图...");

                    // 获取AI配置 - 必须使用指定的AI提供商配置ID
                    string generatedMermaid;

                    if (!request.AIProviderConfigId.HasValue)
                    {
                        _logger.LogWarning("未指定AI提供商配置ID，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "请选择AI提供商");
                        return;
                    }

                    // 使用Helper获取AI配置，原型图生成使用中等温度以平衡创意和一致性
                    var config = await AIConfigurationHelper.GetUserAIConfigurationAsync(
                        _aiConfigRepository,
                        _logger,
                        userId,
                        request.AIProviderConfigId.Value,
                        4000, // maxTokens
                        0.7f, // temperature - 原型图生成使用中等温度
                        _encryptionService
                    );

                    if (config == null)
                    {
                        _logger.LogWarning("没有找到可用的AI配置，用户: {UserId}", userId);
                        await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "没有找到可用的AI配置");
                        return;
                    }

                    _logger.LogInformation("使用AI配置生成原型图: {Provider}, {Model}, 用户: {UserId}",
                        config.Provider, config.Model, userId);

                    generatedMermaid = await _aiService.GeneratePrototypeAsync(prompt, null, config);

                    // 更新使用统计
                    await AIConfigurationHelper.UpdateUsageStatisticsAsync(_aiConfigRepository, _logger, request.AIProviderConfigId.Value);

                    // 更新任务进度
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 70, "正在验证和处理生成结果...");

                    // 清理和验证生成的Mermaid语法
                    var cleanedMermaid = CleanMermaidResponse(generatedMermaid);
                    var validatedMermaid = ValidateMermaidSyntax(cleanedMermaid, GetMermaidDiagramType(request.PrototypeType));

                    _logger.LogInformation("原始Mermaid代码长度: {OriginalLength}, 修复后长度: {ValidatedLength}",
                        generatedMermaid?.Length ?? 0, validatedMermaid?.Length ?? 0);

                    _logger.LogInformation("生成的Mermaid内容预览: {MermaidPreview}",
                        validatedMermaid?.Length > 100 ? validatedMermaid.Substring(0, 100) + "..." : validatedMermaid);

                    // 创建原型图记录
                    var prototype = new Prototype
                    {
                        ProjectId = request.ProjectId,
                        RequirementDocumentId = request.RequirementDocumentId,
                        PrototypeName = $"{project.Name} - {GetPrototypeTypeDisplayName(request.PrototypeType)}",
                        PrototypeType = request.PrototypeType,
                        MermaidDefinition = validatedMermaid,
                        Description = $"AI自动生成的{GetPrototypeTypeDisplayName(request.PrototypeType)}",
                        TargetUsers = request.TargetUsers,
                        DeviceType = request.DeviceType,
                        FidelityLevel = request.FidelityLevel,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now
                    };

                    var createdPrototype = await prototypeRepository.AddAsync(prototype);

                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Running", 90, "正在保存原型图...");

                    // 更新任务状态为完成
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Completed", 100, "原型图生成完成",
                        new {
                            prototypeId = createdPrototype.Id,
                            prototypeName = createdPrototype.PrototypeName,
                            mermaidDefinition = createdPrototype.MermaidDefinition
                        });

                    _logger.LogInformation("原型图生成完成: {PrototypeId}, 任务: {TaskId}", createdPrototype.Id, taskId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "原型图生成失败，任务: {TaskId}", taskId);

                    // 更新任务状态为失败
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, "原型图生成失败", null, ex.Message);
                }
            });

            // 5. 返回任务ID供客户端跟踪进度
            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "PrototypeGeneration",
                Status = "Started",
                Message = $"原型图生成任务已启动，正在使用AI智能生成{GetPrototypeTypeDisplayName(request.PrototypeType)}",
                EstimatedDuration = TimeSpan.FromMinutes(2)
            };

            _logger.LogInformation("原型图生成任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建原型图生成任务时发生错误");
            return StatusCode(500, new { message = "创建原型图生成任务失败" });
        }
    }

    /// <summary>
    /// 生成代码
    /// </summary>
    /// <param name="request">代码生成请求</param>
    /// <returns>代码生成任务ID</returns>
    [HttpPost("generate-code")]
    public async Task<ActionResult<AITaskResponseDto>> GenerateCode([FromBody] CodeGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求代码生成: 项目 {ProjectId}, 技术栈 {TechStack}",
                userId, request.ProjectId, request.TechnologyStack);

            // TODO: 实现代码生成逻辑
            var taskId = Guid.NewGuid().ToString();

            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "CodeGeneration",
                Status = "Started",
                Message = "代码生成任务已启动",
                EstimatedDuration = TimeSpan.FromMinutes(10)
            };

            _logger.LogInformation("代码生成任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建代码生成任务时发生错误");
            return StatusCode(500, new { message = "创建代码生成任务失败" });
        }
    }

    /// <summary>
    /// 生成测试用例
    /// </summary>
    /// <param name="request">测试生成请求</param>
    /// <returns>测试生成任务ID</returns>
    [HttpPost("generate-tests")]
    public async Task<ActionResult<AITaskResponseDto>> GenerateTests([FromBody] TestGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求生成测试: 项目 {ProjectId}, 测试类型 {TestType}",
                userId, request.ProjectId, request.TestType);

            // 1. 验证请求参数
            if (request.ProjectId <= 0)
            {
                return BadRequest(new { message = "项目ID无效" });
            }

            // 2. 验证项目权限
            // TODO: 添加项目权限验证
            // var hasPermission = await _projectService.HasUserAccessAsync(userId, request.ProjectId);
            // if (!hasPermission)
            // {
            //     return Forbid("没有访问该项目的权限");
            // }

            // 3. 创建任务并获取任务ID
            var taskId = await _taskStatusService.CreateTaskAsync("TestGeneration", userId, request.ProjectId, "测试生成任务已启动");

            // 4. 预先记录对话ID，稍后在获取prompt后更新
            var conversationId = $"test-generation-{taskId}";

            // 5. 启动后台任务进行测试生成
            _ = Task.Run(async () =>
            {
                try
                {
                    _logger.LogInformation("开始执行测试生成任务: {TaskId}", taskId);

                    // 更新任务状态为进行中
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "InProgress", 10, "正在分析项目和生成测试用例...");

                    // 获取项目信息和相关代码
                    var projectInfo = await GetProjectInfoForTestGeneration(request.ProjectId);
                    var codeContent = await GetCodeContentForTesting(request.ProjectId, request.CodeGenerationTaskId);

                    // 构建AI模型配置
                    var aiConfig = await GetAIConfigForUser(userId, request.PreferredModel);

                    // 调用AI服务生成测试用例
                    var testCases = await _aiService.GenerateTestCasesAsync(
                        projectInfo.Specification ?? "项目规格说明",
                        codeContent,
                        aiConfig
                    );

                    // 解析和保存测试用例
                    var savedTests = await SaveGeneratedTestCases(request.ProjectId, testCases, request.TestType, request.TestFramework);

                    // 更新任务状态为完成
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Completed", 100,
                        $"测试生成完成，共生成 {savedTests.Count} 个测试用例");

                    _logger.LogInformation("测试生成任务完成: {TaskId}, 生成测试数量: {Count}", taskId, savedTests.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "测试生成任务执行失败: {TaskId}", taskId);
                    await _taskStatusService.UpdateTaskStatusAsync(taskId, "Failed", 0, $"测试生成失败: {ex.Message}", null, ex.Message);
                }
            });

            var response = new AITaskResponseDto
            {
                TaskId = taskId,
                TaskType = "TestGeneration",
                Status = "Started",
                Message = "测试生成任务已启动，正在后台处理...",
                EstimatedDuration = TimeSpan.FromMinutes(5),
                ConversationId = conversationId
            };

            _logger.LogInformation("测试生成任务已创建: {TaskId}", taskId);

            return Accepted(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建测试生成任务时发生错误");
            return StatusCode(500, new { message = "创建测试生成任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 快速测试生成（同步版本，用于测试）
    /// </summary>
    /// <param name="request">测试生成请求</param>
    /// <returns>生成的测试用例</returns>
    [HttpPost("generate-tests-sync")]
    public async Task<ActionResult<TestGenerationResultDto>> GenerateTestsSync([FromBody] TestGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求同步生成测试: 项目 {ProjectId}, 测试类型 {TestType}",
                userId, request.ProjectId, request.TestType);

            // 1. 验证请求参数
            if (request.ProjectId <= 0)
            {
                return BadRequest(new { message = "项目ID无效" });
            }

            // 2. 获取项目信息和相关代码
            var projectInfo = await GetProjectInfoForTestGeneration(request.ProjectId);
            var codeContent = await GetCodeContentForTesting(request.ProjectId, request.CodeGenerationTaskId);

            // 3. 构建AI模型配置
            var aiConfig = await GetAIConfigForUser(userId, request.PreferredModel);

            // 4. 调用AI服务生成测试用例
            var testCases = await _aiService.GenerateTestCasesAsync(
                projectInfo.Specification ?? "项目规格说明",
                codeContent,
                aiConfig
            );

            // 5. 解析和保存测试用例
            var savedTests = await SaveGeneratedTestCases(request.ProjectId, testCases, request.TestType, request.TestFramework);

            var result = new TestGenerationResultDto
            {
                Success = true,
                Message = $"成功生成 {savedTests.Count} 个测试用例",
                TestCases = savedTests.Select(t => new TestCaseDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Description = t.Description,
                    Code = t.Code,
                    TestType = t.TestType,
                    Framework = t.Framework
                }).ToList(),
                GeneratedContent = testCases
            };

            _logger.LogInformation("同步测试生成完成: 项目 {ProjectId}, 生成数量: {Count}", request.ProjectId, savedTests.Count);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步生成测试用例失败");
            return StatusCode(500, new TestGenerationResultDto
            {
                Success = false,
                Message = $"生成测试用例失败: {ex.Message}",
                TestCases = new List<TestCaseDto>()
            });
        }
    }

    /// <summary>
    /// 通用AI聊天接口（流式响应）
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <returns>流式AI回复</returns>
    [HttpPost("chat/stream")]
    public async Task ChatStream([FromBody] AIChatRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 发起AI流式聊天: 任务类型 {TaskType}, 对话 {ConversationId}",
                userId, request.TaskType, request.ConversationId);

            // 设置响应头为流式传输
            Response.Headers.Add("Content-Type", "text/plain; charset=utf-8");
            Response.Headers.Add("Cache-Control", "no-cache");
            Response.Headers.Add("Connection", "keep-alive");

            // 1. 构建提示词
            string prompt = await BuildChatPrompt(request, userId);

            // 2. 获取AI配置
            var aiConfig = await GetAIConfiguration(request.AIProviderConfigId, userId);

            // 3. 发送开始标记
            await WriteToStream($"data: {{\"type\":\"start\",\"messageId\":\"{Guid.NewGuid()}\",\"conversationId\":\"{request.ConversationId}\"}}\n\n");

            // 4. 调用AI服务并流式返回
            var fullResponse = new StringBuilder();

            if (aiConfig != null)
            {
                // 这里需要AI服务支持流式响应，暂时模拟流式效果
                var aiResponse = await _aiService.GenerateTextAsync(prompt, aiConfig);
                await SimulateStreamResponse(aiResponse, fullResponse);

                // 更新使用统计
                if (request.AIProviderConfigId.HasValue)
                {
                    await _userAIConfigRepository.UpdateUsageStatisticsAsync(
                        request.AIProviderConfigId.Value, 4000, true);
                }
            }
            else
            {
                _logger.LogWarning("没有找到可用的AI配置，使用默认配置，用户: {UserId}", userId);
                var aiResponse = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
                await SimulateStreamResponse(aiResponse, fullResponse);
            }

            // 5. 发送建议操作
            var suggestedActions = GenerateSuggestedActions(request.TaskType, fullResponse.ToString());
            if (suggestedActions?.Any() == true)
            {
                var actionsJson = System.Text.Json.JsonSerializer.Serialize(suggestedActions);
                await WriteToStream($"data: {{\"type\":\"actions\",\"actions\":{actionsJson}}}\n\n");
            }

            // 6. 发送结束标记
            await WriteToStream($"data: {{\"type\":\"end\"}}\n\n");

            // 7. 保存对话记录
            try
            {
                await _conversationContextService.SaveConversationAsync(
                    request.ConversationId,
                    request.ProjectId, // 直接传入，可以为NULL
                    userId,
                    request.Message,
                    fullResponse.ToString(),
                    request.TaskType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存对话记录失败: {ConversationId}", request.ConversationId);
            }

            _logger.LogInformation("AI流式聊天完成: {ConversationId}", request.ConversationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI流式聊天失败: {ConversationId}", request.ConversationId);
            await WriteToStream($"data: {{\"type\":\"error\",\"message\":\"{ex.Message}\"}}\n\n");
        }
    }

    /// <summary>
    /// 获取对话历史
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="includeVectorContext">是否包含向量搜索上下文</param>
    /// <returns>对话历史列表</returns>
    [HttpGet("conversation/{conversationId}/history")]
    public async Task<ActionResult<ConversationHistoryResponse>> GetConversationHistory(
        string conversationId,
        [FromQuery] bool includeVectorContext = false)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取AI对话历史: 对话 {ConversationId}, 用户 {UserId}, 向量上下文: {IncludeVector}",
                conversationId, userId, includeVectorContext);

            var history = await _conversationRepository.GetConversationHistoryAsync(conversationId, 50);

            var messages = history.OrderBy(x => x.Timestamp).Select(x => new ConversationMessageDto
            {
                MessageId = x.Id.ToString(),
                ConversationId = x.ConversationId ?? conversationId,
                UserMessage = x.UserMessage,
                AIResponse = x.AIResponse ?? "",
                MessageType = x.MessageType,
                Timestamp = x.Timestamp ?? DateTime.UtcNow
            }).ToList();

            var response = new ConversationHistoryResponse
            {
                Messages = messages,
                TotalCount = messages.Count,
                ConversationId = conversationId
            };

            // 如果需要向量上下文，获取相关文档
            if (includeVectorContext && messages.Any())
            {
                response.VectorContext = await GetConversationVectorContextAsync(conversationId, messages);
            }

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI对话历史失败: {ConversationId}", conversationId);
            return StatusCode(500, new { message = "获取对话历史失败" });
        }
    }

    /// <summary>
    /// 获取对话的向量上下文
    /// </summary>
    private async Task<List<VectorContextItem>> GetConversationVectorContextAsync(
        string conversationId,
        List<ConversationMessageDto> messages)
    {
        try
        {
            var vectorContext = new List<VectorContextItem>();

            // 获取最近几条用户消息作为查询
            var recentUserMessages = messages
                .Where(m => !string.IsNullOrEmpty(m.UserMessage))
                .TakeLast(3)
                .Select(m => m.UserMessage)
                .ToList();

            if (!recentUserMessages.Any())
            {
                return vectorContext;
            }

            // 合并查询文本
            var combinedQuery = string.Join(" ", recentUserMessages);

            // 执行向量搜索
            var searchOptions = new SearchOptions
            {
                TopK = 5,
                SimilarityThreshold = 0.7f
            };

            var searchResults = await _vectorSearchService.SemanticSearchAsync(combinedQuery, searchOptions);

            // 转换为向量上下文项
            vectorContext = searchResults.Select(r => new VectorContextItem
            {
                Content = r.Content,
                SimilarityScore = r.SimilarityScore,
                DocumentId = r.DocumentId,
                Metadata = r.Metadata
            }).ToList();

            _logger.LogInformation("为对话 {ConversationId} 获取了 {Count} 个向量上下文项",
                conversationId, vectorContext.Count);

            return vectorContext;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取对话向量上下文失败: {ConversationId}", conversationId);
            return new List<VectorContextItem>();
        }
    }

    /// <summary>
    /// 通用AI聊天接口
    /// </summary>
    /// <param name="request">聊天请求</param>
    /// <returns>AI回复</returns>
    [HttpPost("chat")]
    public async Task<ActionResult<AIChatResponseDto>> Chat([FromBody] AIChatRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 发起AI聊天: 任务类型 {TaskType}, 对话 {ConversationId}",
                userId, request.TaskType, request.ConversationId);

            // 1. 构建提示词
            string prompt = await BuildChatPrompt(request, userId);

            // 2. 获取AI配置
            var aiConfig = await GetAIConfiguration(request.AIProviderConfigId, userId);

            // 3. 调用AI服务
            string aiResponse;
            if (aiConfig != null)
            {
                aiResponse = await _aiService.GenerateTextAsync(prompt, aiConfig);

                // 更新使用统计
                if (request.AIProviderConfigId.HasValue)
                {
                    await _userAIConfigRepository.UpdateUsageStatisticsAsync(
                        request.AIProviderConfigId.Value, 4000, true);
                }
            }
            else
            {
                _logger.LogWarning("没有找到可用的AI配置，使用默认配置，用户: {UserId}", userId);
                aiResponse = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
            }

            // 4. 保存对话记录
            try
            {
                await _conversationContextService.SaveConversationAsync(
                    request.ConversationId,
                    request.ProjectId, // 直接传入，可以为NULL
                    userId,
                    request.Message,
                    aiResponse,
                    request.TaskType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存对话记录失败: {ConversationId}", request.ConversationId);
                // 不影响主流程，继续返回结果
            }

            // 5. 构建响应
            var response = new AIChatResponseDto
            {
                MessageId = Guid.NewGuid().ToString(),
                ConversationId = request.ConversationId,
                UserMessage = request.Message,
                AIResponse = aiResponse,
                Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                TaskType = request.TaskType,
                SuggestedActions = GenerateSuggestedActions(request.TaskType, aiResponse)
            };

            _logger.LogInformation("AI聊天完成: {ConversationId}", request.ConversationId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI聊天时发生错误: {ConversationId}", request.ConversationId);
            return StatusCode(500, new { message = "AI聊天失败" });
        }
    }

    /// <summary>
    /// 获取AI任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务状态信息</returns>
    [HttpGet("tasks/{taskId}/status")]
    public async Task<ActionResult<AITaskStatusDto>> GetTaskStatus(string taskId)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 查询任务状态: {TaskId}", userId, taskId);

            // 从任务状态服务获取真实状态
            var status = await _taskStatusService.GetTaskStatusAsync(taskId);

            if (status == null)
            {
                _logger.LogWarning("任务不存在: {TaskId}", taskId);
                return NotFound(new { message = "任务不存在" });
            }

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查询任务状态时发生错误: {TaskId}", taskId);
            return StatusCode(500, new { message = "查询任务状态失败" });
        }
    }

    /// <summary>
    /// 获取AI任务结果
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务结果</returns>
    [HttpGet("tasks/{taskId}/result")]
    public async Task<ActionResult<AITaskResultDto>> GetTaskResult(string taskId)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 获取任务结果: {TaskId}", userId, taskId);

            // TODO: 实现任务结果获取逻辑
            // 模拟响应
            var result = new AITaskResultDto
            {
                TaskId = taskId,
                Status = "Completed",
                Result = new
                {
                    Type = "RequirementDocument",
                    Content = "这是生成的需求规格书内容...",
                    GeneratedAt = DateTime.UtcNow
                },
                CompletedAt = DateTime.UtcNow
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务结果时发生错误: {TaskId}", taskId);
            return StatusCode(500, new { message = "获取任务结果失败" });
        }
    }

    /// <summary>
    /// 取消AI任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>取消结果</returns>
    [HttpPost("tasks/{taskId}/cancel")]
    public async Task<ActionResult> CancelTask(string taskId)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 取消任务: {TaskId}", userId, taskId);

            // TODO: 实现任务取消逻辑

            return Ok(new { message = "任务已取消" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消任务时发生错误: {TaskId}", taskId);
            return StatusCode(500, new { message = "取消任务失败" });
        }
    }

    /// <summary>
    /// 获取支持的AI模型列表
    /// </summary>
    /// <returns>AI模型列表</returns>
    [HttpGet("models")]
    public async Task<ActionResult<List<AIModelInfoDto>>> GetAvailableModels()
    {
        try
        {
            _logger.LogInformation("获取可用AI模型列表");

            // TODO: 从配置或AI服务获取实际的模型列表
            var models = new List<AIModelInfoDto>
            {
                new() { Provider = "Azure", Model = "gpt-4", Description = "GPT-4 模型，适合复杂任务", IsAvailable = true },
                new() { Provider = "Azure", Model = "gpt-3.5-turbo", Description = "GPT-3.5 Turbo，快速响应", IsAvailable = true },
                new() { Provider = "DeepSeek", Model = "deepseek-coder", Description = "DeepSeek 代码生成专用模型", IsAvailable = true },
                new() { Provider = "OpenAI", Model = "gpt-4", Description = "OpenAI GPT-4", IsAvailable = false },
                new() { Provider = "Claude", Model = "claude-3-opus", Description = "Claude 3 Opus", IsAvailable = false }
            };

            return Ok(models);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI模型列表时发生错误");
            return StatusCode(500, new { message = "获取模型列表失败" });
        }
    }

    /// <summary>
    /// 获取AI服务使用统计
    /// </summary>
    /// <returns>使用统计信息</returns>
    [HttpGet("usage-statistics")]
    public async Task<ActionResult<AIUsageStatisticsDto>> GetUsageStatistics()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("获取用户 {UserId} 的AI服务使用统计", userId);

            // TODO: 实现使用统计逻辑
            var statistics = new AIUsageStatisticsDto
            {
                TotalRequests = 150,
                SuccessfulRequests = 142,
                FailedRequests = 8,
                TotalTokensUsed = 125000,
                EstimatedCost = 15.75m,
                LastUsed = DateTime.UtcNow.AddHours(-2)
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI使用统计时发生错误");
            return StatusCode(500, new { message = "获取使用统计失败" });
        }
    }

    /// <summary>
    /// 获取AI提供商列表
    /// </summary>
    [HttpGet("providers")]
    public async Task<ActionResult<object>> GetProviders()
    {
        try
        {
            var userId = GetCurrentUserId();

            // 从数据库获取当前用户的AI模型配置
            var dbConfigurations = await _aiConfigRepository.GetByUserIdAsync(userId);

            // 按提供商分组
            var providerGroups = dbConfigurations
                .GroupBy(x => AIConfigurationHelper.GetProviderFromModelName(x.ModelName))
                .ToList();

            var providers = new List<object>();

            // 添加数据库中的提供商
            foreach (var group in providerGroups)
            {
                var providerName = group.Key;
                var configs = group.ToList();
                var activeConfigs = configs.Where(x => x.IsActive).ToList();

                // 尝试从第一个配置的ModelParameters中获取自定义供应商信息
                string displayName = GetProviderDisplayName(providerName);
                string description = GetProviderDescription(providerName);

                var firstConfig = configs.FirstOrDefault();
                if (firstConfig != null && !string.IsNullOrEmpty(firstConfig.ModelParameters))
                {
                    try
                    {
                        var parameters = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(firstConfig.ModelParameters);
                        if (parameters != null)
                        {
                            if (parameters.ContainsKey("DisplayName") && parameters["DisplayName"] != null)
                            {
                                displayName = parameters["DisplayName"].ToString() ?? displayName;
                            }
                            if (parameters.ContainsKey("Description") && parameters["Description"] != null)
                            {
                                description = parameters["Description"].ToString() ?? description;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "解析供应商参数失败: {ProviderName}", providerName);
                    }
                }

                providers.Add(new
                {
                    Name = providerName,
                    DisplayName = displayName,
                    Description = description,
                    IsEnabled = activeConfigs.Any(),
                    IsAvailable = activeConfigs.Any(), // 简化逻辑，后续可以添加健康检查
                    SupportedModels = configs.Select(x => x.ModelName).Distinct().ToArray(),
                    Config = new { },
                    UsageStats = new
                    {
                        TotalRequests = 0, // TODO: 从实际使用统计获取
                        SuccessfulRequests = 0,
                        FailedRequests = 0,
                        AverageResponseTime = 0.0,
                        TotalTokens = 0L,
                        TotalCost = 0.0m,
                        LastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                });
            }

            // 如果数据库中没有配置，添加默认的Mock提供商
            if (!providers.Any())
            {
                providers.Add(new
                {
                    Name = "Mock",
                    DisplayName = "模拟AI",
                    Description = "用于开发和测试的模拟AI服务",
                    IsEnabled = true,
                    IsAvailable = true,
                    SupportedModels = new[] { "mock-gpt-4", "mock-gpt-3.5-turbo" },
                    Config = new { },
                    UsageStats = new
                    {
                        TotalRequests = 150,
                        SuccessfulRequests = 145,
                        FailedRequests = 5,
                        AverageResponseTime = 1200.0,
                        TotalTokens = 15000L,
                        TotalCost = 0.0m,
                        LastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                });
            }

            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商列表失败");
            return StatusCode(500, new { message = "获取提供商列表失败" });
        }
    }

    /// <summary>
    /// 创建AI供应商
    /// </summary>
    [HttpPost("providers")]
    public async Task<ActionResult<object>> CreateProvider([FromBody] CreateProviderRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("创建AI供应商: {ProviderName}, UserId: {UserId}", request.Name, userId);

            // 验证供应商名称是否已存在
            var existingConfigs = await _aiConfigRepository.GetByUserIdAsync(userId);
            var existingProvider = existingConfigs.FirstOrDefault(x =>
                AIConfigurationHelper.GetProviderFromModelName(x.ModelName) == request.Name);

            if (existingProvider != null)
            {
                return BadRequest(new { message = $"供应商 '{request.Name}' 已存在" });
            }

            // 加密API密钥
            var encryptedApiKey = _encryptionService.Encrypt(request.ApiKey);

            // 创建AI模型配置
            var aiModelConfig = new ProjectManagement.Core.Entities.AIModelConfiguration
            {
                UserId = userId,
                ModelName = $"{request.Name}-{request.ModelName}",
                ApiEndpoint = request.ApiEndpoint,
                ApiKey = encryptedApiKey,
                ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                {
                    TimeoutSeconds = request.TimeoutSeconds ?? 60,
                    MaxRetries = request.MaxRetries ?? 3,
                    DisplayName = request.DisplayName,
                    Description = request.Description ?? "",
                    IsCustomProvider = true
                }),
                IsActive = request.Enabled,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 验证配置
            var (isValid, errors) = aiModelConfig.ValidateConfiguration();
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var createdConfig = await _aiConfigRepository.CreateAsync(aiModelConfig);

            // 创建用户AI配置
            var userAIConfig = new ProjectManagement.Core.Entities.UserAIConfiguration
            {
                UserId = userId,
                ProviderName = request.Name,
                ModelName = request.ModelName,
                ModelType = "RequirementAnalysis", // 使用约束允许的值
                ApiEndpoint = request.ApiEndpoint,
                ApiKey = encryptedApiKey, // 使用加密后的API密钥
                ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                {
                    TimeoutSeconds = request.TimeoutSeconds ?? 60,
                    MaxRetries = request.MaxRetries ?? 3,
                    DisplayName = request.DisplayName,
                    Description = request.Description ?? ""
                }),
                IsActive = request.Enabled,
                IsDefault = false,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 验证用户配置
            var (isUserConfigValid, userConfigErrors) = userAIConfig.ValidateConfiguration();
            if (!isUserConfigValid)
            {
                return BadRequest(new { message = "用户配置验证失败", errors = userConfigErrors });
            }

            await _userAIConfigRepository.CreateAsync(userAIConfig);

            // 返回创建的供应商信息
            var providerResponse = new
            {
                Name = request.Name,
                DisplayName = request.DisplayName,
                Description = request.Description ?? "",
                IsEnabled = request.Enabled,
                IsAvailable = request.Enabled,
                SupportedModels = new[] { request.ModelName },
                Config = new { },
                UsageStats = new
                {
                    TotalRequests = 0,
                    SuccessfulRequests = 0,
                    FailedRequests = 0,
                    AverageResponseTime = 0.0,
                    TotalTokens = 0L,
                    TotalCost = 0.0m,
                    LastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }
            };

            return Ok(providerResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建AI供应商失败: {ProviderName}", request.Name);
            return StatusCode(500, new { message = "创建供应商失败" });
        }
    }

    /// <summary>
    /// 获取AI配置
    /// </summary>
    [HttpGet("configuration")]
    public async Task<ActionResult<object>> GetConfiguration()
    {
        try
        {
            // 获取当前用户ID
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized(new { message = "无法获取用户ID" });
            }

            // 从用户任务映射表中获取真实的任务映射配置
            var userTaskMappings = await _userTaskMappingRepository.GetByUserIdAsync(userId);
            var taskMapping = new Dictionary<string, string>();

            // 构建任务映射字典，优先使用默认配置
            foreach (var mapping in userTaskMappings.Where(m => m.IsActive))
            {
                if (mapping.IsDefault || !taskMapping.ContainsKey(mapping.TaskType))
                {
                    taskMapping[mapping.TaskType] = mapping.ProviderName;
                }
            }

            // 如果没有用户配置，使用默认配置
            if (!taskMapping.Any())
            {
                taskMapping = new Dictionary<string, string>
                {
                    { "RequirementAnalysis", "Mock" },
                    { "CodeGeneration", "Mock" },
                    { "DocumentGeneration", "Mock" },
                    { "Embeddings", "Mock" }
                };
            }

            var configuration = new
            {
                DefaultProvider = "Mock",
                DefaultConfig = new
                {
                    Provider = "Mock",
                    Model = "mock-gpt-4",
                    MaxTokens = 4000,
                    Temperature = 0.7,
                    TopP = 1.0,
                    FrequencyPenalty = 0.0,
                    PresencePenalty = 0.0
                },
                TaskMapping = taskMapping,
                Providers = new Dictionary<string, object>
                {
                    {
                        "Mock", new
                        {
                            Name = "Mock",
                            Enabled = true
                        }
                    },
                    {
                        "Azure", new
                        {
                            Name = "Azure",
                            Endpoint = "",
                            ApiKey = "",
                            GPT4DeploymentName = "gpt-4",
                            GPT35DeploymentName = "gpt-35-turbo",
                            EmbeddingDeploymentName = "text-embedding-ada-002",
                            TimeoutSeconds = 300,
                            MaxRetries = 3,
                            Enabled = false
                        }
                    },
                    {
                        "OpenAI", new
                        {
                            Name = "OpenAI",
                            Endpoint = "https://api.openai.com/v1/",
                            ApiKey = "",
                            TimeoutSeconds = 300,
                            MaxRetries = 3,
                            Enabled = false
                        }
                    },
                    {
                        "DeepSeek", new
                        {
                            Name = "DeepSeek",
                            Endpoint = "https://api.deepseek.com/v1/",
                            ApiKey = "",
                            TimeoutSeconds = 300,
                            MaxRetries = 3,
                            Enabled = false
                        }
                    },
                    {
                        "Claude", new
                        {
                            Name = "Claude",
                            Endpoint = "https://api.anthropic.com/v1/",
                            ApiKey = "",
                            TimeoutSeconds = 300,
                            MaxRetries = 3,
                            Enabled = false
                        }
                    },
                    {
                        "Ollama", new
                        {
                            Name = "Ollama",
                            Endpoint = "http://localhost:11434/",
                            TimeoutSeconds = 300,
                            Enabled = false
                        }
                    }
                }
            };

            return Ok(configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI配置失败");
            return StatusCode(500, new { message = "获取配置失败" });
        }
    }

    /// <summary>
    /// 更新AI全局配置
    /// </summary>
    [HttpPut("configuration")]
    public async Task<ActionResult> UpdateConfiguration([FromBody] UpdateAIConfigurationRequest request)
    {
        try
        {
            _logger.LogInformation("更新AI全局配置");

            // 获取当前用户ID
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized(new { message = "无法获取用户ID" });
            }

            // 处理任务映射配置
            if (request.TaskMapping != null && request.TaskMapping.Any())
            {
                await UpdateUserTaskMappings(userId, request.TaskMapping);
            }

            // TODO: 处理其他配置（提供商配置、默认提供商等）
            // 这里可以保存到配置文件或数据库中

            _logger.LogInformation("AI全局配置已更新，用户ID: {UserId}", userId);
            return Ok(new { message = "全局配置已更新" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AI全局配置失败");
            return StatusCode(500, new { message = "更新全局配置失败" });
        }
    }

    /// <summary>
    /// 更新用户任务映射
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="taskMapping">任务映射配置</param>
    private async Task UpdateUserTaskMappings(int userId, Dictionary<string, string> taskMapping)
    {
        try
        {
            // 获取用户现有的任务映射
            var existingMappings = await _userTaskMappingRepository.GetByUserIdAsync(userId);
            var existingMappingDict = existingMappings.ToDictionary(m => m.TaskType, m => m);

            var mappingsToCreate = new List<ProjectManagement.Core.Entities.UserTaskMapping>();
            var mappingsToUpdate = new List<ProjectManagement.Core.Entities.UserTaskMapping>();

            foreach (var kvp in taskMapping)
            {
                var taskType = kvp.Key;
                var providerName = kvp.Value;

                if (existingMappingDict.TryGetValue(taskType, out var existingMapping))
                {
                    // 更新现有映射
                    if (existingMapping.ProviderName != providerName)
                    {
                        existingMapping.ProviderName = providerName;
                        existingMapping.UpdatedAt = DateTime.UtcNow;
                        mappingsToUpdate.Add(existingMapping);
                    }
                }
                else
                {
                    // 创建新映射
                    var newMapping = new ProjectManagement.Core.Entities.UserTaskMapping
                    {
                        UserId = userId,
                        TaskType = taskType,
                        ProviderName = providerName,
                        IsActive = true,
                        IsDefault = true, // 设置为默认配置
                        Priority = 1,
                        Description = $"通过AI配置页面创建的{taskType}任务映射"
                    };

                    // 暂时跳过验证，直接添加到创建列表
                    // 验证将在批量操作之前进行
                    mappingsToCreate.Add(newMapping);
                }
            }

            // 批量创建新映射
            if (mappingsToCreate.Any())
            {
                // 过滤掉无效的映射
                var validMappings = new List<ProjectManagement.Core.Entities.UserTaskMapping>();
                foreach (var mapping in mappingsToCreate)
                {
                    // 简单验证，避免数据库查询
                    var (isValid, errors) = mapping.ValidateConfiguration();
                    if (isValid)
                    {
                        validMappings.Add(mapping);
                    }
                    else
                    {
                        _logger.LogWarning("任务映射验证失败: {TaskType} -> {ProviderName}, 错误: {Errors}",
                            mapping.TaskType, mapping.ProviderName, string.Join(", ", errors));
                    }
                }

                if (validMappings.Any())
                {
                    await _userTaskMappingRepository.CreateBatchAsync(validMappings);
                    _logger.LogInformation("创建了 {Count} 个新的任务映射", validMappings.Count);
                }
            }

            // 批量更新现有映射
            if (mappingsToUpdate.Any())
            {
                await _userTaskMappingRepository.UpdateBatchAsync(mappingsToUpdate);
                _logger.LogInformation("更新了 {Count} 个现有的任务映射", mappingsToUpdate.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户任务映射失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 测试AI提供商连接
    /// </summary>
    [HttpPost("providers/{providerName}/test")]
    public async Task<ActionResult<object>> TestProvider(string providerName)
    {
        try
        {
            _logger.LogInformation("测试AI提供商连接: {ProviderName}", providerName);

            // TODO: 实现真实的提供商测试逻辑
            await Task.Delay(1000); // 模拟测试时间

            var result = new
            {
                Success = true,
                Message = $"{providerName} 连接测试成功",
                ResponseTime = Random.Shared.Next(500, 2000)
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试AI提供商连接失败: {ProviderName}", providerName);
            return Ok(new
            {
                Success = false,
                Message = $"连接测试失败: {ex.Message}",
                Error = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取特定提供商配置
    /// </summary>
    [HttpGet("providers/{providerName}/config")]
    public async Task<ActionResult<object>> GetProviderConfig(string providerName)
    {
        try
        {
            _logger.LogInformation("获取AI提供商配置: {ProviderName}", providerName);

            var userId = GetCurrentUserId();

            // 优先级1：从用户配置中获取
            var userConfigs = await _userAIConfigRepository.GetByUserIdAndProviderAsync(userId, providerName);
            var userConfig = userConfigs.FirstOrDefault();

            if (userConfig != null)
            {
                // 解析用户配置的参数
                var parameters = new Dictionary<string, object>();
                if (!string.IsNullOrEmpty(userConfig.ModelParameters))
                {
                    try
                    {
                        parameters = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(userConfig.ModelParameters) ?? new Dictionary<string, object>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "解析用户配置参数失败: {ProviderName}", providerName);
                    }
                }

                var config = new
                {
                    Name = providerName,
                    Endpoint = userConfig.ApiEndpoint ?? "",
                    ApiKey = MaskApiKey(userConfig.ApiKey ?? ""), // 隐藏API密钥
                    TimeoutSeconds = GetParameterValue<int>(parameters, "TimeoutSeconds", 60),
                    MaxRetries = GetParameterValue<int>(parameters, "MaxRetries", 3),
                    Enabled = userConfig.IsActive,
                    // Azure 特定参数
                    GPT4DeploymentName = GetParameterValue<string>(parameters, "GPT4DeploymentName", "gpt-4"),
                    GPT35DeploymentName = GetParameterValue<string>(parameters, "GPT35DeploymentName", "gpt-35-turbo"),
                    EmbeddingDeploymentName = GetParameterValue<string>(parameters, "EmbeddingDeploymentName", "text-embedding-ada-002")
                };

                _logger.LogInformation("返回用户配置: {ProviderName}, UserId: {UserId}", providerName, userId);
                return Ok(config);
            }

            // 优先级2：从当前用户的AI模型配置中获取
            var aiModelConfigs = await _aiConfigRepository.GetByUserIdAsync(userId);
            var aiModelConfig = aiModelConfigs.FirstOrDefault(x => AIConfigurationHelper.GetProviderFromModelName(x.ModelName) == providerName);

            if (aiModelConfig != null)
            {
                var parameters = new Dictionary<string, object>();
                if (!string.IsNullOrEmpty(aiModelConfig.ModelParameters))
                {
                    try
                    {
                        parameters = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(aiModelConfig.ModelParameters) ?? new Dictionary<string, object>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "解析AI模型配置参数失败: {ProviderName}", providerName);
                    }
                }

                var config = new
                {
                    Name = providerName,
                    Endpoint = aiModelConfig.ApiEndpoint ?? "",
                    ApiKey = MaskApiKey(aiModelConfig.ApiKey ?? ""), // 隐藏API密钥
                    TimeoutSeconds = GetParameterValue<int>(parameters, "TimeoutSeconds", 60),
                    MaxRetries = GetParameterValue<int>(parameters, "MaxRetries", 3),
                    Enabled = aiModelConfig.IsActive,
                    // Azure 特定参数
                    GPT4DeploymentName = GetParameterValue<string>(parameters, "GPT4DeploymentName", "gpt-4"),
                    GPT35DeploymentName = GetParameterValue<string>(parameters, "GPT35DeploymentName", "gpt-35-turbo"),
                    EmbeddingDeploymentName = GetParameterValue<string>(parameters, "EmbeddingDeploymentName", "text-embedding-ada-002")
                };

                _logger.LogInformation("返回AI模型配置: {ProviderName}, UserId: {UserId}", providerName, userId);
                return Ok(config);
            }

            // 优先级3：返回默认配置
            var defaultConfig = GetDefaultProviderConfig(providerName);
            _logger.LogInformation("返回默认配置: {ProviderName}", providerName);
            return Ok(defaultConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商配置失败: {ProviderName}", providerName);
            return StatusCode(500, new { message = "获取提供商配置失败" });
        }
    }

    /// <summary>
    /// 更新特定提供商配置
    /// </summary>
    [HttpPut("providers/{providerName}/config")]
    public async Task<ActionResult> UpdateProviderConfig(string providerName, [FromBody] UpdateProviderConfigRequest request)
    {
        try
        {
            _logger.LogInformation("更新AI提供商配置: {ProviderName}", providerName);

            var userId = GetCurrentUserId();

            // 1. 更新或创建用户级配置 (AIModelConfigurations表)
            var existingConfigs = await _aiConfigRepository.GetByUserIdAsync(userId);
            var existingConfig = existingConfigs.FirstOrDefault(x =>
                AIConfigurationHelper.GetProviderFromModelName(x.ModelName) == providerName);

            if (existingConfig != null)
            {
                // 更新现有配置
                existingConfig.ApiEndpoint = request.Endpoint ?? existingConfig.ApiEndpoint;
                existingConfig.ApiKey = request.ApiKey ?? existingConfig.ApiKey;
                existingConfig.ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                {
                    TimeoutSeconds = request.TimeoutSeconds ?? 60,
                    MaxRetries = request.MaxRetries ?? 3,
                    Enabled = request.Enabled ?? false,
                    GPT4DeploymentName = request.GPT4DeploymentName,
                    GPT35DeploymentName = request.GPT35DeploymentName,
                    EmbeddingDeploymentName = request.EmbeddingDeploymentName
                });
                existingConfig.IsActive = request.Enabled ?? false;
                existingConfig.UpdatedAt = DateTime.Now;

                var updateSuccess = await _aiConfigRepository.UpdateAsync(existingConfig);
                if (!updateSuccess)
                {
                    return StatusCode(500, new { message = "更新系统配置失败" });
                }
            }
            else
            {
                // 创建新的用户配置
                var newConfig = new ProjectManagement.Core.Entities.AIModelConfiguration
                {
                    UserId = userId, // 用户级配置
                    ModelName = GetDefaultModelName(providerName),
                    ApiEndpoint = request.Endpoint,
                    ApiKey = request.ApiKey,
                    ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        TimeoutSeconds = request.TimeoutSeconds ?? 300,
                        MaxRetries = request.MaxRetries ?? 3,
                        Enabled = request.Enabled ?? false,
                        GPT4DeploymentName = request.GPT4DeploymentName,
                        GPT35DeploymentName = request.GPT35DeploymentName,
                        EmbeddingDeploymentName = request.EmbeddingDeploymentName
                    }),
                    IsActive = request.Enabled ?? false,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                await _aiConfigRepository.CreateAsync(newConfig);
            }

            // 2. 更新或创建用户级配置 (UserAIConfigurations表)
            var existingUserConfigs = await _userAIConfigRepository.GetByUserIdAndProviderAsync(userId, providerName);
            var existingUserConfig = existingUserConfigs.FirstOrDefault();

            if (existingUserConfig != null)
            {
                // 更新现有用户配置
                existingUserConfig.ApiEndpoint = request.Endpoint ?? existingUserConfig.ApiEndpoint;
                existingUserConfig.ApiKey = request.ApiKey ?? existingUserConfig.ApiKey;
                existingUserConfig.ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                {
                    TimeoutSeconds = request.TimeoutSeconds ?? 60,
                    MaxRetries = request.MaxRetries ?? 3,
                    GPT4DeploymentName = request.GPT4DeploymentName,
                    GPT35DeploymentName = request.GPT35DeploymentName,
                    EmbeddingDeploymentName = request.EmbeddingDeploymentName
                });
                existingUserConfig.IsActive = request.Enabled ?? false;
                existingUserConfig.UpdatedAt = DateTime.Now;

                var userUpdateSuccess = await _userAIConfigRepository.UpdateAsync(existingUserConfig);
                if (!userUpdateSuccess)
                {
                    return StatusCode(500, new { message = "更新用户配置失败" });
                }
            }
            else
            {
                // 创建新的用户配置
                var newUserConfig = new ProjectManagement.Core.Entities.UserAIConfiguration
                {
                    UserId = userId,
                    ProviderName = providerName,
                    ModelName = GetDefaultModelName(providerName),
                    ApiEndpoint = request.Endpoint,
                    ApiKey = request.ApiKey,
                    ModelParameters = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        TimeoutSeconds = request.TimeoutSeconds ?? 300,
                        MaxRetries = request.MaxRetries ?? 3,
                        GPT4DeploymentName = request.GPT4DeploymentName,
                        GPT35DeploymentName = request.GPT35DeploymentName,
                        EmbeddingDeploymentName = request.EmbeddingDeploymentName
                    }),
                    IsActive = request.Enabled ?? false,
                    IsDefault = false, // 默认不设为默认配置
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                await _userAIConfigRepository.CreateAsync(newUserConfig);
            }

            _logger.LogInformation("AI提供商配置已保存到系统和用户配置: {ProviderName}, UserId: {UserId}", providerName, userId);
            return Ok(new { message = $"{providerName} 配置已更新" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AI提供商配置失败: {ProviderName}", providerName);
            return StatusCode(500, new { message = "更新提供商配置失败" });
        }
    }

    /// <summary>
    /// 启用/禁用提供商
    /// </summary>
    [HttpPost("providers/{providerName}/toggle")]
    public async Task<ActionResult> ToggleProvider(string providerName, [FromBody] ToggleProviderRequest request)
    {
        try
        {
            _logger.LogInformation("切换AI提供商状态: {ProviderName}, Enabled: {Enabled}", providerName, request.Enabled);

            // TODO: 实现提供商状态切换逻辑
            await Task.Delay(100); // 模拟操作时间

            return Ok(new { message = $"{providerName} 已{(request.Enabled ? "启用" : "禁用")}" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换AI提供商状态失败: {ProviderName}", providerName);
            return StatusCode(500, new { message = "切换提供商状态失败" });
        }
    }

    /// <summary>
    /// 获取AI模型配置列表
    /// </summary>
    [HttpGet("model-configurations")]
    public async Task<ActionResult<object>> GetModelConfigurations()
    {
        try
        {
            var userId = GetCurrentUserId();
            var configurations = await _aiConfigRepository.GetByUserIdAsync(userId);
            var result = configurations.Select(config => new
            {
                config.Id,
                config.UserId,
                config.ModelName,
                config.ApiEndpoint,
                ApiKey = string.IsNullOrEmpty(config.ApiKey) ? "" : "***",
                config.ModelParameters,
                config.IsActive,
                config.CreatedAt,
                config.UpdatedAt
            });

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI模型配置失败");
            return StatusCode(500, new { message = "获取模型配置失败" });
        }
    }

    /// <summary>
    /// 创建AI模型配置
    /// </summary>
    [HttpPost("model-configurations")]
    public async Task<ActionResult<object>> CreateModelConfiguration([FromBody] CreateAIModelConfigRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = new ProjectManagement.Core.Entities.AIModelConfiguration
            {
                UserId = userId, // 用户级配置
                ModelName = request.ModelName,
                ApiEndpoint = request.ApiEndpoint,
                ApiKey = request.ApiKey,
                ModelParameters = request.ModelParameters,
                IsActive = request.IsActive
            };

            // 验证配置
            var (isValid, errors) = configuration.ValidateConfiguration();
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var result = await _aiConfigRepository.CreateAsync(configuration);
            return Ok(result.ToProviderConfig());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建AI模型配置失败");
            return StatusCode(500, new { message = "创建配置失败" });
        }
    }

    /// <summary>
    /// 更新AI模型配置
    /// </summary>
    [HttpPut("model-configurations/{id}")]
    public async Task<ActionResult<object>> UpdateModelConfiguration(int id, [FromBody] UpdateAIModelConfigRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _aiConfigRepository.GetByIdAsync(id);
            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 检查用户权限
            if (configuration.UserId != userId)
            {
                return Forbid("无权修改此配置");
            }

            // 更新配置
            configuration.ModelName = request.ModelName ?? configuration.ModelName;
            configuration.ApiEndpoint = request.ApiEndpoint ?? configuration.ApiEndpoint;
            configuration.ApiKey = request.ApiKey ?? configuration.ApiKey;
            configuration.ModelParameters = request.ModelParameters ?? configuration.ModelParameters;
            configuration.IsActive = request.IsActive ?? configuration.IsActive;

            // 验证配置
            var (isValid, errors) = configuration.ValidateConfiguration();
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var success = await _aiConfigRepository.UpdateAsync(configuration);
            if (!success)
            {
                return StatusCode(500, new { message = "更新配置失败" });
            }

            return Ok(configuration.ToProviderConfig());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AI模型配置失败");
            return StatusCode(500, new { message = "更新配置失败" });
        }
    }

    /// <summary>
    /// 删除AI模型配置
    /// </summary>
    [HttpDelete("model-configurations/{id}")]
    public async Task<ActionResult> DeleteModelConfiguration(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var configuration = await _aiConfigRepository.GetByIdAsync(id);
            if (configuration == null)
            {
                return NotFound(new { message = "配置不存在" });
            }

            // 检查用户权限
            if (configuration.UserId != userId)
            {
                return Forbid("无权删除此配置");
            }

            var success = await _aiConfigRepository.DeleteAsync(id);
            if (!success)
            {
                return NotFound(new { message = "配置不存在" });
            }

            return Ok(new { message = "配置已删除" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除AI模型配置失败");
            return StatusCode(500, new { message = "删除配置失败" });
        }
    }

    /// <summary>
    /// 启用/禁用AI模型配置
    /// </summary>
    [HttpPost("model-configurations/{id}/toggle")]
    public async Task<ActionResult> ToggleModelConfiguration(int id, [FromBody] ToggleConfigRequest request)
    {
        try
        {
            var success = await _aiConfigRepository.ToggleActiveAsync(id, request.IsActive);
            if (!success)
            {
                return NotFound(new { message = "配置不存在" });
            }

            return Ok(new { message = request.IsActive ? "配置已启用" : "配置已禁用" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换AI模型配置状态失败");
            return StatusCode(500, new { message = "操作失败" });
        }
    }

    /// <summary>
    /// 获取提供商使用统计
    /// </summary>
    [HttpGet("providers/{providerName}/stats")]
    public async Task<ActionResult<object>> GetProviderStats(string providerName)
    {
        try
        {
            _logger.LogInformation("获取AI提供商使用统计: {ProviderName}", providerName);

            // TODO: 从实际统计数据获取
            var stats = new
            {
                TotalRequests = Random.Shared.Next(50, 500),
                SuccessfulRequests = Random.Shared.Next(45, 480),
                FailedRequests = Random.Shared.Next(0, 20),
                AverageResponseTime = Random.Shared.NextDouble() * 2000 + 500,
                TotalTokens = Random.Shared.NextInt64(1000, 50000),
                TotalCost = Random.Shared.NextDouble() * 10,
                LastUpdated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商使用统计失败: {ProviderName}", providerName);
            return StatusCode(500, new { message = "获取使用统计失败" });
        }
    }

    /// <summary>
    /// 获取AI使用总统计
    /// </summary>
    [HttpGet("total-usage-statistics")]
    public async Task<ActionResult<object>> GetTotalUsageStats()
    {
        try
        {
            _logger.LogInformation("获取AI使用总统计");

            // TODO: 从实际统计数据获取
            var totalStats = new
            {
                TotalRequests = Random.Shared.Next(200, 2000),
                SuccessfulRequests = Random.Shared.Next(180, 1900),
                FailedRequests = Random.Shared.Next(10, 100),
                TotalTokensUsed = Random.Shared.NextInt64(10000, 200000),
                EstimatedCost = Random.Shared.NextDouble() * 50,
                LastUsed = DateTime.Now.AddMinutes(-Random.Shared.Next(1, 60)).ToString("yyyy-MM-dd HH:mm:ss"),
                ProviderBreakdown = new Dictionary<string, object>
                {
                    {
                        "Mock", new
                        {
                            Requests = Random.Shared.Next(50, 200),
                            Tokens = Random.Shared.NextInt64(5000, 20000),
                            Cost = Random.Shared.NextDouble() * 10
                        }
                    },
                    {
                        "Azure", new
                        {
                            Requests = Random.Shared.Next(30, 150),
                            Tokens = Random.Shared.NextInt64(3000, 15000),
                            Cost = Random.Shared.NextDouble() * 15
                        }
                    },
                    {
                        "DeepSeek", new
                        {
                            Requests = Random.Shared.Next(20, 100),
                            Tokens = Random.Shared.NextInt64(2000, 10000),
                            Cost = Random.Shared.NextDouble() * 5
                        }
                    }
                }
            };

            return Ok(totalStats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI使用总统计失败");
            return StatusCode(500, new { message = "获取使用统计失败" });
        }
    }

    /// <summary>
    /// 获取提供商健康状态
    /// </summary>
    [HttpGet("providers/health")]
    public async Task<ActionResult<object>> GetProviderHealth()
    {
        try
        {
            _logger.LogInformation("获取AI提供商健康状态");

            // TODO: 实现真实的健康检查
            var healthStatus = new Dictionary<string, object>
            {
                {
                    "Mock", new
                    {
                        IsHealthy = true,
                        LastCheck = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        ResponseTime = Random.Shared.Next(100, 500)
                    }
                },
                {
                    "Azure", new
                    {
                        IsHealthy = Random.Shared.Next(0, 2) == 1,
                        LastCheck = DateTime.Now.AddMinutes(-Random.Shared.Next(1, 30)).ToString("yyyy-MM-dd HH:mm:ss"),
                        ResponseTime = Random.Shared.Next(500, 2000)
                    }
                },
                {
                    "DeepSeek", new
                    {
                        IsHealthy = Random.Shared.Next(0, 2) == 1,
                        LastCheck = DateTime.Now.AddMinutes(-Random.Shared.Next(1, 30)).ToString("yyyy-MM-dd HH:mm:ss"),
                        ResponseTime = Random.Shared.Next(800, 3000)
                    }
                }
            };

            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商健康状态失败");
            return StatusCode(500, new { message = "获取健康状态失败" });
        }
    }

    /// <summary>
    /// 刷新提供商健康状态
    /// </summary>
    [HttpPost("providers/health/refresh")]
    public async Task<ActionResult> RefreshProviderHealth()
    {
        try
        {
            _logger.LogInformation("刷新AI提供商健康状态");

            // TODO: 实现健康状态刷新逻辑
            await Task.Delay(2000); // 模拟健康检查时间

            return Ok(new { message = "健康状态已刷新" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新AI提供商健康状态失败");
            return StatusCode(500, new { message = "刷新健康状态失败" });
        }
    }

    /// <summary>
    /// 为SQL脚本生成中文注释
    /// </summary>
    /// <param name="request">SQL注释生成请求</param>
    /// <returns>带中文注释的SQL脚本</returns>
    [HttpPost("generate-sql-comments")]
    public async Task<ActionResult<string>> GenerateSQLComments([FromBody] SQLCommentGenerationRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求为SQL脚本生成中文注释", userId);

            if (string.IsNullOrWhiteSpace(request.SqlScript))
            {
                return BadRequest(new { message = "SQL脚本不能为空" });
            }

            // 使用Prompt工程构建SQL注释生成提示词
            string prompt;
            var parameters = new Dictionary<string, object>
            {
                ["sqlScript"] = request.SqlScript,
                ["projectId"] = request.ProjectId ?? 0,
                ["databaseType"] = request.DatabaseType ?? "SqlServer",
                ["businessDomain"] = request.BusinessDomain ?? "项目管理系统"
            };

            prompt = await _promptBuilderService.BuildPromptByTaskTypeAsync("SQLCommentGeneration", parameters, userId, request.ProjectId);
            _logger.LogInformation("使用Prompt模板构建SQL注释生成提示词成功");
            // 获取用户的AI配置
            string enhancedSql;
            string actualProvider = "Default";
            string actualModel = "sql-comment-generation";

            var userMappings = await _userTaskMappingRepository.GetByUserIdAndTaskTypeAsync(userId, "DocumentGeneration");
            var userMapping = userMappings?.FirstOrDefault(x => x.IsActive);

            if (userMapping != null)
            {
                // 根据ProviderName获取用户的AI配置
                var userAIConfigs = await _userAIConfigRepository.GetByUserIdAndProviderAsync(userId, userMapping.ProviderName);
                var userAIConfig = userAIConfigs.FirstOrDefault(x => x.IsActive && x.ModelType == "DocumentGeneration")
                                  ?? userAIConfigs.FirstOrDefault(x => x.IsActive);

                if (userAIConfig != null)
                {
                    // 记录实际使用的AI提供商和模型
                    actualProvider = userAIConfig.ProviderName;
                    actualModel = userAIConfig.ModelName;

                    // 解析用户AI配置的模型参数
                    var maxTokens = 4000;
                    var temperature = 0.3f; // 注释生成使用较低的温度以确保准确性

                    if (!string.IsNullOrEmpty(userAIConfig.ModelParameters))
                    {
                        try
                        {
                            var modelParams = JsonSerializer.Deserialize<Dictionary<string, object>>(userAIConfig.ModelParameters);
                            if (modelParams != null)
                            {
                                if (modelParams.TryGetValue("MaxTokens", out var maxTokensValue))
                                    int.TryParse(maxTokensValue.ToString(), out maxTokens);

                                if (modelParams.TryGetValue("Temperature", out var temperatureValue))
                                    float.TryParse(temperatureValue.ToString(), out temperature);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "解析用户AI配置参数失败，使用默认值");
                        }
                    }

                    // 使用用户的AI配置
                    var config = new AIModelConfig
                    {
                        Provider = userAIConfig.ProviderName,
                        Model = userAIConfig.ModelName,
                        ApiKey = userAIConfig.ApiKey ?? string.Empty,
                        Endpoint = userAIConfig.ApiEndpoint ?? string.Empty,
                        MaxTokens = maxTokens,
                        Temperature = temperature
                    };

                    enhancedSql = await _aiService.GenerateTextAsync(prompt, config);

                    // 更新使用统计
                    await _userAIConfigRepository.UpdateUsageStatisticsAsync(userAIConfig.Id, maxTokens, true);
                }
                else
                {
                    _logger.LogWarning("用户没有配置AI提供商 {ProviderName}，使用默认配置", userMapping.ProviderName);
                    enhancedSql = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
                }
            }
            else
            {
                // 使用默认配置
                enhancedSql = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
            }

            // 清理AI响应，确保返回纯SQL脚本
            var cleanedSql = CleanSQLResponse(enhancedSql);

            // 记录模板使用情况
            if (_promptTemplateService != null)
            {
                try
                {
                    var defaultTemplate = await _promptTemplateService.GetDefaultTemplateAsync("SQLCommentGeneration");
                    if (defaultTemplate != null)
                    {
                        await _promptTemplateService.RecordTemplateUsageAsync(
                            defaultTemplate.Id, userId, request.ProjectId,
                            actualProvider, actualModel,
                            JsonSerializer.Serialize(new
                            {
                                sqlScriptLength = request.SqlScript.Length,
                                databaseType = request.DatabaseType,
                                businessDomain = request.BusinessDomain
                            }),
                            prompt, enhancedSql, null, null, null, true, null);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "记录SQL注释生成模板使用失败");
                }
            }

            _logger.LogInformation("SQL注释生成完成，原始长度: {OriginalLength}, 增强后长度: {EnhancedLength}",
                request.SqlScript.Length, cleanedSql.Length);

            return Ok(cleanedSql);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成SQL注释时发生错误");
            return StatusCode(500, new { message = "生成SQL注释失败", error = ex.Message });
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }

    /// <summary>
    /// 清理SQL响应，移除多余的文本和格式
    /// </summary>
    /// <param name="sqlResponse">AI返回的SQL响应</param>
    /// <returns>清理后的SQL脚本</returns>
    private static string CleanSQLResponse(string sqlResponse)
    {
        if (string.IsNullOrWhiteSpace(sqlResponse))
            return string.Empty;

        // 移除markdown代码块标记
        var cleaned = sqlResponse.Replace("```sql", "").Replace("```", "");

        // 移除常见的AI回复前缀
        var prefixesToRemove = new[]
        {
            "以下是添加了中文注释的SQL脚本：",
            "这是添加了中文注释的SQL脚本：",
            "SQL脚本如下：",
            "修改后的SQL脚本：",
            "带注释的SQL脚本："
        };

        foreach (var prefix in prefixesToRemove)
        {
            if (cleaned.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            {
                cleaned = cleaned.Substring(prefix.Length);
                break;
            }
        }

        // 移除开头和结尾的空白字符
        cleaned = cleaned.Trim();

        // 确保SQL脚本以适当的格式返回
        return cleaned;
    }

    /// <summary>
    /// 隐藏API密钥，只显示前几位和后几位
    /// </summary>
    private string MaskApiKey(string apiKey)
    {
        if (string.IsNullOrEmpty(apiKey))
            return "";

        if (apiKey.Length <= 8)
            return "***";

        return $"{apiKey.Substring(0, 3)}***{apiKey.Substring(apiKey.Length - 3)}";
    }

    /// <summary>
    /// 从参数字典中获取指定类型的值
    /// </summary>
    private T GetParameterValue<T>(Dictionary<string, object> parameters, string key, T defaultValue)
    {
        if (parameters == null || !parameters.ContainsKey(key))
            return defaultValue;

        try
        {
            var value = parameters[key];
            if (value is JsonElement jsonElement)
            {
                // 处理JsonElement类型
                if (typeof(T) == typeof(string))
                    return (T)(object)jsonElement.GetString();
                if (typeof(T) == typeof(int))
                    return (T)(object)jsonElement.GetInt32();
                if (typeof(T) == typeof(bool))
                    return (T)(object)jsonElement.GetBoolean();
            }

            // 直接转换
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取参数值失败: {Key}, 使用默认值: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
    }



    /// <summary>
    /// 获取提供商显示名称
    /// </summary>
    private string GetProviderDisplayName(string providerName)
    {
        return providerName switch
        {
            "OpenAI" => "OpenAI",
            "Azure" => "Azure OpenAI",
            "Claude" => "Claude",
            "DeepSeek" => "DeepSeek",
            "Ollama" => "Ollama",
            "Mock" => "模拟AI",
            _ => providerName
        };
    }

    /// <summary>
    /// 获取提供商描述
    /// </summary>
    private string GetProviderDescription(string providerName)
    {
        return providerName switch
        {
            "OpenAI" => "OpenAI官方API服务",
            "Azure" => "微软Azure OpenAI服务",
            "Claude" => "Anthropic Claude AI服务",
            "DeepSeek" => "DeepSeek AI服务",
            "Ollama" => "本地Ollama AI服务",
            "Mock" => "用于开发和测试的模拟AI服务",
            _ => $"{providerName} AI服务"
        };
    }

    /// <summary>
    /// 获取默认提供商配置
    /// </summary>
    private object GetDefaultProviderConfig(string providerName)
    {
        return providerName switch
        {
            "Mock" => new
            {
                Name = "Mock",
                Enabled = true
            },
            "Azure" => new
            {
                Name = "Azure",
                Endpoint = "",
                ApiKey = "",
                GPT4DeploymentName = "gpt-4",
                GPT35DeploymentName = "gpt-35-turbo",
                EmbeddingDeploymentName = "text-embedding-ada-002",
                TimeoutSeconds = 300,
                MaxRetries = 3,
                Enabled = false
            },
            "OpenAI" => new
            {
                Name = "OpenAI",
                Endpoint = "https://api.openai.com/v1/",
                ApiKey = "",
                TimeoutSeconds = 300,
                MaxRetries = 3,
                Enabled = false
            },
            "DeepSeek" => new
            {
                Name = "DeepSeek",
                Endpoint = "https://api.deepseek.com/v1/",
                ApiKey = "",
                TimeoutSeconds = 300,
                MaxRetries = 3,
                Enabled = false
            },
            "Claude" => new
            {
                Name = "Claude",
                Endpoint = "https://api.anthropic.com/v1/",
                ApiKey = "",
                TimeoutSeconds = 300,
                MaxRetries = 3,
                Enabled = false
            },
            "Ollama" => new
            {
                Name = "Ollama",
                Endpoint = "http://localhost:11434/",
                TimeoutSeconds = 300,
                Enabled = false
            },
            _ => new
            {
                Name = providerName,
                Enabled = false
            }
        };
    }

    /// <summary>
    /// 获取提供商的默认模型名称
    /// </summary>
    private string GetDefaultModelName(string providerName)
    {
        return providerName switch
        {
            "OpenAI" => "gpt-4",
            "Azure" => "gpt-4",
            "Claude" => "claude-3-opus",
            "DeepSeek" => "deepseek-coder",
            "Ollama" => "llama2",
            "Mock" => "mock-gpt-4",
            _ => $"{providerName.ToLower()}-default"
        };
    }


    /// <summary>
    /// 验证和修复Mermaid语法
    /// </summary>
    private string ValidateMermaidSyntax(string mermaidCode, string expectedType)
    {
        try
        {
            // 基本语法验证
            if (string.IsNullOrWhiteSpace(mermaidCode))
            {
                _logger.LogWarning("生成的Mermaid代码为空，使用默认模板");
                return GetDefaultMermaidDiagram(expectedType);
            }

            // 对ER图进行预处理修复
            if (expectedType == "erDiagram")
            {
                mermaidCode = FixERDiagramSyntax(mermaidCode);
                _logger.LogInformation("已对ER图进行语法预处理修复");
            }

            var lines = mermaidCode.Split('\n').ToList();
            var validatedLines = new List<string>();
            var hasValidStart = false;
            var entityCount = 0;
            var relationshipCount = 0;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // 跳过空行
                if (string.IsNullOrEmpty(trimmedLine))
                {
                    validatedLines.Add(line);
                    continue;
                }

                // 保留注释
                if (trimmedLine.StartsWith("%%"))
                {
                    validatedLines.Add(line);
                    continue;
                }

                // 检查图表类型声明
                if (expectedType == "erDiagram" && trimmedLine.StartsWith("erDiagram"))
                {
                    hasValidStart = true;
                    validatedLines.Add("erDiagram");
                    continue;
                }
                else if ((expectedType == "flowchart" || expectedType == "graph") &&
                         (trimmedLine.StartsWith("flowchart") || trimmedLine.StartsWith("graph")))
                {
                    hasValidStart = true;
                    validatedLines.Add(trimmedLine);
                    continue;
                }

                // 验证ER图实体定义
                if (expectedType == "erDiagram" && hasValidStart)
                {
                    // 检查实体定义 (EntityName { ... })
                    if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^\w+\s*\{"))
                    {
                        entityCount++;
                        validatedLines.Add(line);
                        continue;
                    }

                    // 检查字段定义
                    if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^\s*\w+\s+\w+"))
                    {
                        // 修复常见的语法问题
                        var fixedLine = FixFieldDefinition(trimmedLine);
                        validatedLines.Add(fixedLine);
                        continue;
                    }

                    // 检查关系定义
                    if (trimmedLine.Contains("||--") || trimmedLine.Contains("}o--") || trimmedLine.Contains("||.."))
                    {
                        relationshipCount++;
                        validatedLines.Add(line);
                        continue;
                    }

                    // 检查实体结束标记
                    if (trimmedLine == "}")
                    {
                        validatedLines.Add(line);
                        continue;
                    }
                }

                // 验证流程图节点和连接
                if ((expectedType == "flowchart" || expectedType == "graph") && hasValidStart)
                {
                    // 跳过有问题的class定义
                    if (trimmedLine.StartsWith("class ") && trimmedLine.Contains(";"))
                    {
                        _logger.LogWarning("跳过有问题的class定义: {Line}", trimmedLine);
                        continue;
                    }

                    // 检查节点定义和连接
                    if (trimmedLine.Contains("-->") || trimmedLine.Contains("---") ||
                        trimmedLine.Contains("[") || trimmedLine.Contains("(") ||
                        trimmedLine.Contains("{") || trimmedLine.Contains("}") ||
                        System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^\s*\w+\s*\["))
                    {
                        validatedLines.Add(line);
                        continue;
                    }
                }

                // 如果到这里，可能是无效行，但仍然保留（可能是有效的Mermaid语法我们没有覆盖到）
                validatedLines.Add(line);
            }

            if (!hasValidStart)
            {
                _logger.LogWarning("Mermaid代码缺少有效的开始标记，期望类型: {ExpectedType}", expectedType);
                return GetDefaultMermaidDiagram(expectedType);
            }

            // 检查内容完整性
            if (expectedType == "erDiagram" && entityCount == 0)
            {
                _logger.LogWarning("ER图中没有找到实体定义，使用默认模板");
                return GetDefaultMermaidDiagram(expectedType);
            }

            var result = string.Join("\n", validatedLines);

            _logger.LogInformation("Mermaid语法验证通过，类型: {Type}, 实体数: {EntityCount}, 关系数: {RelationshipCount}",
                expectedType, entityCount, relationshipCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证Mermaid语法时发生错误");
            return GetDefaultMermaidDiagram(expectedType);
        }
    }

    /// <summary>
    /// 修复字段定义中的常见问题
    /// </summary>
    private string FixFieldDefinition(string fieldLine)
    {
        try
        {
            var trimmed = fieldLine.Trim();

            // 修复复杂数据类型定义（如decimal(9,6)、varchar(255)等）
            // Mermaid ER图不支持括号内的参数，需要简化
            trimmed = System.Text.RegularExpressions.Regex.Replace(trimmed,
                @"\b(decimal|numeric|varchar|char|nvarchar|nchar)\s*\([^)]+\)",
                "$1",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 修复DEFAULT值中的单引号问题
            trimmed = System.Text.RegularExpressions.Regex.Replace(trimmed,
                @"DEFAULT\s+'([^']+)'",
                "DEFAULT $1",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 移除复杂的约束定义，只保留基本的PK、FK标记
            // 移除引号包围的约束描述
            trimmed = System.Text.RegularExpressions.Regex.Replace(trimmed,
                @"\s*""[^""]*""",
                "",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 移除NOT NULL、UNIQUE等约束（Mermaid ER图中这些会导致解析错误）
            trimmed = System.Text.RegularExpressions.Regex.Replace(trimmed,
                @"\s+(NOT\s+NULL|UNIQUE|DEFAULT\s+\w+)",
                "",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 确保字段定义格式正确：datatype fieldname [PK|FK]
            var match = System.Text.RegularExpressions.Regex.Match(trimmed, @"^\s*(\w+)\s+(\w+)\s*(PK|FK)?\s*$");
            if (match.Success)
            {
                var dataType = match.Groups[1].Value;
                var fieldName = match.Groups[2].Value;
                var modifier = match.Groups[3].Value;

                if (!string.IsNullOrEmpty(modifier))
                {
                    trimmed = $"{dataType} {fieldName} {modifier}";
                }
                else
                {
                    trimmed = $"{dataType} {fieldName}";
                }
            }

            return $"        {trimmed}"; // 保持适当的缩进
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "修复字段定义时发生错误: {FieldLine}", fieldLine);
            // 如果修复失败，返回简化的字段定义
            var parts = fieldLine.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
            {
                var dataType = parts[0].Contains('(') ? parts[0].Split('(')[0] : parts[0];
                var fieldName = parts[1];
                return $"        {dataType} {fieldName}";
            }
            return fieldLine; // 返回原始行
        }
    }

    /// <summary>
    /// 获取默认Mermaid图表
    /// </summary>
    private string GetDefaultMermaidDiagram(string diagramType)
    {
        if (diagramType == "erDiagram")
        {
            return GetDefaultERDiagramTemplate();
        }
        else
        {
            return GetDefaultContextDiagramTemplate();
        }
    }

    /// <summary>
    /// 修复ER图语法错误
    /// </summary>
    private string FixERDiagramSyntax(string mermaidCode)
    {
        try
        {
            var lines = mermaidCode.Split('\n').ToList();
            var fixedLines = new List<string>();
            var insideEntity = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // 跳过空行
                if (string.IsNullOrWhiteSpace(trimmedLine))
                {
                    fixedLines.Add(line);
                    continue;
                }

                // 检查是否进入或退出实体定义
                if (trimmedLine.Contains("{"))
                {
                    insideEntity = true;
                    fixedLines.Add(line);
                    continue;
                }
                else if (trimmedLine == "}")
                {
                    insideEntity = false;
                    fixedLines.Add(line);
                    continue;
                }

                // 移除不支持的PRIMARY_KEY语法
                if (trimmedLine.Contains("PRIMARY_KEY"))
                {
                    _logger.LogInformation("移除不支持的PRIMARY_KEY语法: {Line}", trimmedLine);
                    continue; // 跳过这一行
                }

                // 移除表格分隔符（如 StrategyTemplates : ----------------------- 这样的语法）
                if (trimmedLine.Contains(":") && (trimmedLine.Contains("---") || trimmedLine.Contains("===")))
                {
                    _logger.LogInformation("移除不支持的表格分隔符语法: {Line}", trimmedLine);
                    continue; // 跳过这一行
                }

                // 移除纯分隔符行（只包含 - 或 = 的行）
                if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^[-=]+$"))
                {
                    _logger.LogInformation("移除分隔符行: {Line}", trimmedLine);
                    continue; // 跳过这一行
                }

                // 在实体内部处理字段定义
                if (insideEntity)
                {
                    // 跳过实体内部的分隔符行
                    if (trimmedLine.Contains(":") && (trimmedLine.Contains("---") || trimmedLine.Contains("===")))
                    {
                        _logger.LogInformation("跳过实体内部的分隔符行: {Line}", trimmedLine);
                        continue;
                    }

                    // 跳过纯分隔符行
                    if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^[-=]+$"))
                    {
                        _logger.LogInformation("跳过实体内部的纯分隔符行: {Line}", trimmedLine);
                        continue;
                    }

                    // 修复复杂数据类型（如decimal(9,6)）
                    var fixedLine = FixComplexDataTypes(trimmedLine);

                    // 移除引号和约束描述
                    if (fixedLine.Contains("\""))
                    {
                        fixedLine = FixAttributeLine(fixedLine);
                    }

                    fixedLines.Add(line.Replace(trimmedLine, fixedLine));
                }
                else
                {
                    fixedLines.Add(line);
                }
            }

            return string.Join('\n', fixedLines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修复ER图语法时发生错误");
            return mermaidCode; // 返回原始代码
        }
    }

    /// <summary>
    /// 修复复杂数据类型定义
    /// </summary>
    private string FixComplexDataTypes(string line)
    {
        try
        {
            var trimmed = line.Trim();

            // 修复包含括号的数据类型（如decimal(9,6)、varchar(255)等）
            trimmed = System.Text.RegularExpressions.Regex.Replace(trimmed,
                @"\b(decimal|numeric|varchar|char|nvarchar|nchar|float|double|real)\s*\([^)]+\)",
                "$1",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 修复其他复杂类型
            trimmed = trimmed.Replace("datetime2", "datetime");
            trimmed = trimmed.Replace("bigint", "int");
            trimmed = trimmed.Replace("smallint", "int");
            trimmed = trimmed.Replace("tinyint", "int");
            trimmed = trimmed.Replace("ntext", "text");
            trimmed = trimmed.Replace("nvarchar", "string");
            trimmed = trimmed.Replace("varchar", "string");

            return trimmed;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "修复复杂数据类型时发生错误: {Line}", line);
            return line;
        }
    }

    /// <summary>
    /// 修复属性行的语法
    /// </summary>
    private string FixAttributeLine(string line)
    {
        try
        {
            // 示例输入: string username "NOT NULL, UNIQUE"
            // 期望输出: string username

            var parts = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
            {
                var dataType = parts[0];
                var fieldName = parts[1];
                var modifiers = "";

                // 保留PK和FK标记
                if (parts.Length > 2)
                {
                    for (int i = 2; i < parts.Length; i++)
                    {
                        if (parts[i] == "PK" || parts[i] == "FK")
                        {
                            modifiers += " " + parts[i];
                        }
                        else if (parts[i].StartsWith("\""))
                        {
                            // 遇到引号，停止处理
                            break;
                        }
                        else if (!parts[i].Contains("\""))
                        {
                            // 其他不包含引号的修饰符
                            modifiers += " " + parts[i];
                        }
                    }
                }

                return $"        {dataType} {fieldName}{modifiers}";
            }

            return line;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "修复属性行时发生错误: {Line}", line);
            return line; // 返回原始行
        }
    }

    /// <summary>
    /// 统计Mermaid ER图中的实体数量
    /// </summary>
    private int CountEntitiesInMermaid(string mermaidCode)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(mermaidCode))
                return 0;

            var lines = mermaidCode.Split('\n');
            var entityCount = 0;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                // 检查是否是实体定义行（包含 { 且不是关系定义）
                if (trimmedLine.Contains("{") && !trimmedLine.Contains("||") && !trimmedLine.Contains("--"))
                {
                    entityCount++;
                }
            }

            return entityCount;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "统计Mermaid实体数量时发生错误");
            return 0;
        }
    }

    /// <summary>
    /// 清理AI响应中的Mermaid代码
    /// </summary>
    private string CleanMermaidResponse(string aiResponse)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(aiResponse))
                return string.Empty;

            var cleaned = aiResponse.Trim();

            // 移除markdown代码块标记
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"```mermaid\s*", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"```\s*$", "", System.Text.RegularExpressions.RegexOptions.Multiline);
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"^```\s*", "", System.Text.RegularExpressions.RegexOptions.Multiline);

            // 移除开头和结尾的解释文字
            var lines = cleaned.Split('\n').ToList();

            // 找到第一个以erDiagram或flowchart开头的行
            int startIndex = -1;
            for (int i = 0; i < lines.Count; i++)
            {
                var trimmedLine = lines[i].Trim();
                if (trimmedLine.StartsWith("erDiagram", StringComparison.OrdinalIgnoreCase) ||
                    trimmedLine.StartsWith("flowchart", StringComparison.OrdinalIgnoreCase) ||
                    trimmedLine.StartsWith("graph", StringComparison.OrdinalIgnoreCase))
                {
                    startIndex = i;
                    break;
                }
            }

            if (startIndex == -1)
            {
                _logger.LogWarning("未找到有效的Mermaid图表开始标记");
                return cleaned; // 返回原始内容
            }

            // 从找到的开始位置提取内容
            var mermaidLines = lines.Skip(startIndex).ToList();

            // 清理中间的表格分隔符和无效语法
            var cleanedMermaidLines = new List<string>();
            foreach (var line in mermaidLines)
            {
                var trimmedLine = line.Trim();

                // 跳过表格分隔符行（如 StrategyTemplates : ----------------------- ）
                if (trimmedLine.Contains(":") && (trimmedLine.Contains("---") || trimmedLine.Contains("===")))
                {
                    _logger.LogDebug("跳过表格分隔符行: {Line}", trimmedLine);
                    continue;
                }

                // 跳过纯分隔符行
                if (System.Text.RegularExpressions.Regex.IsMatch(trimmedLine, @"^[-=]+$"))
                {
                    _logger.LogDebug("跳过纯分隔符行: {Line}", trimmedLine);
                    continue;
                }

                cleanedMermaidLines.Add(line);
            }

            mermaidLines = cleanedMermaidLines;

            // 移除末尾的解释文字（通常在最后几行）
            while (mermaidLines.Count > 0)
            {
                var lastLine = mermaidLines.Last().Trim();
                // 如果最后一行是空行、解释文字或不是Mermaid语法，则移除
                if (string.IsNullOrWhiteSpace(lastLine) ||
                    lastLine.StartsWith("注意", StringComparison.OrdinalIgnoreCase) ||
                    lastLine.StartsWith("说明", StringComparison.OrdinalIgnoreCase) ||
                    lastLine.StartsWith("以上", StringComparison.OrdinalIgnoreCase) ||
                    lastLine.StartsWith("这个", StringComparison.OrdinalIgnoreCase) ||
                    lastLine.Contains("根据") ||
                    lastLine.Contains("设计"))
                {
                    mermaidLines.RemoveAt(mermaidLines.Count - 1);
                }
                else
                {
                    break;
                }
            }

            var result = string.Join("\n", mermaidLines).Trim();

            _logger.LogDebug("清理前长度: {Before}, 清理后长度: {After}", aiResponse.Length, result.Length);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理Mermaid响应时发生错误");
            return aiResponse; // 返回原始内容
        }
    }

    /// <summary>
    /// 统计Mermaid上下文图中的外部实体数量
    /// </summary>
    private int CountExternalEntitiesInMermaid(string mermaidCode)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(mermaidCode))
                return 0;

            var lines = mermaidCode.Split('\n');
            var entityCount = 0;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                // 检查是否是节点定义行（通常包含方括号或圆括号）
                if ((trimmedLine.Contains("[") && trimmedLine.Contains("]")) ||
                    (trimmedLine.Contains("(") && trimmedLine.Contains(")")))
                {
                    // 排除箭头连接线
                    if (!trimmedLine.Contains("-->") && !trimmedLine.Contains("---"))
                    {
                        entityCount++;
                    }
                }
            }

            return entityCount;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "统计Mermaid外部实体数量时发生错误");
            return 0;
        }
    }

    /// <summary>
    /// 获取默认ER图模板
    /// </summary>
    private string GetDefaultERDiagramTemplate()
    {
        return @"erDiagram
    USER {
        int id PK
        string username
        string email
        string password_hash
        datetime created_at
        datetime updated_at
    }

    PROJECT {
        int id PK
        string name
        string description
        int owner_id FK
        string status
        datetime created_at
        datetime updated_at
    }

    TASK {
        int id PK
        string title
        text description
        int project_id FK
        int assignee_id FK
        string status
        string priority
        datetime due_date
        datetime created_at
        datetime updated_at
    }

    REQUIREMENT_DOCUMENT {
        int id PK
        int project_id FK
        string title
        text content
        string version
        string status
        datetime created_at
        datetime updated_at
    }

    USER ||--o{ PROJECT : owns
    PROJECT ||--o{ TASK : contains
    USER ||--o{ TASK : assigned_to
    PROJECT ||--o{ REQUIREMENT_DOCUMENT : has";
    }

    /// <summary>
    /// 获取默认Context图模板
    /// </summary>
    private string GetDefaultContextDiagramTemplate()
    {
        return @"flowchart TD
    User[用户] --> System[项目管理系统]
    Admin[管理员] --> System
    Developer[开发者] --> System

    System --> Database[(数据库)]
    System --> FileStorage[文件存储]
    System --> EmailService[邮件服务]
    System --> NotificationService[通知服务]
    System --> AIService[AI服务]

    subgraph ""系统边界""
        System
        Database
        FileStorage
    end

    subgraph ""外部服务""
        EmailService
        NotificationService
        AIService
    end

    subgraph ""用户角色""
        User
        Admin
        Developer
    end";
    }

    /// <summary>
    /// 从Mermaid代码中提取外部实体
    /// </summary>
    private string ExtractExternalEntities(string mermaidCode)
    {
        try
        {
            var entities = new List<string>();
            var lines = mermaidCode.Split('\n');

            foreach (var line in lines)
            {
                if (line.Contains("[") && line.Contains("]") && !line.Contains("subgraph"))
                {
                    var start = line.IndexOf('[') + 1;
                    var end = line.IndexOf(']');
                    if (start > 0 && end > start)
                    {
                        var entity = line.Substring(start, end - start);
                        if (!entities.Contains(entity))
                        {
                            entities.Add(entity);
                        }
                    }
                }
            }

            return string.Join(", ", entities);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取外部实体时发生错误");
            return "用户, 管理员, 外部系统";
        }
    }

    /// <summary>
    /// 从Mermaid代码中提取系统边界
    /// </summary>
    private string ExtractSystemBoundary(string mermaidCode)
    {
        try
        {
            var boundaries = new List<string>();
            var lines = mermaidCode.Split('\n');

            foreach (var line in lines)
            {
                if (line.Contains("subgraph") && line.Contains("系统"))
                {
                    var start = line.IndexOf('"') + 1;
                    var end = line.LastIndexOf('"');
                    if (start > 0 && end > start)
                    {
                        var boundary = line.Substring(start, end - start);
                        boundaries.Add(boundary);
                    }
                }
            }

            return boundaries.Any() ? string.Join(", ", boundaries) : "系统核心, 数据存储";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取系统边界时发生错误");
            return "系统核心, 数据存储";
        }
    }

    /// <summary>
    /// 从Mermaid代码中提取数据流
    /// </summary>
    private string ExtractDataFlows(string mermaidCode)
    {
        try
        {
            var flows = new List<string>();
            var lines = mermaidCode.Split('\n');

            foreach (var line in lines)
            {
                if (line.Contains("-->") || line.Contains("---"))
                {
                    var cleanLine = line.Trim();
                    if (!cleanLine.StartsWith("subgraph") && !cleanLine.StartsWith("end"))
                    {
                        flows.Add(cleanLine);
                    }
                }
            }

            return flows.Any() ? string.Join("; ", flows) : "用户输入 -> 系统处理 -> 数据存储";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "提取数据流时发生错误");
            return "用户输入 -> 系统处理 -> 数据存储";
        }
    }

    /// <summary>
    /// 自然语言生成 Mermaid 序列流程图
    /// </summary>
    [HttpPost("generate-mermaid-sequence")]
    public async Task<IActionResult> GenerateMermaidSequence([FromBody] GenerateMermaidSequenceRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求生成 Mermaid 序列流程图", userId);

            // 构建AI提示词
            var promptTemplate = await GetPromptTemplate("GenerateMermaidSequence");
            var prompt = promptTemplate.Replace("{{sequenceName}}", request.Name ?? "自动化序列")
                                     .Replace("{{sequenceDescription}}", request.Description ?? "")
                                     .Replace("{{techStack}}", request.TechStack ?? "web")
                                     .Replace("{{timestamp}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            string aiResponse;

            // 根据用户配置调用AI服务
            var userMapping = await _userTaskMappingRepository.GetByUserIdAndTaskTypeAsync(userId, "RequirementDecomposition");
            var activeMapping = userMapping.FirstOrDefault(x => x.IsActive) ?? userMapping.FirstOrDefault();

            if (activeMapping != null)
            {
                // 根据ProviderName获取用户的AI配置
                var userAIConfigs = await _userAIConfigRepository.GetByUserIdAndProviderAsync(userId, activeMapping.ProviderName);
                var userAIConfig = userAIConfigs.FirstOrDefault(x => x.IsActive && x.ModelType == "RequirementDecomposition")
                                  ?? userAIConfigs.FirstOrDefault(x => x.IsActive);

                if (userAIConfig != null)
                {
                    _logger.LogInformation("使用用户配置的AI提供商: {Provider}, 模型: {Model}",
                        userAIConfig.ProviderName, userAIConfig.ModelName);

                    // 使用用户的AI配置
                    var config = new ProjectManagement.Core.DTOs.AI.AIModelConfig
                    {
                        Provider = userAIConfig.ProviderName,
                        Model = userAIConfig.ModelName,
                        ApiKey = userAIConfig.ApiKey ?? string.Empty,
                        Endpoint = userAIConfig.ApiEndpoint ?? string.Empty,
                        MaxTokens = 4000,
                        Temperature = 0.7f
                    };

                    aiResponse = await _aiService.GenerateTextAsync(prompt, config);

                    // 更新使用统计
                    await _userAIConfigRepository.UpdateUsageStatisticsAsync(userAIConfig.Id, 4000, true);
                }
                else
                {
                    _logger.LogWarning("用户没有配置AI提供商 {ProviderName}，使用默认配置", activeMapping.ProviderName);
                    aiResponse = await _aiService.GenerateTextAsync(prompt, config: null);
                }
            }
            else
            {
                // 使用默认配置
                aiResponse = await _aiService.GenerateTextAsync(prompt, config: null);
            }

            // 清理AI响应，提取纯Mermaid代码
            var cleanedMermaidCode = CleanMermaidResponse(aiResponse);

            _logger.LogInformation("Mermaid 序列流程图生成完成");

            return Ok(new
            {
                mermaidCode = cleanedMermaidCode,
                originalResponse = aiResponse
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成 Mermaid 序列流程图失败");
            return StatusCode(500, "生成 Mermaid 序列流程图失败");
        }
    }

    /// <summary>
    /// 将 Mermaid 流程图转换为模板序列
    /// </summary>
    [HttpPost("convert-mermaid-to-sequence")]
    public async Task<IActionResult> ConvertMermaidToSequence([FromBody] ConvertMermaidToSequenceRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("用户 {UserId} 请求将 Mermaid 转换为模板序列", userId);

            // 构建AI提示词
            var promptTemplate = await GetPromptTemplate("ConvertMermaidToSequence");
            var prompt = promptTemplate.Replace("{{mermaidCode}}", request.MermaidCode)
                                     .Replace("{{sequenceName}}", request.Name ?? "新序列")
                                     .Replace("{{sequenceDescription}}", request.Description ?? "")
                                     .Replace("{{timestamp}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            string aiResponse;

            // 根据用户配置调用AI服务
            var userMapping = await _userTaskMappingRepository.GetByUserIdAndTaskTypeAsync(userId, "RequirementDecomposition");
            var activeMapping = userMapping.FirstOrDefault(x => x.IsActive) ?? userMapping.FirstOrDefault();

            if (activeMapping != null)
            {
                // 根据ProviderName获取用户的AI配置
                var userAIConfigs = await _userAIConfigRepository.GetByUserIdAndProviderAsync(userId, activeMapping.ProviderName);
                var userAIConfig = userAIConfigs.FirstOrDefault(x => x.IsActive && x.ModelType == "RequirementDecomposition")
                                  ?? userAIConfigs.FirstOrDefault(x => x.IsActive);

                if (userAIConfig != null)
                {
                    _logger.LogInformation("使用用户配置的AI提供商: {Provider}, 模型: {Model}",
                        userAIConfig.ProviderName, userAIConfig.ModelName);

                    // 使用用户的AI配置
                    var config = new AIModelConfig
                    {
                        Provider = userAIConfig.ProviderName,
                        Model = userAIConfig.ModelName,
                        ApiKey = userAIConfig.ApiKey ?? string.Empty,
                        Endpoint = userAIConfig.ApiEndpoint ?? string.Empty,
                        MaxTokens = 4000,
                        Temperature = 0.3f
                    };

                    aiResponse = await _aiService.GenerateTextAsync(prompt, config);

                    // 更新使用统计
                    await _userAIConfigRepository.UpdateUsageStatisticsAsync(userAIConfig.Id, 4000, true);
                }
                else
                {
                    _logger.LogWarning("用户没有配置AI提供商 {ProviderName}，使用默认配置", activeMapping.ProviderName);
                    aiResponse = await _aiService.GenerateTextAsync(prompt, config: null);
                }
            }
            else
            {
                // 使用默认配置
                aiResponse = await _aiService.GenerateTextAsync(prompt, config: null);
            }

            // 解析AI响应为序列数据
            var sequenceData = ParseSequenceFromAIResponse(aiResponse);

            _logger.LogInformation("Mermaid 转换为模板序列完成");

            return Ok(sequenceData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "将 Mermaid 转换为模板序列失败");
            return StatusCode(500, "转换失败");
        }
    }

    #endregion

    #region Mermaid 序列相关辅助方法



    /// <summary>
    /// 解析AI响应为序列数据
    /// </summary>
    private object ParseSequenceFromAIResponse(string aiResponse)
    {
        try
        {
            // 尝试解析JSON格式的响应
            if (aiResponse.Trim().StartsWith("{"))
            {
                var result = JsonSerializer.Deserialize<object>(aiResponse);
                if (result != null)
                {
                    return result;
                }
            }

            // 如果不是JSON，构建默认序列结构
            return new
            {
                name = "AI生成序列",
                description = "通过AI从Mermaid流程图生成的序列",
                category = "AI生成",
                isActive = true,
                tags = new[] { "AI生成", "Mermaid" },
                notes = "此序列由AI自动生成，请根据需要进行调整",
                steps = new[]
                {
                    new
                    {
                        stepOrder = 1,
                        actionType = "click",
                        description = "开始执行",
                        parameters = new { },
                        timeoutSeconds = 5,
                        maxRetries = 3,
                        isActive = true
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析AI响应失败，返回默认序列");
            return new
            {
                name = "默认序列",
                description = "解析失败时的默认序列",
                category = "默认",
                isActive = true,
                tags = new[] { "默认" },
                notes = "AI响应解析失败，请手动配置",
                steps = new object[0]
            };
        }
    }

    /// <summary>
    /// 获取提示词模板
    /// </summary>
    private async Task<string> GetPromptTemplate(string templateName)
    {
        try
        {
            if (_promptTemplateService != null)
            {
                var template = await _promptTemplateService.GetDefaultTemplateAsync(templateName);
                if (template != null)
                {
                    return template.Content;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取提示词模板失败: {TemplateName}", templateName);
        }

        // 返回默认模板
        return GetDefaultPromptTemplate(templateName);
    }

    /// <summary>
    /// 获取默认提示词模板
    /// </summary>
    private string GetDefaultPromptTemplate(string templateName)
    {
        return templateName switch
        {
            "GenerateMermaidSequence" => @"
请根据以下描述生成一个Mermaid流程图，用于表示自动化序列的执行流程：

序列名称：{{sequenceName}}
序列描述：{{sequenceDescription}}
技术栈：{{techStack}}
生成时间：{{timestamp}}

要求：
1. 使用flowchart TD格式
2. 包含开始和结束节点
3. 根据描述添加适当的步骤节点
4. 使用合适的节点形状：
   - [文本] 表示基础操作
   - {文本} 表示条件判断
   - [[文本]] 表示循环
   - >文本] 表示跳转/退出
5. 添加适当的连接线和标签
6. 只返回纯Mermaid代码，不要包含其他文字说明

示例格式：
flowchart TD
    Start([开始]) --> Step1[步骤1]
    Step1 --> Step2{条件判断}
    Step2 -->|是| Step3[执行操作]
    Step2 -->|否| Step4[其他操作]
    Step3 --> End([结束])
    Step4 --> End
",
            "ConvertMermaidToSequence" => @"
请将以下Mermaid流程图转换为模板序列的JSON格式：

Mermaid代码：
{{mermaidCode}}

序列名称：{{sequenceName}}
序列描述：{{sequenceDescription}}
生成时间：{{timestamp}}

要求：
1. 分析Mermaid流程图中的每个节点
2. 将节点转换为对应的步骤
3. 保持步骤的执行顺序
4. 根据节点形状确定actionType：
   - 矩形节点 -> click, input, wait等基础操作
   - 菱形节点 -> condition
   - 子程序节点 -> loop, loop_end
   - 旗帜节点 -> jump, exit
5. 返回完整的JSON格式序列定义

返回格式：
{
  ""name"": ""序列名称"",
  ""description"": ""序列描述"",
  ""category"": ""AI生成"",
  ""isActive"": true,
  ""tags"": [""AI生成"", ""Mermaid""],
  ""notes"": ""从Mermaid流程图生成"",
  ""steps"": [
    {
      ""stepOrder"": 1,
      ""actionType"": ""click"",
      ""description"": ""步骤描述"",
      ""parameters"": {},
      ""timeoutSeconds"": 5,
      ""maxRetries"": 3,
      ""isActive"": true
    }
  ]
}
",
            _ => "请处理用户请求。"
        };
    }

    #endregion
}

/// <summary>
/// 切换提供商请求模型
/// </summary>
public class ToggleProviderRequest
{
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }
}

/// <summary>
/// 更新提供商配置请求模型
/// </summary>
public class UpdateProviderConfigRequest
{
    /// <summary>
    /// API端点
    /// </summary>
    public string? Endpoint { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int? TimeoutSeconds { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int? MaxRetries { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// GPT-4部署名称（Azure专用）
    /// </summary>
    public string? GPT4DeploymentName { get; set; }

    /// <summary>
    /// GPT-3.5部署名称（Azure专用）
    /// </summary>
    public string? GPT35DeploymentName { get; set; }

    /// <summary>
    /// 嵌入模型部署名称（Azure专用）
    /// </summary>
    public string? EmbeddingDeploymentName { get; set; }
}

/// <summary>
/// 更新AI全局配置请求模型
/// </summary>
public class UpdateAIConfigurationRequest
{
    /// <summary>
    /// 提供商配置
    /// </summary>
    public Dictionary<string, object>? Providers { get; set; }

    /// <summary>
    /// 默认提供商
    /// </summary>
    public string? DefaultProvider { get; set; }

    /// <summary>
    /// 任务映射配置
    /// </summary>
    public Dictionary<string, string>? TaskMapping { get; set; }

    /// <summary>
    /// 全局设置
    /// </summary>
    public object? GlobalSettings { get; set; }
}

/// <summary>
/// 生成 Mermaid 序列请求模型
/// </summary>
public class GenerateMermaidSequenceRequest
{
    /// <summary>
    /// 序列名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechStack { get; set; }
}

/// <summary>
/// 转换 Mermaid 为序列请求模型
/// </summary>
public class ConvertMermaidToSequenceRequest
{
    /// <summary>
    /// Mermaid 代码
    /// </summary>
    public string MermaidCode { get; set; } = string.Empty;

    /// <summary>
    /// 序列名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 序列描述
    /// </summary>
    public string? Description { get; set; }
}

// 原型图相关辅助方法扩展
public partial class AIController
{
    /// <summary>
    /// 获取原型图类型显示名称
    /// </summary>
    private string GetPrototypeTypeDisplayName(string prototypeType)
    {
        return prototypeType switch
        {
            "Wireframe" => "线框图",
            "UserFlow" => "用户流程图",
            "ComponentDiagram" => "组件关系图",
            "InteractionFlow" => "交互流程图",
            _ => "原型图"
        };
    }

    /// <summary>
    /// 获取Mermaid图表类型
    /// </summary>
    private string GetMermaidDiagramType(string prototypeType)
    {
        return prototypeType switch
        {
            "Wireframe" => "flowchart",
            "UserFlow" => "flowchart",
            "ComponentDiagram" => "graph",
            "InteractionFlow" => "flowchart",
            _ => "flowchart"
        };
    }

    /// <summary>
    /// 获取原型图提示词模板
    /// </summary>
    private string GetPrototypePromptTemplate(string prototypeType, string deviceType, string fidelityLevel)
    {
        var baseTemplate = @"
请生成一个Mermaid格式的{0}，要求如下：

设备类型：{1}
保真度级别：{2}

生成要求：
1. 使用合适的Mermaid语法（flowchart TD 或 graph TD）
2. 节点命名要清晰明确，使用中文标签
3. 连接线要表达清楚的逻辑关系
4. 只返回纯Mermaid代码，不要包含markdown代码块标记
5. 确保语法正确，可以被Mermaid正确渲染

";

        var specificRequirements = prototypeType switch
        {
            "Wireframe" => @"线框图特定要求：
- 使用矩形节点表示页面区域和组件
- 使用不同的节点样式区分不同类型的元素（头部、导航、内容区、侧边栏、底部等）
- 体现页面的基本布局结构
- 包含主要的UI组件（按钮、表单、列表等）
- 展示页面间的导航关系

示例格式：
flowchart TD
    A[页面头部] --> B[导航菜单]
    B --> C[主内容区]
    C --> D[侧边栏]
    D --> E[页面底部]",

            "UserFlow" => @"用户流程图特定要求：
- 使用圆形节点表示开始和结束
- 使用矩形节点表示用户操作步骤
- 使用菱形节点表示决策点
- 清楚展示用户的操作路径
- 包含异常流程和错误处理

示例格式：
flowchart TD
    Start([用户开始]) --> Login[登录页面]
    Login --> Check{验证登录}
    Check -->|成功| Dashboard[用户仪表板]
    Check -->|失败| Error[错误提示]",

            "ComponentDiagram" => @"组件关系图特定要求：
- 使用矩形节点表示组件
- 使用不同颜色或样式区分组件类型
- 展示组件间的依赖关系
- 体现组件的层次结构
- 包含数据流向

示例格式：
graph TD
    App[应用根组件] --> Header[头部组件]
    App --> Main[主体组件]
    Main --> List[列表组件]
    List --> Item[列表项组件]",

            "InteractionFlow" => @"交互流程图特定要求：
- 展示用户与系统的交互过程
- 包含用户操作、系统响应、状态变化
- 使用不同节点形状区分不同类型的交互
- 体现时间顺序和因果关系
- 包含反馈机制

示例格式：
flowchart TD
    User[用户操作] --> System[系统处理]
    System --> Response[系统响应]
    Response --> Update[状态更新]
    Update --> Feedback[用户反馈]",

            _ => "请根据原型图类型生成合适的Mermaid图表。"
        };

        return string.Format(baseTemplate, GetPrototypeTypeDisplayName(prototypeType), deviceType, fidelityLevel) + specificRequirements;
    }

    /// <summary>
    /// 构建聊天提示词
    /// </summary>
    private async Task<string> BuildChatPrompt(AIChatRequestDto request, int userId)
    {
        try
        {
            // 1. 尝试从数据库获取Prompt模板
            string promptTemplate = await GetPromptTemplateFromDatabase(request.TaskType, request.PromptTemplateId);

            // 2. 构建上下文参数
            var parameters = new Dictionary<string, object>
            {
                ["userMessage"] = request.Message
            };

            // 添加项目上下文
            if (request.ProjectId.HasValue)
            {
                try
                {
                    var project = await _projectRepository.GetByIdAsync(request.ProjectId.Value);
                    if (project != null)
                    {
                        var projectContext = $"项目名称：{project.Name}\n" +
                                           $"项目描述：{project.Description}\n" +
                                           $"项目状态：{project.Status}\n" +
                                           $"技术栈：{project.TechnologyStack}";

                        parameters["projectContext"] = projectContext;
                        parameters["projectInfo"] = projectContext;
                        parameters["projectBackground"] = project.Description ?? "";
                        parameters["context"] = projectContext;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取项目信息失败: {ProjectId}", request.ProjectId);
                }
            }

            // 3. 构建向量增强的上下文
            string enhancedContext = "";
            try
            {
                enhancedContext = await _conversationContextService.BuildVectorEnhancedContextAsync(
                    conversationId: request.ConversationId,
                    currentMessage: request.Message,
                    projectId: request.ProjectId,
                    includeVectorContext: true
                );

                // 将增强上下文添加到参数中
                if (!string.IsNullOrEmpty(enhancedContext))
                {
                    parameters["conversationHistory"] = enhancedContext;
                    parameters["contextualPrompt"] = enhancedContext;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "构建向量增强上下文失败，使用基础模式: {ConversationId}", request.ConversationId);
            }

            // 4. 替换模板中的参数占位符
            string finalPrompt = ReplacePromptParameters(promptTemplate, parameters);

            // 5. 如果有增强上下文，将其添加到最终提示词中
            if (!string.IsNullOrEmpty(enhancedContext))
            {
                finalPrompt = $"{enhancedContext}\n\n{finalPrompt}";
            }

            return finalPrompt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建聊天提示词失败，使用默认模板");
            return GetFallbackPrompt(request.TaskType, request.Message);
        }
    }

    /// <summary>
    /// 从数据库获取Prompt模板
    /// </summary>
    private async Task<string> GetPromptTemplateFromDatabase(string taskType, int? templateId = null)
    {
        try
        {
            PromptTemplate? template = null;

            // 如果指定了模板ID，直接获取
            if (templateId.HasValue)
            {
                template = await _promptTemplateRepository.GetByIdAsync(templateId.Value);
                if (template != null && template.IsEnabled && !template.IsDeleted)
                {
                    // 更新使用次数和最后使用时间
                    await UpdateTemplateUsageAsync(template.Id);
                    return template.Content;
                }
            }

            // 根据任务类型获取默认模板
            template = await _promptTemplateRepository.GetDefaultTemplateAsync(taskType);
            if (template != null)
            {
                await UpdateTemplateUsageAsync(template.Id);
                return template.Content;
            }

            // 如果没有找到默认模板，获取该任务类型的第一个可用模板
            var templates = await _promptTemplateRepository.GetByTaskTypeAsync(taskType);
            if (templates.Any())
            {
                template = templates.First();
                await UpdateTemplateUsageAsync(template.Id);
                return template.Content;
            }

            // 如果没有找到对应的模板，返回通用模板
            template = await _promptTemplateRepository.GetDefaultTemplateAsync("General");
            if (template != null)
            {
                await UpdateTemplateUsageAsync(template.Id);
                return template.Content;
            }

            // 最后尝试获取任何通用类型的模板
            var generalTemplates = await _promptTemplateRepository.GetByTaskTypeAsync("General");
            if (generalTemplates.Any())
            {
                template = generalTemplates.First();
                await UpdateTemplateUsageAsync(template.Id);
                return template.Content;
            }

            return GetFallbackPrompt(taskType, "");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从数据库获取Prompt模板失败: TaskType={TaskType}, TemplateId={TemplateId}", taskType, templateId);
            return GetFallbackPrompt(taskType, "");
        }
    }

    /// <summary>
    /// 更新模板使用统计
    /// </summary>
    private async Task UpdateTemplateUsageAsync(int templateId)
    {
        try
        {
            var template = await _promptTemplateRepository.GetByIdAsync(templateId);
            if (template != null)
            {
                template.UsageCount++;
                template.LastUsedTime = DateTime.UtcNow;
                await _promptTemplateRepository.UpdateAsync(template);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新模板使用统计失败: TemplateId={TemplateId}", templateId);
            // 不抛出异常，避免影响主要功能
        }
    }

    /// <summary>
    /// 替换Prompt模板中的参数占位符
    /// </summary>
    private string ReplacePromptParameters(string template, Dictionary<string, object> parameters)
    {
        string result = template;

        foreach (var param in parameters)
        {
            string placeholder = $"{{{param.Key}}}";
            string value = param.Value?.ToString() ?? "";
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    /// <summary>
    /// 获取后备提示词（当数据库查询失败时使用）
    /// </summary>
    private string GetFallbackPrompt(string taskType, string userMessage)
    {
        var fallbackPrompts = new Dictionary<string, string>
        {
            ["General"] = "你是一个专业的AI技术助手，请回答用户的问题：{userMessage}",
            ["ProjectManagement"] = "你是一个资深的项目管理专家，请为用户提供项目管理建议：{userMessage}",
            ["RequirementAnalysis"] = "你是一个专业的需求分析师，请帮助分析和整理需求：{userMessage}",
            ["DesignGeneration"] = "你是一个资深的系统架构师，请提供设计建议：{userMessage}",
            ["CodeGeneration"] = "你是一个经验丰富的开发导师，请提供开发指导：{userMessage}"
        };

        string template = fallbackPrompts.GetValueOrDefault(taskType, fallbackPrompts["General"]);
        return template.Replace("{userMessage}", userMessage);
    }

    /// <summary>
    /// 写入流式响应
    /// </summary>
    private async Task WriteToStream(string data)
    {
        var bytes = Encoding.UTF8.GetBytes(data);
        await Response.Body.WriteAsync(bytes, 0, bytes.Length);
        await Response.Body.FlushAsync();
    }

    /// <summary>
    /// 模拟流式响应（将完整响应分块发送）
    /// </summary>
    private async Task SimulateStreamResponse(string fullResponse, StringBuilder responseBuilder)
    {
        if (string.IsNullOrEmpty(fullResponse))
            return;

        // 按字符分块发送，模拟打字机效果
        var words = fullResponse.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        for (int i = 0; i < words.Length; i++)
        {
            var word = words[i];
            if (i > 0) word = " " + word; // 添加空格

            responseBuilder.Append(word);

            // 发送当前块
            var chunk = System.Text.Json.JsonSerializer.Serialize(word);
            await WriteToStream($"data: {{\"type\":\"chunk\",\"content\":{chunk}}}\n\n");

            // 添加延迟模拟打字效果（实际使用时可以移除或调整）
            await Task.Delay(50);
        }
    }

    /// <summary>
    /// 获取AI配置
    /// </summary>
    private async Task<AIModelConfig?> GetAIConfiguration(int? aiProviderConfigId, int userId)
    {
        return await AIConfigurationHelper.GetUserAIConfigurationAsync(
            _aiConfigRepository,
            _logger,
            userId,
            aiProviderConfigId,
            4000,
            0.7f,
            _encryptionService);
    }

    /// <summary>
    /// 生成建议的后续操作
    /// </summary>
    private List<string> GenerateSuggestedActions(string taskType, string aiResponse)
    {
        var suggestions = new List<string>();

        switch (taskType)
        {
            case "ProjectManagement":
                if (aiResponse.Contains("项目") || aiResponse.Contains("创建"))
                {
                    suggestions.Add("创建新项目");
                    suggestions.Add("查看项目模板");
                }
                break;

            case "RequirementAnalysis":
                if (aiResponse.Contains("需求") || aiResponse.Contains("文档"))
                {
                    suggestions.Add("保存需求文档");
                    suggestions.Add("生成用户故事");
                }
                break;

            case "DesignGeneration":
                if (aiResponse.Contains("ER图") || aiResponse.Contains("数据库"))
                {
                    suggestions.Add("生成ER图");
                }
                if (aiResponse.Contains("原型") || aiResponse.Contains("界面"))
                {
                    suggestions.Add("生成原型图");
                }
                if (aiResponse.Contains("架构") || aiResponse.Contains("系统"))
                {
                    suggestions.Add("生成架构图");
                }
                break;

            case "CodeGeneration":
                if (aiResponse.Contains("代码") || aiResponse.Contains("开发"))
                {
                    suggestions.Add("分解开发步骤");
                    suggestions.Add("生成代码模板");
                }
                break;
        }

        return suggestions;
    }
}

/// <summary>
/// 创建AI供应商请求模型
/// </summary>
public class CreateProviderRequest
{
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// API端点
    /// </summary>
    public string ApiEndpoint { get; set; } = string.Empty;

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int? TimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int? MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}
