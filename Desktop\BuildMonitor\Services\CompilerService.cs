using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BuildMonitor.Models;
using System.Text;
using System.Text.RegularExpressions;

namespace BuildMonitor.Services
{
    /// <summary>
    /// 编译服务 - 使用dotnet build命令
    /// </summary>
    public class CompilerService
    {

        /// <summary>
        /// 编译C#项目 - 使用dotnet build命令
        /// </summary>
        public async Task<List<CompilationError>> CompileCSharpProjectAsync(string projectPath, int? projectId = null)
        {
            var errors = new List<CompilationError>();
            var sessionId = Guid.NewGuid().ToString();

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔨 开始编译项目: {projectPath}");

                // 验证项目文件是否存在
                if (!File.Exists(projectPath))
                {
                    throw new FileNotFoundException($"项目文件不存在: {projectPath}");
                }

                // 使用dotnet build命令编译
                System.Diagnostics.Debug.WriteLine("🔧 使用dotnet build命令编译");
                return await CompileWithDotnetBuildAsync(projectPath, projectId, sessionId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编译C#项目失败: {ex.Message}");

                // 添加编译异常作为错误 - 修改错误代码避免被过滤
                errors.Add(new CompilationError
                {
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    ProjectType = "Backend",
                    Severity = "Error",
                    Code = "BUILD_EXCEPTION",  // 修改为BUILD_EXCEPTION避免被过滤
                    Message = $"编译失败: {ex.Message}",
                    FilePath = projectPath,
                    LineNumber = 0,
                    ColumnNumber = 0,
                    ProjectName = Path.GetFileNameWithoutExtension(projectPath),
                    ProjectPath = Path.GetDirectoryName(projectPath),
                    CompilerVersion = "Roslyn 4.8.0",
                    TargetFramework = "net8.0",
                    BuildConfiguration = "Debug",
                    CompilationTime = DateTime.Now,
                    CreatedTime = DateTime.Now
                });
            }

            return errors;
        }

        /// <summary>
        /// 查找解决方案文件中的项目文件
        /// </summary>
        private List<string> FindProjectFilesInSolution(string solutionPath)
        {
            var projectFiles = new List<string>();

            try
            {
                var solutionDir = Path.GetDirectoryName(solutionPath);
                var lines = File.ReadAllLines(solutionPath);

                foreach (var line in lines)
                {
                    // 查找项目引用行，格式类似：Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectName", "ProjectPath.csproj", "{GUID}"
                    if (line.StartsWith("Project(") && line.Contains(".csproj"))
                    {
                        var parts = line.Split(',');
                        if (parts.Length >= 2)
                        {
                            var projectRelativePath = parts[1].Trim().Trim('"');
                            var fullProjectPath = Path.Combine(solutionDir, projectRelativePath);

                            if (File.Exists(fullProjectPath))
                            {
                                projectFiles.Add(fullProjectPath);
                                System.Diagnostics.Debug.WriteLine($"📁 找到项目文件: {fullProjectPath}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 解析解决方案文件失败: {ex.Message}");
            }

            return projectFiles;
        }

        /// <summary>
        /// 编译前端项目（TypeScript/Vue）
        /// </summary>
        public async Task<List<CompilationError>> CompileFrontendProjectAsync(string projectPath, int? projectId = null)
        {
            var errors = new List<CompilationError>();
            var sessionId = Guid.NewGuid().ToString();

            try
            {
                System.Diagnostics.Debug.WriteLine($"开始编译前端项目: {projectPath}");

                // 检查项目路径是否为空或不存在
                if (string.IsNullOrWhiteSpace(projectPath))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ 前端项目路径为空，跳过编译");
                    return errors; // 返回空列表，不添加错误
                }

                if (!Directory.Exists(projectPath))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 前端项目目录不存在: {projectPath}，跳过编译");
                    return errors; // 不添加错误，直接返回空列表
                }

                // 检查是否存在package.json
                var packageJsonPath = Path.Combine(projectPath, "package.json");
                if (!File.Exists(packageJsonPath))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 未找到package.json文件: {packageJsonPath}，跳过前端编译");
                    return errors; // 不添加错误，直接返回空列表
                }

                // 运行npm run build或类似命令来检查编译错误
                var buildResult = await RunNpmBuildAsync(projectPath);

                // 解析编译输出中的错误信息
                var parsedErrors = ParseFrontendBuildOutput(buildResult, projectPath, sessionId, projectId);
                errors.AddRange(parsedErrors);

                System.Diagnostics.Debug.WriteLine($"前端项目编译完成，发现 {errors.Count} 个问题");
                if (parsedErrors.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine("前端编译错误详情:");
                    foreach (var error in parsedErrors.Take(3))
                    {
                        System.Diagnostics.Debug.WriteLine($"  - {error.Severity}: {error.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编译前端项目失败: {ex.Message}");
                
                errors.Add(new CompilationError
                {
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    ProjectType = "Frontend",
                    Severity = "Error",
                    Code = "BUILD_EXCEPTION",  // 修改为BUILD_EXCEPTION避免被过滤
                    Message = $"编译失败: {ex.Message}",
                    FilePath = projectPath,
                    LineNumber = 0,
                    ColumnNumber = 0,
                    ProjectName = "Frontend",
                    ProjectPath = projectPath,
                    CompilerVersion = "TypeScript 5.0",
                    TargetFramework = "ES2022",
                    BuildConfiguration = "Development",
                    CompilationTime = DateTime.Now,
                    CreatedTime = DateTime.Now
                });
            }

            return errors;
        }

        /// <summary>
        /// 使用dotnet build命令编译项目（备用方法）
        /// </summary>
        private async Task<List<CompilationError>> CompileWithDotnetBuildAsync(string projectPath, int? projectId, string sessionId)
        {
            var errors = new List<CompilationError>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 使用dotnet build编译项目: {projectPath}");

                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"build \"{projectPath}\" --verbosity normal",
                    WorkingDirectory = Path.GetDirectoryName(projectPath),
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    StandardOutputEncoding = Encoding.UTF8,
                    StandardErrorEncoding = Encoding.UTF8
                };

                using var process = new System.Diagnostics.Process { StartInfo = processInfo };
                var outputBuilder = new StringBuilder();
                var errorBuilder = new StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        outputBuilder.AppendLine(e.Data);
                    }
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        errorBuilder.AppendLine(e.Data);
                    }
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                await process.WaitForExitAsync();

                var output = outputBuilder.ToString();
                var errorOutput = errorBuilder.ToString();

                // 解析编译错误
                var allOutput = output + Environment.NewLine + errorOutput;
                errors.AddRange(ParseDotnetBuildErrors(allOutput, projectId, sessionId));

                System.Diagnostics.Debug.WriteLine($"✅ dotnet build编译完成，发现 {errors.Count} 个错误");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ dotnet build编译失败: {ex.Message}");
                errors.Add(new CompilationError
                {
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    Code = "BUILD_ERROR",
                    Message = $"编译过程异常: {ex.Message}",
                    FilePath = projectPath,
                    LineNumber = 0,
                    ColumnNumber = 0,
                    Severity = "Error",
                    ProjectType = "Backend"
                });
            }

            return errors;
        }

        /// <summary>
        /// 解析dotnet build输出中的错误信息
        /// </summary>
        private List<CompilationError> ParseDotnetBuildErrors(string buildOutput, int? projectId, string sessionId)
        {
            var errors = new List<CompilationError>();
            var uniqueErrors = new HashSet<string>(); // 用于去重的集合

            if (string.IsNullOrEmpty(buildOutput))
                return errors;

            var lines = buildOutput.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                // 跳过空行
                if (string.IsNullOrWhiteSpace(line))
                    continue;

                // 跳过带有编译器前缀的重复行（如 "3>", "2>" 等）
                var cleanLine = line;
                if (Regex.IsMatch(line, @"^\d+>"))
                {
                    cleanLine = Regex.Replace(line, @"^\d+>", "").Trim();

                    // 检查这个清理后的行是否已经处理过
                    if (uniqueErrors.Contains($"PROCESSED_LINE:{cleanLine}"))
                    {
                        System.Diagnostics.Debug.WriteLine($"🔄 跳过重复的编译器输出行: {line}");
                        continue;
                    }
                }

                // 格式1: 标准C#编译错误 - FilePath(LineNumber,ColumnNumber): error ErrorCode: ErrorMessage
                var standardErrorMatch = System.Text.RegularExpressions.Regex.Match(cleanLine,
                    @"^(.+?)\((\d+),(\d+)\):\s*(error|warning)\s*([^:]+):\s*(.+)$",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                if (standardErrorMatch.Success && standardErrorMatch.Groups[4].Value.ToLower() == "error")
                {
                    var filePath = standardErrorMatch.Groups[1].Value.Trim();
                    // 清理文件路径中的编译器前缀（如 "3>", "2>" 等）
                    filePath = Regex.Replace(filePath, @"^\d+>", "").Trim();

                    var lineNumber = int.TryParse(standardErrorMatch.Groups[2].Value, out var lineNum) ? lineNum : 0;
                    var columnNumber = int.TryParse(standardErrorMatch.Groups[3].Value, out var col) ? col : 0;
                    var errorCode = standardErrorMatch.Groups[5].Value.Trim();
                    var message = standardErrorMatch.Groups[6].Value.Trim();

                    // 创建唯一标识符用于去重：文件路径+行号+列号+错误代码
                    var uniqueKey = $"{filePath}:{lineNumber}:{columnNumber}:{errorCode}";

                    // 如果这个错误已经存在，跳过
                    if (uniqueErrors.Contains(uniqueKey))
                    {
                        System.Diagnostics.Debug.WriteLine($"🔄 跳过重复错误: {errorCode} at {filePath}({lineNumber},{columnNumber})");
                        continue;
                    }

                    uniqueErrors.Add(uniqueKey);
                    // 标记原始行已处理
                    uniqueErrors.Add($"PROCESSED_LINE:{cleanLine}");

                    errors.Add(new CompilationError
                    {
                        ProjectId = projectId,
                        CompilationSessionId = sessionId,
                        Code = errorCode,
                        Message = message,
                        FilePath = filePath,
                        LineNumber = lineNumber,
                        ColumnNumber = columnNumber,
                        Severity = "Error",
                        ProjectType = "Backend",
                        ProjectName = Path.GetFileNameWithoutExtension(filePath),
                        ProjectPath = Path.GetDirectoryName(filePath),
                        CompilerVersion = "dotnet build",
                        TargetFramework = "net8.0",
                        BuildConfiguration = "Debug",
                        CompilationTime = DateTime.Now,
                        CreatedTime = DateTime.Now
                    });
                }
                // 格式2: NuGet错误 - ProjectPath : error ErrorCode: ErrorMessage [SolutionPath]
                else
                {
                    var nugetErrorMatch = System.Text.RegularExpressions.Regex.Match(cleanLine,
                        @"^(.+?\.csproj)\s*:\s*(error|warning)\s*([^:]+):\s*(.+?)\s*\[(.+?\.sln)\]$",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    if (nugetErrorMatch.Success && nugetErrorMatch.Groups[2].Value.ToLower() == "error")
                    {
                        var projectPath = nugetErrorMatch.Groups[1].Value.Trim();
                        var errorCode = nugetErrorMatch.Groups[3].Value.Trim();
                        var message = nugetErrorMatch.Groups[4].Value.Trim();
                        var solutionPath = nugetErrorMatch.Groups[5].Value.Trim();

                        // 创建唯一标识符用于去重
                        var uniqueKey = $"NUGET:{projectPath}:{errorCode}:{message}";

                        if (uniqueErrors.Contains(uniqueKey))
                        {
                            System.Diagnostics.Debug.WriteLine($"🔄 跳过重复的NuGet错误: {errorCode} in {projectPath}");
                            continue;
                        }

                        uniqueErrors.Add(uniqueKey);
                        uniqueErrors.Add($"PROCESSED_LINE:{cleanLine}");

                        errors.Add(new CompilationError
                        {
                            ProjectId = projectId,
                            CompilationSessionId = sessionId,
                            Code = errorCode,
                            Message = message,
                            FilePath = projectPath,
                            LineNumber = 0,
                            ColumnNumber = 0,
                            Severity = "Error",
                            ProjectType = "Backend",
                            ProjectName = Path.GetFileNameWithoutExtension(projectPath),
                            ProjectPath = Path.GetDirectoryName(projectPath),
                            CompilerVersion = "dotnet build",
                            TargetFramework = "net8.0",
                            BuildConfiguration = "Debug",
                            CompilationTime = DateTime.Now,
                            CreatedTime = DateTime.Now,
                            Remarks = $"Solution: {Path.GetFileName(solutionPath)}"
                        });

                        System.Diagnostics.Debug.WriteLine($"✅ 解析NuGet错误: {errorCode} - {message}");
                    }
                    // 跳过警告信息，只处理错误
                    else if (standardErrorMatch.Success && standardErrorMatch.Groups[4].Value.ToLower() == "warning")
                    {
                        // 不处理警告信息，直接跳过
                        System.Diagnostics.Debug.WriteLine($"⚠️ 跳过警告信息: {cleanLine}");
                        continue;
                    }
                    // 处理其他格式的错误信息
                    else
                    {
                        // 尝试解析其他常见的错误格式
                        if (TryParseOtherErrorFormats(cleanLine, projectId, sessionId, uniqueErrors, out var parsedError))
                        {
                            errors.Add(parsedError);
                            // 标记原始行已处理
                            uniqueErrors.Add($"PROCESSED_LINE:{cleanLine}");
                        }
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"📊 解析完成: 总共找到 {errors.Count} 个错误");
            return errors;
        }

        /// <summary>
        /// 运行npm build命令
        /// </summary>
        private async Task<string> RunNpmBuildAsync(string projectPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 在目录 {projectPath} 中执行npm run build");

                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c npm run build --no-color",
                    WorkingDirectory = projectPath,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    StandardOutputEncoding = Encoding.UTF8,
                    StandardErrorEncoding = Encoding.UTF8
                };

                // 设置环境变量禁用颜色输出
                processInfo.EnvironmentVariables["NO_COLOR"] = "1";
                processInfo.EnvironmentVariables["FORCE_COLOR"] = "0";

                using var process = new System.Diagnostics.Process { StartInfo = processInfo };
                var outputBuilder = new StringBuilder();
                var errorBuilder = new StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        outputBuilder.AppendLine(e.Data);
                        System.Diagnostics.Debug.WriteLine($"📄 {e.Data}");
                    }
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        errorBuilder.AppendLine(e.Data);
                        System.Diagnostics.Debug.WriteLine($"⚠️ {e.Data}");
                    }
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                await process.WaitForExitAsync();

                var output = outputBuilder.ToString();
                var error = errorBuilder.ToString();

                System.Diagnostics.Debug.WriteLine($"✅ npm build完成，退出代码: {process.ExitCode}");

                return output + "\n" + error;
            }
            catch (Exception ex)
            {
                var errorMessage = $"执行npm build失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"❌ {errorMessage}");
                return errorMessage;
            }
        }

        /// <summary>
        /// 移除 ANSI 颜色代码
        /// </summary>
        private string RemoveAnsiCodes(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // 移除 ANSI 转义序列
            var ansiPattern = @"\x1B\[[0-9;]*[mGKHF]|\x1B\[[0-9]*[ABCD]|\[[0-9;]*m";
            return Regex.Replace(input, ansiPattern, "");
        }

        /// <summary>
        /// 解析前端编译输出中的错误信息
        /// </summary>
        private List<CompilationError> ParseFrontendBuildOutput(string buildOutput, string projectPath, string sessionId, int? projectId = null)
        {
            var errors = new List<CompilationError>();

            if (string.IsNullOrEmpty(buildOutput))
                return errors;

            var lines = buildOutput.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                // 清理 ANSI 颜色代码
                var cleanLine = RemoveAnsiCodes(line).Trim();

                // 跳过空行和纯粹的构建信息行（如文件大小信息）
                if (string.IsNullOrWhiteSpace(cleanLine) ||
                    cleanLine.Contains("kB") ||
                    cleanLine.Contains("gzip:") ||
                    cleanLine.Contains("dist/") ||
                    cleanLine.Contains("built in"))
                {
                    continue;
                }

                // 检查是否包含真正的错误信息
                if (cleanLine.Contains("ERROR") || cleanLine.Contains("Error") ||
                    (cleanLine.Contains("error") && !cleanLine.Contains("0 errors")))
                {
                    errors.Add(new CompilationError
                    {
                        ProjectId = projectId,
                        CompilationSessionId = sessionId,
                        ProjectType = "Frontend",
                        Severity = "Error",
                        Code = "BUILD_ERROR",
                        Message = cleanLine,
                        FilePath = projectPath,
                        LineNumber = 0,
                        ColumnNumber = 0,
                        ProjectName = "Frontend",
                        ProjectPath = projectPath,
                        CompilerVersion = "TypeScript 5.0",
                        TargetFramework = "ES2022",
                        BuildConfiguration = "Development",
                        CompilationTime = DateTime.Now,
                        CreatedTime = DateTime.Now
                    });
                }
                else if (cleanLine.Contains("WARNING") || cleanLine.Contains("Warning") ||
                         (cleanLine.Contains("warning") && !cleanLine.Contains("0 warnings")))
                {
                    // 跳过前端警告信息，只处理错误
                    System.Diagnostics.Debug.WriteLine($"⚠️ 跳过前端警告信息: {cleanLine}");
                    continue;
                }
            }

            return errors;
        }

        /// <summary>
        /// 尝试解析其他格式的错误信息
        /// </summary>
        private bool TryParseOtherErrorFormats(string line, int? projectId, string sessionId, HashSet<string> uniqueErrors, out CompilationError parsedError)
        {
            parsedError = null;

            // 跳过明显不是错误的行
            if (string.IsNullOrWhiteSpace(line) ||
                line.Contains("Build succeeded") ||
                line.Contains("Build FAILED") ||
                line.Contains("Time Elapsed") ||
                line.Contains("Microsoft (R)") ||
                line.Contains("Copyright (C)") ||
                line.Contains("Determining projects") ||
                line.Contains("Restored ") ||
                line.Contains("0 Error(s)") ||
                line.Contains("0 Warning(s)"))
            {
                return false;
            }

            // 格式1: 简单的错误描述（如您提到的格式）
            // 例如: "未能找到类型或命名空间名"lpkklk"(是否缺少 using 指令或程序集引用?)"
            if (line.Contains("未能找到类型或命名空间名") ||
                line.Contains("应输入标识符") ||
                line.Contains("应输入 ;") ||
                line.Contains("应输入") ||
                line.Contains("语法错误") ||
                line.Contains("编译错误"))
            {
                var uniqueKey = $"GENERAL_ERROR:{line.Trim()}";
                if (uniqueErrors.Contains(uniqueKey))
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 跳过重复的通用错误: {line.Trim()}");
                    return false;
                }

                uniqueErrors.Add(uniqueKey);

                parsedError = new CompilationError
                {
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    Code = "GENERAL_ERROR",
                    Message = line.Trim(),
                    FilePath = "",
                    LineNumber = 0,
                    ColumnNumber = 0,
                    Severity = "Error",
                    ProjectType = "Backend",
                    ProjectName = "",
                    ProjectPath = "",
                    CompilerVersion = "dotnet build",
                    TargetFramework = "net8.0",
                    BuildConfiguration = "Debug",
                    CompilationTime = DateTime.Now,
                    CreatedTime = DateTime.Now
                };
                return true;
            }

            // 格式2: 包含错误代码但格式不标准的行
            // 例如: "Error CS1002: 应输入 ;"
            var errorCodeMatch = Regex.Match(line, @"Error\s+(CS\d+):\s*(.+)", RegexOptions.IgnoreCase);
            if (errorCodeMatch.Success)
            {
                var errorCode = errorCodeMatch.Groups[1].Value;
                var message = errorCodeMatch.Groups[2].Value.Trim();
                var uniqueKey = $"ERROR_CODE:{errorCode}:{message}";

                if (uniqueErrors.Contains(uniqueKey))
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 跳过重复的错误代码: {errorCode}");
                    return false;
                }

                uniqueErrors.Add(uniqueKey);

                parsedError = new CompilationError
                {
                    ProjectId = projectId,
                    CompilationSessionId = sessionId,
                    Code = errorCode,
                    Message = message,
                    FilePath = "",
                    LineNumber = 0,
                    ColumnNumber = 0,
                    Severity = "Error",
                    ProjectType = "Backend",
                    ProjectName = "",
                    ProjectPath = "",
                    CompilerVersion = "dotnet build",
                    TargetFramework = "net8.0",
                    BuildConfiguration = "Debug",
                    CompilationTime = DateTime.Now,
                    CreatedTime = DateTime.Now
                };
                return true;
            }

            return false;
        }
    }
}
