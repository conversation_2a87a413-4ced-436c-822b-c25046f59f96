-- =============================================
-- 项目步骤模板映射简化视图
-- 视图名称: vw_ProjectStepTemplateMapping
-- 功能: 简化显示项目步骤与UI模板序列的映射关系
-- 创建日期: 2024-12-25
-- 版本: 1.0
-- =============================================

USE ProjectManagementAI;
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepTemplateMapping')
BEGIN
    DROP VIEW vw_ProjectStepTemplateMapping;
    PRINT '已删除现有视图 vw_ProjectStepTemplateMapping';
END
GO

-- 创建简化映射视图
CREATE VIEW vw_ProjectStepTemplateMapping
AS
SELECT
    -- 项目基本信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,

    -- 步骤基本信息
    ds.Id AS StepId,
    ds.StepName,
    ds.StepType,
    ds.Status AS StepStatus,
    ds.Priority AS StepPriority,
    ds.StepOrder,
    ds.Progress AS StepProgress,

    -- 模板序列基本信息
    uts.Id AS SequenceId,
    uts.Name AS SequenceName,
    uts.Category AS SequenceCategory,
    uts.Description AS SequenceDescription,

    -- 关联状态
    stsa.Status AS AssociationStatus,
    stsa.Progress AS AssociationProgress,
    stsa.AppliedTime,

    -- 序列步骤数量
    (
        SELECT COUNT(*)
        FROM UIAutoMationTemplateSteps steps
        WHERE steps.SequenceId = uts.Id
        AND steps.IsDeleted = 0
        AND steps.IsActive = 1
    ) AS TemplateStepCount,

    -- 序列动作类型汇总
    (
        SELECT STUFF((
            SELECT DISTINCT ', ' + steps.ActionType
            FROM UIAutoMationTemplateSteps steps
            WHERE steps.SequenceId = uts.Id
            AND steps.IsDeleted = 0
            AND steps.IsActive = 1
            FOR XML PATH('')
        ), 1, 2, '')
    ) AS ActionTypes,

    -- 应用者
    appliedUser.RealName AS AppliedBy,

    -- 状态标识
    CASE
        WHEN stsa.Status = 'Running' THEN 'Running'
        WHEN stsa.Status = 'Completed' THEN 'Completed'
        WHEN stsa.Status = 'Failed' THEN 'Failed'
        WHEN stsa.Status = 'Active' THEN 'Active'
        ELSE 'Unknown'
    END AS StatusText,

    -- 进度百分比显示
    CAST(stsa.Progress AS NVARCHAR) + '%' AS ProgressDisplay,

    -- 关联时长（天数）
    DATEDIFF(DAY, stsa.AppliedTime, GETDATE()) AS DaysSinceApplied

FROM Projects p
    INNER JOIN DevelopmentSteps ds ON p.Id = ds.ProjectId
    INNER JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId
    INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
    LEFT JOIN Users appliedUser ON stsa.AppliedByUserId = appliedUser.Id

WHERE
    p.IsDeleted = 0
    AND ds.IsDeleted = 0
    AND stsa.IsDeleted = 0
    AND uts.IsDeleted = 0
    AND stsa.IsActive = 1;

GO

-- 添加视图注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'项目步骤模板映射简化视图 - 提供项目步骤与UI模板序列关联关系的简化视图，便于快速查看和统计',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'vw_ProjectStepTemplateMapping';

GO

-- 验证视图创建
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepTemplateMapping')
BEGIN
    PRINT '✅ 视图 vw_ProjectStepTemplateMapping 创建成功！';
    PRINT '';
    PRINT '📋 常用查询示例：';
    PRINT '';
    PRINT '-- 1. 查看所有项目的步骤模板使用情况';
    PRINT 'SELECT ProjectName, StepName, SequenceName, StatusIcon, ProgressDisplay';
    PRINT 'FROM vw_ProjectStepTemplateMapping';
    PRINT 'ORDER BY ProjectName, StepOrder;';
    PRINT '';
    PRINT '-- 2. 统计每个项目使用的模板序列数量';
    PRINT 'SELECT ProjectName, COUNT(DISTINCT SequenceId) as UniqueSequenceCount,';
    PRINT '       COUNT(*) as TotalAssociations';
    PRINT 'FROM vw_ProjectStepTemplateMapping';
    PRINT 'GROUP BY ProjectId, ProjectName';
    PRINT 'ORDER BY UniqueSequenceCount DESC;';
    PRINT '';
    PRINT '-- 3. 查看特定分类的模板序列使用情况';
    PRINT 'SELECT ProjectName, StepName, SequenceName, ActionTypes';
    PRINT 'FROM vw_ProjectStepTemplateMapping';
    PRINT 'WHERE SequenceCategory = ''CopilotChat自动化'';';
    PRINT '';
    PRINT '-- 4. 查看正在执行或已完成的关联';
    PRINT 'SELECT ProjectName, StepName, SequenceName, StatusIcon, ProgressDisplay';
    PRINT 'FROM vw_ProjectStepTemplateMapping';
    PRINT 'WHERE AssociationStatus IN (''Running'', ''Completed'')';
    PRINT 'ORDER BY AppliedTime DESC;';
    PRINT '';
    PRINT '-- 5. 统计模板序列的使用频率';
    PRINT 'SELECT SequenceName, SequenceCategory, COUNT(*) as UsageCount,';
    PRINT '       STRING_AGG(ProjectName, '', '') as UsedInProjects';
    PRINT 'FROM vw_ProjectStepTemplateMapping';
    PRINT 'GROUP BY SequenceId, SequenceName, SequenceCategory';
    PRINT 'ORDER BY UsageCount DESC;';

END
ELSE
BEGIN
    PRINT '❌ 视图创建失败！';
END

GO

PRINT '';
PRINT '脚本执行完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
