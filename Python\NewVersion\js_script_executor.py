#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript脚本执行器
将JavaScript风格的自动化脚本转换为Python执行
"""

import re
import json
import time
from typing import Dict, List, Any, Optional
from automation_manager import AutomationManager
from api_client import APIClient


class JSScriptExecutor:
    """JavaScript脚本执行器"""
    
    def __init__(self, automation_manager: AutomationManager):
        """初始化执行器"""
        self.automation_manager = automation_manager
        self.variables = {}  # 存储脚本变量
        self.current_step_data = None  # 存储当前执行步骤的数据

    def set_current_step_data(self, step_data: Dict[str, Any]):
        """设置当前执行步骤的数据，用于访问ReferenceImages等变量"""
        self.current_step_data = step_data
        print(f"🔧 设置当前步骤数据: {step_data.get('stepName', '未知步骤')}")

    def execute_script(self, js_code: str, ai_prompt: str = None) -> Dict[str, Any]:
        """
        执行JavaScript风格的自动化脚本

        Args:
            js_code: JavaScript代码
            ai_prompt: AI提示词，用于替换脚本中的{AIPrompt}变量

        Returns:
            执行结果
        """
        try:
            print(f"🚀 开始执行JavaScript脚本...")
            print(f"📝 脚本内容:\n{js_code}")

            # 设置AI Prompt
            self.current_ai_prompt = ai_prompt
            if ai_prompt:
                print(f"🤖 AI Prompt: {ai_prompt[:100]}{'...' if len(ai_prompt) > 100 else ''}")

            # 解析脚本结构
            script_structure = self._parse_js_script(js_code)
            init_steps = script_structure['init_steps']
            loop_steps = script_structure['loop_steps']
            has_loop = script_structure['has_loop']

            all_steps = init_steps + loop_steps  # 用于下载模板
            print(f"📋 解析出 {len(init_steps)} 个初始化步骤，{len(loop_steps)} 个循环步骤")

            # 预先下载脚本中用到的模板图片
            print("📥 检查并下载需要的模板图片...")
            self._ensure_script_templates(all_steps)

            # 激活VSCode窗口
            print("🔄 激活VSCode窗口...")
            print("💡 请确保VSCode窗口在前台，脚本将在3秒后开始执行...")

            # 给用户时间手动切换窗口
            import time
            for i in range(3, 0, -1):
                print(f"   ⏳ {i}秒后开始执行...")
                time.sleep(1)

            # 尝试自动激活VSCode窗口
            self._activate_vscode_window()

            # 执行初始化步骤
            if init_steps:
                print("\n🚀 执行初始化步骤...")
                for i, step in enumerate(init_steps):
                    print(f"\n🔄 初始化步骤 {i+1}/{len(init_steps)}: {step.get('description', '未知步骤')}")
                    result = self._execute_step(step)

                    if not result.get('success', False):
                        if result.get('skipped', False):
                            print(f"⏭️ 步骤 {i+1} 已跳过（模板不存在）")
                        else:
                            print(f"❌ 初始化步骤 {i+1} 执行失败: {result.get('error', '未知错误')}")
                            return {
                                'success': False,
                                'error': f"初始化步骤 {i+1} 执行失败"
                            }
                    else:
                        print(f"✅ 初始化步骤 {i+1} 执行成功")

            # 执行循环步骤
            if has_loop and loop_steps:
                print("\n🔄 开始执行循环步骤...")
                return self._execute_loop_steps(loop_steps, script_structure['loop_condition'])
            elif not has_loop:
                print(f"🎉 脚本执行完成，共执行 {len(init_steps)} 个步骤")
                return {
                    'success': True,
                    'total_steps': len(init_steps)
                }
            else:
                print("⚠️ 检测到循环但没有循环步骤")
                return {
                    'success': True,
                    'total_steps': len(init_steps)
                }
            
        except Exception as e:
            print(f"❌ 脚本执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _execute_with_loop(self, steps: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行带循环的脚本"""
        try:
            print("🔄 开始无限循环执行...")
            loop_count = 0
            max_loops = 500  # 防止真正的无限循环，设置最大循环次数

            while loop_count < max_loops:
                loop_count += 1
                print(f"\n🔄 开始第 {loop_count} 轮循环")

                # 执行所有步骤
                all_success = True
                for i, step in enumerate(steps):
                    print(f"\n🔄 循环 {loop_count} - 步骤 {i+1}/{len(steps)}: {step.get('description', '未知步骤')}")
                    result = self._execute_step(step)

                    if not result.get('success', False):
                        if result.get('skipped', False):
                            print(f"⏭️ 步骤 {i+1} 已跳过（模板不存在）")
                        else:
                            print(f"❌ 步骤 {i+1} 执行失败: {result.get('error', '未知错误')}")
                            all_success = False
                            break
                    else:
                        print(f"✅ 步骤 {i+1} 执行成功")

                if not all_success:
                    print(f"❌ 第 {loop_count} 轮循环执行失败，停止循环")
                    break

                print(f"✅ 第 {loop_count} 轮循环执行完成")

                # 循环间隔
                print("⏳ 等待 2 秒后开始下一轮循环...")
                time.sleep(2)

            if loop_count >= max_loops:
                print(f"⚠️ 达到最大循环次数 {max_loops}，停止执行")

            return {
                'success': True,
                'total_loops': loop_count,
                'message': f"循环执行完成，共执行 {loop_count} 轮"
            }

        except KeyboardInterrupt:
            print(f"\n⏹️ 用户中断执行，共完成 {loop_count} 轮循环")
            return {
                'success': True,
                'total_loops': loop_count,
                'message': f"用户中断，共执行 {loop_count} 轮"
            }
        except Exception as e:
            print(f"❌ 循环执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_loops': loop_count
            }

    def _execute_loop_steps(self, loop_steps: List[Dict[str, Any]], loop_condition: str) -> Dict[str, Any]:
        """执行循环步骤"""
        try:
            print(f"🔄 开始无限循环执行，循环条件: {loop_condition}")
            loop_count = 0
            max_loops = 500  # 防止真正的无限循环

            while loop_count < max_loops:
                loop_count += 1
                print(f"\n🔄 开始第 {loop_count} 轮循环")

                # 检查循环条件（简单实现，主要支持 true 和 shouldContinue）
                if loop_condition and loop_condition.strip() not in ['true', 'shouldContinue']:
                    print(f"⚠️ 复杂循环条件暂不支持: {loop_condition}")

                # 执行循环内的步骤
                should_break = False
                for i, step in enumerate(loop_steps):
                    print(f"\n🔄 循环 {loop_count} - 步骤 {i+1}/{len(loop_steps)}: {step.get('description', '未知步骤')}")
                    result = self._execute_step(step)

                    if not result.get('success', False):
                        if result.get('skipped', False):
                            print(f"⏭️ 步骤 {i+1} 已跳过（模板不存在）")
                        else:
                            print(f"❌ 循环步骤 {i+1} 执行失败: {result.get('error', '未知错误')}")
                            # 循环中的失败不一定要停止整个循环，继续执行
                            pass
                    else:
                        print(f"✅ 循环步骤 {i+1} 执行成功")

                        # 检查是否需要退出循环
                        if result.get('break_loop', False):
                            print(f"🔚 遇到break语句，退出循环")
                            should_break = True
                            break

                # 如果遇到break，退出整个循环
                if should_break:
                    print(f"🔚 循环在第 {loop_count} 轮被break语句终止")
                    break

                print(f"✅ 第 {loop_count} 轮循环执行完成")

                # 循环间隔
                print("⏳ 等待 1 秒后开始下一轮循环...")
                time.sleep(1)

            if loop_count >= max_loops:
                print(f"⚠️ 达到最大循环次数 {max_loops}，停止执行")

            return {
                'success': True,
                'total_loops': loop_count,
                'message': f"循环执行完成，共执行 {loop_count} 轮"
            }

        except KeyboardInterrupt:
            print(f"\n⏹️ 用户中断执行，共完成 {loop_count} 轮循环")
            return {
                'success': True,
                'total_loops': loop_count,
                'message': f"用户中断，共执行 {loop_count} 轮"
            }
        except Exception as e:
            print(f"❌ 循环执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_loops': loop_count
            }
    
    def _parse_js_script(self, js_code: str) -> Dict[str, Any]:
        """解析JavaScript脚本为结构化的执行计划"""
        lines = js_code.strip().split('\n')

        # 分析脚本结构
        script_structure = {
            'init_steps': [],      # 循环前的初始化步骤
            'loop_steps': [],      # 循环内的步骤
            'has_loop': False,     # 是否包含循环
            'loop_condition': None # 循环条件
        }

        current_section = 'init'  # 'init' 或 'loop'
        brace_level = 0
        in_if_block = False
        if_condition = None
        if_block_steps = []
        if_brace_level = 0  # 专门跟踪if块的大括号层级

        for line_num, line in enumerate(lines, 1):
            line = line.strip()

            # 跳过空行和注释
            if not line or line.startswith('//') or line.startswith('/*'):
                continue

            # 检测while循环开始
            if line.startswith('while (') or line.startswith('while('):
                script_structure['has_loop'] = True
                current_section = 'loop'
                # 提取循环条件
                condition_match = re.search(r'while\s*\(\s*(.+?)\s*\)', line)
                if condition_match:
                    script_structure['loop_condition'] = condition_match.group(1)
                continue

            # 检测loop语法开始 - loop(-1, [...])
            if line.startswith('loop(') or line.startswith('loop ('):
                script_structure['has_loop'] = True
                current_section = 'loop'
                # 提取循环次数，-1表示无限循环
                loop_match = re.search(r'loop\s*\(\s*(-?\d+)\s*,\s*\[', line)
                if loop_match:
                    loop_count = int(loop_match.group(1))
                    if loop_count == -1:
                        script_structure['loop_condition'] = 'infinite'
                    else:
                        script_structure['loop_condition'] = f'count:{loop_count}'
                print(f"🔍 检测到loop语法: {script_structure['loop_condition']}")
                continue

            # 处理大括号和方括号
            if '{' in line:
                brace_level += line.count('{')
            if '}' in line:
                brace_level -= line.count('}')
                # 如果大括号闭合且在循环中，可能是循环结束
                if brace_level == 0 and current_section == 'loop':
                    continue

            # 处理loop语法的结束 - ]);
            if current_section == 'loop' and (line.endswith(']);') or line.strip() == ']);'):
                print(f"🔍 检测到loop语法结束，包含 {len(script_structure['loop_steps'])} 个步骤")
                current_section = 'init'  # 回到初始化部分
                continue

            # 检测if语句开始
            if line.startswith('if ('):
                # 解析if条件
                step = self._parse_line(line, line_num)
                if step and step.get('type') == 'condition':
                    print(f"🔍 检测到if语句: {step.get('description', '未知条件')}")
                    in_if_block = True
                    if_condition = step
                    if_block_steps = []
                    if_brace_level = 0
                    # 如果这一行包含开始大括号，计数
                    if '{' in line:
                        if_brace_level += line.count('{')
                    continue

            # 在if块内处理大括号
            if in_if_block:
                # 计算大括号
                if '{' in line:
                    if_brace_level += line.count('{')
                if '}' in line:
                    if_brace_level -= line.count('}')

                # 如果大括号平衡了，说明if块结束
                if if_brace_level <= 0 and '}' in line:
                    print(f"🔍 检测到if语句结束，包含 {len(if_block_steps)} 个步骤")
                    # 创建条件分支步骤
                    conditional_step = {
                        'type': 'conditional_block',
                        'line_num': if_condition.get('line_num', line_num),
                        'description': f"条件分支: {if_condition.get('description', '未知条件')}",
                        'condition': if_condition,
                        'if_steps': if_block_steps
                    }

                    if current_section == 'init':
                        script_structure['init_steps'].append(conditional_step)
                    elif current_section == 'loop':
                        script_structure['loop_steps'].append(conditional_step)

                    print(f"🔍 条件分支已添加到 {current_section} 部分")

                    # 重置if状态
                    in_if_block = False
                    if_condition = None
                    if_block_steps = []
                    if_brace_level = 0
                    continue

            # 如果在if块内，将步骤添加到if_block_steps
            if in_if_block:
                step = self._parse_line(line, line_num)
                if step:
                    print(f"🔍 添加if块内步骤: {step.get('description', '未知步骤')}")
                    if_block_steps.append(step)
                continue

            # 处理单独的开始大括号
            if line == '{' and not in_if_block:
                # 如果前面有if条件等待开始大括号，这里应该已经处理了
                continue
            elif line == '{' and in_if_block:
                # if块的开始大括号，跳过
                continue

            # 跳过单独的大括号行和方括号行
            if line in ['}', '[', ']', ']);']:
                continue

            # 解析语句
            step = self._parse_line(line, line_num)
            if step:
                if current_section == 'init':
                    script_structure['init_steps'].append(step)
                elif current_section == 'loop':
                    script_structure['loop_steps'].append(step)

        return script_structure
    
    def _parse_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """解析单行代码"""
        
        # 移除末尾的分号
        line = line.rstrip(';')
        
        # if语句处理
        if line.startswith('if ('):
            return self._parse_if_statement(line, line_num)
        
        # clickImage函数
        elif 'clickImage(' in line:
            return self._parse_click_image(line, line_num)
        
        # input函数
        elif line.startswith('input('):
            return self._parse_input(line, line_num)
        
        # imageExists函数
        elif 'imageExists(' in line:
            return self._parse_image_exists(line, line_num)
        
        # wait函数
        elif line.startswith('wait('):
            return self._parse_wait(line, line_num)
        
        # screenshot函数
        elif line.startswith('screenshot('):
            return self._parse_screenshot(line, line_num)

        # userConfirm函数
        elif 'userConfirm(' in line:
            return self._parse_user_confirm(line, line_num)

        # pasteImages函数
        elif 'pasteImages(' in line:
            return self._parse_paste_images(line, line_num)

        # waitTime函数
        elif 'waitTime(' in line:
            return self._parse_wait_time(line, line_num)

        # inputText函数
        elif 'inputText(' in line:
            return self._parse_input_text(line, line_num)

        # break语句
        elif line.strip() == 'break;' or line.strip() == 'break':
            return self._parse_break(line, line_num)

        # click函数（简化版）
        elif line.startswith('click('):
            return self._parse_click(line, line_num)

        else:
            print(f"⚠️ 第{line_num}行: 不支持的语句类型: {line}")
            return None
    
    def _parse_if_statement(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析if语句"""
        # 支持 if (!imageExists('template'))
        match = re.match(r'if\s*\(\s*(!?)\s*imageExists\([\'"]([^\'"]+)[\'"]\)\s*\)\s*\{?', line)
        if match:
            is_negated = bool(match.group(1))
            template_name = match.group(2)

            return {
                'type': 'condition',
                'line_num': line_num,
                'description': f"条件判断: {'不' if is_negated else ''}存在图像 {template_name}",
                'condition_type': 'image_exists',
                'template_name': template_name,
                'is_negated': is_negated
            }

        # 支持 if (userConfirm('message'))
        match = re.match(r'if\s*\(\s*userConfirm\([\'"]([^\'"]*)[\'"](?:,\s*\{[^}]*\})?\)\s*\)\s*\{?', line)
        if match:
            message = match.group(1)

            return {
                'type': 'condition',
                'line_num': line_num,
                'description': f"用户确认条件: {message[:30]}{'...' if len(message) > 30 else ''}",
                'condition_type': 'user_confirm',
                'message': message
            }

        return None
    
    def _parse_click_image(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析clickImage函数"""
        # 匹配 clickImage('template_name', { confidence: 0.7 })
        match = re.match(r'clickImage\([\'"]([^\'"]+)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            template_name = match.group(1)
            options_str = match.group(2) or ''
            
            # 解析选项
            options = {}
            if options_str:
                # 简单解析confidence参数
                conf_match = re.search(r'confidence:\s*([\d.]+)', options_str)
                if conf_match:
                    options['confidence'] = float(conf_match.group(1))
            
            return {
                'type': 'click_image',
                'line_num': line_num,
                'description': f"点击图像: {template_name}",
                'template_name': template_name,
                'confidence': options.get('confidence', 0.8),
                'timeout': options.get('timeout', 5)
            }
        
        return None
    
    def _parse_input(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析input函数"""
        # 匹配 input('text', { clear: true }) 或 input('{AIPrompt}', { clear: true })
        match = re.match(r'input\([\'"]([^\'"]*)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            text = match.group(1)
            options_str = match.group(2) or ''

            # 解析选项
            options = {}
            if 'clear: true' in options_str:
                options['clear'] = True

            # 检查是否使用AI Prompt变量
            use_ai_prompt = text == '{AIPrompt}' or text == '{aiPrompt}' or text == '{ai_prompt}'

            return {
                'type': 'input',
                'line_num': line_num,
                'description': f"输入文本: {'AI Prompt' if use_ai_prompt else text[:20]}{'...' if not use_ai_prompt and len(text) > 20 else ''}",
                'text': text,
                'clear': options.get('clear', False),
                'use_ai_prompt': use_ai_prompt
            }

        return None
    
    def _parse_image_exists(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析imageExists函数"""
        match = re.match(r'imageExists\([\'"]([^\'"]+)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            template_name = match.group(1)
            options_str = match.group(2) or ''
            
            options = {}
            if options_str:
                conf_match = re.search(r'confidence:\s*([\d.]+)', options_str)
                if conf_match:
                    options['confidence'] = float(conf_match.group(1))
            
            return {
                'type': 'image_exists',
                'line_num': line_num,
                'description': f"检查图像存在: {template_name}",
                'template_name': template_name,
                'confidence': options.get('confidence', 0.8)
            }
        
        return None
    
    def _parse_wait(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析wait函数"""
        match = re.match(r'wait\((\d+)\)', line)
        if match:
            duration = int(match.group(1)) / 1000.0  # 转换为秒
            
            return {
                'type': 'wait',
                'line_num': line_num,
                'description': f"等待 {duration} 秒",
                'duration': duration
            }
        
        return None
    
    def _parse_screenshot(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析screenshot函数"""
        match = re.match(r'screenshot\([\'"]([^\'"]+)[\'"]\)', line)
        if match:
            filename = match.group(1)
            
            return {
                'type': 'screenshot',
                'line_num': line_num,
                'description': f"截图: {filename}",
                'filename': filename
            }
        
        return None

    def _parse_user_confirm(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析userConfirm函数"""
        # 匹配 userConfirm('message')
        match = re.match(r'userConfirm\([\'"]([^\'"]*)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            message = match.group(1)

            return {
                'type': 'userconfirm',
                'line_num': line_num,
                'description': f"用户确认: {message[:30]}{'...' if len(message) > 30 else ''}",
                'message': message
            }

        return None

    def _parse_paste_images(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析pasteImages函数"""
        # 匹配 pasteImages('{ReferenceImages}') 或 pasteImages('image_path')
        match = re.match(r'pasteImages\([\'"]([^\'"]*)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            reference_images = match.group(1)

            return {
                'type': 'pasteimages',
                'line_num': line_num,
                'description': f"粘贴图片: {reference_images}",
                'reference_images': reference_images
            }

        return None

    def _parse_wait_time(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析waitTime函数"""
        # 匹配 waitTime(1000)
        match = re.match(r'waitTime\((\d+)\)', line)
        if match:
            duration_ms = int(match.group(1))
            duration = duration_ms / 1000.0  # 转换为秒

            return {
                'type': 'wait',
                'line_num': line_num,
                'description': f"等待 {duration} 秒",
                'duration': duration
            }

        return None

    def _parse_input_text(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析inputText函数"""
        # 匹配 inputText('{AIPrompt}') 或 inputText('text')
        match = re.match(r'inputText\([\'"]([^\'"]*)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            text = match.group(1)
            options_str = match.group(2) or ''

            # 解析选项
            options = {}
            if 'clear: true' in options_str:
                options['clear'] = True

            # 检查是否使用AI Prompt变量
            use_ai_prompt = text == '{AIPrompt}' or text == '{aiPrompt}' or text == '{ai_prompt}'

            return {
                'type': 'input',
                'line_num': line_num,
                'description': f"输入文本: {'AI Prompt' if use_ai_prompt else text[:20]}{'...' if not use_ai_prompt and len(text) > 20 else ''}",
                'text': text,
                'clear': options.get('clear', False),
                'use_ai_prompt': use_ai_prompt
            }

        return None

    def _parse_break(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析break语句"""
        return {
            'type': 'break',
            'line_num': line_num,
            'description': '退出循环'
        }

    def _parse_click(self, line: str, line_num: int) -> Dict[str, Any]:
        """解析简化的click函数"""
        # 匹配 click('template_name')
        match = re.match(r'click\([\'"]([^\'"]+)[\'"](?:,\s*\{([^}]*)\})?\)', line)
        if match:
            template_name = match.group(1)
            options_str = match.group(2) or ''

            # 解析选项
            options = {}
            if options_str:
                # 简单解析confidence参数
                conf_match = re.search(r'confidence:\s*([\d.]+)', options_str)
                if conf_match:
                    options['confidence'] = float(conf_match.group(1))

            return {
                'type': 'click_image',
                'line_num': line_num,
                'description': f"点击图像: {template_name}",
                'template_name': template_name,
                'confidence': options.get('confidence', 0.8),
                'timeout': options.get('timeout', 5)
            }

        return None

    def _execute_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个步骤"""
        step_type = step.get('type')
        
        try:
            if step_type == 'click_image':
                return self._execute_click_image(step)
            elif step_type == 'input':
                return self._execute_input(step)
            elif step_type == 'image_exists':
                return self._execute_image_exists(step)
            elif step_type == 'wait':
                return self._execute_wait(step)
            elif step_type == 'screenshot':
                return self._execute_screenshot(step)
            elif step_type == 'condition':
                return self._execute_condition(step)
            elif step_type == 'userconfirm':
                return self._execute_user_confirm(step)
            elif step_type == 'pasteimages':
                return self._execute_paste_images(step)
            elif step_type == 'break':
                return self._execute_break(step)
            elif step_type == 'conditional_block':
                return self._execute_conditional_block(step)
            else:
                return {
                    'success': False,
                    'error': f"不支持的步骤类型: {step_type}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_click_image(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行点击图像"""
        template_name = step['template_name']
        confidence = step.get('confidence', 0.8)
        timeout = step.get('timeout', 5)

        try:
            # 获取模板图片的实际路径
            image_path = self._get_template_image_path(template_name)
            if not image_path:
                # 模板不存在时，静默返回失败
                print(f"💡 模板 '{template_name}' 不存在，跳过点击操作")
                return {
                    'success': False,
                    'step_type': 'click_image',
                    'template_name': template_name,
                    'skipped': True  # 标记为跳过
                }

            print(f"🖼️ 使用模板图片: {image_path}")

            # 使用UI操作器直接点击模板
            # 处理EnhancedAutomationManager的情况
            if hasattr(self.automation_manager, 'base_manager') and self.automation_manager.base_manager:
                ui_actions = self.automation_manager.base_manager.ui_actions
            elif hasattr(self.automation_manager, 'ui_actions'):
                ui_actions = self.automation_manager.ui_actions
            else:
                print(f"❌ UI操作器不可用")
                return {
                    'success': False,
                    'error': "UI操作器不可用",
                    'step_type': 'click_image',
                    'template_name': template_name
                }

            if hasattr(ui_actions, 'click_template'):
                success = ui_actions.click_template(
                    image_path,
                    confidence=confidence,
                    timeout=timeout
                )
            else:
                print(f"❌ UI操作器不支持click_template方法")
                success = False

            return {
                'success': success,
                'step_type': 'click_image',
                'template_name': template_name,
                'image_path': image_path
            }

        except Exception as e:
            print(f"❌ 点击图像失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'step_type': 'click_image',
                'template_name': template_name
            }
    
    def _execute_input(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行输入文本"""
        text = step['text']
        clear = step.get('clear', False)
        use_ai_prompt = step.get('use_ai_prompt', False)

        # 如果使用AI Prompt变量，则使用当前的AI Prompt
        if use_ai_prompt and hasattr(self, 'current_ai_prompt') and self.current_ai_prompt:
            actual_text = self.current_ai_prompt
            print(f"🤖 使用AI Prompt作为输入文本: {actual_text[:50]}{'...' if len(actual_text) > 50 else ''}")
        else:
            actual_text = text
            if use_ai_prompt:
                print(f"⚠️ 脚本要求使用AI Prompt，但当前没有可用的AI Prompt，使用原始文本: {text}")

        # 获取UI操作器
        if hasattr(self.automation_manager, 'base_manager') and self.automation_manager.base_manager:
            ui_actions = self.automation_manager.base_manager.ui_actions
        elif hasattr(self.automation_manager, 'ui_actions'):
            ui_actions = self.automation_manager.ui_actions
        else:
            return {
                'success': False,
                'error': "UI操作器不可用",
                'step_type': 'input',
                'text': actual_text
            }

        # 如果需要清空，先发送Ctrl+A然后输入
        if clear:
            # 先选择全部
            if hasattr(ui_actions, 'key_press'):
                ui_actions.key_press('ctrl+a')
                time.sleep(0.1)

        # 输入文本
        if hasattr(ui_actions, 'type_text'):
            success = ui_actions.type_text(actual_text)
        else:
            print(f"❌ UI操作器不支持type_text方法")
            success = False
        
        return {
            'success': success,
            'step_type': 'input',
            'text': text
        }
    
    def _execute_image_exists(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行图像存在检查"""
        template_name = step['template_name']
        confidence = step.get('confidence', 0.8)

        # 获取模板图片的实际路径
        image_path = self._get_template_image_path(template_name)
        if not image_path:
            # 模板不存在时，不报错，直接返回不存在
            print(f"💡 模板 '{template_name}' 不存在，返回 false")
            return {
                'success': True,  # 执行成功，只是结果是不存在
                'exists': False   # 图像不存在
            }

        print(f"🖼️ 检查模板图片: {image_path}")
        
        # 检查图像是否存在
        # 处理EnhancedAutomationManager的情况
        if hasattr(self.automation_manager, 'base_manager') and self.automation_manager.base_manager:
            screenshot_manager = self.automation_manager.base_manager.screenshot_manager
        elif hasattr(self.automation_manager, 'screenshot_manager'):
            screenshot_manager = self.automation_manager.screenshot_manager
        else:
            return {
                'success': False,
                'error': f"截图管理器不可用",
                'exists': False
            }

        location = screenshot_manager.find_template_on_screen(image_path, confidence)

        exists = location is not None

        if exists:
            print(f"✅ 找到图像: {template_name} (置信度 >= {confidence})")
        else:
            # 获取实际的最高置信度用于调试
            try:
                import cv2
                import numpy as np

                # 截取当前屏幕
                screenshot = screenshot_manager.take_screenshot()
                template = cv2.imread(image_path)

                if screenshot is not None and template is not None:
                    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, _ = cv2.minMaxLoc(result)
                    print(f"❌ 未找到图像: {template_name} (最高置信度: {max_val:.3f}, 需要: {confidence})")

                    # 如果置信度接近阈值，给出提示
                    if max_val > confidence * 0.7:  # 如果达到阈值的70%
                        print(f"   💡 提示: 置信度接近阈值，建议降低confidence或更新模板图片")
                        print(f"   💡 建议: 将confidence从{confidence}降低到{max_val + 0.05:.2f}")
                else:
                    print(f"❌ 未找到图像: {template_name} (无法获取置信度信息)")
            except Exception as e:
                print(f"❌ 未找到图像: {template_name} (调试信息获取失败: {e})")

        return {
            'success': True,
            'step_type': 'image_exists',
            'template_name': template_name,
            'exists': exists,
            'location': location
        }
    
    def _execute_wait(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行等待"""
        duration = step['duration']
        
        print(f"⏳ 等待 {duration} 秒...")
        time.sleep(duration)
        
        return {
            'success': True,
            'step_type': 'wait',
            'duration': duration
        }
    
    def _execute_screenshot(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行截图"""
        filename = step['filename']
        
        # 执行截图
        # 处理EnhancedAutomationManager的情况
        if hasattr(self.automation_manager, 'base_manager') and self.automation_manager.base_manager:
            screenshot_manager = self.automation_manager.base_manager.screenshot_manager
        elif hasattr(self.automation_manager, 'screenshot_manager'):
            screenshot_manager = self.automation_manager.screenshot_manager
        else:
            return {
                'success': False,
                'error': "截图管理器不可用",
                'step_type': 'screenshot',
                'filename': filename
            }

        success = screenshot_manager.take_screenshot(filename)
        
        return {
            'success': success,
            'step_type': 'screenshot',
            'filename': filename
        }
    
    def _execute_condition(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行条件判断"""
        if step['condition_type'] == 'image_exists':
            # 检查图像是否存在
            check_step = {
                'type': 'image_exists',
                'template_name': step['template_name'],
                'confidence': 0.8
            }
            
            result = self._execute_image_exists(check_step)
            exists = result.get('exists', False)
            
            # 应用否定逻辑
            if step.get('is_negated', False):
                condition_met = not exists
            else:
                condition_met = exists
            
            return {
                'success': True,
                'step_type': 'condition',
                'condition_met': condition_met,
                'image_exists': exists,
                'is_negated': step.get('is_negated', False)
            }
        elif step['condition_type'] == 'user_confirm':
            # 执行用户确认
            confirm_step = {
                'message': step.get('message', '是否继续执行下一步？'),
                'line_num': step.get('line_num', 0)
            }

            result = self._execute_user_confirm(confirm_step)
            condition_met = result.get('user_confirmed', False)

            return {
                'success': True,
                'step_type': 'condition',
                'condition_met': condition_met,
                'user_confirmed': condition_met,
                'message': result.get('message', '')
            }

        return {
            'success': False,
            'error': f"不支持的条件类型: {step.get('condition_type')}"
        }

    def _get_template_image_path(self, template_name: str) -> str:
        """获取模板图片的实际路径"""
        try:
            import os
            import json

            # 方法1: 使用模板映射文件
            mapping_file = "templates/template_mapping.json"
            if os.path.exists(mapping_file):
                try:
                    with open(mapping_file, 'r', encoding='utf-8') as f:
                        mapping = json.load(f)

                    if template_name in mapping:
                        mapped_filename = mapping[template_name]
                        local_path = f"templates/{mapped_filename}"
                        if os.path.exists(local_path):
                            print(f"📁 使用映射模板路径: {local_path}")
                            return local_path
                        else:
                            print(f"⚠️ 映射文件存在但图片文件不存在: {local_path}")
                    else:
                        # 静默处理，不打印错误信息
                        pass

                except Exception as e:
                    print(f"⚠️ 读取模板映射失败: {e}")

            # 方法2: 查找安全文件名版本（空格转下划线）
            possible_extensions = ['.png', '.jpg', '.jpeg', '.bmp']
            safe_name = template_name.replace(' ', '_').replace('/', '_').replace('\\', '_')

            for ext in possible_extensions:
                local_path = f"templates/{safe_name}{ext}"
                if os.path.exists(local_path):
                    print(f"📁 使用安全文件名路径: {local_path}")
                    return local_path

            # 方法3: 直接查找本地文件（原始名称）
            for ext in possible_extensions:
                local_path = f"templates/{template_name}{ext}"
                if os.path.exists(local_path):
                    print(f"📁 使用直接路径: {local_path}")
                    return local_path

            # 方法4: 列出templates目录中的所有文件，尝试模糊匹配
            templates_dir = "templates"
            if os.path.exists(templates_dir):
                files = [f for f in os.listdir(templates_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]

                # 尝试包含匹配
                for file in files:
                    file_base = os.path.splitext(file)[0]
                    if template_name.lower() in file_base.lower() or file_base.lower() in template_name.lower():
                        local_path = f"templates/{file}"
                        print(f"📁 使用模糊匹配路径: {local_path}")
                        return local_path

                # 静默处理，不打印详细错误信息
                pass
            else:
                # 静默处理，不打印错误信息
                pass

            return None

        except Exception as e:
            print(f"❌ 获取模板路径失败: {e}")
            return None

    def _ensure_script_templates(self, steps: List[Dict]) -> None:
        """确保脚本中用到的模板图片都已下载"""
        try:
            # 收集脚本中用到的所有模板名称
            template_names = set()

            for step in steps:
                step_type = step.get('type', '')

                if step_type == 'click_image':
                    template_name = step.get('template_name')
                    if template_name:
                        template_names.add(template_name)

                elif step_type == 'condition':
                    condition_type = step.get('condition_type')
                    if condition_type == 'image_exists':
                        template_name = step.get('template_name')
                        if template_name:
                            template_names.add(template_name)

            if not template_names:
                print("   ℹ️ 脚本中没有使用模板图片")
                return

            print(f"   📋 脚本中使用了 {len(template_names)} 个模板:")
            for name in template_names:
                print(f"      - {name}")

            # 获取API客户端
            api_client = None
            if hasattr(self.automation_manager, 'base_manager') and self.automation_manager.base_manager:
                api_client = self.automation_manager.base_manager.api_client
            elif hasattr(self.automation_manager, 'api_client'):
                api_client = self.automation_manager.api_client

            if not api_client:
                print("   ⚠️ 无法获取API客户端，跳过模板下载")
                return

            # 获取所有可用的模板
            try:
                response = api_client._make_request('GET', '/api/custom-templates')
                if not response:
                    print("   ❌ 无法获取模板列表")
                    return

                templates = []
                if isinstance(response, list):
                    templates = response
                elif isinstance(response, dict):
                    if 'items' in response:
                        templates = response['items']
                    elif 'data' in response:
                        templates = response['data']

                # 为每个需要的模板下载图片
                for template_name in template_names:
                    self._download_template_if_needed(template_name, templates, api_client)

            except Exception as e:
                print(f"   ❌ 获取模板列表失败: {e}")

        except Exception as e:
            print(f"❌ 确保模板图片失败: {e}")

    def _download_template_if_needed(self, template_name: str, templates: List[Dict], api_client) -> None:
        """如果需要，下载指定的模板图片"""
        try:
            import os
            import requests
            from urllib.parse import urljoin

            # 检查本地是否已有该模板图片
            local_path = self._get_template_image_path(template_name)
            if local_path and os.path.exists(local_path):
                print(f"   ✅ 模板图片已存在: {template_name}")
                return

            # 在模板列表中查找该模板
            target_template = None
            for template in templates:
                template_template_name = template.get('name') or template.get('Name', '')
                if template_template_name == template_name:
                    target_template = template
                    break

            if not target_template:
                print(f"   ❌ 未找到模板: {template_name}")
                return

            # 获取主模板图片路径 - 修复字段名称匹配问题
            file_path = (target_template.get('FilePath') or
                        target_template.get('filePath') or
                        target_template.get('filepath'))
            region_file_path = (target_template.get('RegionFilePath') or
                               target_template.get('regionFilePath') or
                               target_template.get('regionfilepath'))

            if not file_path:
                print(f"   ❌ 模板没有文件路径: {template_name}")
                return

            print(f"   📥 下载模板图片: {template_name}")

            # 确保templates目录存在
            os.makedirs("templates", exist_ok=True)

            # 下载主模板图片
            success = self._download_single_image(file_path, template_name, "", api_client)

            # 如果有区域图片，也下载区域图片
            if region_file_path and region_file_path.strip():
                print(f"   📥 下载区域图片: {template_name} (区域)")
                region_success = self._download_single_image(region_file_path, template_name, "_region", api_client)
                if not region_success:
                    print(f"   ⚠️ 区域图片下载失败，但主图片可用: {template_name}")

            if not success:
                print(f"   ❌ 主模板图片下载失败: {template_name}")

        except Exception as e:
            print(f"   ❌ 下载模板图片失败: {template_name} - {e}")

    def _download_single_image(self, file_path: str, template_name: str, suffix: str, api_client) -> bool:
        """下载单个图片文件"""
        try:
            import os
            import requests
            from urllib.parse import urljoin

            # 生成本地文件名
            file_extension = os.path.splitext(file_path)[1] or '.png'
            safe_name = template_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
            local_filename = f"{safe_name}{suffix}{file_extension}"
            local_path = os.path.join("templates", local_filename)

            # 下载图片 - 使用API客户端的base_url
            base_url = api_client.base_url.rstrip('/') + '/'
            image_url = urljoin(base_url, file_path)

            headers = {}
            if hasattr(api_client, 'token') and api_client.token:
                headers['Authorization'] = f'Bearer {api_client.token}'

            response = requests.get(image_url, headers=headers, verify=False, timeout=30)

            if response.status_code == 200:
                with open(local_path, 'wb') as f:
                    f.write(response.content)

                file_size = len(response.content)
                suffix_text = f" ({suffix.strip('_')})" if suffix else ""
                print(f"   ✅ 下载成功: {template_name}{suffix_text} (大小: {file_size} 字节)")

                # 更新模板映射（只为主图片更新映射）
                if not suffix:
                    self._update_template_mapping(template_name, local_filename)

                return True
            else:
                suffix_text = f" ({suffix.strip('_')})" if suffix else ""
                print(f"   ❌ 下载失败: {template_name}{suffix_text} (状态码: {response.status_code})")
                return False

        except Exception as e:
            suffix_text = f" ({suffix.strip('_')})" if suffix else ""
            print(f"   ❌ 下载图片失败: {template_name}{suffix_text} - {e}")
            return False

    def _update_template_mapping(self, template_name: str, local_filename: str) -> None:
        """更新模板映射文件"""
        try:
            import json
            import os

            mapping_file = "templates/template_mapping.json"
            mapping = {}

            # 读取现有映射
            if os.path.exists(mapping_file):
                try:
                    with open(mapping_file, 'r', encoding='utf-8') as f:
                        mapping = json.load(f)
                except Exception as e:
                    print(f"   ⚠️ 读取映射文件失败: {e}")

            # 更新映射
            mapping[template_name] = local_filename

            # 保存映射
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(mapping, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"   ⚠️ 更新模板映射失败: {e}")

    def _activate_vscode_window(self) -> None:
        """激活VSCode窗口"""
        try:
            import time
            import pyautogui

            # 方法1: 尝试使用Alt+Tab切换到VSCode
            print("   🔄 尝试激活VSCode窗口...")

            # 使用pyautogui查找VSCode窗口
            try:
                import pygetwindow as gw

                # 查找VSCode窗口
                vscode_windows = []
                for window in gw.getAllWindows():
                    if 'Visual Studio Code' in window.title or 'VSCode' in window.title:
                        vscode_windows.append(window)

                if vscode_windows:
                    # 激活第一个VSCode窗口
                    vscode_window = vscode_windows[0]
                    vscode_window.activate()
                    print(f"   ✅ 已激活VSCode窗口: {vscode_window.title}")
                    time.sleep(1)  # 等待窗口切换完成
                    return
                else:
                    print("   ⚠️ 未找到VSCode窗口")

            except ImportError:
                print("   ⚠️ pygetwindow未安装，使用备用方法")
            except Exception as e:
                print(f"   ⚠️ 窗口激活失败: {e}")

            # 方法2: 使用键盘快捷键尝试切换
            print("   🔄 使用Alt+Tab尝试切换窗口...")
            pyautogui.hotkey('alt', 'tab')
            time.sleep(0.5)

            # 可以多按几次Tab来找到VSCode
            for i in range(3):
                pyautogui.press('tab')
                time.sleep(0.2)

            pyautogui.press('enter')
            time.sleep(1)

            print("   ✅ 窗口切换完成")

        except Exception as e:
            print(f"   ❌ 激活VSCode窗口失败: {e}")
            print("   💡 请手动切换到VSCode窗口后重新执行脚本")

    def _ensure_template_image_exists(self, remote_path: str, template_name: str, api_client) -> str:
        """确保模板图片存在，如果不存在则下载"""
        try:
            import os
            import requests
            from urllib.parse import urljoin

            # 检查远程路径是否存在
            if not os.path.exists(remote_path):
                # 尝试从API服务器下载图片 - 使用API客户端的base_url
                base_url = api_client.base_url.rstrip('/') + '/'
                image_url = urljoin(base_url, remote_path)

                print(f"📥 尝试下载模板图片: {image_url}")

                # 创建本地文件路径
                file_extension = os.path.splitext(remote_path)[1] or '.png'
                local_filename = f"{template_name}{file_extension}"
                local_path = os.path.join("templates", local_filename)

                # 确保templates目录存在
                os.makedirs("templates", exist_ok=True)

                # 下载图片
                headers = {}
                if hasattr(api_client, 'token') and api_client.token:
                    headers['Authorization'] = f'Bearer {api_client.token}'

                response = requests.get(image_url, headers=headers, verify=False, timeout=10)

                if response.status_code == 200:
                    with open(local_path, 'wb') as f:
                        f.write(response.content)

                    print(f"✅ 模板图片下载成功: {local_path}")
                    return local_path
                else:
                    print(f"❌ 下载失败，状态码: {response.status_code}")
            else:
                print(f"✅ 模板图片已存在: {remote_path}")
                return remote_path

        except Exception as e:
            print(f"❌ 下载模板图片失败: {e}")

        return None

    def _execute_user_confirm(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行用户确认步骤"""
        try:
            message = step.get('message', '是否继续执行下一步？')

            # 调用automation_manager的用户确认功能
            confirm_step = {
                'id': f"userconfirm_{step.get('line_num', 0)}",
                'actionType': 'userConfirm'
            }

            parameters = {
                'message': message
            }

            result = self.automation_manager._execute_user_confirm_action(confirm_step, parameters)

            return {
                'success': result.get('success', False),
                'step_type': 'userconfirm',
                'user_confirmed': result.get('result', False),
                'message': result.get('message', '')
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"用户确认失败: {str(e)}"
            }

    def _execute_paste_images(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行图片粘贴步骤"""
        try:
            reference_images = step.get('reference_images', '')

            print(f"🖼️ 执行图片粘贴: {reference_images}")

            # 调用automation_manager的图片粘贴功能
            paste_step = {
                'id': f"pasteimages_{step.get('line_num', 0)}",
                'actionType': 'pasteImages'
            }

            # 解析参考图片
            image_urls = []

            if reference_images == '{ReferenceImages}':
                # 使用当前步骤的参考图片 - 从执行上下文获取
                if hasattr(self, 'current_step_data') and self.current_step_data:
                    step_reference_images = self.current_step_data.get('referenceImages') or self.current_step_data.get('ReferenceImages')
                    if step_reference_images:
                        # 解析参考图片JSON
                        image_urls = self.automation_manager.parse_reference_images(step_reference_images)
                        print(f"📥 从当前步骤获取到 {len(image_urls)} 个参考图片")
                    else:
                        print("⚠️ 当前步骤没有参考图片数据")
                else:
                    print("⚠️ 没有当前步骤数据，无法获取参考图片")
            else:
                # 直接使用提供的图片路径
                image_urls = [reference_images]
                print(f"📥 使用指定的图片路径: {reference_images}")

            if not image_urls:
                return {
                    'success': True,
                    'step_type': 'pasteimages',
                    'message': '没有参考图片需要粘贴'
                }

            parameters = {
                'reference_images': image_urls
            }

            result = self.automation_manager._execute_paste_images_action(paste_step, parameters)

            return {
                'success': result.get('success', False),
                'step_type': 'pasteimages',
                'message': result.get('message', ''),
                'images_count': len(image_urls)
            }

        except Exception as e:
            print(f"❌ 图片粘贴执行失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f"图片粘贴失败: {str(e)}"
            }

    def _execute_break(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行break语句"""
        return {
            'success': True,
            'step_type': 'break',
            'break_loop': True,  # 标记需要退出循环
            'message': '退出循环'
        }

    def _execute_conditional_block(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """执行条件分支块"""
        try:
            condition = step.get('condition')
            if_steps = step.get('if_steps', [])

            print(f"🔀 执行条件分支: {condition.get('description', '未知条件')}")

            # 先执行条件判断
            condition_result = self._execute_condition(condition)

            if not condition_result.get('success', False):
                return {
                    'success': False,
                    'error': f"条件判断失败: {condition_result.get('error', '未知错误')}"
                }

            condition_met = condition_result.get('condition_met', False)
            print(f"🔍 条件结果: {condition_met}")

            if condition_met:
                # 条件为真，执行if块内的步骤
                print(f"✅ 条件为真，执行 {len(if_steps)} 个步骤")

                for i, if_step in enumerate(if_steps):
                    print(f"  🔄 执行步骤 {i+1}/{len(if_steps)}: {if_step.get('description', '未知步骤')}")

                    result = self._execute_step(if_step)

                    if not result.get('success', False):
                        if result.get('skipped', False):
                            print(f"  ⏭️ 步骤 {i+1} 已跳过")
                        else:
                            print(f"  ❌ 步骤 {i+1} 执行失败: {result.get('error', '未知错误')}")
                            return {
                                'success': False,
                                'error': f"条件分支内步骤 {i+1} 执行失败: {result.get('error', '未知错误')}"
                            }
                    else:
                        print(f"  ✅ 步骤 {i+1} 执行成功")

                        # 检查是否需要退出循环
                        if result.get('break_loop', False):
                            return {
                                'success': True,
                                'step_type': 'conditional_block',
                                'condition_met': True,
                                'executed_steps': i + 1,
                                'break_loop': True,
                                'message': '条件分支内遇到break语句'
                            }

                return {
                    'success': True,
                    'step_type': 'conditional_block',
                    'condition_met': True,
                    'executed_steps': len(if_steps),
                    'message': f'条件为真，执行了 {len(if_steps)} 个步骤'
                }
            else:
                # 条件为假，跳过if块
                print(f"❌ 条件为假，跳过 {len(if_steps)} 个步骤")
                return {
                    'success': True,
                    'step_type': 'conditional_block',
                    'condition_met': False,
                    'executed_steps': 0,
                    'message': '条件为假，跳过if块'
                }

        except Exception as e:
            print(f"❌ 条件分支执行失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f"条件分支执行失败: {str(e)}"
            }


def test_js_executor():
    """测试JavaScript执行器"""
    # 您的脚本
    js_script = """
if (!imageExists('Copilot Chat面板')) {
  // 图片存在时执行
  clickImage('Copilot Chat图标', { confidence: 0.7 });
} 
clickImage('Copilot Chat新建回话按钮', { confidence: 0.7 });

clickImage('Copilot Chat消息输入框', { confidence: 0.7 });

input('text', { clear: true });

clickImage('Copilot Chat发送信息按钮', { confidence: 0.7 });
"""
    
    try:
        # 初始化
        api_client = APIClient()
        automation_manager = AutomationManager(api_client)
        executor = JSScriptExecutor(automation_manager)
        
        # 执行脚本
        result = executor.execute_script(js_script)
        
        print(f"\n📊 执行结果:")
        print(f"成功: {result['success']}")
        if result['success']:
            print(f"总步骤数: {result['total_steps']}")
        else:
            print(f"错误: {result.get('error')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_js_executor()
