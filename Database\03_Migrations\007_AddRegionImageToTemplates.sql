-- =============================================
-- 迁移脚本: 为自定义模板表添加区域图片字段
-- 版本: 007
-- 创建时间: 2025-06-26
-- 描述: 为CustomUIAutoMationTemplates表添加区域图片相关字段，用于提高模板匹配精度
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始为自定义模板表添加区域图片字段...';

-- =============================================
-- 1. 检查并添加区域图片路径字段
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'RegionFilePath')
BEGIN
    PRINT '添加RegionFilePath字段...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD RegionFilePath nvarchar(500) NULL;
    
    PRINT 'RegionFilePath字段添加成功';
END
ELSE
BEGIN
    PRINT 'RegionFilePath字段已存在，跳过添加';
END

-- =============================================
-- 2. 检查并添加区域图片描述字段
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'RegionDescription')
BEGIN
    PRINT '添加RegionDescription字段...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD RegionDescription nvarchar(200) NULL;
    
    PRINT 'RegionDescription字段添加成功';
END
ELSE
BEGIN
    PRINT 'RegionDescription字段已存在，跳过添加';
END

-- =============================================
-- 3. 检查并添加区域图片置信度字段
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'RegionConfidence')
BEGIN
    PRINT '添加RegionConfidence字段...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD RegionConfidence decimal(3,2) NULL DEFAULT 0.7;
    
    PRINT 'RegionConfidence字段添加成功';
END
ELSE
BEGIN
    PRINT 'RegionConfidence字段已存在，跳过添加';
END

-- =============================================
-- 4. 检查并添加区域扩展像素字段
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'RegionExpand')
BEGIN
    PRINT '添加RegionExpand字段...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD RegionExpand int NULL DEFAULT 10;
    
    PRINT 'RegionExpand字段添加成功';
END
ELSE
BEGIN
    PRINT 'RegionExpand字段已存在，跳过添加';
END

-- =============================================
-- 5. 检查并添加是否启用区域匹配字段
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'UseRegionMatching')
BEGIN
    PRINT '添加UseRegionMatching字段...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD UseRegionMatching bit NOT NULL DEFAULT 0;
    
    PRINT 'UseRegionMatching字段添加成功';
END
ELSE
BEGIN
    PRINT 'UseRegionMatching字段已存在，跳过添加';
END

-- =============================================
-- 6. 添加约束检查
-- =============================================
-- 检查区域置信度约束
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_CustomUIAutoMationTemplates_RegionConfidence')
BEGIN
    PRINT '添加区域置信度约束...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD CONSTRAINT CK_CustomUIAutoMationTemplates_RegionConfidence 
    CHECK (RegionConfidence IS NULL OR (RegionConfidence >= 0.1 AND RegionConfidence <= 1.0));
    
    PRINT '区域置信度约束添加成功';
END

-- 检查区域扩展像素约束
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_CustomUIAutoMationTemplates_RegionExpand')
BEGIN
    PRINT '添加区域扩展像素约束...';
    
    ALTER TABLE CustomUIAutoMationTemplates
    ADD CONSTRAINT CK_CustomUIAutoMationTemplates_RegionExpand 
    CHECK (RegionExpand IS NULL OR (RegionExpand >= 0 AND RegionExpand <= 200));
    
    PRINT '区域扩展像素约束添加成功';
END

-- =============================================
-- 7. 更新现有数据的默认值
-- =============================================
PRINT '更新现有数据的默认值...';

UPDATE CustomUIAutoMationTemplates 
SET 
    RegionConfidence = 0.7,
    RegionExpand = 10,
    UseRegionMatching = 0
WHERE 
    RegionConfidence IS NULL 
    OR RegionExpand IS NULL;

PRINT '现有数据默认值更新完成';

-- =============================================
-- 8. 添加索引以提高查询性能
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CustomUIAutoMationTemplates_UseRegionMatching')
BEGIN
    PRINT '添加区域匹配索引...';
    
    CREATE INDEX IX_CustomUIAutoMationTemplates_UseRegionMatching 
    ON CustomUIAutoMationTemplates (UseRegionMatching)
    WHERE UseRegionMatching = 1;
    
    PRINT '区域匹配索引添加成功';
END

-- =============================================
-- 9. 添加字段注释
-- =============================================
PRINT '添加字段注释...';

-- 为新字段添加扩展属性（注释）
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'区域图片文件路径，用于限定模板匹配的区域范围',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'CustomUIAutoMationTemplates',
    @level2type = N'COLUMN', @level2name = N'RegionFilePath';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'区域图片的描述信息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'CustomUIAutoMationTemplates',
    @level2type = N'COLUMN', @level2name = N'RegionDescription';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'区域图片匹配的置信度阈值（0.1-1.0）',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'CustomUIAutoMationTemplates',
    @level2type = N'COLUMN', @level2name = N'RegionConfidence';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'区域边界扩展的像素数量（0-200）',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'CustomUIAutoMationTemplates',
    @level2type = N'COLUMN', @level2name = N'RegionExpand';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'是否启用区域匹配功能',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'CustomUIAutoMationTemplates',
    @level2type = N'COLUMN', @level2name = N'UseRegionMatching';

PRINT '字段注释添加完成';

-- =============================================
-- 10. 验证迁移结果
-- =============================================
PRINT '验证迁移结果...';

-- 检查新字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'CustomUIAutoMationTemplates' 
    AND COLUMN_NAME IN ('RegionFilePath', 'RegionDescription', 'RegionConfidence', 'RegionExpand', 'UseRegionMatching')
ORDER BY COLUMN_NAME;

-- 统计现有模板数量
DECLARE @TemplateCount INT;
SELECT @TemplateCount = COUNT(*) FROM CustomUIAutoMationTemplates WHERE IsDeleted = 0;
PRINT '当前模板总数: ' + CAST(@TemplateCount AS NVARCHAR(10));

PRINT '区域图片字段迁移完成！';
PRINT '新增字段说明:';
PRINT '- RegionFilePath: 区域图片文件路径（可选）';
PRINT '- RegionDescription: 区域图片描述';
PRINT '- RegionConfidence: 区域匹配置信度（默认0.7）';
PRINT '- RegionExpand: 区域扩展像素（默认10）';
PRINT '- UseRegionMatching: 是否启用区域匹配（默认false）';
