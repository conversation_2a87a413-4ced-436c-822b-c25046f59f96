-- =============================================
-- 初始数据脚本: 自定义UI自动化模板初始数据
-- 版本: 006
-- 创建时间: 2025-06-23
-- 描述: 为自定义UI自动化模板系统插入初始数据
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始插入自定义UI自动化模板初始数据...';

-- =============================================
-- 1. 插入默认的CopilotChat自动化模板序列
-- =============================================

-- 检查是否已存在数据
IF NOT EXISTS (SELECT 1 FROM UIAutoMationTemplateSequences WHERE Name = 'CopilotChat基础对话流程')
BEGIN
    PRINT '插入CopilotChat自动化模板序列...';
    
    -- 插入CopilotChat基础对话流程
    INSERT INTO UIAutoMationTemplateSequences (
        Name, Description, Category, Tags, Notes, IsActive, CreatedTime
    ) VALUES (
        'CopilotChat基础对话流程',
        '包含打开Copilot Chat、发送消息、等待回复的完整流程',
        'CopilotChat自动化',
        'copilot,chat,基础,对话',
        '这是CopilotChat自动化的基础流程模板，包含最常用的操作步骤',
        1,
        GETDATE()
    );
    
    -- 插入CopilotChat代码生成流程
    INSERT INTO UIAutoMationTemplateSequences (
        Name, Description, Category, Tags, Notes, IsActive, CreatedTime
    ) VALUES (
        'CopilotChat代码生成流程',
        '专门用于代码生成的CopilotChat自动化流程',
        'CopilotChat自动化',
        'copilot,chat,代码生成,编程',
        '针对代码生成任务优化的自动化流程，包含代码插入和验证步骤',
        1,
        GETDATE()
    );
    
    -- 插入CopilotChat代码解释流程
    INSERT INTO UIAutoMationTemplateSequences (
        Name, Description, Category, Tags, Notes, IsActive, CreatedTime
    ) VALUES (
        'CopilotChat代码解释流程',
        '用于代码解释和分析的CopilotChat自动化流程',
        'CopilotChat自动化',
        'copilot,chat,代码解释,分析',
        '专门用于代码解释和分析任务的自动化流程',
        1,
        GETDATE()
    );
    
    -- 插入CopilotChat错误调试流程
    INSERT INTO UIAutoMationTemplateSequences (
        Name, Description, Category, Tags, Notes, IsActive, CreatedTime
    ) VALUES (
        'CopilotChat错误调试流程',
        '用于错误调试和问题解决的CopilotChat自动化流程',
        'CopilotChat自动化',
        'copilot,chat,调试,错误,问题解决',
        '专门用于错误调试和问题解决的自动化流程',
        1,
        GETDATE()
    );
    
    PRINT 'CopilotChat自动化模板序列插入完成';
END
ELSE
BEGIN
    PRINT 'CopilotChat自动化模板序列已存在，跳过插入';
END
GO

-- =============================================
-- 2. 插入默认的模板步骤（基础对话流程）
-- =============================================

DECLARE @SequenceId INT;
SELECT @SequenceId = Id FROM UIAutoMationTemplateSequences WHERE Name = 'CopilotChat基础对话流程';

IF @SequenceId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM UIAutoMationTemplateSteps WHERE SequenceId = @SequenceId)
BEGIN
    PRINT '插入CopilotChat基础对话流程步骤...';
    
    -- 步骤1: 打开Copilot Chat面板
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 1, 'click', '打开Copilot Chat面板',
        '{"template_name": "copilot_chat_icon", "wait_after": 2}',
        10, 3, 1, GETDATE()
    );
    
    -- 步骤2: 等待面板加载
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 2, 'wait', '等待Copilot Chat面板完全加载',
        '{"template_name": "copilot_chat_input", "timeout": 5}',
        15, 2, 1, GETDATE()
    );
    
    -- 步骤3: 点击输入框
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 3, 'click', '点击Copilot Chat输入框',
        '{"template_name": "copilot_chat_input", "wait_after": 1}',
        10, 3, 1, GETDATE()
    );
    
    -- 步骤4: 输入消息
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 4, 'input', '输入要发送的消息',
        '{"text": "{message}", "clear_first": true}',
        5, 2, 1, GETDATE()
    );
    
    -- 步骤5: 发送消息
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 5, 'click', '点击发送按钮',
        '{"template_name": "copilot_chat_send", "wait_after": 2}',
        10, 3, 1, GETDATE()
    );
    
    -- 步骤6: 等待回复
    INSERT INTO UIAutoMationTemplateSteps (
        SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
    ) VALUES (
        @SequenceId, 6, 'wait', '等待Copilot回复',
        '{"template_name": "copilot_response_complete", "timeout": 30}',
        60, 1, 1, GETDATE()
    );
    
    PRINT 'CopilotChat基础对话流程步骤插入完成';
END
ELSE
BEGIN
    PRINT 'CopilotChat基础对话流程步骤已存在或序列不存在，跳过插入';
END
GO

-- =============================================
-- 3. 插入一些示例自定义模板
-- =============================================

IF NOT EXISTS (SELECT 1 FROM CustomUIAutoMationTemplates WHERE Name = 'Copilot Chat图标')
BEGIN
    PRINT '插入示例自定义模板...';
    
    -- Copilot Chat图标模板
    INSERT INTO CustomUIAutoMationTemplates (
        Name, Description, Category, FilePath, Confidence, Tags, Notes, CreatedTime
    ) VALUES (
        'Copilot Chat图标',
        'VSCode中的Copilot Chat图标，用于打开聊天面板',
        'CopilotChat自动化',
        'templates/custom/copilot_chat_icon.png',
        0.8,
        'copilot,图标,chat',
        '这是VSCode侧边栏中的Copilot Chat图标模板',
        GETDATE()
    );
    
    -- Copilot Chat输入框模板
    INSERT INTO CustomUIAutoMationTemplates (
        Name, Description, Category, FilePath, Confidence, Tags, Notes, CreatedTime
    ) VALUES (
        'Copilot Chat输入框',
        'Copilot Chat面板中的消息输入框',
        'CopilotChat自动化',
        'templates/custom/copilot_chat_input.png',
        0.7,
        'copilot,输入框,chat',
        '用于识别和点击Copilot Chat的消息输入框',
        GETDATE()
    );
    
    -- Copilot Chat发送按钮模板
    INSERT INTO CustomUIAutoMationTemplates (
        Name, Description, Category, FilePath, Confidence, Tags, Notes, CreatedTime
    ) VALUES (
        'Copilot Chat发送按钮',
        'Copilot Chat面板中的发送消息按钮',
        'CopilotChat自动化',
        'templates/custom/copilot_chat_send.png',
        0.8,
        'copilot,按钮,发送',
        '用于发送消息到Copilot Chat的按钮模板',
        GETDATE()
    );
    
    PRINT '示例自定义模板插入完成';
END
ELSE
BEGIN
    PRINT '示例自定义模板已存在，跳过插入';
END
GO

PRINT '==============================================';
PRINT '自定义UI自动化模板初始数据插入完成！';
PRINT '==============================================';
