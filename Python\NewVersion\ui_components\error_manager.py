#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误管理器
负责后端和前端编译错误的管理和显示
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, List, Optional
from datetime import datetime
import subprocess
import json
import os


class ErrorManager:
    """错误管理器类"""
    
    def __init__(self, parent_ui, api_client):
        """
        初始化错误管理器
        
        Args:
            parent_ui: 父UI对象
            api_client: API客户端
        """
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # 后端编译错误相关
        self.auto_refresh_timer = None
        self.auto_refresh_var = tk.BooleanVar(value=True)
        self.refresh_interval_var = tk.StringVar(value="30")
        
        # 前端编译错误相关
        self.frontend_auto_refresh_timer = None
        self.frontend_auto_refresh_var = tk.BooleanVar(value=True)
        self.frontend_refresh_interval_var = tk.StringVar(value="30")
        
        # UI组件引用
        self.backend_error_tree = None
        self.frontend_error_tree = None
        self.backend_error_count_var = None
        self.frontend_error_count_var = None
        self.send_to_ai_button = None
        self.frontend_send_to_ai_button = None
        self.error_count_label = None
        self.frontend_error_count_label = None
        self.compile_status_label = None
        self.frontend_compile_status_label = None
        self.last_update_label = None
        self.frontend_last_update_label = None
        self.status_label = None
    
    def set_ui_components(self, backend_error_tree, frontend_error_tree,
                         backend_error_count_var, frontend_error_count_var,
                         send_to_ai_button, frontend_send_to_ai_button,
                         error_count_label, frontend_error_count_label,
                         compile_status_label, frontend_compile_status_label,
                         last_update_label, frontend_last_update_label,
                         status_label, auto_refresh_var, refresh_interval_var,
                         frontend_auto_refresh_var, frontend_refresh_interval_var):
        """设置UI组件引用"""
        self.backend_error_tree = backend_error_tree
        self.frontend_error_tree = frontend_error_tree
        self.backend_error_count_var = backend_error_count_var
        self.frontend_error_count_var = frontend_error_count_var
        self.send_to_ai_button = send_to_ai_button
        self.frontend_send_to_ai_button = frontend_send_to_ai_button
        self.error_count_label = error_count_label
        self.frontend_error_count_label = frontend_error_count_label
        self.compile_status_label = compile_status_label
        self.frontend_compile_status_label = frontend_compile_status_label
        self.last_update_label = last_update_label
        self.frontend_last_update_label = frontend_last_update_label
        self.status_label = status_label
        self.auto_refresh_var = auto_refresh_var
        self.refresh_interval_var = refresh_interval_var
        self.frontend_auto_refresh_var = frontend_auto_refresh_var
        self.frontend_refresh_interval_var = frontend_refresh_interval_var

        # 绑定事件
        if self.backend_error_tree:
            self.backend_error_tree.bind('<Double-1>', self.on_backend_error_double_click)
        if self.frontend_error_tree:
            self.frontend_error_tree.bind('<Double-1>', self.on_frontend_error_double_click)

        # 自动启动自动刷新功能（如果默认启用）
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
        if self.frontend_auto_refresh_var.get():
            self.start_frontend_auto_refresh()
    
    def start_auto_refresh(self):
        """启动后端错误自动刷新"""
        if self.auto_refresh_var.get():
            self.schedule_next_refresh()

    def schedule_next_refresh(self):
        """安排下次刷新"""
        if self.auto_refresh_var.get():
            interval = int(self.refresh_interval_var.get()) * 1000  # 转换为毫秒
            self.auto_refresh_timer = self.parent_ui.parent.after(interval, self.auto_refresh_callback)

    def auto_refresh_callback(self):
        """自动刷新回调"""
        if self.auto_refresh_var.get():
            self.refresh_backend_errors()
            self.schedule_next_refresh()

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        if self.auto_refresh_timer:
            self.parent_ui.parent.after_cancel(self.auto_refresh_timer)
            self.auto_refresh_timer = None
        
        if self.auto_refresh_var.get():
            self.start_auto_refresh()

    def refresh_backend_errors(self):
        """刷新后端编译错误"""
        def fetch_errors():
            try:
                errors = self.get_backend_compilation_errors()
                self.parent_ui.parent.after(0, lambda: self.update_backend_errors(errors))
            except Exception as e:
                error_msg = f"获取后端编译错误失败: {str(e)}"
                self.parent_ui.parent.after(0, lambda: self.handle_backend_error_fetch_error(error_msg))

        threading.Thread(target=fetch_errors, daemon=True).start()

    def get_backend_compilation_errors(self):
        """获取后端编译错误信息"""
        try:
            # 从API获取编译错误
            if not self.api_client:
                print("❌ API客户端未初始化")
                return []

            # 检查是否选择了项目
            selected_project = self.parent_ui.selected_project
            if not selected_project:
                print("❌ 没有选中项目，无法获取编译错误")
                return []

            # 获取当前选中的项目ID
            project_id = selected_project.get('id')


            result = self.api_client.get_compilation_errors(
                project_type="Backend",
                project_id=project_id,
                page_size=1000
            )

            if result and 'items' in result:
                errors = []
                for item in result['items']:
                    errors.append({
                        'severity': item.get('severity', 'Error').upper(),
                        'file': item.get('filePath', ''),
                        'line': item.get('lineNumber', 0),
                        'code': item.get('code', ''),
                        'message': item.get('message', ''),
                        'timestamp': item.get('compilationTime', datetime.now().isoformat()),
                        'project_id': item.get('projectId', project_id)
                    })
                print(f"✅ 从API获取到 {len(errors)} 个后端编译错误")
                return errors
            else:
                print("⚠️ API返回数据格式错误或无数据")
                return []

        except Exception as e:
            print(f"❌ 从API获取后端编译错误失败: {e}")
            return []



    def update_backend_errors(self, errors):
        """更新后端错误显示"""
        try:
            # 清空现有错误
            for item in self.backend_error_tree.get_children():
                self.backend_error_tree.delete(item)

            if not errors:
                self.show_empty_backend_errors_message()
                self.compile_status_label.config(text="✅ 无错误", foreground="green")
                self.error_count_label.config(text="错误: 0 | 警告: 0")
                self.send_to_ai_button.config(state="disabled")
                self.last_update_label.config(text=f"更新: {datetime.now().strftime('%H:%M:%S')}")
                return

            # 统计错误和警告
            error_count = sum(1 for e in errors if e['severity'].upper() == 'ERROR')
            warning_count = sum(1 for e in errors if e['severity'].upper() == 'WARNING')

            # 添加错误到列表
            for error in errors:
                # 获取文件名（用于显示）
                from pathlib import Path
                file_name = Path(error['file']).name if error['file'] else ''

                # 插入到树形控件
                self.backend_error_tree.insert('', 'end', values=(
                    error['severity'].upper(),
                    file_name,
                    error['line'],
                    error['code'],
                    error['message'][:80] + '...' if len(error['message']) > 80 else error['message'],
                    error.get('timestamp', '')
                ), tags=(error['severity'].lower(),))

            # 设置颜色
            self.backend_error_tree.tag_configure('error', foreground='red')
            self.backend_error_tree.tag_configure('warning', foreground='orange')

            # 更新统计信息
            self.error_count_label.config(text=f"错误: {error_count} | 警告: {warning_count}")

            # 更新编译状态
            if error_count > 0:
                self.compile_status_label.config(text="❌ 有错误", foreground="red")
                self.send_to_ai_button.config(state="normal")
            elif warning_count > 0:
                self.compile_status_label.config(text="⚠️ 仅警告", foreground="orange")
                self.send_to_ai_button.config(state="normal")
            else:
                self.compile_status_label.config(text="✅ 无错误", foreground="green")
                self.send_to_ai_button.config(state="disabled")

            # 更新最后更新时间
            self.last_update_label.config(text=f"更新: {datetime.now().strftime('%H:%M:%S')}")

            print(f"✅ 后端编译错误更新完成: {error_count}个错误, {warning_count}个警告")

        except Exception as e:
            print(f"更新后端错误显示失败: {e}")

    def show_empty_backend_errors_message(self):
        """显示空后端编译错误消息"""
        # 清空错误列表
        for item in self.backend_error_tree.get_children():
            self.backend_error_tree.delete(item)

        # 插入提示信息
        self.backend_error_tree.insert('', 'end', values=('', '', '', '', '自动获取后端编译错误信息...', ''))

    def handle_backend_error_fetch_error(self, error_msg):
        """处理后端错误获取失败"""
        print(f"后端错误获取失败: {error_msg}")
        self.compile_status_label.config(text="检查失败", foreground="red")
        self.last_update_label.config(text=f"失败: {datetime.now().strftime('%H:%M:%S')}")

    def clear_backend_errors(self):
        """清空后端错误列表"""
        for item in self.backend_error_tree.get_children():
            self.backend_error_tree.delete(item)
        
        self.error_count_label.config(text="错误: 0 | 警告: 0")
        self.compile_status_label.config(text="未检查", foreground="gray")
        self.send_to_ai_button.config(state="disabled")

    def on_backend_error_double_click(self, event):
        """后端错误双击事件"""
        selection = self.backend_error_tree.selection()
        if selection:
            item = selection[0]
            values = self.backend_error_tree.item(item, 'values')
            if values and len(values) >= 5:
                file_name = values[1]
                line_num = values[2]
                message = values[4]
                
                # 这里可以实现打开文件到指定行的功能
                print(f"双击错误: {file_name}:{line_num} - {message}")

    def send_errors_to_ai(self):
        """发送后端错误给AI修复"""
        try:
            # 获取指定数量的错误
            error_count = int(self.backend_error_count_var.get())
            errors = []
            
            count = 0
            for item in self.backend_error_tree.get_children():
                if count >= error_count:
                    break
                
                values = self.backend_error_tree.item(item, 'values')
                if values and values[0].lower() == 'error':  # 只发送错误，不发送警告
                    errors.append({
                        'file': values[1],
                        'line': values[2],
                        'code': values[3],
                        'message': values[4]
                    })
                    count += 1

            if not errors:
                messagebox.showwarning("警告", "没有找到可发送的错误")
                return

            # 调用AI修复功能
            self.send_backend_errors_to_ai_for_fix(errors)

        except ValueError:
            messagebox.showerror("错误", "请输入有效的错误条数")
        except Exception as e:
            messagebox.showerror("异常", f"发送错误给AI时发生异常：{str(e)}")

    def send_backend_errors_to_ai_for_fix(self, errors):
        """发送后端错误给AI进行修复"""
        try:
            # 构建错误文本
            error_text = "后端编译错误：\n\n"
            for i, error in enumerate(errors, 1):
                error_text += f"{i}. 文件: {error['file']}\n"
                error_text += f"   行号: {error['line']}\n"
                error_text += f"   错误代码: {error['code']}\n"
                error_text += f"   错误信息: {error['message']}\n\n"

            # 调用AI修复脚本
            self.execute_ai_fix_script(error_text, "backend")

        except Exception as e:
            messagebox.showerror("异常", f"发送后端错误给AI修复时发生异常：{str(e)}")

    def execute_ai_fix_script(self, error_text, error_type):
        """执行AI修复脚本"""
        try:
            # 获取自定义Prompt内容
            custom_prompt = ""
            if hasattr(self.parent_ui, 'get_custom_prompt_content'):
                custom_prompt = self.parent_ui.get_custom_prompt_content()

            # 构建完整的AI提示词
            if custom_prompt:
                ai_prompt = custom_prompt.replace("{AIPrompt}", error_text)
            else:
                ai_prompt = f"请帮助修复以下{error_type}编译错误：\n\n{error_text}"

            print(f"🤖 准备发送给AI修复的内容:")
            print(f"📝 错误类型: {error_type}")
            print(f"📝 AI提示词: {ai_prompt[:200]}...")

            def run_ai_fix_script():
                try:
                    # 这里应该调用实际的AI修复脚本
                    # 可以调用VSCode Copilot或其他AI服务
                    
                    # 示例：调用Python脚本发送给Copilot
                    script_path = os.path.join(os.path.dirname(__file__), "..", "send_to_copilot.py")
                    
                    if os.path.exists(script_path):
                        result = subprocess.run(
                            ["python", script_path, ai_prompt],
                            capture_output=True,
                            text=True,
                            timeout=30
                        )
                        
                        if result.returncode == 0:
                            self.parent_ui.parent.after(0, lambda: 
                                messagebox.showinfo("成功", f"{error_type}错误已发送给AI修复"))
                        else:
                            error_msg = result.stderr or "未知错误"
                            self.parent_ui.parent.after(0, lambda: 
                                messagebox.showerror("失败", f"AI修复脚本执行失败：{error_msg}"))
                    else:
                        self.parent_ui.parent.after(0, lambda: 
                            messagebox.showwarning("警告", "未找到AI修复脚本"))

                except subprocess.TimeoutExpired:
                    self.parent_ui.parent.after(0, lambda: 
                        messagebox.showerror("超时", "AI修复脚本执行超时"))
                except Exception as e:
                    self.parent_ui.parent.after(0, lambda: 
                        messagebox.showerror("异常", f"执行AI修复脚本时发生异常：{str(e)}"))

            # 在后台线程中执行
            threading.Thread(target=run_ai_fix_script, daemon=True).start()

        except Exception as e:
            messagebox.showerror("异常", f"准备AI修复脚本时发生异常：{str(e)}")

    # 前端错误管理方法（类似后端，但针对前端项目）
    def start_frontend_auto_refresh(self):
        """启动前端错误自动刷新"""
        if self.frontend_auto_refresh_var.get():
            self.schedule_next_frontend_refresh()

    def schedule_next_frontend_refresh(self):
        """安排下次前端刷新"""
        if self.frontend_auto_refresh_var.get():
            interval = int(self.frontend_refresh_interval_var.get()) * 1000
            self.frontend_auto_refresh_timer = self.parent_ui.parent.after(interval, self.frontend_auto_refresh_callback)

    def frontend_auto_refresh_callback(self):
        """前端自动刷新回调"""
        if self.frontend_auto_refresh_var.get():
            self.refresh_frontend_errors()
            self.schedule_next_frontend_refresh()

    def toggle_frontend_auto_refresh(self):
        """切换前端自动刷新状态"""
        if self.frontend_auto_refresh_timer:
            self.parent_ui.parent.after_cancel(self.frontend_auto_refresh_timer)
            self.frontend_auto_refresh_timer = None
        
        if self.frontend_auto_refresh_var.get():
            self.start_frontend_auto_refresh()

    def refresh_frontend_errors(self):
        """刷新前端编译错误"""
        def fetch_errors():
            try:
                errors = self.get_frontend_compilation_errors()
                self.parent_ui.parent.after(0, lambda: self.update_frontend_errors(errors))
            except Exception as e:
                error_msg = f"获取前端编译错误失败: {str(e)}"
                self.parent_ui.parent.after(0, lambda: self.handle_frontend_error_fetch_error(error_msg))

        threading.Thread(target=fetch_errors, daemon=True).start()

    def get_frontend_compilation_errors(self):
        """获取前端编译错误信息"""
        try:
            # 从API获取编译错误
            if not self.api_client:
                print("❌ API客户端未初始化")
                return []

            # 检查是否选择了项目
            selected_project = self.parent_ui.selected_project
            if not selected_project:
                return []

            # 获取当前选中的项目ID
            project_id = selected_project.get('id')

            result = self.api_client.get_compilation_errors(
                project_type="Frontend",
                project_id=project_id,
                page_size=1000
            )

            if result and 'items' in result:
                errors = []
                for item in result['items']:
                    errors.append({
                        'severity': item.get('severity', 'Error').upper(),
                        'file': item.get('filePath', ''),
                        'line': item.get('lineNumber', 0),
                        'code': item.get('code', ''),
                        'message': item.get('message', ''),
                        'timestamp': item.get('compilationTime', datetime.now().isoformat()),
                        'project_id': item.get('projectId', project_id)
                    })
                return errors
            else:
                print("⚠️ API返回数据格式错误或无数据")
                return []

        except Exception as e:
            print(f"❌ 从API获取前端编译错误失败: {e}")
            return []



    def update_frontend_errors(self, errors):
        """更新前端编译错误列表"""
        try:
            print(f"🔄 开始更新前端错误列表，接收到 {len(errors) if errors else 0} 个错误")

            # 清空现有错误
            for item in self.frontend_error_tree.get_children():
                self.frontend_error_tree.delete(item)

            if not errors:
                print("ℹ️ 前端错误列表为空，显示空消息")
                self.show_empty_frontend_errors_message()
                self.frontend_compile_status_label.config(text="✅ 无错误", foreground="green")
                self.frontend_error_count_label.config(text="错误: 0 | 警告: 0")
                self.frontend_send_to_ai_button.config(state="disabled")
                self.frontend_last_update_label.config(text=f"更新: {datetime.now().strftime('%H:%M:%S')}")
                return

            # 统计错误和警告
            error_count = sum(1 for e in errors if e['severity'].upper() == 'ERROR')
            warning_count = sum(1 for e in errors if e['severity'].upper() == 'WARNING')

            # 添加错误到列表
            for error in errors:
                # 获取文件名（用于显示）
                from pathlib import Path
                file_name = Path(error['file']).name if error['file'] else ''

                self.frontend_error_tree.insert('', 'end', values=(
                    error['severity'].upper(),
                    file_name,
                    error['line'],
                    error['code'],
                    error['message'][:80] + '...' if len(error['message']) > 80 else error['message'],
                    error.get('timestamp', '')
                ), tags=(error['severity'].lower(),))

            # 设置颜色
            self.frontend_error_tree.tag_configure('error', foreground='red')
            self.frontend_error_tree.tag_configure('warning', foreground='orange')

            # 更新状态
            if error_count > 0:
                self.frontend_compile_status_label.config(text="❌ 有错误", foreground="red")
                self.frontend_send_to_ai_button.config(state="normal")
            else:
                self.frontend_compile_status_label.config(text="⚠️ 仅警告", foreground="orange")
                self.frontend_send_to_ai_button.config(state="normal")

            self.frontend_error_count_label.config(text=f"错误: {error_count} | 警告: {warning_count}")
            self.frontend_last_update_label.config(text=f"更新: {datetime.now().strftime('%H:%M:%S')}")

            print(f"✅ 前端编译错误更新完成: {error_count}个错误, {warning_count}个警告")

        except Exception as e:
            print(f"更新前端编译错误列表失败: {e}")

    def show_empty_frontend_errors_message(self):
        """显示空前端编译错误消息"""
        # 清空错误列表
        for item in self.frontend_error_tree.get_children():
            self.frontend_error_tree.delete(item)

        # 添加提示信息
        self.frontend_error_tree.insert('', 'end', values=('', '', '', '', '自动获取前端编译错误信息...', ''))

    def handle_frontend_error_fetch_error(self, error_msg):
        """处理前端编译错误获取失败"""
        print(f"❌ {error_msg}")
        self.frontend_compile_status_label.config(text="获取失败", foreground="red")
        self.frontend_last_update_label.config(text=f"失败: {datetime.now().strftime('%H:%M:%S')}")

    def clear_frontend_errors(self):
        """清空前端编译错误列表"""
        self.show_empty_frontend_errors_message()
        self.frontend_compile_status_label.config(text="未检查", foreground="gray")
        self.frontend_error_count_label.config(text="错误: 0 | 警告: 0")
        self.frontend_send_to_ai_button.config(state="disabled")
        self.frontend_last_update_label.config(text="更新: 未检查")

    def on_frontend_error_double_click(self, event):
        """前端编译错误双击事件"""
        selection = self.frontend_error_tree.selection()
        if selection:
            self.open_frontend_error_file()

    def open_frontend_error_file(self):
        """打开前端错误文件"""
        selection = self.frontend_error_tree.selection()
        if not selection:
            return

        try:
            item = selection[0]
            values = self.frontend_error_tree.item(item)['values']
            if len(values) >= 2:
                file_name = values[1]  # 文件名列
                print(f"尝试打开前端文件: {file_name}")
                # 这里可以实现打开文件的逻辑
        except Exception as e:
            print(f"打开前端文件失败: {e}")

    def send_frontend_errors_to_ai(self):
        """发送所有前端错误给AI修复"""
        try:
            errors = []
            for item in self.frontend_error_tree.get_children():
                values = self.frontend_error_tree.item(item)['values']
                if len(values) >= 5 and values[0]:  # 确保有有效的错误信息
                    errors.append({
                        'severity': values[0],
                        'file': values[1],
                        'line': values[2],
                        'code': values[3],
                        'message': values[4]
                    })

            if not errors:
                messagebox.showwarning("警告", "没有前端编译错误可发送")
                return

            # 发送给AI修复
            self.send_frontend_errors_to_ai_for_fix(errors)

        except Exception as e:
            print(f"发送前端错误给AI失败: {e}")
            messagebox.showerror("错误", f"发送前端错误给AI失败: {e}")

    def send_frontend_errors_to_ai_for_fix(self, errors):
        """发送前端错误给AI进行修复"""
        try:
            print(f"🤖 准备发送 {len(errors)} 个前端编译错误给AI修复")

            # 从文本框获取错误条数
            try:
                error_count = int(self.frontend_error_count_var.get())
            except ValueError:
                error_count = 5  # 默认5个

            # 限制错误数量
            limited_errors = errors[:error_count]

            # 构建错误文本
            error_text = "前端编译错误：\n\n"
            for i, error in enumerate(limited_errors, 1):
                error_text += f"{i}. 文件: {error['file']}\n"
                error_text += f"   行号: {error['line']}\n"
                error_text += f"   错误代码: {error['code']}\n"
                error_text += f"   错误信息: {error['message']}\n\n"

            # 调用AI修复脚本
            self.execute_ai_fix_script(error_text, "frontend")

        except Exception as e:
            print(f"发送前端错误给AI修复失败: {e}")
            messagebox.showerror("错误", f"发送前端错误给AI修复失败: {e}")

    def auto_refresh_backend_errors(self):
        """自动刷新后端编译错误（在选择步骤时调用）"""
        try:
            # 在后台线程中刷新，避免阻塞UI
            def refresh_thread():
                try:
                    errors = self.get_backend_compilation_errors()
                    if errors:
                        # 在主线程中更新后端编译错误选项卡标题
                        error_count = sum(1 for e in errors if e['severity'] == 'ERROR')
                        warning_count = sum(1 for e in errors if e['severity'] == 'WARNING')
                        tab_text = "🔧 后端编译错误"

                        # 更新选项卡标题
                        self.parent_ui.parent.after(0, lambda: self.update_backend_errors_tab_title(tab_text))

                        # 如果当前在后端编译错误选项卡，更新错误列表
                        try:
                            current_tab = self.parent_ui.right_notebook.index(self.parent_ui.right_notebook.select())
                            if current_tab == 4:  # 后端编译错误是第5个选项卡（索引4）
                                self.parent_ui.parent.after(0, lambda: self.update_backend_errors(errors))
                        except:
                            pass  # 如果获取当前选项卡失败，忽略

                except Exception as e:
                    print(f"自动刷新后端编译错误失败: {e}")

            threading.Thread(target=refresh_thread, daemon=True).start()

        except Exception as e:
            print(f"启动自动刷新后端编译错误失败: {e}")

    def auto_refresh_frontend_errors(self):
        """自动刷新前端编译错误（在选择步骤时调用）"""
        try:
            # 在后台线程中刷新，避免阻塞UI
            def refresh_thread():
                try:
                    errors = self.get_frontend_compilation_errors()
                    if errors:
                        # 在主线程中更新前端编译错误选项卡标题
                        error_count = sum(1 for e in errors if e['severity'] == 'ERROR')
                        warning_count = sum(1 for e in errors if e['severity'] == 'WARNING')
                        tab_text = "🌐 前端编译错误"

                        # 更新选项卡标题
                        self.parent_ui.parent.after(0, lambda: self.update_frontend_errors_tab_title(tab_text))

                        # 如果当前在前端编译错误选项卡，更新错误列表
                        try:
                            current_tab = self.parent_ui.right_notebook.index(self.parent_ui.right_notebook.select())
                            if current_tab == 5:  # 前端编译错误是第6个选项卡（索引5）
                                self.parent_ui.parent.after(0, lambda: self.update_frontend_errors(errors))
                        except:
                            pass  # 如果获取当前选项卡失败，忽略

                except Exception as e:
                    print(f"自动刷新前端编译错误失败: {e}")

            threading.Thread(target=refresh_thread, daemon=True).start()

        except Exception as e:
            print(f"启动自动刷新前端编译错误失败: {e}")

    def update_backend_errors_tab_title(self, title):
        """更新后端编译错误选项卡标题"""
        try:
            # 找到后端编译错误选项卡的索引
            for i in range(self.parent_ui.right_notebook.index("end")):
                tab_text = self.parent_ui.right_notebook.tab(i, "text")
                if "后端编译错误" in tab_text:
                    self.parent_ui.right_notebook.tab(i, text=title)
                    break
        except Exception as e:
            print(f"更新后端编译错误选项卡标题失败: {e}")

    def update_frontend_errors_tab_title(self, title):
        """更新前端编译错误选项卡标题"""
        try:
            # 找到前端编译错误选项卡的索引
            for i in range(self.parent_ui.right_notebook.index("end")):
                tab_text = self.parent_ui.right_notebook.tab(i, "text")
                if "前端编译错误" in tab_text:
                    self.parent_ui.right_notebook.tab(i, text=title)
                    break
        except Exception as e:
            print(f"更新前端编译错误选项卡标题失败: {e}")
