using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// Selenium测试管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SeleniumTestController : ControllerBase
    {
        private readonly ISeleniumTestService _seleniumTestService;
        private readonly ILogger<SeleniumTestController> _logger;

        public SeleniumTestController(
            ISeleniumTestService seleniumTestService,
            ILogger<SeleniumTestController> logger)
        {
            _seleniumTestService = seleniumTestService;
            _logger = logger;
        }

        /// <summary>
        /// 获取测试脚本列表
        /// </summary>
        [HttpGet("scripts")]
        public async Task<ActionResult<ProjectManagement.Core.DTOs.PagedResult<SeleniumScriptDto>>> GetScripts(
            [FromQuery] int? projectId,
            [FromQuery] string? category,
            [FromQuery] string? keyword,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _seleniumTestService.GetScriptsAsync(
                    userId, projectId, category, keyword, page, pageSize);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试脚本列表失败");
                return StatusCode(500, "获取测试脚本列表失败");
            }
        }

        /// <summary>
        /// 获取单个测试脚本
        /// </summary>
        [HttpGet("scripts/{id}")]
        public async Task<ActionResult<SeleniumScriptDto>> GetScript(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var script = await _seleniumTestService.GetScriptAsync(id, userId);
                
                if (script == null)
                {
                    return NotFound("测试脚本不存在");
                }
                
                return Ok(script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试脚本失败，ID: {ScriptId}", id);
                return StatusCode(500, "获取测试脚本失败");
            }
        }

        /// <summary>
        /// 创建测试脚本
        /// </summary>
        [HttpPost("scripts")]
        public async Task<ActionResult<SeleniumScriptDto>> CreateScript([FromBody] CreateSeleniumScriptDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var script = await _seleniumTestService.CreateScriptAsync(dto, userId);
                
                return CreatedAtAction(nameof(GetScript), new { id = script.Id }, script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建测试脚本失败");
                return StatusCode(500, "创建测试脚本失败");
            }
        }

        /// <summary>
        /// 更新测试脚本
        /// </summary>
        [HttpPut("scripts/{id}")]
        public async Task<ActionResult<SeleniumScriptDto>> UpdateScript(int id, [FromBody] UpdateSeleniumScriptDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var script = await _seleniumTestService.UpdateScriptAsync(id, dto, userId);
                
                if (script == null)
                {
                    return NotFound("测试脚本不存在");
                }
                
                return Ok(script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新测试脚本失败，ID: {ScriptId}", id);
                return StatusCode(500, "更新测试脚本失败");
            }
        }

        /// <summary>
        /// 删除测试脚本
        /// </summary>
        [HttpDelete("scripts/{id}")]
        public async Task<ActionResult> DeleteScript(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _seleniumTestService.DeleteScriptAsync(id, userId);
                
                if (!success)
                {
                    return NotFound("测试脚本不存在");
                }
                
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除测试脚本失败，ID: {ScriptId}", id);
                return StatusCode(500, "删除测试脚本失败");
            }
        }

        /// <summary>
        /// 执行测试脚本
        /// </summary>
        [HttpPost("scripts/{id}/execute")]
        public async Task<ActionResult<SeleniumExecutionResultDto>> ExecuteScript(int id, [FromBody] ExecuteSeleniumScriptDto? dto = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _seleniumTestService.ExecuteScriptAsync(id, dto, userId);
                
                if (result == null)
                {
                    return NotFound("测试脚本不存在");
                }
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行测试脚本失败，ID: {ScriptId}", id);
                return StatusCode(500, "执行测试脚本失败");
            }
        }

        /// <summary>
        /// 停止执行
        /// </summary>
        [HttpPost("executions/{executionId}/stop")]
        public async Task<ActionResult> StopExecution(string executionId)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _seleniumTestService.StopExecutionAsync(executionId, userId);
                
                if (!success)
                {
                    return NotFound("执行记录不存在");
                }
                
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止执行失败，ExecutionId: {ExecutionId}", executionId);
                return StatusCode(500, "停止执行失败");
            }
        }

        /// <summary>
        /// 获取执行结果
        /// </summary>
        [HttpGet("executions/{executionId}")]
        public async Task<ActionResult<SeleniumExecutionResultDto>> GetExecutionResult(string executionId)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _seleniumTestService.GetExecutionResultAsync(executionId, userId);
                
                if (result == null)
                {
                    return NotFound("执行记录不存在");
                }
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取执行结果失败，ExecutionId: {ExecutionId}", executionId);
                return StatusCode(500, "获取执行结果失败");
            }
        }

        /// <summary>
        /// 获取执行历史
        /// </summary>
        [HttpGet("executions")]
        public async Task<ActionResult<ProjectManagement.Core.DTOs.PagedResult<SeleniumExecutionResultDto>>> GetExecutionHistory(
            [FromQuery] int? scriptId,
            [FromQuery] string? status,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _seleniumTestService.GetExecutionHistoryAsync(
                    userId, scriptId, status, startDate, endDate, page, pageSize);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取执行历史失败");
                return StatusCode(500, "获取执行历史失败");
            }
        }

        /// <summary>
        /// 获取脚本模板
        /// </summary>
        [HttpGet("scripts/templates")]
        public async Task<ActionResult<Dictionary<string, string>>> GetTemplates()
        {
            try
            {
                var templates = await _seleniumTestService.GetTemplatesAsync();
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取脚本模板失败");
                return StatusCode(500, "获取脚本模板失败");
            }
        }

        /// <summary>
        /// 验证脚本语法
        /// </summary>
        [HttpPost("scripts/validate")]
        public async Task<ActionResult<SeleniumValidationResultDto>> ValidateScript([FromBody] ValidateSeleniumScriptDto dto)
        {
            try
            {
                var result = await _seleniumTestService.ValidateScriptAsync(dto.Code);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证脚本语法失败");
                return StatusCode(500, "验证脚本语法失败");
            }
        }

        /// <summary>
        /// 格式化脚本代码
        /// </summary>
        [HttpPost("scripts/format")]
        public async Task<ActionResult<SeleniumFormatResultDto>> FormatScript([FromBody] FormatSeleniumScriptDto dto)
        {
            try
            {
                var result = await _seleniumTestService.FormatScriptAsync(dto.Code);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "格式化脚本代码失败");
                return StatusCode(500, "格式化脚本代码失败");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        [HttpGet("scripts/statistics")]
        public async Task<ActionResult<SeleniumStatisticsDto>> GetStatistics(
            [FromQuery] int? projectId,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            try
            {
                var userId = GetCurrentUserId();
                var statistics = await _seleniumTestService.GetStatisticsAsync(userId, projectId, startDate, endDate);
                
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计信息失败");
                return StatusCode(500, "获取统计信息失败");
            }
        }

        /// <summary>
        /// 批量执行脚本
        /// </summary>
        [HttpPost("scripts/batch-execute")]
        public async Task<ActionResult<SeleniumBatchExecutionResultDto>> BatchExecute([FromBody] BatchExecuteSeleniumDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _seleniumTestService.BatchExecuteAsync(dto, userId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量执行脚本失败");
                return StatusCode(500, "批量执行脚本失败");
            }
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        [HttpGet("scripts/health")]
        public async Task<ActionResult<SeleniumHealthCheckResultDto>> HealthCheck()
        {
            try
            {
                var result = await _seleniumTestService.HealthCheckAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康检查失败");
                return StatusCode(500, "健康检查失败");
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (int.TryParse(userIdClaim, out int userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("无法获取用户信息");
        }
    }
}
