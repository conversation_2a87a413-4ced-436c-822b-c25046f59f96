// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  realName?: string
  phone?: string
  avatar?: string
  role: string  // 修改为string类型，对应后端的角色字符串
  status: number
  lastLoginTime?: string
  emailVerified: boolean
  twoFactorEnabled: boolean
  preferences?: string
  createdTime: string
  updatedTime?: string
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  accessToken: string
  refreshToken: string
  user: User
  expiresIn: number
  tokenType: string
}

// 注册请求
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  realName?: string
  phone?: string
}

// 项目相关类型
export interface Project {
  id: number
  name: string
  projectCode: string
  description?: string
  status: string
  priority: string
  progress: number
  startDate?: string
  endDate?: string
  estimatedHours?: number
  actualHours?: number
  budget?: number
  technologyStack?: string
  createdAt: string
  updatedAt?: string
  ownerId: number
  ownerName: string
}

// 项目摘要
export interface ProjectSummary {
  id: number
  name: string
  projectCode: string
  description?: string
  status: string
  priority: string
  progress: number
  createdAt: string
  updatedAt?: string
  ownerName: string
}

// 创建项目请求
export interface CreateProjectRequest {
  name: string
  description?: string
  priority?: string
  startDate?: string
  endDate?: string
  estimatedHours?: number
  budget?: number
  technologyStack?: string
}

// 更新项目请求
export interface UpdateProjectRequest {
  name?: string
  description?: string
  priority?: string
  startDate?: string
  endDate?: string
  estimatedHours?: number
  actualHours?: number
  budget?: number
  technologyStack?: string
}

// 更新项目状态请求
export interface UpdateProjectStatusRequest {
  status: string
  progress?: number
  note?: string
}

// 项目统计
export interface ProjectStatistics {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  pausedProjects: number
  averageCompletionDays: number
  totalBudget: number
  usedBudget: number
}

// 分页结果
export interface PagedResult<T> {
  items: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasPreviousPage: boolean
  hasNextPage: boolean
}

// API响应
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// 需求对话
export interface RequirementConversation {
  id: number
  projectId: number
  userId: number
  userMessage: string
  aiResponse?: string
  messageType: string
  timestamp: string
}

// 需求文档
export interface RequirementDocument {
  id: number
  projectId: number
  title: string
  content: string
  functionalRequirements?: string
  nonFunctionalRequirements?: string
  userStories?: string
  acceptanceCriteria?: string
  version: string
  status: string
  generatedBy: string
  createdAt: string
  updatedAt: string
}

// ER图
export interface ERDiagram {
  id: number
  projectId: number
  requirementDocumentId?: number
  diagramName: string
  mermaidDefinition: string
  description?: string
  version: string
  createdAt: string
  updatedAt: string
}

// 上下文图
export interface ContextDiagram {
  id: number
  projectId: number
  requirementDocumentId?: number
  diagramName: string
  mermaidDefinition: string
  externalEntities?: string
  systemBoundary?: string
  dataFlows?: string
  version: string
  createdAt: string
  updatedAt: string
}

// 代码生成任务
export interface CodeGenerationTask {
  id: number
  projectId: number
  requirementDocumentId?: number
  taskName: string
  codeType: string
  technology?: string
  status: string
  generatedCode?: string
  filePath?: string
  errorMessage?: string
  createdAt: string
  completedAt?: string
}

// 生成的代码文件
export interface GeneratedCodeFile {
  id: number
  codeGenerationTaskId: number
  fileName: string
  filePath: string
  fileContent: string
  fileType?: string
  language?: string
  createdAt: string
}

// 测试任务
export interface TestTask {
  id: number
  projectId: number
  codeGenerationTaskId?: number
  testName: string
  testType: string
  testFramework?: string
  status: string
  testCode?: string
  testResults?: string
  errorMessage?: string
  createdAt: string
  completedAt?: string
}

// 部署任务
export interface DeploymentTask {
  id: number
  projectId: number
  deploymentName: string
  environment: string
  deploymentType?: string
  status: string
  deploymentScript?: string
  deploymentUrl?: string
  logOutput?: string
  errorMessage?: string
  createdAt: string
  completedAt?: string
}

// 问题/Issue
export interface Issue {
  id: number
  projectId: number
  title: string
  description: string
  issueType: string
  priority: string
  severity: string
  status: string
  assignedTo?: number
  reportedBy: number
  labels?: string
  createdAt: string
  updatedAt: string
  resolvedAt?: string
}

// 问题解决方案
export interface IssueResolution {
  id: number
  issueId: number
  resolutionType?: string
  resolutionDescription?: string
  generatedCode?: string
  resolvedBy?: string
  createdAt: string
}

// AI模型配置
export interface AIModelConfiguration {
  id: number
  modelName: string
  modelType: string
  apiEndpoint?: string
  apiKey?: string
  modelParameters?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 工作流状态
export interface WorkflowState {
  id: number
  projectId: number
  currentStage: string
  stageStatus: string
  stageData?: string
  startedAt: string
  completedAt?: string
}

// 系统日志
export interface SystemLog {
  id: number
  projectId?: number
  userId?: number
  logLevel: string
  component?: string
  message: string
  exception?: string
  additionalData?: string
  createdAt: string
}

// 需求文档扩展类型（用于页面显示）
export interface RequirementSummary {
  id: number
  title: string
  description: string
  status: string
  version: string
  createdAt: string
}

// 项目模板类型
export interface ProjectTemplate {
  id: number
  name: string
  description: string
  icon: string
  tags: string[]
  config: {
    technologyStack: string
    estimatedHours: number
    aiFeatures: string[]
  }
}

// 选项类型
export interface SelectOption {
  label: string
  value: string
  color?: string
}