using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// AI服务接口
    /// </summary>
    public interface IAIService
    {
        /// <summary>
        /// 批量分析项目
        /// </summary>
        Task<IEnumerable<object>> BatchAnalyzeProjectsAsync(IEnumerable<object> projects);

        /// <summary>
        /// 计算项目相似度
        /// </summary>
        Task<double> CalculateSimilarityAsync(object project1, object project2);

        /// <summary>
        /// 分析项目复杂度
        /// </summary>
        Task<double> AnalyzeProjectComplexityAsync(object project);

        /// <summary>
        /// 预测项目风险
        /// </summary>
        Task<double> PredictRiskAsync(object project);

        /// <summary>
        /// 生成优化建议
        /// </summary>
        Task<IEnumerable<OptimizationSuggestion>> GenerateOptimizationSuggestionsAsync(IEnumerable<object> projects);

        /// <summary>
        /// 执行自动化任务
        /// </summary>
        Task<bool> ExecuteAutomationTaskAsync(AutomationTask task);

        /// <summary>
        /// 优化资源分配
        /// </summary>
        Task<ResourceAllocationResult> OptimizeResourceAllocationAsync(IEnumerable<object> projects);

        /// <summary>
        /// 生成代码
        /// </summary>
        Task<string> GenerateCodeAsync(string template, Dictionary<string, object> parameters);

        /// <summary>
        /// 分析代码质量
        /// </summary>
        Task<double> AnalyzeCodeQualityAsync(string code);

        /// <summary>
        /// 生成文档
        /// </summary>
        Task<string> GenerateDocumentationAsync(object project);
    }
}
