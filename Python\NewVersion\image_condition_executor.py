#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像条件执行器
实现"如果图片A存在，则点击图片B"这样的条件逻辑判断功能
"""

import time
from typing import Dict, List, Optional, Any
from pathlib import Path


class ImageConditionExecutor:
    """图像条件执行器类"""
    
    def __init__(self, automation_manager):
        """
        初始化图像条件执行器
        
        Args:
            automation_manager: 自动化管理器实例
        """
        self.automation_manager = automation_manager
        self.ui_actions = automation_manager.ui_actions
        self.screenshot_manager = automation_manager.screenshot_manager
        self.image_manager = automation_manager.image_manager
        
        print("图像条件执行器初始化完成")
    
    def execute_image_condition(self, condition_config: Dict) -> Dict:
        """
        执行图像条件判断

        Args:
            condition_config: 条件配置字典，包含以下字段：
                - condition_template_id: 条件图像模板ID
                - condition_template_name: 条件图像模板名称
                - target_template_id: 目标图像模板ID
                - target_template_name: 目标图像模板名称
                - target_action: 目标动作类型 (click, wait, verify)
                - condition_confidence: 条件图像置信度 (默认0.7)
                - target_confidence: 目标图像置信度 (默认0.7)
                - timeout: 超时时间 (默认5秒)
                - reverse_condition: 是否反向条件判断 (默认False)
                - else_action: 条件不满足时的动作 (可选)
                - else_template_id: 条件不满足时的目标模板ID (可选)
                - else_template_name: 条件不满足时的目标模板名称 (可选)

        Returns:
            执行结果字典
        """
        try:
            print(f"🔍 开始执行图像条件判断")
            print(f"   配置: {condition_config}")
            
            # 1. 检查条件图像是否存在
            condition_result = self._check_condition_image(condition_config)

            # 2. 判断是否为反向条件
            reverse_condition = condition_config.get('reverse_condition', False)
            image_exists = condition_result['exists']

            # 根据反向条件调整判断逻辑
            if reverse_condition:
                # 反向条件：如果图像不存在，则执行目标动作
                condition_satisfied = not image_exists
                print(f"🔄 反向条件判断: 图像{'不存在' if condition_satisfied else '存在'}")
            else:
                # 正向条件：如果图像存在，则执行目标动作
                condition_satisfied = image_exists
                print(f"✅ 正向条件判断: 图像{'存在' if condition_satisfied else '不存在'}")

            if condition_satisfied:
                print(f"✅ 条件满足，执行目标动作")
                # 执行目标动作
                target_result = self._execute_target_action(condition_config)

                return {
                    'success': target_result['success'],
                    'condition_result': condition_satisfied,
                    'image_exists': image_exists,
                    'reverse_condition': reverse_condition,
                    'target_action_executed': True,
                    'target_action_result': target_result,
                    'message': f"条件满足，目标动作{'成功' if target_result['success'] else '失败'}"
                }
            else:
                print(f"❌ 条件不满足")

                # 检查是否有else动作
                if condition_config.get('else_action'):
                    print(f"🔄 执行else动作")
                    else_result = self._execute_else_action(condition_config)

                    return {
                        'success': else_result['success'],
                        'condition_result': condition_satisfied,
                        'image_exists': image_exists,
                        'reverse_condition': reverse_condition,
                        'target_action_executed': False,
                        'else_action_executed': True,
                        'else_action_result': else_result,
                        'message': f"条件不满足，else动作{'成功' if else_result['success'] else '失败'}"
                    }
                else:
                    return {
                        'success': True,  # 条件不满足也算成功执行
                        'condition_result': condition_satisfied,
                        'image_exists': image_exists,
                        'reverse_condition': reverse_condition,
                        'target_action_executed': False,
                        'else_action_executed': False,
                        'message': '条件不满足，跳过所有动作'
                    }
                    
        except Exception as e:
            print(f"❌ 图像条件判断执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'执行失败: {str(e)}'
            }
    
    def _check_condition_image(self, config: Dict) -> Dict:
        """检查条件图像是否存在"""
        try:
            # 获取条件图像模板
            condition_template = self._get_template_by_config(
                config.get('condition_template_id'),
                config.get('condition_template_name')
            )
            
            if not condition_template:
                return {
                    'exists': False,
                    'error': '找不到条件图像模板'
                }
            
            # 获取图像文件路径
            image_path = self.image_manager.get_template_image_path(condition_template)
            if not image_path:
                return {
                    'exists': False,
                    'error': '无法获取条件图像文件'
                }
            
            # 检查图像是否存在于屏幕上
            confidence = config.get('condition_confidence', 0.7)
            location = self.screenshot_manager.find_template_on_screen(
                image_path,
                confidence=confidence
            )
            
            exists = location is not None
            print(f"🔍 条件图像 '{condition_template.get('name')}' 存在性: {exists}")
            
            return {
                'exists': exists,
                'template': condition_template,
                'location': location,
                'confidence': confidence
            }
            
        except Exception as e:
            print(f"❌ 检查条件图像失败: {e}")
            return {
                'exists': False,
                'error': str(e)
            }
    
    def _execute_target_action(self, config: Dict) -> Dict:
        """执行目标动作"""
        try:
            # 获取目标图像模板
            target_template = self._get_template_by_config(
                config.get('target_template_id'),
                config.get('target_template_name')
            )
            
            if not target_template:
                return {
                    'success': False,
                    'error': '找不到目标图像模板'
                }
            
            # 获取图像文件路径
            image_path = self.image_manager.get_template_image_path(target_template)
            if not image_path:
                return {
                    'success': False,
                    'error': '无法获取目标图像文件'
                }
            
            # 执行动作
            action = config.get('target_action', 'click').lower()
            confidence = config.get('target_confidence', 0.7)
            timeout = config.get('timeout', 5)
            
            return self._execute_action_on_template(
                action, image_path, target_template, confidence, timeout
            )
            
        except Exception as e:
            print(f"❌ 执行目标动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_else_action(self, config: Dict) -> Dict:
        """执行else动作"""
        try:
            # 获取else目标图像模板
            else_template = self._get_template_by_config(
                config.get('else_template_id'),
                config.get('else_template_name')
            )
            
            if not else_template:
                return {
                    'success': False,
                    'error': '找不到else目标图像模板'
                }
            
            # 获取图像文件路径
            image_path = self.image_manager.get_template_image_path(else_template)
            if not image_path:
                return {
                    'success': False,
                    'error': '无法获取else目标图像文件'
                }
            
            # 执行动作
            action = config.get('else_action', 'click').lower()
            confidence = config.get('target_confidence', 0.7)  # 使用相同的置信度
            timeout = config.get('timeout', 5)
            
            return self._execute_action_on_template(
                action, image_path, else_template, confidence, timeout
            )
            
        except Exception as e:
            print(f"❌ 执行else动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_action_on_template(self, action: str, image_path: str, template: Dict, 
                                   confidence: float, timeout: int) -> Dict:
        """在模板上执行指定动作"""
        try:
            template_name = template.get('name', '未知模板')
            print(f"🎯 在模板 '{template_name}' 上执行动作: {action}")
            
            success = False
            result_data = {}
            
            if action == 'click':
                success = self.ui_actions.click_template(
                    image_path,
                    confidence=confidence,
                    timeout=timeout
                )
                result_data['action'] = 'click'
                
            elif action == 'wait':
                location = self.screenshot_manager.wait_for_template(
                    image_path,
                    timeout=timeout,
                    confidence=confidence
                )
                success = location is not None
                result_data['action'] = 'wait'
                result_data['location'] = location
                
            elif action == 'verify':
                location = self.screenshot_manager.find_template_on_screen(
                    image_path,
                    confidence=confidence
                )
                success = location is not None
                result_data['action'] = 'verify'
                result_data['location'] = location
                
            else:
                return {
                    'success': False,
                    'error': f'不支持的动作类型: {action}'
                }
            
            if success:
                print(f"✅ 动作执行成功: {action} on {template_name}")
            else:
                print(f"❌ 动作执行失败: {action} on {template_name}")
            
            return {
                'success': success,
                'template': template,
                'confidence': confidence,
                **result_data
            }
            
        except Exception as e:
            print(f"❌ 执行动作失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_template_by_config(self, template_id: Optional[int], 
                               template_name: Optional[str]) -> Optional[Dict]:
        """根据配置获取模板"""
        try:
            if template_id:
                return self.automation_manager.get_template(template_id)
            elif template_name:
                templates = self.automation_manager.get_templates()
                return next((t for t in templates if t.get('name') == template_name), None)
            else:
                return None
        except Exception as e:
            print(f"❌ 获取模板失败: {e}")
            return None
    
    def create_simple_condition(self, condition_image_name: str, target_image_name: str,
                               target_action: str = 'click', reverse: bool = False) -> Dict:
        """
        创建简单的图像条件配置

        Args:
            condition_image_name: 条件图像模板名称
            target_image_name: 目标图像模板名称
            target_action: 目标动作 (click, wait, verify)
            reverse: 是否为反向条件 (True: 如果图像不存在则执行, False: 如果图像存在则执行)

        Returns:
            条件配置字典
        """
        return {
            'condition_template_name': condition_image_name,
            'target_template_name': target_image_name,
            'target_action': target_action,
            'reverse_condition': reverse,
            'condition_confidence': 0.7,
            'target_confidence': 0.7,
            'timeout': 5
        }

    def create_reverse_condition(self, condition_image_name: str, target_image_name: str,
                                target_action: str = 'click') -> Dict:
        """
        创建反向图像条件配置 (如果图像不存在，则执行动作)

        Args:
            condition_image_name: 条件图像模板名称
            target_image_name: 目标图像模板名称
            target_action: 目标动作 (click, wait, verify)

        Returns:
            反向条件配置字典
        """
        return self.create_simple_condition(
            condition_image_name=condition_image_name,
            target_image_name=target_image_name,
            target_action=target_action,
            reverse=True
        )
