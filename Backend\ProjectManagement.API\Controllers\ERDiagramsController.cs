using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;
using ProjectManagement.Core.DTOs;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// ER图管理控制器
/// </summary>
[ApiController]
[Route("api/er-diagrams")]
[Authorize]
public class ERDiagramsController : ControllerBase
{
    private readonly IRepository<ERDiagram> _erDiagramRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly ILogger<ERDiagramsController> _logger;

    public ERDiagramsController(
        IRepository<ERDiagram> erDiagramRepository,
        IRepository<Project> projectRepository,
        ILogger<ERDiagramsController> logger)
    {
        _erDiagramRepository = erDiagramRepository;
        _projectRepository = projectRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取ER图详情
    /// </summary>
    /// <param name="id">ER图ID</param>
    /// <returns>ER图详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ERDiagramDto>> GetERDiagram(int id)
    {
        try
        {
            var erDiagram = await _erDiagramRepository.GetByIdAsync(id);
            if (erDiagram == null)
            {
                return NotFound(new { message = "ER图不存在" });
            }

            var result = new ERDiagramDto
            {
                Id = erDiagram.Id,
                ProjectId = erDiagram.ProjectId,
                RequirementDocumentId = erDiagram.RequirementDocumentId,
                DiagramName = erDiagram.DiagramName,
                MermaidDefinition = erDiagram.MermaidDefinition,
                Description = erDiagram.Description,
                Version = erDiagram.DiagramVersion,
                CreatedAt = erDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = erDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取ER图详情时发生错误: {ERDiagramId}", id);
            return StatusCode(500, new { message = "获取ER图详情失败" });
        }
    }

    /// <summary>
    /// 创建ER图
    /// </summary>
    /// <param name="request">创建ER图请求</param>
    /// <returns>创建的ER图</returns>
    [HttpPost]
    public async Task<ActionResult<ERDiagramDto>> CreateERDiagram([FromBody] CreateERDiagramRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("创建ER图: 项目 {ProjectId}, 用户 {UserId}", request.ProjectId, userId);

            // 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 创建ER图实体
            var erDiagram = new ERDiagram
            {
                ProjectId = request.ProjectId,
                RequirementDocumentId = request.RequirementDocumentId,
                DiagramName = request.DiagramName,
                MermaidDefinition = request.MermaidDefinition,
                Description = request.Description,
                DiagramVersion = "1.0"
            };

            // 保存到数据库
            var savedDiagram = await _erDiagramRepository.AddAsync(erDiagram);

            // 构建返回结果
            var result = new ERDiagramDto
            {
                Id = savedDiagram.Id,
                ProjectId = savedDiagram.ProjectId,
                RequirementDocumentId = savedDiagram.RequirementDocumentId,
                DiagramName = savedDiagram.DiagramName,
                MermaidDefinition = savedDiagram.MermaidDefinition,
                Description = savedDiagram.Description,
                Version = savedDiagram.DiagramVersion,
                CreatedAt = savedDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = savedDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            _logger.LogInformation("ER图创建成功: {ERDiagramId}", savedDiagram.Id);
            return CreatedAtAction(nameof(GetERDiagram), new { id = savedDiagram.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建ER图时发生错误");
            return StatusCode(500, new { message = "创建ER图失败" });
        }
    }

    /// <summary>
    /// 更新ER图
    /// </summary>
    /// <param name="id">ER图ID</param>
    /// <param name="request">更新ER图请求</param>
    /// <returns>更新后的ER图</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ERDiagramDto>> UpdateERDiagram(int id, [FromBody] UpdateERDiagramRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("更新ER图: {ERDiagramId}, 用户 {UserId}", id, userId);

            var erDiagram = await _erDiagramRepository.GetByIdAsync(id);
            if (erDiagram == null)
            {
                return NotFound(new { message = "ER图不存在" });
            }

            // 更新字段
            if (!string.IsNullOrEmpty(request.DiagramName))
                erDiagram.DiagramName = request.DiagramName;

            if (!string.IsNullOrEmpty(request.MermaidDefinition))
                erDiagram.MermaidDefinition = request.MermaidDefinition;

            if (request.Description != null)
                erDiagram.Description = request.Description;

            if (!string.IsNullOrEmpty(request.Version))
                erDiagram.DiagramVersion = request.Version;

            erDiagram.UpdatedTime = DateTime.Now;

            // 保存更新
            await _erDiagramRepository.UpdateAsync(erDiagram);

            // 构建返回结果
            var result = new ERDiagramDto
            {
                Id = erDiagram.Id,
                ProjectId = erDiagram.ProjectId,
                RequirementDocumentId = erDiagram.RequirementDocumentId,
                DiagramName = erDiagram.DiagramName,
                MermaidDefinition = erDiagram.MermaidDefinition,
                Description = erDiagram.Description,
                Version = erDiagram.DiagramVersion,
                CreatedAt = erDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = erDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            _logger.LogInformation("ER图更新成功: {ERDiagramId}", id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新ER图时发生错误: {ERDiagramId}", id);
            return StatusCode(500, new { message = "更新ER图失败" });
        }
    }

    /// <summary>
    /// 删除ER图
    /// </summary>
    /// <param name="id">ER图ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteERDiagram(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("删除ER图: {ERDiagramId}, 用户 {UserId}", id, userId);

            var erDiagram = await _erDiagramRepository.GetByIdAsync(id);
            if (erDiagram == null)
            {
                return NotFound(new { message = "ER图不存在" });
            }

            await _erDiagramRepository.DeleteAsync(id);

            _logger.LogInformation("ER图删除成功: {ERDiagramId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除ER图时发生错误: {ERDiagramId}", id);
            return StatusCode(500, new { message = "删除ER图失败" });
        }
    }

    /// <summary>
    /// 导出ER图
    /// </summary>
    /// <param name="id">ER图ID</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出文件</returns>
    [HttpGet("{id}/export")]
    public async Task<ActionResult> ExportERDiagram(int id, [FromQuery] string format = "svg")
    {
        try
        {
            var erDiagram = await _erDiagramRepository.GetByIdAsync(id);
            if (erDiagram == null)
            {
                return NotFound(new { message = "ER图不存在" });
            }

            // TODO: 实现图表导出逻辑
            // 这里需要集成Mermaid渲染引擎或使用第三方服务

            var fileName = $"er-diagram-{id}.{format}";
            var contentType = format.ToLower() switch
            {
                "svg" => "image/svg+xml",
                "png" => "image/png",
                "pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            // 临时返回Mermaid定义
            var content = System.Text.Encoding.UTF8.GetBytes(erDiagram.MermaidDefinition);
            return File(content, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出ER图时发生错误: {ERDiagramId}", id);
            return StatusCode(500, new { message = "导出ER图失败" });
        }
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }
}
