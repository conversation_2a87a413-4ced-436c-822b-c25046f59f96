using Microsoft.Extensions.Logging;
using Moq;
using ProjectManagementAI.WPF.Services;
using ProjectManagementAI.WPF.ViewModels;
using Xunit;

namespace ProjectManagementAI.WPF.Tests.ViewModels
{
    public class MainViewModelTests
    {
        private readonly Mock<ILogger<MainViewModel>> _mockLogger;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<IAIService> _mockAIService;
        private readonly Mock<IVectorOperationService> _mockVectorOperationService;
        private readonly Mock<IDataAnalysisService> _mockDataAnalysisService;
        private readonly Mock<IKnowledgeGraphService> _mockKnowledgeGraphService;
        private readonly Mock<VectorOperationViewModel> _mockVectorOperationViewModel;
        private readonly MainViewModel _viewModel;

        public MainViewModelTests()
        {
            _mockLogger = new Mock<ILogger<MainViewModel>>();
            _mockProjectService = new Mock<IProjectService>();
            _mockAIService = new Mock<IAIService>();
            _mockVectorOperationService = new Mock<IVectorOperationService>();
            _mockDataAnalysisService = new Mock<IDataAnalysisService>();
            _mockKnowledgeGraphService = new Mock<IKnowledgeGraphService>();
            _mockVectorOperationViewModel = new Mock<VectorOperationViewModel>(
                Mock.Of<ILogger<VectorOperationViewModel>>(),
                _mockVectorOperationService.Object,
                _mockProjectService.Object,
                _mockAIService.Object);

            _viewModel = new MainViewModel(
                _mockLogger.Object,
                _mockProjectService.Object,
                _mockAIService.Object,
                _mockVectorOperationService.Object,
                _mockDataAnalysisService.Object,
                _mockKnowledgeGraphService.Object,
                _mockVectorOperationViewModel.Object);
        }

        [Fact]
        public void Constructor_ShouldInitializeProperties()
        {
            // Assert
            _viewModel.StatusMessage.Should().Be("就绪");
            _viewModel.ConnectionStatusText.Should().Be("已连接");
            _viewModel.ConnectionStatus.Should().BeTrue();
            _viewModel.IsLoading.Should().BeFalse();
            _viewModel.SelectedNavigationIndex.Should().Be(0);
        }

        [Fact]
        public void SelectedNavigationIndex_WhenChanged_ShouldNavigateToCorrectView()
        {
            // Act
            _viewModel.SelectedNavigationIndex = 1;

            // Assert
            _viewModel.CurrentView.Should().NotBeNull();
            _viewModel.StatusMessage.Should().Contain("项目管理");
        }

        [Fact]
        public async Task SyncDataAsync_WhenSuccessful_ShouldUpdateStatus()
        {
            // Arrange
            _mockProjectService.Setup(x => x.SyncWithServerAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _viewModel.SyncDataCommand.ExecuteAsync(null);

            // Assert
            _viewModel.StatusMessage.Should().Be("数据同步完成");
            _viewModel.ConnectionStatus.Should().BeTrue();
            _viewModel.ConnectionStatusText.Should().Be("已连接");
            _viewModel.IsLoading.Should().BeFalse();
        }

        [Fact]
        public async Task SyncDataAsync_WhenFailed_ShouldUpdateStatusWithError()
        {
            // Arrange
            _mockProjectService.Setup(x => x.SyncWithServerAsync())
                .ThrowsAsync(new Exception("网络错误"));

            // Act
            await _viewModel.SyncDataCommand.ExecuteAsync(null);

            // Assert
            _viewModel.StatusMessage.Should().Be("数据同步失败");
            _viewModel.ConnectionStatus.Should().BeFalse();
            _viewModel.ConnectionStatusText.Should().Be("连接失败");
            _viewModel.IsLoading.Should().BeFalse();
        }

        [Fact]
        public async Task BatchCreateProjectsAsync_WhenSuccessful_ShouldUpdateStatus()
        {
            // Arrange
            var batchResult = new BatchOperationResult
            {
                SuccessCount = 5,
                TotalCount = 5,
                FailureCount = 0
            };

            _mockVectorOperationService.Setup(x => x.BatchCreateProjectsAsync(It.IsAny<IEnumerable<ProjectTemplate>>()))
                .ReturnsAsync(batchResult);

            // Act
            await _viewModel.BatchCreateProjectsCommand.ExecuteAsync(null);

            // Assert
            _viewModel.StatusMessage.Should().Contain("批量创建完成");
            _viewModel.StatusMessage.Should().Contain("成功创建 5 个项目");
            _viewModel.IsLoading.Should().BeFalse();
        }

        [Fact]
        public async Task BatchAnalyzeAsync_WhenSuccessful_ShouldUpdateStatus()
        {
            // Arrange
            var projects = new List<object> { new(), new(), new() };
            var analysisResults = new List<object> { new(), new(), new() };

            _mockProjectService.Setup(x => x.GetAllProjectsAsync())
                .ReturnsAsync(projects);

            _mockAIService.Setup(x => x.BatchAnalyzeProjectsAsync(It.IsAny<IEnumerable<object>>()))
                .ReturnsAsync(analysisResults);

            // Act
            await _viewModel.BatchAnalyzeCommand.ExecuteAsync(null);

            // Assert
            _viewModel.StatusMessage.Should().Contain("批量分析完成");
            _viewModel.StatusMessage.Should().Contain("分析了 3 个项目");
            _viewModel.IsLoading.Should().BeFalse();
        }

        [Fact]
        public async Task VectorOptimizeAsync_WhenSuccessful_ShouldUpdateStatus()
        {
            // Arrange
            var optimizationResults = new OptimizationResult
            {
                OverallScore = 0.85,
                Suggestions = new List<OptimizationSuggestion>
                {
                    new() { Title = "优化建议1", Impact = 0.8 },
                    new() { Title = "优化建议2", Impact = 0.7 }
                }
            };

            _mockVectorOperationService.Setup(x => x.OptimizeProjectsAsync())
                .ReturnsAsync(optimizationResults);

            // Act
            await _viewModel.VectorOptimizeCommand.ExecuteAsync(null);

            // Assert
            _viewModel.StatusMessage.Should().Be("智能优化完成");
            _viewModel.IsLoading.Should().BeFalse();
        }

        [Fact]
        public async Task InitializeAsync_WhenSuccessful_ShouldCompleteInitialization()
        {
            // Arrange
            _mockProjectService.Setup(x => x.CheckConnectionAsync())
                .ReturnsAsync(true);

            _mockProjectService.Setup(x => x.LoadCacheDataAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _viewModel.InitializeAsync();

            // Assert
            _viewModel.StatusMessage.Should().Be("初始化完成");
            _viewModel.ConnectionStatus.Should().BeTrue();
            _viewModel.ConnectionStatusText.Should().Be("已连接");
        }

        [Fact]
        public async Task InitializeAsync_WhenConnectionFailed_ShouldUpdateConnectionStatus()
        {
            // Arrange
            _mockProjectService.Setup(x => x.CheckConnectionAsync())
                .ReturnsAsync(false);

            _mockProjectService.Setup(x => x.LoadCacheDataAsync())
                .Returns(Task.CompletedTask);

            // Act
            await _viewModel.InitializeAsync();

            // Assert
            _viewModel.ConnectionStatus.Should().BeFalse();
            _viewModel.ConnectionStatusText.Should().Be("连接失败");
        }

        [Theory]
        [InlineData(0, "仪表板")]
        [InlineData(1, "项目管理")]
        [InlineData(2, "需求管理")]
        [InlineData(3, "代码生成")]
        [InlineData(4, "向量化操作")]
        [InlineData(5, "AI助手")]
        public void NavigateToView_ShouldSetCorrectViewAndStatus(int index, string expectedViewName)
        {
            // Act
            _viewModel.SelectedNavigationIndex = index;

            // Assert
            _viewModel.CurrentView.Should().NotBeNull();
            _viewModel.StatusMessage.Should().Contain(expectedViewName);
        }

        [Fact]
        public void Cleanup_ShouldExecuteWithoutErrors()
        {
            // Act & Assert
            var action = () => _viewModel.Cleanup();
            action.Should().NotThrow();
        }

        [Fact]
        public void Commands_ShouldNotBeNull()
        {
            // Assert
            _viewModel.SyncDataCommand.Should().NotBeNull();
            _viewModel.OpenSettingsCommand.Should().NotBeNull();
            _viewModel.ShowAboutCommand.Should().NotBeNull();
            _viewModel.BatchCreateProjectsCommand.Should().NotBeNull();
            _viewModel.BatchAnalyzeCommand.Should().NotBeNull();
            _viewModel.VectorOptimizeCommand.Should().NotBeNull();
        }

        [Fact]
        public async Task BatchOperations_WhenExecutedConcurrently_ShouldHandleCorrectly()
        {
            // Arrange
            _mockVectorOperationService.Setup(x => x.BatchCreateProjectsAsync(It.IsAny<IEnumerable<ProjectTemplate>>()))
                .ReturnsAsync(new BatchOperationResult { SuccessCount = 3 });

            _mockVectorOperationService.Setup(x => x.OptimizeProjectsAsync())
                .ReturnsAsync(new OptimizationResult { OverallScore = 0.9 });

            // Act
            var task1 = _viewModel.BatchCreateProjectsCommand.ExecuteAsync(null);
            var task2 = _viewModel.VectorOptimizeCommand.ExecuteAsync(null);

            await Task.WhenAll(task1, task2);

            // Assert
            _viewModel.IsLoading.Should().BeFalse();
            _viewModel.StatusMessage.Should().NotBeEmpty();
        }
    }
}
