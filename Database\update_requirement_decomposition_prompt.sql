-- 更新需求分解prompt模板，确保使用maxStepCount参数
USE ProjectManagementAI;
GO

UPDATE PromptTemplates
SET Content = N'你是一个专业的软件架构师和项目经理，请将以下需求分解为具体的开发步骤。

需求内容：
{requirementContent}

技术栈：{technologyStack}
分解粒度：{granularity}
最大步骤数：{maxStepCount}（请严格控制生成的步骤数量不超过此限制）

请按照以下JSON格式返回分解结果：

{
  "analysis": "对需求的整体分析和理解",
  "steps": [
    {
      "stepName": "步骤名称",
      "stepDescription": "详细描述",
      "stepType": "Development|Testing|Documentation|Deployment|Review",
      "priority": "High|Medium|Low",
      "estimatedHours": 8,
      "technologyStack": "具体技术栈",
      "componentType": "Frontend|Backend|Database|API|Model|Service",
      "stepOrder": 1,
      "stepLevel": 1,
      "aiPrompt": "用于AI代码生成的提示词"
    }
  ],
  "dependencies": [
    {
      "stepName": "依赖步骤名称",
      "dependsOnStepName": "被依赖步骤名称",
      "dependencyType": "Sequential|Parallel|Conditional",
      "isRequired": true,
      "description": "依赖关系说明"
    }
  ]
}

分解原则：
1. 按照软件开发生命周期进行分解
2. 考虑前后端分离的架构
3. 包含数据库设计和API设计
4. 合理安排开发顺序和依赖关系
5. 每个步骤都要有明确的交付物
6. 估算合理的工作量
7. 严格控制步骤总数不超过{maxStepCount}个

请确保返回的是纯JSON格式，不要包含其他文本。'
WHERE TaskType = 'RequirementDecomposition';

PRINT '需求分解prompt模板已更新，现在会严格控制最大步骤数';
GO
