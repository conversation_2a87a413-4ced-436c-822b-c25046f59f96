using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using BuildMonitor.Models;
using BuildMonitor.Services;
using static BuildMonitor.Services.AuthService;

namespace BuildMonitor
{
    /// <summary>
    /// LoginWindow_New.xaml 的交互逻辑
    /// </summary>
    public partial class LoginWindow : Window
    {
        private DatabaseService _databaseService;
        private readonly UserSettingsService _userSettings;
        private User? _authenticatedUser;

        public User? AuthenticatedUser => _authenticatedUser;

        public LoginWindow()
        {
            InitializeComponent();
            
            // 初始化数据库服务
            InitializeDatabaseService();
            
            _userSettings = new UserSettingsService();
            
            // 设置回车键登录
            UsernameTextBox.KeyDown += OnKeyDown;
            PasswordBox.KeyDown += OnKeyDown;
            
            // 加载记住的用户名
            LoadRememberedCredentials();
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async Task PerformLogin()
        {
            var username = UsernameTextBox.Text.Trim();
            var password = PasswordBox.Password;

            // 验证输入
            if (string.IsNullOrEmpty(username))
            {
                ShowError("请输入用户名");
                UsernameTextBox.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("请输入密码");
                PasswordBox.Focus();
                return;
            }

            // 禁用登录按钮，显示加载状态
            LoginButton.IsEnabled = false;
            LoginButton.Content = "登录中...";
            HideError();

            try
            {
                // 使用AuthService调用后端API进行登录
                var authService = new AuthService();
                var rememberMe = RememberMeCheckBox.IsChecked == true;
                var loginResponse = await authService.LoginAsync(username, password, rememberMe);

                if (loginResponse != null)
                {
                    // 创建User对象（从API响应转换）
                    _authenticatedUser = new User
                    {
                        Id = loginResponse.User.Id,
                        Username = loginResponse.User.Username,
                        Email = loginResponse.User.Email,
                        RealName = loginResponse.User.RealName,
                        Role = loginResponse.User.Role,
                        Status = loginResponse.User.Status,
                        Avatar = loginResponse.User.Avatar,
                        CreatedTime = loginResponse.User.CreatedAt,
                        LastLoginTime = loginResponse.User.LastLoginAt
                    };

                    // 保存记住的凭据
                    if (rememberMe)
                    {
                        SaveRememberedCredentials(username);
                    }
                    else
                    {
                        ClearRememberedCredentials();
                    }

                    DialogResult = true;
                    Close();
                }
            }
            catch (AuthenticationException ex)
            {
                ShowError(ex.Message);
                PasswordBox.Clear();
                PasswordBox.Focus();
            }
            catch (Exception ex)
            {
                ShowError($"登录失败: {ex.Message}");
            }
            finally
            {
                // 恢复登录按钮状态
                LoginButton.IsEnabled = true;
                LoginButton.Content = "登录";
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void LoadRememberedCredentials()
        {
            try
            {
                var rememberedUsername = _userSettings.RememberedUsername;
                if (!string.IsNullOrEmpty(rememberedUsername))
                {
                    UsernameTextBox.Text = rememberedUsername;
                    RememberMeCheckBox.IsChecked = true;
                    PasswordBox.Focus();
                }
                else
                {
                    UsernameTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load remembered credentials: {ex.Message}");
                UsernameTextBox.Focus();
            }
        }

        private void SaveRememberedCredentials(string username)
        {
            try
            {
                _userSettings.RememberedUsername = username;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save remembered credentials: {ex.Message}");
            }
        }

        private void ClearRememberedCredentials()
        {
            try
            {
                _userSettings.ClearRememberedUsername();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to clear remembered credentials: {ex.Message}");
            }
        }

        private void InitializeDatabaseService()
        {
            try
            {
                // 从配置文件获取连接字符串
                var connectionString = GetConnectionString();
                _databaseService = new DatabaseService(connectionString);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法初始化数据库服务: {ex.Message}", ex);
            }
        }

        private string GetConnectionString()
        {
            try
            {
                // 简单的配置读取
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    dynamic config = Newtonsoft.Json.JsonConvert.DeserializeObject(json);
                    
                    string connectionString = config?.ConnectionStrings?.DefaultConnection;
                    if (!string.IsNullOrEmpty(connectionString))
                        return connectionString;
                    
                    connectionString = config?.ConnectionStrings?.AlternativeConnection;
                    if (!string.IsNullOrEmpty(connectionString))
                        return connectionString;
                }
                
                // 默认连接字符串
                return "Server=.;Database=ProjectManagementAI;Integrated Security=true;TrustServerCertificate=true";
            }
            catch
            {
                // 返回默认连接字符串
                return "Server=.;Database=ProjectManagementAI;Integrated Security=true;TrustServerCertificate=true";
            }
        }
    }
}
