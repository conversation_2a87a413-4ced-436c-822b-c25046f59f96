-- =============================================
-- 更新RequirementConversations表的MessageType约束
-- 添加对AI图表生成功能的支持
-- =============================================

USE ProjectManagementAI;
GO

-- 删除现有的CHECK约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_RequirementConversations_MessageType')
BEGIN
    ALTER TABLE RequirementConversations DROP CONSTRAINT CK_RequirementConversations_MessageType;
    PRINT '已删除旧的MessageType约束';
END
GO

-- 创建新的CHECK约束，支持更多消息类型
ALTER TABLE RequirementConversations 
ADD CONSTRAINT CK_RequirementConversations_MessageType 
CHECK (MessageType IN (
    'Requirement',              -- 需求收集
    'Clarification',           -- 澄清问题
    'Confirmation',            -- 确认需求
    'ERDiagramGeneration',     -- ER图生成
    'ContextDiagramGeneration', -- 上下文图生成
    'CodeGeneration',          -- 代码生成
    'DocumentGeneration',      -- 文档生成
    'RequirementAnalysis'      -- 需求分析
));
GO

PRINT '已更新MessageType约束，支持AI功能对话记录';

-- 验证约束是否正确创建
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition
FROM sys.check_constraints cc
INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
WHERE t.name = 'RequirementConversations' 
  AND cc.name = 'CK_RequirementConversations_MessageType';
GO

PRINT '约束更新完成！';
