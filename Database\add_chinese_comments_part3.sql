-- =============================================
-- 项目管理AI系统数据库字段中文注释脚本 - 第三部分
-- 为剩余表的字段添加中文描述注释
-- 版本: 1.0
-- 创建日期: 2024-12-19
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 11. GeneratedCodeFiles表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'代码文件唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联的代码生成任务ID',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'CodeGenerationTaskId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文件名称（如：UserController.cs, UserList.vue），长度255字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'FileName';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文件完整路径（如：/Controllers/UserController.cs），长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'FilePath';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文件完整内容（实际的代码内容）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'FileContent';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文件扩展名类型：cs, vue, sql, json, js, ts, html, css等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'FileType';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'编程语言：CSharp, JavaScript, SQL, TypeScript, HTML, CSS等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'Language';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'文件生成时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles',
    @level2type = N'Column', @level2name = N'CreatedAt';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'代码文件存储管理表，存储AI生成的具体代码文件内容，支持多文件类型、版本管理、文件组织、代码检索',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'GeneratedCodeFiles';
GO

-- =============================================
-- 12. TestTasks表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试任务唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联的代码生成任务ID，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'CodeGenerationTaskId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试名称（如：用户登录功能测试），长度200字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'TestName';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试类型：Unit=单元测试, Integration=集成测试, E2E=端到端测试, Performance=性能测试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'TestType';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试框架：xUnit, Jest, Cypress, Selenium, JMeter等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'TestFramework';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试状态：Pending=待执行, Running=执行中, Passed=通过, Failed=失败',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'AI生成的测试代码',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'TestCode';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试执行结果，JSON格式存储（通过率、覆盖率、执行时间等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'TestResults';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试失败时的错误信息',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'ErrorMessage';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试任务创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'测试任务完成时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks',
    @level2type = N'Column', @level2name = N'CompletedAt';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'AI测试生成和执行管理表，管理AI生成的测试用例和测试执行结果，支持多测试类型、测试框架、结果分析、自动化测试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'TestTasks';
GO

-- =============================================
-- 13. DeploymentTasks表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署任务唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署环境：Development=开发, Staging=测试, Production=生产',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'Environment';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署类型：Docker=容器, IIS=Windows服务器, Azure=云平台, AWS=云平台, Kubernetes=容器编排',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'DeploymentType';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署状态：Pending=待部署, InProgress=部署中, Completed=部署成功, Failed=部署失败',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署脚本内容（Docker文件、PowerShell脚本等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'DeploymentScript';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署后的访问URL地址，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'DeploymentUrl';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署过程的日志输出',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'LogOutput';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署失败时的错误信息',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'ErrorMessage';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署任务创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'部署任务完成时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks',
    @level2type = N'Column', @level2name = N'CompletedAt';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'自动化部署管理表，管理项目的自动化部署任务和部署历史，支持多环境部署、多平台支持、部署脚本、日志记录',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'DeploymentTasks';
GO

-- =============================================
-- 14. Issues表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题标题（支持全文搜索），长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Title';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题详细描述（支持全文搜索）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Description';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题类型：Bug=缺陷, Feature=功能请求, Enhancement=改进, Task=任务',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'IssueType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'优先级：Low=低, Medium=中, High=高, Critical=紧急',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Priority';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题状态：Open=开放, InProgress=处理中, Resolved=已解决, Closed=已关闭',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'分配给的用户ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'AssignedTo';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'报告人用户ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'ReportedBy';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题标签，逗号分隔（如：前端,登录,紧急），长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'Labels';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题最后更新时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'UpdatedAt';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'问题解决时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues',
    @level2type = N'Column', @level2name = N'ResolvedAt';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目问题和缺陷管理表，管理项目开发过程中的问题、缺陷、功能请求等，支持问题分类、优先级管理、状态跟踪、全文搜索',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Issues';
GO
