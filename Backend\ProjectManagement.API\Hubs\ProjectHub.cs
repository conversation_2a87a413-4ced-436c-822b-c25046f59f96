using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;

namespace ProjectManagement.API.Hubs;

/// <summary>
/// 项目实时通信Hub
/// 功能: 提供项目相关的实时通信功能
/// 支持: 项目状态更新、团队协作、实时通知等
/// </summary>
[Authorize]
public class ProjectHub : Hub
{
    private readonly ILogger<ProjectHub> _logger;

    public ProjectHub(ILogger<ProjectHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时调用
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("用户 {UserId} 连接到ProjectHub", userId);
        
        await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时调用
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = Context.UserIdentifier;
        _logger.LogInformation("用户 {UserId} 从ProjectHub断开连接", userId);
        
        if (exception != null)
        {
            _logger.LogError(exception, "用户 {UserId} 连接异常断开", userId);
        }

        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 加入项目组
    /// </summary>
    /// <param name="projectId">项目ID</param>
    public async Task JoinProjectGroup(int projectId)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Project_{projectId}";
        
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("用户 {UserId} 加入项目组 {GroupName}", userId, groupName);
        
        // 通知组内其他成员
        await Clients.OthersInGroup(groupName).SendAsync("UserJoinedProject", new
        {
            UserId = userId,
            ProjectId = projectId,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 离开项目组
    /// </summary>
    /// <param name="projectId">项目ID</param>
    public async Task LeaveProjectGroup(int projectId)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Project_{projectId}";
        
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("用户 {UserId} 离开项目组 {GroupName}", userId, groupName);
        
        // 通知组内其他成员
        await Clients.OthersInGroup(groupName).SendAsync("UserLeftProject", new
        {
            UserId = userId,
            ProjectId = projectId,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 发送项目消息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="message">消息内容</param>
    public async Task SendProjectMessage(int projectId, string message)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Project_{projectId}";
        
        _logger.LogInformation("用户 {UserId} 向项目组 {GroupName} 发送消息", userId, groupName);
        
        await Clients.Group(groupName).SendAsync("ReceiveProjectMessage", new
        {
            UserId = userId,
            ProjectId = projectId,
            Message = message,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知项目状态更新
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="status">新状态</param>
    /// <param name="details">状态详情</param>
    public async Task NotifyProjectStatusUpdate(int projectId, string status, object? details = null)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Project_{projectId}";
        
        _logger.LogInformation("项目 {ProjectId} 状态更新为 {Status}", projectId, status);
        
        await Clients.Group(groupName).SendAsync("ProjectStatusUpdated", new
        {
            ProjectId = projectId,
            Status = status,
            Details = details,
            UpdatedBy = userId,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知工作流阶段变更
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="stage">新阶段</param>
    /// <param name="progress">进度百分比</param>
    public async Task NotifyWorkflowStageChange(int projectId, string stage, int progress)
    {
        var userId = Context.UserIdentifier;
        var groupName = $"Project_{projectId}";
        
        _logger.LogInformation("项目 {ProjectId} 工作流阶段变更为 {Stage}，进度 {Progress}%", projectId, stage, progress);
        
        await Clients.Group(groupName).SendAsync("WorkflowStageChanged", new
        {
            ProjectId = projectId,
            Stage = stage,
            Progress = progress,
            UpdatedBy = userId,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知文档生成完成
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="documentType">文档类型</param>
    /// <param name="documentId">文档ID</param>
    public async Task NotifyDocumentGenerated(int projectId, string documentType, int documentId)
    {
        var groupName = $"Project_{projectId}";
        
        _logger.LogInformation("项目 {ProjectId} 生成了新文档 {DocumentType} (ID: {DocumentId})", projectId, documentType, documentId);
        
        await Clients.Group(groupName).SendAsync("DocumentGenerated", new
        {
            ProjectId = projectId,
            DocumentType = documentType,
            DocumentId = documentId,
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 通知代码生成完成
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="taskId">代码生成任务ID</param>
    /// <param name="fileCount">生成的文件数量</param>
    public async Task NotifyCodeGenerated(int projectId, int taskId, int fileCount)
    {
        var groupName = $"Project_{projectId}";
        
        _logger.LogInformation("项目 {ProjectId} 代码生成任务 {TaskId} 完成，生成 {FileCount} 个文件", projectId, taskId, fileCount);
        
        await Clients.Group(groupName).SendAsync("CodeGenerated", new
        {
            ProjectId = projectId,
            TaskId = taskId,
            FileCount = fileCount,
            Timestamp = DateTime.UtcNow
        });
    }
}
