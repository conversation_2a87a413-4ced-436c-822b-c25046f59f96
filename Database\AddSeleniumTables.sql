-- =============================================
-- 快速添加Selenium测试表到现有数据库
-- 版本: 1.0
-- 创建日期: 2025-06-25
-- 描述: 在现有ProjectManagementAI数据库中添加Selenium测试相关表
-- =============================================

USE ProjectManagementAI;
GO

PRINT '🚀 开始添加Selenium测试相关表...';
GO

-- =============================================
-- 1. 检查并创建SeleniumScripts表
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumScripts')
BEGIN
    CREATE TABLE SeleniumScripts (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        Category nvarchar(50) NOT NULL DEFAULT 'ui',
        Code ntext NULL,
        ConfigJson ntext NULL,
        TagsJson nvarchar(1000) NULL,
        Priority nvarchar(20) NOT NULL DEFAULT 'medium',
        Status nvarchar(20) NOT NULL DEFAULT 'draft',
        ProjectId int NULL,
        LastExecutedTime datetime2 NULL,
        ExecutionCount int NOT NULL DEFAULT 0,
        SuccessCount int NOT NULL DEFAULT 0,
        AvgDuration int NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumScripts PRIMARY KEY (Id),
        CONSTRAINT FK_SeleniumScripts_Projects FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
        CONSTRAINT FK_SeleniumScripts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumScripts_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumScripts_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumScripts_Category CHECK (Category IN ('ui', 'api', 'integration', 'performance')),
        CONSTRAINT CK_SeleniumScripts_Priority CHECK (Priority IN ('low', 'medium', 'high')),
        CONSTRAINT CK_SeleniumScripts_Status CHECK (Status IN ('draft', 'ready', 'running', 'failed', 'archived'))
    );
    
    PRINT '✅ SeleniumScripts 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumScripts 表已存在';
END
GO

-- =============================================
-- 2. 检查并创建SeleniumExecutions表
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumExecutions')
BEGIN
    CREATE TABLE SeleniumExecutions (
        Id int IDENTITY(1,1) NOT NULL,
        ExecutionId nvarchar(50) NOT NULL,
        ScriptId int NOT NULL,
        Status nvarchar(20) NOT NULL DEFAULT 'running',
        StartTime datetime2 NOT NULL,
        EndTime datetime2 NULL,
        Duration int NULL,
        ErrorMessage ntext NULL,
        ConfigJson ntext NULL,
        StatsJson ntext NULL,
        ScreenshotsJson ntext NULL,
        Environment nvarchar(500) NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumExecutions PRIMARY KEY (Id),
        CONSTRAINT UK_SeleniumExecutions_ExecutionId UNIQUE (ExecutionId),
        CONSTRAINT FK_SeleniumExecutions_Scripts FOREIGN KEY (ScriptId) REFERENCES SeleniumScripts(Id) ON DELETE CASCADE,
        CONSTRAINT FK_SeleniumExecutions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutions_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutions_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumExecutions_Status CHECK (Status IN ('running', 'success', 'failed', 'stopped', 'timeout'))
    );
    
    PRINT '✅ SeleniumExecutions 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumExecutions 表已存在';
END
GO

-- =============================================
-- 3. 检查并创建SeleniumExecutionLogs表
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumExecutionLogs')
BEGIN
    CREATE TABLE SeleniumExecutionLogs (
        Id int IDENTITY(1,1) NOT NULL,
        ExecutionId int NOT NULL,
        Level nvarchar(20) NOT NULL DEFAULT 'info',
        Message ntext NOT NULL,
        Step nvarchar(200) NULL,
        Timestamp datetime2 NOT NULL DEFAULT GETDATE(),
        ExtraData ntext NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumExecutionLogs PRIMARY KEY (Id),
        CONSTRAINT FK_SeleniumExecutionLogs_Executions FOREIGN KEY (ExecutionId) REFERENCES SeleniumExecutions(Id) ON DELETE CASCADE,
        CONSTRAINT FK_SeleniumExecutionLogs_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutionLogs_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutionLogs_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumExecutionLogs_Level CHECK (Level IN ('debug', 'info', 'warning', 'error', 'success'))
    );
    
    PRINT '✅ SeleniumExecutionLogs 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumExecutionLogs 表已存在';
END
GO

-- =============================================
-- 4. 创建索引
-- =============================================
PRINT '📊 创建索引...';

-- SeleniumScripts 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Name')
    CREATE INDEX IX_SeleniumScripts_Name ON SeleniumScripts(Name);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Category')
    CREATE INDEX IX_SeleniumScripts_Category ON SeleniumScripts(Category);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Status')
    CREATE INDEX IX_SeleniumScripts_Status ON SeleniumScripts(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_ProjectId')
    CREATE INDEX IX_SeleniumScripts_ProjectId ON SeleniumScripts(ProjectId);

-- SeleniumExecutions 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_ScriptId')
    CREATE INDEX IX_SeleniumExecutions_ScriptId ON SeleniumExecutions(ScriptId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_Status')
    CREATE INDEX IX_SeleniumExecutions_Status ON SeleniumExecutions(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_StartTime')
    CREATE INDEX IX_SeleniumExecutions_StartTime ON SeleniumExecutions(StartTime);

-- SeleniumExecutionLogs 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_ExecutionId')
    CREATE INDEX IX_SeleniumExecutionLogs_ExecutionId ON SeleniumExecutionLogs(ExecutionId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_Level')
    CREATE INDEX IX_SeleniumExecutionLogs_Level ON SeleniumExecutionLogs(Level);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_Timestamp')
    CREATE INDEX IX_SeleniumExecutionLogs_Timestamp ON SeleniumExecutionLogs(Timestamp);

PRINT '✅ 索引创建完成';
GO

-- =============================================
-- 5. 添加中文注释
-- =============================================
PRINT '💬 添加中文注释...';

-- SeleniumScripts 表注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium测试脚本表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Name';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Description';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本分类(ui/api/integration/performance)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Category';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Python代码内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Code';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'配置信息JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'ConfigJson';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'优先级(low/medium/high)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Priority';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'状态(draft/ready/running/failed/archived)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Status';

-- SeleniumExecutions 表注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium执行记录表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'执行唯一标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions', @level2type = N'COLUMN', @level2name = N'ExecutionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions', @level2type = N'COLUMN', @level2name = N'ScriptId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'执行状态(running/success/failed/stopped/timeout)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions', @level2type = N'COLUMN', @level2name = N'Status';

-- SeleniumExecutionLogs 表注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium执行日志表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'执行记录ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs', @level2type = N'COLUMN', @level2name = N'ExecutionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'日志级别(debug/info/warning/error/success)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs', @level2type = N'COLUMN', @level2name = N'Level';

PRINT '✅ 中文注释添加完成';
GO

-- =============================================
-- 6. 插入示例数据
-- =============================================
PRINT '📝 插入示例数据...';

-- 插入示例脚本
IF NOT EXISTS (SELECT 1 FROM SeleniumScripts WHERE Name = '用户登录测试')
BEGIN
    INSERT INTO SeleniumScripts (Name, Description, Category, Priority, Status, CreatedBy, CreatedTime)
    VALUES (
        N'用户登录测试',
        N'测试用户登录功能的完整流程',
        N'ui',
        N'high',
        N'ready',
        1,
        GETDATE()
    );
    
    PRINT '✅ 已插入示例测试脚本';
END

PRINT '🎉 Selenium测试表添加完成！';
PRINT '';
PRINT '📋 已创建的表:';
PRINT '   - SeleniumScripts (测试脚本表)';
PRINT '   - SeleniumExecutions (执行记录表)';
PRINT '   - SeleniumExecutionLogs (执行日志表)';
PRINT '';
PRINT '🔗 已创建的关系:';
PRINT '   - SeleniumScripts → Projects (项目关联)';
PRINT '   - SeleniumExecutions → SeleniumScripts (脚本关联)';
PRINT '   - SeleniumExecutionLogs → SeleniumExecutions (日志关联)';
PRINT '';
PRINT '📊 已创建性能优化索引';
PRINT '💬 已添加中文字段注释';
PRINT '📝 已插入示例数据';
PRINT '';
PRINT '✨ 现在可以在前端使用Selenium测试功能了！';
GO
