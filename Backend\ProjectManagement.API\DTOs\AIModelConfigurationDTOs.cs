using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.API.DTOs;

/// <summary>
/// 创建AI模型配置请求DTO
/// </summary>
public class CreateAIModelConfigRequest
{
    /// <summary>
    /// AI模型名称
    /// </summary>
    [Required(ErrorMessage = "模型名称不能为空")]
    [StringLength(100, ErrorMessage = "模型名称长度不能超过100个字符")]
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型用途类型
    /// </summary>
    [Required(ErrorMessage = "模型类型不能为空")]
    [StringLength(50, ErrorMessage = "模型类型长度不能超过50个字符")]
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [StringLength(500, ErrorMessage = "API端点长度不能超过500个字符")]
    [Url(ErrorMessage = "API端点格式不正确")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    [StringLength(255, ErrorMessage = "API密钥长度不能超过255个字符")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置，JSON格式
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用该模型配置
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 更新AI模型配置请求DTO
/// </summary>
public class UpdateAIModelConfigRequest
{
    /// <summary>
    /// AI模型名称
    /// </summary>
    [StringLength(100, ErrorMessage = "模型名称长度不能超过100个字符")]
    public string? ModelName { get; set; }

    /// <summary>
    /// 模型用途类型
    /// </summary>
    [StringLength(50, ErrorMessage = "模型类型长度不能超过50个字符")]
    public string? ModelType { get; set; }

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [StringLength(500, ErrorMessage = "API端点长度不能超过500个字符")]
    [Url(ErrorMessage = "API端点格式不正确")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    [StringLength(255, ErrorMessage = "API密钥长度不能超过255个字符")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置，JSON格式
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用该模型配置
    /// </summary>
    public bool? IsActive { get; set; }
}

/// <summary>
/// 切换配置状态请求DTO
/// </summary>
public class ToggleConfigRequest
{
    /// <summary>
    /// 是否启用
    /// </summary>
    [Required(ErrorMessage = "启用状态不能为空")]
    public bool IsActive { get; set; }
}

/// <summary>
/// AI模型配置响应DTO
/// </summary>
public class AIModelConfigurationDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// AI模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型用途类型
    /// </summary>
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥（脱敏显示）
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用该模型配置
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 配置创建时间
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// 配置最后更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// AI提供商配置请求DTO
/// </summary>
public class ProviderConfigRequest
{
    /// <summary>
    /// 提供商名称
    /// </summary>
    [Required(ErrorMessage = "提供商名称不能为空")]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// 配置参数
    /// </summary>
    [Required(ErrorMessage = "配置参数不能为空")]
    public Dictionary<string, object> Config { get; set; } = new();
}

/// <summary>
/// 批量更新配置请求DTO
/// </summary>
public class BatchUpdateConfigRequest
{
    /// <summary>
    /// 配置列表
    /// </summary>
    [Required(ErrorMessage = "配置列表不能为空")]
    public List<UpdateAIModelConfigRequest> Configurations { get; set; } = new();
}

/// <summary>
/// AI配置导入请求DTO
/// </summary>
public class ImportConfigRequest
{
    /// <summary>
    /// 配置数据
    /// </summary>
    [Required(ErrorMessage = "配置数据不能为空")]
    public string ConfigData { get; set; } = string.Empty;

    /// <summary>
    /// 导入选项
    /// </summary>
    public List<string> ImportOptions { get; set; } = new();

    /// <summary>
    /// 冲突处理策略
    /// </summary>
    public string ConflictStrategy { get; set; } = "merge";
}

/// <summary>
/// 配置验证结果DTO
/// </summary>
public class ConfigValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 提供商测试结果DTO
/// </summary>
public class ProviderTestResult
{
    /// <summary>
    /// 测试是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 测试消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public double? ResponseTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? Error { get; set; }
}

/// <summary>
/// AI使用统计DTO
/// </summary>
public class AIUsageStatsDto
{
    /// <summary>
    /// 总请求数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 预估成本
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime LastUsed { get; set; }

    /// <summary>
    /// 提供商使用分布
    /// </summary>
    public Dictionary<string, ProviderUsageStats> ProviderBreakdown { get; set; } = new();
}

/// <summary>
/// 提供商使用统计
/// </summary>
public class ProviderUsageStats
{
    /// <summary>
    /// 请求数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// Token使用量
    /// </summary>
    public long Tokens { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }
}
