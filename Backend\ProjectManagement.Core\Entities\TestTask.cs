using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 测试任务实体 - 对应DatabaseSchema.sql中的TestTasks表
/// 功能: 管理AI生成的测试用例和测试执行结果
/// 支持: 多测试类型、测试框架、结果分析、自动化测试
/// </summary>
[SugarTable("TestTasks", "AI测试生成和执行管理表")]
public class TestTask
{
    /// <summary>
    /// 测试任务唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "测试任务唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的代码生成任务ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的代码生成任务ID，可为空")]
    public int? CodeGenerationTaskId { get; set; }

    /// <summary>
    /// 测试名称（如：用户登录功能测试）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "测试名称")]
    public string TestName { get; set; } = string.Empty;

    /// <summary>
    /// 测试类型: Unit(单元测试), Integration(集成测试), E2E(端到端测试), Performance(性能测试)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "测试类型")]
    public string TestType { get; set; } = string.Empty;

    /// <summary>
    /// 测试框架: xUnit, Jest, Cypress, Selenium, JMeter等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "测试框架")]
    public string? TestFramework { get; set; }

    /// <summary>
    /// 测试状态: Pending(待执行), Running(执行中), Passed(通过), Failed(失败)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "测试状态")]
    public string Status { get; set; } = "Pending";

    /// <summary>
    /// AI生成的测试代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成的测试代码")]
    public string? TestCode { get; set; }

    /// <summary>
    /// 测试执行结果，JSON格式存储（通过率、覆盖率、执行时间等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "测试执行结果，JSON格式存储")]
    public string? TestResults { get; set; }

    /// <summary>
    /// 测试失败时的错误信息
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "测试失败时的错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 测试任务创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "测试任务创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 测试任务完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "测试任务完成时间")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的代码生成任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public CodeGenerationTask? CodeGenerationTask { get; set; }
}
