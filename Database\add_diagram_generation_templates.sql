-- =============================================
-- 添加ER图和Context图生成的Prompt模板
-- 用于AI图表生成功能
-- =============================================

USE ProjectManagementAI;
GO

-- 检查是否需要添加新的任务类型到约束中
-- 首先删除现有约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE PromptTemplates DROP CONSTRAINT CK_PromptTemplates_TaskType;
    PRINT '已删除旧的TaskType约束';
END

-- 添加新的约束，包含图表生成任务类型
ALTER TABLE PromptTemplates ADD CONSTRAINT CK_PromptTemplates_TaskType
CHECK (TaskType IN ('RequirementAnalysis', 'CodeGeneration', 'Testing', 'Debugging', 'Documentation', 'Review', 'ERDiagramGeneration', 'ContextDiagramGeneration'));
PRINT '已添加新的TaskType约束，包含图表生成类型';

-- 同时更新UserTaskMappings表的约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserTaskMappings_TaskType')
BEGIN
    ALTER TABLE UserTaskMappings DROP CONSTRAINT CK_UserTaskMappings_TaskType;
    PRINT '已删除旧的UserTaskMappings TaskType约束';
END

ALTER TABLE UserTaskMappings ADD CONSTRAINT CK_UserTaskMappings_TaskType
CHECK (TaskType IN ('RequirementAnalysis', 'CodeGeneration', 'DocumentGeneration', 'Embeddings', 'Testing', 'Debugging', 'ERDiagramGeneration', 'ContextDiagramGeneration'));
PRINT '已添加新的UserTaskMappings TaskType约束，包含图表生成类型';
GO

-- 添加图表设计分类（如果不存在）
IF NOT EXISTS (SELECT * FROM PromptCategories WHERE Name = '图表设计')
BEGIN
    INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, CreatedTime)
    VALUES ('图表设计', '用于ER图和上下文图等设计图表生成的提示词模板', 'diagram', '#13c2c2', 7, GETDATE());
    PRINT '已添加图表设计分类';
END
ELSE
BEGIN
    PRINT '图表设计分类已存在';
END
GO

-- =============================================
-- 1. ER图生成模板
-- =============================================
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'ER图生成标准模板')
BEGIN
    DECLARE @DiagramCategoryId INT;
    SELECT @DiagramCategoryId = Id FROM PromptCategories WHERE Name = '图表设计';

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, IsEnabled, Tags, CreatedTime
    )
    VALUES (
        'ER图生成标准模板',
        '用于生成数据库实体关系图的标准Prompt模板，支持多种数据库类型',
        @DiagramCategoryId,
        N'请根据以下要求生成一个{{databaseType}}数据库的ER图，使用Mermaid语法：

项目ID: {{projectId}}
数据库类型: {{databaseType}}
图表格式: {{diagramFormat}}
{{#if requirementDocumentId}}需求文档ID: {{requirementDocumentId}}{{/if}}

要求：
1. 生成完整的实体关系图，体现业务逻辑
2. 包含主键(PK)和外键(FK)标识
3. 定义实体之间的关系（一对一、一对多、多对多）
4. 使用标准的{{databaseType}}数据类型
5. 包含常见的业务实体和系统实体
6. 遵循数据库设计最佳实践

请直接返回Mermaid ER图代码，格式如下：
```mermaid
erDiagram
    USER {
        int id PK
        string username
        string email
        datetime created_at
    }

    PROJECT {
        int id PK
        string name
        int owner_id FK
        datetime created_at
    }

    USER ||--o{ PROJECT : owns
```

请生成符合项目管理系统的完整ER图，确保：
- 实体命名清晰且符合业务逻辑
- 字段类型准确且符合{{databaseType}}规范
- 关系定义正确且完整
- 包含必要的索引字段
- 支持软删除和审计字段',
        N'[
            {
                "name": "projectId",
                "type": "number",
                "required": true,
                "description": "项目ID"
            },
            {
                "name": "databaseType",
                "type": "string",
                "required": true,
                "default": "SqlServer",
                "options": ["SqlServer", "MySQL", "PostgreSQL", "Oracle"],
                "description": "数据库类型"
            },
            {
                "name": "diagramFormat",
                "type": "string",
                "required": true,
                "default": "Mermaid",
                "description": "图表格式"
            },
            {
                "name": "requirementDocumentId",
                "type": "number",
                "required": false,
                "description": "关联的需求文档ID"
            }
        ]',
        'System',
        'ERDiagramGeneration',
        'SQL',
        'Mermaid',
        'Medium',
        2000,
        1,
        'ER图,数据库设计,实体关系,Mermaid,数据建模',
        GETDATE()
    );
    PRINT '已添加ER图生成标准模板';
END
ELSE
BEGIN
    PRINT 'ER图生成标准模板已存在';
END
GO

-- =============================================
-- 2. Context图生成模板
-- =============================================
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '上下文图生成标准模板')
BEGIN
    DECLARE @DiagramCategoryId INT;
    SELECT @DiagramCategoryId = Id FROM PromptCategories WHERE Name = '图表设计';

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, IsEnabled, Tags, CreatedTime
    )
    VALUES (
        '上下文图生成标准模板',
        '用于生成系统上下文图的标准Prompt模板，展示系统边界和外部交互',
        @DiagramCategoryId,
        N'请根据以下要求生成一个系统上下文图，使用Mermaid流程图语法：

项目ID: {{projectId}}
图表格式: {{diagramFormat}}
包含外部系统: {{includeExternalSystems}}
{{#if requirementDocumentId}}需求文档ID: {{requirementDocumentId}}{{/if}}

要求：
1. 明确定义系统边界，区分内部组件和外部实体
2. 标识所有外部实体（用户角色、外部系统、第三方服务）
3. 显示数据流向和交互关系
4. 使用子图(subgraph)来表示系统边界
5. {{#if includeExternalSystems}}包含常见的外部服务（数据库、邮件服务、支付网关、文件存储等）{{/if}}
6. 体现系统的主要功能模块和服务

请直接返回Mermaid流程图代码，格式如下：
```mermaid
flowchart TD
    User[用户] --> System[系统核心]
    Admin[管理员] --> System
    System --> Database[(数据库)]
    System --> EmailService[邮件服务]

    subgraph "系统边界"
        System
        Database
    end

    subgraph "外部系统"
        EmailService
    end

    subgraph "用户角色"
        User
        Admin
    end
```

请生成符合项目管理系统的完整上下文图，确保：
- 系统边界清晰明确
- 外部实体分类合理
- 数据流向逻辑正确
- 交互关系完整
- 图表布局美观易读',
        N'[
            {
                "name": "projectId",
                "type": "number",
                "required": true,
                "description": "项目ID"
            },
            {
                "name": "diagramFormat",
                "type": "string",
                "required": true,
                "default": "Mermaid",
                "description": "图表格式"
            },
            {
                "name": "includeExternalSystems",
                "type": "boolean",
                "required": true,
                "default": true,
                "description": "是否包含外部系统"
            },
            {
                "name": "requirementDocumentId",
                "type": "number",
                "required": false,
                "description": "关联的需求文档ID"
            }
        ]',
        'System',
        'ContextDiagramGeneration',
        'Mermaid',
        'Flowchart',
        'Medium',
        1800,
        1,
        '上下文图,系统边界,外部实体,Mermaid,系统设计',
        GETDATE()
    );
    PRINT '已添加上下文图生成标准模板';
END
ELSE
BEGIN
    PRINT '上下文图生成标准模板已存在';
END
GO

-- =============================================
-- 3. 高级ER图生成模板（包含业务规则）
-- =============================================
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = 'ER图生成高级模板（含业务规则）')
BEGIN
    DECLARE @DiagramCategoryId INT;
    SELECT @DiagramCategoryId = Id FROM PromptCategories WHERE Name = '图表设计';

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, IsEnabled, Tags, CreatedTime
    )
    VALUES (
        'ER图生成高级模板（含业务规则）',
        '高级ER图生成模板，包含业务规则验证和约束条件',
        @DiagramCategoryId,
        N'请根据以下要求生成一个高级{{databaseType}}数据库的ER图，包含业务规则和约束：

项目信息：
- 项目ID: {{projectId}}
- 数据库类型: {{databaseType}}
- 图表格式: {{diagramFormat}}
{{#if requirementDocumentId}}
- 需求文档ID: {{requirementDocumentId}}
{{/if}}

高级要求：
1. 生成完整的实体关系图，体现复杂业务逻辑
2. 包含详细的字段定义（主键、外键、唯一键、索引）
3. 定义完整的关系约束和业务规则
4. 包含数据完整性约束（检查约束、默认值）
5. 支持软删除、审计跟踪、版本控制
6. 包含性能优化考虑（索引策略）
7. 遵循{{databaseType}}最佳实践

业务实体要求：
- 用户管理（用户、角色、权限）
- 项目管理（项目、任务、里程碑）
- 文档管理（需求文档、设计文档）
- 工作流管理（状态、审批、通知）
- 系统管理（日志、配置、监控）

请生成包含以下特性的ER图：
```mermaid
erDiagram
    USER {
        int id PK "主键，自增"
        string username UK "用户名，唯一"
        string email UK "邮箱，唯一"
        string password_hash "密码哈希"
        string role "角色：Admin,User,Manager"
        boolean is_active "是否激活，默认true"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }
```

确保生成的ER图：
- 实体命名遵循{{databaseType}}命名规范
- 包含完整的字段注释和约束说明
- 关系定义准确且包含基数约束
- 支持扩展性和维护性
- 考虑查询性能和索引优化',
        N'[
            {
                "name": "projectId",
                "type": "number",
                "required": true,
                "description": "项目ID"
            },
            {
                "name": "databaseType",
                "type": "string",
                "required": true,
                "default": "SqlServer",
                "options": ["SqlServer", "MySQL", "PostgreSQL", "Oracle", "SQLite"],
                "description": "数据库类型"
            },
            {
                "name": "diagramFormat",
                "type": "string",
                "required": true,
                "default": "Mermaid",
                "description": "图表格式"
            },
            {
                "name": "requirementDocumentId",
                "type": "number",
                "required": false,
                "description": "关联的需求文档ID"
            }
        ]',
        'System',
        'ERDiagramGeneration',
        'SQL',
        'Mermaid',
        'Hard',
        3000,
        1,
        'ER图,高级设计,业务规则,约束,数据库优化,Mermaid',
        GETDATE()
    );
    PRINT '已添加ER图生成高级模板';
END
ELSE
BEGIN
    PRINT 'ER图生成高级模板已存在';
END
GO

-- =============================================
-- 4. 微服务架构Context图模板
-- =============================================
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '微服务架构上下文图模板')
BEGIN
    DECLARE @DiagramCategoryId INT;
    SELECT @DiagramCategoryId = Id FROM PromptCategories WHERE Name = '图表设计';

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, IsEnabled, Tags, CreatedTime
    )
    VALUES (
        '微服务架构上下文图模板',
        '专门用于微服务架构的上下文图生成，展示服务间交互和依赖关系',
        @DiagramCategoryId,
        N'请根据以下要求生成一个微服务架构的系统上下文图：

项目信息：
- 项目ID: {{projectId}}
- 图表格式: {{diagramFormat}}
- 包含外部系统: {{includeExternalSystems}}
{{#if requirementDocumentId}}
- 需求文档ID: {{requirementDocumentId}}
{{/if}}

微服务架构要求：
1. 展示微服务之间的交互和依赖关系
2. 明确API网关和服务发现机制
3. 包含数据存储和缓存层
4. 显示外部服务集成点
5. 体现服务的边界和职责
6. 包含监控和日志收集

请生成包含以下微服务的上下文图：
- API网关服务
- 用户服务
- 项目管理服务
- 需求分析服务
- 代码生成服务
- 通知服务
- 文件服务

格式示例：
```mermaid
flowchart TD
    Client[客户端] --> Gateway[API网关]
    Gateway --> UserService[用户服务]
    Gateway --> ProjectService[项目服务]
    Gateway --> AIService[AI服务]

    UserService --> UserDB[(用户数据库)]
    ProjectService --> ProjectDB[(项目数据库)]
    AIService --> Redis[(Redis缓存)]

    subgraph "微服务集群"
        Gateway
        UserService
        ProjectService
        AIService
    end

    subgraph "数据层"
        UserDB
        ProjectDB
        Redis
    end

    subgraph "外部服务"
        EmailService[邮件服务]
        FileStorage[文件存储]
    end
```

确保图表体现：
- 服务的单一职责原则
- 数据的分离和独立性
- 外部依赖的清晰标识
- 通信协议和接口
- 可扩展性和容错性',
        N'[
            {
                "name": "projectId",
                "type": "number",
                "required": true,
                "description": "项目ID"
            },
            {
                "name": "diagramFormat",
                "type": "string",
                "required": true,
                "default": "Mermaid",
                "description": "图表格式"
            },
            {
                "name": "includeExternalSystems",
                "type": "boolean",
                "required": true,
                "default": true,
                "description": "是否包含外部系统"
            },
            {
                "name": "requirementDocumentId",
                "type": "number",
                "required": false,
                "description": "关联的需求文档ID"
            }
        ]',
        'System',
        'ContextDiagramGeneration',
        'Architecture',
        'Microservices',
        'Hard',
        2500,
        1,
        '微服务,架构设计,上下文图,服务边界,API网关,Mermaid',
        GETDATE()
    );
    PRINT '已添加微服务架构上下文图模板';
END
ELSE
BEGIN
    PRINT '微服务架构上下文图模板已存在';
END
GO

-- 更新分类的模板数量统计
UPDATE PromptCategories
SET TemplateCount = (
    SELECT COUNT(*)
    FROM PromptTemplates
    WHERE CategoryId = PromptCategories.Id AND IsDeleted = 0
)
WHERE Name = '图表设计';

PRINT '==============================================';
PRINT '图表生成Prompt模板添加完成！';
PRINT '==============================================';
PRINT '';
PRINT '已添加4个图表生成模板：';
PRINT '1. ER图生成标准模板 - 基础ER图生成';
PRINT '2. 上下文图生成标准模板 - 基础上下文图生成';
PRINT '3. ER图生成高级模板 - 包含业务规则的高级ER图';
PRINT '4. 微服务架构上下文图模板 - 微服务架构专用';
PRINT '';
PRINT '支持的任务类型：';
PRINT '- ERDiagramGeneration (ER图生成)';
PRINT '- ContextDiagramGeneration (上下文图生成)';
PRINT '';
PRINT '模板特性：';
PRINT '- 支持参数化配置';
PRINT '- 包含详细的生成要求';
PRINT '- 提供标准格式示例';
PRINT '- 支持多种数据库类型';
PRINT '- 遵循最佳实践';
GO
