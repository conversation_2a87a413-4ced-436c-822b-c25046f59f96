#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版本UI自动化管理系统
结合后端API实现完整的自动化功能
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from pathlib import Path
import json

# 添加父目录到路径，以便导入现有模块
sys.path.append(str(Path(__file__).parent.parent))

from api_client import APIClient
from automation_manager import AutomationManager
from enhanced_automation_manager import EnhancedAutomationManager
from development_steps_ui_refactored import DevelopmentStepsUI
from settings_ui import SettingsUI


class MainApplication:
    """主应用程序类"""

    def __init__(self):
        """初始化主应用程序"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()

        # 初始化组件
        self.api_client = APIClient()

        # 选择使用增强版本还是原版本的自动化管理器
        try:
            # 优先使用增强版本
            self.automation_manager = EnhancedAutomationManager(self.api_client)
            self.manager_version = "Enhanced"
            print("✅ 使用增强自动化管理器 (支持新的ActionType + LogicType格式)")
        except Exception as e:
            # 回退到原版本
            print(f"⚠️ 增强管理器初始化失败: {e}")
            print("🔄 回退到原版自动化管理器")
            self.automation_manager = AutomationManager(self.api_client)
            self.manager_version = "Classic"

        # 创建界面
        self.create_menu()
        self.create_main_interface()

        # 状态变量
        self.current_tab = None
        self.is_connected = False

        # 启动时检查连接
        self.check_api_connection()

    def setup_window(self):
        """设置主窗口"""
        # 根据管理器版本设置标题
        version_info = f" ({self.manager_version})" if hasattr(self, 'manager_version') else ""
        self.root.title(f"UI自动化管理系统 v2.0{version_info}")
        self.root.minsize(1200, 800)

        # 设置图标（如果存在）
        try:
            icon_path = Path(__file__).parent.parent / "templates" / "ui" / "app_icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass

        # 居中显示窗口
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 根据屏幕大小自适应窗口大小
        # 使用更大的窗口尺寸以充分利用屏幕空间
        if screen_width <= 1366 or screen_height <= 768:
            window_width = min(1300, int(screen_width * 0.95))  # 屏幕宽度的95%，最大1300
            window_height = min(750, int(screen_height * 0.92))  # 屏幕高度的92%，最大750
        else:
            # 大屏幕使用更大的尺寸
            window_width = min(1500, int(screen_width * 0.9))
            window_height = min(950, int(screen_height * 0.9))

        # 确保窗口不会超出屏幕边界，但留更少的边距
        window_width = min(window_width, screen_width - 50)
        window_height = min(window_height, screen_height - 80)  # 为任务栏留出空间

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置和大小
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 确保窗口显示在最前面
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()

        # 设置主题
        try:
            style.theme_use('clam')
        except:
            style.theme_use('default')

        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="截图工具", command=self.open_screenshot_tool)
        tools_menu.add_command(label="模板编辑器", command=self.open_template_editor)
        tools_menu.add_separator()
        tools_menu.add_command(label="测试混合UI", command=self.test_hybrid_ui)
        tools_menu.add_command(label="Copilot自动化", command=self.test_copilot_automation)
        tools_menu.add_command(label="测试DOM操作", command=self.test_dom_operations)
        tools_menu.add_separator()
        tools_menu.add_command(label="日志查看器", command=self.open_log_viewer)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 顶部状态栏
        self.create_status_bar(main_frame)

        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建选项卡
        self.create_tabs(content_frame)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # 连接状态
        ttk.Label(status_frame, text="API连接状态:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        self.connection_label = ttk.Label(status_frame, text="检查中...", style='Status.TLabel')
        self.connection_label.pack(side=tk.LEFT, padx=(5, 20))

        # 刷新按钮
        ttk.Button(status_frame, text="🔄 刷新连接",
                  command=self.check_api_connection).pack(side=tk.LEFT, padx=(0, 20))

        # 系统状态
        ttk.Label(status_frame, text="系统状态:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        self.system_status_label = ttk.Label(status_frame, text="就绪", style='Success.TLabel')
        self.system_status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 右侧信息
        self.info_label = ttk.Label(status_frame, text="欢迎使用UI自动化管理系统", style='Status.TLabel')
        self.info_label.pack(side=tk.RIGHT)

    def create_tabs(self, parent):
        """创建选项卡界面"""
        # 创建Notebook
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 开发步骤选项卡 - 放在第一位
        development_frame = ttk.Frame(self.notebook)
        self.notebook.add(development_frame, text="🚀 开发步骤")
        self.development_steps = DevelopmentStepsUI(development_frame, self.api_client, self.automation_manager)

        # 执行监控选项卡 - 已集成到开发步骤UI中
        # execution_frame = ttk.Frame(self.notebook)
        # self.notebook.add(execution_frame, text="📊 执行监控")
        # self.execution_monitor = ExecutionMonitorUI(execution_frame, self.api_client, self.automation_manager)

        # DOM操作选项卡
        dom_frame = ttk.Frame(self.notebook)
        self.notebook.add(dom_frame, text="🤖 DOM操作")
        try:
            from dom_operations_ui import DOMOperationsUI
            self.dom_operations_ui = DOMOperationsUI(dom_frame, self.api_client, main_ui=self)
            print("✅ DOM操作UI已加载")
        except ImportError as e:
            print(f"⚠️ DOM操作UI模块加载失败: {e}")
            self.dom_operations_ui = None
        except Exception as e:
            print(f"⚠️ DOM操作UI初始化失败: {e}")
            self.dom_operations_ui = None

        # Playwright错误监控选项卡
        playwright_frame = ttk.Frame(self.notebook)
        self.notebook.add(playwright_frame, text="🎭 浏览器错误监控")
        try:
            from playwright_error_ui import PlaywrightErrorUI
            self.playwright_error_ui = PlaywrightErrorUI(playwright_frame, self.api_client)
            print("✅ Playwright错误监控UI已加载")
        except ImportError as e:
            print(f"⚠️ Playwright错误监控UI模块加载失败: {e}")
            print("💡 请安装Playwright: pip install playwright && playwright install")
            self.playwright_error_ui = None
        except Exception as e:
            print(f"⚠️ Playwright错误监控UI初始化失败: {e}")
            self.playwright_error_ui = None

        # 设置选项卡
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ 设置")
        self.settings_ui = SettingsUI(settings_frame, self.api_client)

        # 将设置UI引用传递给开发步骤UI
        self.development_steps.set_settings_ui(self.settings_ui)

        # C#编译选项卡 - 已隐藏，但保留对象引用供开发步骤UI使用
        try:
            from csharp_error_ui import CSharpErrorCaptureUI
            csharp_frame = ttk.Frame(self.notebook)
            # self.notebook.add(csharp_frame, text="🔨 编译C#项目")  # 隐藏选项卡
            self.csharp_error_ui = CSharpErrorCaptureUI(csharp_frame, api_client=self.api_client)

            # 将C#编译错误UI引用传递给开发步骤UI
            self.development_steps.set_csharp_error_ui(self.csharp_error_ui)
            print("✅ C#编译错误UI引用已设置到开发步骤UI（选项卡已隐藏，支持API）")

        except ImportError as e:
            print(f"⚠️ C#编译模块加载失败: {e}")
            self.csharp_error_ui = None
        except Exception as e:
            print(f"⚠️ C#编译界面初始化失败: {e}")
            self.csharp_error_ui = None

        # 前端编译选项卡 - 已隐藏，但保留对象引用供开发步骤UI使用
        try:
            from frontend_build_ui import FrontendBuildUI
            frontend_frame = ttk.Frame(self.notebook)
            # self.notebook.add(frontend_frame, text="🌐 编译前端项目")  # 隐藏选项卡
            self.frontend_build_ui = FrontendBuildUI(frontend_frame, api_client=self.api_client)

            # 将前端编译错误UI引用传递给开发步骤UI
            self.development_steps.frontend_build_ui = self.frontend_build_ui
            print("✅ 前端编译错误UI引用已设置到开发步骤UI（选项卡已隐藏，支持API）")

        except ImportError as e:
            print(f"⚠️ 前端编译模块加载失败: {e}")
            self.frontend_build_ui = None
        except Exception as e:
            print(f"⚠️ 前端编译界面初始化失败: {e}")
            self.frontend_build_ui = None

        # 隐藏的选项卡（保留对象引用但不显示）
        # 模板管理选项卡 - 已集成到开发步骤UI中
        # template_frame = ttk.Frame(self.notebook)
        # self.notebook.add(template_frame, text="📋 模板管理")  # 注释掉不显示
        # self.template_manager = TemplateManagerUI(template_frame, self.api_client, self.automation_manager)

        # 序列管理选项卡 - 已集成到开发步骤UI中
        # sequence_frame = ttk.Frame(self.notebook)
        # self.notebook.add(sequence_frame, text="🔄 序列管理")  # 注释掉不显示
        # self.sequence_manager = SequenceManagerUI(sequence_frame, self.api_client, self.automation_manager)

        # 绑定选项卡切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def check_api_connection(self):
        """检查API连接状态"""
        def check_connection():
            try:
                self.connection_label.config(text="检查中...", style='Status.TLabel')
                self.root.update()

                # 测试API连接
                result = self.api_client.test_connection()

                if result:
                    self.is_connected = True
                    self.connection_label.config(text="✅ 已连接", style='Success.TLabel')
                    self.system_status_label.config(text="就绪", style='Success.TLabel')
                    self.info_label.config(text=f"API服务器: {self.api_client.base_url}")
                else:
                    self.is_connected = False
                    self.connection_label.config(text="❌ 连接失败", style='Error.TLabel')
                    self.system_status_label.config(text="离线", style='Error.TLabel')
                    self.info_label.config(text="无法连接到API服务器")

            except Exception as e:
                self.is_connected = False
                self.connection_label.config(text="❌ 连接错误", style='Error.TLabel')
                self.system_status_label.config(text="错误", style='Error.TLabel')
                self.info_label.config(text=f"连接错误: {str(e)}")

        # 在后台线程中检查连接
        threading.Thread(target=check_connection, daemon=True).start()

    def on_tab_changed(self, event):
        """选项卡切换事件"""
        selected_tab = event.widget.tab('current')['text']
        self.current_tab = selected_tab

        # 根据选项卡更新状态信息
        if "开发步骤" in selected_tab:
            self.info_label.config(text="管理和执行开发步骤任务")
        elif "DOM操作" in selected_tab:
            self.info_label.config(text="VSCode DOM自动化操作和Copilot控制")
        elif "浏览器错误监控" in selected_tab:
            self.info_label.config(text="使用Playwright监控浏览器F12控制台错误和运行时错误")
        elif "执行监控" in selected_tab:
            self.info_label.config(text="监控自动化任务执行")
        elif "设置" in selected_tab:
            self.info_label.config(text="系统设置和配置")
        else:
            self.info_label.config(text="欢迎使用UI自动化管理系统")

    def import_config(self):
        """导入配置"""
        messagebox.showinfo("功能开发中", "配置导入功能正在开发中...")

    def export_config(self):
        """导出配置"""
        messagebox.showinfo("功能开发中", "配置导出功能正在开发中...")

    def open_screenshot_tool(self):
        """打开截图工具"""
        try:
            # 截图工具已被删除，显示提示信息
            messagebox.showinfo("提示", "截图工具已集成到模板管理功能中")
        except Exception as e:
            messagebox.showerror("错误", f"打开截图工具失败: {e}")

    def open_template_editor(self):
        """打开模板编辑器"""
        messagebox.showinfo("功能开发中", "模板编辑器正在开发中...")

    def open_log_viewer(self):
        """打开日志查看器"""
        messagebox.showinfo("功能开发中", "日志查看器正在开发中...")

    def test_hybrid_ui(self):
        """测试混合UI操作器"""
        def run_test():
            try:
                # 添加父目录到路径
                sys.path.append(str(Path(__file__).parent.parent))
                from ui_actions import create_ui_actions

                # 创建混合UI操作器
                ui = create_ui_actions(use_hybrid=True)

                # 执行测试操作
                results = []

                # 测试激活VSCode
                if ui.activate_vscode_window():
                    results.append("✅ VSCode激活成功")
                else:
                    results.append("❌ VSCode激活失败")

                # 测试最大化VSCode
                if ui.maximize_vscode_window():
                    results.append("✅ VSCode最大化成功")
                else:
                    results.append("⚠️ VSCode最大化失败")

                # 获取统计信息
                if hasattr(ui, 'get_stats'):
                    stats = ui.get_stats()
                    total = stats.get('total_operations', 0)
                    uia_success = stats.get('uia_success', 0)
                    results.append(f"📊 总操作: {total}, UIA成功: {uia_success}")

                # 显示结果
                result_text = "\n".join(results)
                messagebox.showinfo("混合UI测试结果", result_text)

            except Exception as e:
                messagebox.showerror("测试失败", f"混合UI测试失败:\n{e}")

        # 在后台线程运行测试
        threading.Thread(target=run_test, daemon=True).start()

    def test_copilot_automation(self):
        """测试Copilot自动化"""
        def run_copilot_test():
            try:
                # Copilot测试功能已集成到开发步骤UI中
                messagebox.showinfo("提示", "Copilot测试功能已集成到开发步骤UI中，请使用脚本执行功能")
                return

            except Exception as e:
                messagebox.showerror("测试失败", f"Copilot自动化测试失败:\n{e}")

        # 在后台线程运行测试
        threading.Thread(target=run_copilot_test, daemon=True).start()

    def test_dom_operations(self):
        """测试DOM操作功能"""
        def run_dom_test():
            try:
                import subprocess
                import sys
                from pathlib import Path

                # 运行DOM操作测试脚本
                test_script = Path(__file__).parent / "test_dom_ui.py"
                if test_script.exists():
                    subprocess.Popen([sys.executable, str(test_script)])
                    messagebox.showinfo("提示", "DOM操作测试窗口已启动")
                else:
                    messagebox.showerror("错误", "找不到DOM操作测试脚本")

            except Exception as e:
                messagebox.showerror("测试失败", f"DOM操作测试失败:\n{e}")

        # 在后台线程运行测试
        threading.Thread(target=run_dom_test, daemon=True).start()

    def show_help(self):
        """显示帮助信息"""
        help_text = """
UI自动化管理系统 v2.0 使用指南

主要功能：
1. 模板管理 - 创建、编辑和管理UI自动化模板
2. 序列管理 - 组合模板创建自动化序列
3. 执行监控 - 实时监控自动化任务执行状态
4. 开发步骤 - 管理和执行开发步骤任务，查看执行历史
5. 系统设置 - 配置API连接和系统参数

快捷键：
- Ctrl+R: 刷新当前页面
- Ctrl+S: 保存当前配置
- F5: 刷新API连接
- F1: 显示帮助

更多信息请访问项目文档。
        """
        messagebox.showinfo("使用指南", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
UI自动化管理系统 v2.0

基于Python和Tkinter开发的现代化UI自动化管理工具
结合后端API实现完整的自动化功能

开发团队: ProjectManagement Team
版本: 2.0.0
发布日期: 2025-06-23

技术栈:
- Python 3.x
- Tkinter (GUI)
- Requests (API客户端)
- PyAutoGUI (UI自动化)
- ASP.NET Core (后端API)
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """程序关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出UI自动化管理系统吗？"):
            try:
                # 保存配置
                self.save_config()
                # 清理资源
                self.cleanup()
            except:
                pass
            finally:
                self.root.destroy()

    def save_config(self):
        """保存配置"""
        try:
            config_path = Path(__file__).parent / "config.json"

            # 先读取现有配置
            existing_config = {}
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        existing_config = json.load(f)
                except Exception as e:
                    print(f"读取现有配置失败: {e}")

            # 更新特定字段，保留其他配置
            existing_config.update({
                'window_geometry': self.root.geometry(),
                'current_tab': self.current_tab,
                'api_settings': self.api_client.get_config()
            })

            # 保存合并后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"保存配置失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'automation_manager'):
                self.automation_manager.cleanup()
        except:
            pass

    def run(self):
        """运行应用程序"""
        try:
            # 绑定关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # 启动主循环
            self.root.mainloop()

        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行错误: {e}")
            messagebox.showerror("错误", f"程序运行错误: {e}")


def main():
    """主函数"""
    try:
        # 创建并运行应用程序
        app = MainApplication()
        app.run()

    except Exception as e:
        print(f"启动失败: {e}")
        messagebox.showerror("启动错误", f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
