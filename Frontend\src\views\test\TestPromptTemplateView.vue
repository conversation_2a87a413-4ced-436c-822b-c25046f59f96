<template>
  <div class="test-prompt-template">
    <div class="page-header">
      <h1>🧪 测试提示词模板验证</h1>
      <p>验证测试相关的AI提示词模板是否正确集成</p>
    </div>

    <el-row :gutter="20">
      <!-- 模板列表 -->
      <el-col :span="12">
        <el-card title="测试提示词模板">
          <template #header>
            <div class="card-header">
              <span>测试提示词模板</span>
              <el-button size="small" @click="loadTemplates" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <div v-if="loading" class="loading">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="templates.length === 0" class="empty">
            <el-empty description="暂无测试模板" />
          </div>

          <div v-else class="template-list">
            <div 
              v-for="template in templates" 
              :key="template.id"
              class="template-item"
              :class="{ active: selectedTemplate?.id === template.id }"
              @click="selectTemplate(template)"
            >
              <div class="template-header">
                <h4>{{ template.name }}</h4>
                <el-tag v-if="template.isDefault" type="success" size="small">默认</el-tag>
              </div>
              <p class="template-desc">{{ template.description }}</p>
              <div class="template-meta">
                <el-tag size="small">{{ template.taskType }}</el-tag>
                <el-tag size="small" type="info">{{ template.framework }}</el-tag>
                <span class="usage-count">使用次数: {{ template.usageCount }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 模板详情和测试 -->
      <el-col :span="12">
        <el-card title="模板详情">
          <div v-if="!selectedTemplate" class="empty">
            <el-empty description="请选择一个模板查看详情" />
          </div>

          <div v-else class="template-detail">
            <div class="detail-section">
              <h4>基本信息</h4>
              <el-descriptions :column="1" size="small">
                <el-descriptions-item label="模板名称">{{ selectedTemplate.name }}</el-descriptions-item>
                <el-descriptions-item label="任务类型">{{ selectedTemplate.taskType }}</el-descriptions-item>
                <el-descriptions-item label="框架">{{ selectedTemplate.framework }}</el-descriptions-item>
                <el-descriptions-item label="难度">{{ selectedTemplate.difficulty }}</el-descriptions-item>
                <el-descriptions-item label="预估Token">{{ selectedTemplate.estimatedTokens }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section">
              <h4>模板内容</h4>
              <el-input
                v-model="selectedTemplate.content"
                type="textarea"
                :rows="8"
                readonly
                class="template-content"
              />
            </div>

            <div class="detail-section" v-if="selectedTemplate.parameters">
              <h4>参数定义</h4>
              <el-input
                v-model="selectedTemplate.parameters"
                type="textarea"
                :rows="3"
                readonly
                class="template-params"
              />
            </div>

            <div class="detail-section">
              <h4>测试模板</h4>
              <div class="test-controls">
                <el-input
                  v-model="testCode"
                  type="textarea"
                  :rows="6"
                  placeholder="输入测试代码进行分析..."
                  class="test-input"
                />
                <div class="test-buttons">
                  <el-button 
                    type="primary" 
                    @click="testTemplate" 
                    :loading="testing"
                    :disabled="!testCode.trim()"
                  >
                    <el-icon><Play /></el-icon>
                    测试模板
                  </el-button>
                  <el-button @click="clearTest">
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </div>
              </div>
            </div>

            <div class="detail-section" v-if="testResult">
              <h4>测试结果</h4>
              <el-alert
                :type="testResult.success ? 'success' : 'error'"
                :title="testResult.success ? '测试成功' : '测试失败'"
                :description="testResult.message"
                show-icon
                :closable="false"
              />
              <div v-if="testResult.response" class="test-response">
                <h5>AI响应:</h5>
                <pre>{{ testResult.response }}</pre>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Play, Delete } from '@element-plus/icons-vue'
import { PromptService, type PromptTemplate } from '@/services/promptService'
import { aiProvider } from '@/services/aiProvider'

// 响应式数据
const loading = ref(false)
const testing = ref(false)
const templates = ref<PromptTemplate[]>([])
const selectedTemplate = ref<PromptTemplate | null>(null)
const testCode = ref(`import { test, expect } from '@playwright/test';

test.describe('示例测试', () => {
  test('基础页面测试', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
  });
});`)

const testResult = ref<{
  success: boolean
  message: string
  response?: string
} | null>(null)

// 方法
const loadTemplates = async () => {
  try {
    loading.value = true
    const promptService = new PromptService()
    templates.value = await promptService.getTemplatesByTaskType('Testing')
    
    if (templates.value.length > 0) {
      selectedTemplate.value = templates.value.find(t => t.isDefault) || templates.value[0]
    }
    
    ElMessage.success(`加载了 ${templates.value.length} 个测试模板`)
  } catch (error: any) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const selectTemplate = (template: PromptTemplate) => {
  selectedTemplate.value = template
  testResult.value = null
}

const testTemplate = async () => {
  if (!selectedTemplate.value || !testCode.value.trim()) {
    return
  }

  try {
    testing.value = true
    testResult.value = null

    // 使用模板生成提示词
    let prompt = selectedTemplate.value.content
    
    // 替换参数
    prompt = prompt
      .replace(/{testCode}/g, testCode.value)
      .replace(/{language}/g, 'JavaScript')
      .replace(/{framework}/g, 'Playwright')
      .replace(/{testType}/g, 'E2E测试')

    // 调用AI服务
    const response = await aiProvider.generateContent(prompt, 'test-analysis')
    
    // 记录模板使用
    const promptService = new PromptService()
    await promptService.recordTemplateUsage(selectedTemplate.value.id, {
      generatedPrompt: prompt,
      aiResponse: response,
      isSuccess: true
    })

    testResult.value = {
      success: true,
      message: '模板测试成功，AI响应正常',
      response: response
    }

    ElMessage.success('模板测试成功')
  } catch (error: any) {
    console.error('模板测试失败:', error)
    testResult.value = {
      success: false,
      message: '模板测试失败: ' + (error.message || '未知错误')
    }
    ElMessage.error('模板测试失败')
  } finally {
    testing.value = false
  }
}

const clearTest = () => {
  testCode.value = ''
  testResult.value = null
}

// 生命周期
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.test-prompt-template {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-list {
  max-height: 500px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.template-item.active {
  border-color: #409eff;
  background-color: #e6f7ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.template-desc {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.usage-count {
  color: #909399;
  margin-left: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.detail-section h5 {
  margin: 12px 0 8px 0;
  color: #606266;
  font-size: 13px;
}

.test-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-buttons {
  display: flex;
  gap: 8px;
}

.test-response {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.test-response pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.loading, .empty {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}
</style>
