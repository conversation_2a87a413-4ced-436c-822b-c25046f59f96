using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// UI操作类型DTO
    /// </summary>
    public class UIActionTypeDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// UI操作类型值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// UI操作描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 是否内置类型
        /// </summary>
        public bool IsBuiltIn { get; set; }

        /// <summary>
        /// 是否需要模板
        /// </summary>
        public bool NeedsTemplate { get; set; }

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }

    /// <summary>
    /// 创建UI操作类型DTO
    /// </summary>
    public class CreateUIActionTypeDto
    {
        /// <summary>
        /// UI操作类型值
        /// </summary>
        [Required(ErrorMessage = "UI操作类型值不能为空")]
        [StringLength(50, ErrorMessage = "UI操作类型值长度不能超过50个字符")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [Required(ErrorMessage = "显示名称不能为空")]
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// UI操作描述
        /// </summary>
        [StringLength(500, ErrorMessage = "UI操作描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否需要模板
        /// </summary>
        public bool NeedsTemplate { get; set; } = false;

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }
    }

    /// <summary>
    /// 更新UI操作类型DTO
    /// </summary>
    public class UpdateUIActionTypeDto
    {
        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string? Label { get; set; }

        /// <summary>
        /// 动作描述
        /// </summary>
        [StringLength(500, ErrorMessage = "动作描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 分类(basic/flow)
        /// </summary>
        [StringLength(50, ErrorMessage = "分类长度不能超过50个字符")]
        public string? Category { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int? SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否需要模板
        /// </summary>
        public bool? NeedsTemplate { get; set; }

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }
    }

    /// <summary>
    /// UI操作类型查询DTO
    /// </summary>
    public class UIActionTypeQueryDto : PageQueryDto
    {
        /// <summary>
        /// 关键词搜索
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 状态过滤
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否内置类型
        /// </summary>
        public bool? IsBuiltIn { get; set; }
    }
}
