<template>
  <div class="testing-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>🧪 测试管理概览</h1>
      <p>统一管理和监控各类自动化测试</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon selenium">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.seleniumTests }}</div>
              <div class="stat-label">Selenium测试</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon api">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.apiTests }}</div>
              <div class="stat-label">API测试</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon performance">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.performanceTests }}</div>
              <div class="stat-label">性能测试</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions">
      <template #header>
        <span>🚀 快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="action-item" @click="navigateTo('/testing/selenium')">
            <el-icon class="action-icon"><Setting /></el-icon>
            <div class="action-content">
              <h3>Selenium自动化</h3>
              <p>创建和管理Web UI自动化测试脚本</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="navigateTo('/testing/api-testing')">
            <el-icon class="action-icon"><Connection /></el-icon>
            <div class="action-content">
              <h3>API测试</h3>
              <p>接口功能和性能测试</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="navigateTo('/testing/performance')">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <div class="action-content">
              <h3>性能测试</h3>
              <p>系统负载和压力测试</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="navigateTo('/testing/playwright')">
            <el-icon class="action-icon"><VideoPlay /></el-icon>
            <div class="action-content">
              <h3>Playwright现代化测试</h3>
              <p>现代化的端到端测试框架</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="runAllTests">
            <el-icon class="action-icon"><VideoPlay /></el-icon>
            <div class="action-content">
              <h3>执行所有测试</h3>
              <p>一键运行所有自动化测试</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近执行记录 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="recent-executions">
          <template #header>
            <div class="card-header">
              <span>📊 最近执行记录</span>
              <el-button type="text" @click="refreshExecutions">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <el-table :data="recentExecutions" style="width: 100%">
            <el-table-column prop="testName" label="测试名称" />
            <el-table-column prop="type" label="类型">
              <template #default="{ row }">
                <el-tag :type="getTestTypeColor(row.type)">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="执行时长" />
            <el-table-column prop="executedAt" label="执行时间">
              <template #default="{ row }">
                {{ formatDateTime(row.executedAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewDetails(row)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="text" size="small" @click="rerunTest(row)">
                  <el-icon><Refresh /></el-icon>
                  重跑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="test-calendar">
          <template #header>
            <span>📅 测试日历</span>
          </template>
          <el-calendar v-model="calendarValue" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getTestTypeColor, getStatusColor } from '@/types/element-plus'
import {
  Setting,
  Connection,
  TrendCharts,
  CircleCheck,
  VideoPlay,
  Refresh,
  View
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const stats = reactive({
  seleniumTests: 12,
  apiTests: 8,
  performanceTests: 3,
  successRate: 85
})

const calendarValue = ref(new Date())

const recentExecutions = ref([
  {
    id: 1,
    testName: '用户登录流程测试',
    type: 'Selenium',
    status: '成功',
    duration: '2分30秒',
    executedAt: new Date(Date.now() - 1000 * 60 * 30)
  },
  {
    id: 2,
    testName: 'API接口测试套件',
    type: 'API',
    status: '失败',
    duration: '1分15秒',
    executedAt: new Date(Date.now() - 1000 * 60 * 60)
  },
  {
    id: 3,
    testName: '页面性能测试',
    type: '性能',
    status: '成功',
    duration: '5分20秒',
    executedAt: new Date(Date.now() - 1000 * 60 * 120)
  }
])

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const runAllTests = () => {
  ElMessage.info('正在启动所有测试...')
  // TODO: 实现运行所有测试的逻辑
}

const refreshExecutions = () => {
  ElMessage.success('执行记录已刷新')
  // TODO: 实现刷新逻辑
}

const viewDetails = (row: any) => {
  ElMessage.info(`查看测试详情: ${row.testName}`)
  // TODO: 实现查看详情逻辑
}

const rerunTest = (row: any) => {
  ElMessage.info(`重新运行测试: ${row.testName}`)
  // TODO: 实现重跑测试逻辑
}



const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.testing-overview {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 24px;
      color: white;
      
      &.selenium {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.api {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.performance {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.success {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }
}

.quick-actions {
  margin-bottom: 24px;
  
  .action-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    
    .action-icon {
      font-size: 32px;
      color: #409eff;
      margin-right: 16px;
    }
    
    .action-content {
      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        color: #303133;
      }
      
      p {
        margin: 0;
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-executions {
  margin-bottom: 24px;
}

.test-calendar {
  :deep(.el-calendar) {
    .el-calendar__header {
      padding: 8px 16px;
    }
    
    .el-calendar__body {
      padding: 8px;
    }
  }
}
</style>
