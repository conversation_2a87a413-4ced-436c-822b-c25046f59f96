<template>
  <div class="app-breadcrumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbItems"
        :key="index"
        :to="item.to"
      >
        <el-icon v-if="item.icon" class="breadcrumb-icon">
          <component :is="item.icon" />
        </el-icon>
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

interface BreadcrumbItem {
  title: string
  to?: string
  icon?: string
}

const route = useRoute()

const breadcrumbItems = computed(() => {
  const items: BreadcrumbItem[] = []

  // 添加首页
  items.push({
    title: '首页',
    to: '/dashboard',
    icon: 'House'
  })

  // 从路由meta中获取面包屑配置
  if (route.meta.breadcrumb && Array.isArray(route.meta.breadcrumb)) {
    items.push(...route.meta.breadcrumb as BreadcrumbItem[])
  } else {
    // 如果没有配置面包屑，根据路由路径自动生成
    const pathSegments = route.path.split('/').filter(segment => segment)

    if (pathSegments.length > 0) {
      // 根据第一级路径添加对应的面包屑
      const firstSegment = pathSegments[0]
      const routeMap: Record<string, BreadcrumbItem> = {
        'projects': { title: '项目管理', to: '/projects', icon: 'Folder' },
        'requirements': { title: '需求管理', to: '/requirements', icon: 'Document' },
        'design': { title: '设计生成', to: '/design', icon: 'PictureRounded' },
        'automation': { title: 'UI自动化管理', to: '/automation', icon: 'Cpu' },
        'testing': { title: '测试管理', to: '/testing', icon: 'CircleCheck' },
        'deployment': { title: '部署管理', to: '/deployment', icon: 'Upload' },
        'prompt-templates': { title: 'Prompt工程', to: '/prompt-templates', icon: 'EditPen' },
        'prompt-analytics': { title: 'Prompt分析', to: '/prompt-analytics', icon: 'DataAnalysis' },
        'issues': { title: '问题管理', to: '/issues', icon: 'Warning' },
        'settings': { title: '系统设置', to: '/settings', icon: 'Setting' },
        'profile': { title: '个人资料', to: '/profile', icon: 'User' }
      }

      if (routeMap[firstSegment]) {
        items.push(routeMap[firstSegment])

        // 如果是详情页面，添加当前页面标题
        if (pathSegments.length > 1) {
          const currentPageTitle = route.meta.title as string
          if (currentPageTitle && currentPageTitle !== routeMap[firstSegment].title) {
            items.push({
              title: currentPageTitle
            })
          }
        }
      }
    }
  }

  return items
})
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: flex;
  align-items: center;

  :deep(.el-breadcrumb) {
    font-size: 14px;

    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        display: flex;
        align-items: center;
        color: var(--el-text-color-regular);

        &:hover {
          color: var(--el-color-primary);
        }

        &.is-link {
          color: var(--el-text-color-secondary);

          &:hover {
            color: var(--el-color-primary);
          }
        }
      }

      &:last-child {
        .el-breadcrumb__inner {
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }

    .el-breadcrumb__separator {
      color: var(--el-text-color-placeholder);
    }
  }
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}
</style>
