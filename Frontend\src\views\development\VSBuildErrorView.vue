<template>
  <div class="vs-build-error-view">
    <div class="header">
      <h1>Visual Studio 编译错误管理</h1>
      <el-button type="primary" @click="showMonitorDialog = true">
        <el-icon><Monitor /></el-icon>
        开始监控编译
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.totalErrors }}</div>
          <div class="stat-label">总错误数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.resolvedErrors }}</div>
          <div class="stat-label">已解决</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.unresolvedErrors }}</div>
          <div class="stat-label">未解决</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.resolutionRate.toFixed(1) }}%</div>
          <div class="stat-label">解决率</div>
        </div>
      </el-card>
    </div>

    <!-- 编译会话列表 -->
    <el-card class="sessions-card">
      <template #header>
        <div class="card-header">
          <span>编译会话</span>
          <el-button size="small" @click="loadBuildSessions">刷新</el-button>
        </div>
      </template>

      <el-table :data="buildSessions" v-loading="loading">
        <el-table-column prop="sessionId" label="会话ID" width="200" />
        <el-table-column prop="buildResult" label="编译结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getBuildResultType(row.buildResult)">
              {{ row.buildResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalErrors" label="错误数" width="80" />
        <el-table-column prop="totalWarnings" label="警告数" width="80" />
        <el-table-column prop="resolvedErrors" label="已解决" width="80" />
        <el-table-column prop="startTime" label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="aiFixRequested" label="AI修复" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.aiFixRequested" type="success">已请求</el-tag>
            <el-tag v-else type="info">未请求</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewSessionDetails(row)">详情</el-button>
            <el-button 
              size="small" 
              type="primary" 
              :disabled="row.aiFixRequested"
              @click="requestAIFix(row.sessionId)"
            >
              AI修复
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadBuildSessions"
        @current-change="loadBuildSessions"
      />
    </el-card>

    <!-- 监控编译对话框 -->
    <el-dialog v-model="showMonitorDialog" title="监控项目编译" width="500px">
      <el-form :model="monitorForm" label-width="100px">
        <el-form-item label="项目路径" required>
          <el-input v-model="monitorForm.projectPath" placeholder="请输入项目路径" />
        </el-form-item>
        <el-form-item label="项目ID">
          <el-input-number v-model="monitorForm.projectId" :min="1" />
        </el-form-item>
        <el-form-item label="编译配置">
          <el-select v-model="monitorForm.buildConfiguration">
            <el-option label="Debug" value="Debug" />
            <el-option label="Release" value="Release" />
          </el-select>
        </el-form-item>
        <el-form-item label="编译平台">
          <el-select v-model="monitorForm.buildPlatform">
            <el-option label="AnyCPU" value="AnyCPU" />
            <el-option label="x86" value="x86" />
            <el-option label="x64" value="x64" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showMonitorDialog = false">取消</el-button>
        <el-button type="primary" @click="startMonitoring" :loading="monitoring">
          开始监控
        </el-button>
      </template>
    </el-dialog>

    <!-- 会话详情对话框 -->
    <el-dialog v-model="showDetailsDialog" title="编译会话详情" width="80%">
      <div v-if="selectedSession">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会话ID">{{ selectedSession.sessionId }}</el-descriptions-item>
          <el-descriptions-item label="编译结果">
            <el-tag :type="getBuildResultType(selectedSession.buildResult)">
              {{ selectedSession.buildResult }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(selectedSession.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatDateTime(selectedSession.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="错误总数">{{ selectedSession.totalErrors }}</el-descriptions-item>
          <el-descriptions-item label="警告总数">{{ selectedSession.totalWarnings }}</el-descriptions-item>
        </el-descriptions>

        <!-- AI修复响应 -->
        <div v-if="selectedSession.aiFixResponse" class="ai-response">
          <h3>AI修复建议</h3>
          <el-card>
            <pre>{{ selectedSession.aiFixResponse }}</pre>
          </el-card>
        </div>

        <!-- 错误列表 -->
        <div class="errors-section">
          <h3>编译错误列表</h3>
          <el-table :data="sessionErrors" max-height="400">
            <el-table-column prop="errorCode" label="错误代码" width="100" />
            <el-table-column prop="errorMessage" label="错误信息" />
            <el-table-column prop="filePath" label="文件路径" width="200" />
            <el-table-column prop="lineNumber" label="行号" width="80" />
            <el-table-column prop="severity" label="严重程度" width="100">
              <template #default="{ row }">
                <el-tag :type="getSeverityType(row.severity)">
                  {{ row.severity }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isResolved" label="状态" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.isResolved" type="success">已解决</el-tag>
                <el-tag v-else type="warning">未解决</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button 
                  size="small" 
                  type="success"
                  :disabled="row.isResolved"
                  @click="resolveError(row.id)"
                >
                  标记解决
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor } from '@element-plus/icons-vue'
import { ApiService } from '@/services/api'

// 响应式数据
const loading = ref(false)
const monitoring = ref(false)
const showMonitorDialog = ref(false)
const showDetailsDialog = ref(false)

// 统计数据
const statistics = ref({
  totalErrors: 0,
  resolvedErrors: 0,
  unresolvedErrors: 0,
  resolutionRate: 0
})

// 编译会话列表
const buildSessions = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 监控表单
const monitorForm = ref({
  projectPath: '',
  projectId: null,
  buildConfiguration: 'Debug',
  buildPlatform: 'AnyCPU'
})

// 选中的会话和错误
const selectedSession = ref<any>(null)
const sessionErrors = ref<any[]>([])

// 方法
const loadStatistics = async () => {
  try {
    const response = await ApiService.get('/api/VSBuildError/statistics')
    statistics.value = response as any
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadBuildSessions = async () => {
  loading.value = true
  try {
    const response = await ApiService.get<any>('/api/VSBuildError/sessions', {
      params: {
        page: currentPage.value,
        pageSize: pageSize.value
      }
    })
    buildSessions.value = response.data
    total.value = response.total
  } catch (error) {
    console.error('加载编译会话失败:', error)
    ElMessage.error('加载编译会话失败')
  } finally {
    loading.value = false
  }
}

const startMonitoring = async () => {
  if (!monitorForm.value.projectPath) {
    ElMessage.warning('请输入项目路径')
    return
  }

  monitoring.value = true
  try {
    const response = await ApiService.post('/api/VSBuildError/monitor-build', monitorForm.value)
    ElMessage.success(`编译监控已启动，会话ID: ${(response as any).sessionId}`)
    showMonitorDialog.value = false
    await loadBuildSessions()
    await loadStatistics()
  } catch (error) {
    console.error('启动监控失败:', error)
    ElMessage.error('启动监控失败')
  } finally {
    monitoring.value = false
  }
}

const requestAIFix = async (sessionId: string) => {
  try {
    const response = await ApiService.post(`/api/VSBuildError/request-ai-fix/${sessionId}`)
    ElMessage.success('AI修复请求已发送')
    await loadBuildSessions()
  } catch (error) {
    console.error('请求AI修复失败:', error)
    ElMessage.error('请求AI修复失败')
  }
}

const viewSessionDetails = async (session: any) => {
  selectedSession.value = session
  try {
    const response = await ApiService.get(`/api/VSBuildError/session/${session.sessionId}/errors`)
    sessionErrors.value = response as any
    showDetailsDialog.value = true
  } catch (error) {
    console.error('加载会话详情失败:', error)
    ElMessage.error('加载会话详情失败')
  }
}

const resolveError = async (errorId: number) => {
  try {
    await ApiService.put(`/api/VSBuildError/error/${errorId}/resolve`)
    ElMessage.success('错误已标记为已解决')
    await viewSessionDetails(selectedSession.value)
    await loadStatistics()
  } catch (error) {
    console.error('标记错误失败:', error)
    ElMessage.error('标记错误失败')
  }
}

// 辅助方法
const getBuildResultType = (result: string): 'success' | 'danger' | 'warning' | 'info' => {
  switch (result) {
    case 'Success': return 'success'
    case 'Failed': return 'danger'
    case 'Running': return 'warning'
    default: return 'info'
  }
}

const getSeverityType = (severity: string): 'danger' | 'warning' | 'info' => {
  switch (severity.toLowerCase()) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    default: return 'info'
  }
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadStatistics()
  loadBuildSessions()
})
</script>

<style scoped>
.vs-build-error-view {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}

.sessions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-response {
  margin: 20px 0;
}

.ai-response pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.errors-section {
  margin-top: 20px;
}
</style>
