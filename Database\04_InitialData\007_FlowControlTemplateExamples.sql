    -- =============================================
    -- 脚本名称: 007_FlowControlTemplateExamples.sql
    -- 脚本描述: 添加流程控制功能的示例模板序列
    -- 创建时间: 2025-06-24
    -- 功能说明:
    --   1. 创建条件判断执行的示例序列
    --   2. 创建循环执行的示例序列
    --   3. 创建复合流程控制的示例序列
    -- =============================================

    USE [ProjectManagementAI]
    GO

    PRINT '======================================================';
    PRINT '开始执行数据脚本: 007_FlowControlTemplateExamples.sql';
    PRINT '功能: 添加流程控制功能的示例模板序列';
    PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
    PRINT '======================================================';
    PRINT '';

    -- =============================================
    -- 1. 创建条件判断执行示例序列
    -- =============================================

    -- 检查是否已存在数据
    IF NOT EXISTS (SELECT 1 FROM UIAutoMationTemplateSequences WHERE Name = '条件判断执行示例')
    BEGIN
        PRINT '插入条件判断执行示例序列...';

        -- 插入条件判断执行示例序列
        INSERT INTO UIAutoMationTemplateSequences (
            Name, Description, Category, Tags, Notes, IsActive, CreatedTime
        ) VALUES (
            '条件判断执行示例',
            '演示如何使用条件判断来控制步骤执行',
            '流程控制示例',
            'condition,判断,示例',
            '这个序列演示了如何根据条件来决定是否执行某些步骤',
            1,
            GETDATE()
        );

        DECLARE @ConditionSequenceId INT;
        SELECT @ConditionSequenceId = Id FROM UIAutoMationTemplateSequences WHERE Name = '条件判断执行示例';

        IF @ConditionSequenceId IS NOT NULL
        BEGIN
            -- 步骤1: 截图获取当前状态
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ConditionSequenceId, 1, 'screenshot', '截图获取当前状态',
                '{"save_path": "current_state.png", "set_variable": "screenshot_taken"}',
                10, 2, 1, GETDATE()
            );

        -- 步骤2: 条件判断 - 检查是否成功截图
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            ConditionExpression, GroupId, CreatedTime
        ) VALUES (
            @ConditionSequenceId, 2, 'condition', '检查截图是否成功',
            '{"true_action": "continue", "false_action": "skip_to_step", "false_target": 5}',
            5, 1, 1,
            '{screenshot_taken} === true',
            'screenshot_check',
            GETDATE()
        );

        -- 步骤3: 条件为真时执行 - 点击某个元素
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @ConditionSequenceId, 3, 'click', '截图成功后点击继续按钮',
            '{"template_name": "continue_button", "wait_after": 2}',
            10, 3, 1,
            'screenshot_check',
            GETDATE()
        );

        -- 步骤4: 输入成功消息
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @ConditionSequenceId, 4, 'input', '输入成功消息',
            '{"text": "截图成功，继续执行", "clear_first": true}',
            5, 2, 1,
            'screenshot_check',
            GETDATE()
        );

            -- 步骤5: 条件为假时的处理
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ConditionSequenceId, 5, 'input', '截图失败，输入错误消息',
                '{"text": "截图失败，请检查权限", "clear_first": true}',
                5, 2, 1, GETDATE()
            );

            PRINT '条件判断执行示例序列步骤创建完成';
        END
        ELSE
        BEGIN
            PRINT '❌ 条件判断执行示例序列创建失败，序列ID为空';
        END

        PRINT '条件判断执行示例序列创建完成';
    END
    ELSE
    BEGIN
        PRINT '条件判断执行示例序列已存在，跳过创建';
    END

    -- =============================================
    -- 2. 创建循环执行示例序列
    -- =============================================

    IF NOT EXISTS (SELECT 1 FROM UIAutoMationTemplateSequences WHERE Name = '循环执行示例')
    BEGIN
        PRINT '插入循环执行示例序列...';

        -- 插入循环执行示例序列
        INSERT INTO UIAutoMationTemplateSequences (
            Name, Description, Category, Tags, Notes, IsActive, CreatedTime
        ) VALUES (
            '循环执行示例',
            '演示如何使用循环来重复执行一组步骤',
            '流程控制示例',
            'loop,循环,重复,示例',
            '这个序列演示了如何循环执行一组步骤，直到满足退出条件',
            1,
            GETDATE()
        );

        DECLARE @LoopSequenceId INT;
        SELECT @LoopSequenceId = Id FROM UIAutoMationTemplateSequences WHERE Name = '循环执行示例';

        IF @LoopSequenceId IS NOT NULL
        BEGIN
            -- 步骤1: 初始化循环变量
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @LoopSequenceId, 1, 'input', '初始化计数器',
                '{"set_variable": "counter", "value": 0}',
                5, 1, 1, GETDATE()
            );

        -- 步骤2: 循环开始
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            LoopCount, LoopVariable, GroupId, CreatedTime
        ) VALUES (
            @LoopSequenceId, 2, 'loop', '开始循环执行',
            '{"max_iterations": 5, "exit_condition": "{counter} >= 5"}',
            5, 1, 1,
            5, 'i',
            'main_loop',
            GETDATE()
        );

        -- 步骤3: 循环体 - 点击操作
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @LoopSequenceId, 3, 'click', '循环中的点击操作',
            '{"template_name": "target_button", "wait_after": 1}',
            10, 3, 1,
            'main_loop',
            GETDATE()
        );

        -- 步骤4: 循环体 - 等待操作
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @LoopSequenceId, 4, 'wait', '等待响应',
            '{"template_name": "response_indicator", "timeout": 3}',
            10, 2, 1,
            'main_loop',
            GETDATE()
        );

        -- 步骤5: 循环体 - 更新计数器
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @LoopSequenceId, 5, 'input', '更新计数器',
            '{"set_variable": "counter", "value": "{counter} + 1"}',
            5, 1, 1,
            'main_loop',
            GETDATE()
        );

        -- 步骤6: 循环结束标记
        INSERT INTO UIAutoMationTemplateSteps (
            SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
            GroupId, CreatedTime
        ) VALUES (
            @LoopSequenceId, 6, 'loop_end', '循环结束',
            '{"loop_group": "main_loop"}',
            5, 1, 1,
            'main_loop',
            GETDATE()
        );

            -- 步骤7: 循环完成后的操作
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @LoopSequenceId, 7, 'input', '循环完成提示',
                '{"text": "循环执行完成，共执行了 {counter} 次", "clear_first": true}',
                5, 2, 1, GETDATE()
            );

            PRINT '循环执行示例序列步骤创建完成';
        END
        ELSE
        BEGIN
            PRINT '❌ 循环执行示例序列创建失败，序列ID为空';
        END

        PRINT '循环执行示例序列创建完成';
    END
    ELSE
    BEGIN
        PRINT '循环执行示例序列已存在，跳过创建';
    END

    -- =============================================
    -- 3. 创建复合流程控制示例序列
    -- =============================================

    IF NOT EXISTS (SELECT 1 FROM UIAutoMationTemplateSequences WHERE Name = '复合流程控制示例')
    BEGIN
        PRINT '插入复合流程控制示例序列...';

        -- 插入复合流程控制示例序列
        INSERT INTO UIAutoMationTemplateSequences (
            Name, Description, Category, Tags, Notes, IsActive, CreatedTime
        ) VALUES (
            '复合流程控制示例',
            '演示条件判断、循环、跳转等多种流程控制的组合使用',
            '流程控制示例',
            'complex,复合,条件,循环,跳转',
            '这个序列演示了多种流程控制功能的组合使用，包括嵌套条件和循环',
            1,
            GETDATE()
        );

        DECLARE @ComplexSequenceId INT;
        SELECT @ComplexSequenceId = Id FROM UIAutoMationTemplateSequences WHERE Name = '复合流程控制示例';

        IF @ComplexSequenceId IS NOT NULL
        BEGIN
            -- 步骤1: 初始化变量
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 1, 'input', '初始化变量',
                '{"set_variable": "retry_count", "value": 0}',
                5, 1, 1, GETDATE()
            );

            -- 步骤2: 外层循环开始 - 最多重试3次
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                LoopCount, LoopVariable, GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 2, 'loop', '开始重试循环',
                '{"max_iterations": 3, "exit_condition": "{success} === true"}',
                5, 1, 1,
                3, 'retry_index',
                'retry_loop',
                GETDATE()
            );

            -- 步骤3: 更新重试计数
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 3, 'input', '更新重试计数',
                '{"set_variable": "retry_count", "value": "{retry_count} + 1"}',
                5, 1, 1,
                'retry_loop',
                GETDATE()
            );

            -- 步骤4: 尝试执行主要操作
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 4, 'click', '尝试点击目标按钮',
                '{"template_name": "target_button", "wait_after": 2, "set_variable": "click_result"}',
                10, 1, 1,
                'retry_loop',
                GETDATE()
            );

            -- 步骤5: 条件判断 - 检查操作是否成功
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                ConditionExpression, GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 5, 'condition', '检查操作是否成功',
                '{"true_action": "jump_to_step", "true_target": 10, "false_action": "continue"}',
                5, 1, 1,
                '{click_result} === "success"',
                'retry_loop',
                GETDATE()
            );

            -- 步骤6: 操作失败时的处理
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 6, 'input', '记录失败信息',
                '{"text": "第 {retry_count} 次尝试失败", "clear_first": false}',
                5, 1, 1,
                'retry_loop',
                GETDATE()
            );

            -- 步骤7: 条件判断 - 检查是否达到最大重试次数
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                ConditionExpression, GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 7, 'condition', '检查是否达到最大重试次数',
                '{"true_action": "jump_to_step", "true_target": 12, "false_action": "continue"}',
                5, 1, 1,
                '{retry_count} >= 3',
                'retry_loop',
                GETDATE()
            );

            -- 步骤8: 等待后继续重试
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 8, 'delay', '等待后重试',
                '{"seconds": 2}',
                5, 1, 1,
                'retry_loop',
                GETDATE()
            );

            -- 步骤9: 循环结束标记
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                GroupId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 9, 'loop_end', '重试循环结束',
                '{"loop_group": "retry_loop"}',
                5, 1, 1,
                'retry_loop',
                GETDATE()
            );

            -- 步骤10: 成功处理分支
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 10, 'input', '操作成功提示',
                '{"text": "操作成功完成！", "set_variable": "success", "value": true}',
                5, 1, 1, GETDATE()
            );

            -- 步骤11: 跳转到结束
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive,
                JumpToStepId, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 11, 'jump', '跳转到结束',
                '{}',
                5, 1, 1,
                14, -- 跳转到步骤14（结束步骤）
                GETDATE()
            );

            -- 步骤12: 失败处理分支
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 12, 'input', '操作失败提示',
                '{"text": "操作失败，已达到最大重试次数", "set_variable": "success", "value": false}',
                5, 1, 1, GETDATE()
            );

            -- 步骤13: 截图记录失败状态
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 13, 'screenshot', '截图记录失败状态',
                '{"save_path": "failure_state_{retry_count}.png"}',
                10, 1, 1, GETDATE()
            );

            -- 步骤14: 序列结束
            INSERT INTO UIAutoMationTemplateSteps (
                SequenceId, StepOrder, ActionType, Description, Parameters, TimeoutSeconds, MaxRetries, IsActive, CreatedTime
            ) VALUES (
                @ComplexSequenceId, 14, 'input', '序列执行完成',
                '{"text": "复合流程控制示例执行完成，最终状态: {success}"}',
                5, 1, 1, GETDATE()
            );

            PRINT '复合流程控制示例序列步骤创建完成';
        END
        ELSE
        BEGIN
            PRINT '❌ 复合流程控制示例序列创建失败，序列ID为空';
        END

        PRINT '复合流程控制示例序列创建完成';
    END
    ELSE
    BEGIN
        PRINT '复合流程控制示例序列已存在，跳过创建';
    END

    PRINT '';
    PRINT '✅ 示例数据脚本执行完成！';
    PRINT '======================================================';
    PRINT '已创建以下示例模板序列：';
    PRINT '1. 条件判断执行示例 - 演示条件判断功能';
    PRINT '2. 循环执行示例 - 演示循环执行功能';
    PRINT '3. 复合流程控制示例 - 演示多种控制结构组合';
    PRINT '';
    PRINT '这些示例展示了以下新功能：';
    PRINT '- 条件判断：根据表达式结果决定执行路径';
    PRINT '- 循环执行：重复执行一组步骤';
    PRINT '- 变量管理：在步骤间传递和更新变量';
    PRINT '- 分组管理：通过GroupId组织相关步骤';
    PRINT '';
    PRINT '下一步：';
    PRINT '1. 在前端界面中查看这些示例序列';
    PRINT '2. 实现流程控制的执行引擎';
    PRINT '3. 测试各种流程控制场景';
    PRINT '';
    PRINT '脚本执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
    GO
