-- =============================================
-- 移除系统参数分类约束，支持动态分类
-- 创建时间: 2025-07-09
-- 说明: 移除 SystemParameters 表的分类检查约束，允许用户自由创建分类
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始移除系统参数分类约束...';

-- 检查约束是否存在
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_SystemParameters_Category')
BEGIN
    -- 删除分类检查约束
    ALTER TABLE SystemParameters 
    DROP CONSTRAINT CK_SystemParameters_Category;
    
    PRINT '已成功移除分类约束 CK_SystemParameters_Category';
END
ELSE
BEGIN
    PRINT '分类约束 CK_SystemParameters_Category 不存在，无需移除';
END

-- 验证约束是否已被移除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_SystemParameters_Category')
BEGIN
    PRINT '✓ 分类约束已成功移除，现在可以自由创建任意分类';
END
ELSE
BEGIN
    PRINT '✗ 分类约束移除失败，请检查权限或手动执行';
END

-- 显示当前表的约束信息
PRINT '当前 SystemParameters 表的约束信息:';
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition,
    cc.is_disabled AS IsDisabled
FROM sys.check_constraints cc
INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
WHERE t.name = 'SystemParameters';

PRINT '分类约束移除操作完成！';
GO
