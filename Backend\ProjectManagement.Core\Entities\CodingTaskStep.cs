using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 编码任务步骤关联实体
    /// </summary>
    [SugarTable("CodingTaskSteps")]
    public class CodingTaskStep : BaseEntity
    {
        /// <summary>
        /// 编码任务ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int CodingTaskId { get; set; }

        /// <summary>
        /// 开发步骤ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int DevelopmentStepId { get; set; }

        /// <summary>
        /// 步骤在任务中的顺序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int OrderIndex { get; set; } = 0;

        /// <summary>
        /// 步骤状态
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string Status { get; set; } = "NotStarted"; // NotStarted, InProgress, Completed, Skipped

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CompletedTime { get; set; }

        /// <summary>
        /// 实际工时（小时）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ActualHours { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 2000)]
        public string? ExecutionResult { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// 是否完成编码 - 标识该步骤的编码工作是否已完成
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsFinishCoding { get; set; } = false;

        /// <summary>
        /// 是否修复错误 - 标识该步骤是否处于错误修复状态
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsFixError { get; set; } = false;

        // 导航属性（不映射到数据库）
        
        /// <summary>
        /// 关联的编码任务
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CodingTask? CodingTask { get; set; }

        /// <summary>
        /// 关联的开发步骤
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public DevelopmentStep? DevelopmentStep { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }
    }
}
