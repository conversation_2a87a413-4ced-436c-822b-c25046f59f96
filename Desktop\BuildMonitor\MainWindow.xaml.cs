using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using BuildMonitor.Models;
using BuildMonitor.Services;
using BuildMonitor.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Win32;
using Newtonsoft.Json;
using System.Windows.Controls;
using static BuildMonitor.Services.AuthService;

namespace BuildMonitor
{
    public partial class MainWindow : Window
    {
        private DatabaseService? _databaseService;
        private CompilerService? _compilerService;
        private AutoCompileService? _autoCompileService;
        private DispatcherTimer _timer;
        private DispatcherTimer _statusUpdateTimer;
        private IConfiguration? _configuration;
        private AppSettings? _appSettings;
        private List<Project> _projects = new List<Project>();
        private int _selectedProjectId = 1;
        private User? _currentUser;
        private UserSettingsService? _userSettings;

        // UI控件字段声明（如果XAML编译器没有自动生成）
        private MenuItem? _logoutMenuItem;

        public MainWindow()
        {
            InitializeComponent();

            // 加载配置
            LoadConfiguration();
            InitializeServices();
            InitializeTimer();

            // 初始化用户设置服务
            _userSettings = new UserSettingsService();

            // 初始化UI状态（未登录状态）
            UpdateUIForLoginState(false);
            UpdateTabLoginStatus(false);

            LoadProjectPaths();
        }

        private void UpdateUIForLoginState(bool isLoggedIn)
        {
            // 初始化UI控件引用（如果还没有初始化）
            if (_logoutMenuItem == null)
                _logoutMenuItem = FindName("LogoutMenuItem") as MenuItem;

            if (isLoggedIn && _currentUser != null)
            {
                // 已登录状态
                this.Title = $"BuildMonitor - {_currentUser.DisplayName}";

                // 启用退出登录菜单
                if (_logoutMenuItem != null)
                    _logoutMenuItem.IsEnabled = true;

                // 启用主要功能
                ProjectComboBox.IsEnabled = true;
                CompileBackendBtn.IsEnabled = true;
                CompileFrontendBtn.IsEnabled = true;

                // 加载用户项目和错误
                LoadProjects();
                LoadErrors();

                // 如果配置启用了自动编译，则自动启动
                StartAutoCompileIfEnabled();
            }
            else
            {
                // 未登录状态
                this.Title = "BuildMonitor - 请登录";

                // 禁用退出登录菜单
                if (_logoutMenuItem != null)
                    _logoutMenuItem.IsEnabled = false;

                // 禁用主要功能
                ProjectComboBox.IsEnabled = false;
                CompileBackendBtn.IsEnabled = false;
                CompileFrontendBtn.IsEnabled = false;

                // 清空数据
                ProjectComboBox.ItemsSource = null;
                BackendErrorsGrid.ItemsSource = null;
                FrontendErrorsGrid.ItemsSource = null;

                StatusLabel.Text = "请先登录以使用系统功能";
            }
        }

        private bool ShowLoginDialog()
        {
            try
            {
                var loginWindow = new LoginWindow();
                var result = loginWindow.ShowDialog();

                if (result == true && loginWindow.AuthenticatedUser != null)
                {
                    _currentUser = loginWindow.AuthenticatedUser;
                    UpdateUIForLoginState(true);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                var errorMessage = $"登录初始化失败: {ex.Message}\n\n";

                if (ex.Message.Contains("Could not load file or assembly"))
                {
                    errorMessage += "这可能是程序集加载问题，请尝试：\n" +
                                  "1. 重新编译项目\n" +
                                  "2. 检查输出目录是否完整\n" +
                                  "3. 以管理员身份运行";
                }
                else if (ex.Message.Contains("connection") || ex.Message.Contains("server") || ex.Message.Contains("database"))
                {
                    errorMessage += "请检查数据库连接配置：\n" +
                                  $"当前连接字符串: {_appSettings?.ConnectionStrings.DefaultConnection ?? "未配置"}";
                }
                else
                {
                    errorMessage += "请检查配置文件和数据库连接";
                }

                MessageBox.Show(errorMessage, "登录错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void LoadConfiguration()
        {
            try
            {
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                _configuration = builder.Build();
                _appSettings = new AppSettings(_configuration);

                // 验证配置文件加载
                var connectionString = _appSettings.ConnectionStrings.DefaultConnection;
                if (!string.IsNullOrEmpty(connectionString))
                {
                    StatusLabel.Text = "配置文件加载成功";
                    ConfigStatusLabel.Text = "配置: 已加载";
                    ConfigStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    StatusLabel.Text = "警告: 未找到连接字符串配置";
                    ConfigStatusLabel.Text = "配置: 连接字符串缺失";
                    ConfigStatusLabel.Foreground = System.Windows.Media.Brushes.Orange;
                }
            }
            catch (FileNotFoundException)
            {
                StatusLabel.Text = "错误: 未找到配置文件";
                ConfigStatusLabel.Text = "配置: 文件缺失";
                ConfigStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show("未找到appsettings.json配置文件，请确保文件存在于应用程序目录中。",
                    "配置文件缺失", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"加载配置失败: {ex.Message}";
                ConfigStatusLabel.Text = "配置: 加载失败";
                ConfigStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        private void InitializeServices()
        {
            try
            {
                // 从配置文件中获取连接字符串
                var connectionString = _appSettings?.ConnectionStrings.DefaultConnection;

                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException("未配置数据库连接字符串");
                }

                // 从配置文件读取数据库设置
                var enableSqlLogging = _appSettings?.Database.EnableSqlLogging ?? true;
                var commandTimeout = _appSettings?.Database.CommandTimeout ?? 30;

                _databaseService = new DatabaseService(connectionString, enableSqlLogging, commandTimeout);
                _compilerService = new CompilerService();
                _autoCompileService = new AutoCompileService(_compilerService, _databaseService, _appSettings);

                // 订阅自动编译完成事件
                _autoCompileService.CompilationCompleted += OnAutoCompilationCompleted;

                StatusLabel.Text = "服务初始化成功";
                DatabaseStatusLabel.Text = "数据库: 已连接";
                DatabaseStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"服务初始化失败: {ex.Message}";
                DatabaseStatusLabel.Text = "数据库: 连接失败";
                DatabaseStatusLabel.Foreground = System.Windows.Media.Brushes.Red;
                MessageBox.Show($"服务初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeTimer()
        {
            // 时间显示定时器
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += (s, e) => TimeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            _timer.Start();

            // 自动编译状态更新定时器
            _statusUpdateTimer = new DispatcherTimer();
            _statusUpdateTimer.Interval = TimeSpan.FromSeconds(2);
            _statusUpdateTimer.Tick += UpdateAutoCompileStatus;
            _statusUpdateTimer.Start();

            // 初始化间隔时间输入框
            IntervalTextBox.Text = _appSettings?.Build.AutoCompileInterval.ToString() ?? "30";
        }

        private async void CompileBackend_Click(object sender, RoutedEventArgs e)
        {
            if (_compilerService == null || _databaseService == null)
            {
                MessageBox.Show("服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                CompileBackendBtn.IsEnabled = false;
                StatusLabel.Text = "正在编译后端项目...";

                // 后端项目路径和项目ID
                var backendProjectPath = string.IsNullOrEmpty(BackendPathTextBox.Text)
                    ? _appSettings?.Build.BackendProjectPath ?? @"D:\Projects\ProjectManagement\Backend\Backend.csproj"
                    : BackendPathTextBox.Text;
                var projectId = _selectedProjectId;

                // 清空旧的后端错误
                await _databaseService.ClearErrorsAsync("Backend", projectId);

                // 编译并获取错误
                var errors = await _compilerService.CompileCSharpProjectAsync(backendProjectPath, projectId);
                
                // 保存错误到数据库
                if (errors.Any())
                {
                    await _databaseService.InsertBackendErrorsAsync(errors);
                }

                StatusLabel.Text = $"后端编译完成，发现 {errors.Count} 个问题";
                
                // 刷新错误列表
                await LoadBackendErrors();
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"后端编译失败: {ex.Message}";
                MessageBox.Show($"后端编译失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CompileBackendBtn.IsEnabled = true;
            }
        }

        private async void CompileFrontend_Click(object sender, RoutedEventArgs e)
        {
            if (_compilerService == null || _databaseService == null)
            {
                MessageBox.Show("服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                CompileFrontendBtn.IsEnabled = false;
                StatusLabel.Text = "正在编译前端项目...";

                // 前端项目路径和项目ID
                var frontendProjectPath = string.IsNullOrEmpty(FrontendPathTextBox.Text)
                    ? _appSettings?.Build.FrontendProjectPath ?? @"D:\Projects\ProjectManagement\Frontend"
                    : FrontendPathTextBox.Text;
                var projectId = _selectedProjectId;

                // 清空旧的前端错误
                await _databaseService.ClearErrorsAsync("Frontend",projectId);

                // 编译并获取错误
                var errors = await _compilerService.CompileFrontendProjectAsync(frontendProjectPath, projectId);
                
                // 保存错误到数据库
                if (errors.Any())
                {
                    await _databaseService.InsertFrontendErrorsAsync(errors);
                }

                StatusLabel.Text = $"前端编译完成，发现 {errors.Count} 个问题";
                
                // 刷新错误列表
                await LoadFrontendErrors();
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"前端编译失败: {ex.Message}";
                MessageBox.Show($"前端编译失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CompileFrontendBtn.IsEnabled = true;
            }
        }

        private async void ClearErrors_Click(object sender, RoutedEventArgs e)
        {
            if (_databaseService == null)
            {
                MessageBox.Show("服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                var result = MessageBox.Show("确定要清空所有编译错误吗？", "确认", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    await _databaseService.ClearErrorsAsync("Backend");
                    await _databaseService.ClearErrorsAsync("Frontend");
                    
                    await LoadErrors();
                    StatusLabel.Text = "所有编译错误已清空";
                }
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"清空错误失败: {ex.Message}";
                MessageBox.Show($"清空错误失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void Refresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadErrors();
            StatusLabel.Text = "错误列表已刷新";
        }

        private async void CleanupDuplicates_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CleanupDuplicatesBtn.IsEnabled = false;
                StatusLabel.Text = "正在清理编译错误...";

                if (_databaseService != null)
                {
                    // 第一步：清理文件路径中的编译器前缀
                    StatusLabel.Text = "正在清理文件路径前缀...";
                    await _databaseService.CleanupFilePathPrefixesAsync();

                    // 第二步：清理重复记录
                    StatusLabel.Text = "正在清理重复的编译错误...";
                    await _databaseService.ForceCleanupDuplicateErrorsAsync();

                    // 刷新显示
                    await LoadErrors();
                    StatusLabel.Text = "编译错误清理完成";
                }
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"清理错误失败: {ex.Message}";
                MessageBox.Show($"清理错误失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CleanupDuplicatesBtn.IsEnabled = true;
            }
        }

        private void ReloadConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadConfiguration();
                ValidateConfiguration();
                InitializeServices();
                LoadProjects();
                LoadProjectPaths();
                StatusLabel.Text = "配置已重新加载";
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"重载配置失败: {ex.Message}";
                MessageBox.Show($"重载配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ValidateConfiguration()
        {
            if (_appSettings == null)
            {
                throw new InvalidOperationException("配置未加载");
            }

            var issues = new List<string>();

            // 验证连接字符串
            if (string.IsNullOrEmpty(_appSettings.ConnectionStrings.DefaultConnection))
            {
                issues.Add("缺少默认连接字符串");
            }

            // 验证项目路径
            if (string.IsNullOrEmpty(_appSettings.Build.BackendProjectPath))
            {
                issues.Add("缺少后端项目路径配置");
            }
            else if (!File.Exists(_appSettings.Build.BackendProjectPath))
            {
                issues.Add($"后端项目文件不存在: {_appSettings.Build.BackendProjectPath}");
            }

            if (string.IsNullOrEmpty(_appSettings.Build.FrontendProjectPath))
            {
                issues.Add("缺少前端项目路径配置");
            }
            else if (!Directory.Exists(_appSettings.Build.FrontendProjectPath))
            {
                issues.Add($"前端项目目录不存在: {_appSettings.Build.FrontendProjectPath}");
            }

            if (issues.Any())
            {
                var message = "配置验证发现以下问题:\n" + string.Join("\n", issues);
                MessageBox.Show(message, "配置验证警告", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void EditConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

                if (!File.Exists(configPath))
                {
                    MessageBox.Show("配置文件不存在，将创建新的配置文件。", "信息",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // 从模板创建配置文件
                    var templatePath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.template.json");
                    if (File.Exists(templatePath))
                    {
                        File.Copy(templatePath, configPath);
                    }
                    else
                    {
                        // 创建默认配置
                        CreateDefaultConfig(configPath);
                    }
                }

                // 使用默认程序打开配置文件
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = configPath,
                    UseShellExecute = true
                };

                System.Diagnostics.Process.Start(startInfo);

                StatusLabel.Text = "配置文件已打开，修改后请点击'重载配置'";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开配置文件失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateDefaultConfig(string configPath)
        {
            var defaultConfig = @"{
  ""ConnectionStrings"": {
    ""DefaultConnection"": ""Server=.;Database=ProjectManagementAI;Integrated Security=true;TrustServerCertificate=true""
  },
  ""BuildSettings"": {
    ""BackendProjectPath"": ""D:\\Projects\\ProjectManagement\\Backend\\ProjectManagement.API\\ProjectManagement.API.csproj"",
    ""FrontendProjectPath"": ""D:\\Projects\\ProjectManagement\\Frontend""
  },
  ""DatabaseSettings"": {
    ""CommandTimeout"": 30,
    ""EnableSqlLogging"": true
  }
}";
            File.WriteAllText(configPath, defaultConfig);
        }

        private async Task LoadErrors()
        {
            await LoadBackendErrors();
            await LoadFrontendErrors();
        }

        private async Task LoadBackendErrors()
        {
            if (_databaseService == null) return;

            try
            {
                var errors = await _databaseService.GetErrorsAsync("Backend");
                BackendErrorsGrid.ItemsSource = errors;
                BackendErrorCountLabel.Text = $"后端错误: {errors.Count}";
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"加载后端错误失败: {ex.Message}";
            }
        }

        private async Task LoadFrontendErrors()
        {
            if (_databaseService == null) return;

            try
            {
                var errors = await _databaseService.GetErrorsAsync("Frontend");
                errors = errors.Where(a => a.Code.ToUpper() != "NO_PACKAGE_JSON").ToList();
                FrontendErrorsGrid.ItemsSource = errors;
                FrontendErrorCountLabel.Text = $"前端错误: {errors.Count}";
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"加载前端错误失败: {ex.Message}";
            }
        }

        private async void LoadProjects()
        {
            if (_databaseService == null || _currentUser == null)
            {
                StatusLabel.Text = "请先登录以加载项目列表";
                return;
            }

            try
            {
                // 使用当前登录用户的ID获取项目列表（只显示用户负责的项目）
                _projects = await _databaseService.GetProjectsByUserIdAsync(_currentUser.Id);
                ProjectComboBox.ItemsSource = _projects;

                StatusLabel.Text = $"已加载 {_projects.Count} 个项目 (用户: {_currentUser.DisplayName})";

                // 选择配置文件中指定的项目
                var configProjectId = _appSettings?.Build.ProjectId ?? 1;
                var selectedProject = _projects.FirstOrDefault(p => p.Id == configProjectId);
                if (selectedProject != null)
                {
                    ProjectComboBox.SelectedItem = selectedProject;
                    _selectedProjectId = selectedProject.Id;
                }
                else if (_projects.Any())
                {
                    ProjectComboBox.SelectedIndex = 0;
                    _selectedProjectId = _projects[0].Id;
                }
                else
                {
                    StatusLabel.Text = $"用户 {_currentUser.DisplayName} 没有负责的项目";
                }
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"加载项目列表失败: {ex.Message}";
            }
        }

        private void LoadProjectPaths()
        {
            // 从配置文件加载项目路径
            BackendPathTextBox.Text = _appSettings?.Build.BackendProjectPath ?? "";
            FrontendPathTextBox.Text = _appSettings?.Build.FrontendProjectPath ?? "";
        }

        private void ProjectComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (ProjectComboBox.SelectedItem is Project selectedProject)
            {
                _selectedProjectId = selectedProject.Id;
                StatusLabel.Text = $"已选择项目: {selectedProject.Name}";
                SaveProjectPaths();
            }
        }

        private void SelectBackend_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择后端项目文件",
                Filter = "sln文件 (*.sln)|*.sln|C# 项目文件 (*.csproj)|*.csproj|所有文件 (*.*)|*.*",
                InitialDirectory = Path.GetDirectoryName(BackendPathTextBox.Text) ?? @"D:\Projects\ProjectManagement\Backend"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                BackendPathTextBox.Text = openFileDialog.FileName;
                StatusLabel.Text = "后端项目路径已更新";
                SaveProjectPaths();
            }
        }

        private void SelectFrontend_Click(object sender, RoutedEventArgs e)
        {
            var folderDialog = new OpenFolderDialog
            {
                Title = "选择前端项目目录",
                InitialDirectory = FrontendPathTextBox.Text ?? @"D:\Projects\ProjectManagement\Frontend"
            };

            if (folderDialog.ShowDialog() == true)
            {
                FrontendPathTextBox.Text = folderDialog.FolderName;
                StatusLabel.Text = "前端项目路径已更新";
                SaveProjectPaths();
            }
        }

        private void SaveProjectPaths()
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    var config = JsonConvert.DeserializeObject<dynamic>(json);

                    if (config?.BuildSettings != null)
                    {
                        config.BuildSettings.ProjectId = _selectedProjectId;
                        config.BuildSettings.BackendProjectPath = BackendPathTextBox.Text;
                        config.BuildSettings.FrontendProjectPath = FrontendPathTextBox.Text;

                        var updatedJson = JsonConvert.SerializeObject(config, Formatting.Indented);
                        File.WriteAllText(configPath, updatedJson);

                        StatusLabel.Text = "项目配置已保存到配置文件";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"保存配置失败: {ex.Message}";
            }
        }

        #region 自动编译相关方法

        /// <summary>
        /// 自动编译完成事件处理
        /// </summary>
        private async void OnAutoCompilationCompleted(object? sender, CompilationCompletedEventArgs e)
        {
            try
            {
                // 在UI线程中刷新错误列表
                await Dispatcher.InvokeAsync(async () =>
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 自动编译完成，刷新错误列表 - 错误数量: {e.ErrorCount}");
                    await LoadErrors();
                    StatusLabel.Text = $"自动编译完成 - 发现 {e.ErrorCount} 个问题 ({e.CompilationTime:HH:mm:ss})";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 刷新错误列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 如果配置启用了自动编译，则自动启动
        /// </summary>
        private void StartAutoCompileIfEnabled()
        {
            try
            {
                if (_autoCompileService != null && _appSettings?.Build.EnableAutoCompile == true)
                {
                    System.Diagnostics.Debug.WriteLine("🚀 配置启用了自动编译，正在自动启动...");
                    _autoCompileService.Start();
                    StartAutoCompileBtn.IsEnabled = false;
                    StopAutoCompileBtn.IsEnabled = true;
                    StatusLabel.Text = "自动编译已自动启动";
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 自动编译未启动 - 服务: {_autoCompileService != null}, 配置启用: {_appSettings?.Build.EnableAutoCompile}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 自动启动自动编译失败: {ex.Message}");
                StatusLabel.Text = $"自动启动自动编译失败: {ex.Message}";
            }
        }

        private void StartAutoCompile_Click(object sender, RoutedEventArgs e)
        {
            if (_autoCompileService == null)
            {
                MessageBox.Show("自动编译服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                _autoCompileService.Start();
                StartAutoCompileBtn.IsEnabled = false;
                StopAutoCompileBtn.IsEnabled = true;
                StatusLabel.Text = "自动编译已启动";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动自动编译失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StopAutoCompile_Click(object sender, RoutedEventArgs e)
        {
            if (_autoCompileService == null)
            {
                MessageBox.Show("自动编译服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            try
            {
                _autoCompileService.Stop();
                StartAutoCompileBtn.IsEnabled = true;
                StopAutoCompileBtn.IsEnabled = false;
                StatusLabel.Text = "自动编译已停止";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"停止自动编译失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateInterval_Click(object sender, RoutedEventArgs e)
        {
            if (_autoCompileService == null)
            {
                MessageBox.Show("自动编译服务未初始化", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            if (!int.TryParse(IntervalTextBox.Text, out int interval) || interval <= 0)
            {
                MessageBox.Show("请输入有效的间隔时间（大于0的整数）", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                _autoCompileService.UpdateInterval(interval);
                SaveIntervalToConfig(interval);
                StatusLabel.Text = $"自动编译间隔已更新为 {interval} 秒";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新间隔时间失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateAutoCompileStatus(object sender, EventArgs e)
        {
            if (_autoCompileService == null) return;

            try
            {
                var status = _autoCompileService.GetStatus();

                // 更新自动编译状态
                if (status.IsRunning)
                {
                    if (status.IsCompiling)
                    {
                        AutoCompileStatusLabel.Text = "自动编译: 编译中...";
                        AutoCompileStatusLabel.Foreground = System.Windows.Media.Brushes.Orange;
                    }
                    else
                    {
                        AutoCompileStatusLabel.Text = "自动编译: 运行中";
                        AutoCompileStatusLabel.Foreground = System.Windows.Media.Brushes.Green;
                    }
                }
                else
                {
                    AutoCompileStatusLabel.Text = "自动编译: 已停止";
                    AutoCompileStatusLabel.Foreground = System.Windows.Media.Brushes.Gray;
                }

                // 更新上次编译时间
                if (status.LastCompileTime != DateTime.MinValue)
                {
                    LastCompileTimeLabel.Text = $"上次编译: {status.LastCompileTime:HH:mm:ss}";
                    LastCompileTimeLabel.Foreground = System.Windows.Media.Brushes.Blue;
                }
                else
                {
                    LastCompileTimeLabel.Text = "上次编译: 无";
                    LastCompileTimeLabel.Foreground = System.Windows.Media.Brushes.Gray;
                }

                // 更新按钮状态
                StartAutoCompileBtn.IsEnabled = !status.IsRunning;
                StopAutoCompileBtn.IsEnabled = status.IsRunning;
            }
            catch (Exception ex)
            {
                // 静默处理状态更新错误，避免频繁弹窗
                Console.WriteLine($"更新自动编译状态失败: {ex.Message}");
            }
        }

        private void SaveIntervalToConfig(int interval)
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    var config = JsonConvert.DeserializeObject<dynamic>(json);

                    if (config?.BuildSettings != null)
                    {
                        config.BuildSettings.AutoCompileInterval = interval;

                        var updatedJson = JsonConvert.SerializeObject(config, Formatting.Indented);
                        File.WriteAllText(configPath, updatedJson);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存间隔时间到配置文件失败: {ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // 取消事件订阅
            if (_autoCompileService != null)
            {
                _autoCompileService.CompilationCompleted -= OnAutoCompilationCompleted;
                _autoCompileService.Dispose();
            }

            // 停止定时器
            _timer?.Stop();
            _statusUpdateTimer?.Stop();

            // 释放数据库服务资源
            if (_databaseService is IDisposable disposableDb)
            {
                disposableDb.Dispose();
            }

            base.OnClosed(e);
        }

        #endregion

        #region 登录相关事件处理

        private async void TabLoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformTabLogin();
        }

        private void Logout_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要退出登录吗？", "确认",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 清除记住的用户名
                try
                {
                    _userSettings?.ClearRememberedUsername();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to clear remembered username: {ex.Message}");
                }

                // 清除当前用户信息
                _currentUser = null;

                // 停止自动编译服务
                if (_autoCompileService != null)
                {
                    try
                    {
                        _autoCompileService.Stop();
                    }
                    catch { }
                }

                // 更新UI为未登录状态
                UpdateUIForLoginState(false);
                UpdateTabLoginStatus(false);

                MessageBox.Show("已成功退出登录", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = $"BuildMonitor v1.0\n\n" +
                             $"编译监控工具\n" +
                             $"当前用户: {_currentUser?.DisplayName ?? "未知"}\n\n" +
                             $"© 2024 ProjectManagement AI";

            MessageBox.Show(aboutMessage, "关于 BuildMonitor",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task PerformTabLogin()
        {
            var username = LoginUsernameTextBox.Text.Trim();
            var password = LoginPasswordBox.Password;

            // 验证输入
            if (string.IsNullOrEmpty(username))
            {
                ShowTabLoginError("请输入用户名");
                LoginUsernameTextBox.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowTabLoginError("请输入密码");
                LoginPasswordBox.Focus();
                return;
            }

            // 禁用登录按钮，显示加载状态
            TabLoginButton.IsEnabled = false;
            TabLoginButton.Content = "登录中...";
            HideTabLoginError();

            try
            {
                // 使用AuthService调用后端API进行登录
                var authService = new AuthService();
                var rememberMe = LoginRememberMeCheckBox.IsChecked == true;
                var loginResponse = await authService.LoginAsync(username, password, rememberMe);

                if (loginResponse != null)
                {
                    // 创建User对象（从API响应转换）
                    _currentUser = new User
                    {
                        Id = loginResponse.User.Id,
                        Username = loginResponse.User.Username,
                        Email = loginResponse.User.Email,
                        RealName = loginResponse.User.RealName,
                        Role = loginResponse.User.Role,
                        Status = loginResponse.User.Status,
                        Avatar = loginResponse.User.Avatar,
                        CreatedTime = loginResponse.User.CreatedAt,
                        LastLoginTime = loginResponse.User.LastLoginAt
                    };

                    // 保存访问令牌
                    SaveAccessToken(loginResponse.AccessToken, loginResponse.RefreshToken);

                    // 保存记住的凭据
                    if (rememberMe)
                    {
                        SaveTabLoginCredentials(username);
                    }
                    else
                    {
                        ClearTabLoginCredentials();
                    }

                    // 更新UI状态
                    UpdateUIForLoginState(true);
                    UpdateTabLoginStatus(true);

                    // 清空密码框
                    LoginPasswordBox.Clear();
                }
            }
            catch (AuthenticationException ex)
            {
                ShowTabLoginError(ex.Message);
                LoginPasswordBox.Clear();
                LoginPasswordBox.Focus();
            }
            catch (Exception ex)
            {
                ShowTabLoginError($"登录失败: {ex.Message}");
            }
            finally
            {
                // 恢复登录按钮状态
                TabLoginButton.IsEnabled = true;
                TabLoginButton.Content = "登录";
            }
        }

        private void ShowTabLoginError(string message)
        {
            LoginErrorMessageTextBlock.Text = message;
            LoginErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideTabLoginError()
        {
            LoginErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void UpdateTabLoginStatus(bool isLoggedIn)
        {
            if (isLoggedIn && _currentUser != null)
            {
                LoginStatusTextBlock.Text = "登录成功！您现在可以使用所有系统功能。";
                CurrentUserTextBlock.Text = $"当前用户: {_currentUser.DisplayName}";
                CurrentUserTextBlock.Visibility = Visibility.Visible;

                // 禁用登录表单
                LoginUsernameTextBox.IsEnabled = false;
                LoginPasswordBox.IsEnabled = false;
                LoginRememberMeCheckBox.IsEnabled = false;
                TabLoginButton.Content = "已登录";
                TabLoginButton.IsEnabled = false;
            }
            else
            {
                LoginStatusTextBlock.Text = "请先登录以使用系统功能";
                CurrentUserTextBlock.Visibility = Visibility.Collapsed;

                // 启用登录表单
                LoginUsernameTextBox.IsEnabled = true;
                LoginPasswordBox.IsEnabled = true;
                LoginRememberMeCheckBox.IsEnabled = true;
                TabLoginButton.Content = "登录";
                TabLoginButton.IsEnabled = true;
            }
        }

        private void SaveTabLoginCredentials(string username)
        {
            try
            {
                if (_userSettings != null)
                {
                    _userSettings.RememberedUsername = username;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save remembered credentials: {ex.Message}");
            }
        }

        private void ClearTabLoginCredentials()
        {
            try
            {
                if (_userSettings != null)
                {
                    _userSettings.ClearRememberedUsername();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to clear remembered credentials: {ex.Message}");
            }
        }

        private void SaveAccessToken(string accessToken, string refreshToken)
        {
            try
            {
                // 保存到用户设置或配置文件中
                // 这里可以根据需要实现令牌的持久化存储
                System.Diagnostics.Debug.WriteLine($"Access token saved: {accessToken.Substring(0, Math.Min(20, accessToken.Length))}...");
                System.Diagnostics.Debug.WriteLine($"Refresh token saved: {refreshToken.Substring(0, Math.Min(20, refreshToken.Length))}...");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save access token: {ex.Message}");
            }
        }

        #endregion

    }
}
