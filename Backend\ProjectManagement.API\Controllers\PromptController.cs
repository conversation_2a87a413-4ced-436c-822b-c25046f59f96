using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.AI.Services;
using ProjectManagement.API.DTOs;
using ProjectManagement.Core.Entities;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// Prompt工程控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PromptController : ControllerBase
{
    private readonly IPromptTemplateService _templateService;
    private readonly IPromptBuilderService _builderService;
    private readonly ILogger<PromptController> _logger;

    public PromptController(
        IPromptTemplateService templateService,
        IPromptBuilderService builderService,
        ILogger<PromptController> logger)
    {
        _templateService = templateService;
        _builderService = builderService;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }

    /// <summary>
    /// 检查当前用户是否为管理员
    /// </summary>
    private bool IsAdmin()
    {
        var role = User.FindFirst(ClaimTypes.Role)?.Value;
        _logger.LogInformation("当前用户角色: {Role}", role);
        return User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
    }

    #region 模板管理

    /// <summary>
    /// 获取所有模板
    /// </summary>
    [HttpGet("templates")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetTemplates()
    {
        try
        {
            var templates = await _templateService.GetAllTemplatesAsync();
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板列表失败");
            return StatusCode(500, new { message = "获取模板列表失败" });
        }
    }

    /// <summary>
    /// 根据ID获取模板
    /// </summary>
    [HttpGet("templates/{id}")]
    public async Task<ActionResult<PromptTemplateDto>> GetTemplate(int id)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(id);
            if (template == null)
                return NotFound(new { message = "模板不存在" });

            return Ok(MapToDto(template));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板失败，ID: {Id}", id);
            return StatusCode(500, new { message = "获取模板失败" });
        }
    }

    /// <summary>
    /// 根据分类获取模板
    /// </summary>
    [HttpGet("templates/category/{categoryId}")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetTemplatesByCategory(int categoryId)
    {
        try
        {
            var templates = await _templateService.GetTemplatesByCategoryAsync(categoryId);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据分类获取模板失败，CategoryId: {CategoryId}", categoryId);
            return StatusCode(500, new { message = "获取模板失败" });
        }
    }

    /// <summary>
    /// 根据任务类型获取模板
    /// </summary>
    [HttpGet("templates/task-type/{taskType}")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetTemplatesByTaskType(string taskType)
    {
        try
        {
            var templates = await _templateService.GetTemplatesByTaskTypeAsync(taskType);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型获取模板失败，TaskType: {TaskType}", taskType);
            return StatusCode(500, new { message = "获取模板失败" });
        }
    }

    /// <summary>
    /// 搜索模板
    /// </summary>
    [HttpPost("templates/search")]
    public async Task<ActionResult<List<PromptTemplateDto>>> SearchTemplates([FromBody] SearchTemplatesRequestDto request)
    {
        try
        {
            var templates = await _templateService.SearchTemplatesAsync(
                request.Keyword ?? "", request.CategoryId, request.TaskType);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索模板失败");
            return StatusCode(500, new { message = "搜索模板失败" });
        }
    }

    /// <summary>
    /// 获取热门模板
    /// </summary>
    [HttpGet("templates/popular")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetPopularTemplates([FromQuery] int count = 10)
    {
        try
        {
            var templates = await _templateService.GetPopularTemplatesAsync(count);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门模板失败");
            return StatusCode(500, new { message = "获取热门模板失败" });
        }
    }

    /// <summary>
    /// 获取用户收藏的模板
    /// </summary>
    [HttpGet("templates/favorites")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetFavoriteTemplates()
    {
        try
        {
            var userId = GetCurrentUserId();
            var templates = await _templateService.GetUserFavoriteTemplatesAsync(userId);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户收藏模板失败");
            return StatusCode(500, new { message = "获取收藏模板失败" });
        }
    }

    /// <summary>
    /// 获取用户最近使用的模板
    /// </summary>
    [HttpGet("templates/recent")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetRecentTemplates([FromQuery] int count = 10)
    {
        try
        {
            var userId = GetCurrentUserId();
            var templates = await _templateService.GetUserRecentTemplatesAsync(userId, count);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户最近使用模板失败");
            return StatusCode(500, new { message = "获取最近使用模板失败" });
        }
    }

    /// <summary>
    /// 创建模板
    /// </summary>
    [HttpPost("templates")]
    public async Task<ActionResult<PromptTemplateDto>> CreateTemplate([FromBody] CreatePromptTemplateDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var template = new PromptTemplate
            {
                Name = request.Name,
                Description = request.Description,
                CategoryId = request.CategoryId,
                Content = request.Content,
                Parameters = request.Parameters,
                TemplateType = request.TemplateType,
                TaskType = request.TaskType,
                SupportedProviders = request.SupportedProviders,
                IsDefault = request.IsDefault,
                IsEnabled = request.IsEnabled,
                Tags = request.Tags,
                CreatedBy = userId
            };

            var result = await _templateService.CreateTemplateAsync(template);
            return Ok(MapToDto(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建模板失败");
            return StatusCode(500, new { message = "创建模板失败" });
        }
    }

    /// <summary>
    /// 更新模板
    /// </summary>
    [HttpPut("templates/{id}")]
    public async Task<ActionResult<PromptTemplateDto>> UpdateTemplate(int id, [FromBody] UpdatePromptTemplateDto request)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(id);
            if (template == null)
                return NotFound(new { message = "模板不存在" });

            // 检查权限：只有创建者或管理员可以修改
            var userId = GetCurrentUserId();
            if (template.CreatedBy != userId && !IsAdmin())
                return StatusCode(403, new { message = "无权限修改此模板" });

            template.Name = request.Name;
            template.Description = request.Description;
            template.CategoryId = request.CategoryId;
            template.Content = request.Content;
            template.Parameters = request.Parameters;
            template.SupportedProviders = request.SupportedProviders;
            template.IsEnabled = request.IsEnabled;
            template.Tags = request.Tags;
            template.UpdatedBy = userId;

            var result = await _templateService.UpdateTemplateAsync(template);
            return Ok(MapToDto(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模板失败，ID: {Id}", id);
            return StatusCode(500, new { message = "更新模板失败" });
        }
    }

    /// <summary>
    /// 删除模板
    /// </summary>
    [HttpDelete("templates/{id}")]
    public async Task<ActionResult> DeleteTemplate(int id)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(id);
            if (template == null)
                return NotFound(new { message = "模板不存在" });

            // 检查权限：只有创建者或管理员可以删除
            var userId = GetCurrentUserId();
            if (template.CreatedBy != userId && !IsAdmin())
                return StatusCode(403, new { message = "无权限删除此模板" });

            var result = await _templateService.DeleteTemplateAsync(id);
            if (result)
                return Ok(new { message = "模板删除成功" });
            else
                return BadRequest(new { message = "模板删除失败" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除模板失败，ID: {Id}", id);
            return StatusCode(500, new { message = "删除模板失败" });
        }
    }

    /// <summary>
    /// 复制模板
    /// </summary>
    [HttpPost("templates/{id}/clone")]
    public async Task<ActionResult<PromptTemplateDto>> CloneTemplate(int id, [FromBody] string newName)
    {
        try
        {
            var userId = GetCurrentUserId();
            var result = await _templateService.CloneTemplateAsync(id, newName, userId);
            return Ok(MapToDto(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制模板失败，ID: {Id}", id);
            return StatusCode(500, new { message = "复制模板失败" });
        }
    }

    #endregion

    #region Prompt构建

    /// <summary>
    /// 构建提示词
    /// </summary>
    [HttpPost("build")]
    public async Task<ActionResult<BuildPromptResponseDto>> BuildPrompt([FromBody] BuildPromptRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var prompt = await _builderService.BuildPromptAsync(
                request.TemplateId, request.Parameters, userId, request.ProjectId);

            var template = await _templateService.GetTemplateByIdAsync(request.TemplateId);

            var response = new BuildPromptResponseDto
            {
                Prompt = prompt,
                TemplateId = request.TemplateId,
                TemplateName = template?.Name ?? "",
                UsedParameters = request.Parameters,
                BuildTime = DateTime.Now
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建提示词失败");
            return StatusCode(500, new { message = ex.Message });
        }
    }

    /// <summary>
    /// 根据任务类型构建提示词
    /// </summary>
    [HttpPost("build-by-task-type/{taskType}")]
    public async Task<ActionResult<BuildPromptResponseDto>> BuildPromptByTaskType(
        string taskType, [FromBody] Dictionary<string, object> parameters, [FromQuery] int? projectId = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var prompt = await _builderService.BuildPromptByTaskTypeAsync(taskType, parameters, userId, projectId);

            // 获取使用的模板信息
            var defaultTemplate = await _templateService.GetDefaultTemplateAsync(taskType);

            var response = new BuildPromptResponseDto
            {
                Prompt = prompt,
                TemplateId = defaultTemplate?.Id ?? 0,
                TemplateName = defaultTemplate?.Name ?? "",
                UsedParameters = parameters,
                BuildTime = DateTime.Now
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型构建提示词失败，TaskType: {TaskType}", taskType);
            return StatusCode(500, new { message = ex.Message });
        }
    }

    /// <summary>
    /// 验证模板参数
    /// </summary>
    [HttpPost("templates/{id}/validate-parameters")]
    public async Task<ActionResult> ValidateParameters(int id, [FromBody] Dictionary<string, object> parameters)
    {
        try
        {
            var (isValid, errors) = await _builderService.ValidateParametersAsync(id, parameters);
            return Ok(new { isValid, errors });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证模板参数失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = "验证参数失败" });
        }
    }

    /// <summary>
    /// 获取模板参数定义
    /// </summary>
    [HttpGet("templates/{id}/parameters")]
    public async Task<ActionResult> GetTemplateParameters(int id)
    {
        try
        {
            var parameters = await _builderService.GetTemplateParametersAsync(id);
            return Ok(parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板参数定义失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = "获取参数定义失败" });
        }
    }

    /// <summary>
    /// 预览构建结果
    /// </summary>
    [HttpPost("templates/{id}/preview")]
    public async Task<ActionResult<string>> PreviewPrompt(int id, [FromBody] Dictionary<string, object> parameters)
    {
        try
        {
            var prompt = await _builderService.PreviewPromptAsync(id, parameters);
            return Ok(prompt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预览构建结果失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = ex.Message });
        }
    }

    /// <summary>
    /// 获取推荐模板
    /// </summary>
    [HttpGet("recommendations/{taskType}")]
    public async Task<ActionResult<List<PromptTemplateDto>>> GetRecommendedTemplates(string taskType, [FromQuery] int? projectId = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var templates = await _builderService.GetRecommendedTemplatesAsync(taskType, userId, projectId);
            var result = templates.Select(MapToDto).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取推荐模板失败，TaskType: {TaskType}", taskType);
            return StatusCode(500, new { message = "获取推荐模板失败" });
        }
    }

    /// <summary>
    /// 优化提示词
    /// </summary>
    [HttpPost("optimize")]
    public async Task<ActionResult<string>> OptimizePrompt([FromBody] dynamic request)
    {
        try
        {
            string originalPrompt = request.prompt;
            string taskType = request.taskType;
            string aiProvider = request.aiProvider;

            var optimizedPrompt = await _builderService.OptimizePromptAsync(originalPrompt, taskType, aiProvider);
            return Ok(optimizedPrompt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "优化提示词失败");
            return StatusCode(500, new { message = "优化提示词失败" });
        }
    }

    /// <summary>
    /// 分析提示词质量
    /// </summary>
    [HttpPost("analyze-quality")]
    public async Task<ActionResult<PromptQualityAnalysisDto>> AnalyzePromptQuality([FromBody] dynamic request)
    {
        try
        {
            string prompt = request.prompt;
            string taskType = request.taskType;

            var analysis = await _builderService.AnalyzePromptQualityAsync(prompt, taskType);

            // 将动态对象转换为DTO
            var result = new PromptQualityAnalysisDto
            {
                Length = (int)analysis.GetType().GetProperty("Length")?.GetValue(analysis)!,
                WordCount = (int)analysis.GetType().GetProperty("WordCount")?.GetValue(analysis)!,
                HasClearInstructions = (bool)analysis.GetType().GetProperty("HasClearInstructions")?.GetValue(analysis)!,
                HasExamples = (bool)analysis.GetType().GetProperty("HasExamples")?.GetValue(analysis)!,
                HasConstraints = (bool)analysis.GetType().GetProperty("HasConstraints")?.GetValue(analysis)!,
                HasOutputFormat = (bool)analysis.GetType().GetProperty("HasOutputFormat")?.GetValue(analysis)!,
                ComplexityScore = (int)analysis.GetType().GetProperty("ComplexityScore")?.GetValue(analysis)!,
                ClarityScore = (int)analysis.GetType().GetProperty("ClarityScore")?.GetValue(analysis)!,
                CompletenessScore = (int)analysis.GetType().GetProperty("CompletenessScore")?.GetValue(analysis)!,
                Suggestions = (List<string>)analysis.GetType().GetProperty("Suggestions")?.GetValue(analysis)!
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析提示词质量失败");
            return StatusCode(500, new { message = "分析提示词质量失败" });
        }
    }

    #endregion

    #region 统计和评价

    /// <summary>
    /// 记录模板使用
    /// </summary>
    [HttpPost("templates/{id}/record-usage")]
    public async Task<ActionResult> RecordTemplateUsage(int id, [FromBody] dynamic request)
    {
        try
        {
            var userId = GetCurrentUserId();
            int? projectId = request.projectId;
            string aiProvider = request.aiProvider;
            string aiModel = request.aiModel;
            string inputParameters = request.inputParameters;
            string generatedPrompt = request.generatedPrompt;
            string? aiResponse = request.aiResponse;
            int? responseTimeMs = request.responseTimeMs;
            int? tokenUsage = request.tokenUsage;
            decimal? cost = request.cost;
            bool isSuccess = request.isSuccess;
            string? errorMessage = request.errorMessage;

            await _templateService.RecordTemplateUsageAsync(
                id, userId, projectId, aiProvider, aiModel, inputParameters,
                generatedPrompt, aiResponse, responseTimeMs, tokenUsage, cost, isSuccess, errorMessage);

            return Ok(new { message = "使用记录已保存" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录模板使用失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = "记录使用失败" });
        }
    }

    /// <summary>
    /// 评价模板
    /// </summary>
    [HttpPost("templates/{id}/rate")]
    public async Task<ActionResult> RateTemplate(int id, [FromBody] RateTemplateRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _templateService.RateTemplateAsync(
                id, userId, request.OverallRating, request.AccuracyRating,
                request.UsefulnessRating, request.EaseOfUseRating, request.Feedback,
                request.Suggestions, request.Tags, request.WouldRecommend);

            return Ok(new { message = "评价已提交" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评价模板失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = "评价提交失败" });
        }
    }

    /// <summary>
    /// 获取模板统计信息
    /// </summary>
    [HttpGet("templates/{id}/stats")]
    public async Task<ActionResult<TemplateStatsDto>> GetTemplateStats(int id)
    {
        try
        {
            var stats = await _templateService.GetTemplateStatsAsync(id);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板统计信息失败，TemplateId: {TemplateId}", id);
            return StatusCode(500, new { message = "获取统计信息失败" });
        }
    }

    #endregion

    #region 用户偏好

    /// <summary>
    /// 设置用户偏好
    /// </summary>
    [HttpPost("preferences")]
    public async Task<ActionResult> SetUserPreference([FromBody] SetUserPreferenceRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _templateService.SetUserPreferenceAsync(
                userId, request.TemplateId, request.PreferenceType,
                request.CustomParameters, request.SortOrder);

            return Ok(new { message = "偏好设置已保存" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置用户偏好失败");
            return StatusCode(500, new { message = "设置偏好失败" });
        }
    }

    /// <summary>
    /// 移除用户偏好
    /// </summary>
    [HttpDelete("preferences/{templateId}/{preferenceType}")]
    public async Task<ActionResult> RemoveUserPreference(int templateId, string preferenceType)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _templateService.RemoveUserPreferenceAsync(userId, templateId, preferenceType);

            return Ok(new { message = "偏好已移除" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除用户偏好失败");
            return StatusCode(500, new { message = "移除偏好失败" });
        }
    }

    /// <summary>
    /// 获取用户偏好
    /// </summary>
    [HttpGet("preferences")]
    public async Task<ActionResult> GetUserPreferences()
    {
        try
        {
            var userId = GetCurrentUserId();
            var preferences = await _templateService.GetUserPreferencesAsync(userId);
            return Ok(preferences);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户偏好失败");
            return StatusCode(500, new { message = "获取偏好失败" });
        }
    }

    #endregion

    /// <summary>
    /// 映射实体到DTO
    /// </summary>
    private PromptTemplateDto MapToDto(PromptTemplate template)
    {
        return new PromptTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Description = template.Description,
            CategoryId = template.CategoryId,
            CategoryName = template.Category?.Name ?? "",
            Content = template.Content,
            Parameters = template.Parameters,
            TemplateType = template.TemplateType,
            TaskType = template.TaskType,
            SupportedProviders = template.SupportedProviders,
            TemplateVersion = template.TemplateVersion,
            IsDefault = template.IsDefault,
            IsEnabled = template.IsEnabled,
            UsageCount = template.UsageCount,
            AverageRating = template.AverageRating,
            LastUsedTime = template.LastUsedTime,
            Tags = template.Tags,
            CreatedTime = template.CreatedTime,
            UpdatedTime = template.UpdatedTime,
            CreatedBy = template.CreatedBy
        };
    }
}
