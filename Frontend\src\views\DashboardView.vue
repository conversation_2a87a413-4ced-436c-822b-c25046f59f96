<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ username }}！</h2>
            <p>今天是 {{ currentDate }}，开始您的AI驱动开发之旅吧</p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" size="large" @click="createProject">
              <el-icon><Plus /></el-icon>
              创建新项目
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon size="24"><Folder /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics?.totalProjects || 0 }}</div>
                <div class="stat-label">总项目数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon size="24"><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics?.activeProjects || 0 }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <el-icon size="24"><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics?.completedProjects || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon budget">
                <el-icon size="24"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ formatMoney(statistics?.totalBudget || 0) }}</div>
                <div class="stat-label">总预算</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 最近项目 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>最近项目</span>
              <el-button type="text" @click="viewAllProjects">查看全部</el-button>
            </div>
          </template>

          <div v-if="recentProjects.length > 0" class="project-list">
            <div
              v-for="project in recentProjects"
              :key="project.id"
              class="project-item"
              @click="viewProject(project.id)"
            >
              <div class="project-info">
                <div class="project-name">{{ project.name }}</div>
                <div class="project-desc">{{ project.description || '暂无描述' }}</div>
              </div>
              <div class="project-status">
                <el-tag :type="getStatusType(project.status)">
                  {{ getStatusLabel(project.status) }}
                </el-tag>
              </div>
            </div>
          </div>

          <div v-else class="empty-state">
            <el-icon size="48" color="#C0C4CC"><FolderOpened /></el-icon>
            <p>暂无项目</p>
            <el-button type="primary" @click="createProject">创建第一个项目</el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 快速操作 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="content-card">
          <template #header>
            <span>快速操作</span>
          </template>

          <div class="quick-actions">
            <div class="action-item" @click="createProject">
              <div class="action-icon">
                <el-icon size="32"><Plus /></el-icon>
              </div>
              <div class="action-text">
                <div class="action-title">创建项目</div>
                <div class="action-desc">开始一个新的AI驱动开发项目</div>
              </div>
            </div>

            <div class="action-item" @click="startRequirement">
              <div class="action-icon">
                <el-icon size="32"><Document /></el-icon>
              </div>
              <div class="action-text">
                <div class="action-title">需求分析</div>
                <div class="action-desc">使用AI进行智能需求分析</div>
              </div>
            </div>

            <div class="action-item" @click="viewSettings">
              <div class="action-icon">
                <el-icon size="32"><Setting /></el-icon>
              </div>
              <div class="action-text">
                <div class="action-title">系统设置</div>
                <div class="action-desc">配置AI模型和系统参数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useProjectStore } from '@/stores/project'
import { ProjectService } from '@/services/project'
// import type { ElTagType } from '@/types/element-plus'
import dayjs from 'dayjs'
import {
  Plus,
  Folder,
  Loading,
  CircleCheck,
  Money,
  FolderOpened,
  Document,
  Setting
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const projectStore = useProjectStore()

// 响应式数据
const recentProjects = ref<any[]>([])

// 计算属性
const username = computed(() => authStore.user?.realName || authStore.user?.username || '用户')
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))
const statistics = computed(() => projectStore.statistics)

// 方法
const formatMoney = (amount: number) => {
  return (amount / 10000).toFixed(1) + 'w'
}

const getStatusType = (status: string): 'warning' | 'success' | 'danger' | 'info' => {
  const typeMap: Record<string, 'warning' | 'success' | 'danger' | 'info'> = {
    'Planning': 'info',
    'InProgress': 'warning',
    'Testing': 'warning',
    'Deployed': 'success',
    'Completed': 'success',
    'Paused': 'danger',
    'Cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  return ProjectService.getStatusLabel(status)
}

const createProject = () => {
  router.push('/projects/create')
}

const viewProject = (id: number) => {
  router.push(`/projects/${id}`)
}

const viewAllProjects = () => {
  router.push('/projects')
}

const startRequirement = () => {
  router.push('/requirements')
}

const viewSettings = () => {
  if (authStore.hasPermission('admin')) {
    router.push('/settings')
  } else {
    router.push('/profile')
  }
}

// 初始化数据的函数
const initData = async () => {
  try {
    // 获取统计信息
    await projectStore.fetchStatistics()

    // 获取最近项目
    const result = await projectStore.fetchProjects({ pageSize: 5 })
    recentProjects.value = result.items.slice(0, 5)
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

// 组件挂载时获取数据
onMounted(initData)

// keep-alive 组件激活时也获取数据（确保数据是最新的）
onActivated(initData)
</script>

<style lang="scss" scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 24px;

  .welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    :deep(.el-card__body) {
      padding: 32px;
    }
  }

  .welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;

    .welcome-text {
      h2 {
        font-size: 24px;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }
}

.stats-section {
  margin-bottom: 24px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        &.total {
          background: #e3f2fd;
          color: #1976d2;
        }

        &.active {
          background: #fff3e0;
          color: #f57c00;
        }

        &.completed {
          background: #e8f5e8;
          color: #388e3c;
        }

        &.budget {
          background: #fce4ec;
          color: #c2185b;
        }
      }

      .stat-info {
        .stat-number {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-top: 4px;
        }
      }
    }
  }
}

.main-content {
  .content-card {
    height: 400px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow-y: auto;
    }
  }
}

.project-list {
  .project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    cursor: pointer;

    &:hover {
      background-color: var(--el-bg-color-page);
      margin: 0 -16px;
      padding: 16px;
      border-radius: 8px;
    }

    &:last-child {
      border-bottom: none;
    }

    .project-info {
      flex: 1;

      .project-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .project-desc {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.quick-actions {
  .action-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background-color: var(--el-bg-color-page);
      margin: 0 -16px;
      padding: 16px;
    }

    .action-icon {
      width: 56px;
      height: 56px;
      border-radius: 12px;
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
    }

    .action-text {
      .action-title {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .action-desc {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);

  p {
    margin: 16px 0 24px;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;

    .welcome-text {
      margin-bottom: 20px;
    }
  }

  .main-content {
    .content-card {
      height: auto;
      margin-bottom: 16px;
    }
  }
}
</style>
