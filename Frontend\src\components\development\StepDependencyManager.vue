<template>
  <div class="dependency-manager">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加依赖
      </el-button>
      <el-button @click="loadDependencies">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 依赖关系列表 -->
    <div class="dependency-content">
      <el-row :gutter="20">
        <!-- 当前步骤依赖的其他步骤 -->
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>依赖的步骤 ({{ dependencies.length }})</span>
            </template>

            <div v-if="dependencies.length === 0" class="empty-state">
              <el-empty description="暂无依赖步骤" />
            </div>

            <div v-else>
              <div
                v-for="dep in dependencies"
                :key="dep.id"
                class="dependency-item"
              >
                <div class="dependency-info">
                  <div class="step-name">{{ (dep as any).dependsOnStep?.stepName || '未知步骤' }}</div>
                  <div class="dependency-meta">
                    <el-tag size="small" :type="getDependencyTypeColor(dep.dependencyType) as any">
                      {{ getDependencyTypeLabel(dep.dependencyType) }}
                    </el-tag>
                    <el-tag v-if="dep.isRequired" size="small" type="danger">必需</el-tag>
                    <el-tag v-else size="small" type="info">可选</el-tag>
                  </div>
                  <div v-if="dep.description" class="dependency-desc">{{ dep.description }}</div>
                </div>
                <div class="dependency-actions">
                  <el-button size="small" type="danger" @click="removeDependency(dep.id)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 依赖当前步骤的其他步骤 -->
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>被依赖的步骤 ({{ dependents.length }})</span>
            </template>

            <div v-if="dependents.length === 0" class="empty-state">
              <el-empty description="暂无被依赖步骤" />
            </div>

            <div v-else>
              <div
                v-for="dep in dependents"
                :key="dep.id"
                class="dependency-item"
              >
                <div class="dependency-info">
                  <div class="step-name">{{ (dep as any).step?.stepName || '未知步骤' }}</div>
                  <div class="dependency-meta">
                    <el-tag size="small" :type="getDependencyTypeColor(dep.dependencyType) as any">
                      {{ getDependencyTypeLabel(dep.dependencyType) }}
                    </el-tag>
                    <el-tag v-if="dep.isRequired" size="small" type="danger">必需</el-tag>
                    <el-tag v-else size="small" type="info">可选</el-tag>
                  </div>
                  <div v-if="dep.description" class="dependency-desc">{{ dep.description }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加依赖对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加依赖关系"
      width="500px"
      :before-close="handleAddDialogClose"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addRules"
        label-width="100px"
      >
        <el-form-item label="依赖步骤" prop="dependsOnStepId">
          <el-select
            v-model="addForm.dependsOnStepId"
            placeholder="请选择依赖的步骤"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="step in availableSteps"
              :key="step.id"
              :label="step.stepName"
              :value="step.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="依赖类型" prop="dependencyType">
          <el-select v-model="addForm.dependencyType" placeholder="请选择依赖类型">
            <el-option label="顺序依赖" value="Sequential" />
            <el-option label="并行依赖" value="Parallel" />
            <el-option label="条件依赖" value="Conditional" />
            <el-option label="可选依赖" value="Optional" />
          </el-select>
        </el-form-item>

        <el-form-item label="是否必需" prop="isRequired">
          <el-switch v-model="addForm.isRequired" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="addForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入依赖关系描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAddDialogClose">取消</el-button>
          <el-button type="primary" @click="handleAddDependency" :loading="adding">
            添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import type { StepDependency, DevelopmentStep } from '@/types/development'
import { DevelopmentService } from '@/services/development'

// Props
interface Props {
  stepId: number
  projectId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'refresh': []
}>()

// 响应式数据
const loading = ref(false)
const adding = ref(false)
const showAddDialog = ref(false)
const dependencies = ref<StepDependency[]>([])
const dependents = ref<StepDependency[]>([])
const availableSteps = ref<DevelopmentStep[]>([])

const addFormRef = ref<FormInstance>()
const addForm = reactive({
  dependsOnStepId: undefined as number | undefined,
  dependencyType: 'Sequential',
  isRequired: true,
  description: ''
})

const addRules: FormRules = {
  dependsOnStepId: [
    { required: true, message: '请选择依赖的步骤', trigger: 'change' }
  ],
  dependencyType: [
    { required: true, message: '请选择依赖类型', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  loadDependencies()
  loadAvailableSteps()
})

// 方法
const loadDependencies = async () => {
  try {
    loading.value = true
    const [deps, dependentSteps] = await Promise.all([
      DevelopmentService.getStepDependencies(props.stepId),
      DevelopmentService.getStepDependents(props.stepId)
    ])
    dependencies.value = deps
    dependents.value = dependentSteps
  } catch (error) {
    console.error('加载依赖关系失败:', error)
    ElMessage.error('加载依赖关系失败')
  } finally {
    loading.value = false
  }
}

const loadAvailableSteps = async () => {
  try {
    const result = await DevelopmentService.getProjectSteps(props.projectId, 1, 1000)
    availableSteps.value = result.items.filter(step => step.id !== props.stepId)
  } catch (error) {
    console.error('加载可用步骤失败:', error)
  }
}

const handleAddDependency = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()
    adding.value = true

    await DevelopmentService.addStepDependency({
      stepId: props.stepId,
      dependsOnStepId: addForm.dependsOnStepId!,
      dependencyType: addForm.dependencyType,
      isRequired: addForm.isRequired,
      description: addForm.description
    })

    ElMessage.success('依赖关系添加成功')
    handleAddDialogClose()
    loadDependencies()
    emit('refresh')
  } catch (error) {
    console.error('添加依赖关系失败:', error)
    ElMessage.error('添加依赖关系失败')
  } finally {
    adding.value = false
  }
}

const removeDependency = async (dependencyId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个依赖关系吗？', '确认删除', {
      type: 'warning'
    })

    await DevelopmentService.removeStepDependency(dependencyId)
    ElMessage.success('依赖关系删除成功')
    loadDependencies()
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除依赖关系失败:', error)
      ElMessage.error('删除依赖关系失败')
    }
  }
}

const handleAddDialogClose = () => {
  showAddDialog.value = false
  Object.assign(addForm, {
    dependsOnStepId: undefined,
    dependencyType: 'Sequential',
    isRequired: true,
    description: ''
  })
}

const getDependencyTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'Sequential': '顺序依赖',
    'Parallel': '并行依赖',
    'Conditional': '条件依赖',
    'Optional': '可选依赖'
  }
  return labels[type] || type
}

const getDependencyTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'Sequential': 'primary',
    'Parallel': 'success',
    'Conditional': 'warning',
    'Optional': 'info'
  }
  return colors[type] || 'info'
}
</script>

<style scoped>
.dependency-manager {
  padding: 16px 0;
}

.toolbar {
  margin-bottom: 16px;
}

.dependency-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
}

.dependency-info {
  flex: 1;
}

.step-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.dependency-meta {
  margin-bottom: 4px;
}

.dependency-meta .el-tag {
  margin-right: 8px;
}

.dependency-desc {
  font-size: 12px;
  color: #909399;
}

.dependency-actions {
  margin-left: 12px;
}

.empty-state {
  text-align: center;
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
