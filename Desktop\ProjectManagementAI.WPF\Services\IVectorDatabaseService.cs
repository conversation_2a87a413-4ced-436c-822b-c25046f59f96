using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 向量数据库服务接口
    /// </summary>
    public interface IVectorDatabaseService
    {
        /// <summary>
        /// 初始化向量数据库
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// 将项目向量化并存储到数据库
        /// </summary>
        /// <param name="project">项目信息</param>
        /// <returns>向量ID</returns>
        Task<string> VectorizeAndStoreProjectAsync(object project);

        /// <summary>
        /// 将本地项目向量化并存储
        /// </summary>
        /// <param name="localProject">本地项目</param>
        /// <returns>向量ID</returns>
        Task<string> VectorizeAndStoreLocalProjectAsync(LocalProject localProject);

        /// <summary>
        /// 搜索相似项目
        /// </summary>
        /// <param name="queryText">查询文本</param>
        /// <param name="topK">返回前K个结果</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似项目列表</returns>
        Task<IEnumerable<VectorSearchResult>> SearchSimilarProjectsAsync(string queryText, int topK = 10, double threshold = 0.7);

        /// <summary>
        /// 基于项目ID搜索相似项目
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="topK">返回前K个结果</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似项目列表</returns>
        Task<IEnumerable<VectorSearchResult>> FindSimilarProjectsByIdAsync(string projectId, int topK = 10, double threshold = 0.7);

        /// <summary>
        /// 获取项目向量
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <returns>项目向量</returns>
        Task<ProjectVector?> GetProjectVectorAsync(string projectId);

        /// <summary>
        /// 更新项目向量
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="project">更新的项目信息</param>
        Task UpdateProjectVectorAsync(string projectId, object project);

        /// <summary>
        /// 删除项目向量
        /// </summary>
        /// <param name="projectId">项目ID</param>
        Task DeleteProjectVectorAsync(string projectId);

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<VectorDatabaseStats> GetDatabaseStatsAsync();

        /// <summary>
        /// 批量向量化项目
        /// </summary>
        /// <param name="projects">项目列表</param>
        /// <returns>向量化结果</returns>
        Task<BatchVectorizationResult> BatchVectorizeProjectsAsync(IEnumerable<object> projects);

        /// <summary>
        /// 执行向量聚类分析
        /// </summary>
        /// <param name="clusterCount">聚类数量</param>
        /// <returns>聚类结果</returns>
        Task<ClusterAnalysisResult> PerformClusterAnalysisAsync(int clusterCount = 5);
    }

    /// <summary>
    /// 项目向量
    /// </summary>
    public class ProjectVector
    {
        public string Id { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public string ProjectType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TechnologyStack { get; set; } = string.Empty;
        public float[] Vector { get; set; } = Array.Empty<float>();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 向量搜索结果
    /// </summary>
    public class VectorSearchResult
    {
        public string ProjectId { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public string ProjectType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double SimilarityScore { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 向量数据库统计信息
    /// </summary>
    public class VectorDatabaseStats
    {
        public int TotalVectors { get; set; }
        public int VectorDimensions { get; set; }
        public long DatabaseSizeBytes { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, int> ProjectTypeDistribution { get; set; } = new();
        public Dictionary<string, int> TechnologyDistribution { get; set; } = new();
    }

    /// <summary>
    /// 批量向量化结果
    /// </summary>
    public class BatchVectorizationResult
    {
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> VectorIds { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// 聚类分析结果
    /// </summary>
    public class ClusterAnalysisResult
    {
        public int ClusterCount { get; set; }
        public List<ProjectCluster> Clusters { get; set; } = new();
        public double SilhouetteScore { get; set; }
        public Dictionary<string, object> ClusterMetrics { get; set; } = new();
    }

    /// <summary>
    /// 项目聚类
    /// </summary>
    public class ProjectCluster
    {
        public int ClusterId { get; set; }
        public string ClusterName { get; set; } = string.Empty;
        public List<string> ProjectIds { get; set; } = new();
        public float[] Centroid { get; set; } = Array.Empty<float>();
        public double IntraClusterDistance { get; set; }
        public Dictionary<string, object> Characteristics { get; set; } = new();
    }
}
