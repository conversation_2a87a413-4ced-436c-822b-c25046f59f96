using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs.VectorSearch;
using ProjectManagement.AI.Services;
using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 向量搜索API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class VectorSearchController : ControllerBase
    {
        private readonly VectorSearchService _vectorSearchService;
        private readonly ILogger<VectorSearchController> _logger;

        public VectorSearchController(VectorSearchService vectorSearchService, ILogger<VectorSearchController> logger)
        {
            _vectorSearchService = vectorSearchService;
            _logger = logger;
        }

        /// <summary>
        /// 索引文档
        /// </summary>
        [HttpPost("index")]
        public async Task<ActionResult> IndexDocument([FromBody] DocumentToIndex document)
        {
            try
            {
                var success = await _vectorSearchService.IndexDocumentAsync(document);
                
                if (success)
                {
                    return Ok(new { message = "文档索引成功" });
                }
                else
                {
                    return StatusCode(500, new { error = "文档索引失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文档索引失败: {DocumentId}", document.Id);
                return StatusCode(500, new { error = "文档索引失败", details = ex.Message });
            }
        }

        /// <summary>
        /// 语义搜索
        /// </summary>
        [HttpPost("search/semantic")]
        public async Task<ActionResult<List<SearchResult>>> SemanticSearch([FromBody] SemanticSearchRequest request)
        {
            try
            {
                var options = new SearchOptions
                {
                    TopK = request.TopK,
                    SimilarityThreshold = request.SimilarityThreshold,
                    Filters = request.Filters
                };

                var results = await _vectorSearchService.SemanticSearchAsync(request.Query, options);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "语义搜索失败: {Query}", request.Query);
                return StatusCode(500, new { error = "语义搜索失败" });
            }
        }

        /// <summary>
        /// 混合搜索
        /// </summary>
        [HttpPost("search/hybrid")]
        public async Task<ActionResult<List<SearchResult>>> HybridSearch([FromBody] HybridSearchRequest request)
        {
            try
            {
                var options = new SearchOptions
                {
                    TopK = request.TopK,
                    SimilarityThreshold = request.SimilarityThreshold,
                    Filters = request.Filters,
                    RerankingWeight = request.RerankingWeight
                };

                var results = await _vectorSearchService.HybridSearchAsync(request.Query, options);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "混合搜索失败: {Query}", request.Query);
                return StatusCode(500, new { error = "混合搜索失败" });
            }
        }

        /// <summary>
        /// RAG增强生成
        /// </summary>
        [HttpPost("rag/generate")]
        public async Task<ActionResult<RAGResponse>> GenerateWithRAG([FromBody] RAGGenerationRequest request)
        {
            try
            {
                var options = new RAGOptions
                {
                    RetrievalTopK = request.RetrievalTopK,
                    SimilarityThreshold = request.SimilarityThreshold,
                    MaxContextLength = request.MaxContextLength,
                    PromptTemplate = request.PromptTemplate,
                    Filters = request.Filters,
                    AIConfig = request.AIConfig
                };

                var response = await _vectorSearchService.GenerateWithRAGAsync(request.Query, options);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RAG增强生成失败: {Query}", request.Query);
                return StatusCode(500, new { error = "RAG增强生成失败" });
            }
        }

        /// <summary>
        /// 删除文档索引
        /// </summary>
        [HttpDelete("index/{documentId}")]
        public async Task<ActionResult> DeleteDocumentIndex(string documentId)
        {
            try
            {
                var success = await _vectorSearchService.DeleteDocumentIndexAsync(documentId);
                
                if (success)
                {
                    return Ok(new { message = "文档索引删除成功" });
                }
                else
                {
                    return StatusCode(500, new { error = "文档索引删除失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文档索引失败: {DocumentId}", documentId);
                return StatusCode(500, new { error = "删除文档索引失败" });
            }
        }

        /// <summary>
        /// 更新文档索引
        /// </summary>
        [HttpPut("index")]
        public async Task<ActionResult> UpdateDocumentIndex([FromBody] DocumentToIndex document)
        {
            try
            {
                var success = await _vectorSearchService.UpdateDocumentIndexAsync(document);
                
                if (success)
                {
                    return Ok(new { message = "文档索引更新成功" });
                }
                else
                {
                    return StatusCode(500, new { error = "文档索引更新失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新文档索引失败: {DocumentId}", document.Id);
                return StatusCode(500, new { error = "更新文档索引失败" });
            }
        }
    }

    // 请求DTO类
    public class SemanticSearchRequest
    {
        public string Query { get; set; } = string.Empty;
        public int TopK { get; set; } = 5;
        public float SimilarityThreshold { get; set; } = 0.7f;
        public Dictionary<string, object> Filters { get; set; } = new();
    }

    public class HybridSearchRequest
    {
        public string Query { get; set; } = string.Empty;
        public int TopK { get; set; } = 5;
        public float SimilarityThreshold { get; set; } = 0.7f;
        public float RerankingWeight { get; set; } = 0.5f;
        public Dictionary<string, object> Filters { get; set; } = new();
    }

    public class RAGGenerationRequest
    {
        public string Query { get; set; } = string.Empty;
        public int RetrievalTopK { get; set; } = 5;
        public float SimilarityThreshold { get; set; } = 0.7f;
        public int MaxContextLength { get; set; } = 4000;
        public string PromptTemplate { get; set; } = string.Empty;
        public Dictionary<string, object> Filters { get; set; } = new();
        public AIModelConfig? AIConfig { get; set; }
    }
}
