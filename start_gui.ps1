# AI驱动的VSCode自动化客户端 - GUI启动脚本

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  AI驱动的VSCode自动化客户端 - GUI版本" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python已安装: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 错误: 未找到Python，请先安装Python 3.8+" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否在正确的目录
if (-not (Test-Path "gui_main.py")) {
    Write-Host "✗ 错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查依赖是否安装
Write-Host "检查依赖项..." -ForegroundColor Yellow
try {
    python -c "import tkinter, yaml, httpx, psutil, loguru" 2>$null
    Write-Host "✓ 依赖项检查通过" -ForegroundColor Green
} catch {
    Write-Host "正在安装依赖项..." -ForegroundColor Yellow
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ 错误: 依赖项安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    Write-Host "✓ 依赖项安装完成" -ForegroundColor Green
}

# 创建必要的目录
$directories = @("logs", "logs/screenshots", "generated_code", "backups", "data")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ 创建目录: $dir" -ForegroundColor Green
    }
}

# 启动GUI应用
Write-Host ""
Write-Host "启动图形界面..." -ForegroundColor Cyan
Write-Host ""

try {
    python gui_main.py
} catch {
    Write-Host ""
    Write-Host "✗ 程序异常退出" -ForegroundColor Red
    Read-Host "按任意键退出"
}
