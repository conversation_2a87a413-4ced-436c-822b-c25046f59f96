using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Visual Studio 编译错误实体
/// 功能: 记录VS编译过程中产生的错误信息
/// 支持: 错误分类、位置定位、修复状态跟踪
/// </summary>
[SugarTable("VSBuildErrors", "Visual Studio编译错误记录表")]
public class VSBuildError : BaseEntity
{
    /// <summary>
    /// 关联的项目ID
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的项目ID")]
    public int? ProjectId { get; set; }

    /// <summary>
    /// 编译会话ID，用于关联同一次编译的多个错误
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "编译会话ID")]
    [Required]
    public string BuildSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码，如CS0103
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "错误代码")]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 错误消息内容
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "错误消息内容")]
    [Required]
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 出错文件路径
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "出错文件路径")]
    public string? FilePath { get; set; }

    /// <summary>
    /// 错误行号
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "错误行号")]
    public int? LineNumber { get; set; }

    /// <summary>
    /// 错误列号
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "错误列号")]
    public int? ColumnNumber { get; set; }

    /// <summary>
    /// 严重程度：Error, Warning, Info
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "严重程度")]
    public string Severity { get; set; } = "Error";

    /// <summary>
    /// 编译配置：Debug, Release等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编译配置")]
    public string? BuildConfiguration { get; set; }

    /// <summary>
    /// 编译平台：x86, x64, AnyCPU等
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "编译平台")]
    public string? BuildPlatform { get; set; }

    /// <summary>
    /// 编译时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "编译时间")]
    public DateTime BuildTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否已解决
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否已解决")]
    public bool IsResolved { get; set; } = false;

    /// <summary>
    /// 解决时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "解决时间")]
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的编译会话 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public VSBuildSession? BuildSession { get; set; }
}
