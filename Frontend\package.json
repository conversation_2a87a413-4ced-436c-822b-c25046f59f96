{"name": "project-management-frontend", "version": "1.0.0", "description": "AI驱动软件开发自动化系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test:playwright": "playwright test", "test:playwright:ui": "playwright test --ui", "test:playwright:debug": "playwright test --debug", "test:playwright:headed": "playwright test --headed", "test:playwright:report": "playwright show-report", "test:playwright:install": "playwright install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@playwright/mcp": "^0.0.32", "@types/lodash-es": "^4.17.12", "@types/marked": "^5.0.2", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "markdown-it": "^14.0.0", "marked": "^15.0.12", "mermaid": "^10.6.1", "monaco-editor": "^0.45.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/markdown-it": "^13.0.7", "@types/node": "^20.10.5", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "packageManager": "npm@11.4.1+sha512.fcee43884166b6f9c5d04535fb95650e9708b6948a1f797eddf40e9778646778a518dfa32651b1c62ff36f4ac42becf177ca46ca27d53f24b539190c8d91802b"}