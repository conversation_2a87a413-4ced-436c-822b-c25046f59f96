<template>
  <div class="execution-history" v-loading="loading">
    <!-- 工具栏 -->
    <div class="history-toolbar">
      <el-button @click="loadHistory">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button type="primary" @click="startExecution" :loading="executing">
        <el-icon><VideoPlay /></el-icon>
        开始执行
      </el-button>
    </div>

    <!-- 执行历史列表 -->
    <div class="history-content">
      <div v-if="historyList.length === 0" class="empty-state">
        <el-empty description="暂无执行历史" />
      </div>

      <div v-else class="history-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="history in historyList"
            :key="history.id"
            :timestamp="formatDateTime(history.executionStartTime)"
            :type="getTimelineType(history.executionStatus) as any"
            :icon="getTimelineIcon(history.executionStatus)"
            placement="top"
          >
            <el-card shadow="never" class="history-card">
              <div class="history-header">
                <div class="execution-info">
                  <span class="execution-id">执行ID: {{ history.executionId }}</span>
                  <el-tag :type="getStatusType(history.executionStatus) as any" size="small">
                    {{ getStatusLabel(history.executionStatus) }}
                  </el-tag>
                </div>
                <div class="execution-meta">
                  <span class="executor-type">{{ getExecutorTypeLabel(history.executorType) }}</span>
                  <span v-if="history.executionDuration" class="duration">
                    耗时: {{ formatDuration(history.executionDuration) }}
                  </span>
                </div>
              </div>

              <!-- 执行详情 -->
              <div class="history-details">
                <el-collapse v-model="expandedItems">
                  <el-collapse-item :name="history.id" title="查看详情">
                    <div class="detail-content">
                      <!-- AI 信息 -->
                      <div v-if="history.aiProvider || history.aiModel" class="detail-section">
                        <h4>AI 信息</h4>
                        <el-descriptions :column="2" size="small">
                          <el-descriptions-item label="AI 提供商" v-if="history.aiProvider">
                            {{ history.aiProvider }}
                          </el-descriptions-item>
                          <el-descriptions-item label="AI 模型" v-if="history.aiModel">
                            {{ history.aiModel }}
                          </el-descriptions-item>
                        </el-descriptions>
                      </div>

                      <!-- 执行环境 -->
                      <div v-if="(history as any).executorInfo || (history as any).vsCodeVersion" class="detail-section">
                        <h4>执行环境</h4>
                        <el-descriptions :column="2" size="small">
                          <el-descriptions-item label="执行器信息" v-if="(history as any).executorInfo">
                            {{ (history as any).executorInfo }}
                          </el-descriptions-item>
                          <el-descriptions-item label="VSCode 版本" v-if="(history as any).vsCodeVersion">
                            {{ (history as any).vsCodeVersion }}
                          </el-descriptions-item>
                          <el-descriptions-item label="插件版本" v-if="(history as any).pluginVersion">
                            {{ (history as any).pluginVersion }}
                          </el-descriptions-item>
                        </el-descriptions>
                      </div>

                      <!-- 提示词 -->
                      <div v-if="history.promptUsed" class="detail-section">
                        <h4>使用的提示词</h4>
                        <el-input
                          :model-value="history.promptUsed"
                          type="textarea"
                          :rows="4"
                          readonly
                        />
                      </div>

                      <!-- 生成的代码 -->
                      <div v-if="history.generatedCode" class="detail-section">
                        <h4>生成的代码</h4>
                        <el-input
                          :model-value="history.generatedCode"
                          type="textarea"
                          :rows="6"
                          readonly
                        />
                      </div>

                      <!-- 输出文件 -->
                      <div v-if="history.outputFiles" class="detail-section">
                        <h4>输出文件</h4>
                        <div class="output-files">
                          <el-tag
                            v-for="file in parseOutputFiles(history.outputFiles)"
                            :key="file"
                            class="file-tag"
                          >
                            {{ file }}
                          </el-tag>
                        </div>
                      </div>

                      <!-- 错误信息 -->
                      <div v-if="history.errorMessage" class="detail-section">
                        <h4>错误信息</h4>
                        <el-alert
                          :title="history.errorMessage"
                          type="error"
                          :closable="false"
                          show-icon
                        />
                      </div>

                      <!-- 执行日志 -->
                      <div v-if="history.executionLog" class="detail-section">
                        <h4>执行日志</h4>
                        <el-input
                          :model-value="history.executionLog"
                          type="textarea"
                          :rows="8"
                          readonly
                        />
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, VideoPlay, Clock, Check, Close, Warning } from '@element-plus/icons-vue'
import type { StepExecutionHistory } from '@/types/development'
import { DevelopmentService } from '@/services/development'

// Props
interface Props {
  stepId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const executing = ref(false)
const historyList = ref<StepExecutionHistory[]>([])
const expandedItems = ref<(string | number)[]>([])

// 生命周期
onMounted(() => {
  loadHistory()
})

// 方法
const loadHistory = async () => {
  try {
    loading.value = true
    historyList.value = await DevelopmentService.getStepExecutionHistory(props.stepId)
  } catch (error) {
    console.error('加载执行历史失败:', error)
    ElMessage.error('加载执行历史失败')
  } finally {
    loading.value = false
  }
}

const startExecution = async () => {
  try {
    executing.value = true
    await DevelopmentService.startStepExecution(props.stepId, 'Manual')
    ElMessage.success('步骤执行已开始')
    loadHistory()
  } catch (error) {
    console.error('开始执行失败:', error)
    ElMessage.error('开始执行失败')
  } finally {
    executing.value = false
  }
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'Running': '执行中',
    'Completed': '已完成',
    'Failed': '失败',
    'Cancelled': '已取消'
  }
  return labels[status] || status
}

const getStatusType = (status: string): 'warning' | 'success' | 'danger' | 'info' => {
  const types: Record<string, 'warning' | 'success' | 'danger' | 'info'> = {
    'Running': 'warning',
    'Completed': 'success',
    'Failed': 'danger',
    'Cancelled': 'info'
  }
  return types[status] || 'info'
}

const getTimelineType = (status: string): 'warning' | 'success' | 'danger' | 'info' | 'primary' => {
  const types: Record<string, 'warning' | 'success' | 'danger' | 'info' | 'primary'> = {
    'Running': 'warning',
    'Completed': 'success',
    'Failed': 'danger',
    'Cancelled': 'info'
  }
  return types[status] || 'primary'
}

const getTimelineIcon = (status: string) => {
  const icons: Record<string, any> = {
    'Running': Clock,
    'Completed': Check,
    'Failed': Close,
    'Cancelled': Warning
  }
  return icons[status] || Clock
}

const getExecutorTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'Manual': '手动执行',
    'Auto': '自动执行',
    'AI': 'AI执行',
    'VSCode': 'VSCode插件'
  }
  return labels[type] || type
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDuration = (duration: number) => {
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    return `${Math.floor(duration / 60)}分${duration % 60}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}小时${minutes}分`
  }
}

const parseOutputFiles = (outputFiles: string) => {
  try {
    return JSON.parse(outputFiles)
  } catch {
    return outputFiles.split(',').map(f => f.trim()).filter(f => f)
  }
}
</script>

<style scoped>
.execution-history {
  padding: 16px 0;
}

.history-toolbar {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.history-card {
  margin-bottom: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.execution-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.execution-id {
  font-weight: 500;
  font-size: 14px;
}

.execution-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.detail-content {
  padding-top: 16px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.output-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-tag {
  margin: 0;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
}
</style>
