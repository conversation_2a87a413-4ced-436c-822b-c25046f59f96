using Microsoft.Extensions.Logging;
using ProjectManagementAI.WPF.Services;

namespace ProjectManagementAI.WPF.Tests.Services
{
    public class VectorOperationServiceTests
    {
        private readonly Mock<ILogger<VectorOperationService>> _mockLogger;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<IAIService> _mockAIService;
        private readonly VectorOperationService _service;

        public VectorOperationServiceTests()
        {
            _mockLogger = new Mock<ILogger<VectorOperationService>>();
            _mockProjectService = new Mock<IProjectService>();
            _mockAIService = new Mock<IAIService>();

            _service = new VectorOperationService(
                _mockLogger.Object,
                _mockProjectService.Object,
                _mockAIService.Object);
        }

        [Fact]
        public async Task BatchCreateProjectsAsync_WithValidTemplates_ShouldReturnSuccessResult()
        {
            // Arrange
            var templates = new List<ProjectTemplate>
            {
                new() { Name = "项目1", ProjectType = "NEW_PROJECT", TechnologyStack = "Vue.js" },
                new() { Name = "项目2", ProjectType = "FEATURE_ENHANCEMENT", TechnologyStack = "React" },
                new() { Name = "项目3", ProjectType = "MAINTENANCE", TechnologyStack = "Angular" }
            };

            _mockProjectService.Setup(x => x.CreateProjectAsync(It.IsAny<object>()))
                .ReturnsAsync(new { Id = 1, Name = "Test Project" });

            // Act
            var result = await _service.BatchCreateProjectsAsync(templates);

            // Assert
            result.Should().NotBeNull();
            result.TotalCount.Should().Be(3);
            result.SuccessCount.Should().Be(3);
            result.FailureCount.Should().Be(0);
            result.Errors.Should().BeEmpty();
        }

        [Fact]
        public async Task BatchCreateProjectsAsync_WithSomeFailures_ShouldReturnPartialSuccessResult()
        {
            // Arrange
            var templates = new List<ProjectTemplate>
            {
                new() { Name = "成功项目", ProjectType = "NEW_PROJECT" },
                new() { Name = "失败项目", ProjectType = "INVALID_TYPE" }
            };

            _mockProjectService.SetupSequence(x => x.CreateProjectAsync(It.IsAny<object>()))
                .ReturnsAsync(new { Id = 1, Name = "Success" })
                .ThrowsAsync(new Exception("创建失败"));

            // Act
            var result = await _service.BatchCreateProjectsAsync(templates);

            // Assert
            result.TotalCount.Should().Be(2);
            result.SuccessCount.Should().Be(1);
            result.FailureCount.Should().Be(1);
            result.Errors.Should().HaveCount(1);
            result.Errors.First().Should().Contain("创建失败");
        }

        [Fact]
        public async Task VectorAnalyzeAsync_WithValidProjectIds_ShouldReturnAnalysisResult()
        {
            // Arrange
            var projectIds = new List<int> { 1, 2, 3 };
            var mockProjects = new List<object>
            {
                new { Id = 1, Name = "项目1", ComplexityScore = 0.7 },
                new { Id = 2, Name = "项目2", ComplexityScore = 0.8 },
                new { Id = 3, Name = "项目3", ComplexityScore = 0.6 }
            };

            _mockProjectService.Setup(x => x.GetProjectsByIdsAsync(projectIds))
                .ReturnsAsync(mockProjects);

            _mockAIService.Setup(x => x.AnalyzeProjectComplexityAsync(It.IsAny<object>()))
                .ReturnsAsync(0.75);

            // Act
            var result = await _service.VectorAnalyzeAsync(projectIds);

            // Assert
            result.Should().NotBeNull();
            result.ProjectAnalyses.Should().HaveCount(3);
            result.GlobalMetrics.Should().ContainKey("AverageComplexity");
            result.Insights.Should().NotBeEmpty();
        }

        [Fact]
        public async Task FindSimilarProjectsAsync_WithValidReference_ShouldReturnSimilarProjects()
        {
            // Arrange
            var referenceProjectId = 1;
            var threshold = 0.7;

            var referenceProject = new { Id = 1, Name = "参考项目", TechnologyStack = "Vue.js" };
            var allProjects = new List<object>
            {
                new { Id = 2, Name = "相似项目1", TechnologyStack = "Vue.js" },
                new { Id = 3, Name = "相似项目2", TechnologyStack = "React" },
                new { Id = 4, Name = "不同项目", TechnologyStack = "Angular" }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(referenceProjectId))
                .ReturnsAsync(referenceProject);

            _mockProjectService.Setup(x => x.GetAllProjectsAsync())
                .ReturnsAsync(allProjects);

            _mockAIService.Setup(x => x.CalculateSimilarityAsync(It.IsAny<object>(), It.IsAny<object>()))
                .ReturnsAsync((object p1, object p2) =>
                {
                    // 模拟相似度计算
                    var tech1 = ((dynamic)p1).TechnologyStack;
                    var tech2 = ((dynamic)p2).TechnologyStack;
                    return tech1 == tech2 ? 0.9 : 0.3;
                });

            // Act
            var result = await _service.FindSimilarProjectsAsync(referenceProjectId, threshold);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1); // 只有一个项目相似度 >= 0.7
            result.First().SimilarityScore.Should().BeGreaterOrEqualTo(threshold);
        }

        [Fact]
        public async Task OptimizeProjectsAsync_ShouldReturnOptimizationSuggestions()
        {
            // Arrange
            var projects = new List<object>
            {
                new { Id = 1, Name = "项目1", Status = "InProgress", Progress = 30 },
                new { Id = 2, Name = "项目2", Status = "Planning", Progress = 0 }
            };

            _mockProjectService.Setup(x => x.GetAllProjectsAsync())
                .ReturnsAsync(projects);

            _mockAIService.Setup(x => x.GenerateOptimizationSuggestionsAsync(It.IsAny<IEnumerable<object>>()))
                .ReturnsAsync(new List<OptimizationSuggestion>
                {
                    new()
                    {
                        ProjectId = 1,
                        Title = "加速开发进度",
                        Description = "建议增加开发人员",
                        Impact = 0.8,
                        Confidence = 0.9
                    }
                });

            // Act
            var result = await _service.OptimizeProjectsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Suggestions.Should().NotBeEmpty();
            result.OverallScore.Should().BeGreaterThan(0);
            result.Metrics.Should().ContainKey("TotalProjects");
        }

        [Fact]
        public async Task PredictProjectRisksAsync_ShouldReturnRiskPredictions()
        {
            // Arrange
            var projectIds = new List<int> { 1, 2 };
            var projects = new List<object>
            {
                new { Id = 1, Name = "高风险项目", EstimatedHours = 1000, ActualHours = 800 },
                new { Id = 2, Name = "低风险项目", EstimatedHours = 100, ActualHours = 90 }
            };

            _mockProjectService.Setup(x => x.GetProjectsByIdsAsync(projectIds))
                .ReturnsAsync(projects);

            _mockAIService.Setup(x => x.PredictRiskAsync(It.IsAny<object>()))
                .ReturnsAsync((object project) =>
                {
                    var estimatedHours = ((dynamic)project).EstimatedHours;
                    return estimatedHours > 500 ? 0.8 : 0.2; // 大项目风险更高
                });

            // Act
            var result = await _service.PredictProjectRisksAsync(projectIds);

            // Assert
            result.Should().HaveCount(2);
            result.First().RiskScore.Should().BeGreaterThan(0.5); // 高风险项目
            result.Last().RiskScore.Should().BeLessThan(0.5);     // 低风险项目
        }

        [Fact]
        public async Task BatchExecuteAutomationAsync_WithValidTasks_ShouldExecuteSuccessfully()
        {
            // Arrange
            var automationTasks = new List<AutomationTask>
            {
                new() { TaskType = "CodeGeneration", ProjectId = 1, Priority = 1 },
                new() { TaskType = "TestGeneration", ProjectId = 2, Priority = 2 }
            };

            _mockAIService.Setup(x => x.ExecuteAutomationTaskAsync(It.IsAny<AutomationTask>()))
                .ReturnsAsync(true);

            // Act
            var result = await _service.BatchExecuteAutomationAsync(automationTasks);

            // Assert
            result.TotalCount.Should().Be(2);
            result.SuccessCount.Should().Be(2);
            result.FailureCount.Should().Be(0);
        }

        [Fact]
        public async Task OptimizeResourceAllocationAsync_ShouldReturnAllocationPlan()
        {
            // Arrange
            var projectIds = new List<int> { 1, 2, 3 };
            var projects = new List<object>
            {
                new { Id = 1, Name = "项目1", EstimatedHours = 100, TechnologyStack = "Vue.js" },
                new { Id = 2, Name = "项目2", EstimatedHours = 200, TechnologyStack = "React" },
                new { Id = 3, Name = "项目3", EstimatedHours = 150, TechnologyStack = "Vue.js" }
            };

            _mockProjectService.Setup(x => x.GetProjectsByIdsAsync(projectIds))
                .ReturnsAsync(projects);

            _mockAIService.Setup(x => x.OptimizeResourceAllocationAsync(It.IsAny<IEnumerable<object>>()))
                .ReturnsAsync(new ResourceAllocationResult
                {
                    EfficiencyScore = 0.85,
                    Allocations = new List<ResourceAllocation>
                    {
                        new()
                        {
                            ProjectId = 1,
                            ProjectName = "项目1",
                            RecommendedTeamMembers = new List<string> { "开发者A", "开发者B" }
                        }
                    }
                });

            // Act
            var result = await _service.OptimizeResourceAllocationAsync(projectIds);

            // Assert
            result.Should().NotBeNull();
            result.EfficiencyScore.Should().BeGreaterThan(0);
            result.Allocations.Should().NotBeEmpty();
            result.Recommendations.Should().NotBeNull();
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public async Task BatchCreateProjectsAsync_WithInvalidInput_ShouldReturnEmptyResult(int templateCount)
        {
            // Arrange
            var templates = templateCount <= 0 ? new List<ProjectTemplate>() : null;

            // Act
            var result = await _service.BatchCreateProjectsAsync(templates ?? new List<ProjectTemplate>());

            // Assert
            result.TotalCount.Should().Be(0);
            result.SuccessCount.Should().Be(0);
            result.FailureCount.Should().Be(0);
        }

        [Fact]
        public async Task VectorAnalyzeAsync_WithEmptyProjectIds_ShouldReturnEmptyResult()
        {
            // Arrange
            var emptyProjectIds = new List<int>();

            // Act
            var result = await _service.VectorAnalyzeAsync(emptyProjectIds);

            // Assert
            result.ProjectAnalyses.Should().BeEmpty();
            result.GlobalMetrics.Should().BeEmpty();
            result.Insights.Should().BeEmpty();
        }
    }
}
