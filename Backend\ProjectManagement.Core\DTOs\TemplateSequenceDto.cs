using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 模板序列DTO
    /// </summary>
    public class TemplateSequenceDto
    {
        /// <summary>
        /// 序列ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 序列名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 序列描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 序列分类
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<TemplateStepDto> Steps { get; set; } = new List<TemplateStepDto>();

        /// <summary>
        /// 序列步骤JSON（完整的序列配置，包含所有步骤信息）
        /// </summary>
        public string? SequenceJson { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool IsDeleted { get; set; }
    }

    /// <summary>
    /// 创建模板序列DTO
    /// </summary>
    public class CreateTemplateSequenceDto
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        [Required(ErrorMessage = "序列名称不能为空")]
        [StringLength(100, ErrorMessage = "序列名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 序列描述
        /// </summary>
        [StringLength(500, ErrorMessage = "序列描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 序列分类
        /// </summary>
        [Required(ErrorMessage = "序列分类不能为空")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<CreateTemplateStepDto> Steps { get; set; } = new List<CreateTemplateStepDto>();
    }

    /// <summary>
    /// 更新模板序列DTO
    /// </summary>
    public class UpdateTemplateSequenceDto
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        [StringLength(100, ErrorMessage = "序列名称长度不能超过100个字符")]
        public string? Name { get; set; }

        /// <summary>
        /// 序列描述
        /// </summary>
        [StringLength(500, ErrorMessage = "序列描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 序列分类
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注信息长度不能超过1000个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<CreateTemplateStepDto>? Steps { get; set; }
    }

    /// <summary>
    /// 序列查询DTO
    /// </summary>
    public class TemplateSequenceQueryDto : PageQueryDto
    {
        /// <summary>
        /// 关键词搜索
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 分类过滤
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string>? Tags { get; set; }

        /// <summary>
        /// 状态过滤
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 创建者过滤
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 创建时间范围开始
        /// </summary>
        public DateTime? CreatedTimeStart { get; set; }

        /// <summary>
        /// 创建时间范围结束
        /// </summary>
        public DateTime? CreatedTimeEnd { get; set; }
    }

    /// <summary>
    /// 序列执行参数DTO
    /// </summary>
    public class ExecuteSequenceDto
    {
        /// <summary>
        /// 执行参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 执行选项
        /// </summary>
        public ExecutionOptionsDto? ExecutionOptions { get; set; }
    }

    /// <summary>
    /// 执行选项DTO
    /// </summary>
    public class ExecutionOptionsDto
    {
        /// <summary>
        /// 执行模式
        /// </summary>
        public string Mode { get; set; } = "normal";

        /// <summary>
        /// 失败处理方式
        /// </summary>
        public string FailureHandling { get; set; } = "stop";

        /// <summary>
        /// 截图选项
        /// </summary>
        public List<string> ScreenshotOptions { get; set; } = new List<string>();

        /// <summary>
        /// 步骤间延迟（秒）
        /// </summary>
        public decimal StepDelay { get; set; } = 1.0m;

        /// <summary>
        /// 全局超时时间（秒）
        /// </summary>
        public int GlobalTimeout { get; set; } = 300;
    }

    /// <summary>
    /// 序列执行结果DTO
    /// </summary>
    public class SequenceExecutionResultDto
    {
        /// <summary>
        /// 执行ID
        /// </summary>
        public int ExecutionId { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 步骤重新排序DTO
    /// </summary>
    public class ReorderStepsDto
    {
        /// <summary>
        /// 步骤ID列表（按新顺序排列）
        /// </summary>
        [Required(ErrorMessage = "步骤ID列表不能为空")]
        public List<int> StepIds { get; set; } = new List<int>();
    }

    /// <summary>
    /// 导出序列DTO
    /// </summary>
    public class ExportSequencesDto
    {
        /// <summary>
        /// 要导出的序列ID列表（为空则导出所有序列）
        /// </summary>
        public List<int>? SequenceIds { get; set; }
    }

    /// <summary>
    /// 导入序列DTO
    /// </summary>
    public class ImportSequencesDto
    {
        /// <summary>
        /// 导出时间
        /// </summary>
        public DateTime ExportTime { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 序列总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 序列数据列表
        /// </summary>
        public List<ImportSequenceDataDto> Sequences { get; set; } = new List<ImportSequenceDataDto>();
    }

    /// <summary>
    /// 导入序列数据DTO
    /// </summary>
    public class ImportSequenceDataDto
    {
        /// <summary>
        /// 序列名称
        /// </summary>
        [Required(ErrorMessage = "序列名称不能为空")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 序列描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 序列分类
        /// </summary>
        [Required(ErrorMessage = "序列分类不能为空")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 步骤列表
        /// </summary>
        public List<ImportStepDataDto> Steps { get; set; } = new List<ImportStepDataDto>();
    }

    /// <summary>
    /// 导入步骤数据DTO
    /// </summary>
    public class ImportStepDataDto
    {
        /// <summary>
        /// 步骤顺序
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "步骤顺序必须大于0")]
        public int StepOrder { get; set; }

        /// <summary>
        /// 动作类型
        /// </summary>
        [Required(ErrorMessage = "动作类型不能为空")]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 步骤描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 参数配置
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        [Range(1, 300, ErrorMessage = "超时时间必须在1到300秒之间")]
        public int TimeoutSeconds { get; set; } = 5;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 10, ErrorMessage = "最大重试次数必须在0到10之间")]
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转到步骤ID
        /// </summary>
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数
        /// </summary>
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量
        /// </summary>
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组ID
        /// </summary>
        public string? GroupId { get; set; }
    }

    /// <summary>
    /// 导入序列结果DTO
    /// </summary>
    public class ImportSequencesResultDto
    {
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
    }
}
