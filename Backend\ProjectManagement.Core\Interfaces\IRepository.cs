using System.Linq.Expressions;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 通用仓储接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IRepository<T> where T : BaseEntity, new()
{
    #region 查询操作

    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体对象</returns>
    Task<T?> GetByIdAsync(int id);

    /// <summary>
    /// 根据条件获取单个实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体对象</returns>
    Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>实体列表</returns>
    Task<List<T>> GetAllAsync(bool includeDeleted = false);

    /// <summary>
    /// 根据条件获取实体列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>实体列表</returns>
    Task<List<T>> GetListAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="predicate">查询条件</param>
    /// <param name="orderBy">排序表达式</param>
    /// <param name="isAsc">是否升序</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<T>> GetPagedListAsync(
        int pageIndex,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool isAsc = true,
        bool includeDeleted = false);

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);

    /// <summary>
    /// 获取记录数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>记录数量</returns>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, bool includeDeleted = false);

    #endregion

    #region 增删改操作

    /// <summary>
    /// 添加实体
    /// </summary>
    /// <param name="entity">实体对象</param>
    /// <returns>添加后的实体</returns>
    Task<T> AddAsync(T entity);

    /// <summary>
    /// 批量添加实体
    /// </summary>
    /// <param name="entities">实体列表</param>
    /// <returns>添加的数量</returns>
    Task<int> AddRangeAsync(List<T> entities);

    /// <summary>
    /// 更新实体
    /// </summary>
    /// <param name="entity">实体对象</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateAsync(T entity);

    /// <summary>
    /// 批量更新实体
    /// </summary>
    /// <param name="entities">实体列表</param>
    /// <returns>更新的数量</returns>
    Task<int> UpdateRangeAsync(List<T> entities);

    /// <summary>
    /// 根据条件更新实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="updateExpression">更新表达式</param>
    /// <returns>更新的数量</returns>
    Task<int> UpdateAsync(Expression<Func<T, bool>> predicate, Expression<Func<T, T>> updateExpression);

    /// <summary>
    /// 物理删除实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(int id);

    /// <summary>
    /// 物理删除实体
    /// </summary>
    /// <param name="entity">实体对象</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(T entity);

    /// <summary>
    /// 根据条件物理删除实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>删除的数量</returns>
    Task<int> DeleteAsync(Expression<Func<T, bool>> predicate);

    /// <summary>
    /// 软删除实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <param name="deletedBy">删除人ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> SoftDeleteAsync(int id, int? deletedBy = null);

    /// <summary>
    /// 根据条件软删除实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <param name="deletedBy">删除人ID</param>
    /// <returns>删除的数量</returns>
    Task<int> SoftDeleteAsync(Expression<Func<T, bool>> predicate, int? deletedBy = null);

    /// <summary>
    /// 恢复软删除的实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>是否恢复成功</returns>
    Task<bool> RestoreAsync(int id);

    #endregion

    #region 事务操作

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <returns>事务对象</returns>
    Task BeginTransactionAsync();

    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitTransactionAsync();

    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackTransactionAsync();

    #endregion
}

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据列表
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages;
}
