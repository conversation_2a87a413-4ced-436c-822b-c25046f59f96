using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// Azure OpenAI服务提供商
/// </summary>
public class AzureOpenAIProvider : IAIProvider
{
    private readonly ILogger<AzureOpenAIProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly AzureOpenAIConfig _config;
    private readonly AIUsageStats _usageStats;

    public string Name => "Azure";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "gpt-4", "gpt-3.5-turbo", "text-embedding-ada-002" };

    public AzureOpenAIProvider(
        ILogger<AzureOpenAIProvider> logger,
        HttpClient httpClient,
        IOptions<AzureOpenAIConfig> config)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config.Value;
        _usageStats = new AIUsageStats();

        // 配置HttpClient
        _httpClient.BaseAddress = new Uri(_config.Endpoint);
        _httpClient.DefaultRequestHeaders.Add("api-key", _config.ApiKey);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "ProjectManagement-AI/1.0");
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Azure OpenAI开始生成文本，模型: {Model}", config.Model);

            // 更新提供商名称引用
            var providerName = Name;

            var requestBody = new
            {
                messages = new[]
                {
                    new { role = "system", content = "你是一个专业的软件开发助手，擅长需求分析、系统设计和代码生成。请用中文回答。" },
                    new { role = "user", content = prompt }
                },
                max_tokens = config.MaxTokens,
                temperature = config.Temperature,
                top_p = 1.0,
                frequency_penalty = 0,
                presence_penalty = 0
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var deploymentName = GetDeploymentName(config.Model);
            var response = await _httpClient.PostAsync(
                $"openai/deployments/{deploymentName}/chat/completions?api-version=2024-02-15-preview",
                content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Azure OpenAI API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"Azure OpenAI API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var result = responseData
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString() ?? string.Empty;

            // 更新使用统计
            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("Azure OpenAI文本生成完成，响应长度: {Length}", result.Length);
            return result;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Azure OpenAI文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Azure OpenAI开始获取向量嵌入");

            var requestBody = new
            {
                input = text,
                model = "text-embedding-ada-002"
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(
                "openai/deployments/text-embedding-ada-002/embeddings?api-version=2024-02-15-preview",
                content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Azure OpenAI嵌入API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"Azure OpenAI嵌入API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var embeddingArray = responseData
                .GetProperty("data")[0]
                .GetProperty("embedding")
                .EnumerateArray()
                .Select(x => x.GetSingle())
                .ToArray();

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("Azure OpenAI向量嵌入获取完成，维度: {Dimension}", embeddingArray.Length);
            return embeddingArray;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Azure OpenAI向量嵌入获取失败");
            throw;
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            var testConfig = new AIModelConfig
            {
                Model = "gpt-3.5-turbo",
                MaxTokens = 10,
                Temperature = 0.1f
            };

            var result = await GenerateAsync("Hello", testConfig);
            IsAvailable = !string.IsNullOrEmpty(result);

            _logger.LogInformation("Azure OpenAI健康检查完成，状态: {Status}", IsAvailable ? "正常" : "异常");
            return IsAvailable;
        }
        catch (Exception ex)
        {
            IsAvailable = false;
            _logger.LogError(ex, "Azure OpenAI健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    #region 私有方法

    /// <summary>
    /// 获取部署名称
    /// </summary>
    private string GetDeploymentName(string model)
    {
        return model switch
        {
            "gpt-4" => _config.GPT4DeploymentName ?? "gpt-4",
            "gpt-3.5-turbo" => _config.GPT35DeploymentName ?? "gpt-35-turbo",
            _ => model
        };
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success, JsonElement? responseData = null)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        // 更新平均响应时间
        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        // 更新Token使用量
        if (success && responseData.HasValue)
        {
            try
            {
                if (responseData.Value.TryGetProperty("usage", out var usage))
                {
                    if (usage.TryGetProperty("total_tokens", out var totalTokens))
                    {
                        _usageStats.TotalTokens += totalTokens.GetInt64();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析Token使用量失败");
            }
        }

        _usageStats.LastUpdated = DateTime.Now;
    }

    #endregion
}

/// <summary>
/// Azure OpenAI配置
/// </summary>
public class AzureOpenAIConfig
{
    /// <summary>
    /// API端点
    /// </summary>
    public string Endpoint { get; set; } = string.Empty;

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// GPT-4部署名称
    /// </summary>
    public string? GPT4DeploymentName { get; set; }

    /// <summary>
    /// GPT-3.5部署名称
    /// </summary>
    public string? GPT35DeploymentName { get; set; }

    /// <summary>
    /// 嵌入模型部署名称
    /// </summary>
    public string? EmbeddingDeploymentName { get; set; }

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300; // 5分钟超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}
