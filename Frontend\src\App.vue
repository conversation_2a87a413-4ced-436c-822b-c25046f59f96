<template>
  <div id="app">
    <!-- 主布局 -->
    <el-container v-if="isAuthenticated" class="app-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="app-sidebar">
        <AppSidebar
          :collapsed="sidebarCollapsed"
          @toggle="toggleSidebar"
        />
      </el-aside>

      <!-- 主内容区 -->
      <el-container class="app-main">
        <!-- 顶部导航 -->
        <el-header class="app-header">
          <AppHeader
            @toggle-sidebar="toggleSidebar"
            @logout="handleLogout"
          />
        </el-header>

        <!-- 标签页导航 -->
        <AppTabs />

        <!-- 面包屑导航 -->
        <div v-if="showBreadcrumb" class="app-breadcrumb">
          <AppBreadcrumb />
        </div>

        <!-- 页面内容 -->
        <el-main class="app-content">
          <router-view v-slot="{ Component, route }">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <!-- 未登录状态 -->
    <div v-else class="auth-container">
      <router-view />
    </div>

    <!-- 全局加载遮罩 -->
    <el-loading
      v-if="globalLoading"
      :lock="true"
      text="加载中..."
      background="rgba(0, 0, 0, 0.7)"
    />

    <!-- 开发环境调试器 -->
    <AuthDebugger v-if="isDevelopment" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useTabsStore } from '@/stores/tabs'
import AppSidebar from '@/components/layout/AppSidebar.vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppTabs from '@/components/layout/AppTabs.vue'
import AppBreadcrumb from '@/components/layout/AppBreadcrumb.vue'
import AuthDebugger from '@/components/AuthDebugger.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const tabsStore = useTabsStore()

// 响应式状态
const sidebarCollapsed = ref(false)
const globalLoading = ref(false)
const cachedViews = ref<string[]>([])

// 计算属性
const isAuthenticated = computed(() => authStore.isAuthenticated)
const sidebarWidth = computed(() => sidebarCollapsed.value ? '64px' : '240px')
const showBreadcrumb = computed(() => {
  return isAuthenticated.value &&
         route.meta.breadcrumb &&
         Array.isArray(route.meta.breadcrumb) &&
         route.meta.breadcrumb.length > 0
})
const isDevelopment = computed(() => import.meta.env.DEV)

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存侧边栏状态到本地存储
  localStorage.setItem('sidebarCollapsed', String(sidebarCollapsed.value))
}

const handleLogout = async () => {
  try {
    globalLoading.value = true
    await authStore.logout()
    // 清空标签页
    tabsStore.clearTabs()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  } finally {
    globalLoading.value = false
  }
}

// 添加页面到缓存
const addToCache = (routeName: string) => {
  if (routeName && !cachedViews.value.includes(routeName)) {
    cachedViews.value.push(routeName)
  }
}

// 监听路由变化，管理缓存的视图
watch(route, (to) => {
  if (to.meta.keepAlive && to.name) {
    addToCache(to.name as string)
  }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
  // 恢复侧边栏状态
  const savedSidebarState = localStorage.getItem('sidebarCollapsed')
  if (savedSidebarState !== null) {
    sidebarCollapsed.value = savedSidebarState === 'true'
  }

  // 初始化标签页
  if (authStore.isAuthenticated) {
    tabsStore.initDefaultTab()
  }

  // 监听窗口大小变化，自动折叠侧边栏
  const handleResize = () => {
    if (window.innerWidth < 768) {
      sidebarCollapsed.value = true
    }
  }

  window.addEventListener('resize', handleResize)
  handleResize() // 初始检查

  // 清理事件监听器
  return () => {
    window.removeEventListener('resize', handleResize)
  }
})

// 监听认证状态变化
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (isAuth) {
    // 登录后初始化默认标签
    tabsStore.initDefaultTab()
  } else {
    // 登出后清空标签
    tabsStore.clearTabs()
  }
})
</script>

<style lang="scss">
#app {
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100vh;
}

.app-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-main {
  flex: 1;
  overflow: hidden;
}

.app-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0;
  height: 60px !important;
  line-height: 60px;
}

.app-breadcrumb {
  background: var(--el-bg-color-page);
  padding: 12px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.app-content {
  background: var(--el-bg-color-page);
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px - 40px); // 减去标签页高度

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-bg-color-page);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 3px;

    &:hover {
      background: var(--el-border-color);
    }
  }
}

.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
