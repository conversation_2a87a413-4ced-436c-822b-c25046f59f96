<template>
  <el-dialog
    v-model="visible"
    :title="isUpdateMode ? '更新自动化序列' : '保存自动化序列'"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="序列名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="例如：登录并发送消息的自动化流程"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
              <el-option label="🔧 代码生成" value="代码生成" />
              <el-option label="🖱️ UI自动化" value="UI自动化" />
              <el-option label="📊 数据处理" value="数据处理" />
              <el-option label="🔄 流程自动化" value="流程自动化" />
              <el-option label="🧪 测试自动化" value="测试自动化" />
              <el-option label="📁 文件操作" value="文件操作" />
              <el-option label="🌐 网络操作" value="网络操作" />
              <el-option label="🔧 系统操作" value="系统操作" />
              <el-option label="📝 文档处理" value="文档处理" />
              <el-option label="🎯 其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="详细描述这个自动化序列的功能和用途..."
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注信息">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="添加一些额外的说明信息，如使用场景、注意事项等..."
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 保存信息展示 -->
      <el-card class="save-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>保存信息</span>
          </div>
        </template>
        <div class="save-info">
          <div class="info-item">
            <span class="label">代码语言：</span>
            <el-tag type="primary" size="small">{{ codeLanguage.toUpperCase() }}</el-tag>
          </div>
          <div class="info-item">
            <span class="label">代码行数：</span>
            <span class="value">{{ codeLines }} 行</span>
          </div>
          <div class="info-item">
            <span class="label">解析步骤：</span>
            <span class="value">{{ stepCount }} 个</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSave">
          <el-icon><Document /></el-icon>
          {{ isUpdateMode ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Document, InfoFilled } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  codeLanguage: string
  codeLines: number
  stepCount: number
  currentSequence?: any
}

const props = withDefaults(defineProps<Props>(), {
  codeLanguage: 'javascript',
  codeLines: 0,
  stepCount: 0,
  currentSequence: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': [data: SequenceInfo]
}>()

// Types
interface SequenceInfo {
  name: string
  description: string
  category: string
  notes: string
}

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  notes: ''
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isUpdateMode = computed(() => {
  return props.currentSequence && (props.currentSequence.Id || props.currentSequence.id)
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入序列名称', trigger: 'blur' },
    { min: 2, max: 50, message: '序列名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    category: '',
    notes: ''
  })
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const sequenceInfo: SequenceInfo = {
      name: form.name,
      description: form.description || `通过代码编辑器创建的${props.codeLanguage.toUpperCase()}序列`,
      category: form.category,
      notes: form.notes
    }

    emit('save', sequenceInfo)
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  resetForm()
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    if (isUpdateMode.value) {
      // 更新模式：预填充现有数据
      const sequence = props.currentSequence
      Object.assign(form, {
        name: sequence.Name || sequence.name || '',
        description: sequence.Description || sequence.description || '',
        category: sequence.Category || sequence.category || '',
        notes: sequence.Notes || sequence.notes || ''
      })
    } else {
      // 新建模式：重置表单
      resetForm()
    }
  }
})
</script>

<style scoped lang="scss">
.save-info-card {
  margin-top: 16px;
  border: 1px solid #e4e7ed;
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #303133;
  }
}

.save-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      color: #606266;
      margin-right: 8px;
      min-width: 80px;
    }
    
    .value {
      font-weight: 600;
      color: #303133;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
