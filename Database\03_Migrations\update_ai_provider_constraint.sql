-- =============================================
-- 更新PromptUsageStats表的AIProvider约束
-- 添加对更多AI提供商的支持
-- =============================================

USE ProjectManagementAI;
GO

-- 删除现有的AIProvider约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptUsageStats_AIProvider')
BEGIN
    ALTER TABLE PromptUsageStats DROP CONSTRAINT CK_PromptUsageStats_AIProvider;
    PRINT '已删除旧的AIProvider约束';
END
GO

-- 创建新的AIProvider约束，支持更多提供商
ALTER TABLE PromptUsageStats 
ADD CONSTRAINT CK_PromptUsageStats_AIProvider 
CHECK (AIProvider IN (
    'Azure',           -- Azure OpenAI
    'OpenAI',          -- <PERSON><PERSON><PERSON>官方
    'DeepSeek',        -- DeepSeek
    'Claude',          -- <PERSON><PERSON><PERSON> <PERSON>
    'Ollama',          -- 本地Ollama
    'Mock',            -- 模拟提供商
    'System',          -- 系统内部使用
    'Default',         -- 默认提供商
    'Gemini',          -- Google Gemini
    'Qwen',            -- 阿里通义千问
    'ChatGLM',         -- 智谱ChatGLM
    'Baichuan',        -- 百川大模型
    'Moonshot',        -- 月之暗面
    'Minimax',         -- MiniMax
    'SenseTime',       -- 商汤科技
    'iFlytek',         -- 科大讯飞
    'Baidu',           -- 百度文心一言
    'Tencent',         -- 腾讯混元
    'ByteDance'        -- 字节跳动豆包
));
GO

PRINT '已更新AIProvider约束，支持更多AI提供商';

-- 验证约束是否正确创建
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition
FROM sys.check_constraints cc
INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
WHERE t.name = 'PromptUsageStats' 
  AND cc.name = 'CK_PromptUsageStats_AIProvider';
GO

-- 检查是否有违反约束的现有数据
SELECT DISTINCT AIProvider, COUNT(*) as Count
FROM PromptUsageStats 
WHERE AIProvider NOT IN (
    'Azure', 'OpenAI', 'DeepSeek', 'Claude', 'Ollama', 'Mock', 'System', 'Default',
    'Gemini', 'Qwen', 'ChatGLM', 'Baichuan', 'Moonshot', 'Minimax', 
    'SenseTime', 'iFlytek', 'Baidu', 'Tencent', 'ByteDance'
)
GROUP BY AIProvider;

PRINT 'AIProvider约束更新完成！';
PRINT '现在支持的AI提供商：Azure, OpenAI, DeepSeek, Claude, Ollama, Mock, System, Default, Gemini, Qwen, ChatGLM, Baichuan, Moonshot, Minimax, SenseTime, iFlytek, Baidu, Tencent, ByteDance';
GO
