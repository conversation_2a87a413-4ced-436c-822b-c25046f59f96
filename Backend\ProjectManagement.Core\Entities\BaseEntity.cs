using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 基础实体类，包含所有实体的公共属性
/// 对应DatabaseSchema.sql中的审计字段设计
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// 主键ID - 自增整型主键
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
    public int Id { get; set; }

    /// <summary>
    /// 创建时间 - 记录创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间 - 记录最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "更新时间")]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 创建人ID - 关联Users表
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "创建人ID")]
    public int? CreatedBy { get; set; }

    /// <summary>
    /// 更新人ID - 关联Users表
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "更新人ID")]
    public int? UpdatedBy { get; set; }

    /// <summary>
    /// 软删除标记 - 0=未删除, 1=已删除
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "软删除标记")]
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// 删除时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "删除时间")]
    public DateTime? DeletedTime { get; set; }

    /// <summary>
    /// 删除人ID - 关联Users表
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "删除人ID")]
    public int? DeletedBy { get; set; }

    /// <summary>
    /// 版本号 - 用于乐观锁并发控制
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "版本号")]
    public int Version { get; set; } = 1;

    /// <summary>
    /// 备注信息
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "备注信息")]
    public string? Remarks { get; set; }
}
