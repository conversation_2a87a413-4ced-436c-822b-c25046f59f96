using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.Core.DTOs.Agent
{
    /// <summary>
    /// Agent定义
    /// </summary>
    public class AgentDefinition
    {
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Capabilities { get; set; } = new();
        public List<string> Specialties { get; set; } = new();
        public string SystemPrompt { get; set; } = string.Empty;
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// Agent任务请求
    /// </summary>
    public class AgentTaskRequest
    {
        public string AgentType { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty; // analysis, recommendation, planning, review
        public string TaskDescription { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public string UserMessage { get; set; } = string.Empty;
        public AIModelConfig? AIConfig { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// Agent执行结果
    /// </summary>
    public class AgentExecutionResult
    {
        public string TaskId { get; set; } = string.Empty;
        public string AgentType { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty;
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Output { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 协作请求
    /// </summary>
    public class CollaborationRequest
    {
        public string CollaborationType { get; set; } = string.Empty; // sequential, parallel, debate, consensus
        public string TaskDescription { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public string UserMessage { get; set; } = string.Empty;
        public List<string> ParticipantAgents { get; set; } = new();
        public AIModelConfig? AIConfig { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 协作结果
    /// </summary>
    public class CollaborationResult
    {
        public string CollaborationId { get; set; } = string.Empty;
        public string CollaborationType { get; set; } = string.Empty;
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
        public string FinalOutput { get; set; } = string.Empty;
        public List<AgentExecutionResult> AgentResults { get; set; } = new();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Agent信息
    /// </summary>
    public class AgentInfo
    {
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Capabilities { get; set; } = new();
        public List<string> Specialties { get; set; } = new();
    }

    /// <summary>
    /// Agent推荐
    /// </summary>
    public class AgentRecommendation
    {
        public string RecommendedAgentType { get; set; } = string.Empty;
        public float ConfidenceScore { get; set; }
        public string Reasoning { get; set; } = string.Empty;
        public Dictionary<string, float> AlternativeAgents { get; set; } = new();
    }
}
