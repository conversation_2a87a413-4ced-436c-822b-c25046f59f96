using SqlSugar;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Configuration;

/// <summary>
/// SqlSugar配置类
/// </summary>
public class SqlSugarConfig
{
    /// <summary>
    /// 创建SqlSugar客户端
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>SqlSugar客户端</returns>
    public static ISqlSugarClient CreateSqlSugarClient(IConfiguration configuration, ILogger? logger = null)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("数据库连接字符串未配置");
        }

        var config = new ConnectionConfig
        {
            ConnectionString = connectionString,
            DbType = DbType.SqlServer,
            IsAutoCloseConnection = true,
            InitKeyType = InitKeyType.Attribute,
            ConfigureExternalServices = new ConfigureExternalServices
            {
                // 实体配置
                EntityService = (property, column) =>
                {
                    // 自动设置主键
                    if (property.Name.Equals("Id", StringComparison.OrdinalIgnoreCase))
                    {
                        column.IsPrimarykey = true;
                        column.IsIdentity = true;
                    }

                    // 自动设置创建时间默认值
                    if (property.Name.Equals("CreatedTime", StringComparison.OrdinalIgnoreCase))
                    {
                        column.IsNullable = false;
                    }

                    // 自动设置软删除字段
                    if (property.Name.Equals("IsDeleted", StringComparison.OrdinalIgnoreCase))
                    {
                        column.IsNullable = false;
                    }
                },

                // 实体命名服务
                EntityNameService = (type, entity) =>
                {
                    // 如果没有指定表名，使用类名的复数形式
                    if (string.IsNullOrEmpty(entity.DbTableName))
                    {
                        entity.DbTableName = GetPluralTableName(type.Name);
                    }
                }
            },
            MoreSettings = new ConnMoreSettings
            {
                // 是否自动删除查询过滤器
                IsAutoRemoveDataCache = true,
                // 是否启用全局过滤器
                IsWithNoLockQuery = true,
                // SQL Server使用NVARCHAR
                SqlServerCodeFirstNvarchar = true
            }
        };

        var db = new SqlSugarClient(config);

        // 设置命令超时时间（秒）
        db.Ado.CommandTimeOut = 30;

        // 配置日志
        if (logger != null)
        {
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                logger.LogDebug("执行SQL: {Sql}, 参数: {Parameters}",
                    sql,
                    string.Join(",", pars?.Select(p => $"{p.ParameterName}:{p.Value}") ?? Array.Empty<string>()));
            };

            db.Aop.OnError = (exp) =>
            {
                logger.LogError("SQL执行错误: {Sql}, 错误信息: {Error}", exp.Sql, exp.Message);
            };

            db.Aop.OnExecutingChangeSql = (sql, pars) =>
            {
                logger.LogDebug("SQL执行前: {Sql}", sql);
                return new KeyValuePair<string, SugarParameter[]>(sql, pars);
            };
        }

        // 配置全局过滤器（软删除）
        db.QueryFilter.AddTableFilter<ISoftDelete>(it => it.IsDeleted == false);

        return db;
    }

    /// <summary>
    /// 获取复数表名
    /// </summary>
    /// <param name="className">类名</param>
    /// <returns>表名</returns>
    private static string GetPluralTableName(string className)
    {
        // 简单的复数规则，可以根据需要扩展
        if (className.EndsWith("y", StringComparison.OrdinalIgnoreCase))
        {
            return className.Substring(0, className.Length - 1) + "ies";
        }
        else if (className.EndsWith("s", StringComparison.OrdinalIgnoreCase) ||
                 className.EndsWith("sh", StringComparison.OrdinalIgnoreCase) ||
                 className.EndsWith("ch", StringComparison.OrdinalIgnoreCase) ||
                 className.EndsWith("x", StringComparison.OrdinalIgnoreCase) ||
                 className.EndsWith("z", StringComparison.OrdinalIgnoreCase))
        {
            return className + "es";
        }
        else
        {
            return className + "s";
        }
    }
}

/// <summary>
/// 软删除接口
/// </summary>
public interface ISoftDelete
{
    /// <summary>
    /// 是否删除
    /// </summary>
    bool IsDeleted { get; set; }
}

/// <summary>
/// SqlSugar扩展方法
/// </summary>
public static class SqlSugarExtensions
{
    /// <summary>
    /// 创建数据库和表
    /// </summary>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="types">实体类型数组</param>
    public static void CreateDatabaseAndTables(this ISqlSugarClient db, params Type[] types)
    {
        try
        {
            // 创建数据库（如果不存在）
            db.DbMaintenance.CreateDatabase();

            // 创建表（如果不存在）
            db.CodeFirst.InitTables(types);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("创建数据库和表失败", ex);
        }
    }

    /// <summary>
    /// 批量插入数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="entities">实体列表</param>
    /// <param name="batchSize">批次大小</param>
    /// <returns>插入的记录数</returns>
    public static async Task<int> BulkInsertAsync<T>(this ISqlSugarClient db, List<T> entities, int batchSize = 1000) where T : class, new()
    {
        if (entities == null || entities.Count == 0)
            return 0;

        var totalInserted = 0;
        var batches = entities.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var result = await db.Insertable(batch.ToList()).ExecuteCommandAsync();
            totalInserted += result;
        }

        return totalInserted;
    }

    /// <summary>
    /// 批量更新数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="entities">实体列表</param>
    /// <param name="batchSize">批次大小</param>
    /// <returns>更新的记录数</returns>
    public static async Task<int> BulkUpdateAsync<T>(this ISqlSugarClient db, List<T> entities, int batchSize = 1000) where T : class, new()
    {
        if (entities == null || entities.Count == 0)
            return 0;

        var totalUpdated = 0;
        var batches = entities.Chunk(batchSize);

        foreach (var batch in batches)
        {
            var result = await db.Updateable(batch.ToList()).ExecuteCommandAsync();
            totalUpdated += result;
        }

        return totalUpdated;
    }

    /// <summary>
    /// 执行事务
    /// </summary>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="action">事务操作</param>
    /// <returns>事务结果</returns>
    public static async Task<DbResult<bool>> ExecuteTransactionAsync(this ISqlSugarClient db, Func<Task> action)
    {
        var result = await db.Ado.UseTranAsync(async () =>
        {
            await action();
        });

        return result;
    }

    /// <summary>
    /// 执行事务并返回结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="func">事务操作</param>
    /// <returns>事务结果</returns>
    public static async Task<DbResult<T>> ExecuteTransactionAsync<T>(this ISqlSugarClient db, Func<Task<T>> func)
    {
        var result = await db.Ado.UseTranAsync(async () =>
        {
            return await func();
        });

        return result;
    }
}
