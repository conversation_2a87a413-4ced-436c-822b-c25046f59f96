using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 部署任务实体 - 对应DatabaseSchema.sql中的DeploymentTasks表
/// 功能: 管理项目的自动化部署任务和部署历史
/// 支持: 多环境部署、多平台支持、部署脚本、日志记录
/// </summary>
[SugarTable("DeploymentTasks", "自动化部署管理表")]
public class DeploymentTask
{
    /// <summary>
    /// 部署任务唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "部署任务唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 部署环境: Development(开发), Staging(测试), Production(生产)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "部署环境")]
    public string Environment { get; set; } = string.Empty;

    /// <summary>
    /// 部署类型: Docker(容器), IIS(Windows服务器), Azure(云平台), AWS(云平台), Kubernetes(容器编排)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "部署类型")]
    public string? DeploymentType { get; set; }

    /// <summary>
    /// 部署状态: Pending(待部署), InProgress(部署中), Completed(部署成功), Failed(部署失败)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "部署状态")]
    public string Status { get; set; } = "Pending";

    /// <summary>
    /// 部署脚本内容（Docker文件、PowerShell脚本等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "部署脚本内容")]
    public string? DeploymentScript { get; set; }

    /// <summary>
    /// 部署后的访问URL地址
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "部署后的访问URL地址")]
    public string? DeploymentUrl { get; set; }

    /// <summary>
    /// 部署过程的日志输出
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "部署过程的日志输出")]
    public string? LogOutput { get; set; }

    /// <summary>
    /// 部署失败时的错误信息
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "部署失败时的错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 部署任务创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "部署任务创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 部署任务完成时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "部署任务完成时间")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }
}
