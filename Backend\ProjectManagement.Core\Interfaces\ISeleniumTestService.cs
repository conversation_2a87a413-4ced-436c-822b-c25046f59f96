using ProjectManagement.Core.DTOs;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// Selenium测试服务接口
    /// </summary>
    public interface ISeleniumTestService
    {
        /// <summary>
        /// 获取测试脚本列表
        /// </summary>
        Task<PagedResult<SeleniumScriptDto>> GetScriptsAsync(
            int userId,
            int? projectId = null,
            string? category = null,
            string? keyword = null,
            int page = 1,
            int pageSize = 20);

        /// <summary>
        /// 获取单个测试脚本
        /// </summary>
        Task<SeleniumScriptDto?> GetScriptAsync(int id, int userId);

        /// <summary>
        /// 创建测试脚本
        /// </summary>
        Task<SeleniumScriptDto> CreateScriptAsync(CreateSeleniumScriptDto dto, int userId);

        /// <summary>
        /// 更新测试脚本
        /// </summary>
        Task<SeleniumScriptDto?> UpdateScriptAsync(int id, UpdateSeleniumScriptDto dto, int userId);

        /// <summary>
        /// 删除测试脚本
        /// </summary>
        Task<bool> DeleteScriptAsync(int id, int userId);

        /// <summary>
        /// 执行测试脚本
        /// </summary>
        Task<SeleniumExecutionResultDto?> ExecuteScriptAsync(int scriptId, ExecuteSeleniumScriptDto? dto, int userId);

        /// <summary>
        /// 停止执行
        /// </summary>
        Task<bool> StopExecutionAsync(string executionId, int userId);

        /// <summary>
        /// 获取执行结果
        /// </summary>
        Task<SeleniumExecutionResultDto?> GetExecutionResultAsync(string executionId, int userId);

        /// <summary>
        /// 获取执行历史
        /// </summary>
        Task<PagedResult<SeleniumExecutionResultDto>> GetExecutionHistoryAsync(
            int userId,
            int? scriptId = null,
            string? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1,
            int pageSize = 20);

        /// <summary>
        /// 获取脚本模板
        /// </summary>
        Task<Dictionary<string, string>> GetTemplatesAsync();

        /// <summary>
        /// 验证脚本语法
        /// </summary>
        Task<SeleniumValidationResultDto> ValidateScriptAsync(string code);

        /// <summary>
        /// 格式化脚本代码
        /// </summary>
        Task<SeleniumFormatResultDto> FormatScriptAsync(string code);

        /// <summary>
        /// 获取统计信息
        /// </summary>
        Task<SeleniumStatisticsDto> GetStatisticsAsync(
            int userId,
            int? projectId = null,
            DateTime? startDate = null,
            DateTime? endDate = null);

        /// <summary>
        /// 批量执行脚本
        /// </summary>
        Task<SeleniumBatchExecutionResultDto> BatchExecuteAsync(BatchExecuteSeleniumDto dto, int userId);

        /// <summary>
        /// 健康检查
        /// </summary>
        Task<SeleniumHealthCheckResultDto> HealthCheckAsync();

        /// <summary>
        /// 复制脚本
        /// </summary>
        Task<SeleniumScriptDto?> DuplicateScriptAsync(int id, string? newName, int userId);

        /// <summary>
        /// 导出脚本
        /// </summary>
        Task<byte[]> ExportScriptsAsync(List<int> scriptIds, string format, int userId);

        /// <summary>
        /// 导入脚本
        /// </summary>
        Task<ImportResultDto> ImportScriptsAsync(Stream fileStream, string fileName, int userId);

        /// <summary>
        /// 获取浏览器信息
        /// </summary>
        Task<BrowserInfoDto> GetBrowserInfoAsync();
    }

    /// <summary>
    /// 导入结果DTO
    /// </summary>
    public class ImportResultDto
    {
        public int Imported { get; set; }
        public int Failed { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// 浏览器信息DTO
    /// </summary>
    public class BrowserInfoDto
    {
        public List<BrowserDto> Browsers { get; set; } = new();
        public List<DriverDto> Drivers { get; set; } = new();
    }

    /// <summary>
    /// 浏览器DTO
    /// </summary>
    public class BrowserDto
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public bool Available { get; set; }
    }

    /// <summary>
    /// 驱动DTO
    /// </summary>
    public class DriverDto
    {
        public string Browser { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
    }
}
