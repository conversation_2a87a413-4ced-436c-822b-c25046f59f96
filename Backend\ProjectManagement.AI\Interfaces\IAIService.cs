using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.AI.Interfaces;

/// <summary>
/// AI服务接口
/// </summary>
public interface IAIService
{
    /// <summary>
    /// 生成文本
    /// </summary>
    /// <param name="prompt">提示词</param>
    /// <param name="config">模型配置</param>
    /// <returns>生成的文本</returns>
    Task<string> GenerateTextAsync(string prompt, AIModelConfig? config = null);

    /// <summary>
    /// 生成文本（指定提供商和模型）
    /// </summary>
    /// <param name="prompt">提示词</param>
    /// <param name="provider">AI提供商</param>
    /// <param name="model">AI模型</param>
    /// <returns>生成的文本</returns>
    Task<string> GenerateTextAsync(string prompt, string? provider = null, string? model = null);

    /// <summary>
    /// 分析需求
    /// </summary>
    /// <param name="requirements">需求描述</param>
    /// <param name="config">模型配置</param>
    /// <returns>需求分析结果</returns>
    Task<RequirementAnalysisResult> AnalyzeRequirementsAsync(string requirements, AIModelConfig? config = null);

    /// <summary>
    /// 生成需求规格书
    /// </summary>
    /// <param name="analysisResult">需求分析结果</param>
    /// <param name="config">模型配置</param>
    /// <returns>需求规格书内容</returns>
    Task<string> GenerateSpecificationAsync(RequirementAnalysisResult analysisResult, AIModelConfig? config = null);

    /// <summary>
    /// 生成ER图代码
    /// </summary>
    /// <param name="prompt">生成提示词</param>
    /// <param name="preferredModel">首选AI模型</param>
    /// <param name="config">模型配置</param>
    /// <returns>ER图Mermaid代码</returns>
    Task<string> GenerateERDiagramAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null);

    /// <summary>
    /// 生成Context图代码
    /// </summary>
    /// <param name="prompt">生成提示词</param>
    /// <param name="preferredModel">首选AI模型</param>
    /// <param name="config">模型配置</param>
    /// <returns>Context图Mermaid代码</returns>
    Task<string> GenerateContextDiagramAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null);

    /// <summary>
    /// 生成原型图代码
    /// </summary>
    /// <param name="prompt">生成提示词</param>
    /// <param name="preferredModel">首选AI模型</param>
    /// <param name="config">模型配置</param>
    /// <returns>原型图Mermaid代码</returns>
    Task<string> GeneratePrototypeAsync(string prompt, string? preferredModel = null, AIModelConfig? config = null);

    /// <summary>
    /// 生成代码
    /// </summary>
    /// <param name="specification">需求规格书</param>
    /// <param name="codeType">代码类型</param>
    /// <param name="config">模型配置</param>
    /// <returns>生成的代码</returns>
    Task<CodeGenerationResult> GenerateCodeAsync(string specification, CodeType codeType, AIModelConfig? config = null);

    /// <summary>
    /// 获取文本向量
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <param name="config">模型配置</param>
    /// <returns>向量数组</returns>
    Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig? config = null);

    /// <summary>
    /// 生成测试用例
    /// </summary>
    /// <param name="specification">需求规格书</param>
    /// <param name="codeContent">代码内容</param>
    /// <param name="config">模型配置</param>
    /// <returns>测试用例</returns>
    Task<string> GenerateTestCasesAsync(string specification, string codeContent, AIModelConfig? config = null);

    /// <summary>
    /// 代码质量分析
    /// </summary>
    /// <param name="codeContent">代码内容</param>
    /// <param name="config">模型配置</param>
    /// <returns>代码质量分析结果</returns>
    Task<CodeQualityAnalysisResult> AnalyzeCodeQualityAsync(string codeContent, AIModelConfig? config = null);

    /// <summary>
    /// 性能优化建议
    /// </summary>
    /// <param name="performanceData">性能数据</param>
    /// <param name="config">模型配置</param>
    /// <returns>优化建议</returns>
    Task<string> GenerateOptimizationSuggestionsAsync(string performanceData, AIModelConfig? config = null);

    /// <summary>
    /// AI分解开发步骤
    /// </summary>
    /// <param name="step">要分解的步骤</param>
    /// <param name="options">分解选项</param>
    /// <param name="userId">用户ID，用于获取用户AI配置</param>
    /// <param name="config">模型配置</param>
    /// <returns>分解结果</returns>
    Task<StepDecompositionResult> DecomposeStepAsync(DevelopmentStep step, StepDecompositionOptions options, int userId = 0, AIModelConfig? config = null);
}

/// <summary>
/// AI提供商接口
/// </summary>
public interface IAIProvider
{
    /// <summary>
    /// 提供商名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 是否可用
    /// </summary>
    bool IsAvailable { get; }

    /// <summary>
    /// 支持的模型列表
    /// </summary>
    List<string> SupportedModels { get; }

    /// <summary>
    /// 生成文本
    /// </summary>
    /// <param name="prompt">提示词</param>
    /// <param name="config">配置</param>
    /// <returns>生成的文本</returns>
    Task<string> GenerateAsync(string prompt, AIModelConfig config);

    /// <summary>
    /// 获取向量
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="config">配置</param>
    /// <returns>向量数组</returns>
    Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config);

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>是否健康</returns>
    Task<bool> HealthCheckAsync();
}
