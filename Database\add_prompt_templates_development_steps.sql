-- =============================================
-- 添加需求分解相关的Prompt模板
-- 用于AI需求分解功能
-- =============================================

USE ProjectManagementAI;
GO

-- 检查是否需要添加新的任务类型到约束中
-- 首先删除现有约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE PromptTemplates DROP CONSTRAINT CK_PromptTemplates_TaskType;
    PRINT '已删除旧的TaskType约束';
END

-- 添加新的约束，包含需求分解任务类型
ALTER TABLE PromptTemplates ADD CONSTRAINT CK_PromptTemplates_TaskType
CHECK (TaskType IN (
    'RequirementAnalysis',      -- 需求分析
    'CodeGeneration',          -- 代码生成
    'Testing',                 -- 测试生成
    'Debugging',               -- 调试辅助
    'Documentation',           -- 文档生成
    'Review',                  -- 代码审查
    'ERDiagramGeneration',     -- ER图生成
    'ContextDiagramGeneration', -- 上下文图生成
    'RequirementDecomposition', -- 需求分解
    'DependencyAnalysis',      -- 依赖分析
    'ComplexityAnalysis',      -- 复杂度分析
    'SQLCommentGeneration'     -- SQL注释生成
));
PRINT '已添加新的TaskType约束，包含需求分解类型';

-- 同时更新UserTaskMappings表的约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UserTaskMappings_TaskType')
BEGIN
    ALTER TABLE UserTaskMappings DROP CONSTRAINT CK_UserTaskMappings_TaskType;
    PRINT '已删除旧的UserTaskMappings TaskType约束';
END

ALTER TABLE UserTaskMappings ADD CONSTRAINT CK_UserTaskMappings_TaskType
CHECK (TaskType IN (
    'RequirementAnalysis',
    'CodeGeneration',
    'DocumentGeneration',
    'Embeddings',
    'Testing',
    'Debugging',
    'ERDiagramGeneration',
    'ContextDiagramGeneration',
    'RequirementDecomposition',
    'DependencyAnalysis'
));
PRINT '已添加新的UserTaskMappings TaskType约束，包含需求分解类型';
GO

-- 添加开发步骤分类（如果不存在）
IF NOT EXISTS (SELECT * FROM PromptCategories WHERE Name = '开发步骤')
BEGIN
    INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, CreatedTime)
    VALUES ('开发步骤', '用于需求分解和开发步骤生成的提示词模板', 'steps', '#722ed1', 8, GETDATE());
    PRINT '已添加开发步骤分类';
END
ELSE
BEGIN
    PRINT '开发步骤分类已存在';
END
GO

-- 获取分类ID
DECLARE @DevelopmentStepsCategoryId INT = (SELECT Id FROM PromptCategories WHERE Name = '开发步骤');

-- 添加需求分解提示词模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '需求分解模板' AND TaskType = 'RequirementDecomposition')
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, UsageCount, CreatedTime
    )
    VALUES (
        '需求分解模板',
        '将需求文档分解为具体的开发步骤，包括前端、后端、数据库等各个层面的任务',
        @DevelopmentStepsCategoryId,
        N'你是一个专业的软件架构师和项目经理，请将以下需求分解为具体的开发步骤。

需求内容：
{requirementContent}

技术栈：{technologyStack}
分解粒度：{granularity}
最大步骤数：{maxStepCount}

请按照以下JSON格式返回分解结果：

```json
{
  "analysis": "对需求的整体分析和理解",
  "steps": [
    {
      "stepName": "步骤名称",
      "stepDescription": "详细描述",
      "stepType": "Development|Testing|Documentation|Deployment|Review",
      "priority": "High|Medium|Low",
      "estimatedHours": 8,
      "technologyStack": "具体技术栈",
      "componentType": "Frontend|Backend|Database|API|Model|Service",
      "stepOrder": 1,
      "stepLevel": 1,
      "aiPrompt": "用于AI代码生成的提示词"
    }
  ],
  "dependencies": [
    {
      "stepName": "依赖步骤名称",
      "dependsOnStepName": "被依赖步骤名称",
      "dependencyType": "Sequential|Parallel|Conditional",
      "isRequired": true,
      "description": "依赖关系说明"
    }
  ]
}
```

分解原则：
1. 按照软件开发生命周期进行分解
2. 考虑前后端分离的架构
3. 包含数据库设计和API设计
4. 合理安排开发顺序和依赖关系
5. 每个步骤都要有明确的交付物
6. 估算合理的工作量

请确保返回的是纯JSON格式，不要包含其他文本。',
        N'[{"name":"requirementContent","type":"string","description":"需求内容"},{"name":"technologyStack","type":"string","description":"技术栈"},{"name":"granularity","type":"string","description":"分解粒度"},{"name":"maxStepCount","type":"number","description":"最大步骤数"}]',
        'System',
        'RequirementDecomposition',
        'General',
        'General',
        'Medium',
        2000,
        N'需求分解,开发步骤,项目管理',
        1,
        0,
        GETDATE()
    );
    PRINT '已添加需求分解模板';
END
ELSE
BEGIN
    PRINT '需求分解模板已存在';
END

-- 添加依赖分析提示词模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '依赖关系分析模板' AND TaskType = 'DependencyAnalysis')
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, UsageCount, CreatedTime
    )
    VALUES (
        '依赖关系分析模板',
        '分析开发步骤之间的依赖关系，确保合理的开发顺序',
        @DevelopmentStepsCategoryId,
        N'你是一个专业的项目管理专家，请分析以下开发步骤之间的依赖关系。

开发步骤列表：
{stepsList}

请按照以下JSON格式返回依赖关系分析结果：

```json
{
  "analysis": "对步骤依赖关系的整体分析",
  "dependencies": [
    {
      "stepName": "步骤名称",
      "dependsOnStepName": "被依赖的步骤名称",
      "dependencyType": "Sequential|Parallel|Conditional",
      "isRequired": true,
      "description": "依赖关系的具体说明"
    }
  ],
  "criticalPath": [
    "关键路径上的步骤名称列表"
  ],
  "parallelGroups": [
    {
      "groupName": "并行组名称",
      "steps": ["可以并行执行的步骤名称列表"]
    }
  ]
}
```

分析原则：
1. 识别必须的前置依赖关系
2. 找出可以并行执行的步骤
3. 确定项目的关键路径
4. 避免循环依赖
5. 优化整体开发效率

请确保返回的是纯JSON格式，不要包含其他文本。',
        N'[{"name":"stepsList","type":"string","description":"开发步骤列表JSON"}]',
        'System',
        'DependencyAnalysis',
        'General',
        'General',
        'Medium',
        1500,
        N'依赖分析,项目管理,关键路径',
        1,
        0,
        GETDATE()
    );
    PRINT '已添加依赖关系分析模板';
END
ELSE
BEGIN
    PRINT '依赖关系分析模板已存在';
END

-- 添加复杂度分析提示词模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '步骤复杂度分析模板' AND TaskType = 'ComplexityAnalysis')
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, UsageCount, CreatedTime
    )
    VALUES (
        '步骤复杂度分析模板',
        '分析开发步骤的复杂度，评估风险和工作量',
        @DevelopmentStepsCategoryId,
        N'你是一个经验丰富的技术专家，请分析以下开发步骤的复杂度。

步骤信息：
步骤名称：{stepName}
步骤描述：{stepDescription}
技术栈：{technologyStack}
组件类型：{componentType}

请按照以下JSON格式返回复杂度分析结果：

```json
{
  "complexityScore": 5,
  "riskLevel": "Low|Medium|High",
  "estimatedHours": 8,
  "factors": [
    {
      "factor": "技术难度",
      "score": 3,
      "description": "具体说明"
    },
    {
      "factor": "业务复杂度",
      "score": 4,
      "description": "具体说明"
    }
  ],
  "risks": [
    {
      "risk": "风险描述",
      "probability": "High|Medium|Low",
      "impact": "High|Medium|Low",
      "mitigation": "缓解措施"
    }
  ],
  "recommendations": [
    "建议1",
    "建议2"
  ]
}
```

评估维度：
1. 技术难度（1-10分）
2. 业务复杂度（1-10分）
3. 集成复杂度（1-10分）
4. 测试难度（1-10分）
5. 维护难度（1-10分）

请确保返回的是纯JSON格式，不要包含其他文本。',
        N'[{"name":"stepName","type":"string","description":"步骤名称"},{"name":"stepDescription","type":"string","description":"步骤描述"},{"name":"technologyStack","type":"string","description":"技术栈"},{"name":"componentType","type":"string","description":"组件类型"}]',
        'System',
        'ComplexityAnalysis',
        'General',
        'General',
        'Hard',
        1200,
        N'复杂度分析,风险评估,工作量估算',
        1,
        0,
        GETDATE()
    );
    PRINT '已添加步骤复杂度分析模板';
END
ELSE
BEGIN
    PRINT '步骤复杂度分析模板已存在';
END

PRINT '==============================================';
PRINT '需求分解相关Prompt模板添加完成！';
PRINT '==============================================';
PRINT '';
PRINT '已添加以下模板：';
PRINT '1. 需求分解模板 - 将需求分解为开发步骤';
PRINT '2. 依赖关系分析模板 - 分析步骤间依赖关系';
PRINT '3. 步骤复杂度分析模板 - 评估步骤复杂度和风险';
PRINT '';
PRINT '已更新TaskType约束，支持：';
PRINT '- RequirementDecomposition (需求分解)';
PRINT '- DependencyAnalysis (依赖分析)';
PRINT '- ComplexityAnalysis (复杂度分析)';
GO