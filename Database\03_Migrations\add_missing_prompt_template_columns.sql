-- =============================================
-- 添加PromptTemplates表缺失的字段
-- 创建时间: 2025-06-20
-- 说明: 添加SupportedProviders、TemplateVersion、IsDefault字段
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '开始添加PromptTemplates表缺失的字段...';

-- 检查并添加SupportedProviders字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'SupportedProviders')
BEGIN
    ALTER TABLE PromptTemplates
    ADD SupportedProviders nvarchar(200) NULL;

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '适用的AI提供商，多个用逗号分隔', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'SupportedProviders';

    PRINT '已添加SupportedProviders字段';
END
ELSE
BEGIN
    PRINT 'SupportedProviders字段已存在，跳过添加';
END

-- 检查并添加TemplateVersion字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'TemplateVersion')
BEGIN
    ALTER TABLE PromptTemplates
    ADD TemplateVersion nvarchar(20) NOT NULL DEFAULT '1.0';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '模板版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'TemplateVersion';

    PRINT '已添加TemplateVersion字段';
END
ELSE
BEGIN
    PRINT 'TemplateVersion字段已存在，跳过添加';
END

-- 检查并添加IsDefault字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'IsDefault')
BEGIN
    ALTER TABLE PromptTemplates
    ADD IsDefault bit NOT NULL DEFAULT 0;

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '是否为默认模板', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'IsDefault';

    PRINT '已添加IsDefault字段';
END
ELSE
BEGIN
    PRINT 'IsDefault字段已存在，跳过添加';
END

-- 为新字段创建索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'IX_PromptTemplates_SupportedProviders')
BEGIN
    CREATE INDEX IX_PromptTemplates_SupportedProviders ON PromptTemplates(SupportedProviders);
    PRINT '已创建SupportedProviders索引';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'IX_PromptTemplates_TemplateVersion')
BEGIN
    CREATE INDEX IX_PromptTemplates_TemplateVersion ON PromptTemplates(TemplateVersion);
    PRINT '已创建TemplateVersion索引';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'IX_PromptTemplates_IsDefault')
BEGIN
    CREATE INDEX IX_PromptTemplates_IsDefault ON PromptTemplates(IsDefault);
    PRINT '已创建IsDefault索引';
END

-- 更新现有记录的默认值（在字段添加完成后）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'SupportedProviders')
BEGIN
    UPDATE PromptTemplates
    SET SupportedProviders = 'Azure,OpenAI,DeepSeek'
    WHERE SupportedProviders IS NULL;
    PRINT '已更新SupportedProviders默认值';
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'TemplateVersion')
BEGIN
    UPDATE PromptTemplates
    SET TemplateVersion = '1.0'
    WHERE TemplateVersion IS NULL OR TemplateVersion = '';
    PRINT '已更新TemplateVersion默认值';
END

-- 将系统模板标记为默认模板
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PromptTemplates') AND name = 'IsDefault')
BEGIN
    UPDATE PromptTemplates
    SET IsDefault = 1
    WHERE TemplateType = 'System' AND IsDefault = 0;
    PRINT '已更新IsDefault默认值';
END

PRINT '已更新现有记录的默认值';

PRINT '==============================================';
PRINT 'PromptTemplates表字段添加完成！';
PRINT '==============================================';
PRINT '已添加字段：';
PRINT '- SupportedProviders: 适用的AI提供商';
PRINT '- TemplateVersion: 模板版本号';
PRINT '- IsDefault: 是否为默认模板';
PRINT '';
PRINT '已创建相应的索引和字段注释';
PRINT '已更新现有记录的默认值';
