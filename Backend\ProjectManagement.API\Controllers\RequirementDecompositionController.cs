using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Services;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;
using SqlSugar;
using DevelopmentStepData = ProjectManagement.Core.Services.DevelopmentStepData;
using StepDependencyData = ProjectManagement.Core.Services.StepDependencyData;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 需求分解控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RequirementDecompositionController : ControllerBase
{
    private readonly IRequirementDecompositionService _decompositionService;
    private readonly IDevelopmentStepRepository _stepRepository;
    private readonly ILogger<RequirementDecompositionController> _logger;
    private readonly ISqlSugarClient _db;

    public RequirementDecompositionController(
        IRequirementDecompositionService decompositionService,
        IDevelopmentStepRepository stepRepository,
        ILogger<RequirementDecompositionController> logger,
        ISqlSugarClient db)
    {
        _decompositionService = decompositionService;
        _stepRepository = stepRepository;
        _logger = logger;
        _db = db;
    }

    /// <summary>
    /// 分解需求文档为开发步骤
    /// </summary>
    /// <param name="requirementDocumentId">需求文档ID</param>
    /// <param name="request">分解请求</param>
    /// <returns>分解结果</returns>
    [HttpPost("decompose/{requirementDocumentId:int}")]
    public async Task<IActionResult> DecomposeRequirement(int requirementDocumentId, [FromBody] DecomposeRequirementRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var decompositionOptions = new DecompositionOptions
            {
                TechnologyStack = request.TechnologyStack ?? "通用",
                Granularity = request.Granularity ?? "Medium",
                IncludeTestSteps = request.IncludeTestSteps,
                IncludeDocumentationSteps = request.IncludeDocumentationSteps,
                AutoAnalyzeDependencies = request.AutoAnalyzeDependencies,
                MaxStepCount = request.MaxStepCount,
                AIProvider = request.AIProvider?.ToString(),
                AIModel = request.AIModel
            };

            var result = await _decompositionService.DecomposeRequirementAsync(
                requirementDocumentId,
                decompositionOptions,
                userId);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = "需求分解成功",
                    data = new
                    {
                        steps = result.Steps.Select(s => new
                        {
                            id = s.Id,
                            stepName = s.StepName,
                            stepDescription = s.StepDescription,
                            stepType = s.StepType,
                            priority = s.Priority,
                            estimatedHours = s.EstimatedHours,
                            technologyStack = s.TechnologyStack,
                            componentType = s.ComponentType,
                            stepOrder = s.StepOrder,
                            stepLevel = s.StepLevel,
                            status = s.Status,
                            progress = s.Progress
                        }),
                        dependencies = result.Dependencies.Select(d => new
                        {
                            id = d.Id,
                            stepId = d.StepId,
                            dependsOnStepId = d.DependsOnStepId,
                            dependencyType = d.DependencyType,
                            isRequired = d.IsRequired,
                            description = d.Description
                        }),
                        statistics = result.Statistics,
                        aiAnalysis = result.AIAnalysisResult
                    },
                    isPreview = result.IsPreview
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage ?? "需求分解失败"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分解需求文档失败，RequirementDocumentId: {RequirementDocumentId}", requirementDocumentId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    /// <summary>
    /// 基于项目信息分解
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="request">分解请求</param>
    /// <returns>分解结果</returns>
    [HttpPost("decompose-project/{projectId:int}")]
    public async Task<IActionResult> DecomposeProject(int projectId, [FromBody] DecomposeRequirementRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var decompositionOptions = new DecompositionOptions
            {
                TechnologyStack = request.TechnologyStack ?? "通用",
                Granularity = request.Granularity ?? "Medium",
                IncludeTestSteps = request.IncludeTestSteps,
                IncludeDocumentationSteps = request.IncludeDocumentationSteps,
                AutoAnalyzeDependencies = request.AutoAnalyzeDependencies,
                MaxStepCount = request.MaxStepCount,
                AIProvider = request.AIProvider?.ToString(),
                AIModel = request.AIModel
            };

            // 暂时使用 DecomposeRequirementContentAsync，传入项目名称作为需求内容
            // 后续可以创建专门的 DecomposeProjectAsync 方法
            var project = await _db.Queryable<Core.Entities.Project>().FirstAsync(p => p.Id == projectId);
            if (project == null)
            {
                return BadRequest(new { success = false, message = "项目不存在" });
            }

            var projectContent = $"项目名称：{project.Name}\n项目描述：{project.Description ?? "无描述"}";

            var result = await _decompositionService.DecomposeRequirementContentAsync(
                projectId,
                projectContent,
                decompositionOptions,
                userId);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = "项目分解成功",
                    data = new
                    {
                        steps = result.Steps.Select(s => new
                        {
                            id = s.Id,
                            stepName = s.StepName,
                            stepDescription = s.StepDescription,
                            stepType = s.StepType,
                            priority = s.Priority,
                            estimatedHours = s.EstimatedHours,
                            technologyStack = s.TechnologyStack,
                            componentType = s.ComponentType,
                            stepOrder = s.StepOrder,
                            stepLevel = s.StepLevel,
                            status = s.Status,
                            progress = s.Progress
                        }),
                        dependencies = result.Dependencies.Select(d => new
                        {
                            id = d.Id,
                            stepId = d.StepId,
                            dependsOnStepId = d.DependsOnStepId,
                            dependencyType = d.DependencyType,
                            isRequired = d.IsRequired,
                            description = d.Description
                        }),
                        statistics = result.Statistics,
                        aiAnalysis = result.AIAnalysisResult
                    },
                    isPreview = result.IsPreview
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage ?? "项目分解失败"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分解项目失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    /// <summary>
    /// 基于需求内容直接分解
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="request">分解请求</param>
    /// <returns>分解结果</returns>
    [HttpPost("decompose-content/{projectId:int}")]
    public async Task<IActionResult> DecomposeRequirementContent(int projectId, [FromBody] DecomposeContentRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var decompositionOptions = new DecompositionOptions
            {
                TechnologyStack = request.TechnologyStack ?? "通用",
                Granularity = request.Granularity ?? "Medium",
                IncludeTestSteps = request.IncludeTestSteps,
                IncludeDocumentationSteps = request.IncludeDocumentationSteps,
                AutoAnalyzeDependencies = request.AutoAnalyzeDependencies,
                MaxStepCount = request.MaxStepCount,
                AIProvider = request.AIProvider?.ToString(),
                AIModel = request.AIModel
            };

            var result = await _decompositionService.DecomposeRequirementContentAsync(
                projectId,
                request.RequirementContent,
                decompositionOptions,
                userId);

            if (result.Success)
            {
                return Ok(new
                {
                    success = true,
                    message = "需求分解成功",
                    data = new
                    {
                        steps = result.Steps.Select(s => new
                        {
                            id = s.Id,
                            stepName = s.StepName,
                            stepDescription = s.StepDescription,
                            stepType = s.StepType,
                            priority = s.Priority,
                            estimatedHours = s.EstimatedHours,
                            technologyStack = s.TechnologyStack,
                            componentType = s.ComponentType,
                            stepOrder = s.StepOrder,
                            stepLevel = s.StepLevel,
                            status = s.Status,
                            progress = s.Progress
                        }),
                        dependencies = result.Dependencies.Select(d => new
                        {
                            id = d.Id,
                            stepId = d.StepId,
                            dependsOnStepId = d.DependsOnStepId,
                            dependencyType = d.DependencyType,
                            isRequired = d.IsRequired,
                            description = d.Description
                        }),
                        statistics = result.Statistics,
                        aiAnalysis = result.AIAnalysisResult
                    },
                    isPreview = result.IsPreview
                });
            }
            else
            {
                return BadRequest(new
                {
                    success = false,
                    message = result.ErrorMessage ?? "需求分解失败"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分解需求内容失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    /// <summary>
    /// 获取项目的开发步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <param name="stepType">步骤类型筛选</param>
    /// <param name="search">搜索关键词</param>
    /// <param name="parentOnly">是否只获取父级步骤（ParentStepId为null的步骤）</param>
    /// <returns>开发步骤列表</returns>
    [HttpGet("steps/{projectId:int}")]
    public async Task<IActionResult> GetProjectSteps(
        int projectId,
        [FromQuery] int pageIndex = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? status = null,
        [FromQuery] string? priority = null,
        [FromQuery] string? stepType = null,
        [FromQuery] string? search = null,
        [FromQuery] bool parentOnly = false)
    {
        try
        {
            PagedResult<Core.Entities.DevelopmentStep> result;

            if (parentOnly)
            {
                // 只获取父级步骤
                var topLevelSteps = await _stepRepository.GetTopLevelStepsAsync(projectId);

                // 应用筛选条件
                var filteredSteps = topLevelSteps.AsQueryable();

                if (!string.IsNullOrEmpty(status))
                    filteredSteps = filteredSteps.Where(x => x.Status == status);

                if (!string.IsNullOrEmpty(priority))
                    filteredSteps = filteredSteps.Where(x => x.Priority == priority);

                if (!string.IsNullOrEmpty(stepType))
                    filteredSteps = filteredSteps.Where(x => x.StepType == stepType);

                if (!string.IsNullOrEmpty(search))
                    filteredSteps = filteredSteps.Where(x =>
                        x.StepName.Contains(search) ||
                        (x.StepDescription != null && x.StepDescription.Contains(search)));

                var filteredList = filteredSteps
                    .OrderBy(x => x.StepOrder)
                    .ThenBy(x => x.CreatedTime)
                    .ToList();
                var totalCount = filteredList.Count;

                // 应用分页
                var pagedItems = filteredList
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                result = new PagedResult<Core.Entities.DevelopmentStep>
                {
                    Items = pagedItems,
                    TotalCount = totalCount,
                    PageIndex = pageIndex,
                    PageSize = pageSize
                };
            }
            else
            {
                // 获取所有步骤 - 需要扩展Repository方法支持search参数
                // 暂时使用现有方法，后续可以扩展
                result = await _stepRepository.GetByProjectIdPagedAsync(
                    projectId, pageIndex, pageSize, status, priority, stepType);

                // 如果有搜索条件，在内存中进行过滤（临时方案）
                if (!string.IsNullOrEmpty(search))
                {
                    var filteredItems = result.Items.Where(x =>
                        x.StepName.Contains(search) ||
                        (x.StepDescription != null && x.StepDescription.Contains(search))).ToList();

                    result = new PagedResult<Core.Entities.DevelopmentStep>
                    {
                        Items = filteredItems,
                        TotalCount = filteredItems.Count,
                        PageIndex = pageIndex,
                        PageSize = pageSize
                    };
                }
            }

            // 获取所有步骤的模板序列信息
            var stepIds = result.Items.Select(s => s.Id).ToList();
            var templateSequences = await GetStepTemplateSequencesBatch(stepIds);

            return Ok(new
            {
                success = true,
                data = new
                {
                    items = result.Items.Select(s => new
                    {
                        id = s.Id,
                        stepName = s.StepName,
                        stepDescription = s.StepDescription,
                        stepType = s.StepType,
                        priority = s.Priority,
                        status = s.Status,
                        progress = s.Progress,
                        estimatedHours = s.EstimatedHours,
                        actualHours = s.ActualHours,
                        technologyStack = s.TechnologyStack,
                        componentType = s.ComponentType,
                        filePath = s.FilePath,
                        aiPrompt = s.AIPrompt,
                        stepOrder = s.StepOrder,
                        stepLevel = s.StepLevel,
                        parentStepId = s.ParentStepId,
                        referenceImages = s.ReferenceImages, // 添加参考图片字段
                        startTime = s.StartTime,
                        endTime = s.EndTime,
                        completedTime = s.CompletedTime,
                        createdTime = s.CreatedTime,
                        templateSequences = templateSequences.ContainsKey(s.Id) ? templateSequences[s.Id] : new List<object>()
                    }),
                    totalCount = result.TotalCount,
                    pageIndex = result.PageIndex,
                    pageSize = result.PageSize,
                    totalPages = (int)Math.Ceiling((double)result.TotalCount / result.PageSize)
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目开发步骤失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    /// <summary>
    /// 获取项目步骤树结构
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>步骤树结构</returns>
    [HttpGet("steps/{projectId:int}/tree")]
    public async Task<IActionResult> GetProjectStepTree(int projectId)
    {
        try
        {
            _logger.LogInformation("开始获取项目步骤树，ProjectId: {ProjectId}", projectId);

            var stepTree = await _stepRepository.GetProjectStepTreeAsync(projectId);

            _logger.LogInformation("获取到 {Count} 个顶级步骤", stepTree.Count);

            var result = stepTree.Select(s => MapStepToTreeNode(s)).ToList();

            _logger.LogInformation("映射完成，返回 {Count} 个步骤节点", result.Count);

            return Ok(new
            {
                success = true,
                data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目步骤树失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    /// <summary>
    /// 获取可执行的步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="stepType">步骤类型筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <returns>可执行步骤列表</returns>
    [HttpGet("steps/{projectId:int}/executable")]
    public async Task<IActionResult> GetExecutableSteps(
        int projectId,
        [FromQuery] string? stepType = null,
        [FromQuery] string? priority = null)
    {
        try
        {
            var executableSteps = await _decompositionService.GetNextExecutableStepsAsync(projectId, stepType, priority);

            return Ok(new
            {
                success = true,
                data = executableSteps.Select(s => new
                {
                    id = s.Id,
                    stepName = s.StepName,
                    stepDescription = s.StepDescription,
                    stepType = s.StepType,
                    priority = s.Priority,
                    estimatedHours = s.EstimatedHours,
                    technologyStack = s.TechnologyStack,
                    componentType = s.ComponentType,
                    stepOrder = s.StepOrder,
                    aiPrompt = s.AIPrompt,
                    referenceImages = s.ReferenceImages // 添加参考图片字段
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可执行步骤失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new
            {
                success = false,
                message = "服务器内部错误"
            });
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 1; // 默认用户ID为1
    }

    /// <summary>
    /// 将步骤映射为树节点
    /// </summary>
    private object MapStepToTreeNode(Core.Entities.DevelopmentStep step)
    {
        return new
        {
            id = step.Id,
            stepName = step.StepName,
            stepDescription = step.StepDescription,
            stepType = step.StepType,
            priority = step.Priority,
            status = step.Status,
            progress = step.Progress,
            estimatedHours = step.EstimatedHours,
            actualHours = step.ActualHours,
            technologyStack = step.TechnologyStack,
            componentType = step.ComponentType,
            filePath = step.FilePath,
            aiPrompt = step.AIPrompt,
            stepOrder = step.StepOrder,
            stepLevel = step.StepLevel,
            parentStepId = step.ParentStepId,
            referenceImages = step.ReferenceImages, // 添加参考图片字段
            children = step.ChildSteps?.Select(child => MapStepToTreeNode(child)) ?? new List<object>()
        };
    }

    /// <summary>
    /// 批量获取步骤的模板序列信息
    /// </summary>
    private async Task<Dictionary<int, List<object>>> GetStepTemplateSequencesBatch(List<int> stepIds)
    {
        var result = new Dictionary<int, List<object>>();

        if (!stepIds.Any())
            return result;

        try
        {
            // 使用SqlSugar查询步骤模板序列关联信息
            var associations = await _db.Queryable<Core.Entities.StepTemplateSequenceAssociation>()
                .LeftJoin<Core.Entities.UIAutoMationTemplateSequence>((a, s) => a.SequenceId == s.Id)
                .Where((a, s) => stepIds.Contains(a.StepId) && a.IsActive && !s.IsDeleted)
                .Select((a, s) => new
                {
                    StepId = a.StepId,
                    SequenceId = s.Id,
                    Name = s.Name,
                    Description = s.Description,
                    Category = s.Category,
                    AppliedTime = a.AppliedTime
                })
                .ToListAsync();

            // 按步骤ID分组
            var groupedAssociations = associations.GroupBy(a => a.StepId);

            foreach (var group in groupedAssociations)
            {
                result[group.Key] = group.Select(a => new
                {
                    id = a.SequenceId,
                    name = a.Name,
                    description = a.Description,
                    category = a.Category,
                    appliedTime = a.AppliedTime
                }).Cast<object>().ToList();
            }

            // 为没有模板序列的步骤添加空列表
            foreach (var stepId in stepIds)
            {
                if (!result.ContainsKey(stepId))
                {
                    result[stepId] = new List<object>();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量获取步骤模板序列失败");
            // 如果出错，为所有步骤返回空列表
            foreach (var stepId in stepIds)
            {
                result[stepId] = new List<object>();
            }
        }

        return result;
    }

    /// <summary>
    /// 确认保存分解的步骤到数据库
    /// </summary>
    [HttpPost("confirm-steps")]
    public async Task<IActionResult> ConfirmSteps([FromBody] ConfirmStepsRequest request)
    {
        try
        {
            _logger.LogInformation("开始确认保存分解步骤，ProjectId: {ProjectId}, StepCount: {StepCount}",
                request.ProjectId, request.Steps?.Count ?? 0);

            // 获取当前用户ID
            var userId = GetCurrentUserId();

            // 调用服务保存确认的步骤
            var result = await _decompositionService.ConfirmAndSaveStepsAsync(
                request.ProjectId,
                request.RequirementDocumentId,
                request.Steps,
                request.Dependencies,
                userId);

            if (result.Success)
            {
                _logger.LogInformation("步骤确认保存成功，生成 {StepCount} 个步骤", result.Steps.Count);
                return Ok(new
                {
                    success = true,
                    message = "步骤保存成功",
                    data = new
                    {
                        steps = result.Steps,
                        dependencies = result.Dependencies,
                        statistics = result.Statistics
                    }
                });
            }
            else
            {
                return BadRequest(new { message = result.ErrorMessage });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认保存步骤失败");
            return StatusCode(500, new { message = "确认保存步骤失败" });
        }
    }

    #endregion
}

#region 请求模型

/// <summary>
/// 分解需求请求模型
/// </summary>
public class DecomposeRequirementRequest
{
    /// <summary>
    /// 技术栈
    /// </summary>
    public string? TechnologyStack { get; set; }

    /// <summary>
    /// 分解粒度
    /// </summary>
    public string? Granularity { get; set; } = "Medium";

    /// <summary>
    /// 是否包含测试步骤
    /// </summary>
    public bool IncludeTestSteps { get; set; } = true;

    /// <summary>
    /// 是否包含文档步骤
    /// </summary>
    public bool IncludeDocumentationSteps { get; set; } = true;

    /// <summary>
    /// 是否自动分析依赖关系
    /// </summary>
    public bool AutoAnalyzeDependencies { get; set; } = true;

    /// <summary>
    /// 最大步骤数量
    /// </summary>
    public int MaxStepCount { get; set; } = 100;

    /// <summary>
    /// AI提供商（支持数字ID或字符串名称）
    /// </summary>
    public object? AIProvider { get; set; }

    /// <summary>
    /// AI模型
    /// </summary>
    public string? AIModel { get; set; }
}

/// <summary>
/// 分解内容请求模型
/// </summary>
public class DecomposeContentRequest : DecomposeRequirementRequest
{
    /// <summary>
    /// 需求内容
    /// </summary>
    public string RequirementContent { get; set; } = string.Empty;
}

/// <summary>
/// 确认步骤请求模型
/// </summary>
public class ConfirmStepsRequest
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID（可选）
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 确认的步骤列表
    /// </summary>
    public List<DevelopmentStepData> Steps { get; set; } = new();

    /// <summary>
    /// 确认的依赖关系列表
    /// </summary>
    public List<StepDependencyData> Dependencies { get; set; } = new();
}

// 使用 Core 项目中的数据模型，避免重复定义

#endregion
