{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "SqlSugar": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft": "Error", "System": "Error", "SqlSugar": "Warning"}}}, "CORS": {"AllowedOrigins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001", "http://localhost:8080", "https://localhost:8080", "http://localhost:5173", "https://localhost:5173", "http://localhost:62573", "https://localhost:62573"]}, "Features": {"EnableSwagger": true, "EnableHealthChecks": true, "EnableMetrics": false, "EnableAIServices": true, "EnableRealTimeUpdates": true, "EnableHttpsRedirection": false}, "RateLimiting": {"EnableRateLimiting": true, "GeneralRules": {"PermitLimit": 200, "Window": "00:01:00", "QueueLimit": 20}, "AIApiRules": {"PermitLimit": 20, "Window": "00:01:00", "QueueLimit": 10}}}