using System.Collections.Generic;
using System.Threading.Tasks;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Models;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 编译错误服务接口
    /// </summary>
    public interface ICompilationErrorService
    {
        /// <summary>
        /// 获取编译错误列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>编译错误列表</returns>
        Task<PagedResult<CompilationErrorDto>> GetCompilationErrorsAsync(CompilationErrorQueryDto query);

        /// <summary>
        /// 根据ID获取编译错误
        /// </summary>
        /// <param name="id">错误ID</param>
        /// <returns>编译错误详情</returns>
        Task<CompilationErrorDto?> GetCompilationErrorByIdAsync(int id);

        /// <summary>
        /// 获取编译错误统计信息
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="projectId">项目ID</param>
        /// <returns>统计信息</returns>
        Task<CompilationErrorStatsDto> GetCompilationErrorStatsAsync(string? projectType = null, int? projectId = null);

        /// <summary>
        /// 清空编译错误
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="projectId">项目ID</param>
        /// <param name="sessionId">会话ID</param>
        /// <returns>清空的错误数量</returns>
        Task<int> ClearCompilationErrorsAsync(string? projectType = null, int? projectId = null, string? sessionId = null);

        /// <summary>
        /// 批量添加编译错误
        /// </summary>
        /// <param name="errors">错误列表</param>
        /// <returns>添加的错误数量</returns>
        Task<int> AddCompilationErrorsAsync(IEnumerable<CompilationErrorDto> errors);

        /// <summary>
        /// 删除编译错误
        /// </summary>
        /// <param name="id">错误ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteCompilationErrorAsync(int id);

        /// <summary>
        /// 获取最新的编译会话ID列表
        /// </summary>
        /// <param name="projectType">项目类型</param>
        /// <param name="limit">返回数量限制</param>
        /// <returns>会话ID列表</returns>
        Task<List<string>> GetRecentCompilationSessionsAsync(string? projectType = null, int limit = 10);
    }
}
