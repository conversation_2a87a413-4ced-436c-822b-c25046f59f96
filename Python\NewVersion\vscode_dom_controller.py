#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VSCode DOM控制器
专门用于VSCode DOM操作的核心模块
"""

import time
import json
from typing import Dict, Optional, Any
from pathlib import Path

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import websocket
    import requests
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False


class VSCodeDOMController:
    """VSCode DOM控制器 - 专门用于VSCode DOM操作"""

    def __init__(self):
        """初始化DOM控制器"""
        self.driver = None
        self.websocket = None
        self.is_connected = False
        self.connection_type = None  # 'selenium' 或 'websocket'
        
        # JavaScript脚本缓存
        self.js_cache = {}
        self.scripts_dir = Path(__file__).parent / "scripts"
        
        # VSCode特定的选择器
        self.selectors = {
            'copilot_input': '.monaco-scrollable-element .view-lines',
            'copilot_textarea': 'textarea[aria-label*="Chat"]',
            'chat_messages': '.monaco-list-row',
            'send_button': '[aria-label*="Send"], [title*="Send"]',
            'clear_button': '[aria-label*="Clear"], [title*="Clear"]',
            'monaco_editor': '.monaco-editor',
            'sidebar': '.sidebar',
            'activity_bar': '.activitybar'
        }

    def connect_selenium(self, debug_port: int = 9222) -> bool:
        """
        通过Selenium连接VSCode
        
        Args:
            debug_port: Chrome调试端口
            
        Returns:
            是否连接成功
        """
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium不可用")
            return False
        
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.is_connected = True
            self.connection_type = 'selenium'
            
            print(f"✅ Selenium连接成功 - 页面: {self.driver.title}")
            return True
            
        except Exception as e:
            print(f"❌ Selenium连接失败: {e}")
            return False

    def connect_websocket(self, debug_port: int = 9222) -> bool:
        """
        通过WebSocket连接VSCode
        
        Args:
            debug_port: Chrome调试端口
            
        Returns:
            是否连接成功
        """
        if not WEBSOCKET_AVAILABLE:
            print("❌ WebSocket不可用")
            return False
        
        try:
            # 获取可用的调试标签页
            response = requests.get(f"http://localhost:{debug_port}/json")
            tabs = response.json()
            
            # 查找VSCode相关的标签页
            vscode_tab = None
            for tab in tabs:
                if 'vscode' in tab.get('url', '').lower() or 'localhost' in tab.get('url', ''):
                    vscode_tab = tab
                    break
            
            if not vscode_tab:
                print("❌ 未找到VSCode标签页")
                return False
            
            # 连接WebSocket
            ws_url = vscode_tab['webSocketDebuggerUrl']
            self.websocket = websocket.create_connection(ws_url)
            self.is_connected = True
            self.connection_type = 'websocket'
            
            print(f"✅ WebSocket连接成功 - 标签页: {vscode_tab.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            if self.websocket:
                self.websocket.close()
                self.websocket = None
            
            self.is_connected = False
            self.connection_type = None
            print("✅ 连接已断开")
            
        except Exception as e:
            print(f"⚠️ 断开连接时出错: {e}")

    def execute_javascript(self, js_code: str) -> Dict[str, Any]:
        """
        执行JavaScript代码
        
        Args:
            js_code: JavaScript代码
            
        Returns:
            执行结果
        """
        if not self.is_connected:
            return {"success": False, "error": "未连接"}
        
        if self.connection_type == 'selenium':
            return self._execute_js_selenium(js_code)
        elif self.connection_type == 'websocket':
            return self._execute_js_websocket(js_code)
        else:
            return {"success": False, "error": "未知连接类型"}

    def _execute_js_selenium(self, js_code: str) -> Dict[str, Any]:
        """通过Selenium执行JavaScript"""
        try:
            # 确保代码有返回值
            if not js_code.strip().startswith('return ') and 'return ' not in js_code:
                js_code = f"return {js_code}"
            
            result = self.driver.execute_script(js_code)
            return {"success": True, "result": result}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _execute_js_websocket(self, js_code: str) -> Dict[str, Any]:
        """通过WebSocket执行JavaScript"""
        try:
            # 构造Chrome DevTools Protocol消息
            import random
            message_id = random.randint(1, 999999)  # 使用随机整数作为ID
            message = {
                "id": message_id,
                "method": "Runtime.evaluate",
                "params": {
                    "expression": js_code,
                    "returnByValue": True,
                    "awaitPromise": False,
                    "silent": False
                }
            }
            
            # 发送消息
            message_json = json.dumps(message)
            self.websocket.send(message_json)
            
            # 接收响应
            response_text = self.websocket.recv()
            response = json.loads(response_text)
            
            # 检查响应ID是否匹配
            if response.get("id") != message_id:
                return {"success": False, "error": f"响应ID不匹配: 期望{message_id}, 收到{response.get('id')}"}
            
            # 详细的响应处理
            if "result" in response:
                outer_result = response["result"]
                # Chrome DevTools Protocol有两层result结构
                if "result" in outer_result:
                    result_data = outer_result["result"]
                    if "value" in result_data:
                        return {"success": True, "result": result_data["value"]}
                    elif "objectId" in result_data:
                        # 对象引用，尝试获取描述
                        return {"success": True, "result": result_data.get("description", "[Object]")}
                    elif "type" in result_data:
                        # 处理不同类型的结果
                        result_type = result_data["type"]
                        if result_type == "undefined":
                            return {"success": True, "result": None}
                        elif result_type == "object" and result_data.get("subtype") == "null":
                            return {"success": True, "result": None}
                        else:
                            return {"success": True, "result": f"[{result_type}]"}
                    else:
                        return {"success": True, "result": result_data}
                else:
                    return {"success": True, "result": outer_result}
            elif "exceptionDetails" in response:
                exception = response["exceptionDetails"]
                error_msg = exception.get("text", "JavaScript执行异常")
                if "exception" in exception and "description" in exception["exception"]:
                    error_msg += f": {exception['exception']['description']}"
                return {"success": False, "error": error_msg}
            elif "error" in response:
                error_info = response["error"]
                return {"success": False, "error": f"Chrome DevTools错误: {error_info.get('message', '未知错误')}"}
            else:
                return {"success": False, "error": f"未知响应格式: {response}"}
                
        except json.JSONDecodeError as e:
            return {"success": False, "error": f"JSON解析错误: {e}"}
        except Exception as e:
            return {"success": False, "error": f"WebSocket通信错误: {e}"}

    def load_js_file(self, filename: str) -> str:
        """
        加载JavaScript文件内容
        
        Args:
            filename: JavaScript文件名
            
        Returns:
            JavaScript代码内容
        """
        if filename in self.js_cache:
            return self.js_cache[filename]
        
        try:
            js_file_path = self.scripts_dir / filename
            if not js_file_path.exists():
                raise FileNotFoundError(f"JavaScript文件不存在: {js_file_path}")
            
            with open(js_file_path, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # 缓存内容
            self.js_cache[filename] = js_content
            return js_content
            
        except Exception as e:
            print(f"❌ 加载JavaScript文件失败: {e}")
            return ""

    def execute_js_file(self, filename: str, function_name: str = None, *args) -> Dict[str, Any]:
        """
        执行JavaScript文件中的函数
        
        Args:
            filename: JavaScript文件名
            function_name: 要执行的函数名（如果为None则执行整个文件）
            *args: 函数参数
            
        Returns:
            执行结果
        """
        js_content = self.load_js_file(filename)
        if not js_content:
            return {"success": False, "error": "无法加载JavaScript文件"}
        
        if function_name:
            # 构造函数调用代码，确保有返回值
            args_str = ', '.join([json.dumps(arg) if isinstance(arg, (str, dict, list)) else str(arg) for arg in args])
            js_code = f"{js_content}\n\n// 调用函数并返回结果\n{function_name}({args_str});"
        else:
            js_code = js_content
        
        return self.execute_javascript(js_code)
