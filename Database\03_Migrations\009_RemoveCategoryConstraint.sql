-- =============================================
-- 删除 UI自动化模板相关表的约束
-- 创建时间: 2025-06-26
-- 描述: 删除 Category、ActionType 字段的 CHECK 约束和 Name 字段的 UNIQUE 约束，允许用户自定义和重复名称
-- =============================================

USE [ProjectManagementAI]
GO

-- 设置错误处理
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '开始删除 UI自动化模板相关表的约束...';
PRINT '========================================';

-- =============================================
-- 1. 检查约束是否存在并删除
-- =============================================

PRINT '正在检查并删除 Category 约束...';

-- 删除 Category 约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSequences_Category')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    DROP CONSTRAINT [CK_UIAutoMationTemplateSequences_Category];
    
    PRINT '✓ 已删除 Category 约束 CK_UIAutoMationTemplateSequences_Category';
END
ELSE
BEGIN
    PRINT '⚠️ Category 约束 CK_UIAutoMationTemplateSequences_Category 不存在，跳过删除';
END

-- =============================================
-- 2. 删除 ActionType 约束
-- =============================================

PRINT '正在检查并删除 ActionType 约束...';

-- 删除 ActionType 约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSteps_ActionType')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    DROP CONSTRAINT [CK_UIAutoMationTemplateSteps_ActionType];

    PRINT '✓ 已删除 ActionType 约束 CK_UIAutoMationTemplateSteps_ActionType';
END
ELSE
BEGIN
    PRINT '⚠️ ActionType 约束 CK_UIAutoMationTemplateSteps_ActionType 不存在，跳过删除';
END

-- =============================================
-- 3. 删除 Name 唯一键约束
-- =============================================

PRINT '正在检查并删除 Name 唯一键约束...';

-- 删除 Name 唯一键约束
IF EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UK_UIAutoMationTemplateSequences_Name')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    DROP CONSTRAINT [UK_UIAutoMationTemplateSequences_Name];

    PRINT '✓ 已删除 Name 唯一键约束 UK_UIAutoMationTemplateSequences_Name';
END
ELSE
BEGIN
    PRINT '⚠️ Name 唯一键约束 UK_UIAutoMationTemplateSequences_Name 不存在，跳过删除';
END

-- =============================================
-- 4. 验证约束删除结果
-- =============================================

PRINT '正在验证约束删除结果...';

-- 检查 Category 约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSequences_Category')
BEGIN
    PRINT '✅ Category 约束删除成功';
END
ELSE
BEGIN
    PRINT '❌ Category 约束删除失败';
    RAISERROR('Category 约束删除失败', 16, 1);
    RETURN;
END

-- 检查 ActionType 约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSteps_ActionType')
BEGIN
    PRINT '✅ ActionType 约束删除成功';
END
ELSE
BEGIN
    PRINT '❌ ActionType 约束删除失败';
    RAISERROR('ActionType 约束删除失败', 16, 1);
    RETURN;
END

-- 检查 Name 唯一键约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UK_UIAutoMationTemplateSequences_Name')
BEGIN
    PRINT '✅ Name 唯一键约束删除成功';
END
ELSE
BEGIN
    PRINT '❌ Name 唯一键约束删除失败';
    RAISERROR('Name 唯一键约束删除失败', 16, 1);
    RETURN;
END

-- =============================================
-- 5. 测试插入操作
-- =============================================

PRINT '正在测试自定义分类、动作类型和重复名称插入...';

-- 测试插入一个自定义分类的序列记录
BEGIN TRY
    -- 使用事务进行测试
    BEGIN TRANSACTION TestInsert;

    DECLARE @TestSequenceId1 INT, @TestSequenceId2 INT;

    -- 测试插入第一个序列
    INSERT INTO [dbo].[UIAutoMationTemplateSequences] (
        Name, Description, Category, IsActive, CreatedTime, IsDeleted, UsageCount
    ) VALUES (
        'TEST_SEQUENCE_DUPLICATE_NAME',
        '测试序列1 - 验证自定义分类',
        '自定义测试分类',
        1,
        GETDATE(),
        0,
        0
    );

    SET @TestSequenceId1 = SCOPE_IDENTITY();
    PRINT '✓ 第一个序列插入测试成功';

    -- 测试插入同名序列（验证唯一键约束已删除）
    INSERT INTO [dbo].[UIAutoMationTemplateSequences] (
        Name, Description, Category, IsActive, CreatedTime, IsDeleted, UsageCount
    ) VALUES (
        'TEST_SEQUENCE_DUPLICATE_NAME',
        '测试序列2 - 验证重复名称允许',
        '另一个自定义分类',
        1,
        GETDATE(),
        0,
        0
    );

    SET @TestSequenceId2 = SCOPE_IDENTITY();
    PRINT '✓ 重复名称序列插入测试成功';

    -- 测试插入自定义动作类型的步骤
    INSERT INTO [dbo].[UIAutoMationTemplateSteps] (
        SequenceId, StepOrder, ActionType, Description, TimeoutSeconds, MaxRetries, IsActive, CreatedTime, IsDeleted
    ) VALUES (
        @TestSequenceId1, 1, '自定义动作类型', '测试步骤 - 验证自定义动作类型', 5, 3, 1, GETDATE(), 0
    );

    PRINT '✓ 自定义动作类型插入测试成功';

    -- 回滚测试数据
    ROLLBACK TRANSACTION TestInsert;
    PRINT '✓ 测试数据已回滚';

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION TestInsert;

    PRINT '❌ 自定义类型插入测试失败';
    PRINT '错误信息: ' + ERROR_MESSAGE();
    RAISERROR('自定义类型插入测试失败', 16, 1);
    RETURN;
END CATCH

PRINT '========================================';
PRINT '✅ 所有约束删除完成！';
PRINT '现在可以：';
PRINT '1. 使用任意自定义分类名称创建序列';
PRINT '2. 使用任意自定义动作类型创建步骤';
PRINT '3. 创建重复名称的序列';
