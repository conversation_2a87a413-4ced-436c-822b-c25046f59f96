-- =============================================
-- Selenium测试初始化数据脚本
-- 版本: 1.0
-- 创建日期: 2025-06-25
-- 描述: 为Selenium测试功能添加示例数据和模板
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. 插入示例Selenium测试脚本
-- =============================================

-- 检查是否已有数据
IF NOT EXISTS (SELECT 1 FROM SeleniumScripts WHERE Name = '用户登录测试')
BEGIN
    INSERT INTO SeleniumScripts (
        Name, 
        Description, 
        Category, 
        Code, 
        ConfigJson, 
        TagsJson, 
        Priority, 
        Status, 
        ProjectId,
        CreatedBy,
        CreatedTime
    ) VALUES (
        N'用户登录测试',
        N'测试用户登录功能的完整流程，包括输入验证和登录成功跳转',
        N'ui',
        N'# 用户登录测试脚本
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

def test_user_login():
    """测试用户登录功能"""
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # 初始化WebDriver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("开始执行用户登录测试...")
        
        # 设置窗口大小
        driver.set_window_size(1920, 1080)
        
        # 设置超时时间
        driver.implicitly_wait(10)
        driver.set_page_load_timeout(30)
        
        # 访问登录页面
        driver.get("https://localhost:61136/login")
        print("已打开登录页面")
        
        # 等待页面加载完成
        wait = WebDriverWait(driver, 10)
        
        # 输入用户名
        username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
        username_field.clear()
        username_field.send_keys("admin")
        print("已输入用户名")
        
        # 输入密码
        password_field = driver.find_element(By.NAME, "password")
        password_field.clear()
        password_field.send_keys("password")
        print("已输入密码")
        
        # 点击登录按钮
        login_button = driver.find_element(By.XPATH, "//button[@type=''submit'']")
        login_button.click()
        print("已点击登录按钮")
        
        # 等待登录成功，检查是否跳转到仪表板
        wait.until(EC.url_contains("/dashboard"))
        print("登录成功，已跳转到仪表板")
        
        # 验证用户信息显示
        user_info = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "user-info")))
        assert "admin" in user_info.text, "用户信息显示不正确"
        print("用户信息验证成功")
        
        print("✅ 用户登录测试执行成功")
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        # 截图保存错误现场
        driver.save_screenshot("login_test_error.png")
        raise
    finally:
        # 关闭浏览器
        driver.quit()
        print("浏览器已关闭")

if __name__ == "__main__":
    test_user_login()',
        N'{"browser":"chrome","headless":false,"windowWidth":1920,"windowHeight":1080,"pageLoadTimeout":30,"implicitWait":10,"scriptTimeout":30,"enableLogging":true,"screenshotOptions":["onError"],"retryCount":1,"waitStrategy":"smart","environment":"local","baseUrl":"https://localhost:61136"}',
        N'["登录测试","UI测试","基础功能"]',
        N'high',
        N'ready',
        NULL,
        1,
        GETDATE()
    );
    
    PRINT '✅ 已插入用户登录测试脚本';
END

-- 表单提交测试脚本
IF NOT EXISTS (SELECT 1 FROM SeleniumScripts WHERE Name = '项目创建表单测试')
BEGIN
    INSERT INTO SeleniumScripts (
        Name, 
        Description, 
        Category, 
        Code, 
        ConfigJson, 
        TagsJson, 
        Priority, 
        Status, 
        CreatedBy,
        CreatedTime
    ) VALUES (
        N'项目创建表单测试',
        N'测试项目创建表单的各种输入验证和提交功能',
        N'ui',
        N'# 项目创建表单测试脚本
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

def test_project_creation():
    """测试项目创建表单"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("开始执行项目创建表单测试...")
        
        driver.set_window_size(1920, 1080)
        driver.implicitly_wait(10)
        
        # 先登录系统
        driver.get("https://localhost:61136/login")
        
        # 登录
        username_field = driver.find_element(By.NAME, "username")
        username_field.send_keys("admin")
        
        password_field = driver.find_element(By.NAME, "password")
        password_field.send_keys("password")
        
        login_button = driver.find_element(By.XPATH, "//button[@type=''submit'']")
        login_button.click()
        
        # 等待登录完成
        wait = WebDriverWait(driver, 10)
        wait.until(EC.url_contains("/dashboard"))
        print("登录成功")
        
        # 导航到项目创建页面
        driver.get("https://localhost:61136/projects/create")
        print("已打开项目创建页面")
        
        # 填写项目名称
        project_name = wait.until(EC.presence_of_element_located((By.NAME, "name")))
        project_name.clear()
        project_name.send_keys("测试项目_" + str(int(time.time())))
        print("已输入项目名称")
        
        # 填写项目描述
        description_field = driver.find_element(By.NAME, "description")
        description_field.clear()
        description_field.send_keys("这是一个通过Selenium自动化测试创建的项目")
        print("已输入项目描述")
        
        # 选择技术栈
        tech_stack_select = Select(driver.find_element(By.NAME, "technologyStack"))
        tech_stack_select.select_by_visible_text("Vue + C# + SQL Server")
        print("已选择技术栈")
        
        # 设置项目优先级
        priority_select = Select(driver.find_element(By.NAME, "priority"))
        priority_select.select_by_value("Medium")
        print("已设置优先级")
        
        # 提交表单
        submit_button = driver.find_element(By.XPATH, "//button[@type=''submit'']")
        submit_button.click()
        print("已提交表单")
        
        # 验证创建成功
        wait.until(EC.url_contains("/projects"))
        success_message = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "success-message")))
        assert "创建成功" in success_message.text, "项目创建失败"
        print("✅ 项目创建表单测试执行成功")
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        driver.save_screenshot("project_creation_error.png")
        raise
    finally:
        driver.quit()

if __name__ == "__main__":
    test_project_creation()',
        N'{"browser":"chrome","headless":false,"windowWidth":1920,"windowHeight":1080,"pageLoadTimeout":30,"implicitWait":10,"scriptTimeout":30,"enableLogging":true,"screenshotOptions":["onError","onSuccess"],"retryCount":2,"waitStrategy":"smart","environment":"local","baseUrl":"https://localhost:61136"}',
        N'["表单测试","项目管理","UI测试"]',
        N'medium',
        N'ready',
        1,
        GETDATE()
    );
    
    PRINT '✅ 已插入项目创建表单测试脚本';
END

-- API测试脚本示例
IF NOT EXISTS (SELECT 1 FROM SeleniumScripts WHERE Name = 'API接口测试')
BEGIN
    INSERT INTO SeleniumScripts (
        Name, 
        Description, 
        Category, 
        Code, 
        ConfigJson, 
        TagsJson, 
        Priority, 
        Status, 
        CreatedBy,
        CreatedTime
    ) VALUES (
        N'API接口测试',
        N'测试后端API接口的功能和性能',
        N'api',
        N'# API接口测试脚本
import requests
import json
import time
from datetime import datetime

def test_api_endpoints():
    """测试API接口"""
    base_url = "https://localhost:61136/api"
    
    # 测试数据
    test_results = []
    
    try:
        print("开始执行API接口测试...")
        
        # 1. 测试登录接口
        print("测试登录接口...")
        login_data = {
            "username": "admin",
            "password": "password"
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/auth/login", 
                               json=login_data, 
                               verify=False,
                               timeout=30)
        end_time = time.time()
        
        assert response.status_code == 200, f"登录失败，状态码: {response.status_code}"
        
        token = response.json().get("token")
        assert token, "未获取到访问令牌"
        
        test_results.append({
            "endpoint": "/auth/login",
            "method": "POST",
            "status_code": response.status_code,
            "response_time": round((end_time - start_time) * 1000, 2),
            "success": True
        })
        print("✅ 登录接口测试成功")
        
        # 设置认证头
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. 测试获取项目列表接口
        print("测试获取项目列表接口...")
        start_time = time.time()
        response = requests.get(f"{base_url}/projects", 
                              headers=headers, 
                              verify=False,
                              timeout=30)
        end_time = time.time()
        
        assert response.status_code == 200, f"获取项目列表失败，状态码: {response.status_code}"
        
        projects = response.json()
        assert isinstance(projects, list), "返回数据格式不正确"
        
        test_results.append({
            "endpoint": "/projects",
            "method": "GET",
            "status_code": response.status_code,
            "response_time": round((end_time - start_time) * 1000, 2),
            "success": True,
            "data_count": len(projects)
        })
        print(f"✅ 获取项目列表成功，共{len(projects)}个项目")
        
        # 3. 测试创建项目接口
        print("测试创建项目接口...")
        project_data = {
            "name": f"API测试项目_{int(time.time())}",
            "description": "通过API测试创建的项目",
            "technologyStack": "Vue + C# + SQL Server",
            "priority": "Medium"
        }
        
        start_time = time.time()
        response = requests.post(f"{base_url}/projects", 
                               json=project_data,
                               headers=headers, 
                               verify=False,
                               timeout=30)
        end_time = time.time()
        
        assert response.status_code in [200, 201], f"创建项目失败，状态码: {response.status_code}"
        
        created_project = response.json()
        assert created_project.get("id"), "创建的项目没有返回ID"
        
        test_results.append({
            "endpoint": "/projects",
            "method": "POST",
            "status_code": response.status_code,
            "response_time": round((end_time - start_time) * 1000, 2),
            "success": True,
            "created_id": created_project.get("id")
        })
        print(f"✅ 创建项目成功，项目ID: {created_project.get(''id'')}")
        
        # 输出测试结果
        print("\n📊 API测试结果汇总:")
        for result in test_results:
            print(f"  {result[''method'']} {result[''endpoint'']}: {result[''status_code'']} ({result[''response_time'']}ms)")
        
        print("✅ API接口测试全部通过")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        raise

if __name__ == "__main__":
    test_api_endpoints()',
        N'{"environment":"local","baseUrl":"https://localhost:61136","timeout":30,"retryCount":1,"enableLogging":true}',
        N'["API测试","接口测试","自动化测试"]',
        N'medium',
        N'ready',
        1,
        GETDATE()
    );
    
    PRINT '✅ 已插入API接口测试脚本';
END

-- 性能测试脚本示例
IF NOT EXISTS (SELECT 1 FROM SeleniumScripts WHERE Name = '页面加载性能测试')
BEGIN
    INSERT INTO SeleniumScripts (
        Name, 
        Description, 
        Category, 
        Code, 
        ConfigJson, 
        TagsJson, 
        Priority, 
        Status, 
        CreatedBy,
        CreatedTime
    ) VALUES (
        N'页面加载性能测试',
        N'测试主要页面的加载性能和响应时间',
        N'performance',
        N'# 页面加载性能测试脚本
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import time
import json

def test_page_performance():
    """测试页面加载性能"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("开始执行页面性能测试...")
        
        driver.set_window_size(1920, 1080)
        
        # 测试页面列表
        test_pages = [
            {"name": "登录页面", "url": "/login"},
            {"name": "仪表板", "url": "/dashboard"},
            {"name": "项目列表", "url": "/projects"},
            {"name": "需求管理", "url": "/requirements"},
            {"name": "设计生成", "url": "/design"}
        ]
        
        performance_results = []
        
        for page in test_pages:
            print(f"测试 {page[''name'']} 性能...")
            
            # 记录开始时间
            start_time = time.time()
            
            # 访问页面
            driver.get(f"https://localhost:61136{page[''url'']}")
            
            # 等待页面加载完成
            WebDriverWait(driver, 30).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # 记录结束时间
            end_time = time.time()
            load_time = round((end_time - start_time) * 1000, 2)
            
            # 获取页面性能指标
            navigation_timing = driver.execute_script("""
                var timing = window.performance.timing;
                return {
                    domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                    loadComplete: timing.loadEventEnd - timing.navigationStart,
                    domInteractive: timing.domInteractive - timing.navigationStart
                };
            """)
            
            result = {
                "page": page["name"],
                "url": page["url"],
                "total_load_time": load_time,
                "dom_content_loaded": navigation_timing.get("domContentLoaded", 0),
                "load_complete": navigation_timing.get("loadComplete", 0),
                "dom_interactive": navigation_timing.get("domInteractive", 0)
            }
            
            performance_results.append(result)
            
            print(f"  ✅ {page[''name'']} 加载完成: {load_time}ms")
            
            # 短暂等待
            time.sleep(1)
        
        # 输出性能测试结果
        print("\n📊 页面性能测试结果:")
        print("-" * 80)
        print(f"{页面名称:<15} {总加载时间:<12} {DOM就绪:<10} {完全加载:<10} {可交互:<10}")
        print("-" * 80)
        
        for result in performance_results:
            print(f"{result[''page'']:<15} {result[''total_load_time'']:<12}ms {result[''dom_content_loaded'']:<10}ms {result[''load_complete'']:<10}ms {result[''dom_interactive'']:<10}ms")
        
        # 性能评估
        avg_load_time = sum(r["total_load_time"] for r in performance_results) / len(performance_results)
        print(f"\n平均加载时间: {avg_load_time:.2f}ms")
        
        if avg_load_time < 2000:
            print("✅ 页面性能优秀")
        elif avg_load_time < 5000:
            print("⚠️ 页面性能良好")
        else:
            print("❌ 页面性能需要优化")
        
        print("✅ 页面性能测试完成")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        driver.save_screenshot("performance_test_error.png")
        raise
    finally:
        driver.quit()

if __name__ == "__main__":
    test_page_performance()',
        N'{"browser":"chrome","headless":false,"windowWidth":1920,"windowHeight":1080,"pageLoadTimeout":30,"implicitWait":5,"scriptTimeout":60,"enableLogging":true,"screenshotOptions":["onError"],"retryCount":1,"waitStrategy":"smart","environment":"local","baseUrl":"https://localhost:61136"}',
        N'["性能测试","页面加载","响应时间"]',
        N'low',
        N'ready',
        1,
        GETDATE()
    );
    
    PRINT '✅ 已插入页面加载性能测试脚本';
END

PRINT '🎉 Selenium测试初始化数据插入完成！';
PRINT '📋 已插入的测试脚本:';
PRINT '   1. 用户登录测试 (UI测试)';
PRINT '   2. 项目创建表单测试 (UI测试)';
PRINT '   3. API接口测试 (API测试)';
PRINT '   4. 页面加载性能测试 (性能测试)';
PRINT '✨ 这些脚本可以作为模板，帮助用户快速开始Selenium自动化测试';
GO
