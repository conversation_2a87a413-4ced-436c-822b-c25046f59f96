using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Models;
using ProjectManagement.Core.DTOs.VectorSearch;
using ProjectManagement.AI.Services;
using System.Text;

namespace ProjectManagement.AI.Services;

/// <summary>
/// 对话上下文管理服务
/// 功能：管理AI对话的上下文，控制token长度，优化对话历史，支持向量搜索增强
/// </summary>
public class ConversationContextService
{
    private readonly IRequirementConversationRepository _conversationRepository;
    private readonly IPromptTemplateService _promptTemplateService;
    private readonly VectorSearchService _vectorSearchService;
    private readonly ILogger<ConversationContextService> _logger;

    // Token限制配置
    private const int MAX_CONTEXT_TOKENS = 3000; // 上下文最大token数
    private const int MAX_HISTORY_MESSAGES = 10; // 最大历史消息数
    private const int SYSTEM_PROMPT_TOKENS = 500; // 系统提示词预估token数
    private const int RESPONSE_RESERVE_TOKENS = 1000; // 为AI回复预留的token数

    public ConversationContextService(
        IRequirementConversationRepository conversationRepository,
        IPromptTemplateService promptTemplateService,
        VectorSearchService vectorSearchService,
        ILogger<ConversationContextService> logger)
    {
        _conversationRepository = conversationRepository;
        _promptTemplateService = promptTemplateService;
        _vectorSearchService = vectorSearchService;
        _logger = logger;
    }

    /// <summary>
    /// 构建包含历史对话的AI提示词
    /// </summary>
    /// <param name="conversationId">会话ID</param>
    /// <param name="currentMessage">当前用户消息</param>
    /// <param name="projectInfo">项目信息</param>
    /// <param name="maxTokens">最大token限制</param>
    /// <returns>优化后的提示词</returns>
    public async Task<string> BuildContextualPromptAsync(
        string conversationId,
        string currentMessage,
        Project? projectInfo = null,
        int maxTokens = 4000)
    {
        try
        {
            // 计算可用于历史对话的token数
            var availableTokens = maxTokens - SYSTEM_PROMPT_TOKENS - RESPONSE_RESERVE_TOKENS - EstimateTokenCount(currentMessage);

            _logger.LogInformation("构建上下文提示词，可用token: {AvailableTokens}", availableTokens);

            // 获取历史对话
            var history = await GetOptimizedConversationHistoryAsync(conversationId, availableTokens);

            // 构建系统提示词
            var systemPrompt = await BuildSystemPromptAsync(projectInfo);

            // 构建历史对话上下文
            var historyContext = BuildHistoryContext(history);

            // 组合完整提示词
            var fullPrompt = $@"{systemPrompt}

{historyContext}

当前用户消息：{currentMessage}

请根据以上对话历史和项目背景，以友好、专业的方式回复用户的当前消息。保持对话的连贯性，并参考之前讨论的内容。";

            var finalTokenCount = EstimateTokenCount(fullPrompt);
            _logger.LogInformation("最终提示词token数: {TokenCount}", finalTokenCount);

            return fullPrompt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建上下文提示词失败");
            // 降级处理：返回不包含历史的基础提示词
            return await BuildBasicPromptAsync(currentMessage, projectInfo);
        }
    }

    /// <summary>
    /// 获取优化的对话历史（控制token数量）
    /// </summary>
    private async Task<List<RequirementConversation>> GetOptimizedConversationHistoryAsync(string conversationId, int maxTokens)
    {
        var allHistory = await _conversationRepository.GetConversationHistoryAsync(conversationId, MAX_HISTORY_MESSAGES);

        if (!allHistory.Any())
        {
            return new List<RequirementConversation>();
        }

        // 按时间正序排列（最早的在前）
        allHistory = allHistory.OrderBy(x => x.Timestamp).ToList();

        var optimizedHistory = new List<RequirementConversation>();
        var currentTokens = 0;

        // 从最新的消息开始，向前添加历史消息，直到达到token限制
        for (int i = allHistory.Count - 1; i >= 0; i--)
        {
            var conversation = allHistory[i];
            var messageTokens = EstimateTokenCount(conversation.UserMessage) +
                               EstimateTokenCount(conversation.AIResponse ?? "");

            if (currentTokens + messageTokens <= maxTokens)
            {
                optimizedHistory.Insert(0, conversation); // 插入到开头保持时间顺序
                currentTokens += messageTokens;
            }
            else
            {
                break;
            }
        }

        _logger.LogInformation("优化历史对话：原始 {OriginalCount} 条，优化后 {OptimizedCount} 条，使用token {UsedTokens}",
            allHistory.Count, optimizedHistory.Count, currentTokens);

        return optimizedHistory;
    }

    /// <summary>
    /// 构建系统提示词
    /// </summary>
    private async Task<string> BuildSystemPromptAsync(Project? projectInfo)
    {
        try
        {
            // 从数据库获取需求分析的默认提示词模板
            var template = await _promptTemplateService.GetDefaultTemplateAsync("RequirementAnalysis");

            if (template != null)
            {
                // 使用模板内容并替换参数
                var promptContent = template.Content;

                // 替换项目相关参数
                if (projectInfo != null)
                {
                    // 构建项目背景信息
                    var projectBackground = $@"项目名称：{projectInfo.Name ?? "未指定"}
项目描述：{projectInfo.Description ?? "暂无描述"}
项目状态：{projectInfo.Status ?? "未知"}
项目优先级：{projectInfo.Priority ?? "普通"}
技术栈：{projectInfo.TechnologyStack ?? "待确定"}";

                    promptContent = promptContent
                        .Replace("{projectBackground}", projectBackground)
                        .Replace("{topic}", "项目需求分析")
                        .Replace("{ProjectName}", projectInfo.Name ?? "")
                        .Replace("{ProjectDescription}", projectInfo.Description ?? "")
                        .Replace("{ProjectStatus}", projectInfo.Status ?? "")
                        .Replace("{ProjectPriority}", projectInfo.Priority ?? "");
                }
                else
                {
                    // 如果没有项目信息，使用默认值
                    promptContent = promptContent
                        .Replace("{projectBackground}", "暂无具体项目信息")
                        .Replace("{topic}", "软件项目需求分析")
                        .Replace("{ProjectName}", "")
                        .Replace("{ProjectDescription}", "")
                        .Replace("{ProjectStatus}", "")
                        .Replace("{ProjectPriority}", "");
                }

                _logger.LogInformation("使用数据库模板构建系统提示词，模板ID: {TemplateId}, 模板名称: {TemplateName}",
                    template.Id, template.Name);

                // 记录模板使用统计
                await RecordTemplateUsageAsync(template.Id);

                return promptContent;
            }
            else
            {
                return "提示词模板为空!";
            }
        }
        catch (Exception ex)
        {

            return "提示词模板为空!";
        }
    }


    /// <summary>
    /// 构建历史对话上下文
    /// </summary>
    private string BuildHistoryContext(List<RequirementConversation> history)
    {
        if (!history.Any())
        {
            return "这是对话的开始。";
        }

        var contextBuilder = new System.Text.StringBuilder();
        contextBuilder.AppendLine("对话历史：");

        foreach (var conversation in history)
        {
            contextBuilder.AppendLine($"用户：{conversation.UserMessage}");
            if (!string.IsNullOrEmpty(conversation.AIResponse))
            {
                contextBuilder.AppendLine($"AI助手：{conversation.AIResponse}");
            }
            contextBuilder.AppendLine();
        }

        return contextBuilder.ToString();
    }

    /// <summary>
    /// 构建基础提示词（不包含历史对话）
    /// </summary>
    private async Task<string> BuildBasicPromptAsync(string currentMessage, Project? projectInfo)
    {
        var systemPrompt = await BuildSystemPromptAsync(projectInfo);
        return $@"{systemPrompt}

用户消息：{currentMessage}

请根据用户的消息，以友好、专业的方式回复。";
    }

    /// <summary>
    /// 估算文本的token数量
    /// </summary>
    private int EstimateTokenCount(string text)
    {
        if (string.IsNullOrEmpty(text)) return 0;

        // 中文字符计数
        var chineseCharCount = text.Count(c => c >= 0x4e00 && c <= 0x9fff);
        // 英文单词计数
        var englishWordCount = text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
        // 其他字符计数
        var otherCharCount = text.Length - chineseCharCount;

        // 估算公式：中文字符*2 + 英文单词*1.3 + 其他字符*0.5
        return (int)(chineseCharCount * 2 + englishWordCount * 1.3 + otherCharCount * 0.5);
    }

    /// <summary>
    /// 保存对话记录
    /// </summary>
    public async Task<RequirementConversation> SaveConversationAsync(
        string conversationId,
        int? projectId,
        int userId,
        string userMessage,
        string aiResponse,
        string messageType = "Requirement")
    {
        try
        {
            var conversation = new RequirementConversation
            {
                ConversationId = conversationId,
                ProjectId = projectId, // 现在支持NULL值
                UserId = userId,
                UserMessage = userMessage,
                AIResponse = aiResponse,
                MessageType = messageType,
                Timestamp = DateTime.UtcNow
            };

            var result = await _conversationRepository.CreateConversationAsync(conversation);

            _logger.LogInformation("保存对话记录成功: {ConversationId}, 项目ID: {ProjectId}, 用户token: {UserTokens}, AI token: {AiTokens}",
                conversationId, projectId?.ToString() ?? "无", EstimateTokenCount(userMessage), EstimateTokenCount(aiResponse));

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存对话记录失败: {ConversationId}, ProjectId: {ProjectId}", conversationId, projectId);
            throw;
        }
    }

    /// <summary>
    /// 获取对话的token使用统计
    /// </summary>
    public async Task<ConversationTokenStats> GetConversationStatsAsync(string conversationId)
    {
        return await _conversationRepository.GetConversationTokenStatsAsync(conversationId);
    }

    /// <summary>
    /// 构建向量增强的对话上下文
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="currentMessage">当前用户消息</param>
    /// <param name="projectId">项目ID（可选）</param>
    /// <param name="includeVectorContext">是否包含向量搜索上下文</param>
    /// <returns>增强的上下文提示词</returns>
    public async Task<string> BuildVectorEnhancedContextAsync(
        string conversationId,
        string currentMessage,
        int? projectId = null,
        bool includeVectorContext = true)
    {
        try
        {
            _logger.LogInformation("构建向量增强对话上下文: {ConversationId}", conversationId);

            // 获取项目信息
            Project? projectInfo = null;
            if (projectId.HasValue)
            {
                try
                {
                    // 这里需要注入项目仓储来获取项目信息
                    // 暂时传递null，后续可以优化
                    _logger.LogWarning("项目信息获取功能需要注入项目仓储，当前传递null");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取项目信息失败: {ProjectId}", projectId);
                }
            }

            // 获取基础对话历史
            var baseContext = await BuildContextualPromptAsync(conversationId, currentMessage, projectInfo);

            if (!includeVectorContext)
            {
                return baseContext;
            }

            // 执行向量搜索获取相关上下文
            var vectorContext = await GetVectorContextAsync(currentMessage, projectId);

            // 合并上下文
            var enhancedContext = CombineContexts(baseContext, vectorContext);

            _logger.LogInformation("向量增强上下文构建完成，总长度: {Length} 字符", enhancedContext.Length);
            return enhancedContext;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建向量增强上下文失败: {ConversationId}", conversationId);
            // 降级到基础上下文
            return await BuildContextualPromptAsync(conversationId, currentMessage, null);
        }
    }

    /// <summary>
    /// 获取向量搜索上下文
    /// </summary>
    private async Task<string> GetVectorContextAsync(string query, int? projectId)
    {
        try
        {
            var searchOptions = new SearchOptions
            {
                TopK = 3, // 限制结果数量以控制token使用
                SimilarityThreshold = 0.75f, // 提高相似度阈值确保质量
                Filters = projectId.HasValue ? new Dictionary<string, object> { ["projectId"] = projectId.Value } : null
            };

            var searchResults = await _vectorSearchService.SemanticSearchAsync(query, searchOptions);

            if (!searchResults.Any())
            {
                return "";
            }

            var contextBuilder = new StringBuilder();
            contextBuilder.AppendLine("\n=== 相关文档上下文 ===");

            foreach (var result in searchResults.Take(3)) // 最多3个结果
            {
                contextBuilder.AppendLine($"\n【文档片段 - 相似度: {result.SimilarityScore:F2}】");
                contextBuilder.AppendLine(result.Content.Length > 300
                    ? result.Content.Substring(0, 300) + "..."
                    : result.Content);
            }

            contextBuilder.AppendLine("\n=== 相关文档上下文结束 ===\n");

            return contextBuilder.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取向量上下文失败: {Query}", query);
            return "";
        }
    }

    /// <summary>
    /// 合并基础上下文和向量上下文
    /// </summary>
    private string CombineContexts(string baseContext, string vectorContext)
    {
        if (string.IsNullOrEmpty(vectorContext))
        {
            return baseContext;
        }

        // 检查总长度，确保不超过token限制
        var combinedLength = baseContext.Length + vectorContext.Length;
        var maxAllowedLength = MAX_CONTEXT_TOKENS * 3; // 粗略估算：1 token ≈ 3 字符

        if (combinedLength > maxAllowedLength)
        {
            // 如果超长，优先保留对话历史，截断向量上下文
            var availableVectorLength = maxAllowedLength - baseContext.Length;
            if (availableVectorLength > 100) // 至少保留100字符的向量上下文
            {
                vectorContext = vectorContext.Substring(0, availableVectorLength) + "\n[上下文已截断]";
            }
            else
            {
                vectorContext = ""; // 如果空间不足，放弃向量上下文
            }
        }

        return $"{vectorContext}\n{baseContext}";
    }

    /// <summary>
    /// 记录模板使用统计
    /// </summary>
    private async Task RecordTemplateUsageAsync(int templateId)
    {
        try
        {
            // 更新模板使用次数
            await _promptTemplateService.RecordTemplateUsageAsync(
                templateId: templateId,
                userId: 0, // 系统使用，用户ID为0
                projectId: null,
                aiProvider: "System",
                aiModel: "ConversationContext",
                inputParameters: "{}",
                generatedPrompt: "System prompt generated",
                aiResponse: null,
                responseTimeMs: null,
                tokenUsage: null,
                cost: null,
                isSuccess: true,
                errorMessage: null
            );

            _logger.LogDebug("记录模板使用统计成功，模板ID: {TemplateId}", templateId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "记录模板使用统计失败，模板ID: {TemplateId}", templateId);
            // 不抛出异常，避免影响主要功能
        }
    }
}
