using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 开发步骤仓储接口
/// </summary>
public interface IDevelopmentStepRepository : IRepository<DevelopmentStep>
{
    #region 项目相关查询

    /// <summary>
    /// 根据项目ID获取所有开发步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>开发步骤列表</returns>
    Task<List<DevelopmentStep>> GetByProjectIdAsync(int projectId, bool includeDeleted = false);

    /// <summary>
    /// 根据项目ID分页获取开发步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <param name="stepType">步骤类型筛选</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<DevelopmentStep>> GetByProjectIdPagedAsync(
        int projectId, 
        int pageIndex, 
        int pageSize, 
        string? status = null, 
        string? priority = null, 
        string? stepType = null);

    #endregion

    #region 需求文档相关查询

    /// <summary>
    /// 根据需求文档ID获取开发步骤
    /// </summary>
    /// <param name="requirementDocumentId">需求文档ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>开发步骤列表</returns>
    Task<List<DevelopmentStep>> GetByRequirementDocumentIdAsync(int requirementDocumentId, bool includeDeleted = false);

    #endregion

    #region 层级结构查询

    /// <summary>
    /// 获取顶级步骤（没有父步骤的步骤）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>顶级步骤列表</returns>
    Task<List<DevelopmentStep>> GetTopLevelStepsAsync(int projectId, bool includeDeleted = false);

    /// <summary>
    /// 根据父步骤ID获取子步骤
    /// </summary>
    /// <param name="parentStepId">父步骤ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>子步骤列表</returns>
    Task<List<DevelopmentStep>> GetChildStepsAsync(int parentStepId, bool includeDeleted = false);

    /// <summary>
    /// 获取步骤的完整层级结构（包含所有子步骤）
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>包含层级结构的步骤</returns>
    Task<DevelopmentStep?> GetStepWithHierarchyAsync(int stepId, bool includeDeleted = false);

    /// <summary>
    /// 获取项目的完整步骤树结构
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>步骤树结构</returns>
    Task<List<DevelopmentStep>> GetProjectStepTreeAsync(int projectId, bool includeDeleted = false);

    #endregion

    #region 状态和进度查询

    /// <summary>
    /// 根据状态获取步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="status">状态</param>
    /// <param name="includeDeleted">是否包含已删除的记录</param>
    /// <returns>步骤列表</returns>
    Task<List<DevelopmentStep>> GetByStatusAsync(int projectId, string status, bool includeDeleted = false);

    /// <summary>
    /// 获取可以开始执行的步骤（依赖已满足）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>可执行步骤列表</returns>
    Task<List<DevelopmentStep>> GetExecutableStepsAsync(int projectId);

    /// <summary>
    /// 获取被阻塞的步骤（依赖未满足）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>被阻塞步骤列表</returns>
    Task<List<DevelopmentStep>> GetBlockedStepsAsync(int projectId);

    #endregion

    #region 依赖关系查询

    /// <summary>
    /// 获取步骤的所有依赖关系
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>依赖关系列表</returns>
    Task<List<StepDependency>> GetStepDependenciesAsync(int stepId);

    /// <summary>
    /// 获取依赖某个步骤的所有步骤
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>依赖关系列表</returns>
    Task<List<StepDependency>> GetStepDependentsAsync(int stepId);

    /// <summary>
    /// 检查是否存在循环依赖
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>true表示存在循环依赖</returns>
    Task<bool> HasCircularDependencyAsync(int projectId);

    #endregion

    #region 执行历史查询

    /// <summary>
    /// 获取步骤的执行历史
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>执行历史分页结果</returns>
    Task<PagedResult<StepExecutionHistory>> GetStepExecutionHistoryAsync(int stepId, int pageIndex = 1, int pageSize = 20);

    /// <summary>
    /// 获取最新的执行历史记录
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>最新执行历史</returns>
    Task<StepExecutionHistory?> GetLatestExecutionHistoryAsync(int stepId);

    #endregion

    #region 统计查询

    /// <summary>
    /// 获取项目步骤统计信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    Task<ProjectStepStatistics> GetProjectStepStatisticsAsync(int projectId);

    /// <summary>
    /// 获取用户的步骤统计信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="projectId">项目ID（可选）</param>
    /// <returns>统计信息</returns>
    Task<UserStepStatistics> GetUserStepStatisticsAsync(int userId, int? projectId = null);

    #endregion

    #region 批量操作

    /// <summary>
    /// 批量更新步骤状态
    /// </summary>
    /// <param name="stepIds">步骤ID列表</param>
    /// <param name="status">新状态</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>更新的记录数</returns>
    Task<int> BatchUpdateStatusAsync(List<int> stepIds, string status, int? updatedBy = null);

    /// <summary>
    /// 批量更新步骤优先级
    /// </summary>
    /// <param name="stepIds">步骤ID列表</param>
    /// <param name="priority">新优先级</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>更新的记录数</returns>
    Task<int> BatchUpdatePriorityAsync(List<int> stepIds, string priority, int? updatedBy = null);

    /// <summary>
    /// 重新排序步骤
    /// </summary>
    /// <param name="stepOrders">步骤ID和排序号的字典</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>更新的记录数</returns>
    Task<int> ReorderStepsAsync(Dictionary<int, int> stepOrders, int? updatedBy = null);

    #endregion

    #region 搜索功能

    /// <summary>
    /// 搜索开发步骤
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    Task<PagedResult<DevelopmentStep>> SearchStepsAsync(int projectId, string keyword, int pageIndex = 1, int pageSize = 20);

    #endregion
}

/// <summary>
/// 项目步骤统计信息
/// </summary>
public class ProjectStepStatistics
{
    public int ProjectId { get; set; }
    public string ProjectName { get; set; } = string.Empty;
    public int TotalSteps { get; set; }
    public int PendingSteps { get; set; }
    public int InProgressSteps { get; set; }
    public int CompletedSteps { get; set; }
    public int FailedSteps { get; set; }
    public int BlockedSteps { get; set; }
    public double AverageProgress { get; set; }
    public decimal TotalEstimatedHours { get; set; }
    public decimal TotalActualHours { get; set; }
    public int TechnologyStackCount { get; set; }
    public DateTime? FirstStepCreated { get; set; }
    public DateTime? LastStepUpdated { get; set; }
}

/// <summary>
/// 用户步骤统计信息
/// </summary>
public class UserStepStatistics
{
    public int UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public int TotalSteps { get; set; }
    public int CompletedSteps { get; set; }
    public int InProgressSteps { get; set; }
    public int FailedSteps { get; set; }
    public double CompletionRate { get; set; }
    public decimal TotalHours { get; set; }
    public int ProjectCount { get; set; }
}
