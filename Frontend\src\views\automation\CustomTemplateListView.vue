<template>
  <div class="custom-template-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          自定义UI自动化模板
        </h1>
        <p class="page-description">管理和维护自定义的UI自动化模板，支持创建、编辑、测试和应用模板</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建模板
        </el-button>
        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
        <el-button @click="exportTemplates">
          <el-icon><Download /></el-icon>
          导出模板
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon template">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalTemplates }}</div>
                <div class="stat-label">总模板数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon sequence">
                <el-icon><List /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalSequences }}</div>
                <div class="stat-label">模板序列</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon execution">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalExecutions }}</div>
                <div class="stat-label">执行次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ (statistics.successRate * 100).toFixed(1) }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和过滤 -->
    <el-card class="search-card">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索模板名称或描述"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="searchForm.category"
              placeholder="选择分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="searchForm.tags"
              placeholder="选择标签"
              multiple
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="tag in tags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 模板列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>模板列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
              <el-button
                :type="viewMode === 'card' ? 'primary' : ''"
                @click="viewMode = 'card'"
              >
                <el-icon><Grid /></el-icon>
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table
        v-if="viewMode === 'table'"
        v-loading="loading"
        :data="templates"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="模板名称" min-width="200">
          <template #default="{ row }">
            <div class="template-name">
              <div class="template-preview" v-if="row.filePath">
                <img :src="getImageUrl(row.filePath)" alt="模板预览" @error="handleImageError" />
              </div>
              <el-icon v-else class="template-icon"><Picture /></el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="confidence" label="置信度" width="100">
          <template #default="{ row }">
            <el-progress
              :percentage="row.confidence * 100"
              :stroke-width="6"
              :show-text="false"
              :color="getConfidenceColor(row.confidence)"
            />
            <span class="confidence-text">{{ (row.confidence * 100).toFixed(0) }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="lastUsedTime" label="最后使用" width="150">
          <template #default="{ row }">
            <span v-if="row.lastUsedTime">{{ formatTime(row.lastUsedTime) }}</span>
            <span v-else class="text-muted">从未使用</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="testTemplate(row)">
                <el-icon><VideoPlay /></el-icon>
                测试
              </el-button>
              <el-button size="small" @click="editTemplate(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="deleteTemplate(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图 -->
      <div v-else class="template-cards">
        <el-row :gutter="20">
          <el-col
            v-for="template in templates"
            :key="template.id"
            :span="8"
            style="margin-bottom: 20px"
          >
            <el-card class="template-card" :class="{ selected: selectedTemplates.includes(template.id) }">
              <template #header>
                <div class="template-card-header">
                  <div class="template-info">
                    <h3>{{ template.name }}</h3>
                    <el-tag :type="getCategoryTagType(template.category)" size="small">
                      {{ template.category }}
                    </el-tag>
                  </div>
                  <el-checkbox
                    :model-value="selectedTemplates.includes(template.id)"
                    @change="toggleTemplateSelection(template.id, $event)"
                  />
                </div>
              </template>

              <div class="template-content">
                <!-- 模板图片预览 -->
                <div class="template-image" v-if="template.filePath">
                  <img :src="getImageUrl(template.filePath)" alt="模板预览" @error="handleImageError" />
                </div>
                <div v-else class="template-placeholder">
                  <el-icon><Picture /></el-icon>
                  <span>暂无图片</span>
                </div>

                <p class="template-description">{{ template.description }}</p>

                <div class="template-stats">
                  <div class="stat-item">
                    <span class="stat-label">置信度:</span>
                    <el-progress
                      :percentage="template.confidence * 100"
                      :stroke-width="4"
                      :show-text="false"
                      :color="getConfidenceColor(template.confidence)"
                    />
                    <span class="stat-value">{{ (template.confidence * 100).toFixed(0) }}%</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">使用次数:</span>
                    <span class="stat-value">{{ template.usageCount }}</span>
                  </div>
                </div>

                <div class="template-tags" v-if="template.tags && template.tags.length">
                  <el-tag
                    v-for="tag in template.tags"
                    :key="tag"
                    size="small"
                    effect="plain"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>

              <template #footer>
                <div class="template-actions">
                  <el-button size="small" @click="testTemplate(template)">
                    <el-icon><VideoPlay /></el-icon>
                    测试
                  </el-button>
                  <el-button size="small" @click="editTemplate(template)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteTemplate(template)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑模板对话框 -->
    <TemplateFormDialog
      v-model="showTemplateDialog"
      :template="currentTemplate"
      @success="handleTemplateSuccess"
    />

    <!-- 导入模板对话框 -->
    <ImportTemplateDialog
      v-model="showImportTemplateDialog"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting, Plus, Upload, Download, Document, List, VideoPlay, CircleCheck,
  Search, Grid, Picture, Edit, Delete
} from '@element-plus/icons-vue'
import type { ElTagType } from '@/types/element-plus'
import CustomTemplateService, {
  type CustomTemplate,
  type TemplateStatistics,
  type PageQuery
} from '@/services/customTemplate'
import TemplateFormDialog from '@/components/automation/TemplateFormDialog.vue'
import ImportTemplateDialog from '@/components/automation/ImportTemplateDialog.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const templates = ref<CustomTemplate[]>([])
const selectedTemplates = ref<number[]>([])
const categories = ref<string[]>([])
const tags = ref<string[]>([])

// 统计信息
const statistics = ref<TemplateStatistics>({
  totalTemplates: 0,
  totalSequences: 0,
  totalExecutions: 0,
  successRate: 0,
  categoryStats: {},
  recentExecutions: [],
  mostUsedTemplates: [],
  mostUsedSequences: []
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  tags: [] as string[]
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const showTemplateDialog = ref(false)
const showImportTemplateDialog = ref(false)
const currentTemplate = ref<CustomTemplate | null>(null)

// 计算属性
const queryParams = computed((): PageQuery => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  keyword: searchForm.keyword || undefined,
  category: searchForm.category || undefined,
  tags: searchForm.tags.length > 0 ? searchForm.tags : undefined
}))

// 生命周期
onMounted(() => {
  loadData()
  loadCategories()
  loadTags()
  loadStatistics()
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const result = await CustomTemplateService.getTemplates(queryParams.value)
    templates.value = result.items
    pagination.total = result.total
  } catch (error) {
    console.error('加载模板列表失败:', error)
    ElMessage.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    categories.value = await CustomTemplateService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadTags = async () => {
  try {
    tags.value = await CustomTemplateService.getTags()
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const loadStatistics = async () => {
  try {
    statistics.value = await CustomTemplateService.getStatistics()
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handleSelectionChange = (selection: CustomTemplate[]) => {
  selectedTemplates.value = selection.map(item => item.id)
}

const toggleTemplateSelection = (templateId: number, selected: string | number | boolean) => {
  if (Boolean(selected)) {
    if (!selectedTemplates.value.includes(templateId)) {
      selectedTemplates.value.push(templateId)
    }
  } else {
    const index = selectedTemplates.value.indexOf(templateId)
    if (index > -1) {
      selectedTemplates.value.splice(index, 1)
    }
  }
}

const showCreateDialog = () => {
  currentTemplate.value = null
  showTemplateDialog.value = true
}

const editTemplate = (template: CustomTemplate) => {
  currentTemplate.value = template
  showTemplateDialog.value = true
}

const testTemplate = async (template: CustomTemplate) => {
  try {
    ElMessage.info('正在测试模板...')
    const result = await CustomTemplateService.testTemplate(template.id)

    if (result.success) {
      ElMessage.success(`模板测试成功！找到位置: ${JSON.stringify(result.location)}`)
    } else {
      ElMessage.warning(`模板测试失败: ${result.error}`)
    }
  } catch (error) {
    console.error('测试模板失败:', error)
    ElMessage.error('测试模板失败')
  }
}

const deleteTemplate = async (template: CustomTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await CustomTemplateService.deleteTemplate(template.id)
    ElMessage.success('删除成功')
    loadData()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

const showImportDialog = () => {
  showImportTemplateDialog.value = true
}

const exportTemplates = async () => {
  try {
    const templateIds = selectedTemplates.value.length > 0 ? selectedTemplates.value : undefined
    const blob = await CustomTemplateService.exportTemplates(templateIds)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `templates_${new Date().toISOString().slice(0, 10)}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出模板失败:', error)
    ElMessage.error('导出模板失败')
  }
}

const handleTemplateSuccess = () => {
  loadData()
  loadStatistics()
  loadCategories()
  loadTags()
}

const handleImportSuccess = () => {
  loadData()
  loadStatistics()
  loadCategories()
  loadTags()
}

// 工具方法
const getCategoryTagType = (category: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    '按钮': 'primary',
    '菜单': 'success',
    '对话框': 'warning',
    '输入框': 'info',
    '图标': 'danger',
    'CopilotChat自动化': 'primary',
    '其他': 'info'
  }
  return typeMap[category] || 'info'
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

const getImageUrl = (filePath: string) => {
  if (filePath.startsWith('http')) {
    return filePath
  }
  // 使用配置的API基础URL构建图片访问路径
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'
  return `${apiBaseUrl}/api/custom-templates/image/${filePath}`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // 可以在这里添加默认图片或图标
}
</script>

<style scoped lang="scss">
.custom-template-list {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .page-description {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;

          &.template {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.sequence {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.execution {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .stat-info {
          .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-row {
        align-items: center;
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .template-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .template-icon {
        color: #409eff;
      }
    }

    .confidence-text {
      margin-left: 8px;
      font-size: 12px;
      color: #606266;
    }

    .text-muted {
      color: #c0c4cc;
    }

    .template-name {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .template-preview {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid #e4e7ed;
      flex-shrink: 0;
    }

    .template-preview img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .template-icon {
      color: #409EFF;
      font-size: 16px;
    }

    .template-cards {
      .template-card {
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.selected {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .template-card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .template-info {
            flex: 1;

            h3 {
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }
          }
        }

        .template-content {
          .template-image {
            width: 100%;
            height: 120px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #e4e7ed;
            margin-bottom: 12px;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .template-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .template-placeholder {
            width: 100%;
            height: 120px;
            border-radius: 6px;
            border: 1px dashed #d9d9d9;
            margin-bottom: 12px;
            background: #fafafa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #c0c4cc;
            font-size: 14px;
          }

          .template-placeholder .el-icon {
            font-size: 32px;
            margin-bottom: 8px;
          }

          .template-description {
            margin: 0 0 16px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .template-stats {
            margin-bottom: 16px;

            .stat-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .stat-label {
                font-size: 12px;
                color: #909399;
                min-width: 60px;
              }

              .stat-value {
                font-size: 12px;
                color: #303133;
                font-weight: 500;
              }

              .el-progress {
                flex: 1;
              }
            }
          }

          .template-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
          }
        }

        .template-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-template-list {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-actions {
        justify-content: flex-start;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .search-card {
      .search-form {
        .el-col {
          margin-bottom: 12px;
        }
      }
    }

    .template-cards {
      .el-col {
        span: 24 !important;
      }
    }
  }
}
</style>
