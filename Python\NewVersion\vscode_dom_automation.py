#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VSCode DOM自动化控制器
通过Chrome调试协议控制VSCode的Copilot聊天功能
"""

import os
import sys
import time
import json
import websocket
import requests
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    SELENIUM_AVAILABLE = True
except ImportError:
    print("⚠️ Selenium未安装，DOM自动化功能将受限")
    SELENIUM_AVAILABLE = False

# WebDriver Manager导入
try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    print("⚠️ webdriver-manager未安装，将尝试使用系统ChromeDriver")
    WEBDRIVER_MANAGER_AVAILABLE = False


class VSCodeDOMAutomation:
    """VSCode DOM自动化控制器"""

    def __init__(self):
        """初始化DOM自动化控制器"""
        self.driver = None
        self.ws = None
        self.is_connected = False
        self.debug_port = 9222
        
        print("VSCode DOM自动化控制器初始化完成")

    def get_vscode_status(self) -> Dict:
        """获取VSCode当前状态"""
        js_code = """
        ({
            title: document.title,
            url: window.location.href,
            copilotPanel: !!document.querySelector('#workbench\\.panel\\.chat'),
            monacoEditors: document.querySelectorAll('.monaco-editor').length,
            textareas: document.querySelectorAll('textarea').length,
            chatMessages: document.querySelectorAll('.monaco-list-row').length,
            activeEditor: !!document.querySelector('.monaco-editor.focused'),
            sidebarVisible: !!document.querySelector('.sidebar.visible'),
            timestamp: new Date().toISOString()
        })
        """

        result = self.execute_javascript(js_code)
        if result.get("success"):
            return result.get("result", {})
        return {}

    def click_button_by_text(self, button_text: str) -> bool:
        """点击包含指定文本的按钮"""
        js_code = f"""
        (() => {{
            const buttons = Array.from(document.querySelectorAll('button, a.action-label, .action-item'));
            const targetButton = buttons.find(btn =>
                btn.textContent?.includes('{button_text}') ||
                btn.title?.includes('{button_text}') ||
                btn.ariaLabel?.includes('{button_text}')
            );

            if (targetButton) {{
                targetButton.click();
                return {{ success: true, text: targetButton.textContent || targetButton.title || targetButton.ariaLabel }};
            }}
            return {{ success: false, error: "按钮未找到: {button_text}" }};
        }})()
        """

        result = self.execute_javascript(js_code)
        if result.get("success"):
            result_data = result.get("result", {})
            if result_data.get("success"):
                print(f"✅ 点击按钮成功: {result_data.get('text', button_text)}")
                return True

        print(f"❌ 点击按钮失败: {button_text}")
        return False

    def get_active_editor_content(self, max_length: int = 1000) -> str:
        """获取当前活动编辑器的内容"""
        js_code = f"""
        (() => {{
            const activeEditor = document.querySelector('.monaco-editor.focused .view-lines');
            if (activeEditor) {{
                const content = activeEditor.textContent || '';
                return {{
                    success: true,
                    content: content.substring(0, {max_length}),
                    totalLength: content.length,
                    truncated: content.length > {max_length}
                }};
            }}
            return {{ success: false, error: "没有活动的编辑器" }};
        }})()
        """

        result = self.execute_javascript(js_code)
        if result.get("success"):
            result_data = result.get("result", {})
            if result_data.get("success"):
                return result_data.get("content", "")

        return ""
    
    def _check_debug_port(self, debug_port: int) -> bool:
        """检查调试端口是否可用"""
        try:
            import requests
            response = requests.get(f"http://localhost:{debug_port}/json", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _find_local_chromedriver(self) -> str:
        """查找本地ChromeDriver"""
        import os
        from pathlib import Path
        
        # 可能的ChromeDriver位置
        possible_paths = [
            os.path.join(os.path.dirname(__file__), "chromedriver.exe"),
            os.path.join(os.path.dirname(__file__), "drivers", "chromedriver.exe"),
            os.path.join(os.path.dirname(__file__), "chromedriver"),
            os.path.join(os.path.dirname(__file__), "drivers", "chromedriver"),
        ]
        
        # 添加父目录的drivers路径
        parent_dir = os.path.dirname(os.path.dirname(__file__))
        possible_paths.extend([
            os.path.join(parent_dir, "drivers", "chromedriver.exe"),
            os.path.join(parent_dir, "chromedriver.exe"),
        ])
        
        # 添加webdriver-manager的路径
        wdm_base = os.path.expanduser("~/.wdm/drivers/chromedriver")
        if os.path.exists(wdm_base):
            for root, dirs, files in os.walk(wdm_base):
                for file in files:
                    if file == "chromedriver.exe" or file == "chromedriver":
                        possible_paths.append(os.path.join(root, file))
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"找到本地ChromeDriver: {path}")
                return path
        
        return None
    
    def _print_selenium_help(self, debug_port: int):
        """打印Selenium帮助信息"""
        print("💡 解决方案:")
        print("1. 确保Chrome浏览器已安装")
        print("2. 安装webdriver-manager: pip install webdriver-manager")
        print("3. 或手动下载ChromeDriver:")
        print("   - 访问: https://chromedriver.chromium.org/")
        print("   - 下载与Chrome版本匹配的ChromeDriver")
        print("   - 将chromedriver.exe放到Python脚本目录或PATH中")
        print(f"4. 确保VSCode已启用调试端口:")
        print(f"   code --remote-debugging-port={debug_port}")
        print("5. 运行修复脚本: python start_vscode_debug.bat")
    
    def connect_via_selenium(self, debug_port: int = 9222) -> bool:
        """
        通过Selenium连接到VSCode Chrome实例

        Args:
            debug_port: Chrome调试端口

        Returns:
            是否连接成功
        """
        # 首先检查调试端口是否可用
        if not self._check_debug_port(debug_port):
            print(f"❌ 调试端口 {debug_port} 不可用")
            print("💡 请先启动VSCode调试模式:")
            print(f"   code --remote-debugging-port={debug_port}")
            return False
        
        try:
            options = Options()
            options.add_experimental_option("debuggerAddress", f"localhost:{debug_port}")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-extensions")

            # 方法1: 优先使用本地下载的正确版本ChromeDriver
            local_driver_path = self._find_local_chromedriver()
            if local_driver_path:
                try:
                    service = Service(local_driver_path)
                    self.driver = webdriver.Chrome(service=service, options=options)
                    print(f"✅ 使用本地ChromeDriver连接成功: {local_driver_path}")
                    self.is_connected = True
                    return True
                except Exception as local_error:
                    print(f"❌ 本地ChromeDriver失败: {local_error}")

            # 方法2: 尝试使用系统PATH中的ChromeDriver
            print("尝试使用系统PATH中的ChromeDriver...")
            try:
                self.driver = webdriver.Chrome(options=options)
                print("✅ 使用系统ChromeDriver连接成功")
                self.is_connected = True
                return True
            except Exception as path_error:
                print(f"❌ 系统ChromeDriver失败: {path_error}")

            # 方法3: 最后尝试webdriver-manager（可能版本不匹配）
            if WEBDRIVER_MANAGER_AVAILABLE:
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service, options=options)
                    print("✅ 使用webdriver-manager连接成功")
                    self.is_connected = True
                    return True
                except Exception as wm_error:
                    print(f"⚠️ webdriver-manager失败: {wm_error}")

            # 所有方法都失败
            print("❌ 所有ChromeDriver方法都失败")
            return False

        except Exception as e:
            print(f"❌ Selenium连接失败: {e}")
            self._print_selenium_help(debug_port)
            return False

    def connect_via_websocket(self, debug_port: int = 9222) -> bool:
        """
        通过WebSocket连接到VSCode Chrome实例

        Args:
            debug_port: Chrome调试端口

        Returns:
            是否连接成功
        """
        try:
            # 获取可用的标签页
            response = requests.get(f"http://localhost:{debug_port}/json")
            tabs = response.json()

            if not tabs:
                print("❌ 没有找到可用的标签页")
                return False

            # 连接到第一个标签页
            ws_url = tabs[0]['webSocketDebuggerUrl']
            self.ws = websocket.create_connection(ws_url)
            self.is_connected = True
            print(f"✅ 通过WebSocket连接到VSCode成功 (端口: {debug_port})")
            return True

        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None

            if self.ws:
                self.ws.close()
                self.ws = None

            self.is_connected = False
            print("✅ VSCode DOM自动化连接已断开")
        except Exception as e:
            print(f"⚠️ 断开连接时出错: {e}")

    def execute_javascript(self, js_code: str) -> Dict:
        """
        执行JavaScript代码

        Args:
            js_code: JavaScript代码

        Returns:
            执行结果
        """
        if not self.is_connected:
            return {"success": False, "error": "未连接到VSCode"}

        if self.driver:
            return self._execute_js_selenium(js_code)
        elif self.ws:
            return self._execute_js_websocket(js_code)
        else:
            return {"success": False, "error": "没有可用的连接方式"}

    def _execute_js_selenium(self, js_code: str) -> Dict:
        """通过Selenium执行JavaScript"""
        try:
            # 如果代码已经包含return语句，直接执行；否则添加return
            if js_code.strip().startswith('return ') or 'return ' in js_code:
                result = self.driver.execute_script(js_code)
            else:
                result = self.driver.execute_script(f"return {js_code}")
            return {"success": True, "result": result}
        except Exception as e:
            print(f"JavaScript执行失败: {e}")
            return {"success": False, "error": str(e)}

    def _execute_js_websocket(self, js_code: str) -> Dict:
        """通过WebSocket执行JavaScript"""
        try:
            import json

            command = {
                "id": 1,
                "method": "Runtime.evaluate",
                "params": {
                    "expression": js_code,
                    "returnByValue": True
                }
            }

            self.ws.send(json.dumps(command))
            response = json.loads(self.ws.recv())

            if "result" in response:
                result_data = response["result"]
                if "result" in result_data:
                    # 检查是否有value字段
                    if "value" in result_data["result"]:
                        return {"success": True, "result": result_data["result"]["value"]}
                    else:
                        # 如果没有value字段，返回整个result对象
                        return {"success": True, "result": result_data["result"]}
                elif "exceptionDetails" in result_data:
                    error_msg = result_data["exceptionDetails"].get("text", "JavaScript执行异常")
                    return {"success": False, "error": error_msg}
                else:
                    return {"success": False, "error": "未知的执行结果格式"}
            else:
                return {"success": False, "error": "JavaScript执行失败"}

        except Exception as e:
            print(f"WebSocket JavaScript执行失败: {e}")
            return {"success": False, "error": str(e)}

    def find_copilot_chat_input(self) -> Optional[Dict]:
        """查找Copilot聊天输入框"""
        js_code = """
        // 尝试多种可能的选择器，包括Monaco编辑器和Copilot特定元素
        const selectors = [
            'textarea.inputarea.monaco-mouse-cursor-text',
            'textarea[data-mprt="7"]',
            'textarea[role="textbox"][aria-roledescription="编辑器"]',
            '.chat-input-container textarea',
            '.interactive-input-editor textarea',
            '[data-testid="chat-input"]',
            '.copilot-chat-input',
            'textarea[placeholder*="Ask Copilot"]',
            'textarea[placeholder*="Type a message"]',
            '.monaco-inputbox textarea',
            '.monaco-scrollable-element.editor-scrollable',
            '.view-lines.monaco-mouse-cursor-text',
            '.monaco-editor .view-lines',
            '.lines-content.monaco-editor-background'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                // 检查是否是可编辑的Monaco编辑器
                const isMonacoEditor = element.classList.contains('view-lines') ||
                                     element.classList.contains('monaco-scrollable-element') ||
                                     element.classList.contains('lines-content');

                return {
                    found: true,
                    selector: selector,
                    tagName: element.tagName,
                    className: element.className,
                    placeholder: element.placeholder || '',
                    id: element.id || '',
                    rect: element.getBoundingClientRect(),
                    isMonacoEditor: isMonacoEditor,
                    contentEditable: element.contentEditable || 'false'
                };
            }
        }

        // 特殊处理：查找包含Copilot相关类名的Monaco编辑器
        const monacoElements = document.querySelectorAll('.monaco-editor, .monaco-scrollable-element');
        for (const element of monacoElements) {
            const parent = element.closest('[class*="copilot"], [class*="chat"], [id*="copilot"], [id*="chat"]');
            if (parent || element.textContent.includes('添加') || element.textContent.includes('上下文')) {
                return {
                    found: true,
                    selector: 'monaco-editor-copilot',
                    tagName: element.tagName,
                    className: element.className,
                    placeholder: '检测到Monaco编辑器',
                    id: element.id || '',
                    rect: element.getBoundingClientRect(),
                    isMonacoEditor: true,
                    contentEditable: 'true'
                };
            }
        }

        return { found: false };
        """

        result = self.execute_javascript(js_code)
        if result.get("success"):
            return result.get("result")
        return None

    def send_message_to_copilot(self, message: str) -> bool:
        """
        发送消息到Copilot聊天

        Args:
            message: 要发送的消息

        Returns:
            是否发送成功
        """
        js_code = f"""
        (async function() {{
        // 查找输入框
        const inputSelectors = [
            'textarea.inputarea.monaco-mouse-cursor-text',
            'textarea[data-mprt="7"]',
            'textarea[role="textbox"][aria-roledescription="编辑器"]',
            '.chat-input-container textarea',
            '.interactive-input-editor textarea',
            '[data-testid="chat-input"]',
            '.copilot-chat-input',
            'textarea[placeholder*="Ask Copilot"]',
            'textarea[placeholder*="Type a message"]'
        ];

        let inputElement = null;
        for (const selector of inputSelectors) {{
            const element = document.querySelector(selector);
            if (element) {{
                inputElement = element;
                break;
            }}
        }}

        if (!inputElement) {{
            return {{ success: false, error: "未找到输入框" }};
        }}

        // 设置消息内容 - 针对Monaco编辑器的特殊处理
        inputElement.focus();

        // 清空现有内容
        inputElement.value = '';
        inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));

        // 设置新内容
        inputElement.value = `{message}`;

        // 触发多种事件确保Monaco编辑器识别
        inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
        inputElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
        inputElement.dispatchEvent(new KeyboardEvent('keydown', {{ bubbles: true }}));
        inputElement.dispatchEvent(new KeyboardEvent('keyup', {{ bubbles: true }}));

        // 尝试通过模拟打字的方式
        const event = new Event('input', {{ bubbles: true, cancelable: true }});
        event.inputType = 'insertText';
        event.data = `{message}`;
        inputElement.dispatchEvent(event);

        // 等待一下确保内容设置完成
        await new Promise(resolve => setTimeout(resolve, 500));

        // 查找发送按钮
        const sendSelectors = [
            'a.action-label.codicon.codicon-send',
            'a[aria-label*="发送"]',
            'a[aria-label*="Send"]',
            '.chat-execute-toolbar a.codicon-send',
            '[data-testid="send-button"]',
            '.copilot-send-button',
            'button[title*="Send"]',
            'button[aria-label*="Send"]'
        ];

        let sendButton = null;
        for (const selector of sendSelectors) {{
            const element = document.querySelector(selector);
            if (element) {{
                sendButton = element;
                break;
            }}
        }}

        if (sendButton) {{
            sendButton.click();
        }} else {{
            // 尝试按Enter键
            const enterEvent = new KeyboardEvent('keydown', {{
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                bubbles: true
            }});
            inputElement.dispatchEvent(enterEvent);
        }}

        return {{ success: true, message: "消息已发送" }};
        }})();
        """

        result = self.execute_javascript(js_code)
        if result and result.get("success"):
            result_data = result.get("result")
            if result_data and result_data.get("success"):
                print(f"✅ 消息已发送到Copilot: {message}")
                return True

        # JavaScript方法失败，尝试Selenium直接操作
        print("JavaScript方法失败，尝试Selenium直接操作...")
        return self._send_message_via_selenium(message)

    def _send_message_via_selenium(self, message: str) -> bool:
        """通过Selenium直接操作发送消息 - 使用Monaco编辑器的真实机制"""
        try:
            import time

            # 方法1: 直接操作Monaco编辑器的view-lines
            success = self._send_via_monaco_viewlines(message)
            if success:
                return True

            # 方法2: 传统的textarea方法作为备选
            return self._send_via_traditional_textarea(message)

        except Exception as e:
            print(f"❌ Selenium直接操作失败: {e}")
            return False

    def _send_via_monaco_viewlines(self, message: str) -> bool:
        """通过Monaco编辑器的view-lines机制发送消息"""
        try:
            # 查找view-lines容器
            js_code = f"""
            // 查找Monaco编辑器的view-lines容器
            const viewLines = document.querySelector('.view-lines.monaco-mouse-cursor-text');
            if (!viewLines) {{
                return {{ success: false, error: "未找到view-lines容器" }};
            }}

            // 清空现有内容
            viewLines.innerHTML = '';

            // 创建新的view-line元素来显示输入的文本
            const viewLine = document.createElement('div');
            viewLine.style.top = '8px';
            viewLine.style.height = '20px';
            viewLine.className = 'view-line';

            const span1 = document.createElement('span');
            const span2 = document.createElement('span');
            span2.className = 'mtk1';
            span2.textContent = `{message}`;

            span1.appendChild(span2);
            viewLine.appendChild(span1);
            viewLines.appendChild(viewLine);

            // 同时更新对应的textarea值
            const textarea = document.querySelector('textarea.inputarea.monaco-mouse-cursor-text');
            if (textarea) {{
                textarea.value = `{message}`;

                // 触发必要的事件
                const inputEvent = new Event('input', {{ bubbles: true }});
                textarea.dispatchEvent(inputEvent);

                const changeEvent = new Event('change', {{ bubbles: true }});
                textarea.dispatchEvent(changeEvent);
            }}

            // 等待一下确保内容设置完成
            await new Promise(resolve => setTimeout(resolve, 500));

            // 查找并点击发送按钮
            const sendButton = document.querySelector('a.action-label.codicon.codicon-send, a[aria-label*="发送"], a[aria-label*="Send"]');
            if (sendButton) {{
                sendButton.click();
                return {{ success: true, method: "view-lines + 发送按钮" }};
            }} else {{
                // 尝试按Enter键
                if (textarea) {{
                    const enterEvent = new KeyboardEvent('keydown', {{
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        bubbles: true
                    }});
                    textarea.dispatchEvent(enterEvent);
                    return {{ success: true, method: "view-lines + Enter键" }};
                }}
            }}

            return {{ success: false, error: "未找到发送方式" }};
            """

            result = self.execute_javascript(f"(async function() {{ {js_code} }})()")

            if result and result.get("success"):
                result_data = result.get("result")
                if result_data and result_data.get("success"):
                    method = result_data.get("method", "Monaco view-lines")
                    print(f"✅ 通过{method}发送消息")
                    return True

            return False

        except Exception as e:
            print(f"❌ Monaco view-lines方法失败: {e}")
            return False

    def _send_via_traditional_textarea(self, message: str) -> bool:
        """传统的textarea输入方法作为备选"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.common.keys import Keys
            import time

            # 查找输入框
            selectors = [
                "textarea.inputarea.monaco-mouse-cursor-text",
                "textarea[data-mprt='7']",
                "textarea[role='textbox'][aria-roledescription='编辑器']"
            ]

            input_element = None
            for selector in selectors:
                try:
                    input_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if input_element.is_displayed():
                        break
                except:
                    continue

            if not input_element:
                print("❌ 未找到textarea输入框")
                return False

            # 使用JavaScript来操作
            self.driver.execute_script("arguments[0].focus();", input_element)
            time.sleep(0.5)
            self.driver.execute_script("arguments[0].value = arguments[1];", input_element, message)

            # 触发输入事件
            self.driver.execute_script("""
                var element = arguments[0];
                var inputEvent = new Event('input', { bubbles: true });
                element.dispatchEvent(inputEvent);
                var changeEvent = new Event('change', { bubbles: true });
                element.dispatchEvent(changeEvent);
            """, input_element)
            time.sleep(1)

            # 尝试按Enter键发送
            input_element.send_keys(Keys.ENTER)
            print("✅ 通过传统textarea + Enter键发送消息")
            return True

        except Exception as e:
            print(f"❌ 传统textarea方法失败: {e}")
            return False
