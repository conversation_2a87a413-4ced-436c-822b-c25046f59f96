<template>
  <div class="requirement-decompose-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Operation /></el-icon>
            需求分解
          </h1>
          <p class="page-description">
            将需求文档智能分解为具体的开发步骤
          </p>
        </div>
      </div>
    </div>

    <!-- 分解向导 -->
    <div class="decompose-wizard">
      <el-card shadow="never">
        <el-steps :active="currentStep" align-center>
          <el-step title="选择项目" description="选择要分解需求的项目" />
          <el-step title="选择需求" description="选择需求文档或输入需求内容" />
          <el-step title="配置选项" description="配置分解参数和选项" />
          <el-step title="执行分解" description="AI智能分解需求" />
          <el-step title="查看结果" description="查看分解结果和开发步骤" />
        </el-steps>
      </el-card>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1: 选择项目 -->
      <el-card v-if="currentStep === 0" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Folder /></el-icon>
            <span>选择项目</span>
          </div>
        </template>

        <div class="project-selection">
          <el-row :gutter="16">
            <el-col
              v-for="project in projects"
              :key="project.id"
              :span="8"
            >
              <div
                class="project-card"
                :class="{ active: selectedProject?.id === project.id }"
                @click="selectProject(project)"
              >
                <div class="project-info">
                  <h3>{{ project.name }}</h3>
                  <p>{{ project.description || '暂无描述' }}</p>
                  <div class="project-stats">
                    <span>需求: {{ (project as any).requirementCount || 0 }}</span>
                    <span>成员: {{ (project as any).memberCount || 0 }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-empty v-if="projects.length === 0" description="暂无项目">
            <el-button type="primary" @click="navigateToProjects">
              创建项目
            </el-button>
          </el-empty>
        </div>
      </el-card>

      <!-- 步骤2: 选择需求 -->
      <el-card v-if="currentStep === 1" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>选择需求</span>
          </div>
        </template>

        <div class="requirement-selection">
          <el-radio-group v-model="decomposeMode" class="mode-selection">
            <el-radio label="document">基于需求文档分解</el-radio>
            <el-radio label="content">基于需求内容分解</el-radio>
          </el-radio-group>

          <!-- 需求文档选择 -->
          <div v-if="decomposeMode === 'document'" class="document-selection">
            <el-table
              :data="requirements"
              @row-click="selectRequirement"
              highlight-current-row
              :current-row-key="selectedRequirement?.id"
              row-key="id"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="title" label="需求标题" min-width="200" />
              <el-table-column prop="priority" label="优先级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getPriorityType(row.priority)">
                    {{ row.priority }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="创建时间" width="150">
                <template #default="{ row }">
                  {{ formatTime(row.createdTime) }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 需求内容输入 -->
          <div v-if="decomposeMode === 'content'" class="content-input">
            <el-input
              v-model="requirementContent"
              type="textarea"
              :rows="12"
              placeholder="请输入需求描述...

示例：
# 用户管理模块

## 功能需求
1. 用户注册和登录
2. 用户信息管理
3. 权限控制

## 技术要求
- 前端使用Vue.js
- 后端使用.NET Core
- 数据库使用SQL Server"
              show-word-limit
              maxlength="5000"
            />
          </div>
        </div>
      </el-card>

      <!-- 步骤3: 配置选项 -->
      <el-card v-if="currentStep === 2" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>配置选项</span>
          </div>
        </template>

        <div class="options-config">
          <el-form :model="decomposeOptions" label-width="120px">
            <el-form-item label="技术栈">
              <el-select v-model="decomposeOptions.technologyStack" style="width: 300px">
                <el-option label="Vue.js + .NET Core" value="vue-dotnet" />
                <el-option label="React + Node.js" value="react-node" />
                <el-option label="Vue.js + Python" value="vue-python" />
                <el-option label="通用" value="general" />
              </el-select>
            </el-form-item>

            <el-form-item label="分解粒度">
              <el-radio-group v-model="decomposeOptions.granularity">
                <el-radio label="Coarse">粗粒度（大模块）</el-radio>
                <el-radio label="Medium">中等粒度（推荐）</el-radio>
                <el-radio label="Fine">细粒度（详细步骤）</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="包含步骤">
              <el-checkbox-group v-model="includeOptions">
                <el-checkbox label="test">测试步骤</el-checkbox>
                <el-checkbox label="documentation">文档步骤</el-checkbox>
                <el-checkbox label="dependencies">自动分析依赖</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="最大步骤数">
              <el-input-number
                v-model="decomposeOptions.maxStepCount"
                :min="10"
                :max="500"
                :step="10"
                :precision="0"
                controls
                controls-position="right"
                style="width: 200px"
                placeholder="请输入步骤数"
              />
            </el-form-item>

            <el-form-item label="AI供应商" required>
              <el-select
                v-model="decomposeOptions.aiProvider"
                placeholder="选择AI供应商"
                :loading="loadingAIProviders"
                style="width: 300px"
              >
                <el-option
                  v-for="provider in aiProviders"
                  :key="provider.id"
                  :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
                  :value="provider.id"
                  :disabled="!provider.isActive"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>{{ provider.modelName }}</span>
                    <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 步骤4: 执行分解 -->
      <el-card v-if="currentStep === 3" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Loading /></el-icon>
            <span>执行分解</span>
          </div>
        </template>

        <div class="decompose-execution">
          <div class="execution-status">
            <el-result
              :icon="(decomposing ? 'info' : (decomposeResult?.success ? 'success' : 'error')) as any"
              :title="getExecutionTitle()"
              :sub-title="getExecutionSubTitle()"
            >
              <template #extra>
                <el-button
                  v-if="!decomposing && !decomposeResult"
                  type="primary"
                  @click="executeDecompose"
                  size="large"
                >
                  开始分解
                </el-button>
                <el-button
                  v-if="decomposeResult?.success"
                  type="primary"
                  @click="nextStep"
                  size="large"
                >
                  查看结果
                </el-button>
                <el-button
                  v-if="decomposeResult && !decomposeResult.success"
                  @click="prevStep"
                  size="large"
                >
                  重新配置
                </el-button>
              </template>
            </el-result>
          </div>
        </div>
      </el-card>

      <!-- 步骤5: 查看结果 -->
      <el-card v-if="currentStep === 4" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><CircleCheck /></el-icon>
            <span>分解结果</span>
          </div>
        </template>

        <div class="decompose-result" v-if="decomposeResult?.success">
          <!-- 统计信息 -->
          <div class="result-stats">
            <el-row :gutter="16">
              <el-col :span="6">
                <el-statistic title="总步骤数" :value="decomposeResult.data?.statistics.totalSteps || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="开发步骤" :value="decomposeResult.data?.statistics.developmentSteps || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="测试步骤" :value="decomposeResult.data?.statistics.testSteps || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="预估工时" :value="decomposeResult.data?.statistics.estimatedTotalHours || 0" suffix="小时" />
              </el-col>
            </el-row>
          </div>

          <!-- 步骤列表 -->
          <div class="result-steps">
            <div class="steps-header">
              <h3>生成的开发步骤</h3>
              <!-- 调试信息 -->
              <div style="background: #f0f0f0; padding: 8px; margin: 8px 0; font-size: 12px;">
                调试: isPreview = {{ decomposeResult?.isPreview }}, success = {{ decomposeResult?.success }}
              </div>
              <div class="steps-actions" v-if="decomposeResult?.isPreview">
                <el-alert
                  title="预览模式"
                  description="这些步骤尚未保存到数据库，请确认后保存"
                  type="warning"
                  :closable="false"
                  style="margin-bottom: 16px;"
                />
                <el-button
                  type="primary"
                  :loading="confirmingSave"
                  @click="confirmAndSaveSteps"
                  size="large"
                >
                  <el-icon><Check /></el-icon>
                  确认并保存步骤
                </el-button>
                <el-button @click="regenerateSteps" size="large">
                  <el-icon><Refresh /></el-icon>
                  重新生成
                </el-button>
              </div>
              <div v-else-if="decomposeResult?.success && !decomposeResult?.isPreview">
                <el-alert
                  title="已保存"
                  description="步骤已成功保存到数据库"
                  type="success"
                  :closable="false"
                  style="margin-bottom: 16px;"
                />
              </div>
            </div>
            <div class="steps-grid">
              <div
                v-for="(step, index) in decomposeResult.data?.steps"
                :key="step.id"
                class="step-card-wrapper"
              >
                <StepCard
                  :step="step"
                  @click="viewStepDetail"
                />
                <div v-if="decomposeResult?.isPreview" class="step-actions">
                  <el-button
                    type="primary"
                    size="small"
                    class="edit-step-btn"
                    @click="editStep(index)"
                    :icon="Edit"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    class="delete-step-btn"
                    @click="removeStep(index)"
                    :icon="Delete"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="prevStep" :disabled="currentStep === 0">
        上一步
      </el-button>
      <el-button
        type="primary"
        @click="nextStep"
        :disabled="!canNextStep"
        v-if="currentStep < 4"
      >
        下一步
      </el-button>
      <el-button
        type="success"
        @click="viewAllSteps"
        v-if="currentStep === 4"
      >
        查看所有步骤
      </el-button>
    </div>

    <!-- 编辑步骤对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑开发步骤"
      width="60%"
      :before-close="handleEditClose"
    >
      <el-form
        ref="editFormRef"
        :model="editingStepData"
        :rules="editFormRules"
        label-width="120px"
      >
        <el-form-item label="步骤名称" prop="stepName">
          <el-input v-model="editingStepData.stepName" placeholder="请输入步骤名称" />
        </el-form-item>

        <el-form-item label="步骤描述" prop="stepDescription">
          <el-input
            v-model="editingStepData.stepDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入步骤描述"
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="步骤类型" prop="stepType">
              <el-select v-model="editingStepData.stepType" placeholder="选择步骤类型">
                <el-option label="开发" value="Development" />
                <el-option label="测试" value="Testing" />
                <el-option label="文档" value="Documentation" />
                <el-option label="部署" value="Deployment" />
                <el-option label="配置" value="Configuration" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="editingStepData.priority" placeholder="选择优先级">
                <el-option label="高" value="High" />
                <el-option label="中" value="Medium" />
                <el-option label="低" value="Low" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="预估工时" prop="estimatedHours">
              <el-input-number
                v-model="editingStepData.estimatedHours"
                :min="0"
                :step="0.5"
                placeholder="预估工时（小时）"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术栈" prop="technologyStack">
              <el-input v-model="editingStepData.technologyStack" placeholder="技术栈" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="文件类型" prop="fileType">
              <el-input v-model="editingStepData.fileType" placeholder="文件类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件路径" prop="filePath">
              <el-input v-model="editingStepData.filePath" placeholder="文件路径" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="组件类型" prop="componentType">
          <el-input v-model="editingStepData.componentType" placeholder="组件类型" />
        </el-form-item>

        <el-form-item label="AI提示词" prop="aiPrompt">
          <el-input
            v-model="editingStepData.aiPrompt"
            type="textarea"
            :rows="2"
            placeholder="AI提示词"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditClose">取消</el-button>
          <el-button type="primary" @click="saveStepEdit" :loading="savingEdit">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Operation, Folder, Document, Setting, Loading, CircleCheck, Check, Refresh, Delete, Edit
} from '@element-plus/icons-vue'
import { ProjectService } from '@/services/project'
import { RequirementService } from '@/services/requirement'
import { developmentService } from '@/services/development'
import { AIProviderService } from '@/services/aiProvider'
import StepCard from '@/components/development/StepCard.vue'
import type { ProjectSummary, RequirementSummary } from '@/types'
import type {
  DecomposeRequirementRequest,
  DecomposeContentRequest,
  DecompositionResult
} from '@/types/development'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const currentStep = ref(0)
const decomposing = ref(false)
const confirmingSave = ref(false)
const projects = ref<ProjectSummary[]>([])
const requirements = ref<RequirementSummary[]>([])
const selectedProject = ref<ProjectSummary>()
const selectedRequirement = ref<RequirementSummary>()
const decomposeMode = ref<'document' | 'content'>('document')
const requirementContent = ref('')
const decomposeResult = ref<DecompositionResult>()
const showEditDialog = ref(false)
const savingEdit = ref(false)
const editingStepIndex = ref(-1)
const editFormRef = ref()

// AI供应商相关
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// 编辑表单数据
const editingStepData = ref({
  stepName: '',
  stepDescription: '',
  stepType: '',
  priority: '',
  estimatedHours: 0,
  technologyStack: '',
  fileType: '',
  filePath: '',
  componentType: '',
  aiPrompt: ''
})

const decomposeOptions = reactive({
  technologyStack: 'vue-dotnet',
  granularity: 'Medium' as 'Coarse' | 'Medium' | 'Fine',
  maxStepCount: 20,
  aiProvider: undefined as number | undefined
})

const includeOptions = ref(['test', 'documentation', 'dependencies'])

// 编辑表单验证规则
const editFormRules = {
  stepName: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' },
    { min: 2, max: 100, message: '步骤名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  stepDescription: [
    { required: true, message: '请输入步骤描述', trigger: 'blur' },
    { min: 5, max: 500, message: '步骤描述长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  stepType: [
    { required: true, message: '请选择步骤类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return !!selectedProject.value
    case 1:
      return decomposeMode.value === 'document'
        ? !!selectedRequirement.value
        : requirementContent.value.trim().length > 0
    case 2:
      return true
    case 3:
      return !!decomposeResult.value?.success
    default:
      return false
  }
})

// 方法
const loadProjects = async () => {
  try {
    const result = await ProjectService.getProjects({ pageNumber: 1, pageSize: 100 })
    projects.value = result.items
  } catch (error) {
    ElMessage.error('加载项目列表失败')
  }
}

const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
  } catch (error: any) {
    console.error('加载AI供应商列表失败:', error)
    ElMessage.error('加载AI供应商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}

const loadRequirements = async (projectId: number) => {
  try {
    const result = await RequirementService.getRequirementDocuments(projectId, 1, 100)
    requirements.value = result.items.map(item => ({
      ...item,
      description: item.title || ''
    })) as RequirementSummary[]
  } catch (error) {
    ElMessage.error('加载需求列表失败')
  }
}

const selectProject = (project: ProjectSummary) => {
  selectedProject.value = project
  loadRequirements(project.id)
}

const selectRequirement = (requirement: RequirementSummary) => {
  selectedRequirement.value = requirement
}

const nextStep = () => {
  if (canNextStep.value && currentStep.value < 4) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const executeDecompose = async () => {
  if (!selectedProject.value) return

  if (!decomposeOptions.aiProvider) {
    ElMessage.error('请选择AI供应商')
    return
  }

  try {
    decomposing.value = true

    const options = {
      technologyStack: decomposeOptions.technologyStack,
      granularity: decomposeOptions.granularity,
      includeTestSteps: includeOptions.value.includes('test'),
      includeDocumentationSteps: includeOptions.value.includes('documentation'),
      autoAnalyzeDependencies: includeOptions.value.includes('dependencies'),
      maxStepCount: decomposeOptions.maxStepCount,
      aiProviderConfigId: decomposeOptions.aiProvider
    }

    if (decomposeMode.value === 'document' && selectedRequirement.value) {
      decomposeResult.value = await developmentService.decomposeRequirement(
        selectedRequirement.value.id,
        options as DecomposeRequirementRequest
      )
    } else {
      decomposeResult.value = await developmentService.decomposeRequirementContent(
        selectedProject.value.id,
        {
          ...options,
          requirementContent: requirementContent.value
        } as DecomposeContentRequest
      )
    }

    if (decomposeResult.value.success) {
      console.log('分解结果:', decomposeResult.value) // 调试信息
      ElMessage.success('需求分解成功！')
    } else {
      ElMessage.error(decomposeResult.value.message || '需求分解失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '需求分解失败')
    decomposeResult.value = {
      success: false,
      message: error.message || '需求分解失败'
    }
  } finally {
    decomposing.value = false
  }
}

const getExecutionTitle = () => {
  if (decomposing.value) return '正在分解需求...'
  if (decomposeResult.value?.success) return '分解完成！'
  if (decomposeResult.value && !decomposeResult.value.success) return '分解失败'
  return '准备开始分解'
}

const getExecutionSubTitle = () => {
  if (decomposing.value) return 'AI正在智能分析需求并生成开发步骤'
  if (decomposeResult.value?.success) {
    const count = decomposeResult.value.data?.steps?.length || 0
    return `成功生成 ${count} 个开发步骤`
  }
  if (decomposeResult.value && !decomposeResult.value.success) {
    return decomposeResult.value.message || '请检查配置后重试'
  }
  return '点击下方按钮开始分解需求'
}

const viewStepDetail = (step: any) => {
  // 查看步骤详情
  console.log('查看步骤详情:', step)
}

const viewAllSteps = () => {
  if (selectedProject.value) {
    router.push(`/projects/${selectedProject.value.id}?tab=steps`)
  }
}

const confirmAndSaveSteps = async () => {
  if (!decomposeResult.value?.data || !selectedProject.value) return

  try {
    confirmingSave.value = true

    // 转换步骤数据格式
    const steps = decomposeResult.value.data?.steps?.map(step => ({
      stepName: step.stepName,
      stepDescription: step.stepDescription || '',
      stepType: step.stepType as string,
      priority: step.priority as string,
      estimatedHours: step.estimatedHours,
      technologyStack: step.technologyStack,
      fileType: step.fileType,
      filePath: step.filePath,
      componentType: step.componentType as string,
      aiPrompt: step.aiPrompt,
      stepOrder: step.stepOrder,
      stepGroup: step.stepGroup,
      stepLevel: step.stepLevel,
      parentStepName: (step as any).parentStepName
    })) || []

    // 转换依赖关系数据格式
    const dependencies = decomposeResult.value.data?.dependencies?.map(dep => ({
      stepName: (dep as any).stepName || '',
      dependsOnStepName: (dep as any).dependsOnStepName || '',
      dependencyType: (dep as any).dependencyType || 'Sequential'
    })) || []

    const request = {
      projectId: selectedProject.value.id,
      requirementDocumentId: selectedRequirement.value?.id,
      steps,
      dependencies
    }

    const result = await developmentService.confirmSteps(request)

    if (result.success) {
      // 更新结果为已保存状态
      decomposeResult.value = {
        ...result,
        isPreview: false
      }
      ElMessage.success('步骤已成功保存到数据库！')
    } else {
      ElMessage.error(result.message || '保存步骤失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存步骤失败')
  } finally {
    confirmingSave.value = false
  }
}

const regenerateSteps = () => {
  // 重置到分解配置步骤
  currentStep.value = 2
  decomposeResult.value = undefined
}

const removeStep = (index: number) => {
  if (!decomposeResult.value?.data?.steps) return

  // 删除指定索引的步骤
  decomposeResult.value.data?.steps?.splice(index, 1)

  // 如果没有步骤了，提示用户
  if ((decomposeResult.value.data?.steps?.length || 0) === 0) {
    ElMessage.warning('所有步骤已删除，请重新生成或返回上一步')
  } else {
    ElMessage.success('步骤已删除')
  }
}

const editStep = (index: number) => {
  if (!decomposeResult.value?.data?.steps?.[index]) return

  const step = decomposeResult.value.data.steps[index]
  editingStepIndex.value = index

  // 填充编辑表单
  editingStepData.value = {
    stepName: step.stepName || '',
    stepDescription: step.stepDescription || '',
    stepType: step.stepType || '',
    priority: step.priority || '',
    estimatedHours: step.estimatedHours || 0,
    technologyStack: step.technologyStack || '',
    fileType: step.fileType || '',
    filePath: step.filePath || '',
    componentType: step.componentType || '',
    aiPrompt: step.aiPrompt || ''
  }

  showEditDialog.value = true
}

const handleEditClose = () => {
  if (!savingEdit.value) {
    showEditDialog.value = false
    editingStepIndex.value = -1
  }
}

const saveStepEdit = async () => {
  if (!editFormRef.value) return

  try {
    // 验证表单
    await editFormRef.value.validate()

    savingEdit.value = true

    // 更新步骤数据
    if (decomposeResult.value?.data?.steps?.[editingStepIndex.value]) {
      const step = decomposeResult.value.data.steps[editingStepIndex.value]
      Object.assign(step, editingStepData.value)

      ElMessage.success('步骤编辑成功')
      showEditDialog.value = false
      editingStepIndex.value = -1
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    savingEdit.value = false
  }
}

const navigateToProjects = () => {
  router.push('/projects')
}

// 辅助方法
const getPriorityType = (priority: string): 'success' | 'primary' | 'warning' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'danger'> = {
    Low: 'success',
    Medium: 'primary',
    High: 'warning',
    Critical: 'danger'
  }
  return typeMap[priority] || 'primary'
}

const getStatusType = (status: string): 'warning' | 'success' | 'danger' | 'info' => {
  const typeMap: Record<string, 'warning' | 'success' | 'danger' | 'info'> = {
    Draft: 'info',
    InReview: 'warning',
    Approved: 'success',
    Rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD')
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadAIProviders()
})
</script>

<style scoped>
.requirement-decompose-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.decompose-wizard {
  margin-bottom: 24px;
}

.step-content {
  margin-bottom: 24px;
  min-height: 500px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.project-selection {
  padding: 16px 0;
}

.project-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  height: 140px;
}

.project-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.project-card.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.project-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.project-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.project-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.requirement-selection {
  padding: 16px 0;
}

.mode-selection {
  margin-bottom: 24px;
}

.document-selection {
  margin-top: 16px;
}

.content-input {
  margin-top: 16px;
}

.options-config {
  padding: 16px 0;
}

.decompose-execution {
  padding: 40px 0;
}

.execution-status {
  text-align: center;
}

.decompose-result {
  padding: 16px 0;
}

.result-stats {
  margin-bottom: 32px;
}

.result-steps {
  .steps-header {
    margin-bottom: 16px;

    h3 {
      margin-bottom: 16px;
      color: #303133;
    }

    .steps-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;

      .el-button {
        align-self: flex-start;
      }
    }
  }
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.step-card-wrapper {
  position: relative;
}

.step-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  gap: 8px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.step-actions:hover {
  opacity: 1;
}

.edit-step-btn,
.delete-step-btn {
  min-width: auto;
  padding: 4px 8px;
}

.action-buttons {
  text-align: center;
  padding: 24px 0;
}

.action-buttons .el-button {
  margin: 0 8px;
}
</style>
