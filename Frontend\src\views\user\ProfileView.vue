<template>
  <div class="profile-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="goBack" type="text" size="large">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div class="page-title">
            <el-icon><User /></el-icon>
            <span>个人资料</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="profile-content">
      <el-row :gutter="24">
        <!-- 左侧：头像和基本信息 -->
        <el-col :span="8">
          <el-card class="profile-card">
            <div class="profile-avatar">
              <el-avatar :size="120" :src="getAvatarUrl(userInfo?.avatar)">
                <el-icon size="60"><User /></el-icon>
              </el-avatar>
              <el-upload
                ref="avatarUploadRef"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleAvatarUpload"
                accept="image/jpeg,image/jpg,image/png,image/webp"
                class="avatar-upload"
              >
                <el-button type="primary" size="small" class="upload-btn" :loading="avatarUploading">
                  <el-icon><Camera /></el-icon>
                  {{ avatarUploading ? '上传中...' : '更换头像' }}
                </el-button>
              </el-upload>
            </div>

            <div class="profile-basic">
              <h3>{{ userInfo?.realName || userInfo?.username }}</h3>
              <p class="user-role">{{ getRoleText(userInfo?.role) }}</p>
              <p class="user-email">{{ userInfo?.email }}</p>
              <p class="join-time">
                加入时间：{{ formatDate(userInfo?.createdAt) }}
              </p>
              <p v-if="userInfo?.lastLoginAt" class="last-login">
                最后登录：{{ formatDate(userInfo?.lastLoginAt) }}
              </p>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：详细信息和设置 -->
        <el-col :span="16">
          <el-tabs v-model="activeTab" class="profile-tabs">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>基本信息</span>
                    <el-button
                      v-if="!editMode"
                      type="primary"
                      size="small"
                      @click="toggleEditMode"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <div v-else class="edit-actions">
                      <el-button size="small" @click="cancelEdit">取消</el-button>
                      <el-button
                        type="primary"
                        size="small"
                        :loading="saving"
                        @click="saveProfile"
                      >
                        保存
                      </el-button>
                    </div>
                  </div>
                </template>

                <el-form
                  ref="profileFormRef"
                  :model="profileForm"
                  :rules="profileRules"
                  label-width="100px"
                  class="profile-form"
                >
                  <el-form-item label="用户名">
                    <el-input v-model="profileForm.username" disabled />
                  </el-form-item>

                  <el-form-item label="邮箱">
                    <el-input v-model="profileForm.email" disabled />
                  </el-form-item>

                  <el-form-item label="真实姓名" prop="realName">
                    <el-input
                      v-model="profileForm.realName"
                      :disabled="!editMode"
                      placeholder="请输入真实姓名"
                    />
                  </el-form-item>

                  <el-form-item label="手机号码" prop="phone">
                    <el-input
                      v-model="profileForm.phone"
                      :disabled="!editMode"
                      placeholder="请输入手机号码"
                    />
                  </el-form-item>

                  <el-form-item label="用户角色">
                    <el-tag
                      :type="getRoleTagType(profileForm.role) || undefined"
                    >
                      {{ getRoleText(profileForm.role) }}
                    </el-tag>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-tab-pane>

            <!-- 安全设置 -->
            <el-tab-pane label="安全设置" name="security">
              <el-card>
                <template #header>
                  <span>安全设置</span>
                </template>

                <div class="security-items">
                  <div class="security-item">
                    <div class="item-info">
                      <h4>登录密码</h4>
                      <p>定期更换密码可以提高账户安全性</p>
                    </div>
                    <el-button type="primary" @click="showChangePasswordDialog">
                      修改密码
                    </el-button>
                  </div>

                  <el-divider />

                  <div class="security-item">
                    <div class="item-info">
                      <h4>邮箱验证</h4>
                      <p>
                        邮箱验证状态：
                        <el-tag v-if="userInfo?.emailVerified" type="success">已验证</el-tag>
                        <el-tag v-else type="warning">未验证</el-tag>
                      </p>
                    </div>
                    <el-button
                      v-if="!userInfo?.emailVerified"
                      type="primary"
                      @click="sendVerificationEmail"
                    >
                      发送验证邮件
                    </el-button>
                  </div>

                  <el-divider />

                  <div class="security-item">
                    <div class="item-info">
                      <h4>双因子认证</h4>
                      <p>
                        双因子认证状态：
                        <el-tag v-if="userInfo?.twoFactorEnabled" type="success">已启用</el-tag>
                        <el-tag v-else type="info">未启用</el-tag>
                      </p>
                    </div>
                    <el-button
                      :type="userInfo?.twoFactorEnabled ? 'danger' : 'primary'"
                      @click="toggleTwoFactor"
                    >
                      {{ userInfo?.twoFactorEnabled ? '关闭' : '启用' }}双因子认证
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-tab-pane>

            <!-- 偏好设置 -->
            <el-tab-pane label="偏好设置" name="preferences">
              <el-card>
                <template #header>
                  <span>偏好设置</span>
                </template>

                <el-form label-width="120px" class="preferences-form">
                  <el-form-item label="主题设置">
                    <el-radio-group v-model="themePreference">
                      <el-radio label="light">浅色主题</el-radio>
                      <el-radio label="dark">深色主题</el-radio>
                      <el-radio label="auto">跟随系统</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="语言设置">
                    <el-select v-model="languagePreference" style="width: 200px">
                      <el-option label="简体中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="邮件通知">
                    <el-switch
                      v-model="emailNotification"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </el-form-item>

                  <el-form-item label="系统通知">
                    <el-switch
                      v-model="systemNotification"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="savePreferences">
                      保存偏好设置
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordVisible"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            placeholder="请输入当前密码"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请确认新密码"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="changePasswordVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="changingPassword"
          @click="changePassword"
        >
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { UserService } from '@/services/user'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadInstance, type UploadRequestOptions } from 'element-plus'
import dayjs from 'dayjs'
import {
  ArrowLeft,
  User,
  Camera,
  Edit
} from '@element-plus/icons-vue'
import type {
  User as UserType,
  UpdateUserRequestDto,
  ChangePasswordRequestDto
} from '@/types/user'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('basic')
const editMode = ref(false)
const saving = ref(false)
const changePasswordVisible = ref(false)
const changingPassword = ref(false)
const avatarUploading = ref(false)
const userInfo = ref<UserType | null>(null)

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const avatarUploadRef = ref<UploadInstance>()

// 个人资料表单
const profileForm = reactive<UpdateUserRequestDto & { username: string; email: string; role: string }>({
  username: '',
  email: '',
  role: '',
  realName: '',
  phone: ''
})

// 修改密码表单
const passwordForm = reactive<ChangePasswordRequestDto>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 偏好设置
const themePreference = ref('light')
const languagePreference = ref('zh-CN')
const emailNotification = ref(true)
const systemNotification = ref(true)

// 表单验证规则
const profileRules: FormRules = {
  realName: [
    { max: 50, message: '真实姓名不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const currentUser = computed(() => authStore.user)

// 方法
const goBack = () => {
  router.back()
}

const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    'User': '普通用户',
    'ProjectManager': '项目经理',
    'Developer': '开发者',
    'Tester': '测试员',
    'ProductManager': '产品经理',
    'Admin': '管理员',
    'SuperAdmin': '超级管理员'
  }
  return roleMap[role || ''] || role || '未知'
}

const getRoleTagType = (role?: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' | '' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger' | ''> = {
    'SuperAdmin': 'danger',
    'Admin': 'warning',
    'ProjectManager': 'success',
    'Developer': 'primary',
    'Tester': 'info',
    'ProductManager': 'success',
    'User': ''
  }
  return typeMap[role || ''] || ''
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

const getAvatarUrl = (avatar?: string) => {
  if (!avatar) return ''
  // 如果是完整URL，直接返回
  if (avatar.startsWith('http')) return avatar
  // 如果是相对路径，添加后端服务器地址
  return `https://localhost:61136/${avatar}`
}

const loadUserInfo = async () => {
  try {
    if (currentUser.value?.id) {
      userInfo.value = await UserService.getUser(currentUser.value.id)

      // 填充表单数据
      profileForm.username = userInfo.value.username
      profileForm.email = userInfo.value.email
      profileForm.role = userInfo.value.role
      profileForm.realName = userInfo.value.realName || ''
      profileForm.phone = (userInfo.value as any).phone || ''
    }
  } catch (error: any) {
    console.error('Failed to load user info:', error)
    ElMessage.error(error.message || '加载用户信息失败')
  }
}

const toggleEditMode = () => {
  editMode.value = !editMode.value
  if (!editMode.value) {
    // 取消编辑时恢复原始数据
    loadUserInfo()
  }
}

const cancelEdit = () => {
  editMode.value = false
  loadUserInfo()
}

const saveProfile = async () => {
  if (!profileFormRef.value || !userInfo.value) return

  try {
    const valid = await profileFormRef.value.validate()
    if (!valid) return

    saving.value = true

    const updateData: UpdateUserRequestDto = {
      realName: profileForm.realName,
      phone: profileForm.phone
    }

    const updatedUser = await UserService.updateUser(userInfo.value.id, updateData)

    // 更新本地数据
    userInfo.value = updatedUser

    // 更新认证store中的用户信息
    await authStore.getCurrentUser()

    editMode.value = false
    ElMessage.success('个人资料更新成功')

  } catch (error: any) {
    console.error('Failed to update profile:', error)
    ElMessage.error(error.message || '更新个人资料失败')
  } finally {
    saving.value = false
  }
}

const beforeAvatarUpload = (file: File) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、JPEG、PNG、WEBP 格式的图片')
    return false
  }

  // 检查文件大小（2MB）
  const maxSize = 2 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('头像文件大小不能超过 2MB')
    return false
  }

  return true
}

const handleAvatarUpload = async (options: UploadRequestOptions) => {
  const file = options.file as File

  try {
    avatarUploading.value = true

    const response = await UserService.uploadAvatar(file, (progress) => {
      console.log('上传进度:', progress + '%')
    })

    if (response.success) {
      // 更新本地用户信息
      if (userInfo.value) {
        userInfo.value.avatar = response.data.avatar
      }

      // 更新认证store中的用户信息
      await authStore.getCurrentUser()

      ElMessage.success('头像上传成功')
    } else {
      ElMessage.error('头像上传失败')
    }

  } catch (error: any) {
    console.error('Failed to upload avatar:', error)
    ElMessage.error(error.message || '头像上传失败')
  } finally {
    avatarUploading.value = false
  }
}

const showChangePasswordDialog = () => {
  changePasswordVisible.value = true
  // 清空表单
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}

const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return

    changingPassword.value = true

    await UserService.changePassword(passwordForm)

    changePasswordVisible.value = false
    ElMessage.success('密码修改成功，请重新登录')

    // 密码修改成功后，退出登录
    setTimeout(() => {
      authStore.logout()
    }, 1500)

  } catch (error: any) {
    console.error('Failed to change password:', error)
    ElMessage.error(error.message || '修改密码失败')
  } finally {
    changingPassword.value = false
  }
}

const sendVerificationEmail = () => {
  ElMessage.info('发送验证邮件功能开发中...')
}

const toggleTwoFactor = () => {
  ElMessage.info('双因子认证功能开发中...')
}

const savePreferences = () => {
  ElMessage.success('偏好设置保存成功')
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo()
})
</script>

<style lang="scss" scoped>
.profile-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 16px 24px;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.profile-content {
  flex: 1;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.profile-card {
  text-align: center;

  .profile-avatar {
    margin-bottom: 24px;

    .avatar-upload {
      margin-top: 16px;

      .upload-btn {
        width: 100%;
      }
    }
  }

  .profile-basic {
    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 4px 0;
      color: var(--el-text-color-regular);

      &.user-role {
        font-weight: 500;
        color: var(--el-color-primary);
      }

      &.user-email {
        font-size: 14px;
      }

      &.join-time,
      &.last-login {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .edit-actions {
    display: flex;
    gap: 8px;
  }
}

.profile-form {
  max-width: 600px;
}

.security-items {
  .security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;

    .item-info {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.preferences-form {
  max-width: 600px;
}

// 响应式设计
@media (max-width: 768px) {
  .profile-content {
    padding: 16px;

    :deep(.el-col) {
      margin-bottom: 16px;
    }
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .item-info {
      margin-bottom: 0;
    }
  }
}
</style>
