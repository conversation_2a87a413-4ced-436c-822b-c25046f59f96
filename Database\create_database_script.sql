-- =============================================
-- 项目管理AI系统数据库创建脚本
-- 基于 Backend\ProjectManagement.Core\Entities\ 实体类生成
-- 数据库: ProjectManagementAI
-- 版本: 1.0
-- 创建日期: 2024-12-19
-- =============================================

-- 如果数据库存在则删除
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'ProjectManagementAI')
BEGIN
    ALTER DATABASE ProjectManagementAI SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE ProjectManagementAI;
END
GO

-- 创建数据库（指定支持中文的排序规则）
CREATE DATABASE ProjectManagementAI
COLLATE Chinese_PRC_CI_AS;
GO

USE ProjectManagementAI;
GO

-- =============================================
-- 1. 用户表 (Users) - 基于 User.cs 实体
-- =============================================
CREATE TABLE Users (
    Id int IDENTITY(1,1) NOT NULL,
    Username nvarchar(50) NOT NULL,
    Email nvarchar(100) NOT NULL,
    PasswordHash nvarchar(255) NOT NULL,
    Salt nvarchar(50) NOT NULL,
    RealName nvarchar(50) NULL,
    Phone nvarchar(20) NULL,
    Avatar nvarchar(255) NULL,
    Role nvarchar(50) NOT NULL DEFAULT 'User',
    Status int NOT NULL DEFAULT 1,
    LastLoginTime datetime2 NULL,
    LastLoginIp nvarchar(50) NULL,
    EmailVerified bit NOT NULL DEFAULT 0,
    EmailVerifiedTime datetime2 NULL,
    TwoFactorEnabled bit NOT NULL DEFAULT 0,
    TwoFactorSecret nvarchar(100) NULL,
    Preferences nvarchar(max) NULL,
    RefreshToken nvarchar(255) NULL,
    RefreshTokenExpiryTime datetime2 NULL,
    LastLoginAt datetime2 NULL,
    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,
    
    CONSTRAINT PK_Users PRIMARY KEY (Id),
    CONSTRAINT UK_Users_Username UNIQUE (Username),
    CONSTRAINT UK_Users_Email UNIQUE (Email),
    CONSTRAINT CK_Users_Status CHECK (Status IN (1, 2, 3, 4, 5)),
    CONSTRAINT CK_Users_Role CHECK (Role IN ('User', 'ProjectManager', 'Developer', 'Tester', 'ProductManager', 'Admin', 'SuperAdmin'))
);
GO

-- 用户表索引
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_Username ON Users(Username);
CREATE INDEX IX_Users_Role ON Users(Role);
CREATE INDEX IX_Users_Status ON Users(Status);
CREATE INDEX IX_Users_IsDeleted ON Users(IsDeleted);
GO

-- =============================================
-- 2. 项目表 (Projects) - 基于 Project.cs 实体
-- =============================================
CREATE TABLE Projects (
    Id int IDENTITY(1,1) NOT NULL,
    Name nvarchar(200) NOT NULL,
    Description nvarchar(max) NULL,
    ProjectCode nvarchar(50) NOT NULL,
    OwnerId int NOT NULL,
    Status nvarchar(20) NULL DEFAULT 'Planning',
    Priority nvarchar(10) NULL DEFAULT 'Medium',
    StartDate datetime2 NULL,
    EndDate datetime2 NULL,
    Progress int NOT NULL DEFAULT 0,
    EstimatedHours decimal(10,2) NULL,
    ActualHours decimal(10,2) NULL,
    Budget decimal(18,2) NULL,
    TechnologyStack nvarchar(max) NULL,
    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,
    
    CONSTRAINT PK_Projects PRIMARY KEY (Id),
    CONSTRAINT UK_Projects_ProjectCode UNIQUE (ProjectCode),
    CONSTRAINT FK_Projects_OwnerId FOREIGN KEY (OwnerId) REFERENCES Users(Id),
    CONSTRAINT CK_Projects_Status CHECK (Status IN ('Planning', 'InProgress', 'Testing', 'Deployed', 'Completed')),
    CONSTRAINT CK_Projects_Priority CHECK (Priority IN ('Low', 'Medium', 'High', 'Critical')),
    CONSTRAINT CK_Projects_Progress CHECK (Progress >= 0 AND Progress <= 100)
);
GO

-- 项目表索引
CREATE INDEX IX_Projects_OwnerId ON Projects(OwnerId);
CREATE INDEX IX_Projects_Status ON Projects(Status);
CREATE INDEX IX_Projects_Priority ON Projects(Priority);
CREATE INDEX IX_Projects_ProjectCode ON Projects(ProjectCode);
CREATE INDEX IX_Projects_IsDeleted ON Projects(IsDeleted);
GO

-- =============================================
-- 3. AI模型配置表 (AIModelConfigurations) - 基于 AIModelConfiguration.cs 实体
-- =============================================
CREATE TABLE AIModelConfigurations (
    Id int IDENTITY(1,1) NOT NULL,
    ModelName nvarchar(100) NOT NULL,
    ModelType nvarchar(50) NOT NULL,
    ApiEndpoint nvarchar(500) NULL,
    ApiKey nvarchar(255) NULL,
    ModelParameters nvarchar(max) NULL,
    IsActive bit NULL DEFAULT 1,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),
    UpdatedAt datetime2 NULL DEFAULT GETDATE(),
    
    CONSTRAINT PK_AIModelConfigurations PRIMARY KEY (Id),
    CONSTRAINT CK_AIModelConfigurations_ModelType CHECK (ModelType IN ('RequirementAnalysis', 'CodeGeneration', 'Testing', 'Debugging'))
);
GO

-- AI模型配置表索引
CREATE INDEX IX_AIModelConfigurations_ModelType ON AIModelConfigurations(ModelType);
CREATE INDEX IX_AIModelConfigurations_IsActive ON AIModelConfigurations(IsActive);
GO

-- =============================================
-- 4. 用户AI配置表 (UserAIConfigurations) - 基于 UserAIConfiguration.cs 实体
-- =============================================
CREATE TABLE UserAIConfigurations (
    Id int IDENTITY(1,1) NOT NULL,
    UserId int NOT NULL,
    ProviderName nvarchar(50) NOT NULL,
    ModelName nvarchar(100) NOT NULL,
    ModelType nvarchar(50) NOT NULL,
    ApiEndpoint nvarchar(500) NULL,
    ApiKey nvarchar(255) NULL,
    ModelParameters nvarchar(max) NULL,
    IsActive bit NULL DEFAULT 1,
    IsDefault bit NULL DEFAULT 0,
    DailyTokenLimit int NULL,
    MonthlyTokenLimit int NULL,
    TotalTokensUsed bigint NULL DEFAULT 0,
    TotalRequests int NULL DEFAULT 0,
    SuccessfulRequests int NULL DEFAULT 0,
    FailedRequests int NULL DEFAULT 0,
    LastUsedAt datetime2 NULL,
    EstimatedCost decimal(10,4) NULL DEFAULT 0,
    CurrentMonthCost decimal(10,4) NULL DEFAULT 0,
    CurrentDayTokens int NULL DEFAULT 0,
    CurrentMonthTokens bigint NULL DEFAULT 0,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),
    UpdatedAt datetime2 NULL DEFAULT GETDATE(),

    CONSTRAINT PK_UserAIConfigurations PRIMARY KEY (Id),
    CONSTRAINT FK_UserAIConfigurations_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT CK_UserAIConfigurations_ProviderName CHECK (ProviderName IN ('Azure', 'OpenAI', 'DeepSeek', 'Claude', 'Ollama', 'Mock')),
    CONSTRAINT CK_UserAIConfigurations_ModelType CHECK (ModelType IN ('RequirementAnalysis', 'CodeGeneration', 'Testing', 'Debugging'))
);
GO

-- 用户AI配置表索引
CREATE INDEX IX_UserAIConfigurations_UserId ON UserAIConfigurations(UserId);
CREATE INDEX IX_UserAIConfigurations_ProviderName ON UserAIConfigurations(ProviderName);
CREATE INDEX IX_UserAIConfigurations_ModelType ON UserAIConfigurations(ModelType);
CREATE INDEX IX_UserAIConfigurations_IsActive ON UserAIConfigurations(IsActive);
GO

-- =============================================
-- 5. 用户任务映射表 (UserTaskMappings) - 基于 UserTaskMapping.cs 实体
-- =============================================
CREATE TABLE UserTaskMappings (
    Id int IDENTITY(1,1) NOT NULL,
    UserId int NOT NULL,
    TaskType nvarchar(50) NOT NULL,
    ProviderName nvarchar(50) NOT NULL,
    ModelName nvarchar(100) NULL,
    IsActive bit NOT NULL DEFAULT 1,
    IsDefault bit NOT NULL DEFAULT 0,
    Priority int NOT NULL DEFAULT 1,
    ConfigurationParameters nvarchar(max) NULL,
    Description nvarchar(500) NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETDATE(),

    CONSTRAINT PK_UserTaskMappings PRIMARY KEY (Id),
    CONSTRAINT FK_UserTaskMappings_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT CK_UserTaskMappings_TaskType CHECK (TaskType IN ('RequirementAnalysis', 'CodeGeneration', 'DocumentGeneration', 'Embeddings', 'Testing', 'Debugging')),
    CONSTRAINT CK_UserTaskMappings_ProviderName CHECK (ProviderName IN ('Azure', 'OpenAI', 'DeepSeek', 'Claude', 'Ollama', 'Mock')),
    CONSTRAINT CK_UserTaskMappings_Priority CHECK (Priority >= 1 AND Priority <= 100)
);
GO

-- 用户任务映射表索引
CREATE INDEX IX_UserTaskMappings_UserId ON UserTaskMappings(UserId);
CREATE INDEX IX_UserTaskMappings_TaskType ON UserTaskMappings(TaskType);
CREATE INDEX IX_UserTaskMappings_ProviderName ON UserTaskMappings(ProviderName);
CREATE INDEX IX_UserTaskMappings_IsActive ON UserTaskMappings(IsActive);
CREATE INDEX IX_UserTaskMappings_IsDefault ON UserTaskMappings(IsDefault);
GO

-- =============================================
-- 6. 需求对话记录表 (RequirementConversations) - 基于 RequirementConversation.cs 实体
-- =============================================
CREATE TABLE RequirementConversations (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    UserId int NOT NULL,
    ConversationId nvarchar(50) NULL,
    UserMessage nvarchar(max) NOT NULL,
    AIResponse nvarchar(max) NULL,
    MessageType nvarchar(20) NULL DEFAULT 'Requirement',
    Timestamp datetime2 NULL DEFAULT GETDATE(),

    CONSTRAINT PK_RequirementConversations PRIMARY KEY (Id),
    CONSTRAINT FK_RequirementConversations_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_RequirementConversations_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT CK_RequirementConversations_MessageType CHECK (MessageType IN ('Requirement', 'Clarification', 'Confirmation'))
);
GO

-- 需求对话记录表索引
CREATE INDEX IX_RequirementConversations_ProjectId ON RequirementConversations(ProjectId);
CREATE INDEX IX_RequirementConversations_UserId ON RequirementConversations(UserId);
CREATE INDEX IX_RequirementConversations_ConversationId ON RequirementConversations(ConversationId);
CREATE INDEX IX_RequirementConversations_MessageType ON RequirementConversations(MessageType);
GO

-- =============================================
-- 7. 需求文档表 (RequirementDocuments) - 基于 RequirementDocument.cs 实体
-- =============================================
CREATE TABLE RequirementDocuments (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    Title nvarchar(500) NOT NULL,
    Content nvarchar(max) NOT NULL,
    FunctionalRequirements nvarchar(max) NULL,
    NonFunctionalRequirements nvarchar(max) NULL,
    UserStories nvarchar(max) NULL,
    AcceptanceCriteria nvarchar(max) NULL,
    DocumentVersion nvarchar(20) NULL DEFAULT '1.0',
    Status nvarchar(20) NULL DEFAULT 'Draft',
    GeneratedBy nvarchar(50) NULL DEFAULT 'AI',
    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_RequirementDocuments PRIMARY KEY (Id),
    CONSTRAINT FK_RequirementDocuments_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT CK_RequirementDocuments_Status CHECK (Status IN ('Draft', 'Review', 'Approved', 'Rejected', 'Published')),
    CONSTRAINT CK_RequirementDocuments_GeneratedBy CHECK (GeneratedBy IN ('AI', 'Manual', 'Hybrid'))
);
GO

-- 需求文档表索引
CREATE INDEX IX_RequirementDocuments_ProjectId ON RequirementDocuments(ProjectId);
CREATE INDEX IX_RequirementDocuments_Status ON RequirementDocuments(Status);
CREATE INDEX IX_RequirementDocuments_GeneratedBy ON RequirementDocuments(GeneratedBy);
CREATE INDEX IX_RequirementDocuments_IsDeleted ON RequirementDocuments(IsDeleted);
GO

-- =============================================
-- 8. ER图表 (ERDiagrams) - 基于 ERDiagram.cs 实体
-- =============================================
CREATE TABLE [dbo].[ERDiagrams] (
    [Id] INT IDENTITY(1,1) NOT NULL,
    [ProjectId] INT NOT NULL,
    [RequirementDocumentId] INT NULL,
    [DiagramName] NVARCHAR(200) NOT NULL,
    [MermaidDefinition] NVARCHAR(MAX) NOT NULL,
    [Description] NVARCHAR(MAX) NULL,
    [DiagramVersion] NVARCHAR(20) NOT NULL DEFAULT '1.0',
    -- BaseEntity 字段
    [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
    [UpdatedTime] DATETIME2 NULL,
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0,
    [DeletedTime] DATETIME2 NULL,
    [DeletedBy] INT NULL,
    [Version] INT NOT NULL DEFAULT 1,
    [Remarks] NVARCHAR(500) NULL,
    
    CONSTRAINT [PK_ERDiagrams] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_ERDiagrams_ProjectId] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[Projects]([Id]),
    CONSTRAINT [FK_ERDiagrams_RequirementDocumentId] FOREIGN KEY ([RequirementDocumentId]) REFERENCES [dbo].[RequirementDocuments]([Id]),
    CONSTRAINT [FK_ERDiagrams_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
    CONSTRAINT [FK_ERDiagrams_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id]),
    CONSTRAINT [FK_ERDiagrams_DeletedBy] FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([Id])
);
GO

-- 创建索引
CREATE INDEX [IX_ERDiagrams_ProjectId] ON [dbo].[ERDiagrams]([ProjectId]);
CREATE INDEX [IX_ERDiagrams_RequirementDocumentId] ON [dbo].[ERDiagrams]([RequirementDocumentId]);
CREATE INDEX [IX_ERDiagrams_CreatedBy] ON [dbo].[ERDiagrams]([CreatedBy]);
GO

-- =============================================
-- 9. 上下文图表 (ContextDiagrams) - 基于 ContextDiagram.cs 实体
-- =============================================

CREATE TABLE [dbo].[ContextDiagrams] (
    [Id] INT IDENTITY(1,1) NOT NULL,
    [ProjectId] INT NOT NULL,
    [RequirementDocumentId] INT NULL,
    [DiagramName] NVARCHAR(200) NOT NULL,
    [MermaidDefinition] NVARCHAR(MAX) NOT NULL,
    [ExternalEntities] NVARCHAR(MAX) NULL,
    [SystemBoundary] NVARCHAR(MAX) NULL,
    [DataFlows] NVARCHAR(MAX) NULL,
    [DiagramVersion] NVARCHAR(20) NOT NULL DEFAULT '1.0',
    -- BaseEntity 字段
    [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
    [UpdatedTime] DATETIME2 NULL,
    [CreatedBy] INT NULL,
    [UpdatedBy] INT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0,
    [DeletedTime] DATETIME2 NULL,
    [DeletedBy] INT NULL,
    [Version] INT NOT NULL DEFAULT 1,
    [Remarks] NVARCHAR(500) NULL,
    
    CONSTRAINT [PK_ContextDiagrams] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_ContextDiagrams_ProjectId] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[Projects]([Id]),
    CONSTRAINT [FK_ContextDiagrams_RequirementDocumentId] FOREIGN KEY ([RequirementDocumentId]) REFERENCES [dbo].[RequirementDocuments]([Id]),
    CONSTRAINT [FK_ContextDiagrams_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
    CONSTRAINT [FK_ContextDiagrams_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id]),
    CONSTRAINT [FK_ContextDiagrams_DeletedBy] FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([Id])
);
GO

-- 创建索引
CREATE INDEX [IX_ContextDiagrams_ProjectId] ON [dbo].[ContextDiagrams]([ProjectId]);
CREATE INDEX [IX_ContextDiagrams_RequirementDocumentId] ON [dbo].[ContextDiagrams]([RequirementDocumentId]);
CREATE INDEX [IX_ContextDiagrams_CreatedBy] ON [dbo].[ContextDiagrams]([CreatedBy]);
GO

-- =============================================
-- 10. 代码生成任务表 (CodeGenerationTasks) - 基于 CodeGenerationTask.cs 实体
-- =============================================
CREATE TABLE CodeGenerationTasks (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    RequirementDocumentId int NULL,
    TaskName nvarchar(200) NOT NULL,
    CodeType nvarchar(50) NOT NULL,
    Technology nvarchar(50) NULL,
    Status nvarchar(20) NULL DEFAULT 'Pending',
    GeneratedCode nvarchar(max) NULL,
    FilePath nvarchar(500) NULL,
    ErrorMessage nvarchar(max) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),
    CompletedAt datetime2 NULL,

    CONSTRAINT PK_CodeGenerationTasks PRIMARY KEY (Id),
    CONSTRAINT FK_CodeGenerationTasks_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_CodeGenerationTasks_RequirementDocumentId FOREIGN KEY (RequirementDocumentId) REFERENCES RequirementDocuments(Id),
    CONSTRAINT CK_CodeGenerationTasks_CodeType CHECK (CodeType IN ('Frontend', 'Backend', 'Database', 'API', 'Model')),
    CONSTRAINT CK_CodeGenerationTasks_Status CHECK (Status IN ('Pending', 'InProgress', 'Completed', 'Failed'))
);
GO

-- 代码生成任务表索引
CREATE INDEX IX_CodeGenerationTasks_ProjectId ON CodeGenerationTasks(ProjectId);
CREATE INDEX IX_CodeGenerationTasks_RequirementDocumentId ON CodeGenerationTasks(RequirementDocumentId);
CREATE INDEX IX_CodeGenerationTasks_Status ON CodeGenerationTasks(Status);
CREATE INDEX IX_CodeGenerationTasks_CodeType ON CodeGenerationTasks(CodeType);
GO

-- =============================================
-- 11. 生成的代码文件表 (GeneratedCodeFiles) - 基于 GeneratedCodeFile.cs 实体
-- =============================================
CREATE TABLE GeneratedCodeFiles (
    Id int IDENTITY(1,1) NOT NULL,
    CodeGenerationTaskId int NOT NULL,
    FileName nvarchar(255) NOT NULL,
    FilePath nvarchar(500) NOT NULL,
    FileContent nvarchar(max) NOT NULL,
    FileType nvarchar(50) NULL,
    Language nvarchar(50) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),

    CONSTRAINT PK_GeneratedCodeFiles PRIMARY KEY (Id),
    CONSTRAINT FK_GeneratedCodeFiles_CodeGenerationTaskId FOREIGN KEY (CodeGenerationTaskId) REFERENCES CodeGenerationTasks(Id)
);
GO

-- 生成的代码文件表索引
CREATE INDEX IX_GeneratedCodeFiles_CodeGenerationTaskId ON GeneratedCodeFiles(CodeGenerationTaskId);
CREATE INDEX IX_GeneratedCodeFiles_FileType ON GeneratedCodeFiles(FileType);
CREATE INDEX IX_GeneratedCodeFiles_Language ON GeneratedCodeFiles(Language);
GO

-- =============================================
-- 12. 测试任务表 (TestTasks) - 基于 TestTask.cs 实体
-- =============================================
CREATE TABLE TestTasks (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    CodeGenerationTaskId int NULL,
    TestName nvarchar(200) NOT NULL,
    TestType nvarchar(50) NOT NULL,
    TestFramework nvarchar(50) NULL,
    Status nvarchar(20) NULL DEFAULT 'Pending',
    TestCode nvarchar(max) NULL,
    TestResults nvarchar(max) NULL,
    ErrorMessage nvarchar(max) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),
    CompletedAt datetime2 NULL,

    CONSTRAINT PK_TestTasks PRIMARY KEY (Id),
    CONSTRAINT FK_TestTasks_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_TestTasks_CodeGenerationTaskId FOREIGN KEY (CodeGenerationTaskId) REFERENCES CodeGenerationTasks(Id),
    CONSTRAINT CK_TestTasks_TestType CHECK (TestType IN ('Unit', 'Integration', 'E2E', 'Performance')),
    CONSTRAINT CK_TestTasks_Status CHECK (Status IN ('Pending', 'Running', 'Passed', 'Failed'))
);
GO

-- 测试任务表索引
CREATE INDEX IX_TestTasks_ProjectId ON TestTasks(ProjectId);
CREATE INDEX IX_TestTasks_CodeGenerationTaskId ON TestTasks(CodeGenerationTaskId);
CREATE INDEX IX_TestTasks_Status ON TestTasks(Status);
CREATE INDEX IX_TestTasks_TestType ON TestTasks(TestType);
GO

-- =============================================
-- 13. 部署任务表 (DeploymentTasks) - 基于 DeploymentTask.cs 实体
-- =============================================
CREATE TABLE DeploymentTasks (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    Environment nvarchar(50) NOT NULL,
    DeploymentType nvarchar(50) NULL,
    Status nvarchar(20) NULL DEFAULT 'Pending',
    DeploymentScript nvarchar(max) NULL,
    DeploymentUrl nvarchar(500) NULL,
    LogOutput nvarchar(max) NULL,
    ErrorMessage nvarchar(max) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),
    CompletedAt datetime2 NULL,

    CONSTRAINT PK_DeploymentTasks PRIMARY KEY (Id),
    CONSTRAINT FK_DeploymentTasks_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT CK_DeploymentTasks_Environment CHECK (Environment IN ('Development', 'Staging', 'Production')),
    CONSTRAINT CK_DeploymentTasks_DeploymentType CHECK (DeploymentType IN ('Docker', 'IIS', 'Azure', 'AWS', 'Kubernetes')),
    CONSTRAINT CK_DeploymentTasks_Status CHECK (Status IN ('Pending', 'InProgress', 'Completed', 'Failed'))
);
GO

-- 部署任务表索引
CREATE INDEX IX_DeploymentTasks_ProjectId ON DeploymentTasks(ProjectId);
CREATE INDEX IX_DeploymentTasks_Environment ON DeploymentTasks(Environment);
CREATE INDEX IX_DeploymentTasks_Status ON DeploymentTasks(Status);
GO

-- =============================================
-- 14. 问题表 (Issues) - 基于 Issue.cs 实体
-- =============================================
CREATE TABLE Issues (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    Title nvarchar(500) NOT NULL,
    Description nvarchar(max) NOT NULL,
    IssueType nvarchar(50) NOT NULL,
    Priority nvarchar(10) NULL DEFAULT 'Medium',
    Status nvarchar(20) NULL DEFAULT 'Open',
    AssignedTo int NULL,
    ReportedBy int NOT NULL,
    Labels nvarchar(500) NULL,
    ResolvedAt datetime2 NULL,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_Issues PRIMARY KEY (Id),
    CONSTRAINT FK_Issues_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_Issues_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Users(Id),
    CONSTRAINT FK_Issues_ReportedBy FOREIGN KEY (ReportedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Issues_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Issues_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Issues_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_Issues_IssueType CHECK (IssueType IN ('Bug', 'Feature', 'Enhancement', 'Task')),
    CONSTRAINT CK_Issues_Priority CHECK (Priority IN ('Low', 'Medium', 'High', 'Critical')),
    CONSTRAINT CK_Issues_Status CHECK (Status IN ('Open', 'InProgress', 'Resolved', 'Closed'))
);
GO

-- 问题表索引
CREATE INDEX IX_Issues_ProjectId ON Issues(ProjectId);
CREATE INDEX IX_Issues_AssignedTo ON Issues(AssignedTo);
CREATE INDEX IX_Issues_ReportedBy ON Issues(ReportedBy);
CREATE INDEX IX_Issues_Status ON Issues(Status);
CREATE INDEX IX_Issues_Priority ON Issues(Priority);
CREATE INDEX IX_Issues_IssueType ON Issues(IssueType);
GO

-- =============================================
-- 15. 问题解决记录表 (IssueResolutions) - 基于 IssueResolution.cs 实体
-- =============================================
CREATE TABLE IssueResolutions (
    Id int IDENTITY(1,1) NOT NULL,
    IssueId int NOT NULL,
    ResolutionType nvarchar(50) NULL,
    ResolutionDescription nvarchar(max) NULL,
    GeneratedCode nvarchar(max) NULL,
    ResolvedBy nvarchar(50) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),

    CONSTRAINT PK_IssueResolutions PRIMARY KEY (Id),
    CONSTRAINT FK_IssueResolutions_IssueId FOREIGN KEY (IssueId) REFERENCES Issues(Id),
    CONSTRAINT CK_IssueResolutions_ResolutionType CHECK (ResolutionType IN ('AutoFixed', 'ManualFixed', 'CodeGenerated', 'TestAdded')),
    CONSTRAINT CK_IssueResolutions_ResolvedBy CHECK (ResolvedBy IN ('AI', 'User'))
);
GO

-- 问题解决记录表索引
CREATE INDEX IX_IssueResolutions_IssueId ON IssueResolutions(IssueId);
CREATE INDEX IX_IssueResolutions_ResolutionType ON IssueResolutions(ResolutionType);
GO

-- =============================================
-- 16. 工作流状态表 (WorkflowStates) - 基于 WorkflowState.cs 实体
-- =============================================
CREATE TABLE WorkflowStates (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NOT NULL,
    CurrentStage nvarchar(50) NOT NULL,
    StageStatus nvarchar(20) NULL DEFAULT 'InProgress',
    StageData nvarchar(max) NULL,
    StartedAt datetime2 NULL DEFAULT GETDATE(),
    CompletedAt datetime2 NULL,

    CONSTRAINT PK_WorkflowStates PRIMARY KEY (Id),
    CONSTRAINT FK_WorkflowStates_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT CK_WorkflowStates_CurrentStage CHECK (CurrentStage IN ('RequirementGathering', 'DocumentGeneration', 'DiagramGeneration', 'CodeGeneration', 'Testing', 'Deployment', 'IssueHandling')),
    CONSTRAINT CK_WorkflowStates_StageStatus CHECK (StageStatus IN ('Pending', 'InProgress', 'Completed', 'Failed'))
);
GO

-- 工作流状态表索引
CREATE INDEX IX_WorkflowStates_ProjectId ON WorkflowStates(ProjectId);
CREATE INDEX IX_WorkflowStates_CurrentStage ON WorkflowStates(CurrentStage);
CREATE INDEX IX_WorkflowStates_StageStatus ON WorkflowStates(StageStatus);
GO

-- =============================================
-- 17. 系统日志表 (SystemLogs) - 基于 SystemLog.cs 实体
-- =============================================
CREATE TABLE SystemLogs (
    Id int IDENTITY(1,1) NOT NULL,
    ProjectId int NULL,
    UserId int NULL,
    LogLevel nvarchar(20) NOT NULL,
    Component nvarchar(100) NULL,
    Message nvarchar(max) NOT NULL,
    Exception nvarchar(max) NULL,
    AdditionalData nvarchar(max) NULL,
    CreatedAt datetime2 NULL DEFAULT GETDATE(),

    CONSTRAINT PK_SystemLogs PRIMARY KEY (Id),
    CONSTRAINT FK_SystemLogs_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_SystemLogs_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT CK_SystemLogs_LogLevel CHECK (LogLevel IN ('Info', 'Warning', 'Error', 'Debug'))
);
GO

-- 系统日志表索引
CREATE INDEX IX_SystemLogs_ProjectId ON SystemLogs(ProjectId);
CREATE INDEX IX_SystemLogs_UserId ON SystemLogs(UserId);
CREATE INDEX IX_SystemLogs_LogLevel ON SystemLogs(LogLevel);
CREATE INDEX IX_SystemLogs_Component ON SystemLogs(Component);
CREATE INDEX IX_SystemLogs_CreatedAt ON SystemLogs(CreatedAt);
GO

-- =============================================
-- 18. 创建全文搜索索引 (用于需求文档和问题搜索)
-- =============================================

-- 创建全文目录
IF NOT EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ProjectManagementCatalog')
    CREATE FULLTEXT CATALOG ProjectManagementCatalog AS DEFAULT;
GO

-- =============================================
-- 全文搜索索引创建（可选）
-- 注意：需要SQL Server安装全文搜索功能
-- =============================================

-- 检查全文搜索是否可用
IF SERVERPROPERTY('IsFullTextInstalled') = 1
BEGIN
    PRINT '全文搜索功能可用，创建全文目录和索引...';

    -- 创建全文目录（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ProjectManagementCatalog')
    BEGIN
        CREATE FULLTEXT CATALOG ProjectManagementCatalog AS DEFAULT;
        PRINT '全文目录 ProjectManagementCatalog 创建成功';
    END

    -- 为需求文档表创建全文索引
    IF NOT EXISTS (SELECT * FROM sys.fulltext_indexes WHERE object_id = OBJECT_ID('RequirementDocuments'))
    BEGIN
        CREATE FULLTEXT INDEX ON RequirementDocuments(
            Title, Content, FunctionalRequirements, NonFunctionalRequirements, AcceptanceCriteria
        )
        KEY INDEX PK_RequirementDocuments
        ON ProjectManagementCatalog;
        PRINT '需求文档表全文索引创建成功';
    END
END
ELSE
BEGIN
    PRINT '警告：全文搜索功能未安装，跳过全文索引创建';
    PRINT '建议：可以使用LIKE查询进行文本搜索，或安装全文搜索功能';

    -- 创建常规索引以提高LIKE查询性能
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('RequirementDocuments') AND name = 'IX_RequirementDocuments_Title')
    BEGIN
        CREATE NONCLUSTERED INDEX IX_RequirementDocuments_Title
        ON RequirementDocuments(Title);
        PRINT '需求文档标题索引创建成功';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('RequirementDocuments') AND name = 'IX_RequirementDocuments_ProjectId')
    BEGIN
        CREATE NONCLUSTERED INDEX IX_RequirementDocuments_ProjectId
        ON RequirementDocuments(ProjectId);
        PRINT '需求文档项目ID索引创建成功';
    END
END
GO

-- 为问题表创建全文索引（如果全文搜索可用）
IF SERVERPROPERTY('IsFullTextInstalled') = 1
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.fulltext_indexes WHERE object_id = OBJECT_ID('Issues'))
    BEGIN
        CREATE FULLTEXT INDEX ON Issues(
            Title, Description
        )
        KEY INDEX PK_Issues
        ON ProjectManagementCatalog;
        PRINT '问题表全文索引创建成功';
    END
END
ELSE
BEGIN
    PRINT '警告：全文搜索功能未安装，跳过问题表全文索引创建';

    -- 创建常规索引以提高LIKE查询性能
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Issues') AND name = 'IX_Issues_Title')
    BEGIN
        CREATE NONCLUSTERED INDEX IX_Issues_Title
        ON Issues(Title);
        PRINT '问题表标题索引创建成功';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Issues') AND name = 'IX_Issues_ProjectId_Status')
    BEGIN
        CREATE NONCLUSTERED INDEX IX_Issues_ProjectId_Status
        ON Issues(ProjectId, Status);
        PRINT '问题表项目ID和状态索引创建成功';
    END
END
GO

-- =============================================
-- 19. 插入初始数据
-- =============================================

-- 插入默认管理员用户
INSERT INTO Users (Username, Email, PasswordHash, Salt, RealName, Role, Status, EmailVerified)
VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin_salt_2024', '系统管理员', 'SuperAdmin', 1, 1),
('demo_user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin_salt_2024', '演示用户', 'User', 1, 1);
GO

-- 插入默认AI模型配置
INSERT INTO AIModelConfigurations (ModelName, ModelType, ApiEndpoint, IsActive)
VALUES
('GPT-4', 'RequirementAnalysis', 'https://api.openai.com/v1/chat/completions', 1),
('GPT-4', 'CodeGeneration', 'https://api.openai.com/v1/chat/completions', 1),
('DeepSeek-Coder', 'CodeGeneration', 'https://api.deepseek.com/v1/chat/completions', 1),
('Claude-3', 'RequirementAnalysis', 'https://api.anthropic.com/v1/messages', 1);
GO

-- =============================================
-- 20. 创建有用的视图
-- =============================================

-- 项目概览视图
CREATE VIEW vw_ProjectOverview AS
SELECT
    p.Id,
    p.Name,
    p.ProjectCode,
    p.Status,
    p.Priority,
    p.Progress,
    u.Username AS OwnerName,
    u.RealName AS OwnerRealName,
    p.StartDate,
    p.EndDate,
    p.EstimatedHours,
    p.ActualHours,
    p.Budget,
    p.CreatedTime,
    (SELECT COUNT(*) FROM RequirementDocuments rd WHERE rd.ProjectId = p.Id AND rd.IsDeleted = 0) AS RequirementDocumentCount,
    (SELECT COUNT(*) FROM Issues i WHERE i.ProjectId = p.Id) AS IssueCount,
    (SELECT COUNT(*) FROM CodeGenerationTasks cgt WHERE cgt.ProjectId = p.Id) AS CodeGenerationTaskCount
FROM Projects p
INNER JOIN Users u ON p.OwnerId = u.Id
WHERE p.IsDeleted = 0;
GO

-- 用户任务统计视图
CREATE VIEW vw_UserTaskStatistics AS
SELECT
    u.Id AS UserId,
    u.Username,
    u.RealName,
    COUNT(DISTINCT p.Id) AS ProjectCount,
    COUNT(DISTINCT i_assigned.Id) AS AssignedIssueCount,
    COUNT(DISTINCT i_reported.Id) AS ReportedIssueCount,
    COUNT(DISTINCT rc.Id) AS ConversationCount
FROM Users u
LEFT JOIN Projects p ON u.Id = p.OwnerId AND p.IsDeleted = 0
LEFT JOIN Issues i_assigned ON u.Id = i_assigned.AssignedTo
LEFT JOIN Issues i_reported ON u.Id = i_reported.ReportedBy
LEFT JOIN RequirementConversations rc ON u.Id = rc.UserId
WHERE u.IsDeleted = 0
GROUP BY u.Id, u.Username, u.RealName;
GO

-- AI使用统计视图
CREATE VIEW vw_AIUsageStatistics AS
SELECT
    uac.UserId,
    u.Username,
    uac.ProviderName,
    uac.ModelName,
    uac.ModelType,
    uac.TotalRequests,
    uac.SuccessfulRequests,
    uac.FailedRequests,
    uac.TotalTokensUsed,
    uac.EstimatedCost,
    uac.CurrentMonthCost,
    uac.LastUsedAt
FROM UserAIConfigurations uac
INNER JOIN Users u ON uac.UserId = u.Id
WHERE uac.IsActive = 1 AND u.IsDeleted = 0;
GO

-- =============================================
-- 21. 创建文本搜索存储过程（全文搜索的替代方案）
-- =============================================

-- 需求文档搜索存储过程
CREATE PROCEDURE sp_SearchRequirementDocuments
    @SearchText NVARCHAR(500),
    @ProjectId INT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    SELECT
        rd.Id,
        rd.ProjectId,
        p.Name AS ProjectName,
        rd.Title,
        rd.Content,
        rd.FunctionalRequirements,
        rd.NonFunctionalRequirements,
        rd.AcceptanceCriteria,
        rd.CreatedTime,
        rd.UpdatedTime,
        -- 计算相关性得分（简单实现）
        (
            CASE WHEN rd.Title LIKE '%' + @SearchText + '%' THEN 10 ELSE 0 END +
            CASE WHEN rd.Content LIKE '%' + @SearchText + '%' THEN 5 ELSE 0 END +
            CASE WHEN rd.FunctionalRequirements LIKE '%' + @SearchText + '%' THEN 3 ELSE 0 END +
            CASE WHEN rd.NonFunctionalRequirements LIKE '%' + @SearchText + '%' THEN 3 ELSE 0 END +
            CASE WHEN rd.AcceptanceCriteria LIKE '%' + @SearchText + '%' THEN 2 ELSE 0 END
        ) AS RelevanceScore
    FROM RequirementDocuments rd
    INNER JOIN Projects p ON rd.ProjectId = p.Id
    WHERE
        (
            rd.Title LIKE '%' + @SearchText + '%' OR
            rd.Content LIKE '%' + @SearchText + '%' OR
            rd.FunctionalRequirements LIKE '%' + @SearchText + '%' OR
            rd.NonFunctionalRequirements LIKE '%' + @SearchText + '%' OR
            rd.AcceptanceCriteria LIKE '%' + @SearchText + '%'
        )
        AND (@ProjectId IS NULL OR rd.ProjectId = @ProjectId)
        AND rd.IsDeleted = 0
    ORDER BY RelevanceScore DESC, rd.UpdatedTime DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;

    -- 返回总记录数
    SELECT COUNT(*) AS TotalCount
    FROM RequirementDocuments rd
    WHERE
        (
            rd.Title LIKE '%' + @SearchText + '%' OR
            rd.Content LIKE '%' + @SearchText + '%' OR
            rd.FunctionalRequirements LIKE '%' + @SearchText + '%' OR
            rd.NonFunctionalRequirements LIKE '%' + @SearchText + '%' OR
            rd.AcceptanceCriteria LIKE '%' + @SearchText + '%'
        )
        AND (@ProjectId IS NULL OR rd.ProjectId = @ProjectId)
        AND rd.IsDeleted = 0;
END;
GO

-- 问题搜索存储过程
CREATE PROCEDURE sp_SearchIssues
    @SearchText NVARCHAR(500),
    @ProjectId INT = NULL,
    @Status NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    SELECT
        i.Id,
        i.ProjectId,
        p.Name AS ProjectName,
        i.Title,
        i.Description,
        i.IssueType,
        i.Priority,
        i.Status,
        i.Labels,
        u_assigned.Username AS AssignedToUsername,
        u_reported.Username AS ReportedByUsername,
        i.CreatedTime,
        i.UpdatedTime,
        i.ResolvedAt,
        -- 计算相关性得分
        (
            CASE WHEN i.Title LIKE '%' + @SearchText + '%' THEN 10 ELSE 0 END +
            CASE WHEN i.Description LIKE '%' + @SearchText + '%' THEN 5 ELSE 0 END
        ) AS RelevanceScore
    FROM Issues i
    INNER JOIN Projects p ON i.ProjectId = p.Id
    LEFT JOIN Users u_assigned ON i.AssignedTo = u_assigned.Id
    INNER JOIN Users u_reported ON i.ReportedBy = u_reported.Id
    WHERE
        i.IsDeleted = 0 AND
        (
            i.Title LIKE '%' + @SearchText + '%' OR
            i.Description LIKE '%' + @SearchText + '%'
        )
        AND (@ProjectId IS NULL OR i.ProjectId = @ProjectId)
        AND (@Status IS NULL OR i.Status = @Status)
    ORDER BY RelevanceScore DESC, i.UpdatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;

    -- 返回总记录数
    SELECT COUNT(*) AS TotalCount
    FROM Issues i
    WHERE
        (
            i.Title LIKE '%' + @SearchText + '%' OR
            i.Description LIKE '%' + @SearchText + '%'
        )
        AND (@ProjectId IS NULL OR i.ProjectId = @ProjectId)
        AND (@Status IS NULL OR i.Status = @Status);
END;
GO

-- =============================================
-- 22. 创建其他存储过程
-- =============================================

-- 获取项目详细信息的存储过程
CREATE PROCEDURE sp_GetProjectDetails
    @ProjectId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- 项目基本信息
    SELECT * FROM vw_ProjectOverview WHERE Id = @ProjectId;

    -- 项目需求文档
    SELECT * FROM RequirementDocuments WHERE ProjectId = @ProjectId AND IsDeleted = 0;

    -- 项目问题列表
    SELECT
        i.*,
        u_assigned.Username AS AssignedToUsername,
        u_reported.Username AS ReportedByUsername
    FROM Issues i
    LEFT JOIN Users u_assigned ON i.AssignedTo = u_assigned.Id
    INNER JOIN Users u_reported ON i.ReportedBy = u_reported.Id
    WHERE i.ProjectId = @ProjectId;

    -- 项目代码生成任务
    SELECT * FROM CodeGenerationTasks WHERE ProjectId = @ProjectId;
END;
GO

-- 用户AI配置管理存储过程
CREATE PROCEDURE sp_GetUserAIConfiguration
    @UserId INT,
    @TaskType NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    IF @TaskType IS NULL
    BEGIN
        -- 获取用户所有AI配置
        SELECT * FROM UserAIConfigurations
        WHERE UserId = @UserId AND IsActive = 1
        ORDER BY ModelType, IsDefault DESC;
    END
    ELSE
    BEGIN
        -- 获取特定任务类型的配置
        SELECT TOP 1 * FROM UserAIConfigurations
        WHERE UserId = @UserId AND ModelType = @TaskType AND IsActive = 1
        ORDER BY IsDefault DESC;
    END
END;
GO

-- =============================================
-- 22. 脚本完成信息
-- =============================================

PRINT '==============================================';
PRINT '项目管理AI系统数据库创建完成！';
PRINT '==============================================';
PRINT '';
PRINT '包含以下17个数据表：';
PRINT '1. Users - 用户表 (用户信息、权限、认证)';
PRINT '2. Projects - 项目表 (项目基本信息、状态、进度)';
PRINT '3. AIModelConfigurations - AI模型配置表 (系统级AI模型配置)';
PRINT '4. UserAIConfigurations - 用户AI配置表 (用户个人AI配置)';
PRINT '5. UserTaskMappings - 用户任务映射表 (用户任务类型与AI提供商映射)';
PRINT '6. RequirementConversations - 需求对话记录表 (AI需求收集对话)';
PRINT '7. RequirementDocuments - 需求文档表 (AI生成的需求规格书)';
PRINT '8. ERDiagrams - ER图表 (数据库实体关系图)';
PRINT '9. ContextDiagrams - 上下文图表 (系统上下文图)';
PRINT '10. CodeGenerationTasks - 代码生成任务表 (AI代码生成任务)';
PRINT '11. GeneratedCodeFiles - 生成的代码文件表 (具体代码文件存储)';
PRINT '12. TestTasks - 测试任务表 (AI测试生成和执行)';
PRINT '13. DeploymentTasks - 部署任务表 (自动化部署管理)';
PRINT '14. Issues - 问题表 (项目问题和缺陷管理)';
PRINT '15. IssueResolutions - 问题解决记录表 (问题解决方案记录)';
PRINT '16. WorkflowStates - 工作流状态表 (项目工作流管理)';
PRINT '17. SystemLogs - 系统日志表 (系统运行日志)';
PRINT '';
PRINT '已创建3个视图：';
PRINT '- vw_ProjectOverview (项目概览视图)';
PRINT '- vw_UserTaskStatistics (用户任务统计视图)';
PRINT '- vw_AIUsageStatistics (AI使用统计视图)';
PRINT '';
PRINT '已创建4个存储过程：';
PRINT '- sp_SearchRequirementDocuments (搜索需求文档，支持分页和相关性排序)';
PRINT '- sp_SearchIssues (搜索问题，支持分页和相关性排序)';
PRINT '- sp_GetProjectDetails (获取项目详细信息)';
PRINT '- sp_GetUserAIConfiguration (获取用户AI配置)';
PRINT '';
PRINT '搜索功能说明：';
PRINT '- 智能检测全文搜索功能是否可用';
PRINT '- 如果全文搜索可用，创建全文索引提供高性能搜索';
PRINT '- 如果全文搜索不可用，使用LIKE查询和常规索引';
PRINT '- 搜索存储过程兼容两种模式，提供统一的搜索接口';
PRINT '';
PRINT '已插入初始数据：';
PRINT '- 默认管理员用户 (admin)';
PRINT '- 演示用户 (demo_user)';
PRINT '- 默认AI模型配置 (GPT-4, DeepSeek-Coder, Claude-3)';
PRINT '';
PRINT '数据库特性：';
PRINT '- 支持软删除 (IsDeleted字段)';
PRINT '- 支持审计跟踪 (CreatedBy, UpdatedBy, DeletedBy)';
PRINT '- 支持乐观锁 (Version字段)';
PRINT '- 支持全文搜索';
PRINT '- 完整的外键约束';
PRINT '- 性能优化索引';
PRINT '- 数据完整性检查约束';
PRINT '';
PRINT '使用说明：';
PRINT '1. 请根据实际环境修改数据库文件路径';
PRINT '2. 请更新默认用户的密码哈希值';
PRINT '3. 请根据需要配置AI模型的API密钥';
PRINT '4. 建议定期备份数据库';
PRINT '';
PRINT '脚本执行完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '==============================================';
GO

-- =============================================
-- 19. 插入初始数据
-- =============================================

-- 插入默认管理员用户
INSERT INTO Users (Username, Email, PasswordHash, Salt, RealName, Role, Status, EmailVerified)
VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123456', '系统管理员', 'SuperAdmin', 1, 1),
('demo_user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123456', '演示用户', 'User', 1, 1);
GO

-- 插入默认AI模型配置
INSERT INTO AIModelConfigurations (ModelName, ModelType, ApiEndpoint, IsActive)
VALUES
('GPT-4', 'RequirementAnalysis', 'https://api.openai.com/v1/chat/completions', 1),
('GPT-4', 'CodeGeneration', 'https://api.openai.com/v1/chat/completions', 1),
('DeepSeek-Coder', 'CodeGeneration', 'https://api.deepseek.com/v1/chat/completions', 1),
('Claude-3', 'RequirementAnalysis', 'https://api.anthropic.com/v1/messages', 1);
GO

-- =============================================
-- 20. 创建有用的视图
-- =============================================

-- 项目概览视图
CREATE VIEW vw_ProjectOverview AS
SELECT
    p.Id,
    p.Name,
    p.ProjectCode,
    p.Status,
    p.Priority,
    p.Progress,
    u.Username AS OwnerName,
    u.RealName AS OwnerRealName,
    p.StartDate,
    p.EndDate,
    p.EstimatedHours,
    p.ActualHours,
    p.Budget,
    p.CreatedTime,
    (SELECT COUNT(*) FROM RequirementDocuments rd WHERE rd.ProjectId = p.Id AND rd.IsDeleted = 0) AS RequirementDocumentCount,
    (SELECT COUNT(*) FROM Issues i WHERE i.ProjectId = p.Id) AS IssueCount,
    (SELECT COUNT(*) FROM CodeGenerationTasks cgt WHERE cgt.ProjectId = p.Id) AS CodeGenerationTaskCount
FROM Projects p
INNER JOIN Users u ON p.OwnerId = u.Id
WHERE p.IsDeleted = 0;
GO

-- 用户任务统计视图
CREATE VIEW vw_UserTaskStatistics AS
SELECT
    u.Id AS UserId,
    u.Username,
    u.RealName,
    COUNT(DISTINCT p.Id) AS ProjectCount,
    COUNT(DISTINCT i_assigned.Id) AS AssignedIssueCount,
    COUNT(DISTINCT i_reported.Id) AS ReportedIssueCount,
    COUNT(DISTINCT rc.Id) AS ConversationCount
FROM Users u
LEFT JOIN Projects p ON u.Id = p.OwnerId AND p.IsDeleted = 0
LEFT JOIN Issues i_assigned ON u.Id = i_assigned.AssignedTo
LEFT JOIN Issues i_reported ON u.Id = i_reported.ReportedBy
LEFT JOIN RequirementConversations rc ON u.Id = rc.UserId
WHERE u.IsDeleted = 0
GROUP BY u.Id, u.Username, u.RealName;
GO

-- AI使用统计视图
CREATE VIEW vw_AIUsageStatistics AS
SELECT
    uac.UserId,
    u.Username,
    uac.ProviderName,
    uac.ModelName,
    uac.ModelType,
    uac.TotalRequests,
    uac.SuccessfulRequests,
    uac.FailedRequests,
    uac.TotalTokensUsed,
    uac.EstimatedCost,
    uac.CurrentMonthCost,
    uac.LastUsedAt
FROM UserAIConfigurations uac
INNER JOIN Users u ON uac.UserId = u.Id
WHERE uac.IsActive = 1 AND u.IsDeleted = 0;
GO

-- =============================================
-- 21. 创建存储过程
-- =============================================

-- 获取项目详细信息的存储过程
CREATE PROCEDURE sp_GetProjectDetails
    @ProjectId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- 项目基本信息
    SELECT * FROM vw_ProjectOverview WHERE Id = @ProjectId;

    -- 项目需求文档
    SELECT * FROM RequirementDocuments WHERE ProjectId = @ProjectId AND IsDeleted = 0;

    -- 项目问题列表
    SELECT
        i.*,
        u_assigned.Username AS AssignedToUsername,
        u_reported.Username AS ReportedByUsername
    FROM Issues i
    LEFT JOIN Users u_assigned ON i.AssignedTo = u_assigned.Id
    INNER JOIN Users u_reported ON i.ReportedBy = u_reported.Id
    WHERE i.ProjectId = @ProjectId;

    -- 项目代码生成任务
    SELECT * FROM CodeGenerationTasks WHERE ProjectId = @ProjectId;
END;
GO

-- =============================================
-- 22. Selenium测试相关表
-- =============================================

-- Selenium测试脚本表
CREATE TABLE SeleniumScripts (
    Id int IDENTITY(1,1) NOT NULL,
    Name nvarchar(100) NOT NULL,
    Description nvarchar(500) NULL,
    Category nvarchar(50) NOT NULL DEFAULT 'ui',
    Code ntext NULL,
    ConfigJson ntext NULL,
    TagsJson nvarchar(1000) NULL,
    Priority nvarchar(20) NOT NULL DEFAULT 'medium',
    Status nvarchar(20) NOT NULL DEFAULT 'draft',
    ProjectId int NULL,
    LastExecutedTime datetime2 NULL,
    ExecutionCount int NOT NULL DEFAULT 0,
    SuccessCount int NOT NULL DEFAULT 0,
    AvgDuration int NULL,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_SeleniumScripts PRIMARY KEY (Id),
    CONSTRAINT FK_SeleniumScripts_Projects FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
    CONSTRAINT FK_SeleniumScripts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumScripts_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumScripts_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_SeleniumScripts_Category CHECK (Category IN ('ui', 'api', 'integration', 'performance')),
    CONSTRAINT CK_SeleniumScripts_Priority CHECK (Priority IN ('low', 'medium', 'high')),
    CONSTRAINT CK_SeleniumScripts_Status CHECK (Status IN ('draft', 'ready', 'running', 'failed', 'archived'))
);

-- Selenium执行记录表
CREATE TABLE SeleniumExecutions (
    Id int IDENTITY(1,1) NOT NULL,
    ExecutionId nvarchar(50) NOT NULL,
    ScriptId int NOT NULL,
    Status nvarchar(20) NOT NULL DEFAULT 'running',
    StartTime datetime2 NOT NULL,
    EndTime datetime2 NULL,
    Duration int NULL,
    ErrorMessage ntext NULL,
    ConfigJson ntext NULL,
    StatsJson ntext NULL,
    ScreenshotsJson ntext NULL,
    Environment nvarchar(500) NULL,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_SeleniumExecutions PRIMARY KEY (Id),
    CONSTRAINT UK_SeleniumExecutions_ExecutionId UNIQUE (ExecutionId),
    CONSTRAINT FK_SeleniumExecutions_Scripts FOREIGN KEY (ScriptId) REFERENCES SeleniumScripts(Id) ON DELETE CASCADE,
    CONSTRAINT FK_SeleniumExecutions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumExecutions_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumExecutions_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_SeleniumExecutions_Status CHECK (Status IN ('running', 'success', 'failed', 'stopped', 'timeout'))
);

-- Selenium执行日志表
CREATE TABLE SeleniumExecutionLogs (
    Id int IDENTITY(1,1) NOT NULL,
    ExecutionId int NOT NULL,
    Level nvarchar(20) NOT NULL DEFAULT 'info',
    Message ntext NOT NULL,
    Step nvarchar(200) NULL,
    Timestamp datetime2 NOT NULL DEFAULT GETDATE(),
    ExtraData ntext NULL,

    -- BaseEntity 字段
    CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedTime datetime2 NULL,
    CreatedBy int NULL,
    UpdatedBy int NULL,
    IsDeleted bit NOT NULL DEFAULT 0,
    DeletedTime datetime2 NULL,
    DeletedBy int NULL,
    Version int NOT NULL DEFAULT 1,
    Remarks nvarchar(500) NULL,

    CONSTRAINT PK_SeleniumExecutionLogs PRIMARY KEY (Id),
    CONSTRAINT FK_SeleniumExecutionLogs_Executions FOREIGN KEY (ExecutionId) REFERENCES SeleniumExecutions(Id) ON DELETE CASCADE,
    CONSTRAINT FK_SeleniumExecutionLogs_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumExecutionLogs_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_SeleniumExecutionLogs_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
    CONSTRAINT CK_SeleniumExecutionLogs_Level CHECK (Level IN ('debug', 'info', 'warning', 'error', 'success'))
);

-- 创建Selenium相关索引
CREATE INDEX IX_SeleniumScripts_Name ON SeleniumScripts(Name);
CREATE INDEX IX_SeleniumScripts_Category ON SeleniumScripts(Category);
CREATE INDEX IX_SeleniumScripts_Status ON SeleniumScripts(Status);
CREATE INDEX IX_SeleniumScripts_ProjectId ON SeleniumScripts(ProjectId);
CREATE INDEX IX_SeleniumScripts_CreatedBy ON SeleniumScripts(CreatedBy);
CREATE INDEX IX_SeleniumScripts_IsDeleted ON SeleniumScripts(IsDeleted);

CREATE INDEX IX_SeleniumExecutions_ScriptId ON SeleniumExecutions(ScriptId);
CREATE INDEX IX_SeleniumExecutions_Status ON SeleniumExecutions(Status);
CREATE INDEX IX_SeleniumExecutions_StartTime ON SeleniumExecutions(StartTime);
CREATE INDEX IX_SeleniumExecutions_CreatedBy ON SeleniumExecutions(CreatedBy);
CREATE INDEX IX_SeleniumExecutions_IsDeleted ON SeleniumExecutions(IsDeleted);

CREATE INDEX IX_SeleniumExecutionLogs_ExecutionId ON SeleniumExecutionLogs(ExecutionId);
CREATE INDEX IX_SeleniumExecutionLogs_Level ON SeleniumExecutionLogs(Level);
CREATE INDEX IX_SeleniumExecutionLogs_Timestamp ON SeleniumExecutionLogs(Timestamp);
CREATE INDEX IX_SeleniumExecutionLogs_IsDeleted ON SeleniumExecutionLogs(IsDeleted);

-- 添加Selenium表的中文注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium测试脚本表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Name';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Description';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本分类', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Category';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Python代码内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumScripts', @level2type = N'COLUMN', @level2name = N'Code';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium执行记录表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'执行唯一标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions', @level2type = N'COLUMN', @level2name = N'ExecutionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'脚本ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutions', @level2type = N'COLUMN', @level2name = N'ScriptId';

EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'Selenium执行日志表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'执行记录ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs', @level2type = N'COLUMN', @level2name = N'ExecutionId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'日志级别', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs', @level2type = N'COLUMN', @level2name = N'Level';

PRINT '✅ Selenium测试相关表创建完成';
GO

PRINT '数据库创建完成！';
PRINT '包含以下表：';
PRINT '1. Users - 用户表';
PRINT '2. Projects - 项目表';
PRINT '3. AIModelConfigurations - AI模型配置表';
PRINT '4. UserAIConfigurations - 用户AI配置表';
PRINT '5. UserTaskMappings - 用户任务映射表';
PRINT '6. RequirementConversations - 需求对话记录表';
PRINT '7. RequirementDocuments - 需求文档表';
PRINT '8. ERDiagrams - ER图表';
PRINT '9. ContextDiagrams - 上下文图表';
PRINT '10. CodeGenerationTasks - 代码生成任务表';
PRINT '11. GeneratedCodeFiles - 生成的代码文件表';
PRINT '12. TestTasks - 测试任务表';
PRINT '13. DeploymentTasks - 部署任务表';
PRINT '14. Issues - 问题表';
PRINT '15. IssueResolutions - 问题解决记录表';
PRINT '16. WorkflowStates - 工作流状态表';
PRINT '17. SystemLogs - 系统日志表';
PRINT '18. SeleniumScripts - Selenium测试脚本表';
PRINT '19. SeleniumExecutions - Selenium执行记录表';
PRINT '20. SeleniumExecutionLogs - Selenium执行日志表';
PRINT '';
PRINT '已创建视图：vw_ProjectOverview, vw_UserTaskStatistics, vw_AIUsageStatistics';
PRINT '已创建存储过程：sp_GetProjectDetails';
PRINT '已创建全文搜索索引用于需求文档和问题搜索';
PRINT '已创建Selenium测试相关表和索引';
GO
