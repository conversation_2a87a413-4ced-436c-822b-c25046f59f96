-- 创建AI相关表的SQL脚本
-- 执行日期：2024-12-20

-- 1. 创建知识实体表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='KnowledgeEntities' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[KnowledgeEntities] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Type] NVARCHAR(50) NOT NULL,
        [Name] NVARCHAR(200) NOT NULL,
        [ReferenceId] NVARCHAR(50) NULL,
        [PropertiesJson] NTEXT NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [Version] INT NOT NULL DEFAULT 1,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [CreatedBy] NVARCHAR(100) NULL,
        [UpdatedBy] NVARCHAR(100) NULL
    );

    -- 添加索引
    CREATE INDEX IX_KnowledgeEntities_Type ON [dbo].[KnowledgeEntities] ([Type]);
    CREATE INDEX IX_KnowledgeEntities_Name ON [dbo].[KnowledgeEntities] ([Name]);
    CREATE INDEX IX_KnowledgeEntities_ReferenceId ON [dbo].[KnowledgeEntities] ([ReferenceId]);
    CREATE INDEX IX_KnowledgeEntities_Type_Name ON [dbo].[KnowledgeEntities] ([Type], [Name]);

    PRINT '知识实体表 KnowledgeEntities 创建成功';
END
ELSE
BEGIN
    PRINT '知识实体表 KnowledgeEntities 已存在';
END

-- 2. 创建知识关系表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='KnowledgeRelations' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[KnowledgeRelations] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [FromEntityId] INT NOT NULL,
        [ToEntityId] INT NOT NULL,
        [RelationType] NVARCHAR(50) NOT NULL,
        [Weight] FLOAT NOT NULL DEFAULT 1.0,
        [PropertiesJson] NTEXT NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [Version] INT NOT NULL DEFAULT 1,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [CreatedBy] NVARCHAR(100) NULL,
        [UpdatedBy] NVARCHAR(100) NULL,
        
        -- 外键约束
        CONSTRAINT FK_KnowledgeRelations_FromEntity FOREIGN KEY ([FromEntityId]) 
            REFERENCES [dbo].[KnowledgeEntities]([Id]) ON DELETE CASCADE,
        CONSTRAINT FK_KnowledgeRelations_ToEntity FOREIGN KEY ([ToEntityId]) 
            REFERENCES [dbo].[KnowledgeEntities]([Id]) ON DELETE NO ACTION
    );

    -- 添加索引
    CREATE INDEX IX_KnowledgeRelations_FromEntityId ON [dbo].[KnowledgeRelations] ([FromEntityId]);
    CREATE INDEX IX_KnowledgeRelations_ToEntityId ON [dbo].[KnowledgeRelations] ([ToEntityId]);
    CREATE INDEX IX_KnowledgeRelations_RelationType ON [dbo].[KnowledgeRelations] ([RelationType]);
    CREATE INDEX IX_KnowledgeRelations_FromEntity_Type ON [dbo].[KnowledgeRelations] ([FromEntityId], [RelationType]);
    CREATE INDEX IX_KnowledgeRelations_ToEntity_Type ON [dbo].[KnowledgeRelations] ([ToEntityId], [RelationType]);

    PRINT '知识关系表 KnowledgeRelations 创建成功';
END
ELSE
BEGIN
    PRINT '知识关系表 KnowledgeRelations 已存在';
END

-- 3. 创建文档向量表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DocumentVectors' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[DocumentVectors] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [DocumentId] NVARCHAR(100) NOT NULL,
        [ChunkIndex] INT NOT NULL,
        [Content] NTEXT NOT NULL,
        [EmbeddingJson] NTEXT NOT NULL,
        [MetadataJson] NTEXT NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [Version] INT NOT NULL DEFAULT 1,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [CreatedBy] NVARCHAR(100) NULL,
        [UpdatedBy] NVARCHAR(100) NULL
    );

    -- 添加索引
    CREATE INDEX IX_DocumentVectors_DocumentId ON [dbo].[DocumentVectors] ([DocumentId]);
    CREATE INDEX IX_DocumentVectors_DocumentId_ChunkIndex ON [dbo].[DocumentVectors] ([DocumentId], [ChunkIndex]);
    CREATE INDEX IX_DocumentVectors_CreatedAt ON [dbo].[DocumentVectors] ([CreatedAt]);

    PRINT '文档向量表 DocumentVectors 创建成功';
END
ELSE
BEGIN
    PRINT '文档向量表 DocumentVectors 已存在';
END

-- 4. 添加表注释
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'知识实体表，存储项目、人员、技术、模块等实体信息', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'KnowledgeEntities';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'知识关系表，存储实体间的关系信息', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'KnowledgeRelations';

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'文档向量表，存储文档的向量化表示用于语义搜索', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'DocumentVectors';

-- 5. 添加字段注释
-- KnowledgeEntities 字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'实体类型(Project,Person,Technology,Module,Skill,BestPractice)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeEntities', @level2type = N'COLUMN', @level2name = N'Type';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'实体名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeEntities', @level2type = N'COLUMN', @level2name = N'Name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'关联的业务实体ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeEntities', @level2type = N'COLUMN', @level2name = N'ReferenceId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'实体属性JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeEntities', @level2type = N'COLUMN', @level2name = N'PropertiesJson';

-- KnowledgeRelations 字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'源实体ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeRelations', @level2type = N'COLUMN', @level2name = N'FromEntityId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'目标实体ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeRelations', @level2type = N'COLUMN', @level2name = N'ToEntityId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'关系类型(UsesTechnology,WorksOn,ResponsibleFor,HasSkill)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeRelations', @level2type = N'COLUMN', @level2name = N'RelationType';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'关系权重', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'KnowledgeRelations', @level2type = N'COLUMN', @level2name = N'Weight';

-- DocumentVectors 字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'文档ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'DocumentVectors', @level2type = N'COLUMN', @level2name = N'DocumentId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'文档分块索引', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'DocumentVectors', @level2type = N'COLUMN', @level2name = N'ChunkIndex';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'文档内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'DocumentVectors', @level2type = N'COLUMN', @level2name = N'Content';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'向量嵌入JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'DocumentVectors', @level2type = N'COLUMN', @level2name = N'EmbeddingJson';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'元数据JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'DocumentVectors', @level2type = N'COLUMN', @level2name = N'MetadataJson';

PRINT '所有AI相关表创建完成！';
PRINT '请注意：';
PRINT '1. 文档向量表使用简化的向量搜索实现，生产环境建议使用专门的向量数据库';
PRINT '2. 知识图谱功能需要根据实际业务需求填充数据';
PRINT '3. 推理链和Agent功能需要配置相应的AI模型';
