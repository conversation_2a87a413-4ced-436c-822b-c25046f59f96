-- =============================================
-- 创建UI操作类型表
-- 用于维护UI自动化模板步骤的UI操作类型
-- =============================================

USE [ProjectManagementAI]
GO

-- 检查表是否存在，如果存在则删除
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UIActionTypes]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[UIActionTypes]
    PRINT '已删除现有的 UIActionTypes 表'
END
GO

-- 创建UI操作类型表
CREATE TABLE [dbo].[UIActionTypes] (
    [Id] int IDENTITY(1,1) NOT NULL,
    [Value] nvarchar(50) NOT NULL,
    [Label] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [Icon] nvarchar(100) NULL,
    [Color] nvarchar(20) NULL,
    [SortOrder] int NOT NULL DEFAULT 0,
    [IsActive] bit NOT NULL DEFAULT 1,
    [IsBuiltIn] bit NOT NULL DEFAULT 0,
    [NeedsTemplate] bit NOT NULL DEFAULT 0,
    [ParameterSchema] nvarchar(max) NULL,

    -- BaseEntity 字段
    [CreatedTime] datetime2 NOT NULL DEFAULT GETDATE(),
    [UpdatedTime] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL DEFAULT 0,
    [DeletedTime] datetime2 NULL,
    [DeletedBy] int NULL,
    [Version] int NOT NULL DEFAULT 1,
    [Remarks] nvarchar(500) NULL,

    CONSTRAINT [PK_UIActionTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [UK_UIActionTypes_Value] UNIQUE NONCLUSTERED ([Value] ASC)
)
GO

-- 添加字段注释
EXEC sp_addextendedproperty 'MS_Description', 'UI操作类型值', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Value'
EXEC sp_addextendedproperty 'MS_Description', '显示名称', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Label'
EXEC sp_addextendedproperty 'MS_Description', 'UI操作描述', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Description'
EXEC sp_addextendedproperty 'MS_Description', '图标名称', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Icon'
EXEC sp_addextendedproperty 'MS_Description', '颜色标识', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Color'
EXEC sp_addextendedproperty 'MS_Description', '排序顺序', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'SortOrder'
EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'IsActive'
EXEC sp_addextendedproperty 'MS_Description', '是否内置类型', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'IsBuiltIn'
EXEC sp_addextendedproperty 'MS_Description', '是否需要模板', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'NeedsTemplate'
EXEC sp_addextendedproperty 'MS_Description', '参数架构JSON', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'ParameterSchema'
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'CreatedTime'
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'UpdatedTime'
EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'CreatedBy'
EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'UpdatedBy'
EXEC sp_addextendedproperty 'MS_Description', '是否已删除', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'IsDeleted'
EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'DeletedTime'
EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'DeletedBy'
EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Version'
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'UIActionTypes', 'COLUMN', 'Remarks'
GO

-- 创建索引
CREATE NONCLUSTERED INDEX [IX_UIActionTypes_IsActive] ON [dbo].[UIActionTypes] ([IsActive] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_UIActionTypes_SortOrder] ON [dbo].[UIActionTypes] ([SortOrder] ASC)
GO

-- 插入初始数据
PRINT '开始插入UI操作类型初始数据...'

-- UI操作类型
INSERT INTO [dbo].[UIActionTypes] ([Value], [Label], [Description], [Icon], [Color], [SortOrder], [IsActive], [IsBuiltIn], [NeedsTemplate], [ParameterSchema], [CreatedTime])
VALUES
    ('click', '点击', '点击指定的模板元素', 'el-icon-mouse', 'primary', 1, 1, 1, 1, '{"x": "number", "y": "number", "button": "string"}', GETDATE()),
    ('wait', '等待', '等待指定元素出现', 'el-icon-time', 'info', 2, 1, 1, 1, '{"timeout": "number"}', GETDATE()),
    ('input', '输入', '在输入框中输入文本', 'el-icon-edit', 'success', 3, 1, 1, 1, '{"text": "string", "clear": "boolean"}', GETDATE()),
    ('delay', '延迟', '等待指定时间', 'el-icon-timer', 'warning', 4, 1, 1, 0, '{"seconds": "number"}', GETDATE()),
    ('screenshot', '截图', '截取当前屏幕', 'el-icon-camera', 'danger', 5, 1, 1, 0, '{"path": "string", "region": "object"}', GETDATE()),
    ('verify', '验证', '验证元素是否存在', 'el-icon-check', 'info', 6, 1, 1, 1, '{"expected": "boolean", "timeout": "number"}', GETDATE()),
    ('scroll', '滚动', '滚动页面', 'el-icon-sort', 'warning', 7, 1, 1, 0, '{"direction": "string", "distance": "number"}', GETDATE()),
    ('key_press', '按键', '按下指定按键', 'el-icon-position', 'primary', 8, 1, 1, 0, '{"key": "string", "modifier": "array"}', GETDATE())
GO

PRINT 'UI操作类型初始数据插入完成'

-- 验证数据
SELECT COUNT(*) as '插入的记录数' FROM [dbo].[UIActionTypes] WHERE [IsDeleted] = 0
GO

PRINT 'UIActionTypes 表创建完成'
