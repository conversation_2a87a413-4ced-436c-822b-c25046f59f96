<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑开发步骤"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="步骤名称" prop="stepName">
            <el-input v-model="formData.stepName" placeholder="请输入步骤名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="步骤类型" prop="stepType">
            <el-select v-model="formData.stepType" placeholder="请选择步骤类型">
              <el-option label="开发" value="Development" />
              <el-option label="测试" value="Testing" />
              <el-option label="部署" value="Deployment" />
              <el-option label="文档" value="Documentation" />
              <el-option label="设计" value="Design" />
              <el-option label="分析" value="Analysis" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级">
              <el-option label="低" value="Low" />
              <el-option label="中" value="Medium" />
              <el-option label="高" value="High" />
              <el-option label="紧急" value="Critical" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option label="待处理" value="Pending" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="失败" value="Failed" />
              <el-option label="阻塞" value="Blocked" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预估工时" prop="estimatedHours">
            <el-input-number
              v-model="formData.estimatedHours"
              :min="0"
              :max="1000"
              :precision="1"
              placeholder="预估工时（小时）"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际工时" prop="actualHours">
            <el-input-number
              v-model="formData.actualHours"
              :min="0"
              :max="1000"
              :precision="1"
              placeholder="实际工时（小时）"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="技术栈" prop="technologyStack">
            <el-select
              v-model="formData.technologyStack"
              placeholder="请选择技术栈"
              filterable
              allow-create
            >
              <el-option label="Vue.js" value="vue" />
              <el-option label=".NET Core" value="dotnet" />
              <el-option label="SQL Server" value="sqlserver" />
              <el-option label="TypeScript" value="typescript" />
              <el-option label="C#" value="csharp" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组件类型" prop="componentType">
            <el-select v-model="formData.componentType" placeholder="请选择组件类型">
              <el-option label="前端" value="Frontend" />
              <el-option label="后端" value="Backend" />
              <el-option label="数据库" value="Database" />
              <el-option label="API" value="API" />
              <el-option label="配置" value="Configuration" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="文件路径" prop="filePath">
        <el-input v-model="formData.filePath" placeholder="请输入文件路径" />
      </el-form-item>

      <el-form-item label="步骤描述" prop="stepDescription">
        <el-input
          v-model="formData.stepDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入步骤描述"
        />
      </el-form-item>

      <el-form-item label="AI 提示词" prop="aiPrompt">
        <el-input
          v-model="formData.aiPrompt"
          type="textarea"
          :rows="6"
          placeholder="请输入 AI 提示词"
        />
      </el-form-item>

      <!-- 图片上传功能 -->
      <el-form-item label="参考图片">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          multiple
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>

        <el-dialog v-model="dialogImageVisible" title="图片预览">
          <img w-full :src="dialogImageUrl" alt="Preview Image" style="width: 100%" />
        </el-dialog>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadProps, type UploadUserFile } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { DevelopmentStep } from '@/types/development'
import { DevelopmentService } from '@/services/development'

// Props
interface Props {
  modelValue: boolean
  step?: DevelopmentStep
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  step: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<UploadUserFile[]>([])
const dialogImageVisible = ref(false)
const dialogImageUrl = ref('')

const formData = reactive({
  stepName: '',
  stepDescription: '',
  stepType: 'Development',
  priority: 'Medium',
  status: 'Pending',
  estimatedHours: 0,
  actualHours: 0,
  technologyStack: '',
  componentType: '',
  filePath: '',
  aiPrompt: ''
})

// 表单验证规则
const rules: FormRules = {
  stepName: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' },
    { min: 2, max: 200, message: '步骤名称长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  stepType: [
    { required: true, message: '请选择步骤类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听步骤变化，初始化表单数据
watch(() => props.step, (step) => {
  if (step) {
    Object.assign(formData, {
      stepName: step.stepName || '',
      stepDescription: step.stepDescription || '',
      stepType: step.stepType || 'Development',
      priority: step.priority || 'Medium',
      status: step.status || 'Pending',
      estimatedHours: step.estimatedHours || 0,
      actualHours: step.actualHours || 0,
      technologyStack: step.technologyStack || '',
      componentType: step.componentType || '',
      filePath: step.filePath || '',
      aiPrompt: step.aiPrompt || ''
    })
    // 加载现有的参考图片
    loadExistingImages(step)
  }
}, { immediate: true })

// 加载现有图片
const loadExistingImages = (step: any) => {
  fileList.value = []

  if (step.referenceImages) {
    try {
      const imageUrls = JSON.parse(step.referenceImages)

      if (Array.isArray(imageUrls)) {
        fileList.value = imageUrls.map((url: string, index: number) => {
          // 构建完整的图片URL
          let fullUrl = url
          if (!url.startsWith('http')) {
            // 使用专门的图片访问接口
            const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://localhost:61136'
            fullUrl = `${apiBaseUrl}/api/DevelopmentSteps/image/${url}`
          }

          return {
            name: `image-${index + 1}`,
            url: fullUrl,
            uid: Date.now() + index
          }
        })
      }
    } catch (error) {
      console.error('解析参考图片失败:', error)
    }
  }
}

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = async () => {
  if (!formRef.value || !props.step) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 处理新上传的图片
    const newImageUrls: string[] = []
    const existingImageUrls: string[] = []

    for (const fileItem of fileList.value) {
      if (fileItem.raw) {
        // 新上传的图片
        try {
          const response = await DevelopmentService.uploadStepImage(props.step.id, fileItem.raw)
          newImageUrls.push(response.data.filePath)
        } catch (error) {
          console.error('上传图片失败:', error)
          ElMessage.warning(`上传图片 ${fileItem.name} 失败`)
        }
      } else if (fileItem.url) {
        // 已存在的图片，需要提取相对路径
        let relativePath = fileItem.url
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://localhost:61136'
        if (relativePath.startsWith(apiBaseUrl)) {
          relativePath = relativePath.replace(apiBaseUrl + '/', '')
        } else if (relativePath.startsWith('http')) {
          // 如果是其他完整URL，尝试提取路径部分
          try {
            const url = new URL(relativePath)
            relativePath = url.pathname.substring(1) // 移除开头的 /
          } catch (e) {
            console.warn('无法解析图片URL:', relativePath)
          }
        }
        existingImageUrls.push(relativePath)
      }
    }

    // 合并所有图片URL
    const allImageUrls = [...existingImageUrls, ...newImageUrls]

    // 更新步骤数据
    const updateData = {
      ...formData,
      referenceImages: allImageUrls.length > 0 ? JSON.stringify(allImageUrls) : null
    }

    // // 如果有新图片，将图片信息添加到AI提示词中
    // if (newImageUrls.length > 0) {
    //   updateData.aiPrompt = (formData.aiPrompt || '') + `\n\n新增参考图片：\n${newImageUrls.map(url => `- ${url}`).join('\n')}`
    // }

    await DevelopmentService.updateStep(props.step.id, updateData as any)

    ElMessage.success('步骤更新成功')
    emit('save')
    handleClose()
  } catch (error) {
    console.error('更新步骤失败:', error)
    ElMessage.error('更新步骤失败')
  } finally {
    loading.value = false
  }
}

// 图片上传相关方法
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogImageVisible.value = true
}

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type.indexOf('image/') !== 0) {
    ElMessage.error('只能上传图片文件!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}


</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
