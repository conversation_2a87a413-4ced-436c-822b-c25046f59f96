<template>
  <div class="code-sequence-creator" :class="{ 'maximized': props.isParentMaximized === undefined && isMaximized }">
    <!-- 编程语言选择 -->
    <div class="language-selector" v-show="!(props.isParentMaximized === undefined && isMaximized)">
      <el-radio-group v-model="selectedLanguage" @change="handleLanguageChange">
        <el-radio-button label="javascript">JavaScript</el-radio-button>
        <el-radio-button label="python">Python</el-radio-button>
        <el-radio-button label="typescript">TypeScript</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar" v-show="!isMaximized">
      <div class="toolbar-left">
        <el-tag type="info" size="small" style="margin-right: 8px;">
          <el-icon><MagicStick /></el-icon>
          智能提示: Ctrl+Space
        </el-tag>
      </div>
      <div class="toolbar-center">
        <!-- 模板插入按钮组 -->
        <el-button-group>
          <el-button size="small" @click="insertTemplate('basic')" title="插入基础模板">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button size="small" @click="insertTemplate('condition')" title="插入条件判断">
            <el-icon><Share /></el-icon>
          </el-button>
          <el-button size="small" @click="insertTemplate('loop')" title="插入循环控制">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button size="small" @click="insertTemplate('imageCheck')" title="插入图片判断">
            <el-icon><Document /></el-icon>
          </el-button>
          <el-button size="small" @click="insertTemplate('keyboard')" title="插入按键操作">
            <el-icon><MagicStick /></el-icon>
          </el-button>
          <el-button size="small" @click="insertTemplate('screenshot')" title="插入截图操作">
            <el-icon><VideoPlay /></el-icon>
          </el-button>
        </el-button-group>

        <!-- 工具按钮组 -->
        <el-button-group>
          <el-button size="small" @click="showApiReference" title="API参考文档">
            <el-icon><Document /></el-icon>
          </el-button>
          <el-button size="small" @click="triggerIntelliSense" title="触发智能提示 (Ctrl+Space)">
            <el-icon><MagicStick /></el-icon>
          </el-button>
        </el-button-group>

        <!-- 文件操作按钮组 -->
        <el-button-group>
          <el-button size="small" @click="saveToFile" title="导出到文件">
            <el-icon><Share /></el-icon>
          </el-button>
          <el-button size="small" @click="loadFromFile" title="从文件导入">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button size="small" @click="loadFromDatabase" title="从数据库加载">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button size="small" @click="createNewSequence" title="创建新序列">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <!-- 代码操作按钮组 -->
        <el-button-group>
          <el-button size="small" @click="validateCode" title="验证代码语法">
            <el-icon><CircleCheck /></el-icon>
          </el-button>
          <el-button size="small" type="primary" @click="executeCode" title="运行代码测试">
            <el-icon><VideoPlay /></el-icon>
          </el-button>
          <el-button size="small" type="success" @click="generateSequence" title="生成自动化序列">
            <el-icon><MagicStick /></el-icon>
          </el-button>
        </el-button-group>

        <!-- 保存按钮 -->
        <el-button size="small" type="warning" @click="saveSequence" title="保存序列到数据库 (Ctrl+S)">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <!-- 代码编辑器 -->
    <div class="editor-container">
      <!-- 左侧：模板卡片 -->
      <div class="template-panel">
        <div class="panel-header">
          <span>
            <el-icon><Picture /></el-icon>
            图像模板
          </span>
          <div class="header-actions">
            <el-button size="small" text @click="loadTemplates">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        <div class="template-content">
          <div class="template-search">
            <el-input
              v-model="templateSearchKeyword"
              placeholder="搜索模板..."
              size="small"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="template-list" v-loading="templatesLoading">
            <!-- 按分类显示模板 -->
            <div v-for="(templates, category) in groupedTemplates" :key="category" class="template-category-group">
              <div
                class="category-header"
                @click="toggleCategory(category)"
                :class="{ 'collapsed': collapsedCategories[category] }"
              >
                <el-icon class="collapse-icon">
                  <ArrowRight v-if="collapsedCategories[category]" />
                  <ArrowDown v-else />
                </el-icon>
                <el-icon><Folder /></el-icon>
                <span class="category-name">{{ category }}</span>
                <span class="category-count">({{ templates.length }})</span>
              </div>

              <div
                class="category-templates"
                v-show="!collapsedCategories[category]"
              >
                <div
                  v-for="template in templates"
                  :key="template.id"
                  class="template-item"
                  :draggable="true"
                  @dragstart="handleTemplateDragStart(template, $event)"
                  @dblclick="insertTemplateCode(template)"
                  :title="`双击插入模板: ${template.name}`"
                >
                  <div class="template-image">
                    <img
                      v-if="template.filePath"
                      :src="getTemplateImageUrl(template.filePath)"
                      :alt="template.name"
                      @error="handleImageError"
                    />
                    <div v-else class="template-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </div>
                  <div class="template-info">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-usage">使用: {{ template.usageCount || 0 }}次</div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="Object.keys(groupedTemplates).length === 0 && !templatesLoading" class="empty-templates">
              <el-icon><Picture /></el-icon>
              <p>暂无模板</p>
              <el-button size="small" type="primary" @click="openTemplateManager">
                创建模板
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：代码编辑器 -->
      <div class="editor-panel">
        <div class="panel-header">
          <span>{{ selectedLanguage.toUpperCase() }} 代码编辑器</span>
          <span v-if="currentSequence" class="sequence-indicator">
            📝 {{ currentSequence.Name || currentSequence.name || '未命名序列' }}
          </span>
          <span v-if="hasUnsavedChanges" class="unsaved-indicator">● 未保存</span>
          <div class="header-actions">
            <el-button size="small" text @click="toggleMaximize">
              <el-icon><View /></el-icon>
              {{ (props.isParentMaximized !== undefined ? props.isParentMaximized : isMaximized) ? '还原' : '最大化' }}
            </el-button>
            <el-button size="small" type="primary" @click="saveSequence" title="保存序列到数据库 (Ctrl+S)">
              <el-icon><Document /></el-icon>
              💾 保存
            </el-button>
            <el-button size="small" text @click="formatCode">
              <el-icon><Brush /></el-icon>
              格式化
            </el-button>
            <el-button size="small" text @click="clearCode">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>
        <div
          class="editor-content"
          @drop="handleEditorDrop"
          @dragover="handleEditorDragOver"
        >
          <MonacoEditor
            ref="codeEditor"
            v-model="codeContent"
            :language="selectedLanguage"
            :theme="editorTheme"
            :height="editorHeight"
            :options="editorOptions"
            @change="handleCodeChange"
            @focus="handleEditorFocus"
            @blur="handleEditorBlur"
          />
        </div>
      </div>

      <!-- 预览面板 -->
      <div class="preview-panel" v-show="!isMaximized">
        <div class="panel-header">
          <span>序列预览</span>
          <el-button size="small" text @click="refreshPreview">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="preview-content">
          <div v-if="parsedSteps.length > 0" class="steps-preview">
            <div
              v-for="(step, index) in parsedSteps"
              :key="index"
              class="step-preview-item"
            >
              <div class="step-header">
                <span class="step-number">{{ index + 1 }}</span>
                <el-tag :type="getStepTypeTag(step.actionType)" size="small">
                  {{ step.actionType }}
                </el-tag>
              </div>
              <div class="step-description">{{ step.description }}</div>
              <div v-if="step.parameters" class="step-parameters">
                <pre>{{ JSON.stringify(step.parameters, null, 2) }}</pre>
              </div>
            </div>
          </div>
          <div v-else class="empty-preview">
            <el-empty description="暂无步骤，请编写代码生成序列步骤" />
          </div>
        </div>
      </div>
    </div>

    <!-- 控制台输出 -->
    <div class="console-panel" v-show="!isMaximized">
      <div class="panel-header">
        <span>控制台输出</span>
        <el-button size="small" text @click="clearConsole">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
      <div class="console-content">
        <div
          v-for="(log, index) in consoleLogs"
          :key="index"
          :class="['console-log', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <!-- API 参考对话框 -->
    <el-dialog
      v-model="showApiDialog"
      title="自动化序列 API 参考"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="api-reference">
        <el-tabs v-model="activeApiTab">
          <el-tab-pane label="基础操作" name="basic">
            <div class="api-section">
              <h3>基础操作 API</h3>
              <div class="api-item">
                <h4>click(selector, options)</h4>
                <p>基于选择器点击元素（适用于网页、应用程序控件）</p>
                <pre><code>// 点击网页按钮（CSS选择器）
click('#submit-btn', { timeout: 5000 });

// 点击应用程序按钮（控件名称）
click('Button[Name="确定"]');

// 点击坐标
click({ x: 100, y: 200 });</code></pre>
              </div>
              <div class="api-item">
                <h4>input(selector, text, options)</h4>
                <p>在输入框中输入文本</p>
                <pre><code>// 输入文本
input('#username', 'admin');

// 清空后输入
input('#password', 'password123', { clear: true });</code></pre>
              </div>
              <div class="api-item">
                <h4>type(text, options)</h4>
                <p>模拟键盘逐字符输入</p>
                <pre><code>// 模拟真实打字
type('Hello World', { delay: 100 });

// 快速输入
type('快速文本', { delay: 50 });</code></pre>
              </div>
              <div class="api-item">
                <h4>inputText(selector, text)</h4>
                <p>简化的文字输入函数</p>
                <pre><code>// 输入中文文字
inputText('#comment', '这是一段中文评论');

// 输入长文本
inputText('#description', '详细的产品描述信息...');</code></pre>
              </div>
              <div class="api-item">
                <h4>clearInput(selector)</h4>
                <p>清空输入框内容</p>
                <pre><code>// 清空输入框
clearInput('#search-box');

// 清空后重新输入
clearInput('#username');
input('#username', 'new_user');</code></pre>
              </div>
              <div class="api-item">
                <h4>wait(condition, timeout)</h4>
                <p>等待条件满足</p>
                <pre><code>// 等待元素出现
wait('#loading', { visible: true, timeout: 10000 });

// 等待固定时间
waitTime(2000);

// 等待直到条件为真
waitUntil(() => imageExists('success_icon'), { timeout: 15000 });</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="输入操作" name="input">
            <div class="api-section">
              <h3>文字输入 API</h3>
              <div class="api-item">
                <h4>input(selector, text, options)</h4>
                <p>在指定输入框中输入文本（推荐）</p>
                <pre><code>// 基础输入
input('#username', 'admin');
input('#password', 'password123');

// 清空后输入
input('#search', '搜索内容', { clear: true });

// 带选项的输入
input('#email', '<EMAIL>', {
  clear: true,
  timeout: 5000
});</code></pre>
              </div>
              <div class="api-item">
                <h4>type(text, options)</h4>
                <p>模拟真实键盘输入（逐字符）</p>
                <pre><code>// 模拟真实打字
type('Hello World', { delay: 100 });

// 快速输入
type('快速文本', { delay: 50 });

// 慢速输入（模拟思考）
type('仔细思考的内容', { delay: 200 });</code></pre>
              </div>
              <div class="api-item">
                <h4>inputText(selector, text)</h4>
                <p>简化的中文输入函数</p>
                <pre><code>// 输入中文
inputText('#comment', '这是一段中文评论');
inputText('#feedback', '用户反馈信息');

// 输入长文本
inputText('#description', `
  这是一段很长的描述信息，
  可以包含多行内容，
  支持中文和英文混合输入。
`);</code></pre>
              </div>
              <div class="api-item">
                <h4>输入操作组合示例</h4>
                <p>常见的输入场景</p>
                <pre><code>// 登录表单
click('#username');
input('#username', '<EMAIL>');
keyPress('Tab');
input('#password', 'password123');
keyPress('Enter');

// 搜索功能
click('#search-box');
inputText('#search-box', '搜索关键词');
keyPress('Enter');
waitForImage('search_results');

// 表单填写
input('#name', '张三', { clear: true });
input('#phone', '13800138000');
inputText('#address', '北京市朝阳区某某街道');
type('补充说明信息', { delay: 80 });</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流程控制" name="control">
            <div class="api-section">
              <h3>流程控制 API</h3>
              <div class="api-item">
                <h4>if(condition, thenSteps, elseSteps)</h4>
                <p>条件判断</p>
                <pre><code>// 条件执行
if(exists('#error-message'), [
  screenshot('error.png'),
  click('#close-error')
], [
  click('#continue-btn')
]);</code></pre>
              </div>
              <div class="api-item">
                <h4>loop(count, steps)</h4>
                <p>循环执行</p>
                <pre><code>// 循环点击
loop(5, [
  click('#next-btn'),
  wait(1000)
]);</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按键鼠标" name="keyboard">
            <div class="api-section">
              <h3>按键操作 API</h3>
              <div class="api-item">
                <h4>keyPress(key)</h4>
                <p>按下指定按键</p>
                <pre><code>// 常用按键
keyPress('Enter');
keyPress('Tab');
keyPress('Escape');
keyPress('Space');
keyPress('Backspace');</code></pre>
              </div>
              <div class="api-item">
                <h4>keyCombo(combination)</h4>
                <p>按下组合键</p>
                <pre><code>// 常用组合键
keyCombo('Ctrl+C');     // 复制
keyCombo('Ctrl+V');     // 粘贴
keyCombo('Ctrl+S');     // 保存
keyCombo('Alt+Tab');    // 切换窗口
keyCombo('Ctrl+Shift+N'); // 新建</code></pre>
              </div>
              <div class="api-item">
                <h4>鼠标操作</h4>
                <p>各种鼠标操作</p>
                <pre><code>// 鼠标操作
rightClick('#element');        // 右键点击
doubleClick('#element');       // 双击
mouseMove({ x: 100, y: 200 }); // 移动鼠标</code></pre>
              </div>
              <div class="api-item">
                <h4>拖拽操作详解</h4>
                <p>完整的拖拽操作功能</p>
                <pre><code>// 1. 元素间拖拽
drag('#source-item', '#target-zone', { duration: 1000 });
drag('#file', '#upload-area');

// 2. 拖拽到指定坐标
dragToPosition('#slider', { x: 300, y: 200 });
dragToPosition('#movable-item', { x: 100, y: 150 });

// 3. 图像识别拖拽
dragImage('source_template', 'target_template');
dragImage('file_icon', 'trash_bin');

// 4. 实际应用示例
// 文件上传
drag('#local-file', '#upload-dropzone');

// 滑块调整
dragToPosition('#volume-slider', { x: 250, y: 100 });

// 列表重排序
drag('#list-item-1', '#list-item-3');</code></pre>
              </div>
              <div class="api-item">
                <h4>滚动操作</h4>
                <p>页面和元素滚动</p>
                <pre><code>// 滚动操作
scroll('#container', { direction: 'down', distance: 300 });
scroll('#list', { direction: 'up', distance: 200 });
scroll(window, { direction: 'right', distance: 500 });</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="截图操作" name="screenshot">
            <div class="api-section">
              <h3>截图操作 API</h3>
              <div class="api-item">
                <h4>screenshot(filename)</h4>
                <p>截取整个屏幕</p>
                <pre><code>// 基础截图
screenshot('full_screen.png');

// 带时间戳的截图
screenshot('error_' + Date.now() + '.png');</code></pre>
              </div>
              <div class="api-item">
                <h4>screenshotElement(selector, filename)</h4>
                <p>截取指定元素</p>
                <pre><code>// 截取元素
screenshotElement('#dialog', 'dialog.png');
screenshotElement('.error-message', 'error.png');</code></pre>
              </div>
              <div class="api-item">
                <h4>screenshotRegion(region, filename)</h4>
                <p>截取指定区域</p>
                <pre><code>// 截取区域
screenshotRegion({
  x: 100, y: 200,
  width: 800, height: 600
}, 'region.png');</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="验证操作" name="verify">
            <div class="api-section">
              <h3>验证操作 API</h3>
              <div class="api-item">
                <h4>verify(selector, expected)</h4>
                <p>验证元素状态</p>
                <pre><code>// 验证文本
verify('#status', { text: '成功' });

// 验证可见性
verify('#modal', { visible: true });

// 验证存在性
verify('#element', { exists: true });</code></pre>
              </div>
              <div class="api-item">
                <h4>exists(selector)</h4>
                <p>检查元素是否存在</p>
                <pre><code>// 检查元素存在
if (exists('#error-dialog')) {
  click('#close-error');
}</code></pre>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="图片判断" name="image">
            <div class="api-section">
              <h3>图片判断 API</h3>
              <div class="api-item">
                <h4>imageExists(templateName, options)</h4>
                <p>检查图片模板是否存在于屏幕上</p>
                <pre><code>// 基础图片检查
if (imageExists('login_button')) {
  console.log('登录按钮存在');
}

// 带置信度的检查
if (imageExists('success_icon', { confidence: 0.8 })) {
  click('next_button');
}</code></pre>
              </div>
              <div class="api-item">
                <h4>waitForImage(templateName, options)</h4>
                <p>等待图片出现，超时则失败</p>
                <pre><code>// 等待加载完成
waitForImage('loading_complete', { timeout: 10000 });

// 等待错误消息出现
waitForImage('error_message', {
  timeout: 5000,
  confidence: 0.9
});</code></pre>
              </div>
              <div class="api-item">
                <h4>clickImage(templateName, options)</h4>
                <p>基于图像识别点击目标（适用于任何可视化界面）</p>
                <pre><code>// 点击图片模板（通过图像识别）
clickImage('submit_button', { confidence: 0.8 });

// 点击游戏界面按钮
clickImage('start_game_btn', { confidence: 0.9 });

// 点击并等待
clickImage('refresh_btn', {
  confidence: 0.9,
  waitAfter: 2000
});</code></pre>
              </div>
              <div class="api-item">
                <h4>click vs clickImage 使用对比</h4>
                <p>选择合适的点击方法</p>
                <pre><code>// 网页自动化 - 使用 click
click('#login-button');           // CSS选择器
click('input[name="username"]');  // 属性选择器

// 桌面应用自动化 - 使用 click
click('Button[Name="确定"]');      // UIA控件
click('MenuItem[Name="文件"]');    // 菜单项

// 图像识别自动化 - 使用 clickImage
clickImage('login_btn_template'); // 图片模板
clickImage('game_start_button');  // 游戏界面
clickImage('dialog_ok_btn');      // 对话框按钮

// 混合使用场景
if (imageExists('captcha_image')) {
  // 有验证码时用图像识别
  clickImage('captcha_input');
} else {
  // 正常登录用选择器
  click('#username');
}</code></pre>
              </div>
              <div class="api-item">
                <h4>图片条件判断示例</h4>
                <p>常见的图片判断使用场景</p>
                <pre><code>// 重试机制
while (!imageExists('success_template')) {
  if (imageExists('retry_button')) {
    clickImage('retry_button');
    wait(2000);
  } else {
    wait(1000);
  }
}

// 条件分支
if (imageExists('error_dialog')) {
  clickImage('close_error');
  screenshot('error_handled.png');
} else if (imageExists('warning_dialog')) {
  clickImage('ignore_warning');
} else {
  clickImage('continue_button');
}</code></pre>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 保存序列对话框 -->
    <CodeSequenceSaveDialog
      v-model="showSaveDialogVisible"
      :code-language="selectedLanguage"
      :code-lines="codeContent.split('\n').length"
      :step-count="parsedSteps.length"
      :current-sequence="currentSequence"
      @save="handleSaveSequence"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Share, Refresh, Document, CircleCheck, VideoPlay, MagicStick,
  Brush, Delete, View, Picture, Search, Folder, ArrowRight, ArrowDown
} from '@element-plus/icons-vue'
import MonacoEditor from '@/components/common/MonacoEditor.vue'
import CodeSequenceSaveDialog from './CodeSequenceSaveDialog.vue'
import CustomTemplateService from '@/services/customTemplate'

// Props
interface Props {
  isParentMaximized?: boolean
  editingSequence?: any
}

const props = withDefaults(defineProps<Props>(), {
  isParentMaximized: false,
  editingSequence: null
})

// Emits
const emit = defineEmits<{
  'sequence-created': [sequence: any]
  'toggle-maximize': []
}>()

// 响应式数据
const selectedLanguage = ref<'javascript' | 'python' | 'typescript'>('javascript')
const codeContent = ref('')
const parsedSteps = ref<any[]>([])
const consoleLogs = ref<Array<{ type: string; message: string; time: string }>>([])
const showApiDialog = ref(false)
const showSaveDialogVisible = ref(false)
const activeApiTab = ref('basic')
const availableTemplates = ref<any[]>([])
const templateSearchKeyword = ref('')
const templatesLoading = ref(false)
const collapsedCategories = ref<Record<string, boolean>>({})
const codeEditor = ref()
const isMaximized = ref(false)
const hasUnsavedChanges = ref(false)
const lastSavedContent = ref('')
// 新增：当前加载的序列信息（用于更新）
const currentSequence = ref<any>(null)

// 监听编辑序列的变化
watch(() => props.editingSequence, (newSequence) => {
  if (newSequence) {
    loadSequenceForEditing(newSequence)
  }
}, { immediate: true })

// 计算属性
const isCodeEmpty = computed(() => !codeContent.value.trim())
const hasSteps = computed(() => parsedSteps.value.length > 0)

// 过滤模板
const filteredTemplates = computed(() => {
  if (!templateSearchKeyword.value) {
    return availableTemplates.value
  }
  return availableTemplates.value.filter(template =>
    template.name.toLowerCase().includes(templateSearchKeyword.value.toLowerCase()) ||
    template.description?.toLowerCase().includes(templateSearchKeyword.value.toLowerCase())
  )
})

// 按分类分组模板
const groupedTemplates = computed(() => {
  const groups: Record<string, any[]> = {}

  filteredTemplates.value.forEach(template => {
    const category = template.category || '其他'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(template)
  })

  // 按分类名称排序
  const sortedGroups: Record<string, any[]> = {}
  Object.keys(groups).sort().forEach(category => {
    // 按使用次数和名称排序模板
    sortedGroups[category] = groups[category].sort((a, b) => {
      // 先按使用次数降序
      const usageA = a.usageCount || 0
      const usageB = b.usageCount || 0
      if (usageA !== usageB) {
        return usageB - usageA
      }
      // 再按名称升序
      return a.name.localeCompare(b.name)
    })
  })

  return sortedGroups
})

// 编辑器配置
const editorTheme = 'vs'  // 固定使用亮色主题
const editorHeight = ref('800px')
const editorOptions = computed(() => ({
  fontSize: 14,
  lineNumbers: 'on' as const,
  roundedSelection: false,
  scrollBeyondLastLine: false,
  minimap: { enabled: true },
  wordWrap: 'on' as const,
  contextmenu: true,
  selectOnLineNumbers: true,
  glyphMargin: true,
  folding: true,
  foldingStrategy: 'indentation' as const,
  showFoldingControls: 'always' as const,
  bracketPairColorization: { enabled: true },
  guides: {
    bracketPairs: true,
    indentation: true
  },
  suggest: {
    showKeywords: true,
    showSnippets: true,
    showFunctions: true
  },
  tabSize: 2,
  insertSpaces: true,
  detectIndentation: false
}))

// 初始化代码模板
const initializeCode = () => {
  const templates = {
    javascript: `// 自动化序列脚本
// 使用 JavaScript 语法编写自动化步骤

// 基础操作示例
click('#login-btn');
input('#username', 'admin');
input('#password', 'password123');
click('#submit');

// 等待页面加载
wait('#dashboard', { visible: true, timeout: 10000 });

// 条件判断
if(exists('#welcome-message'), [
  screenshot('welcome.png'),
  click('#close-welcome')
]);

// 循环操作
loop(3, [
  click('#refresh-btn'),
  wait(2000)
]);

// 验证结果
verify('#status', { text: '登录成功' });`,

    python: `# 自动化序列脚本
# 使用 Python 语法编写自动化步骤

# 基础操作示例
click('#login-btn')
input('#username', 'admin')
input('#password', 'password123')
click('#submit')

# 等待页面加载
wait('#dashboard', visible=True, timeout=10000)

# 条件判断
if exists('#welcome-message'):
    screenshot('welcome.png')
    click('#close-welcome')

# 循环操作
for i in range(3):
    click('#refresh-btn')
    wait(2000)

# 验证结果
verify('#status', text='登录成功')`,

    typescript: `// 自动化序列脚本
// 使用 TypeScript 语法编写自动化步骤

interface ClickOptions {
  timeout?: number;
  retries?: number;
}

interface InputOptions {
  clear?: boolean;
  timeout?: number;
}

// 基础操作示例
click('#login-btn');
input('#username', 'admin');
input('#password', 'password123');
click('#submit');

// 等待页面加载
wait('#dashboard', { visible: true, timeout: 10000 });

// 条件判断
if (exists('#welcome-message')) {
  screenshot('welcome.png');
  click('#close-welcome');
}

// 循环操作
for (let i = 0; i < 3; i++) {
  click('#refresh-btn');
  wait(2000);
}

// 验证结果
verify('#status', { text: '登录成功' });`
  }

  codeContent.value = templates[selectedLanguage.value]
  // 清空当前序列信息（表示这是新序列）
  currentSequence.value = null
}

// 编辑器事件处理
const handleEditorFocus = () => {
  addConsoleLog('info', '编辑器获得焦点')
}

const handleEditorBlur = () => {
  addConsoleLog('info', '编辑器失去焦点')
}

// 切换最大化
const toggleMaximize = () => {
  // 如果有父级最大化控制，则触发父级最大化
  if (props.isParentMaximized !== undefined) {
    emit('toggle-maximize')
    addConsoleLog('info', `${props.isParentMaximized ? '还原' : '最大化'}智能序列创建窗口`)
  } else {
    // 否则使用原来的本地最大化
    isMaximized.value = !isMaximized.value
    addConsoleLog('info', `编辑器${isMaximized.value ? '最大化' : '还原'}`)
  }
}






// 显示序列选择对话框
const showSequenceSelectionDialog = async (sequences: any[], sequenceList: string[]): Promise<number | null> => {
  return new Promise((resolve) => {
    // 创建选择对话框的HTML
    const optionsHtml = sequenceList.map((item, index) =>
      `<option value="${index}">${index + 1}. ${item}</option>`
    ).join('')

    const formHtml = `
      <div style="text-align: left;">
        <p style="margin-bottom: 15px; color: #666;">找到 ${sequences.length} 个序列，请选择要加载的序列：</p>
        <select id="sequence-selector"
                style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
          <option value="">请选择序列</option>
          ${optionsHtml}
        </select>
        <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-size: 12px; color: #666;">
          <strong>提示：</strong>如果显示"未命名序列"或"未分类"，可能是数据库字段问题，请检查控制台调试信息。
        </div>
      </div>
    `

    ElMessageBox({
      title: '从数据库加载序列',
      message: formHtml,
      dangerouslyUseHTMLString: true,
      showCancelButton: true,
      confirmButtonText: '加载',
      cancelButtonText: '取消',
      beforeClose: (action, _instance, done) => {
        if (action === 'confirm') {
          const selector = document.getElementById('sequence-selector') as HTMLSelectElement
          const selectedValue = selector?.value

          if (!selectedValue) {
            ElMessage.warning('请选择要加载的序列')
            return
          }

          resolve(parseInt(selectedValue))
        } else {
          resolve(null)
        }
        done()
      }
    }).catch(() => {
      resolve(null)
    })
  })
}

// 保存序列到数据库
const saveSequence = async () => {
  try {
    // 首先解析代码生成步骤
    parseCode()

    if (parsedSteps.value.length === 0) {
      ElMessage.warning('请先编写代码生成序列步骤')
      return
    }

    // 显示保存对话框
    showSaveDialogVisible.value = true
  } catch (error: any) {
    console.error('保存序列失败:', error)
    addConsoleLog('error', `保存失败: ${error.message || error}`)
    ElMessage.error(`保存失败: ${error.message || '请检查网络连接'}`)
  }
}

// 处理保存序列事件
const handleSaveSequence = async (sequenceInfo: any) => {
  try {
    const isUpdate = currentSequence.value && (currentSequence.value.Id || currentSequence.value.id)
    console.log('保存序列调试信息:', {
      currentSequence: currentSequence.value,
      isUpdate,
      sequenceId: currentSequence.value?.Id || currentSequence.value?.id
    })
    addConsoleLog('info', `正在${isUpdate ? '更新' : '保存'}序列到数据库...`)

    // 构建序列数据 - 符合 UIAutomationTemplateSequences 表结构
    const sequenceData = {
      Name: sequenceInfo.name,
      Description: sequenceInfo.description,
      Category: sequenceInfo.category,
      Tags: JSON.stringify([sequenceInfo.category, selectedLanguage.value, '编辑器创建']),
      Notes: sequenceInfo.notes || `语言: ${selectedLanguage.value}\n${isUpdate ? '更新' : '创建'}时间: ${new Date().toLocaleString()}`,
      IsActive: true,
      // 新增：直接保存原生代码
      SourceCode: codeContent.value,
      CodeLanguage: selectedLanguage.value,
      Steps: parsedSteps.value.map((step, index) => ({
        StepOrder: index + 1,
        ActionType: step.actionType || 'click',
        LogicType: step.logicType || null,
        Description: step.description || `步骤 ${index + 1}`,
        Parameters: step.parameters || {},
        TimeoutSeconds: step.timeout || 5,
        MaxRetries: step.retries || 3,
        IsActive: true,
        ConditionExpression: step.condition || '',
        JumpToStepId: step.jumpTo || null,
        LoopCount: step.loopCount || null,
        LoopVariable: step.loopVariable || '',
        GroupId: step.groupId || ''
      })),
      // 保留扩展属性用于其他元数据
      ExtendedProperties: JSON.stringify({
        createdBy: 'CodeEditor',
        version: '1.0.0',
        editorSettings: {
          theme: 'vs',
          fontSize: 14,
          wordWrap: true
        }
      })
    }

    // 根据是否为更新选择不同的API端点和方法
    const sequenceId = currentSequence.value?.Id || currentSequence.value?.id
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'
    const url = isUpdate
      ? `${apiBaseUrl}/api/UIAutomationTemplateSequences/${sequenceId}`
      : `${apiBaseUrl}/api/UIAutomationTemplateSequences`
    const method = isUpdate ? 'PUT' : 'POST'

    console.log('API调用信息:', { url, method, isUpdate })
    addConsoleLog('info', `API调用: ${method} ${url}`)

    const response = await fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sequenceData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    const savedSequence = await response.json()

    // 更新当前序列信息
    if (isUpdate) {
      currentSequence.value = { ...currentSequence.value, ...savedSequence }
    } else {
      currentSequence.value = savedSequence
    }

    // 触发保存事件
    emit('sequence-created', savedSequence)

    // 更新保存状态
    lastSavedContent.value = codeContent.value
    hasUnsavedChanges.value = false

    addConsoleLog('success', `序列 "${sequenceInfo.name}" 已${isUpdate ? '更新' : '保存'}到数据库 (ID: ${savedSequence.Id || currentSequence.value.Id})`)
    ElMessage.success(`序列${isUpdate ? '更新' : '保存'}成功！`)

  } catch (error: any) {
    console.error('保存序列失败:', error)
    addConsoleLog('error', `保存失败: ${error.message || error}`)
    ElMessage.error(`保存失败: ${error.message || '请检查网络连接'}`)
  }
}

// 保存到文件
const saveToFile = () => {
  try {
    // 解析代码生成步骤
    parseCode()

    const sequenceData = {
      name: '代码编辑器序列',
      description: `通过代码编辑器创建的${selectedLanguage.value.toUpperCase()}序列`,
      language: selectedLanguage.value,
      code: codeContent.value,
      steps: parsedSteps.value,
      createdAt: new Date().toISOString(),
      version: '1.0.0'
    }

    // 创建下载链接
    const dataStr = JSON.stringify(sequenceData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `automation-sequence-${Date.now()}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    addConsoleLog('success', '序列已导出到文件')
    ElMessage.success('序列导出成功！')

  } catch (error) {
    addConsoleLog('error', `导出失败: ${error}`)
    ElMessage.error('导出失败，请重试')
  }
}

// 从文件加载
const loadFromFile = () => {
  try {
    // 创建文件输入元素
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'

    input.onchange = (event: any) => {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e: any) => {
        try {
          const sequenceData = JSON.parse(e.target.result)

          // 验证数据格式
          if (!sequenceData.code && !sequenceData.steps) {
            throw new Error('无效的序列文件格式')
          }

          // 加载代码
          if (sequenceData.code) {
            codeContent.value = sequenceData.code
          }

          // 设置语言
          if (sequenceData.language) {
            selectedLanguage.value = sequenceData.language
          }

          // 解析步骤
          parseCode()

          addConsoleLog('success', `序列 "${sequenceData.name || '未命名'}" 加载成功`)
          ElMessage.success('序列导入成功！')

        } catch (error) {
          addConsoleLog('error', `导入失败: ${error}`)
          ElMessage.error('导入失败，请检查文件格式')
        }
      }
      reader.readAsText(file)
    }

    input.click()

  } catch (error) {
    addConsoleLog('error', `导入失败: ${error}`)
    ElMessage.error('导入失败，请重试')
  }
}

// 加载序列用于编辑
const loadSequenceForEditing = async (sequence: any) => {
  try {
    const sequenceName = sequence.Name || sequence.name || '未命名序列'
    addConsoleLog('info', `正在加载序列用于编辑: ${sequenceName}`)

    // 优先使用新的 SourceCode 字段
    let originalCode = sequence.SourceCode || sequence.sourceCode || ''
    let language = sequence.CodeLanguage || sequence.codeLanguage || 'javascript'

    // 如果新字段为空，尝试从扩展属性获取
    if (!originalCode) {
      const extProps = sequence.ExtendedProperties || sequence.extendedProperties
      if (extProps) {
        const parsedProps = typeof extProps === 'string' ? safeJsonParse(extProps, {}, '扩展属性') : extProps
        originalCode = parsedProps.originalCode || ''
        language = parsedProps.language || language
      }
    }

    // 如果仍然没有原始代码，尝试从步骤重构代码
    const steps = sequence.Steps || sequence.steps || []
    if (!originalCode && steps.length > 0) {
      originalCode = reconstructCodeFromSteps(steps, language)
      addConsoleLog('warning', '未找到原始代码，已从步骤重构')
    }

    // 加载到编辑器
    if (originalCode) {
      codeContent.value = originalCode
      selectedLanguage.value = language as any
      parseCode()

      // 保存当前序列信息（用于更新）
      currentSequence.value = sequence

      // 更新保存状态
      lastSavedContent.value = originalCode
      hasUnsavedChanges.value = false

      addConsoleLog('success', `序列 "${sequenceName}" 加载成功，可以编辑`)
    } else {
      // 即使没有原始代码，也保存序列信息
      currentSequence.value = sequence
      addConsoleLog('warning', '序列中没有找到原始代码，请手动编写')
    }

  } catch (error: any) {
    console.error('加载序列失败:', error)
    addConsoleLog('error', `加载序列失败: ${error.message || error}`)
    ElMessage.error('加载序列失败')
  }
}

// 从数据库加载序列
const loadFromDatabase = async () => {
  try {
    addConsoleLog('info', '正在从数据库加载序列列表...')

    // 获取序列列表
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'
    const response = await fetch(`${apiBaseUrl}/api/UIAutomationTemplateSequences`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const sequences = await response.json()

    // 调试：打印返回的数据结构
    console.log('API返回的序列数据:', sequences)
    if (sequences.length > 0) {
      console.log('第一个序列的结构:', sequences[0])
      console.log('字段检查:', {
        Name: sequences[0].Name,
        name: sequences[0].name,
        Category: sequences[0].Category,
        category: sequences[0].category,
        Description: sequences[0].Description,
        description: sequences[0].description
      })
    }

    if (!sequences || sequences.length === 0) {
      ElMessage.info('数据库中暂无序列')
      return
    }

    // 创建序列选择列表 - 兼容不同的字段名格式
    const sequenceList = sequences.map((seq: any) => {
      const name = seq.Name || seq.name || '未命名序列'
      const category = seq.Category || seq.category || '未分类'
      const description = seq.Description || seq.description || '无描述'
      return `${name} (${category}) - ${description}`
    })

    // 使用更好的选择对话框
    const selectedSequenceIndex = await showSequenceSelectionDialog(sequences, sequenceList)
    if (selectedSequenceIndex === null) return

    // 获取选中的序列详情
    const selectedIndex = selectedSequenceIndex
    const selectedSequence = sequences[selectedIndex]
    if (!selectedSequence) {
      throw new Error('未找到选中的序列')
    }

    const sequenceName = selectedSequence.Name || selectedSequence.name || '未命名序列'
    addConsoleLog('info', `正在加载序列: ${sequenceName}`)

    // 调试：打印选中序列的详细信息
    console.log('选中的序列详情:', selectedSequence)

    // 优先使用新的 SourceCode 字段（兼容不同字段名格式）
    let originalCode = selectedSequence.SourceCode || selectedSequence.sourceCode || ''
    let language = selectedSequence.CodeLanguage || selectedSequence.codeLanguage || 'javascript'

    // 如果新字段为空，尝试从扩展属性获取（兼容旧数据）
    if (!originalCode) {
      const extProps = selectedSequence.ExtendedProperties || selectedSequence.extendedProperties
      if (extProps) {
        const parsedProps = typeof extProps === 'string' ? safeJsonParse(extProps, {}, '扩展属性') : extProps
        originalCode = parsedProps.originalCode || ''
        language = parsedProps.language || language
      }
    }

    // 如果仍然没有原始代码，尝试从步骤重构代码
    const steps = selectedSequence.Steps || selectedSequence.steps || []
    if (!originalCode && steps.length > 0) {
      originalCode = reconstructCodeFromSteps(steps, language)
      addConsoleLog('warning', '未找到原始代码，已从步骤重构')
    }

    // 加载到编辑器
    if (originalCode) {
      codeContent.value = originalCode
      selectedLanguage.value = language as any
      parseCode()

      // 保存当前序列信息（用于更新）
      currentSequence.value = selectedSequence
      console.log('加载序列到currentSequence:', {
        selectedSequence,
        Id: selectedSequence.Id || selectedSequence.id,
        Name: selectedSequence.Name || selectedSequence.name
      })

      // 更新保存状态
      lastSavedContent.value = originalCode
      hasUnsavedChanges.value = false

      addConsoleLog('success', `序列 "${sequenceName}" 加载成功 (ID: ${selectedSequence.Id || selectedSequence.id})`)
      ElMessage.success('序列加载成功！可以编辑后保存更新')
    } else {
      // 即使没有原始代码，也保存序列信息
      currentSequence.value = selectedSequence
      addConsoleLog('warning', '序列中没有找到原始代码，请手动编写')
      ElMessage.warning('序列加载成功，但未找到原始代码')
    }

  } catch (error: any) {
    if (error === 'cancel') {
      addConsoleLog('info', '用户取消加载')
    } else {
      console.error('从数据库加载失败:', error)
      addConsoleLog('error', `加载失败: ${error.message || error}`)
      ElMessage.error(`加载失败: ${error.message || '请检查网络连接'}`)
    }
  }
}

// 从步骤重构代码（简单实现）
const reconstructCodeFromSteps = (steps: any[], language: string) => {
  const codeLines: string[] = []

  if (language === 'javascript') {
    codeLines.push('// 从数据库步骤重构的代码')
    steps.forEach((step, index) => {
      const action = step.ActionType || 'click'
      const desc = step.Description || `步骤 ${index + 1}`

      codeLines.push(`// ${desc}`)

      if (action === 'click') {
        codeLines.push(`click('#selector');`)
      } else if (action === 'input') {
        codeLines.push(`input('#selector', 'text');`)
      } else if (action === 'wait') {
        codeLines.push(`wait(${step.TimeoutSeconds || 5}000);`)
      } else {
        codeLines.push(`${action}();`)
      }

      if (index < steps.length - 1) {
        codeLines.push('')
      }
    })
  } else {
    // Python 或其他语言的重构逻辑
    codeLines.push('# 从数据库步骤重构的代码')
    steps.forEach((step, index) => {
      codeLines.push(`# ${step.Description || `步骤 ${index + 1}`}`)
      codeLines.push(`${step.ActionType || 'click'}()`)
      if (index < steps.length - 1) {
        codeLines.push('')
      }
    })
  }

  return codeLines.join('\n')
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isMaximized.value) {
    isMaximized.value = false
    addConsoleLog('info', '编辑器还原（ESC键）')
  }

  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveSequence()
    addConsoleLog('info', '快捷键保存（Ctrl+S）')
  }

  // Ctrl+O 打开文件
  if (event.ctrlKey && event.key === 'o') {
    event.preventDefault()
    loadFromFile()
    addConsoleLog('info', '快捷键导入（Ctrl+O）')
  }

  // Ctrl+E 导出文件
  if (event.ctrlKey && event.key === 'e') {
    event.preventDefault()
    saveToFile()
    addConsoleLog('info', '快捷键导出（Ctrl+E）')
  }

  // Ctrl+L 从数据库加载
  if (event.ctrlKey && event.key === 'l') {
    event.preventDefault()
    loadFromDatabase()
    addConsoleLog('info', '快捷键从数据库加载（Ctrl+L）')
  }
}



// 处理语言切换
const handleLanguageChange = (language: string | number | boolean | undefined) => {
  selectedLanguage.value = language as 'python' | 'typescript' | 'javascript'
  initializeCode()
  parseCode()
}

// 创建新序列
const createNewSequence = () => {
  ElMessageBox.confirm('确定要创建新序列吗？当前未保存的更改将丢失。', '创建新序列', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    currentSequence.value = null
    initializeCode()
    parseCode()
    lastSavedContent.value = ''
    hasUnsavedChanges.value = false
    addConsoleLog('info', '已创建新序列，可以开始编写代码')
    ElMessage.success('新序列创建成功！')
  }).catch(() => {
    // 用户取消
  })
}

// 插入代码模板
const insertTemplate = (type: string) => {
  const templates = {
    basic: {
      javascript: `
// 基础操作模板
click('#element-selector');
input('#input-field', 'text content');
wait(2000);`,
      python: `
# 基础操作模板
click('#element-selector')
input('#input-field', 'text content')
wait(2000)`,
      typescript: `
// 基础操作模板
click('#element-selector');
input('#input-field', 'text content');
wait(2000);`
    },
    condition: {
      javascript: `
// 条件判断模板
if(exists('#condition-element'), [
  // 条件为真时执行
  click('#true-action'),
  screenshot('condition-true.png')
], [
  // 条件为假时执行
  click('#false-action'),
  screenshot('condition-false.png')
]);`,
      python: `
# 条件判断模板
if exists('#condition-element'):
    # 条件为真时执行
    click('#true-action')
    screenshot('condition-true.png')
else:
    # 条件为假时执行
    click('#false-action')
    screenshot('condition-false.png')`,
      typescript: `
// 条件判断模板
if (exists('#condition-element')) {
  // 条件为真时执行
  click('#true-action');
  screenshot('condition-true.png');
} else {
  // 条件为假时执行
  click('#false-action');
  screenshot('condition-false.png');
}`
    },
    loop: {
      javascript: `
// 循环控制模板
loop(5, [
  click('#repeat-action'),
  wait(1000),
  verify('#status', { visible: true })
]);`,
      python: `
# 循环控制模板
for i in range(5):
    click('#repeat-action')
    wait(1000)
    verify('#status', visible=True)`,
      typescript: `
// 循环控制模板
for (let i = 0; i < 5; i++) {
  click('#repeat-action');
  wait(1000);
  verify('#status', { visible: true });
}`
    },
    imageCheck: {
      javascript: `
// 图片存在判断模板
if (imageExists('template_name', { confidence: 0.8 })) {
  console.log('图片存在，执行操作');
  click('target_template');
} else {
  console.log('图片不存在，跳过操作');
}

// 等待图片出现
waitForImage('loading_template', { timeout: 10000 });

// 图片条件循环 - 重试机制
while (!imageExists('success_template')) {
  console.log('等待成功图片出现...');

  if (imageExists('retry_button')) {
    console.log('发现重试按钮，点击重试');
    click('retry_button');
    wait(2000);
  } else {
    wait(1000);
  }
}`,
      python: `
# 图片存在判断模板
if image_exists('template_name', confidence=0.8):
    print('图片存在，执行操作')
    click('target_template')
else:
    print('图片不存在，跳过操作')

# 等待图片出现
wait_for_image('loading_template', timeout=10000)

# 图片条件循环 - 重试机制
while not image_exists('success_template'):
    print('等待成功图片出现...')

    if image_exists('retry_button'):
        print('发现重试按钮，点击重试')
        click('retry_button')
        wait(2000)
    else:
        wait(1000)`,
      typescript: `
// 图片存在判断模板
if (imageExists('template_name', { confidence: 0.8 })) {
  console.log('图片存在，执行操作');
  click('target_template');
} else {
  console.log('图片不存在，跳过操作');
}

// 等待图片出现
waitForImage('loading_template', { timeout: 10000 });

// 图片条件循环 - 重试机制
while (!imageExists('success_template')) {
  console.log('等待成功图片出现...');

  if (imageExists('retry_button')) {
    console.log('发现重试按钮，点击重试');
    click('retry_button');
    wait(2000);
  } else {
    wait(1000);
  }
}`
    },
    keyboard: {
      javascript: `
// 按键操作模板
keyPress('Enter');
keyCombo('Ctrl+C');
keyCombo('Ctrl+V');

// 鼠标操作
rightClick('#element');
doubleClick('#element');
drag('#source', '#target');`,
      python: `
# 按键操作模板
key_press('Enter')
key_combo('Ctrl+C')
key_combo('Ctrl+V')

# 鼠标操作
right_click('#element')
double_click('#element')
drag('#source', '#target')`,
      typescript: `
// 按键操作模板
keyPress('Enter');
keyCombo('Ctrl+C');
keyCombo('Ctrl+V');

// 鼠标操作
rightClick('#element');
doubleClick('#element');
drag('#source', '#target');`
    },
    screenshot: {
      javascript: `
// 截图操作模板
screenshot('full_screen.png');
screenshotElement('#dialog', 'dialog.png');
screenshotRegion({ x: 0, y: 0, width: 800, height: 600 }, 'region.png');`,
      python: `
# 截图操作模板
screenshot('full_screen.png')
screenshot_element('#dialog', 'dialog.png')
screenshot_region({'x': 0, 'y': 0, 'width': 800, 'height': 600}, 'region.png')`,
      typescript: `
// 截图操作模板
screenshot('full_screen.png');
screenshotElement('#dialog', 'dialog.png');
screenshotRegion({ x: 0, y: 0, width: 800, height: 600 }, 'region.png');`
    }
  }

  const template = templates[type as keyof typeof templates]?.[selectedLanguage.value]
  if (template) {
    if (codeEditor.value?.insertText) {
      // 使用 Monaco Editor 的 insertText 方法
      codeEditor.value.insertText(template)
    } else {
      // 降级到直接添加到末尾
      codeContent.value += template
    }
    parseCode()
  }
}

// 显示API参考
const showApiReference = () => {
  showApiDialog.value = true
}

// 手动触发智能提示
const triggerIntelliSense = () => {
  if (codeEditor.value?.triggerSuggest) {
    codeEditor.value.triggerSuggest()
    addConsoleLog('info', '已触发智能提示 (Ctrl+Space)')
    ElMessage.success('智能提示已激活，请在编辑器中输入代码')
  } else if (codeEditor.value?.reregisterSuggestions) {
    // 如果直接触发失败，尝试重新注册智能提示
    codeEditor.value.reregisterSuggestions()
    addConsoleLog('info', '已重新注册智能提示功能')
    ElMessage.info('智能提示功能已重新初始化')
  } else {
    addConsoleLog('warning', '编辑器智能提示功能不可用')
    ElMessage.warning('编辑器智能提示功能不可用，请检查编辑器状态')
  }
}



// 加载模板列表
const loadTemplates = async () => {
  try {
    templatesLoading.value = true
    // 使用前端的API服务，它会自动处理认证
    const result = await CustomTemplateService.getTemplates({
      page: 1,
      pageSize: 100, // 获取更多模板
      isActive: true // 只获取活跃的模板
    })

    availableTemplates.value = result.items || []
    addConsoleLog('info', `加载了 ${availableTemplates.value.length} 个模板`)

    // 初始化分类折叠状态
    setTimeout(() => {
      initializeCategoryStates()
    }, 100) // 等待计算属性更新
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
    addConsoleLog('error', '加载模板失败: ' + error)
  } finally {
    templatesLoading.value = false
  }
}



// 插入模板代码
const insertTemplateCode = (template: any) => {
  // 只插入模板名称
  const templateName = template.name

  // 在当前光标位置插入模板名称
  const currentCode = codeContent.value
  const newCode = currentCode + (currentCode ? '\n' : '') + templateName

  codeContent.value = newCode
  parseCode()
  addConsoleLog('info', `插入了模板名称: ${template.name}`)
}

// 处理模板拖拽开始
const handleTemplateDragStart = (template: any, event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(template))
    event.dataTransfer.effectAllowed = 'copy'
    addConsoleLog('info', `开始拖拽模板: ${template.name}`)
  }
}

// 处理编辑器拖拽悬停
const handleEditorDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

// 处理编辑器拖拽放置
const handleEditorDrop = (event: DragEvent) => {
  event.preventDefault()
  try {
    const templateData = event.dataTransfer?.getData('application/json')
    if (templateData) {
      const template = JSON.parse(templateData)
      insertTemplateCode(template)
      addConsoleLog('success', `通过拖拽插入模板: ${template.name}`)
    }
  } catch (error) {
    console.error('拖拽处理失败:', error)
    addConsoleLog('error', '拖拽处理失败')
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // 显示占位符
  const placeholder = img.parentElement?.querySelector('.template-placeholder')
  if (placeholder) {
    (placeholder as HTMLElement).style.display = 'flex'
  }
}

// 打开模板管理器
const openTemplateManager = () => {
  window.open('/automation/templates', '_blank')
}

// 获取模板图片URL
const getTemplateImageUrl = (filePath: string) => {
  if (!filePath) return ''

  // 如果已经是完整URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath
  }

  // 使用配置的API基础URL构建图片访问路径
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:62573/backend'

  // 如果是相对路径，构建完整的API URL
  if (filePath.startsWith('/')) {
    return `${apiBaseUrl}/api/custom-templates/image${filePath}`
  } else {
    return `${apiBaseUrl}/api/custom-templates/image/${filePath}`
  }
}

// 切换分类折叠状态
const toggleCategory = (category: string) => {
  collapsedCategories.value[category] = !collapsedCategories.value[category]
  addConsoleLog('info', `${collapsedCategories.value[category] ? '折叠' : '展开'}分类: ${category}`)
}

// 初始化分类折叠状态（默认全部折叠）
const initializeCategoryStates = () => {
  const categories = Object.keys(groupedTemplates.value)
  categories.forEach(category => {
    if (!(category in collapsedCategories.value)) {
      collapsedCategories.value[category] = true // 默认折叠
    }
  })
}

// 验证代码
const validateCode = () => {
  try {
    parseCode()
    addConsoleLog('success', '代码验证通过')
    ElMessage.success('代码验证通过')
  } catch (error) {
    addConsoleLog('error', `代码验证失败: ${error}`)
    ElMessage.error('代码验证失败')
  }
}

// 执行代码测试
const executeCode = () => {
  try {
    parseCode()
    addConsoleLog('info', '开始执行代码测试...')

    // 模拟执行过程
    setTimeout(() => {
      addConsoleLog('success', `成功解析 ${parsedSteps.value.length} 个步骤`)
      addConsoleLog('info', '代码测试完成')
      ElMessage.success('代码测试完成')
    }, 1000)
  } catch (error) {
    addConsoleLog('error', `执行失败: ${error}`)
    ElMessage.error('执行失败')
  }
}

// 生成序列
const generateSequence = () => {
  try {
    parseCode()
    if (parsedSteps.value.length === 0) {
      ElMessage.warning('请先编写代码生成步骤')
      return
    }

    // 生成序列数据
    const sequenceData = {
      name: `代码生成序列_${new Date().getTime()}`,
      description: '通过代码编辑器生成的自动化序列',
      category: 'code-generated',
      tags: [selectedLanguage.value, 'code-generated'],
      steps: parsedSteps.value,
      language: selectedLanguage.value,
      sourceCode: codeContent.value
    }

    emit('sequence-created', sequenceData)
    addConsoleLog('success', '序列生成成功')
    ElMessage.success('序列生成成功')
  } catch (error) {
    addConsoleLog('error', `生成序列失败: ${error}`)
    ElMessage.error('生成序列失败')
  }
}

// 格式化代码
const formatCode = () => {
  try {
    if (codeEditor.value?.formatCode) {
      // 使用 Monaco Editor 的格式化功能
      codeEditor.value.formatCode()
      addConsoleLog('info', '代码格式化完成')
    } else {
      // 降级到简单格式化
      let formatted = codeContent.value

      // 移除多余的空行
      formatted = formatted.replace(/\n\s*\n\s*\n/g, '\n\n')

      codeContent.value = formatted
      addConsoleLog('info', '代码格式化完成（简单模式）')
    }
  } catch (error) {
    addConsoleLog('error', `格式化失败: ${error}`)
  }
}

// 清空代码
const clearCode = () => {
  ElMessageBox.confirm('确定要清空所有代码吗？', '确认清空', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    codeContent.value = ''
    parsedSteps.value = []
    currentSequence.value = null // 清空当前序列信息
    lastSavedContent.value = ''
    hasUnsavedChanges.value = false
    addConsoleLog('info', '代码已清空，准备创建新序列')
  }).catch(() => {
    // 用户取消
  })
}

// 处理代码变化
const handleCodeChange = () => {
  // 防抖解析代码
  clearTimeout(parseTimeout.value)
  parseTimeout.value = setTimeout(() => {
    try {
      parseCode()
    } catch (error) {
      // 静默处理解析错误，避免频繁提示
    }
  }, 1000)
}

// 安全的JSON解析函数
const safeJsonParse = (jsonString: string, defaultValue: any = {}, context: string = '') => {
  try {
    return JSON.parse(jsonString)
  } catch (e) {
    console.warn(`JSON解析失败${context ? ` (${context})` : ''}:`, jsonString, e)
    addConsoleLog('warning', `JSON解析失败${context ? ` (${context})` : ''}: ${jsonString}`)
    return defaultValue
  }
}

// 解析代码为步骤
const parseCode = () => {
  const code = codeContent.value.trim()
  if (!code) {
    parsedSteps.value = []
    return
  }

  const steps: any[] = []
  let stepOrder = 1

  // 简单的代码解析器
  const lines = code.split('\n').filter(line => line.trim() && !line.trim().startsWith('//') && !line.trim().startsWith('#'))

  console.log('解析代码:', code)
  console.log('有效行数:', lines.length)

  for (const line of lines) {
    const trimmed = line.trim()

    // 解析基础操作
    if (trimmed.startsWith('click(')) {
      const match = trimmed.match(/click\(['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const clickParams = match[2] ? safeJsonParse(match[2], {}, '点击参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'click',
          description: `点击元素: ${match[1]}`,
          parameters: {
            selector: match[1],
            ...clickParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('input(')) {
      const match = trimmed.match(/input\(['"`]([^'"`]+)['"`],\s*['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const inputParams = match[3] ? safeJsonParse(match[3], {}, '输入参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'input',
          description: `输入文本到: ${match[1]}`,
          parameters: {
            selector: match[1],
            text: match[2],
            ...inputParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('wait(')) {
      const match = trimmed.match(/wait\((?:['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?|(\d+))\)/)
      if (match) {
        if (match[3]) {
          // 等待时间
          steps.push({
            stepOrder: stepOrder++,
            actionType: 'delay',
            description: `等待 ${match[3]} 毫秒`,
            parameters: {
              duration: parseInt(match[3])
            },
            timeoutSeconds: 5,
            maxRetries: 3,
            isActive: true
          })
        } else {
          // 等待元素
          const additionalParams = match[2] ? safeJsonParse(match[2], {}, '等待参数') : {}

          steps.push({
            stepOrder: stepOrder++,
            actionType: 'wait',
            description: `等待元素: ${match[1]}`,
            parameters: {
              selector: match[1],
              ...additionalParams
            },
            timeoutSeconds: 5,
            maxRetries: 3,
            isActive: true
          })
        }
      }
    }

    else if (trimmed.startsWith('screenshot(')) {
      const match = trimmed.match(/screenshot\(['"`]([^'"`]+)['"`]\)/)
      if (match) {
        steps.push({
          stepOrder: stepOrder++,
          actionType: 'screenshot',
          description: `截图保存为: ${match[1]}`,
          parameters: {
            filename: match[1]
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('verify(')) {
      const match = trimmed.match(/verify\(['"`]([^'"`]+)['"`],\s*({[^}]*})\)/)
      if (match) {
        const verifyParams = safeJsonParse(match[2], {}, '验证参数')

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'verify',
          description: `验证元素: ${match[1]}`,
          parameters: {
            selector: match[1],
            ...verifyParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    // 图像相关操作
    else if (trimmed.startsWith('clickImage(')) {
      const match = trimmed.match(/clickImage\(['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const clickParams = match[2] ? safeJsonParse(match[2], {}, '图像点击参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'clickImage',
          description: `点击图像: ${match[1]}`,
          parameters: {
            imageName: match[1],
            confidence: 0.8,
            ...clickParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('imageExists(')) {
      const match = trimmed.match(/imageExists\(['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const imageParams = match[2] ? safeJsonParse(match[2], {}, '图像检查参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'imageExists',
          description: `检查图像是否存在: ${match[1]}`,
          parameters: {
            imageName: match[1],
            confidence: 0.8,
            timeout: 5000,
            ...imageParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('waitForImage(')) {
      const match = trimmed.match(/waitForImage\(['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const waitParams = match[2] ? safeJsonParse(match[2], {}, '等待图像参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'waitForImage',
          description: `等待图像出现: ${match[1]}`,
          parameters: {
            imageName: match[1],
            confidence: 0.8,
            timeout: 10000,
            ...waitParams
          },
          timeoutSeconds: 10,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    // 键盘操作
    else if (trimmed.startsWith('keyPress(')) {
      const match = trimmed.match(/keyPress\(['"`]([^'"`]+)['"`]\)/)
      if (match) {
        steps.push({
          stepOrder: stepOrder++,
          actionType: 'keyPress',
          description: `按键: ${match[1]}`,
          parameters: {
            key: match[1]
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    else if (trimmed.startsWith('keyCombo(')) {
      const match = trimmed.match(/keyCombo\(['"`]([^'"`]+)['"`]\)/)
      if (match) {
        steps.push({
          stepOrder: stepOrder++,
          actionType: 'keyCombo',
          description: `组合键: ${match[1]}`,
          parameters: {
            combo: match[1]
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    // 拖拽操作
    else if (trimmed.startsWith('drag(')) {
      const match = trimmed.match(/drag\(['"`]([^'"`]+)['"`],\s*['"`]([^'"`]+)['"`](?:,\s*({[^}]*}))?\)/)
      if (match) {
        const dragParams = match[3] ? safeJsonParse(match[3], {}, '拖拽参数') : {}

        steps.push({
          stepOrder: stepOrder++,
          actionType: 'drag',
          description: `拖拽从 ${match[1]} 到 ${match[2]}`,
          parameters: {
            source: match[1],
            target: match[2],
            duration: 1000,
            ...dragParams
          },
          timeoutSeconds: 5,
          maxRetries: 3,
          isActive: true
        })
      }
    }

    // 条件判断和循环（简单解析）
    else if (trimmed.includes('if') && trimmed.includes('imageExists')) {
      steps.push({
        stepOrder: stepOrder++,
        actionType: 'condition',
        description: '条件判断（图像检查）',
        parameters: {
          condition: trimmed,
          type: 'imageExists'
        },
        timeoutSeconds: 5,
        maxRetries: 3,
        isActive: true
      })
    }

    else if (trimmed.includes('for') || trimmed.includes('while')) {
      steps.push({
        stepOrder: stepOrder++,
        actionType: 'loop',
        description: '循环操作',
        parameters: {
          loopCode: trimmed
        },
        timeoutSeconds: 5,
        maxRetries: 3,
        isActive: true
      })
    }
  }

  console.log('解析结果:', steps)
  console.log('解析到的步骤数:', steps.length)
  parsedSteps.value = steps
}

// 刷新预览
const refreshPreview = () => {
  parseCode()
  addConsoleLog('info', '预览已刷新')
}

// 获取步骤类型标签
const getStepTypeTag = (actionType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const tagMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    click: 'primary',
    input: 'success',
    wait: 'warning',
    delay: 'info',
    screenshot: 'danger',
    verify: 'warning'
  }
  return tagMap[actionType] || 'primary'
}

// 添加控制台日志
const addConsoleLog = (type: string, message: string) => {
  const time = new Date().toLocaleTimeString()
  consoleLogs.value.push({ type, message, time })

  // 限制日志数量
  if (consoleLogs.value.length > 100) {
    consoleLogs.value = consoleLogs.value.slice(-50)
  }
}

// 清空控制台
const clearConsole = () => {
  consoleLogs.value = []
}

// 防抖定时器
const parseTimeout = ref<NodeJS.Timeout>()

// 生命周期
onMounted(() => {
  initializeCode()
  parseCode()

  // 自动加载模板
  loadTemplates()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})

// 监听代码内容变化
watch(codeContent, (newContent) => {
  hasUnsavedChanges.value = newContent !== lastSavedContent.value
}, { immediate: true })
</script>

<style scoped>
.code-sequence-creator {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
}

.code-sequence-creator.maximized {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: var(--el-bg-color);
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.code-sequence-creator.maximized .editor-container {
  min-height: calc(100vh - 200px);
}

.code-sequence-creator.maximized .editor-content {
  min-height: calc(100vh - 300px);
}

.code-sequence-creator.maximized .editor-content :deep(.monaco-editor-container) {
  height: calc(100vh - 300px) !important;
}

.code-sequence-creator.maximized .editor-panel {
  height: calc(100vh - 200px);
}

.code-sequence-creator.maximized .panel-header {
  background: var(--el-color-primary-light-9);
  border-bottom: 2px solid var(--el-color-primary);
}

.code-sequence-creator.maximized .panel-header span {
  font-weight: 600;
  color: var(--el-color-primary);
}

.language-selector {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.toolbar-center {
  display: flex;
  gap: 12px;
  flex: 2;
  justify-content: center;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}

/* 按钮组样式优化 */
.el-button-group {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.el-button-group .el-button {
  border-radius: 0;
}

.el-button-group .el-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.el-button-group .el-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 工具栏标签样式 */
.toolbar-left .el-tag {
  font-size: 12px;
  height: 24px;
  line-height: 22px;
}

.editor-container {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 5fr 3fr;
  gap: 16px;
  min-height: 600px;
  height: 100%;
}

.template-panel,
.editor-panel,
.preview-panel {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
  font-weight: 500;
}

.sequence-indicator {
  color: #409eff;
  font-weight: 500;
  margin-left: 8px;
  padding: 2px 8px;
  background: #ecf5ff;
  border-radius: 12px;
  font-size: 12px;
}

.unsaved-indicator {
  color: var(--el-color-warning);
  font-size: 12px;
  margin-left: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.editor-content {
  flex: 1;
  position: relative;
  padding: 8px;
  min-height: 400px;
  height: 100%;
}

.editor-content :deep(.monaco-editor-container) {
  height: 100% !important;
  min-height: 400px !important;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--el-border-color);
}

.editor-content :deep(.monaco-editor) {
  border-radius: 6px;
}

.editor-content :deep(.monaco-editor .monaco-editor-background) {
  border-radius: 6px;
}

.preview-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.steps-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-preview-item {
  padding: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background: var(--el-bg-color);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.step-description {
  color: var(--el-text-color-primary);
  font-size: 14px;
  margin-bottom: 8px;
}

.step-parameters {
  background: var(--el-bg-color-page);
  border-radius: 4px;
  padding: 8px;
}

.step-parameters pre {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-placeholder);
}

.console-panel {
  height: 180px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.console-content {
  flex: 1;
  padding: 8px;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-y: auto;
}

.console-log {
  display: flex;
  gap: 8px;
  padding: 2px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-time {
  color: #808080;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-success .log-message {
  color: #4ade80;
}

.log-error .log-message {
  color: #f87171;
}

.log-warning .log-message {
  color: #fbbf24;
}

.log-info .log-message {
  color: #60a5fa;
}

.api-reference {
  max-height: 600px;
  overflow-y: auto;
}

.api-section {
  padding: 16px;
}

.api-section h3 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.api-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background: var(--el-bg-color-page);
}

.api-item h4 {
  margin: 0 0 8px 0;
  color: var(--el-color-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.api-item p {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
}

.api-item pre {
  margin: 0;
  padding: 12px;
  background: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.api-item code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
  }
}

/* 滚动条样式 */
.console-content::-webkit-scrollbar,
.preview-content::-webkit-scrollbar,
.code-textarea::-webkit-scrollbar {
  width: 6px;
}

.console-content::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track,
.code-textarea::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

.console-content::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb,
.code-textarea::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.console-content::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover,
.code-textarea::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-placeholder);
}

/* 模板面板样式 */
.template-panel {
  .template-content {
    flex: 1;
    padding: 12px;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex子元素可以收缩 */
  }

  .template-search {
    margin-bottom: 12px;
  }

  .template-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-right: 4px; /* 为滚动条留出空间 */

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-darker);
      border-radius: 3px;

      &:hover {
        background: var(--el-text-color-placeholder);
      }
    }
  }

  .template-category-group {
    .category-header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-7);
      border-radius: 6px;
      margin-bottom: 8px;
      font-size: 13px;
      font-weight: 500;
      color: var(--el-color-primary);
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;

      &:hover {
        background: var(--el-color-primary-light-8);
        border-color: var(--el-color-primary-light-6);
      }

      &.collapsed {
        margin-bottom: 4px;

        .collapse-icon {
          transform: rotate(0deg);
        }
      }

      .collapse-icon {
        margin-right: 4px;
        font-size: 12px;
        transition: transform 0.2s ease;
        transform: rotate(90deg);
      }

      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }

      .category-name {
        flex: 1;
      }

      .category-count {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        font-weight: normal;
      }
    }

    .category-templates {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding-left: 8px;
      transition: all 0.3s ease;
      overflow: hidden;
    }
  }

  .template-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .template-image {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--el-border-color-lighter);
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .template-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: var(--el-text-color-placeholder);
      font-size: 14px;
    }
  }

  .template-info {
    flex: 1;
    min-width: 0;

    .template-name {
      font-size: 12px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .template-usage {
      font-size: 10px;
      color: var(--el-text-color-secondary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .empty-templates {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--el-text-color-placeholder);
    text-align: center;

    .el-icon {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      margin: 0 0 16px 0;
      font-size: 14px;
    }
  }
}

</style>
