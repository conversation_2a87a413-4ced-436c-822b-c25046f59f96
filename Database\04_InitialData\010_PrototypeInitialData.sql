-- 原型图初始数据和模板
-- 为原型图功能提供示例数据和常用模板

USE [ProjectManagementAI]
GO

-- 检查是否已有原型图数据，避免重复插入
IF NOT EXISTS (SELECT 1 FROM [dbo].[Prototypes])
BEGIN
    PRINT '开始插入原型图初始数据...'
    
    -- 假设已有项目ID为1的项目，插入一些示例原型图
    -- 注意：实际使用时需要确保项目和用户ID存在
    
    -- 1. 用户登录页面线框图
    INSERT INTO [dbo].[Prototypes] (
        [ProjectId], [RequirementDocumentId], [PrototypeName], [PrototypeType], 
        [MermaidDefinition], [Description], [TargetUsers], [DeviceType], [FidelityLevel], 
        [CreatedBy], [CreatedTime]
    ) VALUES (
        1, NULL, '用户登录页面线框图', 'Wireframe',
        'flowchart TD
    A[页面头部] --> B[网站Logo]
    A --> C[导航菜单]
    B --> D[登录表单区域]
    C --> D
    D --> E[用户名输入框]
    D --> F[密码输入框]
    D --> G[记住我复选框]
    D --> H[登录按钮]
    D --> I[忘记密码链接]
    D --> J[注册链接]
    H --> K[页面底部]
    I --> K
    J --> K
    K --> L[版权信息]
    K --> M[联系我们]',
        '用户登录页面的基本布局设计，包含必要的登录元素和导航',
        '所有用户',
        'Desktop',
        'Low',
        1,
        GETDATE()
    );

    -- 2. 用户注册流程图
    INSERT INTO [dbo].[Prototypes] (
        [ProjectId], [RequirementDocumentId], [PrototypeName], [PrototypeType], 
        [MermaidDefinition], [Description], [TargetUsers], [DeviceType], [FidelityLevel], 
        [CreatedBy], [CreatedTime]
    ) VALUES (
        1, NULL, '用户注册流程图', 'UserFlow',
        'flowchart TD
    Start([用户开始注册]) --> A[访问注册页面]
    A --> B[填写基本信息]
    B --> C[输入用户名]
    C --> D[输入邮箱]
    D --> E[设置密码]
    E --> F[确认密码]
    F --> G[阅读用户协议]
    G --> H{同意协议?}
    H -->|是| I[点击注册按钮]
    H -->|否| G
    I --> J{验证信息}
    J -->|通过| K[发送验证邮件]
    J -->|失败| L[显示错误信息]
    L --> B
    K --> M[用户查收邮件]
    M --> N[点击验证链接]
    N --> O[激活账户]
    O --> End([注册完成])',
        '用户注册的完整流程，包含验证和错误处理',
        '新用户',
        'Desktop',
        'Medium',
        1,
        GETDATE()
    );

    -- 3. 移动端主页组件关系图
    INSERT INTO [dbo].[Prototypes] (
        [ProjectId], [RequirementDocumentId], [PrototypeName], [PrototypeType], 
        [MermaidDefinition], [Description], [TargetUsers], [DeviceType], [FidelityLevel], 
        [CreatedBy], [CreatedTime]
    ) VALUES (
        1, NULL, '移动端主页组件关系图', 'ComponentDiagram',
        'graph TD
    App[App根组件] --> Header[头部组件]
    App --> Main[主体组件]
    App --> Footer[底部导航组件]
    
    Header --> Logo[Logo组件]
    Header --> Search[搜索组件]
    Header --> User[用户头像组件]
    
    Main --> Banner[轮播图组件]
    Main --> Category[分类组件]
    Main --> ProductList[商品列表组件]
    
    ProductList --> ProductCard[商品卡片组件]
    ProductCard --> ProductImage[商品图片组件]
    ProductCard --> ProductInfo[商品信息组件]
    ProductCard --> ProductPrice[价格组件]
    ProductCard --> AddToCart[加购按钮组件]
    
    Footer --> HomeTab[首页标签]
    Footer --> CategoryTab[分类标签]
    Footer --> CartTab[购物车标签]
    Footer --> ProfileTab[个人中心标签]',
        '移动端主页的组件层次结构和依赖关系',
        '移动端用户',
        'Mobile',
        'High',
        1,
        GETDATE()
    );

    -- 4. 购物车交互流程图
    INSERT INTO [dbo].[Prototypes] (
        [ProjectId], [RequirementDocumentId], [PrototypeName], [PrototypeType], 
        [MermaidDefinition], [Description], [TargetUsers], [DeviceType], [FidelityLevel], 
        [CreatedBy], [CreatedTime]
    ) VALUES (
        1, NULL, '购物车交互流程图', 'InteractionFlow',
        'flowchart TD
    User[用户操作] --> ViewCart[查看购物车]
    ViewCart --> System1[系统加载购物车数据]
    System1 --> Display1[显示商品列表]
    Display1 --> UserAction{用户选择操作}
    
    UserAction -->|修改数量| ChangeQty[修改商品数量]
    UserAction -->|删除商品| DeleteItem[删除商品]
    UserAction -->|继续购物| ContinueShopping[返回商品页面]
    UserAction -->|结算| Checkout[进入结算页面]
    
    ChangeQty --> System2[系统更新数量]
    System2 --> Recalculate[重新计算总价]
    Recalculate --> Display2[更新显示]
    Display2 --> UserAction
    
    DeleteItem --> System3[系统删除商品]
    System3 --> Recalculate
    
    ContinueShopping --> ProductPage[商品页面]
    
    Checkout --> ValidateCart{验证购物车}
    ValidateCart -->|有效| PaymentPage[支付页面]
    ValidateCart -->|无效| ErrorMsg[显示错误信息]
    ErrorMsg --> Display1',
        '购物车页面的用户交互流程和系统响应',
        '购物用户',
        'Responsive',
        'High',
        1,
        GETDATE()
    );

    PRINT '原型图初始数据插入完成'
END
ELSE
BEGIN
    PRINT '原型图表中已有数据，跳过初始数据插入'
END
GO

-- 插入原型图相关的提示词模板（如果PromptTemplates表存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PromptTemplates]') AND type in (N'U'))
BEGIN
    -- 检查是否已有原型图相关的提示词模板
    IF NOT EXISTS (SELECT 1 FROM [dbo].[PromptTemplates] WHERE [TaskType] = 'PrototypeGeneration')
    BEGIN
        PRINT '开始插入原型图生成提示词模板...'

        -- 获取设计生成分类ID（如果不存在则创建）
        DECLARE @DesignCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '设计生成');
        IF @DesignCategoryId IS NULL
        BEGIN
            INSERT INTO PromptCategories (Name, Description, SortOrder, IsEnabled, CreatedTime)
            VALUES ('设计生成', '原型图、ER图、上下文图等设计相关的AI生成模板', 10, 1, GETDATE());
            SET @DesignCategoryId = SCOPE_IDENTITY();
        END

        -- 线框图生成模板
        INSERT INTO [dbo].[PromptTemplates] (
            [Name], [CategoryId], [Description], [Content], [TemplateType], [TaskType], [IsEnabled], [CreatedBy], [CreatedTime]
        ) VALUES (
            'WireframeGenerationPrompt', @DesignCategoryId, '线框图生成提示词模板',
            N'请生成一个Mermaid格式的线框图，要求如下：

设备类型：{DeviceType}
保真度级别：{FidelityLevel}
目标用户：{TargetUsers}

需求描述：
{Requirements}

生成要求：
1. 使用flowchart TD语法
2. 节点命名要清晰明确，使用中文标签
3. 体现页面的基本布局结构（头部、导航、内容区、侧边栏、底部等）
4. 包含主要的UI组件（按钮、表单、列表等）
5. 展示页面间的导航关系
6. 只返回纯Mermaid代码，不要包含markdown代码块标记
7. 确保语法正确，可以被Mermaid正确渲染

示例格式：
flowchart TD
    A[页面头部] --> B[导航菜单]
    B --> C[主内容区]
    C --> D[侧边栏]
    D --> E[页面底部]',
            'System', 'PrototypeGeneration', 1, 1, GETDATE()
        );

        -- 用户流程图生成模板
        INSERT INTO [dbo].[PromptTemplates] (
            [Name], [CategoryId], [Description], [Content], [TemplateType], [TaskType], [IsEnabled], [CreatedBy], [CreatedTime]
        ) VALUES (
            'UserFlowGenerationPrompt', @DesignCategoryId, '用户流程图生成提示词模板',
            N'请生成一个Mermaid格式的用户流程图，要求如下：

设备类型：{DeviceType}
保真度级别：{FidelityLevel}
目标用户：{TargetUsers}

需求描述：
{Requirements}

生成要求：
1. 使用flowchart TD语法
2. 使用圆形节点表示开始和结束：([开始])
3. 使用矩形节点表示用户操作步骤：[操作步骤]
4. 使用菱形节点表示决策点：{决策点}
5. 清楚展示用户的操作路径
6. 包含异常流程和错误处理
7. 只返回纯Mermaid代码，不要包含markdown代码块标记

示例格式：
flowchart TD
    Start([用户开始]) --> Login[登录页面]
    Login --> Check{验证登录}
    Check -->|成功| Dashboard[用户仪表板]
    Check -->|失败| Error[错误提示]',
            'System', 'PrototypeGeneration', 1, 1, GETDATE()
        );

        PRINT '原型图生成提示词模板插入完成'
    END
    ELSE
    BEGIN
        PRINT '原型图生成提示词模板已存在，跳过插入'
    END
END
ELSE
BEGIN
    PRINT 'PromptTemplates表不存在，跳过提示词模板插入'
END
GO

PRINT '原型图初始数据脚本执行完成'
