# 区域图片模板功能说明

## 🎯 功能概述

在模板创建页面 `http://localhost:3000/automation/templates` 中，现在支持为每个模板添加一个**可选的区域图片**。当页面有多个相似图片时，区域图片可以限定搜索范围，大幅提高模板匹配的精度。

## 🔧 实现的功能

### 1. 数据库层面
- ✅ 添加了5个新字段到 `CustomUIAutoMationTemplates` 表：
  - `RegionFilePath`: 区域图片文件路径（可选）
  - `RegionDescription`: 区域图片描述
  - `RegionConfidence`: 区域图片匹配置信度（默认0.7）
  - `RegionExpand`: 区域边界扩展像素（默认10）
  - `UseRegionMatching`: 是否启用区域匹配（默认false）

### 2. 后端API层面
- ✅ 更新了实体类 `CustomUIAutoMationTemplate`
- ✅ 更新了DTO类 `CustomTemplateDto`、`CreateCustomTemplateDto`、`UpdateCustomTemplateDto`
- ✅ 更新了控制器 `CustomTemplateController` 的创建、更新、映射方法

### 3. 前端界面层面
- ✅ 更新了TypeScript类型定义 `CustomTemplate`
- ✅ 更新了模板表单组件 `TemplateFormDialog.vue`
- ✅ 添加了区域图片上传功能
- ✅ 添加了区域匹配配置选项

## 🖥️ 前端使用方法

### 1. 创建带区域图片的模板

1. **访问模板管理页面**: `http://localhost:3000/automation/templates`
2. **点击"创建模板"按钮**
3. **填写基本信息**:
   - 模板名称
   - 描述
   - 分类
   - 置信度
4. **上传主模板图片**: 这是要匹配的目标图片
5. **启用区域匹配**（可选）:
   - 打开"启用区域匹配"开关
   - 上传区域图片（用于限定搜索范围的大图）
   - 填写区域描述
   - 调整区域置信度（0.1-1.0）
   - 设置区域扩展像素（0-200）
6. **添加标签和备注**
7. **保存模板**

### 2. 界面说明

```
┌─────────────────────────────────────────┐
│ 模板名称: [确定按钮]                     │
├─────────────────────────────────────────┤
│ 描述: [对话框中的确定按钮]               │
├─────────────────────────────────────────┤
│ 分类: [按钮]                            │
├─────────────────────────────────────────┤
│ 置信度: [████████░░] 80%                │
├─────────────────────────────────────────┤
│ 模板图片: [上传主图片]                   │
├─────────────────────────────────────────┤
│ 区域图片: ○ 禁用区域匹配 ● 启用区域匹配  │
│ ┌─────────────────────────────────────┐ │
│ │ [上传区域图片]                       │ │
│ │ 区域描述: [对话框区域]               │ │
│ │ 区域置信度: [██████░░░░] 70%         │ │
│ │ 区域扩展: [10] 像素                  │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ 标签: [对话框, 按钮, 确定]               │
└─────────────────────────────────────────┘
```

## 🎯 应用场景

### 场景1: 相似按钮区分
**问题**: 页面有多个"确定"按钮，难以准确定位
**解决**: 
- 主图片: 确定按钮的截图
- 区域图片: 包含目标确定按钮的对话框截图
- 效果: 只在对话框区域内查找确定按钮

### 场景2: 表格中的特定单元格
**问题**: 表格中有多个相似的图标或文本
**解决**:
- 主图片: 目标图标或文本
- 区域图片: 包含目标行或列的表格区域
- 效果: 只在指定表格区域内查找目标元素

### 场景3: 工具栏中的特定工具
**问题**: 工具栏有多个相似的图标
**解决**:
- 主图片: 目标工具图标
- 区域图片: 特定工具栏区域
- 效果: 只在指定工具栏内查找目标图标

## ⚙️ 配置参数说明

### 区域置信度 (Region Confidence)
- **范围**: 0.1 - 1.0
- **默认**: 0.7
- **说明**: 区域图片匹配的精确度要求
- **建议**: 
  - 0.6-0.7: 适用于可能有轻微变化的界面
  - 0.8-0.9: 适用于相对固定的界面

### 区域扩展 (Region Expand)
- **范围**: 0 - 200 像素
- **默认**: 10 像素
- **说明**: 在找到区域图片后，向四周扩展的像素数
- **建议**:
  - 5-10像素: 适用于精确定位
  - 15-30像素: 适用于可能有位置偏移的界面

## 🔄 工作原理

1. **区域定位**: 首先在屏幕上查找区域图片的位置
2. **区域提取**: 根据区域图片位置和扩展设置，提取目标区域
3. **模板匹配**: 在提取的区域内查找主模板图片
4. **精确定位**: 返回主模板图片在整个屏幕上的绝对坐标

## 📋 数据库迁移

在使用此功能前，需要运行数据库迁移脚本：

```sql
-- 运行迁移脚本
USE ProjectManagementAI;
GO
EXEC('Database/03_Migrations/007_AddRegionImageToTemplates.sql');
```

## 🚀 技术优势

1. **提高精度**: 通过限定搜索区域，减少误匹配
2. **向下兼容**: 现有模板无需修改，新功能完全可选
3. **灵活配置**: 支持多种参数调整以适应不同场景
4. **用户友好**: 直观的界面设计，易于理解和使用

## 📝 注意事项

1. **图片质量**: 确保区域图片和主图片都清晰可见
2. **尺寸匹配**: 图片应与实际显示尺寸一致
3. **区域选择**: 区域图片应包含足够的上下文信息
4. **性能考虑**: 大区域图片可能影响匹配速度
5. **存储空间**: 每个模板可能需要存储两张图片

## 🔧 故障排除

### 问题1: 区域图片匹配失败
**解决**: 降低区域置信度或增加区域扩展像素

### 问题2: 主图片在区域内找不到
**解决**: 检查区域图片是否包含主图片，或调整主图片置信度

### 问题3: 匹配速度较慢
**解决**: 减小区域图片尺寸或优化区域扩展设置

这个功能让模板匹配更加精准可靠，特别适合复杂界面的自动化操作！
