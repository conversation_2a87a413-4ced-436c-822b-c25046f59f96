namespace ProjectManagement.Core.DTOs;

/// <summary>
/// 需求文档DTO
/// </summary>
public class RequirementDocumentDto
{
    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 需求文档状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 创建人姓名
    /// </summary>
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// 需求文档详情DTO
/// </summary>
public class RequirementDocumentDetailDto
{
    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 需求文档内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 需求文档状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    public int? CreatedBy { get; set; }

    /// <summary>
    /// 创建人姓名
    /// </summary>
    public string CreatedByName { get; set; } = string.Empty;

    /// <summary>
    /// 更新人ID
    /// </summary>
    public int? UpdatedBy { get; set; }

    /// <summary>
    /// 更新人姓名
    /// </summary>
    public string UpdatedByName { get; set; } = string.Empty;

    /// <summary>
    /// 功能性需求详细描述
    /// </summary>
    public string? FunctionalRequirements { get; set; }

    /// <summary>
    /// 非功能性需求详细描述
    /// </summary>
    public string? NonFunctionalRequirements { get; set; }

    /// <summary>
    /// 用户故事集合
    /// </summary>
    public string? UserStories { get; set; }

    /// <summary>
    /// 验收标准详细说明
    /// </summary>
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// 生成方式: AI(AI生成), Manual(手动创建), Hybrid(混合方式)
    /// </summary>
    public string GeneratedBy { get; set; } = "Manual";
}

/// <summary>
/// 创建需求文档请求DTO
/// </summary>
public class CreateRequirementRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 需求文档内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 需求类型
    /// </summary>
    public string? RequirementType { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string? Priority { get; set; }
}

/// <summary>
/// 更新需求文档请求DTO
/// </summary>
public class UpdateRequirementRequestDto
{
    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 需求文档内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 需求文档状态
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// 功能性需求详细描述
    /// </summary>
    public string? FunctionalRequirements { get; set; }

    /// <summary>
    /// 非功能性需求详细描述
    /// </summary>
    public string? NonFunctionalRequirements { get; set; }

    /// <summary>
    /// 用户故事集合
    /// </summary>
    public string? UserStories { get; set; }

    /// <summary>
    /// 验收标准详细说明
    /// </summary>
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// 需求类型
    /// </summary>
    public string? RequirementType { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string? Priority { get; set; }
}


/// <summary>
/// 从AI对话保存需求文档请求DTO
/// </summary>
public class SaveRequirementFromChatRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// AI回复内容（作为需求文档内容）
    /// </summary>
    public string AiContent { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息（作为需求背景）
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// 对话ID（用于关联）
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 功能性需求（可选，从AI内容中提取）
    /// </summary>
    public string? FunctionalRequirements { get; set; }

    /// <summary>
    /// 非功能性需求（可选，从AI内容中提取）
    /// </summary>
    public string? NonFunctionalRequirements { get; set; }

    /// <summary>
    /// 用户故事（可选，从AI内容中提取）
    /// </summary>
    public string? UserStories { get; set; }

    /// <summary>
    /// 验收标准（可选，从AI内容中提取）
    /// </summary>
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// AI提供商配置ID（可选，用于指定使用的AI模型）
    /// </summary>
    public int? AIProviderConfigId { get; set; }
}

/// <summary>
/// 开始需求对话请求DTO
/// </summary>
public class StartRequirementConversationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 初始需求描述
    /// </summary>
    public string InitialRequirement { get; set; } = string.Empty;

    /// <summary>
    /// 对话类型
    /// </summary>
    public string ConversationType { get; set; } = "RequirementGathering";
}

/// <summary>
/// 需求对话响应DTO
/// </summary>
public class RequirementConversationResponseDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 对话状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// AI初始回复
    /// </summary>
    public string? InitialResponse { get; set; }
}

/// <summary>
/// 发送需求对话消息请求DTO
/// </summary>
public class SendRequirementMessageRequestDto
{
    /// <summary>
    /// 用户消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID（可选，用于获取项目上下文）
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// AI提供商配置ID（可选，用于指定使用的AI模型）
    /// </summary>
    public int? AIProviderConfigId { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = "Text";

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// 系统参数DTO
/// </summary>
public class SystemParameterDto
{
    /// <summary>
    /// 参数ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 参数分类
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 参数键名
    /// </summary>
    public string ParameterKey { get; set; } = string.Empty;

    /// <summary>
    /// 参数值
    /// </summary>
    public string ParameterValue { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 是否系统参数
    /// </summary>
    public bool IsSystem { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }
}

/// <summary>
/// 创建系统参数请求DTO
/// </summary>
public class CreateSystemParameterDto
{
    /// <summary>
    /// 参数分类
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 参数键名
    /// </summary>
    public string ParameterKey { get; set; } = string.Empty;

    /// <summary>
    /// 参数值
    /// </summary>
    public string ParameterValue { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }
}

/// <summary>
/// 更新系统参数请求DTO
/// </summary>
public class UpdateSystemParameterDto
{
    /// <summary>
    /// 参数键名
    /// </summary>
    public string? ParameterKey { get; set; }

    /// <summary>
    /// 参数值
    /// </summary>
    public string? ParameterValue { get; set; }

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int? SortOrder { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }
}

/// <summary>
/// 系统参数选项DTO
/// </summary>
public class SystemParameterOptionDto
{
    /// <summary>
    /// 参数值
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string Label { get; set; } = string.Empty;

    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    public string? ExtendedProperties { get; set; }
}

/// <summary>
/// 需求消息响应DTO
/// </summary>
public class RequirementMessageResponseDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// AI回复
    /// </summary>
    public string AIResponse { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 建议的后续问题
    /// </summary>
    public List<string>? SuggestedQuestions { get; set; }

    /// <summary>
    /// 提取的需求要点
    /// </summary>
    public List<string>? ExtractedRequirements { get; set; }
}

/// <summary>
/// 需求对话历史DTO
/// </summary>
public class RequirementConversationHistoryDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 对话状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 消息列表
    /// </summary>
    public List<RequirementMessageDto> Messages { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime? LastUpdatedAt { get; set; }
}

/// <summary>
/// 需求消息DTO
/// </summary>
public class RequirementMessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 发送者类型 (User/AI)
    /// </summary>
    public string SenderType { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = "Text";
}

/// <summary>
/// 对话消息DTO（用于获取对话历史）
/// </summary>
public class ConversationMessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// AI回复
    /// </summary>
    public string AIResponse { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 对话历史响应DTO
/// </summary>
public class ConversationHistoryResponse
{
    /// <summary>
    /// 对话消息列表
    /// </summary>
    public List<ConversationMessageDto> Messages { get; set; } = new();

    /// <summary>
    /// 消息总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 向量上下文（可选）
    /// </summary>
    public List<VectorContextItem>? VectorContext { get; set; }
}

/// <summary>
/// 向量上下文项DTO
/// </summary>
public class VectorContextItem
{
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 相似度分数
    /// </summary>
    public float SimilarityScore { get; set; }

    /// <summary>
    /// 文档ID
    /// </summary>
    public string DocumentId { get; set; } = string.Empty;

    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}
