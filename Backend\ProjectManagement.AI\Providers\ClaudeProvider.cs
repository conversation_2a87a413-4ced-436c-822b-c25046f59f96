using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// Anthropic Claude AI服务提供商
/// </summary>
public class ClaudeProvider : IAIProvider
{
    private readonly ILogger<ClaudeProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly ClaudeConfig _config;
    private readonly AIUsageStats _usageStats;

    public string Name => "Claude";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307" };

    public ClaudeProvider(
        ILogger<ClaudeProvider> logger,
        HttpClient httpClient,
        IOptions<ClaudeConfig> config)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config.Value;
        _usageStats = new AIUsageStats();

        // 配置HttpClient
        if (!string.IsNullOrEmpty(_config.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("x-api-key", _config.ApiKey);
            _httpClient.DefaultRequestHeaders.Add("anthropic-version", "2023-06-01");
        }

        _httpClient.BaseAddress = new Uri(_config.Endpoint);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Claude开始生成文本，模型: {Model}", config.Model);

            // Claude API格式
            var requestBody = new
            {
                model = GetClaudeModel(config.Model),
                max_tokens = config.MaxTokens,
                temperature = config.Temperature,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                }
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("messages", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Claude API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"Claude API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var generatedText = responseData
                .GetProperty("content")[0]
                .GetProperty("text")
                .GetString() ?? string.Empty;

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("Claude文本生成完成，长度: {Length}", generatedText.Length);
            return generatedText;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Claude文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("Claude开始获取向量嵌入");

            // Claude目前不支持嵌入，返回模拟数据
            await Task.Delay(500);

            // 生成模拟的1536维向量
            var embedding = new float[1536];
            var random = new Random();
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2 - 1);
            }

            UpdateUsageStats(startTime, true);

            _logger.LogInformation("Claude向量嵌入获取完成，维度: {Dimension}", embedding.Length);
            return embedding;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "Claude向量嵌入获取失败");
            throw;
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            var testConfig = new AIModelConfig
            {
                Model = "claude-3-haiku-20240307",
                MaxTokens = 10,
                Temperature = 0.1f
            };

            var result = await GenerateAsync("Hello", testConfig);
            IsAvailable = !string.IsNullOrEmpty(result);

            _logger.LogInformation("Claude健康检查完成，状态: {Status}", IsAvailable ? "正常" : "异常");
            return IsAvailable;
        }
        catch (Exception ex)
        {
            IsAvailable = false;
            _logger.LogError(ex, "Claude健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    #region 私有方法

    /// <summary>
    /// 获取Claude模型名称
    /// </summary>
    private string GetClaudeModel(string model)
    {
        return model.ToLower() switch
        {
            "claude-3-opus" => "claude-3-opus-20240229",
            "claude-3-sonnet" => "claude-3-sonnet-20240229",
            "claude-3-haiku" => "claude-3-haiku-20240307",
            _ => "claude-3-haiku-20240307"
        };
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success, JsonElement? responseData = null)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        // 更新平均响应时间
        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        // 更新Token使用量
        if (success && responseData.HasValue)
        {
            try
            {
                if (responseData.Value.TryGetProperty("usage", out var usage))
                {
                    if (usage.TryGetProperty("input_tokens", out var inputTokens) &&
                        usage.TryGetProperty("output_tokens", out var outputTokens))
                    {
                        _usageStats.TotalTokens += inputTokens.GetInt64() + outputTokens.GetInt64();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析Token使用量失败");
            }
        }

        _usageStats.LastUpdated = DateTime.Now;
    }

    #endregion
}

/// <summary>
/// Claude配置
/// </summary>
public class ClaudeConfig
{
    /// <summary>
    /// API端点
    /// </summary>
    public string Endpoint { get; set; } = "https://api.anthropic.com/v1/";

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300; // 5分钟超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}
