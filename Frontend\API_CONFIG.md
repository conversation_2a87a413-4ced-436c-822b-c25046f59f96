# API配置说明

## 问题描述
前端发送HTTP请求，但后端API运行在HTTPS上，导致协议不匹配错误。

## 解决方案

### 1. 环境变量配置
已创建以下环境变量文件：

- `.env.development` - 开发环境配置（使用HTTPS）
- `.env.production` - 生产环境配置
- `.env.local` - 本地覆盖配置（可选）

### 2. 后端API地址
根据部署环境：
- **IIS部署**: `http://localhost:62573/backend` （生产环境）
- **开发环境HTTPS**: `https://localhost:61136` （开发时）
- **开发环境HTTP**: `http://localhost:61137` （备用）

### 3. 前端配置更新
- 更新了 `vite.config.ts` 中的代理配置
- 更新了 `api.ts` 中的默认API地址
- 添加了更好的错误处理

### 4. CORS配置
后端 `appsettings.json` 已更新，支持：
- `http://localhost:3000`
- `https://localhost:3000`
- 其他常用端口的HTTP和HTTPS版本

## 使用方法

### 方法1：使用IIS部署的后端（推荐）
1. 确保后端已部署到IIS，运行在 `http://localhost:62573/backend`
2. 前端会自动使用 `.env.local` 中的IIS配置
3. 登录API地址：`http://localhost:62573/backend/api/Auth/login`

### 方法2：使用开发环境后端
1. 修改 `.env.local` 文件：
   ```
   VITE_API_BASE_URL=https://localhost:61136
   ```
2. 确保后端开发服务器正在运行

### 方法3：修改后端只使用HTTP
1. 修改 `Backend/ProjectManagement.API/Properties/launchSettings.json`：
   ```json
   "applicationUrl": "http://localhost:61137"
   ```
2. 重启后端服务

## 验证配置
1. 启动后端API服务
2. 启动前端开发服务器：`npm run dev`
3. 打开浏览器开发者工具，检查网络请求
4. 确认API请求使用正确的协议和端口

## 常见问题

### SSL证书错误
如果遇到SSL证书错误，可以：
1. 在浏览器中直接访问 `https://localhost:61136`，接受证书警告
2. 或者使用HTTP版本的API地址

### CORS错误
如果遇到CORS错误，检查：
1. 后端CORS配置是否包含前端地址
2. 前端请求的协议是否与后端匹配

### 网络连接错误
如果遇到连接错误：
1. 确认后端服务正在运行
2. 确认端口号是否正确
3. 检查防火墙设置
