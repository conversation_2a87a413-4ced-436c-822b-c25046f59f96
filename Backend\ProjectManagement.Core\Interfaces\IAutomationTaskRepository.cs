using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 自动化任务仓储接口
/// </summary>
public interface IAutomationTaskRepository : IRepository<AutomationTask>
{
    /// <summary>
    /// 获取待处理的任务队列
    /// </summary>
    /// <param name="clientId">客户端ID，为空则获取所有未分配的任务</param>
    /// <param name="taskTypes">任务类型过滤</param>
    /// <param name="maxCount">最大返回数量</param>
    /// <returns>待处理任务列表</returns>
    Task<List<AutomationTask>> GetPendingTasksAsync(string? clientId = null, string[]? taskTypes = null, int maxCount = 10);

    /// <summary>
    /// 分配任务给客户端
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否分配成功</returns>
    Task<bool> AssignTaskAsync(int taskId, string clientId);

    /// <summary>
    /// 批量分配任务给客户端
    /// </summary>
    /// <param name="taskIds">任务ID列表</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>成功分配的任务数量</returns>
    Task<int> AssignTasksAsync(List<int> taskIds, string clientId);

    /// <summary>
    /// 开始执行任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否开始成功</returns>
    Task<bool> StartTaskAsync(int taskId, string clientId);

    /// <summary>
    /// 完成任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="result">执行结果</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否完成成功</returns>
    Task<bool> CompleteTaskAsync(int taskId, string result, string clientId);

    /// <summary>
    /// 任务执行失败
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="shouldRetry">是否应该重试</param>
    /// <returns>是否处理成功</returns>
    Task<bool> FailTaskAsync(int taskId, string errorMessage, string clientId, bool shouldRetry = true);

    /// <summary>
    /// 取消任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="reason">取消原因</param>
    /// <returns>是否取消成功</returns>
    Task<bool> CancelTaskAsync(int taskId, string reason);

    /// <summary>
    /// 获取项目的任务列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="status">状态过滤</param>
    /// <param name="taskType">任务类型过滤</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页任务列表</returns>
    Task<(List<AutomationTask> Items, int TotalCount)> GetProjectTasksAsync(
        int projectId, 
        string? status = null, 
        string? taskType = null, 
        int pageIndex = 1, 
        int pageSize = 20);

    /// <summary>
    /// 获取客户端的任务列表
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="status">状态过滤</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页任务列表</returns>
    Task<(List<AutomationTask> Items, int TotalCount)> GetClientTasksAsync(
        string clientId, 
        string? status = null, 
        int pageIndex = 1, 
        int pageSize = 20);

    /// <summary>
    /// 获取超时的任务
    /// </summary>
    /// <returns>超时任务列表</returns>
    Task<List<AutomationTask>> GetTimeoutTasksAsync();

    /// <summary>
    /// 重置超时任务状态
    /// </summary>
    /// <param name="taskIds">任务ID列表</param>
    /// <returns>重置成功的任务数量</returns>
    Task<int> ResetTimeoutTasksAsync(List<int> taskIds);

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <param name="projectId">项目ID，为空则获取全部项目统计</param>
    /// <returns>任务统计</returns>
    Task<Dictionary<string, int>> GetTaskStatisticsAsync(int? projectId = null);

    /// <summary>
    /// 根据来源创建任务
    /// </summary>
    /// <param name="sourceType">来源类型</param>
    /// <param name="sourceId">来源ID</param>
    /// <param name="taskType">任务类型</param>
    /// <param name="taskName">任务名称</param>
    /// <param name="taskData">任务数据</param>
    /// <param name="priority">优先级</param>
    /// <returns>创建的任务</returns>
    Task<AutomationTask> CreateTaskFromSourceAsync(
        string sourceType,
        int sourceId,
        string taskType,
        string taskName,
        string taskData,
        string priority = AutomationTaskPriority.Medium);

    /// <summary>
    /// 批量创建任务
    /// </summary>
    /// <param name="tasks">任务列表</param>
    /// <returns>创建成功的任务数量</returns>
    Task<int> CreateTasksAsync(List<AutomationTask> tasks);

    /// <summary>
    /// 检查任务依赖是否满足
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>依赖是否满足</returns>
    Task<bool> CheckDependenciesAsync(int taskId);

    /// <summary>
    /// 获取可执行的任务（依赖已满足）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="maxCount">最大返回数量</param>
    /// <returns>可执行任务列表</returns>
    Task<List<AutomationTask>> GetExecutableTasksAsync(int? projectId = null, int maxCount = 10);
}
