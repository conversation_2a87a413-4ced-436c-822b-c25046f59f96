-- 初始化测试提示词数据
USE ProjectManagementAI;

-- 检查是否已有分类数据
IF NOT EXISTS (SELECT * FROM PromptCategories WHERE Name = '需求分析')
BEGIN
    -- 插入测试分类
    INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, IsEnabled, TemplateCount, CreatedTime)
    VALUES
    ('需求分析', '用于需求收集、分析和整理的提示词模板', 'analysis', '#1890ff', 1, 1, 0, GETDATE()),
    ('代码生成', '用于各种代码生成任务的提示词模板', 'code', '#52c41a', 2, 1, 0, GETDATE()),
    ('测试生成', '用于测试用例和测试代码生成的提示词模板', 'test', '#faad14', 3, 1, 0, GETDATE()),
    ('文档生成', '用于技术文档和说明文档生成的提示词模板', 'document', '#722ed1', 4, 1, 0, GETDATE()),
    ('代码审查', '用于代码质量检查和优化建议的提示词模板', 'review', '#f5222d', 5, 1, 0, GETDATE()),
    ('调试辅助', '用于问题诊断和调试的提示词模板', 'debug', '#fa8c16', 6, 1, 0, GETDATE());
    
    PRINT '测试分类数据插入完成';
END

-- 检查是否已有模板数据
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '需求分析对话模板')
BEGIN
    -- 获取分类ID
    DECLARE @RequirementCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '需求分析');
    DECLARE @CodeGenCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '代码生成');
    DECLARE @TestCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '测试生成');
    
    -- 插入测试模板
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, IsEnabled, UsageCount, CreatedTime
    )
    VALUES
    -- 需求分析模板
    (
        '需求分析对话模板',
        '用于与用户进行需求分析对话，收集和整理项目需求',
        @RequirementCategoryId,
        N'你是一个专业的需求分析师，请与用户进行需求分析对话。请按照以下步骤进行需求分析：1. 需求理解 2. 需求细化 3. 需求确认',
        N'{"projectBackground": {"type": "string", "description": "项目背景信息", "required": true}}',
        'System',
        'RequirementAnalysis',
        NULL,
        NULL,
        'Easy',
        800,
        '需求分析,对话,业务需求',
        1,
        0,
        GETDATE()
    ),
    (
        '需求规格书生成模板',
        '根据需求对话内容生成标准的需求规格书文档',
        @RequirementCategoryId,
        N'请根据需求对话内容，生成一份完整的需求规格书。包含：1. 项目概述 2. 功能需求 3. 非功能需求 4. 技术约束 5. 验收标准',
        N'{"conversationHistory": {"type": "string", "description": "需求对话历史记录", "required": true}}',
        'System',
        'RequirementAnalysis',
        NULL,
        NULL,
        'Medium',
        1200,
        '需求规格书,文档生成',
        1,
        0,
        GETDATE()
    ),
    -- 代码生成模板
    (
        'C# Web API控制器生成',
        '根据需求规格书生成C# Web API控制器代码',
        @CodeGenCategoryId,
        N'请根据需求规格书，生成C# Web API控制器代码。包含：1. 基础结构 2. CRUD操作 3. 业务操作 4. 代码规范',
        N'{"specification": {"type": "string", "description": "需求规格书内容", "required": true}}',
        'System',
        'CodeGeneration',
        'C#',
        'ASP.NET Core',
        'Medium',
        1500,
        'C#,Web API,控制器',
        1,
        0,
        GETDATE()
    ),
    (
        'Vue组件生成模板',
        '根据UI设计和功能需求生成Vue组件代码',
        @CodeGenCategoryId,
        N'请根据设计和需求，生成Vue 3组件代码。包含：1. 组件结构 2. 模板部分 3. 脚本部分 4. 样式部分',
        N'{"requirements": {"type": "string", "description": "功能需求描述", "required": true}}',
        'System',
        'CodeGeneration',
        'TypeScript',
        'Vue 3',
        'Medium',
        1800,
        'Vue,组件,TypeScript',
        1,
        0,
        GETDATE()
    ),
    -- 测试生成模板
    (
        '单元测试生成模板',
        '根据代码和需求规格书生成完整的单元测试代码',
        @TestCategoryId,
        N'请根据代码和需求规格书，生成完整的单元测试。包含：1. 测试类结构 2. 测试用例覆盖 3. 测试数据 4. 断言验证',
        N'{"codeContent": {"type": "string", "description": "要测试的代码", "required": true}}',
        'System',
        'Testing',
        'C#',
        'xUnit',
        'Medium',
        1600,
        '单元测试,测试用例',
        1,
        0,
        GETDATE()
    );
    
    PRINT '测试模板数据插入完成';
END

-- 更新分类的模板数量
UPDATE c SET 
    TemplateCount = (
        SELECT COUNT(*) 
        FROM PromptTemplates t 
        WHERE t.CategoryId = c.Id AND t.IsDeleted = 0 AND t.IsEnabled = 1
    )
FROM PromptCategories c
WHERE c.IsDeleted = 0;

PRINT '分类模板数量更新完成';

-- 显示结果
SELECT '=== 分类和模板数量 ===' as Info;
SELECT 
    c.Id,
    c.Name as CategoryName,
    c.TemplateCount,
    COUNT(t.Id) as ActualCount
FROM PromptCategories c
LEFT JOIN PromptTemplates t ON c.Id = t.CategoryId AND t.IsDeleted = 0 AND t.IsEnabled = 1
WHERE c.IsDeleted = 0
GROUP BY c.Id, c.Name, c.TemplateCount;
