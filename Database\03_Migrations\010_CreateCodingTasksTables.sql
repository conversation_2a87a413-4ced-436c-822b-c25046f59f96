-- =============================================
-- 创建编码任务相关表
-- 创建时间: 2025-06-26
-- 描述: 创建编码任务管理所需的数据库表
-- =============================================

USE [ProjectManagementAI]
GO

-- 设置错误处理
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '开始创建编码任务相关表...';
PRINT '========================================';

-- =============================================
-- 1. 创建编码任务表
-- =============================================

PRINT '正在创建编码任务表...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTasks]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CodingTasks] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [ProjectId] INT NOT NULL,
        [TaskName] NVARCHAR(200) NOT NULL,
        [Description] NVARCHAR(1000) NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'NotStarted', -- NotStarted, InProgress, Completed, Blocked, Cancelled
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium', -- High, Medium, Low
        [AssignedTo] INT NULL, -- 分配给的用户ID
        [EstimatedHours] DECIMAL(10,2) NULL, -- 预估工时
        [ActualHours] DECIMAL(10,2) NULL, -- 实际工时
        [StartDate] DATETIME2 NULL, -- 开始日期
        [DueDate] DATETIME2 NULL, -- 截止日期
        [CompletedDate] DATETIME2 NULL, -- 完成日期
        [TechnologyStack] NVARCHAR(500) NULL, -- 技术栈
        [Tags] NVARCHAR(500) NULL, -- 标签，逗号分隔
        [Notes] NVARCHAR(2000) NULL, -- 备注
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] DATETIME2 NULL,
        [CreatedBy] INT NULL,
        [UpdatedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [Version] INT NOT NULL DEFAULT 1,
        
        CONSTRAINT [PK_CodingTasks] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_CodingTasks_Projects] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[Projects]([Id]),
        CONSTRAINT [FK_CodingTasks_AssignedTo] FOREIGN KEY ([AssignedTo]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_CodingTasks_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_CodingTasks_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_CodingTasks_ProjectId] ON [dbo].[CodingTasks] ([ProjectId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTasks_Status] ON [dbo].[CodingTasks] ([Status]);
    CREATE NONCLUSTERED INDEX [IX_CodingTasks_AssignedTo] ON [dbo].[CodingTasks] ([AssignedTo]);
    CREATE NONCLUSTERED INDEX [IX_CodingTasks_Priority] ON [dbo].[CodingTasks] ([Priority]);
    CREATE NONCLUSTERED INDEX [IX_CodingTasks_CreatedTime] ON [dbo].[CodingTasks] ([CreatedTime]);
    
    PRINT '✓ 编码任务表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ 编码任务表已存在，跳过创建';
END

-- =============================================
-- 2. 创建编码任务与开发步骤关联表
-- =============================================

PRINT '正在创建编码任务步骤关联表...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskSteps]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CodingTaskSteps] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [CodingTaskId] INT NOT NULL,
        [DevelopmentStepId] INT NOT NULL,
        [StepOrder] INT NOT NULL DEFAULT 0, -- 在任务中的排序
        [IsRequired] BIT NOT NULL DEFAULT 1, -- 是否必需
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'NotStarted', -- NotStarted, InProgress, Completed, Blocked, Skipped
        [StartedAt] DATETIME2 NULL, -- 开始时间
        [CompletedAt] DATETIME2 NULL, -- 完成时间
        [EstimatedHours] DECIMAL(10,2) NULL, -- 该步骤在此任务中的预估工时
        [ActualHours] DECIMAL(10,2) NULL, -- 该步骤在此任务中的实际工时
        [Notes] NVARCHAR(1000) NULL, -- 步骤备注
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] DATETIME2 NULL,
        [CreatedBy] INT NULL,
        [UpdatedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        
        CONSTRAINT [PK_CodingTaskSteps] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_CodingTaskSteps_CodingTask] FOREIGN KEY ([CodingTaskId]) REFERENCES [dbo].[CodingTasks]([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_CodingTaskSteps_DevelopmentStep] FOREIGN KEY ([DevelopmentStepId]) REFERENCES [dbo].[DevelopmentSteps]([Id]),
        CONSTRAINT [FK_CodingTaskSteps_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_CodingTaskSteps_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [UQ_CodingTaskSteps_TaskStep] UNIQUE ([CodingTaskId], [DevelopmentStepId]) -- 防止重复关联
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_CodingTaskId] ON [dbo].[CodingTaskSteps] ([CodingTaskId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_DevelopmentStepId] ON [dbo].[CodingTaskSteps] ([DevelopmentStepId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_Status] ON [dbo].[CodingTaskSteps] ([Status]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_Order] ON [dbo].[CodingTaskSteps] ([CodingTaskId], [StepOrder]);
    
    PRINT '✓ 编码任务步骤关联表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ 编码任务步骤关联表已存在，跳过创建';
END

-- =============================================
-- 3. 创建编码任务执行日志表
-- =============================================

PRINT '正在创建编码任务执行日志表...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskExecutionLogs]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CodingTaskExecutionLogs] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [CodingTaskId] INT NOT NULL,
        [CodingTaskStepId] INT NULL, -- 如果是步骤级别的日志
        [ExecutionType] NVARCHAR(50) NOT NULL, -- Task, Step, Comment, StatusChange
        [Action] NVARCHAR(100) NOT NULL, -- Started, Completed, Paused, Resumed, Failed, Commented
        [Description] NVARCHAR(1000) NULL, -- 执行描述
        [OldValue] NVARCHAR(500) NULL, -- 变更前的值
        [NewValue] NVARCHAR(500) NULL, -- 变更后的值
        [ExecutedBy] INT NOT NULL, -- 执行人
        [ExecutedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [Duration] INT NULL, -- 执行时长（分钟）
        [Result] NVARCHAR(50) NULL, -- Success, Failed, Partial
        [ErrorMessage] NVARCHAR(1000) NULL, -- 错误信息
        [AdditionalData] NVARCHAR(MAX) NULL, -- 额外数据（JSON格式）
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        
        CONSTRAINT [PK_CodingTaskExecutionLogs] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_CodingTaskExecutionLogs_CodingTask] FOREIGN KEY ([CodingTaskId]) REFERENCES [dbo].[CodingTasks]([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_CodingTaskExecutionLogs_CodingTaskStep] FOREIGN KEY ([CodingTaskStepId]) REFERENCES [dbo].[CodingTaskSteps]([Id]),
        CONSTRAINT [FK_CodingTaskExecutionLogs_ExecutedBy] FOREIGN KEY ([ExecutedBy]) REFERENCES [dbo].[Users]([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_CodingTaskExecutionLogs_CodingTaskId] ON [dbo].[CodingTaskExecutionLogs] ([CodingTaskId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskExecutionLogs_ExecutedAt] ON [dbo].[CodingTaskExecutionLogs] ([ExecutedAt]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskExecutionLogs_ExecutedBy] ON [dbo].[CodingTaskExecutionLogs] ([ExecutedBy]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskExecutionLogs_ExecutionType] ON [dbo].[CodingTaskExecutionLogs] ([ExecutionType]);
    
    PRINT '✓ 编码任务执行日志表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ 编码任务执行日志表已存在，跳过创建';
END

-- =============================================
-- 4. 创建编码任务评论表
-- =============================================

PRINT '正在创建编码任务评论表...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskComments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CodingTaskComments] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [CodingTaskId] INT NOT NULL,
        [CodingTaskStepId] INT NULL, -- 如果是针对特定步骤的评论
        [ParentCommentId] INT NULL, -- 父评论ID，支持回复
        [Content] NVARCHAR(2000) NOT NULL, -- 评论内容
        [CommentType] NVARCHAR(50) NOT NULL DEFAULT 'General', -- General, Question, Suggestion, Issue, Solution
        [IsInternal] BIT NOT NULL DEFAULT 0, -- 是否内部评论
        [Attachments] NVARCHAR(1000) NULL, -- 附件路径，JSON格式
        [CreatedBy] INT NOT NULL,
        [CreatedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedBy] INT NULL,
        [UpdatedTime] DATETIME2 NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        
        CONSTRAINT [PK_CodingTaskComments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_CodingTaskComments_CodingTask] FOREIGN KEY ([CodingTaskId]) REFERENCES [dbo].[CodingTasks]([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_CodingTaskComments_CodingTaskStep] FOREIGN KEY ([CodingTaskStepId]) REFERENCES [dbo].[CodingTaskSteps]([Id]),
        CONSTRAINT [FK_CodingTaskComments_ParentComment] FOREIGN KEY ([ParentCommentId]) REFERENCES [dbo].[CodingTaskComments]([Id]),
        CONSTRAINT [FK_CodingTaskComments_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_CodingTaskComments_UpdatedBy] FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_CodingTaskComments_CodingTaskId] ON [dbo].[CodingTaskComments] ([CodingTaskId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskComments_CreatedTime] ON [dbo].[CodingTaskComments] ([CreatedTime]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskComments_CreatedBy] ON [dbo].[CodingTaskComments] ([CreatedBy]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskComments_ParentCommentId] ON [dbo].[CodingTaskComments] ([ParentCommentId]);
    
    PRINT '✓ 编码任务评论表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ 编码任务评论表已存在，跳过创建';
END

-- =============================================
-- 5. 创建编码任务附件表
-- =============================================

PRINT '正在创建编码任务附件表...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskAttachments]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CodingTaskAttachments] (
        [Id] INT IDENTITY(1,1) NOT NULL,
        [CodingTaskId] INT NOT NULL,
        [CodingTaskStepId] INT NULL, -- 如果是步骤级别的附件
        [FileName] NVARCHAR(255) NOT NULL, -- 原始文件名
        [FilePath] NVARCHAR(500) NOT NULL, -- 存储路径
        [FileSize] BIGINT NOT NULL, -- 文件大小（字节）
        [FileType] NVARCHAR(100) NULL, -- 文件类型
        [MimeType] NVARCHAR(100) NULL, -- MIME类型
        [Description] NVARCHAR(500) NULL, -- 文件描述
        [Category] NVARCHAR(50) NULL, -- 文件分类：Document, Image, Code, Other
        [UploadedBy] INT NOT NULL,
        [UploadedTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        
        CONSTRAINT [PK_CodingTaskAttachments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_CodingTaskAttachments_CodingTask] FOREIGN KEY ([CodingTaskId]) REFERENCES [dbo].[CodingTasks]([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_CodingTaskAttachments_CodingTaskStep] FOREIGN KEY ([CodingTaskStepId]) REFERENCES [dbo].[CodingTaskSteps]([Id]),
        CONSTRAINT [FK_CodingTaskAttachments_UploadedBy] FOREIGN KEY ([UploadedBy]) REFERENCES [dbo].[Users]([Id])
    );
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_CodingTaskAttachments_CodingTaskId] ON [dbo].[CodingTaskAttachments] ([CodingTaskId]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskAttachments_UploadedTime] ON [dbo].[CodingTaskAttachments] ([UploadedTime]);
    CREATE NONCLUSTERED INDEX [IX_CodingTaskAttachments_Category] ON [dbo].[CodingTaskAttachments] ([Category]);
    
    PRINT '✓ 编码任务附件表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ 编码任务附件表已存在，跳过创建';
END

PRINT '========================================';
PRINT '✅ 编码任务相关表创建完成！';
PRINT '已创建以下表：';
PRINT '1. CodingTasks - 编码任务主表';
PRINT '2. CodingTaskSteps - 编码任务步骤关联表';
PRINT '3. CodingTaskExecutionLogs - 编码任务执行日志表';
PRINT '4. CodingTaskComments - 编码任务评论表';
PRINT '5. CodingTaskAttachments - 编码任务附件表';
