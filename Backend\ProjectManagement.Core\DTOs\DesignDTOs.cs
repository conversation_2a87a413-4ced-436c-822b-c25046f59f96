namespace ProjectManagement.Core.DTOs;

/// <summary>
/// ER图DTO
/// </summary>
public class ERDiagramDto
{
    /// <summary>
    /// ER图ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// ER图名称
    /// </summary>
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的ER图定义
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// ER图描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public string CreatedAt { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public string UpdatedAt { get; set; } = string.Empty;
}

/// <summary>
/// 创建ER图请求DTO
/// </summary>
public class CreateERDiagramRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// ER图名称
    /// </summary>
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的ER图定义
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// ER图描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 更新ER图请求DTO
/// </summary>
public class UpdateERDiagramRequestDto
{
    /// <summary>
    /// ER图名称
    /// </summary>
    public string? DiagramName { get; set; }

    /// <summary>
    /// Mermaid格式的ER图定义
    /// </summary>
    public string? MermaidDefinition { get; set; }

    /// <summary>
    /// ER图描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string? Version { get; set; }
}

/// <summary>
/// 上下文图DTO
/// </summary>
public class ContextDiagramDto
{
    /// <summary>
    /// 上下文图ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 上下文图名称
    /// </summary>
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的上下文图定义
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 外部实体信息
    /// </summary>
    public string? ExternalEntities { get; set; }

    /// <summary>
    /// 系统边界定义
    /// </summary>
    public string? SystemBoundary { get; set; }

    /// <summary>
    /// 数据流信息
    /// </summary>
    public string? DataFlows { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public string CreatedAt { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public string UpdatedAt { get; set; } = string.Empty;
}

/// <summary>
/// 创建上下文图请求DTO
/// </summary>
public class CreateContextDiagramRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 上下文图名称
    /// </summary>
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的上下文图定义
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 外部实体信息
    /// </summary>
    public string? ExternalEntities { get; set; }

    /// <summary>
    /// 系统边界定义
    /// </summary>
    public string? SystemBoundary { get; set; }

    /// <summary>
    /// 数据流信息
    /// </summary>
    public string? DataFlows { get; set; }
}

/// <summary>
/// 更新上下文图请求DTO
/// </summary>
public class UpdateContextDiagramRequestDto
{
    /// <summary>
    /// 上下文图名称
    /// </summary>
    public string? DiagramName { get; set; }

    /// <summary>
    /// Mermaid格式的上下文图定义
    /// </summary>
    public string? MermaidDefinition { get; set; }

    /// <summary>
    /// 外部实体信息
    /// </summary>
    public string? ExternalEntities { get; set; }

    /// <summary>
    /// 系统边界定义
    /// </summary>
    public string? SystemBoundary { get; set; }

    /// <summary>
    /// 数据流信息
    /// </summary>
    public string? DataFlows { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string? Version { get; set; }
}
