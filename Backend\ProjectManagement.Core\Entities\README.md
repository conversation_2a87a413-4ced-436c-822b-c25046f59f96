# SqlSugar 数据模型 - 完整实体类

本目录包含了根据 `DatabaseSchema.sql` 设计的完整 SqlSugar 实体类，支持 AI 驱动的软件开发自动化系统的所有功能。

## 📋 实体类列表

### 1. 核心实体类

| 实体类 | 对应表名 | 功能描述 |
|--------|----------|----------|
| `User` | Users | 系统用户信息管理，支持多角色权限、双因子认证 |
| `Project` | Projects | 项目信息管理，支持项目生命周期跟踪 |
| `RequirementConversation` | RequirementConversations | AI需求对话记录，支持需求收集过程追踪 |
| `RequirementDocument` | RequirementDocuments | 需求规格书管理，支持版本控制和全文搜索 |
| `ERDiagram` | ERDiagrams | 实体关系图管理，支持Mermaid格式 |
| `ContextDiagram` | ContextDiagrams | 系统上下文图管理，支持外部实体和数据流分析 |

### 2. 代码生成相关实体

| 实体类 | 对应表名 | 功能描述 |
|--------|----------|----------|
| `CodeGenerationTask` | CodeGenerationTasks | AI代码生成任务管理，支持多技术栈 |
| `GeneratedCodeFile` | GeneratedCodeFiles | 生成的代码文件存储，支持多文件类型 |
| `TestTask` | TestTasks | AI测试生成和执行管理，支持多测试框架 |
| `DeploymentTask` | DeploymentTasks | 自动化部署管理，支持多环境和多平台 |

### 3. 问题管理相关实体

| 实体类 | 对应表名 | 功能描述 |
|--------|----------|----------|
| `Issue` | Issues | 项目问题和缺陷管理，支持全文搜索 |
| `IssueResolution` | IssueResolutions | 问题解决方案记录，支持AI自动修复 |

### 4. 系统管理相关实体

| 实体类 | 对应表名 | 功能描述 |
|--------|----------|----------|
| `AIModelConfiguration` | AIModelConfigurations | AI模型配置管理，支持多模型和参数配置 |
| `WorkflowState` | WorkflowStates | 项目工作流状态跟踪，支持阶段管理 |
| `SystemLog` | SystemLogs | 系统运行日志管理，支持多级别日志 |

## 🏗️ 架构设计特点

### 1. 统一的主键设计
- 所有实体使用 `INT IDENTITY(1,1)` 自增主键
- 符合 DatabaseSchema.sql 的设计规范
- 提供更好的性能和兼容性

### 2. 完整的审计字段
- `User` 实体包含完整的审计字段（创建时间、更新时间、创建人、更新人等）
- 其他实体根据业务需要包含相应的时间戳字段
- 支持软删除和版本控制

### 3. 丰富的导航属性
- 每个实体都包含相应的导航属性
- 支持一对多、多对一关系
- 便于进行关联查询和数据加载

### 4. 详细的中文注释
- 所有属性都包含详细的中文注释
- 注释说明字段用途、数据类型、约束条件
- 符合 DatabaseSchema.sql 的注释规范

## 🔧 SqlSugar 特性使用

### 1. 表映射配置
```csharp
[SugarTable("TableName", "表描述")]
```

### 2. 列映射配置
```csharp
[SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "字段描述")]
```

### 3. 主键配置
```csharp
[SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
```

### 4. 导航属性配置
```csharp
[SugarColumn(IsIgnore = true)]
```

## 📊 数据类型映射

| C# 类型 | SQL Server 类型 | 说明 |
|---------|-----------------|------|
| `int` | `INT` | 整型，用于ID和枚举 |
| `string` | `NVARCHAR(n)` | 字符串，支持中文 |
| `string` | `NVARCHAR(MAX)` | 大文本，用于内容存储 |
| `DateTime` | `DATETIME2` | 日期时间 |
| `bool` | `BIT` | 布尔值 |
| `decimal` | `DECIMAL(18,2)` | 精确数值 |

## 🚀 使用示例

### 1. 基本查询
```csharp
// 获取所有活跃用户
var activeUsers = await db.Queryable<User>()
    .Where(u => u.Status == 1 && !u.IsDeleted)
    .ToListAsync();
```

### 2. 关联查询
```csharp
// 获取项目及其负责人信息
var projectsWithOwner = await db.Queryable<Project>()
    .Includes(p => p.Owner)
    .ToListAsync();
```

### 3. 复杂查询
```csharp
// 获取项目的完整信息
var projectDetails = await db.Queryable<Project>()
    .Includes(p => p.RequirementDocuments)
    .Includes(p => p.CodeGenerationTasks)
    .Includes(p => p.Issues)
    .Where(p => p.Id == projectId)
    .FirstAsync();
```

## 📝 注意事项

1. **字符编码**: 所有字符串字段使用 `NVARCHAR` 类型，支持中文字符
2. **时间字段**: 使用 `DATETIME2` 类型，提供更高精度
3. **JSON字段**: 大文本字段使用 `NVARCHAR(MAX)` 存储JSON数据
4. **索引优化**: 根据查询需求在数据库层面创建相应索引
5. **外键约束**: 在数据库层面配置外键约束，实体类中使用导航属性

## 🔄 版本信息

- **版本**: 1.0
- **创建日期**: 2024-06-18
- **基于**: DatabaseSchema.sql v1.0
- **SqlSugar版本**: 5.x+
- **目标框架**: .NET 8.0

## 📚 相关文档

- [DatabaseSchema.sql](../../../Database/DatabaseSchema.sql) - 数据库架构设计
- [InitialData.sql](../../../Database/InitialData.sql) - 初始化数据脚本
- [SqlSugar官方文档](https://www.donet5.com/Home/Doc) - SqlSugar使用指南
