-- =============================================
-- 修复编码任务步骤表结构
-- 文件: 011_FixCodingTaskStepsTable.sql
-- 目的: 修复CodingTaskSteps表结构，使其与CodingTaskStep实体类定义匹配
-- 作者: System
-- 创建时间: 2025-06-29
-- =============================================

PRINT '========================================';
PRINT '开始修复编码任务步骤表结构...';
PRINT '========================================';

-- 检查表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskSteps]') AND type in (N'U'))
BEGIN
    PRINT '正在修复CodingTaskSteps表结构...';
    
    -- 1. 添加缺失的字段
    
    -- 添加OrderIndex字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'OrderIndex')
    BEGIN
        IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'StepOrder')
        BEGIN
            -- 如果存在StepOrder，重命名为OrderIndex
            EXEC sp_rename 'CodingTaskSteps.StepOrder', 'OrderIndex', 'COLUMN';
            PRINT '✓ 字段StepOrder重命名为OrderIndex';
        END
        ELSE
        BEGIN
            -- 如果都不存在，添加OrderIndex
            ALTER TABLE CodingTaskSteps ADD OrderIndex INT NOT NULL DEFAULT 0;
            PRINT '✓ 添加字段OrderIndex';
        END
    END
    
    -- 添加StartTime字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'StartTime')
    BEGIN
        IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'StartedAt')
        BEGIN
            -- 如果存在StartedAt，重命名为StartTime
            EXEC sp_rename 'CodingTaskSteps.StartedAt', 'StartTime', 'COLUMN';
            PRINT '✓ 字段StartedAt重命名为StartTime';
        END
        ELSE
        BEGIN
            -- 如果都不存在，添加StartTime
            ALTER TABLE CodingTaskSteps ADD StartTime DATETIME2 NULL;
            PRINT '✓ 添加字段StartTime';
        END
    END
    
    -- 添加CompletedTime字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'CompletedTime')
    BEGIN
        IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'CompletedAt')
        BEGIN
            -- 如果存在CompletedAt，重命名为CompletedTime
            EXEC sp_rename 'CodingTaskSteps.CompletedAt', 'CompletedTime', 'COLUMN';
            PRINT '✓ 字段CompletedAt重命名为CompletedTime';
        END
        ELSE
        BEGIN
            -- 如果都不存在，添加CompletedTime
            ALTER TABLE CodingTaskSteps ADD CompletedTime DATETIME2 NULL;
            PRINT '✓ 添加字段CompletedTime';
        END
    END
    
    -- 添加ExecutionResult字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'ExecutionResult')
    BEGIN
        ALTER TABLE CodingTaskSteps ADD ExecutionResult NVARCHAR(2000) NULL;
        PRINT '✓ 添加字段ExecutionResult';
    END
    
    -- 添加DeletedTime字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'DeletedTime')
    BEGIN
        ALTER TABLE CodingTaskSteps ADD DeletedTime DATETIME2 NULL;
        PRINT '✓ 添加字段DeletedTime';
    END
    
    -- 添加DeletedBy字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'DeletedBy')
    BEGIN
        ALTER TABLE CodingTaskSteps ADD DeletedBy INT NULL;
        PRINT '✓ 添加字段DeletedBy';
        
        -- 添加外键约束
        IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_CodingTaskSteps_DeletedBy')
        BEGIN
            ALTER TABLE CodingTaskSteps ADD CONSTRAINT FK_CodingTaskSteps_DeletedBy 
                FOREIGN KEY (DeletedBy) REFERENCES Users(Id);
            PRINT '✓ 添加DeletedBy外键约束';
        END
    END
    
    -- 添加Version字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'Version')
    BEGIN
        ALTER TABLE CodingTaskSteps ADD Version INT NOT NULL DEFAULT 1;
        PRINT '✓ 添加字段Version';
    END
    
    -- 添加Remarks字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'Remarks')
    BEGIN
        ALTER TABLE CodingTaskSteps ADD Remarks NVARCHAR(500) NULL;
        PRINT '✓ 添加字段Remarks';
    END
    
    -- 2. 删除不需要的字段

    -- 删除IsRequired字段（如果存在）
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IsRequired')
    BEGIN
        -- 先删除默认约束
        DECLARE @ConstraintName NVARCHAR(200)
        SELECT @ConstraintName = name FROM sys.default_constraints
        WHERE parent_object_id = OBJECT_ID('CodingTaskSteps')
        AND parent_column_id = (SELECT column_id FROM sys.columns
                               WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IsRequired')

        IF @ConstraintName IS NOT NULL
        BEGIN
            EXEC('ALTER TABLE CodingTaskSteps DROP CONSTRAINT ' + @ConstraintName)
            PRINT '✓ 删除IsRequired字段的默认约束: ' + @ConstraintName;
        END

        ALTER TABLE CodingTaskSteps DROP COLUMN IsRequired;
        PRINT '✓ 删除字段IsRequired';
    END

    -- 删除EstimatedHours字段（如果存在）
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'EstimatedHours')
    BEGIN
        -- 先删除默认约束（如果有）
        DECLARE @ConstraintName2 NVARCHAR(200)
        SELECT @ConstraintName2 = name FROM sys.default_constraints
        WHERE parent_object_id = OBJECT_ID('CodingTaskSteps')
        AND parent_column_id = (SELECT column_id FROM sys.columns
                               WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'EstimatedHours')

        IF @ConstraintName2 IS NOT NULL
        BEGIN
            EXEC('ALTER TABLE CodingTaskSteps DROP CONSTRAINT ' + @ConstraintName2)
            PRINT '✓ 删除EstimatedHours字段的默认约束: ' + @ConstraintName2;
        END

        ALTER TABLE CodingTaskSteps DROP COLUMN EstimatedHours;
        PRINT '✓ 删除字段EstimatedHours';
    END
    
    PRINT '✅ CodingTaskSteps表结构修复完成';
END
ELSE
BEGIN
    PRINT '❌ CodingTaskSteps表不存在，无法修复';
END

PRINT '========================================';
PRINT '编码任务步骤表结构修复完成！';
PRINT '========================================';
