using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 模板步骤DTO
    /// </summary>
    public class TemplateStepDto
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 所属序列ID
        /// </summary>
        public int SequenceId { get; set; }

        /// <summary>
        /// 关联模板ID
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤顺序
        /// </summary>
        public int StepOrder { get; set; }

        /// <summary>
        /// 动作类型（基础UI操作类型）
        /// </summary>
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 逻辑语句类型（可选，用于条件判断、循环等逻辑控制）
        /// </summary>
        public string? LogicType { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 参数配置
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 条件表达式（用于条件判断类型的步骤）
        /// </summary>
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转目标步骤ID（用于跳转类型的步骤）
        /// </summary>
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数（用于循环类型的步骤）
        /// </summary>
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量名（用于循环类型的步骤）
        /// </summary>
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组标识（用于标识循环体、条件分支等逻辑分组）
        /// </summary>
        public string? GroupId { get; set; }

        /// <summary>
        /// 关联模板信息
        /// </summary>
        public CustomTemplateDto? Template { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }

    /// <summary>
    /// 创建模板步骤DTO
    /// </summary>
    public class CreateTemplateStepDto
    {
        /// <summary>
        /// 步骤ID（更新时使用，创建时为null）
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// 所属序列ID
        /// </summary>
        [Required(ErrorMessage = "所属序列ID不能为空")]
        public int SequenceId { get; set; }

        /// <summary>
        /// 关联模板ID
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤顺序
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "步骤顺序必须大于0")]
        public int StepOrder { get; set; }

        /// <summary>
        /// 动作类型（基础UI操作类型）
        /// </summary>
        [Required(ErrorMessage = "动作类型不能为空")]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 逻辑语句类型（可选，用于条件判断、循环等逻辑控制）
        /// </summary>
        public string? LogicType { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        [StringLength(500, ErrorMessage = "步骤描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 参数配置
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        [Range(1, 300, ErrorMessage = "超时时间必须在1到300秒之间")]
        public int TimeoutSeconds { get; set; } = 5;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 10, ErrorMessage = "最大重试次数必须在0到10次之间")]
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 条件表达式（用于条件判断类型的步骤）
        /// </summary>
        [StringLength(1000, ErrorMessage = "条件表达式长度不能超过1000个字符")]
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转目标步骤ID（用于跳转类型的步骤）
        /// </summary>
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数（用于循环类型的步骤）
        /// </summary>
        [Range(-1, 1000, ErrorMessage = "循环次数必须在-1到1000之间")]
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量名（用于循环类型的步骤）
        /// </summary>
        [StringLength(50, ErrorMessage = "循环变量名长度不能超过50个字符")]
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组标识（用于标识循环体、条件分支等逻辑分组）
        /// </summary>
        [StringLength(50, ErrorMessage = "分组标识长度不能超过50个字符")]
        public string? GroupId { get; set; }
    }

    /// <summary>
    /// 更新模板步骤DTO
    /// </summary>
    public class UpdateTemplateStepDto
    {
        /// <summary>
        /// 关联模板ID
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤顺序
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "步骤顺序必须大于0")]
        public int? StepOrder { get; set; }

        /// <summary>
        /// 动作类型（基础UI操作类型）
        /// </summary>
        public string? ActionType { get; set; }

        /// <summary>
        /// 逻辑语句类型（可选，用于条件判断、循环等逻辑控制）
        /// </summary>
        public string? LogicType { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        [StringLength(500, ErrorMessage = "步骤描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 参数配置
        /// </summary>
        public Dictionary<string, object>? Parameters { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        [Range(1, 300, ErrorMessage = "超时时间必须在1到300秒之间")]
        public int? TimeoutSeconds { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 10, ErrorMessage = "最大重试次数必须在0到10次之间")]
        public int? MaxRetries { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 条件表达式（用于条件判断类型的步骤）
        /// </summary>
        [StringLength(1000, ErrorMessage = "条件表达式长度不能超过1000个字符")]
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转目标步骤ID（用于跳转类型的步骤）
        /// </summary>
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数（用于循环类型的步骤）
        /// </summary>
        [Range(-1, 1000, ErrorMessage = "循环次数必须在-1到1000之间")]
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量名（用于循环类型的步骤）
        /// </summary>
        [StringLength(50, ErrorMessage = "循环变量名长度不能超过50个字符")]
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组标识（用于标识循环体、条件分支等逻辑分组）
        /// </summary>
        [StringLength(50, ErrorMessage = "分组标识长度不能超过50个字符")]
        public string? GroupId { get; set; }
    }
}

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 执行日志DTO
    /// </summary>
    public class ExecutionLogDto
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 序列ID
        /// </summary>
        public int? SequenceId { get; set; }

        /// <summary>
        /// 模板ID
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤ID
        /// </summary>
        public int? StepId { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        public string ExecutionType { get; set; } = string.Empty;

        /// <summary>
        /// 执行状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行耗时（毫秒）
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string? Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 截图路径
        /// </summary>
        public string? ScreenshotPath { get; set; }

        /// <summary>
        /// 执行者
        /// </summary>
        public string? ExecutedBy { get; set; }

        /// <summary>
        /// 关联序列信息
        /// </summary>
        public TemplateSequenceDto? Sequence { get; set; }

        /// <summary>
        /// 关联模板信息
        /// </summary>
        public CustomTemplateDto? Template { get; set; }

        /// <summary>
        /// 关联步骤信息
        /// </summary>
        public TemplateStepDto? Step { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 执行日志查询DTO
    /// </summary>
    public class ExecutionLogQueryDto : PageQueryDto
    {
        /// <summary>
        /// 序列ID过滤
        /// </summary>
        public int? SequenceId { get; set; }

        /// <summary>
        /// 模板ID过滤
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤ID过滤
        /// </summary>
        public int? StepId { get; set; }

        /// <summary>
        /// 执行类型过滤
        /// </summary>
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 状态过滤
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 执行者过滤
        /// </summary>
        public string? ExecutedBy { get; set; }

        /// <summary>
        /// 开始时间范围开始
        /// </summary>
        public DateTime? StartTimeStart { get; set; }

        /// <summary>
        /// 开始时间范围结束
        /// </summary>
        public DateTime? StartTimeEnd { get; set; }
    }
}
