using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 向量化操作服务接口 - 核心的批量处理和AI优化功能
    /// </summary>
    public interface IVectorOperationService
    {
        /// <summary>
        /// 批量创建项目
        /// </summary>
        /// <param name="projectTemplates">项目模板列表</param>
        /// <returns>批量操作结果</returns>
        Task<BatchOperationResult> BatchCreateProjectsAsync(IEnumerable<ProjectTemplate> projectTemplates);

        /// <summary>
        /// 批量更新项目
        /// </summary>
        /// <param name="projectUpdates">项目更新信息</param>
        /// <returns>批量操作结果</returns>
        Task<BatchOperationResult> BatchUpdateProjectsAsync(IEnumerable<ProjectUpdate> projectUpdates);

        /// <summary>
        /// 批量删除项目
        /// </summary>
        /// <param name="projectIds">项目ID列表</param>
        /// <returns>批量操作结果</returns>
        Task<BatchOperationResult> BatchDeleteProjectsAsync(IEnumerable<int> projectIds);

        /// <summary>
        /// 智能优化项目 - 基于AI分析的优化建议
        /// </summary>
        /// <returns>优化结果</returns>
        Task<OptimizationResult> OptimizeProjectsAsync();

        /// <summary>
        /// 向量化分析 - 对多个项目进行并行分析
        /// </summary>
        /// <param name="projectIds">要分析的项目ID列表</param>
        /// <returns>分析结果</returns>
        Task<VectorAnalysisResult> VectorAnalyzeAsync(IEnumerable<int> projectIds);

        /// <summary>
        /// 批量生成开发步骤
        /// </summary>
        /// <param name="projectIds">项目ID列表</param>
        /// <returns>批量生成结果</returns>
        Task<BatchOperationResult> BatchGenerateStepsAsync(IEnumerable<int> projectIds);

        /// <summary>
        /// 批量执行自动化任务
        /// </summary>
        /// <param name="automationTasks">自动化任务列表</param>
        /// <returns>执行结果</returns>
        Task<BatchOperationResult> BatchExecuteAutomationAsync(IEnumerable<AutomationTask> automationTasks);

        /// <summary>
        /// 相似性分析 - 找出相似的项目
        /// </summary>
        /// <param name="referenceProjectId">参考项目ID</param>
        /// <param name="threshold">相似度阈值</param>
        /// <returns>相似项目列表</returns>
        Task<IEnumerable<SimilarityResult>> FindSimilarProjectsAsync(int referenceProjectId, double threshold = 0.7);

        /// <summary>
        /// 预测项目风险
        /// </summary>
        /// <param name="projectIds">项目ID列表</param>
        /// <returns>风险预测结果</returns>
        Task<IEnumerable<RiskPrediction>> PredictProjectRisksAsync(IEnumerable<int> projectIds);

        /// <summary>
        /// 智能资源分配
        /// </summary>
        /// <param name="projectIds">项目ID列表</param>
        /// <returns>资源分配建议</returns>
        Task<ResourceAllocationResult> OptimizeResourceAllocationAsync(IEnumerable<int> projectIds);
    }

    #region 数据模型

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResult
    {
        public int TotalCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 项目模板
    /// </summary>
    public class ProjectTemplate
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ProjectType { get; set; } = string.Empty;
        public string TechnologyStack { get; set; } = string.Empty;
        public int? ParentProjectId { get; set; }
        public Dictionary<string, object> CustomProperties { get; set; } = new();
    }

    /// <summary>
    /// 项目更新信息
    /// </summary>
    public class ProjectUpdate
    {
        public int ProjectId { get; set; }
        public Dictionary<string, object> Updates { get; set; } = new();
    }

    /// <summary>
    /// 优化结果
    /// </summary>
    public class OptimizationResult
    {
        public List<OptimizationSuggestion> Suggestions { get; set; } = new();
        public double OverallScore { get; set; }
        public Dictionary<string, double> Metrics { get; set; } = new();
    }

    /// <summary>
    /// 优化建议
    /// </summary>
    public class OptimizationSuggestion
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Impact { get; set; }
        public double Confidence { get; set; }
        public string ActionType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 向量分析结果
    /// </summary>
    public class VectorAnalysisResult
    {
        public List<ProjectAnalysis> ProjectAnalyses { get; set; } = new();
        public Dictionary<string, double> GlobalMetrics { get; set; } = new();
        public List<string> Insights { get; set; } = new();
    }

    /// <summary>
    /// 项目分析
    /// </summary>
    public class ProjectAnalysis
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public double ComplexityScore { get; set; }
        public double RiskScore { get; set; }
        public double QualityScore { get; set; }
        public Dictionary<string, double> DetailedMetrics { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 自动化任务
    /// </summary>
    public class AutomationTask
    {
        public string TaskType { get; set; } = string.Empty;
        public int ProjectId { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public int Priority { get; set; }
    }

    /// <summary>
    /// 相似性结果
    /// </summary>
    public class SimilarityResult
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public double SimilarityScore { get; set; }
        public List<string> SimilarityFactors { get; set; } = new();
    }

    /// <summary>
    /// 风险预测
    /// </summary>
    public class RiskPrediction
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public double RiskScore { get; set; }
        public List<RiskFactor> RiskFactors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 风险因子
    /// </summary>
    public class RiskFactor
    {
        public string Category { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Impact { get; set; }
        public double Probability { get; set; }
    }

    /// <summary>
    /// 资源分配结果
    /// </summary>
    public class ResourceAllocationResult
    {
        public List<ResourceAllocation> Allocations { get; set; } = new();
        public double EfficiencyScore { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 资源分配
    /// </summary>
    public class ResourceAllocation
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public Dictionary<string, double> ResourceRequirements { get; set; } = new();
        public List<string> RecommendedTeamMembers { get; set; } = new();
        public DateTime RecommendedStartDate { get; set; }
        public DateTime RecommendedEndDate { get; set; }
    }

    #endregion
}
