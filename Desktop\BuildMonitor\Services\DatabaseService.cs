using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SqlSugar;
using BuildMonitor.Models;
using System.Security.Cryptography;
using System.Text;

namespace BuildMonitor.Services
{
    /// <summary>
    /// 数据库服务
    /// </summary>
    public class DatabaseService : IDisposable
    {
        private readonly ISqlSugarClient _db;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public DatabaseService(string connectionString, bool enableSqlLogging = true, int commandTimeout = 30)
        {
            _db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
                MoreSettings = new ConnMoreSettings()
                {
                    IsAutoRemoveDataCache = true,
                    SqlServerCodeFirstNvarchar = true
                }
            });

            // 设置命令超时时间
            _db.Ado.CommandTimeOut = commandTimeout;

            // 配置SQL日志
            if (enableSqlLogging)
            {
                _db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    Console.WriteLine($"[SQL] {sql}");
                    if (pars != null && pars.Length > 0)
                    {
                        Console.WriteLine($"[参数] {string.Join(", ", pars.Select(p => $"{p.ParameterName}={p.Value}"))}");
                    }
                };

                _db.Aop.OnError = (exp) =>
                {
                    Console.WriteLine($"[SQL错误] {exp.Message}");
                };
            }
        }

        /// <summary>
        /// 清空指定项目类型的错误
        /// </summary>
        public async Task ClearErrorsAsync(string projectType, int? projectId = null, string? sessionId = null)
        {
            await _semaphore.WaitAsync();
            try
            {
                var queryable = _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted && e.ProjectType == projectType);

                if (projectId.HasValue)
                {
                    queryable = queryable.Where(e => e.ProjectId == projectId);
                }

                if (!string.IsNullOrEmpty(sessionId))
                {
                    queryable = queryable.Where(e => e.CompilationSessionId == sessionId);
                }

                var errors = await queryable.ToListAsync();

                foreach (var error in errors)
                {
                    error.IsDeleted = true;
                    error.DeletedTime = DateTime.Now;
                }

                if (errors.Any())
                {
                    await _db.Deleteable(errors).ExecuteCommandAsync();
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 批量插入编译错误
        /// </summary>
        public async Task InsertBackendErrorsAsync(IEnumerable<CompilationError> errors)
        {
            await _semaphore.WaitAsync();
            try
            {
                // 过滤掉不需要的错误类型，只保留Error级别的数据
                var filteredErrors = errors.Where(a =>
                    a.Code.ToUpper() != "PARSE_ERROR" &&
                    a.Code.ToUpper() != "COMPILE_ERROR" &&
                    a.Severity.Equals("Error", StringComparison.OrdinalIgnoreCase)).ToList();

                // 进一步去重：基于文件路径、行号、列号、错误代码的组合
                var uniqueErrors = filteredErrors
                    .GroupBy(e => new { e.FilePath, e.LineNumber, e.ColumnNumber, e.Code })
                    .Select(g => g.First()) // 每组只取第一个
                    .ToList();

                if (uniqueErrors.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"💾 原始错误数: {errors.Count()}, 过滤后(仅Error级别): {filteredErrors.Count}, 去重后: {uniqueErrors.Count}");
                    await _db.Insertable(uniqueErrors).ExecuteCommandAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ 原始错误数: {errors.Count()}, 过滤后无Error级别数据需要保存");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }
        /// <summary>
        /// 批量插入编译错误
        /// </summary>
        public async Task InsertFrontendErrorsAsync(IEnumerable<CompilationError> errors)
        {
            await _semaphore.WaitAsync();
            try
            {
                // 过滤掉不需要的错误类型，只保留Error级别的数据
                var filteredErrors = errors.Where(a =>
                    a.Code.ToUpper() != "PARSE_ERROR" &&
                    a.Code.ToUpper() != "COMPILE_ERROR" &&
                    a.Severity.Equals("Error", StringComparison.OrdinalIgnoreCase)).ToList();
                filteredErrors = filteredErrors.Where(a => a.Code.ToUpper() != "NO_PACKAGE_JSON").ToList();
                if (filteredErrors.Any())
                {
                    await _db.Insertable(filteredErrors).ExecuteCommandAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ 原始错误数: {errors.Count()}, 过滤后无Error级别数据需要保存");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }


        /// <summary>
        /// 保存编译错误（自动编译服务使用）
        /// </summary>
        public async Task SaveCompilationBackendErrorsAsync(IEnumerable<CompilationError> errors)
        {
            await _semaphore.WaitAsync();
            try
            {
                var errorList = errors.ToList();
                if (errorList.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"💾 准备保存 {errorList.Count} 个编译错误到数据库");

                    // 过滤掉不需要的错误类型，只保留Error级别的数据
                    var filteredErrors = errorList.Where(a =>
                        a.Code.ToUpper() != "PARSE_ERROR" &&
                        a.Code.ToUpper() != "COMPILE_ERROR" &&
                        a.Severity.Equals("Error", StringComparison.OrdinalIgnoreCase)).ToList();

                    // 进一步去重：基于文件路径、行号、列号、错误代码的组合
                    var uniqueErrors = filteredErrors
                        .GroupBy(e => new { e.FilePath, e.LineNumber, e.ColumnNumber, e.Code })
                        .Select(g => g.First()) // 每组只取第一个
                        .ToList();

                    foreach (var error in uniqueErrors.Take(3)) // 只显示前3个错误的详情
                    {
                        System.Diagnostics.Debug.WriteLine($"   - {error.ProjectType}: {error.Severity} - {error.Message}");
                    }

                    if (uniqueErrors.Any())
                    {
                        var result = await _db.Insertable(uniqueErrors).ExecuteCommandAsync();
                        System.Diagnostics.Debug.WriteLine($"✅ 原始: {errorList.Count}, 过滤(仅Error级别): {filteredErrors.Count}, 去重: {uniqueErrors.Count}, 保存: {result} 条记录");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"ℹ️ 原始: {errorList.Count}, 过滤后无Error级别数据需要保存");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有编译错误需要保存");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }



        /// <summary>
        /// 保存编译错误（自动编译服务使用）
        /// </summary>
        public async Task SaveCompilationFroentendErrorsAsync(IEnumerable<CompilationError> errors)
        {
            await _semaphore.WaitAsync();
            try
            {
                var errorList = errors.ToList();
                if (errorList.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"💾 准备保存 {errorList.Count} 个编译错误到数据库");

                    // 过滤掉不需要的错误类型，只保留Error级别的数据
                    var filteredErrors = errorList.Where(a =>
                        a.Code.ToUpper() != "PARSE_ERROR" &&
                        a.Code.ToUpper() != "COMPILE_ERROR" &&
                        a.Severity.Equals("Error", StringComparison.OrdinalIgnoreCase)).ToList();
                    filteredErrors = filteredErrors.Where(a => a.Code.ToUpper() != "NO_PACKAGE_JSON").ToList();
                    if (filteredErrors.Any())
                    {
                        var result = await _db.Insertable(filteredErrors).ExecuteCommandAsync();
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"ℹ️ 原始: {errorList.Count}, 过滤后无Error级别数据需要保存");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有编译错误需要保存");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }


        /// <summary>
        /// 获取编译错误列表
        /// </summary>
        public async Task<List<CompilationError>> GetErrorsAsync(
            string? projectType = null,
            string? severity = null,
            int? projectId = null,
            string? sessionId = null)
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 查询编译错误 - ProjectType: {projectType}, ProjectId: {projectId}");

                var queryable = _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted);

                if (!string.IsNullOrEmpty(projectType))
                {
                    queryable = queryable.Where(e => e.ProjectType == projectType);
                }

                if (!string.IsNullOrEmpty(severity))
                {
                    queryable = queryable.Where(e => e.Severity == severity);
                }

                if (projectId.HasValue)
                {
                    queryable = queryable.Where(e => e.ProjectId == projectId);
                }

                if (!string.IsNullOrEmpty(sessionId))
                {
                    queryable = queryable.Where(e => e.CompilationSessionId == sessionId);
                }

                var result = await queryable
                    .OrderByDescending(e => e.CompilationTime)
                    .OrderByDescending(e => e.Id)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 查询到 {result.Count} 个编译错误");
                return result;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 获取项目列表
        /// </summary>
        public async Task<List<Project>> GetProjectsAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                return await _db.Queryable<Project>()
                    .Where(p => !p.IsDeleted)
                    .OrderBy(p => p.Name)
                    .ToListAsync();
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 根据用户ID获取项目列表（用户只能看到自己负责的项目）
        /// </summary>
        public async Task<List<Project>> GetProjectsByUserIdAsync(int userId)
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 查询用户 {userId} 的项目列表");

                var result = await _db.Queryable<Project>()
                    .Where(p => !p.IsDeleted && p.OwnerId.HasValue && p.OwnerId.Value == userId)
                    .OrderBy(p => p.Name)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 用户 {userId} 拥有 {result.Count} 个项目");
                return result;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 清理包含 ANSI 代码的错误记录
        /// </summary>
        public async Task CleanupAnsiErrorsAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 开始清理包含 ANSI 代码的错误记录");

                // 查找包含 ANSI 代码的错误记录
                var ansiErrors = await _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted &&
                               (e.Message.Contains("[") && e.Message.Contains("m")) ||
                               e.Message.Contains("kB") ||
                               e.Message.Contains("gzip:") ||
                               e.Message.Contains("dist/"))
                    .ToListAsync();

                if (ansiErrors.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"🗑️ 找到 {ansiErrors.Count} 条包含 ANSI 代码的错误记录，准备删除");

                    foreach (var error in ansiErrors)
                    {
                        error.IsDeleted = true;
                        error.DeletedTime = DateTime.Now;
                    }

                    await _db.Deleteable(ansiErrors).ExecuteCommandAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ 已清理 {ansiErrors.Count} 条无效错误记录");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有找到需要清理的 ANSI 错误记录");
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _semaphore?.Dispose();
            _db?.Dispose();
        }

        /// <summary>
        /// 清理重复的编译错误记录
        /// </summary>
        public async Task CleanupDuplicateErrorsAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 开始清理重复的编译错误记录");

                // 查找重复的错误记录（基于文件路径、行号、列号、错误代码）
                // 先获取所有未删除的记录，然后在内存中进行分组处理
                var allErrors = await _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 数据库中共有 {allErrors.Count} 条未删除的编译错误记录");

                // 特别检查 CS1001 和 CS1002 错误
                var cs1001Errors = allErrors.Where(e => e.Code == "CS1001").ToList();
                var cs1002Errors = allErrors.Where(e => e.Code == "CS1002").ToList();
                System.Diagnostics.Debug.WriteLine($"📊 CS1001 错误: {cs1001Errors.Count} 条");
                System.Diagnostics.Debug.WriteLine($"📊 CS1002 错误: {cs1002Errors.Count} 条");

                var duplicateGroups = allErrors
                    .GroupBy(e => new { e.FilePath, e.LineNumber, e.ColumnNumber, e.Code })
                    .Where(g => g.Count() > 1)
                    .Select(g => new {
                        Key = g.Key,
                        Count = g.Count(),
                        MinId = g.Min(x => x.Id), // 保留最早的记录
                        Items = g.ToList()
                    })
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"📊 找到 {duplicateGroups.Count} 组重复错误组");

                if (duplicateGroups.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 找到 {duplicateGroups.Count} 组重复错误");

                    int totalDuplicates = 0;
                    foreach (var group in duplicateGroups)
                    {
                        // 从内存中的分组数据直接获取需要删除的重复记录
                        var duplicatesToDelete = group.Items
                            .Where(e => e.Id != group.MinId)
                            .ToList();

                        if (duplicatesToDelete.Count > 0)
                        {
                            foreach (var duplicate in duplicatesToDelete)
                            {
                                duplicate.IsDeleted = true;
                                duplicate.DeletedTime = DateTime.Now;
                            }

                            await _db.Updateable(duplicatesToDelete).ExecuteCommandAsync();
                            totalDuplicates += duplicatesToDelete.Count;

                            System.Diagnostics.Debug.WriteLine($"   - 删除 {duplicatesToDelete.Count} 条重复记录: {group.Key.Code} at {group.Key.FilePath}({group.Key.LineNumber},{group.Key.ColumnNumber})");
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ 成功清理 {totalDuplicates} 条重复的编译错误记录");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有发现重复的编译错误记录");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 清理重复错误记录失败: {ex.Message}");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 强制清理所有重复的编译错误记录（更激进的方法）
        /// </summary>
        public async Task ForceCleanupDuplicateErrorsAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 开始强制清理重复的编译错误记录");

                // 获取所有错误记录
                var allErrors = await _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted)
                    .OrderBy(e => e.Id) // 按ID排序，保留最早的
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 数据库中共有 {allErrors.Count} 条未删除的编译错误记录");

                var duplicatesToDelete = new List<CompilationError>();
                var processedKeys = new HashSet<string>();

                foreach (var error in allErrors)
                {
                    // 创建更宽松的唯一键：只基于错误代码、文件路径、行号
                    var uniqueKey = $"{error.Code}:{error.FilePath}:{error.LineNumber}";

                    if (processedKeys.Contains(uniqueKey))
                    {
                        // 这是重复记录，标记为删除
                        duplicatesToDelete.Add(error);
                        System.Diagnostics.Debug.WriteLine($"🗑️ 标记删除重复记录: {error.Code} at {error.FilePath}({error.LineNumber}) - ID: {error.Id}");
                    }
                    else
                    {
                        processedKeys.Add(uniqueKey);
                        System.Diagnostics.Debug.WriteLine($"✅ 保留记录: {error.Code} at {error.FilePath}({error.LineNumber}) - ID: {error.Id}");
                    }
                }

                if (duplicatesToDelete.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"🗑️ 准备删除 {duplicatesToDelete.Count} 条重复记录");

                    foreach (var duplicate in duplicatesToDelete)
                    {
                        duplicate.IsDeleted = true;
                        duplicate.DeletedTime = DateTime.Now;
                    }

                    var result = await _db.Updateable(duplicatesToDelete).ExecuteCommandAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ 成功删除 {result} 条重复的编译错误记录");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有发现重复的编译错误记录");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 强制清理重复错误记录失败: {ex.Message}");
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 清理数据库中文件路径的编译器前缀
        /// </summary>
        public async Task CleanupFilePathPrefixesAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 开始清理文件路径中的编译器前缀");

                // 查找包含编译器前缀的记录
                var errorsWithPrefix = await _db.Queryable<CompilationError>()
                    .Where(e => !e.IsDeleted && SqlFunc.Contains(e.FilePath, ">"))
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"📊 找到 {errorsWithPrefix.Count} 条包含编译器前缀的记录");

                if (errorsWithPrefix.Count > 0)
                {
                    foreach (var error in errorsWithPrefix)
                    {
                        var originalPath = error.FilePath;
                        // 清理文件路径中的编译器前缀（如 "3>", "2>" 等）
                        error.FilePath = System.Text.RegularExpressions.Regex.Replace(error.FilePath, @"^\d+>", "").Trim();

                        if (originalPath != error.FilePath)
                        {
                            System.Diagnostics.Debug.WriteLine($"🔧 清理路径: {originalPath} -> {error.FilePath}");
                            error.UpdatedTime = DateTime.Now;
                        }
                    }

                    var result = await _db.Updateable(errorsWithPrefix).ExecuteCommandAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ 成功清理 {result} 条记录的文件路径");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ 没有发现需要清理的文件路径");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 清理文件路径前缀失败: {ex.Message}");
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 验证用户登录凭据
        /// </summary>
        public async Task<User?> ValidateUserAsync(string username, string password)
        {
            await _semaphore.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔐 验证用户登录: {username}");

                var user = await _db.Queryable<User>()
                    .Where(u => u.Username == username && !u.IsDeleted && u.Status == 1)
                    .FirstAsync();

                if (user != null)
                {
                    // 验证密码（这里简化处理，实际应该使用哈希验证）
                    var passwordHash = ComputePasswordHash(password);
                    if (user.PasswordHash == passwordHash || user.PasswordHash == password)
                    {
                        // 更新最后登录时间
                        user.LastLoginTime = DateTime.Now;
                        await _db.Updateable(user).ExecuteCommandAsync();

                        System.Diagnostics.Debug.WriteLine($"✅ 用户 {username} 登录成功");
                        return user;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"❌ 用户 {username} 登录失败");
                return null;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        public async Task<List<User>> GetUsersAsync()
        {
            await _semaphore.WaitAsync();
            try
            {
                return await _db.Queryable<User>()
                    .Where(u => !u.IsDeleted && u.Status == 1)
                    .OrderBy(u => u.Username)
                    .ToListAsync();
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        public async Task<User?> GetUserByIdAsync(int userId)
        {
            await _semaphore.WaitAsync();
            try
            {
                return await _db.Queryable<User>()
                    .Where(u => u.Id == userId && !u.IsDeleted)
                    .FirstAsync();
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 计算密码哈希（简化版本）
        /// </summary>
        private string ComputePasswordHash(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
