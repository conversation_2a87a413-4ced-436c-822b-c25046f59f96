import { test, expect } from '@playwright/test';

test.describe('Playwright基础功能测试', () => {
  test('应用首页可以正常访问', async ({ page }) => {
    // 访问应用首页
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');

    // 验证页面加载成功
    await expect(page.locator('body')).toBeVisible();

    // 验证页面标题包含应用名称
    await expect(page).toHaveTitle(/登录|AI驱动软件开发自动化系统/);
  });

  test('页面基本元素存在', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');

    // 验证基本页面结构
    const body = page.locator('body');
    await expect(body).toBeVisible();

    // 验证页面不是空白的
    const content = await body.textContent();
    expect(content).toBeTruthy();
    expect(content!.length).toBeGreaterThan(0);
  });

});


