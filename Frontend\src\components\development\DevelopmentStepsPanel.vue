<template>
  <div class="development-steps-panel">
    <!-- 工具栏 -->
    <div class="steps-toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          :icon="Plus"
          @click="showCreateStepDialog = true"
        >
          添加步骤
        </el-button>

        <el-button
          type="default"
          @click="showDecomposeDialog = true"
          :loading="decomposing"
        >
          分解需求
        </el-button>

        <el-button
          type="success"
          @click="showTemplateSequenceDialog = true"
        >
          <el-icon><List /></el-icon>
          应用模板序列
        </el-button>

        <el-button
          :icon="Refresh"
          @click="refreshSteps"
          :loading="loading"
        >
          刷新
        </el-button>

        <el-dropdown @command="handleBatchOperation" v-if="selectedSteps.length > 0">
          <el-button type="default">
            批量操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="applyTemplate">应用模板序列</el-dropdown-item>
              <el-dropdown-item command="updateStatus">更新状态</el-dropdown-item>
              <el-dropdown-item command="updatePriority">更新优先级</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索步骤..."
          :prefix-icon="Search"
          style="width: 200px"
          @input="handleSearch"
          clearable
        />

        <el-select v-model="filterStatus" placeholder="状态" clearable style="width: 120px">
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>

        <el-select v-model="filterPriority" placeholder="优先级" clearable style="width: 120px">
          <el-option
            v-for="priority in priorityOptions"
            :key="priority.value"
            :label="priority.label"
            :value="priority.value"
          />
        </el-select>

        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="list">列表</el-radio-button>
          <el-radio-button label="tree">树形</el-radio-button>
          <el-radio-button label="kanban">看板</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="steps-statistics" v-if="statistics">
      <el-row :gutter="16">
        <el-col :span="4">
          <el-statistic title="总步骤" :value="statistics.totalSteps" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="已完成" :value="statistics.completedSteps" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="进行中" :value="statistics.inProgressSteps" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="待处理" :value="statistics.pendingSteps" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="预估工时" :value="statistics.totalEstimatedHours" suffix="小时" />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="完成率"
            :value="statistics.totalSteps > 0 ? Number((statistics.completedSteps / statistics.totalSteps * 100).toFixed(1)) : 0"
            suffix="%"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 步骤内容 -->
    <div class="steps-content">
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view">
        <el-table
          :data="steps"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          row-key="id"
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="stepName" label="步骤名称" min-width="200">
            <template #default="{ row }">
              <div class="step-name">
                <el-tag :type="getStepTypeTagType(row.stepType)" size="small">
                  {{ getStepTypeIcon(row.stepType) }}
                </el-tag>
                <span class="name">{{ row.stepName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="子步骤" width="100" align="center">
            <template #default="{ row }">
              <div
                class="child-steps-cell"
                :class="{ 'has-children': getChildStepCount(row) > 0 }"
                @dblclick="viewChildSteps(row)"
              >
                {{ getChildStepCount(row) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityTagType(row.priority)" size="small">
                {{ getPriorityIcon(row.priority) }} {{ row.priority }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusIcon(row.status) }} {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </template>
          </el-table-column>

          <el-table-column prop="estimatedHours" label="预估工时" width="100">
            <template #default="{ row }">
              {{ row.estimatedHours || '-' }}h
            </template>
          </el-table-column>

          <el-table-column prop="technologyStack" label="技术栈" width="120" />

          <el-table-column label="模板序列" width="150">
            <template #default="{ row }">
              <div v-if="row.templateSequences && row.templateSequences.length > 0" class="template-sequences">
                <el-tag
                  v-for="sequence in row.templateSequences.slice(0, 2)"
                  :key="sequence.id"
                  size="small"
                  type="success"
                  class="sequence-tag"
                >
                  {{ sequence.name }}
                </el-tag>
                <el-tag
                  v-if="row.templateSequences.length > 2"
                  size="small"
                  type="info"
                >
                  +{{ row.templateSequences.length - 2 }}
                </el-tag>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row, $index }">
              <el-button size="small" @click="viewStepDetail(row)">详情</el-button>
              <el-button size="small" @click="editStep(row)">编辑</el-button>

              <!-- 上移下移按钮 -->
              <el-button
                size="small"
                :disabled="$index === 0"
                @click="moveStepUp(row, $index)"
                title="上移"
              >
                ↑
              </el-button>
              <el-button
                size="small"
                :disabled="$index === steps.length - 1"
                @click="moveStepDown(row, $index)"
                title="下移"
              >
                ↓
              </el-button>

              <el-dropdown @command="(command) => handleStepOperation(command, row)">
                <el-button size="small">
                  更多 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="execute" v-if="row.status === 'Pending'">
                      执行
                    </el-dropdown-item>
                    <el-dropdown-item command="aiDecompose">
                      <el-icon><MagicStick /></el-icon>
                      AI细分
                    </el-dropdown-item>
                    <el-dropdown-item command="customDecompose">
                      <el-icon><Plus /></el-icon>
                      自定义细分
                    </el-dropdown-item>
                    <el-dropdown-item command="applyTemplate">应用模板序列</el-dropdown-item>
                    <el-dropdown-item command="viewTemplates" v-if="row.templateSequences?.length">
                      查看模板序列
                    </el-dropdown-item>
                    <el-dropdown-item command="dependencies">依赖关系</el-dropdown-item>
                    <el-dropdown-item command="history">执行历史</el-dropdown-item>
                    <el-dropdown-item command="complexity">复杂度分析</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="!showTree && viewMode !== 'list'">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!-- 树形数据统计信息 -->
        <div class="tree-stats" v-if="viewMode === 'list'">
          <span>共 {{ totalCount }} 个步骤</span>
        </div>
      </div>

      <!-- 树形视图 -->
      <div v-else-if="viewMode === 'tree'" class="tree-view">
        <el-tree
          :data="treeSteps"
          :props="{ children: 'children', label: 'stepName' }"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          v-loading="loading"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="step-info">
                <span class="step-icon">{{ getStepTypeIcon(data.stepType) }}</span>
                <span class="step-name">{{ data.stepName }}</span>
                <el-tag :type="getStepTypeTagType(data.stepType)" size="small">{{ data.stepType }}</el-tag>
                <el-tag :type="getPriorityTagType(data.priority)" size="small">{{ data.priority }}</el-tag>
                <el-tag :type="getStatusTagType(data.status)" size="small">{{ getStatusText(data.status) }}</el-tag>
              </div>
              <div class="step-actions">
                <el-button size="small" @click="viewStepDetail(data)">查看</el-button>
                <el-button size="small" @click="editStep(data)">编辑</el-button>
                <el-button size="small" type="success" @click="decomposeStep(data)">
                  <el-icon><MagicStick /></el-icon>
                  AI细分
                </el-button>
                <el-button size="small" type="primary" @click="customDecomposeStep(data)">
                  <el-icon><Plus /></el-icon>
                  自定义细分
                </el-button>
                <el-button size="small" type="danger" @click="deleteStep(data)">删除</el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 看板视图 -->
      <div v-else-if="viewMode === 'kanban'" class="kanban-view">
        <!-- TODO: 实现看板视图组件 -->
        <el-empty description="看板视图功能正在开发中" />
        <!-- <StepKanbanBoard
          :steps="steps"
          @update-step="updateStepStatus"
          @view-step="viewStepDetail"
          @edit-step="editStep"
        /> -->
      </div>
    </div>

    <!-- 创建步骤对话框 -->
    <StepCreateDialog
      v-model="showCreateStepDialog"
      :project-id="projectId"
      @save="handleStepSave"
    />

    <!-- 需求分解对话框 -->
    <RequirementDecomposeDialog
      v-model="showDecomposeDialog"
      :project-id="projectId"
      :requirement-document-id="requirementDocumentId"
      @success="handleDecomposeSuccess"
    />

    <!-- 步骤详情对话框 -->
    <StepDetailDialog
      v-model="showStepDetail"
      :step="selectedStep"
      @refresh="refreshSteps"
      @edit="handleEditFromDetail"
      @decompose-success="handleStepDecomposeSuccess"
    />

    <!-- 步骤编辑对话框 -->
    <StepEditDialog
      v-model="showStepEdit"
      :step="selectedStep"
      @save="handleStepSave"
    />

    <!-- 模板序列选择对话框 -->
    <TemplateSequenceSelectionDialog
      v-model="showTemplateSequenceDialog"
      :project-id="projectId"
      :selected-steps="selectedSteps"
      @apply="handleTemplateSequenceApply"
    />

    <!-- 步骤分解对话框 -->
    <StepDecomposeDialog
      v-model="showStepDecomposeDialog"
      :step="selectedStep"
      @decompose-success="handleStepDecomposeSuccess"
    />

    <!-- 自定义细分对话框 -->
    <StepCustomDecomposeDialog
      v-model="showCustomDecomposeDialog"
      :step="selectedStep"
      @decompose-success="handleStepDecomposeSuccess"
    />

    <!-- 步骤模板序列详情对话框 -->
    <el-dialog
      v-model="showStepTemplateSequencesDialog"
      :title="`步骤模板序列 - ${currentStepForTemplates?.stepName}`"
      width="70%"
      :close-on-click-modal="false"
    >
      <div class="step-template-sequences-content">
        <div class="step-info-section">
          <h4>步骤信息</h4>
          <div class="step-basic-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="步骤名称">
                {{ currentStepForTemplates?.stepName }}
              </el-descriptions-item>
              <el-descriptions-item label="步骤类型">
                <el-tag :type="getStepTypeTagType(currentStepForTemplates?.stepType || '') as any">
                  {{ getStepTypeIcon(currentStepForTemplates?.stepType || '') }}
                  {{ currentStepForTemplates?.stepType }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="getPriorityTagType(currentStepForTemplates?.priority || '') as any">
                  {{ currentStepForTemplates?.priority }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusTagType(currentStepForTemplates?.status || '') as any">
                  {{ currentStepForTemplates?.status }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <div class="sequences-section">
          <h4>关联的模板序列 ({{ stepTemplateSequences.length }}个)</h4>
          <div class="sequences-list">
            <el-card
              v-for="sequence in stepTemplateSequences"
              :key="sequence.id"
              class="sequence-card"
              shadow="hover"
            >
              <template #header>
                <div class="sequence-header">
                  <div class="sequence-title">
                    <el-icon><List /></el-icon>
                    <span class="sequence-name">{{ sequence.name }}</span>
                  </div>
                  <div class="sequence-meta">
                    <el-tag size="small" type="info">{{ sequence.category || '未分类' }}</el-tag>
                    <el-tag size="small" v-if="sequence.stepCount">{{ sequence.stepCount }}步</el-tag>
                  </div>
                </div>
              </template>

              <div class="sequence-content">
                <div class="sequence-description" v-if="sequence.description">
                  <p>{{ sequence.description }}</p>
                </div>

                <div class="sequence-info">
                  <div class="info-item">
                    <span class="label">应用时间:</span>
                    <span class="value">{{ formatDate(sequence.appliedTime) }}</span>
                  </div>
                </div>
              </div>

              <template #footer>
                <div class="sequence-actions">
                  <el-button size="small" type="primary" @click="viewSequenceDetails(sequence)">
                    查看详情
                  </el-button>
                  <el-button size="small" type="danger" @click="removeSequenceFromStep(sequence)">
                    移除关联
                  </el-button>
                </div>
              </template>
            </el-card>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showStepTemplateSequencesDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 序列详情对话框 -->
    <el-dialog
      v-model="showSequenceDetailDialog"
      :title="`模板序列详情 - ${currentSequenceDetail?.name}`"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="sequence-detail-content" v-if="currentSequenceDetail">
        <!-- 序列基本信息 -->
        <div class="sequence-basic-info">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="序列名称">
              {{ currentSequenceDetail.name }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              <el-tag type="info">{{ currentSequenceDetail.category || '未分类' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentSequenceDetail.isActive ? 'success' : 'danger'">
                {{ currentSequenceDetail.isActive ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="使用次数">
              {{ currentSequenceDetail.usageCount || 0 }}次
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(currentSequenceDetail.createdTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后使用">
              {{ currentSequenceDetail.lastUsedTime ? formatDate(currentSequenceDetail.lastUsedTime) : '从未使用' }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="sequence-description" v-if="currentSequenceDetail.description">
            <h5>描述</h5>
            <p>{{ currentSequenceDetail.description }}</p>
          </div>

          <div class="sequence-tags" v-if="currentSequenceDetail.tags && currentSequenceDetail.tags.length > 0">
            <h5>标签</h5>
            <el-tag
              v-for="tag in currentSequenceDetail.tags"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
          </div>

          <div class="sequence-notes" v-if="currentSequenceDetail.notes">
            <h5>备注</h5>
            <p>{{ currentSequenceDetail.notes }}</p>
          </div>
        </div>

        <!-- 序列步骤 -->
        <div class="sequence-steps-section">
          <h4>执行步骤 ({{ currentSequenceDetail.steps?.length || 0 }}步)</h4>
          <div class="steps-timeline" v-if="currentSequenceDetail.steps && currentSequenceDetail.steps.length > 0">
            <div
              v-for="(step, index) in currentSequenceDetail.steps"
              :key="step.id"
              class="step-timeline-item"
              :class="{ 'inactive': !step.isActive }"
            >
              <div class="step-timeline-marker">
                <div class="step-number">{{ step.stepOrder }}</div>
              </div>
              <div class="step-timeline-content">
                <div class="step-header">
                  <div class="step-action-type">
                    <el-tag :type="getActionTypeTagType(step.actionType) as any" size="small">
                      {{ getActionTypeText(step.actionType) }}
                    </el-tag>
                    <el-tag v-if="!step.isActive" type="danger" size="small">已禁用</el-tag>
                  </div>
                  <div class="step-meta">
                    <span class="timeout">超时: {{ step.timeoutSeconds }}s</span>
                    <span class="retries">重试: {{ step.maxRetries }}次</span>
                  </div>
                </div>
                <div class="step-description">
                  {{ step.description }}
                </div>
                <div class="step-parameters" v-if="step.parameters && Object.keys(step.parameters).length > 0">
                  <h6>参数配置</h6>
                  <el-descriptions size="small" :column="1" border>
                    <el-descriptions-item
                      v-for="(value, key) in step.parameters"
                      :key="key"
                      :label="String(key)"
                    >
                      <code>{{ formatParameterValue(value) }}</code>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <!-- 流程控制信息 -->
                <div class="step-flow-control" v-if="hasFlowControl(step)">
                  <h6>流程控制</h6>
                  <div class="flow-control-items">
                    <div v-if="step.conditionExpression" class="flow-item">
                      <span class="label">条件表达式:</span>
                      <code>{{ step.conditionExpression }}</code>
                    </div>
                    <div v-if="step.jumpToStepId" class="flow-item">
                      <span class="label">跳转到步骤:</span>
                      <el-tag size="small">步骤 {{ step.jumpToStepId }}</el-tag>
                    </div>
                    <div v-if="step.loopCount" class="flow-item">
                      <span class="label">循环次数:</span>
                      <el-tag size="small" type="warning">{{ step.loopCount }}次</el-tag>
                    </div>
                    <div v-if="step.loopVariable" class="flow-item">
                      <span class="label">循环变量:</span>
                      <code>{{ step.loopVariable }}</code>
                    </div>
                    <div v-if="step.groupId" class="flow-item">
                      <span class="label">分组ID:</span>
                      <el-tag size="small" type="info">{{ step.groupId }}</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-steps">
            <el-empty description="该序列暂无配置步骤" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSequenceDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 步骤详情对话框 -->
    <!-- TODO: 实现步骤详情对话框组件 -->
    <!-- <StepDetailDialog
      v-model="showStepDetail"
      :step="selectedStep"
      @update="handleStepUpdate"
    /> -->

    <!-- 步骤编辑对话框 -->
    <!-- TODO: 实现步骤编辑对话框组件 -->
    <!-- <StepEditDialog
      v-model="showStepEdit"
      :step="selectedStep"
      @save="handleStepSave"
    /> -->

    <!-- 子步骤查看对话框 -->
    <el-dialog
      v-model="showChildStepsDialog"
      :title="`子步骤详情 - ${selectedStepName}`"
      width="80%"
      :before-close="handleChildStepsClose"
    >
      <div v-if="childStepsData.length > 0">
        <el-alert
          title="提示"
          description="双击子步骤名称可以查看该步骤的子步骤"
          type="info"
          :closable="false"
          style="margin-bottom: 16px;"
        />

        <el-table :data="childStepsData" border>
          <el-table-column prop="stepName" label="步骤名称" min-width="200">
            <template #default="{ row }">
              <div class="step-name" @dblclick="viewChildSteps(row)">
                <el-tag :type="getStepTypeTagType(row.stepType)" size="small">
                  {{ getStepTypeIcon(row.stepType) }}
                </el-tag>
                <span class="name">{{ row.stepName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="子步骤" width="100" align="center">
            <template #default="{ row }">
              <div
                class="child-steps-cell"
                :class="{ 'has-children': getChildStepCount(row) > 0 }"
              >
                {{ getChildStepCount(row) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityTagType(row.priority)" size="small">
                {{ getPriorityIcon(row.priority) }} {{ row.priority }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusIcon(row.status) }} {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress }}%</span>
            </template>
          </el-table-column>

          <el-table-column prop="estimatedHours" label="预估工时" width="100">
            <template #default="{ row }">
              {{ row.estimatedHours || '-' }}h
            </template>
          </el-table-column>

          <el-table-column prop="technologyStack" label="技术栈" width="120" />
        </el-table>
      </div>

      <el-empty v-else description="该步骤暂无子步骤" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleChildStepsClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, ArrowDown, List, MagicStick } from '@element-plus/icons-vue'
import { developmentService } from '@/services/development'
import CustomTemplateService from '@/services/customTemplate'
import type { ElTagType } from '@/types/element-plus'
import type {
  DevelopmentStep,
  ProjectStepStatistics,
  StepFilterOptions,
  DecompositionResult
} from '@/types/development'
import {
  STEP_TYPE_CONFIG,
  PRIORITY_CONFIG,
  STATUS_CONFIG
} from '@/types/development'

// 组件引入
// import StepTreeNode from './StepTreeNode.vue'
// import StepKanbanBoard from './StepKanbanBoard.vue'
import StepCreateDialog from './StepCreateDialog.vue'
import RequirementDecomposeDialog from './RequirementDecomposeDialog.vue'
import TemplateSequenceSelectionDialog from './TemplateSequenceSelectionDialog.vue'
import StepDetailDialog from './StepDetailDialog.vue'
import StepEditDialog from './StepEditDialog.vue'
import StepDecomposeDialog from './StepDecomposeDialog.vue'
import StepCustomDecomposeDialog from './StepCustomDecomposeDialog.vue'

// Props
interface Props {
  projectId: number
  requirementDocumentId?: number
  showTree?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showTree: false
})

// 响应式数据
const loading = ref(false)
const decomposing = ref(false)
const steps = ref<DevelopmentStep[]>([])
const treeSteps = ref<DevelopmentStep[]>([])
const statistics = ref<ProjectStepStatistics>()
const selectedSteps = ref<DevelopmentStep[]>([])
const selectedStep = ref<DevelopmentStep>()

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 筛选和搜索
const searchKeyword = ref('')
const filterStatus = ref('')
const filterPriority = ref('')
const viewMode = ref<'list' | 'tree' | 'kanban'>('list')

// 对话框状态
const showCreateStepDialog = ref(false)
const showDecomposeDialog = ref(false)
const showStepDecomposeDialog = ref(false)
const showCustomDecomposeDialog = ref(false)
const showTemplateSequenceDialog = ref(false)
const showChildStepsDialog = ref(false)
const selectedStepName = ref('')
const childStepsData = ref<DevelopmentStep[]>([])
const showStepDetail = ref(false)
const showStepEdit = ref(false)
const showStepTemplateSequencesDialog = ref(false)
const showSequenceDetailDialog = ref(false)

// 模板序列相关数据
const stepTemplateSequences = ref<any[]>([])
const currentStepForTemplates = ref<DevelopmentStep>()
const currentSequenceDetail = ref<any>()

// 选项数据
const statusOptions = computed(() =>
  Object.entries(STATUS_CONFIG).map(([value, config]) => ({
    value,
    label: config.description
  }))
)

const priorityOptions = computed(() =>
  Object.entries(PRIORITY_CONFIG).map(([value, config]) => ({
    value,
    label: config.description
  }))
)

// 方法
// 为树形数据添加hasChildren属性
const processTreeData = (treeData: DevelopmentStep[]): DevelopmentStep[] => {
  const processNode = (node: DevelopmentStep): DevelopmentStep => {
    const processedNode = {
      ...node,
      hasChildren: node.children && node.children.length > 0
    }

    if (processedNode.children && processedNode.children.length > 0) {
      processedNode.children = processedNode.children.map(child => processNode(child))
    }

    return processedNode
  }

  return treeData.map(node => processNode(node))
}

// 递归筛选树形数据
const filterTreeData = (treeData: DevelopmentStep[]): DevelopmentStep[] => {
  const filterNode = (node: DevelopmentStep): DevelopmentStep | null => {
    // 检查当前节点是否匹配筛选条件
    let nodeMatches = true

    if (filterStatus.value && node.status !== filterStatus.value) {
      nodeMatches = false
    }
    if (filterPriority.value && node.priority !== filterPriority.value) {
      nodeMatches = false
    }
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      const textMatches = node.stepName.toLowerCase().includes(keyword) ||
                         (node.stepDescription && node.stepDescription.toLowerCase().includes(keyword))
      if (!textMatches) {
        nodeMatches = false
      }
    }

    // 递归筛选子节点
    let filteredChildren: DevelopmentStep[] = []
    if (node.children && node.children.length > 0) {
      filteredChildren = node.children
        .map(child => filterNode(child))
        .filter(child => child !== null) as DevelopmentStep[]
    }

    // 如果当前节点匹配或有匹配的子节点，则保留该节点
    if (nodeMatches || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren,
        hasChildren: filteredChildren.length > 0
      }
    }

    return null
  }

  return treeData
    .map(node => filterNode(node))
    .filter(node => node !== null) as DevelopmentStep[]
}

// 计算树形数据的总节点数
const countTreeNodes = (treeData: DevelopmentStep[]): number => {
  let count = 0
  const countNode = (node: DevelopmentStep) => {
    count++
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => countNode(child))
    }
  }
  treeData.forEach(node => countNode(node))
  return count
}

const loadSteps = async () => {
  loading.value = true

  // 清空现有数据，防止重复
  steps.value = []
  treeSteps.value = []
  totalCount.value = 0

  try {
    console.log('Loading steps for project:', props.projectId, 'viewMode:', viewMode.value, 'showTree:', props.showTree)

    if (viewMode.value === 'tree') {
      console.log('Loading tree steps...')
      treeSteps.value = await developmentService.getProjectStepTree(props.projectId)
      console.log('Tree steps loaded:', treeSteps.value.length)
    } else {
      console.log('Loading tree steps for list view...')
      // 列表视图也使用树形数据，以支持层级显示
      let treeData = await developmentService.getProjectStepTree(props.projectId)
      console.log('Tree data loaded for list view:', treeData.length)

      // 处理树形数据，添加hasChildren属性
      treeData = processTreeData(treeData)

      // 应用筛选
      if (filterStatus.value || filterPriority.value || searchKeyword.value) {
        treeData = filterTreeData(treeData)
        console.log('Filtered tree data:', treeData.length)
      }

      steps.value = treeData
      totalCount.value = countTreeNodes(treeData)

      console.log('Processed tree data for list view:', steps.value.length, 'total nodes:', totalCount.value)
    }
  } catch (error) {
    console.error('加载开发步骤失败:', error)
    ElMessage.error('加载开发步骤失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    statistics.value = await developmentService.getProjectStepStatistics(props.projectId)
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const refreshSteps = () => {
  loadSteps()
  loadStatistics()
}

// 事件处理
const handleSelectionChange = (selection: DevelopmentStep[]) => {
  selectedSteps.value = selection
}

const handleSearch = () => {
  currentPage.value = 1
  loadSteps()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  // 列表视图使用树形数据，不需要重新加载
  if (viewMode.value !== 'list') {
    loadSteps()
  }
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // 列表视图使用树形数据，不需要重新加载
  if (viewMode.value !== 'list') {
    loadSteps()
  }
}

const handleDecomposeSuccess = (result: DecompositionResult) => {
  ElMessage.success(`需求分解成功，生成 ${result.data?.steps.length || 0} 个开发步骤`)
  refreshSteps()
}

const handleStepDecomposeSuccess = (result: any) => {
  ElMessage.success(`步骤分解成功，生成 ${result.childSteps?.length || 0} 个子步骤`)
  refreshSteps()
}

const handleBatchOperation = (command: string) => {
  switch (command) {
    case 'applyTemplate':
      if (selectedSteps.value.length === 0) {
        ElMessage.warning('请先选择要应用模板序列的步骤')
        return
      }
      showTemplateSequenceDialog.value = true
      break
    case 'updateStatus':
      // TODO: 实现批量更新状态
      ElMessage.info('批量更新状态功能正在开发中')
      break
    case 'updatePriority':
      // TODO: 实现批量更新优先级
      ElMessage.info('批量更新优先级功能正在开发中')
      break
    case 'delete':
      handleBatchDelete()
      break
    default:
      console.log('批量操作:', command, selectedSteps.value)
  }
}

const handleStepOperation = (command: string, step: DevelopmentStep) => {
  selectedStep.value = step

  switch (command) {
    case 'execute':
      executeStep(step)
      break
    case 'aiDecompose':
      decomposeStep(step)
      break
    case 'customDecompose':
      customDecomposeStep(step)
      break
    case 'applyTemplate':
      selectedSteps.value = [step]
      showTemplateSequenceDialog.value = true
      break
    case 'viewTemplates':
      viewStepTemplateSequences(step)
      break
    case 'dependencies':
      // 显示依赖关系
      viewStepDetail(step)
      break
    case 'history':
      // 显示执行历史
      viewStepDetail(step)
      break
    case 'complexity':
      // 显示复杂度分析
      viewStepDetail(step)
      break
    case 'delete':
      deleteStep(step)
      break
  }
}

const viewStepDetail = (step: DevelopmentStep) => {
  selectedStep.value = step
  showStepDetail.value = true
}

const editStep = (step: DevelopmentStep) => {
  selectedStep.value = step
  showStepEdit.value = true
}

const decomposeStep = (step: DevelopmentStep) => {
  selectedStep.value = step
  showStepDecomposeDialog.value = true
}

const customDecomposeStep = (step: DevelopmentStep) => {
  selectedStep.value = step
  showCustomDecomposeDialog.value = true
}

const executeStep = async (step: DevelopmentStep) => {
  try {
    await developmentService.startStepExecution(step.id)
    ElMessage.success('步骤执行已开始')
    refreshSteps()
  } catch (error) {
    ElMessage.error('启动步骤执行失败')
  }
}

const deleteStep = async (step: DevelopmentStep) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除步骤"${step.stepName}"吗？`,
      '确认删除',
      { type: 'warning' }
    )

    await developmentService.deleteStep(step.id)
    ElMessage.success('删除成功')
    refreshSteps()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除步骤
const handleBatchDelete = async () => {
  if (selectedSteps.value.length === 0) {
    ElMessage.warning('请先选择要删除的步骤')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedSteps.value.length} 个步骤吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除步骤
    const deletePromises = selectedSteps.value.map(step =>
      developmentService.deleteStep(step.id)
    )

    await Promise.all(deletePromises)

    ElMessage.success(`成功删除 ${selectedSteps.value.length} 个步骤`)
    selectedSteps.value = []
    refreshSteps()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const updateStepStatus = async (stepId: number, status: string) => {
  try {
    await developmentService.updateStep(stepId, { status: status as any })
    ElMessage.success('状态更新成功')
    refreshSteps()
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const handleStepUpdate = () => {
  refreshSteps()
}

const handleStepSave = () => {
  refreshSteps()
}

const handleEditFromDetail = (step: DevelopmentStep) => {
  selectedStep.value = step
  showStepDetail.value = false
  showStepEdit.value = true
}

const handleTemplateSequenceApply = async (sequenceId: number, targetSteps: DevelopmentStep[]) => {
  try {
    ElMessage.info('正在应用模板序列...')

    // 调用后端API应用模板序列到指定步骤
    await developmentService.applyTemplateSequenceToSteps(sequenceId, targetSteps.map(s => s.id))

    ElMessage.success(`模板序列已成功应用到 ${targetSteps.length} 个开发步骤`)
    refreshSteps()
  } catch (error) {
    console.error('应用模板序列失败:', error)
    ElMessage.error('应用模板序列失败')
  }
}

// 上移步骤
const moveStepUp = async (step: DevelopmentStep, index: number) => {
  if (index === 0) return

  try {
    const currentStep = steps.value[index]
    const previousStep = steps.value[index - 1]

    // 交换步骤顺序
    const stepOrders = [
      { stepId: currentStep.id, order: previousStep.stepOrder, parentStepId: currentStep.parentStepId },
      { stepId: previousStep.id, order: currentStep.stepOrder, parentStepId: previousStep.parentStepId }
    ]

    await developmentService.reorderSteps(stepOrders)
    ElMessage.success('步骤上移成功')
    refreshSteps()
  } catch (error) {
    console.error('步骤上移失败:', error)
    ElMessage.error('步骤上移失败')
  }
}

// 下移步骤
const moveStepDown = async (step: DevelopmentStep, index: number) => {
  if (index === steps.value.length - 1) return

  try {
    const currentStep = steps.value[index]
    const nextStep = steps.value[index + 1]

    // 交换步骤顺序
    const stepOrders = [
      { stepId: currentStep.id, order: nextStep.stepOrder, parentStepId: currentStep.parentStepId },
      { stepId: nextStep.id, order: currentStep.stepOrder, parentStepId: nextStep.parentStepId }
    ]

    await developmentService.reorderSteps(stepOrders)
    ElMessage.success('步骤下移成功')
    refreshSteps()
  } catch (error) {
    console.error('步骤下移失败:', error)
    ElMessage.error('步骤下移失败')
  }
}

const viewStepTemplateSequences = async (step: DevelopmentStep) => {
  try {
    const sequences = await developmentService.getStepTemplateSequences(step.id)

    if (sequences.length === 0) {
      ElMessage.info('该步骤没有关联的模板序列')
      return
    }

    // 显示模板序列详情对话框
    currentStepForTemplates.value = step
    stepTemplateSequences.value = sequences
    showStepTemplateSequencesDialog.value = true
    console.log('步骤模板序列:', sequences)
  } catch (error) {
    console.error('获取步骤模板序列失败:', error)
    ElMessage.error('获取步骤模板序列失败')
  }
}

// 格式化日期时间
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看序列详情
const viewSequenceDetails = async (sequence: any) => {
  try {
    ElMessage.info('正在加载序列详情...')

    // 调用API获取完整的序列详情（包含步骤）
    const sequenceDetail = await CustomTemplateService.getSequence(sequence.id)

    currentSequenceDetail.value = sequenceDetail
    showSequenceDetailDialog.value = true

    console.log('序列详情:', sequenceDetail)
  } catch (error) {
    console.error('获取序列详情失败:', error)
    ElMessage.error('获取序列详情失败')
  }
}

// 移除步骤的模板序列关联
const removeSequenceFromStep = async (sequence: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除步骤 "${currentStepForTemplates.value?.stepName}" 与模板序列 "${sequence.name}" 的关联吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (!currentStepForTemplates.value) {
      ElMessage.error('当前步骤信息不存在')
      return
    }

    // 调用后端API移除关联
    await developmentService.removeStepTemplateSequence(
      currentStepForTemplates.value.id,
      sequence.id
    )

    ElMessage.success('模板序列关联已移除')
    console.log('移除关联成功:', {
      stepId: currentStepForTemplates.value.id,
      sequenceId: sequence.id,
      sequenceName: sequence.name
    })

    // 刷新模板序列列表
    await viewStepTemplateSequences(currentStepForTemplates.value)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('移除关联失败:', error)
      ElMessage.error('移除关联失败')
    }
  }
}

// 获取动作类型的标签类型
const getActionTypeTagType = (actionType: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    'click': 'primary',
    'input': 'success',
    'wait': 'warning',
    'scroll': 'info',
    'key': 'danger',
    'condition': 'warning',
    'loop': 'info'
  }
  return typeMap[actionType] || 'default'
}

// 获取动作类型的文本
const getActionTypeText = (actionType: string) => {
  const textMap: Record<string, string> = {
    'click': '点击',
    'input': '输入',
    'wait': '等待',
    'scroll': '滚动',
    'key': '按键',
    'condition': '条件',
    'loop': '循环'
  }
  return textMap[actionType] || actionType
}

// 格式化参数值
const formatParameterValue = (value: any) => {
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2)
  }
  return String(value)
}

// 检查是否有流程控制
const hasFlowControl = (step: any) => {
  return step.conditionExpression ||
         step.jumpToStepId ||
         step.loopCount ||
         step.loopVariable ||
         step.groupId
}

// 辅助方法
const getStepTypeIcon = (type: string) => STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG]?.icon || '📝'
const getStepTypeTagType = (type: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    Development: 'primary',
    Testing: 'success',
    Documentation: 'info',
    Deployment: 'warning',
    Review: 'danger'
  }
  return typeMap[type] || 'info'
}

const getPriorityIcon = (priority: string) => PRIORITY_CONFIG[priority as keyof typeof PRIORITY_CONFIG]?.icon || '➡️'
const getPriorityTagType = (priority: string): ElTagType => {
  const priorityMap: Record<string, ElTagType> = {
    Low: 'success',
    Medium: 'primary',
    High: 'warning',
    Critical: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getStatusIcon = (status: string) => STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]?.icon || '❓'
const getStatusText = (status: string) => STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]?.description || status
const getStatusTagType = (status: string): ElTagType => {
  const statusMap: Record<string, ElTagType> = {
    Pending: 'info',
    InProgress: 'primary',
    Completed: 'success',
    Failed: 'danger',
    Blocked: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取子步骤数量
const getChildStepCount = (step: DevelopmentStep) => {
  // 如果步骤有 children 属性（树形结构），返回其长度
  if ((step as any).children && Array.isArray((step as any).children)) {
    return (step as any).children.length
  }
  // 如果没有子步骤，返回 0
  return 0
}

// 查看子步骤
const viewChildSteps = async (step: DevelopmentStep) => {
  selectedStepName.value = step.stepName

  try {
    // 如果步骤有 children 属性（树形结构），直接使用
    if (step.children && Array.isArray(step.children)) {
      childStepsData.value = step.children
      showChildStepsDialog.value = true
      return
    }

    // 如果步骤有 childSteps 属性，直接使用
    if (step.children && Array.isArray(step.children)) {
      childStepsData.value = step.children
      showChildStepsDialog.value = true
      return
    }

    // 通过API获取子步骤
    const result = await developmentService.getProjectSteps(step.projectId, 1, 100, { keyword: `parent:${step.id}` })
    childStepsData.value = result.items
    showChildStepsDialog.value = true

  } catch (error) {
    console.error('获取子步骤失败:', error)
    ElMessage.error('获取子步骤失败')
    childStepsData.value = []
  }
}

// 关闭子步骤对话框
const handleChildStepsClose = () => {
  showChildStepsDialog.value = false
  selectedStepName.value = ''
  childStepsData.value = []
}

// 监听器
watch([filterStatus, filterPriority], () => {
  currentPage.value = 1
  loadSteps()
})

watch(viewMode, () => {
  loadSteps()
})

// 生命周期
onMounted(() => {
  refreshSteps()
})

// 暴露方法给父组件
defineExpose({
  refreshSteps,
  loadSteps,
  loadStatistics
})
</script>

<style scoped>
.development-steps-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.steps-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.steps-statistics {
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
}

.steps-content {
  flex: 1;
  padding: 16px 0;
  overflow: hidden;
}

.list-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-view {
  height: 100%;
  overflow: auto;
}

.kanban-view {
  height: 100%;
}

.step-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-name .name {
  font-weight: 500;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: center;
}

.tree-stats {
  margin-top: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tree-node:hover {
  background-color: var(--el-fill-color-light);
}

.step-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.step-icon {
  font-size: 16px;
}

.step-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .step-actions {
  opacity: 1;
}

.template-sequences {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .sequence-tag {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

/* 步骤模板序列对话框样式 */
.step-template-sequences-content {
  .step-info-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .step-basic-info {
      .el-descriptions {
        --el-descriptions-item-bordered-label-background: #fafafa;
      }
    }
  }

  .sequences-section {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .sequences-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 16px;
      max-height: 400px;
      overflow-y: auto;

      .sequence-card {
        .sequence-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .sequence-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .sequence-name {
              font-weight: 600;
              color: #303133;
            }
          }

          .sequence-meta {
            display: flex;
            gap: 8px;
          }
        }

        .sequence-content {
          .sequence-description {
            margin-bottom: 12px;

            p {
              margin: 0;
              color: #606266;
              line-height: 1.5;
            }
          }

          .sequence-info {
            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 4px 0;

              .label {
                color: #909399;
                font-size: 14px;
              }

              .value {
                color: #606266;
                font-size: 14px;
              }
            }
          }
        }

        .sequence-actions {
          display: flex;
          gap: 8px;
          justify-content: flex-end;
        }
      }
    }
  }
}

/* 序列详情对话框样式 */
.sequence-detail-content {
  .sequence-basic-info {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    h5 {
      margin: 16px 0 8px 0;
      color: #606266;
      font-size: 14px;
      font-weight: 600;
    }

    .sequence-description p,
    .sequence-notes p {
      margin: 0;
      color: #606266;
      line-height: 1.6;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
    }

    .sequence-tags {
      .tag-item {
        margin-right: 8px;
        margin-bottom: 4px;
      }
    }
  }

  .sequence-steps-section {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .steps-timeline {
      max-height: 500px;
      overflow-y: auto;

      .step-timeline-item {
        display: flex;
        margin-bottom: 20px;

        &.inactive {
          opacity: 0.6;
        }

        .step-timeline-marker {
          margin-right: 16px;

          .step-number {
            width: 32px;
            height: 32px;
            background: #409eff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
          }
        }

        .step-timeline-content {
          flex: 1;
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          border-left: 3px solid #409eff;

          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .step-action-type {
              display: flex;
              gap: 8px;
            }

            .step-meta {
              display: flex;
              gap: 12px;
              font-size: 12px;
              color: #909399;

              span {
                background: #e8e8e8;
                padding: 2px 6px;
                border-radius: 3px;
              }
            }
          }

          .step-description {
            color: #303133;
            font-weight: 500;
            margin-bottom: 12px;
            line-height: 1.5;
          }

          .step-parameters {
            margin-top: 12px;

            h6 {
              margin: 0 0 8px 0;
              color: #606266;
              font-size: 13px;
              font-weight: 600;
            }

            code {
              background: #f1f1f1;
              padding: 2px 4px;
              border-radius: 3px;
              font-size: 12px;
              color: #e6a23c;
              word-break: break-all;
            }
          }

          .step-flow-control {
            margin-top: 12px;

            h6 {
              margin: 0 0 8px 0;
              color: #606266;
              font-size: 13px;
              font-weight: 600;
            }

            .flow-control-items {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 8px;

              .flow-item {
                display: flex;
                align-items: center;
                gap: 8px;

                .label {
                  font-size: 12px;
                  color: #909399;
                  white-space: nowrap;
                }

                code {
                  background: #f1f1f1;
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-size: 12px;
                  color: #e6a23c;
                }
              }
            }
          }
        }
      }
    }

    .no-steps {
      text-align: center;
      padding: 40px 0;
    }
  }
}

.child-steps-cell {
  cursor: pointer;
  transition: all 0.3s;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  min-width: 20px;
  text-align: center;

  &:hover {
    transform: scale(1.1);
  }

  &.has-children {
    background-color: #e1f3ff;
    color: #409eff;
    font-weight: 600;
    border: 1px solid #b3d8ff;
  }
}

.step-name {
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}
</style>
