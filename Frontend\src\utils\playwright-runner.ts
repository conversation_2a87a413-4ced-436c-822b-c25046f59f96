/**
 * Playwright测试运行器
 * 用于在前端界面中执行Playwright测试
 */

export interface PlaywrightTestConfig {
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  timeout: number;
  baseURL: string;
  browsers: string[];
  device: string;
  userAgent: string;
  parallel: boolean;
  workers: number;
  retries: number;
  globalTimeout: number;
  reporters: string[];
  screenshot: string;
  video: string;
  trace: string;
  ignoreHTTPSErrors: boolean;
  javaScriptEnabled: boolean;
  waitForNetworkIdle: boolean;
  launchOptions: string;
}

export interface TestResult {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped' | 'running';
  duration: number;
  error?: string;
  screenshot?: string;
  video?: string;
  trace?: string;
}

export interface TestSuite {
  id: string;
  name: string;
  tests: TestResult[];
  status: 'passed' | 'failed' | 'running' | 'pending';
  duration: number;
  passedCount: number;
  failedCount: number;
  skippedCount: number;
}

export class PlaywrightRunner {
  private config: PlaywrightTestConfig;
  private isRunning: boolean = false;
  private currentExecution: string | null = null;

  constructor(config: PlaywrightTestConfig) {
    this.config = config;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<PlaywrightTestConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 生成Playwright配置文件内容
   */
  generateConfigFile(): string {
    const configContent = `
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/playwright',
  fullyParallel: ${this.config.parallel},
  retries: ${this.config.retries},
  workers: ${this.config.workers},
  timeout: ${this.config.timeout},
  globalTimeout: ${this.config.globalTimeout},
  
  reporter: [
    ${this.config.reporters.map(reporter => {
      switch (reporter) {
        case 'html':
          return "['html', { outputFolder: 'playwright-report' }]";
        case 'json':
          return "['json', { outputFile: 'test-results/results.json' }]";
        case 'junit':
          return "['junit', { outputFile: 'test-results/junit.xml' }]";
        case 'line':
          return "['line']";
        default:
          return `['${reporter}']`;
      }
    }).join(',\n    ')}
  ],
  
  use: {
    baseURL: '${this.config.baseURL}',
    headless: ${this.config.headless},
    viewport: { width: ${this.config.viewport.width}, height: ${this.config.viewport.height} },
    ignoreHTTPSErrors: ${this.config.ignoreHTTPSErrors},
    javaScriptEnabled: ${this.config.javaScriptEnabled},
    screenshot: '${this.config.screenshot}',
    video: '${this.config.video}',
    trace: '${this.config.trace}',
    actionTimeout: ${this.config.timeout},
    navigationTimeout: ${this.config.timeout},
    ${this.config.userAgent ? `userAgent: '${this.config.userAgent}',` : ''}
    ${this.config.launchOptions ? `launchOptions: { args: [${this.config.launchOptions.split(' ').map(arg => `'${arg}'`).join(', ')}] },` : ''}
  },

  projects: [
    ${this.config.browsers.map(browser => {
      if (this.config.device) {
        return `{
          name: '${browser}',
          use: { ...devices['${this.config.device}'] },
        }`;
      } else {
        const deviceMap: Record<string, string> = {
          chromium: 'Desktop Chrome',
          firefox: 'Desktop Firefox',
          webkit: 'Desktop Safari'
        };
        return `{
          name: '${browser}',
          use: { ...devices['${deviceMap[browser] || 'Desktop Chrome'}'] },
        }`;
      }
    }).join(',\n    ')}
  ],
});`;

    return configContent;
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.config.viewport.width < 320 || this.config.viewport.height < 240) {
      errors.push('视窗大小过小');
    }

    if (this.config.timeout < 1000) {
      errors.push('超时时间不能小于1秒');
    }

    if (this.config.browsers.length === 0) {
      errors.push('至少选择一个浏览器');
    }

    if (this.config.parallel && this.config.workers < 1) {
      errors.push('并行模式下工作进程数不能小于1');
    }

    if (this.config.reporters.length === 0) {
      errors.push('至少选择一种报告格式');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 运行测试
   */
  async runTests(testFiles?: string[]): Promise<TestSuite[]> {
    if (this.isRunning) {
      throw new Error('测试正在运行中');
    }

    const validation = this.validateConfig();
    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
    }

    this.isRunning = true;
    this.currentExecution = Date.now().toString();

    try {
      // 这里应该调用实际的Playwright测试执行
      // 由于在浏览器环境中无法直接执行Node.js命令，
      // 需要通过API调用后端服务来执行测试
      
      const response = await fetch('/api/testing/playwright/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: this.config,
          testFiles: testFiles || [],
          executionId: this.currentExecution
        })
      });

      if (!response.ok) {
        throw new Error(`测试执行失败: ${response.statusText}`);
      }

      const results = await response.json();
      return results.testSuites || [];

    } finally {
      this.isRunning = false;
      this.currentExecution = null;
    }
  }

  /**
   * 停止测试
   */
  async stopTests(): Promise<void> {
    if (!this.isRunning || !this.currentExecution) {
      return;
    }

    await fetch('/api/testing/playwright/stop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        executionId: this.currentExecution
      })
    });

    this.isRunning = false;
    this.currentExecution = null;
  }

  /**
   * 获取测试状态
   */
  getStatus(): { isRunning: boolean; executionId: string | null } {
    return {
      isRunning: this.isRunning,
      executionId: this.currentExecution
    };
  }

  /**
   * 获取测试报告
   */
  async getTestReport(executionId: string): Promise<any> {
    const response = await fetch(`/api/testing/playwright/report/${executionId}`);
    
    if (!response.ok) {
      throw new Error(`获取报告失败: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 获取可用的测试文件列表
   */
  async getTestFiles(): Promise<string[]> {
    const response = await fetch('/api/testing/playwright/files');
    
    if (!response.ok) {
      throw new Error(`获取测试文件失败: ${response.statusText}`);
    }

    const data = await response.json();
    return data.files || [];
  }

  /**
   * 创建测试模板
   */
  generateTestTemplate(type: 'basic' | 'login' | 'api'): string {
    const templates = {
      basic: `import { test, expect } from '@playwright/test';

test.describe('基础测试', () => {
  test('页面加载测试', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('h1')).toBeVisible();
  });
});`,

      login: `import { test, expect } from '../fixtures/base-fixtures';

test.describe('登录功能测试', () => {
  test('用户登录', async ({ page, helpers }) => {
    await page.goto('/login');
    await helpers.login('admin', 'admin123');
    await expect(page).toHaveURL('/dashboard');
  });
});`,

      api: `import { test, expect } from '@playwright/test';

test.describe('API测试', () => {
  test('获取用户信息', async ({ request }) => {
    const response = await request.get('/api/users');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(Array.isArray(data)).toBeTruthy();
  });
});`
    };

    return templates[type] || templates.basic;
  }
}
