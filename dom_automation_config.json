{"connection": {"debug_port": 9222, "connection_timeout": 10, "preferred_method": "selenium", "retry_attempts": 3, "retry_delay": 2}, "automation": {"action_delay": 0.5, "wait_timeout": 30, "message_poll_interval": 1, "screenshot_on_error": true}, "selectors": {"copilot_input": ["[data-testid=\"chat-input\"]", ".copilot-chat-input", "textarea[placeholder*=\"Ask Copilot\"]", "textarea[placeholder*=\"Type a message\"]", ".monaco-inputbox textarea"], "copilot_send_button": ["[data-testid=\"send-button\"]", ".copilot-send-button", "button[title*=\"Send\"]", "button[aria-label*=\"Send\"]"], "copilot_messages": [".copilot-message", ".chat-message", "[data-testid=\"chat-message\"]", ".monaco-list-row"], "file_input": ["input[type=\"file\"]", "[data-testid=\"file-input\"]", ".file-upload-input"], "monaco_editor": [".monaco-editor textarea", ".monaco-editor .view-lines"]}, "chrome_options": {"no_sandbox": true, "disable_dev_shm_usage": true, "disable_gpu": false, "headless": false, "window_size": "1920,1080"}, "logging": {"level": "INFO", "log_file": "dom_automation.log", "max_log_size": "10MB", "backup_count": 5}}