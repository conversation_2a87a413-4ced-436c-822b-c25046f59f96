using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.API.DTOs;
using System.Security.Claims;
using System.Text;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 原型图管理控制器
/// </summary>
[ApiController]
[Route("api/prototypes")]
[Authorize]
public class PrototypesController : ControllerBase
{
    private readonly IRepository<Prototype> _prototypeRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<RequirementDocument> _requirementRepository;
    private readonly ILogger<PrototypesController> _logger;

    public PrototypesController(
        IRepository<Prototype> prototypeRepository,
        IRepository<Project> projectRepository,
        IRepository<RequirementDocument> requirementRepository,
        ILogger<PrototypesController> logger)
    {
        _prototypeRepository = prototypeRepository;
        _projectRepository = projectRepository;
        _requirementRepository = requirementRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取原型图详情
    /// </summary>
    /// <param name="id">原型图ID</param>
    /// <returns>原型图详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<PrototypeDto>> GetPrototype(int id)
    {
        try
        {
            var prototype = await _prototypeRepository.GetByIdAsync(id);
            if (prototype == null)
            {
                return NotFound(new { message = "原型图不存在" });
            }

            // 获取关联的项目信息
            var project = await _projectRepository.GetByIdAsync(prototype.ProjectId);
            
            // 获取关联的需求文档信息
            RequirementDocument? requirement = null;
            if (prototype.RequirementDocumentId.HasValue)
            {
                requirement = await _requirementRepository.GetByIdAsync(prototype.RequirementDocumentId.Value);
            }

            var result = new PrototypeDto
            {
                Id = prototype.Id,
                ProjectId = prototype.ProjectId,
                RequirementDocumentId = prototype.RequirementDocumentId,
                PrototypeName = prototype.PrototypeName,
                PrototypeType = prototype.PrototypeType,
                MermaidDefinition = prototype.MermaidDefinition,
                Description = prototype.Description,
                TargetUsers = prototype.TargetUsers,
                PageModules = prototype.PageModules,
                InteractionFlows = prototype.InteractionFlows,
                UIComponents = prototype.UIComponents,
                PrototypeVersion = prototype.PrototypeVersion,
                DeviceType = prototype.DeviceType,
                FidelityLevel = prototype.FidelityLevel,
                CreatedTime = prototype.CreatedTime,
                UpdatedTime = prototype.UpdatedTime,
                ProjectName = project?.Name,
                RequirementDocumentTitle = requirement?.Title
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取原型图详情失败，ID: {Id}", id);
            return StatusCode(500, new { message = "获取原型图详情失败" });
        }
    }

    /// <summary>
    /// 获取项目的原型图列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="prototypeType">原型图类型</param>
    /// <param name="deviceType">设备类型</param>
    /// <param name="searchKeyword">搜索关键词</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>原型图列表</returns>
    [HttpGet]
    public async Task<ActionResult<object>> GetPrototypes(
        [FromQuery] int? projectId = null,
        [FromQuery] string? prototypeType = null,
        [FromQuery] string? deviceType = null,
        [FromQuery] string? searchKeyword = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            IEnumerable<Prototype> query = await _prototypeRepository.GetAllAsync();

            // 按项目筛选
            if (projectId.HasValue)
            {
                query = query.Where(p => p.ProjectId == projectId.Value);
            }

            // 按原型图类型筛选
            if (!string.IsNullOrEmpty(prototypeType))
            {
                query = query.Where(p => p.PrototypeType == prototypeType);
            }

            // 按设备类型筛选
            if (!string.IsNullOrEmpty(deviceType))
            {
                query = query.Where(p => p.DeviceType == deviceType);
            }

            // 搜索关键词
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                query = query.Where(p => 
                    p.PrototypeName.Contains(searchKeyword) ||
                    (p.Description != null && p.Description.Contains(searchKeyword)));
            }

            // 排序
            query = query.OrderByDescending(p => p.CreatedTime);

            // 分页
            var totalCount = query.Count();
            var prototypes = query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            // 获取项目信息
            var projectIds = prototypes.Select(p => p.ProjectId).Distinct().ToList();
            var projects = await _projectRepository.GetAllAsync();
            var projectDict = projects.Where(p => projectIds.Contains(p.Id))
                .ToDictionary(p => p.Id, p => p.Name);

            // 获取需求文档信息
            var requirementIds = prototypes
                .Where(p => p.RequirementDocumentId.HasValue)
                .Select(p => p.RequirementDocumentId!.Value)
                .Distinct()
                .ToList();
            var requirements = await _requirementRepository.GetAllAsync();
            var requirementDict = requirements.Where(r => requirementIds.Contains(r.Id))
                .ToDictionary(r => r.Id, r => r.Title);

            var result = prototypes.Select(p => new PrototypeDto
            {
                Id = p.Id,
                ProjectId = p.ProjectId,
                RequirementDocumentId = p.RequirementDocumentId,
                PrototypeName = p.PrototypeName,
                PrototypeType = p.PrototypeType,
                MermaidDefinition = p.MermaidDefinition,
                Description = p.Description,
                TargetUsers = p.TargetUsers,
                PageModules = p.PageModules,
                InteractionFlows = p.InteractionFlows,
                UIComponents = p.UIComponents,
                PrototypeVersion = p.PrototypeVersion,
                DeviceType = p.DeviceType,
                FidelityLevel = p.FidelityLevel,
                CreatedTime = p.CreatedTime,
                UpdatedTime = p.UpdatedTime,
                ProjectName = projectDict.GetValueOrDefault(p.ProjectId),
                RequirementDocumentTitle = p.RequirementDocumentId.HasValue 
                    ? requirementDict.GetValueOrDefault(p.RequirementDocumentId.Value) 
                    : null
            }).ToList();

            return Ok(new
            {
                data = result,
                totalCount,
                pageNumber,
                pageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取原型图列表失败");
            return StatusCode(500, new { message = "获取原型图列表失败" });
        }
    }

    /// <summary>
    /// 获取项目的原型图列表（简化版本）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>项目的原型图列表</returns>
    [HttpGet("project/{projectId}")]
    public async Task<ActionResult<List<PrototypeDto>>> GetPrototypesByProject(int projectId)
    {
        try
        {
            // 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            var prototypes = await _prototypeRepository.GetAllAsync();
            var projectPrototypes = prototypes
                .Where(p => p.ProjectId == projectId)
                .OrderByDescending(p => p.CreatedTime)
                .ToList();

            // 获取需求文档信息
            var requirementIds = projectPrototypes
                .Where(p => p.RequirementDocumentId.HasValue)
                .Select(p => p.RequirementDocumentId!.Value)
                .Distinct()
                .ToList();
            var requirements = await _requirementRepository.GetAllAsync();
            var requirementDict = requirements.Where(r => requirementIds.Contains(r.Id))
                .ToDictionary(r => r.Id, r => r.Title);

            var result = projectPrototypes.Select(p => new PrototypeDto
            {
                Id = p.Id,
                ProjectId = p.ProjectId,
                RequirementDocumentId = p.RequirementDocumentId,
                PrototypeName = p.PrototypeName,
                PrototypeType = p.PrototypeType,
                MermaidDefinition = p.MermaidDefinition,
                Description = p.Description,
                TargetUsers = p.TargetUsers,
                PageModules = p.PageModules,
                InteractionFlows = p.InteractionFlows,
                UIComponents = p.UIComponents,
                PrototypeVersion = p.PrototypeVersion,
                DeviceType = p.DeviceType,
                FidelityLevel = p.FidelityLevel,
                CreatedTime = p.CreatedTime,
                UpdatedTime = p.UpdatedTime,
                ProjectName = project.Name,
                RequirementDocumentTitle = p.RequirementDocumentId.HasValue
                    ? requirementDict.GetValueOrDefault(p.RequirementDocumentId.Value)
                    : null
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目原型图列表失败，项目ID: {ProjectId}", projectId);
            return StatusCode(500, new { message = "获取项目原型图列表失败" });
        }
    }

    /// <summary>
    /// 创建原型图
    /// </summary>
    /// <param name="createDto">创建原型图请求</param>
    /// <returns>创建的原型图</returns>
    [HttpPost]
    public async Task<ActionResult<PrototypeDto>> CreatePrototype([FromBody] CreatePrototypeDto createDto)
    {
        try
        {
            // 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(createDto.ProjectId);
            if (project == null)
            {
                return BadRequest(new { message = "项目不存在" });
            }

            // 验证需求文档是否存在（如果提供了）
            if (createDto.RequirementDocumentId.HasValue)
            {
                var requirement = await _requirementRepository.GetByIdAsync(createDto.RequirementDocumentId.Value);
                if (requirement == null)
                {
                    return BadRequest(new { message = "需求文档不存在" });
                }
            }

            var userId = GetCurrentUserId() ?? 1; // 默认用户ID为1

            var prototype = new Prototype
            {
                ProjectId = createDto.ProjectId,
                RequirementDocumentId = createDto.RequirementDocumentId,
                PrototypeName = createDto.PrototypeName,
                PrototypeType = createDto.PrototypeType,
                MermaidDefinition = createDto.MermaidDefinition,
                Description = createDto.Description,
                TargetUsers = createDto.TargetUsers,
                PageModules = createDto.PageModules,
                InteractionFlows = createDto.InteractionFlows,
                UIComponents = createDto.UIComponents,
                DeviceType = createDto.DeviceType,
                FidelityLevel = createDto.FidelityLevel,
                CreatedBy = userId,
                CreatedTime = DateTime.Now
            };

            var createdPrototype = await _prototypeRepository.AddAsync(prototype);

            var result = new PrototypeDto
            {
                Id = createdPrototype.Id,
                ProjectId = createdPrototype.ProjectId,
                RequirementDocumentId = createdPrototype.RequirementDocumentId,
                PrototypeName = createdPrototype.PrototypeName,
                PrototypeType = createdPrototype.PrototypeType,
                MermaidDefinition = createdPrototype.MermaidDefinition,
                Description = createdPrototype.Description,
                TargetUsers = createdPrototype.TargetUsers,
                PageModules = createdPrototype.PageModules,
                InteractionFlows = createdPrototype.InteractionFlows,
                UIComponents = createdPrototype.UIComponents,
                PrototypeVersion = createdPrototype.PrototypeVersion,
                DeviceType = createdPrototype.DeviceType,
                FidelityLevel = createdPrototype.FidelityLevel,
                CreatedTime = createdPrototype.CreatedTime,
                UpdatedTime = createdPrototype.UpdatedTime,
                ProjectName = project.Name
            };

            _logger.LogInformation("原型图创建成功，ID: {Id}, 名称: {Name}", createdPrototype.Id, createdPrototype.PrototypeName);
            return CreatedAtAction(nameof(GetPrototype), new { id = createdPrototype.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建原型图失败");
            return StatusCode(500, new { message = "创建原型图失败" });
        }
    }

    /// <summary>
    /// 更新原型图
    /// </summary>
    /// <param name="id">原型图ID</param>
    /// <param name="updateDto">更新原型图请求</param>
    /// <returns>更新后的原型图</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<PrototypeDto>> UpdatePrototype(int id, [FromBody] UpdatePrototypeDto updateDto)
    {
        try
        {
            var prototype = await _prototypeRepository.GetByIdAsync(id);
            if (prototype == null)
            {
                return NotFound(new { message = "原型图不存在" });
            }

            var userId = GetCurrentUserId() ?? 1; // 默认用户ID为1

            // 更新字段
            if (!string.IsNullOrEmpty(updateDto.PrototypeName))
                prototype.PrototypeName = updateDto.PrototypeName;

            if (!string.IsNullOrEmpty(updateDto.PrototypeType))
                prototype.PrototypeType = updateDto.PrototypeType;

            if (!string.IsNullOrEmpty(updateDto.MermaidDefinition))
                prototype.MermaidDefinition = updateDto.MermaidDefinition;

            if (updateDto.Description != null)
                prototype.Description = updateDto.Description;

            if (updateDto.TargetUsers != null)
                prototype.TargetUsers = updateDto.TargetUsers;

            if (updateDto.PageModules != null)
                prototype.PageModules = updateDto.PageModules;

            if (updateDto.InteractionFlows != null)
                prototype.InteractionFlows = updateDto.InteractionFlows;

            if (updateDto.UIComponents != null)
                prototype.UIComponents = updateDto.UIComponents;

            if (!string.IsNullOrEmpty(updateDto.DeviceType))
                prototype.DeviceType = updateDto.DeviceType;

            if (!string.IsNullOrEmpty(updateDto.FidelityLevel))
                prototype.FidelityLevel = updateDto.FidelityLevel;

            prototype.UpdatedBy = userId;
            prototype.UpdatedTime = DateTime.Now;

            var updateResult = await _prototypeRepository.UpdateAsync(prototype);
            if (!updateResult)
            {
                return BadRequest(new { message = "更新原型图失败" });
            }

            // 获取项目信息
            var project = await _projectRepository.GetByIdAsync(prototype.ProjectId);

            var result = new PrototypeDto
            {
                Id = prototype.Id,
                ProjectId = prototype.ProjectId,
                RequirementDocumentId = prototype.RequirementDocumentId,
                PrototypeName = prototype.PrototypeName,
                PrototypeType = prototype.PrototypeType,
                MermaidDefinition = prototype.MermaidDefinition,
                Description = prototype.Description,
                TargetUsers = prototype.TargetUsers,
                PageModules = prototype.PageModules,
                InteractionFlows = prototype.InteractionFlows,
                UIComponents = prototype.UIComponents,
                PrototypeVersion = prototype.PrototypeVersion,
                DeviceType = prototype.DeviceType,
                FidelityLevel = prototype.FidelityLevel,
                CreatedTime = prototype.CreatedTime,
                UpdatedTime = prototype.UpdatedTime,
                ProjectName = project?.Name
            };

            _logger.LogInformation("原型图更新成功，ID: {Id}", id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新原型图失败，ID: {Id}", id);
            return StatusCode(500, new { message = "更新原型图失败" });
        }
    }

    /// <summary>
    /// 删除原型图
    /// </summary>
    /// <param name="id">原型图ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeletePrototype(int id)
    {
        try
        {
            var prototype = await _prototypeRepository.GetByIdAsync(id);
            if (prototype == null)
            {
                return NotFound(new { message = "原型图不存在" });
            }

            var userId = GetCurrentUserId() ?? 1; // 默认用户ID为1

            // 软删除：直接删除记录（如果需要软删除，需要在实体中添加相应字段）
            await _prototypeRepository.DeleteAsync(prototype);

            _logger.LogInformation("原型图删除成功，ID: {Id}", id);
            return Ok(new { message = "原型图删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除原型图失败，ID: {Id}", id);
            return StatusCode(500, new { message = "删除原型图失败" });
        }
    }

    /// <summary>
    /// 复制原型图
    /// </summary>
    /// <param name="id">原型图ID</param>
    /// <returns>复制的原型图</returns>
    [HttpPost("{id}/copy")]
    public async Task<ActionResult<PrototypeDto>> CopyPrototype(int id)
    {
        try
        {
            var originalPrototype = await _prototypeRepository.GetByIdAsync(id);
            if (originalPrototype == null)
            {
                return NotFound(new { message = "原型图不存在" });
            }

            var userId = GetCurrentUserId() ?? 1; // 默认用户ID为1

            var copiedPrototype = new Prototype
            {
                ProjectId = originalPrototype.ProjectId,
                RequirementDocumentId = originalPrototype.RequirementDocumentId,
                PrototypeName = $"{originalPrototype.PrototypeName} - 副本",
                PrototypeType = originalPrototype.PrototypeType,
                MermaidDefinition = originalPrototype.MermaidDefinition,
                Description = originalPrototype.Description,
                TargetUsers = originalPrototype.TargetUsers,
                PageModules = originalPrototype.PageModules,
                InteractionFlows = originalPrototype.InteractionFlows,
                UIComponents = originalPrototype.UIComponents,
                DeviceType = originalPrototype.DeviceType,
                FidelityLevel = originalPrototype.FidelityLevel,
                CreatedBy = userId,
                CreatedTime = DateTime.Now
            };

            var createdPrototype = await _prototypeRepository.AddAsync(copiedPrototype);

            // 获取项目信息
            var project = await _projectRepository.GetByIdAsync(createdPrototype.ProjectId);

            var result = new PrototypeDto
            {
                Id = createdPrototype.Id,
                ProjectId = createdPrototype.ProjectId,
                RequirementDocumentId = createdPrototype.RequirementDocumentId,
                PrototypeName = createdPrototype.PrototypeName,
                PrototypeType = createdPrototype.PrototypeType,
                MermaidDefinition = createdPrototype.MermaidDefinition,
                Description = createdPrototype.Description,
                TargetUsers = createdPrototype.TargetUsers,
                PageModules = createdPrototype.PageModules,
                InteractionFlows = createdPrototype.InteractionFlows,
                UIComponents = createdPrototype.UIComponents,
                PrototypeVersion = createdPrototype.PrototypeVersion,
                DeviceType = createdPrototype.DeviceType,
                FidelityLevel = createdPrototype.FidelityLevel,
                CreatedTime = createdPrototype.CreatedTime,
                UpdatedTime = createdPrototype.UpdatedTime,
                ProjectName = project?.Name
            };

            _logger.LogInformation("原型图复制成功，原始ID: {OriginalId}, 新ID: {NewId}", id, createdPrototype.Id);
            return CreatedAtAction(nameof(GetPrototype), new { id = createdPrototype.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制原型图失败，ID: {Id}", id);
            return StatusCode(500, new { message = "复制原型图失败" });
        }
    }

    /// <summary>
    /// 验证Mermaid语法
    /// </summary>
    /// <param name="request">验证请求</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate-mermaid")]
    public ActionResult<object> ValidateMermaid([FromBody] ValidateMermaidRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MermaidDefinition))
            {
                return BadRequest(new { valid = false, error = "Mermaid定义不能为空" });
            }

            // 基本语法检查
            var definition = request.MermaidDefinition.Trim();

            // 检查是否包含基本的Mermaid语法
            if (!definition.StartsWith("flowchart") && !definition.StartsWith("graph"))
            {
                return Ok(new { valid = false, error = "Mermaid定义必须以 'flowchart' 或 'graph' 开头" });
            }

            // 检查是否包含节点定义
            if (!definition.Contains("[") && !definition.Contains("("))
            {
                return Ok(new { valid = false, error = "Mermaid定义必须包含节点定义" });
            }

            // 简单的语法验证通过
            return Ok(new { valid = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证Mermaid语法失败");
            return Ok(new { valid = false, error = "语法验证失败" });
        }
    }

    /// <summary>
    /// 导出原型图
    /// </summary>
    /// <param name="id">原型图ID</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出文件</returns>
    [HttpGet("{id}/export")]
    public async Task<ActionResult> ExportPrototype(int id, [FromQuery] string format = "svg")
    {
        try
        {
            var prototype = await _prototypeRepository.GetByIdAsync(id);
            if (prototype == null)
            {
                return NotFound(new { message = "原型图不存在" });
            }

            if (string.IsNullOrEmpty(prototype.MermaidDefinition))
            {
                return BadRequest(new { message = "原型图内容为空，无法导出" });
            }

            // 这里应该调用Mermaid渲染服务来生成图片
            // 暂时返回Mermaid定义文本
            var content = prototype.MermaidDefinition;
            var fileName = $"{prototype.PrototypeName}.{format}";

            if (format.ToLower() == "svg")
            {
                // 返回SVG格式（这里应该是渲染后的SVG，暂时返回文本）
                return File(System.Text.Encoding.UTF8.GetBytes(content), "image/svg+xml", fileName);
            }
            else
            {
                // 其他格式暂时返回文本
                return File(System.Text.Encoding.UTF8.GetBytes(content), "text/plain", $"{prototype.PrototypeName}.txt");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出原型图失败，ID: {Id}", id);
            return StatusCode(500, new { message = "导出原型图失败" });
        }
    }

    /// <summary>
    /// 获取原型图模板
    /// </summary>
    /// <param name="prototypeType">原型图类型</param>
    /// <returns>模板列表</returns>
    [HttpGet("templates")]
    public ActionResult<object> GetPrototypeTemplates([FromQuery] string? prototypeType = null)
    {
        try
        {
            var templates = new List<object>();

            // 线框图模板
            if (string.IsNullOrEmpty(prototypeType) || prototypeType == "Wireframe")
            {
                templates.Add(new
                {
                    id = "wireframe-login",
                    name = "登录页面线框图",
                    type = "Wireframe",
                    description = "标准的用户登录页面布局",
                    mermaidDefinition = @"flowchart TD
    A[页面头部] --> B[网站Logo]
    A --> C[导航菜单]
    B --> D[登录表单区域]
    C --> D
    D --> E[用户名输入框]
    D --> F[密码输入框]
    D --> G[登录按钮]
    D --> H[忘记密码链接]
    G --> I[页面底部]
    H --> I"
                });
            }

            // 用户流程图模板
            if (string.IsNullOrEmpty(prototypeType) || prototypeType == "UserFlow")
            {
                templates.Add(new
                {
                    id = "userflow-registration",
                    name = "用户注册流程",
                    type = "UserFlow",
                    description = "标准的用户注册流程图",
                    mermaidDefinition = @"flowchart TD
    Start([开始注册]) --> A[填写注册信息]
    A --> B{验证信息}
    B -->|通过| C[发送验证邮件]
    B -->|失败| D[显示错误]
    D --> A
    C --> E[用户验证邮件]
    E --> End([注册完成])"
                });
            }

            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取原型图模板失败");
            return StatusCode(500, new { message = "获取原型图模板失败" });
        }
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
