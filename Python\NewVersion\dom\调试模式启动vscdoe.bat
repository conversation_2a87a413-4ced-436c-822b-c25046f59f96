@echo off
chcp 65001 >nul
echo 关闭现有的VSCode进程...
taskkill /f /im Code.exe 2>nul

echo 等待进程完全关闭...
timeout /t 3 /nobreak >nul

echo 启动VSCode（调试模式）...
REM 使用指定的VSCode安装路径
set "VSCODE_PATH=D:\Microsoft VS Code\Code.exe"

if not exist "%VSCODE_PATH%" (
    echo 错误：VSCode未找到在路径: %VSCODE_PATH%
    echo 请检查VSCode是否正确安装
    pause
    exit /b 1
)

echo 使用VSCode路径: %VSCODE_PATH%
"%VSCODE_PATH%" --remote-debugging-port=9222 --disable-web-security --remote-allow-origins=* "%~dp0.."

echo 等待VSCode启动...
timeout /t 5 /nobreak >nul

echo 检查调试端口9222...
curl -s http://localhost:9222/json >nul 2>&1
if errorlevel 1 (
    echo 调试端口可能未启动，请等待VSCode完全加载
) else (
    echo 调试端口9222已启用
)

echo VSCode调试模式启动完成！
pause