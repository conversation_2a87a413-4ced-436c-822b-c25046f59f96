namespace ProjectManagement.Core.DTOs;

/// <summary>
/// AI任务响应DTO
/// </summary>
public class AITaskResponseDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 预估完成时间
    /// </summary>
    public TimeSpan? EstimatedDuration { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 对话ID
    /// </summary>
    public string? ConversationId { get; set; }
}

/// <summary>
/// AI任务状态DTO
/// </summary>
public class AITaskStatusDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public int Progress { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 预估完成时间
    /// </summary>
    public DateTime? EstimatedCompletion { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? Error { get; set; }

    /// <summary>
    /// 任务结果
    /// </summary>
    public object? Result { get; set; }
}

/// <summary>
/// AI任务结果DTO
/// </summary>
public class AITaskResultDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 任务结果
    /// </summary>
    public object? Result { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? Error { get; set; }
}

/// <summary>
/// 需求分析请求DTO
/// </summary>
public class RequirementAnalysisRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求描述
    /// </summary>
    public string RequirementText { get; set; } = string.Empty;

    /// <summary>
    /// 分析类型
    /// </summary>
    public string AnalysisType { get; set; } = "Comprehensive";

    /// <summary>
    /// AI提供商配置ID
    /// </summary>
    public int? AIProviderConfigId { get; set; }

    /// <summary>
    /// 附加参数
    /// </summary>
    public Dictionary<string, object>? Parameters { get; set; }
}

/// <summary>
/// ER图生成请求DTO
/// </summary>
public class ERDiagramGenerationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 数据库类型
    /// </summary>
    public string DatabaseType { get; set; } = "SqlServer";

    /// <summary>
    /// 图表格式
    /// </summary>
    public string DiagramFormat { get; set; } = "Mermaid";

    /// <summary>
    /// 首选AI模型
    /// </summary>
    public string? PreferredModel { get; set; }

    /// <summary>
    /// AI提供商配置ID
    /// </summary>
    public int? AIProviderConfigId { get; set; }
}

/// <summary>
/// Context图生成请求DTO
/// </summary>
public class ContextDiagramGenerationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 图表格式
    /// </summary>
    public string DiagramFormat { get; set; } = "Mermaid";

    /// <summary>
    /// 包含外部系统
    /// </summary>
    public bool IncludeExternalSystems { get; set; } = true;

    /// <summary>
    /// 首选AI模型
    /// </summary>
    public string? PreferredModel { get; set; }

    /// <summary>
    /// AI提供商配置ID
    /// </summary>
    public int? AIProviderConfigId { get; set; }
}

/// <summary>
/// 原型图生成请求DTO
/// </summary>
public class PrototypeGenerationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 原型图类型：Wireframe、UserFlow、ComponentDiagram、InteractionFlow
    /// </summary>
    public string PrototypeType { get; set; } = "Wireframe";

    /// <summary>
    /// 设备类型：Desktop、Mobile、Tablet、Responsive
    /// </summary>
    public string DeviceType { get; set; } = "Desktop";

    /// <summary>
    /// 保真度级别：Low、Medium、High
    /// </summary>
    public string FidelityLevel { get; set; } = "Low";

    /// <summary>
    /// 目标用户群体
    /// </summary>
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 需求描述
    /// </summary>
    public string Requirements { get; set; } = string.Empty;

    /// <summary>
    /// 图表格式
    /// </summary>
    public string DiagramFormat { get; set; } = "Mermaid";

    /// <summary>
    /// 首选AI模型
    /// </summary>
    public string? PreferredModel { get; set; }

    /// <summary>
    /// AI提供商配置ID
    /// </summary>
    public int? AIProviderConfigId { get; set; }
}

/// <summary>
/// 代码生成请求DTO
/// </summary>
public class CodeGenerationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 技术栈
    /// </summary>
    public string TechnologyStack { get; set; } = string.Empty;

    /// <summary>
    /// 生成类型
    /// </summary>
    public string GenerationType { get; set; } = "FullStack";

    /// <summary>
    /// 包含的组件
    /// </summary>
    public List<string> Components { get; set; } = new();

    /// <summary>
    /// 代码风格
    /// </summary>
    public string CodeStyle { get; set; } = "Standard";

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    public string? PreferredModel { get; set; }

    /// <summary>
    /// 附加配置
    /// </summary>
    public Dictionary<string, object>? Configuration { get; set; }
}

/// <summary>
/// 通用AI聊天请求DTO
/// </summary>
public class AIChatRequestDto
{
    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = "General";

    /// <summary>
    /// 项目ID（可选）
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// AI提供商配置ID（可选）
    /// </summary>
    public int? AIProviderConfigId { get; set; }

    /// <summary>
    /// Prompt模板ID（可选）
    /// </summary>
    public int? PromptTemplateId { get; set; }

    /// <summary>
    /// 附加元数据
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// 通用AI聊天响应DTO
/// </summary>
public class AIChatResponseDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 对话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 用户消息
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// AI回复
    /// </summary>
    public string AIResponse { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public string Timestamp { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 建议的后续操作
    /// </summary>
    public List<string>? SuggestedActions { get; set; }
}

/// <summary>
/// 测试生成请求DTO
/// </summary>
public class TestGenerationRequestDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 代码生成任务ID
    /// </summary>
    public int? CodeGenerationTaskId { get; set; }

    /// <summary>
    /// 测试类型
    /// </summary>
    public string TestType { get; set; } = "Unit";

    /// <summary>
    /// 测试框架
    /// </summary>
    public string TestFramework { get; set; } = "xUnit";

    /// <summary>
    /// 覆盖率目标
    /// </summary>
    public int CoverageTarget { get; set; } = 80;

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    public string? PreferredModel { get; set; }
}

/// <summary>
/// SQL注释生成请求DTO
/// </summary>
public class SQLCommentGenerationRequestDto
{
    /// <summary>
    /// SQL脚本内容
    /// </summary>
    public string SqlScript { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID（可选，用于上下文）
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// 数据库类型（可选，用于优化注释）
    /// </summary>
    public string? DatabaseType { get; set; }

    /// <summary>
    /// 业务领域（可选，用于更准确的注释）
    /// </summary>
    public string? BusinessDomain { get; set; }
}

/// <summary>
/// AI模型信息DTO
/// </summary>
public class AIModelInfoDto
{
    /// <summary>
    /// 提供商
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Model { get; set; } = string.Empty;

    /// <summary>
    /// 模型描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// 支持的任务类型
    /// </summary>
    public List<string> SupportedTasks { get; set; } = new();

    /// <summary>
    /// 最大令牌数
    /// </summary>
    public int? MaxTokens { get; set; }

    /// <summary>
    /// 每千令牌成本
    /// </summary>
    public decimal? CostPerThousandTokens { get; set; }
}

/// <summary>
/// AI使用统计DTO
/// </summary>
public class AIUsageStatisticsDto
{
    /// <summary>
    /// 总请求数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 总令牌使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 预估成本
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsed { get; set; }

    /// <summary>
    /// 按模型分组的统计
    /// </summary>
    public Dictionary<string, ModelUsageDto> ModelUsage { get; set; } = new();
}

/// <summary>
/// 模型使用统计DTO
/// </summary>
public class ModelUsageDto
{
    /// <summary>
    /// 请求数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// 令牌使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }
}
