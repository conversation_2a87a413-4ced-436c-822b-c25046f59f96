-- ================================================
-- 迁移脚本: 019_AddExtendedPropertiesField
-- 描述: 为UIAutoMationTemplateSequences表添加ExtendedProperties、SourceCode和CodeLanguage字段
-- 创建时间: 2025-06-27
-- ================================================

USE [ProjectManagementAI]
GO

PRINT '开始执行迁移: 添加ExtendedProperties、SourceCode和CodeLanguage字段...'

-- 1. 为 UIAutoMationTemplateSequences 表添加 ExtendedProperties 字段
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
               AND COLUMN_NAME = 'ExtendedProperties')
BEGIN
    PRINT '添加 UIAutoMationTemplateSequences.ExtendedProperties 字段...'
    
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    ADD [ExtendedProperties] NVARCHAR(MAX) NULL;
    
    -- 添加字段注释
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'扩展属性（JSON格式）- 用于存储其他元数据，如编辑器设置等', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'ExtendedProperties';
    
    PRINT 'UIAutoMationTemplateSequences.ExtendedProperties 字段添加完成'
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSequences.ExtendedProperties 字段已存在，跳过添加'
END

-- 2. 为 UIAutoMationTemplateSequences 表添加 SourceCode 字段
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
               AND COLUMN_NAME = 'SourceCode')
BEGIN
    PRINT '添加 UIAutoMationTemplateSequences.SourceCode 字段...'
    
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    ADD [SourceCode] NVARCHAR(MAX) NULL;
    
    -- 添加字段注释
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'原生代码 - 用于保存用户在代码编辑器中编写的原始代码', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'SourceCode';
    
    PRINT 'UIAutoMationTemplateSequences.SourceCode 字段添加完成'
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSequences.SourceCode 字段已存在，跳过添加'
END

-- 3. 为 UIAutoMationTemplateSequences 表添加 CodeLanguage 字段
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
               AND COLUMN_NAME = 'CodeLanguage')
BEGIN
    PRINT '添加 UIAutoMationTemplateSequences.CodeLanguage 字段...'
    
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    ADD [CodeLanguage] NVARCHAR(50) NULL DEFAULT 'javascript';
    
    -- 添加字段注释
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'代码语言 - 支持的语言：javascript, python, typescript 等', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutoMationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'CodeLanguage';
    
    PRINT 'UIAutoMationTemplateSequences.CodeLanguage 字段添加完成'
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSequences.CodeLanguage 字段已存在，跳过添加'
END

-- 4. 验证字段添加成功
PRINT '验证字段添加结果...'

SELECT 
    'UIAutoMationTemplateSequences' AS TableName, 
    'ExtendedProperties' AS ColumnName,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
    AND COLUMN_NAME = 'ExtendedProperties'

UNION ALL

SELECT 
    'UIAutoMationTemplateSequences' AS TableName, 
    'SourceCode' AS ColumnName,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
    AND COLUMN_NAME = 'SourceCode'

UNION ALL

SELECT 
    'UIAutoMationTemplateSequences' AS TableName, 
    'CodeLanguage' AS ColumnName,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UIAutoMationTemplateSequences' 
    AND COLUMN_NAME = 'CodeLanguage';

-- 5. 显示迁移完成信息
PRINT '========================================='
PRINT '迁移 019_AddExtendedPropertiesField 执行完成!'
PRINT '已添加字段:'
PRINT '  - UIAutoMationTemplateSequences.ExtendedProperties (NVARCHAR(MAX))'
PRINT '  - UIAutoMationTemplateSequences.SourceCode (NVARCHAR(MAX))'
PRINT '  - UIAutoMationTemplateSequences.CodeLanguage (NVARCHAR(50))'
PRINT '========================================='

GO
