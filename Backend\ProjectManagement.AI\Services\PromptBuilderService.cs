using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt构建服务实现
/// </summary>
public class PromptBuilderService : IPromptBuilderService
{
    private readonly IPromptTemplateService _templateService;
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<User> _userRepository;
    private readonly IRepository<RequirementDocument> _requirementDocumentRepository;
    private readonly ILogger<PromptBuilderService> _logger;

    public PromptBuilderService(
        IPromptTemplateService templateService,
        IRepository<Project> projectRepository,
        IRepository<User> userRepository,
        IRepository<RequirementDocument> requirementDocumentRepository,
        ILogger<PromptBuilderService> logger)
    {
        _templateService = templateService;
        _projectRepository = projectRepository;
        _userRepository = userRepository;
        _requirementDocumentRepository = requirementDocumentRepository;
        _logger = logger;
    }

    /// <summary>
    /// 构建提示词
    /// </summary>
    public async Task<string> BuildPromptAsync(int templateId, Dictionary<string, object> parameters, int userId, int? projectId = null)
    {
        try
        {
            _logger.LogInformation("构建提示词，TemplateId: {TemplateId}, UserId: {UserId}", templateId, userId);

            var template = await _templateService.GetTemplateByIdAsync(templateId);
            if (template == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            // 确保参数不为null
            if (parameters == null)
                parameters = new Dictionary<string, object>();

            // 验证参数
            var (isValid, errors) = await ValidateParametersAsync(templateId, parameters);
            if (!isValid)
                throw new ArgumentException($"参数验证失败: {string.Join(", ", errors)}");

            // 获取上下文信息
            var requirementDocumentId = parameters.ContainsKey("requirementDocumentId")
                ? Convert.ToInt32(parameters["requirementDocumentId"])
                : (int?)null;
            var context = await BuildContextAsync(userId, projectId, requirementDocumentId);

            // 替换模板中的参数
            var prompt = await ReplaceParametersAsync(template.Content, parameters, context);

            _logger.LogInformation("提示词构建完成，长度: {Length}", prompt.Length);
            return prompt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "构建提示词失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 根据任务类型构建提示词
    /// </summary>
    public async Task<string> BuildPromptByTaskTypeAsync(string taskType, Dictionary<string, object> parameters, int userId, int? projectId = null)
    {
        try
        {
            _logger.LogInformation("根据任务类型构建提示词，TaskType: {TaskType}, UserId: {UserId}", taskType, userId);

            // 确保参数不为null
            if (parameters == null)
                parameters = new Dictionary<string, object>();

            // 获取用户偏好的模板
            var userTemplates = await _templateService.GetUserRecentTemplatesAsync(userId, 5);
            var preferredTemplate = userTemplates.FirstOrDefault(t => t.TaskType == taskType);

            // 如果没有用户偏好，使用默认模板
            if (preferredTemplate == null)
            {
                preferredTemplate = await _templateService.GetDefaultTemplateAsync(taskType);
            }

            if (preferredTemplate == null)
            {
                // 如果没有默认模板，获取该类型的第一个可用模板
                var availableTemplates = await _templateService.GetTemplatesByTaskTypeAsync(taskType);
                preferredTemplate = availableTemplates.FirstOrDefault();
            }

            if (preferredTemplate == null)
                throw new ArgumentException($"没有找到适用于任务类型 {taskType} 的模板");

            return await BuildPromptAsync(preferredTemplate.Id, parameters, userId, projectId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型构建提示词失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 验证模板参数
    /// </summary>
    public async Task<(bool IsValid, List<string> Errors)> ValidateParametersAsync(int templateId, Dictionary<string, object> parameters)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(templateId);
            if (template == null)
                return (false, new List<string> { "模板不存在" });

            var errors = new List<string>();

            // 如果参数为null，初始化为空字典
            if (parameters == null)
                parameters = new Dictionary<string, object>();

            if (string.IsNullOrEmpty(template.Parameters))
                return (true, errors); // 没有参数定义，认为验证通过

            // 尝试解析为数组格式（新格式）
            try
            {
                var parameterArray = JsonSerializer.Deserialize<JsonElement[]>(template.Parameters);
                if (parameterArray != null)
                {
                    foreach (var paramElement in parameterArray)
                    {
                        if (!paramElement.TryGetProperty("name", out var nameElement))
                            continue;

                        var paramName = nameElement.GetString();
                        if (string.IsNullOrEmpty(paramName))
                            continue;

                        var isRequired = paramElement.TryGetProperty("required", out var requiredElement) &&
                                       requiredElement.GetBoolean();

                        if (isRequired && !parameters.ContainsKey(paramName))
                        {
                            errors.Add($"缺少必需参数: {paramName}");
                        }

                        if (parameters.ContainsKey(paramName))
                        {
                            var value = parameters[paramName];
                            var expectedType = paramElement.TryGetProperty("type", out var typeElement)
                                ? typeElement.GetString() ?? "string"
                                : "string";

                            if (!ValidateParameterType(value, expectedType))
                            {
                                errors.Add($"参数 {paramName} 类型不匹配，期望: {expectedType}");
                            }
                        }
                    }
                    return (errors.Count == 0, errors);
                }
            }
            catch
            {
                // 如果数组格式解析失败，尝试对象格式（旧格式）
            }

            // 尝试解析为对象格式（旧格式）
            try
            {
                var parameterDefinitions = JsonSerializer.Deserialize<Dictionary<string, object>>(template.Parameters);
                if (parameterDefinitions == null)
                    return (true, errors);

                foreach (var paramDef in parameterDefinitions)
                {
                    var paramName = paramDef.Key;
                    var paramConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(paramDef.Value.ToString() ?? "{}");

                    if (paramConfig == null) continue;

                    var isRequired = paramConfig.ContainsKey("required") &&
                                   bool.TryParse(paramConfig["required"].ToString(), out var required) && required;

                    if (isRequired && !parameters.ContainsKey(paramName))
                    {
                        errors.Add($"缺少必需参数: {paramName}");
                    }

                    if (parameters.ContainsKey(paramName))
                    {
                        var value = parameters[paramName];
                        var expectedType = paramConfig.ContainsKey("type") ? paramConfig["type"].ToString() : "string";

                        if (!ValidateParameterType(value, expectedType))
                        {
                            errors.Add($"参数 {paramName} 类型不匹配，期望: {expectedType}");
                        }
                    }
                }
            }
            catch
            {
                // 如果两种格式都解析失败，跳过验证
                _logger.LogWarning("无法解析模板参数定义，跳过参数验证。TemplateId: {TemplateId}", templateId);
                return (true, errors);
            }

            return (errors.Count == 0, errors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证模板参数失败，TemplateId: {TemplateId}", templateId);
            return (false, new List<string> { "参数验证过程中发生错误" });
        }
    }

    /// <summary>
    /// 获取模板参数定义
    /// </summary>
    public async Task<Dictionary<string, object>?> GetTemplateParametersAsync(int templateId)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(templateId);
            if (template == null || string.IsNullOrEmpty(template.Parameters))
                return null;

            return JsonSerializer.Deserialize<Dictionary<string, object>>(template.Parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板参数定义失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 预览构建结果
    /// </summary>
    public async Task<string> PreviewPromptAsync(int templateId, Dictionary<string, object> parameters)
    {
        try
        {
            var template = await _templateService.GetTemplateByIdAsync(templateId);
            if (template == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            // 使用空的上下文进行预览
            var emptyContext = new Dictionary<string, object>();
            return await ReplaceParametersAsync(template.Content, parameters, emptyContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预览构建结果失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 获取推荐模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetRecommendedTemplatesAsync(string taskType, int userId, int? projectId = null)
    {
        try
        {
            _logger.LogInformation("获取推荐模板，TaskType: {TaskType}, UserId: {UserId}", taskType, userId);

            var recommendations = new List<PromptTemplate>();

            // 1. 用户最近使用的相关模板
            var recentTemplates = await _templateService.GetUserRecentTemplatesAsync(userId, 3);
            recommendations.AddRange(recentTemplates.Where(t => t.TaskType == taskType));

            // 2. 用户收藏的相关模板
            var favoriteTemplates = await _templateService.GetUserFavoriteTemplatesAsync(userId);
            recommendations.AddRange(favoriteTemplates.Where(t => t.TaskType == taskType &&
                !recommendations.Any(r => r.Id == t.Id)));

            // 3. 热门模板
            var popularTemplates = await _templateService.GetPopularTemplatesAsync(5);
            recommendations.AddRange(popularTemplates.Where(t => t.TaskType == taskType &&
                !recommendations.Any(r => r.Id == t.Id)));

            // 4. 默认模板
            var defaultTemplate = await _templateService.GetDefaultTemplateAsync(taskType);
            if (defaultTemplate != null && !recommendations.Any(r => r.Id == defaultTemplate.Id))
            {
                recommendations.Insert(0, defaultTemplate);
            }

            return recommendations.Take(10).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取推荐模板失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 优化提示词
    /// </summary>
    public async Task<string> OptimizePromptAsync(string originalPrompt, string taskType, string aiProvider)
    {
        try
        {
            _logger.LogInformation("优化提示词，TaskType: {TaskType}, Provider: {Provider}", taskType, aiProvider);

            // 这里可以实现基于AI提供商特性的提示词优化逻辑
            // 例如：为不同的AI提供商调整提示词格式、添加特定指令等

            var optimizedPrompt = originalPrompt;

            // 根据AI提供商进行优化
            switch (aiProvider.ToLower())
            {
                case "openai":
                case "azureopenai":
                    optimizedPrompt = OptimizeForOpenAI(originalPrompt, taskType);
                    break;
                case "claude":
                    optimizedPrompt = OptimizeForClaude(originalPrompt, taskType);
                    break;
                case "deepseek":
                    optimizedPrompt = OptimizeForDeepSeek(originalPrompt, taskType);
                    break;
                default:
                    // 通用优化
                    optimizedPrompt = ApplyGeneralOptimizations(originalPrompt, taskType);
                    break;
            }

            return optimizedPrompt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "优化提示词失败");
            throw;
        }
    }

    /// <summary>
    /// 分析提示词质量
    /// </summary>
    public async Task<object> AnalyzePromptQualityAsync(string prompt, string taskType)
    {
        try
        {
            _logger.LogInformation("分析提示词质量，TaskType: {TaskType}", taskType);

            var analysis = new
            {
                Length = prompt.Length,
                WordCount = prompt.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length,
                HasClearInstructions = CheckForClearInstructions(prompt),
                HasExamples = CheckForExamples(prompt),
                HasConstraints = CheckForConstraints(prompt),
                HasOutputFormat = CheckForOutputFormat(prompt),
                ComplexityScore = CalculateComplexityScore(prompt),
                ClarityScore = CalculateClarityScore(prompt),
                CompletenessScore = CalculateCompletenessScore(prompt, taskType),
                Suggestions = GenerateImprovementSuggestions(prompt, taskType)
            };

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析提示词质量失败");
            throw;
        }
    }

    #region 私有辅助方法

    /// <summary>
    /// 构建上下文信息
    /// </summary>
    private async Task<Dictionary<string, object>> BuildContextAsync(int userId, int? projectId, int? requirementDocumentId = null)
    {
        var context = new Dictionary<string, object>();

        try
        {
            // 添加用户信息
            var user = await _userRepository.GetByIdAsync(userId);
            if (user != null)
            {
                context["user_name"] = user.RealName ?? user.Username;
                context["user_role"] = user.Role;
            }

            // 添加项目信息
            if (projectId.HasValue)
            {
                var project = await _projectRepository.GetByIdAsync(projectId.Value);
                if (project != null)
                {
                    context["project_name"] = project.Name;
                    context["project_description"] = project.Description ?? "";
                    context["project_status"] = project.Status;
                    context["project_priority"] = project.Priority;
                    context["technology_stack"] = project.TechnologyStack ?? "";

                    // 构建详细的项目背景信息
                    context["project_background"] = BuildProjectBackground(project);

                    // 获取项目的需求文档信息
                    if (requirementDocumentId.HasValue && requirementDocumentId.Value > 0)
                    {
                        // 获取特定需求文档的详细信息
                        context["project_requirements"] = await GetSpecificRequirementDocumentAsync(requirementDocumentId.Value);
                        context["requirement_document_info"] = await GetRequirementDocumentInfoAsync(requirementDocumentId.Value);
                    }
                    else
                    {
                        // 获取项目所有需求文档的概要信息
                        context["project_requirements"] = await GetProjectRequirementsAsync(projectId.Value);
                        context["requirement_document_info"] = "未指定特定需求文档，使用项目所有需求文档信息";
                    }
                }
            }

            // 添加系统信息 - 基于开发文档的系统架构
            context["system_architecture"] = GetSystemArchitectureInfo();
            context["business_entities"] = GetBusinessEntitiesInfo();
            context["technical_stack"] = GetTechnicalStackInfo();

            // 添加时间信息
            context["current_date"] = DateTime.Now.ToString("yyyy-MM-dd");
            context["current_time"] = DateTime.Now.ToString("HH:mm:ss");
            context["current_datetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "构建上下文信息时发生错误");
        }

        return context;
    }

    /// <summary>
    /// 构建项目背景信息
    /// </summary>
    private string BuildProjectBackground(Project project)
    {
        return $@"
项目名称：{project.Name}
项目描述：{project.Description ?? "AI驱动软件开发自动化系统"}
项目状态：{project.Status}
项目优先级：{project.Priority}
技术栈：{project.TechnologyStack ?? "Vue 3 + ASP.NET Core + SQL Server"}
开始时间：{project.StartDate?.ToString("yyyy-MM-dd") ?? "未设定"}
预计结束时间：{project.EndDate?.ToString("yyyy-MM-dd") ?? "未设定"}";
    }

    /// <summary>
    /// 获取项目需求信息
    /// </summary>
    private async Task<string> GetProjectRequirementsAsync(int projectId)
    {
        try
        {
            // 从数据库获取项目的需求文档
            var requirementDocs = await _requirementDocumentRepository.GetListAsync(r =>
                r.ProjectId == projectId && !r.IsDeleted);

            if (requirementDocs == null || !requirementDocs.Any())
            {
                _logger.LogInformation("项目 {ProjectId} 暂无需求文档，使用默认需求信息", projectId);
                return GetDefaultRequirementsInfo();
            }

            var requirementsInfo = new System.Text.StringBuilder();
            requirementsInfo.AppendLine("项目需求文档信息：");

            foreach (var doc in requirementDocs.OrderByDescending(d => d.CreatedTime))
            {
                requirementsInfo.AppendLine($"\n=== {doc.Title} (版本: {doc.DocumentVersion}) ===");

                if (!string.IsNullOrEmpty(doc.Content))
                {
                    requirementsInfo.AppendLine("需求概述：");
                    requirementsInfo.AppendLine(TruncateText(doc.Content, 500));
                }

                if (!string.IsNullOrEmpty(doc.FunctionalRequirements))
                {
                    requirementsInfo.AppendLine("\n功能性需求：");
                    requirementsInfo.AppendLine(TruncateText(doc.FunctionalRequirements, 800));
                }

                if (!string.IsNullOrEmpty(doc.NonFunctionalRequirements))
                {
                    requirementsInfo.AppendLine("\n非功能性需求：");
                    requirementsInfo.AppendLine(TruncateText(doc.NonFunctionalRequirements, 400));
                }

                if (!string.IsNullOrEmpty(doc.UserStories))
                {
                    requirementsInfo.AppendLine("\n用户故事：");
                    requirementsInfo.AppendLine(TruncateText(doc.UserStories, 600));
                }

                if (!string.IsNullOrEmpty(doc.AcceptanceCriteria))
                {
                    requirementsInfo.AppendLine("\n验收标准：");
                    requirementsInfo.AppendLine(TruncateText(doc.AcceptanceCriteria, 400));
                }

                requirementsInfo.AppendLine($"\n文档状态：{doc.Status}");
                requirementsInfo.AppendLine($"生成方式：{doc.GeneratedBy}");
                requirementsInfo.AppendLine("---");
            }

            _logger.LogInformation("成功获取项目 {ProjectId} 的 {Count} 个需求文档", projectId, requirementDocs.Count());
            return requirementsInfo.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取项目需求信息失败，ProjectId: {ProjectId}", projectId);
            return GetDefaultRequirementsInfo();
        }
    }

    /// <summary>
    /// 获取默认需求信息（当项目没有需求文档时使用）
    /// </summary>
    private string GetDefaultRequirementsInfo()
    {
        return @"
默认系统需求（项目暂无具体需求文档）：
1. 需求管理模块 - AI需求分析和理解、自动生成需求规格书
2. 设计生成模块 - 自动生成ER图、Context图、设计文档管理
3. 代码生成模块 - AI驱动的代码生成、模板管理系统、代码质量检查
4. 测试自动化 - 单元测试生成、集成测试框架、UI自动化测试
5. 部署自动化 - CI/CD流水线、容器化部署、环境管理
6. 运维监控 - 系统监控集成、日志聚合、告警机制
7. 用户管理 - 用户认证授权、角色权限管理、多租户支持
8. 项目管理 - 项目创建管理、进度跟踪、团队协作

注意：建议为项目创建具体的需求文档以获得更准确的设计结果。";
    }

    /// <summary>
    /// 获取特定需求文档的详细信息
    /// </summary>
    private async Task<string> GetSpecificRequirementDocumentAsync(int requirementDocumentId)
    {
        try
        {
            var doc = await _requirementDocumentRepository.GetByIdAsync(requirementDocumentId);
            if (doc == null || doc.IsDeleted)
            {
                _logger.LogWarning("需求文档不存在或已删除: {RequirementDocumentId}", requirementDocumentId);
                return "指定的需求文档不存在或已删除";
            }

            var docInfo = new System.Text.StringBuilder();
            docInfo.AppendLine($"需求文档详细信息：");
            docInfo.AppendLine($"标题：{doc.Title}");
            docInfo.AppendLine($"版本：{doc.DocumentVersion}");
            docInfo.AppendLine($"状态：{doc.Status}");
            docInfo.AppendLine($"生成方式：{doc.GeneratedBy}");
            docInfo.AppendLine();

            if (!string.IsNullOrEmpty(doc.Content))
            {
                docInfo.AppendLine("需求概述：");
                docInfo.AppendLine(doc.Content);
                docInfo.AppendLine();
            }

            if (!string.IsNullOrEmpty(doc.FunctionalRequirements))
            {
                docInfo.AppendLine("功能性需求：");
                docInfo.AppendLine(doc.FunctionalRequirements);
                docInfo.AppendLine();
            }

            if (!string.IsNullOrEmpty(doc.NonFunctionalRequirements))
            {
                docInfo.AppendLine("非功能性需求：");
                docInfo.AppendLine(doc.NonFunctionalRequirements);
                docInfo.AppendLine();
            }

            if (!string.IsNullOrEmpty(doc.UserStories))
            {
                docInfo.AppendLine("用户故事：");
                docInfo.AppendLine(doc.UserStories);
                docInfo.AppendLine();
            }

            if (!string.IsNullOrEmpty(doc.AcceptanceCriteria))
            {
                docInfo.AppendLine("验收标准：");
                docInfo.AppendLine(doc.AcceptanceCriteria);
                docInfo.AppendLine();
            }

            _logger.LogInformation("成功获取需求文档详细信息: {RequirementDocumentId}", requirementDocumentId);
            return docInfo.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求文档详细信息失败: {RequirementDocumentId}", requirementDocumentId);
            return $"获取需求文档信息失败：{ex.Message}";
        }
    }

    /// <summary>
    /// 获取需求文档基本信息
    /// </summary>
    private async Task<string> GetRequirementDocumentInfoAsync(int requirementDocumentId)
    {
        try
        {
            var doc = await _requirementDocumentRepository.GetByIdAsync(requirementDocumentId);
            if (doc == null || doc.IsDeleted)
            {
                return "需求文档不存在";
            }

            return $"需求文档：{doc.Title} (版本: {doc.DocumentVersion}, 状态: {doc.Status})";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取需求文档基本信息失败: {RequirementDocumentId}", requirementDocumentId);
            return "获取需求文档信息失败";
        }
    }

    /// <summary>
    /// 截断文本到指定长度
    /// </summary>
    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }

    /// <summary>
    /// 获取系统架构信息
    /// </summary>
    private string GetSystemArchitectureInfo()
    {
        return @"
系统架构：前后端分离架构
- 前端：Vue 3 + Element Plus + TypeScript + Vite
- 后端：ASP.NET Core 8.0 + SqlSugar ORM + JWT认证
- 数据库：SQL Server 2022 + Redis缓存
- AI服务：Azure OpenAI + DeepSeek + Claude等多模型支持
- 部署：Docker + Kubernetes + Azure云服务";
    }

    /// <summary>
    /// 获取业务实体信息
    /// </summary>
    private string GetBusinessEntitiesInfo()
    {
        return @"
核心业务实体：
- 用户管理：Users, Roles, UserRoles, UserProfiles
- 项目管理：Projects, ProjectMembers, ProjectSettings
- 需求管理：RequirementDocuments, RequirementConversations
- 设计管理：ERDiagrams, ContextDiagrams, DesignDocuments
- 代码管理：CodeGenerationTasks, GeneratedCodeFiles, CodeTemplates
- 测试管理：TestTasks, TestResults, TestCases
- 部署管理：DeploymentTasks, DeploymentEnvironments
- 系统管理：SystemLogs, SystemConfigurations, AIModelConfigurations";
    }

    /// <summary>
    /// 获取技术栈信息
    /// </summary>
    private string GetTechnicalStackInfo()
    {
        return @"
技术栈详情：
- 数据库：SQL Server 2022，支持高可用和性能优化
- ORM：SqlSugar，轻量级高性能ORM
- 认证：JWT + OAuth 2.0，支持多租户
- 缓存：Redis，用于会话存储和数据缓存
- 消息队列：RabbitMQ，异步任务处理
- 监控：Azure Monitor + Application Insights
- 日志：Serilog + ELK Stack";
    }

    /// <summary>
    /// 替换模板中的参数
    /// </summary>
    private async Task<string> ReplaceParametersAsync(string template, Dictionary<string, object> parameters, Dictionary<string, object> context)
    {
        var result = template;

        // 确保参数不为null
        if (parameters == null)
            parameters = new Dictionary<string, object>();
        if (context == null)
            context = new Dictionary<string, object>();

        // 合并参数和上下文
        var allParameters = new Dictionary<string, object>(context);
        foreach (var param in parameters)
        {
            allParameters[param.Key] = param.Value;
        }

        // 首先替换 {{parameter_name}} 格式的占位符（Handlebars语法）
        var handlebarsRegex = new Regex(@"\{\{([^}]+)\}\}", RegexOptions.IgnoreCase);
        result = handlebarsRegex.Replace(result, match =>
        {
            var paramName = match.Groups[1].Value.Trim();

            // 处理条件语句 {{#if parameter}}
            if (paramName.StartsWith("#if "))
            {
                var conditionParam = paramName.Substring(4).Trim();
                if (allParameters.ContainsKey(conditionParam))
                {
                    var value = allParameters[conditionParam];
                    // 如果值存在且不为空，则保留条件内容，否则移除
                    return value != null && !string.IsNullOrEmpty(value.ToString()) ? "" : "";
                }
                return ""; // 条件不满足，移除内容
            }

            // 处理普通参数替换
            if (allParameters.ContainsKey(paramName))
            {
                return allParameters[paramName]?.ToString() ?? "";
            }
            return match.Value; // 保持原样如果参数不存在
        });

        // 然后替换 {parameter_name} 格式的占位符（向后兼容）
        var singleBraceRegex = new Regex(@"\{([^}]+)\}", RegexOptions.IgnoreCase);
        result = singleBraceRegex.Replace(result, match =>
        {
            var paramName = match.Groups[1].Value;
            if (allParameters.ContainsKey(paramName))
            {
                return allParameters[paramName]?.ToString() ?? "";
            }
            return match.Value; // 保持原样如果参数不存在
        });

        // 处理Handlebars条件语句的结束标签和内容
        result = ProcessHandlebarsConditionals(result, allParameters);

        return result;
    }

    /// <summary>
    /// 处理Handlebars条件语句
    /// </summary>
    private string ProcessHandlebarsConditionals(string template, Dictionary<string, object> parameters)
    {
        // 处理 {{#if condition}}content{{/if}} 格式
        var conditionalRegex = new Regex(@"\{\{#if\s+(\w+)\}\}(.*?)\{\{/if\}\}", RegexOptions.Singleline | RegexOptions.IgnoreCase);

        return conditionalRegex.Replace(template, match =>
        {
            var conditionParam = match.Groups[1].Value.Trim();
            var content = match.Groups[2].Value;

            if (parameters.ContainsKey(conditionParam))
            {
                var value = parameters[conditionParam];
                // 如果条件为真（值存在且不为空），则保留内容
                if (value != null && !string.IsNullOrEmpty(value.ToString()) && !value.ToString().Equals("false", StringComparison.OrdinalIgnoreCase))
                {
                    return content;
                }
            }

            return ""; // 条件不满足，移除整个条件块
        });
    }

    /// <summary>
    /// 验证参数类型
    /// </summary>
    private bool ValidateParameterType(object value, string expectedType)
    {
        if (value == null) return true; // null值认为是有效的

        var lowerType = expectedType.ToLower();

        switch (lowerType)
        {
            case "string":
                return value is string;
            case "number":
            case "int":
            case "integer":
                return value is int || value is long || value is decimal || value is double;
            case "boolean":
            case "bool":
                return value is bool;
            case "array":
                return IsArrayType(value);
            case "object":
                return true; // 所有对象都是object类型
            default:
                return true; // 未知类型认为有效
        }
    }

    private bool IsArrayType(object value)
    {
        return value is Array || value.GetType().IsGenericType;
    }

    /// <summary>
    /// 为OpenAI优化提示词
    /// </summary>
    private string OptimizeForOpenAI(string prompt, string taskType)
    {
        var optimized = prompt;

        // 添加角色定义
        if (!prompt.Contains("You are") && !prompt.Contains("你是"))
        {
            optimized = "You are a professional AI assistant specialized in software development.\n\n" + optimized;
        }

        // 确保有明确的输出格式要求
        if (taskType == "RequirementAnalysis" && !prompt.Contains("JSON"))
        {
            optimized += "\n\nPlease provide the response in JSON format.";
        }

        return optimized;
    }

    /// <summary>
    /// 为Claude优化提示词
    /// </summary>
    private string OptimizeForClaude(string prompt, string taskType)
    {
        var optimized = prompt;

        // Claude喜欢更详细的指令
        if (!prompt.Contains("step by step") && !prompt.Contains("逐步"))
        {
            optimized = optimized.Replace("请", "请逐步");
        }

        return optimized;
    }

    /// <summary>
    /// 为DeepSeek优化提示词
    /// </summary>
    private string OptimizeForDeepSeek(string prompt, string taskType)
    {
        var optimized = prompt;

        // DeepSeek在代码生成方面表现较好，可以添加更多技术细节要求
        if (taskType == "CodeGeneration")
        {
            optimized += "\n\n请确保代码包含详细的注释和错误处理。";
        }

        return optimized;
    }

    /// <summary>
    /// 应用通用优化
    /// </summary>
    private string ApplyGeneralOptimizations(string prompt, string taskType)
    {
        var optimized = prompt;

        // 确保提示词以明确的指令开始
        if (!prompt.TrimStart().StartsWith("请") && !prompt.TrimStart().StartsWith("Please"))
        {
            optimized = "请" + optimized;
        }

        return optimized;
    }

    /// <summary>
    /// 检查是否有清晰的指令
    /// </summary>
    private bool CheckForClearInstructions(string prompt)
    {
        var instructionKeywords = new[] { "请", "Please", "生成", "Generate", "分析", "Analyze", "创建", "Create" };
        return instructionKeywords.Any(keyword => prompt.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 检查是否有示例
    /// </summary>
    private bool CheckForExamples(string prompt)
    {
        var exampleKeywords = new[] { "例如", "示例", "example", "for example", "如下", "格式示例" };
        return exampleKeywords.Any(keyword => prompt.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 检查是否有约束条件
    /// </summary>
    private bool CheckForConstraints(string prompt)
    {
        var constraintKeywords = new[] { "不要", "避免", "don't", "avoid", "限制", "constraint", "要求", "requirement" };
        return constraintKeywords.Any(keyword => prompt.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 检查是否有输出格式要求
    /// </summary>
    private bool CheckForOutputFormat(string prompt)
    {
        var formatKeywords = new[] { "JSON", "格式", "format", "输出", "output", "返回", "return" };
        return formatKeywords.Any(keyword => prompt.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 计算复杂度评分
    /// </summary>
    private int CalculateComplexityScore(string prompt)
    {
        var score = 0;
        var wordCount = prompt.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;

        if (wordCount > 100) score += 2;
        else if (wordCount > 50) score += 1;

        if (prompt.Contains("JSON") || prompt.Contains("格式")) score += 1;
        if (CheckForExamples(prompt)) score += 1;
        if (CheckForConstraints(prompt)) score += 1;

        return Math.Min(score, 5); // 最高5分
    }

    /// <summary>
    /// 计算清晰度评分
    /// </summary>
    private int CalculateClarityScore(string prompt)
    {
        var score = 5; // 从满分开始

        if (!CheckForClearInstructions(prompt)) score -= 2;
        if (prompt.Length < 50) score -= 1;
        if (!CheckForOutputFormat(prompt)) score -= 1;

        return Math.Max(score, 1); // 最低1分
    }

    /// <summary>
    /// 计算完整性评分
    /// </summary>
    private int CalculateCompletenessScore(string prompt, string taskType)
    {
        var score = 3; // 基础分

        if (CheckForClearInstructions(prompt)) score += 1;
        if (CheckForOutputFormat(prompt)) score += 1;

        // 根据任务类型检查特定要求
        switch (taskType)
        {
            case "RequirementAnalysis":
                if (prompt.Contains("功能需求") || prompt.Contains("functional")) score += 1;
                if (prompt.Contains("非功能需求") || prompt.Contains("non-functional")) score += 1;
                break;
            case "CodeGeneration":
                if (prompt.Contains("注释") || prompt.Contains("comment")) score += 1;
                if (prompt.Contains("错误处理") || prompt.Contains("error handling")) score += 1;
                break;
        }

        return Math.Min(score, 5); // 最高5分
    }

    /// <summary>
    /// 生成改进建议
    /// </summary>
    private List<string> GenerateImprovementSuggestions(string prompt, string taskType)
    {
        var suggestions = new List<string>();

        if (!CheckForClearInstructions(prompt))
        {
            suggestions.Add("添加更明确的指令词，如'请分析'、'请生成'等");
        }

        if (!CheckForOutputFormat(prompt))
        {
            suggestions.Add("指定期望的输出格式，如JSON、Markdown等");
        }

        if (!CheckForExamples(prompt))
        {
            suggestions.Add("添加示例以帮助AI更好地理解需求");
        }

        if (prompt.Length < 100)
        {
            suggestions.Add("提供更详细的背景信息和要求");
        }

        if (!CheckForConstraints(prompt))
        {
            suggestions.Add("添加约束条件以避免不期望的输出");
        }

        return suggestions;
    }

    #endregion
}
