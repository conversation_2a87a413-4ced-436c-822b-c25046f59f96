using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.AI.Services;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// Prompt分析控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PromptAnalyticsController : ControllerBase
{
    private readonly IPromptAnalyticsService _analyticsService;
    private readonly ILogger<PromptAnalyticsController> _logger;

    public PromptAnalyticsController(
        IPromptAnalyticsService analyticsService,
        ILogger<PromptAnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 0;
    }

    /// <summary>
    /// 获取模板使用统计
    /// </summary>
    [HttpGet("templates/{templateId}/usage")]
    public async Task<ActionResult<TemplateUsageAnalytics>> GetTemplateUsageAnalytics(
        int templateId, 
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analytics = await _analyticsService.GetTemplateUsageAnalyticsAsync(templateId, startDate, endDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板使用统计失败，TemplateId: {TemplateId}", templateId);
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取用户使用统计
    /// </summary>
    [HttpGet("users/{userId}/usage")]
    public async Task<ActionResult<UserUsageAnalytics>> GetUserUsageAnalytics(
        int userId, 
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            // 检查权限：只能查看自己的统计或管理员可以查看所有用户
            var currentUserId = GetCurrentUserId();
            if (userId != currentUserId && !User.IsInRole("Admin"))
                return Forbid("无权限查看其他用户的统计数据");

            var analytics = await _analyticsService.GetUserUsageAnalyticsAsync(userId, startDate, endDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户使用统计失败，UserId: {UserId}", userId);
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取当前用户使用统计
    /// </summary>
    [HttpGet("my-usage")]
    public async Task<ActionResult<UserUsageAnalytics>> GetMyUsageAnalytics(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var userId = GetCurrentUserId();
            var analytics = await _analyticsService.GetUserUsageAnalyticsAsync(userId, startDate, endDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户使用统计失败");
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取整体使用统计（仅管理员）
    /// </summary>
    [HttpGet("overall")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<ActionResult<OverallUsageAnalytics>> GetOverallUsageAnalytics(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analytics = await _analyticsService.GetOverallUsageAnalyticsAsync(startDate, endDate);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取整体使用统计失败");
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取热门模板排行
    /// </summary>
    [HttpGet("templates/popular")]
    public async Task<ActionResult<List<TemplateRanking>>> GetPopularTemplatesRanking(
        [FromQuery] int count = 10,
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var rankings = await _analyticsService.GetPopularTemplatesRankingAsync(count, startDate, endDate);
            return Ok(rankings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门模板排行失败");
            return StatusCode(500, new { message = "获取排行数据失败" });
        }
    }

    /// <summary>
    /// 获取AI提供商使用统计
    /// </summary>
    [HttpGet("providers/usage")]
    public async Task<ActionResult<List<ProviderUsageStats>>> GetProviderUsageStats(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _analyticsService.GetProviderUsageStatsAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商使用统计失败");
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取任务类型使用统计
    /// </summary>
    [HttpGet("task-types/usage")]
    public async Task<ActionResult<List<TaskTypeUsageStats>>> GetTaskTypeUsageStats(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _analyticsService.GetTaskTypeUsageStatsAsync(startDate, endDate);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务类型使用统计失败");
            return StatusCode(500, new { message = "获取统计数据失败" });
        }
    }

    /// <summary>
    /// 获取模板效果分析
    /// </summary>
    [HttpGet("templates/{templateId}/effectiveness")]
    public async Task<ActionResult<TemplateEffectivenessAnalysis>> GetTemplateEffectiveness(int templateId)
    {
        try
        {
            var analysis = await _analyticsService.GetTemplateEffectivenessAsync(templateId);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板效果分析失败，TemplateId: {TemplateId}", templateId);
            return StatusCode(500, new { message = "获取分析数据失败" });
        }
    }

    /// <summary>
    /// 比较模板效果
    /// </summary>
    [HttpGet("templates/compare")]
    public async Task<ActionResult<TemplateComparisonResult>> CompareTemplates(
        [FromQuery] int templateId1, 
        [FromQuery] int templateId2)
    {
        try
        {
            var comparison = await _analyticsService.CompareTemplatesAsync(templateId1, templateId2);
            return Ok(comparison);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "比较模板效果失败");
            return StatusCode(500, new { message = "比较分析失败" });
        }
    }

    /// <summary>
    /// 获取优化建议
    /// </summary>
    [HttpGet("templates/{templateId}/optimization-suggestions")]
    public async Task<ActionResult<List<OptimizationSuggestion>>> GetOptimizationSuggestions(int templateId)
    {
        try
        {
            var suggestions = await _analyticsService.GetOptimizationSuggestionsAsync(templateId);
            return Ok(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取优化建议失败，TemplateId: {TemplateId}", templateId);
            return StatusCode(500, new { message = "获取优化建议失败" });
        }
    }

    /// <summary>
    /// 执行A/B测试分析
    /// </summary>
    [HttpPost("ab-test")]
    public async Task<ActionResult<ABTestResult>> AnalyzeABTest([FromBody] ABTestRequest request)
    {
        try
        {
            var result = await _analyticsService.AnalyzeABTestAsync(
                request.TemplateAId, 
                request.TemplateBId, 
                request.StartDate, 
                request.EndDate);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "A/B测试分析失败");
            return StatusCode(500, new { message = "A/B测试分析失败" });
        }
    }

    /// <summary>
    /// 获取成本分析
    /// </summary>
    [HttpGet("cost-analysis")]
    public async Task<ActionResult<CostAnalysis>> GetCostAnalysis(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analysis = await _analyticsService.GetCostAnalysisAsync(startDate, endDate);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取成本分析失败");
            return StatusCode(500, new { message = "获取成本分析失败" });
        }
    }

    /// <summary>
    /// 获取性能分析
    /// </summary>
    [HttpGet("performance-analysis")]
    public async Task<ActionResult<PerformanceAnalysis>> GetPerformanceAnalysis(
        [FromQuery] DateTime? startDate = null, 
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var analysis = await _analyticsService.GetPerformanceAnalysisAsync(startDate, endDate);
            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能分析失败");
            return StatusCode(500, new { message = "获取性能分析失败" });
        }
    }

    /// <summary>
    /// 生成分析报告
    /// </summary>
    [HttpPost("reports")]
    public async Task<ActionResult<AnalyticsReport>> GenerateReport([FromBody] GenerateReportRequest request)
    {
        try
        {
            var report = await _analyticsService.GenerateReportAsync(request.ReportType, request.Parameters);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成分析报告失败");
            return StatusCode(500, new { message = "生成报告失败" });
        }
    }
}

/// <summary>
/// A/B测试请求
/// </summary>
public class ABTestRequest
{
    public int TemplateAId { get; set; }
    public int TemplateBId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
}

/// <summary>
/// 生成报告请求
/// </summary>
public class GenerateReportRequest
{
    public ReportType ReportType { get; set; }
    public ReportParameters Parameters { get; set; } = new();
}
