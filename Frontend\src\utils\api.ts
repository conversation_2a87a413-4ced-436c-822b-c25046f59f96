import { ElMessage } from 'element-plus'

/**
 * API请求工具类
 */
export class ApiClient {
  private static baseURL = ''

  /**
   * 获取认证token
   */
  private static getAuthToken(): string | null {
    return localStorage.getItem('token') || sessionStorage.getItem('token')
  }

  /**
   * 获取默认请求头
   */
  private static getDefaultHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    const token = this.getAuthToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    return headers
  }

  /**
   * 处理响应错误
   */
  private static async handleResponse(response: Response): Promise<any> {
    if (!response.ok) {
      if (response.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        // 清除过期token
        localStorage.removeItem('token')
        sessionStorage.removeItem('token')
        // 可以在这里触发重新登录逻辑
        throw new Error('Unauthorized')
      }
      
      if (response.status === 403) {
        ElMessage.error('无权限访问')
        throw new Error('Forbidden')
      }
      
      if (response.status === 404) {
        throw new Error('Not Found')
      }
      
      if (response.status >= 500) {
        ElMessage.error('服务器错误，请稍后重试')
        throw new Error('Server Error')
      }
      
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      return await response.json()
    }
    
    return await response.text()
  }

  /**
   * GET请求
   */
  static async get(url: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: 'GET',
        headers: this.getDefaultHeaders()
      })
      
      return await this.handleResponse(response)
    } catch (error) {
      console.error(`GET ${url} 失败:`, error)
      throw error
    }
  }

  /**
   * POST请求
   */
  static async post(url: string, data?: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: 'POST',
        headers: this.getDefaultHeaders(),
        body: data ? JSON.stringify(data) : undefined
      })
      
      return await this.handleResponse(response)
    } catch (error) {
      console.error(`POST ${url} 失败:`, error)
      throw error
    }
  }

  /**
   * PUT请求
   */
  static async put(url: string, data?: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: 'PUT',
        headers: this.getDefaultHeaders(),
        body: data ? JSON.stringify(data) : undefined
      })
      
      return await this.handleResponse(response)
    } catch (error) {
      console.error(`PUT ${url} 失败:`, error)
      throw error
    }
  }

  /**
   * DELETE请求
   */
  static async delete(url: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: 'DELETE',
        headers: this.getDefaultHeaders()
      })
      
      return await this.handleResponse(response)
    } catch (error) {
      console.error(`DELETE ${url} 失败:`, error)
      throw error
    }
  }
}

/**
 * 项目相关API
 */
export const ProjectApi = {
  /**
   * 获取项目列表
   */
  async getProjects(params?: { pageNumber?: number; pageSize?: number; status?: string; search?: string }) {
    const queryParams = new URLSearchParams()
    if (params?.pageNumber) queryParams.append('pageNumber', params.pageNumber.toString())
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params?.status) queryParams.append('status', params.status)
    if (params?.search) queryParams.append('search', params.search)
    
    const url = `/api/projects${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    return await ApiClient.get(url)
  },

  /**
   * 获取项目详情
   */
  async getProject(id: number) {
    return await ApiClient.get(`/api/projects/${id}`)
  },

  /**
   * 创建项目
   */
  async createProject(data: any) {
    return await ApiClient.post('/api/projects', data)
  },

  /**
   * 更新项目
   */
  async updateProject(id: number, data: any) {
    return await ApiClient.put(`/api/projects/${id}`, data)
  },

  /**
   * 删除项目
   */
  async deleteProject(id: number) {
    return await ApiClient.delete(`/api/projects/${id}`)
  }
}

/**
 * 需求相关API
 */
export const RequirementApi = {
  /**
   * 获取项目需求列表
   */
  async getProjectRequirements(projectId: number, params?: { pageNumber?: number; pageSize?: number }) {
    const queryParams = new URLSearchParams()
    if (params?.pageNumber) queryParams.append('pageNumber', params.pageNumber.toString())
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    
    const url = `/api/requirements/projects/${projectId}/documents${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    return await ApiClient.get(url)
  },

  /**
   * 获取需求详情
   */
  async getRequirement(id: number) {
    return await ApiClient.get(`/api/requirements/${id}`)
  },

  /**
   * 创建需求
   */
  async createRequirement(data: any) {
    return await ApiClient.post('/api/requirements', data)
  }
}