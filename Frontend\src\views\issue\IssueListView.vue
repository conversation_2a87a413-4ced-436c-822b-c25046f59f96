<template>
  <div class="issue-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Warning /></el-icon>
          问题管理
        </h1>
        <p class="page-subtitle">管理项目问题、缺陷和功能请求</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建问题
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalCount }}</div>
                <div class="stat-label">总问题数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon open">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.openCount }}</div>
                <div class="stat-label">开放问题</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon progress">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.inProgressCount }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon resolved">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.resolvedCount }}</div>
                <div class="stat-label">已解决</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题、描述或标签"
            style="width: 200px"
            clearable
            @keyup.enter="searchIssues"
          />
        </el-form-item>
        <el-form-item label="项目">
          <el-select
            v-model="searchForm.projectId"
            placeholder="选择项目"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            style="width: 120px"
            clearable
          >
            <el-option
              v-for="status in STATUSES"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="选择优先级"
            style="width: 120px"
            clearable
          >
            <el-option
              v-for="priority in PRIORITIES"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="searchForm.issueType"
            placeholder="选择类型"
            style="width: 120px"
            clearable
          >
            <el-option
              v-for="type in ISSUE_TYPES"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchIssues">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 问题列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>问题列表</span>
          <div class="table-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'my-assigned' ? 'primary' : ''"
                @click="viewMode = 'my-assigned'; loadMyAssignedIssues()"
              >
                分配给我的
              </el-button>
              <el-button
                :type="viewMode === 'my-reported' ? 'primary' : ''"
                @click="viewMode = 'my-reported'; loadMyReportedIssues()"
              >
                我报告的
              </el-button>
              <el-button
                :type="viewMode === 'all' ? 'primary' : ''"
                @click="viewMode = 'all'; searchIssues()"
              >
                全部问题
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <el-table
        :data="issues"
        v-loading="loading"
        stripe
        @row-click="viewIssueDetail"
        style="cursor: pointer"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="issue-title">
              <span>{{ row.title }}</span>
              <div class="issue-labels" v-if="row.labels && row.labels.length > 0">
                <el-tag
                  v-for="label in row.labels"
                  :key="label"
                  size="small"
                  type="info"
                >
                  {{ label }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="issueType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag
              :color="getIssueTypeInfo(row.issueType).color"
              effect="light"
              size="small"
            >
              {{ getIssueTypeInfo(row.issueType).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag
              :color="getPriorityInfo(row.priority).color"
              effect="light"
              size="small"
            >
              {{ getPriorityInfo(row.priority).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :color="getStatusInfo(row.status).color"
              effect="light"
              size="small"
            >
              {{ getStatusInfo(row.status).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目" width="150" />
        <el-table-column prop="assignedToUser" label="分配给" width="120">
          <template #default="{ row }">
            <span v-if="row.assignedToUser">{{ row.assignedToUser.realName || row.assignedToUser.username }}</span>
            <span v-else class="text-gray-400">未分配</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportedByUser" label="报告人" width="120">
          <template #default="{ row }">
            {{ row.reportedByUser?.realName || row.reportedByUser?.username }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click.stop="editIssue(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-dropdown @click.stop trigger="click">
              <el-button size="small">
                更多
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="changeStatus(row, 'InProgress')" v-if="row.status === 'Open'">
                    标记为处理中
                  </el-dropdown-item>
                  <el-dropdown-item @click="changeStatus(row, 'Resolved')" v-if="row.status !== 'Resolved' && row.status !== 'Closed'">
                    标记为已解决
                  </el-dropdown-item>
                  <el-dropdown-item @click="changeStatus(row, 'Closed')" v-if="row.status !== 'Closed'">
                    关闭问题
                  </el-dropdown-item>
                  <el-dropdown-item @click="assignIssue(row)">
                    重新分配
                  </el-dropdown-item>
                  <el-dropdown-item @click="deleteIssue(row)" divided>
                    <span style="color: #f56565">删除问题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑问题对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑问题' : '创建问题'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="项目" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="选择项目"
            style="width: 100%"
            :disabled="isEditing"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入问题标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述问题"
          />
        </el-form-item>
        <el-form-item label="类型" prop="issueType">
          <el-select v-model="form.issueType" style="width: 100%">
            <el-option
              v-for="type in ISSUE_TYPES"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" style="width: 100%">
            <el-option
              v-for="priority in PRIORITIES"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分配给" prop="assignedTo" v-if="isEditing">
          <el-select
            v-model="form.assignedTo"
            placeholder="选择分配用户"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.realName || user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="isEditing">
          <el-select v-model="form.status" style="width: 100%">
            <el-option
              v-for="status in STATUSES"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="labels">
          <el-input
            v-model="form.labels"
            placeholder="多个标签用逗号分隔"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 问题详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="问题详情"
      width="800px"
    >
      <div v-if="selectedIssue" class="issue-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedIssue.id }}</el-descriptions-item>
          <el-descriptions-item label="项目">{{ selectedIssue.projectName }}</el-descriptions-item>
          <el-descriptions-item label="标题" :span="2">{{ selectedIssue.title }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :color="getIssueTypeInfo(selectedIssue.issueType).color" effect="light">
              {{ getIssueTypeInfo(selectedIssue.issueType).label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :color="getPriorityInfo(selectedIssue.priority).color" effect="light">
              {{ getPriorityInfo(selectedIssue.priority).label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :color="getStatusInfo(selectedIssue.status).color" effect="light">
              {{ getStatusInfo(selectedIssue.status).label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分配给">
            {{ selectedIssue.assignedToUser?.realName || selectedIssue.assignedToUser?.username || '未分配' }}
          </el-descriptions-item>
          <el-descriptions-item label="报告人">
            {{ selectedIssue.reportedByUser?.realName || selectedIssue.reportedByUser?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedIssue.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(selectedIssue.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="解决时间" v-if="selectedIssue.resolvedAt">
            {{ formatDate(selectedIssue.resolvedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="标签" :span="2" v-if="selectedIssue.labels && selectedIssue.labels.length > 0">
            <el-tag
              v-for="label in selectedIssue.labels"
              :key="label"
              size="small"
              type="info"
              style="margin-right: 8px"
            >
              {{ label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div class="issue-description">{{ selectedIssue.description }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="editIssue(selectedIssue!)">编辑问题</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Warning,
  Plus,
  Refresh,
  Document,
  CircleCheck,
  Loading,
  Check,
  Search,
  Edit,
  ArrowDown
} from '@element-plus/icons-vue'
import {
  IssueService,
  type Issue,
  type IssueStatistics,
  type CreateIssueRequest,
  type UpdateIssueRequest,
  type IssueSearchParams,
  ISSUE_TYPES,
  PRIORITIES,
  STATUSES,
  getIssueTypeInfo,
  getPriorityInfo,
  getStatusInfo
} from '@/services/issue'
import { ProjectService, type ProjectSummary } from '@/services/project'
import { UserService } from '@/services/user'
import type { User } from '@/types/user'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const issues = ref<Issue[]>([])
const statistics = ref<IssueStatistics | null>(null)
const projects = ref<ProjectSummary[]>([])
const users = ref<User[]>([])
const selectedIssue = ref<Issue | null>(null)

// 对话框状态
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEditing = ref(false)

// 视图模式
const viewMode = ref<'all' | 'my-assigned' | 'my-reported'>('all')

// 搜索表单
const searchForm = reactive<IssueSearchParams>({
  keyword: '',
  projectId: undefined,
  status: '',
  priority: '',
  issueType: '',
  assignedTo: undefined,
  pageIndex: 1,
  pageSize: 20
})

// 分页
const pagination = reactive({
  pageIndex: 1,
  pageSize: 20,
  total: 0
})

// 表单
const formRef = ref<FormInstance>()
const form = reactive({
  id: 0,
  projectId: 0,
  title: '',
  description: '',
  issueType: 'Bug',
  priority: 'Medium',
  status: 'Open',
  assignedTo: undefined as number | undefined,
  labels: ''
})

// 表单验证规则
const formRules: FormRules = {
  projectId: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入问题标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度应在 5 到 200 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 10, message: '描述至少需要 10 个字符', trigger: 'blur' }
  ],
  issueType: [
    { required: true, message: '请选择问题类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 生命周期
onMounted(() => {
  loadInitialData()
})

// 方法
const loadInitialData = async () => {
  await Promise.all([
    loadProjects(),
    loadUsers(),
    loadStatistics(),
    searchIssues()
  ])
}

const loadProjects = async () => {
  try {
    projects.value = await ProjectService.getAllProjects()
  } catch (error: any) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

const loadUsers = async () => {
  try {
    users.value = await UserService.getAllUsers()
  } catch (error: any) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

const loadStatistics = async () => {
  try {
    statistics.value = await IssueService.getIssueStatistics()
  } catch (error: any) {
    console.error('加载统计信息失败:', error)
    ElMessage.error('加载统计信息失败')
  }
}

const searchIssues = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    }
    const result = await IssueService.getIssues(params)
    issues.value = result.items
    pagination.total = result.totalCount
  } catch (error: any) {
    console.error('搜索问题失败:', error)
    ElMessage.error('搜索问题失败')
  } finally {
    loading.value = false
  }
}

const loadMyAssignedIssues = async () => {
  try {
    loading.value = true
    issues.value = await IssueService.getAssignedIssues()
    pagination.total = issues.value.length
  } catch (error: any) {
    console.error('加载分配给我的问题失败:', error)
    ElMessage.error('加载分配给我的问题失败')
  } finally {
    loading.value = false
  }
}

const loadMyReportedIssues = async () => {
  try {
    loading.value = true
    issues.value = await IssueService.getReportedIssues()
    pagination.total = issues.value.length
  } catch (error: any) {
    console.error('加载我报告的问题失败:', error)
    ElMessage.error('加载我报告的问题失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await loadStatistics()
  if (viewMode.value === 'my-assigned') {
    await loadMyAssignedIssues()
  } else if (viewMode.value === 'my-reported') {
    await loadMyReportedIssues()
  } else {
    await searchIssues()
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    projectId: undefined,
    status: '',
    priority: '',
    issueType: '',
    assignedTo: undefined,
    pageIndex: 1,
    pageSize: 20
  })
  pagination.pageIndex = 1
  searchIssues()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  searchIssues()
}

const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  searchIssues()
}

const showCreateDialog = () => {
  isEditing.value = false
  resetForm()
  dialogVisible.value = true
}

const editIssue = (issue: Issue) => {
  isEditing.value = true
  form.id = issue.id
  form.projectId = issue.projectId
  form.title = issue.title
  form.description = issue.description
  form.issueType = issue.issueType
  form.priority = issue.priority
  form.status = issue.status
  form.assignedTo = issue.assignedTo
  form.labels = issue.labels?.join(', ') || ''
  dialogVisible.value = true
  detailDialogVisible.value = false
}

const resetForm = () => {
  Object.assign(form, {
    id: 0,
    projectId: 0,
    title: '',
    description: '',
    issueType: 'Bug',
    priority: 'Medium',
    status: 'Open',
    assignedTo: undefined,
    labels: ''
  })
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value) {
      const updateRequest: UpdateIssueRequest = {
        title: form.title,
        description: form.description,
        issueType: form.issueType,
        priority: form.priority,
        status: form.status,
        assignedTo: form.assignedTo,
        labels: form.labels
      }
      await IssueService.updateIssue(form.id, updateRequest)
      ElMessage.success('问题更新成功')
    } else {
      const createRequest: CreateIssueRequest = {
        projectId: form.projectId,
        title: form.title,
        description: form.description,
        issueType: form.issueType,
        priority: form.priority,
        assignedTo: form.assignedTo,
        labels: form.labels
      }
      await IssueService.createIssue(createRequest)
      ElMessage.success('问题创建成功')
    }

    dialogVisible.value = false
    await refreshData()
  } catch (error: any) {
    console.error('提交表单失败:', error)
    ElMessage.error(isEditing.value ? '更新问题失败' : '创建问题失败')
  } finally {
    submitting.value = false
  }
}

const viewIssueDetail = (issue: Issue) => {
  selectedIssue.value = issue
  detailDialogVisible.value = true
}

const changeStatus = async (issue: Issue, newStatus: string) => {
  try {
    await IssueService.updateIssueStatus(issue.id, newStatus)
    ElMessage.success('状态更新成功')
    await refreshData()
  } catch (error: any) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

const assignIssue = async (issue: Issue) => {
  try {
    const { value: assignedTo } = await ElMessageBox.prompt(
      '请选择要分配的用户ID（留空表示取消分配）',
      '重新分配问题',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d*$/,
        inputErrorMessage: '请输入有效的用户ID'
      }
    )

    const userId = assignedTo ? parseInt(assignedTo) : undefined
    await IssueService.assignIssue(issue.id, userId)
    ElMessage.success('分配成功')
    await refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('分配问题失败:', error)
      ElMessage.error('分配问题失败')
    }
  }
}

const deleteIssue = async (issue: Issue) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除问题 "${issue.title}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await IssueService.deleteIssue(issue.id)
    ElMessage.success('问题删除成功')
    await refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除问题失败:', error)
      ElMessage.error('删除问题失败')
    }
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.issue-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #909399;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.statistics-cards {
  margin-bottom: 20px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.open {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.progress {
          background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        &.resolved {
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
      }

      .stat-info {
        .stat-number {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-actions {
      display: flex;
      gap: 12px;
    }
  }

  .issue-title {
    .issue-labels {
      margin-top: 4px;
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.issue-detail {
  .issue-description {
    white-space: pre-wrap;
    line-height: 1.6;
    color: #606266;
  }
}

.text-gray-400 {
  color: #a0aec0;
}
</style>
