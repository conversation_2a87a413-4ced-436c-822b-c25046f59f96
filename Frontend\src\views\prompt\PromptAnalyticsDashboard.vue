<template>
  <div class="prompt-analytics-dashboard">
    <div class="page-header">
      <h1>AI Prompt 分析仪表板</h1>
      <p>深入了解您的AI提示词使用情况和效果分析</p>
    </div>

    <!-- 时间范围选择器 -->
    <div class="time-range-selector">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="onDateRangeChange"
        class="date-picker"
      />
      
      <div class="quick-ranges">
        <el-button 
          v-for="range in quickRanges" 
          :key="range.key"
          :type="selectedRange === range.key ? 'primary' : ''"
          size="small"
          @click="selectQuickRange(range)"
        >
          {{ range.label }}
        </el-button>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon usage">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewData.totalUsage.toLocaleString() }}</div>
              <div class="card-label">总使用次数</div>
              <div class="card-trend" :class="{ positive: overviewData.usageTrend > 0 }">
                <el-icon><ArrowUp v-if="overviewData.usageTrend > 0" /><ArrowDown v-else /></el-icon>
                {{ Math.abs(overviewData.usageTrend).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ (overviewData.successRate * 100).toFixed(1) }}%</div>
              <div class="card-label">成功率</div>
              <div class="card-trend" :class="{ positive: overviewData.successTrend > 0 }">
                <el-icon><ArrowUp v-if="overviewData.successTrend > 0" /><ArrowDown v-else /></el-icon>
                {{ Math.abs(overviewData.successTrend).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon performance">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ overviewData.avgResponseTime.toFixed(0) }}ms</div>
              <div class="card-label">平均响应时间</div>
              <div class="card-trend" :class="{ positive: overviewData.responseTrend < 0 }">
                <el-icon><ArrowUp v-if="overviewData.responseTrend > 0" /><ArrowDown v-else /></el-icon>
                {{ Math.abs(overviewData.responseTrend).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="overview-card">
            <div class="card-icon cost">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">${{ overviewData.totalCost.toFixed(2) }}</div>
              <div class="card-label">总成本</div>
              <div class="card-trend" :class="{ positive: overviewData.costTrend < 0 }">
                <el-icon><ArrowUp v-if="overviewData.costTrend > 0" /><ArrowDown v-else /></el-icon>
                {{ Math.abs(overviewData.costTrend).toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 使用趋势图 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>使用趋势</h3>
              <el-select v-model="usageTrendMetric" size="small" style="width: 120px">
                <el-option label="使用次数" value="count" />
                <el-option label="成功率" value="successRate" />
                <el-option label="响应时间" value="responseTime" />
              </el-select>
            </div>
            <div class="chart-container">
              <UsageTrendChart 
                :data="usageTrendData" 
                :metric="usageTrendMetric"
                :loading="chartsLoading"
              />
            </div>
          </div>
        </el-col>

        <!-- 提供商分布 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>AI提供商分布</h3>
            </div>
            <div class="chart-container">
              <ProviderDistributionChart 
                :data="providerData" 
                :loading="chartsLoading"
              />
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <!-- 任务类型统计 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>任务类型统计</h3>
            </div>
            <div class="chart-container">
              <TaskTypeChart 
                :data="taskTypeData" 
                :loading="chartsLoading"
              />
            </div>
          </div>
        </el-col>

        <!-- 热门模板排行 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>热门模板排行</h3>
              <el-button size="small" @click="viewAllTemplates">查看全部</el-button>
            </div>
            <div class="ranking-list">
              <div 
                v-for="(template, index) in popularTemplates" 
                :key="template.templateId"
                class="ranking-item"
                @click="viewTemplateDetails(template)"
              >
                <div class="rank-number" :class="getRankClass(index + 1)">
                  {{ index + 1 }}
                </div>
                <div class="template-info">
                  <div class="template-name">{{ template.templateName }}</div>
                  <div class="template-stats">
                    <span class="usage-count">{{ template.usageCount }}次使用</span>
                    <span class="success-rate">{{ (template.successRate * 100).toFixed(1) }}%成功率</span>
                  </div>
                </div>
                <div class="template-rating">
                  <el-rate
                    :model-value="template.averageRating"
                    disabled
                    size="small"
                    show-score
                    text-color="#ff9900"
                    score-template="{value}"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析区域 -->
    <div class="analysis-section">
      <el-tabs v-model="activeAnalysisTab" class="analysis-tabs">
        <el-tab-pane label="成本分析" name="cost">
          <CostAnalysisPanel
            :data="costAnalysisData"
            :loading="analysisLoading"
            @refresh="loadCostAnalysis"
          />
        </el-tab-pane>

        <el-tab-pane label="性能分析" name="performance">
          <PerformanceAnalysisPanel
            :data="performanceAnalysisData"
            :loading="analysisLoading"
            @refresh="loadPerformanceAnalysis"
          />
        </el-tab-pane>

        <el-tab-pane label="质量分析" name="quality">
          <QualityAnalysisPanel
            :data="qualityAnalysisData"
            :loading="analysisLoading"
            @refresh="loadQualityAnalysis"
          />
        </el-tab-pane>

        <el-tab-pane label="用户行为" name="behavior">
          <UserBehaviorPanel
            :data="userBehaviorData"
            :loading="analysisLoading"
            @refresh="loadUserBehavior"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 模板详情对话框 -->
    <TemplateAnalysisDialog
      :visible="showTemplateDialog"
      :template-id="selectedTemplateId"
      @update:visible="showTemplateDialog = $event"
      @view-template="handleViewTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  TrendCharts, 
  SuccessFilled, 
  Timer, 
  Money, 
  ArrowUp, 
  ArrowDown 
} from '@element-plus/icons-vue'
import { PromptAnalyticsService } from '@/services/promptAnalyticsService'
import UsageTrendChart from './components/UsageTrendChart.vue'
import ProviderDistributionChart from './components/ProviderDistributionChart.vue'
import TaskTypeChart from './components/TaskTypeChart.vue'
import CostAnalysisPanel from './components/CostAnalysisPanel.vue'
import PerformanceAnalysisPanel from './components/PerformanceAnalysisPanel.vue'
import QualityAnalysisPanel from './components/QualityAnalysisPanel.vue'
import UserBehaviorPanel from './components/UserBehaviorPanel.vue'
import TemplateAnalysisDialog from './components/TemplateAnalysisDialog.vue'

// 服务
const analyticsService = new PromptAnalyticsService()

// 响应式数据
const loading = ref(false)
const chartsLoading = ref(false)
const analysisLoading = ref(false)
const dateRange = ref<[Date, Date]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
  new Date()
])
const selectedRange = ref('30d')
const usageTrendMetric = ref('count')
const activeAnalysisTab = ref('cost')
const showTemplateDialog = ref(false)
const selectedTemplateId = ref(0)

// 数据
const overviewData = reactive({
  totalUsage: 0,
  successRate: 0,
  avgResponseTime: 0,
  totalCost: 0,
  usageTrend: 0,
  successTrend: 0,
  responseTrend: 0,
  costTrend: 0
})

const usageTrendData = ref<any[]>([])
const providerData = ref<any[]>([])
const taskTypeData = ref<any[]>([])
const popularTemplates = ref<any[]>([])

// 分析数据
const costAnalysisData = ref<any>({})
const performanceAnalysisData = ref<any>({})
const qualityAnalysisData = ref<any>({})
const userBehaviorData = ref<any>({})

// 快速时间范围选项
const quickRanges = [
  { key: '7d', label: '最近7天', days: 7 },
  { key: '30d', label: '最近30天', days: 30 },
  { key: '90d', label: '最近90天', days: 90 },
  { key: '1y', label: '最近1年', days: 365 }
]

// 生命周期
onMounted(() => {
  loadDashboardData()
})

// 方法
const loadDashboardData = async () => {
  loading.value = true
  chartsLoading.value = true
  
  try {
    const [startDate, endDate] = dateRange.value
    
    // 并行加载所有数据
    const [
      overallAnalytics,
      popularRanking,
      providerStats,
      taskTypeStats
    ] = await Promise.all([
      analyticsService.getOverallUsageAnalytics(startDate, endDate),
      analyticsService.getPopularTemplatesRanking(10, startDate, endDate),
      analyticsService.getProviderUsageStats(startDate, endDate),
      analyticsService.getTaskTypeUsageStats(startDate, endDate)
    ])

    // 更新概览数据
    Object.assign(overviewData, {
      totalUsage: overallAnalytics.totalUsage,
      successRate: overallAnalytics.overallSuccessRate,
      avgResponseTime: overallAnalytics.averageResponseTime,
      totalCost: overallAnalytics.totalCost,
      // 趋势数据需要额外计算
      usageTrend: 5.2, // 示例数据
      successTrend: 2.1,
      responseTrend: -3.5,
      costTrend: 1.8
    })

    // 更新图表数据
    usageTrendData.value = overallAnalytics.dailyTrends
    popularTemplates.value = popularRanking
    providerData.value = providerStats
    taskTypeData.value = taskTypeStats

  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    chartsLoading.value = false
  }
}

const onDateRangeChange = () => {
  selectedRange.value = ''
  loadDashboardData()
}

const selectQuickRange = (range: any) => {
  selectedRange.value = range.key
  const endDate = new Date()
  const startDate = new Date(Date.now() - range.days * 24 * 60 * 60 * 1000)
  dateRange.value = [startDate, endDate]
  loadDashboardData()
}

const getRankClass = (rank: number) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  return ''
}

const viewTemplateDetails = (template: any) => {
  selectedTemplateId.value = template.templateId
  showTemplateDialog.value = true
}

const viewAllTemplates = () => {
  // 跳转到模板管理页面
  // router.push('/prompt/templates')
}

// 分析数据加载方法
const loadCostAnalysis = async () => {
  try {
    // 模拟数据
    costAnalysisData.value = {
      totalCost: 1234.56,
      monthlyCost: 456.78,
      avgCostPerRequest: 0.0234,
      costGrowthRate: 12.5,
      costTrend: [
        { date: '2024-06-01', cost: 45.23 },
        { date: '2024-06-02', cost: 52.18 },
        { date: '2024-06-03', cost: 48.76 }
      ],
      providerCosts: [
        { provider: 'OpenAI', cost: 567.89, percentage: 46.0, requests: 1234 },
        { provider: 'Claude', cost: 345.67, percentage: 28.0, requests: 890 }
      ],
      suggestions: [
        { id: 1, title: '优化提示词长度', description: '减少不必要的提示词内容可以降低成本', type: 'warning' }
      ]
    }
  } catch (error) {
    console.error('加载成本分析失败:', error)
  }
}

const loadPerformanceAnalysis = async () => {
  try {
    // 模拟数据
    performanceAnalysisData.value = {
      avgResponseTime: 2340,
      successRate: 92.3,
      p95ResponseTime: 4500,
      errorRate: 2.1,
      responseTimeDistribution: [
        { name: '< 1s', value: 45 },
        { name: '1-3s', value: 123 },
        { name: '3-5s', value: 67 },
        { name: '> 5s', value: 23 }
      ],
      providerPerformance: [
        { provider: 'OpenAI', avgResponseTime: 2100, successRate: 94.5, p95ResponseTime: 4200, errorRate: 1.8 },
        { provider: 'Claude', avgResponseTime: 2580, successRate: 89.7, p95ResponseTime: 4800, errorRate: 2.5 }
      ],
      performanceTrend: [
        { date: '2024-06-01', avgResponseTime: 2200, successRate: 93.2 },
        { date: '2024-06-02', avgResponseTime: 2340, successRate: 92.8 }
      ],
      suggestions: [
        { id: 1, title: '优化模型选择', description: '考虑使用更快的模型版本', type: 'info' }
      ]
    }
  } catch (error) {
    console.error('加载性能分析失败:', error)
  }
}

const loadQualityAnalysis = async () => {
  try {
    // 模拟数据
    qualityAnalysisData.value = {
      avgQualityScore: 8.7,
      highQualityRate: 89.5,
      userSatisfaction: 91.2,
      retryRate: 5.8,
      qualityDistribution: [
        { range: '9-10分', count: 45 },
        { range: '7-8分', count: 123 },
        { range: '5-6分', count: 32 },
        { range: '< 5分', count: 8 }
      ],
      taskQuality: [
        { taskType: '需求分析', avgScore: 8.9, highQualityRate: 92.3, sampleCount: 156 },
        { taskType: '代码生成', avgScore: 8.2, highQualityRate: 85.7, sampleCount: 234 }
      ],
      qualityTrend: [
        { date: '2024-06-01', avgScore: 8.5, highQualityRate: 87.2, userSatisfaction: 89.8 },
        { date: '2024-06-02', avgScore: 8.7, highQualityRate: 89.5, userSatisfaction: 91.2 }
      ],
      qualityIssues: [
        { issue: '回答不完整', frequency: 12, impact: '中', suggestion: '优化提示词结构' },
        { issue: '格式不规范', frequency: 8, impact: '低', suggestion: '添加格式要求' }
      ],
      suggestions: [
        { id: 1, title: '改进提示词质量', description: '定期审查和优化提示词模板', type: 'success' }
      ]
    }
  } catch (error) {
    console.error('加载质量分析失败:', error)
  }
}

const loadUserBehavior = async () => {
  try {
    // 模拟数据
    userBehaviorData.value = {
      activeUsers: 156,
      newUsers: 23,
      avgSessionDuration: 45,
      retentionRate: 78.5,
      activityTrend: [
        { date: '2024-06-01', activeUsers: 145, newUsers: 18 },
        { date: '2024-06-02', activeUsers: 156, newUsers: 23 }
      ],
      featureUsage: [
        { name: '需求分析', value: 45 },
        { name: '代码生成', value: 32 },
        { name: '文档编写', value: 23 }
      ],
      userJourney: [
        { path: '登录 -> 需求分析 -> 代码生成', userCount: 89, percentage: 34.2, avgDuration: 25 },
        { path: '登录 -> 文档编写', userCount: 67, percentage: 25.8, avgDuration: 18 }
      ],
      positiveFeedback: 234,
      negativeFeedback: 45,
      feedbackRate: 67.8,
      userSegments: [
        { segment: '高频用户', userCount: 45, avgUsage: 8.5, satisfaction: 4.2 },
        { segment: '普通用户', userCount: 123, avgUsage: 3.2, satisfaction: 3.8 }
      ],
      insights: [
        { id: 1, title: '用户活跃度提升', description: '本月新用户增长23%', type: 'success' }
      ]
    }
  } catch (error) {
    console.error('加载用户行为分析失败:', error)
  }
}

const handleViewTemplate = (templateId: number) => {
  selectedTemplateId.value = templateId
  showTemplateDialog.value = true
}
</script>

<style scoped>
.prompt-analytics-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #303133;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.date-picker {
  width: 300px;
}

.quick-ranges {
  display: flex;
  gap: 8px;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.usage {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.performance {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.cost {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #f56c6c;
}

.card-trend.positive {
  color: #67c23a;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 0;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.chart-container {
  padding: 20px 24px 24px;
  height: 300px;
}

.ranking-list {
  padding: 0 24px 24px;
  max-height: 400px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.ranking-item:hover {
  background-color: #f8f9fa;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  margin-right: 16px;
  background: #f0f0f0;
  color: #666;
}

.rank-number.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #b8860b;
}

.rank-number.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #696969;
}

.rank-number.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #8b4513;
}

.template-info {
  flex: 1;
  margin-right: 16px;
}

.template-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.template-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.template-rating {
  min-width: 120px;
}

.analysis-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analysis-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 24px;
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;
  }
  
  :deep(.el-tabs__content) {
    padding: 24px;
  }
}
</style>
