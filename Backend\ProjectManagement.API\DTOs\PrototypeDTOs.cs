namespace ProjectManagement.API.DTOs;

/// <summary>
/// 原型图创建请求DTO
/// </summary>
public class CreatePrototypeDto
{
    /// <summary>
    /// 所属项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 原型图名称
    /// </summary>
    public string PrototypeName { get; set; } = string.Empty;

    /// <summary>
    /// 原型图类型：Wireframe、UserFlow、ComponentDiagram、InteractionFlow
    /// </summary>
    public string PrototypeType { get; set; } = "Wireframe";

    /// <summary>
    /// Mermaid格式的原型图定义代码
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 原型图描述说明
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 目标用户群体
    /// </summary>
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 页面/功能模块信息，JSON格式
    /// </summary>
    public string? PageModules { get; set; }

    /// <summary>
    /// 交互流程信息，JSON格式
    /// </summary>
    public string? InteractionFlows { get; set; }

    /// <summary>
    /// UI组件信息，JSON格式
    /// </summary>
    public string? UIComponents { get; set; }

    /// <summary>
    /// 设备类型：Desktop、Mobile、Tablet、Responsive
    /// </summary>
    public string? DeviceType { get; set; } = "Desktop";

    /// <summary>
    /// 保真度级别：Low、Medium、High
    /// </summary>
    public string? FidelityLevel { get; set; } = "Low";
}

/// <summary>
/// 原型图更新请求DTO
/// </summary>
public class UpdatePrototypeDto
{
    /// <summary>
    /// 原型图名称
    /// </summary>
    public string? PrototypeName { get; set; }

    /// <summary>
    /// 原型图类型
    /// </summary>
    public string? PrototypeType { get; set; }

    /// <summary>
    /// Mermaid格式的原型图定义代码
    /// </summary>
    public string? MermaidDefinition { get; set; }

    /// <summary>
    /// 原型图描述说明
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 目标用户群体
    /// </summary>
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 页面/功能模块信息，JSON格式
    /// </summary>
    public string? PageModules { get; set; }

    /// <summary>
    /// 交互流程信息，JSON格式
    /// </summary>
    public string? InteractionFlows { get; set; }

    /// <summary>
    /// UI组件信息，JSON格式
    /// </summary>
    public string? UIComponents { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string? DeviceType { get; set; }

    /// <summary>
    /// 保真度级别
    /// </summary>
    public string? FidelityLevel { get; set; }
}

/// <summary>
/// 验证Mermaid语法请求DTO
/// </summary>
public class ValidateMermaidRequest
{
    /// <summary>
    /// Mermaid定义代码
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;
}

/// <summary>
/// 原型图响应DTO
/// </summary>
public class PrototypeDto
{
    /// <summary>
    /// 原型图ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID
    /// </summary>
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 原型图名称
    /// </summary>
    public string PrototypeName { get; set; } = string.Empty;

    /// <summary>
    /// 原型图类型
    /// </summary>
    public string PrototypeType { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的原型图定义代码
    /// </summary>
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 原型图描述说明
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 目标用户群体
    /// </summary>
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 页面/功能模块信息
    /// </summary>
    public string? PageModules { get; set; }

    /// <summary>
    /// 交互流程信息
    /// </summary>
    public string? InteractionFlows { get; set; }

    /// <summary>
    /// UI组件信息
    /// </summary>
    public string? UIComponents { get; set; }

    /// <summary>
    /// 原型图版本号
    /// </summary>
    public string PrototypeVersion { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型
    /// </summary>
    public string? DeviceType { get; set; }

    /// <summary>
    /// 保真度级别
    /// </summary>
    public string? FidelityLevel { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    public string? RequirementDocumentTitle { get; set; }
}

/// <summary>
/// AI生成原型图请求DTO
/// </summary>
public class GeneratePrototypeDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求描述
    /// </summary>
    public string Requirements { get; set; } = string.Empty;

    /// <summary>
    /// 原型图类型
    /// </summary>
    public string PrototypeType { get; set; } = "Wireframe";

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; } = "Desktop";

    /// <summary>
    /// 保真度级别
    /// </summary>
    public string FidelityLevel { get; set; } = "Low";

    /// <summary>
    /// 目标用户群体
    /// </summary>
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 首选AI模型
    /// </summary>
    public string? PreferredModel { get; set; }
}

/// <summary>
/// 原型图列表查询DTO
/// </summary>
public class PrototypeQueryDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// 原型图类型
    /// </summary>
    public string? PrototypeType { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string? DeviceType { get; set; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchKeyword { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;
}
