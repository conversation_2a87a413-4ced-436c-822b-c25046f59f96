-- =============================================
-- 创建流程控制类型表
-- 用于维护UI自动化序列的流程控制类型
-- =============================================

USE [ProjectManagementAI]
GO

-- 检查表是否存在，如果存在则删除
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[FlowControlTypes]') AND type in (N'U'))
BEGIN
    DROP TABLE [dbo].[FlowControlTypes]
    PRINT '已删除现有的 FlowControlTypes 表'
END
GO

-- 创建流程控制类型表
CREATE TABLE [dbo].[FlowControlTypes] (
    [Id] int IDENTITY(1,1) NOT NULL,
    [Value] nvarchar(50) NOT NULL,
    [Label] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [Icon] nvarchar(100) NULL,
    [Color] nvarchar(20) NULL,
    [SortOrder] int NOT NULL DEFAULT 0,
    [IsActive] bit NOT NULL DEFAULT 1,
    [IsBuiltIn] bit NOT NULL DEFAULT 0,
    [ParameterSchema] nvarchar(max) NULL,
    [ExecutionType] nvarchar(50) NULL,
    [RequiresTarget] bit NOT NULL DEFAULT 0,
    [CanNest] bit NOT NULL DEFAULT 0,

    -- BaseEntity 字段
    [CreatedTime] datetime2 NOT NULL DEFAULT GETDATE(),
    [UpdatedTime] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL DEFAULT 0,
    [DeletedTime] datetime2 NULL,
    [DeletedBy] int NULL,
    [Version] int NOT NULL DEFAULT 1,
    [Remarks] nvarchar(500) NULL,

    CONSTRAINT [PK_FlowControlTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [UK_FlowControlTypes_Value] UNIQUE NONCLUSTERED ([Value] ASC)
)
GO

-- 添加字段注释
EXEC sp_addextendedproperty 'MS_Description', '流程控制类型值', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Value'
EXEC sp_addextendedproperty 'MS_Description', '显示名称', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Label'
EXEC sp_addextendedproperty 'MS_Description', '流程控制描述', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Description'
EXEC sp_addextendedproperty 'MS_Description', '图标名称', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Icon'
EXEC sp_addextendedproperty 'MS_Description', '颜色标识', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Color'
EXEC sp_addextendedproperty 'MS_Description', '排序顺序', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'SortOrder'
EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'IsActive'
EXEC sp_addextendedproperty 'MS_Description', '是否内置类型', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'IsBuiltIn'
EXEC sp_addextendedproperty 'MS_Description', '参数架构JSON', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'ParameterSchema'
EXEC sp_addextendedproperty 'MS_Description', '执行类型(condition/loop/branch/jump/exit)', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'ExecutionType'
EXEC sp_addextendedproperty 'MS_Description', '是否需要目标步骤', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'RequiresTarget'
EXEC sp_addextendedproperty 'MS_Description', '是否可以嵌套', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'CanNest'
EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'CreatedTime'
EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'UpdatedTime'
EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'CreatedBy'
EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'UpdatedBy'
EXEC sp_addextendedproperty 'MS_Description', '是否已删除', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'IsDeleted'
EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'DeletedTime'
EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'DeletedBy'
EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Version'
EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'FlowControlTypes', 'COLUMN', 'Remarks'
GO

-- 创建索引
CREATE NONCLUSTERED INDEX [IX_FlowControlTypes_IsActive] ON [dbo].[FlowControlTypes] ([IsActive] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_FlowControlTypes_SortOrder] ON [dbo].[FlowControlTypes] ([SortOrder] ASC)
GO

CREATE NONCLUSTERED INDEX [IX_FlowControlTypes_ExecutionType] ON [dbo].[FlowControlTypes] ([ExecutionType] ASC)
GO

-- 插入初始数据
PRINT '开始插入流程控制类型初始数据...'

-- 流程控制类型
INSERT INTO [dbo].[FlowControlTypes] ([Value], [Label], [Description], [Icon], [Color], [SortOrder], [IsActive], [IsBuiltIn], [ParameterSchema], [ExecutionType], [RequiresTarget], [CanNest], [CreatedTime])
VALUES 
    ('condition', '条件判断', '根据条件决定是否执行后续步骤', 'el-icon-question', 'warning', 1, 1, 1, '{"expression": "string", "trueAction": "string", "falseAction": "string"}', 'condition', 0, 1, GETDATE()),
    ('image_condition', '图像条件', '如果图片A存在，则点击图片B', 'el-icon-picture', 'warning', 2, 1, 1, '{"conditionImage": "string", "actionImage": "string", "timeout": "number"}', 'condition', 0, 0, GETDATE()),
    ('region_image_condition', '区域图像条件', '在图片A区域内检测图片B是否存在', 'el-icon-crop', 'warning', 3, 1, 1, '{"regionImage": "string", "targetImage": "string", "region": "object"}', 'condition', 0, 0, GETDATE()),
    ('loop', '循环开始', '开始循环执行一组步骤', 'el-icon-refresh', 'success', 4, 1, 1, '{"count": "number", "variable": "string", "condition": "string"}', 'loop', 0, 1, GETDATE()),
    ('loop_end', '循环结束', '标记循环体的结束', 'el-icon-refresh-right', 'success', 5, 1, 1, '{}', 'loop', 0, 0, GETDATE()),
    ('branch', '分支执行', '根据条件选择不同的执行分支', 'el-icon-share', 'warning', 6, 1, 1, '{"condition": "string", "branches": "array"}', 'branch', 0, 1, GETDATE()),
    ('jump', '跳转', '跳转到指定的步骤', 'el-icon-right', 'primary', 7, 1, 1, '{"targetStep": "number", "condition": "string"}', 'jump', 1, 0, GETDATE()),
    ('exit', '退出', '退出序列执行', 'el-icon-close', 'danger', 8, 1, 1, '{"code": "number", "message": "string"}', 'exit', 0, 0, GETDATE())
GO

PRINT '流程控制类型初始数据插入完成'

-- 验证数据
SELECT COUNT(*) as '插入的记录数' FROM [dbo].[FlowControlTypes] WHERE [IsDeleted] = 0
GO

PRINT 'FlowControlTypes 表创建完成'

-- 添加序列JSON字段到UIAutoMationTemplateSequences表
ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
ADD [SequenceJson] NVARCHAR(MAX) NULL;