using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Enums;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户对象</returns>
    Task<User?> GetByUsernameAsync(string username);

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <returns>用户对象</returns>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsUsernameExistsAsync(string username, int? excludeUserId = null);

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="excludeUserId">排除的用户ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsEmailExistsAsync(string email, int? excludeUserId = null);

    /// <summary>
    /// 根据角色获取用户列表
    /// </summary>
    /// <param name="role">用户角色</param>
    /// <returns>用户列表</returns>
    Task<List<User>> GetUsersByRoleAsync(UserRole role);

    /// <summary>
    /// 根据状态获取用户列表
    /// </summary>
    /// <param name="status">用户状态</param>
    /// <returns>用户列表</returns>
    Task<List<User>> GetUsersByStatusAsync(UserStatus status);

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="loginTime">登录时间</param>
    /// <param name="loginIp">登录IP</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateLastLoginAsync(int userId, DateTime loginTime, string loginIp);

    /// <summary>
    /// 更新用户密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="passwordHash">密码哈希</param>
    /// <param name="salt">盐值</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdatePasswordAsync(int userId, string passwordHash, string salt);

    /// <summary>
    /// 验证用户邮箱
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>是否验证成功</returns>
    Task<bool> VerifyEmailAsync(int userId);

    /// <summary>
    /// 启用/禁用双因子认证
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="enabled">是否启用</param>
    /// <param name="secret">双因子认证密钥</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateTwoFactorAsync(int userId, bool enabled, string? secret = null);

    /// <summary>
    /// 更新用户偏好设置
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="preferences">偏好设置JSON</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdatePreferencesAsync(int userId, string preferences);

    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    Task<PagedResult<User>> SearchUsersAsync(string keyword, int pageIndex, int pageSize);

    /// <summary>
    /// 获取活跃用户统计
    /// </summary>
    /// <param name="days">天数</param>
    /// <returns>活跃用户数量</returns>
    Task<int> GetActiveUserCountAsync(int days = 30);

    /// <summary>
    /// 获取用户注册统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>注册用户数量</returns>
    Task<int> GetRegistrationCountAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 根据刷新令牌获取用户
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <returns>用户对象</returns>
    Task<User?> GetByRefreshTokenAsync(string refreshToken);
}
