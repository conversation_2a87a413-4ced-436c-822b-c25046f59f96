/**
 * 图表导出工具类
 * 支持将Mermaid图表导出为SVG、PNG、PDF格式
 */

import mermaid from 'mermaid'
import jsPDF from 'jspdf'

export interface ExportOptions {
  filename?: string
  width?: number
  height?: number
  backgroundColor?: string
  scale?: number
}

export class DiagramExporter {
  /**
   * 导出为SVG格式
   */
  static async exportToSVG(
    mermaidDefinition: string,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      const { backgroundColor = 'white' } = options

      // 生成唯一ID
      const id = `export-${Date.now()}`

      // 渲染Mermaid图表
      const { svg } = await mermaid.render(id, mermaidDefinition)

      // 添加背景色
      const svgWithBackground = this.addBackgroundToSVG(svg, backgroundColor)

      // 创建Blob
      const blob = new Blob([svgWithBackground], { type: 'image/svg+xml' })
      return blob

    } catch (error) {
      console.error('SVG导出失败:', error)
      throw new Error('SVG导出失败')
    }
  }

  /**
   * 导出为PNG格式
   */
  static async exportToPNG(
    mermaidDefinition: string,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      const {
        width = 1200,
        height = 800,
        backgroundColor = 'white',
        scale = 2
      } = options

      // 先获取SVG
      const svgBlob = await this.exportToSVG(mermaidDefinition, { backgroundColor })
      const svgText = await svgBlob.text()

      // 创建Canvas
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!

      // 设置Canvas尺寸
      canvas.width = width * scale
      canvas.height = height * scale

      // 设置背景色
      ctx.fillStyle = backgroundColor
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 创建Image对象
      const img = new Image()

      return new Promise((resolve, reject) => {
        img.onload = () => {
          try {
            // 计算缩放比例以适应Canvas
            const imgAspectRatio = img.width / img.height
            const canvasAspectRatio = canvas.width / canvas.height

            let drawWidth = canvas.width
            let drawHeight = canvas.height
            let offsetX = 0
            let offsetY = 0

            if (imgAspectRatio > canvasAspectRatio) {
              // 图片更宽，以宽度为准
              drawHeight = canvas.width / imgAspectRatio
              offsetY = (canvas.height - drawHeight) / 2
            } else {
              // 图片更高，以高度为准
              drawWidth = canvas.height * imgAspectRatio
              offsetX = (canvas.width - drawWidth) / 2
            }

            // 绘制图片
            ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight)

            // 转换为Blob
            canvas.toBlob((blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('PNG转换失败'))
              }
            }, 'image/png', 0.9)

          } catch (error) {
            reject(error)
          }
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }

        // 设置SVG数据URL
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(encodeURIComponent(svgText).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))))}`
        img.src = svgDataUrl
      })

    } catch (error) {
      console.error('PNG导出失败:', error)
      throw new Error('PNG导出失败')
    }
  }

  /**
   * 导出为PDF格式
   */
  static async exportToPDF(
    mermaidDefinition: string,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      const {
        width = 210, // A4宽度(mm)
        height = 297, // A4高度(mm)
        backgroundColor = 'white'
      } = options

      // 先获取PNG
      const pngBlob = await this.exportToPNG(mermaidDefinition, {
        width: 1200,
        height: 800,
        backgroundColor
      })

      // 创建PDF
      const pdf = new jsPDF({
        orientation: width > height ? 'landscape' : 'portrait',
        unit: 'mm',
        format: [width, height]
      })

      // 将PNG转换为DataURL
      const pngDataUrl = await this.blobToDataURL(pngBlob)

      // 计算图片在PDF中的尺寸
      const margin = 10
      const maxWidth = width - 2 * margin
      const maxHeight = height - 2 * margin

      // 添加图片到PDF
      pdf.addImage(
        pngDataUrl,
        'PNG',
        margin,
        margin,
        maxWidth,
        maxHeight,
        undefined,
        'FAST'
      )

      // 返回PDF Blob
      const pdfBlob = pdf.output('blob')
      return pdfBlob

    } catch (error) {
      console.error('PDF导出失败:', error)
      throw new Error('PDF导出失败')
    }
  }

  /**
   * 下载文件
   */
  static downloadBlob(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 为SVG添加背景色
   */
  private static addBackgroundToSVG(svg: string, backgroundColor: string): string {
    // 在SVG中添加背景矩形
    const svgElement = new DOMParser().parseFromString(svg, 'image/svg+xml')
    const svgNode = svgElement.documentElement

    // 获取SVG尺寸（这些变量保留用于未来可能的扩展）
    // const width = svgNode.getAttribute('width') || '100%'
    // const height = svgNode.getAttribute('height') || '100%'
    // const viewBox = svgNode.getAttribute('viewBox')

    // 创建背景矩形
    const rect = svgElement.createElementNS('http://www.w3.org/2000/svg', 'rect')
    rect.setAttribute('width', '100%')
    rect.setAttribute('height', '100%')
    rect.setAttribute('fill', backgroundColor)

    // 将背景矩形插入到第一个位置
    svgNode.insertBefore(rect, svgNode.firstChild)

    return new XMLSerializer().serializeToString(svgElement)
  }

  /**
   * 将Blob转换为DataURL
   */
  private static blobToDataURL(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }
}

/**
 * 导出图表的便捷函数
 */
export async function exportDiagram(
  mermaidDefinition: string,
  format: 'svg' | 'png' | 'pdf',
  filename: string,
  options: ExportOptions = {}
): Promise<void> {
  try {
    let blob: Blob
    const fullFilename = `${filename}.${format}`

    switch (format) {
      case 'svg':
        blob = await DiagramExporter.exportToSVG(mermaidDefinition, options)
        break
      case 'png':
        blob = await DiagramExporter.exportToPNG(mermaidDefinition, options)
        break
      case 'pdf':
        blob = await DiagramExporter.exportToPDF(mermaidDefinition, options)
        break
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }

    DiagramExporter.downloadBlob(blob, fullFilename)

  } catch (error) {
    console.error('导出失败:', error)
    throw error
  }
}
