#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright错误监控UI组件
集成到主UI中，提供浏览器错误监控功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
from datetime import datetime
from typing import Dict, List, Optional
import time

from playwright_error_monitor import PlaywrightErrorMonitorSync


class PlaywrightErrorUI:
    """Playwright错误监控UI"""
    
    def __init__(self, parent_frame, api_client=None):
        """
        初始化Playwright错误监控UI
        
        Args:
            parent_frame: 父容器
            api_client: API客户端（可选）
        """
        self.parent_frame = parent_frame
        self.api_client = api_client
        
        # Playwright监控器
        self.monitor: Optional[PlaywrightErrorMonitorSync] = None
        self.is_monitoring = False
        
        # 错误数据
        self.all_errors: List[Dict] = []
        
        # 创建UI
        self.create_ui()
        
        # 自动刷新定时器
        self.refresh_timer = None
        self.auto_refresh_interval = 2000  # 2秒
    
    def create_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎭 Playwright浏览器错误监控", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # 控制面板
        self.create_control_panel(main_frame)
        
        # 错误显示区域
        self.create_error_display(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：浏览器控制
        browser_frame = ttk.Frame(control_frame)
        browser_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 浏览器类型选择
        ttk.Label(browser_frame, text="浏览器:").pack(side=tk.LEFT)
        self.browser_var = tk.StringVar(value="chromium")
        browser_combo = ttk.Combobox(browser_frame, textvariable=self.browser_var,
                                   values=["chromium", "firefox", "webkit"],
                                   state="readonly", width=10)
        browser_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        # 无头模式
        self.headless_var = tk.BooleanVar(value=True)
        headless_check = ttk.Checkbutton(browser_frame, text="无头模式",
                                       variable=self.headless_var)
        headless_check.pack(side=tk.LEFT, padx=(0, 10))
        
        # 启动/停止按钮
        self.start_button = ttk.Button(browser_frame, text="🚀 启动监控",
                                     command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(browser_frame, text="🛑 停止监控",
                                    command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空错误按钮
        clear_button = ttk.Button(browser_frame, text="🗑️ 清空错误",
                                command=self.clear_errors)
        clear_button.pack(side=tk.LEFT)
        
        # 第二行：URL导航
        nav_frame = ttk.Frame(control_frame)
        nav_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(nav_frame, text="URL:").pack(side=tk.LEFT)
        self.url_var = tk.StringVar(value="http://localhost:3000")
        url_entry = ttk.Entry(nav_frame, textvariable=self.url_var, width=50)
        url_entry.pack(side=tk.LEFT, padx=(5, 10), fill=tk.X, expand=True)
        
        navigate_button = ttk.Button(nav_frame, text="🌐 导航",
                                   command=self.navigate_to_url)
        navigate_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 执行JS按钮
        js_button = ttk.Button(nav_frame, text="⚡ 执行JS",
                             command=self.show_js_dialog)
        js_button.pack(side=tk.LEFT)
    
    def create_error_display(self, parent):
        """创建错误显示区域"""
        display_frame = ttk.LabelFrame(parent, text="错误信息", padding=10)
        display_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 错误统计
        stats_frame = ttk.Frame(display_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="错误统计: 控制台: 0, 页面: 0, 网络: 0, 运行时: 0")
        self.stats_label.pack(side=tk.LEFT)
        
        # 自动刷新
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_refresh_check = ttk.Checkbutton(stats_frame, text="自动刷新",
                                           variable=self.auto_refresh_var,
                                           command=self.toggle_auto_refresh)
        auto_refresh_check.pack(side=tk.RIGHT)
        
        # 错误列表
        list_frame = ttk.Frame(display_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('时间', '类型', '级别', '消息', '位置')
        self.error_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        self.error_tree.heading('时间', text='时间')
        self.error_tree.heading('类型', text='类型')
        self.error_tree.heading('级别', text='级别')
        self.error_tree.heading('消息', text='消息')
        self.error_tree.heading('位置', text='位置')
        
        self.error_tree.column('时间', width=150)
        self.error_tree.column('类型', width=100)
        self.error_tree.column('级别', width=80)
        self.error_tree.column('消息', width=400)
        self.error_tree.column('位置', width=200)
        
        # 滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.error_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.error_tree.xview)
        self.error_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.error_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 双击事件
        self.error_tree.bind('<Double-1>', self.show_error_details)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 监控状态指示器
        self.monitor_status_label = ttk.Label(status_frame, text="●", foreground="red")
        self.monitor_status_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Label(status_frame, text="监控状态:").pack(side=tk.RIGHT)
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            messagebox.showwarning("警告", "监控已在运行中")
            return
        
        def start_thread():
            try:
                self.status_label.config(text="正在启动Playwright监控器...")
                
                # 创建监控器
                self.monitor = PlaywrightErrorMonitorSync(headless=self.headless_var.get())
                
                # 设置错误回调
                self.monitor.set_error_callback(self.on_error_received)
                
                # 启动监控器
                browser_type = self.browser_var.get()
                success = self.monitor.start(browser_type)
                
                if success:
                    self.is_monitoring = True
                    
                    # 更新UI状态
                    self.parent_frame.after(0, self.update_monitoring_ui, True)
                    self.parent_frame.after(0, lambda: self.status_label.config(text="监控器启动成功"))
                    
                    # 启动自动刷新
                    if self.auto_refresh_var.get():
                        self.parent_frame.after(0, self.start_auto_refresh)
                    
                    print("✅ Playwright监控器启动成功")
                else:
                    self.parent_frame.after(0, lambda: self.status_label.config(text="监控器启动失败"))
                    messagebox.showerror("错误", "Playwright监控器启动失败")
                    
            except Exception as e:
                error_msg = f"启动监控器失败: {e}"
                self.parent_frame.after(0, lambda: self.status_label.config(text=error_msg))
                messagebox.showerror("错误", error_msg)
        
        # 在后台线程启动
        threading.Thread(target=start_thread, daemon=True).start()
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        try:
            self.is_monitoring = False
            
            # 停止自动刷新
            self.stop_auto_refresh()
            
            # 停止监控器
            if self.monitor:
                self.monitor.stop()
                self.monitor = None
            
            # 更新UI状态
            self.update_monitoring_ui(False)
            self.status_label.config(text="监控已停止")
            
            print("✅ Playwright监控器已停止")
            
        except Exception as e:
            error_msg = f"停止监控器失败: {e}"
            self.status_label.config(text=error_msg)
            messagebox.showerror("错误", error_msg)
    
    def update_monitoring_ui(self, is_running: bool):
        """更新监控UI状态"""
        if is_running:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.monitor_status_label.config(foreground="green")
        else:
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.monitor_status_label.config(foreground="red")
    
    def navigate_to_url(self):
        """导航到指定URL"""
        if not self.monitor or not self.is_monitoring:
            messagebox.showwarning("警告", "请先启动监控器")
            return
        
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("警告", "请输入URL")
            return
        
        def navigate_thread():
            try:
                self.parent_frame.after(0, lambda: self.status_label.config(text=f"正在导航到: {url}"))
                success = self.monitor.navigate_to(url)
                
                if success:
                    self.parent_frame.after(0, lambda: self.status_label.config(text=f"成功导航到: {url}"))
                else:
                    self.parent_frame.after(0, lambda: self.status_label.config(text=f"导航失败: {url}"))
                    
            except Exception as e:
                error_msg = f"导航失败: {e}"
                self.parent_frame.after(0, lambda: self.status_label.config(text=error_msg))
        
        threading.Thread(target=navigate_thread, daemon=True).start()
    
    def show_js_dialog(self):
        """显示JavaScript执行对话框"""
        if not self.monitor or not self.is_monitoring:
            messagebox.showwarning("警告", "请先启动监控器")
            return
        
        # 创建对话框
        dialog = tk.Toplevel(self.parent_frame)
        dialog.title("执行JavaScript")
        dialog.geometry("600x400")
        dialog.transient(self.parent_frame)
        dialog.grab_set()
        
        # JavaScript输入框
        ttk.Label(dialog, text="JavaScript代码:").pack(pady=(10, 5))
        js_text = scrolledtext.ScrolledText(dialog, height=15, width=70)
        js_text.pack(padx=10, pady=(0, 10), fill=tk.BOTH, expand=True)
        
        # 预设一些示例代码
        js_text.insert(tk.END, "// 示例：产生控制台错误\nconsole.error('测试错误消息');\n\n")
        js_text.insert(tk.END, "// 示例：抛出异常\n// throw new Error('测试异常');\n\n")
        js_text.insert(tk.END, "// 示例：获取页面标题\n// document.title")
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def execute_js():
            js_code = js_text.get(1.0, tk.END).strip()
            if not js_code:
                messagebox.showwarning("警告", "请输入JavaScript代码")
                return
            
            try:
                result = self.monitor.execute_javascript(js_code)
                messagebox.showinfo("执行结果", f"结果: {result}")
            except Exception as e:
                messagebox.showerror("执行失败", f"JavaScript执行失败: {e}")
        
        ttk.Button(button_frame, text="执行", command=execute_js).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.LEFT)
    
    def on_error_received(self, error_info: Dict):
        """接收到错误时的回调"""
        # 在主线程中更新UI
        self.parent_frame.after(0, self.add_error_to_display, error_info)
    
    def add_error_to_display(self, error_info: Dict):
        """添加错误到显示列表"""
        self.all_errors.append(error_info)
        
        # 格式化显示信息
        timestamp = error_info.get('timestamp', '')
        if timestamp:
            timestamp = timestamp.split('T')[1].split('.')[0]  # 只显示时间部分
        
        error_type = error_info.get('type', '')
        level = error_info.get('level', error_info.get('name', ''))
        
        # 获取消息
        message = error_info.get('text', error_info.get('message', ''))
        if not message and 'args' in error_info:
            message = ' '.join(map(str, error_info['args']))
        
        # 获取位置信息
        location = ''
        if 'location' in error_info:
            loc = error_info['location']
            if isinstance(loc, dict):
                url = loc.get('url', '')
                line = loc.get('line', 0)
                if url and line:
                    location = f"{url}:{line}"
        elif 'url' in error_info:
            location = error_info['url']
        
        # 插入到树形控件
        self.error_tree.insert('', 0, values=(timestamp, error_type, level, message[:100], location))
        
        # 更新统计信息
        self.update_error_stats()
    
    def update_error_stats(self):
        """更新错误统计信息"""
        if not self.monitor:
            return
        
        try:
            errors = self.monitor.get_all_errors()
            console_count = len(errors.get('console_errors', []))
            page_count = len(errors.get('page_errors', []))
            network_count = len(errors.get('network_errors', []))
            runtime_count = len(errors.get('runtime_errors', []))
            
            stats_text = f"错误统计: 控制台: {console_count}, 页面: {page_count}, 网络: {network_count}, 运行时: {runtime_count}"
            self.stats_label.config(text=stats_text)
            
        except Exception as e:
            print(f"⚠️ 更新统计信息失败: {e}")
    
    def show_error_details(self, event):
        """显示错误详情"""
        selection = self.error_tree.selection()
        if not selection:
            return
        
        item = self.error_tree.item(selection[0])
        index = self.error_tree.index(selection[0])
        
        if index < len(self.all_errors):
            error_info = self.all_errors[-(index + 1)]  # 因为是倒序插入的
            
            # 创建详情对话框
            dialog = tk.Toplevel(self.parent_frame)
            dialog.title("错误详情")
            dialog.geometry("700x500")
            dialog.transient(self.parent_frame)
            
            # 显示详细信息
            details_text = scrolledtext.ScrolledText(dialog, height=25, width=80)
            details_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
            
            details_text.insert(tk.END, json.dumps(error_info, indent=2, ensure_ascii=False))
            details_text.config(state=tk.DISABLED)
    
    def clear_errors(self):
        """清空错误记录"""
        # 清空显示
        for item in self.error_tree.get_children():
            self.error_tree.delete(item)
        
        # 清空数据
        self.all_errors.clear()
        
        # 清空监控器中的错误
        if self.monitor:
            self.monitor.clear_errors()
        
        # 更新统计
        self.update_error_stats()
        
        self.status_label.config(text="错误记录已清空")
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        if self.auto_refresh_var.get() and self.is_monitoring:
            self.update_error_stats()
            self.refresh_timer = self.parent_frame.after(self.auto_refresh_interval, self.start_auto_refresh)
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        if self.refresh_timer:
            self.parent_frame.after_cancel(self.refresh_timer)
            self.refresh_timer = None
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.title("Playwright错误监控测试")
    root.geometry("1200x800")
    
    app = PlaywrightErrorUI(root)
    
    root.mainloop()
