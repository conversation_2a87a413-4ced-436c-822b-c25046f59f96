<template>
  <div class="cost-analysis-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>成本分析</span>
          <el-button type="text" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else class="cost-content">
        <!-- 总成本概览 -->
        <div class="cost-overview">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总成本" :value="data.totalCost" prefix="¥" :precision="2" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="本月成本" :value="data.monthlyCost" prefix="¥" :precision="2" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均单次成本" :value="data.avgCostPerRequest" prefix="¥" :precision="4" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成本增长率" :value="data.costGrowthRate" suffix="%" :precision="1">
                <template #suffix>
                  <span :class="data.costGrowthRate >= 0 ? 'growth-positive' : 'growth-negative'">
                    {{ data.costGrowthRate >= 0 ? '+' : '' }}{{ data.costGrowthRate }}%
                  </span>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>

        <!-- 成本趋势图 -->
        <div class="cost-trend">
          <h4>成本趋势</h4>
          <div ref="costTrendChart" class="chart-container"></div>
        </div>

        <!-- 提供商成本分布 -->
        <div class="provider-cost">
          <h4>提供商成本分布</h4>
          <el-table :data="data.providerCosts" style="width: 100%">
            <el-table-column prop="provider" label="提供商" />
            <el-table-column prop="cost" label="成本" align="right">
              <template #default="{ row }">
                ¥{{ row.cost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="占比" align="right">
              <template #default="{ row }">
                {{ row.percentage.toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column prop="requests" label="请求次数" align="right" />
          </el-table>
        </div>

        <!-- 成本优化建议 -->
        <div class="cost-suggestions">
          <h4>成本优化建议</h4>
          <el-alert
            v-for="suggestion in data.suggestions"
            :key="suggestion.id"
            :title="suggestion.title"
            :description="suggestion.description"
            :type="suggestion.type"
            show-icon
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  data: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  refresh: []
}>()

const costTrendChart = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!costTrendChart.value) return

  chartInstance = echarts.init(costTrendChart.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.data?.costTrend) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: props.data.costTrend.map((item: any) => item.date)
    },
    yAxis: {
      type: 'value',
      name: '成本(¥)'
    },
    series: [
      {
        name: '日成本',
        type: 'line',
        data: props.data.costTrend.map((item: any) => item.cost),
        smooth: true,
        lineStyle: { color: '#E6A23C' },
        itemStyle: { color: '#E6A23C' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
              { offset: 1, color: 'rgba(230, 162, 60, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

const refreshData = () => {
  emit('refresh')
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  window.addEventListener('resize', resizeChart)
})

watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && costTrendChart.value && !chartInstance) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style scoped>
.cost-analysis-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.cost-content {
  padding: 10px 0;
}

.cost-overview {
  margin-bottom: 30px;
}

.cost-trend {
  margin-bottom: 30px;
}

.cost-trend h4,
.provider-cost h4,
.cost-suggestions h4 {
  margin-bottom: 15px;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.provider-cost {
  margin-bottom: 30px;
}

.growth-positive {
  color: #F56C6C;
}

.growth-negative {
  color: #67C23A;
}
</style>
