import { ApiService } from './api'

export interface AutomationTask {
  id: number
  projectId: number
  sourceType: string
  sourceId?: number
  taskType: string
  taskName: string
  description?: string
  taskData?: string
  status: string
  priority: string
  assignedTo?: string
  createdTime: string
  startedTime?: string
  completedTime?: string
  result?: string
  errorMessage?: string
  retryCount: number
  maxRetries: number
  timeoutMinutes?: number
  dependencies?: string
  tags?: string
}

export interface GetTasksRequest {
  clientId: string
  taskTypes?: string[]
  maxCount?: number
  projectId?: number
}

export interface CreateTaskRequest {
  projectId: number
  sourceType?: string
  sourceId?: number
  taskType: string
  taskName: string
  description?: string
  taskData?: string
  priority?: string
  timeoutMinutes?: number
  dependencies?: number[]
  tags?: string
}

export interface UpdateTaskStatusRequest {
  taskId: number
  clientId: string
  status: string
  result?: string
  errorMessage?: string
  shouldRetry?: boolean
}

export interface TaskStatistics {
  statusCounts: Record<string, number>
  totalTasks: number
  activeClients: number
  todayCompleted: number
  todayFailed: number
}

export interface PagedTasksResponse {
  items: AutomationTask[]
  totalCount: number
  pageIndex: number
  pageSize: number
  totalPages: number
}

export interface ClientCapabilities {
  supportedTaskTypes: string[]
  maxConcurrentTasks: number
  version: string
  environment: string
}

/**
 * 自动化任务服务
 */
export class AutomationService {
  /**
   * 获取待处理任务
   */
  static async getPendingTasks(request: GetTasksRequest): Promise<AutomationTask[]> {
    try {
      const response = await ApiService.post<AutomationTask[]>(
        '/api/AutomationTask/pending',
        request
      )
      return response
    } catch (error) {
      console.error('获取待处理任务失败:', error)
      throw error
    }
  }

  /**
   * 分配任务给客户端
   */
  static async assignTask(taskId: number, clientId: string): Promise<boolean> {
    try {
      const response = await ApiService.post<{ success: boolean }>(
        '/api/AutomationTask/assign',
        { taskId, clientId }
      )
      return response.success
    } catch (error) {
      console.error('分配任务失败:', error)
      throw error
    }
  }

  /**
   * 批量分配任务
   */
  static async assignTasks(taskIds: number[], clientId: string): Promise<number> {
    try {
      const response = await ApiService.post<{ assignedCount: number }>(
        '/api/AutomationTask/assign-batch',
        { taskIds, clientId }
      )
      return response.assignedCount
    } catch (error) {
      console.error('批量分配任务失败:', error)
      throw error
    }
  }

  /**
   * 开始执行任务
   */
  static async startTask(taskId: number, clientId: string): Promise<boolean> {
    try {
      const response = await ApiService.post<{ success: boolean }>(
        `/api/AutomationTask/${taskId}/start?clientId=${clientId}`
      )
      return response.success
    } catch (error) {
      console.error('开始执行任务失败:', error)
      throw error
    }
  }

  /**
   * 更新任务状态
   */
  static async updateTaskStatus(request: UpdateTaskStatusRequest): Promise<boolean> {
    try {
      const response = await ApiService.post<{ success: boolean }>(
        '/api/AutomationTask/status',
        request
      )
      return response.success
    } catch (error) {
      console.error('更新任务状态失败:', error)
      throw error
    }
  }

  /**
   * 创建新任务
   */
  static async createTask(request: CreateTaskRequest): Promise<AutomationTask> {
    try {
      const response = await ApiService.post<AutomationTask>(
        '/api/AutomationTask',
        request
      )
      return response
    } catch (error) {
      console.error('创建任务失败:', error)
      throw error
    }
  }

  /**
   * 获取任务详情
   */
  static async getTask(taskId: number): Promise<AutomationTask> {
    try {
      const response = await ApiService.get<AutomationTask>(
        `/api/AutomationTask/${taskId}`
      )
      return response
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    }
  }

  /**
   * 获取项目任务列表
   */
  static async getProjectTasks(
    projectId: number,
    status?: string,
    taskType?: string,
    pageIndex: number = 1,
    pageSize: number = 20
  ): Promise<PagedTasksResponse> {
    try {
      const params = new URLSearchParams({
        pageIndex: pageIndex.toString(),
        pageSize: pageSize.toString()
      })

      if (status) params.append('status', status)
      if (taskType) params.append('taskType', taskType)

      const response = await ApiService.get<PagedTasksResponse>(
        `/api/AutomationTask/project/${projectId}?${params.toString()}`
      )
      return response
    } catch (error) {
      console.error('获取项目任务列表失败:', error)
      throw error
    }
  }

  /**
   * 获取客户端任务列表
   */
  static async getClientTasks(
    clientId: string,
    status?: string,
    pageIndex: number = 1,
    pageSize: number = 20
  ): Promise<PagedTasksResponse> {
    try {
      const params = new URLSearchParams({
        pageIndex: pageIndex.toString(),
        pageSize: pageSize.toString()
      })

      if (status) params.append('status', status)

      const response = await ApiService.get<PagedTasksResponse>(
        `/api/AutomationTask/client/${clientId}?${params.toString()}`
      )
      return response
    } catch (error) {
      console.error('获取客户端任务列表失败:', error)
      throw error
    }
  }

  /**
   * 获取任务统计信息
   */
  static async getTaskStatistics(projectId?: number): Promise<TaskStatistics> {
    try {
      const params = projectId ? `?projectId=${projectId}` : ''
      const response = await ApiService.get<TaskStatistics>(
        `/api/AutomationTask/statistics${params}`
      )
      return response
    } catch (error) {
      console.error('获取任务统计失败:', error)
      throw error
    }
  }

  /**
   * 取消任务
   */
  static async cancelTask(taskId: number, reason: string = '手动取消'): Promise<boolean> {
    try {
      const response = await ApiService.post<{ success: boolean }>(
        `/api/AutomationTask/${taskId}/cancel?reason=${encodeURIComponent(reason)}`
      )
      return response.success
    } catch (error) {
      console.error('取消任务失败:', error)
      throw error
    }
  }

  /**
   * 获取可执行的任务
   */
  static async getExecutableTasks(projectId?: number, maxCount: number = 10): Promise<AutomationTask[]> {
    try {
      const params = new URLSearchParams({ maxCount: maxCount.toString() })
      if (projectId) params.append('projectId', projectId.toString())

      const response = await ApiService.get<AutomationTask[]>(
        `/api/AutomationTask/executable?${params.toString()}`
      )
      return response
    } catch (error) {
      console.error('获取可执行任务失败:', error)
      throw error
    }
  }

  /**
   * 检查任务依赖
   */
  static async checkTaskDependencies(taskId: number): Promise<boolean> {
    try {
      const response = await ApiService.get<{ dependenciesSatisfied: boolean }>(
        `/api/AutomationTask/${taskId}/dependencies`
      )
      return response.dependenciesSatisfied
    } catch (error) {
      console.error('检查任务依赖失败:', error)
      throw error
    }
  }

  /**
   * 重置超时任务
   */
  static async resetTimeoutTasks(): Promise<number> {
    try {
      const response = await ApiService.post<{ resetCount: number }>(
        '/api/AutomationTask/reset-timeout'
      )
      return response.resetCount
    } catch (error) {
      console.error('重置超时任务失败:', error)
      throw error
    }
  }

  /**
   * 客户端心跳
   */
  static async heartbeat(clientId: string, capabilities?: ClientCapabilities): Promise<any> {
    try {
      const response = await ApiService.post(
        `/api/AutomationTask/heartbeat?clientId=${clientId}`,
        capabilities
      )
      return response
    } catch (error) {
      console.error('客户端心跳失败:', error)
      throw error
    }
  }

  /**
   * 删除任务
   */
  static async deleteTask(taskId: number): Promise<boolean> {
    try {
      await ApiService.delete(`/api/AutomationTask/${taskId}`)
      return true
    } catch (error) {
      console.error('删除任务失败:', error)
      throw error
    }
  }
}
