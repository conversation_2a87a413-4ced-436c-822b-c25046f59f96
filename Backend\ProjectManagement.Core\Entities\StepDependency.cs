using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 步骤依赖关系实体 - 对应DatabaseSchema.sql中的StepDependencies表
/// 功能: 管理开发步骤之间的依赖关系
/// 支持: 顺序依赖、并行依赖、条件依赖、可选依赖
/// </summary>
[SugarTable("StepDependencies", "步骤依赖关系管理表")]
public class StepDependency : BaseEntity
{
    /// <summary>
    /// 步骤ID（依赖方）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "步骤ID（依赖方）")]
    public int StepId { get; set; }

    /// <summary>
    /// 被依赖的步骤ID（被依赖方）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "被依赖的步骤ID（被依赖方）")]
    public int DependsOnStepId { get; set; }

    /// <summary>
    /// 依赖类型: Sequential(顺序依赖), Parallel(并行依赖), Conditional(条件依赖), Optional(可选依赖)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "依赖类型")]
    public string DependencyType { get; set; } = "Sequential";

    /// <summary>
    /// 是否必需依赖（true=必须完成被依赖步骤才能开始，false=可选依赖）
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否必需依赖")]
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// 依赖描述
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "依赖描述")]
    public string? Description { get; set; }

    #region 导航属性

    /// <summary>
    /// 依赖方步骤
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DevelopmentStep? Step { get; set; }

    /// <summary>
    /// 被依赖方步骤
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DevelopmentStep? DependsOnStep { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Creator { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Updater { get; set; }

    #endregion

    #region 业务方法

    /// <summary>
    /// 检查依赖是否满足
    /// </summary>
    /// <returns>true表示依赖已满足，可以开始执行</returns>
    public bool IsSatisfied()
    {
        if (DependsOnStep == null)
            return !IsRequired; // 如果找不到被依赖步骤，只有在非必需时才满足

        return DependencyType switch
        {
            "Sequential" => DependsOnStep.Status == "Completed",
            "Parallel" => DependsOnStep.Status == "InProgress" || DependsOnStep.Status == "Completed",
            "Conditional" => !IsRequired || DependsOnStep.Status == "Completed",
            "Optional" => true, // 可选依赖总是满足
            _ => DependsOnStep.Status == "Completed"
        };
    }

    /// <summary>
    /// 获取依赖状态描述
    /// </summary>
    /// <returns>依赖状态的文字描述</returns>
    public string GetStatusDescription()
    {
        if (DependsOnStep == null)
            return "被依赖步骤不存在";

        var satisfied = IsSatisfied();
        var requiredText = IsRequired ? "必需" : "可选";
        var typeText = DependencyType switch
        {
            "Sequential" => "顺序",
            "Parallel" => "并行",
            "Conditional" => "条件",
            "Optional" => "可选",
            _ => "未知"
        };

        return $"{typeText}依赖({requiredText}) - {DependsOnStep.StepName}: {DependsOnStep.Status} - {(satisfied ? "已满足" : "未满足")}";
    }

    /// <summary>
    /// 验证依赖关系是否有效（防止循环依赖）
    /// </summary>
    /// <param name="allDependencies">所有依赖关系</param>
    /// <returns>true表示有效，false表示存在循环依赖</returns>
    public bool IsValidDependency(List<StepDependency> allDependencies)
    {
        // 简单的循环依赖检测
        var visited = new HashSet<int>();
        var recursionStack = new HashSet<int>();

        return !HasCyclicDependency(StepId, allDependencies, visited, recursionStack);
    }

    /// <summary>
    /// 递归检测循环依赖
    /// </summary>
    private bool HasCyclicDependency(int stepId, List<StepDependency> dependencies, 
        HashSet<int> visited, HashSet<int> recursionStack)
    {
        visited.Add(stepId);
        recursionStack.Add(stepId);

        // 查找当前步骤的所有依赖
        var stepDependencies = dependencies.Where(d => d.StepId == stepId);

        foreach (var dependency in stepDependencies)
        {
            var dependsOnId = dependency.DependsOnStepId;

            // 如果在递归栈中找到了这个步骤，说明存在循环
            if (recursionStack.Contains(dependsOnId))
                return true;

            // 如果还没访问过，继续递归检查
            if (!visited.Contains(dependsOnId) && 
                HasCyclicDependency(dependsOnId, dependencies, visited, recursionStack))
                return true;
        }

        recursionStack.Remove(stepId);
        return false;
    }

    #endregion

    #region 静态方法

    /// <summary>
    /// 创建顺序依赖
    /// </summary>
    public static StepDependency CreateSequentialDependency(int stepId, int dependsOnStepId, string? description = null)
    {
        return new StepDependency
        {
            StepId = stepId,
            DependsOnStepId = dependsOnStepId,
            DependencyType = "Sequential",
            IsRequired = true,
            Description = description ?? "顺序依赖"
        };
    }

    /// <summary>
    /// 创建并行依赖
    /// </summary>
    public static StepDependency CreateParallelDependency(int stepId, int dependsOnStepId, string? description = null)
    {
        return new StepDependency
        {
            StepId = stepId,
            DependsOnStepId = dependsOnStepId,
            DependencyType = "Parallel",
            IsRequired = true,
            Description = description ?? "并行依赖"
        };
    }

    /// <summary>
    /// 创建可选依赖
    /// </summary>
    public static StepDependency CreateOptionalDependency(int stepId, int dependsOnStepId, string? description = null)
    {
        return new StepDependency
        {
            StepId = stepId,
            DependsOnStepId = dependsOnStepId,
            DependencyType = "Optional",
            IsRequired = false,
            Description = description ?? "可选依赖"
        };
    }

    /// <summary>
    /// 创建条件依赖
    /// </summary>
    public static StepDependency CreateConditionalDependency(int stepId, int dependsOnStepId, bool isRequired, string? description = null)
    {
        return new StepDependency
        {
            StepId = stepId,
            DependsOnStepId = dependsOnStepId,
            DependencyType = "Conditional",
            IsRequired = isRequired,
            Description = description ?? "条件依赖"
        };
    }

    #endregion
}
