<template>
  <div class="ai-provider-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          AI提供商配置
        </h1>
        <p class="page-subtitle">管理和配置AI服务提供商，监控使用情况</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshHealth" :loading="refreshingHealth">
          <el-icon><Refresh /></el-icon>
          刷新状态
        </el-button>
        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入配置
        </el-button>
        <el-button @click="exportConfiguration">
          <el-icon><Download /></el-icon>
          导出配置
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="总请求数" :value="totalStats.totalRequests">
              <template #prefix>
                <el-icon><DataAnalysis /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="成功率" :value="successRate" suffix="%">
              <template #prefix>
                <el-icon><CircleCheck /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="总Token使用" :value="totalStats.totalTokensUsed">
              <template #prefix>
                <el-icon><Coin /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="预估成本" :value="totalStats.estimatedCost" prefix="$" :precision="4">
              <template #prefix>
                <el-icon><Money /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 提供商列表 -->
    <el-card class="providers-card">
      <template #header>
        <div class="card-header">
          <span>AI提供商列表</span>
          <el-button type="primary" @click="showAddProviderDialog">
            <el-icon><Plus /></el-icon>
            添加提供商
          </el-button>
        </div>
      </template>

      <div class="providers-grid">
        <div
          v-for="provider in providers"
          :key="provider.name"
          class="provider-card"
          :class="{ 'disabled': !provider.isEnabled }"
        >
          <!-- 提供商头部 -->
          <div class="provider-header">
            <div class="provider-info">
              <div class="provider-icon">
                <el-icon size="24">
                  <component :is="getProviderIcon(provider.name)" />
                </el-icon>
              </div>
              <div class="provider-details">
                <h3 class="provider-name">{{ provider.displayName }}</h3>
                <p class="provider-description">{{ provider.description }}</p>
              </div>
            </div>
            <div class="provider-status">
              <el-tag
                :type="getStatusType(provider)"
                :effect="provider.isEnabled ? 'dark' : 'plain'"
              >
                {{ getStatusText(provider) }}
              </el-tag>
            </div>
          </div>

          <!-- 提供商统计 -->
          <div class="provider-stats">
            <div class="stat-item">
              <span class="stat-label">请求数</span>
              <span class="stat-value">{{ provider.usageStats.totalRequests }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率</span>
              <span class="stat-value">
                {{ getProviderSuccessRate(provider) }}%
              </span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均响应</span>
              <span class="stat-value">{{ provider.usageStats.averageResponseTime.toFixed(0) }}ms</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Token使用</span>
              <span class="stat-value">{{ formatNumber(provider.usageStats.totalTokens) }}</span>
            </div>
          </div>

          <!-- 支持的模型 -->
          <div class="provider-models">
            <div class="models-label">支持的模型:</div>
            <div class="models-list">
              <el-tag
                v-for="model in provider.supportedModels.slice(0, 3)"
                :key="model"
                size="small"
                class="model-tag"
              >
                {{ model }}
              </el-tag>
              <el-tag
                v-if="provider.supportedModels.length > 3"
                size="small"
                type="info"
                class="model-tag"
              >
                +{{ provider.supportedModels.length - 3 }}
              </el-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="provider-actions">
            <el-button
              size="small"
              @click="configureProvider(provider)"
            >
              <el-icon><Setting /></el-icon>
              配置
            </el-button>
            <el-button
              size="small"
              @click="testProvider(provider)"
              :loading="testingProviders[provider.name]"
            >
              <el-icon><Connection /></el-icon>
              测试
            </el-button>
            <el-button
              size="small"
              :type="provider.isEnabled ? 'danger' : 'success'"
              @click="toggleProvider(provider)"
            >
              <el-icon>
                <component :is="provider.isEnabled ? 'VideoPause' : 'VideoPlay'" />
              </el-icon>
              {{ provider.isEnabled ? '禁用' : '启用' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 全局配置 - 已隐藏 -->
    <el-card class="global-config-card" style="display: none;">
      <template #header>
        <span>全局配置</span>
      </template>

      <el-form :model="globalConfig" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认提供商">
              <el-select v-model="globalConfig.defaultProvider" placeholder="选择默认提供商">
                <el-option
                  v-for="provider in enabledProviders"
                  :key="provider.name"
                  :label="provider.displayName"
                  :value="provider.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认模型">
              <el-select v-model="globalConfig.defaultConfig.model" placeholder="选择默认模型">
                <el-option
                  v-for="model in defaultProviderModels"
                  :key="model"
                  :label="model"
                  :value="model"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最大Token数">
              <el-input-number
                v-model="globalConfig.defaultConfig.maxTokens"
                :min="1"
                :max="32000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="温度">
              <el-input-number
                v-model="globalConfig.defaultConfig.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                :precision="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Top P">
              <el-input-number
                v-model="globalConfig.defaultConfig.topP"
                :min="0"
                :max="1"
                :step="0.1"
                :precision="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="saveGlobalConfig" :loading="savingConfig">
            保存全局配置
          </el-button>
          <el-button @click="resetGlobalConfig">
            重置配置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务映射配置 - 已隐藏 -->
    <el-card class="task-mapping-card" style="display: none;">
      <template #header>
        <div class="card-header">
          <span>任务映射配置</span>
          <el-button type="primary" @click="showAddTaskMappingDialog">
            <el-icon><Plus /></el-icon>
            添加自定义映射
          </el-button>
        </div>
      </template>

      <el-table :data="taskMappingData" style="width: 100%">
        <el-table-column prop="task" label="任务类型" width="200">
          <template #default="{ row }">
            <el-tag :type="row.isCustom ? 'warning' : 'primary'">
              {{ row.taskLabel }}
            </el-tag>
            <el-tag v-if="row.isCustom" size="small" type="info" style="margin-left: 8px">
              自定义
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="指定提供商">
          <template #default="{ row }">
            <el-select v-model="row.provider" placeholder="选择提供商">
              <el-option
                v-for="provider in enabledProviders"
                :key="provider.name"
                :label="provider.displayName"
                :value="provider.name"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.isCustom"
              size="small"
              type="danger"
              @click="deleteTaskMapping(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="task-mapping-actions">
        <el-button type="primary" @click="saveTaskMapping" :loading="savingTaskMapping">
          保存任务映射
        </el-button>
      </div>
    </el-card>

    <!-- 提供商配置对话框 -->
    <ProviderConfigDialog
      v-model="configDialogVisible"
      :provider="selectedProvider"
      @saved="handleProviderConfigSaved"
    />

    <!-- 添加供应商对话框 -->
    <AddProviderDialog
      v-model="addProviderDialogVisible"
      @success="handleProviderAdded"
    />

    <!-- 导入配置对话框 -->
    <ImportConfigDialog
      v-model="importDialogVisible"
      @imported="handleConfigImported"
    />

    <!-- 添加任务映射对话框 -->
    <el-dialog
      v-model="addTaskMappingDialogVisible"
      title="添加自定义任务映射"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="taskMappingFormRef"
        :model="newTaskMapping"
        :rules="taskMappingRules"
        label-width="120px"
      >
        <el-form-item label="任务类型" prop="taskType">
          <el-input
            v-model="newTaskMapping.taskType"
            placeholder="请输入任务类型（英文，如：CustomAnalysis）"
            maxlength="50"
            show-word-limit
          />
          <div class="form-tip">
            建议使用英文驼峰命名，如：CustomAnalysis、DataProcessing等
          </div>
        </el-form-item>

        <el-form-item label="显示名称" prop="taskLabel">
          <el-input
            v-model="newTaskMapping.taskLabel"
            placeholder="请输入任务显示名称（中文）"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="AI提供商" prop="providerName">
          <el-select v-model="newTaskMapping.providerName" placeholder="选择AI提供商" style="width: 100%">
            <el-option
              v-for="provider in enabledProviders"
              :key="provider.name"
              :label="provider.displayName"
              :value="provider.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="模型名称" prop="modelName">
          <el-select
            v-model="newTaskMapping.modelName"
            placeholder="选择模型（可选）"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="model in selectedProviderModels"
              :key="model"
              :label="model"
              :value="model"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="newTaskMapping.priority"
            :min="1"
            :max="10"
            style="width: 100%"
          />
          <div class="form-tip">
            数字越小优先级越高，相同任务类型时优先使用优先级高的配置
          </div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="newTaskMapping.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务映射的描述信息"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="设置选项">
          <el-checkbox v-model="newTaskMapping.isActive">启用此映射</el-checkbox>
          <el-checkbox v-model="newTaskMapping.isDefault" style="margin-left: 20px">
            设为默认配置
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addTaskMappingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddTaskMapping" :loading="addingTaskMapping">
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { AIProviderService, type AIProvider, type AIConfiguration } from '@/services/aiProvider'
import ProviderConfigDialog from './components/ProviderConfigDialog.vue'
import AddProviderDialog from './components/AddProviderDialog.vue'
import ImportConfigDialog from './components/ImportConfigDialog.vue'
import {
  Setting,
  Refresh,
  Upload,
  Download,
  DataAnalysis,
  CircleCheck,
  Coin,
  Money,
  Plus,
  Connection,
  VideoPause,
  VideoPlay,
  Platform,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const providers = ref<AIProvider[]>([])
const globalConfig = ref<AIConfiguration>({
  defaultProvider: '',
  defaultConfig: {
    provider: '',
    model: '',
    maxTokens: 4000,
    temperature: 0.7,
    topP: 1.0
  },
  taskMapping: {},
  providers: {}
})
const totalStats = ref({
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  totalTokensUsed: 0,
  estimatedCost: 0,
  lastUsed: '',
  providerBreakdown: {}
})

const loading = ref(false)
const refreshingHealth = ref(false)
const savingConfig = ref(false)
const savingTaskMapping = ref(false)
const testingProviders = ref<Record<string, boolean>>({})

const configDialogVisible = ref(false)
const addProviderDialogVisible = ref(false)
const importDialogVisible = ref(false)
const selectedProvider = ref<AIProvider | null>(null)

// 新增：用户任务映射数据
const userTaskMappings = ref<any[]>([])
const loadingTaskMappings = ref(false)

// 添加任务映射对话框相关
const addTaskMappingDialogVisible = ref(false)
const addingTaskMapping = ref(false)
const taskMappingFormRef = ref()
const newTaskMapping = ref({
  taskType: '',
  taskLabel: '',
  providerName: '',
  modelName: '',
  priority: 1,
  description: '',
  isActive: true,
  isDefault: false
})

// 表单验证规则
const taskMappingRules = {
  taskType: [
    { required: true, message: '请输入任务类型', trigger: 'blur' },
    { pattern: /^[A-Za-z][A-Za-z0-9]*$/, message: '任务类型必须以字母开头，只能包含字母和数字', trigger: 'blur' }
  ],
  taskLabel: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  providerName: [
    { required: true, message: '请选择AI提供商', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请设置优先级', trigger: 'blur' }
  ]
}

// 计算属性
const successRate = computed(() => {
  const total = totalStats.value.totalRequests
  const successful = totalStats.value.successfulRequests
  return total > 0 ? Math.round((successful / total) * 100) : 0
})

const enabledProviders = computed(() => {
  return providers.value.filter(p => p.isEnabled)
})

const defaultProviderModels = computed(() => {
  const defaultProvider = providers.value.find(p => p.name === globalConfig.value.defaultProvider)
  return defaultProvider?.supportedModels || []
})

const selectedProviderModels = computed(() => {
  const selectedProvider = providers.value.find(p => p.name === newTaskMapping.value.providerName)
  return selectedProvider?.supportedModels || []
})

const taskMappingData = computed(() => {
  // 预定义的系统任务类型
  const systemTasks = [
    { task: 'RequirementAnalysis', taskLabel: '需求分析', description: '分析和解析用户需求', isCustom: false },
    { task: 'CodeGeneration', taskLabel: '代码生成', description: '根据需求生成代码', isCustom: false },
    { task: 'DocumentGeneration', taskLabel: '文档生成', description: '生成技术文档', isCustom: false },
    { task: 'Embeddings', taskLabel: '向量嵌入', description: '文本向量化处理', isCustom: false }
  ]

  // 从用户任务映射中获取自定义任务类型
  const customTasks = userTaskMappings.value
    .filter(mapping => mapping.isActive)
    .filter(mapping => !systemTasks.some(sysTask => sysTask.task === mapping.taskType))
    .map(mapping => ({
      task: mapping.taskType,
      taskLabel: mapping.taskType, // 如果没有显示名称，使用任务类型
      description: mapping.description || '自定义任务映射',
      isCustom: true
    }))

  // 合并系统任务和自定义任务
  const allTasks = [...systemTasks, ...customTasks]

  return allTasks.map(task => {
    // 优先从用户任务映射中查找默认配置
    const userMapping = userTaskMappings.value.find(mapping =>
      mapping.taskType === task.task && mapping.isActive && mapping.isDefault
    )

    // 如果没有默认配置，查找任何激活的配置（特别是自定义任务）
    const anyUserMapping = userMapping || userTaskMappings.value.find(mapping =>
      mapping.taskType === task.task && mapping.isActive
    )

    return {
      ...task,
      provider: userMapping?.providerName || globalConfig.value.taskMapping[task.task] || globalConfig.value.defaultProvider,
      mappingId: anyUserMapping?.id // 保存映射ID用于更新和删除
    }
  })
})

// 方法
const loadProviders = async () => {
  try {
    loading.value = true
    providers.value = await AIProviderService.getProviders()
  } catch (error: any) {
    console.error('加载提供商失败:', error)
    ElMessage.error('加载提供商失败')
  } finally {
    loading.value = false
  }
}

const loadConfiguration = async () => {
  try {
    globalConfig.value = await AIProviderService.getConfiguration()
  } catch (error: any) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  }
}

const loadTotalStats = async () => {
  try {
    totalStats.value = await AIProviderService.getTotalUsageStats()
  } catch (error: any) {
    console.error('加载统计失败:', error)
  }
}

// 新增：加载用户任务映射
const loadUserTaskMappings = async () => {
  try {
    loadingTaskMappings.value = true
    userTaskMappings.value = await AIProviderService.getUserTaskMappings()
    console.log('加载用户任务映射成功:', userTaskMappings.value)
    console.log('任务映射数据结构:', userTaskMappings.value.map(m => ({ id: m.id, taskType: m.taskType, isActive: m.isActive })))
  } catch (error: any) {
    console.error('加载用户任务映射失败:', error)
    ElMessage.error('加载任务映射失败')
  } finally {
    loadingTaskMappings.value = false
  }
}

const getProviderIcon = (providerName: string) => {
  const iconMap: Record<string, any> = {
    'Azure': Platform,
    'OpenAI': Platform,
    'DeepSeek': Platform,
    'Claude': Platform,
    'Ollama': Platform
  }
  return iconMap[providerName] || Platform
}

const getStatusType = (provider: AIProvider) => {
  if (!provider.isEnabled) return 'info'
  return provider.isAvailable ? 'success' : 'danger'
}

const getStatusText = (provider: AIProvider) => {
  if (!provider.isEnabled) return '已禁用'
  return provider.isAvailable ? '可用' : '不可用'
}

const getProviderSuccessRate = (provider: AIProvider) => {
  const total = provider.usageStats.totalRequests
  const successful = provider.usageStats.successfulRequests
  return total > 0 ? Math.round((successful / total) * 100) : 0
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const configureProvider = (provider: AIProvider) => {
  selectedProvider.value = provider
  configDialogVisible.value = true
}

const testProvider = async (provider: AIProvider) => {
  try {
    testingProviders.value[provider.name] = true
    const result = await AIProviderService.testProvider(provider.name)

    if (result.success) {
      ElMessage.success(`${provider.displayName} 连接测试成功 (${result.responseTime}ms)`)
    } else {
      ElMessage.error(`${provider.displayName} 连接测试失败: ${result.message}`)
    }
  } catch (error: any) {
    console.error('测试提供商失败:', error)
    ElMessage.error(`测试 ${provider.displayName} 失败`)
  } finally {
    testingProviders.value[provider.name] = false
  }
}

const toggleProvider = async (provider: AIProvider) => {
  try {
    const action = provider.isEnabled ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action} ${provider.displayName} 吗？`,
      `${action}提供商`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await AIProviderService.toggleProvider(provider.name, !provider.isEnabled)
    provider.isEnabled = !provider.isEnabled

    ElMessage.success(`${provider.displayName} 已${action}`)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('切换提供商状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const refreshHealth = async () => {
  try {
    refreshingHealth.value = true
    await AIProviderService.refreshProviderHealth()
    await loadProviders()
    ElMessage.success('健康状态已刷新')
  } catch (error: any) {
    console.error('刷新健康状态失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshingHealth.value = false
  }
}

const saveGlobalConfig = async () => {
  try {
    savingConfig.value = true
    await AIProviderService.updateConfiguration(globalConfig.value)
    ElMessage.success('全局配置已保存')
  } catch (error: any) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    savingConfig.value = false
  }
}

const resetGlobalConfig = () => {
  loadConfiguration()
  ElMessage.info('配置已重置')
}

const saveTaskMapping = async () => {
  try {
    savingTaskMapping.value = true

    // 构建用户任务映射数据
    const mappingsToUpdate = []
    const mappingsToCreate = []

    for (const item of taskMappingData.value) {
      if (item.mappingId) {
        // 更新现有映射 - 只发送需要更新的字段
        mappingsToUpdate.push({
          id: item.mappingId,
          taskType: item.task,
          providerName: item.provider,
          isActive: true,
          isDefault: true,
          priority: 1,
          description: item.description || ''
        })
      } else {
        // 创建新映射
        mappingsToCreate.push({
          taskType: item.task,
          providerName: item.provider,
          isActive: true,
          isDefault: true,
          priority: 1,
          description: item.description || ''
        })
      }
    }

    // 批量更新现有映射
    for (const mapping of mappingsToUpdate) {
      const updateData = {
        taskType: mapping.taskType,
        providerName: mapping.providerName,
        isActive: mapping.isActive,
        isDefault: mapping.isDefault,
        priority: mapping.priority,
        description: mapping.description
      }
      await AIProviderService.updateTaskMapping(mapping.id, updateData)
    }

    // 批量创建新映射
    for (const mapping of mappingsToCreate) {
      await AIProviderService.createTaskMapping(mapping)
    }

    // 重新加载数据
    await loadUserTaskMappings()

    ElMessage.success('任务映射已保存')
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message || '保存任务映射失败'
    ElMessage.error(errorMessage)
  } finally {
    savingTaskMapping.value = false
  }
}

const exportConfiguration = async () => {
  try {
    await AIProviderService.exportConfiguration()
    ElMessage.success('配置已导出')
  } catch (error: any) {
    console.error('导出配置失败:', error)
    ElMessage.error('导出配置失败')
  }
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const showAddProviderDialog = () => {
  addProviderDialogVisible.value = true
}

const handleProviderConfigSaved = () => {
  loadProviders()
  loadConfiguration()
  loadUserTaskMappings()
}

const handleProviderAdded = () => {
  loadProviders()
  loadConfiguration()
  loadTotalStats()
  loadUserTaskMappings()
}

const handleConfigImported = () => {
  loadProviders()
  loadConfiguration()
  loadTotalStats()
  loadUserTaskMappings()
}

// 显示添加任务映射对话框
const showAddTaskMappingDialog = () => {
  // 重置表单数据
  newTaskMapping.value = {
    taskType: '',
    taskLabel: '',
    providerName: '',
    modelName: '',
    priority: 1,
    description: '',
    isActive: true,
    isDefault: false
  }
  addTaskMappingDialogVisible.value = true
}

// 确认添加任务映射
const confirmAddTaskMapping = async () => {
  try {
    // 表单验证
    if (!taskMappingFormRef.value) return
    const valid = await taskMappingFormRef.value.validate()
    if (!valid) return

    addingTaskMapping.value = true

    // 检查任务类型是否已存在
    const existingMapping = userTaskMappings.value.find(
      mapping => mapping.taskType === newTaskMapping.value.taskType && mapping.isActive
    )

    if (existingMapping) {
      ElMessage.warning('该任务类型已存在，请使用不同的任务类型')
      return
    }

    // 创建任务映射
    const mappingData = {
      taskType: newTaskMapping.value.taskType,
      providerName: newTaskMapping.value.providerName,
      modelName: newTaskMapping.value.modelName || undefined,
      isActive: newTaskMapping.value.isActive,
      isDefault: newTaskMapping.value.isDefault,
      priority: newTaskMapping.value.priority,
      description: newTaskMapping.value.description
    }

    await AIProviderService.createTaskMapping(mappingData)

    // 重新加载数据
    await loadUserTaskMappings()

    // 关闭对话框
    addTaskMappingDialogVisible.value = false

    ElMessage.success('自定义任务映射添加成功')
  } catch (error: any) {
    console.error('添加任务映射失败:', error)
    ElMessage.error('添加任务映射失败')
  } finally {
    addingTaskMapping.value = false
  }
}

// 删除任务映射
const deleteTaskMapping = async (row: any) => {
  try {
    console.log('删除任务映射:', row) // 调试信息

    await ElMessageBox.confirm(
      `确定要删除任务映射 "${row.taskLabel}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (row.mappingId) {
      console.log('正在删除映射ID:', row.mappingId) // 调试信息
      await AIProviderService.deleteTaskMapping(row.mappingId)
      await loadUserTaskMappings()
      ElMessage.success('任务映射删除成功')
    } else {
      console.warn('没有找到映射ID，无法删除:', row)
      ElMessage.warning('无法删除：未找到映射ID')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除任务映射失败:', error)
      ElMessage.error('删除任务映射失败: ' + (error.message || error))
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProviders()
  loadConfiguration()
  loadTotalStats()
  loadUserTaskMappings()
})
</script>

<style lang="scss" scoped>
.ai-provider-config {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }

    .page-subtitle {
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-overview {
  margin-bottom: 24px;

  .stat-card {
    text-align: center;

    :deep(.el-statistic__content) {
      font-size: 28px;
      font-weight: 600;
    }
  }
}

.providers-card {
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.provider-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  background: var(--el-bg-color);
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.disabled {
    opacity: 0.6;
    background: var(--el-bg-color-page);
  }
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .provider-info {
    display: flex;
    align-items: flex-start;
    gap: 12px;

    .provider-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .provider-details {
      .provider-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      .provider-description {
        font-size: 13px;
        color: var(--el-text-color-secondary);
        line-height: 1.4;
      }
    }
  }
}

.provider-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--el-bg-color-page);
    border-radius: 6px;

    .stat-label {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .stat-value {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.provider-models {
  margin-bottom: 16px;

  .models-label {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-bottom: 8px;
  }

  .models-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .model-tag {
      font-size: 11px;
    }
  }
}

.provider-actions {
  display: flex;
  gap: 8px;

  .el-button {
    flex: 1;
    font-size: 12px;
  }
}

.global-config-card,
.task-mapping-card {
  margin-bottom: 24px;
}

.task-mapping-actions {
  margin-top: 16px;
  text-align: right;
}

// 响应式设计
@media (max-width: 1200px) {
  .providers-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .ai-provider-config {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;

    .header-actions {
      align-self: stretch;

      .el-button {
        flex: 1;
      }
    }
  }

  .providers-grid {
    grid-template-columns: 1fr;
  }

  .provider-stats {
    grid-template-columns: 1fr;
  }

  .provider-actions {
    flex-direction: column;
  }
}

// 表单提示样式
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

// 对话框样式
.dialog-footer {
  text-align: right;
}
</style>
