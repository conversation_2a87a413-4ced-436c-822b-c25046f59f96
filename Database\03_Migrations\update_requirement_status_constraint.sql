-- =============================================
-- 更新需求文档状态约束，添加 Published 状态
-- 创建时间: 2025-07-09
-- 说明: 为 RequirementDocuments 表的 Status 字段添加 Published 状态支持
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始更新需求文档状态约束...';

-- 1. 删除现有的状态检查约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_RequirementDocuments_Status')
BEGIN
    ALTER TABLE RequirementDocuments 
    DROP CONSTRAINT CK_RequirementDocuments_Status;
    PRINT '已删除原有状态约束';
END

-- 2. 创建新的状态检查约束，包含 Published 状态
ALTER TABLE RequirementDocuments 
ADD CONSTRAINT CK_RequirementDocuments_Status 
CHECK (Status IN ('Draft', 'Review', 'Approved', 'Rejected', 'Published'));

PRINT '已创建新的状态约束，支持以下状态:';
PRINT '- Draft: 草稿';
PRINT '- Review: 审核中';
PRINT '- Approved: 已批准';
PRINT '- Rejected: 已拒绝';
PRINT '- Published: 已发布';

-- 3. 验证约束是否正确创建
SELECT 
    cc.name AS ConstraintName,
    cc.definition AS ConstraintDefinition
FROM sys.check_constraints cc
INNER JOIN sys.tables t ON cc.parent_object_id = t.object_id
WHERE t.name = 'RequirementDocuments' 
  AND cc.name = 'CK_RequirementDocuments_Status';

-- 4. 检查当前表中是否有不符合新约束的数据
SELECT 
    Id,
    Title,
    Status,
    '需要更新状态' AS Note
FROM RequirementDocuments 
WHERE Status NOT IN ('Draft', 'Review', 'Approved', 'Rejected', 'Published')
  AND IsDeleted = 0;

PRINT '需求文档状态约束更新完成！';
GO
