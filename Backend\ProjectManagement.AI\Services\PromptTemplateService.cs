using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.AI.Services;

/// <summary>
/// Prompt模板服务实现
/// </summary>
public class PromptTemplateService : IPromptTemplateService
{
    private readonly IPromptTemplateRepository _templateRepository;
    private readonly IPromptCategoryRepository _categoryRepository;
    private readonly IRepository<PromptUsageStats> _usageStatsRepository;
    private readonly IRepository<PromptRating> _ratingRepository;
    private readonly IRepository<UserPromptPreference> _preferenceRepository;
    private readonly ILogger<PromptTemplateService> _logger;

    public PromptTemplateService(
        IPromptTemplateRepository templateRepository,
        IPromptCategoryRepository categoryRepository,
        IRepository<PromptUsageStats> usageStatsRepository,
        IRepository<PromptRating> ratingRepository,
        IRepository<UserPromptPreference> preferenceRepository,
        ILogger<PromptTemplateService> logger)
    {
        _templateRepository = templateRepository;
        _categoryRepository = categoryRepository;
        _usageStatsRepository = usageStatsRepository;
        _ratingRepository = ratingRepository;
        _preferenceRepository = preferenceRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetAllTemplatesAsync()
    {
        try
        {
            return await _templateRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有模板失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取模板
    /// </summary>
    public async Task<PromptTemplate?> GetTemplateByIdAsync(int id)
    {
        try
        {
            return await _templateRepository.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取模板失败，ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 根据分类获取模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetTemplatesByCategoryAsync(int categoryId)
    {
        try
        {
            return await _templateRepository.GetByCategoryIdAsync(categoryId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据分类获取模板失败，CategoryId: {CategoryId}", categoryId);
            throw;
        }
    }

    /// <summary>
    /// 根据任务类型获取模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetTemplatesByTaskTypeAsync(string taskType)
    {
        try
        {
            return await _templateRepository.GetByTaskTypeAsync(taskType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型获取模板失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 获取默认模板
    /// </summary>
    public async Task<PromptTemplate?> GetDefaultTemplateAsync(string taskType)
    {
        try
        {
            return await _templateRepository.GetDefaultTemplateAsync(taskType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取默认模板失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 搜索模板
    /// </summary>
    public async Task<List<PromptTemplate>> SearchTemplatesAsync(string keyword, int? categoryId = null, string? taskType = null)
    {
        try
        {
            return await _templateRepository.SearchAsync(keyword, categoryId, taskType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索模板失败，Keyword: {Keyword}", keyword);
            throw;
        }
    }

    /// <summary>
    /// 获取热门模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetPopularTemplatesAsync(int count = 10)
    {
        try
        {
            return await _templateRepository.GetPopularTemplatesAsync(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门模板失败");
            throw;
        }
    }

    /// <summary>
    /// 获取用户收藏的模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetUserFavoriteTemplatesAsync(int userId)
    {
        try
        {
            return await _templateRepository.GetUserFavoriteTemplatesAsync(userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户收藏模板失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户最近使用的模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetUserRecentTemplatesAsync(int userId, int count = 10)
    {
        try
        {
            return await _templateRepository.GetUserRecentTemplatesAsync(userId, count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户最近使用模板失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 创建模板
    /// </summary>
    public async Task<PromptTemplate> CreateTemplateAsync(PromptTemplate template)
    {
        try
        {
            _logger.LogInformation("创建模板，Name: {Name}", template.Name);

            var result = await _templateRepository.AddAsync(template);

            // 更新分类模板数量
            await UpdateCategoryTemplateCount(template.CategoryId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建模板失败，Name: {Name}", template.Name);
            throw;
        }
    }

    /// <summary>
    /// 更新模板
    /// </summary>
    public async Task<PromptTemplate> UpdateTemplateAsync(PromptTemplate template)
    {
        try
        {
            _logger.LogInformation("更新模板，ID: {Id}", template.Id);

            var oldTemplate = await _templateRepository.GetByIdAsync(template.Id);
            var success = await _templateRepository.UpdateAsync(template);

            if (!success)
                throw new InvalidOperationException("更新模板失败");

            // 如果分类发生变化，更新相关分类的模板数量
            if (oldTemplate != null && oldTemplate.CategoryId != template.CategoryId)
            {
                await UpdateCategoryTemplateCount(oldTemplate.CategoryId);
                await UpdateCategoryTemplateCount(template.CategoryId);
            }

            // 返回更新后的模板
            return await _templateRepository.GetByIdAsync(template.Id) ?? template;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模板失败，ID: {Id}", template.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除模板
    /// </summary>
    public async Task<bool> DeleteTemplateAsync(int id)
    {
        try
        {
            _logger.LogInformation("删除模板，ID: {Id}", id);

            var template = await _templateRepository.GetByIdAsync(id);
            if (template == null) return false;

            var result = await _templateRepository.DeleteAsync(id);

            // 更新分类模板数量
            await UpdateCategoryTemplateCount(template.CategoryId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除模板失败，ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 复制模板
    /// </summary>
    public async Task<PromptTemplate> CloneTemplateAsync(int templateId, string newName, int userId)
    {
        try
        {
            _logger.LogInformation("复制模板，TemplateId: {TemplateId}, NewName: {NewName}", templateId, newName);

            var originalTemplate = await _templateRepository.GetByIdAsync(templateId);
            if (originalTemplate == null)
                throw new ArgumentException($"模板不存在，ID: {templateId}");

            var clonedTemplate = new PromptTemplate
            {
                Name = newName,
                Description = originalTemplate.Description,
                CategoryId = originalTemplate.CategoryId,
                Content = originalTemplate.Content,
                Parameters = originalTemplate.Parameters,
                TemplateType = "User", // 复制的模板设为用户模板
                TaskType = originalTemplate.TaskType,
                SupportedProviders = originalTemplate.SupportedProviders,
                TemplateVersion = "1.0",
                IsDefault = false,
                IsEnabled = true,
                Tags = originalTemplate.Tags,
                CreatedBy = userId
            };

            return await CreateTemplateAsync(clonedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "复制模板失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 设置默认模板
    /// </summary>
    public async Task<bool> SetDefaultTemplateAsync(int templateId, string taskType)
    {
        try
        {
            _logger.LogInformation("设置默认模板，TemplateId: {TemplateId}, TaskType: {TaskType}", templateId, taskType);

            // 先取消同类型的其他默认模板
            var existingDefaults = await _templateRepository.GetByTaskTypeAsync(taskType);
            foreach (var template in existingDefaults.Where(t => t.IsDefault))
            {
                template.IsDefault = false;
                await _templateRepository.UpdateAsync(template);
            }

            // 设置新的默认模板
            var targetTemplate = await _templateRepository.GetByIdAsync(templateId);
            if (targetTemplate == null) return false;

            targetTemplate.IsDefault = true;
            await _templateRepository.UpdateAsync(targetTemplate);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认模板失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 启用/禁用模板
    /// </summary>
    public async Task<bool> ToggleTemplateStatusAsync(int templateId, bool isEnabled)
    {
        try
        {
            _logger.LogInformation("切换模板状态，TemplateId: {TemplateId}, IsEnabled: {IsEnabled}", templateId, isEnabled);

            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null) return false;

            template.IsEnabled = isEnabled;
            await _templateRepository.UpdateAsync(template);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换模板状态失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 记录模板使用
    /// </summary>
    public async Task RecordTemplateUsageAsync(int templateId, int userId, int? projectId, string aiProvider, string aiModel,
        string inputParameters, string generatedPrompt, string? aiResponse, int? responseTimeMs,
        int? tokenUsage, decimal? cost, bool isSuccess, string? errorMessage)
    {
        try
        {
            _logger.LogInformation("记录模板使用，TemplateId: {TemplateId}, UserId: {UserId}", templateId, userId);

            var usageStats = new PromptUsageStats
            {
                TemplateId = templateId,
                UserId = userId,
                ProjectId = projectId,
                AIProvider = aiProvider,
                AIModel = aiModel,
                InputParameters = inputParameters,
                GeneratedPrompt = generatedPrompt,
                AIResponse = aiResponse?.Length > 1000 ? aiResponse.Substring(0, 1000) : aiResponse,
                ResponseTimeMs = responseTimeMs,
                TokenUsage = tokenUsage,
                Cost = cost,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage,
                UsedAt = DateTime.Now
            };

            await _usageStatsRepository.AddAsync(usageStats);

            // 更新模板使用统计
            await _templateRepository.UpdateUsageStatsAsync(templateId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录模板使用失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 评价模板
    /// </summary>
    public async Task RateTemplateAsync(int templateId, int userId, int overallRating, int? accuracyRating,
        int? usefulnessRating, int? easeOfUseRating, string? feedback, string? suggestions,
        string? tags, bool? wouldRecommend)
    {
        try
        {
            _logger.LogInformation("评价模板，TemplateId: {TemplateId}, UserId: {UserId}, Rating: {Rating}",
                templateId, userId, overallRating);

            var rating = new PromptRating
            {
                TemplateId = templateId,
                UserId = userId,
                OverallRating = overallRating,
                AccuracyRating = accuracyRating,
                UsefulnessRating = usefulnessRating,
                EaseOfUseRating = easeOfUseRating,
                Feedback = feedback,
                Suggestions = suggestions,
                Tags = tags,
                WouldRecommend = wouldRecommend,
                RatedAt = DateTime.Now
            };

            await _ratingRepository.AddAsync(rating);

            // 重新计算平均评分
            await UpdateTemplateAverageRating(templateId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评价模板失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 获取模板统计信息
    /// </summary>
    public async Task<object> GetTemplateStatsAsync(int templateId)
    {
        try
        {
            var template = await _templateRepository.GetByIdAsync(templateId);
            if (template == null) return new { };

            var usageStats = await _usageStatsRepository.GetListAsync(x => x.TemplateId == templateId);
            var ratings = await _ratingRepository.GetListAsync(x => x.TemplateId == templateId);

            return new
            {
                TemplateId = templateId,
                TotalUsage = template.UsageCount,
                AverageRating = template.AverageRating,
                TotalRatings = ratings.Count,
                SuccessRate = usageStats.Count > 0 ? (double)usageStats.Count(x => x.IsSuccess) / usageStats.Count : 0,
                AverageResponseTime = usageStats.Where(x => x.ResponseTimeMs.HasValue).Average(x => x.ResponseTimeMs),
                TotalCost = usageStats.Where(x => x.Cost.HasValue).Sum(x => x.Cost),
                LastUsed = template.LastUsedTime,
                PopularProviders = usageStats.GroupBy(x => x.AIProvider)
                    .Select(g => new { Provider = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(5)
                    .ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取模板统计信息失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户模板偏好
    /// </summary>
    public async Task<List<UserPromptPreference>> GetUserPreferencesAsync(int userId)
    {
        try
        {
            return await _preferenceRepository.GetListAsync(x => x.UserId == userId && !x.IsDeleted && x.IsEnabled);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户模板偏好失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 设置用户模板偏好
    /// </summary>
    public async Task SetUserPreferenceAsync(int userId, int templateId, string preferenceType,
        string? customParameters = null, int sortOrder = 0)
    {
        try
        {
            _logger.LogInformation("设置用户模板偏好，UserId: {UserId}, TemplateId: {TemplateId}, Type: {Type}",
                userId, templateId, preferenceType);

            // 检查是否已存在相同偏好
            var existing = await _preferenceRepository.GetFirstOrDefaultAsync(x =>
                x.UserId == userId && x.TemplateId == templateId && x.PreferenceType == preferenceType);

            if (existing != null)
            {
                existing.CustomParameters = customParameters;
                existing.SortOrder = sortOrder;
                existing.LastUsedAt = DateTime.Now;
                await _preferenceRepository.UpdateAsync(existing);
            }
            else
            {
                var preference = new UserPromptPreference
                {
                    UserId = userId,
                    TemplateId = templateId,
                    PreferenceType = preferenceType,
                    CustomParameters = customParameters,
                    SortOrder = sortOrder,
                    LastUsedAt = DateTime.Now
                };

                await _preferenceRepository.AddAsync(preference);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置用户模板偏好失败，UserId: {UserId}, TemplateId: {TemplateId}", userId, templateId);
            throw;
        }
    }

    /// <summary>
    /// 移除用户模板偏好
    /// </summary>
    public async Task RemoveUserPreferenceAsync(int userId, int templateId, string preferenceType)
    {
        try
        {
            _logger.LogInformation("移除用户模板偏好，UserId: {UserId}, TemplateId: {TemplateId}, Type: {Type}",
                userId, templateId, preferenceType);

            var preference = await _preferenceRepository.GetFirstOrDefaultAsync(x =>
                x.UserId == userId && x.TemplateId == templateId && x.PreferenceType == preferenceType);

            if (preference != null)
            {
                await _preferenceRepository.DeleteAsync(preference.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除用户模板偏好失败，UserId: {UserId}, TemplateId: {TemplateId}", userId, templateId);
            throw;
        }
    }

    /// <summary>
    /// 更新模板平均评分
    /// </summary>
    private async Task UpdateTemplateAverageRating(int templateId)
    {
        try
        {
            var ratings = await _ratingRepository.GetListAsync(x => x.TemplateId == templateId);
            if (ratings.Any())
            {
                var averageRating = ratings.Average(x => x.OverallRating);
                await _templateRepository.UpdateRatingAsync(templateId, (decimal)averageRating);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模板平均评分失败，TemplateId: {TemplateId}", templateId);
        }
    }

    /// <summary>
    /// 更新分类模板数量
    /// </summary>
    private async Task UpdateCategoryTemplateCount(int categoryId)
    {
        try
        {
            var templates = await _templateRepository.GetByCategoryIdAsync(categoryId);
            await _categoryRepository.UpdateTemplateCountAsync(categoryId, templates.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新分类模板数量失败，CategoryId: {CategoryId}", categoryId);
        }
    }
}
