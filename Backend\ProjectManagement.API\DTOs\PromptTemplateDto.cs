namespace ProjectManagement.API.DTOs;

/// <summary>
/// Prompt模板DTO
/// </summary>
public class PromptTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? Parameters { get; set; }
    public string TemplateType { get; set; } = string.Empty;
    public string TaskType { get; set; } = string.Empty;
    public string? SupportedProviders { get; set; }
    public string TemplateVersion { get; set; } = string.Empty;
    public bool IsDefault { get; set; }
    public bool IsEnabled { get; set; }
    public int UsageCount { get; set; }
    public decimal? AverageRating { get; set; }
    public DateTime? LastUsedTime { get; set; }
    public string? Tags { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime? UpdatedTime { get; set; }
    public int? CreatedBy { get; set; }
    public string? CreatedByName { get; set; }
}

/// <summary>
/// 创建Prompt模板请求DTO
/// </summary>
public class CreatePromptTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategoryId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Parameters { get; set; }
    public string TemplateType { get; set; } = "User";
    public string TaskType { get; set; } = string.Empty;
    public string? SupportedProviders { get; set; }
    public bool IsDefault { get; set; } = false;
    public bool IsEnabled { get; set; } = true;
    public string? Tags { get; set; }
}

/// <summary>
/// 更新Prompt模板请求DTO
/// </summary>
public class UpdatePromptTemplateDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategoryId { get; set; }
    public string Content { get; set; } = string.Empty;
    public string? Parameters { get; set; }
    public string? SupportedProviders { get; set; }
    public bool IsEnabled { get; set; } = true;
    public string? Tags { get; set; }
}

/// <summary>
/// Prompt分类DTO
/// </summary>
public class PromptCategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? ParentId { get; set; }
    public string? ParentName { get; set; }
    public string? Icon { get; set; }
    public string? Color { get; set; }
    public int SortOrder { get; set; }
    public bool IsEnabled { get; set; }
    public int TemplateCount { get; set; }
    public List<PromptCategoryDto> Children { get; set; } = new();
}

/// <summary>
/// 构建Prompt请求DTO
/// </summary>
public class BuildPromptRequestDto
{
    public int TemplateId { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public int? ProjectId { get; set; }
}

/// <summary>
/// 构建Prompt响应DTO
/// </summary>
public class BuildPromptResponseDto
{
    public string Prompt { get; set; } = string.Empty;
    public int TemplateId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
    public Dictionary<string, object> UsedParameters { get; set; } = new();
    public DateTime BuildTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 模板搜索请求DTO
/// </summary>
public class SearchTemplatesRequestDto
{
    public string? Keyword { get; set; }
    public int? CategoryId { get; set; }
    public string? TaskType { get; set; }
    public string? TemplateType { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 模板评价请求DTO
/// </summary>
public class RateTemplateRequestDto
{
    public int TemplateId { get; set; }
    public int OverallRating { get; set; }
    public int? AccuracyRating { get; set; }
    public int? UsefulnessRating { get; set; }
    public int? EaseOfUseRating { get; set; }
    public string? Feedback { get; set; }
    public string? Suggestions { get; set; }
    public string? Tags { get; set; }
    public bool? WouldRecommend { get; set; }
}

/// <summary>
/// 用户偏好设置请求DTO
/// </summary>
public class SetUserPreferenceRequestDto
{
    public int TemplateId { get; set; }
    public string PreferenceType { get; set; } = string.Empty; // Favorite, Recent, Custom
    public string? CustomParameters { get; set; }
    public int SortOrder { get; set; } = 0;
}

/// <summary>
/// 模板统计信息DTO
/// </summary>
public class TemplateStatsDto
{
    public int TemplateId { get; set; }
    public int TotalUsage { get; set; }
    public decimal? AverageRating { get; set; }
    public int TotalRatings { get; set; }
    public double SuccessRate { get; set; }
    public double? AverageResponseTime { get; set; }
    public decimal? TotalCost { get; set; }
    public DateTime? LastUsed { get; set; }
    public List<ProviderUsageDto> PopularProviders { get; set; } = new();
}

/// <summary>
/// 提供商使用统计DTO
/// </summary>
public class ProviderUsageDto
{
    public string Provider { get; set; } = string.Empty;
    public int Count { get; set; }
}

/// <summary>
/// 提示词质量分析DTO
/// </summary>
public class PromptQualityAnalysisDto
{
    public int Length { get; set; }
    public int WordCount { get; set; }
    public bool HasClearInstructions { get; set; }
    public bool HasExamples { get; set; }
    public bool HasConstraints { get; set; }
    public bool HasOutputFormat { get; set; }
    public int ComplexityScore { get; set; }
    public int ClarityScore { get; set; }
    public int CompletenessScore { get; set; }
    public List<string> Suggestions { get; set; } = new();
}
