<Window x:Class="ProjectManagementAI.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="AI项目管理系统" 
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <!-- 向量化操作样式 -->
        <Style x:Key="VectorOperationButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="RenderTransform">
                        <Setter.Value>
                            <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 项目卡片样式 -->
        <Style x:Key="ProjectCard" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth4"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon Kind="Brain" Width="32" Height="32" VerticalAlignment="Center"/>
                    <TextBlock Text="AI项目管理系统" FontSize="20" FontWeight="Bold" 
                              VerticalAlignment="Center" Margin="10,0"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Style="{StaticResource MaterialDesignToolButton}" 
                            ToolTip="同步数据" Command="{Binding SyncDataCommand}">
                        <materialDesign:PackIcon Kind="Sync"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignToolButton}" 
                            ToolTip="设置" Command="{Binding OpenSettingsCommand}">
                        <materialDesign:PackIcon Kind="Settings"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignToolButton}" 
                            ToolTip="关于" Command="{Binding ShowAboutCommand}">
                        <materialDesign:PackIcon Kind="Information"/>
                    </Button>
                </StackPanel>

                <!-- 向量化操作工具栏 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Style="{StaticResource VectorOperationButton}" 
                            Command="{Binding BatchCreateProjectsCommand}"
                            Background="{StaticResource PrimaryHueMidBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="VectorPolyline" Margin="0,0,5,0"/>
                            <TextBlock Text="批量创建"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource VectorOperationButton}" 
                            Command="{Binding BatchAnalyzeCommand}"
                            Background="{StaticResource SecondaryHueMidBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Margin="0,0,5,0"/>
                            <TextBlock Text="批量分析"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource VectorOperationButton}" 
                            Command="{Binding VectorOptimizeCommand}"
                            Background="{StaticResource PrimaryHueDarkBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AutoFix" Margin="0,0,5,0"/>
                            <TextBlock Text="智能优化"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航 -->
            <materialDesign:Card Grid.Column="0" Margin="10">
                <ScrollViewer>
                    <StackPanel>
                        <TextBlock Text="功能导航" FontSize="16" FontWeight="Bold" 
                                  Margin="15,15,15,10"/>
                        
                        <ListBox x:Name="NavigationListBox"
                                SelectedIndex="{Binding SelectedNavigationIndex}">
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard" Margin="0,0,10,0"/>
                                    <TextBlock Text="仪表板"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="VectorPolyline" Margin="0,0,10,0"/>
                                    <TextBlock Text="向量化操作"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" Margin="0,0,10,0"/>
                                    <TextBlock Text="数据分析"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="GraphOutline" Margin="0,0,10,0"/>
                                    <TextBlock Text="知识图谱"/>
                                </StackPanel>
                            </ListBoxItem>
                        </ListBox>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" 
                         Background="{StaticResource MaterialDesignDivider}"/>

            <!-- 主内容区 -->
            <ContentControl Grid.Column="2" Content="{Binding CurrentView}" 
                           Margin="10"/>
        </Grid>

        <!-- 底部状态栏 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="10">
            <DockPanel>
                <TextBlock DockPanel.Dock="Left" Text="{Binding StatusMessage}" 
                          VerticalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <TextBlock Text="连接状态: " VerticalAlignment="Center"/>
                    <Ellipse Width="10" Height="10"
                            Fill="{Binding ConnectionStatus, Converter={StaticResource BoolToColorConverter}}"
                            VerticalAlignment="Center" Margin="5,0"/>
                    <TextBlock Text="{Binding ConnectionStatusText}" 
                              VerticalAlignment="Center" Margin="5,0"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>
