using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using System.Text.Json;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 自动化任务仓储实现
/// </summary>
public class AutomationTaskRepository : BaseRepository<AutomationTask>, IAutomationTaskRepository
{
    public AutomationTaskRepository(ISqlSugarClient db, ILogger<AutomationTaskRepository> logger)
        : base(db, logger)
    {
    }

    /// <summary>
    /// 获取待处理的任务队列
    /// </summary>
    public async Task<List<AutomationTask>> GetPendingTasksAsync(string? clientId = null, string[]? taskTypes = null, int maxCount = 10)
    {
        try
        {
            var query = _db.Queryable<AutomationTask>()
                .Where(t => !t.IsDeleted);

            // 如果指定了客户端ID，获取分配给该客户端的待处理任务
            if (!string.IsNullOrEmpty(clientId))
            {
                query = query.Where(t => t.AssignedTo == clientId &&
                    (t.Status == AutomationTaskStatus.Assigned || t.Status == AutomationTaskStatus.InProgress));
            }
            else
            {
                // 否则获取未分配的待处理任务
                query = query.Where(t => t.Status == AutomationTaskStatus.Pending &&
                    (t.AssignedTo == null || t.AssignedTo == ""));
            }

            // 任务类型过滤
            if (taskTypes != null && taskTypes.Length > 0)
            {
                query = query.Where(t => taskTypes.Contains(t.TaskType));
            }

            var tasks = await query
                .OrderBy(t => t.Priority == AutomationTaskPriority.Critical ? 1 :
                            t.Priority == AutomationTaskPriority.High ? 2 :
                            t.Priority == AutomationTaskPriority.Medium ? 3 : 4)
                .OrderBy(t => t.CreatedTime)
                .Take(maxCount)
                .ToListAsync();

            _logger.LogInformation("获取到 {Count} 个待处理任务，客户端: {ClientId}", tasks.Count, clientId ?? "未指定");
            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取待处理任务失败，客户端: {ClientId}", clientId);
            throw;
        }
    }

    /// <summary>
    /// 分配任务给客户端
    /// </summary>
    public async Task<bool> AssignTaskAsync(int taskId, string clientId)
    {
        try
        {
            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    AssignedTo = clientId,
                    Status = AutomationTaskStatus.Assigned,
                    UpdatedTime = DateTime.Now
                })
                .Where(t => t.Id == taskId && t.Status == AutomationTaskStatus.Pending && !t.IsDeleted)
                .ExecuteCommandAsync();

            var success = result > 0;
            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 已分配给客户端 {ClientId}", taskId, clientId);
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 分配失败，可能已被其他客户端分配", taskId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配任务失败，TaskId: {TaskId}, ClientId: {ClientId}", taskId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 批量分配任务给客户端
    /// </summary>
    public async Task<int> AssignTasksAsync(List<int> taskIds, string clientId)
    {
        try
        {
            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    AssignedTo = clientId,
                    Status = AutomationTaskStatus.Assigned,
                    UpdatedTime = DateTime.Now
                })
                .Where(t => taskIds.Contains(t.Id) && t.Status == AutomationTaskStatus.Pending && !t.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("批量分配任务成功，分配数量: {Count}, 客户端: {ClientId}", result, clientId);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量分配任务失败，ClientId: {ClientId}", clientId);
            return 0;
        }
    }

    /// <summary>
    /// 开始执行任务
    /// </summary>
    public async Task<bool> StartTaskAsync(int taskId, string clientId)
    {
        try
        {
            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    Status = AutomationTaskStatus.InProgress,
                    StartedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                })
                .Where(t => t.Id == taskId && t.AssignedTo == clientId &&
                    t.Status == AutomationTaskStatus.Assigned && !t.IsDeleted)
                .ExecuteCommandAsync();

            var success = result > 0;
            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 开始执行，客户端: {ClientId}", taskId, clientId);
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 开始执行失败，状态或分配不匹配", taskId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始执行任务失败，TaskId: {TaskId}, ClientId: {ClientId}", taskId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 完成任务
    /// </summary>
    public async Task<bool> CompleteTaskAsync(int taskId, string result, string clientId)
    {
        try
        {
            var updateResult = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    Status = AutomationTaskStatus.Completed,
                    Result = result,
                    CompletedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                })
                .Where(t => t.Id == taskId && t.AssignedTo == clientId &&
                    t.Status == AutomationTaskStatus.InProgress && !t.IsDeleted)
                .ExecuteCommandAsync();

            var success = updateResult > 0;
            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 执行完成，客户端: {ClientId}", taskId, clientId);
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 完成失败，状态或分配不匹配", taskId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完成任务失败，TaskId: {TaskId}, ClientId: {ClientId}", taskId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 任务执行失败
    /// </summary>
    public async Task<bool> FailTaskAsync(int taskId, string errorMessage, string clientId, bool shouldRetry = true)
    {
        try
        {
            // 先获取当前任务信息
            var task = await _db.Queryable<AutomationTask>()
                .Where(t => t.Id == taskId && !t.IsDeleted)
                .FirstAsync();

            if (task == null)
            {
                _logger.LogWarning("任务 {TaskId} 不存在", taskId);
                return false;
            }

            // 判断是否应该重试
            var newRetryCount = task.RetryCount + 1;
            var shouldRetryTask = shouldRetry && newRetryCount <= task.MaxRetries;
            var newStatus = shouldRetryTask ? AutomationTaskStatus.Pending : AutomationTaskStatus.Failed;
            var newAssignedTo = shouldRetryTask ? null : task.AssignedTo;

            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    Status = newStatus,
                    ErrorMessage = errorMessage,
                    RetryCount = newRetryCount,
                    AssignedTo = newAssignedTo,
                    UpdatedTime = DateTime.Now
                })
                .Where(t => t.Id == taskId && t.AssignedTo == clientId && !t.IsDeleted)
                .ExecuteCommandAsync();

            var success = result > 0;
            if (success)
            {
                var action = shouldRetryTask ? "重新排队等待重试" : "标记为失败";
                _logger.LogInformation("任务 {TaskId} 执行失败，{Action}，重试次数: {RetryCount}/{MaxRetries}",
                    taskId, action, newRetryCount, task.MaxRetries);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理任务失败失败，TaskId: {TaskId}, ClientId: {ClientId}", taskId, clientId);
            return false;
        }
    }

    /// <summary>
    /// 取消任务
    /// </summary>
    public async Task<bool> CancelTaskAsync(int taskId, string reason)
    {
        try
        {
            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    Status = AutomationTaskStatus.Cancelled,
                    ErrorMessage = $"任务已取消: {reason}",
                    UpdatedTime = DateTime.Now
                })
                .Where(t => t.Id == taskId &&
                    (t.Status == AutomationTaskStatus.Pending ||
                     t.Status == AutomationTaskStatus.Assigned ||
                     t.Status == AutomationTaskStatus.InProgress) &&
                    !t.IsDeleted)
                .ExecuteCommandAsync();

            var success = result > 0;
            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 已取消，原因: {Reason}", taskId, reason);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消任务失败，TaskId: {TaskId}", taskId);
            return false;
        }
    }

    /// <summary>
    /// 获取项目的任务列表
    /// </summary>
    public async Task<(List<AutomationTask> Items, int TotalCount)> GetProjectTasksAsync(
        int projectId,
        string? status = null,
        string? taskType = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<AutomationTask>()
                .Where(t => t.ProjectId == projectId && !t.IsDeleted);

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            if (!string.IsNullOrEmpty(taskType))
            {
                query = query.Where(t => t.TaskType == taskType);
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(t => t.CreatedTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目任务列表失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 获取客户端的任务列表
    /// </summary>
    public async Task<(List<AutomationTask> Items, int TotalCount)> GetClientTasksAsync(
        string clientId,
        string? status = null,
        int pageIndex = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _db.Queryable<AutomationTask>()
                .Where(t => t.AssignedTo == clientId && !t.IsDeleted);

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(t => t.Status == status);
            }

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderByDescending(t => t.UpdatedTime)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户端任务列表失败，ClientId: {ClientId}", clientId);
            throw;
        }
    }

    /// <summary>
    /// 获取超时的任务
    /// </summary>
    public async Task<List<AutomationTask>> GetTimeoutTasksAsync()
    {
        try
        {
            var now = DateTime.Now;
            var tasks = await _db.Queryable<AutomationTask>()
                .Where(t => !t.IsDeleted &&
                    t.Status == AutomationTaskStatus.InProgress &&
                    t.TimeoutMinutes.HasValue &&
                    t.StartedTime.HasValue &&
                    SqlFunc.DateDiff(DateType.Minute, t.StartedTime.Value, now) > t.TimeoutMinutes.Value)
                .ToListAsync();

            _logger.LogInformation("发现 {Count} 个超时任务", tasks.Count);
            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取超时任务失败");
            throw;
        }
    }

    /// <summary>
    /// 重置超时任务状态
    /// </summary>
    public async Task<int> ResetTimeoutTasksAsync(List<int> taskIds)
    {
        try
        {
            var result = await _db.Updateable<AutomationTask>()
                .SetColumns(t => new AutomationTask
                {
                    Status = AutomationTaskStatus.Pending,
                    AssignedTo = null,
                    StartedTime = null,
                    ErrorMessage = "任务超时，已重置为待处理状态",
                    UpdatedTime = DateTime.Now
                })
                .Where(t => taskIds.Contains(t.Id) && !t.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("重置 {Count} 个超时任务", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置超时任务失败");
            return 0;
        }
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    public async Task<Dictionary<string, int>> GetTaskStatisticsAsync(int? projectId = null)
    {
        try
        {
            var query = _db.Queryable<AutomationTask>()
                .Where(t => !t.IsDeleted);

            if (projectId.HasValue)
            {
                query = query.Where(t => t.ProjectId == projectId.Value);
            }

            var stats = await query
                .GroupBy(t => t.Status)
                .Select(t => new { Status = t.Status, Count = SqlFunc.AggregateCount(t.Status) })
                .ToListAsync();

            var result = new Dictionary<string, int>
            {
                [AutomationTaskStatus.Pending] = 0,
                [AutomationTaskStatus.Assigned] = 0,
                [AutomationTaskStatus.InProgress] = 0,
                [AutomationTaskStatus.Completed] = 0,
                [AutomationTaskStatus.Failed] = 0,
                [AutomationTaskStatus.Cancelled] = 0
            };

            foreach (var stat in stats)
            {
                if (result.ContainsKey(stat.Status))
                {
                    result[stat.Status] = stat.Count;
                }
                else
                {
                    // 如果状态不在预定义列表中，添加到结果中
                    result[stat.Status] = stat.Count;
                    _logger.LogWarning("发现未预期的任务状态: {Status}", stat.Status);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务统计失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 根据来源创建任务
    /// </summary>
    public async Task<AutomationTask> CreateTaskFromSourceAsync(
        string sourceType,
        int sourceId,
        string taskType,
        string taskName,
        string taskData,
        string priority = AutomationTaskPriority.Medium)
    {
        try
        {
            // 根据来源类型获取项目ID
            int projectId = await GetProjectIdFromSourceAsync(sourceType, sourceId);

            var task = new AutomationTask
            {
                ProjectId = projectId,
                SourceType = sourceType,
                SourceId = sourceId,
                TaskType = taskType,
                TaskName = taskName,
                TaskData = taskData,
                Priority = priority,
                Status = AutomationTaskStatus.Pending,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };

            var result = await AddAsync(task);
            _logger.LogInformation("创建任务成功，TaskId: {TaskId}, 来源: {SourceType}#{SourceId}",
                result.Id, sourceType, sourceId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建任务失败，来源: {SourceType}#{SourceId}", sourceType, sourceId);
            throw;
        }
    }

    /// <summary>
    /// 批量创建任务
    /// </summary>
    public async Task<int> CreateTasksAsync(List<AutomationTask> tasks)
    {
        try
        {
            var now = DateTime.Now;
            foreach (var task in tasks)
            {
                task.CreatedTime = now;
                task.UpdatedTime = now;
                if (string.IsNullOrEmpty(task.Status))
                {
                    task.Status = AutomationTaskStatus.Pending;
                }
            }

            var result = await _db.Insertable(tasks).ExecuteCommandAsync();
            _logger.LogInformation("批量创建任务成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建任务失败");
            return 0;
        }
    }

    /// <summary>
    /// 检查任务依赖是否满足
    /// </summary>
    public async Task<bool> CheckDependenciesAsync(int taskId)
    {
        try
        {
            var task = await GetByIdAsync(taskId);
            if (task == null || string.IsNullOrEmpty(task.Dependencies))
            {
                return true; // 没有依赖或任务不存在
            }

            var dependencyIds = JsonSerializer.Deserialize<List<int>>(task.Dependencies);
            if (dependencyIds == null || dependencyIds.Count == 0)
            {
                return true;
            }

            // 检查所有依赖任务是否都已完成
            var completedCount = await _db.Queryable<AutomationTask>()
                .Where(t => dependencyIds.Contains(t.Id) &&
                    t.Status == AutomationTaskStatus.Completed &&
                    !t.IsDeleted)
                .CountAsync();

            return completedCount == dependencyIds.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查任务依赖失败，TaskId: {TaskId}", taskId);
            return false;
        }
    }

    /// <summary>
    /// 获取可执行的任务（依赖已满足）
    /// </summary>
    public async Task<List<AutomationTask>> GetExecutableTasksAsync(int? projectId = null, int maxCount = 10)
    {
        try
        {
            var query = _db.Queryable<AutomationTask>()
                .Where(t => !t.IsDeleted && t.Status == AutomationTaskStatus.Pending);

            if (projectId.HasValue)
            {
                query = query.Where(t => t.ProjectId == projectId.Value);
            }

            var pendingTasks = await query.ToListAsync();
            var executableTasks = new List<AutomationTask>();

            foreach (var task in pendingTasks)
            {
                if (await CheckDependenciesAsync(task.Id))
                {
                    executableTasks.Add(task);
                    if (executableTasks.Count >= maxCount)
                    {
                        break;
                    }
                }
            }

            _logger.LogInformation("找到 {Count} 个可执行任务", executableTasks.Count);
            return executableTasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可执行任务失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// 根据来源类型和ID获取项目ID
    /// </summary>
    private async Task<int> GetProjectIdFromSourceAsync(string sourceType, int sourceId)
    {
        return sourceType switch
        {
            AutomationTaskSourceType.DevelopmentStep =>
                await _db.Queryable<DevelopmentStep>()
                    .Where(s => s.Id == sourceId)
                    .Select(s => s.ProjectId)
                    .FirstAsync(),

            AutomationTaskSourceType.CodeGenerationTask =>
                await _db.Queryable<CodeGenerationTask>()
                    .Where(c => c.Id == sourceId)
                    .Select(c => c.ProjectId)
                    .FirstAsync(),

            AutomationTaskSourceType.TestTask =>
                await _db.Queryable<TestTask>()
                    .Where(t => t.Id == sourceId)
                    .Select(t => t.ProjectId)
                    .FirstAsync(),

            AutomationTaskSourceType.DeploymentTask =>
                await _db.Queryable<DeploymentTask>()
                    .Where(d => d.Id == sourceId)
                    .Select(d => d.ProjectId)
                    .FirstAsync(),

            _ => throw new ArgumentException($"不支持的来源类型: {sourceType}")
        };
    }
}
