-- =============================================
-- 项目步骤UI模板序列视图创建脚本（修复版）
-- 脚本名称: CreateProjectStepTemplateViews_Fixed.sql
-- 功能: 创建用于显示项目步骤与UI模板序列关联关系的视图
-- 创建日期: 2024-12-25
-- 版本: 1.1 (修复版)
-- =============================================

USE ProjectManagementAI;
GO

PRINT '========================================';
PRINT '  项目步骤UI模板序列视图创建脚本';
PRINT '========================================';
GO

-- =============================================
-- 1. 创建详细视图 vw_ProjectStepUITemplateSequences
-- =============================================

PRINT '正在创建详细视图 vw_ProjectStepUITemplateSequences...';
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepUITemplateSequences')
BEGIN
    DROP VIEW vw_ProjectStepUITemplateSequences;
END
GO

-- 创建详细视图
CREATE VIEW vw_ProjectStepUITemplateSequences
AS
SELECT
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,

    -- 步骤信息
    ds.Id AS StepId,
    ds.StepName,
    ds.StepDescription,
    ds.StepType,
    ds.Status AS StepStatus,
    ds.Priority AS StepPriority,
    ds.Progress AS StepProgress,
    ds.StepOrder,
    ds.StepLevel,
    ds.TechnologyStack AS StepTechnologyStack,
    ds.ComponentType,
    ds.FilePath,
    ds.EstimatedHours,
    ds.ActualHours,

    -- 模板序列信息
    uts.Id AS SequenceId,
    uts.Name AS SequenceName,
    uts.Description AS SequenceDescription,
    uts.Category AS SequenceCategory,
    uts.UsageCount AS SequenceUsageCount,
    uts.LastUsedTime AS SequenceLastUsedTime,
    uts.IsActive AS SequenceIsActive,

    -- 关联信息
    stsa.Id AS AssociationId,
    stsa.AppliedTime,
    stsa.Status AS AssociationStatus,
    stsa.Progress AS AssociationProgress,
    stsa.ExecutionStartTime,
    stsa.ExecutionEndTime,
    stsa.ExecutionResult,
    stsa.ErrorMessage,
    stsa.Notes AS AssociationNotes,

    -- 应用者信息
    appliedUser.Username AS AppliedByUsername,
    appliedUser.RealName AS AppliedByRealName,

    -- 项目负责人信息
    owner.Username AS ProjectOwnerUsername,
    owner.RealName AS ProjectOwnerRealName,

    -- 步骤创建者信息
    stepCreator.Username AS StepCreatedByUsername,
    stepCreator.RealName AS StepCreatedByRealName,

    -- 序列步骤统计
    (
        SELECT COUNT(*)
        FROM UIAutoMationTemplateSteps steps
        WHERE steps.SequenceId = uts.Id
        AND steps.IsDeleted = 0
        AND steps.IsActive = 1
    ) AS SequenceStepCount,

    -- 执行时长（分钟）
    CASE
        WHEN stsa.ExecutionStartTime IS NOT NULL AND stsa.ExecutionEndTime IS NOT NULL
        THEN DATEDIFF(MINUTE, stsa.ExecutionStartTime, stsa.ExecutionEndTime)
        ELSE NULL
    END AS ExecutionDurationMinutes,

    -- 是否正在执行
    CASE
        WHEN stsa.Status = 'Running' THEN 1
        ELSE 0
    END AS IsExecuting,

    -- 是否执行成功
    CASE
        WHEN stsa.Status = 'Completed' AND stsa.ExecutionResult IS NOT NULL THEN 1
        ELSE 0
    END AS IsExecutionSuccessful,

    -- 创建时间
    ds.CreatedTime AS StepCreatedTime,
    uts.CreatedTime AS SequenceCreatedTime,
    stsa.CreatedTime AS AssociationCreatedTime

FROM Projects p
    INNER JOIN DevelopmentSteps ds ON p.Id = ds.ProjectId
    INNER JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId
    INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
    LEFT JOIN Users owner ON p.OwnerId = owner.Id
    LEFT JOIN Users appliedUser ON stsa.AppliedByUserId = appliedUser.Id
    LEFT JOIN Users stepCreator ON ds.CreatedBy = stepCreator.Id

WHERE
    p.IsDeleted = 0
    AND ds.IsDeleted = 0
    AND stsa.IsDeleted = 0
    AND uts.IsDeleted = 0
    AND stsa.IsActive = 1;
GO

PRINT '✓ 详细视图 vw_ProjectStepUITemplateSequences 创建成功';
GO

-- =============================================
-- 2. 创建简化映射视图 vw_ProjectStepTemplateMapping
-- =============================================

PRINT '正在创建简化映射视图 vw_ProjectStepTemplateMapping...';
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepTemplateMapping')
BEGIN
    DROP VIEW vw_ProjectStepTemplateMapping;
END
GO

-- 创建简化映射视图
CREATE VIEW vw_ProjectStepTemplateMapping
AS
SELECT
    -- 项目基本信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,

    -- 步骤基本信息
    ds.Id AS StepId,
    ds.StepName,
    ds.StepType,
    ds.Status AS StepStatus,
    ds.Priority AS StepPriority,
    ds.StepOrder,
    ds.Progress AS StepProgress,

    -- 模板序列基本信息
    uts.Id AS SequenceId,
    uts.Name AS SequenceName,
    uts.Category AS SequenceCategory,
    uts.Description AS SequenceDescription,

    -- 关联状态
    stsa.Status AS AssociationStatus,
    stsa.Progress AS AssociationProgress,
    stsa.AppliedTime,

    -- 序列步骤数量
    (
        SELECT COUNT(*)
        FROM UIAutoMationTemplateSteps steps
        WHERE steps.SequenceId = uts.Id
        AND steps.IsDeleted = 0
        AND steps.IsActive = 1
    ) AS TemplateStepCount,

    -- 应用者
    appliedUser.RealName AS AppliedBy,

    -- 状态标识
    CASE
        WHEN stsa.Status = 'Running' THEN 'Running'
        WHEN stsa.Status = 'Completed' THEN 'Completed'
        WHEN stsa.Status = 'Failed' THEN 'Failed'
        WHEN stsa.Status = 'Active' THEN 'Active'
        ELSE 'Unknown'
    END AS StatusText,

    -- 进度百分比显示
    CAST(stsa.Progress AS NVARCHAR) + '%' AS ProgressDisplay,

    -- 关联时长（天数）
    DATEDIFF(DAY, stsa.AppliedTime, GETDATE()) AS DaysSinceApplied

FROM Projects p
    INNER JOIN DevelopmentSteps ds ON p.Id = ds.ProjectId
    INNER JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId
    INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
    LEFT JOIN Users appliedUser ON stsa.AppliedByUserId = appliedUser.Id

WHERE
    p.IsDeleted = 0
    AND ds.IsDeleted = 0
    AND stsa.IsDeleted = 0
    AND uts.IsDeleted = 0
    AND stsa.IsActive = 1;
GO

PRINT '✓ 简化映射视图 vw_ProjectStepTemplateMapping 创建成功';
GO

-- =============================================
-- 3. 创建统计分析视图 vw_ProjectTemplateUsageStats
-- =============================================

PRINT '正在创建统计分析视图 vw_ProjectTemplateUsageStats...';
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectTemplateUsageStats')
BEGIN
    DROP VIEW vw_ProjectTemplateUsageStats;
END
GO

-- 创建统计分析视图
CREATE VIEW vw_ProjectTemplateUsageStats
AS
SELECT
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,
    owner.RealName AS ProjectOwner,

    -- 步骤统计
    (
        SELECT COUNT(*)
        FROM DevelopmentSteps ds
        WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
    ) AS TotalSteps,

    (
        SELECT COUNT(DISTINCT stsa.StepId)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS StepsWithTemplates,

    -- 模板序列统计
    (
        SELECT COUNT(DISTINCT stsa.SequenceId)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS UniqueSequencesUsed,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS TotalAssociations,

    -- 执行状态统计
    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Completed'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS CompletedAssociations,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Running'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS RunningAssociations,

    (
        SELECT COUNT(*)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.Status = 'Failed'
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS FailedAssociations,

    -- 最常用的模板序列
    (
        SELECT TOP 1 uts.Name
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
        AND uts.IsDeleted = 0
        GROUP BY uts.Id, uts.Name
        ORDER BY COUNT(*) DESC
    ) AS MostUsedSequence,

    -- 平均执行进度
    (
        SELECT AVG(CAST(stsa.Progress AS FLOAT))
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS AverageProgress,

    -- 成功率
    CASE
        WHEN (
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) > 0
        THEN CAST((
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.Status = 'Completed'
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) AS FLOAT) * 100.0 / (
            SELECT COUNT(*)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        )
        ELSE 0
    END AS SuccessRate,

    -- 模板覆盖率
    CASE
        WHEN (
            SELECT COUNT(*)
            FROM DevelopmentSteps ds
            WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
        ) > 0
        THEN CAST((
            SELECT COUNT(DISTINCT stsa.StepId)
            FROM StepTemplateSequenceAssociations stsa
            INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
            WHERE ds.ProjectId = p.Id
            AND stsa.IsDeleted = 0
            AND stsa.IsActive = 1
        ) AS FLOAT) * 100.0 / (
            SELECT COUNT(*)
            FROM DevelopmentSteps ds
            WHERE ds.ProjectId = p.Id AND ds.IsDeleted = 0
        )
        ELSE 0
    END AS TemplateCoverageRate,

    -- 最近应用时间
    (
        SELECT MAX(stsa.AppliedTime)
        FROM StepTemplateSequenceAssociations stsa
        INNER JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id
        WHERE ds.ProjectId = p.Id
        AND stsa.IsDeleted = 0
        AND stsa.IsActive = 1
    ) AS LastAppliedTime,

    -- 项目创建时间
    p.CreatedTime AS ProjectCreatedTime

FROM Projects p
    LEFT JOIN Users owner ON p.OwnerId = owner.Id
WHERE p.IsDeleted = 0;
GO

PRINT '✓ 统计分析视图 vw_ProjectTemplateUsageStats 创建成功';
GO

-- =============================================
-- 验证和完成
-- =============================================

PRINT '';
PRINT '正在验证视图创建结果...';
GO

DECLARE @CreatedViews INT = 0;

IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepUITemplateSequences')
    SET @CreatedViews = @CreatedViews + 1;

IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepTemplateMapping')
    SET @CreatedViews = @CreatedViews + 1;

IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectTemplateUsageStats')
    SET @CreatedViews = @CreatedViews + 1;

IF @CreatedViews = 3
BEGIN
    PRINT '✅ 所有视图创建成功！';
    PRINT '已创建的视图: vw_ProjectStepUITemplateSequences, vw_ProjectStepTemplateMapping, vw_ProjectTemplateUsageStats';
END
ELSE
BEGIN
    PRINT '⚠️  部分视图创建失败，已创建 ' + CAST(@CreatedViews AS NVARCHAR) + ' 个视图（共3个）';
END

PRINT '';
PRINT '✅ 脚本执行完成！';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
