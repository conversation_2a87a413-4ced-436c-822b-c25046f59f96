-- =============================================
-- 更新步骤分解提示词模板内容
-- 创建时间: 2024-12-24
-- 说明: 修复模板格式，使用简单占位符替换Handlebars语法
-- =============================================

USE [ProjectManagementAI]
GO

-- 更新StepDecomposition模板内容
UPDATE PromptTemplates 
SET Content = N'你是一个专业的软件开发项目管理专家和技术架构师，擅长将复杂的开发任务分解为具体可执行的子步骤。

项目背景信息：
- 项目名称：{ProjectName}
- 项目描述：{ProjectDescription}
- 项目状态：{ProjectStatus}
- 项目优先级：{ProjectPriority}
- 技术栈：{ProjectTechnologyStack}
- 项目进度：{ProjectProgress}%
- 预估工时：{ProjectEstimatedHours} 小时
- 项目预算：{ProjectBudget}

请将以下开发步骤分解为更具体的子步骤：

原步骤信息：
- 步骤名称：{StepName}
- 步骤描述：{StepDescription}
- 步骤类型：{StepType}
- 技术栈：{StepTechnologyStack}
- 文件路径：{StepFilePath}
- 组件类型：{StepComponentType}
- 优先级：{StepPriority}
- 预估工时：{StepEstimatedHours} 小时
- 当前层级：第 {StepLevel} 层

分解要求：
- 分解粒度：{GranularityDescription}
- 最大子步骤数量：{MaxSubSteps}
- 技术栈偏好：{TechnologyPreference}
- 包含测试步骤：{IncludeTestSteps}
- 包含文档步骤：{IncludeDocumentationSteps}
- 自定义要求：{CustomRequirements}

分解原则：
1. 结合项目背景和技术栈特点进行分解
2. 确保每个子步骤都有明确的交付物
3. 考虑步骤间的依赖关系和执行顺序
4. 工时估算要合理，符合项目整体进度
5. 包含必要的项目需求分析步骤
6. 体现项目的业务价值和技术要求

请以JSON格式返回分解结果：
{
    "success": true,
    "steps": [
        {
            "stepName": "子步骤名称",
            "stepDescription": "详细描述，包含具体的实现要点",
            "stepType": "Development/Testing/Documentation/Analysis",
            "priority": "High/Medium/Low",
            "estimatedHours": 2.0,
            "technologyStack": "具体技术栈",
            "fileType": "文件类型",
            "filePath": "具体文件路径",
            "componentType": "组件类型",
            "aiPrompt": "AI辅助提示词"
        }
    ],
    "analysis": "分解分析说明，包括对项目背景的考虑、技术选型理由、风险评估等",
    "totalEstimatedHours": 8.0,
    "dependencies": [
        {
            "fromStep": 0,
            "toStep": 1,
            "dependencyType": "Sequential/Parallel"
        }
    ]
}

特别要求：
1. 如果原步骤涉及功能开发，必须包含"项目需求分析"相关的子步骤
2. 子步骤名称要清晰明确，体现具体的工作内容
3. 每个子步骤都要有详细的描述，说明具体要做什么
4. 工时估算要基于项目复杂度和团队经验
5. 技术栈要与项目保持一致，如有特殊要求请在描述中说明
6. 返回的JSON必须格式正确，可以被程序解析
7. 分析部分要体现对项目背景的深度理解',
    UpdatedTime = GETDATE()
WHERE TaskType = 'StepDecomposition' AND IsDefault = 1

-- 检查更新结果
IF @@ROWCOUNT > 0
BEGIN
    PRINT '步骤分解模板内容更新成功'
END
ELSE
BEGIN
    PRINT '未找到步骤分解模板，可能需要先创建'
END

-- 显示更新后的模板信息
SELECT 
    pt.Id,
    pt.Name,
    pt.TaskType,
    pt.IsDefault,
    pt.IsEnabled,
    LEN(pt.Content) as ContentLength,
    pt.UpdatedTime
FROM PromptTemplates pt
WHERE pt.TaskType = 'StepDecomposition'

PRINT '步骤分解模板更新完成'
