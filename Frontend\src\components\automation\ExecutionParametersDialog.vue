<template>
  <el-dialog
    v-model="visible"
    :title="`执行序列: ${sequence?.name || ''}`"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="sequence" class="execution-content">
      <!-- 序列信息 -->
      <el-card class="sequence-info">
        <template #header>
          <span>序列信息</span>
        </template>
        <div class="info-item">
          <span class="label">名称:</span>
          <span>{{ sequence.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">描述:</span>
          <span>{{ sequence.description }}</span>
        </div>
        <div class="info-item">
          <span class="label">步骤数:</span>
          <span>{{ sequence.steps?.length || 0 }} 个步骤</span>
        </div>
      </el-card>

      <!-- 参数配置 -->
      <el-card class="parameters-config">
        <template #header>
          <span>执行参数</span>
        </template>

        <el-form
          ref="formRef"
          :model="form"
          label-width="120px"
        >
          <!-- 动态参数 -->
          <div v-if="dynamicParameters.length > 0">
            <h4>动态参数</h4>
            <p class="param-tip">以下参数在序列步骤中被引用，请设置具体值：</p>
            
            <el-form-item
              v-for="param in dynamicParameters"
              :key="param"
              :label="param"
            >
              <el-input
                v-model="form.parameters[param]"
                :placeholder="`请输入 ${param} 的值`"
              />
            </el-form-item>
          </div>

          <!-- 执行选项 -->
          <h4>执行选项</h4>
          
          <el-form-item label="执行模式">
            <el-radio-group v-model="form.executionMode">
              <el-radio label="normal">正常执行</el-radio>
              <el-radio label="debug">调试模式</el-radio>
              <el-radio label="dry-run">预演模式</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="失败处理">
            <el-radio-group v-model="form.failureHandling">
              <el-radio label="stop">遇到错误停止</el-radio>
              <el-radio label="continue">忽略错误继续</el-radio>
              <el-radio label="retry">自动重试</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="截图设置">
            <el-checkbox-group v-model="form.screenshotOptions">
              <el-checkbox label="before-step">步骤执行前截图</el-checkbox>
              <el-checkbox label="after-step">步骤执行后截图</el-checkbox>
              <el-checkbox label="on-error">出错时截图</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="执行延迟">
            <el-input-number
              v-model="form.stepDelay"
              :min="0"
              :max="10"
              :step="0.1"
              style="width: 200px"
            />
            <span class="unit">秒 (步骤间延迟)</span>
          </el-form-item>

          <el-form-item label="超时设置">
            <el-input-number
              v-model="form.globalTimeout"
              :min="60"
              :max="3600"
              style="width: 200px"
            />
            <span class="unit">秒 (整体超时)</span>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 步骤预览 -->
      <el-card class="steps-preview">
        <template #header>
          <span>执行步骤预览</span>
        </template>
        
        <div class="steps-list">
          <div
            v-for="(step, index) in sequence.steps"
            :key="step.id || index"
            class="step-item"
            :class="{ disabled: !step.isActive }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-header">
                <span class="step-action">{{ getActionTypeLabel(step.actionType) }}</span>
                <el-tag v-if="!step.isActive" type="info" size="small">已禁用</el-tag>
              </div>
              <div class="step-description">{{ step.description }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="executing" @click="handleExecute">
          <el-icon><VideoPlay /></el-icon>
          开始执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'
import type { TemplateSequence } from '@/services/customTemplate'

// Props
interface Props {
  modelValue: boolean
  sequence?: TemplateSequence | null
}

const props = withDefaults(defineProps<Props>(), {
  sequence: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  execute: [parameters: Record<string, any>]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const executing = ref(false)

// 表单数据
const form = reactive({
  parameters: {} as Record<string, any>,
  executionMode: 'normal',
  failureHandling: 'stop',
  screenshotOptions: ['on-error'],
  stepDelay: 1.0,
  globalTimeout: 300
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 提取动态参数
const dynamicParameters = computed(() => {
  if (!props.sequence?.steps) return []
  
  const params = new Set<string>()
  
  props.sequence.steps.forEach(step => {
    if (step.parameters) {
      // 查找 {variable} 格式的参数
      const paramStr = JSON.stringify(step.parameters)
      const matches = paramStr.match(/\{([^}]+)\}/g)
      if (matches) {
        matches.forEach(match => {
          const param = match.slice(1, -1) // 移除 { }
          params.add(param)
        })
      }
    }
  })
  
  return Array.from(params)
})

// 监听器
watch(() => props.sequence, (newSequence) => {
  if (newSequence) {
    // 重置参数
    form.parameters = {}
    // 为动态参数设置默认值
    dynamicParameters.value.forEach(param => {
      form.parameters[param] = ''
    })
  }
}, { immediate: true })

// 方法
const getActionTypeLabel = (actionType: string) => {
  const labels: Record<string, string> = {
    'click': '点击',
    'wait': '等待',
    'input': '输入',
    'delay': '延迟',
    'screenshot': '截图',
    'verify': '验证',
    'scroll': '滚动',
    'key_press': '按键'
  }
  return labels[actionType] || actionType
}

const handleExecute = async () => {
  try {
    // 验证必填参数
    const missingParams = dynamicParameters.value.filter(param => !form.parameters[param])
    if (missingParams.length > 0) {
      ElMessage.warning(`请填写必填参数: ${missingParams.join(', ')}`)
      return
    }

    executing.value = true
    
    // 构建执行参数
    const executionParams = {
      ...form.parameters,
      _execution_options: {
        mode: form.executionMode,
        failureHandling: form.failureHandling,
        screenshotOptions: form.screenshotOptions,
        stepDelay: form.stepDelay,
        globalTimeout: form.globalTimeout
      }
    }

    emit('execute', executionParams)
    handleClose()
  } catch (error) {
    console.error('执行失败:', error)
    ElMessage.error('执行失败')
  } finally {
    executing.value = false
  }
}

const handleClose = () => {
  visible.value = false
  executing.value = false
}
</script>

<style scoped lang="scss">
.execution-content {
  .sequence-info {
    margin-bottom: 20px;

    .info-item {
      display: flex;
      margin-bottom: 8px;

      .label {
        min-width: 60px;
        color: #909399;
        font-weight: 500;
      }
    }
  }

  .parameters-config {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 8px;
    }

    .param-tip {
      margin: 0 0 16px 0;
      color: #606266;
      font-size: 14px;
      background-color: #f0f9ff;
      padding: 8px 12px;
      border-radius: 4px;
      border-left: 3px solid #409eff;
    }

    .unit {
      margin-left: 8px;
      color: #909399;
      font-size: 12px;
    }
  }

  .steps-preview {
    .steps-list {
      .step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #409eff;

        &.disabled {
          opacity: 0.6;
          border-left-color: #c0c4cc;
        }

        .step-number {
          width: 24px;
          height: 24px;
          background-color: #409eff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .step-content {
          flex: 1;

          .step-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .step-action {
              font-weight: 500;
              color: #303133;
            }
          }

          .step-description {
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
