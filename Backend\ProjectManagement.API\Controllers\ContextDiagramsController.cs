using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;
using ProjectManagement.Core.DTOs;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 上下文图管理控制器
/// </summary>
[ApiController]
[Route("api/context-diagrams")]
[Authorize]
public class ContextDiagramsController : ControllerBase
{
    private readonly IRepository<ContextDiagram> _contextDiagramRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly ILogger<ContextDiagramsController> _logger;

    public ContextDiagramsController(
        IRepository<ContextDiagram> contextDiagramRepository,
        IRepository<Project> projectRepository,
        ILogger<ContextDiagramsController> logger)
    {
        _contextDiagramRepository = contextDiagramRepository;
        _projectRepository = projectRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取上下文图详情
    /// </summary>
    /// <param name="id">上下文图ID</param>
    /// <returns>上下文图详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ContextDiagramDto>> GetContextDiagram(int id)
    {
        try
        {
            var contextDiagram = await _contextDiagramRepository.GetByIdAsync(id);
            if (contextDiagram == null)
            {
                return NotFound(new { message = "上下文图不存在" });
            }

            var result = new ContextDiagramDto
            {
                Id = contextDiagram.Id,
                ProjectId = contextDiagram.ProjectId,
                RequirementDocumentId = contextDiagram.RequirementDocumentId,
                DiagramName = contextDiagram.DiagramName,
                MermaidDefinition = contextDiagram.MermaidDefinition,
                ExternalEntities = contextDiagram.ExternalEntities,
                SystemBoundary = contextDiagram.SystemBoundary,
                DataFlows = contextDiagram.DataFlows,
                Version = contextDiagram.DiagramVersion,
                CreatedAt = contextDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = contextDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取上下文图详情时发生错误: {ContextDiagramId}", id);
            return StatusCode(500, new { message = "获取上下文图详情失败" });
        }
    }

    /// <summary>
    /// 创建上下文图
    /// </summary>
    /// <param name="request">创建上下文图请求</param>
    /// <returns>创建的上下文图</returns>
    [HttpPost]
    public async Task<ActionResult<ContextDiagramDto>> CreateContextDiagram([FromBody] CreateContextDiagramRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("创建上下文图: 项目 {ProjectId}, 用户 {UserId}", request.ProjectId, userId);

            // 验证项目是否存在
            var project = await _projectRepository.GetByIdAsync(request.ProjectId);
            if (project == null)
            {
                return NotFound(new { message = "项目不存在" });
            }

            // 创建上下文图实体
            var contextDiagram = new ContextDiagram
            {
                ProjectId = request.ProjectId,
                RequirementDocumentId = request.RequirementDocumentId,
                DiagramName = request.DiagramName,
                MermaidDefinition = request.MermaidDefinition,
                ExternalEntities = request.ExternalEntities,
                SystemBoundary = request.SystemBoundary,
                DataFlows = request.DataFlows,
                DiagramVersion = "1.0"
            };

            // 保存到数据库
            var savedDiagram = await _contextDiagramRepository.AddAsync(contextDiagram);

            // 构建返回结果
            var result = new ContextDiagramDto
            {
                Id = savedDiagram.Id,
                ProjectId = savedDiagram.ProjectId,
                RequirementDocumentId = savedDiagram.RequirementDocumentId,
                DiagramName = savedDiagram.DiagramName,
                MermaidDefinition = savedDiagram.MermaidDefinition,
                ExternalEntities = savedDiagram.ExternalEntities,
                SystemBoundary = savedDiagram.SystemBoundary,
                DataFlows = savedDiagram.DataFlows,
                Version = savedDiagram.DiagramVersion,
                CreatedAt = savedDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = savedDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            _logger.LogInformation("上下文图创建成功: {ContextDiagramId}", savedDiagram.Id);
            return CreatedAtAction(nameof(GetContextDiagram), new { id = savedDiagram.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建上下文图时发生错误");
            return StatusCode(500, new { message = "创建上下文图失败" });
        }
    }

    /// <summary>
    /// 更新上下文图
    /// </summary>
    /// <param name="id">上下文图ID</param>
    /// <param name="request">更新上下文图请求</param>
    /// <returns>更新后的上下文图</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ContextDiagramDto>> UpdateContextDiagram(int id, [FromBody] UpdateContextDiagramRequestDto request)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("更新上下文图: {ContextDiagramId}, 用户 {UserId}", id, userId);

            var contextDiagram = await _contextDiagramRepository.GetByIdAsync(id);
            if (contextDiagram == null)
            {
                return NotFound(new { message = "上下文图不存在" });
            }

            // 更新字段
            if (!string.IsNullOrEmpty(request.DiagramName))
                contextDiagram.DiagramName = request.DiagramName;

            if (!string.IsNullOrEmpty(request.MermaidDefinition))
                contextDiagram.MermaidDefinition = request.MermaidDefinition;

            if (request.ExternalEntities != null)
                contextDiagram.ExternalEntities = request.ExternalEntities;

            if (request.SystemBoundary != null)
                contextDiagram.SystemBoundary = request.SystemBoundary;

            if (request.DataFlows != null)
                contextDiagram.DataFlows = request.DataFlows;

            if (!string.IsNullOrEmpty(request.Version))
                contextDiagram.DiagramVersion = request.Version;

            contextDiagram.UpdatedTime = DateTime.Now;

            // 保存更新
            await _contextDiagramRepository.UpdateAsync(contextDiagram);

            // 构建返回结果
            var result = new ContextDiagramDto
            {
                Id = contextDiagram.Id,
                ProjectId = contextDiagram.ProjectId,
                RequirementDocumentId = contextDiagram.RequirementDocumentId,
                DiagramName = contextDiagram.DiagramName,
                MermaidDefinition = contextDiagram.MermaidDefinition,
                ExternalEntities = contextDiagram.ExternalEntities,
                SystemBoundary = contextDiagram.SystemBoundary,
                DataFlows = contextDiagram.DataFlows,
                Version = contextDiagram.DiagramVersion,
                CreatedAt = contextDiagram.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdatedAt = contextDiagram.UpdatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            };

            _logger.LogInformation("上下文图更新成功: {ContextDiagramId}", id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新上下文图时发生错误: {ContextDiagramId}", id);
            return StatusCode(500, new { message = "更新上下文图失败" });
        }
    }

    /// <summary>
    /// 删除上下文图
    /// </summary>
    /// <param name="id">上下文图ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteContextDiagram(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("删除上下文图: {ContextDiagramId}, 用户 {UserId}", id, userId);

            var contextDiagram = await _contextDiagramRepository.GetByIdAsync(id);
            if (contextDiagram == null)
            {
                return NotFound(new { message = "上下文图不存在" });
            }

            await _contextDiagramRepository.DeleteAsync(id);

            _logger.LogInformation("上下文图删除成功: {ContextDiagramId}", id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除上下文图时发生错误: {ContextDiagramId}", id);
            return StatusCode(500, new { message = "删除上下文图失败" });
        }
    }

    /// <summary>
    /// 导出上下文图
    /// </summary>
    /// <param name="id">上下文图ID</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出文件</returns>
    [HttpGet("{id}/export")]
    public async Task<ActionResult> ExportContextDiagram(int id, [FromQuery] string format = "svg")
    {
        try
        {
            var contextDiagram = await _contextDiagramRepository.GetByIdAsync(id);
            if (contextDiagram == null)
            {
                return NotFound(new { message = "上下文图不存在" });
            }

            // TODO: 实现图表导出逻辑
            // 这里需要集成Mermaid渲染引擎或使用第三方服务

            var fileName = $"context-diagram-{id}.{format}";
            var contentType = format.ToLower() switch
            {
                "svg" => "image/svg+xml",
                "png" => "image/png",
                "pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            // 临时返回Mermaid定义
            var content = System.Text.Encoding.UTF8.GetBytes(contextDiagram.MermaidDefinition);
            return File(content, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出上下文图时发生错误: {ContextDiagramId}", id);
            return StatusCode(500, new { message = "导出上下文图失败" });
        }
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        return int.Parse(userIdClaim!.Value);
    }
}
