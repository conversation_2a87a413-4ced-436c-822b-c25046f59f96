using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Enums;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 需求文档仓储接口
/// </summary>
public interface IRequirementDocumentRepository : IRepository<RequirementDocument>
{
    /// <summary>
    /// 根据项目获取需求文档列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>需求文档列表</returns>
    Task<List<RequirementDocument>> GetRequirementDocumentsByProjectAsync(int projectId);

    /// <summary>
    /// 根据标题搜索需求文档
    /// </summary>
    /// <param name="title">标题关键词</param>
    /// <returns>需求文档列表</returns>
    Task<List<RequirementDocument>> GetByTitleAsync(string title);

    /// <summary>
    /// 根据状态获取需求文档列表
    /// </summary>
    /// <param name="status">文档状态</param>
    /// <returns>需求文档列表</returns>
    Task<List<RequirementDocument>> GetByStatusAsync(string status);

    /// <summary>
    /// 根据生成方式获取需求文档列表
    /// </summary>
    /// <param name="generatedBy">生成方式</param>
    /// <returns>需求文档列表</returns>
    Task<List<RequirementDocument>> GetByGeneratedByAsync(string generatedBy);

    /// <summary>
    /// 更新需求文档状态
    /// </summary>
    /// <param name="documentId">需求文档ID</param>
    /// <param name="status">文档状态</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateStatusAsync(int documentId, string status, int? updatedBy = null);

    /// <summary>
    /// 更新需求文档内容
    /// </summary>
    /// <param name="documentId">需求文档ID</param>
    /// <param name="content">文档内容</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateContentAsync(int documentId, string content, int? updatedBy = null);

    /// <summary>
    /// 搜索需求文档
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="projectId">项目ID（可选）</param>
    /// <param name="status">文档状态（可选）</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    Task<ProjectManagement.Core.Interfaces.PagedResult<RequirementDocument>> SearchDocumentsAsync(
        string keyword,
        int? projectId = null,
        string? status = null,
        int pageIndex = 1,
        int pageSize = 20);

    /// <summary>
    /// 获取需求文档统计信息
    /// </summary>
    /// <param name="projectId">项目ID（可选）</param>
    /// <returns>需求文档统计</returns>
    Task<RequirementDocumentStatistics> GetDocumentStatisticsAsync(int? projectId = null);
}

/// <summary>
/// 需求文档统计信息
/// </summary>
public class RequirementDocumentStatistics
{
    /// <summary>
    /// 总文档数
    /// </summary>
    public int TotalDocuments { get; set; }

    /// <summary>
    /// 草稿文档数
    /// </summary>
    public int DraftDocuments { get; set; }

    /// <summary>
    /// 审核中文档数
    /// </summary>
    public int ReviewDocuments { get; set; }

    /// <summary>
    /// 已批准文档数
    /// </summary>
    public int ApprovedDocuments { get; set; }

    /// <summary>
    /// AI生成文档数
    /// </summary>
    public int AIGeneratedDocuments { get; set; }

    /// <summary>
    /// 手动创建文档数
    /// </summary>
    public int ManualDocuments { get; set; }

    /// <summary>
    /// 按状态分组的文档数
    /// </summary>
    public Dictionary<string, int> DocumentsByStatus { get; set; } = new();

    /// <summary>
    /// 按生成方式分组的文档数
    /// </summary>
    public Dictionary<string, int> DocumentsByGeneratedBy { get; set; } = new();
}
