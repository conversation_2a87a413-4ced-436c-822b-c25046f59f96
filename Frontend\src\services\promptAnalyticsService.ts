import { ApiService } from './api'

// 类型定义
export interface TemplateUsageAnalytics {
  templateId: number
  templateName: string
  totalUsage: number
  uniqueUsers: number
  successRate: number
  averageResponseTime: number
  totalCost: number
  averageRating: number
  dailyUsage: DailyUsage[]
  hourlyUsage: HourlyUsage[]
  providerDistribution: Record<string, number>
  errorTypes: Record<string, number>
}

export interface UserUsageAnalytics {
  userId: number
  userName: string
  totalUsage: number
  uniqueTemplates: number
  mostUsedTaskType: string
  preferredProvider: string
  averageSessionDuration: number
  totalCost: number
  topTemplates: TemplateUsageSummary[]
  dailyActivity: DailyUsage[]
}

export interface OverallUsageAnalytics {
  totalUsers: number
  activeUsers: number
  totalTemplates: number
  totalUsage: number
  overallSuccessRate: number
  totalCost: number
  averageResponseTime: number
  dailyTrends: DailyUsage[]
  taskTypeDistribution: Record<string, number>
  providerDistribution: Record<string, number>
  topUsers: TopUser[]
}

export interface TemplateRanking {
  templateId: number
  templateName: string
  taskType: string
  usageCount: number
  successRate: number
  averageRating: number
  rank: number
  trendScore: number
}

export interface ProviderUsageStats {
  provider: string
  usageCount: number
  successRate: number
  averageResponseTime: number
  totalCost: number
  averageCost: number
  popularModels: string[]
}

export interface TaskTypeUsageStats {
  taskType: string
  taskTypeName: string
  usageCount: number
  templateCount: number
  successRate: number
  averageRating: number
  topTemplates: TemplateUsageSummary[]
}

export interface TemplateEffectivenessAnalysis {
  templateId: number
  templateName: string
  effectivenessScore: number
  qualityScore: number
  usabilityScore: number
  performanceScore: number
  costEfficiencyScore: number
  strengths: string[]
  weaknesses: string[]
  improvementAreas: string[]
  metricTrends: Record<string, number>
}

export interface TemplateComparisonResult {
  templateA: TemplateComparisonItem
  templateB: TemplateComparisonItem
  summary: ComparisonSummary
  metricComparisons: MetricComparison[]
  recommendation: string
}

export interface OptimizationSuggestion {
  type: string
  title: string
  description: string
  priority: string
  impactScore: number
  implementationDifficulty: number
  actionItems: string[]
  expectedOutcome: string
}

export interface ABTestResult {
  testName: string
  startDate: string
  endDate: string
  templateA: TemplateTestResult
  templateB: TemplateTestResult
  statistics: TestStatistics
  conclusion: string
  isStatisticallySignificant: boolean
  confidenceLevel: number
}

export interface CostAnalysis {
  totalCost: number
  averageCostPerRequest: number
  costByProvider: Record<string, number>
  costByTaskType: Record<string, number>
  dailyCosts: DailyCost[]
  optimizationSuggestions: CostOptimizationSuggestion[]
}

export interface PerformanceAnalysis {
  averageResponseTime: number
  medianResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  responseTimeByProvider: Record<string, number>
  responseTimeByTaskType: Record<string, number>
  bottlenecks: PerformanceBottleneck[]
}

export interface AnalyticsReport {
  reportId: string
  type: ReportType
  generatedAt: string
  parameters: ReportParameters
  summary: string
  data: Record<string, any>
  keyInsights: string[]
  recommendations: string[]
}

// 辅助类型
export interface DailyUsage {
  date: string
  count: number
  successRate: number
}

export interface HourlyUsage {
  hour: number
  count: number
}

export interface TemplateUsageSummary {
  templateId: number
  templateName: string
  usageCount: number
  successRate: number
}

export interface TopUser {
  userId: number
  userName: string
  usageCount: number
}

export interface TemplateComparisonItem {
  templateId: number
  templateName: string
  metrics: Record<string, number>
}

export interface ComparisonSummary {
  winner: string
  confidenceLevel: number
  keyDifferences: string[]
}

export interface MetricComparison {
  metricName: string
  valueA: number
  valueB: number
  difference: number
  differencePercentage: number
  winner: string
  isSignificant: boolean
}

export interface TemplateTestResult {
  templateId: number
  templateName: string
  sampleSize: number
  successRate: number
  averageRating: number
  averageResponseTime: number
  averageCost: number
}

export interface TestStatistics {
  pValue: number
  effectSize: number
  powerAnalysis: number
  statisticalTest: string
}

export interface DailyCost {
  date: string
  cost: number
}

export interface CostOptimizationSuggestion {
  title: string
  description: string
  potentialSavings: number
}

export interface PerformanceBottleneck {
  component: string
  description: string
  impactScore: number
}

export enum ReportType {
  Usage = 'Usage',
  Performance = 'Performance',
  Cost = 'Cost',
  Quality = 'Quality',
  Comparison = 'Comparison',
  Optimization = 'Optimization'
}

export interface ReportParameters {
  startDate?: string
  endDate?: string
  templateIds: number[]
  userIds: number[]
  taskTypes: string[]
  providers: string[]
  customParameters: Record<string, any>
}

export class PromptAnalyticsService {
  // 模板分析
  async getTemplateUsageAnalytics(templateId: number, startDate?: Date, endDate?: Date): Promise<TemplateUsageAnalytics> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/templates/${templateId}/usage?${params}`)
  }

  async getTemplateEffectiveness(templateId: number): Promise<TemplateEffectivenessAnalysis> {
    return ApiService.get(`/api/promptanalytics/templates/${templateId}/effectiveness`)
  }

  async getOptimizationSuggestions(templateId: number): Promise<OptimizationSuggestion[]> {
    return ApiService.get(`/api/promptanalytics/templates/${templateId}/optimization-suggestions`)
  }

  async compareTemplates(templateId1: number, templateId2: number): Promise<TemplateComparisonResult> {
    return ApiService.get(`/api/promptanalytics/templates/compare?templateId1=${templateId1}&templateId2=${templateId2}`)
  }

  // 用户分析
  async getUserUsageAnalytics(userId: number, startDate?: Date, endDate?: Date): Promise<UserUsageAnalytics> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/users/${userId}/usage?${params}`)
  }

  async getMyUsageAnalytics(startDate?: Date, endDate?: Date): Promise<UserUsageAnalytics> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/my-usage?${params}`)
  }

  // 整体分析
  async getOverallUsageAnalytics(startDate?: Date, endDate?: Date): Promise<OverallUsageAnalytics> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/overall?${params}`)
  }

  async getPopularTemplatesRanking(count: number = 10, startDate?: Date, endDate?: Date): Promise<TemplateRanking[]> {
    const params = new URLSearchParams()
    params.append('count', count.toString())
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/templates/popular?${params}`)
  }

  async getProviderUsageStats(startDate?: Date, endDate?: Date): Promise<ProviderUsageStats[]> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/providers/usage?${params}`)
  }

  async getTaskTypeUsageStats(startDate?: Date, endDate?: Date): Promise<TaskTypeUsageStats[]> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/task-types/usage?${params}`)
  }

  // A/B测试
  async analyzeABTest(templateAId: number, templateBId: number, startDate: Date, endDate: Date): Promise<ABTestResult> {
    return ApiService.post('/api/promptanalytics/ab-test', {
      templateAId,
      templateBId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    })
  }

  // 成本和性能分析
  async getCostAnalysis(startDate?: Date, endDate?: Date): Promise<CostAnalysis> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/cost-analysis?${params}`)
  }

  async getPerformanceAnalysis(startDate?: Date, endDate?: Date): Promise<PerformanceAnalysis> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate.toISOString())
    if (endDate) params.append('endDate', endDate.toISOString())

    return ApiService.get(`/api/promptanalytics/performance-analysis?${params}`)
  }

  // 报告生成
  async generateReport(reportType: ReportType, parameters: ReportParameters): Promise<AnalyticsReport> {
    return ApiService.post('/api/promptanalytics/reports', {
      reportType,
      parameters
    })
  }
}
