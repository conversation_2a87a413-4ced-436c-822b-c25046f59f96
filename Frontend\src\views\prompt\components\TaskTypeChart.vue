<template>
  <div class="task-type-chart">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  data: any[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.data) return

  const option = {
    title: {
      text: '任务类型分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.name),
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '使用次数'
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        barWidth: '60%',
        data: props.data.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: getColor(index)
          }
        }))
      }
    ]
  }

  chartInstance.setOption(option)
}

const getColor = (index: number) => {
  const colors = [
    '#409EFF',
    '#67C23A', 
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#C0C4CC',
    '#606266',
    '#303133'
  ]
  return colors[index % colors.length]
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  window.addEventListener('resize', resizeChart)
})

watch(() => props.data, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && chartContainer.value && !chartInstance) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style scoped>
.task-type-chart {
  width: 100%;
  height: 400px;
}

.loading-container {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
