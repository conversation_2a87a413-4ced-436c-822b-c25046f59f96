<template>
  <el-dialog
    v-model="visible"
    title="添加开发步骤到编码任务"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="dialog-content">
      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索步骤名称或描述"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filterStatus"
              placeholder="步骤状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="未开始" value="NotStarted" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="已阻塞" value="Blocked" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤列表 -->
      <div class="steps-section">
        <div class="section-header">
          <h4>可用的开发步骤</h4>
          <div class="selection-info">
            已选择 {{ selectedSteps.length }} 个步骤
          </div>
        </div>

        <el-table
          :data="availableSteps"
          v-loading="loading"
          style="width: 100%"
          max-height="400px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="stepName" label="步骤名称" min-width="200">
            <template #default="{ row }">
              <div class="step-name-cell">
                <div class="step-title">{{ row.stepName }}</div>
                <div class="step-description">{{ row.stepDescription }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="stepType" label="类型" width="100">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row.stepType }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="estimatedHours" label="预估工时" width="100">
            <template #default="{ row }">
              <span v-if="row.estimatedHours">{{ row.estimatedHours }}h</span>
              <el-text v-else type="info">-</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="technologyStack" label="技术栈" min-width="150">
            <template #default="{ row }">
              <span v-if="row.technologyStack">{{ row.technologyStack }}</span>
              <el-text v-else type="info">-</el-text>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalCount"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 已选择的步骤 -->
      <div class="selected-section" v-if="selectedSteps.length > 0">
        <h4>已选择的步骤</h4>
        <div class="selected-steps">
          <el-tag
            v-for="step in selectedSteps"
            :key="step.id"
            closable
            @close="removeSelectedStep(step)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ step.stepName }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="selectedSteps.length === 0"
        >
          添加到任务 ({{ selectedSteps.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { developmentService } from '@/services/development'
import { codingTaskService } from '@/services/codingTask'
import type { ElTagType } from '@/types/element-plus'
import type { StepStatus } from '@/types/development'
import type { DevelopmentStep } from '@/types/development'

// Props
interface Props {
  modelValue: boolean
  task?: any
  projectId?: number
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const availableSteps = ref<DevelopmentStep[]>([])
const selectedSteps = ref<DevelopmentStep[]>([])

// 搜索和筛选
const searchKeyword = ref('')
const filterStatus = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    loadAvailableSteps()
  } else {
    resetData()
  }
})

// 方法
const loadAvailableSteps = async () => {
  if (!props.projectId) return

  loading.value = true
  try {
    // 获取项目的父级开发步骤（只显示顶级步骤，不包含子步骤）
    const result = await developmentService.getProjectSteps(
      props.projectId,
      currentPage.value,
      pageSize.value,
      {
        status: (filterStatus.value as StepStatus) || undefined,
        keyword: searchKeyword.value || undefined,
        parentOnly: true  // 只获取父级步骤
      }
    )

    // 如果当前任务已有关联步骤，需要排除它们
    let taskStepIds: number[] = []
    if (props.task?.id) {
      try {
        const taskSteps = await codingTaskService.getTaskSteps(props.task.id)
        taskStepIds = taskSteps.map((step: any) => step.id)
      } catch (error) {
        console.warn('获取任务已关联步骤失败:', error)
      }
    }

    // 过滤掉已经在当前任务中的步骤
    availableSteps.value = result.items.filter(step => !taskStepIds.includes(step.id))
    totalCount.value = result.totalCount
  } catch (error) {
    console.error('加载可用步骤失败:', error)
    ElMessage.error('加载可用步骤失败')
    availableSteps.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadAvailableSteps()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadAvailableSteps()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadAvailableSteps()
}

const handleSelectionChange = (selection: DevelopmentStep[]) => {
  selectedSteps.value = selection
}

const removeSelectedStep = (step: DevelopmentStep) => {
  const index = selectedSteps.value.findIndex(s => s.id === step.id)
  if (index > -1) {
    selectedSteps.value.splice(index, 1)
  }
}

const handleClose = () => {
  visible.value = false
}

const resetData = () => {
  selectedSteps.value = []
  searchKeyword.value = ''
  filterStatus.value = ''
  currentPage.value = 1
}

const handleSubmit = async () => {
  if (selectedSteps.value.length === 0) {
    ElMessage.warning('请选择要添加的步骤')
    return
  }

  if (!props.task?.id) {
    ElMessage.error('任务信息不完整')
    return
  }

  submitting.value = true
  try {
    const stepIds = selectedSteps.value.map(step => step.id)
    const addedCount = await codingTaskService.addStepsToTask(props.task.id, { stepIds })

    ElMessage.success(`成功添加 ${addedCount} 个步骤到任务`)
    emit('success')
    handleClose()
  } catch (error) {
    console.error('添加步骤到任务失败:', error)
    ElMessage.error('添加步骤到任务失败')
  } finally {
    submitting.value = false
  }
}

// 辅助方法
const getStatusType = (status: string): 'warning' | 'success' | 'danger' | 'info' => {
  const typeMap: Record<string, 'warning' | 'success' | 'danger' | 'info'> = {
    NotStarted: 'info',
    InProgress: 'warning',
    Completed: 'success',
    Blocked: 'danger',
    Cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    NotStarted: '未开始',
    InProgress: '进行中',
    Completed: '已完成',
    Blocked: '已阻塞',
    Cancelled: '已取消'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    High: 'danger',
    Medium: 'warning',
    Low: 'info'
  }
  return typeMap[priority] || 'info'
}
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.steps-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.selection-info {
  font-size: 14px;
  color: #606266;
}

.step-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.step-title {
  font-weight: 500;
  color: #303133;
}

.step-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.selected-section {
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.selected-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.selected-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
