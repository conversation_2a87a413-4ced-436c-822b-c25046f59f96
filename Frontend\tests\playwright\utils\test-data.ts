/**
 * 测试数据工厂
 */
export class TestDataFactory {
  /**
   * 生成随机字符串
   */
  static randomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成测试数据
   */
  static createTestData() {
    return {
      testName: `测试_${this.randomString(6)}`,
      testDescription: `这是一个测试描述_${this.randomString(10)}`,
      randomId: Math.floor(Math.random() * 10000)
    };
  }

}
