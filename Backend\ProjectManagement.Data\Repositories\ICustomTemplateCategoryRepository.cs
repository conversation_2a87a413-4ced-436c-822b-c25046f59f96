using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 自定义模板分类仓储接口
    /// </summary>
    public interface ICustomTemplateCategoryRepository
    {
        /// <summary>
        /// 获取所有分类
        /// </summary>
        Task<List<CustomTemplateCategory>> GetAllAsync();

        /// <summary>
        /// 获取启用的分类
        /// </summary>
        Task<List<CustomTemplateCategory>> GetEnabledCategoriesAsync();

        /// <summary>
        /// 根据ID获取分类
        /// </summary>
        Task<CustomTemplateCategory?> GetByIdAsync(int id);

        /// <summary>
        /// 根据名称获取分类
        /// </summary>
        Task<CustomTemplateCategory?> GetByNameAsync(string name);

        /// <summary>
        /// 获取分类树结构
        /// </summary>
        Task<List<CustomTemplateCategory>> GetCategoryTreeAsync();

        /// <summary>
        /// 获取根分类列表
        /// </summary>
        Task<List<CustomTemplateCategory>> GetRootCategoriesAsync();

        /// <summary>
        /// 获取子分类列表
        /// </summary>
        Task<List<CustomTemplateCategory>> GetChildCategoriesAsync(int parentId);

        /// <summary>
        /// 检查分类名称是否存在
        /// </summary>
        Task<bool> ExistsNameAsync(string name, int? excludeId = null);

        /// <summary>
        /// 检查分类是否有子分类
        /// </summary>
        Task<bool> HasChildrenAsync(int categoryId);

        /// <summary>
        /// 检查分类是否有模板
        /// </summary>
        Task<bool> HasTemplatesAsync(int categoryId);

        /// <summary>
        /// 添加分类
        /// </summary>
        Task<CustomTemplateCategory> AddAsync(CustomTemplateCategory category);

        /// <summary>
        /// 更新分类
        /// </summary>
        Task<bool> UpdateAsync(CustomTemplateCategory category);

        /// <summary>
        /// 删除分类
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// 软删除分类
        /// </summary>
        Task<bool> SoftDeleteAsync(int id, int deletedBy);

        /// <summary>
        /// 更新分类的模板数量
        /// </summary>
        Task<bool> UpdateTemplateCountAsync(int categoryId);

        /// <summary>
        /// 批量更新分类排序
        /// </summary>
        Task<bool> UpdateSortOrderAsync(List<(int CategoryId, int SortOrder)> sortOrders);

        /// <summary>
        /// 获取分类统计信息
        /// </summary>
        Task<List<CategoryStatisticsDto>> GetCategoryStatisticsAsync();

        /// <summary>
        /// 获取系统预定义分类
        /// </summary>
        Task<List<CustomTemplateCategory>> GetSystemCategoriesAsync();

        /// <summary>
        /// 获取用户自定义分类
        /// </summary>
        Task<List<CustomTemplateCategory>> GetUserCategoriesAsync();

        /// <summary>
        /// 移动分类到新的父分类下
        /// </summary>
        Task<bool> MoveCategoryAsync(int categoryId, int? newParentId);

        /// <summary>
        /// 启用/禁用分类
        /// </summary>
        Task<bool> SetEnabledAsync(int categoryId, bool enabled);
    }
}
