# Mermaid 序列创建功能实现说明

## 概述

为了让序列创建更加直观和智能，我们实现了基于 Mermaid 的序列创建功能，支持：
1. **AI 自然语言生成** - 用自然语言描述需求，AI 自动生成 Mermaid 流程图
2. **可视化编辑** - 直接编辑 Mermaid 代码，实时预览流程图
3. **智能转换** - 将 Mermaid 流程图自动转换为可执行的序列步骤

## 功能特性

### 🤖 AI 智能生成模式

#### 输入方式
- **流程名称**：序列的名称
- **流程描述**：详细的自然语言描述
- **技术栈选择**：Web应用、桌面应用、移动应用、VS Code等

#### AI 生成能力
- 理解复杂的业务流程描述
- 自动识别条件判断、循环、异常处理等逻辑
- 生成符合 Mermaid 语法的流程图代码
- 考虑不同技术栈的特殊需求

#### 示例输入
```
流程名称：用户登录并发送消息
流程描述：
1. 打开应用并检查登录状态
2. 如果未登录，显示登录页面
3. 输入用户名和密码
4. 验证登录信息，如果失败最多重试3次
5. 登录成功后进入主界面
6. 导航到消息页面
7. 输入消息内容并发送
8. 验证消息发送成功
技术栈：Web 应用
```

### 🎨 可视化编辑模式

#### 编辑器功能
- **代码编辑器**：支持 Mermaid 语法高亮
- **实时预览**：编辑代码时实时渲染流程图
- **语法验证**：自动检测语法错误并提示
- **快速插入**：提供常用节点类型的快速插入按钮

#### 支持的节点类型
- `[文本]` - 矩形节点（基础操作）
- `{文本}` - 菱形节点（条件判断）
- `[[文本]]` - 子程序节点（循环）
- `>文本]` - 旗帜节点（跳转/退出）
- `([文本])` - 圆形节点（开始/结束）

#### 工具栏功能
- **添加节点**：快速添加不同类型的节点
- **预览刷新**：手动刷新流程图预览
- **清空画布**：重置为基础模板
- **语法帮助**：显示 Mermaid 语法说明

### 🔄 智能转换功能

#### 转换规则
1. **节点类型映射**：
   - 矩形节点 → 基础操作（click, input, wait等）
   - 菱形节点 → 条件判断（condition）
   - 子程序节点 → 循环控制（loop, loop_end）
   - 旗帜节点 → 跳转操作（jump, exit）

2. **参数智能推断**：
   - 根据节点描述自动推断操作类型
   - 设置合适的超时时间和重试次数
   - 生成条件表达式和循环参数

3. **流程逻辑保持**：
   - 保持原有的执行顺序
   - 正确处理分支和循环逻辑
   - 维护步骤间的依赖关系

## 技术实现

### 前端组件架构

```
MermaidSequenceCreator.vue (主组件)
├── AI 生成模式
│   ├── 自然语言输入表单
│   ├── AI 生成结果预览
│   └── 生成结果操作
├── 可视化编辑模式
│   ├── Mermaid 代码编辑器
│   ├── 实时预览面板
│   └── 编辑工具栏
└── 传统表单模式
    └── 原有的表单组件
```

### 后端 API 接口

#### 1. 自然语言生成 Mermaid
```http
POST /api/AI/generate-mermaid-sequence
Content-Type: application/json

{
  "name": "序列名称",
  "description": "详细描述",
  "techStack": "web"
}
```

#### 2. Mermaid 转换为序列
```http
POST /api/AI/convert-mermaid-to-sequence
Content-Type: application/json

{
  "mermaidCode": "flowchart TD...",
  "name": "序列名称",
  "description": "序列描述"
}
```

### AI 提示词模板

#### GenerateMermaidSequence 模板
- 专门设计用于自然语言转 Mermaid
- 包含详细的语法要求和示例
- 考虑不同技术栈的特殊需求
- 强调流程的完整性和逻辑性

#### ConvertMermaidToSequence 模板
- 专门用于 Mermaid 转序列步骤
- 定义了详细的节点类型映射规则
- 包含参数生成的智能逻辑
- 确保生成的序列可执行

## 使用流程

### 方式一：AI 智能生成
1. 访问 `http://localhost:3000/automation/sequences`
2. 点击"创建序列"下拉菜单，选择"AI 智能生成"
3. 填写序列名称、详细描述和技术栈
4. 点击"AI 生成流程图"
5. 查看生成的 Mermaid 流程图
6. 可选择编辑或直接使用
7. 点击"使用此流程图创建序列"

### 方式二：可视化编辑
1. 选择"流程图创建"模式
2. 在代码编辑器中编写 Mermaid 代码
3. 实时查看右侧的流程图预览
4. 使用工具栏快速添加节点
5. 验证语法无误后转换为序列

### 方式三：传统表单
1. 选择"传统表单"模式
2. 使用原有的表单界面创建序列

## 示例场景

### 场景1：Web 应用自动化测试
```
描述：自动化测试用户注册流程
1. 打开注册页面
2. 填写用户信息（用户名、邮箱、密码）
3. 点击注册按钮
4. 如果出现验证错误，修正后重试
5. 注册成功后跳转到欢迎页面
6. 验证欢迎信息显示正确
```

### 场景2：桌面应用操作自动化
```
描述：批量处理文档的自动化流程
1. 打开文档处理软件
2. 循环处理文件夹中的每个文档：
   a. 打开文档
   b. 应用格式化规则
   c. 保存文档
   d. 关闭文档
3. 处理完成后生成报告
```

### 场景3：VS Code 插件操作
```
描述：代码重构的自动化流程
1. 打开项目文件夹
2. 搜索需要重构的代码模式
3. 对每个匹配项：
   a. 应用重构规则
   b. 验证语法正确性
   c. 如果有错误，回滚更改
4. 运行测试验证重构结果
5. 提交更改到版本控制
```

## 优势对比

### 传统方式 vs 新方式

| 特性 | 传统表单创建 | Mermaid + AI 创建 |
|------|-------------|------------------|
| 学习成本 | 需要了解步骤配置 | 自然语言描述即可 |
| 创建效率 | 逐步手动配置 | AI 快速生成 |
| 流程可视化 | 无可视化 | 直观的流程图 |
| 复杂逻辑 | 难以表达 | 清晰展示分支循环 |
| 错误检查 | 运行时发现 | 设计时可视化验证 |
| 维护性 | 难以理解和修改 | 图形化易于维护 |

## 扩展功能

### 未来可能的增强
1. **模板库**：保存常用的流程图模板
2. **协作编辑**：多人同时编辑流程图
3. **版本控制**：流程图的版本管理
4. **执行监控**：实时显示执行进度
5. **智能优化**：AI 建议流程优化方案

## 总结

通过引入 Mermaid + AI 的序列创建方式，我们实现了：

✅ **降低门槛** - 用自然语言即可创建复杂序列  
✅ **提升效率** - AI 快速生成，无需手动配置  
✅ **增强可视化** - 流程图让逻辑一目了然  
✅ **支持复杂场景** - 轻松处理条件、循环等复杂逻辑  
✅ **保持灵活性** - 支持手动编辑和传统方式  

这个功能让自动化序列的创建从"技术活"变成了"描述需求"，大大降低了使用门槛，提升了创建效率。
