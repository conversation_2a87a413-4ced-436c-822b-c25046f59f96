using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Services;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using Microsoft.AspNetCore.Authorization;
using ProjectManagement.Core.DTOs.AI;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 开发步骤管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DevelopmentStepsController : ControllerBase
    {
        private readonly IDevelopmentStepService _developmentStepService;
        private readonly IAIService _aiService;
        private readonly ILogger<DevelopmentStepsController> _logger;
        private readonly ProjectManagement.Data.Repositories.IAIModelConfigurationRepository _aiModelConfigurationRepository;

        /// <summary>
        /// 开发步骤控制器构造函数
        /// </summary>
        public DevelopmentStepsController(
            IDevelopmentStepService developmentStepService,
            IAIService aiService,
            ILogger<DevelopmentStepsController> logger,
            ProjectManagement.Data.Repositories.IAIModelConfigurationRepository aiModelConfigurationRepository)
        {
            _developmentStepService = developmentStepService;
            _aiService = aiService;
            _logger = logger;
            _aiModelConfigurationRepository = aiModelConfigurationRepository;
        }

        /// <summary>
        /// 获取项目步骤统计信息
        /// </summary>
        [HttpGet("statistics/{projectId}")]
        public async Task<IActionResult> GetProjectStepStatistics(int projectId)
        {
            try
            {
                var statistics = await _developmentStepService.GetProjectStepStatisticsAsync(projectId);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取项目步骤统计失败，项目ID: {ProjectId}", projectId);
                return StatusCode(500, new { message = "获取统计信息失败" });
            }
        }

        /// <summary>
        /// 获取项目步骤进度
        /// </summary>
        [HttpGet("progress/{projectId}")]
        public async Task<IActionResult> GetStepExecutionProgress(int projectId)
        {
            try
            {
                var progress = await _developmentStepService.GetStepExecutionProgressAsync(projectId);
                return Ok(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤执行进度失败，项目ID: {ProjectId}", projectId);
                return StatusCode(500, new { message = "获取进度信息失败" });
            }
        }

        /// <summary>
        /// 获取单个开发步骤详情
        /// </summary>
        [HttpGet("{stepId}")]
        public async Task<IActionResult> GetStepById(int stepId)
        {
            try
            {
                var step = await _developmentStepService.GetStepByIdAsync(stepId);
                if (step == null)
                {
                    return NotFound(new { success = false, message = "步骤不存在" });
                }

                return Ok(new { success = true, data = step });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤详情失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "获取步骤详情失败" });
            }
        }

        /// <summary>
        /// 创建开发步骤
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateStep([FromBody] CreateStepRequest request)
        {
            try
            {
                var step = new DevelopmentStep
                {
                    ProjectId = request.ProjectId,
                    RequirementDocumentId = request.RequirementDocumentId,
                    ParentStepId = request.ParentStepId,
                    StepName = request.StepName,
                    StepDescription = request.StepDescription,
                    StepType = request.StepType ?? "Development",
                    Priority = request.Priority ?? "Medium",
                    EstimatedHours = request.EstimatedHours,
                    TechnologyStack = request.TechnologyStack,
                    FileType = request.FileType,
                    FilePath = request.FilePath,
                    ComponentType = request.ComponentType,
                    AIPrompt = request.AIPrompt,
                    StepOrder = request.StepOrder,
                    StepGroup = request.StepGroup,
                    StepLevel = request.StepLevel ?? 1,
                    ReferenceImages = request.ReferenceImages
                };

                var createdStep = await _developmentStepService.CreateStepAsync(step);
                return Ok(createdStep);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建开发步骤失败");
                return StatusCode(500, new { message = "创建步骤失败" });
            }
        }

        /// <summary>
        /// 更新开发步骤
        /// </summary>
        [HttpPut("{stepId}")]
        public async Task<IActionResult> UpdateStep(int stepId, [FromBody] UpdateStepRequest request)
        {
            try
            {
                var step = await _developmentStepService.GetStepByIdAsync(stepId);
                if (step == null)
                {
                    return NotFound(new { message = "步骤不存在" });
                }

                // 更新字段
                if (!string.IsNullOrEmpty(request.StepName))
                    step.StepName = request.StepName;
                if (!string.IsNullOrEmpty(request.StepDescription))
                    step.StepDescription = request.StepDescription;
                if (!string.IsNullOrEmpty(request.Priority))
                    step.Priority = request.Priority;
                if (!string.IsNullOrEmpty(request.Status))
                    step.Status = request.Status;
                if (request.EstimatedHours.HasValue)
                    step.EstimatedHours = request.EstimatedHours;
                if (request.ActualHours.HasValue)
                    step.ActualHours = request.ActualHours;
                if (!string.IsNullOrEmpty(request.TechnologyStack))
                    step.TechnologyStack = request.TechnologyStack;
                if (request.ComponentType != null)
                    step.ComponentType = request.ComponentType;
                if (request.FilePath != null)
                    step.FilePath = request.FilePath;
                if (request.AIPrompt != null)
                    step.AIPrompt = request.AIPrompt;
                if (request.Progress.HasValue)
                    step.UpdateProgress(request.Progress.Value);
                if (request.ReferenceImages != null)
                    step.ReferenceImages = request.ReferenceImages;

                var updatedStep = await _developmentStepService.UpdateStepAsync(step);
                return Ok(updatedStep);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新开发步骤失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { message = "更新步骤失败" });
            }
        }

        /// <summary>
        /// 删除开发步骤
        /// </summary>
        [HttpDelete("{stepId}")]
        public async Task<IActionResult> DeleteStep(int stepId, [FromQuery] bool deleteChildSteps = false)
        {
            try
            {
                var success = await _developmentStepService.DeleteStepAsync(stepId, deleteChildSteps);
                return Ok(new { success, message = success ? "删除步骤成功" : "删除步骤失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除开发步骤失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { message = "删除步骤失败" });
            }
        }

        /// <summary>
        /// 开始执行步骤
        /// </summary>
        [HttpPost("{stepId}/execute")]
        public async Task<IActionResult> StartStepExecution(int stepId, [FromBody] StartExecutionRequest request)
        {
            try
            {
                var executionHistory = await _developmentStepService.StartStepExecutionAsync(stepId, request.ExecutorType ?? "Manual");
                return Ok(executionHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始执行步骤失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { message = "开始执行步骤失败" });
            }
        }

        /// <summary>
        /// 分析步骤复杂度
        /// </summary>
        [HttpGet("{stepId}/complexity")]
        public async Task<IActionResult> AnalyzeStepComplexity(int stepId)
        {
            try
            {
                var analysis = await _developmentStepService.AnalyzeStepComplexityAsync(stepId);
                return Ok(new
                {
                    success = true,
                    data = analysis
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析步骤复杂度失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "分析步骤复杂度失败" });
            }
        }

        /// <summary>
        /// 获取步骤复杂度分析结果
        /// </summary>
        [HttpGet("{stepId}/complexity-analysis")]
        public async Task<IActionResult> GetStepComplexityAnalysis(int stepId)
        {
            try
            {
                var analysis = await _developmentStepService.AnalyzeStepComplexityAsync(stepId);
                return Ok(new
                {
                    success = true,
                    data = analysis
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤复杂度分析失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "获取复杂度分析失败" });
            }
        }

        /// <summary>
        /// 获取步骤的依赖关系
        /// </summary>
        [HttpGet("{stepId}/dependencies")]
        public async Task<IActionResult> GetStepDependencies(int stepId)
        {
            try
            {
                var dependencies = await _developmentStepService.GetStepDependenciesAsync(stepId);
                return Ok(new
                {
                    success = true,
                    data = dependencies.Select(d => new
                    {
                        id = d.Id,
                        stepId = d.StepId,
                        dependsOnStepId = d.DependsOnStepId,
                        dependencyType = d.DependencyType,
                        isRequired = d.IsRequired,
                        description = d.Description,
                        dependsOnStep = d.DependsOnStep != null ? new
                        {
                            id = d.DependsOnStep.Id,
                            stepName = d.DependsOnStep.StepName,
                            status = d.DependsOnStep.Status,
                            priority = d.DependsOnStep.Priority
                        } : null
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤依赖关系失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "获取依赖关系失败" });
            }
        }

        /// <summary>
        /// 获取依赖当前步骤的其他步骤
        /// </summary>
        [HttpGet("{stepId}/dependents")]
        public async Task<IActionResult> GetStepDependents(int stepId)
        {
            try
            {
                // 查找所有依赖当前步骤的其他步骤
                var dependents = await _developmentStepService.GetStepDependentsAsync(stepId);
                return Ok(new
                {
                    success = true,
                    data = dependents.Select(d => new
                    {
                        id = d.Id,
                        stepId = d.StepId,
                        dependsOnStepId = d.DependsOnStepId,
                        dependencyType = d.DependencyType,
                        isRequired = d.IsRequired,
                        description = d.Description,
                        step = d.Step != null ? new
                        {
                            id = d.Step.Id,
                            stepName = d.Step.StepName,
                            status = d.Step.Status,
                            priority = d.Step.Priority
                        } : null
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤被依赖关系失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "获取被依赖关系失败" });
            }
        }

        /// <summary>
        /// 获取步骤执行历史
        /// </summary>
        [HttpGet("{stepId}/executions")]
        public async Task<IActionResult> GetStepExecutionHistory(int stepId)
        {
            try
            {
                // 获取执行历史列表（不分页）
                var historyList = await _developmentStepService.GetStepExecutionHistoryListAsync(stepId);

                return Ok(historyList.Select(h => new
                {
                    id = h.Id,
                    stepId = h.StepId,
                    executionId = h.ExecutionId,
                    executionStartTime = h.ExecutionStartTime,
                    executionEndTime = h.ExecutionEndTime,
                    executionDuration = h.ExecutionDuration,
                    executionStatus = h.ExecutionStatus,
                    executionResult = h.ExecutionResult,
                    aiProvider = h.AIProvider,
                    aiModel = h.AIModel,
                    promptUsed = h.PromptUsed,
                    generatedCode = h.GeneratedCode,
                    outputFiles = h.OutputFiles,
                    errorMessage = h.ErrorMessage,
                    executionLog = h.ExecutionLog,
                    executorType = h.ExecutorType,
                    executorInfo = h.ExecutorInfo,
                    vsCodeVersion = h.VSCodeVersion,
                    pluginVersion = h.PluginVersion
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤执行历史失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { success = false, message = "获取执行历史失败" });
            }
        }

        /// <summary>
        /// 搜索开发步骤
        /// </summary>
        [HttpGet("search/{projectId}")]
        public async Task<IActionResult> SearchSteps(
            int projectId,
            [FromQuery] string keyword,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var result = await _developmentStepService.SearchStepsAsync(projectId, keyword, pageIndex, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索开发步骤失败，项目ID: {ProjectId}", projectId);
                return StatusCode(500, new { message = "搜索步骤失败" });
            }
        }

        /// <summary>
        /// 应用模板序列到开发步骤
        /// </summary>
        [HttpPost("apply-template-sequence")]
        public async Task<IActionResult> ApplyTemplateSequenceToSteps([FromBody] ApplyTemplateSequenceRequest request)
        {
            try
            {
                await _developmentStepService.ApplyTemplateSequenceToStepsAsync(request.SequenceId, request.StepIds);
                return Ok(new { message = "模板序列应用成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用模板序列失败，序列ID: {SequenceId}", request.SequenceId);
                return StatusCode(500, new { message = "应用模板序列失败" });
            }
        }

        /// <summary>
        /// 获取步骤关联的模板序列
        /// </summary>
        [HttpGet("{stepId}/template-sequences")]
        public async Task<IActionResult> GetStepTemplateSequences(int stepId)
        {
            try
            {
                var sequences = await _developmentStepService.GetStepTemplateSequencesAsync(stepId);
                return Ok(sequences);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤模板序列失败，步骤ID: {StepId}", stepId);
                return StatusCode(500, new { message = "获取模板序列失败" });
            }
        }

        /// <summary>
        /// 移除步骤的模板序列关联
        /// </summary>
        [HttpDelete("{stepId}/template-sequences/{sequenceId}")]
        public async Task<IActionResult> RemoveStepTemplateSequence(int stepId, int sequenceId)
        {
            try
            {
                await _developmentStepService.RemoveStepTemplateSequenceAsync(stepId, sequenceId);
                return Ok(new { message = "模板序列关联已移除" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除步骤模板序列关联失败，步骤ID: {StepId}, 序列ID: {SequenceId}", stepId, sequenceId);
                return StatusCode(500, new { message = "移除关联失败" });
            }
        }

        /// <summary>
        /// 重新排序步骤
        /// </summary>
        [HttpPut("reorder")]
        public async Task<IActionResult> ReorderSteps([FromBody] ReorderStepsRequest request)
        {
            try
            {
                var result = await _developmentStepService.ReorderStepsAsync(request.StepOrders);
                return Ok(new { success = result, message = result ? "重新排序成功" : "重新排序失败" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新排序步骤失败");
                return StatusCode(500, new { success = false, message = "重新排序失败" });
            }
        }

        /// <summary>
        /// 上传开发步骤参考图片
        /// </summary>
        [HttpPost("{stepId}/upload-image")]
        public async Task<ActionResult<object>> UploadStepImage(int stepId, IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("请选择文件");
                }

                // 验证文件类型
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    return BadRequest("只支持 JPG、JPEG、PNG、GIF、WEBP 格式的图片");
                }

                // 验证文件大小（10MB）
                if (file.Length > 10 * 1024 * 1024)
                {
                    return BadRequest("文件大小不能超过 10MB");
                }

                // 验证步骤是否存在
                var step = await _developmentStepService.GetStepByIdAsync(stepId);
                if (step == null)
                {
                    return NotFound("开发步骤不存在");
                }

                // 生成文件名
                var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                var uploadPath = Path.Combine("uploads", "development-steps", stepId.ToString());
                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", uploadPath);

                // 确保目录存在
                Directory.CreateDirectory(fullPath);

                // 保存文件
                var filePath = Path.Combine(fullPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var relativePath = Path.Combine(uploadPath, fileName).Replace("\\", "/");

                return Ok(new { success = true, data = new { filePath = relativePath } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传开发步骤图片失败");
                return StatusCode(500, "上传图片失败");
            }
        }

        /// <summary>
        /// 获取开发步骤参考图片
        /// </summary>
        [HttpGet("image/{*filePath}")]
        [AllowAnonymous] // 允许匿名访问图片
        public IActionResult GetStepImage(string filePath)
        {
            try
            {
                // 安全检查：防止路径遍历攻击
                if (filePath.Contains("..") || Path.IsPathRooted(filePath))
                {
                    return BadRequest("无效的文件路径");
                }

                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", filePath);

                if (!System.IO.File.Exists(fullPath))
                {
                    return NotFound("文件不存在");
                }

                var contentType = GetContentType(filePath);
                var fileBytes = System.IO.File.ReadAllBytes(fullPath);

                return File(fileBytes, contentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取开发步骤图片失败: {FilePath}", filePath);
                return StatusCode(500, "获取图片失败");
            }
        }

        /// <summary>
        /// 删除开发步骤参考图片
        /// </summary>
        [HttpDelete("{stepId}/delete-image")]
        public async Task<ActionResult> DeleteStepImage(int stepId, [FromQuery] string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                {
                    return BadRequest("图片路径不能为空");
                }

                // 验证步骤是否存在
                var step = await _developmentStepService.GetStepByIdAsync(stepId);
                if (step == null)
                {
                    return NotFound("开发步骤不存在");
                }

                // 安全检查：防止路径遍历攻击
                if (imagePath.Contains("..") || Path.IsPathRooted(imagePath))
                {
                    return BadRequest("无效的文件路径");
                }

                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", imagePath);

                if (System.IO.File.Exists(fullPath))
                {
                    System.IO.File.Delete(fullPath);
                }

                return Ok(new { success = true, message = "图片删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除开发步骤图片失败: {ImagePath}", imagePath);
                return StatusCode(500, "删除图片失败");
            }
        }

        /// <summary>
        /// AI分解开发步骤
        /// </summary>
        [HttpPost("{stepId}/decompose")]
        public async Task<IActionResult> DecomposeStep(int stepId, [FromBody] StepDecomposeRequest request)
        {
            try
            {
                _logger.LogInformation("开始AI分解步骤，StepId: {StepId}", stepId);

                // 获取当前用户ID
                var userId = GetCurrentUserId();

                // 获取原步骤信息
                var originalStep = await _developmentStepService.GetStepByIdAsync(stepId);
                if (originalStep == null)
                {
                    return NotFound(new { message = "步骤不存在" });
                }

                // 获取AI配置
                AIModelConfig? aiConfig = null;
                if (request.AIProviderId.HasValue)
                {
                    var aiModelConfig = await _aiModelConfigurationRepository.GetByIdAsync(request.AIProviderId.Value);
                    if (aiModelConfig != null)
                    {
                        // 从ModelName推断Provider
                        var provider = GetProviderFromModelName(aiModelConfig.ModelName);

                        aiConfig = new AIModelConfig
                        {
                            Provider = provider,
                            Model = aiModelConfig.ModelName,
                            ApiKey = aiModelConfig.ApiKey ?? string.Empty,
                            Endpoint = aiModelConfig.ApiEndpoint ?? string.Empty,
                            MaxTokens = 4000, // 使用默认值
                            Temperature = 0.7f // 使用默认值
                        };
                    }
                }

                // 调用AI分解服务，传递用户ID和AI配置
                var decompositionResult = await _aiService.DecomposeStepAsync(originalStep, request.DecompositionOptions, userId, aiConfig);

                if (!decompositionResult.Success)
                {
                    return BadRequest(new { message = decompositionResult.ErrorMessage });
                }

                // 创建子步骤预览（不保存到数据库）
                var childSteps = new List<DevelopmentStep>();
                var stepOrder = 1;
                int tempId = -1; // 使用负数作为临时ID

                foreach (var stepData in decompositionResult.Steps)
                {
                    var childStep = new DevelopmentStep
                    {
                        Id = tempId, // 临时ID
                        ProjectId = originalStep.ProjectId,
                        RequirementDocumentId = originalStep.RequirementDocumentId,
                        ParentStepId = stepId,
                        StepName = stepData.StepName,
                        StepDescription = stepData.StepDescription,
                        StepType = stepData.StepType ?? originalStep.StepType,
                        Priority = stepData.Priority ?? originalStep.Priority,
                        EstimatedHours = stepData.EstimatedHours,
                        TechnologyStack = stepData.TechnologyStack ?? originalStep.TechnologyStack,
                        FileType = stepData.FileType,
                        FilePath = stepData.FilePath,
                        ComponentType = stepData.ComponentType,
                        AIPrompt = stepData.AIPrompt,
                        StepOrder = stepOrder++,
                        StepLevel = originalStep.StepLevel + 1,
                        Status = "Pending",
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now
                    };

                    childSteps.Add(childStep);
                    tempId--;
                }

                _logger.LogInformation("AI分解步骤完成，生成 {Count} 个子步骤", childSteps.Count);

                return Ok(new
                {
                    success = true,
                    message = "步骤分解成功",
                    data = new
                    {
                        originalStepId = stepId,
                        childSteps = childSteps.Select(s => new
                        {
                            id = s.Id,
                            stepName = s.StepName,
                            stepDescription = s.StepDescription,
                            stepType = s.StepType,
                            priority = s.Priority,
                            estimatedHours = s.EstimatedHours,
                            technologyStack = s.TechnologyStack,
                            fileType = s.FileType,
                            filePath = s.FilePath,
                            componentType = s.ComponentType,
                            aiPrompt = s.AIPrompt,
                            stepOrder = s.StepOrder,
                            stepLevel = s.StepLevel,
                            parentStepId = s.ParentStepId,
                            status = s.Status,
                            createdTime = s.CreatedTime
                        }),
                        decompositionAnalysis = decompositionResult.AIAnalysisResult
                    },
                    isPreview = true // 标记为预览模式
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI分解步骤失败，StepId: {StepId}", stepId);
                return StatusCode(500, new { message = "步骤分解失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 确认保存步骤分解结果
        /// </summary>
        [HttpPost("{stepId}/confirm-decompose")]
        public async Task<IActionResult> ConfirmStepDecomposition(int stepId, [FromBody] ConfirmStepDecomposeRequest request)
        {
            try
            {
                _logger.LogInformation("开始确认保存步骤分解结果，StepId: {StepId}, ChildStepCount: {Count}",
                    stepId, request.ChildSteps?.Count ?? 0);

                // 获取当前用户ID
                var userId = GetCurrentUserId();

                // 获取原步骤信息
                var originalStep = await _developmentStepService.GetStepByIdAsync(stepId);
                if (originalStep == null)
                {
                    return NotFound(new { message = "步骤不存在" });
                }

                // 创建子步骤
                var childSteps = new List<DevelopmentStep>();
                var stepOrder = 1;

                foreach (var stepData in request.ChildSteps)
                {
                    var childStep = new DevelopmentStep
                    {
                        ProjectId = originalStep.ProjectId,
                        RequirementDocumentId = originalStep.RequirementDocumentId,
                        ParentStepId = stepId,
                        StepName = stepData.StepName,
                        StepDescription = stepData.StepDescription,
                        StepType = stepData.StepType ?? originalStep.StepType,
                        Priority = stepData.Priority ?? originalStep.Priority,
                        EstimatedHours = stepData.EstimatedHours,
                        TechnologyStack = stepData.TechnologyStack ?? originalStep.TechnologyStack,
                        FileType = stepData.FileType,
                        FilePath = stepData.FilePath,
                        ComponentType = stepData.ComponentType,
                        AIPrompt = stepData.AIPrompt,
                        StepOrder = stepOrder++,
                        StepLevel = originalStep.StepLevel + 1,
                        Status = "Pending"
                    };

                    var createdStep = await _developmentStepService.CreateStepAsync(childStep);
                    childSteps.Add(createdStep);
                }

                _logger.LogInformation("步骤分解确认保存完成，生成 {Count} 个子步骤", childSteps.Count);

                return Ok(new
                {
                    success = true,
                    message = "步骤分解保存成功",
                    data = new
                    {
                        originalStepId = stepId,
                        childSteps = childSteps.Select(s => new
                        {
                            id = s.Id,
                            stepName = s.StepName,
                            stepDescription = s.StepDescription,
                            stepType = s.StepType,
                            priority = s.Priority,
                            estimatedHours = s.EstimatedHours,
                            technologyStack = s.TechnologyStack,
                            fileType = s.FileType,
                            filePath = s.FilePath,
                            componentType = s.ComponentType,
                            aiPrompt = s.AIPrompt,
                            stepOrder = s.StepOrder,
                            stepLevel = s.StepLevel,
                            parentStepId = s.ParentStepId,
                            status = s.Status,
                            createdTime = s.CreatedTime
                        })
                    },
                    isPreview = false // 已保存到数据库
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认保存步骤分解失败，StepId: {StepId}", stepId);
                return StatusCode(500, new { message = "确认保存步骤分解失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 自定义细分开发步骤
        /// </summary>
        [HttpPost("{stepId}/custom-decompose")]
        public async Task<IActionResult> CustomDecomposeStep(int stepId, [FromBody] CustomStepDecomposeRequest request)
        {
            try
            {
                _logger.LogInformation("开始自定义细分步骤，StepId: {StepId}", stepId);

                // 获取当前用户ID
                var userId = GetCurrentUserId();

                // 获取原步骤信息
                var originalStep = await _developmentStepService.GetStepByIdAsync(stepId);
                if (originalStep == null)
                {
                    return NotFound(new { message = "步骤不存在" });
                }

                var childSteps = new List<DevelopmentStep>();

                // 创建子步骤
                foreach (var subStepData in request.SubSteps)
                {
                    var childStep = new DevelopmentStep
                    {
                        StepName = subStepData.StepName,
                        StepDescription = subStepData.StepDescription,
                        StepType = subStepData.StepType,
                        Priority = subStepData.Priority,
                        EstimatedHours = subStepData.EstimatedHours,
                        TechnologyStack = subStepData.TechnologyStack,
                        ProjectId = originalStep.ProjectId,
                        RequirementDocumentId = originalStep.RequirementDocumentId,
                        ParentStepId = stepId,
                        StepLevel = originalStep.StepLevel + 1,
                        StepOrder = subStepData.StepOrder,
                        Status = "Pending",
                        Progress = 0,
                        CreatedBy = userId,
                        CreatedTime = DateTime.Now
                    };

                    var createdStep = await _developmentStepService.CreateStepAsync(childStep);
                    childSteps.Add(createdStep);
                }

                _logger.LogInformation("自定义细分步骤完成，生成 {Count} 个子步骤", childSteps.Count);

                return Ok(new
                {
                    message = "自定义细分成功",
                    data = new
                    {
                        parentStepId = stepId,
                        childSteps = childSteps
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自定义细分步骤失败，StepId: {StepId}", stepId);
                return StatusCode(500, new { message = "自定义细分失败", error = ex.Message });
            }
        }

    // 请求模型
    public class CreateStepRequest
    {
        public int ProjectId { get; set; }
        public int? RequirementDocumentId { get; set; }
        public int? ParentStepId { get; set; }
        public string StepName { get; set; } = string.Empty;
        public string? StepDescription { get; set; }
        public string? StepType { get; set; }
        public string? Priority { get; set; }
        public decimal? EstimatedHours { get; set; }
        public string? TechnologyStack { get; set; }
        public string? FileType { get; set; }
        public string? FilePath { get; set; }
        public string? ComponentType { get; set; }
        public string? AIPrompt { get; set; }
        public int StepOrder { get; set; }
        public string? StepGroup { get; set; }
        public int? StepLevel { get; set; }
        public string? ReferenceImages { get; set; }
    }

    public class UpdateStepRequest
    {
        public string? StepName { get; set; }
        public string? StepDescription { get; set; }
        public string? Priority { get; set; }
        public string? Status { get; set; }
        public decimal? EstimatedHours { get; set; }
        public decimal? ActualHours { get; set; }
        public string? TechnologyStack { get; set; }
        public string? ComponentType { get; set; }
        public string? FilePath { get; set; }
        public string? AIPrompt { get; set; }
        public int? Progress { get; set; }
        public string? ReferenceImages { get; set; }
    }

    public class StartExecutionRequest
    {
        public string? ExecutorType { get; set; }
    }

    public class ApplyTemplateSequenceRequest
    {
        public int SequenceId { get; set; }
        public List<int> StepIds { get; set; } = new();
    }

    public class ReorderStepsRequest
    {
        public List<ProjectManagement.Core.Services.StepOrderInfo> StepOrders { get; set; } = new();
    }

    public class StepDecomposeRequest
    {
        public StepDecompositionOptions DecompositionOptions { get; set; } = new();
        public int? AIProviderId { get; set; }
    }

    public class CustomStepDecomposeRequest
    {
        public List<CustomSubStepData> SubSteps { get; set; } = new();
    }

    public class CustomSubStepData
    {
        public string StepName { get; set; } = string.Empty;
        public string? StepDescription { get; set; }
        public string StepType { get; set; } = "Development";
        public string Priority { get; set; } = "Medium";
        public decimal? EstimatedHours { get; set; }
        public string? TechnologyStack { get; set; }
        public int StepOrder { get; set; }
    }

    #region 私有方法

    /// <summary>
    /// 根据模型名称推断提供商
    /// </summary>
    private static string GetProviderFromModelName(string modelName)
    {
        var lowerModelName = modelName.ToLower();

        if (lowerModelName.Contains("gpt") || lowerModelName.Contains("openai"))
            return "Azure";
        else if (lowerModelName.Contains("claude"))
            return "Claude";
        else if (lowerModelName.Contains("deepseek"))
            return "DeepSeek";
        else
            return "Azure"; // 默认提供商
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : 1; // 默认用户ID为1
    }

    /// <summary>
    /// 根据文件扩展名获取Content-Type
    /// </summary>
    private static string GetContentType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".webp" => "image/webp",
            ".bmp" => "image/bmp",
            ".svg" => "image/svg+xml",
            _ => "application/octet-stream"
        };
    }

    #endregion
    }

    /// <summary>
    /// 确认步骤分解请求模型
    /// </summary>
    public class ConfirmStepDecomposeRequest
    {
        /// <summary>
        /// 确认的子步骤列表
        /// </summary>
        public List<ChildStepData> ChildSteps { get; set; } = new();
    }

    /// <summary>
    /// 子步骤数据模型
    /// </summary>
    public class ChildStepData
    {
        public string StepName { get; set; } = string.Empty;
        public string StepDescription { get; set; } = string.Empty;
        public string? StepType { get; set; }
        public string? Priority { get; set; }
        public decimal? EstimatedHours { get; set; }
        public string? TechnologyStack { get; set; }
        public string? FileType { get; set; }
        public string? FilePath { get; set; }
        public string? ComponentType { get; set; }
        public string? AIPrompt { get; set; }
    }
}
