import api from './api'

// Playwright测试接口
export interface PlaywrightTest {
  id: number
  name: string
  description: string
  category: string
  browser: string
  code: string
  config: PlaywrightConfig
  tags: string[]
  priority: 'low' | 'medium' | 'high'
  status: string
  createdTime: Date
  updatedTime: Date
  createdBy?: number
  projectId?: number
}

// Playwright配置接口
export interface PlaywrightConfig {
  headless: boolean
  viewport: {
    width: number
    height: number
  }
  timeout: number
  baseURL: string
  browsers: string[]
  device: string
  userAgent: string
  parallel: boolean
  workers: number
  retries: number
  globalTimeout: number
  reporters: string[]
  screenshot: string
  video: string
  trace: string
  ignoreHTTPSErrors: boolean
  javaScriptEnabled: boolean
  waitForNetworkIdle: boolean
  launchOptions: string
}

// 执行结果接口
export interface PlaywrightExecutionResult {
  id: number
  testId: number
  status: 'running' | 'passed' | 'failed' | 'skipped' | 'timedout'
  startTime: Date
  endTime?: Date
  duration?: number
  browser: string
  logs: PlaywrightExecutionLog[]
  screenshots: string[]
  videos: string[]
  traces: string[]
  errorMessage?: string
  stats: PlaywrightExecutionStats
}

export interface PlaywrightExecutionLog {
  timestamp: Date
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
  step?: string
  screenshot?: string
}

export interface PlaywrightExecutionStats {
  totalTests: number
  passedTests: number
  failedTests: number
  skippedTests: number
  flakyTests: number
}

// Playwright测试服务类
export class PlaywrightTestService {
  // 获取测试列表
  static async getTests(params?: {
    projectId?: number
    category?: string
    browser?: string
    keyword?: string
    page?: number
    pageSize?: number
  }): Promise<PlaywrightTest[]> {
    const response = await api.get('/api/playwright-tests', { params })
    return response.data.data || []
  }

  // 获取单个测试
  static async getTest(id: number): Promise<PlaywrightTest> {
    const response = await api.get(`/api/playwright-tests/${id}`)
    return response.data
  }

  // 创建测试
  static async createTest(test: Partial<PlaywrightTest>): Promise<PlaywrightTest> {
    const response = await api.post('/api/playwright-tests', test)
    return response.data
  }

  // 更新测试
  static async updateTest(id: number, test: Partial<PlaywrightTest>): Promise<PlaywrightTest> {
    const response = await api.put(`/api/playwright-tests/${id}`, test)
    return response.data
  }

  // 删除测试
  static async deleteTest(id: number): Promise<void> {
    await api.delete(`/api/playwright-tests/${id}`)
  }

  // 执行测试
  static async executeTest(id: number, config?: Partial<PlaywrightConfig>): Promise<{ executionId: string }> {
    const response = await api.post(`/playwright-tests/${id}/execute`, { config })
    return response.data
  }

  // 停止执行
  static async stopExecution(executionId: string): Promise<void> {
    await api.post(`/playwright-executions/${executionId}/stop`)
  }

  // 获取执行结果
  static async getExecutionResult(executionId: string): Promise<PlaywrightExecutionResult> {
    const response = await api.get(`/playwright-executions/${executionId}`)
    return response.data
  }

  // 获取执行历史
  static async getExecutionHistory(params?: {
    testId?: number
    status?: string
    browser?: string
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }): Promise<{ data: PlaywrightExecutionResult[], total: number }> {
    const response = await api.get('/playwright-executions', { params })
    return response.data
  }

  // 批量执行测试
  static async batchExecute(testIds: number[], config?: Partial<PlaywrightConfig>): Promise<{ executionIds: string[] }> {
    const response = await api.post('/playwright-tests/batch-execute', { testIds, config })
    return response.data
  }

  // 录制测试
  static async recordTest(config: {
    url: string
    browser?: string
    device?: string
    outputPath?: string
  }): Promise<{ recordingId: string }> {
    const response = await api.post('/playwright-tests/record', config)
    return response.data
  }

  // 停止录制
  static async stopRecording(recordingId: string): Promise<{ code: string }> {
    const response = await api.post(`/playwright-recordings/${recordingId}/stop`)
    return response.data
  }

  // 获取测试模板
  static async getTemplates(): Promise<{ [key: string]: string }> {
    const response = await api.get('/playwright-tests/templates')
    return response.data
  }

  // 验证测试代码
  static async validateTest(code: string): Promise<{ valid: boolean, errors?: string[] }> {
    const response = await api.post('/playwright-tests/validate', { code })
    return response.data
  }

  // 格式化测试代码
  static async formatTest(code: string): Promise<{ code: string }> {
    const response = await api.post('/playwright-tests/format', { code })
    return response.data
  }

  // 获取浏览器状态
  static async getBrowserStatus(): Promise<{
    chromium: { installed: boolean, version?: string }
    firefox: { installed: boolean, version?: string }
    webkit: { installed: boolean, version?: string }
  }> {
    const response = await api.get('/playwright-tests/browser-status')
    return response.data
  }

  // 安装浏览器
  static async installBrowsers(browsers?: string[]): Promise<{ success: boolean, message: string }> {
    const response = await api.post('/playwright-tests/install-browsers', { browsers })
    return response.data
  }

  // 获取统计信息
  static async getStatistics(params?: {
    projectId?: number
    startDate?: string
    endDate?: string
  }): Promise<{
    totalTests: number
    totalExecutions: number
    passRate: number
    avgDuration: number
    browserStats: { [key: string]: number }
    dailyStats: { date: string, executions: number, passRate: number }[]
    flakyTests: { testId: number, testName: string, flakyRate: number }[]
  }> {
    const response = await api.get('/playwright-tests/statistics', { params })
    return response.data
  }

  // 生成测试报告
  static async generateReport(executionIds: string[], format: 'html' | 'pdf' | 'json' = 'html'): Promise<Blob> {
    const response = await api.post('/playwright-tests/generate-report', { executionIds, format }, {
      responseType: 'blob'
    })
    return response.data
  }

  // 导出测试
  static async exportTests(testIds: number[], format: 'json' | 'zip' = 'json'): Promise<Blob> {
    const response = await api.post('/playwright-tests/export', { testIds, format }, {
      responseType: 'blob'
    })
    return response.data
  }

  // 导入测试
  static async importTests(file: File): Promise<{ imported: number, failed: number, errors?: string[] }> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await api.post('/playwright-tests/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // 复制测试
  static async duplicateTest(id: number, name?: string): Promise<PlaywrightTest> {
    const response = await api.post(`/playwright-tests/${id}/duplicate`, { name })
    return response.data
  }

  // 健康检查
  static async healthCheck(): Promise<{
    playwright: boolean
    browsers: { [key: string]: boolean }
    dependencies: { [key: string]: boolean }
  }> {
    const response = await api.get('/playwright-tests/health')
    return response.data
  }

  // 获取设备列表
  static async getDevices(): Promise<{ [key: string]: any }> {
    const response = await api.get('/playwright-tests/devices')
    return response.data
  }

  // 调试测试
  static async debugTest(id: number, config?: Partial<PlaywrightConfig>): Promise<{ debugUrl: string }> {
    const response = await api.post(`/playwright-tests/${id}/debug`, { config })
    return response.data
  }
}

// 默认导出
export default PlaywrightTestService

// 工具函数
export const playwrightUtils = {
  // 生成基础测试模板
  generateBasicTemplate: (config: Partial<PlaywrightConfig> = {}) => {
    const browser = config.browsers?.[0] || 'chromium'
    const baseURL = config.baseURL || 'https://example.com'
    
    return `const { test, expect } = require('@playwright/test');

test.describe('${config.baseURL ? new URL(baseURL).hostname : 'Example'} 测试套件', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前的设置
    await page.goto('${baseURL}');
  });

  test('基础页面测试', async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/.*Example.*/);
    
    // 验证页面加载
    await expect(page.locator('body')).toBeVisible();
    
    // 在这里添加您的测试代码
    console.log('测试开始执行...');
    
    // 示例：点击元素
    // await page.click('button#submit');
    
    // 示例：填写表单
    // await page.fill('input[name="username"]', 'testuser');
    
    // 示例：等待元素出现
    // await expect(page.locator('.result')).toBeVisible();
    
    console.log('测试执行完成');
  });
});`
  },

  // 生成登录测试模板
  generateLoginTemplate: (config: Partial<PlaywrightConfig> = {}) => {
    const baseURL = config.baseURL || 'https://example.com'
    
    return `const { test, expect } = require('@playwright/test');

test.describe('用户登录测试', () => {
  test('成功登录流程', async ({ page }) => {
    // 访问登录页面
    await page.goto('${baseURL}/login');
    
    // 填写用户名
    await page.fill('[name="username"]', 'testuser');
    
    // 填写密码
    await page.fill('[name="password"]', 'password123');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待页面跳转
    await page.waitForURL('**/dashboard');
    
    // 验证登录成功
    await expect(page.locator('.user-info')).toContainText('testuser');
    await expect(page.locator('.dashboard')).toBeVisible();
  });

  test('登录失败处理', async ({ page }) => {
    await page.goto('${baseURL}/login');
    
    // 输入错误的凭据
    await page.fill('[name="username"]', 'wronguser');
    await page.fill('[name="password"]', 'wrongpass');
    await page.click('button[type="submit"]');
    
    // 验证错误消息
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('用户名或密码错误');
  });
});`
  },

  // 生成API测试模板
  generateAPITemplate: (config: Partial<PlaywrightConfig> = {}) => {
    const baseURL = config.baseURL || 'https://api.example.com'
    
    return `const { test, expect } = require('@playwright/test');

test.describe('API测试套件', () => {
  test('GET请求测试', async ({ request }) => {
    const response = await request.get('${baseURL}/api/users');
    
    // 验证响应状态
    expect(response.status()).toBe(200);
    
    // 验证响应头
    expect(response.headers()['content-type']).toContain('application/json');
    
    // 验证响应数据
    const data = await response.json();
    expect(data).toHaveProperty('users');
    expect(Array.isArray(data.users)).toBeTruthy();
  });

  test('POST请求测试', async ({ request }) => {
    const newUser = {
      name: 'Test User',
      email: '<EMAIL>'
    };
    
    const response = await request.post('${baseURL}/api/users', {
      data: newUser
    });
    
    expect(response.status()).toBe(201);
    
    const data = await response.json();
    expect(data.name).toBe(newUser.name);
    expect(data.email).toBe(newUser.email);
  });

  test('认证API测试', async ({ request }) => {
    // 先获取认证token
    const loginResponse = await request.post('${baseURL}/api/auth/login', {
      data: {
        username: 'testuser',
        password: 'password123'
      }
    });
    
    const { token } = await loginResponse.json();
    
    // 使用token访问受保护的API
    const response = await request.get('${baseURL}/api/protected', {
      headers: {
        'Authorization': \`Bearer \${token}\`
      }
    });
    
    expect(response.status()).toBe(200);
  });
});`
  },

  // 验证JavaScript语法
  validateJavaScriptSyntax: (code: string): { valid: boolean, errors: string[] } => {
    const errors: string[] = []
    
    // 基础语法检查
    if (!code.trim()) {
      errors.push('测试代码不能为空')
      return { valid: false, errors }
    }
    
    // 检查必要的导入
    if (!code.includes("require('@playwright/test')")) {
      errors.push("缺少必要的导入: require('@playwright/test')")
    }
    
    // 检查测试结构
    if (!code.includes('test(') && !code.includes('test.describe(')) {
      errors.push('未找到测试用例定义')
    }
    
    // 检查异步函数
    if (code.includes('test(') && !code.includes('async')) {
      errors.push('测试函数应该是异步的 (async)')
    }
    
    // 检查expect断言
    if (!code.includes('expect(')) {
      errors.push('建议添加expect断言来验证测试结果')
    }
    
    return { valid: errors.length === 0, errors }
  },

  // 格式化执行时间
  formatDuration: (milliseconds: number): string => {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`
    } else if (milliseconds < 60000) {
      const seconds = (milliseconds / 1000).toFixed(1)
      return `${seconds}s`
    } else {
      const minutes = Math.floor(milliseconds / 60000)
      const seconds = Math.floor((milliseconds % 60000) / 1000)
      return `${minutes}m ${seconds}s`
    }
  },

  // 获取状态颜色
  getStatusColor: (status: string): string => {
    const colors: { [key: string]: string } = {
      'running': 'warning',
      'passed': 'success',
      'failed': 'danger',
      'skipped': 'info',
      'timedout': 'warning',
      '就绪': 'success',
      '草稿': 'info',
      '运行中': 'warning',
      '失败': 'danger'
    }
    return colors[status] || 'info'
  },

  // 获取浏览器图标
  getBrowserIcon: (browser: string): string => {
    const icons: { [key: string]: string } = {
      'chromium': '🟢',
      'firefox': '🟠',
      'webkit': '🔵',
      'chrome': '🟢',
      'safari': '🔵'
    }
    return icons[browser.toLowerCase()] || '🌐'
  },

  // 解析设备配置
  parseDeviceConfig: (device: string): { width: number, height: number, userAgent?: string } => {
    const devices: { [key: string]: any } = {
      'iPhone 12': { width: 390, height: 844, userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' },
      'iPhone 12 Pro': { width: 390, height: 844, userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' },
      'iPad': { width: 768, height: 1024, userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)' },
      'Galaxy S21': { width: 384, height: 854, userAgent: 'Mozilla/5.0 (Linux; Android 11; SM-G991B)' },
      'Pixel 5': { width: 393, height: 851, userAgent: 'Mozilla/5.0 (Linux; Android 11; Pixel 5)' }
    }
    
    return devices[device] || { width: 1920, height: 1080 }
  }
}