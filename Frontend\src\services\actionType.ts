/**
 * 动作类型服务
 */
import api from './api'

// UI操作类型接口
export interface UIActionType {
  id: number
  value: string
  label: string
  description?: string
  icon?: string
  color?: string
  sortOrder: number
  isActive: boolean
  isBuiltIn: boolean
  needsTemplate: boolean
  parameterSchema?: string
  createdTime: string
  updatedTime?: string
}

// 分页查询参数
export interface UIActionTypeQuery {
  page: number
  pageSize: number
  keyword?: string
  isActive?: boolean
  isBuiltIn?: boolean
}

// 分页结果
export interface PageResult<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/**
 * UI操作类型服务类
 */
export class UIActionTypeService {

  /**
   * 获取UI操作类型列表
   */
  static async getUIActionTypes(query: UIActionTypeQuery): Promise<PageResult<UIActionType>> {
    const response = await api.get('/api/ui-action-types', { params: query })
    return response.data
  }

  /**
   * 获取启用的UI操作类型
   */
  static async getActiveUIActionTypes(): Promise<UIActionType[]> {
    const response = await api.get('/api/ui-action-types/active')
    return response.data
  }

  /**
   * 获取UI操作类型详情
   */
  static async getUIActionType(id: number): Promise<UIActionType> {
    const response = await api.get(`/api/ui-action-types/${id}`)
    return response.data
  }

  /**
   * 创建UI操作类型
   */
  static async createUIActionType(actionType: Partial<UIActionType>): Promise<UIActionType> {
    const response = await api.post('/api/ui-action-types', actionType)
    return response.data
  }

  /**
   * 更新UI操作类型
   */
  static async updateUIActionType(id: number, actionType: Partial<UIActionType>): Promise<UIActionType> {
    const response = await api.put(`/api/ui-action-types/${id}`, actionType)
    return response.data
  }

  /**
   * 删除UI操作类型
   */
  static async deleteUIActionType(id: number): Promise<void> {
    await api.delete(`/api/ui-action-types/${id}`)
  }

  /**
   * 启用/禁用UI操作类型
   */
  static async setActiveStatus(id: number, isActive: boolean): Promise<void> {
    await api.put(`/api/ui-action-types/${id}/status`, isActive)
  }
}

export default UIActionTypeService

// 为了向后兼容，保留旧的导出名称
export { UIActionTypeService as ActionTypeService }
export type { UIActionType as ActionType }
