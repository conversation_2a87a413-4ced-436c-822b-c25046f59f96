<template>
  <div class="selenium-config">
    <el-form :model="config" label-width="120px">
      <el-card class="config-section">
        <template #header>
          <span>🌐 浏览器配置</span>
        </template>
        
        <el-form-item label="浏览器类型">
          <el-select v-model="config.browser" style="width: 200px;">
            <el-option label="Chrome" value="chrome">
              <span style="float: left">Chrome</span>
              <span style="float: right; color: #8492a6; font-size: 13px">推荐</span>
            </el-option>
            <el-option label="Firefox" value="firefox" />
            <el-option label="Safari" value="safari" />
            <el-option label="Edge" value="edge" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="无头模式">
          <el-switch v-model="config.headless" />
          <span class="config-hint">启用后浏览器将在后台运行，不显示界面</span>
        </el-form-item>
        
        <el-form-item label="窗口大小">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-input-number
                v-model="config.windowWidth"
                :min="800"
                :max="3840"
                controls-position="right"
                placeholder="宽度"
              />
            </el-col>
            <el-col :span="1" class="text-center">×</el-col>
            <el-col :span="8">
              <el-input-number
                v-model="config.windowHeight"
                :min="600"
                :max="2160"
                controls-position="right"
                placeholder="高度"
              />
            </el-col>
            <el-col :span="7">
              <el-button size="small" @click="setCommonSize">常用尺寸</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="用户代理">
          <el-input
            v-model="config.userAgent"
            placeholder="留空使用默认用户代理"
            style="width: 400px;"
          />
        </el-form-item>
      </el-card>
      
      <el-card class="config-section">
        <template #header>
          <span>⏱️ 超时配置</span>
        </template>
        
        <el-form-item label="页面加载超时">
          <el-input-number
            v-model="config.pageLoadTimeout"
            :min="5"
            :max="300"
            controls-position="right"
            style="width: 150px;"
          />
          <span class="config-unit">秒</span>
        </el-form-item>
        
        <el-form-item label="元素查找超时">
          <el-input-number
            v-model="config.implicitWait"
            :min="1"
            :max="60"
            controls-position="right"
            style="width: 150px;"
          />
          <span class="config-unit">秒</span>
        </el-form-item>
        
        <el-form-item label="脚本执行超时">
          <el-input-number
            v-model="config.scriptTimeout"
            :min="5"
            :max="300"
            controls-position="right"
            style="width: 150px;"
          />
          <span class="config-unit">秒</span>
        </el-form-item>
      </el-card>
      
      <el-card class="config-section">
        <template #header>
          <span>🔧 高级配置</span>
        </template>
        
        <el-form-item label="启用日志">
          <el-switch v-model="config.enableLogging" />
          <span class="config-hint">记录详细的执行日志</span>
        </el-form-item>
        
        <el-form-item label="截图设置">
          <el-checkbox-group v-model="config.screenshotOptions">
            <el-checkbox label="onError">失败时截图</el-checkbox>
            <el-checkbox label="onSuccess">成功时截图</el-checkbox>
            <el-checkbox label="onStep">每步截图</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="重试次数">
          <el-input-number
            v-model="config.retryCount"
            :min="0"
            :max="5"
            controls-position="right"
            style="width: 150px;"
          />
          <span class="config-hint">失败时自动重试次数</span>
        </el-form-item>
        
        <el-form-item label="等待策略">
          <el-select v-model="config.waitStrategy" style="width: 200px;">
            <el-option label="智能等待" value="smart" />
            <el-option label="固定等待" value="fixed" />
            <el-option label="条件等待" value="conditional" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="Chrome选项">
          <el-input
            v-model="config.chromeOptions"
            type="textarea"
            :rows="3"
            placeholder="每行一个选项，例如：&#10;--disable-web-security&#10;--allow-running-insecure-content"
            style="width: 400px;"
          />
        </el-form-item>
      </el-card>
      
      <el-card class="config-section">
        <template #header>
          <span>🌍 环境配置</span>
        </template>
        
        <el-form-item label="测试环境">
          <el-radio-group v-model="config.environment">
            <el-radio label="local">本地环境</el-radio>
            <el-radio label="dev">开发环境</el-radio>
            <el-radio label="test">测试环境</el-radio>
            <el-radio label="prod">生产环境</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="基础URL">
          <el-input
            v-model="config.baseUrl"
            placeholder="https://example.com"
            style="width: 400px;"
          />
        </el-form-item>
        
        <el-form-item label="代理设置">
          <el-input
            v-model="config.proxy"
            placeholder="http://proxy.example.com:8080"
            style="width: 400px;"
          />
        </el-form-item>
      </el-card>
    </el-form>
    
    <!-- 常用尺寸选择器 -->
    <el-dialog v-model="showSizeDialog" title="选择常用尺寸" width="500px">
      <div class="size-options">
        <div
          v-for="size in commonSizes"
          :key="`${size.width}x${size.height}`"
          class="size-option"
          @click="selectSize(size)"
        >
          <div class="size-label">{{ size.label }}</div>
          <div class="size-value">{{ size.width }} × {{ size.height }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

// Props
interface Props {
  modelValue: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 响应式数据
const showSizeDialog = ref(false)

const config = reactive({
  browser: 'chrome',
  headless: false,
  windowWidth: 1920,
  windowHeight: 1080,
  userAgent: '',
  pageLoadTimeout: 30,
  implicitWait: 10,
  scriptTimeout: 30,
  enableLogging: true,
  screenshotOptions: ['onError'],
  retryCount: 1,
  waitStrategy: 'smart',
  chromeOptions: '',
  environment: 'local',
  baseUrl: '',
  proxy: '',
  ...props.modelValue
})

const commonSizes = [
  { label: '桌面 - 全高清', width: 1920, height: 1080 },
  { label: '桌面 - 标准', width: 1366, height: 768 },
  { label: '桌面 - 宽屏', width: 1440, height: 900 },
  { label: '平板 - iPad', width: 1024, height: 768 },
  { label: '平板 - iPad Pro', width: 1366, height: 1024 },
  { label: '手机 - iPhone', width: 375, height: 667 },
  { label: '手机 - Android', width: 360, height: 640 }
]

// 监听器
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
}, { deep: true })

// 方法
const setCommonSize = () => {
  showSizeDialog.value = true
}

const selectSize = (size: any) => {
  config.windowWidth = size.width
  config.windowHeight = size.height
  showSizeDialog.value = false
}
</script>

<style lang="scss" scoped>
.selenium-config {
  .config-section {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .config-hint {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .config-unit {
    margin-left: 8px;
    color: #606266;
  }
  
  .text-center {
    text-align: center;
    line-height: 32px;
    color: #909399;
  }
}

.size-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  .size-option {
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      background-color: #ecf5ff;
    }
    
    .size-label {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .size-value {
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
