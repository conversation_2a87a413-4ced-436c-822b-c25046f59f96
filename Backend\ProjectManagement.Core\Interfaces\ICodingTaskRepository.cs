using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces;

/// <summary>
/// 编码任务仓储接口
/// </summary>
public interface ICodingTaskRepository : IRepository<CodingTask>
{
    /// <summary>
    /// 根据项目ID获取编码任务列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="status">状态筛选</param>
    /// <param name="priority">优先级筛选</param>
    /// <param name="assignedTo">分配人筛选</param>
    /// <param name="searchKeyword">搜索关键词</param>
    /// <returns>分页的编码任务列表</returns>
    Task<(List<CodingTask> items, int totalCount)> GetByProjectIdAsync(
        int projectId,
        int pageIndex = 1,
        int pageSize = 20,
        string? status = null,
        string? priority = null,
        int? assignedTo = null,
        string? searchKeyword = null);

    /// <summary>
    /// 根据项目ID获取所有编码任务（不分页）
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>编码任务列表</returns>
    Task<List<CodingTask>> GetAllByProjectIdAsync(int projectId);

    /// <summary>
    /// 获取编码任务统计信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    Task<CodingTaskStatistics> GetStatisticsAsync(int projectId);

    /// <summary>
    /// 根据分配人获取编码任务
    /// </summary>
    /// <param name="assignedTo">分配人ID</param>
    /// <param name="status">状态筛选</param>
    /// <returns>编码任务列表</returns>
    Task<List<CodingTask>> GetByAssignedUserAsync(int assignedTo, string? status = null);

    /// <summary>
    /// 获取即将到期的编码任务
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="days">天数</param>
    /// <returns>即将到期的任务列表</returns>
    Task<List<CodingTask>> GetUpcomingDueTasksAsync(int projectId, int days = 7);

    /// <summary>
    /// 批量更新任务状态
    /// </summary>
    /// <param name="taskIds">任务ID列表</param>
    /// <param name="status">新状态</param>
    /// <param name="updatedBy">更新人ID</param>
    /// <returns>更新成功的任务数量</returns>
    Task<int> BatchUpdateStatusAsync(List<int> taskIds, string status, int updatedBy);

    /// <summary>
    /// 获取任务关联的开发步骤数量
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>关联的步骤数量</returns>
    Task<int> GetRelatedStepsCountAsync(int taskId);

    /// <summary>
    /// 添加开发步骤到编码任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="stepIds">步骤ID列表</param>
    /// <returns>添加成功的步骤数量</returns>
    Task<int> AddStepsToTaskAsync(int taskId, List<int> stepIds);

    /// <summary>
    /// 从编码任务中移除开发步骤
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="stepIds">步骤ID列表</param>
    /// <returns>移除成功的步骤数量</returns>
    Task<int> RemoveStepsFromTaskAsync(int taskId, List<int> stepIds);

    /// <summary>
    /// 获取任务关联的开发步骤（包含CodingTaskSteps执行状态信息）
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>关联的开发步骤列表（包含执行状态）</returns>
    Task<List<object>> GetTaskStepsAsync(int taskId);

    /// <summary>
    /// 获取特定的编码任务步骤
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="stepId">步骤ID</param>
    /// <returns>编码任务步骤</returns>
    Task<CodingTaskStep?> GetTaskStepAsync(int taskId, int stepId);

    /// <summary>
    /// 更新编码任务步骤
    /// </summary>
    /// <param name="taskStep">编码任务步骤</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateTaskStepAsync(CodingTaskStep taskStep);

    /// <summary>
    /// 获取任务的第一个步骤
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>第一个步骤</returns>
    Task<CodingTaskStep?> GetFirstTaskStepAsync(int taskId);

    /// <summary>
    /// 开始任务的第一个步骤
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns>是否成功</returns>
    Task<bool> StartFirstTaskStepAsync(int taskId, int userId);
}

/// <summary>
/// 编码任务统计信息
/// </summary>
public class CodingTaskStatistics
{
    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 进行中任务数
    /// </summary>
    public int InProgressTasks { get; set; }

    /// <summary>
    /// 已完成任务数
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// 已阻塞任务数
    /// </summary>
    public int BlockedTasks { get; set; }

    /// <summary>
    /// 待开始任务数
    /// </summary>
    public int NotStartedTasks { get; set; }

    /// <summary>
    /// 高优先级任务数
    /// </summary>
    public int HighPriorityTasks { get; set; }

    /// <summary>
    /// 即将到期任务数
    /// </summary>
    public int UpcomingDueTasks { get; set; }

    /// <summary>
    /// 平均完成时间（小时）
    /// </summary>
    public double AverageCompletionHours { get; set; }
}
