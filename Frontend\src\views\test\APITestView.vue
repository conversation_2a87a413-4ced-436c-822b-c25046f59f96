<template>
  <div class="api-test-view">
    <div class="page-header">
      <h1>🔗 API测试</h1>
      <p>接口功能和性能测试工具</p>
    </div>
    
    <el-card class="coming-soon">
      <div class="content">
        <el-icon class="icon"><Connection /></el-icon>
        <h2>API测试功能开发中</h2>
        <p>我们正在开发强大的API测试功能，包括：</p>
        <ul>
          <li>RESTful API测试</li>
          <li>GraphQL测试</li>
          <li>接口性能测试</li>
          <li>自动化回归测试</li>
          <li>测试数据管理</li>
        </ul>
        <el-button type="primary" @click="$router.push('/testing/selenium')">
          体验Selenium测试
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Connection } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.api-test-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.coming-soon {
  .content {
    text-align: center;
    padding: 40px;
    
    .icon {
      font-size: 64px;
      color: #409eff;
      margin-bottom: 20px;
    }
    
    h2 {
      margin: 0 0 16px 0;
      color: #303133;
    }
    
    p {
      margin-bottom: 16px;
      color: #606266;
    }
    
    ul {
      text-align: left;
      display: inline-block;
      margin-bottom: 24px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }
}
</style>
