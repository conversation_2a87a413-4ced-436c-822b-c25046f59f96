-- =============================================
-- 自定义模板分类管理表创建脚本
-- 版本: 1.0
-- 创建时间: 2024-06-23
-- 描述: 为自定义模板添加分类管理功能，支持层级分类和自定义分类
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '开始创建自定义模板分类管理表...';

-- =============================================
-- 1. 创建自定义模板分类表
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CustomTemplateCategories]') AND type in (N'U'))
BEGIN
    PRINT '创建CustomTemplateCategories表...';

    CREATE TABLE CustomTemplateCategories (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        ParentId int NULL,
        Icon nvarchar(50) NULL,
        Color nvarchar(20) NULL,
        SortOrder int NOT NULL DEFAULT 0,
        IsSystem bit NOT NULL DEFAULT 0,
        IsEnabled bit NOT NULL DEFAULT 1,
        TemplateCount int NOT NULL DEFAULT 0,

        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_CustomTemplateCategories PRIMARY KEY (Id),
        CONSTRAINT UK_CustomTemplateCategories_Name UNIQUE (Name),
        CONSTRAINT FK_CustomTemplateCategories_Parent FOREIGN KEY (ParentId) REFERENCES CustomTemplateCategories(Id),
        CONSTRAINT FK_CustomTemplateCategories_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CustomTemplateCategories_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CustomTemplateCategories_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_CustomTemplateCategories_SortOrder CHECK (SortOrder >= 0),
        CONSTRAINT CK_CustomTemplateCategories_TemplateCount CHECK (TemplateCount >= 0)
    );

    -- 创建索引
    CREATE INDEX IX_CustomTemplateCategories_ParentId ON CustomTemplateCategories(ParentId);
    CREATE INDEX IX_CustomTemplateCategories_SortOrder ON CustomTemplateCategories(SortOrder);
    CREATE INDEX IX_CustomTemplateCategories_IsEnabled ON CustomTemplateCategories(IsEnabled);
    CREATE INDEX IX_CustomTemplateCategories_IsSystem ON CustomTemplateCategories(IsSystem);

    PRINT '✅ CustomTemplateCategories表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ CustomTemplateCategories表已存在，跳过创建';
END

-- =============================================
-- 2. 修改CustomUIAutoMationTemplates表，移除分类约束
-- =============================================
PRINT '修改CustomUIAutoMationTemplates表分类约束...';

-- 检查并删除现有的分类约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_CustomUIAutoMationTemplates_Category')
BEGIN
    PRINT '删除现有分类约束...';
    ALTER TABLE CustomUIAutoMationTemplates DROP CONSTRAINT CK_CustomUIAutoMationTemplates_Category;
    PRINT '✅ 现有分类约束已删除';
END

-- 添加分类ID外键列（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = 'CategoryId')
BEGIN
    PRINT '添加CategoryId列...';
    ALTER TABLE CustomUIAutoMationTemplates ADD CategoryId int NULL;

    -- 添加外键约束
    ALTER TABLE CustomUIAutoMationTemplates
    ADD CONSTRAINT FK_CustomUIAutoMationTemplates_Category
    FOREIGN KEY (CategoryId) REFERENCES CustomTemplateCategories(Id);

    -- 创建索引
    CREATE INDEX IX_CustomUIAutoMationTemplates_CategoryId ON CustomUIAutoMationTemplates(CategoryId);

    PRINT '✅ CategoryId列添加成功';
END
ELSE
BEGIN
    PRINT '⚠️ CategoryId列已存在，跳过添加';
END

-- =============================================
-- 3. 修改UIAutoMationTemplateSequences表，添加分类ID
-- =============================================
PRINT '修改UIAutoMationTemplateSequences表...';

-- 添加分类ID外键列（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSequences]') AND name = 'CategoryId')
BEGIN
    PRINT '添加CategoryId列到序列表...';
    ALTER TABLE UIAutoMationTemplateSequences ADD CategoryId int NULL;

    -- 添加外键约束
    ALTER TABLE UIAutoMationTemplateSequences
    ADD CONSTRAINT FK_UIAutoMationTemplateSequences_Category
    FOREIGN KEY (CategoryId) REFERENCES CustomTemplateCategories(Id);

    -- 创建索引
    CREATE INDEX IX_UIAutoMationTemplateSequences_CategoryId ON UIAutoMationTemplateSequences(CategoryId);

    PRINT '✅ 序列表CategoryId列添加成功';
END
ELSE
BEGIN
    PRINT '⚠️ 序列表CategoryId列已存在，跳过添加';
END

PRINT '✅ 自定义模板分类管理表创建完成！';
