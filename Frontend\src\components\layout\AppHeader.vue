<template>
  <div class="app-header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 菜单折叠按钮 -->
      <el-button 
        type="text" 
        @click="$emit('toggleSidebar')"
        class="menu-toggle"
      >
        <el-icon size="18">
          <Menu />
        </el-icon>
      </el-button>
      
      <!-- 页面标题 -->
      <span class="page-title">{{ currentPageTitle }}</span>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索项目、需求..."
          :prefix-icon="Search"
          size="small"
          style="width: 200px"
          @keyup.enter="handleSearch"
          clearable
        />
      </div>
      
      <!-- 通知 -->
      <el-dropdown trigger="click" @command="handleNotificationCommand">
        <el-button type="text" class="notification-button">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon size="18">
              <Bell />
            </el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="notification-header">
              <span>通知消息</span>
              <el-button type="text" size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            <el-divider style="margin: 8px 0" />
            <div class="notification-list">
              <div 
                v-for="notification in notifications" 
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-desc">{{ notification.description }}</div>
                  <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
                </div>
              </div>
              <div v-if="notifications.length === 0" class="no-notifications">
                暂无通知
              </div>
            </div>
            <el-divider style="margin: 8px 0" />
            <el-dropdown-item command="viewAll">
              查看全部通知
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 主题切换 -->
      <el-button 
        type="text" 
        @click="toggleTheme"
        class="theme-toggle"
      >
        <el-icon size="18">
          <Sunny v-if="isDark" />
          <Moon v-else />
        </el-icon>
      </el-button>
      
      <!-- 用户菜单 -->
      <el-dropdown trigger="click" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar
            :src="getAvatarUrl(userAvatar)"
            :size="32"
            class="user-avatar"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ username }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-divider style="margin: 4px 0" />
            <el-dropdown-item command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Menu,
  Search,
  Bell,
  Sunny,
  Moon,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'

defineEmits<{
  toggleSidebar: []
  logout: []
}>()

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const searchKeyword = ref('')
const isDark = ref(false)
const notifications = ref([
  {
    id: 1,
    title: '项目创建成功',
    description: '您的项目"电商系统"已创建成功',
    createdAt: new Date(),
    read: false
  },
  {
    id: 2,
    title: 'AI代码生成完成',
    description: '用户管理模块代码生成已完成',
    createdAt: new Date(Date.now() - 3600000),
    read: true
  }
])

// 计算属性
const currentPageTitle = computed(() => {
  return route.meta?.title as string || '首页'
})

const username = computed(() => {
  return authStore.user?.realName || authStore.user?.username || '用户'
})

const userAvatar = computed(() => {
  return authStore.user?.avatar || ''
})

const getAvatarUrl = (avatar?: string) => {
  if (!avatar) return ''
  // 如果是完整URL，直接返回
  if (avatar.startsWith('http')) return avatar
  // 如果是相对路径，添加后端服务器地址
  return `https://localhost:61136/${avatar}`
}

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 方法
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    // 实现搜索逻辑
    console.log('搜索:', searchKeyword.value)
    ElMessage.info(`搜索: ${searchKeyword.value}`)
  }
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  ElMessage.success(`已切换到${isDark.value ? '深色' : '浅色'}主题`)
}

const handleNotificationCommand = (command: string) => {
  switch (command) {
    case 'viewAll':
      router.push('/notifications')
      break
  }
}

const handleNotificationClick = (notification: any) => {
  if (!notification.read) {
    notification.read = true
  }
  // 处理通知点击逻辑
  console.log('点击通知:', notification)
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.read = true)
  ElMessage.success('所有通知已标记为已读')
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      if (authStore.hasPermission('admin')) {
        router.push('/settings')
      } else {
        ElMessage.warning('权限不足')
      }
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await authStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

const formatTime = (time: Date) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 组件挂载时初始化主题
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    isDark.value = true
    document.documentElement.classList.add('dark')
  }
})
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 100%;
  background: var(--el-bg-color);
}

.header-left {
  display: flex;
  align-items: center;
  
  .menu-toggle {
    margin-right: 16px;
    padding: 8px;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .page-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  :deep(.el-input__wrapper) {
    border-radius: 20px;
  }
}

.notification-button,
.theme-toggle {
  padding: 8px;
  
  &:hover {
    background-color: var(--el-color-primary-light-9);
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  font-weight: 500;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  cursor: pointer;
  border-left: 3px solid transparent;
  
  &:hover {
    background-color: var(--el-bg-color-page);
  }
  
  &.unread {
    background-color: var(--el-color-primary-light-9);
    border-left-color: var(--el-color-primary);
  }
  
  .notification-content {
    .notification-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .notification-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-bottom: 4px;
    }
    
    .notification-time {
      font-size: 11px;
      color: var(--el-text-color-placeholder);
    }
  }
}

.no-notifications {
  padding: 20px;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  
  &:hover {
    background-color: var(--el-color-primary-light-9);
  }
  
  .user-avatar {
    margin-right: 8px;
  }
  
  .username {
    margin-right: 4px;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
  
  .dropdown-icon {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}
</style>
