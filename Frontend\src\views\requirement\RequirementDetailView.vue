<template>
  <div class="requirement-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="text" @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="header-content">
          <h1 class="page-title">{{ requirement?.title || '需求详情' }}</h1>
          <div class="page-meta">
            <el-tag :type="getStatusType(requirement?.status || '')" size="small">
              {{ getStatusText(requirement?.status || '') }}
            </el-tag>
            <span class="version">版本 {{ requirement?.version }}</span>
            <span class="project">{{ getProjectName(requirement?.projectId || 0) }}</span>
            <span class="time">{{ formatDate(requirement?.createdAt || '') }}</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="showDecomposeDialog = true" type="success">
          <el-icon><Operation /></el-icon>
          分解需求
        </el-button>
        <el-button @click="editRequirement" type="primary">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-dropdown @command="handleAction">
          <el-button>
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="viewSteps">查看开发步骤</el-dropdown-item>
              <el-dropdown-item command="export" divided>导出文档</el-dropdown-item>
              <el-dropdown-item command="duplicate">复制需求</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除需求</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 需求内容 -->
    <div v-else-if="requirement" class="requirement-content">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="需求标题">{{ requirement.title }}</el-descriptions-item>
          <el-descriptions-item label="所属项目">{{ getProjectName(requirement.projectId) }}</el-descriptions-item>
          <el-descriptions-item label="需求状态">
            <el-tag :type="getStatusType(requirement.status)">{{ getStatusText(requirement.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文档版本">{{ requirement.version }}</el-descriptions-item>
          <el-descriptions-item label="生成方式">
            <el-tag v-if="requirement.generatedBy === 'AI'" type="success" size="small">AI生成</el-tag>
            <el-tag v-else type="info" size="small">手动创建</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(requirement.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间" v-if="requirement.updatedAt">{{ formatDate(requirement.updatedAt) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 需求内容 -->
      <el-card class="content-card collapsible-card" shadow="never" v-if="requirement.content">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><Document /></el-icon>
              <span>需求内容</span>
            </div>
            <el-button
              text
              size="small"
              @click="toggleCardCollapse('content')"
              class="collapse-btn"
            >
              <el-icon :class="{ 'rotate-180': cardCollapsed.content }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div v-show="!cardCollapsed.content" class="markdown-content" v-html="formatMarkdown(requirement.content)"></div>
        </el-collapse-transition>
      </el-card>

      <!-- 功能性需求 -->
      <el-card class="content-card collapsible-card" shadow="never" v-if="requirement.functionalRequirements">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><Setting /></el-icon>
              <span>功能性需求</span>
            </div>
            <el-button
              text
              size="small"
              @click="toggleCardCollapse('functionalRequirements')"
              class="collapse-btn"
            >
              <el-icon :class="{ 'rotate-180': cardCollapsed.functionalRequirements }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div v-show="!cardCollapsed.functionalRequirements" class="markdown-content" v-html="formatMarkdown(requirement.functionalRequirements)"></div>
        </el-collapse-transition>
      </el-card>

      <!-- 非功能性需求 -->
      <el-card class="content-card collapsible-card" shadow="never" v-if="requirement.nonFunctionalRequirements">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><Monitor /></el-icon>
              <span>非功能性需求</span>
            </div>
            <el-button
              text
              size="small"
              @click="toggleCardCollapse('nonFunctionalRequirements')"
              class="collapse-btn"
            >
              <el-icon :class="{ 'rotate-180': cardCollapsed.nonFunctionalRequirements }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div v-show="!cardCollapsed.nonFunctionalRequirements" class="markdown-content" v-html="formatMarkdown(requirement.nonFunctionalRequirements)"></div>
        </el-collapse-transition>
      </el-card>

      <!-- 用户故事 -->
      <el-card class="content-card collapsible-card" shadow="never" v-if="requirement.userStories">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><User /></el-icon>
              <span>用户故事</span>
            </div>
            <el-button
              text
              size="small"
              @click="toggleCardCollapse('userStories')"
              class="collapse-btn"
            >
              <el-icon :class="{ 'rotate-180': cardCollapsed.userStories }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div v-show="!cardCollapsed.userStories" class="markdown-content" v-html="formatMarkdown(requirement.userStories)"></div>
        </el-collapse-transition>
      </el-card>

      <!-- 验收标准 -->
      <el-card class="content-card collapsible-card" shadow="never" v-if="requirement.acceptanceCriteria">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><CircleCheck /></el-icon>
              <span>验收标准</span>
            </div>
            <el-button
              text
              size="small"
              @click="toggleCardCollapse('acceptanceCriteria')"
              class="collapse-btn"
            >
              <el-icon :class="{ 'rotate-180': cardCollapsed.acceptanceCriteria }">
                <ArrowDown />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div v-show="!cardCollapsed.acceptanceCriteria" class="markdown-content" v-html="formatMarkdown(requirement.acceptanceCriteria)"></div>
        </el-collapse-transition>
      </el-card>

      <!-- 开发步骤 -->
      <el-card class="content-card collapsible-card" shadow="never">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <el-icon><List /></el-icon>
              <span>开发步骤</span>
            </div>
            <div class="header-actions">
              <el-button
                size="small"
                type="primary"
                @click="showDecomposeDialog = true"
                v-if="!hasSteps"
              >
                分解需求
              </el-button>
              <el-button
                size="small"
                @click="refreshSteps"
                :loading="stepsLoading"
              >
                刷新
              </el-button>
              <el-button
                text
                size="small"
                @click="toggleCardCollapse('developmentSteps')"
                class="collapse-btn"
              >
                <el-icon :class="{ 'rotate-180': cardCollapsed.developmentSteps }">
                  <ArrowDown />
                </el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="!cardCollapsed.developmentSteps">
            <DevelopmentStepsPanel
              :project-id="requirement.projectId"
              :requirement-document-id="requirement.id"
              ref="stepsPanel"
            />
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="需求文档不存在或已被删除">
        <el-button type="primary" @click="$router.push('/requirements')">返回需求列表</el-button>
      </el-empty>
    </div>

    <!-- 需求分解对话框 -->
    <RequirementDecomposeDialog
      v-model="showDecomposeDialog"
      :project-id="requirement?.projectId || 0"
      :requirement-document-id="requirement?.id"
      @success="handleDecomposeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  ArrowLeft, Edit, ArrowDown, InfoFilled, Document, Setting,
  Monitor, User, CircleCheck, Operation, List
} from '@element-plus/icons-vue'
import { RequirementService } from '@/services/requirement'
import { ProjectService } from '@/services/project'
import { developmentService } from '@/services/development'
import type { RequirementDocument, ProjectSummary } from '@/types'
import type { DecompositionResult } from '@/types/development'
import DevelopmentStepsPanel from '@/components/development/DevelopmentStepsPanel.vue'
import RequirementDecomposeDialog from '@/components/development/RequirementDecomposeDialog.vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const stepsLoading = ref(false)
const requirement = ref<RequirementDocument | null>(null)
const projects = ref<ProjectSummary[]>([])
const requirementId = computed(() => parseInt(route.params.id as string))
const showDecomposeDialog = ref(false)
const hasSteps = ref(false)
const stepsPanel = ref()

// 卡片折叠状态
const cardCollapsed = ref({
  content: false,
  functionalRequirements: false,
  nonFunctionalRequirements: false,
  userStories: false,
  acceptanceCriteria: false,
  developmentSteps: false
})

// 方法
const loadRequirement = async () => {
  try {
    loading.value = true
    const response = await RequirementService.getRequirementDocument(requirementId.value)
    requirement.value = response
  } catch (error: any) {
    console.error('加载需求详情失败:', error)
    ElMessage.error('加载需求详情失败')
    requirement.value = null
  } finally {
    loading.value = false
  }
}

const loadProjects = async () => {
  try {
    const response = await ProjectService.getProjects({ pageSize: 100 })
    projects.value = response.items || []
  } catch (error: any) {
    console.error('加载项目列表失败:', error)
  }
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const getProjectName = (projectId: number) => {
  const project = projects.value.find(p => p.id === projectId)
  return project?.name || '未知项目'
}

const getStatusType = (status: string): 'warning' | 'success' | 'primary' | 'info' => {
  const statusMap: Record<string, 'warning' | 'success' | 'primary' | 'info'> = {
    'Draft': 'info',
    'InProgress': 'warning',
    'Completed': 'success',
    'PendingReview': 'primary',
    'Published': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Draft': '草稿',
    'InProgress': '进行中',
    'Completed': '已完成',
    'PendingReview': '待审核',
    'Published': '已发布'
  }
  return statusMap[status] || status
}

const formatMarkdown = (content: string) => {
  // 简单的文本格式化，将换行转换为 <br>
  // 后续可以集成更完整的 Markdown 解析器
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
}

const editRequirement = () => {
  router.push(`/requirements/documents/${requirementId.value}/edit`)
}

const handleAction = async (command: string) => {
  switch (command) {
    case 'viewSteps':
      await viewDevelopmentSteps()
      break
    case 'export':
      await exportRequirement()
      break
    case 'duplicate':
      await duplicateRequirement()
      break
    case 'delete':
      await deleteRequirement()
      break
  }
}

const exportRequirement = async () => {
  if (!requirement.value) return

  try {
    // 显示格式选择对话框
    const { value: format } = await ElMessageBox.confirm(
      '请选择导出格式：\n\n1. Markdown (.md) - 推荐\n2. PDF (.pdf) - 开发中\n3. Word (.docx) - 开发中',
      '导出需求文档',
      {
        confirmButtonText: '导出 Markdown',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    ElMessage.info('正在导出文档，请稍候...')

    await RequirementService.exportRequirementDocument(
      requirementId.value,
      'markdown'
    )

    ElMessage.success('文档导出成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('导出需求文档失败:', error)
      ElMessage.error('导出需求文档失败')
    }
  }
}

const duplicateRequirement = async () => {
  if (!requirement.value) return

  try {
    await RequirementService.createRequirementDocument({
      projectId: requirement.value.projectId,
      title: `${requirement.value.title} (副本)`,
      content: requirement.value.content,
      functionalRequirements: requirement.value.functionalRequirements,
      nonFunctionalRequirements: requirement.value.nonFunctionalRequirements,
      userStories: requirement.value.userStories,
      acceptanceCriteria: requirement.value.acceptanceCriteria
    })

    ElMessage.success('需求复制成功')
    router.push('/requirements')
  } catch (error: any) {
    console.error('复制需求失败:', error)
    ElMessage.error('复制需求失败')
  }
}

const deleteRequirement = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个需求文档吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await RequirementService.deleteRequirementDocument(requirementId.value)
    ElMessage.success('需求删除成功')
    router.push('/requirements')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除需求失败:', error)
      ElMessage.error('删除需求失败')
    }
  }
}

const viewDevelopmentSteps = async () => {
  // 跳转到开发步骤页面，并传递项目ID和需求ID参数
  if (requirement.value) {
    router.push(`/development/steps?projectId=${requirement.value.projectId}&requirementId=${requirementId.value}`)
  }
}

const handleDecomposeSuccess = (result: DecompositionResult) => {
  ElMessage.success(`需求分解成功，生成 ${result.data?.steps.length || 0} 个开发步骤`)
  hasSteps.value = true
  refreshSteps()
}

const refreshSteps = () => {
  stepsLoading.value = true
  try {
    if (stepsPanel.value && typeof stepsPanel.value.refreshSteps === 'function') {
      stepsPanel.value.refreshSteps()
    } else {
      console.warn('stepsPanel.refreshSteps is not available')
      // 如果组件方法不可用，直接重新检查步骤
      checkHasSteps()
    }
  } catch (error) {
    console.error('刷新步骤失败:', error)
  } finally {
    stepsLoading.value = false
  }
}

const checkHasSteps = async () => {
  if (!requirement.value) return

  try {
    const steps = await developmentService.getProjectSteps(
      requirement.value.projectId,
      1,
      1,
      { keyword: '' }
    )
    hasSteps.value = steps.totalCount > 0
  } catch (error) {
    console.error('检查开发步骤失败:', error)
  }
}

// 卡片折叠控制
const toggleCardCollapse = (cardName: keyof typeof cardCollapsed.value) => {
  cardCollapsed.value[cardName] = !cardCollapsed.value[cardName]
  // 保存到localStorage
  localStorage.setItem(`requirement-card-collapsed-${cardName}`, cardCollapsed.value[cardName].toString())
}

// 从localStorage恢复折叠状态
const restoreCollapseState = () => {
  Object.keys(cardCollapsed.value).forEach(key => {
    const saved = localStorage.getItem(`requirement-card-collapsed-${key}`)
    if (saved !== null) {
      cardCollapsed.value[key as keyof typeof cardCollapsed.value] = saved === 'true'
    }
  })
}

// 生命周期
onMounted(() => {
  restoreCollapseState()
  loadProjects()
  loadRequirement().then(() => {
    checkHasSteps()
  })
})
</script>

<style lang="scss" scoped>
.requirement-detail {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }

      .page-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        color: var(--el-text-color-secondary);

        .version, .project, .time {
          padding: 2px 8px;
          background: var(--el-fill-color-light);
          border-radius: 4px;
        }
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.loading-container {
  padding: 20px;
}

.requirement-content {
  .info-card, .content-card {
    margin-bottom: 24px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      font-weight: 600;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .collapsible-card {
    .collapse-btn {
      padding: 4px;
      margin-left: 8px;

      .el-icon {
        transition: transform 0.3s ease;

        &.rotate-180 {
          transform: rotate(180deg);
        }
      }

      &:hover {
        background-color: var(--el-fill-color-light);
        border-radius: 4px;
      }
    }
  }

  .markdown-content {
    line-height: 1.6;

    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      margin: 16px 0 8px 0;
      font-weight: 600;
    }

    :deep(p) {
      margin: 8px 0;
    }

    :deep(ul), :deep(ol) {
      margin: 8px 0;
      padding-left: 24px;
    }

    :deep(li) {
      margin: 4px 0;
    }

    :deep(code) {
      background: var(--el-fill-color-light);
      padding: 2px 4px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
    }

    :deep(pre) {
      background: var(--el-fill-color-light);
      padding: 12px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 12px 0;
    }

    :deep(blockquote) {
      border-left: 4px solid var(--el-color-primary);
      padding-left: 12px;
      margin: 12px 0;
      color: var(--el-text-color-secondary);
    }
  }
}

.error-container {
  padding: 60px 20px;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .requirement-detail {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;

    .header-left {
      flex-direction: column;
      gap: 8px;
    }

    .header-actions {
      align-self: stretch;

      .el-button {
        flex: 1;
      }
    }
  }

  .page-meta {
    flex-wrap: wrap;
  }
}
</style>
