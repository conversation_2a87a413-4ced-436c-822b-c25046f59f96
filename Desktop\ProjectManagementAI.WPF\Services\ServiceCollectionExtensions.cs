using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProjectManagementAI.WPF.ViewModels;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 注册应用程序服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // 注册日志服务
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 注册业务服务
            services.AddSingleton<IProjectService, MockProjectService>();
            services.AddSingleton<IAIService, MockAIService>();
            services.AddSingleton<IVectorOperationService, VectorOperationService>();
            services.AddSingleton<IDataAnalysisService, MockDataAnalysisService>();
            services.AddSingleton<IKnowledgeGraphService, MockKnowledgeGraphService>();
            services.AddSingleton<ILocalProjectService, LocalProjectService>();
            services.AddSingleton<IVectorDatabaseService, VectorDatabaseService>();

            // 注册ViewModels
            services.AddTransient<MainViewModel>();
            services.AddTransient<VectorOperationViewModel>();

            return services;
        }
    }
}
