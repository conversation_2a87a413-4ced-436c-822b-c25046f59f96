-- =============================================
-- 修改RequirementConversations表，允许ProjectId为NULL
-- 支持无项目关联的通用AI对话
-- =============================================

USE ProjectManagementAI;
GO

-- 检查表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'RequirementConversations')
BEGIN
    PRINT '开始修改RequirementConversations表结构...';
    
    -- 删除外键约束
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_RequirementConversations_ProjectId')
    BEGIN
        ALTER TABLE RequirementConversations DROP CONSTRAINT FK_RequirementConversations_ProjectId;
        PRINT '已删除外键约束 FK_RequirementConversations_ProjectId';
    END
    
    -- 修改ProjectId列为可空
    ALTER TABLE RequirementConversations 
    ALTER COLUMN ProjectId int NULL;
    PRINT '已修改ProjectId列为可空';
    
    -- 重新创建外键约束（允许NULL值）
    ALTER TABLE RequirementConversations 
    ADD CONSTRAINT FK_RequirementConversations_ProjectId 
    FOREIGN KEY (ProjectId) REFERENCES Projects(Id);
    PRINT '已重新创建外键约束（支持NULL值）';
    
    -- 验证修改结果
    SELECT 
        c.COLUMN_NAME,
        c.IS_NULLABLE,
        c.DATA_TYPE
    FROM INFORMATION_SCHEMA.COLUMNS c
    WHERE c.TABLE_NAME = 'RequirementConversations' 
      AND c.COLUMN_NAME = 'ProjectId';
    
    PRINT '表结构修改完成！现在支持无项目关联的对话记录。';
END
ELSE
BEGIN
    PRINT '错误：RequirementConversations表不存在';
END
GO
