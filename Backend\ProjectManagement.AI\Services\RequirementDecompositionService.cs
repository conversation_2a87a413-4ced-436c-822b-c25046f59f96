using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Services;
using ProjectManagement.Data.Repositories;
using System.Text.Json;

namespace ProjectManagement.AI.Services;

/// <summary>
/// 需求分解服务实现
/// </summary>
public class RequirementDecompositionService : IRequirementDecompositionService
{
    private readonly IDevelopmentStepRepository _stepRepository;
    private readonly IRequirementDocumentRepository _requirementRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IRepository<StepDependency> _dependencyRepository;
    private readonly IRepository<StepExecutionHistory> _executionHistoryRepository;
    private readonly IAIService _aiService;
    private readonly IPromptTemplateRepository _promptTemplateRepository;
    private readonly IUserAIConfigurationRepository _userAIConfigurationRepository;
    private readonly IAIModelConfigurationRepository _aiModelConfigurationRepository;
    private readonly ILogger<RequirementDecompositionService> _logger;

    public RequirementDecompositionService(
        IDevelopmentStepRepository stepRepository,
        IRequirementDocumentRepository requirementRepository,
        IProjectRepository projectRepository,
        IRepository<StepDependency> dependencyRepository,
        IRepository<StepExecutionHistory> executionHistoryRepository,
        IAIService aiService,
        IPromptTemplateRepository promptTemplateRepository,
        IUserAIConfigurationRepository userAIConfigurationRepository,
        IAIModelConfigurationRepository aiModelConfigurationRepository,
        ILogger<RequirementDecompositionService> logger)
    {
        _stepRepository = stepRepository;
        _requirementRepository = requirementRepository;
        _projectRepository = projectRepository;
        _dependencyRepository = dependencyRepository;
        _executionHistoryRepository = executionHistoryRepository;
        _aiService = aiService;
        _promptTemplateRepository = promptTemplateRepository;
        _userAIConfigurationRepository = userAIConfigurationRepository;
        _aiModelConfigurationRepository = aiModelConfigurationRepository;
        _logger = logger;
    }

    #region 需求分解

    public async Task<RequirementDecompositionResult> DecomposeRequirementAsync(
        int requirementDocumentId,
        DecompositionOptions decompositionOptions,
        int userId)
    {
        try
        {
            // 1. 获取需求文档
            var requirementDocument = await _requirementRepository.GetByIdAsync(requirementDocumentId);
            if (requirementDocument == null)
            {
                return new RequirementDecompositionResult
                {
                    Success = false,
                    ErrorMessage = "需求文档不存在"
                };
            }

            // 2. 构建需求内容
            var requirementContent = BuildRequirementContent(requirementDocument);

            // 3. 调用分解逻辑
            return await DecomposeRequirementContentAsync(
                requirementDocument.ProjectId,
                requirementContent,
                decompositionOptions,
                userId,
                requirementDocumentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分解需求文档失败，ID: {RequirementDocumentId}", requirementDocumentId);
            return new RequirementDecompositionResult
            {
                Success = false,
                ErrorMessage = $"分解失败: {ex.Message}"
            };
        }
    }

    public async Task<RequirementDecompositionResult> DecomposeRequirementContentAsync(
        int projectId,
        string requirementContent,
        DecompositionOptions decompositionOptions,
        int userId)
    {
        return await DecomposeRequirementContentAsync(projectId, requirementContent, decompositionOptions, userId, null);
    }

    private async Task<RequirementDecompositionResult> DecomposeRequirementContentAsync(
        int projectId,
        string requirementContent,
        DecompositionOptions decompositionOptions,
        int userId,
        int? requirementDocumentId)
    {
        try
        {

            // 1. 获取AI分解提示词模板
            var promptTemplate = await _promptTemplateRepository.GetDefaultTemplateAsync("RequirementDecomposition");
            if (promptTemplate == null)
            {
                // 如果没有默认模板，尝试获取第一个可用模板
                var templates = await _promptTemplateRepository.GetByTaskTypeAsync("RequirementDecomposition");
                promptTemplate = templates.FirstOrDefault();

                if (promptTemplate == null)
                {
                    return new RequirementDecompositionResult
                    {
                        Success = false,
                        ErrorMessage = "未找到需求分解提示词模板"
                    };
                }
            }

            // 2. 构建AI提示词
            var prompt = BuildDecompositionPrompt(promptTemplate, requirementContent, decompositionOptions);

            // 3. 根据用户配置调用AI服务进行分解
            string aiResponse;
            try
            {
                aiResponse = await CallAIServiceWithUserConfigAsync(prompt, userId, "RequirementDecomposition", decompositionOptions);
                if (string.IsNullOrEmpty(aiResponse))
                {
                    return new RequirementDecompositionResult
                    {
                        Success = false,
                        ErrorMessage = "AI分解失败，未返回结果"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用AI服务失败");
                return new RequirementDecompositionResult
                {
                    Success = false,
                    ErrorMessage = $"AI服务调用失败: {ex.Message}"
                };
            }

            // 4. 解析AI返回结果
            var decompositionData = ParseAIDecompositionResult(aiResponse);
            if (decompositionData == null)
            {
                return new RequirementDecompositionResult
                {
                    Success = false,
                    ErrorMessage = "AI返回结果解析失败"
                };
            }

            // 5. 创建开发步骤预览（不保存到数据库）
            var stepPreviews = CreateDevelopmentStepPreviews(decompositionData, projectId, requirementDocumentId, userId);

            // 6. 创建依赖关系预览（不保存到数据库）
            var dependencyPreviews = CreateStepDependencyPreviews(decompositionData, stepPreviews);

            // 7. 构建返回结果
            var result = new RequirementDecompositionResult
            {
                Success = true,
                Steps = stepPreviews,
                Dependencies = dependencyPreviews,
                Statistics = CalculateStatistics(stepPreviews, dependencyPreviews),
                AIAnalysisResult = decompositionData.Analysis,
                IsPreview = true // 标记为预览模式
            };


            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分解需求内容失败，项目ID: {ProjectId}", projectId);
            return new RequirementDecompositionResult
            {
                Success = false,
                ErrorMessage = $"分解失败: {ex.Message}"
            };
        }
    }

    public async Task<RequirementDecompositionResult> RedecomposeRequirementAsync(
        int requirementDocumentId,
        DecompositionOptions decompositionOptions,
        int userId,
        bool preserveExistingSteps = true)
    {
        try
        {
            _logger.LogInformation("开始重新分解需求文档，ID: {RequirementDocumentId}", requirementDocumentId);

            if (preserveExistingSteps)
            {
                // 软删除现有步骤
                var existingSteps = await _stepRepository.GetByRequirementDocumentIdAsync(requirementDocumentId);
                foreach (var step in existingSteps)
                {
                    await _stepRepository.SoftDeleteAsync(step.Id, userId);
                }
            }

            // 执行新的分解
            return await DecomposeRequirementAsync(requirementDocumentId, decompositionOptions, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新分解需求文档失败，ID: {RequirementDocumentId}", requirementDocumentId);
            return new RequirementDecompositionResult
            {
                Success = false,
                ErrorMessage = $"重新分解失败: {ex.Message}"
            };
        }
    }

    /// <summary>
    /// 确认并保存分解的步骤到数据库
    /// </summary>
    public async Task<RequirementDecompositionResult> ConfirmAndSaveStepsAsync(
        int projectId,
        int? requirementDocumentId,
        List<Core.Services.DevelopmentStepData> stepDataList,
        List<Core.Services.StepDependencyData> dependencyDataList,
        int userId)
    {
        try
        {
            _logger.LogInformation("开始确认保存步骤，ProjectId: {ProjectId}, StepCount: {StepCount}",
                projectId, stepDataList.Count);

            var savedSteps = new List<DevelopmentStep>();
            var stepNameToIdMap = new Dictionary<string, int>();

            // 第一遍：创建所有步骤（不设置父步骤）
            foreach (var stepData in stepDataList)
            {
                var step = new DevelopmentStep
                {
                    ProjectId = projectId,
                    RequirementDocumentId = requirementDocumentId,
                    StepName = stepData.StepName,
                    StepDescription = stepData.StepDescription,
                    StepType = stepData.StepType,
                    Priority = stepData.Priority,
                    EstimatedHours = stepData.EstimatedHours,
                    TechnologyStack = stepData.TechnologyStack,
                    FileType = stepData.FileType,
                    FilePath = stepData.FilePath,
                    ComponentType = stepData.ComponentType,
                    AIPrompt = stepData.AIPrompt,
                    StepOrder = stepData.StepOrder,
                    StepGroup = stepData.StepGroup,
                    StepLevel = stepData.StepLevel,
                    CreatedBy = userId,
                    Status = "Pending"
                };

                var savedStep = await _stepRepository.AddAsync(step);
                savedSteps.Add(savedStep);
                stepNameToIdMap[stepData.StepName] = savedStep.Id;
            }

            // 第二遍：设置父步骤关系
            for (int i = 0; i < stepDataList.Count; i++)
            {
                var stepData = stepDataList[i];
                if (!string.IsNullOrEmpty(stepData.ParentStepName) &&
                    stepNameToIdMap.TryGetValue(stepData.ParentStepName, out var parentId))
                {
                    savedSteps[i].ParentStepId = parentId;
                    await _stepRepository.UpdateAsync(savedSteps[i]);
                }
            }

            // 创建依赖关系
            var savedDependencies = new List<StepDependency>();
            foreach (var depData in dependencyDataList)
            {
                if (stepNameToIdMap.TryGetValue(depData.StepName, out var stepId) &&
                    stepNameToIdMap.TryGetValue(depData.DependsOnStepName, out var dependsOnStepId))
                {
                    var dependency = new StepDependency
                    {
                        StepId = stepId,
                        DependsOnStepId = dependsOnStepId,
                        DependencyType = depData.DependencyType,
                        CreatedBy = userId
                    };

                    var savedDependency = await _dependencyRepository.AddAsync(dependency);
                    savedDependencies.Add(savedDependency);
                }
            }

            // 构建返回结果
            var result = new RequirementDecompositionResult
            {
                Success = true,
                Steps = savedSteps,
                Dependencies = savedDependencies,
                Statistics = CalculateStatistics(savedSteps, savedDependencies),
                IsPreview = false // 已保存到数据库
            };

            _logger.LogInformation("步骤确认保存完成，生成 {StepCount} 个步骤，{DependencyCount} 个依赖关系",
                savedSteps.Count, savedDependencies.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认保存步骤失败");
            return new RequirementDecompositionResult
            {
                Success = false,
                ErrorMessage = $"保存步骤失败: {ex.Message}"
            };
        }
    }

    #endregion

    #region 私有辅助方法 - AI结果解析

    /// <summary>
    /// 解析AI依赖分析结果
    /// </summary>
    private AIDependencyAnalysisData? ParseAIDependencyAnalysisResult(string aiResponse)
    {
        try
        {
            var jsonContent = ExtractJsonFromResponse(aiResponse);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true
            };

            return JsonSerializer.Deserialize<AIDependencyAnalysisData>(jsonContent, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析AI依赖分析结果失败: {Response}", aiResponse);
            return null;
        }
    }

    /// <summary>
    /// 解析AI复杂度分析结果
    /// </summary>
    private StepComplexityAnalysis? ParseAIComplexityAnalysisResult(string aiResponse)
    {
        try
        {
            var jsonContent = ExtractJsonFromResponse(aiResponse);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true
            };

            return JsonSerializer.Deserialize<StepComplexityAnalysis>(jsonContent, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析AI复杂度分析结果失败: {Response}", aiResponse);
            return null;
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 构建需求内容
    /// </summary>
    private string BuildRequirementContent(RequirementDocument requirementDocument)
    {
        var content = $"# {requirementDocument.Title}\n\n";

        if (!string.IsNullOrEmpty(requirementDocument.Content))
        {
            content += $"## 需求描述\n{requirementDocument.Content}\n\n";
        }

        if (!string.IsNullOrEmpty(requirementDocument.FunctionalRequirements))
        {
            content += $"## 功能性需求\n{requirementDocument.FunctionalRequirements}\n\n";
        }

        if (!string.IsNullOrEmpty(requirementDocument.NonFunctionalRequirements))
        {
            content += $"## 非功能性需求\n{requirementDocument.NonFunctionalRequirements}\n\n";
        }

        if (!string.IsNullOrEmpty(requirementDocument.UserStories))
        {
            content += $"## 用户故事\n{requirementDocument.UserStories}\n\n";
        }

        if (!string.IsNullOrEmpty(requirementDocument.AcceptanceCriteria))
        {
            content += $"## 验收标准\n{requirementDocument.AcceptanceCriteria}\n\n";
        }

        return content;
    }

    /// <summary>
    /// 构建分解提示词
    /// </summary>
    private string BuildDecompositionPrompt(PromptTemplate template, string requirementContent, DecompositionOptions options)
    {
        var prompt = template.Content;

        // 替换参数
        prompt = prompt.Replace("{requirementContent}", requirementContent);
        prompt = prompt.Replace("{technologyStack}", options.TechnologyStack);
        prompt = prompt.Replace("{granularity}", options.Granularity);
        prompt = prompt.Replace("{maxStepCount}", options.MaxStepCount.ToString());
        prompt = prompt.Replace("{includeTestSteps}", options.IncludeTestSteps.ToString().ToLower());
        prompt = prompt.Replace("{includeDocumentationSteps}", options.IncludeDocumentationSteps.ToString().ToLower());

        return prompt;
    }

    /// <summary>
    /// 解析AI分解结果
    /// </summary>
    private AIDecompositionData? ParseAIDecompositionResult(string aiResponse)
    {
        try
        {
            // 清理AI响应，提取JSON部分
            var jsonContent = ExtractJsonFromResponse(aiResponse);
            _logger.LogInformation("提取的JSON内容长度: {Length}", jsonContent.Length);

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };

            var result = JsonSerializer.Deserialize<AIDecompositionData>(jsonContent, options);

            // 验证解析结果
            if (result == null)
            {
                _logger.LogWarning("JSON反序列化返回null");
                return TryParsePartialResult(aiResponse);
            }

            if (result.Steps == null || result.Steps.Count == 0)
            {
                _logger.LogWarning("解析结果中没有步骤信息");
                return TryParsePartialResult(aiResponse);
            }

            _logger.LogInformation("成功解析AI分解结果，包含 {StepCount} 个步骤，{DependencyCount} 个依赖关系",
                result.Steps.Count, result.Dependencies?.Count ?? 0);

            return result;
        }
        catch (JsonException jsonEx)
        {
            _logger.LogError(jsonEx, "JSON解析失败，尝试部分解析: {Message}", jsonEx.Message);
            return TryParsePartialResult(aiResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析AI分解结果失败: {Response}", aiResponse.Substring(0, Math.Min(500, aiResponse.Length)));
            return TryParsePartialResult(aiResponse);
        }
    }

    /// <summary>
    /// 尝试部分解析结果，作为降级策略
    /// </summary>
    private AIDecompositionData? TryParsePartialResult(string aiResponse)
    {
        try
        {
            _logger.LogInformation("尝试部分解析AI响应");

            // 尝试只解析steps部分
            var stepsMatch = System.Text.RegularExpressions.Regex.Match(aiResponse, @"""steps""\s*:\s*\[(.*?)\]",
                System.Text.RegularExpressions.RegexOptions.Singleline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            if (stepsMatch.Success)
            {
                var stepsJson = "[" + stepsMatch.Groups[1].Value + "]";

                // 尝试修复steps JSON
                stepsJson = TryFixJson(stepsJson);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                };

                var steps = JsonSerializer.Deserialize<List<AIStepData>>(stepsJson, options);

                if (steps != null && steps.Count > 0)
                {
                    _logger.LogInformation("部分解析成功，获得 {StepCount} 个步骤", steps.Count);

                    return new AIDecompositionData
                    {
                        Analysis = "部分解析结果",
                        Steps = steps,
                        Dependencies = new List<AIDependencyData>() // 空的依赖关系列表
                    };
                }
            }

            // 如果steps解析也失败，创建一个基本的结果
            _logger.LogWarning("部分解析也失败，创建基本结果");
            return CreateFallbackResult(aiResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "部分解析失败");
            return CreateFallbackResult(aiResponse);
        }
    }

    /// <summary>
    /// 创建降级结果
    /// </summary>
    private AIDecompositionData CreateFallbackResult(string aiResponse)
    {
        _logger.LogInformation("创建降级结果");

        return new AIDecompositionData
        {
            Analysis = "AI解析失败，已创建基本步骤",
            Steps = new List<AIStepData>
            {
                new AIStepData
                {
                    StepName = "需求分析",
                    StepDescription = "分析和理解需求文档",
                    StepType = "Development",
                    Priority = "High",
                    EstimatedHours = 8,
                    TechnologyStack = "General",
                    ComponentType = "Analysis",
                    StepOrder = 1,
                    StepLevel = 1,
                    AIPrompt = "请分析以下需求并提供详细的分析报告"
                },
                new AIStepData
                {
                    StepName = "设计方案",
                    StepDescription = "基于需求分析设计技术方案",
                    StepType = "Development",
                    Priority = "High",
                    EstimatedHours = 16,
                    TechnologyStack = "General",
                    ComponentType = "Design",
                    StepOrder = 2,
                    StepLevel = 1,
                    AIPrompt = "请基于需求分析结果设计详细的技术实现方案"
                }
            },
            Dependencies = new List<AIDependencyData>
            {
                new AIDependencyData
                {
                    StepName = "设计方案",
                    DependsOnStepName = "需求分析",
                    DependencyType = "Sequential",
                    IsRequired = true,
                    Description = "设计方案依赖于需求分析的结果"
                }
            }
        };
    }

    /// <summary>
    /// 从AI响应中提取JSON内容
    /// </summary>
    private string ExtractJsonFromResponse(string response)
    {
        try
        {
            // 移除markdown代码块标记
            response = response.Replace("```json", "").Replace("```", "").Trim();

            // 查找JSON开始标记
            var startIndex = response.IndexOf('{');
            if (startIndex < 0)
            {
                _logger.LogWarning("AI响应中未找到JSON开始标记: {Response}", response.Substring(0, Math.Min(200, response.Length)));
                return response;
            }

            // 尝试找到匹配的结束标记
            var jsonContent = ExtractValidJson(response, startIndex);

            if (string.IsNullOrEmpty(jsonContent))
            {
                _logger.LogWarning("无法提取有效的JSON内容: {Response}", response.Substring(0, Math.Min(200, response.Length)));
                return response;
            }

            return jsonContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提取JSON内容失败: {Response}", response.Substring(0, Math.Min(200, response.Length)));
            return response;
        }
    }

    /// <summary>
    /// 提取有效的JSON内容，处理不完整的JSON
    /// </summary>
    private string ExtractValidJson(string response, int startIndex)
    {
        var braceCount = 0;
        var inString = false;
        var escapeNext = false;
        var endIndex = -1;

        for (int i = startIndex; i < response.Length; i++)
        {
            var currentChar = response[i];

            if (escapeNext)
            {
                escapeNext = false;
                continue;
            }

            if (currentChar == '\\')
            {
                escapeNext = true;
                continue;
            }

            if (currentChar == '"')
            {
                inString = !inString;
                continue;
            }

            if (!inString)
            {
                if (currentChar == '{')
                {
                    braceCount++;
                }
                else if (currentChar == '}')
                {
                    braceCount--;
                    if (braceCount == 0)
                    {
                        endIndex = i;
                        break;
                    }
                }
            }
        }

        if (endIndex > startIndex)
        {
            var jsonContent = response.Substring(startIndex, endIndex - startIndex + 1);

            // 验证JSON是否有效
            if (IsValidJson(jsonContent))
            {
                return jsonContent;
            }
            else
            {
                // 尝试修复常见的JSON问题
                return TryFixJson(jsonContent);
            }
        }

        // 如果找不到完整的JSON，尝试修复不完整的JSON
        var partialJson = response.Substring(startIndex);
        return TryFixIncompleteJson(partialJson);
    }

    /// <summary>
    /// 验证JSON是否有效
    /// </summary>
    private bool IsValidJson(string jsonString)
    {
        try
        {
            JsonDocument.Parse(jsonString);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 尝试修复常见的JSON问题
    /// </summary>
    private string TryFixJson(string jsonContent)
    {
        try
        {
            // 移除尾随逗号
            jsonContent = System.Text.RegularExpressions.Regex.Replace(jsonContent, @",(\s*[}\]])", "$1");

            // 确保字符串值被正确引用
            jsonContent = System.Text.RegularExpressions.Regex.Replace(jsonContent, @":\s*([^""\[\{][^,\}\]]*?)(\s*[,\}\]])", ":\"$1\"$2");

            return jsonContent;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "修复JSON失败");
            return jsonContent;
        }
    }

    /// <summary>
    /// 尝试修复不完整的JSON
    /// </summary>
    private string TryFixIncompleteJson(string partialJson)
    {
        try
        {
            // 计算需要补充的大括号数量
            var openBraces = partialJson.Count(c => c == '{');
            var closeBraces = partialJson.Count(c => c == '}');
            var missingBraces = openBraces - closeBraces;

            // 计算需要补充的方括号数量
            var openBrackets = partialJson.Count(c => c == '[');
            var closeBrackets = partialJson.Count(c => c == ']');
            var missingBrackets = openBrackets - closeBrackets;

            // 移除最后的不完整内容
            var lastCommaIndex = partialJson.LastIndexOf(',');
            var lastBraceIndex = partialJson.LastIndexOf('{');
            var lastBracketIndex = partialJson.LastIndexOf('[');

            // 如果最后一个逗号在最后一个开括号之后，可能是不完整的对象/数组项
            if (lastCommaIndex > Math.Max(lastBraceIndex, lastBracketIndex))
            {
                partialJson = partialJson.Substring(0, lastCommaIndex);
            }

            // 补充缺失的括号
            for (int i = 0; i < missingBrackets; i++)
            {
                partialJson += "]";
            }

            for (int i = 0; i < missingBraces; i++)
            {
                partialJson += "}";
            }

            // 验证修复后的JSON
            if (IsValidJson(partialJson))
            {
                _logger.LogInformation("成功修复不完整的JSON");
                return partialJson;
            }
            else
            {
                _logger.LogWarning("修复不完整JSON失败，返回原始内容");
                return partialJson;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修复不完整JSON时发生错误");
            return partialJson;
        }
    }

    /// <summary>
    /// 创建开发步骤预览（不保存到数据库）
    /// </summary>
    private List<DevelopmentStep> CreateDevelopmentStepPreviews(
        AIDecompositionData decompositionData,
        int projectId,
        int? requirementDocumentId,
        int userId)
    {
        var steps = new List<DevelopmentStep>();
        var stepNameToTempIdMap = new Dictionary<string, int>();
        int tempId = -1; // 使用负数作为临时ID

        // 创建所有步骤预览（使用临时ID）
        foreach (var stepData in decompositionData.Steps)
        {
            var step = new DevelopmentStep
            {
                Id = tempId, // 临时ID
                ProjectId = projectId,
                RequirementDocumentId = requirementDocumentId,
                StepName = stepData.StepName,
                StepDescription = stepData.StepDescription,
                StepType = stepData.StepType,
                Priority = stepData.Priority,
                EstimatedHours = stepData.EstimatedHours,
                TechnologyStack = stepData.TechnologyStack,
                FileType = stepData.FileType,
                FilePath = stepData.FilePath,
                ComponentType = stepData.ComponentType,
                AIPrompt = stepData.AIPrompt,
                StepOrder = stepData.StepOrder,
                StepGroup = stepData.StepGroup,
                StepLevel = stepData.StepLevel,
                CreatedBy = userId,
                CreatedTime = DateTime.Now,
                Status = "Pending"
            };

            steps.Add(step);
            stepNameToTempIdMap[stepData.StepName] = tempId;
            tempId--;
        }

        // 设置父步骤关系（使用临时ID）
        for (int i = 0; i < decompositionData.Steps.Count; i++)
        {
            var stepData = decompositionData.Steps[i];
            if (!string.IsNullOrEmpty(stepData.ParentStepName) &&
                stepNameToTempIdMap.TryGetValue(stepData.ParentStepName, out var parentTempId))
            {
                steps[i].ParentStepId = parentTempId;
            }
        }

        return steps;
    }

    /// <summary>
    /// 创建步骤依赖关系预览（不保存到数据库）
    /// </summary>
    private List<StepDependency> CreateStepDependencyPreviews(
        AIDecompositionData decompositionData,
        List<DevelopmentStep> stepPreviews)
    {
        var dependencies = new List<StepDependency>();
        var stepNameToTempIdMap = stepPreviews.ToDictionary(s => s.StepName, s => s.Id);
        int tempDepId = -1000; // 使用更小的负数作为依赖关系临时ID

        foreach (var stepData in decompositionData.Steps)
        {
            if (stepData.Dependencies?.Any() == true)
            {
                foreach (var depName in stepData.Dependencies)
                {
                    if (stepNameToTempIdMap.TryGetValue(stepData.StepName, out var stepTempId) &&
                        stepNameToTempIdMap.TryGetValue(depName, out var depStepTempId))
                    {
                        dependencies.Add(new StepDependency
                        {
                            Id = tempDepId,
                            StepId = stepTempId,
                            DependsOnStepId = depStepTempId,
                            DependencyType = "Sequential",
                            CreatedTime = DateTime.Now
                        });
                        tempDepId--;
                    }
                }
            }
        }

        return dependencies;
    }


    /// <summary>
    /// 计算统计信息
    /// </summary>
    private DecompositionStatistics CalculateStatistics(List<DevelopmentStep> steps, List<StepDependency> dependencies)
    {
        return new DecompositionStatistics
        {
            TotalSteps = steps.Count,
            DevelopmentSteps = steps.Count(s => s.StepType == "Development"),
            TestSteps = steps.Count(s => s.StepType == "Testing"),
            DocumentationSteps = steps.Count(s => s.StepType == "Documentation"),
            DependencyCount = dependencies.Count,
            EstimatedTotalHours = steps.Sum(s => s.EstimatedHours ?? 0),
            MaxDepth = steps.Max(s => s.StepLevel),
            TechnologyStacks = steps.Where(s => !string.IsNullOrEmpty(s.TechnologyStack))
                                  .Select(s => s.TechnologyStack!)
                                  .Distinct()
                                  .ToList()
        };
    }

    #endregion

    #region 步骤管理

    public async Task<DevelopmentStep> AddCustomStepAsync(DevelopmentStep step, int userId)
    {
        try
        {
            step.CreatedBy = userId;
            return await _stepRepository.AddAsync(step);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加自定义步骤失败");
            throw;
        }
    }

    public async Task<DevelopmentStep> UpdateStepAsync(int stepId, StepUpdateData updateData, int userId)
    {
        try
        {
            var step = await _stepRepository.GetByIdAsync(stepId);
            if (step == null)
                throw new ArgumentException("步骤不存在");

            // 更新字段
            if (!string.IsNullOrEmpty(updateData.StepName))
                step.StepName = updateData.StepName;

            if (!string.IsNullOrEmpty(updateData.StepDescription))
                step.StepDescription = updateData.StepDescription;

            if (!string.IsNullOrEmpty(updateData.Priority))
                step.Priority = updateData.Priority;

            if (!string.IsNullOrEmpty(updateData.Status))
                step.Status = updateData.Status;

            if (updateData.EstimatedHours.HasValue)
                step.EstimatedHours = updateData.EstimatedHours;

            if (updateData.ActualHours.HasValue)
                step.ActualHours = updateData.ActualHours;

            if (!string.IsNullOrEmpty(updateData.TechnologyStack))
                step.TechnologyStack = updateData.TechnologyStack;

            if (!string.IsNullOrEmpty(updateData.AIPrompt))
                step.AIPrompt = updateData.AIPrompt;

            if (updateData.Progress.HasValue)
                step.UpdateProgress(updateData.Progress.Value);

            step.UpdatedBy = userId;
            await _stepRepository.UpdateAsync(step);

            return step;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新步骤失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<bool> DeleteStepAsync(int stepId, int userId, bool deleteChildSteps = false)
    {
        try
        {
            if (deleteChildSteps)
            {
                var childSteps = await _stepRepository.GetChildStepsAsync(stepId);
                foreach (var childStep in childSteps)
                {
                    await _stepRepository.SoftDeleteAsync(childStep.Id, userId);
                }
            }

            return await _stepRepository.SoftDeleteAsync(stepId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除步骤失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<bool> MoveStepAsync(int stepId, int? newParentStepId, int userId)
    {
        try
        {
            var step = await _stepRepository.GetByIdAsync(stepId);
            if (step == null)
                return false;

            step.ParentStepId = newParentStepId;
            step.UpdatedBy = userId;

            return await _stepRepository.UpdateAsync(step);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动步骤失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<bool> ReorderStepsAsync(List<StepOrderInfo> stepOrders, int userId)
    {
        try
        {
            var orderDict = stepOrders.ToDictionary(so => so.StepId, so => so.Order);
            var updateCount = await _stepRepository.ReorderStepsAsync(orderDict, userId);

            return updateCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新排序步骤失败");
            throw;
        }
    }

    #endregion

    #region 依赖管理

    public async Task<StepDependency> AddStepDependencyAsync(
        int stepId,
        int dependsOnStepId,
        string dependencyType = "Sequential",
        bool isRequired = true,
        int userId = 0)
    {
        try
        {
            var dependency = new StepDependency
            {
                StepId = stepId,
                DependsOnStepId = dependsOnStepId,
                DependencyType = dependencyType,
                IsRequired = isRequired,
                CreatedBy = userId
            };

            return await _dependencyRepository.AddAsync(dependency);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加步骤依赖失败，StepId: {StepId}, DependsOnStepId: {DependsOnStepId}",
                stepId, dependsOnStepId);
            throw;
        }
    }

    public async Task<bool> RemoveStepDependencyAsync(int dependencyId, int userId)
    {
        try
        {
            return await _dependencyRepository.SoftDeleteAsync(dependencyId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除步骤依赖失败，DependencyId: {DependencyId}", dependencyId);
            throw;
        }
    }

    public async Task<int> AutoAnalyzeDependenciesAsync(int projectId, int userId)
    {
        try
        {
            var steps = await _stepRepository.GetByProjectIdAsync(projectId);
            if (steps.Count == 0)
                return 0;

            // 获取依赖分析提示词模板
            var promptTemplate = await _promptTemplateRepository.GetDefaultTemplateAsync("DependencyAnalysis");
            if (promptTemplate == null)
            {
                // 如果没有默认模板，尝试获取第一个可用模板
                var templates = await _promptTemplateRepository.GetByTaskTypeAsync("DependencyAnalysis");
                promptTemplate = templates.FirstOrDefault();

                if (promptTemplate == null)
                    return 0;
            }

            // 构建步骤列表JSON
            var stepsJson = JsonSerializer.Serialize(steps.Select(s => new
            {
                s.StepName,
                s.StepDescription,
                s.StepType,
                s.TechnologyStack,
                s.ComponentType,
                s.StepOrder
            }));

            // 构建AI提示词
            var prompt = promptTemplate.Content.Replace("{stepsList}", stepsJson);

            // 调用AI服务
            var aiResponse = await CallAIServiceWithUserConfigAsync(prompt, userId, "DependencyAnalysis", new DecompositionOptions());
            if (string.IsNullOrEmpty(aiResponse))
                return 0;

            // 解析AI返回的依赖关系
            var dependencyData = ParseAIDependencyAnalysisResult(aiResponse);
            if (dependencyData?.Dependencies == null)
                return 0;

            // 创建依赖关系
            var stepNameToIdMap = steps.ToDictionary(s => s.StepName, s => s.Id);
            var createdCount = 0;

            foreach (var depData in dependencyData.Dependencies)
            {
                if (stepNameToIdMap.TryGetValue(depData.StepName, out var stepId) &&
                    stepNameToIdMap.TryGetValue(depData.DependsOnStepName, out var dependsOnStepId))
                {
                    await AddStepDependencyAsync(stepId, dependsOnStepId, depData.DependencyType, depData.IsRequired, userId);
                    createdCount++;
                }
            }

            return createdCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "自动分析依赖关系失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<DependencyValidationResult> ValidateDependenciesAsync(int projectId)
    {
        try
        {
            var result = new DependencyValidationResult { IsValid = true };

            // 检查循环依赖
            var hasCircular = await _stepRepository.HasCircularDependencyAsync(projectId);
            if (hasCircular)
            {
                result.IsValid = false;
                result.Errors.Add("检测到循环依赖");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证依赖关系失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    #endregion

    #region 执行管理

    public async Task<StepExecutionHistory> StartStepExecutionAsync(int stepId, string executorType, int userId)
    {
        try
        {
            var step = await _stepRepository.GetByIdAsync(stepId);
            if (step == null)
                throw new ArgumentException("步骤不存在");

            // 检查依赖是否满足
            if (!step.CanStart())
                throw new InvalidOperationException("步骤依赖未满足，无法开始执行");

            // 开始执行步骤
            step.Start();
            await _stepRepository.UpdateAsync(step);

            // 创建执行历史记录
            var executionHistory = StepExecutionHistory.CreateNew(stepId, executorType);
            executionHistory.CreatedBy = userId;

            return await _executionHistoryRepository.AddAsync(executionHistory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始步骤执行失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    public async Task<bool> CompleteStepExecutionAsync(
        string executionId,
        string result,
        string? generatedCode = null,
        string? outputFiles = null,
        int userId = 0)
    {
        try
        {
            var executionHistory = await _executionHistoryRepository.GetFirstOrDefaultAsync(h => h.ExecutionId == executionId);
            if (executionHistory == null)
                return false;

            // 更新执行历史
            executionHistory.CompleteExecution(result);
            if (!string.IsNullOrEmpty(generatedCode))
                executionHistory.GeneratedCode = generatedCode;
            if (!string.IsNullOrEmpty(outputFiles))
                executionHistory.OutputFiles = outputFiles;

            await _executionHistoryRepository.UpdateAsync(executionHistory);

            // 更新步骤状态
            var step = await _stepRepository.GetByIdAsync(executionHistory.StepId);
            if (step != null)
            {
                if (result == "Success")
                {
                    step.Complete();
                    if (!string.IsNullOrEmpty(generatedCode))
                        step.AIGeneratedCode = generatedCode;
                    if (!string.IsNullOrEmpty(outputFiles))
                        step.GeneratedFiles = outputFiles;
                }
                else
                {
                    step.Fail($"执行结果: {result}");
                }

                await _stepRepository.UpdateAsync(step);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完成步骤执行失败，ExecutionId: {ExecutionId}", executionId);
            throw;
        }
    }

    public async Task<bool> FailStepExecutionAsync(string executionId, string errorMessage, int userId)
    {
        try
        {
            var executionHistory = await _executionHistoryRepository.GetFirstOrDefaultAsync(h => h.ExecutionId == executionId);
            if (executionHistory == null)
                return false;

            // 更新执行历史
            executionHistory.FailExecution(errorMessage);
            await _executionHistoryRepository.UpdateAsync(executionHistory);

            // 更新步骤状态
            var step = await _stepRepository.GetByIdAsync(executionHistory.StepId);
            if (step != null)
            {
                step.Fail(errorMessage);
                await _stepRepository.UpdateAsync(step);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记步骤执行失败失败，ExecutionId: {ExecutionId}", executionId);
            throw;
        }
    }

    public async Task<List<DevelopmentStep>> GetNextExecutableStepsAsync(
        int projectId,
        string? stepType = null,
        string? priority = null)
    {
        try
        {
            var executableSteps = await _stepRepository.GetExecutableStepsAsync(projectId);

            if (!string.IsNullOrEmpty(stepType))
            {
                executableSteps = executableSteps.Where(s => s.StepType == stepType).ToList();
            }

            if (!string.IsNullOrEmpty(priority))
            {
                executableSteps = executableSteps.Where(s => s.Priority == priority).ToList();
            }

            // 按优先级和排序号排序
            var priorityOrder = new Dictionary<string, int>
            {
                { "Critical", 1 },
                { "High", 2 },
                { "Medium", 3 },
                { "Low", 4 }
            };

            return executableSteps
                .OrderBy(s => priorityOrder.GetValueOrDefault(s.Priority, 5))
                .ThenBy(s => s.StepOrder)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取下一个可执行步骤失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    #endregion

    #region 统计分析

    public async Task<ProjectDecompositionStatistics> GetProjectDecompositionStatisticsAsync(int projectId)
    {
        try
        {
            var project = await _projectRepository.GetByIdAsync(projectId);
            var stepStats = await _stepRepository.GetProjectStepStatisticsAsync(projectId);
            var requirements = await _requirementRepository.GetRequirementDocumentsByProjectAsync(projectId);

            return new ProjectDecompositionStatistics
            {
                ProjectId = projectId,
                ProjectName = project?.Name ?? "",
                RequirementCount = requirements.Count,
                TotalSteps = stepStats.TotalSteps,
                CompletedSteps = stepStats.CompletedSteps,
                CompletionRate = stepStats.TotalSteps > 0 ? (double)stepStats.CompletedSteps / stepStats.TotalSteps * 100 : 0,
                TotalEstimatedHours = stepStats.TotalEstimatedHours,
                TotalActualHours = stepStats.TotalActualHours,
                AverageStepsPerRequirement = requirements.Count > 0 ? stepStats.TotalSteps / requirements.Count : 0,
                TechnologyStacks = new List<string>() // 需要从步骤中提取
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目分解统计失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<StepExecutionProgress> GetStepExecutionProgressAsync(int projectId)
    {
        try
        {
            var stepStats = await _stepRepository.GetProjectStepStatisticsAsync(projectId);
            var steps = await _stepRepository.GetByProjectIdAsync(projectId);

            var progressByType = steps
                .GroupBy(s => s.StepType)
                .Select(g => new StepTypeProgress
                {
                    StepType = g.Key,
                    Total = g.Count(),
                    Completed = g.Count(s => s.Status == "Completed"),
                    Progress = g.Count() > 0 ? (double)g.Count(s => s.Status == "Completed") / g.Count() * 100 : 0
                })
                .ToList();

            return new StepExecutionProgress
            {
                ProjectId = projectId,
                TotalSteps = stepStats.TotalSteps,
                PendingSteps = stepStats.PendingSteps,
                InProgressSteps = stepStats.InProgressSteps,
                CompletedSteps = stepStats.CompletedSteps,
                FailedSteps = stepStats.FailedSteps,
                BlockedSteps = stepStats.BlockedSteps,
                OverallProgress = stepStats.AverageProgress,
                ProgressByType = progressByType
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取步骤执行进度失败，ProjectId: {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<StepComplexityAnalysis> AnalyzeStepComplexityAsync(int stepId)
    {
        try
        {
            var step = await _stepRepository.GetByIdAsync(stepId);
            if (step == null)
                throw new ArgumentException("步骤不存在");

            // 获取复杂度评估提示词模板
            var promptTemplate = await _promptTemplateRepository.GetDefaultTemplateAsync("ComplexityAnalysis");
            if (promptTemplate == null)
            {
                // 如果没有默认模板，尝试获取第一个可用模板
                var templates = await _promptTemplateRepository.GetByTaskTypeAsync("ComplexityAnalysis");
                promptTemplate = templates.FirstOrDefault();

                if (promptTemplate == null)
                {
                    // 使用简单的复杂度评估
                    return new StepComplexityAnalysis
                    {
                        StepId = stepId,
                        StepName = step.StepName,
                        ComplexityScore = 5, // 默认中等复杂度
                        EstimatedHours = step.EstimatedHours ?? 8,
                        RiskLevel = "Medium"
                    };
                }
            }

            // 构建AI提示词
            var prompt = promptTemplate.Content
                .Replace("{stepName}", step.StepName)
                .Replace("{stepDescription}", step.StepDescription ?? "")
                .Replace("{technologyStack}", step.TechnologyStack ?? "")
                .Replace("{componentType}", step.ComponentType ?? "");

            // 调用AI服务 - 由于接口限制，这里暂时使用默认配置
            // TODO: 需要修改接口添加 userId 参数以支持用户配置
            var aiResponse = await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
            if (string.IsNullOrEmpty(aiResponse))
            {
                return new StepComplexityAnalysis
                {
                    StepId = stepId,
                    StepName = step.StepName,
                    ComplexityScore = 5,
                    EstimatedHours = step.EstimatedHours ?? 8,
                    RiskLevel = "Medium"
                };
            }

            // 解析AI返回结果
            var complexityData = ParseAIComplexityAnalysisResult(aiResponse);
            return complexityData ?? new StepComplexityAnalysis
            {
                StepId = stepId,
                StepName = step.StepName,
                ComplexityScore = 5,
                EstimatedHours = step.EstimatedHours ?? 8,
                RiskLevel = "Medium"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分析步骤复杂度失败，StepId: {StepId}", stepId);
            throw;
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 根据用户配置调用AI服务
    /// </summary>
    private async Task<string> CallAIServiceWithUserConfigAsync(
        string prompt,
        int userId,
        string taskType,
        DecompositionOptions decompositionOptions)
    {
        try
        {
            // 使用前端传递的AI配置ID
            if (!string.IsNullOrEmpty(decompositionOptions.AIProvider) && int.TryParse(decompositionOptions.AIProvider, out int configId))
            {
                var selectedAIConfig = await _aiModelConfigurationRepository.GetByIdAsync(configId);
                if (selectedAIConfig != null && (selectedAIConfig.UserId == userId || selectedAIConfig.UserId == null) && selectedAIConfig.IsActive)
                {
                    // 解析模型参数
                    int maxTokens = 4000;
                    float temperature = 0.7f;

                    if (!string.IsNullOrEmpty(selectedAIConfig.ModelParameters))
                    {
                        try
                        {
                            var modelParams = JsonSerializer.Deserialize<Dictionary<string, object>>(selectedAIConfig.ModelParameters);
                            if (modelParams?.TryGetValue("MaxTokens", out var maxTokensValue) == true)
                            {
                                int.TryParse(maxTokensValue.ToString(), out maxTokens);
                            }
                            if (modelParams?.TryGetValue("Temperature", out var temperatureValue) == true)
                            {
                                float.TryParse(temperatureValue.ToString(), out temperature);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "解析用户AI配置参数失败，使用默认值");
                        }
                    }

                    // 根据模型名称推断提供商
                    string providerName = InferProviderFromModelName(selectedAIConfig.ModelName);

                    // 使用指定的AI配置
                    var config = new AIModelConfig
                    {
                        Provider = providerName,
                        Model = selectedAIConfig.ModelName,
                        ApiKey = selectedAIConfig.ApiKey ?? string.Empty,
                        Endpoint = selectedAIConfig.ApiEndpoint ?? string.Empty,
                        MaxTokens = maxTokens,
                        Temperature = temperature
                    };

                    _logger.LogInformation("使用指定的AI配置进行需求分解: {ConfigId}, {Provider}, {Model}, 用户: {UserId}",
                        configId, providerName, selectedAIConfig.ModelName, userId);

                    var response = await _aiService.GenerateTextAsync(prompt, config);

                    return response;
                }
                else
                {
                    _logger.LogWarning("指定的AI配置不存在或不属于当前用户: {ConfigId}, 用户: {UserId}", configId, userId);
                    throw new InvalidOperationException($"指定的AI配置不存在或无权访问: {configId}");
                }
            }
            else
            {
                // 如果没有提供AI配置ID，使用默认配置（主要用于依赖分析等后台任务）
                _logger.LogInformation("未提供AI配置ID，使用默认配置，用户: {UserId}, 任务类型: {TaskType}", userId, taskType);
                return await _aiService.GenerateTextAsync(prompt, (AIModelConfig?)null);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调用AI服务失败，用户: {UserId}, 任务类型: {TaskType}", userId, taskType);
            throw;
        }
    }

    #endregion

    #region AI数据模型

    /// <summary>
    /// AI分解数据模型
    /// </summary>
    public class AIDecompositionData
    {
        public List<AIStepData> Steps { get; set; } = new();
        public List<AIDependencyData> Dependencies { get; set; } = new();
        public AIStatisticsData Statistics { get; set; } = new();
        public string Analysis { get; set; } = string.Empty;
    }

    /// <summary>
    /// AI步骤数据
    /// </summary>
    public class AIStepData
    {
        public string StepName { get; set; } = string.Empty;
        public string StepDescription { get; set; } = string.Empty;
        public string StepType { get; set; } = "Development";
        public string Priority { get; set; } = "Medium";
        public decimal EstimatedHours { get; set; }
        public string TechnologyStack { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty;
        public string AIPrompt { get; set; } = string.Empty;
        public int StepOrder { get; set; }
        public string StepGroup { get; set; } = string.Empty;
        public int StepLevel { get; set; } = 1;
        public string ParentStepName { get; set; } = string.Empty;
        public List<string> Dependencies { get; set; } = new();
    }

    /// <summary>
    /// AI依赖数据
    /// </summary>
    public class AIDependencyData
    {
        public string StepName { get; set; } = string.Empty;
        public string DependsOnStepName { get; set; } = string.Empty;
        public string DependencyType { get; set; } = "Sequential";
        public bool IsRequired { get; set; } = true;
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// AI统计数据
    /// </summary>
    public class AIStatisticsData
    {
        public int TotalSteps { get; set; }
        public int DevelopmentSteps { get; set; }
        public int TestSteps { get; set; }
        public int DocumentationSteps { get; set; }
        public decimal EstimatedTotalHours { get; set; }
        public int MaxDepth { get; set; }
        public List<string> TechnologyStacks { get; set; } = new();
    }

    /// <summary>
    /// AI依赖分析数据
    /// </summary>
    public class AIDependencyAnalysisData
    {
        public List<AIDependencyData> Dependencies { get; set; } = new();
        public List<ExecutionPhase> ExecutionOrder { get; set; } = new();
        public DependencyValidation Validation { get; set; } = new();
        public List<string> CriticalPath { get; set; } = new();
        public decimal EstimatedDuration { get; set; }
    }

    /// <summary>
    /// 执行阶段
    /// </summary>
    public class ExecutionPhase
    {
        public int Phase { get; set; }
        public List<string> ParallelSteps { get; set; } = new();
    }

    /// <summary>
    /// 依赖验证
    /// </summary>
    public class DependencyValidation
    {
        public bool HasCircularDependency { get; set; }
        public List<string> CircularDependencies { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 根据模型名称推断AI提供商
    /// </summary>
    private string InferProviderFromModelName(string modelName)
    {
        if (string.IsNullOrEmpty(modelName))
            return "Mock";

        var lowerModelName = modelName.ToLower();

        if (lowerModelName.Contains("gpt") || lowerModelName.Contains("openai"))
            return "Azure"; // 或者 "OpenAI"，根据实际情况

        if (lowerModelName.Contains("deepseek"))
            return "DeepSeek";

        if (lowerModelName.Contains("claude"))
            return "Claude";

        if (lowerModelName.Contains("ollama"))
            return "Ollama";

        // 默认返回Mock
        return "Mock";
    }
}

#endregion
