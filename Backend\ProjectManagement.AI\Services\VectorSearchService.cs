using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.Core.DTOs.VectorSearch;
using ProjectManagement.Data.Repositories;
using System.Text.Json;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// 向量搜索服务 - 提供语义搜索和RAG支持
    /// </summary>
    public class VectorSearchService
    {
        private readonly ILogger<VectorSearchService> _logger;
        private readonly IAIService _aiService;
        private readonly IDocumentVectorRepository _vectorRepository;

        public VectorSearchService(
            ILogger<VectorSearchService> logger,
            IAIService aiService,
            IDocumentVectorRepository vectorRepository)
        {
            _logger = logger;
            _aiService = aiService;
            _vectorRepository = vectorRepository;
        }

        /// <summary>
        /// 索引文档
        /// </summary>
        public async Task<bool> IndexDocumentAsync(DocumentToIndex document)
        {
            _logger.LogInformation("索引文档: {DocumentId} - {Title}", document.Id, document.Title);

            try
            {
                // 将文档分块
                var chunks = SplitDocumentIntoChunks(document.Content, 500, 50);

                foreach (var (chunk, index) in chunks.Select((c, i) => (c, i)))
                {
                    // 生成向量嵌入
                    var embedding = await GenerateEmbeddingAsync(chunk);

                    // 存储向量
                    var vectorDoc = new DocumentVector
                    {
                        DocumentId = document.Id,
                        ChunkIndex = index,
                        Content = chunk,
                        Embedding = embedding,
                        Metadata = new Dictionary<string, object>
                        {
                            ["title"] = document.Title,
                            ["type"] = document.Type,
                            ["projectId"] = document.ProjectId,
                            ["createdAt"] = document.CreatedAt,
                            ["tags"] = document.Tags
                        }
                    };

                    await _vectorRepository.CreateAsync(vectorDoc);
                }

                _logger.LogInformation("文档索引完成: {DocumentId}, 共 {ChunkCount} 个分块", document.Id, chunks.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文档索引失败: {DocumentId}", document.Id);
                return false;
            }
        }

        /// <summary>
        /// 语义搜索
        /// </summary>
        public async Task<List<SearchResult>> SemanticSearchAsync(string query, SearchOptions options)
        {
            _logger.LogInformation("执行语义搜索: {Query}", query);

            try
            {
                // 生成查询向量
                var queryEmbedding = await GenerateEmbeddingAsync(query);

                // 向量相似度搜索
                var similarDocuments = await _vectorRepository.FindSimilarAsync(
                    queryEmbedding, 
                    options.TopK, 
                    options.SimilarityThreshold,
                    options.Filters);

                // 构建搜索结果
                var results = similarDocuments.Select(doc => new SearchResult
                {
                    DocumentId = doc.DocumentId,
                    ChunkIndex = doc.ChunkIndex,
                    Content = doc.Content,
                    SimilarityScore = doc.SimilarityScore,
                    Metadata = doc.Metadata,
                    Highlights = ExtractHighlights(doc.Content, query)
                }).ToList();

                _logger.LogInformation("语义搜索完成: 找到 {ResultCount} 个相关结果", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "语义搜索失败: {Query}", query);
                return new List<SearchResult>();
            }
        }

        /// <summary>
        /// 混合搜索（关键词 + 语义）
        /// </summary>
        public async Task<List<SearchResult>> HybridSearchAsync(string query, SearchOptions options)
        {
            _logger.LogInformation("执行混合搜索: {Query}", query);

            // 语义搜索结果
            var semanticResults = await SemanticSearchAsync(query, options);

            // 关键词搜索结果
            var keywordResults = await KeywordSearchAsync(query, options);

            // 合并和重排序结果
            var combinedResults = CombineSearchResults(semanticResults, keywordResults, options.RerankingWeight);

            return combinedResults.Take(options.TopK).ToList();
        }

        /// <summary>
        /// RAG增强生成
        /// </summary>
        public async Task<RAGResponse> GenerateWithRAGAsync(string query, RAGOptions options)
        {
            _logger.LogInformation("执行RAG增强生成: {Query}", query);

            try
            {
                // 检索相关文档
                var searchResults = await SemanticSearchAsync(query, new SearchOptions
                {
                    TopK = options.RetrievalTopK,
                    SimilarityThreshold = options.SimilarityThreshold,
                    Filters = options.Filters
                });

                if (!searchResults.Any())
                {
                    return new RAGResponse
                    {
                        Success = false,
                        ErrorMessage = "未找到相关文档",
                        GeneratedText = "抱歉，我没有找到相关的文档来回答您的问题。"
                    };
                }

                // 构建增强上下文
                var context = BuildRAGContext(searchResults, options.MaxContextLength);

                // 构建RAG提示词
                var ragPrompt = BuildRAGPrompt(query, context, options.PromptTemplate);

                // 生成回答
                var generatedText = await _aiService.GenerateTextAsync(ragPrompt, options.AIConfig);

                return new RAGResponse
                {
                    Success = true,
                    GeneratedText = generatedText,
                    SourceDocuments = searchResults.Select(r => new SourceDocument
                    {
                        DocumentId = r.DocumentId,
                        Content = r.Content,
                        SimilarityScore = r.SimilarityScore,
                        Metadata = r.Metadata
                    }).ToList(),
                    Context = context
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RAG增强生成失败: {Query}", query);
                return new RAGResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    GeneratedText = "生成回答时发生错误，请稍后重试。"
                };
            }
        }

        /// <summary>
        /// 生成文本嵌入向量
        /// </summary>
        private async Task<float[]> GenerateEmbeddingAsync(string text)
        {
            // 这里应该调用实际的嵌入模型API
            // 例如 OpenAI Embeddings, Sentence Transformers 等
            // 目前返回模拟向量
            
            var random = new Random(text.GetHashCode());
            var embedding = new float[1536]; // OpenAI ada-002 维度
            
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2 - 1); // -1 到 1 之间
            }

            // 归一化向量
            var magnitude = Math.Sqrt(embedding.Sum(x => x * x));
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(embedding[i] / magnitude);
            }

            return embedding;
        }

        /// <summary>
        /// 将文档分块
        /// </summary>
        private List<string> SplitDocumentIntoChunks(string content, int chunkSize, int overlap)
        {
            var chunks = new List<string>();
            var sentences = content.Split(new[] { '.', '!', '?' }, StringSplitOptions.RemoveEmptyEntries);
            
            var currentChunk = "";
            var currentLength = 0;

            foreach (var sentence in sentences)
            {
                var trimmedSentence = sentence.Trim();
                if (string.IsNullOrEmpty(trimmedSentence)) continue;

                if (currentLength + trimmedSentence.Length > chunkSize && !string.IsNullOrEmpty(currentChunk))
                {
                    chunks.Add(currentChunk.Trim());
                    
                    // 保留重叠部分
                    var words = currentChunk.Split(' ');
                    var overlapWords = words.TakeLast(overlap).ToArray();
                    currentChunk = string.Join(" ", overlapWords) + " ";
                    currentLength = currentChunk.Length;
                }

                currentChunk += trimmedSentence + ". ";
                currentLength += trimmedSentence.Length + 2;
            }

            if (!string.IsNullOrEmpty(currentChunk.Trim()))
            {
                chunks.Add(currentChunk.Trim());
            }

            return chunks;
        }

        /// <summary>
        /// 关键词搜索
        /// </summary>
        private async Task<List<SearchResult>> KeywordSearchAsync(string query, SearchOptions options)
        {
            // 实现基于关键词的全文搜索
            // 这里简化实现
            return new List<SearchResult>();
        }

        /// <summary>
        /// 合并搜索结果
        /// </summary>
        private List<SearchResult> CombineSearchResults(List<SearchResult> semanticResults, List<SearchResult> keywordResults, float rerankingWeight)
        {
            // 实现结果合并和重排序算法
            // 这里简化实现，直接返回语义搜索结果
            return semanticResults;
        }

        /// <summary>
        /// 提取高亮片段
        /// </summary>
        private List<string> ExtractHighlights(string content, string query)
        {
            var highlights = new List<string>();
            var queryWords = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var sentences = content.Split('.', StringSplitOptions.RemoveEmptyEntries);

            foreach (var sentence in sentences)
            {
                if (queryWords.Any(word => sentence.ToLower().Contains(word)))
                {
                    highlights.Add(sentence.Trim());
                }
            }

            return highlights.Take(3).ToList(); // 最多返回3个高亮片段
        }

        /// <summary>
        /// 构建RAG上下文
        /// </summary>
        private string BuildRAGContext(List<SearchResult> searchResults, int maxLength)
        {
            var context = "";
            var currentLength = 0;

            foreach (var result in searchResults.OrderByDescending(r => r.SimilarityScore))
            {
                var addition = $"\n\n--- 文档片段 (相似度: {result.SimilarityScore:F2}) ---\n{result.Content}";
                
                if (currentLength + addition.Length > maxLength)
                {
                    break;
                }

                context += addition;
                currentLength += addition.Length;
            }

            return context;
        }

        /// <summary>
        /// 构建RAG提示词
        /// </summary>
        private string BuildRAGPrompt(string query, string context, string promptTemplate)
        {
            var template = promptTemplate ?? @"
基于以下文档内容回答用户问题。如果文档中没有相关信息，请明确说明。

文档内容：
{context}

用户问题：{query}

请基于文档内容提供准确、详细的回答：";

            return template
                .Replace("{context}", context)
                .Replace("{query}", query);
        }

        /// <summary>
        /// 删除文档索引
        /// </summary>
        public async Task<bool> DeleteDocumentIndexAsync(string documentId)
        {
            _logger.LogInformation("删除文档索引: {DocumentId}", documentId);

            try
            {
                await _vectorRepository.DeleteByDocumentIdAsync(documentId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文档索引失败: {DocumentId}", documentId);
                return false;
            }
        }

        /// <summary>
        /// 更新文档索引
        /// </summary>
        public async Task<bool> UpdateDocumentIndexAsync(DocumentToIndex document)
        {
            _logger.LogInformation("更新文档索引: {DocumentId}", document.Id);

            // 先删除旧索引
            await DeleteDocumentIndexAsync(document.Id);

            // 重新索引
            return await IndexDocumentAsync(document);
        }
    }
}
