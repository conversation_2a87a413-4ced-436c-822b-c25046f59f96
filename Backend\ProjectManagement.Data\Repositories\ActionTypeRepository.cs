using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// UI操作类型仓储实现
    /// </summary>
    public class UIActionTypeRepository : BaseRepository<UIActionType>, IUIActionTypeRepository
    {
        public UIActionTypeRepository(ISqlSugarClient db, ILogger<BaseRepository<UIActionType>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 分页查询UI操作类型
        /// </summary>
        public async Task<PagedResultDto<UIActionType>> GetPagedAsync(UIActionTypeQueryDto query)
        {
            var queryable = _db.Queryable<UIActionType>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                queryable = queryable.Where(x =>
                    x.Label.Contains(query.Keyword) ||
                    x.Value.Contains(query.Keyword) ||
                    (x.Description != null && x.Description.Contains(query.Keyword)));
            }

            // 状态过滤
            if (query.IsActive.HasValue)
            {
                queryable = queryable.Where(x => x.IsActive == query.IsActive.Value);
            }

            // 是否内置类型过滤
            if (query.IsBuiltIn.HasValue)
            {
                queryable = queryable.Where(x => x.IsBuiltIn == query.IsBuiltIn.Value);
            }

            // 排序
            queryable = queryable.OrderBy(x => x.SortOrder);

            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            return new PagedResultDto<UIActionType>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 根据值获取UI操作类型
        /// </summary>
        public async Task<UIActionType?> GetByValueAsync(string value)
        {
            return await _db.Queryable<UIActionType>()
                .Where(x => x.Value == value && !x.IsDeleted)
                .FirstAsync();
        }

        /// <summary>
        /// 获取启用的UI操作类型
        /// </summary>
        public async Task<List<UIActionType>> GetActiveAsync()
        {
            return await _db.Queryable<UIActionType>()
                .Where(x => !x.IsDeleted && x.IsActive)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 检查UI操作类型值是否已存在
        /// </summary>
        public async Task<bool> ExistsValueAsync(string value, int? excludeId = null)
        {
            var queryable = _db.Queryable<UIActionType>()
                .Where(x => x.Value == value && !x.IsDeleted);

            if (excludeId.HasValue)
            {
                queryable = queryable.Where(x => x.Id != excludeId.Value);
            }

            return await queryable.AnyAsync();
        }

        /// <summary>
        /// 获取最大排序顺序
        /// </summary>
        public async Task<int> GetMaxSortOrderAsync()
        {
            var queryable = _db.Queryable<UIActionType>()
                .Where(x => !x.IsDeleted);

            var hasData = await queryable.AnyAsync();
            if (!hasData)
            {
                return 0;
            }

            return await queryable.MaxAsync(x => x.SortOrder);
        }

        /// <summary>
        /// 批量更新排序顺序
        /// </summary>
        public async Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders)
        {
            try
            {
                foreach (var kvp in sortOrders)
                {
                    await _db.Updateable<UIActionType>()
                        .SetColumns(x => x.SortOrder == kvp.Value)
                        .SetColumns(x => x.UpdatedTime == DateTime.Now)
                        .Where(x => x.Id == kvp.Key)
                        .ExecuteCommandAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 启用/禁用UI操作类型
        /// </summary>
        public async Task<bool> SetActiveStatusAsync(int id, bool isActive)
        {
            return await _db.Updateable<UIActionType>()
                .SetColumns(x => x.IsActive == isActive)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 获取内置UI操作类型
        /// </summary>
        public async Task<List<UIActionType>> GetBuiltInAsync()
        {
            return await _db.Queryable<UIActionType>()
                .Where(x => !x.IsDeleted && x.IsBuiltIn)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取自定义UI操作类型
        /// </summary>
        public async Task<List<UIActionType>> GetCustomAsync()
        {
            return await _db.Queryable<UIActionType>()
                .Where(x => !x.IsDeleted && !x.IsBuiltIn)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }
    }
}
