# VSCode自动化系统

基于pyautogui的VSCode自动化操作系统，通过截图和图像识别实现VSCode的自动化控制。

## 功能特性

- 🖥️ **VSCode窗口管理**: 启动、关闭、聚焦VSCode窗口
- 📸 **截图识别**: 基于pyautogui和OpenCV的图像识别
- 🎯 **模板匹配**: 创建和管理UI元素模板
- 🖱️ **UI操作**: 点击、输入、滚动等自动化操作
- ⚙️ **配置管理**: 灵活的配置系统
- 🔄 **重试机制**: 自动重试失败的操作
- 📝 **日志记录**: 详细的操作日志

## 安装依赖

### 自动安装（推荐）

```bash
cd Python
python install.py
```

### 如果遇到安装问题

如果自动安装失败，请尝试以下方案：

#### 方案1：手动安装脚本
```bash
python install_manual.py
```

#### 方案2：逐个安装
```bash
python -m pip install numpy pillow psutil pyyaml colorama
python -m pip install pyautogui opencv-python pywin32
```

#### 方案3：使用国内镜像
```bash
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
```

#### 方案4：简化版运行
如果依赖安装困难，可以使用简化版：
```bash
python simple_main.py
```

详细安装指南请查看：[INSTALL_GUIDE.md](INSTALL_GUIDE.md)

## 快速开始

### 1. 基本使用

```bash
# 启动交互式模式
python main.py

# 启动VSCode并打开项目
python main.py --start "D:\Projects\MyProject"

# 截取屏幕截图
python main.py --screenshot

# 查看VSCode状态
python main.py --status

# 列出所有模板
python main.py --templates
```

### 2. 交互式命令

在交互式模式下，可以使用以下命令：

```
help                    - 显示帮助信息
status                  - 显示VSCode状态
start [项目路径]        - 启动VSCode
focus                   - 将VSCode窗口置于前台
close                   - 关闭VSCode
open <文件路径>         - 在VSCode中打开文件
new                     - 创建新文件
save                    - 保存当前文件
screenshot              - 截取屏幕截图
templates               - 列出所有模板
create_template         - 交互式创建模板
click <x> <y>           - 点击指定坐标
type <文本>             - 输入文本
mouse                   - 显示当前鼠标位置
config                  - 显示配置信息
quit/exit               - 退出程序
```

### 3. 编程接口

```python
from vscode_automation import VSCodeAutomation
from template_manager import TemplateManager
from ui_actions import UIActions

# 创建自动化实例
vscode = VSCodeAutomation()
template_manager = TemplateManager()
ui_actions = UIActions()

# 启动VSCode
vscode.start_vscode("D:\\Projects\\MyProject")

# 点击坐标
ui_actions.click(100, 200)

# 输入文本
ui_actions.type_text("Hello, World!")

# 使用模板点击
ui_actions.click_template("vscode_menu_file")

# 创建模板
template_manager.create_template("my_button", (100, 100, 50, 30), "custom", "我的按钮")
```

## 配置说明

配置文件 `config.yaml` 包含以下主要配置项：

### VSCode配置
- `executable_path`: VSCode可执行文件路径
- `project_path`: 默认项目路径
- `startup_wait_time`: 启动等待时间

### 自动化配置
- `confidence_threshold`: 图像匹配置信度阈值 (0.0-1.0)
- `timeout`: 操作超时时间（秒）
- `retry_count`: 重试次数
- `safe_mode`: 安全模式（启用失败保护）

### UI操作配置
- `click_duration`: 点击持续时间
- `type_interval`: 打字间隔
- `action_delay`: 操作间隔

## 模板管理

### 创建模板

1. **交互式创建**:
   ```bash
   python main.py
   > create_template
   ```

2. **编程方式创建**:
   ```python
   template_manager.create_template(
       name="button_name",
       region=(x, y, width, height),
       category="vscode",  # vscode, ui, custom
       description="按钮描述"
   )
   ```

### 模板分类

- `vscode`: VSCode相关的UI元素
- `ui`: 通用UI元素
- `custom`: 自定义模板

### 常用VSCode模板

建议创建以下VSCode模板：

- `vscode_menu_file`: 文件菜单
- `vscode_menu_edit`: 编辑菜单
- `vscode_menu_view`: 视图菜单
- `vscode_explorer_panel`: 资源管理器面板
- `vscode_terminal_panel`: 终端面板
- `vscode_search_box`: 搜索框
- `vscode_command_palette`: 命令面板
- `vscode_new_file_button`: 新建文件按钮
- `vscode_save_button`: 保存按钮
- `vscode_close_tab`: 关闭标签页按钮

## 目录结构

```
Python/
├── main.py                 # 主程序入口
├── config.py              # 配置管理
├── config.yaml            # 配置文件
├── vscode_automation.py   # VSCode自动化控制器
├── screenshot_manager.py  # 截图管理器
├── ui_actions.py          # UI操作封装
├── template_manager.py    # 模板管理器
├── requirements.txt       # 依赖包列表
├── README.md              # 说明文档
├── templates/             # 模板图像目录
│   ├── vscode/           # VSCode模板
│   ├── ui/               # UI模板
│   └── custom/           # 自定义模板
└── logs/                  # 日志目录
    └── screenshots/       # 截图目录
```

## 注意事项

1. **安全模式**: 默认启用PyAutoGUI的失败保护，将鼠标移动到屏幕左上角可中断操作
2. **屏幕分辨率**: 模板匹配对屏幕分辨率敏感，建议在固定分辨率下使用
3. **窗口状态**: 确保VSCode窗口可见且未被其他窗口遮挡
4. **权限要求**: 在某些系统上可能需要管理员权限

## 故障排除

### 常见问题

1. **找不到VSCode**:
   - 检查VSCode是否已安装
   - 确认`code`命令在PATH中可用
   - 或在配置文件中指定VSCode可执行文件路径

2. **模板匹配失败**:
   - 降低置信度阈值
   - 重新创建模板
   - 检查屏幕分辨率和缩放设置

3. **操作失败**:
   - 检查VSCode窗口是否在前台
   - 增加操作间隔时间
   - 启用重试机制

### 调试技巧

1. **截图调试**: 使用`screenshot`命令查看当前屏幕状态
2. **鼠标位置**: 使用`mouse`命令获取当前鼠标位置
3. **模板验证**: 使用`templates`命令检查模板状态
4. **日志查看**: 查看`logs/vscode_automation.log`文件

## 扩展开发

### 添加新功能

1. 在相应的类中添加新方法
2. 在CLI中添加对应的命令处理
3. 更新帮助信息和文档

### 自定义操作

```python
class CustomAutomation:
    def __init__(self):
        self.vscode = VSCodeAutomation()
        self.ui_actions = UIActions()

    def custom_operation(self):
        # 自定义操作逻辑
        self.vscode.focus_vscode_window()
        self.ui_actions.press_key(['ctrl', 'shift', 'p'])
        self.ui_actions.type_text("Python: Select Interpreter")
        self.ui_actions.press_key('enter')
```

## 许可证

本项目采用MIT许可证。
