import { ApiService } from './api'

// 类型定义
export interface PromptTemplate {
  id: number
  name: string
  description?: string
  categoryId: number
  categoryName: string
  content: string
  parameters?: string
  templateType: string
  taskType: string
  supportedProviders?: string
  templateVersion: string
  isDefault: boolean
  isEnabled: boolean
  usageCount: number
  averageRating?: number
  lastUsedTime?: string
  tags?: string
  createdTime: string
  updatedTime?: string
  createdBy?: number
  createdByName?: string
}

export interface PromptCategory {
  id: number
  name: string
  description?: string
  parentId?: number
  parentName?: string
  icon?: string
  color?: string
  sortOrder: number
  isEnabled: boolean
  templateCount: number
  children: PromptCategory[]
}

export interface CreatePromptTemplateRequest {
  name: string
  description?: string
  categoryId: number
  content: string
  parameters?: string
  templateType: string
  taskType: string
  supportedProviders?: string
  isDefault: boolean
  isEnabled: boolean
  tags?: string
}

export interface UpdatePromptTemplateRequest {
  name: string
  description?: string
  categoryId: number
  content: string
  parameters?: string
  supportedProviders?: string
  isEnabled: boolean
  tags?: string
}

export interface BuildPromptRequest {
  templateId: number
  parameters: Record<string, any>
  projectId?: number
}

export interface BuildPromptResponse {
  prompt: string
  templateId: number
  templateName: string
  usedParameters: Record<string, any>
  buildTime: string
}

export interface SearchTemplatesRequest {
  keyword?: string
  categoryId?: number
  taskType?: string
  templateType?: string
  page: number
  pageSize: number
}

export interface RateTemplateRequest {
  templateId: number
  overallRating: number
  accuracyRating?: number
  usefulnessRating?: number
  easeOfUseRating?: number
  feedback?: string
  suggestions?: string
  tags?: string
  wouldRecommend?: boolean
}

export interface TemplateStats {
  templateId: number
  totalUsage: number
  averageRating?: number
  totalRatings: number
  successRate: number
  averageResponseTime?: number
  totalCost?: number
  lastUsed?: string
  popularProviders: Array<{
    provider: string
    count: number
  }>
}

export interface PromptQualityAnalysis {
  length: number
  wordCount: number
  hasClearInstructions: boolean
  hasExamples: boolean
  hasConstraints: boolean
  hasOutputFormat: boolean
  complexityScore: number
  clarityScore: number
  completenessScore: number
  suggestions: string[]
}

export class PromptService {
  // 模板管理
  async getTemplates(): Promise<PromptTemplate[]> {
    return ApiService.get('/api/prompt/templates')
  }

  async getTemplate(id: number): Promise<PromptTemplate> {
    return ApiService.get(`/api/prompt/templates/${id}`)
  }

  async getTemplatesByCategory(categoryId: number): Promise<PromptTemplate[]> {
    return ApiService.get(`/api/prompt/templates/category/${categoryId}`)
  }

  async getTemplatesByTaskType(taskType: string): Promise<PromptTemplate[]> {
    return ApiService.get(`/api/prompt/templates/task-type/${taskType}`)
  }

  async getTemplateByTaskType(taskType: string, templateName?: string): Promise<PromptTemplate | null> {
    try {
      const templates = await this.getTemplatesByTaskType(taskType)

      if (templateName) {
        // 查找指定名称的模板
        const template = templates.find(t => t.name === templateName && t.isEnabled)
        return template || null
      } else {
        // 返回默认模板或第一个启用的模板
        const defaultTemplate = templates.find(t => t.isDefault && t.isEnabled)
        if (defaultTemplate) return defaultTemplate

        const firstEnabled = templates.find(t => t.isEnabled)
        return firstEnabled || null
      }
    } catch (error) {
      console.error('获取任务类型模板失败:', error)
      return null
    }
  }

  async searchTemplates(request: SearchTemplatesRequest): Promise<PromptTemplate[]> {
    return ApiService.post('/api/prompt/templates/search', request)
  }

  async getPopularTemplates(count: number = 10): Promise<PromptTemplate[]> {
    return ApiService.get(`/api/prompt/templates/popular?count=${count}`)
  }

  async getFavoriteTemplates(): Promise<PromptTemplate[]> {
    return ApiService.get('/api/prompt/templates/favorites')
  }

  async getRecentTemplates(count: number = 10): Promise<PromptTemplate[]> {
    return ApiService.get(`/api/prompt/templates/recent?count=${count}`)
  }

  async createTemplate(request: CreatePromptTemplateRequest): Promise<PromptTemplate> {
    return ApiService.post('/api/prompt/templates', request)
  }

  async updateTemplate(id: number, request: UpdatePromptTemplateRequest): Promise<PromptTemplate> {
    return ApiService.put(`/api/prompt/templates/${id}`, request)
  }

  async deleteTemplate(id: number): Promise<void> {
    return ApiService.delete(`/api/prompt/templates/${id}`)
  }

  async cloneTemplate(id: number, newName: string): Promise<PromptTemplate> {
    return ApiService.post(`/api/prompt/templates/${id}/clone`, newName)
  }

  // 分类管理
  async getCategories(): Promise<PromptCategory[]> {
    return ApiService.get('/api/promptcategory')
  }

  async getCategoryTree(): Promise<PromptCategory[]> {
    return ApiService.get('/api/promptcategory/tree')
  }

  async getRootCategories(): Promise<PromptCategory[]> {
    return ApiService.get('/api/promptcategory/root')
  }

  async getChildCategories(parentId: number): Promise<PromptCategory[]> {
    return ApiService.get(`/api/promptcategory/${parentId}/children`)
  }

  async getCategory(id: number): Promise<PromptCategory> {
    return ApiService.get(`/api/promptcategory/${id}`)
  }

  async getCategoryPath(id: number): Promise<PromptCategory[]> {
    return ApiService.get(`/api/promptcategory/${id}/path`)
  }

  // Prompt构建
  async buildPrompt(request: BuildPromptRequest): Promise<BuildPromptResponse> {
    return ApiService.post('/api/prompt/build', request)
  }

  async buildPromptByTaskType(
    taskType: string,
    parameters: Record<string, any>,
    projectId?: number
  ): Promise<BuildPromptResponse> {
    const url = `/api/prompt/build-by-task-type/${taskType}${projectId ? `?projectId=${projectId}` : ''}`
    return ApiService.post(url, parameters)
  }

  async validateParameters(id: number, parameters: Record<string, any>): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    return ApiService.post(`/api/prompt/templates/${id}/validate-parameters`, parameters)
  }

  async getTemplateParameters(id: number): Promise<Record<string, any> | null> {
    return ApiService.get(`/api/prompt/templates/${id}/parameters`)
  }

  async previewPrompt(id: number, parameters: Record<string, any>): Promise<string> {
    return ApiService.post(`/api/prompt/templates/${id}/preview`, parameters)
  }

  async getRecommendedTemplates(taskType: string, projectId?: number): Promise<PromptTemplate[]> {
    const url = `/api/prompt/recommendations/${taskType}${projectId ? `?projectId=${projectId}` : ''}`
    return ApiService.get(url)
  }

  async optimizePrompt(prompt: string, taskType: string, aiProvider: string): Promise<string> {
    return ApiService.post('/api/prompt/optimize', { prompt, taskType, aiProvider })
  }

  async analyzePromptQuality(prompt: string, taskType: string): Promise<PromptQualityAnalysis> {
    return ApiService.post('/api/prompt/analyze-quality', { prompt, taskType })
  }

  // 统计和评价
  async recordTemplateUsage(id: number, data?: {
    projectId?: number
    aiProvider?: string
    aiModel?: string
    inputParameters?: string
    generatedPrompt?: string
    aiResponse?: string
    responseTimeMs?: number
    tokenUsage?: number
    cost?: number
    isSuccess?: boolean
    errorMessage?: string
  }): Promise<void> {
    // 提供默认值，简化调用
    const usageData = {
      aiProvider: 'DeepSeek',
      aiModel: 'deepseek-chat',
      inputParameters: '{}',
      generatedPrompt: '',
      isSuccess: true,
      ...data
    }

    try {
      return await ApiService.post(`/api/prompt/templates/${id}/record-usage`, usageData)
    } catch (error) {
      // 记录使用失败不应该影响主要功能
      console.warn('记录模板使用失败:', error)
    }
  }

  async rateTemplate(id: number, request: RateTemplateRequest): Promise<void> {
    return ApiService.post(`/api/prompt/templates/${id}/rate`, request)
  }

  async getTemplateStats(id: number): Promise<TemplateStats> {
    return ApiService.get(`/api/prompt/templates/${id}/stats`)
  }

  // 用户偏好
  async setUserPreference(data: {
    templateId: number
    preferenceType: string
    customParameters?: string
    sortOrder: number
  }): Promise<void> {
    return ApiService.post('/api/prompt/preferences', data)
  }

  async removeUserPreference(templateId: number, preferenceType: string): Promise<void> {
    return ApiService.delete(`/api/prompt/preferences/${templateId}/${preferenceType}`)
  }

  async getUserPreferences(): Promise<any[]> {
    return ApiService.get('/api/prompt/preferences')
  }
}
