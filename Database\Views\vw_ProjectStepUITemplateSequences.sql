-- =============================================
-- 项目步骤UI序列模板视图
-- 视图名称: vw_ProjectStepUITemplateSequences
-- 功能: 显示项目的每个步骤都使用了哪些UI序列模板
-- 创建日期: 2024-12-25
-- 版本: 1.0
-- =============================================

USE ProjectManagementAI;
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepUITemplateSequences')
BEGIN
    DROP VIEW vw_ProjectStepUITemplateSequences;
    PRINT '已删除现有视图 vw_ProjectStepUITemplateSequences';
END
GO

-- 创建视图
CREATE VIEW vw_ProjectStepUITemplateSequences
AS
SELECT
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,

    -- 步骤信息
    ds.Id AS StepId,
    ds.StepName,
    ds.StepDescription,
    ds.StepType,
    ds.Status AS StepStatus,
    ds.Priority AS StepPriority,
    ds.Progress AS StepProgress,
    ds.StepOrder,
    ds.StepLevel,
    ds.TechnologyStack AS StepTechnologyStack,
    ds.ComponentType,
    ds.FilePath,
    ds.EstimatedHours,
    ds.ActualHours,

    -- 模板序列信息
    uts.Id AS SequenceId,
    uts.Name AS SequenceName,
    uts.Description AS SequenceDescription,
    uts.Category AS SequenceCategory,
    uts.UsageCount AS SequenceUsageCount,
    uts.LastUsedTime AS SequenceLastUsedTime,
    uts.IsActive AS SequenceIsActive,

    -- 关联信息
    stsa.Id AS AssociationId,
    stsa.AppliedTime,
    stsa.Status AS AssociationStatus,
    stsa.Progress AS AssociationProgress,
    stsa.ExecutionStartTime,
    stsa.ExecutionEndTime,
    stsa.ExecutionResult,
    stsa.ErrorMessage,
    stsa.Notes AS AssociationNotes,

    -- 应用者信息
    appliedUser.Username AS AppliedByUsername,
    appliedUser.RealName AS AppliedByRealName,

    -- 项目负责人信息
    owner.Username AS ProjectOwnerUsername,
    owner.RealName AS ProjectOwnerRealName,

    -- 步骤创建者信息
    stepCreator.Username AS StepCreatedByUsername,
    stepCreator.RealName AS StepCreatedByRealName,

    -- 序列步骤统计
    (
        SELECT COUNT(*)
        FROM UIAutoMationTemplateSteps steps
        WHERE steps.SequenceId = uts.Id
        AND steps.IsDeleted = 0
        AND steps.IsActive = 1
    ) AS SequenceStepCount,

    -- 序列步骤详情（简化）
    (
        SELECT COUNT(*)
        FROM UIAutoMationTemplateSteps steps
        WHERE steps.SequenceId = uts.Id
        AND steps.IsDeleted = 0
        AND steps.IsActive = 1
    ) AS ActiveStepCount,

    -- 执行时长（分钟）
    CASE
        WHEN stsa.ExecutionStartTime IS NOT NULL AND stsa.ExecutionEndTime IS NOT NULL
        THEN DATEDIFF(MINUTE, stsa.ExecutionStartTime, stsa.ExecutionEndTime)
        ELSE NULL
    END AS ExecutionDurationMinutes,

    -- 是否正在执行
    CASE
        WHEN stsa.Status = 'Running' THEN 1
        ELSE 0
    END AS IsExecuting,

    -- 是否执行成功
    CASE
        WHEN stsa.Status = 'Completed' AND stsa.ExecutionResult IS NOT NULL THEN 1
        ELSE 0
    END AS IsExecutionSuccessful,

    -- 创建时间
    ds.CreatedTime AS StepCreatedTime,
    uts.CreatedTime AS SequenceCreatedTime,
    stsa.CreatedTime AS AssociationCreatedTime

FROM Projects p
    INNER JOIN DevelopmentSteps ds ON p.Id = ds.ProjectId
    INNER JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId
    INNER JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id
    LEFT JOIN Users owner ON p.OwnerId = owner.Id
    LEFT JOIN Users appliedUser ON stsa.AppliedByUserId = appliedUser.Id
    LEFT JOIN Users stepCreator ON ds.CreatedBy = stepCreator.Id

WHERE
    p.IsDeleted = 0
    AND ds.IsDeleted = 0
    AND stsa.IsDeleted = 0
    AND uts.IsDeleted = 0
    AND stsa.IsActive = 1;

GO

-- 添加视图注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'项目步骤UI序列模板视图 - 显示项目的每个步骤都使用了哪些UI序列模板，包含详细的关联信息、执行状态、步骤详情等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'vw_ProjectStepUITemplateSequences';

GO

-- 创建索引视图（可选，用于性能优化）
-- 注意：索引视图需要满足特定条件，这里提供示例但可能需要根据实际情况调整

-- 验证视图创建
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ProjectStepUITemplateSequences')
BEGIN
    PRINT '✅ 视图 vw_ProjectStepUITemplateSequences 创建成功！';

    -- 显示视图结构
    SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'vw_ProjectStepUITemplateSequences'
    ORDER BY ORDINAL_POSITION;

    PRINT '';
    PRINT '📊 视图功能说明：';
    PRINT '1. 显示所有项目的开发步骤及其关联的UI自动化模板序列';
    PRINT '2. 包含项目、步骤、模板序列的详细信息';
    PRINT '3. 显示关联状态、执行进度、执行结果等';
    PRINT '4. 提供序列步骤的JSON格式详情';
    PRINT '5. 计算执行时长和成功状态';
    PRINT '';
    PRINT '🔍 常用查询示例：';
    PRINT '-- 查看特定项目的所有步骤模板关联';
    PRINT 'SELECT * FROM vw_ProjectStepUITemplateSequences WHERE ProjectId = 1;';
    PRINT '';
    PRINT '-- 查看正在执行的模板序列';
    PRINT 'SELECT * FROM vw_ProjectStepUITemplateSequences WHERE IsExecuting = 1;';
    PRINT '';
    PRINT '-- 查看执行成功的关联';
    PRINT 'SELECT * FROM vw_ProjectStepUITemplateSequences WHERE IsExecutionSuccessful = 1;';
    PRINT '';
    PRINT '-- 按项目统计模板序列使用情况';
    PRINT 'SELECT ProjectName, COUNT(*) as TemplateUsageCount FROM vw_ProjectStepUITemplateSequences GROUP BY ProjectId, ProjectName;';

END
ELSE
BEGIN
    PRINT '❌ 视图创建失败！';
END

GO

PRINT '';
PRINT '脚本执行完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
