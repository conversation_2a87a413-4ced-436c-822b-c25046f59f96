namespace ProjectManagement.Core.Exceptions;

/// <summary>
/// 业务异常基类
/// 用于表示业务逻辑相关的异常
/// </summary>
public class BusinessException : Exception
{
    /// <summary>
    /// 错误详细信息
    /// </summary>
    public object? Details { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; }

    public BusinessException() : base()
    {
    }

    public BusinessException(string message) : base(message)
    {
    }

    public BusinessException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public BusinessException(string message, string errorCode) : base(message)
    {
        ErrorCode = errorCode;
    }

    public BusinessException(string message, object details) : base(message)
    {
        Details = details;
    }

    public BusinessException(string message, string errorCode, object details) : base(message)
    {
        ErrorCode = errorCode;
        Details = details;
    }
}

/// <summary>
/// 验证异常
/// 用于表示数据验证失败的异常
/// </summary>
public class ValidationException : BusinessException
{
    /// <summary>
    /// 验证错误集合
    /// </summary>
    public Dictionary<string, string[]>? Errors { get; }

    public ValidationException() : base("验证失败")
    {
    }

    public ValidationException(string message) : base(message)
    {
    }

    public ValidationException(Dictionary<string, string[]> errors) : base("验证失败")
    {
        Errors = errors;
    }

    public ValidationException(string field, string error) : base("验证失败")
    {
        Errors = new Dictionary<string, string[]>
        {
            { field, new[] { error } }
        };
    }
}

/// <summary>
/// 资源未找到异常
/// </summary>
public class NotFoundException : BusinessException
{
    public NotFoundException() : base("资源未找到")
    {
    }

    public NotFoundException(string message) : base(message)
    {
    }

    public NotFoundException(string resourceType, object id) : base($"{resourceType} (ID: {id}) 未找到")
    {
    }
}

/// <summary>
/// 权限不足异常
/// </summary>
public class ForbiddenException : BusinessException
{
    public ForbiddenException() : base("权限不足")
    {
    }

    public ForbiddenException(string message) : base(message)
    {
    }

    public ForbiddenException(string action, string resource) : base($"无权限执行操作 '{action}' 在资源 '{resource}' 上")
    {
    }
}

/// <summary>
/// 冲突异常
/// 用于表示资源冲突的异常
/// </summary>
public class ConflictException : BusinessException
{
    public ConflictException() : base("资源冲突")
    {
    }

    public ConflictException(string message) : base(message)
    {
    }

    public ConflictException(string resource, string reason) : base($"资源 '{resource}' 冲突: {reason}")
    {
    }
}

/// <summary>
/// AI服务异常
/// </summary>
public class AIServiceException : BusinessException
{
    /// <summary>
    /// AI提供商
    /// </summary>
    public string? Provider { get; }

    /// <summary>
    /// AI模型
    /// </summary>
    public string? Model { get; }

    public AIServiceException() : base("AI服务异常")
    {
    }

    public AIServiceException(string message) : base(message)
    {
    }

    public AIServiceException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public AIServiceException(string message, string provider, string model) : base(message)
    {
        Provider = provider;
        Model = model;
    }
}

/// <summary>
/// 外部服务异常
/// </summary>
public class ExternalServiceException : BusinessException
{
    /// <summary>
    /// 服务名称
    /// </summary>
    public string? ServiceName { get; }

    /// <summary>
    /// HTTP状态码
    /// </summary>
    public int? StatusCode { get; }

    public ExternalServiceException() : base("外部服务异常")
    {
    }

    public ExternalServiceException(string message) : base(message)
    {
    }

    public ExternalServiceException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ExternalServiceException(string serviceName, string message) : base(message)
    {
        ServiceName = serviceName;
    }

    public ExternalServiceException(string serviceName, int statusCode, string message) : base(message)
    {
        ServiceName = serviceName;
        StatusCode = statusCode;
    }
}
