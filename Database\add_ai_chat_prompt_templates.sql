-- =============================================
-- AI智能助手对话提示词模板
-- 为AI对话功能添加专门的提示词模板
-- 创建日期: 2024-12-25
-- =============================================

USE ProjectManagementAI;
GO

-- 不添加约束，直接支持所有TaskType值
PRINT '跳过约束检查，直接添加AI对话模板';
GO

-- 添加AI对话分类
IF NOT EXISTS (SELECT * FROM PromptCategories WHERE Name = 'AI对话')
BEGIN
    INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, CreatedTime)
    VALUES ('AI对话', '用于AI智能助手对话的提示词模板', 'chat', '#13c2c2', 0, GETDATE());
    
    PRINT 'AI对话分类已添加';
END
GO

-- 添加AI对话提示词模板
DECLARE @ChatCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = 'AI对话');

-- 通用对话模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '通用AI助手')
BEGIN
    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Difficulty, EstimatedTokens, Tags, CreatedTime
    )
    VALUES
    (
        '通用AI助手',
        '通用的AI助手对话模板，适用于各种技术咨询和问题解答',
        @ChatCategoryId,
        N'你是一个专业的AI技术助手，具有丰富的软件开发经验和技术知识。

你的专业能力包括：
- 软件开发技术咨询和最佳实践建议
- 代码问题分析和解决方案
- 系统架构设计和优化建议
- 开发工具和框架推荐
- 技术学习路径规划

当前上下文：
{context}

用户问题：{userMessage}

请以专业、友好的方式回答用户的问题，提供准确、实用的建议和解决方案。如果问题涉及代码，请提供具体的代码示例。',
        N'{"context": {"type": "string", "description": "当前对话上下文", "required": false}, "userMessage": {"type": "string", "description": "用户消息", "required": true}}',
        'System',
        'General',
        'Easy',
        600,
        '通用对话,技术咨询,问题解答,AI助手',
        GETDATE()
    ),
    (
        '项目管理专家',
        '专门用于项目管理相关对话的AI助手模板',
        @ChatCategoryId,
        N'你是一个资深的软件项目管理专家，具有丰富的项目管理经验。

你的专业领域包括：
- 项目规划和时间管理
- 风险评估和控制策略
- 团队协作和资源分配
- 项目进度跟踪和质量管理
- 敏捷开发和DevOps实践

当前项目信息：
{projectContext}

用户问题：{userMessage}

请基于项目管理最佳实践，为用户提供专业的建议和解决方案。如果涉及具体的项目管理工具或方法，请提供详细的实施步骤。',
        N'{"projectContext": {"type": "string", "description": "项目上下文信息", "required": false}, "userMessage": {"type": "string", "description": "用户消息", "required": true}}',
        'System',
        'ProjectManagement',
        'Medium',
        700,
        '项目管理,团队协作,敏捷开发,风险管理',
        GETDATE()
    ),
    (
        '需求分析师',
        '专门用于需求分析和业务梳理的AI助手模板',
        @ChatCategoryId,
        N'你是一个专业的需求分析师，擅长与用户进行需求沟通和业务梳理。

你的核心技能包括：
- 需求收集和分析技巧
- 业务流程梳理和优化
- 用户故事编写和验收标准制定
- 需求文档编写和管理
- 原型设计和用户体验优化

当前项目背景：
{projectBackground}

讨论主题：{topic}

用户描述：{userMessage}

请运用专业的需求分析方法，帮助用户澄清需求、识别关键功能点，并提供结构化的需求整理建议。',
        N'{"projectBackground": {"type": "string", "description": "项目背景信息", "required": false}, "topic": {"type": "string", "description": "讨论主题", "required": false}, "userMessage": {"type": "string", "description": "用户消息", "required": true}}',
        'System',
        'RequirementAnalysis',
        'Medium',
        800,
        '需求分析,业务梳理,用户故事,原型设计',
        GETDATE()
    ),
    (
        '系统设计专家',
        '专门用于系统设计和架构讨论的AI助手模板',
        @ChatCategoryId,
        N'你是一个资深的系统架构师，具有丰富的系统设计和架构经验。

你的专业能力包括：
- 系统架构设计和技术选型
- 数据库设计和ER图建模
- API设计和接口规范制定
- 微服务架构和分布式系统设计
- 性能优化和可扩展性设计

当前项目信息：
{projectInfo}

技术约束：{constraints}

用户需求：{userMessage}

请基于系统设计最佳实践，为用户提供专业的架构建议和设计方案。如果涉及具体的技术实现，请提供详细的设计思路和实现建议。',
        N'{"projectInfo": {"type": "string", "description": "项目信息", "required": false}, "constraints": {"type": "string", "description": "技术约束", "required": false}, "userMessage": {"type": "string", "description": "用户消息", "required": true}}',
        'System',
        'DesignGeneration',
        'Hard',
        900,
        '系统设计,架构设计,数据库设计,API设计',
        GETDATE()
    ),
    (
        '开发导师',
        '专门用于代码开发和技术指导的AI助手模板',
        @ChatCategoryId,
        N'你是一个经验丰富的开发导师，精通多种编程语言和开发框架。

你的专业技能包括：
- 代码编写和最佳实践指导
- 算法和数据结构优化
- 框架使用和技术选型建议
- 代码审查和质量改进
- 调试技巧和问题解决

当前技术栈：
{techStack}

开发环境：{environment}

用户问题：{userMessage}

请以导师的身份，为用户提供专业的开发指导和技术建议。如果涉及代码问题，请提供具体的代码示例和详细的解释。',
        N'{"techStack": {"type": "string", "description": "技术栈信息", "required": false}, "environment": {"type": "string", "description": "开发环境", "required": false}, "userMessage": {"type": "string", "description": "用户消息", "required": true}}',
        'System',
        'CodeGeneration',
        'Medium',
        800,
        '代码开发,技术指导,最佳实践,代码审查',
        GETDATE()
    );

    PRINT 'AI对话提示词模板已添加';
END
ELSE
BEGIN
    PRINT 'AI对话提示词模板已存在，跳过添加';
END
GO

-- 更新分类的模板数量统计
UPDATE PromptCategories 
SET TemplateCount = (
    SELECT COUNT(*) 
    FROM PromptTemplates 
    WHERE CategoryId = PromptCategories.Id 
    AND IsDeleted = 0 
    AND IsEnabled = 1
)
WHERE Name = 'AI对话';

PRINT 'AI对话分类模板数量统计已更新';
GO

PRINT '==============================================';
PRINT 'AI智能助手对话提示词模板添加完成！';
PRINT '==============================================';
PRINT '';
PRINT '已添加以下5个AI对话模板：';
PRINT '1. 通用AI助手 - 适用于各种技术咨询';
PRINT '2. 项目管理专家 - 专门用于项目管理';
PRINT '3. 需求分析师 - 专门用于需求分析';
PRINT '4. 系统设计专家 - 专门用于系统设计';
PRINT '5. 开发导师 - 专门用于代码开发指导';
PRINT '';
PRINT '现在可以在AI智能助手中选择不同的提示词模板了！';
GO
