using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 自定义模板仓储接口
    /// </summary>
    public interface ICustomTemplateRepository : IRepository<CustomUIAutoMationTemplate>
    {
        /// <summary>
        /// 分页查询模板
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResultDto<CustomUIAutoMationTemplate>> GetPagedAsync(CustomTemplateQueryDto query);

        /// <summary>
        /// 根据分类获取模板
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>模板列表</returns>
        Task<List<CustomUIAutoMationTemplate>> GetByCategoryAsync(string category);

        /// <summary>
        /// 根据标签获取模板
        /// </summary>
        /// <param name="tags">标签列表</param>
        /// <returns>模板列表</returns>
        Task<List<CustomUIAutoMationTemplate>> GetByTagsAsync(List<string> tags);

        /// <summary>
        /// 搜索模板
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <returns>模板列表</returns>
        Task<List<CustomUIAutoMationTemplate>> SearchAsync(string keyword);

        /// <summary>
        /// 获取所有分类
        /// </summary>
        /// <returns>分类列表</returns>
        Task<List<string>> GetCategoriesAsync();

        /// <summary>
        /// 获取所有标签
        /// </summary>
        /// <returns>标签列表</returns>
        Task<List<string>> GetTagsAsync();

        /// <summary>
        /// 增加使用次数
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>是否成功</returns>
        Task<bool> IncrementUsageCountAsync(int id);

        /// <summary>
        /// 更新最后使用时间
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <param name="lastUsedTime">最后使用时间</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateLastUsedTimeAsync(int id, DateTime lastUsedTime);

        /// <summary>
        /// 获取最常用的模板
        /// </summary>
        /// <param name="count">数量</param>
        /// <returns>模板列表</returns>
        Task<List<CustomUIAutoMationTemplate>> GetMostUsedAsync(int count = 10);

        /// <summary>
        /// 检查模板名称是否存在
        /// </summary>
        /// <param name="name">模板名称</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsNameAsync(string name, int? excludeId = null);

        /// <summary>
        /// 批量删除模板
        /// </summary>
        /// <param name="ids">模板ID列表</param>
        /// <returns>是否成功</returns>
        Task<bool> BatchDeleteAsync(List<int> ids);

        /// <summary>
        /// 获取模板统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<TemplateStatisticsDto> GetStatisticsAsync();
    }
}
