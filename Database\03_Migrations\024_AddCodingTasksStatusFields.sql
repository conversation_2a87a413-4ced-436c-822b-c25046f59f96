-- =============================================
-- 为CodingTaskSteps表添加编码完成和错误修复状态字段
-- 文件: 024_AddCodingTaskStepsStatusFields.sql
-- 目的: 添加IsFinishCoding和IsFixError字段到CodingTaskSteps表
-- 作者: System
-- 创建时间: 2025-01-09
-- =============================================

USE [ProjectManagementAI]
GO

-- 设置错误处理
SET NOCOUNT ON;
SET XACT_ABORT ON;

PRINT '========================================';
PRINT '开始为CodingTaskSteps表添加状态字段...';
PRINT '========================================';

-- 检查CodingTaskSteps表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CodingTaskSteps]') AND type in (N'U'))
BEGIN
    PRINT '正在为CodingTaskSteps表添加新字段...';

    -- 添加IsFinishCoding字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IsFinishCoding')
    BEGIN
        ALTER TABLE [dbo].[CodingTaskSteps] ADD [IsFinishCoding] BIT NOT NULL DEFAULT 0;
        PRINT '✓ 添加字段IsFinishCoding (BIT, 默认值: 0)';

        -- 添加字段注释
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'是否完成编码 - 标识该步骤的编码工作是否已完成',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'CodingTaskSteps',
            @level2type = N'COLUMN', @level2name = N'IsFinishCoding';
        PRINT '✓ 添加IsFinishCoding字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠️ IsFinishCoding字段已存在，跳过添加';
    END

    -- 添加IsFixError字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IsFixError')
    BEGIN
        ALTER TABLE [dbo].[CodingTaskSteps] ADD [IsFixError] BIT NOT NULL DEFAULT 0;
        PRINT '✓ 添加字段IsFixError (BIT, 默认值: 0)';

        -- 添加字段注释
        EXEC sys.sp_addextendedproperty
            @name = N'MS_Description',
            @value = N'是否修复错误 - 标识该步骤是否处于错误修复状态',
            @level0type = N'SCHEMA', @level0name = N'dbo',
            @level1type = N'TABLE', @level1name = N'CodingTaskSteps',
            @level2type = N'COLUMN', @level2name = N'IsFixError';
        PRINT '✓ 添加IsFixError字段注释';
    END
    ELSE
    BEGIN
        PRINT '⚠️ IsFixError字段已存在，跳过添加';
    END

    -- 创建索引以提高查询性能
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IX_CodingTaskSteps_IsFinishCoding')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_IsFinishCoding] ON [dbo].[CodingTaskSteps] ([IsFinishCoding]);
        PRINT '✓ 创建IsFinishCoding字段索引';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IX_CodingTaskSteps_IsFixError')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_IsFixError] ON [dbo].[CodingTaskSteps] ([IsFixError]);
        PRINT '✓ 创建IsFixError字段索引';
    END

    -- 创建组合索引用于常见查询场景
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('CodingTaskSteps') AND name = 'IX_CodingTaskSteps_Status_Flags')
    BEGIN
        CREATE NONCLUSTERED INDEX [IX_CodingTaskSteps_Status_Flags] ON [dbo].[CodingTaskSteps]
        ([Status], [IsFinishCoding], [IsFixError])
        INCLUDE ([CodingTaskId], [DevelopmentStepId], [OrderIndex]);
        PRINT '✓ 创建状态和标志位组合索引';
    END

    PRINT '✅ CodingTaskSteps表字段添加完成';
END
ELSE
BEGIN
    PRINT '❌ CodingTaskSteps表不存在，无法添加字段';
END

PRINT '========================================';
PRINT '✅ CodingTaskSteps表状态字段添加完成！';
PRINT '已添加字段：';
PRINT '1. IsFinishCoding (BIT) - 是否完成编码';
PRINT '2. IsFixError (BIT) - 是否修复错误';
PRINT '========================================';
