import { ApiService } from './api'

export interface Issue {
  id: number
  projectId: number
  projectName?: string
  title: string
  description: string
  issueType: string
  priority: string
  status: string
  assignedTo?: number
  assignedToUser?: {
    id: number
    username: string
    realName: string
  }
  reportedBy: number
  reportedByUser?: {
    id: number
    username: string
    realName: string
  }
  labels?: string[]
  createdAt: string
  updatedAt: string
  resolvedAt?: string
}

export interface IssueStatistics {
  totalCount: number
  openCount: number
  inProgressCount: number
  resolvedCount: number
  closedCount: number
  bugCount: number
  featureCount: number
  enhancementCount: number
  taskCount: number
  highPriorityCount: number
  criticalPriorityCount: number
}

export interface CreateIssueRequest {
  projectId: number
  title: string
  description: string
  issueType: string
  priority?: string
  assignedTo?: number
  labels?: string
}

export interface UpdateIssueRequest {
  title?: string
  description?: string
  issueType?: string
  priority?: string
  status?: string
  assignedTo?: number
  labels?: string
}

export interface IssueSearchParams {
  keyword?: string
  projectId?: number
  status?: string
  priority?: string
  issueType?: string
  assignedTo?: number
  pageIndex?: number
  pageSize?: number
}

export interface PagedIssueResult {
  items: Issue[]
  totalCount: number
  pageIndex: number
  pageSize: number
  totalPages: number
}

export class IssueService {
  /**
   * 获取问题列表（支持搜索和筛选）
   */
  static async getIssues(params: IssueSearchParams = {}): Promise<PagedIssueResult> {
    const queryParams = new URLSearchParams()
    
    if (params.keyword) queryParams.append('keyword', params.keyword)
    if (params.projectId) queryParams.append('projectId', params.projectId.toString())
    if (params.status) queryParams.append('status', params.status)
    if (params.priority) queryParams.append('priority', params.priority)
    if (params.issueType) queryParams.append('issueType', params.issueType)
    if (params.assignedTo) queryParams.append('assignedTo', params.assignedTo.toString())
    if (params.pageIndex) queryParams.append('pageIndex', params.pageIndex.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())

    const response = await ApiService.get<{ data: PagedIssueResult }>(`/api/issues?${queryParams}`)
    return response.data
  }

  /**
   * 根据ID获取问题详情
   */
  static async getIssue(id: number): Promise<Issue> {
    const response = await ApiService.get<{ data: Issue }>(`/api/issues/${id}`)
    return response.data
  }

  /**
   * 创建新问题
   */
  static async createIssue(request: CreateIssueRequest): Promise<Issue> {
    const response = await ApiService.post<{ data: Issue }>('/api/issues', request)
    return response.data
  }

  /**
   * 更新问题
   */
  static async updateIssue(id: number, request: UpdateIssueRequest): Promise<Issue> {
    const response = await ApiService.put<{ data: Issue }>(`/api/issues/${id}`, request)
    return response.data
  }

  /**
   * 删除问题
   */
  static async deleteIssue(id: number): Promise<void> {
    await ApiService.delete(`/api/issues/${id}`)
  }

  /**
   * 获取问题统计信息
   */
  static async getIssueStatistics(projectId?: number): Promise<IssueStatistics> {
    const queryParams = projectId ? `?projectId=${projectId}` : ''
    const response = await ApiService.get<{ data: IssueStatistics }>(`/api/issues/statistics${queryParams}`)
    return response.data
  }

  /**
   * 更新问题状态
   */
  static async updateIssueStatus(id: number, status: string): Promise<void> {
    await ApiService.patch(`/api/issues/${id}/status`, { status })
  }

  /**
   * 分配问题给用户
   */
  static async assignIssue(id: number, assignedTo?: number): Promise<void> {
    await ApiService.patch(`/api/issues/${id}/assign`, { assignedTo })
  }

  /**
   * 根据项目ID获取问题列表
   */
  static async getIssuesByProject(projectId: number): Promise<Issue[]> {
    const response = await ApiService.get<{ data: Issue[] }>(`/api/issues/project/${projectId}`)
    return response.data
  }

  /**
   * 获取分配给当前用户的问题
   */
  static async getAssignedIssues(): Promise<Issue[]> {
    const response = await ApiService.get<{ data: Issue[] }>('/api/issues/assigned-to-me')
    return response.data
  }

  /**
   * 获取当前用户报告的问题
   */
  static async getReportedIssues(): Promise<Issue[]> {
    const response = await ApiService.get<{ data: Issue[] }>('/api/issues/reported-by-me')
    return response.data
  }
}

// 问题类型选项
export const ISSUE_TYPES = [
  { value: 'Bug', label: '缺陷', color: '#f56565' },
  { value: 'Feature', label: '功能请求', color: '#48bb78' },
  { value: 'Enhancement', label: '改进', color: '#4299e1' },
  { value: 'Task', label: '任务', color: '#9f7aea' }
]

// 优先级选项
export const PRIORITIES = [
  { value: 'Low', label: '低', color: '#68d391' },
  { value: 'Medium', label: '中', color: '#fbd38d' },
  { value: 'High', label: '高', color: '#fc8181' },
  { value: 'Critical', label: '紧急', color: '#e53e3e' }
]

// 状态选项
export const STATUSES = [
  { value: 'Open', label: '开放', color: '#4299e1' },
  { value: 'InProgress', label: '处理中', color: '#fbd38d' },
  { value: 'Resolved', label: '已解决', color: '#68d391' },
  { value: 'Closed', label: '已关闭', color: '#a0aec0' }
]

// 获取问题类型的显示信息
export function getIssueTypeInfo(type: string) {
  return ISSUE_TYPES.find(t => t.value === type) || { value: type, label: type, color: '#a0aec0' }
}

// 获取优先级的显示信息
export function getPriorityInfo(priority: string) {
  return PRIORITIES.find(p => p.value === priority) || { value: priority, label: priority, color: '#a0aec0' }
}

// 获取状态的显示信息
export function getStatusInfo(status: string) {
  return STATUSES.find(s => s.value === status) || { value: status, label: status, color: '#a0aec0' }
}
