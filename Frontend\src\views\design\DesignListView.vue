<template>
  <div class="design-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><PictureRounded /></el-icon>
          设计生成
        </h1>
        <p class="page-description">
          基于AI技术自动生成ER图和系统上下文图，支持多种格式导出和在线编辑
        </p>
      </div>
      <div class="header-actions">
        <!-- 如果是从AI聊天跳转过来，显示返回按钮 -->
        <el-button
          v-if="fromAIChat"
          @click="returnToAIChat"
          type="info"
        >
          <el-icon><Back /></el-icon>
          返回AI聊天
        </el-button>

        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建设计
        </el-button>

        <!-- AI聊天快捷入口 -->
        <el-button @click="openAIChat" type="success">
          <el-icon><ChatDotRound /></el-icon>
          AI设计助手
        </el-button>
      </div>
    </div>

    <!-- 项目选择器 -->
    <div class="project-selector">
      <el-card>
        <div class="selector-content">
          <div class="selector-label">
            <el-icon><Folder /></el-icon>
            选择项目：
          </div>
          <el-select
            v-model="selectedProjectId"
            placeholder="请选择项目"
            style="width: 300px"
            @change="onProjectChange"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            >
              <div class="project-option">
                <span class="project-name">{{ project.name }}</span>
                <span class="project-status" :class="project.status?.toLowerCase() || 'unknown'">
                  {{ project.status || '未知状态' }}
                </span>
              </div>
            </el-option>
          </el-select>
        </div>
      </el-card>
    </div>

    <!-- AI提供商选择器 -->
    <div class="ai-selector" v-if="selectedProjectId">
      <el-card>
        <div class="selector-content">
          <div class="selector-label">
            <el-icon><Platform /></el-icon>
            选择AI提供商：
          </div>
          <div v-if="aiProviders.length === 0 && !loadingAIProviders" class="no-providers-hint">
            <el-alert
              title="暂无可用的AI供应商配置"
              type="warning"
              :closable="false"
              show-icon
              size="small"
            >
              <template #default>
                <p style="margin: 4px 0;">您还没有配置任何AI供应商。请先配置AI供应商后再进行设计生成。</p>
                <el-button type="primary" size="small" @click="navigateToAIConfig">
                  前往配置AI供应商
                </el-button>
              </template>
            </el-alert>
          </div>
          <el-select
            v-else
            v-model="selectedAIProvider"
            placeholder="请选择AI提供商"
            style="width: 300px"
            :loading="loadingAIProviders"
            @change="onAIProviderChange"
          >
            <el-option
              v-for="provider in aiProviders"
              :key="provider.id"
              :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
              :value="provider.id"
              :disabled="!provider.isActive"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ provider.modelName }}</span>
                <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
                <el-tag v-else-if="provider.isDefault" type="success" size="small">默认</el-tag>
              </div>
            </el-option>
          </el-select>
          <div class="ai-info" v-if="selectedAIProvider">
            <el-tooltip content="当前选择的AI提供商将用于生成ER图和上下文图" placement="top">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
            <span class="ai-description">
              使用选定的AI提供商生成设计图
            </span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 设计类型选择 -->
    <div class="design-types" v-if="selectedProjectId">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-card class="design-type-card" @click="activeTab = 'er'">
            <div class="design-type-content" :class="{ active: activeTab === 'er' }">
              <div class="design-type-icon">
                <el-icon size="48"><DataBoard /></el-icon>
              </div>
              <h3>ER图设计</h3>
              <p>数据库实体关系图，展示表结构和关联关系</p>
              <div class="design-count">
                <span>{{ erDiagrams.length }} 个设计</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="design-type-card" @click="activeTab = 'context'">
            <div class="design-type-content" :class="{ active: activeTab === 'context' }">
              <div class="design-type-icon">
                <el-icon size="48"><Connection /></el-icon>
              </div>
              <h3>上下文图设计</h3>
              <p>系统上下文图，展示系统边界和外部实体</p>
              <div class="design-count">
                <span>{{ contextDiagrams.length }} 个设计</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="design-type-card" @click="activeTab = 'prototype'">
            <div class="design-type-content" :class="{ active: activeTab === 'prototype' }">
              <div class="design-type-icon">
                <el-icon size="48"><Monitor /></el-icon>
              </div>
              <h3>原型图设计</h3>
              <p>UI原型图，包括线框图、用户流程图等</p>
              <div class="design-count">
                <span>{{ prototypes.length }} 个设计</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 设计列表 -->
    <div class="design-content" v-if="selectedProjectId">
      <el-tabs v-model="activeTab" @tab-change="onTabChange" class="design-tabs">
        <!-- ER图列表 -->
        <el-tab-pane label="ER图" name="er">
          <div class="design-list-header">
            <div class="list-actions">
              <el-button type="primary" @click="generateERDiagram">
                <el-icon><MagicStick /></el-icon>
                AI生成ER图
              </el-button>
              <el-button @click="createERDiagram">
                <el-icon><Plus /></el-icon>
                手动创建
              </el-button>
              <el-button
                type="success"
                @click="showBatchSQLDialog"
                :disabled="erDiagrams.length === 0"
              >
                <el-icon><DocumentAdd /></el-icon>
                生成SQL
              </el-button>
            </div>
            <div class="list-filters">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索ER图..."
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <div class="design-grid">
            <div
              v-for="diagram in filteredERDiagrams"
              :key="diagram.id"
              class="design-item"
              @click="viewERDiagram(diagram.id)"
            >
              <div class="design-preview">
                <el-icon size="64"><DataBoard /></el-icon>
              </div>
              <div class="design-info">
                <h4 class="design-title">{{ diagram.diagramName }}</h4>
                <p class="design-description">{{ diagram.description || '暂无描述' }}</p>
                <div class="design-meta">
                  <span class="version">v{{ diagram.version }}</span>
                  <span class="date">{{ formatDate(diagram.updatedAt) }}</span>
                </div>
              </div>
              <div class="design-actions" @click.stop>
                <el-dropdown trigger="click">
                  <el-button text>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="editERDiagram(diagram.id)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="exportDiagram('er', diagram.id)">
                        <el-icon><Download /></el-icon>
                        导出
                      </el-dropdown-item>
                      <el-dropdown-item @click="generateSQL(diagram)">
                        <el-icon><DocumentAdd /></el-icon>
                        生成SQL
                      </el-dropdown-item>
                      <el-dropdown-item @click="duplicateDiagram('er', diagram.id)">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="deleteERDiagram(diagram.id)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredERDiagrams.length === 0" class="empty-state">
              <el-empty description="暂无ER图设计">
                <el-button type="primary" @click="generateERDiagram">
                  开始创建第一个ER图
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>

        <!-- Context图列表 -->
        <el-tab-pane label="上下文图" name="context">
          <div class="design-list-header">
            <div class="list-actions">
              <el-button type="primary" @click="generateContextDiagram">
                <el-icon><MagicStick /></el-icon>
                AI生成上下文图
              </el-button>
              <el-button @click="createContextDiagram">
                <el-icon><Plus /></el-icon>
                手动创建
              </el-button>
            </div>
            <div class="list-filters">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索上下文图..."
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <div class="design-grid">
            <div
              v-for="diagram in filteredContextDiagrams"
              :key="diagram.id"
              class="design-item"
              @click="viewContextDiagram(diagram.id)"
            >
              <div class="design-preview">
                <el-icon size="64"><Connection /></el-icon>
              </div>
              <div class="design-info">
                <h4 class="design-title">{{ diagram.diagramName }}</h4>
                <div class="design-meta">
                  <span class="version">v{{ diagram.version }}</span>
                  <span class="date">{{ formatDate(diagram.updatedAt) }}</span>
                </div>
              </div>
              <div class="design-actions" @click.stop>
                <el-dropdown trigger="click">
                  <el-button text>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="editContextDiagram(diagram.id)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="exportDiagram('context', diagram.id)">
                        <el-icon><Download /></el-icon>
                        导出
                      </el-dropdown-item>
                      <el-dropdown-item @click="duplicateDiagram('context', diagram.id)">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="deleteContextDiagram(diagram.id)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredContextDiagrams.length === 0" class="empty-state">
              <el-empty description="暂无上下文图设计">
                <el-button type="primary" @click="generateContextDiagram">
                  开始创建第一个上下文图
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>

        <!-- 原型图列表 -->
        <el-tab-pane label="原型图" name="prototype">
          <div class="design-list-header">
            <div class="list-actions">
              <el-button type="primary" @click="generatePrototype">
                <el-icon><MagicStick /></el-icon>
                AI生成原型图
              </el-button>
              <el-button @click="createPrototype">
                <el-icon><Plus /></el-icon>
                手动创建
              </el-button>
            </div>
            <div class="list-filters">
              <el-select
                v-model="prototypeTypeFilter"
                placeholder="原型图类型"
                style="width: 150px; margin-right: 10px"
                clearable
              >
                <el-option label="线框图" value="Wireframe" />
                <el-option label="用户流程图" value="UserFlow" />
                <el-option label="组件关系图" value="ComponentDiagram" />
                <el-option label="交互流程图" value="InteractionFlow" />
              </el-select>
              <el-select
                v-model="deviceTypeFilter"
                placeholder="设备类型"
                style="width: 120px; margin-right: 10px"
                clearable
              >
                <el-option label="桌面端" value="Desktop" />
                <el-option label="移动端" value="Mobile" />
                <el-option label="平板" value="Tablet" />
                <el-option label="响应式" value="Responsive" />
              </el-select>
              <el-select
                v-model="fidelityLevelFilter"
                placeholder="保真度级别"
                style="width: 120px; margin-right: 10px"
                clearable
              >
                <el-option label="低保真" value="Low" />
                <el-option label="中保真" value="Medium" />
                <el-option label="高保真" value="High" />
              </el-select>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索原型图..."
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <div class="design-grid">
            <div
              v-for="prototype in filteredPrototypes"
              :key="prototype.id"
              class="design-item"
              @click="viewPrototype(prototype.id)"
            >
              <div class="design-header">
                <div class="design-title">{{ prototype.prototypeName }}</div>
                <el-dropdown trigger="click" @command="(command: string) => handlePrototypeAction(command, prototype)">
                  <el-button type="text" size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="view">查看</el-dropdown-item>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="copy">复制</el-dropdown-item>
                      <el-dropdown-item command="export">导出</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <div class="design-content">
                <div class="design-meta">
                  <el-tag size="small" type="primary">{{ getPrototypeTypeLabel(prototype.prototypeType) }}</el-tag>
                  <el-tag size="small" type="info">{{ prototype.deviceType }}</el-tag>
                  <el-tag size="small" type="success">{{ prototype.fidelityLevel }}</el-tag>
                </div>
                <div class="design-description">
                  {{ prototype.description || '暂无描述' }}
                </div>
                <div class="design-footer">
                  <span class="design-time">{{ formatTime(prototype.createdTime) }}</span>
                  <span class="design-version">v{{ prototype.prototypeVersion }}</span>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="filteredPrototypes.length === 0" class="empty-state">
              <el-empty description="暂无原型图设计">
                <el-button type="primary" @click="generatePrototype">
                  开始创建第一个原型图
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 项目未选择状态 -->
    <div v-else class="no-project-selected">
      <el-empty description="请先选择一个项目">
        <el-button type="primary" @click="$router.push('/projects')">
          前往项目管理
        </el-button>
      </el-empty>
    </div>

    <!-- SQL生成对话框 -->
    <el-dialog
      v-model="showSQLDialog"
      title="生成SQL脚本"
      width="80%"
      :close-on-click-modal="false"
      @close="closeSQLDialog"
    >
      <div class="sql-generator">
        <!-- 配置选项 -->
        <div class="sql-options">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item label="数据库类型">
                <el-select
                  v-model="sqlGenerationOptions.databaseType"
                  @change="generateSQLPreview"
                  style="width: 100%"
                >
                  <el-option label="SQL Server" value="SqlServer" />
                  <el-option label="MySQL" value="MySQL" />
                  <el-option label="PostgreSQL" value="PostgreSQL" />
                  <el-option label="Oracle" value="Oracle" />
                  <el-option label="SQLite" value="SQLite" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="表前缀">
                <el-input
                  v-model="sqlGenerationOptions.tablePrefix"
                  placeholder="可选"
                  @input="generateSQLPreview"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生成选项">
                <div class="sql-options-checkboxes">
                  <el-checkbox
                    v-model="sqlGenerationOptions.includeDropStatements"
                    @change="generateSQLPreview"
                  >
                    包含删除语句
                  </el-checkbox>
                  <el-checkbox
                    v-model="sqlGenerationOptions.includeComments"
                    @change="generateSQLPreview"
                  >
                    包含注释
                  </el-checkbox>
                  <el-checkbox
                    v-model="sqlGenerationOptions.includeIndexes"
                    @change="generateSQLPreview"
                  >
                    包含索引
                  </el-checkbox>
                  <el-checkbox
                    v-model="sqlGenerationOptions.includeForeignKeys"
                    @change="generateSQLPreview"
                  >
                    包含外键
                  </el-checkbox>
                  <el-checkbox
                    v-model="sqlGenerationOptions.generateChineseComments"
                    @change="generateSQLPreview"
                  >
                    <span>字段加中文注释</span>
                    <el-tooltip content="使用AI智能生成数据库字段的中文注释，提高代码可读性" placement="top">
                      <el-icon style="margin-left: 4px; color: #909399;"><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- SQL预览 -->
        <div class="sql-preview">
          <div class="preview-header">
            <h4>SQL预览</h4>
            <div class="preview-actions">
              <el-button
                type="primary"
                :loading="sqlGenerating || commentGenerating"
                @click="generateSQLPreview"
              >
                <el-icon><Refresh /></el-icon>
                {{ commentGenerating ? '生成中文注释中...' : sqlGenerating ? '生成SQL中...' : '重新生成' }}
              </el-button>
              <el-button
                type="success"
                @click="downloadSQL"
                :disabled="!generatedSQL"
              >
                <el-icon><Download /></el-icon>
                下载SQL
              </el-button>
            </div>
          </div>

          <div class="sql-content">
            <el-input
              v-model="generatedSQL"
              type="textarea"
              :rows="20"
              readonly
              placeholder="SQL脚本将在这里显示..."
              class="sql-textarea"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeSQLDialog">取消</el-button>
          <el-button type="primary" @click="downloadSQL" :disabled="!generatedSQL">
            下载SQL文件
          </el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  PictureRounded,
  Plus,
  Folder,
  DataBoard,
  Connection,
  Monitor,
  MagicStick,
  Search,
  MoreFilled,
  Edit,
  Download,
  CopyDocument,
  Delete,
  DocumentAdd,
  Refresh,
  QuestionFilled,
  Platform,
  InfoFilled,
  Back,
  ChatDotRound
} from '@element-plus/icons-vue'
import { ProjectService } from '@/services/project'
import { DesignService } from '@/services/design'
import { PrototypeService } from '@/services/prototype'
import { AIProviderService } from '@/services/aiProvider'
import type { ERDiagram, ContextDiagram, ProjectSummary } from '@/types'
import type { Prototype } from '@/services/prototype'


const router = useRouter()

// 获取路由参数，检查是否从AI聊天跳转过来
const route = router.currentRoute.value
const fromAIChat = ref(route.query.fromAIChat === 'true')
const aiChatConversationId = ref(route.query.conversationId as string)
const aiChatMode = ref(route.query.mode as string)

// 响应式数据
const projects = ref<ProjectSummary[]>([])
const selectedProjectId = ref<number | undefined>(undefined)
const activeTab = ref('er')
const searchKeyword = ref('')
const showCreateDialog = ref(false)
const loading = ref(false)

// AI提供商相关数据
const selectedAIProvider = ref<number | undefined>()
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// ER图和Context图数据
const erDiagrams = ref<ERDiagram[]>([])
const contextDiagrams = ref<ContextDiagram[]>([])

// 原型图数据
const prototypes = ref<Prototype[]>([])
const prototypeTypeFilter = ref('')
const deviceTypeFilter = ref('')
const fidelityLevelFilter = ref('')



// SQL生成相关数据
const showSQLDialog = ref(false)
const sqlGenerationOptions = ref({
  databaseType: 'SqlServer' as 'SqlServer' | 'MySQL' | 'PostgreSQL' | 'Oracle' | 'SQLite',
  includeDropStatements: true,
  includeComments: true,
  includeIndexes: true,
  includeForeignKeys: true,
  generateChineseComments: false, // 新增：AI生成中文注释
  tablePrefix: '',
  charset: 'utf8mb4'
})
const generatedSQL = ref('')
const currentDiagramForSQL = ref<ERDiagram | null>(null)
const sqlGenerating = ref(false)
const commentGenerating = ref(false) // 新增：注释生成状态

// 计算属性
const filteredERDiagrams = computed(() => {
  if (!searchKeyword.value) return erDiagrams.value
  return erDiagrams.value.filter(diagram =>
    diagram.diagramName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (diagram.description && diagram.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

const filteredContextDiagrams = computed(() => {
  if (!searchKeyword.value) return contextDiagrams.value
  return contextDiagrams.value.filter(diagram =>
    diagram.diagramName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredPrototypes = computed(() => {
  let filtered = prototypes.value

  // 按类型过滤
  if (prototypeTypeFilter.value) {
    filtered = filtered.filter(prototype => prototype.prototypeType === prototypeTypeFilter.value)
  }

  // 按设备类型过滤
  if (deviceTypeFilter.value) {
    filtered = filtered.filter(prototype => prototype.deviceType === deviceTypeFilter.value)
  }

  // 按保真度级别过滤
  if (fidelityLevelFilter.value) {
    filtered = filtered.filter(prototype => prototype.fidelityLevel === fidelityLevelFilter.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    filtered = filtered.filter(prototype =>
      prototype.prototypeName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (prototype.description && prototype.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
    )
  }

  return filtered
})

// 生命周期
onMounted(async () => {
  await loadProjects()

  // 如果URL中有项目ID参数，自动选择
  const projectId = router.currentRoute.value.query.projectId
  if (projectId) {
    selectedProjectId.value = Number(projectId)
    await onProjectChange()
  }
})

// 方法
const loadProjects = async () => {
  try {
    loading.value = true
    const result = await ProjectService.getProjects()
    projects.value = result.items || []
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
    projects.value = []
  } finally {
    loading.value = false
  }
}

const onProjectChange = async () => {
  if (!selectedProjectId.value) return

  try {
    loading.value = true
    await Promise.all([
      loadERDiagrams(),
      loadContextDiagrams(),
      loadPrototypes(),
      loadAIProviders() // 加载AI提供商配置
    ])
  } catch (error) {
    console.error('加载设计数据失败:', error)
    ElMessage.error('加载设计数据失败')
  } finally {
    loading.value = false
  }
}

const loadERDiagrams = async () => {
  if (!selectedProjectId.value) return
  try {
    erDiagrams.value = await DesignService.getERDiagrams(selectedProjectId.value)
  } catch (error) {
    console.error('加载ER图失败:', error)
    erDiagrams.value = []
  }
}

const loadContextDiagrams = async () => {
  if (!selectedProjectId.value) return
  try {
    contextDiagrams.value = await DesignService.getContextDiagrams(selectedProjectId.value)
  } catch (error) {
    console.error('加载上下文图失败:', error)
    contextDiagrams.value = []
  }
}

const loadPrototypes = async () => {
  if (!selectedProjectId.value) return
  try {
    prototypes.value = await PrototypeService.getPrototypes(selectedProjectId.value)
  } catch (error) {
    console.error('加载原型图失败:', error)
    prototypes.value = []
  }
}

// AI提供商相关方法
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
    console.log('加载AI提供商列表成功:', aiProviders.value)
  } catch (error: any) {
    console.error('加载AI供应商列表失败:', error)
    ElMessage.error('加载AI供应商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}



const navigateToAIConfig = () => {
  // 打开新窗口到AI配置页面
  window.open('/ai-providers', '_blank')
}

const onAIProviderChange = async (value: number) => {
  console.log('AI提供商选择变更:', value)
  selectedAIProvider.value = value
}



const onTabChange = (tabName: string | number) => {
  activeTab.value = String(tabName)
}

// ER图相关操作
const generateERDiagram = async () => {
  if (!selectedProjectId.value) return

  if (!selectedAIProvider.value) {
    ElMessage.warning('请先选择AI提供商')
    return
  }

  try {
    const requestData: any = {
      projectId: selectedProjectId.value,
      databaseType: 'SqlServer',
      diagramFormat: 'Mermaid',
      aiProviderConfigId: selectedAIProvider.value
    }

    console.log('生成ER图请求参数:', requestData)

    const result = await DesignService.generateERDiagram(requestData)

    ElMessage.success('ER图生成任务已启动，请稍候...')

    // 跳转到ER图页面并传递任务ID
    router.push({
      name: 'ERDiagram',
      params: { projectId: selectedProjectId.value },
      query: { taskId: result.taskId }
    })
  } catch (error: any) {
    console.error('生成ER图失败:', error)

    // 显示详细的错误信息
    let errorMessage = '生成ER图失败'
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `生成ER图失败: ${error.response.data?.message || error.response.statusText}`
      console.error('API错误响应:', error.response.data)
    } else if (error.request) {
      // 请求发送了但没有收到响应
      errorMessage = '生成ER图失败: 无法连接到服务器，请检查后端服务是否启动'
      console.error('网络错误:', error.request)
    } else {
      // 其他错误
      errorMessage = `生成ER图失败: ${error.message}`
    }

    ElMessage.error(errorMessage)
  }
}

const createERDiagram = () => {
  if (!selectedProjectId.value) return

  router.push({
    name: 'ERDiagram',
    params: { projectId: selectedProjectId.value },
    query: { mode: 'create' }
  })
}

const viewERDiagram = (id: number) => {
  router.push({
    name: 'ERDiagram',
    params: { projectId: selectedProjectId.value },
    query: { id }
  })
}

const editERDiagram = (id: number) => {
  router.push({
    name: 'ERDiagram',
    params: { projectId: selectedProjectId.value },
    query: { id, mode: 'edit' }
  })
}

const deleteERDiagram = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个ER图吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DesignService.deleteERDiagram(id)
    ElMessage.success('删除成功')
    await loadERDiagrams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除ER图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// Context图相关操作
const generateContextDiagram = async () => {
  if (!selectedProjectId.value) return

  if (!selectedAIProvider.value) {
    ElMessage.warning('请先选择AI提供商')
    return
  }

  try {
    const requestData: any = {
      projectId: selectedProjectId.value,
      diagramFormat: 'Mermaid',
      includeExternalSystems: true,
      aiProviderConfigId: selectedAIProvider.value
    }

    console.log('生成Context图请求参数:', requestData)

    const result = await DesignService.generateContextDiagram(requestData)

    ElMessage.success('上下文图生成任务已启动，请稍候...')

    // 跳转到上下文图页面并传递任务ID
    router.push({
      name: 'ContextDiagram',
      params: { projectId: selectedProjectId.value },
      query: { taskId: result.taskId }
    })
  } catch (error) {
    console.error('生成上下文图失败:', error)
    ElMessage.error('生成上下文图失败')
  }
}

const createContextDiagram = () => {
  if (!selectedProjectId.value) return

  router.push({
    name: 'ContextDiagram',
    params: { projectId: selectedProjectId.value },
    query: { mode: 'create' }
  })
}

const viewContextDiagram = (id: number) => {
  router.push({
    name: 'ContextDiagram',
    params: { projectId: selectedProjectId.value },
    query: { id }
  })
}

const editContextDiagram = (id: number) => {
  router.push({
    name: 'ContextDiagram',
    params: { projectId: selectedProjectId.value },
    query: { id, mode: 'edit' }
  })
}

const deleteContextDiagram = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个上下文图吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DesignService.deleteContextDiagram(id)
    ElMessage.success('删除成功')
    await loadContextDiagrams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除上下文图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// SQL生成功能
const generateSQL = async (diagram: ERDiagram) => {
  try {
    if (!diagram.mermaidDefinition) {
      ElMessage.error('该ER图没有Mermaid定义，无法生成SQL')
      return
    }

    currentDiagramForSQL.value = diagram
    showSQLDialog.value = true

    // 立即生成SQL预览
    await generateSQLPreview()

  } catch (error) {
    console.error('准备SQL生成失败:', error)
    ElMessage.error('准备SQL生成失败')
  }
}

const generateSQLPreview = async () => {
  if (!currentDiagramForSQL.value?.mermaidDefinition) return

  try {
    sqlGenerating.value = true

    // 动态导入SQL生成器和AI注释生成器
    const { SQLGenerator } = await import('@/services/sqlGenerator')

    // 解析Mermaid ER图
    let tables = SQLGenerator.parseMermaidER(currentDiagramForSQL.value.mermaidDefinition)

    if (tables.length === 0) {
      ElMessage.warning('无法从ER图中解析出表结构，请检查Mermaid定义格式')
      generatedSQL.value = '-- 无法解析表结构\n-- 请确保ER图使用正确的Mermaid语法'
      return
    }

    // 生成SQL
    generatedSQL.value = SQLGenerator.generateSQL(tables, sqlGenerationOptions.value)

    // 如果启用了中文注释生成，使用AI增强SQL脚本
    if (sqlGenerationOptions.value.generateChineseComments) {
      try {
        commentGenerating.value = true

        // 显示详细的加载提示
        ElMessage.info({
          message: 'AI正在生成SQL扩展属性注释，这可能需要几分钟时间，请耐心等待...',
          duration: 0, // 不自动关闭
          showClose: true
        })

        const { enhanceSQLWithComments } = await import('@/services/aiCommentGenerator')
        generatedSQL.value = await enhanceSQLWithComments(generatedSQL.value, {
          projectId: selectedProjectId.value,
          databaseType: sqlGenerationOptions.value.databaseType,
          businessDomain: '项目管理系统'
        })

        // 关闭加载消息并显示成功消息
        ElMessage.closeAll()
        ElMessage.success('AI扩展属性注释生成完成！')
      } catch (error) {
        console.error('生成中文注释失败:', error)
        ElMessage.closeAll()

        if ((error as any)?.message?.includes('timeout') || (error as any)?.message?.includes('超时')) {
          ElMessage.error('AI服务响应超时，请稍后重试。如果问题持续，请检查网络连接或联系管理员。')
        } else {
          ElMessage.warning('AI注释生成失败，使用原始SQL: ' + ((error as any)?.message || '未知错误'))
        }
      } finally {
        commentGenerating.value = false
      }
    }

  } catch (error) {
    console.error('生成SQL失败:', error)
    ElMessage.error('生成SQL失败')
    generatedSQL.value = '-- SQL生成失败\n-- 请检查ER图格式是否正确'
  } finally {
    sqlGenerating.value = false
    commentGenerating.value = false
  }
}

const downloadSQL = () => {
  if (!generatedSQL.value || !currentDiagramForSQL.value) return

  const blob = new Blob([generatedSQL.value], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${currentDiagramForSQL.value.diagramName || 'database'}_${sqlGenerationOptions.value.databaseType}.sql`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('SQL文件下载成功')
}

const closeSQLDialog = () => {
  showSQLDialog.value = false
  currentDiagramForSQL.value = null
  generatedSQL.value = ''
}

// 批量SQL生成功能
const showBatchSQLDialog = () => {
  if (erDiagrams.value.length === 0) {
    ElMessage.warning('没有可用的ER图')
    return
  }

  // 如果只有一个ER图，直接生成
  if (erDiagrams.value.length === 1) {
    generateSQL(erDiagrams.value[0])
    return
  }

  // 多个ER图时，让用户选择第一个或显示选择对话框
  generateSQL(erDiagrams.value[0])
}

// 通用操作
const exportDiagram = async (type: 'er' | 'context', id: number) => {
  try {
    ElMessage.info('正在导出图表，请稍候...')

    // 获取图表数据
    let diagram: any
    if (type === 'er') {
      diagram = await DesignService.getERDiagram(id)
    } else {
      diagram = await DesignService.getContextDiagram(id)
    }

    if (!diagram.mermaidDefinition) {
      ElMessage.error('图表内容为空，无法导出')
      return
    }

    const { exportDiagram: exportDiagramUtil } = await import('@/utils/exportUtils')
    const filename = diagram.diagramName || `${type}-diagram-${id}`

    await exportDiagramUtil(diagram.mermaidDefinition, 'svg', filename, {
      backgroundColor: 'white',
      scale: 2
    })

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const duplicateDiagram = async (type: 'er' | 'context', id: number) => {
  try {
    if (type === 'er') {
      const diagram = await DesignService.getERDiagram(id)
      await DesignService.createERDiagram({
        projectId: diagram.projectId,
        requirementDocumentId: diagram.requirementDocumentId,
        diagramName: `${diagram.diagramName} (副本)`,
        mermaidDefinition: diagram.mermaidDefinition,
        description: diagram.description
      })
      await loadERDiagrams()
    } else {
      const diagram = await DesignService.getContextDiagram(id)
      await DesignService.createContextDiagram({
        projectId: diagram.projectId,
        requirementDocumentId: diagram.requirementDocumentId,
        diagramName: `${diagram.diagramName} (副本)`,
        mermaidDefinition: diagram.mermaidDefinition,
        externalEntities: diagram.externalEntities,
        systemBoundary: diagram.systemBoundary,
        dataFlows: diagram.dataFlows
      })
      await loadContextDiagrams()
    }

    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 原型图相关操作
const generatePrototype = async () => {
  if (!selectedProjectId.value) {
    ElMessage.warning('请先选择项目')
    return
  }

  if (!selectedAIProvider.value) {
    ElMessage.warning('请先选择AI提供商')
    return
  }

  // 使用用户选择的参数生成原型图
  const selectedPrototypeType = prototypeTypeFilter.value || 'Wireframe'
  const selectedDeviceType = deviceTypeFilter.value || 'Desktop'
  const selectedFidelityLevel = fidelityLevelFilter.value || 'Low'

  try {
    const result = await PrototypeService.generatePrototype({
      projectId: selectedProjectId.value!,
      prototypeType: selectedPrototypeType,
      deviceType: selectedDeviceType,
      fidelityLevel: selectedFidelityLevel,
      requirements: '请生成基本的用户界面原型图，包含主要功能模块和页面布局',
      aiProviderConfigId: selectedAIProvider.value!
    })

    // 显示生成参数信息
    const typeLabel = getPrototypeTypeLabel(selectedPrototypeType)
    ElMessage.success(`${typeLabel}生成任务已启动，请稍候...`)

    // 跳转到原型图页面并传递任务ID
    router.push({
      name: 'PrototypeView',
      params: { projectId: selectedProjectId.value },
      query: { taskId: result.taskId }
    })
  } catch (error: any) {
    console.error('生成原型图失败:', error)
    ElMessage.error('生成原型图失败，请稍后重试')
  }
}



const createPrototype = () => {
  if (!selectedProjectId.value) return

  router.push({
    name: 'PrototypeView',
    params: { projectId: selectedProjectId.value },
    query: { mode: 'create' }
  })
}

const viewPrototype = (id: number) => {
  router.push({
    name: 'PrototypeView',
    params: { projectId: selectedProjectId.value },
    query: { id }
  })
}

const handlePrototypeAction = async (command: string, prototype: any) => {
  switch (command) {
    case 'view':
      viewPrototype(prototype.id)
      break
    case 'edit':
      editPrototype(prototype.id)
      break
    case 'duplicate':
      await duplicatePrototype(prototype.id)
      break
    case 'export':
      await exportPrototype(prototype.id)
      break
    case 'delete':
      await deletePrototype(prototype.id)
      break
  }
}

const editPrototype = (id: number) => {
  router.push({
    name: 'PrototypeView',
    params: { projectId: selectedProjectId.value },
    query: { id, mode: 'edit' }
  })
}

const duplicatePrototype = async (id: number) => {
  try {
    await PrototypeService.duplicatePrototype(id)
    await loadPrototypes()
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制原型图失败:', error)
    ElMessage.error('复制失败')
  }
}

const exportPrototype = async (id: number) => {
  try {
    ElMessage.info('正在导出原型图，请稍候...')

    const blob = await PrototypeService.exportPrototype(id, 'svg')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `prototype-${id}.svg`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出原型图失败:', error)
    ElMessage.error('导出失败')
  }
}

const deletePrototype = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个原型图吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await PrototypeService.deletePrototype(id)
    ElMessage.success('删除成功')
    await loadPrototypes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除原型图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const getPrototypeTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'Wireframe': '线框图',
    'UserFlow': '用户流程图',
    'ComponentDiagram': '组件关系图',
    'InteractionFlow': '交互流程图'
  }
  return typeMap[type] || type
}

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString('zh-CN')
}

// AI聊天相关方法
const returnToAIChat = () => {
  if (aiChatConversationId.value && aiChatMode.value) {
    // 返回到AI聊天页面，并恢复对话状态
    router.push({
      path: '/ai-chat',
      query: {
        conversationId: aiChatConversationId.value,
        mode: aiChatMode.value,
        returnFrom: 'design'
      }
    })
  } else {
    // 如果没有对话ID，直接跳转到设计模式
    router.push({
      path: '/ai-chat',
      query: {
        mode: 'design',
        returnFrom: 'design'
      }
    })
  }
}

const openAIChat = () => {
  // 打开AI聊天页面的设计模式
  const query: any = {
    mode: 'design'
  }

  // 如果有选中的项目，传递项目ID
  if (selectedProjectId.value) {
    query.projectId = selectedProjectId.value
  }

  router.push({
    path: '/ai-chat',
    query
  })
}
</script>

<style lang="scss" scoped>
.design-list {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          color: #409eff;
        }
      }

      .page-description {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .header-actions {
      .el-button {
        height: 40px;
        padding: 0 20px;
      }
    }
  }

  .project-selector,
  .ai-selector {
    margin-bottom: 24px;

    .el-card {
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .selector-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .selector-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: #303133;
        white-space: nowrap;

        .el-icon {
          color: #409eff;
        }
      }

      .project-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .project-name {
          font-weight: 500;
        }

        .project-status {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.active {
            background-color: #e7f7ff;
            color: #409eff;
          }

          &.completed {
            background-color: #f0f9ff;
            color: #67c23a;
          }

          &.paused {
            background-color: #fdf6ec;
            color: #e6a23c;
          }
        }
      }

      .provider-option {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .provider-name {
          font-weight: 500;
          flex: 1;
        }

        .provider-model {
          font-size: 12px;
          color: #909399;
        }
      }

      .ai-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 16px;
        padding: 8px 12px;
        background-color: #f0f9ff;
        border-radius: 6px;
        border: 1px solid #d9ecff;

        .info-icon {
          color: #409eff;
          font-size: 16px;
        }

        .ai-description {
          font-size: 13px;
          color: #409eff;
          font-weight: 500;
        }
      }
    }
  }

  .design-types {
    margin-bottom: 24px;

    .design-type-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .design-type-content {
        text-align: center;
        padding: 20px;
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
          color: white;

          .design-type-icon .el-icon {
            color: white;
          }
        }

        .design-type-icon {
          margin-bottom: 16px;

          .el-icon {
            color: #409eff;
            transition: color 0.3s ease;
          }
        }

        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          margin: 0 0 16px 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }

        .design-count {
          font-size: 14px;
          font-weight: 500;
          opacity: 0.8;
        }

        &.active p,
        &.active .design-count {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  .design-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .design-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;

      .list-actions {
        display: flex;
        gap: 12px;
      }

      .list-filters {
        display: flex;
        gap: 12px;
      }
    }

    .design-grid {
      padding: 24px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;

      .design-item {
        background: white;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          border-color: #409eff;
        }

        .design-preview {
          text-align: center;
          margin-bottom: 16px;

          .el-icon {
            color: #409eff;
          }
        }

        .design-info {
          .design-title {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            line-height: 1.4;
          }

          .design-description {
            margin: 0 0 12px 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .design-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #909399;

            .version {
              background-color: #f0f2f5;
              padding: 2px 6px;
              border-radius: 4px;
              font-weight: 500;
            }
          }
        }

        .design-quick-actions {
          margin-top: 12px;

          .sql-button {
            width: 100%;
            border-radius: 6px;
            font-weight: 500;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
            }
          }
        }

        .design-actions {
          position: absolute;
          top: 16px;
          right: 16px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover .design-actions {
          opacity: 1;
        }
      }

      .empty-state {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
      }
    }
  }

  .no-project-selected {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .design-list {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-actions {
        align-self: flex-start;
      }
    }

    .design-types .el-col {
      margin-bottom: 16px;
    }

    .design-content .design-list-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .design-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

/* 标签页样式调整 */
.design-tabs :deep(.el-tabs__item) {
  padding: 0 30px !important; /* 增加左右内边距，使标签页更宽 */
  min-width: 120px !important; /* 设置最小宽度 */
  font-size: 16px !important; /* 稍微增大字体 */
  font-weight: 500 !important;
}

.design-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600 !important;
}

/* 备用方案：全局样式 */
.el-tabs__item {
  padding: 0 30px !important;
  min-width: 120px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.el-tabs__item.is-active {
  font-weight: 600 !important;
}

/* SQL生成对话框样式 */
.sql-generator {
  .sql-options {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .sql-options-checkboxes {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .el-checkbox {
        margin-right: 0;
      }
    }
  }

  .sql-preview {
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }

    .sql-content {
      .sql-textarea {
        :deep(.el-textarea__inner) {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;
          background: #1e1e1e;
          color: #d4d4d4;
          border: 1px solid #3c3c3c;

          &:focus {
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
          }
        }
      }
    }
  }
}
</style>
