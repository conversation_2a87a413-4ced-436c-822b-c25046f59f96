-- =============================================
-- 删除RequirementConversations表的MessageType约束
-- 允许任意MessageType值，提高系统灵活性
-- =============================================

USE ProjectManagementAI;
GO

-- 删除现有的CHECK约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_RequirementConversations_MessageType')
BEGIN
    ALTER TABLE RequirementConversations DROP CONSTRAINT CK_RequirementConversations_MessageType;
    PRINT '已删除MessageType约束';
END
ELSE
BEGIN
    PRINT 'MessageType约束不存在，无需删除';
END
GO

-- 验证约束是否已删除
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_RequirementConversations_MessageType')
BEGIN
    PRINT '确认：MessageType约束已成功删除';
END
ELSE
BEGIN
    PRINT '警告：MessageType约束仍然存在';
END
GO

PRINT '约束删除完成！现在MessageType字段可以接受任意值。';
