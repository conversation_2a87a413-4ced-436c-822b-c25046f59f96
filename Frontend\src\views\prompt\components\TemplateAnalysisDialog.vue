<template>
  <el-dialog
    v-model="visible"
    title="模板分析详情"
    width="80%"
    :before-close="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else class="template-analysis">
      <!-- 模板基本信息 -->
      <el-card class="template-info">
        <template #header>
          <span>模板信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">{{ templateData.name }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ templateData.taskType }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ templateData.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ templateData.updatedAt }}</el-descriptions-item>
          <el-descriptions-item label="使用次数">{{ templateData.usageCount }}</el-descriptions-item>
          <el-descriptions-item label="成功率">{{ templateData.successRate }}%</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 性能指标 -->
      <el-card class="performance-metrics">
        <template #header>
          <span>性能指标</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="平均响应时间" :value="templateData.avgResponseTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="平均成本" :value="templateData.avgCost" prefix="¥" :precision="4" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="质量评分" :value="templateData.qualityScore" :precision="1" suffix="/10" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="用户满意度" :value="templateData.userSatisfaction" suffix="%" :precision="1" />
          </el-col>
        </el-row>
      </el-card>

      <!-- 使用趋势 -->
      <el-card class="usage-trend">
        <template #header>
          <span>使用趋势</span>
        </template>
        <div ref="usageTrendChart" class="chart-container"></div>
      </el-card>

      <!-- 错误分析 -->
      <el-card class="error-analysis">
        <template #header>
          <span>错误分析</span>
        </template>
        <el-table :data="templateData.errorAnalysis" style="width: 100%">
          <el-table-column prop="errorType" label="错误类型" />
          <el-table-column prop="frequency" label="出现频率" align="right" />
          <el-table-column prop="lastOccurrence" label="最后出现时间" />
          <el-table-column prop="impact" label="影响程度" align="right">
            <template #default="{ row }">
              <el-tag :type="getImpactType(row.impact)">
                {{ row.impact }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 优化建议 -->
      <el-card class="optimization-suggestions">
        <template #header>
          <span>优化建议</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="suggestion in templateData.suggestions"
            :key="suggestion.id"
            :type="getSuggestionType(suggestion.priority)"
            :timestamp="suggestion.category"
          >
            <h4>{{ suggestion.title }}</h4>
            <p>{{ suggestion.description }}</p>
            <el-tag v-if="suggestion.priority" :type="getPriorityType(suggestion.priority)" size="small">
              {{ suggestion.priority }}
            </el-tag>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 相关模板推荐 -->
      <el-card class="related-templates">
        <template #header>
          <span>相关模板推荐</span>
        </template>
        <el-row :gutter="16">
          <el-col v-for="template in templateData.relatedTemplates" :key="template.id" :span="8">
            <el-card class="related-template-card" shadow="hover">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-stats">
                <span>成功率: {{ template.successRate }}%</span>
                <span>使用次数: {{ template.usageCount }}</span>
              </div>
              <el-button type="text" @click="viewTemplate(template.id)">查看详情</el-button>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">导出分析报告</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  visible: boolean
  templateId?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'view-template': [templateId: number]
}>()

const loading = ref(false)
const templateData = ref<any>({})
const usageTrendChart = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loadTemplateAnalysis = async () => {
  if (!props.templateId) return

  loading.value = true
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    templateData.value = {
      name: '需求分析模板',
      taskType: '需求分析',
      createdAt: '2024-01-15',
      updatedAt: '2024-03-20',
      usageCount: 156,
      successRate: 92.3,
      avgResponseTime: 2340,
      avgCost: 0.0234,
      qualityScore: 8.7,
      userSatisfaction: 89.5,
      usageTrend: [
        { date: '2024-01', count: 12 },
        { date: '2024-02', count: 18 },
        { date: '2024-03', count: 25 },
        { date: '2024-04', count: 31 },
        { date: '2024-05', count: 28 },
        { date: '2024-06', count: 42 }
      ],
      errorAnalysis: [
        { errorType: '超时错误', frequency: 5, lastOccurrence: '2024-06-15', impact: '中' },
        { errorType: '格式错误', frequency: 3, lastOccurrence: '2024-06-10', impact: '低' },
        { errorType: '内容不完整', frequency: 2, lastOccurrence: '2024-06-08', impact: '高' }
      ],
      suggestions: [
        {
          id: 1,
          title: '优化提示词结构',
          description: '建议重新组织提示词的逻辑结构，提高AI理解准确性',
          category: '内容优化',
          priority: '高'
        },
        {
          id: 2,
          title: '增加示例说明',
          description: '在模板中添加更多具体示例，帮助AI更好地理解期望输出',
          category: '功能增强',
          priority: '中'
        }
      ],
      relatedTemplates: [
        { id: 2, name: '用户故事模板', successRate: 88.5, usageCount: 89 },
        { id: 3, name: '技术方案模板', successRate: 91.2, usageCount: 67 },
        { id: 4, name: '测试用例模板', successRate: 85.7, usageCount: 123 }
      ]
    }
  } catch (error) {
    console.error('加载模板分析失败:', error)
  } finally {
    loading.value = false
  }
}

const initChart = () => {
  if (!usageTrendChart.value) return

  chartInstance = echarts.init(usageTrendChart.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !templateData.value.usageTrend) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: templateData.value.usageTrend.map((item: any) => item.date)
    },
    yAxis: {
      type: 'value',
      name: '使用次数'
    },
    series: [
      {
        name: '使用次数',
        type: 'line',
        data: templateData.value.usageTrend.map((item: any) => item.count),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

const getImpactType = (impact: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[impact] || 'info'
}

const getSuggestionType = (priority: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[priority] || 'primary'
}

const getPriorityType = (priority: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[priority] || 'info'
}

const viewTemplate = (templateId: number) => {
  emit('view-template', templateId)
}

const exportAnalysis = () => {
  // 导出分析报告逻辑
  console.log('导出分析报告')
}

const handleClose = () => {
  visible.value = false
}

watch(() => props.visible, (newVisible) => {
  if (newVisible && props.templateId) {
    loadTemplateAnalysis()
    nextTick(() => {
      initChart()
    })
  }
})

watch(() => templateData.value, () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })
</script>

<style scoped>
.loading-container {
  padding: 40px;
  text-align: center;
}

.template-analysis {
  max-height: 70vh;
  overflow-y: auto;
}

.template-info,
.performance-metrics,
.usage-trend,
.error-analysis,
.optimization-suggestions,
.related-templates {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.related-template-card {
  margin-bottom: 16px;
}

.template-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.template-stats {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.template-stats span {
  margin-right: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
