<template>
  <div class="requirement-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          需求管理
        </h1>
        <p class="page-subtitle">管理项目需求文档，创建和分析需求规格书</p>
      </div>
      <div class="header-actions">
        <!-- 如果是从AI聊天跳转过来，显示返回按钮 -->
        <el-button
          v-if="fromAIChat"
          @click="returnToAIChat"
          type="info"
        >
          <el-icon><Back /></el-icon>
          返回AI聊天
        </el-button>

        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建需求文档
        </el-button>
        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入需求
        </el-button>

        <!-- AI聊天快捷入口 -->
        <el-button @click="openAIChat" type="success">
          <el-icon><ChatDotRound /></el-icon>
          AI需求分析
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="总需求数" :value="statistics.totalRequirements" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="进行中" :value="statistics.inProgressRequirements" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="已完成" :value="statistics.completedRequirements" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="待审核" :value="statistics.pendingRequirements" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <div class="filter-section">
        <div class="filter-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索需求文档..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="selectedProject"
            placeholder="选择项目"
            style="width: 200px; margin-left: 12px"
            clearable
            @change="handleProjectChange"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>

          <el-select
            v-model="selectedStatus"
            placeholder="状态筛选"
            style="width: 150px; margin-left: 12px"
            clearable
            @change="handleStatusChange"
          >
            <el-option label="草稿" value="Draft" />
            <el-option label="进行中" value="InProgress" />
            <el-option label="已完成" value="Completed" />
            <el-option label="待审核" value="PendingReview" />
            <el-option label="已发布" value="Published" />
          </el-select>
        </div>

        <div class="filter-right">
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置筛选
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 需求列表 -->
    <el-card class="requirements-card">
      <template #header>
        <div class="card-header">
          <span>需求文档列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="viewMode = 'list'"
                size="small"
              >
                <el-icon><List /></el-icon>
              </el-button>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="viewMode = 'grid'"
                size="small"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <el-empty
        v-else-if="filteredRequirements.length === 0"
        description="暂无需求文档"
        :image-size="120"
      >
        <el-button type="primary" @click="showCreateDialog">
          创建第一个需求文档
        </el-button>
      </el-empty>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="list-view">
        <el-table :data="filteredRequirements" style="width: 100%">
          <el-table-column prop="title" label="需求标题" min-width="200">
            <template #default="{ row }">
              <div class="requirement-title">
                <el-link @click="viewRequirement(row.id)" type="primary">
                  {{ row.title }}
                </el-link>
                <div class="requirement-meta">
                  <span class="project-name">{{ getProjectName(row.projectId) }}</span>
                  <span class="version">v{{ row.version }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="generatedBy" label="创建方式" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.generatedBy === 'AI'" type="success" size="small">
                <el-icon><Avatar /></el-icon>
                AI生成
              </el-tag>
              <el-tag v-else type="info" size="small">
                <el-icon><User /></el-icon>
                手动创建
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" @click="viewRequirement(row.id)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button size="small" @click="editRequirement(row.id)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-dropdown @command="(command) => handleAction(command, row)">
                  <el-button size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- <el-dropdown-item :command="`chat-${row.id}`">
                        <el-icon><ChatDotRound /></el-icon>
                        AI对话分析
                      </el-dropdown-item> -->
                      <el-dropdown-item :command="`export-${row.id}`">
                        <el-icon><Download /></el-icon>
                        导出文档
                      </el-dropdown-item>
                      <el-dropdown-item :command="`duplicate-${row.id}`">
                        <el-icon><CopyDocument /></el-icon>
                        复制需求
                      </el-dropdown-item>
                      <el-dropdown-item :command="`delete-${row.id}`" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 网格视图 -->
      <div v-else class="grid-view">
        <el-row :gutter="20">
          <el-col
            v-for="requirement in filteredRequirements"
            :key="requirement.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
          >
            <div class="requirement-card" @click="viewRequirement(requirement.id)">
              <div class="card-header">
                <div class="requirement-title">{{ requirement.title }}</div>
                <el-dropdown @command="(command) => handleAction(command, requirement)">
                  <el-button type="text" size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${requirement.id}`">编辑</el-dropdown-item>
                      <el-dropdown-item :command="`chat-${requirement.id}`">AI对话</el-dropdown-item>
                      <el-dropdown-item :command="`export-${requirement.id}`">导出</el-dropdown-item>
                      <el-dropdown-item :command="`delete-${requirement.id}`" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>

              <div class="card-content">
                <div class="requirement-meta">
                  <span class="project-name">{{ getProjectName(requirement.projectId) }}</span>
                  <span class="version">v{{ requirement.version }}</span>
                </div>

                <div class="requirement-status">
                  <el-tag :type="getStatusType(requirement.status)" size="small">
                    {{ getStatusText(requirement.status) }}
                  </el-tag>
                  <el-tag v-if="requirement.generatedBy === 'AI'" type="success" size="small">
                    <el-icon><Avatar /></el-icon>
                    AI生成
                  </el-tag>
                </div>

                <div class="requirement-time">
                  {{ formatDate(requirement.createdAt) }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建需求对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建需求文档"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="项目" prop="projectId">
          <el-select v-model="createForm.projectId" placeholder="选择项目" style="width: 100%">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="需求标题" prop="title">
          <el-input v-model="createForm.title" placeholder="请输入需求标题" />
        </el-form-item>

        <el-form-item label="创建方式" prop="createMethod">
          <el-radio-group v-model="createForm.createMethod">
            <el-radio label="manual">手动创建</el-radio>
            <el-radio label="ai">AI对话生成</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="createForm.createMethod === 'ai'" label="AI提供商" prop="aiProvider">
          <el-select
            v-model="createForm.aiProvider"
            placeholder="选择AI提供商"
            style="width: 100%"
            :loading="loadingAIProviders"
          >
            <el-option
              v-for="provider in aiProviders"
              :key="provider.id"
              :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
              :value="provider.id"
              :disabled="!provider.isActive"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ provider.modelName }}</span>
                <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="createForm.createMethod === 'manual'" label="需求内容" prop="content">
          <el-input
            v-model="createForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入需求内容..."
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="creating">
          {{ createForm.createMethod === 'ai' ? '开始AI对话' : '创建需求' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 导入需求对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入需求文档"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="import-section">
        <el-form :model="importForm" label-width="100px">
          <el-form-item label="选择项目">
            <el-select v-model="importForm.projectId" placeholder="选择项目" style="width: 100%">
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="上传文件">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :limit="1"
              accept=".txt,.md,.doc,.docx"
              :on-change="handleFileChange"
            >
              <el-button type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持 .txt, .md, .doc, .docx 格式文件
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleImport" :loading="importing">
          导入需求
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  Document, Plus, Upload, Search, Refresh, List, Grid, View, Edit,
  MoreFilled, ChatDotRound, Download, CopyDocument, Delete, Avatar, User, Back
} from '@element-plus/icons-vue'
import { RequirementService } from '@/services/requirement'
import { ProjectService } from '@/services/project'
import { AIProviderService } from '@/services/aiProvider'
import { useAuthStore } from '@/stores/auth'
import type { RequirementDocument, ProjectSummary } from '@/types'
import dayjs from 'dayjs'

const router = useRouter()

// 获取路由参数，检查是否从AI聊天跳转过来
const route = router.currentRoute.value
const fromAIChat = ref(route.query.fromAIChat === 'true')
const aiChatConversationId = ref(route.query.conversationId as string)
const aiChatMode = ref(route.query.mode as string)

// 响应式数据
const loading = ref(false)
const requirements = ref<RequirementDocument[]>([])
const projects = ref<ProjectSummary[]>([])
const viewMode = ref<'list' | 'grid'>('list')

// 筛选和搜索
const searchKeyword = ref('')
const selectedProject = ref<number | undefined>(undefined)
const selectedStatus = ref('')

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const statistics = ref({
  totalRequirements: 0,
  inProgressRequirements: 0,
  completedRequirements: 0,
  pendingRequirements: 0
})

// 对话框状态
const createDialogVisible = ref(false)
const importDialogVisible = ref(false)
const creating = ref(false)
const importing = ref(false)

// AI提供商相关
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// 表单数据
const createFormRef = ref<FormInstance>()
const createForm = ref({
  projectId: undefined as number | undefined,
  title: '',
  content: '',
  createMethod: 'manual' as 'manual' | 'ai',
  aiProvider: undefined as number | undefined
})

const importForm = ref({
  projectId: undefined as number | undefined,
  file: null as File | null
})

// 表单验证规则
const createRules = {
  projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
  title: [{ required: true, message: '请输入需求标题', trigger: 'blur' }],
  content: [
    {
      required: true,
      message: '请输入需求内容',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: Function) => {
        if (createForm.value.createMethod === 'manual' && !value) {
          callback(new Error('请输入需求内容'))
        } else {
          callback()
        }
      }
    }
  ],
  aiProvider: [
    {
      required: true,
      message: '请选择AI提供商',
      trigger: 'change',
      validator: (rule: any, value: number, callback: Function) => {
        if (createForm.value.createMethod === 'ai' && !value) {
          callback(new Error('请选择AI提供商'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 计算属性
const filteredRequirements = computed(() => {
  // 确保 requirements.value 是数组
  let filtered = Array.isArray(requirements.value) ? requirements.value : []

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(req =>
      req.title?.toLowerCase().includes(keyword) ||
      req.content?.toLowerCase().includes(keyword)
    )
  }

  // 项目筛选
  if (selectedProject.value) {
    filtered = filtered.filter(req => req.projectId === selectedProject.value)
  }

  // 状态筛选
  if (selectedStatus.value) {
    filtered = filtered.filter(req => req.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const loadRequirements = async () => {
  try {
    loading.value = true

    // 使用新的统一API获取需求文档
    const response = await RequirementService.getAllRequirementDocuments(
      pagination.value.currentPage,
      pagination.value.pageSize,
      selectedProject.value || undefined,
      selectedStatus.value || undefined
    )

    // 从分页结果中提取数据
    requirements.value = response.items || []
    pagination.value.total = response.totalCount || 0
    console.log('加载需求列表成功:', requirements.value)

    updateStatistics()
  } catch (error: any) {
    console.error('加载需求列表失败:', error)
    ElMessage.error('加载需求列表失败')
    // 确保在错误情况下也设置为空数组
    requirements.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

const loadProjects = async () => {
  try {
    const response = await ProjectService.getProjects({ pageSize: 100 }) // 获取更多项目用于下拉选择
    projects.value = response.items || []
    console.log('加载项目列表成功:', projects.value)
  } catch (error: any) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
    console.log('加载AI提供商列表成功:', aiProviders.value)
  } catch (error: any) {
    console.error('加载AI提供商列表失败:', error)
    ElMessage.error('加载AI提供商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}

const updateStatistics = () => {
  // 确保 requirements.value 是数组
  const reqs = Array.isArray(requirements.value) ? requirements.value : []

  const total = reqs.length
  const inProgress = reqs.filter(req => req.status === 'InProgress').length
  const completed = reqs.filter(req => req.status === 'Completed').length
  const pending = reqs.filter(req => req.status === 'PendingReview').length

  statistics.value = {
    totalRequirements: total,
    inProgressRequirements: inProgress,
    completedRequirements: completed,
    pendingRequirements: pending
  }
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const getProjectName = (projectId: number) => {
  const project = projects.value.find(p => p.id === projectId)
  return project?.name || '未知项目'
}

const getStatusType = (status: string): 'warning' | 'success' | 'primary' | 'info' => {
  const statusMap: Record<string, 'warning' | 'success' | 'primary' | 'info'> = {
    'Draft': 'info',
    'InProgress': 'warning',
    'Completed': 'success',
    'PendingReview': 'primary',
    'Published': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Draft': '草稿',
    'InProgress': '进行中',
    'Completed': '已完成',
    'PendingReview': '待审核',
    'Published': '已发布'
  }
  return statusMap[status] || status
}

// 事件处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleProjectChange = () => {
  pagination.value.currentPage = 1 // 重置到第一页
  loadRequirements()
}

const handleStatusChange = () => {
  pagination.value.currentPage = 1 // 重置到第一页
  loadRequirements()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedProject.value = undefined
  selectedStatus.value = ''
  loadRequirements()
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  loadRequirements()
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  loadRequirements()
}

const showCreateDialog = () => {
  createForm.value = {
    projectId: undefined,
    title: '',
    content: '',
    createMethod: 'manual',
    aiProvider: undefined
  }
  createDialogVisible.value = true
  // 加载AI提供商列表
  loadAIProviders()
}

const showImportDialog = () => {
  importForm.value = {
    projectId: undefined,
    file: null
  }
  importDialogVisible.value = true
}

const handleCreate = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()
    creating.value = true

    if (createForm.value.createMethod === 'ai') {
      // 跳转到AI对话页面，传递AI提供商配置ID
      const query = createForm.value.aiProvider ? { aiProvider: createForm.value.aiProvider } : {}
      router.push({
        path: `/requirements/${createForm.value.projectId}/chat`,
        query
      })
    } else {
      // 创建手动需求文档
      await RequirementService.createRequirementDocument({
        projectId: createForm.value.projectId!,
        title: createForm.value.title,
        content: createForm.value.content
      })

      ElMessage.success('需求文档创建成功')
      createDialogVisible.value = false
      loadRequirements()
    }
  } catch (error: any) {
    console.error('创建需求失败:', error)
    ElMessage.error('创建需求失败')
  } finally {
    creating.value = false
  }
}

const handleFileChange = (file: any) => {
  importForm.value.file = file.raw
}

const handleImport = async () => {
  if (!importForm.value.projectId || !importForm.value.file) {
    ElMessage.warning('请选择项目和上传文件')
    return
  }

  try {
    importing.value = true
    const result = await RequirementService.importRequirements(
      importForm.value.projectId,
      importForm.value.file
    )

    ElMessage.success(`成功导入 ${result.importedCount} 个需求`)
    if (result.errors.length > 0) {
      ElMessage.warning(`有 ${result.errors.length} 个错误`)
    }

    importDialogVisible.value = false
    loadRequirements()
  } catch (error: any) {
    console.error('导入需求失败:', error)
    ElMessage.error('导入需求失败')
  } finally {
    importing.value = false
  }
}

// 操作方法
const viewRequirement = (id: number) => {
  // TODO: 跳转到需求详情页面
  router.push(`/requirements/documents/${id}`)
}

const editRequirement = (id: number) => {
  // TODO: 跳转到需求编辑页面
  router.push(`/requirements/documents/${id}/edit`)
}

const handleAction = async (command: string, requirement: RequirementDocument) => {
  const [action, idStr] = command.split('-')
  const id = parseInt(idStr)

  switch (action) {
    case 'edit':
      editRequirement(id)
      break

    case 'chat':
      // 跳转到AI对话页面
      router.push(`/requirements/${requirement.projectId}/chat`)
      break

    case 'export':
      await exportRequirement(id)
      break

    case 'duplicate':
      await duplicateRequirement(requirement)
      break

    case 'delete':
      await deleteRequirement(id)
      break
  }
}

const exportRequirement = async (id: number) => {
  try {
    // 显示格式选择对话框
    await ElMessageBox.confirm(
      '请选择导出格式：\n\n1. Markdown (.md) - 推荐\n2. PDF (.pdf) - 开发中\n3. Word (.docx) - 开发中',
      '导出需求文档',
      {
        confirmButtonText: '导出 Markdown',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    ElMessage.info('正在导出文档，请稍候...')

    await RequirementService.exportRequirementDocument(
      id,
      'markdown'
    )

    ElMessage.success('文档导出成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('导出需求文档失败:', error)
      ElMessage.error('导出需求文档失败')
    }
  }
}

const duplicateRequirement = async (requirement: RequirementDocument) => {
  try {
    await RequirementService.createRequirementDocument({
      projectId: requirement.projectId,
      title: `${requirement.title} (副本)`,
      content: requirement.content,
      functionalRequirements: requirement.functionalRequirements,
      nonFunctionalRequirements: requirement.nonFunctionalRequirements,
      userStories: requirement.userStories,
      acceptanceCriteria: requirement.acceptanceCriteria
    })

    ElMessage.success('需求复制成功')
    loadRequirements()
  } catch (error: any) {
    console.error('复制需求失败:', error)
    ElMessage.error('复制需求失败')
  }
}

const deleteRequirement = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个需求文档吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await RequirementService.deleteRequirementDocument(id)
    ElMessage.success('需求删除成功')
    loadRequirements()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除需求失败:', error)
      ElMessage.error('删除需求失败')
    }
  }
}

// AI聊天相关方法
const returnToAIChat = () => {
  if (aiChatConversationId.value && aiChatMode.value) {
    // 返回到AI聊天页面，并恢复对话状态
    router.push({
      path: '/ai-chat',
      query: {
        conversationId: aiChatConversationId.value,
        mode: aiChatMode.value,
        returnFrom: 'requirements'
      }
    })
  } else {
    // 如果没有对话ID，直接跳转到需求分析模式
    router.push({
      path: '/ai-chat',
      query: {
        mode: 'requirement',
        returnFrom: 'requirements'
      }
    })
  }
}

const openAIChat = () => {
  // 打开AI聊天页面的需求分析模式
  const query: any = {
    mode: 'requirement'
  }

  // 如果有选中的项目，传递项目ID
  if (selectedProject.value) {
    query.projectId = selectedProject.value
  }

  router.push({
    path: '/ai-chat',
    query
  })
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadRequirements()
  loadAIProviders()

  // 如果是从AI聊天跳转过来且有项目ID，自动选择该项目
  if (fromAIChat.value && route.query.projectId) {
    selectedProject.value = parseInt(route.query.projectId as string)
  }
})

// 监听项目和状态变化
watch([selectedProject, selectedStatus], () => {
  loadRequirements()
})
</script>

<style lang="scss" scoped>
.requirement-list {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-content {
    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }

    .page-subtitle {
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-overview {
  margin-bottom: 24px;

  .stat-card {
    text-align: center;

    :deep(.el-statistic__content) {
      font-size: 28px;
      font-weight: 600;
    }
  }
}

.filter-card {
  margin-bottom: 24px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .filter-left {
      display: flex;
      align-items: center;
    }
  }
}

.requirements-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }

  .loading-container {
    padding: 20px;
  }
}

.list-view {
  .requirement-title {
    .requirement-meta {
      display: flex;
      gap: 8px;
      margin-top: 4px;
      font-size: 12px;
      color: var(--el-text-color-secondary);

      .project-name {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        padding: 2px 6px;
        border-radius: 4px;
      }

      .version {
        background: var(--el-color-info-light-9);
        color: var(--el-color-info);
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }
}

.grid-view {
  .requirement-card {
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    padding: 16px;
    background: var(--el-bg-color);
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 20px;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .requirement-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        line-height: 1.4;
        flex: 1;
        margin-right: 8px;
      }
    }

    .card-content {
      .requirement-meta {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 12px;

        .project-name {
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
          padding: 2px 6px;
          border-radius: 4px;
        }

        .version {
          background: var(--el-color-info-light-9);
          color: var(--el-color-info);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .requirement-status {
        display: flex;
        gap: 6px;
        margin-bottom: 8px;
      }

      .requirement-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.import-section {
  .el-upload__tip {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .requirement-list {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;

    .header-actions {
      align-self: stretch;

      .el-button {
        flex: 1;
      }
    }
  }

  .filter-section {
    flex-direction: column;
    gap: 12px;

    .filter-left {
      flex-direction: column;
      gap: 12px;
      width: 100%;

      .el-input,
      .el-select {
        width: 100% !important;
        margin-left: 0 !important;
      }
    }
  }

  .stats-overview {
    .el-col {
      margin-bottom: 12px;
    }
  }
}
</style>
