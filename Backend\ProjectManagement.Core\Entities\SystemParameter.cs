using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 系统参数实体
    /// </summary>
    [SugarTable("SystemParameters")]
    public class SystemParameter : BaseEntity
    {
        /// <summary>
        /// 参数分类
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "参数分类")]
        [Required(ErrorMessage = "参数分类不能为空")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 参数键名
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "参数键名")]
        [Required(ErrorMessage = "参数键名不能为空")]
        public string ParameterKey { get; set; } = string.Empty;

        /// <summary>
        /// 参数值
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false, ColumnDescription = "参数值")]
        [Required(ErrorMessage = "参数值不能为空")]
        public string ParameterValue { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "显示名称")]
        [Required(ErrorMessage = "显示名称不能为空")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 参数描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true, ColumnDescription = "参数描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "排序顺序")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "是否启用")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否系统参数（不可删除）
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "是否系统参数")]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 扩展属性（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "扩展属性")]
        public string? ExtendedProperties { get; set; }
    }

    /// <summary>
    /// 系统参数预定义分类（仅作为参考，不限制用户创建新分类）
    /// </summary>
    public static class SystemParameterCategory
    {
        /// <summary>
        /// 技术栈
        /// </summary>
        public const string TechnologyStack = "TechnologyStack";

        /// <summary>
        /// 优先级
        /// </summary>
        public const string Priority = "Priority";

        /// <summary>
        /// 项目状态
        /// </summary>
        public const string ProjectStatus = "ProjectStatus";

        /// <summary>
        /// 需求状态
        /// </summary>
        public const string RequirementStatus = "RequirementStatus";

        /// <summary>
        /// 步骤类型
        /// </summary>
        public const string StepType = "StepType";

        /// <summary>
        /// 组件类型
        /// </summary>
        public const string ComponentType = "ComponentType";

        /// <summary>
        /// AI提供商
        /// </summary>
        public const string AIProvider = "AIProvider";

        /// <summary>
        /// 数据库类型
        /// </summary>
        public const string DatabaseType = "DatabaseType";

        /// <summary>
        /// 原型类型
        /// </summary>
        public const string PrototypeType = "PrototypeType";

        /// <summary>
        /// 设备类型
        /// </summary>
        public const string DeviceType = "DeviceType";

        /// <summary>
        /// 保真度级别
        /// </summary>
        public const string FidelityLevel = "FidelityLevel";

        /// <summary>
        /// 消息类型
        /// </summary>
        public const string MessageType = "MessageType";

        /// <summary>
        /// 任务状态
        /// </summary>
        public const string TaskStatus = "TaskStatus";

        /// <summary>
        /// 用户角色
        /// </summary>
        public const string UserRole = "UserRole";

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public const string FileExtension = "FileExtension";

        /// <summary>
        /// 框架
        /// </summary>
        public const string Framework = "Framework";

        /// <summary>
        /// 工具
        /// </summary>
        public const string Tool = "Tool";

        /// <summary>
        /// 其他
        /// </summary>
        public const string Other = "Other";

        /// <summary>
        /// 获取预定义分类（用户可以创建新分类，不限于此列表）
        /// </summary>
        public static readonly string[] PredefinedCategories = {
            TechnologyStack, Priority, ProjectStatus, RequirementStatus,
            StepType, ComponentType, AIProvider, DatabaseType,
            PrototypeType, DeviceType, FidelityLevel, MessageType,
            TaskStatus, UserRole, FileExtension, Framework, Tool, Other
        };

        /// <summary>
        /// 获取预定义分类的显示名称映射
        /// </summary>
        public static readonly Dictionary<string, string> CategoryDisplayNames = new()
        {
            { TechnologyStack, "技术栈" },
            { Priority, "优先级" },
            { ProjectStatus, "项目状态" },
            { RequirementStatus, "需求状态" },
            { StepType, "步骤类型" },
            { ComponentType, "组件类型" },
            { AIProvider, "AI提供商" },
            { DatabaseType, "数据库类型" },
            { PrototypeType, "原型类型" },
            { DeviceType, "设备类型" },
            { FidelityLevel, "保真度级别" },
            { MessageType, "消息类型" },
            { TaskStatus, "任务状态" },
            { UserRole, "用户角色" },
            { FileExtension, "文件扩展名" },
            { Framework, "框架" },
            { Tool, "工具" },
            { Other, "其他" }
        };
    }
}
