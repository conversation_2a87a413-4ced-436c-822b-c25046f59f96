using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 上下文图实体 - 对应DatabaseSchema.sql中的ContextDiagrams表
/// 功能: 存储AI生成的系统上下文图，展示系统边界和外部实体
/// 支持: Mermaid格式、外部实体管理、数据流分析
/// </summary>
[SugarTable("ContextDiagrams", "系统上下文图管理表")]
public class ContextDiagram:BaseEntity
{


    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的需求文档ID，可为空")]
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 上下文图名称（如：电商系统上下文图）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "上下文图名称")]
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的上下文图定义代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "Mermaid格式的上下文图定义代码")]
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 外部实体信息，JSON格式存储（用户、第三方系统等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "外部实体信息，JSON格式存储")]
    public string? ExternalEntities { get; set; }

    /// <summary>
    /// 系统边界定义，JSON格式存储
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "系统边界定义，JSON格式存储")]
    public string? SystemBoundary { get; set; }

    /// <summary>
    /// 数据流信息，JSON格式存储（输入输出数据）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "数据流信息，JSON格式存储")]
    public string? DataFlows { get; set; }

    /// <summary>
    /// 上下文图版本号（如：1.0, 1.1, 2.0）
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "上下文图版本号")]
    public string DiagramVersion { get; set; } = "1.0";





    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的需求文档 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RequirementDocument? RequirementDocument { get; set; }
}
