using Microsoft.AspNetCore.Mvc;
using ProjectManagement.API.Models.AutomationTask;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Text.Json;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 自动化任务API控制器
/// 为本地自动化客户端提供任务管理接口
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class AutomationTaskController : ControllerBase
{
    private readonly IAutomationTaskRepository _taskRepository;
    private readonly ILogger<AutomationTaskController> _logger;

    public AutomationTaskController(
        IAutomationTaskRepository taskRepository,
        ILogger<AutomationTaskController> logger)
    {
        _taskRepository = taskRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取待处理任务
    /// </summary>
    /// <param name="request">获取任务请求</param>
    /// <returns>待处理任务列表</returns>
    [HttpPost("pending")]
    public async Task<ActionResult<List<AutomationTaskDto>>> GetPendingTasks([FromBody] GetTasksRequest request)
    {
        try
        {
            _logger.LogInformation("客户端 {ClientId} 请求获取任务，类型: {TaskTypes}, 数量: {MaxCount}",
                request.ClientId, string.Join(",", request.TaskTypes ?? Array.Empty<string>()), request.MaxCount);

            var tasks = await _taskRepository.GetPendingTasksAsync(
                request.ClientId,
                request.TaskTypes,
                request.MaxCount);

            var result = tasks.Select(MapToDto).ToList();

            _logger.LogInformation("返回 {Count} 个待处理任务给客户端 {ClientId}", result.Count, request.ClientId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取待处理任务失败，客户端: {ClientId}", request.ClientId);
            return StatusCode(500, new { message = "获取任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 分配任务给客户端
    /// </summary>
    /// <param name="request">分配任务请求</param>
    /// <returns>分配结果</returns>
    [HttpPost("assign")]
    public async Task<ActionResult<bool>> AssignTask([FromBody] AssignTaskRequest request)
    {
        try
        {
            var success = await _taskRepository.AssignTaskAsync(request.TaskId, request.ClientId);

            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 成功分配给客户端 {ClientId}", request.TaskId, request.ClientId);
                return Ok(new { success = true, message = "任务分配成功" });
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 分配失败，可能已被其他客户端分配", request.TaskId);
                return BadRequest(new { success = false, message = "任务分配失败，可能已被其他客户端分配" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分配任务失败，TaskId: {TaskId}, ClientId: {ClientId}", request.TaskId, request.ClientId);
            return StatusCode(500, new { success = false, message = "分配任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 批量分配任务给客户端
    /// </summary>
    /// <param name="request">批量分配任务请求</param>
    /// <returns>分配结果</returns>
    [HttpPost("assign-batch")]
    public async Task<ActionResult<int>> AssignTasks([FromBody] AssignTasksRequest request)
    {
        try
        {
            var assignedCount = await _taskRepository.AssignTasksAsync(request.TaskIds, request.ClientId);

            _logger.LogInformation("批量分配任务成功，分配数量: {Count}/{Total}, 客户端: {ClientId}",
                assignedCount, request.TaskIds.Count, request.ClientId);

            return Ok(new {
                success = true,
                assignedCount = assignedCount,
                totalRequested = request.TaskIds.Count,
                message = $"成功分配 {assignedCount} 个任务"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量分配任务失败，ClientId: {ClientId}", request.ClientId);
            return StatusCode(500, new { success = false, message = "批量分配任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 开始执行任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>开始结果</returns>
    [HttpPost("{taskId}/start")]
    public async Task<ActionResult<bool>> StartTask(int taskId, [FromQuery] string clientId)
    {
        try
        {
            var success = await _taskRepository.StartTaskAsync(taskId, clientId);

            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 开始执行，客户端: {ClientId}", taskId, clientId);
                return Ok(new { success = true, message = "任务开始执行" });
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 开始执行失败，状态或分配不匹配", taskId);
                return BadRequest(new { success = false, message = "任务开始执行失败，状态或分配不匹配" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始执行任务失败，TaskId: {TaskId}, ClientId: {ClientId}", taskId, clientId);
            return StatusCode(500, new { success = false, message = "开始执行任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 更新任务状态
    /// </summary>
    /// <param name="request">状态更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPost("status")]
    public async Task<ActionResult<bool>> UpdateTaskStatus([FromBody] UpdateTaskStatusRequest request)
    {
        try
        {
            bool success = false;

            switch (request.Status.ToLower())
            {
                case "inprogress":
                    success = await _taskRepository.StartTaskAsync(request.TaskId, request.ClientId);
                    break;

                case "completed":
                    success = await _taskRepository.CompleteTaskAsync(request.TaskId, request.Result ?? "", request.ClientId);
                    break;

                case "failed":
                    success = await _taskRepository.FailTaskAsync(request.TaskId, request.ErrorMessage ?? "", request.ClientId, request.ShouldRetry);
                    break;

                case "cancelled":
                    success = await _taskRepository.CancelTaskAsync(request.TaskId, request.ErrorMessage ?? "客户端取消");
                    break;

                default:
                    return BadRequest(new { success = false, message = $"不支持的状态: {request.Status}" });
            }

            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 状态更新为 {Status}，客户端: {ClientId}",
                    request.TaskId, request.Status, request.ClientId);
                return Ok(new { success = true, message = "状态更新成功" });
            }
            else
            {
                _logger.LogWarning("任务 {TaskId} 状态更新失败，状态: {Status}", request.TaskId, request.Status);
                return BadRequest(new { success = false, message = "状态更新失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新任务状态失败，TaskId: {TaskId}, Status: {Status}", request.TaskId, request.Status);
            return StatusCode(500, new { success = false, message = "更新任务状态失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 创建新任务
    /// </summary>
    /// <param name="request">创建任务请求</param>
    /// <returns>创建的任务</returns>
    [HttpPost]
    public async Task<ActionResult<AutomationTaskDto>> CreateTask([FromBody] CreateTaskRequest request)
    {
        try
        {
            var task = new AutomationTask
            {
                ProjectId = request.ProjectId,
                SourceType = request.SourceType,
                SourceId = request.SourceId,
                TaskType = request.TaskType,
                TaskName = request.TaskName,
                Description = request.Description,
                TaskData = request.TaskData,
                Priority = request.Priority,
                TimeoutMinutes = request.TimeoutMinutes,
                Dependencies = request.Dependencies?.Count > 0 ? JsonSerializer.Serialize(request.Dependencies) : null,
                Tags = request.Tags,
                Status = AutomationTaskStatus.Pending,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };

            var createdTask = await _taskRepository.AddAsync(task);

            _logger.LogInformation("创建任务成功，TaskId: {TaskId}, 名称: {TaskName}", createdTask.Id, createdTask.TaskName);
            return Ok(MapToDto(createdTask));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建任务失败，任务名称: {TaskName}", request.TaskName);
            return StatusCode(500, new { message = "创建任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取任务详情
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务详情</returns>
    [HttpGet("{taskId}")]
    public async Task<ActionResult<AutomationTaskDto>> GetTask(int taskId)
    {
        try
        {
            var task = await _taskRepository.GetByIdAsync(taskId);
            if (task == null)
            {
                return NotFound(new { message = "任务不存在" });
            }

            return Ok(MapToDto(task));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务详情失败，TaskId: {TaskId}", taskId);
            return StatusCode(500, new { message = "获取任务详情失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取项目任务列表
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="status">状态过滤</param>
    /// <param name="taskType">任务类型过滤</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页任务列表</returns>
    [HttpGet("project/{projectId}")]
    public async Task<ActionResult<PagedTasksResponse>> GetProjectTasks(
        int projectId,
        [FromQuery] string? status = null,
        [FromQuery] string? taskType = null,
        [FromQuery] int pageIndex = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var (items, totalCount) = await _taskRepository.GetProjectTasksAsync(
                projectId, status, taskType, pageIndex, pageSize);

            var response = new PagedTasksResponse
            {
                Items = items.Select(MapToDto).ToList(),
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目任务列表失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { message = "获取项目任务列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取客户端任务列表
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="status">状态过滤</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页任务列表</returns>
    [HttpGet("client/{clientId}")]
    public async Task<ActionResult<PagedTasksResponse>> GetClientTasks(
        string clientId,
        [FromQuery] string? status = null,
        [FromQuery] int pageIndex = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var (items, totalCount) = await _taskRepository.GetClientTasksAsync(
                clientId, status, pageIndex, pageSize);

            var response = new PagedTasksResponse
            {
                Items = items.Select(MapToDto).ToList(),
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户端任务列表失败，ClientId: {ClientId}", clientId);
            return StatusCode(500, new { message = "获取客户端任务列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <param name="projectId">项目ID（可选）</param>
    /// <returns>任务统计</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<TaskStatisticsResponse>> GetTaskStatistics([FromQuery] int? projectId = null)
    {
        try
        {
            var statusCounts = await _taskRepository.GetTaskStatisticsAsync(projectId);

            // 计算今日完成和失败任务数（这里简化处理，实际可能需要更复杂的查询）
            var totalTasks = statusCounts.Values.Sum();
            var todayCompleted = statusCounts.GetValueOrDefault(AutomationTaskStatus.Completed, 0);
            var todayFailed = statusCounts.GetValueOrDefault(AutomationTaskStatus.Failed, 0);

            var response = new TaskStatisticsResponse
            {
                StatusCounts = statusCounts,
                TotalTasks = totalTasks,
                ActiveClients = 0, // TODO: 实现活跃客户端统计
                TodayCompleted = todayCompleted,
                TodayFailed = todayFailed
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务统计失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { message = "获取任务统计失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 取消任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="reason">取消原因</param>
    /// <returns>取消结果</returns>
    [HttpPost("{taskId}/cancel")]
    public async Task<ActionResult<bool>> CancelTask(int taskId, [FromQuery] string reason = "手动取消")
    {
        try
        {
            var success = await _taskRepository.CancelTaskAsync(taskId, reason);

            if (success)
            {
                _logger.LogInformation("任务 {TaskId} 已取消，原因: {Reason}", taskId, reason);
                return Ok(new { success = true, message = "任务已取消" });
            }
            else
            {
                return BadRequest(new { success = false, message = "取消任务失败，任务可能不存在或状态不允许取消" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消任务失败，TaskId: {TaskId}", taskId);
            return StatusCode(500, new { success = false, message = "取消任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取可执行的任务（依赖已满足）
    /// </summary>
    /// <param name="projectId">项目ID（可选）</param>
    /// <param name="maxCount">最大返回数量</param>
    /// <returns>可执行任务列表</returns>
    [HttpGet("executable")]
    public async Task<ActionResult<List<AutomationTaskDto>>> GetExecutableTasks(
        [FromQuery] int? projectId = null,
        [FromQuery] int maxCount = 10)
    {
        try
        {
            var tasks = await _taskRepository.GetExecutableTasksAsync(projectId, maxCount);
            var result = tasks.Select(MapToDto).ToList();

            _logger.LogInformation("返回 {Count} 个可执行任务，项目: {ProjectId}", result.Count, projectId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可执行任务失败，ProjectId: {ProjectId}", projectId);
            return StatusCode(500, new { message = "获取可执行任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 检查任务依赖是否满足
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>依赖检查结果</returns>
    [HttpGet("{taskId}/dependencies")]
    public async Task<ActionResult<bool>> CheckTaskDependencies(int taskId)
    {
        try
        {
            var satisfied = await _taskRepository.CheckDependenciesAsync(taskId);
            return Ok(new { taskId = taskId, dependenciesSatisfied = satisfied });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查任务依赖失败，TaskId: {TaskId}", taskId);
            return StatusCode(500, new { message = "检查任务依赖失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 重置超时任务
    /// </summary>
    /// <returns>重置结果</returns>
    [HttpPost("reset-timeout")]
    public async Task<ActionResult<int>> ResetTimeoutTasks()
    {
        try
        {
            var timeoutTasks = await _taskRepository.GetTimeoutTasksAsync();
            if (timeoutTasks.Count == 0)
            {
                return Ok(new { resetCount = 0, message = "没有超时任务需要重置" });
            }

            var taskIds = timeoutTasks.Select(t => t.Id).ToList();
            var resetCount = await _taskRepository.ResetTimeoutTasksAsync(taskIds);

            _logger.LogInformation("重置了 {Count} 个超时任务", resetCount);
            return Ok(new { resetCount = resetCount, message = $"成功重置 {resetCount} 个超时任务" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置超时任务失败");
            return StatusCode(500, new { message = "重置超时任务失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 客户端心跳检测
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="capabilities">客户端能力</param>
    /// <returns>心跳响应</returns>
    [HttpPost("heartbeat")]
    public async Task<ActionResult> Heartbeat([FromQuery] string clientId, [FromBody] ClientCapabilities? capabilities = null)
    {
        try
        {
            _logger.LogDebug("收到客户端 {ClientId} 心跳", clientId);

            // TODO: 实现客户端状态管理
            // 可以在这里记录客户端的最后活跃时间、能力等信息

            return Ok(new
            {
                success = true,
                serverTime = DateTime.UtcNow,
                message = "心跳正常",
                recommendations = new
                {
                    maxConcurrentTasks = capabilities?.MaxConcurrentTasks ?? 1,
                    preferredTaskTypes = capabilities?.SupportedTaskTypes ?? new[] { "CodeGeneration" }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端心跳失败，ClientId: {ClientId}", clientId);
            return StatusCode(500, new { message = "心跳处理失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 将实体转换为DTO
    /// </summary>
    /// <param name="task">任务实体</param>
    /// <returns>任务DTO</returns>
    private static AutomationTaskDto MapToDto(AutomationTask task)
    {
        return new AutomationTaskDto
        {
            Id = task.Id,
            ProjectId = task.ProjectId,
            SourceType = task.SourceType,
            SourceId = task.SourceId,
            TaskType = task.TaskType,
            TaskName = task.TaskName,
            Description = task.Description,
            TaskData = task.TaskData,
            Status = task.Status,
            Priority = task.Priority,
            AssignedTo = task.AssignedTo,
            CreatedTime = task.CreatedTime,
            StartedTime = task.StartedTime,
            CompletedTime = task.CompletedTime,
            Result = task.Result,
            ErrorMessage = task.ErrorMessage,
            RetryCount = task.RetryCount,
            MaxRetries = task.MaxRetries,
            TimeoutMinutes = task.TimeoutMinutes,
            Dependencies = task.Dependencies,
            Tags = task.Tags
        };
    }
}

/// <summary>
/// 客户端能力描述
/// </summary>
public class ClientCapabilities
{
    /// <summary>
    /// 支持的任务类型
    /// </summary>
    public string[] SupportedTaskTypes { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 最大并发任务数
    /// </summary>
    public int MaxConcurrentTasks { get; set; } = 1;

    /// <summary>
    /// 客户端版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 运行环境信息
    /// </summary>
    public string Environment { get; set; } = string.Empty;
}
