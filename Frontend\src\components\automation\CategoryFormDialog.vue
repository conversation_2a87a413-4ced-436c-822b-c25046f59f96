<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑分类' : '创建分类'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入分类名称"
          maxlength="100"
          show-word-limit
          :disabled="isSystemCategory"
        />
      </el-form-item>

      <el-form-item label="分类描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入分类描述"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="父分类" prop="parentId">
        <el-select
          v-model="form.parentId"
          placeholder="请选择父分类（可选）"
          clearable
          style="width: 100%"
          :disabled="isSystemCategory"
        >
          <el-option
            v-for="category in availableParentCategories"
            :key="category.id"
            :label="getCategoryDisplayName(category)"
            :value="category.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分类图标" prop="icon">
        <div class="icon-selector">
          <el-input
            v-model="form.icon"
            placeholder="选择或输入图标"
            style="width: 200px"
          >
            <template #prepend>
              <el-icon class="icon-preview">
                <component :is="form.icon || 'FolderOpened'" />
              </el-icon>
            </template>
          </el-input>
          <el-button @click="showIconPicker = !showIconPicker">
            选择图标
          </el-button>
        </div>

        <!-- 图标选择器 -->
        <div v-if="showIconPicker" class="icon-picker">
          <div class="icon-grid">
            <div
              v-for="iconItem in predefinedIcons"
              :key="iconItem.icon"
              class="icon-item"
              :class="{ active: form.icon === iconItem.icon }"
              @click="selectIcon(iconItem.icon)"
              :title="iconItem.name"
            >
              <el-icon>
                <component :is="iconItem.icon" />
              </el-icon>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="分类颜色" prop="color">
        <div class="color-selector">
          <el-color-picker
            v-model="form.color"
            :predefine="predefinedColors.map(c => c.color)"
            show-alpha
          />
          <span class="color-preview" :style="{ color: form.color }">
            <el-icon style="margin-right: 4px;">
              <component :is="form.icon || 'FolderOpened'" />
            </el-icon>
            {{ form.name || '分类名称' }}
          </span>
        </div>
      </el-form-item>

      <el-form-item label="排序顺序" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="0"
          :max="9999"
          placeholder="排序顺序"
          style="width: 200px"
        />
        <span class="form-tip">数值越小排序越靠前</span>
      </el-form-item>

      <el-form-item label="启用状态" prop="isEnabled">
        <el-switch
          v-model="form.isEnabled"
          active-text="启用"
          inactive-text="禁用"
          :disabled="isSystemCategory"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  FolderOpened, Operation, Menu, ChatDotRound, Edit, Picture, Document,
  DataLine, Tools, Box, Cpu, Lightning, Reading, Setting, User, Star,
  MagicStick, Link
} from '@element-plus/icons-vue'
import CustomTemplateCategoryService, {
  type CustomTemplateCategory,
  type CreateCustomTemplateCategoryDto,
  type UpdateCustomTemplateCategoryDto
} from '@/services/customTemplateCategory'

// Props
interface Props {
  modelValue: boolean
  category?: CustomTemplateCategory | null
  parentCategory?: CustomTemplateCategory | null
  allCategories: CustomTemplateCategory[]
}

const props = withDefaults(defineProps<Props>(), {
  category: null,
  parentCategory: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const showIconPicker = ref(false)

const form = reactive({
  name: '',
  description: '',
  parentId: undefined as number | undefined,
  icon: '',
  color: '#409EFF',
  sortOrder: 0,
  isEnabled: true
})

// 预定义图标和颜色
const predefinedIcons = CustomTemplateCategoryService.getPredefinedIcons()
const predefinedColors = CustomTemplateCategoryService.getPredefinedColors()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.category)
const isSystemCategory = computed(() => props.category?.isSystem || false)

const availableParentCategories = computed(() => {
  let categories = props.allCategories.filter(c => c.isEnabled)

  // 编辑时排除自己和自己的子分类
  if (props.category) {
    categories = categories.filter(c => {
      if (c.id === props.category!.id) return false
      return !isDescendantOf(c, props.category!)
    })
  }

  return categories
})

// 验证函数
const validateCategoryName = (rule: any, value: string, callback: any) => {
  const validation = CustomTemplateCategoryService.validateCategoryName(value)
  if (!validation.valid) {
    callback(new Error(validation.message))
  } else {
    callback()
  }
}

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 100, message: '分类名称长度在 1 到 100 个字符', trigger: 'blur' },
    { validator: validateCategoryName, trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '分类描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序顺序必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 监听器
watch(() => props.modelValue, (visible) => {
  if (visible) {
    initForm()
  } else {
    resetForm()
  }
})

watch(() => props.parentCategory, (parent) => {
  if (parent && !isEdit.value) {
    form.parentId = parent.id
  }
})

// 方法
const initForm = () => {
  if (props.category) {
    // 编辑模式
    Object.assign(form, {
      name: props.category.name,
      description: props.category.description || '',
      parentId: props.category.parentId,
      icon: props.category.icon || '',
      color: props.category.color || '#409EFF',
      sortOrder: props.category.sortOrder,
      isEnabled: props.category.isEnabled
    })
  } else {
    // 创建模式
    resetForm()
    if (props.parentCategory) {
      form.parentId = props.parentCategory.id
    }
  }
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    parentId: undefined,
    icon: '',
    color: '#409EFF',
    sortOrder: 0,
    isEnabled: true
  })
  showIconPicker.value = false
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const selectIcon = (icon: string) => {
  form.icon = icon
  showIconPicker.value = false
}

const getCategoryDisplayName = (category: CustomTemplateCategory): string => {
  const path = buildCategoryPath(category, props.allCategories)
  return path.join(' / ')
}

const buildCategoryPath = (category: CustomTemplateCategory, allCategories: CustomTemplateCategory[]): string[] => {
  const path: string[] = []
  let current: CustomTemplateCategory | undefined = category

  while (current) {
    path.unshift(current.name)
    current = current.parentId ? allCategories.find(c => c.id === current!.parentId) : undefined
  }

  return path
}

const isDescendantOf = (category: CustomTemplateCategory, ancestor: CustomTemplateCategory): boolean => {
  let current = category
  while (current.parentId) {
    if (current.parentId === ancestor.id) {
      return true
    }
    current = props.allCategories.find(c => c.id === current.parentId)!
    if (!current) break
  }
  return false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      // 更新分类
      const dto: UpdateCustomTemplateCategoryDto = {
        name: form.name,
        description: form.description || undefined,
        parentId: form.parentId,
        icon: form.icon || undefined,
        color: form.color,
        sortOrder: form.sortOrder,
        isEnabled: form.isEnabled
      }
      await CustomTemplateCategoryService.updateCategory(props.category!.id, dto)
      ElMessage.success('分类更新成功')
    } else {
      // 创建分类
      const dto: CreateCustomTemplateCategoryDto = {
        name: form.name,
        description: form.description || undefined,
        parentId: form.parentId,
        icon: form.icon || undefined,
        color: form.color,
        sortOrder: form.sortOrder,
        isEnabled: form.isEnabled
      }
      await CustomTemplateCategoryService.createCategory(dto)
      ElMessage.success('分类创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error('保存分类失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.icon-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-preview {
  font-size: 16px;
  width: 24px;
  text-align: center;
}

.icon-picker {
  margin-top: 12px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fafafa;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.icon-item {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.icon-item:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.icon-item.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.color-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-preview {
  font-size: 16px;
  font-weight: 500;
}

.form-tip {
  margin-left: 12px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
