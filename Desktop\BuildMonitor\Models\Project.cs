using System;
using SqlSugar;

namespace BuildMonitor.Models
{
    /// <summary>
    /// 项目模型
    /// </summary>
    [SugarTable("Projects")]
    public class Project
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 项目描述
        /// </summary>
        [SugarColumn(ColumnDataType = "NVARCHAR(MAX)", IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 项目代码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目负责人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? OwnerId { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Status { get; set; } = "Planning";

        /// <summary>
        /// 优先级
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        public string Priority { get; set; } = "Medium";

        /// <summary>
        /// 开始日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 进度百分比
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public decimal Progress { get; set; } = 0;

        /// <summary>
        /// 预估工时
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? EstimatedHours { get; set; }

        /// <summary>
        /// 实际工时
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? ActualHours { get; set; }

        /// <summary>
        /// 预算
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public decimal? Budget { get; set; }

        /// <summary>
        /// 技术栈
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? TechnologyStack { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? DeletedTime { get; set; }

        /// <summary>
        /// 删除者ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DeletedBy { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Version { get; set; } = 1;

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 显示名称（用于ComboBox显示）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DisplayName => $"{Name} ({ProjectCode})";
    }
}
