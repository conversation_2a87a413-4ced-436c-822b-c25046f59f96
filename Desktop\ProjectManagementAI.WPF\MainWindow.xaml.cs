using System.Windows;
using ProjectManagementAI.WPF.ViewModels;

namespace ProjectManagementAI.WPF
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            // DataContext 将通过 App.xaml.cs 中的依赖注入设置
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 窗口加载完成后的初始化操作
            if (DataContext is MainViewModel viewModel)
            {
                await viewModel.InitializeAsync();
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 窗口关闭前的清理操作
            if (DataContext is MainViewModel viewModel)
            {
                viewModel.Cleanup();
            }
        }
    }
}
