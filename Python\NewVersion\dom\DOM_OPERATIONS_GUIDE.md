# VSCode DOM操作指南

## 基本使用方法

```python
# 1. 创建脚本实例
script = MyVSCodeScript()

# 2. 连接到VSCode
script.connect()

# 3. 执行JavaScript
result = script.execute_js("您的JavaScript代码")

# 4. 断开连接
script.disconnect()
```

## 常用DOM操作

### 1. 查找元素

```javascript
// 查找单个元素
const element = document.querySelector('.class-name');
const element = document.querySelector('#id-name');
const element = document.querySelector('tag-name');

// 查找多个元素
const elements = document.querySelectorAll('.class-name');

// 检查元素是否存在
const exists = !!document.querySelector('.class-name');
```

### 2. 点击操作

```javascript
// 点击按钮
const button = document.querySelector('button');
if (button) {
    button.click();
}

// 点击特定文本的按钮
const buttons = Array.from(document.querySelectorAll('button'));
const targetButton = buttons.find(btn => btn.textContent.includes('发送'));
if (targetButton) {
    targetButton.click();
}
```

### 3. 输入文本

```javascript
// 在输入框中输入文本
const input = document.querySelector('input');
if (input) {
    input.value = '您的文本';
    input.dispatchEvent(new Event('input', { bubbles: true }));
}

// 在textarea中输入文本
const textarea = document.querySelector('textarea');
if (textarea) {
    textarea.value = '您的文本';
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));
}
```

### 4. 获取信息

```javascript
// 获取文本内容
const text = element.textContent;
const innerText = element.innerText;

// 获取属性值
const value = element.value;
const className = element.className;
const id = element.id;

// 获取元素位置和大小
const rect = element.getBoundingClientRect();
```

### 5. 键盘事件

```javascript
// 模拟按键
const enterEvent = new KeyboardEvent('keydown', {
    key: 'Enter',
    code: 'Enter',
    keyCode: 13,
    bubbles: true
});
element.dispatchEvent(enterEvent);

// Ctrl+S 保存
const saveEvent = new KeyboardEvent('keydown', {
    key: 's',
    code: 'KeyS',
    ctrlKey: true,
    bubbles: true
});
document.dispatchEvent(saveEvent);
```

### 6. 等待元素

```javascript
// 等待元素出现
const waitForElement = async (selector, timeout = 5000) => {
    return new Promise((resolve) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }
        
        const observer = new MutationObserver(() => {
            const element = document.querySelector(selector);
            if (element) {
                observer.disconnect();
                resolve(element);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        setTimeout(() => {
            observer.disconnect();
            resolve(null);
        }, timeout);
    });
};

// 使用方法
const element = await waitForElement('.my-element');
```

## VSCode特定选择器

### Copilot相关
```javascript
// Copilot聊天面板
'#workbench\\.panel\\.chat'

// Copilot输入框
'textarea.inputarea.monaco-mouse-cursor-text'
'textarea[data-mprt="7"]'

// Copilot发送按钮
'a.action-label.codicon.codicon-send'

// 聊天消息
'.monaco-list-row'
```

### 编辑器相关
```javascript
// Monaco编辑器
'.monaco-editor'
'.monaco-editor.focused'

// 编辑器内容
'.view-lines'
'.view-line'

// 文件标签
'.tab'
'.tab.active'
```

### 界面元素
```javascript
// 侧边栏
'.sidebar'
'.activitybar'

// 面板
'.panel'

// 终端
'.terminal'

// 命令面板
'.quick-input-widget'
```

## 完整示例

### 示例1：发送消息到Copilot

```python
def send_copilot_message(self, message):
    js_code = f'''
    (() => {{
        // 查找输入框
        const input = document.querySelector('textarea.inputarea.monaco-mouse-cursor-text');
        if (!input) {{
            return {{ success: false, error: "输入框未找到" }};
        }}
        
        // 输入文本
        input.value = '{message}';
        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
        
        // 查找发送按钮
        const sendBtn = document.querySelector('a.action-label.codicon.codicon-send');
        if (sendBtn) {{
            sendBtn.click();
            return {{ success: true }};
        }}
        
        return {{ success: false, error: "发送按钮未找到" }};
    }})()
    '''
    
    return self.execute_js(js_code)
```

### 示例2：获取编辑器内容

```python
def get_editor_content(self):
    js_code = '''
    (() => {
        const editor = document.querySelector('.monaco-editor.focused .view-lines');
        if (editor) {
            return {
                success: true,
                content: editor.textContent
            };
        }
        return { success: false, error: "编辑器未找到" };
    })()
    '''
    
    result = self.execute_js(js_code)
    if result and result.get('success'):
        return result.get('content', '')
    return ''
```

### 示例3：点击菜单项

```python
def click_menu_item(self, menu_text):
    js_code = f'''
    (() => {{
        const menuItems = Array.from(document.querySelectorAll('.action-label, .menu-item'));
        const targetItem = menuItems.find(item => 
            item.textContent?.includes('{menu_text}') ||
            item.title?.includes('{menu_text}')
        );
        
        if (targetItem) {{
            targetItem.click();
            return {{ success: true }};
        }}
        
        return {{ success: false, error: "菜单项未找到: {menu_text}" }};
    }})()
    '''
    
    return self.execute_js(js_code)
```

## 调试技巧

1. **在浏览器开发者工具中测试JavaScript**
2. **使用console.log()输出调试信息**
3. **检查元素的实际选择器**
4. **使用try-catch处理异常**

```javascript
try {
    const element = document.querySelector('.my-element');
    if (element) {
        element.click();
        return { success: true };
    }
    return { success: false, error: "元素未找到" };
} catch (error) {
    return { success: false, error: error.message };
}
```
