namespace ProjectManagement.API.Models.AutomationTask;

/// <summary>
/// 自动化任务DTO
/// </summary>
public class AutomationTaskDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 任务来源类型
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// 任务来源ID
    /// </summary>
    public int? SourceId { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 任务描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 任务数据（JSON格式）
    /// </summary>
    public string? TaskData { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public string Priority { get; set; } = string.Empty;

    /// <summary>
    /// 分配给的客户端
    /// </summary>
    public string? AssignedTo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartedTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedTime { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 超时时间（分钟）
    /// </summary>
    public int? TimeoutMinutes { get; set; }

    /// <summary>
    /// 依赖任务ID列表
    /// </summary>
    public string? Dependencies { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public string? Tags { get; set; }
}

/// <summary>
/// 任务获取请求
/// </summary>
public class GetTasksRequest
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 支持的任务类型
    /// </summary>
    public string[]? TaskTypes { get; set; }

    /// <summary>
    /// 最大获取数量
    /// </summary>
    public int MaxCount { get; set; } = 5;

    /// <summary>
    /// 项目ID过滤
    /// </summary>
    public int? ProjectId { get; set; }
}

/// <summary>
/// 任务分配请求
/// </summary>
public class AssignTaskRequest
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int TaskId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;
}

/// <summary>
/// 批量任务分配请求
/// </summary>
public class AssignTasksRequest
{
    /// <summary>
    /// 任务ID列表
    /// </summary>
    public List<int> TaskIds { get; set; } = new();

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;
}

/// <summary>
/// 任务状态更新请求
/// </summary>
public class UpdateTaskStatusRequest
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int TaskId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 新状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 执行结果（完成时）
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 错误信息（失败时）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否应该重试（失败时）
    /// </summary>
    public bool ShouldRetry { get; set; } = true;
}

/// <summary>
/// 创建任务请求
/// </summary>
public class CreateTaskRequest
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 任务来源类型
    /// </summary>
    public string SourceType { get; set; } = "Manual";

    /// <summary>
    /// 任务来源ID
    /// </summary>
    public int? SourceId { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 任务描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 任务数据（JSON格式）
    /// </summary>
    public string? TaskData { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// 超时时间（分钟）
    /// </summary>
    public int? TimeoutMinutes { get; set; }

    /// <summary>
    /// 依赖任务ID列表
    /// </summary>
    public List<int>? Dependencies { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public string? Tags { get; set; }
}

/// <summary>
/// 任务统计响应
/// </summary>
public class TaskStatisticsResponse
{
    /// <summary>
    /// 各状态任务数量
    /// </summary>
    public Dictionary<string, int> StatusCounts { get; set; } = new();

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 活跃客户端数量
    /// </summary>
    public int ActiveClients { get; set; }

    /// <summary>
    /// 今日完成任务数
    /// </summary>
    public int TodayCompleted { get; set; }

    /// <summary>
    /// 今日失败任务数
    /// </summary>
    public int TodayFailed { get; set; }
}

/// <summary>
/// 分页任务响应
/// </summary>
public class PagedTasksResponse
{
    /// <summary>
    /// 任务列表
    /// </summary>
    public List<AutomationTaskDto> Items { get; set; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }
}
