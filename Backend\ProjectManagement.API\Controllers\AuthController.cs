using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Cryptography;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 认证授权控制器
/// 功能: 处理用户登录、注册、令牌刷新等认证相关操作
/// 支持: JWT令牌、刷新令牌、密码加密、用户验证
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthController> _logger;

    public AuthController(
        IUserRepository userRepository,
        IConfiguration configuration,
        ILogger<AuthController> logger)
    {
        _userRepository = userRepository;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录响应，包含访问令牌和刷新令牌</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto request)
    {
        try
        {
            _logger.LogInformation("用户尝试登录: {Username}", request.Username);

            // 验证用户凭据
            var user = await ValidateUserCredentials(request.Username, request.Password);
            if (user == null)
            {
                _logger.LogWarning("用户登录失败: {Username} - 无效的用户名或密码", request.Username);
                return Unauthorized(new { message = "用户名或密码错误" });
            }

            // 检查用户状态
            if (user.Status != 1)
            {
                _logger.LogWarning("用户登录失败: {Username} - 账户已禁用", request.Username);
                return Unauthorized(new { message = "账户已被禁用" });
            }

            // 生成JWT令牌
            var accessToken = GenerateAccessToken(user);
            var refreshToken = GenerateRefreshToken();

            // 更新用户最后登录时间和刷新令牌
            user.LastLoginAt = DateTime.UtcNow;
            user.RefreshToken = refreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(GetRefreshTokenExpirationDays());

            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("用户登录成功: {Username} (ID: {UserId})", user.Username, user.Id);

            return Ok(new LoginResponseDto
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = GetAccessTokenExpirationMinutes() * 60,
                TokenType = "Bearer",
                User = new UserInfoDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    RealName = user.RealName,
                    Role = user.Role,
                    Avatar = user.Avatar
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录过程中发生错误: {Username}", request.Username);
            return StatusCode(500, new { message = "登录过程中发生错误" });
        }
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <returns>注册响应</returns>
    [HttpPost("register")]
    [AllowAnonymous]
    public async Task<ActionResult<RegisterResponseDto>> Register([FromBody] RegisterRequestDto request)
    {
        try
        {
            _logger.LogInformation("新用户尝试注册: {Username}, {Email}", request.Username, request.Email);

            // 检查用户名是否已存在
            var existingUser = await _userRepository.GetByUsernameAsync(request.Username);
            if (existingUser != null)
            {
                return BadRequest(new { message = "用户名已存在" });
            }

            // 检查邮箱是否已存在
            existingUser = await _userRepository.GetByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return BadRequest(new { message = "邮箱已被注册" });
            }

            // 创建新用户
            var user = new User
            {
                Username = request.Username,
                Email = request.Email,
                RealName = string.IsNullOrWhiteSpace(request.RealName) ? null : request.RealName,
                PasswordHash = HashPassword(request.Password),
                Role = "User", // 默认角色
                Status = 1, // 激活状态
                CreatedTime = DateTime.UtcNow,
                CreatedBy = null // 自注册
            };

            var createdUser = await _userRepository.AddAsync(user);

            _logger.LogInformation("新用户注册成功: {Username} (ID: {UserId})", createdUser.Username, createdUser.Id);

            return Ok(new RegisterResponseDto
            {
                Success = true,
                Message = "注册成功",
                UserId = createdUser.Id
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册过程中发生错误: {Username}", request.Username);
            return StatusCode(500, new { message = "注册过程中发生错误" });
        }
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <param name="request">刷新令牌请求</param>
    /// <returns>新的访问令牌</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    public async Task<ActionResult<RefreshTokenResponseDto>> RefreshToken([FromBody] RefreshTokenRequestDto request)
    {
        try
        {
            _logger.LogInformation("尝试刷新令牌");

            // 验证刷新令牌
            var user = await _userRepository.GetByRefreshTokenAsync(request.RefreshToken);
            if (user == null || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
            {
                _logger.LogWarning("无效的刷新令牌");
                return Unauthorized(new { message = "无效的刷新令牌" });
            }

            // 生成新的访问令牌
            var newAccessToken = GenerateAccessToken(user);
            var newRefreshToken = GenerateRefreshToken();

            // 更新刷新令牌
            user.RefreshToken = newRefreshToken;
            user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(GetRefreshTokenExpirationDays());

            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("令牌刷新成功: 用户 {UserId}", user.Id);

            return Ok(new RefreshTokenResponseDto
            {
                AccessToken = newAccessToken,
                RefreshToken = newRefreshToken,
                ExpiresIn = GetAccessTokenExpirationMinutes() * 60,
                TokenType = "Bearer"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌过程中发生错误");
            return StatusCode(500, new { message = "刷新令牌过程中发生错误" });
        }
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <returns>登出响应</returns>
    [HttpPost("logout")]
    public async Task<ActionResult> Logout()
    {
        try
        {
            // 尝试获取用户ID，如果token过期则忽略
            var userId = TryGetCurrentUserId();

            if (userId.HasValue)
            {
                _logger.LogInformation("用户登出: {UserId}", userId);

                // 清除刷新令牌
                var user = await _userRepository.GetByIdAsync(userId.Value);
                if (user != null)
                {
                    user.RefreshToken = null;
                    user.RefreshTokenExpiryTime = null;
                    await _userRepository.UpdateAsync(user);
                }
            }
            else
            {
                _logger.LogInformation("匿名用户登出（token可能已过期或无效）");
            }

            return Ok(new { message = "登出成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登出过程中发生错误");
            // 即使发生错误，也返回成功，因为客户端需要清除本地状态
            return Ok(new { message = "登出成功" });
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns>当前用户信息</returns>
    [HttpGet("me")]
    [Authorize]
    public async Task<ActionResult<UserInfoDto>> GetCurrentUser()
    {
        try
        {
            var userId = GetCurrentUserId();
            var user = await _userRepository.GetByIdAsync(userId);

            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            return Ok(new UserInfoDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                RealName = user.RealName,
                Role = user.Role,
                Status = user.Status,
                Avatar = user.Avatar,
                CreatedAt = user.CreatedTime,
                LastLoginAt = user.LastLoginAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户信息时发生错误");
            return StatusCode(500, new { message = "获取用户信息失败" });
        }
    }

    #region 私有方法

    /// <summary>
    /// 验证用户凭据
    /// </summary>
    private async Task<User?> ValidateUserCredentials(string username, string password)
    {
        var user = await _userRepository.GetByUsernameAsync(username);
        if (user == null)
        {
            return null;
        }

        return VerifyPassword(password, user.PasswordHash) ? user : null;
    }

    /// <summary>
    /// 生成访问令牌
    /// </summary>
    private string GenerateAccessToken(User user)
    {
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"];
        var key = Encoding.ASCII.GetBytes(secretKey!);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Role, user.Role),
            new Claim("real_name", user.RealName ?? ""),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(GetAccessTokenExpirationMinutes()),
            Issuer = jwtSettings["Issuer"],
            Audience = jwtSettings["Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    private static string GenerateRefreshToken()
    {
        var randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }

    /// <summary>
    /// 哈希密码
    /// </summary>
    private static string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    private static bool VerifyPassword(string password, string hash)
    {
        return BCrypt.Net.BCrypt.Verify(password, hash);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null)
        {
            throw new UnauthorizedAccessException("用户未认证");
        }
        return int.Parse(userIdClaim.Value);
    }

    /// <summary>
    /// 尝试获取当前用户ID（不抛出异常）
    /// </summary>
    private int? TryGetCurrentUserId()
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
            {
                return userId;
            }
        }
        catch
        {
            // 忽略异常
        }
        return null;
    }

    /// <summary>
    /// 获取访问令牌过期时间（分钟）
    /// </summary>
    private int GetAccessTokenExpirationMinutes()
    {
        return _configuration.GetValue<int>("JwtSettings:ExpirationInMinutes");
    }

    /// <summary>
    /// 获取刷新令牌过期时间（天）
    /// </summary>
    private int GetRefreshTokenExpirationDays()
    {
        return _configuration.GetValue<int>("JwtSettings:RefreshTokenExpirationInDays");
    }

    #endregion
}
