using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using SqlSugar;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 用户AI配置仓储实现
/// 功能: 管理用户个人AI配置的数据访问操作
/// 支持: CRUD操作、用户配置查询、使用统计更新
/// </summary>
public class UserAIConfigurationRepository : IUserAIConfigurationRepository
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<UserAIConfigurationRepository> _logger;

    public UserAIConfigurationRepository(ISqlSugarClient db, ILogger<UserAIConfigurationRepository> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 根据ID获取用户AI配置
    /// </summary>
    public async Task<UserAIConfiguration?> GetByIdAsync(int id)
    {
        try
        {
            return await _db.Queryable<UserAIConfiguration>()
                .Where(x => x.Id == id)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户AI配置失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 获取用户的所有AI配置
    /// </summary>
    public async Task<List<UserAIConfiguration>> GetByUserIdAsync(int userId)
    {
        try
        {
            return await Task.FromResult(_db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId)
                .OrderBy(x => x.ModelType)
                .OrderBy(x => x.ProviderName)
                .ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户AI配置失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户指定类型的AI配置
    /// </summary>
    public async Task<List<UserAIConfiguration>> GetByUserIdAndTypeAsync(int userId, string modelType)
    {
        try
        {
            return await _db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId && x.ModelType == modelType)
                .OrderBy(x => x.ProviderName)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户指定类型AI配置失败: {UserId}, {ModelType}", userId, modelType);
            throw;
        }
    }

    /// <summary>
    /// 获取用户指定提供商的AI配置
    /// </summary>
    public async Task<List<UserAIConfiguration>> GetByUserIdAndProviderAsync(int userId, string providerName)
    {
        try
        {
            return await _db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId && x.ProviderName == providerName)
                .OrderBy(x => x.ModelType)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户指定提供商AI配置失败: {UserId}, {ProviderName}", userId, providerName);
            throw;
        }
    }

    /// <summary>
    /// 获取用户的默认AI配置
    /// </summary>
    public async Task<UserAIConfiguration?> GetDefaultConfigurationAsync(int userId, string? modelType = null)
    {
        try
        {
            var query = _db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId && x.IsDefault && x.IsActive);

            if (!string.IsNullOrEmpty(modelType))
            {
                query = query.Where(x => x.ModelType == modelType);
            }

            return await query.FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户默认AI配置失败: {UserId}, {ModelType}", userId, modelType);
            throw;
        }
    }

    /// <summary>
    /// 获取用户活跃的AI配置
    /// </summary>
    public async Task<List<UserAIConfiguration>> GetActiveConfigurationsAsync(int userId)
    {
        try
        {
            return await _db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId && x.IsActive)
                .OrderBy(x => x.ModelType)
                .OrderBy(x => x.ProviderName)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户活跃AI配置失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 创建用户AI配置
    /// </summary>
    public async Task<UserAIConfiguration> CreateAsync(UserAIConfiguration configuration)
    {
        try
        {
            configuration.CreatedAt = DateTime.UtcNow;
            configuration.UpdatedAt = DateTime.UtcNow;

            // 如果设置为默认配置，需要先取消同类型的其他默认配置
            if (configuration.IsDefault)
            {
                await ClearDefaultConfigurationAsync(configuration.UserId, configuration.ModelType);
            }

            var result = await _db.Insertable(configuration).ExecuteReturnEntityAsync();
            _logger.LogInformation("创建用户AI配置成功: {UserId}, {ProviderName}, {ModelType}",
                configuration.UserId, configuration.ProviderName, configuration.ModelType);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建用户AI配置失败: {UserId}, {ProviderName}",
                configuration.UserId, configuration.ProviderName);
            throw;
        }
    }

    /// <summary>
    /// 更新用户AI配置
    /// </summary>
    public async Task<bool> UpdateAsync(UserAIConfiguration configuration)
    {
        try
        {
            configuration.UpdatedAt = DateTime.UtcNow;

            // 如果设置为默认配置，需要先取消同类型的其他默认配置
            if (configuration.IsDefault)
            {
                await ClearDefaultConfigurationAsync(configuration.UserId, configuration.ModelType, configuration.Id);
            }

            var result = await _db.Updateable(configuration).ExecuteCommandAsync();
            _logger.LogInformation("更新用户AI配置成功: {Id}, {UserId}", configuration.Id, configuration.UserId);

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户AI配置失败: {Id}", configuration.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除用户AI配置
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var result = await _db.Deleteable<UserAIConfiguration>()
                .Where(x => x.Id == id)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除用户AI配置成功: {Id}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户AI配置失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 设置默认配置
    /// </summary>
    public async Task<bool> SetDefaultConfigurationAsync(int userId, int configurationId, string modelType)
    {
        try
        {
            await _db.Ado.BeginTranAsync();

            // 先取消同类型的其他默认配置
            await ClearDefaultConfigurationAsync(userId, modelType, configurationId);

            // 设置新的默认配置
            var result = await _db.Updateable<UserAIConfiguration>()
                .SetColumns(x => new UserAIConfiguration
                {
                    IsDefault = true,
                    UpdatedAt = DateTime.UtcNow
                })
                .Where(x => x.Id == configurationId && x.UserId == userId)
                .ExecuteCommandAsync();

            await _db.Ado.CommitTranAsync();
            _logger.LogInformation("设置默认AI配置成功: {UserId}, {ConfigurationId}, {ModelType}",
                userId, configurationId, modelType);

            return result > 0;
        }
        catch (Exception ex)
        {
            await _db.Ado.RollbackTranAsync();
            _logger.LogError(ex, "设置默认AI配置失败: {UserId}, {ConfigurationId}", userId, configurationId);
            throw;
        }
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    public async Task<bool> UpdateUsageStatisticsAsync(int configurationId, int tokensUsed, bool isSuccess, decimal cost = 0)
    {
        try
        {
            var configuration = await GetByIdAsync(configurationId);
            if (configuration == null)
            {
                _logger.LogWarning("配置不存在，无法更新使用统计: {ConfigurationId}", configurationId);
                return false;
            }

            configuration.UpdateUsageStatistics(tokensUsed, isSuccess, cost);
            return await UpdateAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新使用统计失败: {ConfigurationId}", configurationId);
            throw;
        }
    }

    /// <summary>
    /// 重置每日统计
    /// </summary>
    public async Task<bool> ResetDailyStatisticsAsync(int? userId = null)
    {
        try
        {
            var query = _db.Updateable<UserAIConfiguration>()
                .SetColumns(x => new UserAIConfiguration
                {
                    CurrentDayTokens = 0,
                    UpdatedAt = DateTime.UtcNow
                });

            if (userId.HasValue)
            {
                query = query.Where(x => x.UserId == userId.Value);
            }

            var result = await query.ExecuteCommandAsync();
            _logger.LogInformation("重置每日统计成功: {UserId}, 影响行数: {Count}", userId, result);

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置每日统计失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 重置每月统计
    /// </summary>
    public async Task<bool> ResetMonthlyStatisticsAsync(int? userId = null)
    {
        try
        {
            var query = _db.Updateable<UserAIConfiguration>()
                .SetColumns(x => new UserAIConfiguration
                {
                    CurrentMonthTokens = 0,
                    CurrentMonthCost = 0,
                    UpdatedAt = DateTime.UtcNow
                });

            if (userId.HasValue)
            {
                query = query.Where(x => x.UserId == userId.Value);
            }

            var result = await query.ExecuteCommandAsync();
            _logger.LogInformation("重置每月统计成功: {UserId}, 影响行数: {Count}", userId, result);

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置每月统计失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 检查用户是否有指定类型的配置
    /// </summary>
    public async Task<bool> HasConfigurationAsync(int userId, string modelType)
    {
        try
        {
            return await _db.Queryable<UserAIConfiguration>()
                .Where(x => x.UserId == userId && x.ModelType == modelType)
                .AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户配置失败: {UserId}, {ModelType}", userId, modelType);
            throw;
        }
    }

    /// <summary>
    /// 获取用户的使用统计
    /// </summary>
    public async Task<UserAIUsageStatistics> GetUsageStatisticsAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var configurations = await GetByUserIdAsync(userId);

            var statistics = new UserAIUsageStatistics
            {
                UserId = userId,
                TotalRequests = configurations.Sum(x => x.TotalRequests),
                SuccessfulRequests = configurations.Sum(x => x.SuccessfulRequests),
                FailedRequests = configurations.Sum(x => x.FailedRequests),
                TotalTokensUsed = configurations.Sum(x => x.TotalTokensUsed),
                TotalCost = configurations.Sum(x => x.EstimatedCost),
                CurrentMonthCost = configurations.Sum(x => x.CurrentMonthCost),
                CurrentDayTokens = configurations.Sum(x => x.CurrentDayTokens),
                CurrentMonthTokens = configurations.Sum(x => x.CurrentMonthTokens),
                LastUsedAt = configurations.Where(x => x.LastUsedAt.HasValue).Max(x => x.LastUsedAt)
            };

            // 按提供商分组统计
            statistics.ProviderStatistics = configurations
                .GroupBy(x => x.ProviderName)
                .ToDictionary(g => g.Key, g => new ProviderUsageStatistics
                {
                    ProviderName = g.Key,
                    Requests = g.Sum(x => x.TotalRequests),
                    TokensUsed = g.Sum(x => x.TotalTokensUsed),
                    Cost = g.Sum(x => x.EstimatedCost)
                });

            // 按模型类型分组统计
            statistics.ModelTypeStatistics = configurations
                .GroupBy(x => x.ModelType)
                .ToDictionary(g => g.Key, g => new ModelTypeUsageStatistics
                {
                    ModelType = g.Key,
                    Requests = g.Sum(x => x.TotalRequests),
                    TokensUsed = g.Sum(x => x.TotalTokensUsed),
                    Cost = g.Sum(x => x.EstimatedCost)
                });

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户使用统计失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取超出限制的配置
    /// </summary>
    public async Task<List<UserAIConfiguration>> GetOverLimitConfigurationsAsync(int userId)
    {
        try
        {
            var configurations = await GetActiveConfigurationsAsync(userId);

            return configurations.Where(config =>
                (config.DailyTokenLimit.HasValue && config.CurrentDayTokens >= config.DailyTokenLimit.Value) ||
                (config.MonthlyTokenLimit.HasValue && config.CurrentMonthTokens >= config.MonthlyTokenLimit.Value)
            ).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取超出限制的配置失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 批量更新配置状态
    /// </summary>
    public async Task<bool> BatchUpdateStatusAsync(int userId, bool isActive, List<int>? configurationIds = null)
    {
        try
        {
            var query = _db.Updateable<UserAIConfiguration>()
                .SetColumns(x => new UserAIConfiguration
                {
                    IsActive = isActive,
                    UpdatedAt = DateTime.UtcNow
                })
                .Where(x => x.UserId == userId);

            if (configurationIds != null && configurationIds.Any())
            {
                query = query.Where(x => configurationIds.Contains(x.Id));
            }

            var result = await query.ExecuteCommandAsync();
            _logger.LogInformation("批量更新配置状态成功: {UserId}, {IsActive}, 影响行数: {Count}",
                userId, isActive, result);

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新配置状态失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 清除默认配置
    /// </summary>
    private async Task ClearDefaultConfigurationAsync(int userId, string modelType, int? excludeId = null)
    {
        var query = _db.Updateable<UserAIConfiguration>()
            .SetColumns(x => new UserAIConfiguration
            {
                IsDefault = false,
                UpdatedAt = DateTime.UtcNow
            })
            .Where(x => x.UserId == userId && x.ModelType == modelType && x.IsDefault);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        await query.ExecuteCommandAsync();
    }
}
