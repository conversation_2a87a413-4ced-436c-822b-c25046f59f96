<template>
  <div class="association-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>关联管理</h1>
        <p>管理开发步骤之间的依赖关系和模板序列关联</p>
      </div>
    </div>

    <!-- 项目选择 -->
    <el-card class="project-selector">
      <div class="selector-content">
        <el-select
          v-model="selectedProjectId"
          placeholder="请选择项目"
          @change="handleProjectChange"
          style="width: 300px"
        >
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
        <el-button
          v-if="selectedProjectId"
          type="primary"
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </el-card>

    <!-- 关联管理内容 -->
    <div v-if="selectedProjectId" class="association-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 步骤依赖关系 -->
        <el-tab-pane label="步骤依赖关系" name="dependencies">
          <div class="tab-content">
            <div class="section-header">
              <h3>项目步骤依赖关系图</h3>
              <div class="header-actions">
                <el-button type="primary" @click="showDependencyAnalysis = true">
                  <el-icon><Operation /></el-icon>
                  AI依赖分析
                </el-button>
                <el-button @click="validateDependencies">
                  <el-icon><CircleCheck /></el-icon>
                  验证依赖
                </el-button>
              </div>
            </div>

            <!-- 依赖关系统计 -->
            <el-row :gutter="20" class="stats-row">
              <el-col :span="6">
                <el-statistic title="总步骤数" :value="dependencyStats.totalSteps" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="依赖关系数" :value="dependencyStats.totalDependencies" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="循环依赖" :value="dependencyStats.circularDependencies" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="孤立步骤" :value="dependencyStats.isolatedSteps" />
              </el-col>
            </el-row>

            <!-- 依赖关系列表 -->
            <el-card class="dependency-list">
              <template #header>
                <span>依赖关系详情</span>
              </template>
              <el-table :data="dependencies" v-loading="loadingDependencies">
                <el-table-column prop="stepName" label="步骤" width="200" />
                <el-table-column prop="dependsOnStepName" label="依赖步骤" width="200" />
                <el-table-column prop="dependencyType" label="依赖类型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getDependencyTypeTag(row.dependencyType)">
                      {{ getDependencyTypeText(row.dependencyType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="isRequired" label="是否必需" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.isRequired ? 'success' : 'info'">
                      {{ row.isRequired ? '必需' : '可选' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      size="small"
                      type="danger"
                      @click="removeDependency(row.id)"
                    >
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 模板序列关联 -->
        <el-tab-pane label="模板序列关联" name="templates">
          <div class="tab-content">
            <div class="section-header">
              <h3>步骤模板序列关联</h3>
              <div class="header-actions">
                <el-button type="primary" @click="showBatchApplyDialog = true">
                  <el-icon><Plus /></el-icon>
                  批量应用模板
                </el-button>
              </div>
            </div>

            <!-- 模板关联统计 -->
            <el-row :gutter="20" class="stats-row">
              <el-col :span="6">
                <el-statistic title="已关联步骤" :value="templateStats.associatedSteps" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="未关联步骤" :value="templateStats.unassociatedSteps" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="使用的模板序列" :value="templateStats.usedSequences" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="总关联数" :value="templateStats.totalAssociations" />
              </el-col>
            </el-row>

            <!-- 模板关联列表 -->
            <el-card class="template-associations">
              <template #header>
                <span>模板序列关联详情</span>
              </template>
              <el-table :data="templateAssociations" v-loading="loadingTemplates">
                <el-table-column prop="stepName" label="步骤" width="200" />
                <el-table-column prop="sequenceName" label="模板序列" width="200" />
                <el-table-column prop="category" label="分类" width="120">
                  <template #default="{ row }">
                    <el-tag type="info">{{ row.category || '未分类' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="appliedTime" label="应用时间" width="180">
                  <template #default="{ row }">
                    {{ formatDate(row.appliedTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="appliedByUserName" label="应用人" width="120" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button
                      size="small"
                      type="danger"
                      @click="removeTemplateAssociation(row.stepId, row.sequenceId)"
                    >
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请先选择一个项目来管理关联关系" />
    </div>

    <!-- AI依赖分析对话框 -->
    <el-dialog
      v-model="showDependencyAnalysis"
      title="AI依赖分析"
      width="600px"
    >
      <div class="analysis-content">
        <p>AI将自动分析项目中的开发步骤，并建立合理的依赖关系。</p>
        <el-alert
          title="注意"
          type="warning"
          description="此操作将覆盖现有的依赖关系，请谨慎操作。"
          show-icon
          :closable="false"
        />
      </div>
      <template #footer>
        <el-button @click="showDependencyAnalysis = false">取消</el-button>
        <el-button
          type="primary"
          @click="performDependencyAnalysis"
          :loading="analyzing"
        >
          开始分析
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量应用模板对话框 -->
    <el-dialog
      v-model="showBatchApplyDialog"
      title="批量应用模板序列"
      width="800px"
    >
      <div class="batch-apply-content">
        <p>选择要应用模板序列的步骤和目标模板序列。</p>
        <!-- TODO: 实现批量应用模板的界面 -->
        <el-empty description="批量应用模板功能正在开发中" />
      </div>
      <template #footer>
        <el-button @click="showBatchApplyDialog = false">取消</el-button>
        <el-button type="primary" disabled>应用</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Operation, CircleCheck, Plus } from '@element-plus/icons-vue'
import { ProjectService } from '@/services/project'
import { developmentService } from '@/services/development'
import type { ProjectSummary } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const loadingDependencies = ref(false)
const loadingTemplates = ref(false)
const analyzing = ref(false)
const projects = ref<ProjectSummary[]>([])
const selectedProjectId = ref<number>()
const activeTab = ref('dependencies')

// 对话框状态
const showDependencyAnalysis = ref(false)
const showBatchApplyDialog = ref(false)

// 依赖关系数据
const dependencies = ref<any[]>([])
const dependencyStats = reactive({
  totalSteps: 0,
  totalDependencies: 0,
  circularDependencies: 0,
  isolatedSteps: 0
})

// 模板关联数据
const templateAssociations = ref<any[]>([])
const templateStats = reactive({
  associatedSteps: 0,
  unassociatedSteps: 0,
  usedSequences: 0,
  totalAssociations: 0
})

// 计算属性
const hasValidationErrors = computed(() => dependencyStats.circularDependencies > 0)

// 方法
const loadProjects = async () => {
  try {
    const result = await ProjectService.getProjects({ pageNumber: 1, pageSize: 1000 })
    projects.value = result.items
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

const handleProjectChange = () => {
  if (selectedProjectId.value) {
    refreshData()
  }
}

const refreshData = async () => {
  if (!selectedProjectId.value) return
  
  loading.value = true
  try {
    await Promise.all([
      loadDependencies(),
      loadTemplateAssociations()
    ])
  } finally {
    loading.value = false
  }
}

const loadDependencies = async () => {
  if (!selectedProjectId.value) return
  
  loadingDependencies.value = true
  try {
    // TODO: 实现加载项目依赖关系的API
    dependencies.value = []
    
    // 更新统计信息
    dependencyStats.totalSteps = 0
    dependencyStats.totalDependencies = dependencies.value.length
    dependencyStats.circularDependencies = 0
    dependencyStats.isolatedSteps = 0
  } catch (error) {
    console.error('加载依赖关系失败:', error)
    ElMessage.error('加载依赖关系失败')
  } finally {
    loadingDependencies.value = false
  }
}

const loadTemplateAssociations = async () => {
  if (!selectedProjectId.value) return
  
  loadingTemplates.value = true
  try {
    // TODO: 实现加载模板关联的API
    templateAssociations.value = []
    
    // 更新统计信息
    templateStats.associatedSteps = 0
    templateStats.unassociatedSteps = 0
    templateStats.usedSequences = 0
    templateStats.totalAssociations = templateAssociations.value.length
  } catch (error) {
    console.error('加载模板关联失败:', error)
    ElMessage.error('加载模板关联失败')
  } finally {
    loadingTemplates.value = false
  }
}

const validateDependencies = async () => {
  if (!selectedProjectId.value) return
  
  try {
    const result = await developmentService.validateDependencies(selectedProjectId.value)
    
    if (result.isValid) {
      ElMessage.success('依赖关系验证通过')
    } else {
      ElMessage.warning(`发现 ${result.errors.length} 个错误，${result.warnings.length} 个警告`)
    }
  } catch (error) {
    console.error('验证依赖关系失败:', error)
    ElMessage.error('验证依赖关系失败')
  }
}

const performDependencyAnalysis = async () => {
  if (!selectedProjectId.value) return
  
  analyzing.value = true
  try {
    const count = await developmentService.autoAnalyzeDependencies(selectedProjectId.value)
    ElMessage.success(`AI分析完成，建立了 ${count} 个依赖关系`)
    showDependencyAnalysis.value = false
    await loadDependencies()
  } catch (error) {
    console.error('AI依赖分析失败:', error)
    ElMessage.error('AI依赖分析失败')
  } finally {
    analyzing.value = false
  }
}

const removeDependency = async (dependencyId: number) => {
  try {
    await ElMessageBox.confirm('确定要移除这个依赖关系吗？', '确认移除', {
      type: 'warning'
    })
    
    await developmentService.removeStepDependency(dependencyId)
    ElMessage.success('依赖关系已移除')
    await loadDependencies()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('移除依赖关系失败:', error)
      ElMessage.error('移除依赖关系失败')
    }
  }
}

const removeTemplateAssociation = async (stepId: number, sequenceId: number) => {
  try {
    await ElMessageBox.confirm('确定要移除这个模板序列关联吗？', '确认移除', {
      type: 'warning'
    })
    
    await developmentService.removeStepTemplateSequence(stepId, sequenceId)
    ElMessage.success('模板序列关联已移除')
    await loadTemplateAssociations()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('移除模板关联失败:', error)
      ElMessage.error('移除模板关联失败')
    }
  }
}

// 辅助方法
const getDependencyTypeTag = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    'Sequential': 'primary',
    'Parallel': 'success',
    'Conditional': 'warning',
    'Optional': 'info'
  }
  return typeMap[type] || 'info'
}

const getDependencyTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Sequential': '顺序依赖',
    'Parallel': '并行依赖',
    'Conditional': '条件依赖',
    'Optional': '可选依赖'
  }
  return typeMap[type] || type
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style lang="scss" scoped>
.association-management {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  
  .header-content {
    h1 {
      margin: 0 0 8px 0;
      color: var(--el-text-color-primary);
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }
}

.project-selector {
  margin-bottom: 20px;
  
  .selector-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.association-content {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
      font-size: 18px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .tab-content {
    padding: 20px 0;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.analysis-content,
.batch-apply-content {
  padding: 20px 0;
  
  p {
    margin-bottom: 16px;
    color: var(--el-text-color-regular);
  }
}
</style>
