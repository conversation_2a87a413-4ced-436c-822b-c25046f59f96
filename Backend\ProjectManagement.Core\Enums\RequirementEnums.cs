namespace ProjectManagement.Core.Enums;

/// <summary>
/// 需求类型枚举
/// </summary>
public enum RequirementType
{
    /// <summary>
    /// 功能性需求
    /// </summary>
    Functional = 1,

    /// <summary>
    /// 非功能性需求
    /// </summary>
    NonFunctional = 2,

    /// <summary>
    /// 业务需求
    /// </summary>
    Business = 3,

    /// <summary>
    /// 技术需求
    /// </summary>
    Technical = 4,

    /// <summary>
    /// 用户界面需求
    /// </summary>
    UserInterface = 5,

    /// <summary>
    /// 性能需求
    /// </summary>
    Performance = 6,

    /// <summary>
    /// 安全需求
    /// </summary>
    Security = 7,

    /// <summary>
    /// 兼容性需求
    /// </summary>
    Compatibility = 8
}

/// <summary>
/// 需求状态枚举
/// </summary>
public enum RequirementStatus
{
    /// <summary>
    /// 草稿
    /// </summary>
    Draft = 1,

    /// <summary>
    /// 待审核
    /// </summary>
    PendingReview = 2,

    /// <summary>
    /// 已审核
    /// </summary>
    Reviewed = 3,

    /// <summary>
    /// 已批准
    /// </summary>
    Approved = 4,

    /// <summary>
    /// 开发中
    /// </summary>
    InDevelopment = 5,

    /// <summary>
    /// 开发完成
    /// </summary>
    DevelopmentCompleted = 6,

    /// <summary>
    /// 测试中
    /// </summary>
    InTesting = 7,

    /// <summary>
    /// 测试完成
    /// </summary>
    TestingCompleted = 8,

    /// <summary>
    /// 已部署
    /// </summary>
    Deployed = 9,

    /// <summary>
    /// 已验收
    /// </summary>
    Accepted = 10,

    /// <summary>
    /// 已拒绝
    /// </summary>
    Rejected = 11,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 12
}

/// <summary>
/// 需求来源枚举
/// </summary>
public enum RequirementSource
{
    /// <summary>
    /// 用户输入
    /// </summary>
    UserInput = 1,

    /// <summary>
    /// 客户反馈
    /// </summary>
    CustomerFeedback = 2,

    /// <summary>
    /// 市场调研
    /// </summary>
    MarketResearch = 3,

    /// <summary>
    /// 竞品分析
    /// </summary>
    CompetitorAnalysis = 4,

    /// <summary>
    /// 内部需求
    /// </summary>
    Internal = 5,

    /// <summary>
    /// 法规要求
    /// </summary>
    Regulatory = 6,

    /// <summary>
    /// 技术升级
    /// </summary>
    TechnicalUpgrade = 7,

    /// <summary>
    /// AI生成
    /// </summary>
    AIGenerated = 8
}
