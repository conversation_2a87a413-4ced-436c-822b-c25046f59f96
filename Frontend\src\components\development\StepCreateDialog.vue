<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建开发步骤"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="步骤名称" prop="stepName">
            <el-input v-model="formData.stepName" placeholder="请输入步骤名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="步骤类型" prop="stepType">
            <el-select v-model="formData.stepType" placeholder="请选择步骤类型">
              <el-option label="开发" value="Development" />
              <el-option label="测试" value="Testing" />
              <el-option label="部署" value="Deployment" />
              <el-option label="文档" value="Documentation" />
              <el-option label="设计" value="Design" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级">
              <el-option label="低" value="Low" />
              <el-option label="中" value="Medium" />
              <el-option label="高" value="High" />
              <el-option label="紧急" value="Critical" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option label="待处理" value="Pending" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="失败" value="Failed" />
              <el-option label="阻塞" value="Blocked" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预估工时" prop="estimatedHours">
            <el-input-number
              v-model="formData.estimatedHours"
              :min="0"
              :max="999"
              :precision="1"
              placeholder="预估工时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际工时" prop="actualHours">
            <el-input-number
              v-model="formData.actualHours"
              :min="0"
              :max="999"
              :precision="1"
              placeholder="实际工时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="技术栈" prop="technologyStack">
            <el-select
              v-model="formData.technologyStack"
              placeholder="请选择技术栈"
              filterable
              allow-create
            >
              <el-option label="Vue.js" value="vue" />
              <el-option label=".NET Core" value="dotnet" />
              <el-option label="SQL Server" value="sqlserver" />
              <el-option label="TypeScript" value="typescript" />
              <el-option label="C#" value="csharp" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组件类型" prop="componentType">
            <el-select v-model="formData.componentType" placeholder="请选择组件类型">
              <el-option label="前端" value="Frontend" />
              <el-option label="后端" value="Backend" />
              <el-option label="数据库" value="Database" />
              <el-option label="API" value="API" />
              <el-option label="配置" value="Configuration" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="文件路径" prop="filePath">
        <el-input v-model="formData.filePath" placeholder="请输入文件路径" />
      </el-form-item>

      <el-form-item label="步骤描述" prop="stepDescription">
        <el-input
          v-model="formData.stepDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入步骤描述"
        />
      </el-form-item>

      <el-form-item label="AI 提示词" prop="aiPrompt">
        <el-input
          v-model="formData.aiPrompt"
          type="textarea"
          :rows="6"
          placeholder="请输入 AI 提示词"
        />
      </el-form-item>

      <!-- 图片上传功能 -->
      <el-form-item label="参考图片">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          multiple
          accept="image/*"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>

        <el-dialog v-model="dialogImageVisible" title="图片预览">
          <img w-full :src="dialogImageUrl" alt="Preview Image" style="width: 100%" />
        </el-dialog>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadProps, type UploadUserFile } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { DevelopmentService } from '@/services/development'
import type { StepType, Priority, StepStatus, ComponentType } from '@/types/development'

// Props
interface Props {
  modelValue: boolean
  projectId: number
  parentStepId?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  parentStepId: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<UploadUserFile[]>([])
const dialogImageVisible = ref(false)
const dialogImageUrl = ref('')

const formData = reactive({
  stepName: '',
  stepDescription: '',
  stepType: 'Development' as StepType,
  priority: 'Medium' as Priority,
  status: 'Pending' as StepStatus,
  estimatedHours: 0,
  actualHours: 0,
  technologyStack: '',
  componentType: 'Frontend' as ComponentType,
  filePath: '',
  aiPrompt: '',
  stepOrder: 0,
  stepLevel: 1
})

// 表单验证规则
const rules: FormRules = {
  stepName: [
    { required: true, message: '请输入步骤名称', trigger: 'blur' },
    { min: 2, max: 200, message: '步骤名称长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  stepType: [
    { required: true, message: '请选择步骤类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开，重置表单
watch(() => props.modelValue, (visible) => {
  if (visible) {
    resetForm()
    loadNextStepOrder()
  }
})

// 方法
const resetForm = () => {
  Object.assign(formData, {
    stepName: '',
    stepDescription: '',
    stepType: 'Development',
    priority: 'Medium',
    status: 'Pending',
    estimatedHours: 0,
    actualHours: 0,
    technologyStack: '',
    componentType: 'Frontend',
    filePath: '',
    aiPrompt: '',
    stepOrder: 0,
    stepLevel: props.parentStepId ? 2 : 1
  })
  fileList.value = []
}

const loadNextStepOrder = async () => {
  try {
    // 获取当前项目的最大步骤顺序
    const result = await DevelopmentService.getProjectSteps(props.projectId, 1, 1)
    formData.stepOrder = result.totalCount + 1
  } catch (error) {
    console.error('获取步骤顺序失败:', error)
    formData.stepOrder = 1
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 先创建步骤
    const stepData = {
      ...formData,
      projectId: props.projectId,
      parentStepId: props.parentStepId
    }

    const createdStep = await DevelopmentService.createStep(stepData)

    // 然后上传图片
    const imageUrls: string[] = []
    for (const fileItem of fileList.value) {
      if (fileItem.raw) {
        try {
          const response = await DevelopmentService.uploadStepImage(createdStep.id, fileItem.raw)
          imageUrls.push(response.data.filePath)
        } catch (error) {
          console.error('上传图片失败:', error)
          ElMessage.warning(`上传图片 ${fileItem.name} 失败`)
        }
      }
    }

    // 如果有图片，更新步骤的参考图片字段
    if (imageUrls.length > 0) {
      try {
        await DevelopmentService.updateStep(createdStep.id, {
          referenceImages: JSON.stringify(imageUrls)
        })

        // 同时将图片信息添加到AI提示词中
        const updatedPrompt = formData.aiPrompt + `\n\n参考图片：\n${imageUrls.map(url => `- ${url}`).join('\n')}`
        await DevelopmentService.updateStep(createdStep.id, {
          aiPrompt: updatedPrompt
        })
      } catch (error) {
        console.error('更新步骤图片信息失败:', error)
        ElMessage.warning('图片上传成功，但更新步骤信息失败')
      }
    }

    ElMessage.success('步骤创建成功')
    emit('save')
    handleClose()
  } catch (error) {
    console.error('创建步骤失败:', error)
    ElMessage.error('创建步骤失败')
  } finally {
    loading.value = false
  }
}

// 图片上传相关方法
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogImageVisible.value = true
}

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type.indexOf('image/') !== 0) {
    ElMessage.error('只能上传图片文件!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

const uploadImages = async (): Promise<string[]> => {
  // 这个方法现在主要用于编辑对话框，创建对话框的逻辑已移到handleSave中
  return fileList.value.map(file => file.url || '').filter(url => url)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
