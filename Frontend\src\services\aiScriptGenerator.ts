import { aiProvider } from './aiProvider'
import type { PlaywrightTest } from './playwrightTest'

// AI脚本生成服务
export class AIScriptGeneratorService {
  
  // 1. 基于测试用例生成CI/CD脚本
  static async generateCIScript(
    tests: PlaywrightTest[], 
    platform: 'github' | 'gitlab' | 'jenkins' | 'azure'
  ): Promise<CIScriptResult> {
    try {
      const prompt = `
基于以下Playwright测试用例，生成${platform}的CI/CD自动化脚本：

测试用例列表：
${tests.map(test => `
- 测试名称: ${test.name}
- 测试类型: ${test.category}
- 浏览器: ${test.browser}
- 描述: ${test.description}
`).join('\n')}

请生成包含以下功能的CI/CD脚本：
1. 环境准备和依赖安装
2. 浏览器安装和配置
3. 测试执行（支持并行）
4. 测试报告生成
5. 失败通知机制
6. 测试结果存档

根据${platform}平台的最佳实践生成配置文件。
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseCIScriptResult(response, platform)
    } catch (error) {
      console.error('生成CI脚本失败:', error)
      throw error
    }
  }

  // 2. 生成Docker容器化脚本
  static async generateDockerScript(
    tests: PlaywrightTest[],
    environment: 'development' | 'staging' | 'production'
  ): Promise<DockerScriptResult> {
    try {
      const prompt = `
为以下Playwright测试生成Docker容器化方案：

测试环境: ${environment}
测试数量: ${tests.length}
测试类型分布: ${this.getTestTypeDistribution(tests)}

请生成：
1. Dockerfile - 包含Playwright和所有依赖
2. docker-compose.yml - 支持多浏览器并行测试
3. 启动脚本 - 自动化容器管理
4. 环境配置文件
5. 测试数据卷配置

确保容器化方案支持：
- 多浏览器并行执行
- 测试报告持久化
- 环境变量配置
- 健康检查机制
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseDockerScriptResult(response)
    } catch (error) {
      console.error('生成Docker脚本失败:', error)
      throw error
    }
  }

  // 3. 生成测试数据准备脚本
  static async generateTestDataScript(
    tests: PlaywrightTest[],
    dataSource: 'database' | 'api' | 'file' | 'mock'
  ): Promise<TestDataScriptResult> {
    try {
      const prompt = `
基于以下测试用例，生成测试数据准备脚本：

数据源类型: ${dataSource}
测试用例分析：
${tests.map(test => `
- ${test.name}: ${this.extractDataRequirements(test.code)}
`).join('\n')}

请生成：
1. 数据准备脚本 - 创建测试所需的基础数据
2. 数据清理脚本 - 测试后清理临时数据
3. 数据验证脚本 - 确保数据完整性
4. 数据备份恢复脚本
5. 环境数据同步脚本

支持的功能：
- 数据隔离（每个测试独立数据）
- 并行测试数据管理
- 数据版本控制
- 自动回滚机制
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseTestDataScriptResult(response, dataSource)
    } catch (error) {
      console.error('生成测试数据脚本失败:', error)
      throw error
    }
  }

  // 4. 生成性能监控脚本
  static async generateMonitoringScript(
    tests: PlaywrightTest[]
  ): Promise<MonitoringScriptResult> {
    try {
      const prompt = `
为以下Playwright测试生成性能监控和报警脚本：

测试概况：
- 测试总数: ${tests.length}
- 关键测试: ${tests.filter(t => t.priority === 'high').length}
- E2E测试: ${tests.filter(t => t.category === 'e2e').length}

请生成：
1. 性能监控脚本 - 收集测试执行指标
2. 报警规则配置 - 基于阈值的智能报警
3. 仪表板配置 - 可视化测试健康状态
4. 日志聚合脚本 - 集中化日志管理
5. 趋势分析脚本 - 性能趋势预测

监控指标包括：
- 测试执行时间
- 成功率趋势
- 资源使用情况
- 错误模式分析
- 浏览器兼容性
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseMonitoringScriptResult(response)
    } catch (error) {
      console.error('生成监控脚本失败:', error)
      throw error
    }
  }

  // 5. 生成测试环境管理脚本
  static async generateEnvironmentScript(
    tests: PlaywrightTest[],
    environments: string[]
  ): Promise<EnvironmentScriptResult> {
    try {
      const prompt = `
为以下测试生成多环境管理脚本：

目标环境: ${environments.join(', ')}
测试配置需求：
${tests.map(test => `
- ${test.name}: 需要${test.config.baseURL || '默认'}环境
`).join('\n')}

请生成：
1. 环境切换脚本 - 快速切换测试环境
2. 配置管理脚本 - 环境配置版本化管理
3. 环境健康检查脚本 - 验证环境可用性
4. 数据同步脚本 - 环境间数据同步
5. 部署验证脚本 - 自动化部署验证

支持功能：
- 环境隔离
- 配置热更新
- 回滚机制
- 环境监控
- 自动故障转移
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseEnvironmentScriptResult(response, environments)
    } catch (error) {
      console.error('生成环境管理脚本失败:', error)
      throw error
    }
  }

  // 6. 生成测试报告自动化脚本
  static async generateReportingScript(
    tests: PlaywrightTest[],
    reportTypes: string[]
  ): Promise<ReportingScriptResult> {
    try {
      const prompt = `
为以下测试生成自动化报告脚本：

报告类型: ${reportTypes.join(', ')}
测试统计：
- 总测试数: ${tests.length}
- 测试分类: ${this.getTestTypeDistribution(tests)}

请生成：
1. 报告生成脚本 - 多格式报告自动生成
2. 报告聚合脚本 - 合并多次执行结果
3. 报告分发脚本 - 自动发送给相关人员
4. 历史报告管理脚本 - 报告存档和清理
5. 报告模板定制脚本 - 可定制的报告格式

报告功能：
- 实时报告更新
- 趋势分析图表
- 失败用例详情
- 性能指标统计
- 覆盖率分析
- 自定义报告模板
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseReportingScriptResult(response, reportTypes)
    } catch (error) {
      console.error('生成报告脚本失败:', error)
      throw error
    }
  }

  // 7. 生成负载测试脚本
  static async generateLoadTestScript(
    tests: PlaywrightTest[],
    loadConfig: LoadTestConfig
  ): Promise<LoadTestScriptResult> {
    try {
      const prompt = `
基于以下E2E测试生成负载测试脚本：

负载配置：
- 并发用户数: ${loadConfig.concurrentUsers}
- 测试持续时间: ${loadConfig.duration}
- 目标RPS: ${loadConfig.targetRPS}

基础测试用例：
${tests.filter(t => t.category === 'e2e').map(test => `
- ${test.name}: ${this.extractUserJourney(test.code)}
`).join('\n')}

请生成：
1. K6负载测试脚本 - 基于真实用户行为
2. 性能基准脚本 - 建立性能基线
3. 压力测试脚本 - 系统极限测试
4. 容量规划脚本 - 容量评估工具
5. 监控集成脚本 - 实时性能监控

包含功能：
- 真实用户模拟
- 动态负载调整
- 性能指标收集
- 瓶颈自动识别
- 报告可视化
`

      const response = await aiProvider.generateContent(prompt)
      return this.parseLoadTestScriptResult(response, loadConfig)
    } catch (error) {
      console.error('生成负载测试脚本失败:', error)
      throw error
    }
  }

  // 辅助方法：解析CI脚本结果
  private static parseCIScriptResult(aiResponse: string, platform: string): CIScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        platform,
        configFile: result.configFile || '',
        scripts: result.scripts || {},
        documentation: result.documentation || '',
        estimatedExecutionTime: result.estimatedExecutionTime || '未知'
      }
    } catch (error) {
      console.error('解析CI脚本结果失败:', error)
      return {
        platform,
        configFile: '# 生成失败，请重试',
        scripts: {},
        documentation: '生成失败',
        estimatedExecutionTime: '未知'
      }
    }
  }

  // 辅助方法：解析Docker脚本结果
  private static parseDockerScriptResult(aiResponse: string): DockerScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        dockerfile: result.dockerfile || '',
        dockerCompose: result.dockerCompose || '',
        startupScript: result.startupScript || '',
        environmentConfig: result.environmentConfig || {},
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析Docker脚本结果失败:', error)
      return {
        dockerfile: '# 生成失败，请重试',
        dockerCompose: '# 生成失败，请重试',
        startupScript: '#!/bin/bash\necho "生成失败，请重试"',
        environmentConfig: {},
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：解析测试数据脚本结果
  private static parseTestDataScriptResult(aiResponse: string, dataSource: string): TestDataScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        dataSource,
        setupScript: result.setupScript || '',
        cleanupScript: result.cleanupScript || '',
        validationScript: result.validationScript || '',
        backupScript: result.backupScript || '',
        syncScript: result.syncScript || '',
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析测试数据脚本结果失败:', error)
      return {
        dataSource,
        setupScript: '# 生成失败，请重试',
        cleanupScript: '# 生成失败，请重试',
        validationScript: '# 生成失败，请重试',
        backupScript: '# 生成失败，请重试',
        syncScript: '# 生成失败，请重试',
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：解析监控脚本结果
  private static parseMonitoringScriptResult(aiResponse: string): MonitoringScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        monitoringScript: result.monitoringScript || '',
        alertRules: result.alertRules || '',
        dashboardConfig: result.dashboardConfig || '',
        logAggregation: result.logAggregation || '',
        trendAnalysis: result.trendAnalysis || '',
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析监控脚本结果失败:', error)
      return {
        monitoringScript: '# 生成失败，请重试',
        alertRules: '# 生成失败，请重试',
        dashboardConfig: '# 生成失败，请重试',
        logAggregation: '# 生成失败，请重试',
        trendAnalysis: '# 生成失败，请重试',
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：解析环境管理脚本结果
  private static parseEnvironmentScriptResult(aiResponse: string, environments: string[]): EnvironmentScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        environments,
        switchScript: result.switchScript || '',
        configManagement: result.configManagement || '',
        healthCheck: result.healthCheck || '',
        dataSync: result.dataSync || '',
        deploymentValidation: result.deploymentValidation || '',
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析环境管理脚本结果失败:', error)
      return {
        environments,
        switchScript: '# 生成失败，请重试',
        configManagement: '# 生成失败，请重试',
        healthCheck: '# 生成失败，请重试',
        dataSync: '# 生成失败，请重试',
        deploymentValidation: '# 生成失败，请重试',
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：解析报告脚本结果
  private static parseReportingScriptResult(aiResponse: string, reportTypes: string[]): ReportingScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        reportTypes,
        generationScript: result.generationScript || '',
        aggregationScript: result.aggregationScript || '',
        distributionScript: result.distributionScript || '',
        archiveScript: result.archiveScript || '',
        templateScript: result.templateScript || '',
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析报告脚本结果失败:', error)
      return {
        reportTypes,
        generationScript: '# 生成失败，请重试',
        aggregationScript: '# 生成失败，请重试',
        distributionScript: '# 生成失败，请重试',
        archiveScript: '# 生成失败，请重试',
        templateScript: '# 生成失败，请重试',
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：解析负载测试脚本结果
  private static parseLoadTestScriptResult(aiResponse: string, loadConfig: LoadTestConfig): LoadTestScriptResult {
    try {
      const result = JSON.parse(aiResponse)
      return {
        loadConfig,
        k6Script: result.k6Script || '',
        benchmarkScript: result.benchmarkScript || '',
        stressTestScript: result.stressTestScript || '',
        capacityScript: result.capacityScript || '',
        monitoringIntegration: result.monitoringIntegration || '',
        documentation: result.documentation || ''
      }
    } catch (error) {
      console.error('解析负载测试脚本结果失败:', error)
      return {
        loadConfig,
        k6Script: '# 生成失败，请重试',
        benchmarkScript: '# 生成失败，请重试',
        stressTestScript: '# 生成失败，请重试',
        capacityScript: '# 生成失败，请重试',
        monitoringIntegration: '# 生成失败，请重试',
        documentation: '生成失败'
      }
    }
  }

  // 辅助方法：获取测试类型分布
  private static getTestTypeDistribution(tests: PlaywrightTest[]): string {
    const distribution = tests.reduce((acc, test) => {
      acc[test.category] = (acc[test.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(distribution)
      .map(([type, count]) => `${type}: ${count}`)
      .join(', ')
  }

  // 辅助方法：提取数据需求
  private static extractDataRequirements(code: string): string {
    // 简单的代码分析，提取可能的数据需求
    const patterns = [
      /fill\(['"]([^'"]+)['"],\s*['"]([^'"]+)['"]\)/g,
      /goto\(['"]([^'"]+)['"]\)/g,
      /expect\([^)]+\)\.toContainText\(['"]([^'"]+)['"]\)/g
    ]

    const requirements = []
    for (const pattern of patterns) {
      const matches = [...code.matchAll(pattern)]
      requirements.push(...matches.map(match => match[1] || match[2]).filter(Boolean))
    }

    return requirements.length > 0 ? requirements.join(', ') : '基础测试数据'
  }

  // 辅助方法：提取用户旅程
  private static extractUserJourney(code: string): string {
    // 分析测试代码，提取用户操作流程
    const actions = []
    
    if (code.includes('goto')) actions.push('页面访问')
    if (code.includes('fill')) actions.push('表单填写')
    if (code.includes('click')) actions.push('点击操作')
    if (code.includes('expect')) actions.push('结果验证')
    
    return actions.length > 0 ? actions.join(' -> ') : '基础用户流程'
  }
}

// 类型定义
export interface CIScriptResult {
  platform: string
  configFile: string
  scripts: Record<string, string>
  documentation: string
  estimatedExecutionTime: string
}

export interface DockerScriptResult {
  dockerfile: string
  dockerCompose: string
  startupScript: string
  environmentConfig: Record<string, any>
  documentation: string
}

export interface TestDataScriptResult {
  dataSource: string
  setupScript: string
  cleanupScript: string
  validationScript: string
  backupScript: string
  syncScript: string
  documentation: string
}

export interface MonitoringScriptResult {
  monitoringScript: string
  alertRules: string
  dashboardConfig: string
  logAggregation: string
  trendAnalysis: string
  documentation: string
}

export interface EnvironmentScriptResult {
  environments: string[]
  switchScript: string
  configManagement: string
  healthCheck: string
  dataSync: string
  deploymentValidation: string
  documentation: string
}

export interface ReportingScriptResult {
  reportTypes: string[]
  generationScript: string
  aggregationScript: string
  distributionScript: string
  archiveScript: string
  templateScript: string
  documentation: string
}

export interface LoadTestConfig {
  concurrentUsers: number
  duration: string
  targetRPS: number
  rampUpTime?: string
  thresholds?: Record<string, string>
}

export interface LoadTestScriptResult {
  loadConfig: LoadTestConfig
  k6Script: string
  benchmarkScript: string
  stressTestScript: string
  capacityScript: string
  monitoringIntegration: string
  documentation: string
}

// 脚本生成配置
export interface ScriptGenerationConfig {
  tests: PlaywrightTest[]
  scriptType: 'ci' | 'docker' | 'data' | 'monitoring' | 'environment' | 'reporting' | 'load'
  platform?: string
  environment?: string
  dataSource?: string
  environments?: string[]
  reportTypes?: string[]
  loadConfig?: LoadTestConfig
  customOptions?: Record<string, any>
}

// 批量生成脚本
export async function batchGenerateScripts(config: ScriptGenerationConfig): Promise<any> {
  const { tests, scriptType } = config

  switch (scriptType) {
    case 'ci':
      return await AIScriptGeneratorService.generateCIScript(
        tests, 
        config.platform as 'github' | 'gitlab' | 'jenkins' | 'azure'
      )
    
    case 'docker':
      return await AIScriptGeneratorService.generateDockerScript(
        tests,
        config.environment as 'development' | 'staging' | 'production'
      )
    
    case 'data':
      return await AIScriptGeneratorService.generateTestDataScript(
        tests,
        config.dataSource as 'database' | 'api' | 'file' | 'mock'
      )
    
    case 'monitoring':
      return await AIScriptGeneratorService.generateMonitoringScript(tests)
    
    case 'environment':
      return await AIScriptGeneratorService.generateEnvironmentScript(
        tests,
        config.environments || []
      )
    
    case 'reporting':
      return await AIScriptGeneratorService.generateReportingScript(
        tests,
        config.reportTypes || []
      )
    
    case 'load':
      return await AIScriptGeneratorService.generateLoadTestScript(
        tests,
        config.loadConfig || { concurrentUsers: 10, duration: '5m', targetRPS: 100 }
      )
    
    default:
      throw new Error(`不支持的脚本类型: ${scriptType}`)
  }
}

export default AIScriptGeneratorService