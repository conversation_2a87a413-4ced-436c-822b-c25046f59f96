# 🔧 编译监控器配置说明

## 📁 配置文件位置
- **主配置文件**: `appsettings.json`
- **配置模板**: `appsettings.template.json`

## 🔗 数据库连接字符串配置

### 1. 本地SQL Server (Windows身份验证)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=ProjectManagementAI;Integrated Security=true;TrustServerCertificate=true"
  }
}
```

### 2. 远程SQL Server
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=************;Database=ProjectManagementAI;User ID=test_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=false"
  }
}
```

## 🏗️ 项目配置

### 项目ID绑定
```json
{
  "BuildSettings": {
    "ProjectId": 1,  // 对应Projects表中的项目ID
    "BackendProjectPath": "D:\\Projects\\ProjectManagement\\Backend\\ProjectManagement.API\\ProjectManagement.API.csproj",
    "FrontendProjectPath": "D:\\Projects\\ProjectManagement\\Frontend"
  }
}
```

**重要**: ProjectId必须对应数据库Projects表中的有效项目ID，编译错误将关联到此项目。

## ⚙️ 完整配置示例

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=************;Database=ProjectManagementAI;User ID=test_user;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=false"
  },
  "BuildSettings": {
    "ProjectId": 1,
    "BackendProjectPath": "D:\\Projects\\ProjectManagement\\Backend\\ProjectManagement.API\\ProjectManagement.API.csproj",
    "FrontendProjectPath": "D:\\Projects\\ProjectManagement\\Frontend",
    "AutoCompileInterval": 30,
    "MaxErrorsToDisplay": 100,
    "EnableAutoCompile": false,
    "CompileTimeoutSeconds": 300
  },
  "DatabaseSettings": {
    "CommandTimeout": 30,
    "EnableSqlLogging": true,
    "AutoCreateTables": false
  },
  "UISettings": {
    "RefreshInterval": 5,
    "ShowWarnings": true,
    "ShowInfoMessages": false,
    "MaxDisplayRows": 1000
  }
}
```

## 🎯 ProjectId绑定说明

编译错误会自动绑定到配置的ProjectId：
- **后端编译错误**: ProjectId = 1, ProjectType = "Backend"
- **前端编译错误**: ProjectId = 1, ProjectType = "Frontend"

确保ProjectId在Projects表中存在，例如：
```sql
SELECT Id, Name FROM Projects WHERE Id = 1;
-- 结果: 1 | 自动生成前端报表
```

## 🚀 快速配置步骤

1. **设置ProjectId**: 在BuildSettings中设置正确的项目ID
2. **配置数据库**: 设置正确的连接字符串
3. **设置项目路径**: 配置后端和前端项目路径
4. **测试连接**: 运行应用程序验证配置

## ❗ 注意事项

- ProjectId必须是有效的数据库项目ID
- 项目路径必须存在且可访问
- 数据库连接字符串必须正确
- 编译错误将自动关联到指定的项目
