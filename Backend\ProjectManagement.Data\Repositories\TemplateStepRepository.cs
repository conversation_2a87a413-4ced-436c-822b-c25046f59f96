using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 模板步骤仓储实现
    /// </summary>
    public class TemplateStepRepository : BaseRepository<UIAutoMationTemplateStep>, ITemplateStepRepository
    {
        public TemplateStepRepository(ISqlSugarClient db, ILogger<BaseRepository<UIAutoMationTemplateStep>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 根据序列ID获取步骤列表
        /// </summary>
        public async Task<List<UIAutoMationTemplateStep>> GetBySequenceIdAsync(int sequenceId)
        {
            return await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.SequenceId == sequenceId && !x.IsDeleted)
                .OrderBy(x => x.StepOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 根据序列ID获取启用的步骤列表
        /// </summary>
        public async Task<List<UIAutoMationTemplateStep>> GetActiveBySequenceIdAsync(int sequenceId)
        {
            return await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.SequenceId == sequenceId && !x.IsDeleted && x.IsActive)
                .OrderBy(x => x.StepOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取步骤详情（包含关联信息）
        /// </summary>
        public async Task<UIAutoMationTemplateStep?> GetWithRelationsAsync(int id)
        {
            var step = await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.Id == id && !x.IsDeleted)
                .FirstAsync();

            if (step != null)
            {
                // 加载关联的序列信息
                step.Sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(x => x.Id == step.SequenceId)
                    .FirstAsync();

                // 加载关联的模板信息
                if (step.TemplateId.HasValue)
                {
                    step.Template = await _db.Queryable<CustomUIAutoMationTemplate>()
                        .Where(x => x.Id == step.TemplateId.Value)
                        .FirstAsync();
                }
            }

            return step;
        }

        /// <summary>
        /// 批量创建步骤
        /// </summary>
        public async Task<bool> BatchCreateAsync(List<UIAutoMationTemplateStep> steps)
        {
            try
            {
                await _db.Insertable(steps).ExecuteCommandAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 批量更新步骤
        /// </summary>
        public async Task<bool> BatchUpdateAsync(List<UIAutoMationTemplateStep> steps)
        {
            try
            {
                await _db.Updateable(steps).ExecuteCommandAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 删除序列的所有步骤
        /// </summary>
        public async Task<bool> DeleteBySequenceIdAsync(int sequenceId)
        {
            return await _db.Updateable<UIAutoMationTemplateStep>()
                .SetColumns(x => x.IsDeleted == true)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => x.SequenceId == sequenceId)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 重新排序步骤
        /// </summary>
        public async Task<bool> ReorderStepsAsync(int sequenceId, List<int> stepIds)
        {
            try
            {
                _db.Ado.BeginTran();

                for (int i = 0; i < stepIds.Count; i++)
                {
                    await _db.Updateable<UIAutoMationTemplateStep>()
                        .SetColumns(x => x.StepOrder == i + 1)
                        .SetColumns(x => x.UpdatedTime == DateTime.Now)
                        .Where(x => x.Id == stepIds[i] && x.SequenceId == sequenceId)
                        .ExecuteCommandAsync();
                }

                _db.Ado.CommitTran();
                return true;
            }
            catch
            {
                _db.Ado.RollbackTran();
                return false;
            }
        }

        /// <summary>
        /// 获取序列中的最大步骤顺序
        /// </summary>
        public async Task<int> GetMaxStepOrderAsync(int sequenceId)
        {
            var steps = await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.SequenceId == sequenceId && !x.IsDeleted)
                .ToListAsync();

            return steps.Any() ? steps.Max(x => x.StepOrder) : 0;
        }

        /// <summary>
        /// 启用/禁用步骤
        /// </summary>
        public async Task<bool> SetActiveStatusAsync(int id, bool isActive)
        {
            return await _db.Updateable<UIAutoMationTemplateStep>()
                .SetColumns(x => x.IsActive == isActive)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 根据模板ID获取使用该模板的步骤
        /// </summary>
        public async Task<List<UIAutoMationTemplateStep>> GetByTemplateIdAsync(int templateId)
        {
            return await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.TemplateId == templateId && !x.IsDeleted)
                .OrderBy(x => x.SequenceId)
                .OrderBy(x => x.StepOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 检查模板是否被步骤使用
        /// </summary>
        public async Task<bool> IsTemplateUsedAsync(int templateId)
        {
            return await _db.Queryable<UIAutoMationTemplateStep>()
                .Where(x => x.TemplateId == templateId && !x.IsDeleted)
                .AnyAsync();
        }

        /// <summary>
        /// 复制序列的步骤到新序列
        /// </summary>
        public async Task<bool> CopyStepsAsync(int sourceSequenceId, int targetSequenceId)
        {
            try
            {
                var sourceSteps = await GetBySequenceIdAsync(sourceSequenceId);

                var newSteps = sourceSteps.Select(step => new UIAutoMationTemplateStep
                {
                    SequenceId = targetSequenceId,
                    TemplateId = step.TemplateId,
                    StepOrder = step.StepOrder,
                    ActionType = step.ActionType,
                    Description = step.Description,
                    Parameters = step.Parameters,
                    TimeoutSeconds = step.TimeoutSeconds,
                    RetryCount = 0, // 重置重试次数
                    MaxRetries = step.MaxRetries,
                    IsActive = step.IsActive,
                    CreatedTime = DateTime.Now
                }).ToList();

                return await BatchCreateAsync(newSteps);
            }
            catch
            {
                return false;
            }
        }
    }
}
