<template>
  <div class="system-parameter">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">系统参数管理</h1>
        <p class="page-subtitle">管理系统各种配置参数，如技术栈、优先级等选项</p>
      </div>
      <div class="header-right">
        <el-button @click="showCreateCategoryDialog">
          <el-icon><FolderAdd /></el-icon>
          新建分类
        </el-button>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增参数
        </el-button>
      </div>
    </div>

    <!-- 分类选择 -->
    <div class="category-tabs">
      <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
        <el-tab-pane
          v-for="category in categories"
          :key="category"
          :label="getCategoryDisplayName(category)"
          :name="category"
        />
      </el-tabs>
    </div>

    <!-- 参数列表 -->
    <div class="parameter-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ getCategoryDisplayName(activeCategory) }} 参数</span>
            <div class="header-actions">
              <el-switch
                v-model="showInactive"
                @change="loadParameters"
                active-text="显示已禁用"
                inactive-text="仅显示启用"
              />
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="parameters"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="sortOrder" label="排序" width="80" />
          <el-table-column prop="parameterKey" label="参数键" width="150" />
          <el-table-column prop="displayName" label="显示名称" width="200" />
          <el-table-column prop="parameterValue" label="参数值" width="200" />
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
          <el-table-column prop="isActive" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.isActive ? 'success' : 'danger'" size="small">
                {{ row.isActive ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isSystem" label="类型" width="80">
            <template #default="{ row }">
              <el-tag :type="row.isSystem ? 'warning' : 'info'" size="small">
                {{ row.isSystem ? '系统' : '自定义' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="editParameter(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="!row.isSystem"
                type="danger"
                size="small"
                @click="deleteParameter(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑参数' : '新增参数'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="参数分类" prop="category">
          <el-select
            v-model="form.category"
            placeholder="请选择或输入分类"
            :disabled="isEdit"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="getCategoryDisplayName(category)"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="参数键" prop="parameterKey">
          <el-input
            v-model="form.parameterKey"
            placeholder="请输入参数键（英文）"
            :disabled="isEdit && currentParameter?.isSystem"
          />
        </el-form-item>

        <el-form-item label="显示名称" prop="displayName">
          <el-input
            v-model="form.displayName"
            placeholder="请输入显示名称"
          />
        </el-form-item>

        <el-form-item label="参数值" prop="parameterValue">
          <el-input
            v-model="form.parameterValue"
            placeholder="请输入参数值"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入参数描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number
                v-model="form.sortOrder"
                :min="0"
                :max="999"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-switch
                v-model="form.isActive"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="扩展属性">
          <el-input
            v-model="form.extendedProperties"
            type="textarea"
            :rows="3"
            placeholder="JSON格式的扩展属性（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitForm"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新建分类对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      title="新建分类"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类键名" prop="categoryKey">
          <el-input
            v-model="categoryForm.categoryKey"
            placeholder="请输入分类键名（英文，如：MyCategory）"
          />
          <div class="form-tip">
            分类键名用于系统内部标识，建议使用英文
          </div>
        </el-form-item>

        <el-form-item label="显示名称" prop="displayName">
          <el-input
            v-model="categoryForm.displayName"
            placeholder="请输入分类显示名称（如：我的分类）"
          />
          <div class="form-tip">
            显示名称用于界面展示，可以使用中文
          </div>
        </el-form-item>

        <el-form-item label="分类描述">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="creatingCategory"
            @click="createCategory"
          >
            创建分类
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, FolderAdd } from '@element-plus/icons-vue'
import { SystemParameterService, SystemParameterCategory } from '@/services/systemParameter'
import type { SystemParameter, CreateSystemParameterRequest, UpdateSystemParameterRequest } from '@/services/systemParameter'
import type { FormInstance, FormRules } from 'element-plus'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const showInactive = ref(false)
const activeCategory = ref('TechnologyStack')
const categories = ref<string[]>([])
const parameters = ref<SystemParameter[]>([])
const currentParameter = ref<SystemParameter | null>(null)

// 新建分类相关
const categoryDialogVisible = ref(false)
const creatingCategory = ref(false)
const categoryFormRef = ref<FormInstance>()
const categoryForm = reactive({
  categoryKey: '',
  displayName: '',
  description: ''
})

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive<CreateSystemParameterRequest>({
  category: '',
  parameterKey: '',
  parameterValue: '',
  displayName: '',
  description: '',
  sortOrder: 0,
  isActive: true,
  extendedProperties: ''
})

// 表单验证规则
const rules: FormRules = {
  category: [
    { required: true, message: '请选择参数分类', trigger: 'change' }
  ],
  parameterKey: [
    { required: true, message: '请输入参数键', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '参数键只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  parameterValue: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ]
}

// 新建分类表单验证规则
const categoryRules: FormRules = {
  categoryKey: [
    { required: true, message: '请输入分类键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '分类键名只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ]
}

// 分类显示名称映射
const categoryDisplayNames: Record<string, string> = {
  TechnologyStack: '技术栈',
  Priority: '优先级',
  ProjectStatus: '项目状态',
  RequirementStatus: '需求状态',
  StepType: '步骤类型',
  ComponentType: '组件类型',
  AIProvider: 'AI提供商',
  DatabaseType: '数据库类型',
  PrototypeType: '原型类型',
  DeviceType: '设备类型',
  FidelityLevel: '保真度级别',
  MessageType: '消息类型',
  TaskStatus: '任务状态',
  UserRole: '用户角色',
  FileExtension: '文件扩展名',
  Framework: '框架',
  Tool: '工具',
  Other: '其他'
}

// 方法
const getCategoryDisplayName = (category: string) => {
  return categoryDisplayNames[category] || category
}

const showCreateCategoryDialog = () => {
  categoryForm.categoryKey = ''
  categoryForm.displayName = ''
  categoryForm.description = ''
  categoryFormRef.value?.clearValidate()
  categoryDialogVisible.value = true
}

const createCategory = async () => {
  if (!categoryFormRef.value) return

  try {
    const valid = await categoryFormRef.value.validate()
    if (!valid) return

    creatingCategory.value = true

    // 创建一个示例参数来建立新分类
    const sampleParameter = {
      category: categoryForm.categoryKey,
      parameterKey: 'sample',
      parameterValue: 'sample',
      displayName: '示例参数（可删除）',
      description: `${categoryForm.displayName} 分类的示例参数，您可以删除此参数并添加自己的参数`,
      sortOrder: 0,
      isActive: true,
      extendedProperties: JSON.stringify({
        categoryDisplayName: categoryForm.displayName,
        categoryDescription: categoryForm.description,
        isSampleParameter: true
      })
    }

    await SystemParameterService.createParameter(sampleParameter)

    // 更新分类显示名称映射
    categoryDisplayNames[categoryForm.categoryKey] = categoryForm.displayName

    ElMessage.success(`分类 "${categoryForm.displayName}" 创建成功`)
    categoryDialogVisible.value = false

    // 重新加载分类列表
    await loadCategories()

    // 切换到新创建的分类
    activeCategory.value = categoryForm.categoryKey
    await loadParameters()
  } catch (error: any) {
    console.error('创建分类失败:', error)
    ElMessage.error(error.response?.data?.message || '创建分类失败')
  } finally {
    creatingCategory.value = false
  }
}

const loadCategories = async () => {
  try {
    categories.value = await SystemParameterService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  }
}

const loadParameters = async () => {
  try {
    loading.value = true
    parameters.value = await SystemParameterService.getParametersByCategory(
      activeCategory.value,
      showInactive.value
    )
  } catch (error) {
    console.error('加载参数失败:', error)
    ElMessage.error('加载参数失败')
  } finally {
    loading.value = false
  }
}

const handleCategoryChange = () => {
  loadParameters()
}

const showCreateDialog = () => {
  isEdit.value = false
  currentParameter.value = null
  resetForm()
  form.category = activeCategory.value
  dialogVisible.value = true
}

const editParameter = (parameter: SystemParameter) => {
  isEdit.value = true
  currentParameter.value = parameter
  Object.assign(form, {
    category: parameter.category,
    parameterKey: parameter.parameterKey,
    parameterValue: parameter.parameterValue,
    displayName: parameter.displayName,
    description: parameter.description || '',
    sortOrder: parameter.sortOrder,
    isActive: parameter.isActive,
    extendedProperties: parameter.extendedProperties || ''
  })
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    category: '',
    parameterKey: '',
    parameterValue: '',
    displayName: '',
    description: '',
    sortOrder: 0,
    isActive: true,
    extendedProperties: ''
  })
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (isEdit.value && currentParameter.value) {
      // 更新参数
      const updateData: UpdateSystemParameterRequest = {
        parameterKey: form.parameterKey,
        parameterValue: form.parameterValue,
        displayName: form.displayName,
        description: form.description,
        sortOrder: form.sortOrder,
        isActive: form.isActive,
        extendedProperties: form.extendedProperties
      }
      await SystemParameterService.updateParameter(currentParameter.value.id, updateData)
      ElMessage.success('参数更新成功')
    } else {
      // 创建参数
      await SystemParameterService.createParameter(form)
      ElMessage.success('参数创建成功')
    }

    dialogVisible.value = false
    loadParameters()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const deleteParameter = async (parameter: SystemParameter) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除参数 "${parameter.displayName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await SystemParameterService.deleteParameter(parameter.id)
    ElMessage.success('删除成功')
    loadParameters()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
  loadParameters()
})
</script>

<style lang="scss" scoped>
.system-parameter {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .header-left {
    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .page-subtitle {
      margin: 0;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
}

.category-tabs {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}
</style>
