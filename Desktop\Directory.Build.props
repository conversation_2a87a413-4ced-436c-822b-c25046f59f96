<Project>
  <PropertyGroup>
    <!-- 通用属性 -->
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    
    <!-- 版本信息 -->
    <Version>1.0.0</Version>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
    <InformationalVersion>1.0.0</InformationalVersion>
    
    <!-- 公司信息 -->
    <Company>ProjectManagement AI</Company>
    <Product>AI项目管理系统</Product>
    <Copyright>Copyright © 2024 ProjectManagement AI</Copyright>
    <Authors>ProjectManagement AI Team</Authors>
    
    <!-- 包信息 -->
    <PackageProjectUrl>https://github.com/projectmanagement-ai</PackageProjectUrl>
    <RepositoryUrl>https://github.com/projectmanagement-ai/desktop</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    
    <!-- 构建配置 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <DebugType>embedded</DebugType>
    <DebugSymbols>true</DebugSymbols>
    
    <!-- 分析器 -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <!-- Debug配置 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <!-- Release配置 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PublishTrimmed>true</PublishTrimmed>
    <PublishSingleFile>true</PublishSingleFile>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

  <!-- WPF特定配置 -->
  <PropertyGroup Condition="'$(UseWPF)' == 'true'">
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>false</UseWindowsForms>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <!-- 通用包引用已移至各项目的csproj文件中，避免重复引用 -->

  <!-- 测试项目包引用已移至各测试项目的csproj文件中，避免重复引用 -->
</Project>
