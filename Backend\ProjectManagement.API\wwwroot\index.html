<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI驱动软件开发自动化系统 API</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .links {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .link-button {
            background: #667eea;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 5px;
            transition: background 0.3s;
            display: inline-block;
        }
        .link-button:hover {
            background: #5a6fd8;
        }
        .api-info {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }
        .status {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI驱动软件开发自动化系统</h1>
        <div class="description">
            <p>欢迎使用AI驱动软件开发自动化系统API服务！</p>
            <p class="status">✅ API服务正在运行</p>
        </div>
        
        <div class="api-info">
            <h3>📋 API信息</h3>
            <ul>
                <li><strong>API版本:</strong> v1.0</li>
                <li><strong>环境:</strong> Production</li>
                <li><strong>功能:</strong> 需求分析、代码生成、测试、部署自动化</li>
                <li><strong>支持:</strong> JWT认证、SignalR实时通信、文件上传</li>
            </ul>
        </div>
        
        <div class="links">
            <a href="/swagger" class="link-button">📖 API文档 (Swagger)</a>
            <a href="/api/health" class="link-button">🔍 健康检查</a>
            <a href="/api/Auth/login" class="link-button">🔐 登录接口</a>
        </div>
        
        <div class="api-info">
            <h3>🔗 常用API端点</h3>
            <ul>
                <li><code>POST /api/Auth/login</code> - 用户登录</li>
                <li><code>GET /api/Projects</code> - 获取项目列表</li>
                <li><code>GET /api/DevelopmentSteps</code> - 获取开发步骤</li>
                <li><code>POST /api/AI/analyze-requirements</code> - AI需求分析</li>
                <li><code>GET /api/UIAutomationTemplateSequences</code> - UI自动化模板</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 检查API健康状态
        fetch('/api/health')
            .then(response => {
                if (response.ok) {
                    console.log('✅ API服务健康检查通过');
                } else {
                    console.warn('⚠️ API服务可能存在问题');
                }
            })
            .catch(error => {
                console.error('❌ 无法连接到API服务:', error);
            });
    </script>
</body>
</html>
