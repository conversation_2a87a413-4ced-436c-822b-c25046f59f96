using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs.KnowledgeGraph;
using ProjectManagement.AI.Services;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 知识图谱API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class KnowledgeGraphController : ControllerBase
    {
        private readonly KnowledgeGraphService _knowledgeGraphService;
        private readonly ILogger<KnowledgeGraphController> _logger;

        public KnowledgeGraphController(KnowledgeGraphService knowledgeGraphService, ILogger<KnowledgeGraphController> logger)
        {
            _knowledgeGraphService = knowledgeGraphService;
            _logger = logger;
        }

        /// <summary>
        /// 查找相似项目
        /// </summary>
        [HttpGet("projects/{projectId}/similar")]
        public async Task<ActionResult<List<ProjectSimilarity>>> FindSimilarProjects(int projectId, [FromQuery] int topK = 5)
        {
            try
            {
                var similarProjects = await _knowledgeGraphService.FindSimilarProjectsAsync(projectId, topK);
                return Ok(similarProjects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找相似项目失败: {ProjectId}", projectId);
                return StatusCode(500, new { error = "查找相似项目失败" });
            }
        }

        /// <summary>
        /// 获取专家推荐
        /// </summary>
        [HttpPost("experts/recommend")]
        public async Task<ActionResult<List<ExpertRecommendation>>> GetExpertRecommendations([FromBody] ExpertRecommendationRequest request)
        {
            try
            {
                var recommendations = await _knowledgeGraphService.GetExpertRecommendationsAsync(
                    request.SkillRequired, 
                    request.ProblemDomain, 
                    request.TopK);
                
                return Ok(recommendations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取专家推荐失败");
                return StatusCode(500, new { error = "获取专家推荐失败" });
            }
        }

        /// <summary>
        /// 获取最佳实践
        /// </summary>
        [HttpPost("best-practices")]
        public async Task<ActionResult<List<BestPractice>>> GetBestPractices([FromBody] BestPracticeRequest request)
        {
            try
            {
                var practices = await _knowledgeGraphService.GetBestPracticesAsync(request.Technology, request.Context);
                return Ok(practices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取最佳实践失败");
                return StatusCode(500, new { error = "获取最佳实践失败" });
            }
        }

        /// <summary>
        /// 构建项目知识图谱
        /// </summary>
        [HttpPost("projects/{projectId}/build")]
        public async Task<ActionResult> BuildProjectKnowledgeGraph(int projectId, [FromBody] ProjectKnowledgeData data)
        {
            try
            {
                await _knowledgeGraphService.BuildProjectKnowledgeGraphAsync(projectId, data);
                return Ok(new { message = "项目知识图谱构建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建项目知识图谱失败: {ProjectId}", projectId);
                return StatusCode(500, new { error = "构建项目知识图谱失败" });
            }
        }

        /// <summary>
        /// 创建知识实体
        /// </summary>
        [HttpPost("entities")]
        public async Task<ActionResult<KnowledgeEntity>> CreateEntity([FromBody] CreateEntityRequest request)
        {
            try
            {
                var entity = await _knowledgeGraphService.CreateEntityAsync(
                    request.Type, 
                    request.Name, 
                    request.Properties);
                
                return Ok(entity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建知识实体失败");
                return StatusCode(500, new { error = "创建知识实体失败" });
            }
        }

        /// <summary>
        /// 创建实体关系
        /// </summary>
        [HttpPost("relations")]
        public async Task<ActionResult<KnowledgeRelation>> CreateRelation([FromBody] CreateRelationRequest request)
        {
            try
            {
                var relation = await _knowledgeGraphService.CreateRelationAsync(
                    request.FromEntityId,
                    request.ToEntityId,
                    request.RelationType,
                    request.Weight);
                
                return Ok(relation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建实体关系失败");
                return StatusCode(500, new { error = "创建实体关系失败" });
            }
        }
    }

    // 请求DTO类
    public class ExpertRecommendationRequest
    {
        public string SkillRequired { get; set; } = string.Empty;
        public string ProblemDomain { get; set; } = string.Empty;
        public int TopK { get; set; } = 3;
    }

    public class BestPracticeRequest
    {
        public string Technology { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
    }

    public class CreateEntityRequest
    {
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    public class CreateRelationRequest
    {
        public int FromEntityId { get; set; }
        public int ToEntityId { get; set; }
        public string RelationType { get; set; } = string.Empty;
        public float Weight { get; set; } = 1.0f;
    }
}
