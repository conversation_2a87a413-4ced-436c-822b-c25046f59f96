-- =============================================
-- 脚本名称: 008_MermaidSequencePromptTemplates.sql
-- 脚本描述: 添加 Mermaid 序列生成相关的提示词模板
-- 创建时间: 2025-06-24
-- 功能说明:
--   1. 添加自然语言生成 Mermaid 流程图的提示词模板
--   2. 添加 Mermaid 转换为序列步骤的提示词模板
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '======================================================';
PRINT '开始执行数据脚本: 008_MermaidSequencePromptTemplates.sql';
PRINT '功能: 添加 Mermaid 序列生成相关的提示词模板';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '======================================================';
PRINT '';

-- =============================================
-- 1. 添加自然语言生成 Mermaid 流程图的提示词模板
-- =============================================

IF NOT EXISTS (SELECT 1 FROM PromptTemplates WHERE Name = 'GenerateMermaidSequence')
BEGIN
    PRINT '插入 GenerateMermaidSequence 提示词模板...';

    -- 获取或创建分类ID
    DECLARE @CategoryId INT;
    SELECT @CategoryId = Id FROM PromptCategories WHERE Name = 'AI生成';

    -- 如果分类不存在，创建一个
    IF @CategoryId IS NULL
    BEGIN
        INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, CreatedTime)
        VALUES ('AI生成', '用于AI智能生成各种内容的提示词模板', 'magic', '#722ed1', 10, GETDATE());

        SELECT @CategoryId = Id FROM PromptCategories WHERE Name = 'AI生成';
        PRINT '✓ 已创建 AI生成 分类';
    END

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, TemplateType, TaskType, Tags, IsEnabled, CreatedTime
    ) VALUES (
        'GenerateMermaidSequence',
        '自然语言生成 Mermaid 序列流程图',
        @CategoryId,
        '你是一个专业的自动化流程设计师，擅长将自然语言描述转换为清晰的 Mermaid 流程图。

请根据以下描述生成一个 Mermaid 流程图，用于表示自动化序列的执行流程：

**序列信息：**
- 序列名称：{{sequenceName}}
- 序列描述：{{sequenceDescription}}
- 技术栈：{{techStack}}
- 生成时间：{{timestamp}}

**生成要求：**
1. 使用 flowchart TD 格式（从上到下的流程图）
2. 必须包含 Start([开始]) 和 End([结束]) 节点
3. 根据描述分析并添加适当的步骤节点
4. 使用合适的节点形状表示不同类型的操作：
   - `[文本]` 表示基础操作（点击、输入、等待等）
   - `{文本}` 表示条件判断（if-else逻辑）
   - `[[文本]]` 表示循环操作（for、while等）
   - `>文本]` 表示跳转/退出操作
   - `([文本])` 表示开始/结束节点
5. 添加适当的连接线和标签：
   - `-->` 表示普通连接
   - `-->|标签|` 表示带条件的连接
   - 条件判断使用 `-->|是|` 和 `-->|否|` 分支
6. 考虑错误处理和重试逻辑
7. 确保流程逻辑清晰、完整

**技术栈特定考虑：**
- Web应用：考虑页面加载、元素等待、表单提交等
- 桌面应用：考虑窗口切换、控件识别、键盘快捷键等
- VS Code：考虑命令面板、文件操作、插件交互等
- 移动应用：考虑触摸操作、屏幕旋转、应用切换等

**输出格式：**
只返回纯 Mermaid 代码，不要包含任何其他文字说明、markdown 标记或代码块符号。

**示例格式：**
```
flowchart TD
    Start([开始]) --> Step1[打开应用]
    Step1 --> Step2{检查登录状态}
    Step2 -->|已登录| Step5[进入主界面]
    Step2 -->|未登录| Step3[显示登录页面]
    Step3 --> Step4[输入用户名密码]
    Step4 --> Step6{验证登录}
    Step6 -->|成功| Step5
    Step6 -->|失败| Step7[显示错误信息]
    Step7 --> Step3
    Step5 --> End([结束])
```

现在请根据上述要求生成对应的 Mermaid 流程图：',
        'System',
        'RequirementDecomposition',
        'mermaid,流程图,自动化,AI生成',
        1,
        GETDATE()
    );

    PRINT '✓ GenerateMermaidSequence 提示词模板创建完成';
END
ELSE
BEGIN
    PRINT '⚠ GenerateMermaidSequence 提示词模板已存在，跳过创建';
END

-- =============================================
-- 2. 添加 Mermaid 转换为序列步骤的提示词模板
-- =============================================

IF NOT EXISTS (SELECT 1 FROM PromptTemplates WHERE Name = 'ConvertMermaidToSequence')
BEGIN
    PRINT '插入 ConvertMermaidToSequence 提示词模板...';

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, TemplateType, TaskType, Tags, IsEnabled, CreatedTime
    ) VALUES (
        'ConvertMermaidToSequence',
        'Mermaid 流程图转换为模板序列步骤',
        @CategoryId,
        '你是一个专业的自动化测试工程师，擅长将流程图转换为可执行的自动化步骤。

请将以下 Mermaid 流程图转换为模板序列的 JSON 格式：

**输入信息：**
- Mermaid 代码：
```
{{mermaidCode}}
```
- 序列名称：{{sequenceName}}
- 序列描述：{{sequenceDescription}}
- 生成时间：{{timestamp}}

**转换规则：**
1. **节点类型映射：**
   - 矩形节点 `[文本]` → 基础操作类型：
     - 包含"点击"、"click" → actionType: "click"
     - 包含"输入"、"input" → actionType: "input"
     - 包含"等待"、"wait" → actionType: "wait"
     - 包含"截图"、"screenshot" → actionType: "screenshot"
     - 包含"验证"、"verify" → actionType: "verify"
     - 包含"滚动"、"scroll" → actionType: "scroll"
     - 包含"按键"、"key" → actionType: "key_press"
     - 包含"延迟"、"delay" → actionType: "delay"
     - 其他 → actionType: "click"
   - 菱形节点 `{文本}` → actionType: "condition"
   - 子程序节点 `[[文本]]` →
     - 包含"开始"、"循环" → actionType: "loop"
     - 包含"结束" → actionType: "loop_end"
   - 旗帜节点 `>文本]` →
     - 包含"跳转" → actionType: "jump"
     - 包含"退出" → actionType: "exit"

2. **步骤顺序：**
   - 按照流程图中的逻辑顺序分配 stepOrder
   - 跳过 Start 和 End 节点
   - 确保步骤编号连续

3. **参数设置：**
   - 基础操作：根据描述推断合适的参数
   - 条件判断：设置 conditionExpression 和分支参数
   - 循环：设置 loopCount、loopVariable、groupId
   - 跳转：设置 jumpToStepId

4. **默认值：**
   - timeoutSeconds: 5（条件判断和循环为3）
   - maxRetries: 3（条件判断和循环为1）
   - isActive: true

**输出格式：**
返回完整的 JSON 格式序列定义，不要包含任何其他文字说明：

```json
{
  "name": "序列名称",
  "description": "序列描述",
  "category": "AI生成",
  "isActive": true,
  "tags": ["AI生成", "Mermaid", "自动化"],
  "notes": "从 Mermaid 流程图自动生成的序列",
  "steps": [
    {
      "stepOrder": 1,
      "actionType": "click",
      "description": "步骤描述",
      "parameters": {
        "template_name": "目标元素",
        "wait_after": 1
      },
      "timeoutSeconds": 5,
      "maxRetries": 3,
      "isActive": true
    },
    {
      "stepOrder": 2,
      "actionType": "condition",
      "description": "条件判断描述",
      "parameters": {
        "true_action": "continue",
        "false_action": "jump_to_step",
        "false_target": 5
      },
      "conditionExpression": "{result} === ''success''",
      "timeoutSeconds": 3,
      "maxRetries": 1,
      "isActive": true
    },
    {
      "stepOrder": 3,
      "actionType": "loop",
      "description": "循环开始",
      "parameters": {
        "max_iterations": 5,
        "exit_condition": "{counter} >= 5"
      },
      "loopCount": 5,
      "loopVariable": "i",
      "groupId": "main_loop",
      "timeoutSeconds": 3,
      "maxRetries": 1,
      "isActive": true
    }
  ]
}
```

现在请根据上述规则转换提供的 Mermaid 流程图：',
        'System',
        'RequirementDecomposition',
        'mermaid,转换,序列,自动化,JSON',
        1,
        GETDATE()
    );

    PRINT '✓ ConvertMermaidToSequence 提示词模板创建完成';
END
ELSE
BEGIN
    PRINT '⚠ ConvertMermaidToSequence 提示词模板已存在，跳过创建';
END

PRINT '';
PRINT '✅ Mermaid 序列生成提示词模板脚本执行完成！';
PRINT '======================================================';
PRINT '已添加以下提示词模板：';
PRINT '1. GenerateMermaidSequence - 自然语言生成 Mermaid 流程图';
PRINT '2. ConvertMermaidToSequence - Mermaid 转换为序列步骤';
PRINT '';
PRINT '新增功能特性：';
PRINT '- 支持自然语言描述转 Mermaid 流程图';
PRINT '- 支持 Mermaid 流程图转可执行序列步骤';
PRINT '- 智能识别不同类型的操作和控制结构';
PRINT '- 自动生成合适的参数和配置';
PRINT '';
PRINT '使用方式：';
PRINT '1. 前端调用 /api/AI/generate-mermaid-sequence 生成流程图';
PRINT '2. 前端调用 /api/AI/convert-mermaid-to-sequence 转换为序列';
PRINT '3. AI 将根据模板生成相应的结果';
PRINT '';
PRINT '脚本执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
