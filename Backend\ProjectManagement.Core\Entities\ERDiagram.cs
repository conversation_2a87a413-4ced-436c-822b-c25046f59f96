using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// ER图实体 - 对应DatabaseSchema.sql中的ERDiagrams表
/// 功能: 存储AI生成的数据库实体关系图
/// 支持: Mermaid格式、版本控制、可视化展示
/// </summary>
[SugarTable("ERDiagrams", "实体关系图管理表")]
public class ERDiagram : BaseEntity
{



    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的需求文档ID，可为空")]
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// ER图名称（如：用户管理模块ER图）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "ER图名称")]
    public string DiagramName { get; set; } = string.Empty;

    /// <summary>
    /// Mermaid格式的ER图定义代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "Mermaid格式的ER图定义代码")]
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// ER图描述说明
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "ER图描述说明")]
    public string? Description { get; set; }

    /// <summary>
    /// ER图版本号（如：1.0, 1.1, 2.0）
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "ER图版本号")]
    public string DiagramVersion { get; set; } = "1.0";





    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的需求文档 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RequirementDocument? RequirementDocument { get; set; }
}
