


-- =============================================
-- Issues表结构更新脚本
-- 目的: 修复列名不匹配问题，将CreatedAt/UpdatedAt改为CreatedTime/UpdatedTime
-- 并添加BaseEntity中的其他字段
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始更新Issues表结构...';

-- 检查表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Issues')
BEGIN
    PRINT 'Issues表存在，开始结构更新...';
    
    -- 1. 添加新的BaseEntity字段（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'CreatedTime')
    BEGIN
        ALTER TABLE Issues ADD CreatedTime datetime2 NOT NULL DEFAULT GETDATE();
        PRINT '添加CreatedTime字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'UpdatedTime')
    BEGIN
        ALTER TABLE Issues ADD UpdatedTime datetime2 NULL;
        PRINT '添加UpdatedTime字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'CreatedBy')
    BEGIN
        ALTER TABLE Issues ADD CreatedBy int NULL;
        PRINT '添加CreatedBy字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'UpdatedBy')
    BEGIN
        ALTER TABLE Issues ADD UpdatedBy int NULL;
        PRINT '添加UpdatedBy字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'IsDeleted')
    BEGIN
        ALTER TABLE Issues ADD IsDeleted bit NOT NULL DEFAULT 0;
        PRINT '添加IsDeleted字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'DeletedTime')
    BEGIN
        ALTER TABLE Issues ADD DeletedTime datetime2 NULL;
        PRINT '添加DeletedTime字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'DeletedBy')
    BEGIN
        ALTER TABLE Issues ADD DeletedBy int NULL;
        PRINT '添加DeletedBy字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'Version')
    BEGIN
        ALTER TABLE Issues ADD Version int NOT NULL DEFAULT 1;
        PRINT '添加Version字段';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'Remarks')
    BEGIN
        ALTER TABLE Issues ADD Remarks nvarchar(500) NULL;
        PRINT '添加Remarks字段';
    END
    
    -- 2. 如果存在旧的CreatedAt和UpdatedAt字段，迁移数据并删除
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'CreatedAt')
    BEGIN
        -- 迁移CreatedAt数据到CreatedTime
        UPDATE Issues SET CreatedTime = CreatedAt WHERE CreatedAt IS NOT NULL;
        PRINT '迁移CreatedAt数据到CreatedTime';
        
        -- 删除CreatedAt字段
        ALTER TABLE Issues DROP COLUMN CreatedAt;
        PRINT '删除CreatedAt字段';
    END
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Issues') AND name = 'UpdatedAt')
    BEGIN
        -- 迁移UpdatedAt数据到UpdatedTime
        UPDATE Issues SET UpdatedTime = UpdatedAt WHERE UpdatedAt IS NOT NULL;
        PRINT '迁移UpdatedAt数据到UpdatedTime';
        
        -- 删除UpdatedAt字段
        ALTER TABLE Issues DROP COLUMN UpdatedAt;
        PRINT '删除UpdatedAt字段';
    END
    
    -- 3. 添加外键约束（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Issues_CreatedBy')
    BEGIN
        ALTER TABLE Issues ADD CONSTRAINT FK_Issues_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id);
        PRINT '添加CreatedBy外键约束';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Issues_UpdatedBy')
    BEGIN
        ALTER TABLE Issues ADD CONSTRAINT FK_Issues_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id);
        PRINT '添加UpdatedBy外键约束';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Issues_DeletedBy')
    BEGIN
        ALTER TABLE Issues ADD CONSTRAINT FK_Issues_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id);
        PRINT '添加DeletedBy外键约束';
    END
    
    -- 4. 添加索引（如果不存在）
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Issues') AND name = 'IX_Issues_CreatedTime')
    BEGIN
        CREATE INDEX IX_Issues_CreatedTime ON Issues(CreatedTime);
        PRINT '添加CreatedTime索引';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Issues') AND name = 'IX_Issues_IsDeleted')
    BEGIN
        CREATE INDEX IX_Issues_IsDeleted ON Issues(IsDeleted);
        PRINT '添加IsDeleted索引';
    END
    
    PRINT 'Issues表结构更新完成！';
END
ELSE
BEGIN
    PRINT '错误：Issues表不存在！';
END

-- 验证更新结果
PRINT '验证Issues表结构：';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Issues'
ORDER BY ORDINAL_POSITION;

PRINT 'Issues表结构更新脚本执行完成！';
