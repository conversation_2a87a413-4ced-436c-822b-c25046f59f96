<template>
  <el-dialog
    v-model="dialogVisible"
    :title="showPreview ? '需求分解结果预览' : '需求分解'"
    :width="showPreview ? '1200px' : '800px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 分解模式选择 -->
      <el-form-item label="分解模式">
        <el-radio-group v-model="decomposeMode">
          <el-radio label="document" v-if="requirementDocumentId">
            基于需求文档分解
          </el-radio>
          <el-radio label="project">
            基于项目信息分解
          </el-radio>
          <el-radio label="content">
            基于需求内容分解
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 需求内容输入 -->
      <el-form-item
        label="需求内容"
        prop="requirementContent"
        v-if="decomposeMode === 'content'"
      >
        <el-input
          v-model="form.requirementContent"
          type="textarea"
          :rows="8"
          placeholder="请输入需求描述..."
          show-word-limit
          maxlength="5000"
        />
      </el-form-item>

      <!-- 技术栈选择 -->
      <el-form-item label="技术栈" prop="technologyStack">
        <el-select
          v-model="form.technologyStack"
          placeholder="请选择技术栈"
          style="width: 100%"
          :loading="loadingProject"
          @change="handleTechnologyStackChange"
        >
          <el-option
            v-for="stack in technologyStacks"
            :key="stack.name"
            :label="stack.displayName"
            :value="stack.name"
          >
            <div class="tech-stack-option">
              <div class="name">
                {{ stack.displayName }}
                <el-tag
                  v-if="projectInfo?.technologyStack && stack.description.includes('项目技术栈')"
                  size="small"
                  type="primary"
                  style="margin-left: 8px;"
                >
                  项目默认
                </el-tag>
              </div>
              <div class="description">{{ stack.description }}</div>
            </div>
          </el-option>
        </el-select>
        <div v-if="loadingProject" class="form-tip">
          正在加载项目技术栈信息...
        </div>
        <div v-else-if="projectInfo?.technologyStack" class="form-tip">
          已从项目配置中加载技术栈信息
        </div>
      </el-form-item>

      <!-- 分解粒度 -->
      <el-form-item label="分解粒度">
        <el-radio-group v-model="form.granularity">
          <el-radio label="Coarse">粗粒度（大模块）</el-radio>
          <el-radio label="Medium">中等粒度（推荐）</el-radio>
          <el-radio label="Fine">细粒度（详细步骤）</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 最大步骤数 -->
      <el-form-item label="最大步骤数">
        <el-input-number
          v-model="form.maxStepCount"
          :min="5"
          :max="200"
          :step="5"
          :precision="0"
          controls
          controls-position="right"
          style="width: 200px"
          placeholder="请输入步骤数"
        />
        <span class="form-tip">控制AI生成的步骤数量，建议20-50个</span>
      </el-form-item>

      <!-- 分解模式 -->
      <el-form-item label="分解模式" v-if="hasExistingSteps">
        <el-radio-group v-model="form.decompositionMode">
          <el-radio label="replace">覆盖现有步骤</el-radio>
          <el-radio label="append">添加到现有步骤</el-radio>
        </el-radio-group>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          <span v-if="form.decompositionMode === 'replace'">
            将删除现有的开发步骤，重新生成新的步骤
          </span>
          <span v-else>
            保留现有步骤，在此基础上添加新的步骤
          </span>
        </div>
      </el-form-item>

      <!-- 包含选项 -->
      <el-form-item label="包含步骤">
        <el-checkbox-group v-model="includeOptions">
          <el-checkbox label="test">测试步骤</el-checkbox>
          <el-checkbox label="documentation">文档步骤</el-checkbox>
          <el-checkbox label="dependencies">自动分析依赖</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- AI供应商选择 -->
      <el-form-item label="AI模型" prop="aiProvider" required>
        <el-select
          v-model="form.aiProvider"
          placeholder="选择AI模型"
          style="width: 100%"
          :loading="loadingAIProviders"
        >
          <el-option
            v-for="provider in aiProviders"
            :key="provider.id"
            :label="`${provider.modelName} (${provider.apiEndpoint || 'Default'})`"
            :value="provider.id"
            :disabled="!provider.isActive"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>{{ provider.modelName }}</span>
              <el-tag v-if="!provider.isActive" type="info" size="small">已禁用</el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 高级选项
      <el-form-item>
        <el-button
          type="text"
          @click="showAdvanced = !showAdvanced"
          style="padding: 0;"
        >
          {{ showAdvanced ? '收起' : '展开' }}高级选项
          <el-icon :class="{ 'rotate-180': showAdvanced }">
            <ArrowDown />
          </el-icon>
        </el-button>
      </el-form-item> -->

      <!-- 高级选项内容
      <div v-show="showAdvanced" class="advanced-options">
        <el-form-item label="AI提供商">
          <el-select v-model="form.aiProvider" placeholder="使用默认配置" clearable>
            <el-option label="Azure OpenAI" value="Azure" />
            <el-option label="OpenAI" value="OpenAI" />
            <el-option label="DeepSeek" value="DeepSeek" />
            <el-option label="Claude" value="Claude" />
            <el-option label="Mock (测试)" value="Mock" />
          </el-select>
        </el-form-item>

        <el-form-item label="AI模型">
          <el-select v-model="form.aiModel" placeholder="使用默认模型" clearable>
            <el-option
              v-for="model in availableModels"
              :key="model"
              :label="model"
              :value="model"
            />
          </el-select>
        </el-form-item>
      </div> -->

      <!-- 预览信息 -->
      <el-form-item label="预览信息" v-if="selectedTechStack">
        <el-card class="preview-card" shadow="never">
          <div class="preview-content">
            <div class="preview-item">
              <span class="label">技术栈：</span>
              <span>{{ selectedTechStack.displayName }}</span>
            </div>
            <div class="preview-item">
              <span class="label">框架：</span>
              <el-tag
                v-for="framework in selectedTechStack.frameworks"
                :key="framework"
                size="small"
                style="margin-right: 4px;"
              >
                {{ framework }}
              </el-tag>
            </div>
            <div class="preview-item">
              <span class="label">默认步骤类型：</span>
              <el-tag
                v-for="stepType in selectedTechStack.defaultStepTypes"
                :key="stepType"
                :type="getStepTypeTagType(stepType)"
                size="small"
                style="margin-right: 4px;"
              >
                {{ getStepTypeIcon(stepType) }} {{ stepType }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-form-item>
    </el-form>

    <!-- 预览界面 -->
    <div v-if="showPreview && decomposeResult" class="preview-container">
      <el-alert
        title="预览模式"
        type="warning"
        description="这些步骤尚未保存到数据库，请确认后保存"
        show-icon
        :closable="false"
        style="margin-bottom: 16px;"
      />

      <!-- 统计信息 -->
      <el-card v-if="decomposeResult.data?.statistics" class="statistics-card" style="margin-bottom: 16px;">
        <template #header>
          <span>分解统计</span>
        </template>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总步骤数" :value="decomposeResult.data.statistics.totalSteps" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="开发步骤" :value="decomposeResult.data.statistics.developmentSteps" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="测试步骤" :value="decomposeResult.data.statistics.testSteps" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="预估工时" :value="decomposeResult.data.statistics.estimatedTotalHours" suffix="小时" />
          </el-col>
        </el-row>
      </el-card>

      <!-- 步骤列表 -->
      <el-card class="steps-card">
        <template #header>
          <span>开发步骤 ({{ decomposeResult.data?.steps?.length || 0 }})</span>
        </template>
        <div class="steps-list" style="max-height: 400px; overflow-y: auto;">
          <el-card
            v-for="(step, index) in decomposeResult.data?.steps"
            :key="step.id"
            class="step-card"
            style="margin-bottom: 12px;"
            shadow="hover"
          >
            <div class="step-header">
              <div class="step-title">
                <el-tag :type="getPriorityType(step.priority)" size="small" style="margin-right: 8px;">
                  {{ step.priority }}
                </el-tag>
                <span class="step-name">{{ step.stepName }}</span>
              </div>
              <div class="step-actions">
                <div class="step-meta">
                  <el-tag size="small" style="margin-right: 4px;">{{ step.stepType }}</el-tag>
                  <el-tag size="small" type="info">{{ step.estimatedHours }}h</el-tag>
                </div>
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  circle
                  @click="removeStep(index)"
                  title="删除此步骤"
                  style="margin-left: 8px;"
                />
              </div>
            </div>
            <div class="step-description" style="margin-top: 8px; color: #666;">
              {{ step.stepDescription }}
            </div>
            <div class="step-tech" style="margin-top: 8px;">
              <el-tag size="small" type="success">{{ step.technologyStack }}</el-tag>
            </div>
          </el-card>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <template v-if="showPreview">
          <!-- 预览模式的按钮 -->
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="handleRegenerate">重新生成</el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleConfirmSave"
          >
            确认并保存步骤
          </el-button>
        </template>
        <template v-else>
          <!-- 分解模式的按钮 -->
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleDecompose"
            :loading="loading"
            :disabled="!canDecompose"
          >
            {{ loading ? '分解中...' : '开始分解' }}
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, InfoFilled } from '@element-plus/icons-vue'
import { developmentService } from '@/services/development'
import { ProjectService } from '@/services/project'
import { AIProviderService } from '@/services/aiProvider'
import type { ElTagType } from '@/types/element-plus'
import type {
  DecomposeRequirementRequest,
  DecomposeContentRequest,
  DecompositionResult,
  TechnologyStackConfig
} from '@/types/development'
import type { Project } from '@/types'
import {
  TECHNOLOGY_STACKS,
  STEP_TYPE_CONFIG
} from '@/types/development'

// Props
interface Props {
  modelValue: boolean
  projectId: number
  requirementDocumentId?: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [result: DecompositionResult]
}>()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const showAdvanced = ref(false)
const decomposeMode = ref<'document' | 'project' | 'content'>('project')
const projectInfo = ref<Project | null>(null)
const loadingProject = ref(false)
const decomposeResult = ref<DecompositionResult | null>(null)
const showPreview = ref(false)

const form = reactive({
  requirementContent: '',
  technologyStack: '',
  granularity: 'Medium' as 'Coarse' | 'Medium' | 'Fine',
  maxStepCount: 20,
  decompositionMode: 'replace' as 'replace' | 'append',
  aiProvider: undefined as number | undefined,
  aiModel: ''
})

const hasExistingSteps = ref(false)

const includeOptions = ref(['test', 'documentation', 'dependencies'])

// AI供应商相关数据
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const technologyStacks = computed(() => {
  const stacks = [...TECHNOLOGY_STACKS]

  // 如果项目有技术栈信息，添加到选项中
  if (projectInfo.value?.technologyStack) {
    try {
      const projectTechStack = JSON.parse(projectInfo.value.technologyStack)

      // 如果是字符串，创建一个简单的配置
      if (typeof projectTechStack === 'string') {
        const existingStack = stacks.find(s => s.name === projectTechStack.toLowerCase())
        if (!existingStack) {
          stacks.unshift({
            name: projectTechStack.toLowerCase(),
            displayName: projectTechStack,
            description: `项目技术栈: ${projectTechStack}`,
            defaultStepTypes: ['Development', 'Testing'],
            defaultComponentTypes: ['Frontend', 'Backend'],
            fileExtensions: [],
            frameworks: [projectTechStack],
            tools: []
          })
        }
      }
      // 如果是对象，使用对象信息
      else if (typeof projectTechStack === 'object' && projectTechStack.name) {
        const existingStack = stacks.find(s => s.name === projectTechStack.name)
        if (!existingStack) {
          stacks.unshift({
            name: projectTechStack.name,
            displayName: projectTechStack.displayName || projectTechStack.name,
            description: projectTechStack.description || `项目技术栈: ${projectTechStack.name}`,
            defaultStepTypes: projectTechStack.defaultStepTypes || ['Development', 'Testing'],
            defaultComponentTypes: projectTechStack.defaultComponentTypes || ['Frontend', 'Backend'],
            fileExtensions: projectTechStack.fileExtensions || [],
            frameworks: projectTechStack.frameworks || [projectTechStack.name],
            tools: projectTechStack.tools || []
          })
        }
      }
    } catch (error) {
      // 如果解析失败，当作字符串处理
      const techStackName = projectInfo.value.technologyStack
      const existingStack = stacks.find(s => s.name === techStackName.toLowerCase())
      if (!existingStack) {
        stacks.unshift({
          name: techStackName.toLowerCase(),
          displayName: techStackName,
          description: `项目技术栈: ${techStackName}`,
          defaultStepTypes: ['Development', 'Testing'],
          defaultComponentTypes: ['Frontend', 'Backend'],
          fileExtensions: [],
          frameworks: [techStackName],
          tools: []
        })
      }
    }
  }

  return stacks
})

const selectedTechStack = computed(() =>
  technologyStacks.value.find(stack => stack.name === form.technologyStack)
)

// 注释掉旧的模型映射，现在直接从数据库获取AI配置
// const availableModels = computed(() => {
//   const modelMap: Record<string, string[]> = {
//     Azure: ['gpt-4', 'gpt-35-turbo'],
//     OpenAI: ['gpt-4', 'gpt-3.5-turbo'],
//     DeepSeek: ['deepseek-chat', 'deepseek-coder'],
//     Claude: ['claude-3-opus', 'claude-3-sonnet'],
//     Mock: ['mock-model']
//   }
//   return modelMap[form.aiProvider] || []
// })

const canDecompose = computed(() => {
  if (decomposeMode.value === 'content') {
    return form.requirementContent.trim().length > 0 && form.technologyStack && form.aiProvider
  }
  return form.technologyStack && props.requirementDocumentId && form.aiProvider
})

// 表单验证规则
const rules = {
  requirementContent: [
    { required: true, message: '请输入需求内容', trigger: 'blur' },
    { min: 10, message: '需求内容至少10个字符', trigger: 'blur' }
  ],
  technologyStack: [
    { required: true, message: '请选择技术栈', trigger: 'change' }
  ],
  aiProvider: [
    { required: true, message: '请选择AI模型', trigger: 'change' }
  ]
}

// 方法
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
  } catch (error: any) {
    console.error('加载AI供应商列表失败:', error)
    ElMessage.error('加载AI供应商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}

const loadProjectInfo = async () => {
  if (!props.projectId) return

  try {
    loadingProject.value = true
    projectInfo.value = await ProjectService.getProject(props.projectId)

    // 如果项目有技术栈信息，设置为默认值
    if (projectInfo.value.technologyStack && !form.technologyStack) {
      try {
        const projectTechStack = JSON.parse(projectInfo.value.technologyStack)
        if (typeof projectTechStack === 'string') {
          form.technologyStack = projectTechStack.toLowerCase()
        } else if (typeof projectTechStack === 'object' && projectTechStack.name) {
          form.technologyStack = projectTechStack.name
        }
      } catch (error) {
        // 如果解析失败，当作字符串处理
        form.technologyStack = projectInfo.value.technologyStack.toLowerCase()
      }
    }

    // 如果还没有设置技术栈，使用默认值
    if (!form.technologyStack) {
      form.technologyStack = 'vue'
    }

    // 检查是否已有开发步骤
    await checkExistingSteps()
  } catch (error) {
    console.error('获取项目信息失败:', error)
    ElMessage.warning('获取项目技术栈信息失败，将使用默认配置')
    form.technologyStack = 'vue'
  } finally {
    loadingProject.value = false
  }
}

const checkExistingSteps = async () => {
  if (!props.projectId) return

  try {
    const result = await developmentService.getProjectSteps(props.projectId, 1, 1)
    hasExistingSteps.value = result.totalCount > 0

    if (hasExistingSteps.value) {
      console.log(`检测到 ${result.totalCount} 个现有步骤`)
    }
  } catch (error) {
    console.error('检查现有步骤失败:', error)
    hasExistingSteps.value = false
  }
}

const handleTechnologyStackChange = () => {
  // 清空AI模型选择，让用户重新选择适合的模型
  form.aiModel = ''
}

const handleDecompose = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
  } catch (validationError) {
    console.error('表单验证失败:', validationError)
    ElMessage.error('请检查表单输入')
    return
  }

  try {
    loading.value = true

    const options = {
      technologyStack: form.technologyStack,
      granularity: form.granularity,
      includeTestSteps: includeOptions.value.includes('test'),
      includeDocumentationSteps: includeOptions.value.includes('documentation'),
      autoAnalyzeDependencies: includeOptions.value.includes('dependencies'),
      maxStepCount: form.maxStepCount,
      decompositionMode: form.decompositionMode,
      aiProvider: form.aiProvider || undefined,
      aiModel: form.aiModel || undefined
    }

    let result: DecompositionResult

    if (decomposeMode.value === 'document' && props.requirementDocumentId) {
      result = await developmentService.decomposeRequirement(
        props.requirementDocumentId,
        options as DecomposeRequirementRequest
      )
    } else if (decomposeMode.value === 'project') {
      result = await developmentService.decomposeProject(
        props.projectId,
        options as DecomposeRequirementRequest
      )
    } else {
      result = await developmentService.decomposeRequirementContent(
        props.projectId,
        {
          ...options,
          requirementContent: form.requirementContent
        } as DecomposeContentRequest
      )
    }

    if (result.success) {
      ElMessage.success('需求分解成功！')

      // 如果是预览模式，不关闭对话框，显示预览界面
      if (result.isPreview) {
        decomposeResult.value = result
        showPreview.value = true
        console.log('分解结果（预览模式）:', result)
      } else {
        emit('success', result)
        handleClose()
      }
    } else {
      ElMessage.error(result.message || '需求分解失败')
    }
  } catch (error: any) {
    console.error('需求分解API调用失败:', error)
    ElMessage.error(error.message || '需求分解失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  showPreview.value = false
  decomposeResult.value = null
  resetForm()
}

// 获取优先级标签类型
const getPriorityType = (priority: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const types: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    'Critical': 'danger',
    'High': 'warning',
    'Medium': 'info',
    'Low': 'success'
  }
  return types[priority] || 'info'
}

// 删除步骤
const removeStep = async (index: number) => {
  if (!decomposeResult.value?.data?.steps) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这个步骤吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 删除步骤
    decomposeResult.value.data.steps.splice(index, 1)

    // 重新计算步骤顺序
    decomposeResult.value.data.steps.forEach((step, idx) => {
      step.stepOrder = idx + 1
    })

    // 更新统计信息
    if (decomposeResult.value.data.statistics) {
      decomposeResult.value.data.statistics.totalSteps = decomposeResult.value.data.steps.length

      // 重新计算各类型步骤数量
      const stepTypes = decomposeResult.value.data.steps.reduce((acc, step) => {
        acc[step.stepType] = (acc[step.stepType] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      decomposeResult.value.data.statistics.developmentSteps = stepTypes['Development'] || 0
      decomposeResult.value.data.statistics.testSteps = stepTypes['Testing'] || 0
      decomposeResult.value.data.statistics.documentationSteps = stepTypes['Documentation'] || 0

      // 重新计算总工时
      decomposeResult.value.data.statistics.estimatedTotalHours = decomposeResult.value.data.steps.reduce(
        (total, step) => total + (step.estimatedHours || 0), 0
      )
    }

    ElMessage.success('步骤已删除')
  } catch (error) {
    // 用户取消删除
  }
}

// 重新生成
const handleRegenerate = () => {
  showPreview.value = false
  decomposeResult.value = null
  // 重新触发分解
  handleDecompose()
}

// 确认保存
const handleConfirmSave = async () => {
  if (!decomposeResult.value?.data || !props.projectId) return

  try {
    loading.value = true

    // 调用确认保存API
    const result = await developmentService.confirmSteps({
      projectId: props.projectId,
      requirementDocumentId: props.requirementDocumentId,
      steps: decomposeResult.value.data.steps.map(step => ({
        stepName: step.stepName || '',
        stepDescription: step.stepDescription || '',
        stepType: step.stepType || 'Development',
        priority: step.priority || 'Medium',
        estimatedHours: step.estimatedHours || 0,
        technologyStack: step.technologyStack || '',
        componentType: step.componentType || 'Backend',
        stepOrder: step.stepOrder || 0,
        stepLevel: step.stepLevel || 1
      })),
      dependencies: []
    })

    if (result.success) {
      ElMessage.success('步骤已成功保存到数据库！')
      emit('success', result)
      handleClose()
    } else {
      ElMessage.error(result.message || '保存步骤失败')
    }
  } catch (error: any) {
    console.error('保存步骤失败:', error)
    ElMessage.error(error.message || '保存步骤失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.requirementContent = ''
  form.granularity = 'Medium'
  form.maxStepCount = 50
  form.decompositionMode = 'replace'
  form.aiProvider = undefined
  form.aiModel = ''
  includeOptions.value = ['test', 'documentation', 'dependencies']
  showAdvanced.value = false
  decomposeMode.value = props.requirementDocumentId ? 'document' : 'project'

  // 重新设置技术栈
  if (projectInfo.value?.technologyStack) {
    try {
      const projectTechStack = JSON.parse(projectInfo.value.technologyStack)
      if (typeof projectTechStack === 'string') {
        form.technologyStack = projectTechStack.toLowerCase()
      } else if (typeof projectTechStack === 'object' && projectTechStack.name) {
        form.technologyStack = projectTechStack.name
      }
    } catch (error) {
      form.technologyStack = projectInfo.value.technologyStack.toLowerCase()
    }
  } else {
    form.technologyStack = 'vue'
  }

  // 重新检查现有步骤
  checkExistingSteps()
}

// 辅助方法
const getStepTypeIcon = (type: string) =>
  STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG]?.icon || '📝'

const getStepTypeTagType = (type: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    Development: 'primary',
    Testing: 'success',
    Documentation: 'info',
    Deployment: 'warning',
    Review: 'danger'
  }
  return typeMap[type] || 'info'
}

// 生命周期
onMounted(() => {
  loadProjectInfo()
  loadAIProviders()
})

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    decomposeMode.value = props.requirementDocumentId ? 'document' : 'content'
    // 如果对话框打开且还没有加载项目信息，则加载
    if (!projectInfo.value) {
      loadProjectInfo()
    }
  }
})

watch(() => props.projectId, () => {
  if (props.projectId) {
    loadProjectInfo()
  }
})

watch(() => form.aiProvider, () => {
  form.aiModel = ''
})
</script>

<style scoped>
.preview-container {
  margin-top: 20px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.step-title {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-name {
  font-weight: 500;
  font-size: 14px;
}

.step-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-meta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.step-description {
  margin-top: 8px;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.step-tech {
  margin-top: 8px;
}

.step-card {
  transition: all 0.3s ease;
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.statistics-card .el-statistic {
  text-align: center;
}

.steps-card .el-card__body {
  padding: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.tech-stack-option {
  display: flex;
  flex-direction: column;
}

.tech-stack-option .name {
  font-weight: 500;
}

.tech-stack-option .description {
  font-size: 12px;
  color: #999;
}

.advanced-options {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.preview-card {
  background: #f8f9fa;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  align-items: center;
}

.preview-item .label {
  font-weight: 500;
  margin-right: 8px;
  min-width: 80px;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s;
}
</style>
