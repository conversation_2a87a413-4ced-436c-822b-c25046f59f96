using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// UI操作类型实体
    /// </summary>
    [SugarTable("UIActionTypes", "UI操作类型表")]
    public class UIActionType : BaseEntity
    {
        /// <summary>
        /// UI操作类型值
        /// </summary>
        [SugarColumn(ColumnDescription = "UI操作类型值", Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "UI操作类型值不能为空")]
        [StringLength(50, ErrorMessage = "UI操作类型值长度不能超过50个字符")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [SugarColumn(ColumnDescription = "显示名称", Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "显示名称不能为空")]
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// UI操作描述
        /// </summary>
        [SugarColumn(ColumnDescription = "UI操作描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "UI操作描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [SugarColumn(ColumnDescription = "图标名称", Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [SugarColumn(ColumnDescription = "颜色标识", Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "排序顺序", IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用", IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 是否内置类型
        /// </summary>
        [SugarColumn(ColumnDescription = "是否内置类型", IsNullable = false)]
        public bool IsBuiltIn { get; set; } = false;

        /// <summary>
        /// 是否需要模板
        /// </summary>
        [SugarColumn(ColumnDescription = "是否需要模板", IsNullable = false)]
        public bool NeedsTemplate { get; set; } = false;

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        [SugarColumn(ColumnDescription = "参数架构JSON", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? ParameterSchema { get; set; }
    }


}
