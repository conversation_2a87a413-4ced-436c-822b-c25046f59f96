<template>
  <div class="prototype-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="goBack" type="text" size="large">
            <el-icon><ArrowLeft /></el-icon>
            返回设计列表
          </el-button>
          <div class="page-title">
            <el-icon><Monitor /></el-icon>
            <span v-if="isCreateMode">创建原型图</span>
            <span v-else-if="isEditMode">编辑原型图</span>
            <span v-else>{{ prototype?.prototypeName || '原型图详情' }}</span>
          </div>
          <div v-if="prototype?.description" class="page-description">
            {{ prototype.description }}
          </div>
        </div>
        <div class="header-actions">
          <el-button v-if="!isEditMode && !isCreateMode" @click="toggleEditMode" type="primary">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button v-if="isEditMode || isCreateMode" @click="savePrototype" type="primary" :loading="saving">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button v-if="isEditMode" @click="cancelEdit">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-dropdown v-if="!isCreateMode" trigger="click" @command="handleMoreAction">
            <el-button type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export">
                  <el-icon><Download /></el-icon>
                  导出
                </el-dropdown-item>
                <el-dropdown-item command="duplicate">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：图表预览区域 -->
        <el-col :span="isEditMode || isCreateMode ? 14 : 24">
          <el-card class="preview-card">
            <template #header>
              <div class="card-header">
                <span>原型图预览</span>
                <div class="header-actions">
                  <el-button @click="refreshDiagram" type="text" size="small">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="toggleFullscreen" type="text" size="small">
                    <el-icon><FullScreen /></el-icon>
                    全屏
                  </el-button>
                </div>
              </div>
            </template>

            <!-- Mermaid图表渲染区域 -->
            <div class="diagram-container" ref="diagramContainer">
              <div v-if="loading" class="loading-container">
                <el-skeleton :rows="8" animated />
              </div>
              <div v-else-if="error" class="error-container">
                <el-empty description="图表加载失败">
                  <el-button @click="loadDiagram" type="primary">重新加载</el-button>
                </el-empty>
              </div>
              <div v-else-if="!mermaidDefinition" class="empty-container">
                <el-empty description="暂无图表内容">
                  <el-button v-if="isEditMode || isCreateMode" @click="generateSampleDiagram" type="primary">
                    生成示例图表
                  </el-button>
                </el-empty>
              </div>
              <!-- 始终渲染mermaid-diagram元素，但根据条件显示不同内容 -->
              <div id="mermaid-diagram" class="mermaid-diagram" v-show="!loading && !error && mermaidDefinition"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：编辑面板（仅在编辑模式显示） -->
        <el-col v-if="isEditMode || isCreateMode" :span="10">
          <el-card class="edit-panel">
            <template #header>
              <span>原型图编辑</span>
            </template>

            <el-form :model="formData" label-width="100px" size="default">
              <!-- 基本信息 -->
              <el-form-item label="原型图名称" required>
                <el-input v-model="formData.prototypeName" placeholder="请输入原型图名称" />
              </el-form-item>

              <el-form-item label="原型图类型" required>
                <el-select v-model="formData.prototypeType" placeholder="请选择类型" style="width: 100%">
                  <el-option label="线框图" value="Wireframe" />
                  <el-option label="用户流程图" value="UserFlow" />
                  <el-option label="组件关系图" value="ComponentDiagram" />
                  <el-option label="交互流程图" value="InteractionFlow" />
                </el-select>
              </el-form-item>

              <el-form-item label="设备类型">
                <el-select v-model="formData.deviceType" placeholder="请选择设备类型" style="width: 100%">
                  <el-option label="桌面端" value="Desktop" />
                  <el-option label="移动端" value="Mobile" />
                  <el-option label="平板端" value="Tablet" />
                  <el-option label="响应式" value="Responsive" />
                </el-select>
              </el-form-item>

              <el-form-item label="保真度级别">
                <el-select v-model="formData.fidelityLevel" placeholder="请选择保真度级别" style="width: 100%">
                  <el-option label="低保真" value="Low" />
                  <el-option label="中保真" value="Medium" />
                  <el-option label="高保真" value="High" />
                </el-select>
              </el-form-item>

              <el-form-item label="目标用户">
                <el-input v-model="formData.targetUsers" placeholder="如：管理员、普通用户、访客" />
              </el-form-item>

              <el-form-item label="描述">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入原型图描述"
                />
              </el-form-item>

              <!-- Mermaid编辑器 -->
              <el-form-item label="Mermaid定义">
                <div class="mermaid-editor">
                  <el-input
                    v-model="formData.mermaidDefinition"
                    type="textarea"
                    :rows="12"
                    placeholder="请输入Mermaid图表定义..."
                    @input="onMermaidChange"
                  />
                  <div class="editor-actions">
                    <el-button @click="generateWithAI" type="primary" size="small" :loading="aiGenerating">
                      <el-icon><MagicStick /></el-icon>
                      AI生成
                    </el-button>
                    <el-button @click="insertTemplate" size="small">
                      <el-icon><DocumentAdd /></el-icon>
                      插入模板
                    </el-button>
                    <el-button @click="validateMermaid" size="small">
                      <el-icon><Check /></el-icon>
                      验证语法
                    </el-button>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Monitor,
  Edit,
  Check,
  Close,
  MoreFilled,
  Download,
  CopyDocument,
  Delete,
  Refresh,
  FullScreen,
  MagicStick,
  DocumentAdd
} from '@element-plus/icons-vue'
import { PrototypeService } from '@/services/prototype'
import { AIProviderService } from '@/services/aiProvider'
import type { Prototype } from '@/services/prototype'
import mermaid from 'mermaid'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const error = ref(false)
const aiGenerating = ref(false)


// AI供应商相关
const aiProviders = ref<any[]>([])
const loadingAIProviders = ref(false)

// 原型图数据
const prototype = ref<Prototype | null>(null)
const mermaidDefinition = ref('')

// 表单数据
const formData = ref({
  prototypeName: '',
  prototypeType: 'Wireframe',
  deviceType: 'Desktop',
  fidelityLevel: 'Low',
  targetUsers: '',
  description: '',
  mermaidDefinition: ''
})



// 计算属性
const projectId = computed(() => Number(route.params.projectId))
const prototypeId = computed(() => route.query.id ? Number(route.query.id) : null)
const mode = computed(() => route.query.mode as string)
const taskId = computed(() => route.query.taskId as string)

const isCreateMode = computed(() => mode.value === 'create' || !prototypeId.value)
const isEditMode = computed(() => mode.value === 'edit')

// 生命周期
onMounted(async () => {
  // 初始化Mermaid
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose',
    flowchart: {
      diagramPadding: 20,
      htmlLabels: true,
      curve: 'basis'
    }
  })

  if (taskId.value) {
    // 如果有任务ID，监听AI生成进度
    await monitorAIGeneration()
  } else if (prototypeId.value) {
    // 加载现有原型图
    await loadPrototype()
  } else {
    // 创建模式，初始化表单
    initializeForm()
  }
})

// 方法
const loadPrototype = async () => {
  if (!prototypeId.value) return

  try {
    loading.value = true
    console.log('开始加载原型图，ID:', prototypeId.value)
    prototype.value = await PrototypeService.getPrototype(prototypeId.value)
    console.log('原型图数据加载成功:', prototype.value)
    formData.value = {
      prototypeName: prototype.value.prototypeName,
      prototypeType: prototype.value.prototypeType,
      deviceType: prototype.value.deviceType || 'Desktop',
      fidelityLevel: prototype.value.fidelityLevel || 'Low',
      targetUsers: prototype.value.targetUsers || '',
      description: prototype.value.description || '',
      mermaidDefinition: prototype.value.mermaidDefinition
    }
    mermaidDefinition.value = prototype.value.mermaidDefinition || ''

    // 渲染图表 - 使用延迟确保DOM完全准备好
    await nextTick()
    setTimeout(() => {
      loadDiagram()
    }, 100)
  } catch (err: any) {
    console.error('加载原型图失败:', err)

    let errorMessage = '加载原型图失败'
    if (err.response) {
      if (err.response.status === 404) {
        errorMessage = `原型图不存在 (ID: ${prototypeId.value})`
      } else {
        errorMessage = `加载原型图失败: ${err.response.data?.message || err.response.statusText}`
      }
    } else if (err.request) {
      errorMessage = '无法连接到服务器，请检查后端服务是否启动'
    } else {
      errorMessage = `加载原型图失败: ${err.message}`
    }

    ElMessage.error(errorMessage)
    error.value = true
  } finally {
    loading.value = false
  }
}

const initializeForm = () => {
  formData.value = {
    prototypeName: '',
    prototypeType: 'Wireframe',
    deviceType: 'Desktop',
    fidelityLevel: 'Low',
    targetUsers: '',
    description: '',
    mermaidDefinition: ''
  }
  mermaidDefinition.value = ''
}

const monitorAIGeneration = async () => {
  if (!taskId.value || taskId.value === 'undefined') {
    console.log('taskId为空或undefined，结束轮询')
    return
  }

  try {
    loading.value = true
    ElMessage.info('正在监听AI生成进度...')

    // 轮询任务状态
    const checkStatus = async () => {
      try {
        const status = await PrototypeService.getGenerationTaskStatus(taskId.value)

        if (status.status === 'Completed') {
          console.log('AI生成完成，任务状态:', status)
          ElMessage.success('AI生成完成！')

          if (status.result?.prototypeId) {
            console.log('获取到原型图ID:', status.result.prototypeId)
            // 直接加载生成的原型图数据，不跳转页面
            try {
              prototype.value = await PrototypeService.getPrototype(status.result.prototypeId)
              console.log('加载的原型图数据:', prototype.value)

              formData.value = {
                prototypeName: prototype.value.prototypeName,
                prototypeType: prototype.value.prototypeType,
                deviceType: prototype.value.deviceType || 'Desktop',
                fidelityLevel: prototype.value.fidelityLevel || 'Low',
                targetUsers: prototype.value.targetUsers || '',
                description: prototype.value.description || '',
                mermaidDefinition: prototype.value.mermaidDefinition
              }
              mermaidDefinition.value = prototype.value.mermaidDefinition || ''

              console.log('设置的mermaidDefinition:', mermaidDefinition.value)
              console.log('mermaidDefinition长度:', mermaidDefinition.value.length)

              // 更新URL但不重新加载页面
              router.replace({
                name: 'PrototypeView',
                params: { projectId: projectId.value },
                query: { id: status.result.prototypeId }
              })

              // 渲染图表
              await nextTick()
              setTimeout(async () => {
                console.log('开始渲染图表...')
                await loadDiagram()
                // 确保加载状态正确设置
                loading.value = false
              }, 100)
            } catch (err) {
              console.error('加载生成的原型图失败:', err)
              ElMessage.error('加载生成的原型图失败')
            }
          } else {
            console.error('任务完成但没有返回原型图ID')
            ElMessage.error('生成完成但没有返回原型图数据')
          }
          return
        } else if (status.status === 'Failed') {
          ElMessage.error(`AI生成失败: ${status.errorMessage || '未知错误'}`)
          error.value = true
          return
        } else if (status.status === 'Running') {
          ElMessage.info(`生成进度: ${status.progress || 0}% - ${status.message || '正在生成...'}`)
          // 继续轮询
          setTimeout(checkStatus, 2000)
        }
      } catch (err) {
        console.error('检查任务状态失败:', err)
        ElMessage.error('检查任务状态失败')
        error.value = true
      }
    }

    await checkStatus()
  } catch (err) {
    console.error('监听AI生成失败:', err)
    ElMessage.error('监听AI生成失败')
    error.value = true
    loading.value = false
  }
}

const goBack = () => {
  router.push({
    name: 'Design',
    query: { projectId: projectId.value }
  })
}

const toggleEditMode = () => {
  router.push({
    name: 'PrototypeView',
    params: { projectId: projectId.value },
    query: { id: prototypeId.value, mode: 'edit' }
  })
}

const cancelEdit = () => {
  router.push({
    name: 'PrototypeView',
    params: { projectId: projectId.value },
    query: { id: prototypeId.value }
  })
}

const savePrototype = async () => {
  try {
    saving.value = true

    if (isCreateMode.value) {
      const newPrototype = await PrototypeService.createPrototype({
        projectId: projectId.value,
        ...formData.value
      })
      ElMessage.success('创建成功')
      // 跳转到查看模式
      router.replace({
        name: 'PrototypeView',
        params: { projectId: projectId.value },
        query: { id: newPrototype.id }
      })
    } else {
      await PrototypeService.updatePrototype(prototypeId.value!, {
        prototypeName: formData.value.prototypeName,
        prototypeType: formData.value.prototypeType,
        deviceType: formData.value.deviceType,
        fidelityLevel: formData.value.fidelityLevel,
        targetUsers: formData.value.targetUsers,
        description: formData.value.description,
        mermaidDefinition: formData.value.mermaidDefinition
      })
      ElMessage.success('保存成功')
      // 重新加载数据
      await loadPrototype()
      // 退出编辑模式
      router.replace({
        name: 'PrototypeView',
        params: { projectId: projectId.value },
        query: { id: prototypeId.value }
      })
    }
  } catch (error) {
    console.error('保存原型图失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleMoreAction = async (command: string) => {
  switch (command) {
    case 'export':
      await exportPrototype()
      break
    case 'duplicate':
      await duplicatePrototype()
      break
    case 'delete':
      await deletePrototype()
      break
  }
}

const exportPrototype = async () => {
  if (!prototypeId.value) return

  try {
    ElMessage.info('正在导出原型图，请稍候...')

    const blob = await PrototypeService.exportPrototype(prototypeId.value, 'svg')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${prototype.value?.prototypeName || 'prototype'}.svg`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出原型图失败:', error)
    ElMessage.error('导出失败')
  }
}

const duplicatePrototype = async () => {
  if (!prototypeId.value) return

  try {
    const newPrototype = await PrototypeService.duplicatePrototype(prototypeId.value)
    ElMessage.success('复制成功')
    // 跳转到新的原型图
    router.push({
      name: 'PrototypeView',
      params: { projectId: projectId.value },
      query: { id: newPrototype.id }
    })
  } catch (error) {
    console.error('复制原型图失败:', error)
    ElMessage.error('复制失败')
  }
}

const deletePrototype = async () => {
  if (!prototypeId.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这个原型图吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await PrototypeService.deletePrototype(prototypeId.value)
    ElMessage.success('删除成功')
    goBack()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除原型图失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const refreshDiagram = async () => {
  await loadDiagram()
}

const toggleFullscreen = () => {
  ElMessage.info('全屏功能正在开发中...')
}

const loadDiagram = async () => {
  try {
    console.log('loadDiagram被调用，mermaidDefinition:', mermaidDefinition.value)
    loading.value = true
    error.value = false

    if (!mermaidDefinition.value) {
      console.log('没有Mermaid定义，跳过渲染')
      return
    }

    console.log('开始渲染Mermaid图表，定义长度:', mermaidDefinition.value.length)

    // 等待DOM完全渲染
    await nextTick()

    let element = document.getElementById('mermaid-diagram')

    // 如果元素不存在，尝试创建一个
    if (!element) {
      console.log('mermaid-diagram元素不存在，尝试创建...')
      const container = document.querySelector('.diagram-container')
      if (container) {
        element = document.createElement('div')
        element.id = 'mermaid-diagram'
        element.className = 'mermaid-diagram'
        container.appendChild(element)
        console.log('已创建mermaid-diagram元素')
      }
    }

    if (element) {
      try {
        console.log('找到渲染元素，开始渲染原型图...')

        // 清空之前的内容
        element.innerHTML = ''

        // 生成唯一ID
        const id = `mermaid-prototype-${Date.now()}`

        // 渲染图表
        const { svg } = await mermaid.render(id, mermaidDefinition.value)
        element.innerHTML = svg

        console.log('原型图渲染成功')
        return // 渲染成功，退出函数

      } catch (err) {
        console.error('渲染原型图失败:', err)
        element.innerHTML = `<div class="error-message">图表渲染失败: ${err}</div>`
        return
      }
    } else {
      console.error('无法找到或创建mermaid-diagram元素')
      error.value = true
    }
  } catch (err) {
    console.error('渲染图表失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

const generateSampleDiagram = () => {
  const sampleDiagram = `flowchart TD
    A[用户登录页面] --> B[输入用户名密码]
    B --> C{验证登录信息}
    C -->|成功| D[跳转到主页面]
    C -->|失败| E[显示错误信息]
    E --> B`
  
  formData.value.mermaidDefinition = sampleDiagram
  mermaidDefinition.value = sampleDiagram
  onMermaidChange()
}

let mermaidRenderTimer: NodeJS.Timeout | null = null

const onMermaidChange = () => {
  mermaidDefinition.value = formData.value.mermaidDefinition

  // 防抖渲染
  if (mermaidRenderTimer) {
    clearTimeout(mermaidRenderTimer)
  }
  mermaidRenderTimer = setTimeout(() => {
    loadDiagram()
  }, 1000)
}

// 加载AI供应商
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers || []
  } catch (error: any) {
    console.error('加载AI供应商列表失败:', error)
    ElMessage.error('加载AI供应商列表失败')
  } finally {
    loadingAIProviders.value = false
  }
}

const generateWithAI = async () => {
  try {
    aiGenerating.value = true

    // 加载AI供应商列表
    await loadAIProviders()

    // 如果没有可用的AI供应商，提示用户
    if (aiProviders.value.length === 0) {
      ElMessage.warning('暂无可用的AI供应商配置，请先配置AI供应商')
      return
    }

    // 使用第一个可用的AI供应商
    const defaultProvider = aiProviders.value.find(p => p.isActive) || aiProviders.value[0]

    const result = await PrototypeService.generatePrototype({
      projectId: projectId.value,
      prototypeType: formData.value.prototypeType,
      deviceType: formData.value.deviceType,
      fidelityLevel: formData.value.fidelityLevel,
      requirements: '请生成基本的用户界面原型图，包含主要功能模块和页面布局',
      targetUsers: formData.value.targetUsers || '普通用户',
      aiProviderConfigId: defaultProvider.id
    })

    ElMessage.success('AI生成任务已启动，请稍候...')

    // 开始监听生成进度
    const checkStatus = async () => {
      try {
        const status = await PrototypeService.getGenerationTaskStatus(result.taskId)

        if (status.status === 'Completed') {
          if (status.result?.mermaidDefinition) {
            formData.value.mermaidDefinition = status.result.mermaidDefinition
            mermaidDefinition.value = status.result.mermaidDefinition
            await loadDiagram()
            ElMessage.success('AI生成完成')
          }
          return
        } else if (status.status === 'Failed') {
          ElMessage.error(`AI生成失败: ${status.errorMessage || '未知错误'}`)
          return
        } else if (status.status === 'Running') {
          // 继续轮询
          setTimeout(checkStatus, 2000)
        }
      } catch (err) {
        console.error('检查任务状态失败:', err)
        ElMessage.error('检查任务状态失败')
      }
    }

    await checkStatus()
  } catch (error) {
    console.error('AI生成失败:', error)
    ElMessage.error('AI生成失败')
  } finally {
    aiGenerating.value = false
  }
}



const insertTemplate = () => {
  ElMessage.info('插入模板功能正在开发中...')
}

const validateMermaid = async () => {
  if (!formData.value.mermaidDefinition.trim()) {
    ElMessage.warning('请先输入Mermaid定义')
    return
  }

  try {
    // 尝试渲染以验证语法
    const id = `validate-prototype-${Date.now()}`
    await mermaid.render(id, formData.value.mermaidDefinition)
    ElMessage.success('Mermaid语法验证通过')
  } catch (err) {
    ElMessage.error(`Mermaid语法错误: ${err}`)
  }
}
</script>

<style lang="scss" scoped>
.prototype-view {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);

  .page-header {
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        flex: 1;

        .page-title {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 12px 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #303133;

          .el-icon {
            color: #409eff;
          }
        }

        .page-description {
          margin: 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;

        .el-button {
          height: 40px;
          padding: 0 20px;
        }
      }
    }
  }

  .main-content {
    .preview-card {
      height: calc(100vh - 200px);
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-actions {
          display: flex;
          gap: 8px;
        }
      }

      .diagram-container {
        height: calc(100vh - 280px);
        overflow: auto;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background: #fafafa;
        position: relative;

        .loading-container,
        .error-container,
        .empty-container {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .mermaid-diagram {
          padding: 20px;
          text-align: center;
          min-height: 100%;
          background: white;

          :deep(svg) {
            max-width: 100%;
            height: auto;
          }
        }

        .error-message {
          padding: 20px;
          color: #f56c6c;
          background: #fef0f0;
          border: 1px solid #fbc4c4;
          border-radius: 4px;
          text-align: center;
        }
      }
    }

    .edit-panel {
      height: calc(100vh - 200px);
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      :deep(.el-card__body) {
        height: calc(100vh - 260px);
        overflow-y: auto;
        padding: 20px;
      }

      .mermaid-editor {
        .editor-actions {
          margin-top: 12px;
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }

        :deep(.el-textarea__inner) {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .prototype-view {
    padding: 16px;

    .page-header {
      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .header-actions {
          align-self: flex-start;
        }
      }
    }

    .main-content {
      .el-col {
        margin-bottom: 16px;
      }

      .preview-card,
      .edit-panel {
        height: auto;
        min-height: 400px;

        :deep(.el-card__body) {
          height: auto;
          min-height: 350px;
        }

        .diagram-container {
          height: 350px;
        }
      }
    }
  }
}
</style>
