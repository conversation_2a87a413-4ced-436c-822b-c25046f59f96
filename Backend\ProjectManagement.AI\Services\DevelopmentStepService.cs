using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Services;
using ProjectManagement.Core.Interfaces;
using SqlSugar;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// 开发步骤服务实现
    /// </summary>
    public class DevelopmentStepService : IDevelopmentStepService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DevelopmentStepService> _logger;
        private readonly IDevelopmentStepRepository _stepRepository;

        public DevelopmentStepService(ISqlSugarClient db, ILogger<DevelopmentStepService> logger, IDevelopmentStepRepository stepRepository)
        {
            _db = db;
            _logger = logger;
            _stepRepository = stepRepository;
        }

        // ==================== 基础CRUD ====================

        public async Task<DevelopmentStep?> GetStepByIdAsync(int stepId)
        {
            return await _db.Queryable<DevelopmentStep>()
                .Where(s => s.Id == stepId && !s.IsDeleted)
                .FirstAsync();
        }

        public async Task<DevelopmentStep> CreateStepAsync(DevelopmentStep step)
        {
            step.CreatedTime = DateTime.Now;
            step.UpdatedTime = DateTime.Now;

            var id = await _db.Insertable(step).ExecuteReturnIdentityAsync();
            step.Id = id;
            return step;
        }

        public async Task<DevelopmentStep> UpdateStepAsync(DevelopmentStep step)
        {
            step.UpdatedTime = DateTime.Now;

            var affectedRows = await _db.Updateable(step).ExecuteCommandAsync();
            _logger.LogInformation("更新步骤完成，ID: {StepId}, 影响行数: {AffectedRows}, AIPrompt: {AIPrompt}", step.Id, affectedRows, step.AIPrompt);

            // 重新查询以确认更新
            var updatedStep = await GetStepByIdAsync(step.Id);

            return updatedStep ?? step;
        }

        public async Task<bool> DeleteStepAsync(int stepId, bool deleteChildSteps = false)
        {
            try
            {
                if (deleteChildSteps)
                {
                    // 递归删除子步骤
                    var childSteps = await _db.Queryable<DevelopmentStep>()
                        .Where(s => s.ParentStepId == stepId && !s.IsDeleted)
                        .ToListAsync();

                    foreach (var child in childSteps)
                    {
                        await DeleteStepAsync(child.Id, true);
                    }
                }

                // 软删除主步骤
                await _db.Updateable<DevelopmentStep>()
                    .SetColumns(s => new DevelopmentStep { IsDeleted = true, UpdatedTime = DateTime.Now })
                    .Where(s => s.Id == stepId)
                    .ExecuteCommandAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除开发步骤失败，步骤ID: {StepId}", stepId);
                return false;
            }
        }

        // ==================== 查询方法 ====================

        public async Task<ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>> GetProjectStepsAsync(
            int projectId,
            int pageIndex = 1,
            int pageSize = 20,
            string? status = null,
            string? priority = null,
            string? stepType = null,
            string? keyword = null)
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted);

            if (!string.IsNullOrEmpty(status))
                query = query.Where(s => s.Status == status);

            if (!string.IsNullOrEmpty(priority))
                query = query.Where(s => s.Priority == priority);

            if (!string.IsNullOrEmpty(stepType))
                query = query.Where(s => s.StepType == stepType);

            if (!string.IsNullOrEmpty(keyword))
                query = query.Where(s => s.StepName.Contains(keyword) || s.StepDescription!.Contains(keyword));

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(s =>new { s.StepOrder, s.CreatedTime })
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }

        public async Task<List<DevelopmentStep>> GetProjectStepTreeAsync(int projectId)
        {
            var allSteps = await _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted)
                .OrderBy(s => s.StepOrder)
                .ToListAsync();

            return BuildStepTree(allSteps);
        }

        public async Task<List<DevelopmentStep>> GetExecutableStepsAsync(
            int projectId,
            string? stepType = null,
            string? priority = null)
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted && s.Status == "Pending");

            if (!string.IsNullOrEmpty(stepType))
                query = query.Where(s => s.StepType == stepType);

            if (!string.IsNullOrEmpty(priority))
                query = query.Where(s => s.Priority == priority);

            return await query
                .OrderBy(s => new {
                    PriorityOrder = s.Priority == "Critical" ? 1 : s.Priority == "High" ? 2 : s.Priority == "Medium" ? 3 : 4,
                    s.StepOrder
                })
                .ToListAsync();
        }

        public async Task<ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>> SearchStepsAsync(
            int projectId,
            string keyword,
            int pageIndex = 1,
            int pageSize = 20)
        {
            var query = _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted)
                .Where(s => s.StepName.Contains(keyword) ||
                           s.StepDescription!.Contains(keyword) ||
                           s.TechnologyStack!.Contains(keyword));

            var totalCount = await query.CountAsync();
            var items = await query
                .OrderBy(s => s.StepOrder)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }

        // ==================== 统计分析 ====================

        public async Task<ProjectManagement.Core.Interfaces.ProjectStepStatistics> GetProjectStepStatisticsAsync(int projectId)
        {
            var project = await _db.Queryable<Project>()
                .Where(p => p.Id == projectId)
                .FirstAsync();

            var steps = await _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted)
                .ToListAsync();

            var statistics = new ProjectManagement.Core.Interfaces.ProjectStepStatistics
            {
                ProjectId = projectId,
                ProjectName = project?.Name ?? "未知项目",
                TotalSteps = steps.Count,
                PendingSteps = steps.Count(s => s.Status == "Pending"),
                InProgressSteps = steps.Count(s => s.Status == "InProgress"),
                CompletedSteps = steps.Count(s => s.Status == "Completed"),
                FailedSteps = steps.Count(s => s.Status == "Failed"),
                BlockedSteps = steps.Count(s => s.Status == "Blocked"),
                AverageProgress = steps.Count > 0 ? steps.Average(s => s.Progress) : 0,
                TotalEstimatedHours = steps.Sum(s => s.EstimatedHours ?? 0),
                TotalActualHours = steps.Sum(s => s.ActualHours ?? 0),
                TechnologyStackCount = steps.Where(s => !string.IsNullOrEmpty(s.TechnologyStack))
                    .Select(s => s.TechnologyStack).Distinct().Count(),
                FirstStepCreated = steps.Count > 0 ? steps.Min(s => s.CreatedTime) : null,
                LastStepUpdated = steps.Count > 0 ? steps.Max(s => s.UpdatedTime) : null
            };

            return statistics;
        }

        public async Task<StepExecutionProgress> GetStepExecutionProgressAsync(int projectId)
        {
            var steps = await _db.Queryable<DevelopmentStep>()
                .Where(s => s.ProjectId == projectId && !s.IsDeleted)
                .ToListAsync();

            var progressByType = steps.GroupBy(s => s.StepType)
                .Select(g => new StepTypeProgress
                {
                    StepType = g.Key,
                    Total = g.Count(),
                    Completed = g.Count(s => s.Status == "Completed"),
                    Progress = g.Count() > 0 ? (double)g.Count(s => s.Status == "Completed") / g.Count() * 100 : 0
                }).ToList();

            return new StepExecutionProgress
            {
                ProjectId = projectId,
                TotalSteps = steps.Count,
                PendingSteps = steps.Count(s => s.Status == "Pending"),
                InProgressSteps = steps.Count(s => s.Status == "InProgress"),
                CompletedSteps = steps.Count(s => s.Status == "Completed"),
                FailedSteps = steps.Count(s => s.Status == "Failed"),
                BlockedSteps = steps.Count(s => s.Status == "Blocked"),
                OverallProgress = steps.Count > 0 ? steps.Average(s => (double)s.Progress) : 0,
                ProgressByType = progressByType
            };
        }

        public async Task<StepComplexityAnalysis> AnalyzeStepComplexityAsync(int stepId)
        {
            var step = await GetStepByIdAsync(stepId);
            if (step == null)
                throw new ArgumentException("步骤不存在");

            // 简单的复杂度分析逻辑
            var complexityScore = CalculateComplexityScore(step);
            var factors = GetComplexityFactors(step);
            var recommendations = GetRecommendations(step, complexityScore);
            var riskLevel = complexityScore <= 3 ? "Low" : complexityScore <= 7 ? "Medium" : "High";

            return new StepComplexityAnalysis
            {
                StepId = stepId,
                StepName = step.StepName,
                ComplexityScore = complexityScore,
                EstimatedHours = step.EstimatedHours ?? 0,
                ComplexityFactors = factors,
                Recommendations = recommendations,
                RiskLevel = riskLevel
            };
        }

        // ==================== 执行管理 ====================

        public async Task<StepExecutionHistory> StartStepExecutionAsync(int stepId, string executorType = "Manual")
        {
            var step = await GetStepByIdAsync(stepId);
            if (step == null)
                throw new ArgumentException("步骤不存在");

            // 创建执行历史记录
            var executionHistory = new StepExecutionHistory
            {
                StepId = stepId,
                ExecutionId = Guid.NewGuid().ToString(),
                ExecutionStartTime = DateTime.Now,
                ExecutionStatus = "Running",
                ExecutorType = executorType,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };

            await _db.Insertable(executionHistory).ExecuteCommandAsync();

            // 更新步骤状态
            step.Start();
            await UpdateStepAsync(step);

            return executionHistory;
        }

        // ==================== 辅助方法 ====================

        private List<DevelopmentStep> BuildStepTree(List<DevelopmentStep> allSteps)
        {
            var stepDict = allSteps.ToDictionary(s => s.Id);
            var rootSteps = new List<DevelopmentStep>();

            foreach (var step in allSteps)
            {
                if (step.ParentStepId == null)
                {
                    rootSteps.Add(step);
                }
                else if (stepDict.ContainsKey(step.ParentStepId.Value))
                {
                    stepDict[step.ParentStepId.Value].ChildSteps.Add(step);
                }
            }

            return rootSteps;
        }

        private int CalculateComplexityScore(DevelopmentStep step)
        {
            int score = 1;

            // 基于步骤类型
            score += step.StepType switch
            {
                "Development" => 3,
                "Testing" => 2,
                "Documentation" => 1,
                "Deployment" => 4,
                "Review" => 1,
                _ => 2
            };

            // 基于技术栈
            if (!string.IsNullOrEmpty(step.TechnologyStack))
            {
                if (step.TechnologyStack.Contains("AI") || step.TechnologyStack.Contains("ML"))
                    score += 3;
                else if (step.TechnologyStack.Contains("Database"))
                    score += 2;
                else
                    score += 1;
            }

            // 基于预估工时
            if (step.EstimatedHours.HasValue)
            {
                if (step.EstimatedHours > 20) score += 3;
                else if (step.EstimatedHours > 10) score += 2;
                else if (step.EstimatedHours > 5) score += 1;
            }

            return Math.Min(10, score);
        }

        private List<string> GetComplexityFactors(DevelopmentStep step)
        {
            var factors = new List<string>();

            if (step.StepType == "Development")
                factors.Add("开发任务通常需要编码和调试");

            if (!string.IsNullOrEmpty(step.TechnologyStack))
                factors.Add($"使用技术栈: {step.TechnologyStack}");

            if (step.EstimatedHours > 10)
                factors.Add("预估工时较长，可能涉及复杂逻辑");

            if (!string.IsNullOrEmpty(step.AIPrompt))
                factors.Add("需要AI辅助生成代码");

            return factors;
        }

        private List<string> GetRecommendations(DevelopmentStep step, int complexityScore)
        {
            var recommendations = new List<string>();

            if (complexityScore > 7)
            {
                recommendations.Add("建议将此步骤分解为更小的子步骤");
                recommendations.Add("考虑增加代码审查环节");
            }

            if (step.EstimatedHours > 15)
                recommendations.Add("建议分阶段实施，设置中间检查点");

            if (!string.IsNullOrEmpty(step.AIPrompt))
                recommendations.Add("确保AI提示词清晰准确，便于生成高质量代码");

            return recommendations;
        }

        // ==================== 依赖管理 ====================

        public async Task<List<StepDependency>> GetStepDependenciesAsync(int stepId)
        {
            // 获取依赖关系
            var dependencies = await _db.Queryable<StepDependency>()
                .Where(d => d.StepId == stepId && !d.IsDeleted)
                .ToListAsync();

            // 手动加载被依赖的步骤信息
            if (dependencies.Any())
            {
                var dependsOnStepIds = dependencies.Select(d => d.DependsOnStepId).ToList();
                var dependsOnSteps = await _db.Queryable<DevelopmentStep>()
                    .Where(s => dependsOnStepIds.Contains(s.Id) && !s.IsDeleted)
                    .ToListAsync();

                foreach (var dependency in dependencies)
                {
                    dependency.DependsOnStep = dependsOnSteps.FirstOrDefault(s => s.Id == dependency.DependsOnStepId);
                }
            }

            return dependencies;
        }

        public async Task<List<StepDependency>> GetStepDependentsAsync(int stepId)
        {
            // 获取依赖关系
            var dependents = await _db.Queryable<StepDependency>()
                .Where(d => d.DependsOnStepId == stepId && !d.IsDeleted)
                .ToListAsync();

            // 手动加载依赖步骤信息
            if (dependents.Any())
            {
                var stepIds = dependents.Select(d => d.StepId).ToList();
                var steps = await _db.Queryable<DevelopmentStep>()
                    .Where(s => stepIds.Contains(s.Id) && !s.IsDeleted)
                    .ToListAsync();

                foreach (var dependent in dependents)
                {
                    dependent.Step = steps.FirstOrDefault(s => s.Id == dependent.StepId);
                }
            }

            return dependents;
        }

        public async Task<StepDependency> AddStepDependencyAsync(int stepId, int dependsOnStepId, string dependencyType = "Sequential", bool isRequired = true)
        {
            var dependency = new StepDependency
            {
                StepId = stepId,
                DependsOnStepId = dependsOnStepId,
                DependencyType = dependencyType,
                IsRequired = isRequired,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now
            };

            var id = await _db.Insertable(dependency).ExecuteReturnIdentityAsync();
            dependency.Id = id;
            return dependency;
        }

        public async Task<bool> RemoveStepDependencyAsync(int dependencyId)
        {
            var dependency = await _db.Queryable<StepDependency>()
                .Where(d => d.Id == dependencyId && !d.IsDeleted)
                .FirstAsync();

            if (dependency == null)
                return false;

            dependency.IsDeleted = true;
            dependency.DeletedTime = DateTime.Now;
            dependency.UpdatedTime = DateTime.Now;

            await _db.Updateable(dependency).ExecuteCommandAsync();
            return true;
        }

        public Task<int> AutoAnalyzeDependenciesAsync(int projectId)
        {
            return Task.FromResult(0);
        }

        public Task<DependencyValidationResult> ValidateDependenciesAsync(int projectId)
        {
            return Task.FromResult(new DependencyValidationResult { IsValid = true });
        }

        public Task<bool> MoveStepAsync(int stepId, int? newParentStepId)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> ReorderStepsAsync(List<ProjectManagement.Core.Services.StepOrderInfo> stepOrders)
        {
            try
            {
                var orderDict = stepOrders.ToDictionary(so => so.StepId, so => so.Order);
                var updateCount = await _stepRepository.ReorderStepsAsync(orderDict, null);
                return updateCount > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新排序步骤失败");
                throw;
            }
        }

        public Task<bool> BatchOperateStepsAsync(List<int> stepIds, string operation, object? value = null)
        {
            throw new NotImplementedException();
        }

        public Task<bool> CompleteStepExecutionAsync(string executionId, string result, string? generatedCode = null, string? outputFiles = null)
        {
            throw new NotImplementedException();
        }

        public Task<bool> FailStepExecutionAsync(string executionId, string errorMessage)
        {
            throw new NotImplementedException();
        }

        public async Task<ProjectManagement.Core.Interfaces.PagedResult<StepExecutionHistory>> GetStepExecutionHistoryAsync(int stepId, int pageIndex = 1, int pageSize = 20)
        {
            var query = _db.Queryable<StepExecutionHistory>()
                .Where(h => h.StepId == stepId && !h.IsDeleted)
                .OrderBy(h => h.ExecutionStartTime, OrderByType.Desc);

            var totalCount = await query.CountAsync();
            var items = await query.ToPageListAsync(pageIndex, pageSize);

            return new ProjectManagement.Core.Interfaces.PagedResult<StepExecutionHistory>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }

        public async Task<List<StepExecutionHistory>> GetStepExecutionHistoryListAsync(int stepId)
        {
            return await _db.Queryable<StepExecutionHistory>()
                .Where(h => h.StepId == stepId && !h.IsDeleted)
                .OrderBy(h => h.ExecutionStartTime, OrderByType.Desc)
                .ToListAsync();
        }

        // ==================== 模板序列应用 ====================

        public async Task ApplyTemplateSequenceToStepsAsync(int sequenceId, List<int> stepIds)
        {
            try
            {
                _logger.LogInformation("开始应用模板序列 {SequenceId} 到 {StepCount} 个步骤", sequenceId, stepIds.Count);

                // 获取模板序列信息
                var sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                    .Where(s => s.Id == sequenceId && !s.IsDeleted)
                    .FirstAsync();

                if (sequence == null)
                {
                    throw new ArgumentException($"模板序列 {sequenceId} 不存在");
                }

                // 获取序列的步骤
                var templateSteps = await _db.Queryable<UIAutoMationTemplateStep>()
                    .Where(s => s.SequenceId == sequenceId && s.IsActive && !s.IsDeleted)
                    .OrderBy(s => s.StepOrder)
                    .ToListAsync();

                if (!templateSteps.Any())
                {
                    throw new ArgumentException($"模板序列 {sequenceId} 没有可用的步骤");
                }

                // 为每个开发步骤创建模板序列关联记录
                var associations = new List<StepTemplateSequenceAssociation>();
                foreach (var stepId in stepIds)
                {
                    // 检查步骤是否存在
                    var step = await GetStepByIdAsync(stepId);
                    if (step == null)
                    {
                        _logger.LogWarning("步骤 {StepId} 不存在，跳过", stepId);
                        continue;
                    }

                    // 创建关联记录
                    var association = new StepTemplateSequenceAssociation
                    {
                        StepId = stepId,
                        SequenceId = sequenceId,
                        AppliedTime = DateTime.Now,
                        IsActive = true,
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now
                    };

                    associations.Add(association);
                }

                // 批量插入关联记录
                if (associations.Any())
                {
                    await _db.Insertable(associations).ExecuteCommandAsync();
                    _logger.LogInformation("成功应用模板序列 {SequenceId} 到 {Count} 个步骤", sequenceId, associations.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用模板序列失败，序列ID: {SequenceId}", sequenceId);
                throw;
            }
        }

        public async Task<List<object>> GetStepTemplateSequencesAsync(int stepId)
        {
            try
            {
                var sequences = await _db.Queryable<StepTemplateSequenceAssociation>()
                    .LeftJoin<UIAutoMationTemplateSequence>((a, s) => a.SequenceId == s.Id)
                    .Where((a, s) => a.StepId == stepId && a.IsActive && !s.IsDeleted)
                    .Select((a, s) => new
                    {
                        s.Id,
                        s.Name,
                        s.Description,
                        s.Category,
                        AppliedTime = a.AppliedTime,
                        StepCount = SqlFunc.Subqueryable<UIAutoMationTemplateStep>()
                            .Where(ts => ts.SequenceId == s.Id && ts.IsActive && !ts.IsDeleted)
                            .Count()
                    })
                    .ToListAsync();

                return sequences.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤模板序列失败，步骤ID: {StepId}", stepId);
                throw;
            }
        }

        public async Task RemoveStepTemplateSequenceAsync(int stepId, int sequenceId)
        {
            try
            {
                await _db.Updateable<StepTemplateSequenceAssociation>()
                    .SetColumns(a => new StepTemplateSequenceAssociation { IsActive = false, UpdatedTime = DateTime.Now })
                    .Where(a => a.StepId == stepId && a.SequenceId == sequenceId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("成功移除步骤 {StepId} 的模板序列 {SequenceId} 关联", stepId, sequenceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除步骤模板序列关联失败，步骤ID: {StepId}, 序列ID: {SequenceId}", stepId, sequenceId);
                throw;
            }
        }
    }
}
