graph TD
    A[需求规格书] --> B[分析技术需求]
    A --> C[ER图/Context图]
    B --> D{选择技术栈}
    C --> D
    
    D --> E[Vue前端代码生成]
    D --> F[C# API代码生成]
    D --> G[SQL数据库脚本生成]
    
    E --> H[前端组件]
    E --> I[路由配置]
    E --> J[状态管理]
    E --> K[样式文件]
    
    F --> L[控制器]
    F --> M[服务层]
    F --> N[数据模型]
    F --> O[配置文件]
    
    G --> P[表结构]
    G --> Q[存储过程]
    G --> R[初始数据]
    G --> S[索引优化]
    
    H --> T[代码质量检查]
    I --> T
    J --> T
    K --> T
    L --> T
    M --> T
    N --> T
    O --> T
    P --> U[数据库验证]
    Q --> U
    R --> U
    S --> U
    
    T --> V[前端代码包]
    U --> W[数据库脚本包]
    T --> X[后端代码包]
    
    V --> Y[集成测试准备]
    W --> Y
    X --> Y
    
    %% 样式定义
    classDef inputStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef frontendStyle fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef backendStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef databaseStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef validationStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef outputStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    %% 应用样式
    class A,C inputStyle
    class E,H,I,J,K frontendStyle
    class F,L,M,N,O backendStyle
    class G,P,Q,R,S databaseStyle
    class T,U validationStyle
    class V,W,X,Y outputStyle
