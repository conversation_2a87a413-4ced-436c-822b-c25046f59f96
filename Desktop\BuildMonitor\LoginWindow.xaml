﻿<Window x:Class="BuildMonitor.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BuildMonitor"
        mc:Ignorable="d"
        Title="LoginWindow_New" 
        Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="0,0,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="🔧" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="BuildMonitor" FontSize="20" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 登录表单区域 -->
        <Border Grid.Row="1" Margin="40,30,40,20" Background="White" 
                CornerRadius="10" BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel Margin="30">
                <TextBlock Text="请登录以继续" FontSize="16" FontWeight="Bold" 
                          HorizontalAlignment="Center" Margin="0,0,0,30" Foreground="#333"/>

                <!-- 用户名输入 -->
                <TextBlock Text="用户名:" Margin="0,0,0,5" FontWeight="SemiBold"/>
                <TextBox Name="UsernameTextBox" Height="35" FontSize="14" 
                         Padding="10,8" BorderBrush="#DDD" BorderThickness="1"
                         VerticalContentAlignment="Center"/>

                <!-- 密码输入 -->
                <TextBlock Text="密码:" Margin="0,20,0,5" FontWeight="SemiBold"/>
                <PasswordBox Name="PasswordBox" Height="35" FontSize="14" 
                            Padding="10,8" BorderBrush="#DDD" BorderThickness="1"
                            VerticalContentAlignment="Center"/>

                <!-- 记住我选项 -->
                <CheckBox Name="RememberMeCheckBox" Content="记住我" Margin="0,15,0,0" 
                         FontSize="12" Foreground="#666"/>

                <!-- 错误信息显示 -->
                <TextBlock Name="ErrorMessageTextBlock" Margin="0,10,0,0" 
                          Foreground="Red" FontSize="12" TextWrapping="Wrap"
                          Visibility="Collapsed"/>
            </StackPanel>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" 
                   VerticalAlignment="Center">
            <Button Name="LoginButton" Content="登录" Width="100" Height="35" 
                   Background="#2196F3" Foreground="White" FontSize="14" FontWeight="Bold"
                   BorderThickness="0" Cursor="Hand" Margin="0,0,15,0"
                   Click="LoginButton_Click"/>
            <Button Name="CancelButton" Content="取消" Width="100" Height="35" 
                   Background="#757575" Foreground="White" FontSize="14" FontWeight="Bold"
                   BorderThickness="0" Cursor="Hand"
                   Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
