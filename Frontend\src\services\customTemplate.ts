/**
 * 自定义UI自动化模板服务
 */
import api from './api'

// 自定义模板接口
export interface CustomTemplate {
  id: number
  name: string
  description: string
  category: string
  filePath: string
  confidence: number
  tags: string[]
  notes: string
  usageCount: number
  lastUsedTime?: string
  createdTime: string
  updatedTime?: string
  createdBy?: number
  isDeleted: boolean
  // 区域图片相关字段
  regionFilePath?: string
  regionDescription?: string
  regionConfidence?: number
  regionExpand?: number
  useRegionMatching: boolean
}

// 模板序列接口
export interface TemplateSequence {
  id: number
  name: string
  description: string
  category: string
  tags: string[]
  notes: string
  usageCount: number
  lastUsedTime?: string
  isActive: boolean
  steps: TemplateStep[]
  createdTime: string
  updatedTime?: string
  createdBy?: number
  isDeleted: boolean
}

// 模板步骤接口
export interface TemplateStep {
  id: number
  sequenceId: number
  templateId?: number
  stepOrder: number
  actionType: 'click' | 'wait' | 'input' | 'delay' | 'screenshot' | 'verify' | 'scroll' | 'key_press'
  description: string
  parameters: Record<string, any>
  timeoutSeconds: number
  retryCount: number
  maxRetries: number
  isActive: boolean
  createdTime: string
  updatedTime?: string
}

// 执行日志接口
export interface ExecutionLog {
  id: number
  sequenceId?: number
  templateId?: number
  stepId?: number
  executionType: 'Template' | 'Sequence' | 'Step'
  status: 'Started' | 'Running' | 'Completed' | 'Failed' | 'Cancelled'
  startTime: string
  endTime?: string
  duration?: number
  result?: string
  errorMessage?: string
  screenshotPath?: string
  executedBy?: string
  createdTime: string
}

// 分页查询参数
export interface PageQuery {
  page: number
  pageSize: number
  keyword?: string
  category?: string
  tags?: string[]
  isActive?: boolean
}

// 分页结果
export interface PageResult<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 统计信息
export interface TemplateStatistics {
  totalTemplates: number
  totalSequences: number
  totalExecutions: number
  successRate: number
  categoryStats: Record<string, number>
  recentExecutions: ExecutionLog[]
  mostUsedTemplates: CustomTemplate[]
  mostUsedSequences: TemplateSequence[]
}

/**
 * 自定义模板服务类
 */
export class CustomTemplateService {

  // ==================== 自定义模板管理 ====================

  /**
   * 获取模板列表
   */
  static async getTemplates(query: PageQuery): Promise<PageResult<CustomTemplate>> {
    const response = await api.get('/api/custom-templates', { params: query })
    return response.data
  }

  /**
   * 获取模板详情
   */
  static async getTemplate(id: number): Promise<CustomTemplate> {
    const response = await api.get(`/api/custom-templates/${id}`)
    return response.data
  }

  /**
   * 创建模板
   */
  static async createTemplate(template: Partial<CustomTemplate>): Promise<CustomTemplate> {
    const response = await api.post('/api/custom-templates', template)
    return response.data
  }

  /**
   * 更新模板
   */
  static async updateTemplate(id: number, template: Partial<CustomTemplate>): Promise<CustomTemplate> {
    const response = await api.put(`/api/custom-templates/${id}`, template)
    return response.data
  }

  /**
   * 删除模板
   */
  static async deleteTemplate(id: number): Promise<void> {
    await api.delete(`/api/custom-templates/${id}`)
  }

  /**
   * 测试模板
   */
  static async testTemplate(id: number): Promise<{ success: boolean; location?: any; error?: string }> {
    const response = await api.post(`/api/custom-templates/${id}/test`)
    return response.data
  }

  /**
   * 上传模板图片
   */
  static async uploadTemplateImage(file: File): Promise<{ filePath: string }> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await api.post('/api/custom-templates/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // ==================== 模板序列管理 ====================

  /**
   * 获取序列列表
   */
  static async getSequences(query: PageQuery): Promise<PageResult<TemplateSequence>> {
    const response = await api.get('/api/template-sequences', { params: query })
    return response.data
  }

  /**
   * 获取序列详情
   */
  static async getSequence(id: number): Promise<TemplateSequence> {
    const response = await api.get(`/api/template-sequences/${id}`)
    return response.data
  }

  /**
   * 创建序列
   */
  static async createSequence(sequence: Partial<TemplateSequence>): Promise<TemplateSequence> {
    const response = await api.post('/api/template-sequences', sequence)
    return response.data
  }

  /**
   * 更新序列
   */
  static async updateSequence(id: number, sequence: Partial<TemplateSequence>): Promise<TemplateSequence> {
    const response = await api.put(`/api/template-sequences/${id}`, sequence)
    return response.data
  }

  /**
   * 删除序列
   */
  static async deleteSequence(id: number): Promise<void> {
    await api.delete(`/api/template-sequences/${id}`)
  }

  /**
   * 执行序列
   */
  static async executeSequence(id: number, parameters?: Record<string, any>): Promise<{ executionId: number }> {
    const response = await api.post(`/api/template-sequences/${id}/execute`, { parameters })
    return response.data
  }

  /**
   * 停止序列执行
   */
  static async stopSequence(executionId: number): Promise<void> {
    await api.post(`/api/template-sequences/executions/${executionId}/stop`)
  }

  // ==================== 模板步骤管理 ====================

  /**
   * 获取序列的步骤列表
   */
  static async getSequenceSteps(sequenceId: number): Promise<TemplateStep[]> {
    const response = await api.get(`/api/template-sequences/${sequenceId}/steps`)
    return response.data
  }

  /**
   * 创建步骤
   */
  static async createStep(step: Partial<TemplateStep>): Promise<TemplateStep> {
    const response = await api.post('/api/template-steps', step)
    return response.data
  }

  /**
   * 更新步骤
   */
  static async updateStep(id: number, step: Partial<TemplateStep>): Promise<TemplateStep> {
    const response = await api.put(`/api/template-steps/${id}`, step)
    return response.data
  }

  /**
   * 删除步骤
   */
  static async deleteStep(id: number): Promise<void> {
    await api.delete(`/api/template-steps/${id}`)
  }

  /**
   * 调整步骤顺序
   */
  static async reorderSteps(sequenceId: number, stepIds: number[]): Promise<void> {
    await api.put(`/api/template-sequences/${sequenceId}/steps/reorder`, { stepIds })
  }

  // ==================== 执行日志管理 ====================

  /**
   * 获取执行日志
   */
  static async getExecutionLogs(query: PageQuery & {
    sequenceId?: number
    status?: string
    startTime?: string
    endTime?: string
  }): Promise<PageResult<ExecutionLog>> {
    const response = await api.get('/api/template-execution-logs', { params: query })
    return response.data
  }

  /**
   * 获取执行详情
   */
  static async getExecutionDetail(id: number): Promise<ExecutionLog> {
    const response = await api.get(`/api/template-execution-logs/${id}`)
    return response.data
  }

  // ==================== 统计和分析 ====================

  /**
   * 获取统计信息
   */
  static async getStatistics(): Promise<TemplateStatistics> {
    const response = await api.get('/api/custom-templates/statistics')
    return response.data
  }

  /**
   * 获取分类列表
   */
  static async getCategories(): Promise<string[]> {
    try {
      // 优先使用新的分类API
      const response = await api.get('/api/CustomTemplateCategory')
      return response.data.map((category: any) => category.name)
    } catch (error) {
      // 降级到旧的API
      const response = await api.get('/api/custom-templates/categories')
      return response.data
    }
  }

  /**
   * 获取标签列表
   */
  static async getTags(): Promise<string[]> {
    const response = await api.get('/api/custom-templates/tags')
    return response.data
  }

  // ==================== 导入导出 ====================

  /**
   * 导出模板
   */
  static async exportTemplates(templateIds?: number[]): Promise<Blob> {
    const response = await api.post('/api/custom-templates/export',
      { templateIds },
      { responseType: 'blob' }
    )
    return response.data
  }

  /**
   * 导入模板
   */
  static async importTemplates(file: File): Promise<{ success: boolean; imported: number; errors?: string[] }> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await api.post('/api/custom-templates/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // ==================== 序列导入导出 ====================

  /**
   * 导出序列为JSON格式
   */
  static async exportSequences(sequenceIds?: number[]): Promise<Blob> {
    const response = await api.post('/api/template-sequences/export',
      { sequenceIds },
      { responseType: 'blob' }
    )
    return response.data
  }

  /**
   * 从JSON格式导入序列
   */
  static async importSequences(file: File): Promise<{
    totalCount: number;
    successCount: number;
    failedCount: number;
    errors: string[]
  }> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await api.post('/api/template-sequences/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }
}

export default CustomTemplateService
