using Microsoft.Extensions.Configuration;

namespace BuildMonitor.Configuration
{
    /// <summary>
    /// 应用程序配置管理类
    /// </summary>
    public class AppSettings
    {
        private readonly IConfiguration _configuration;

        public AppSettings(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// 连接字符串配置
        /// </summary>
        public ConnectionStringsSettings ConnectionStrings => new(_configuration);

        /// <summary>
        /// 构建设置配置
        /// </summary>
        public BuildSettings Build => new(_configuration);

        /// <summary>
        /// 数据库设置配置
        /// </summary>
        public DatabaseSettings Database => new(_configuration);

        /// <summary>
        /// UI设置配置
        /// </summary>
        public UISettings UI => new(_configuration);
    }

    /// <summary>
    /// 连接字符串配置
    /// </summary>
    public class ConnectionStringsSettings
    {
        private readonly IConfiguration _configuration;

        public ConnectionStringsSettings(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string DefaultConnection => _configuration.GetConnectionString("DefaultConnection") ?? "";
        public string AlternativeConnection => _configuration.GetConnectionString("AlternativeConnection") ?? "";
    }

    /// <summary>
    /// 构建设置配置
    /// </summary>
    public class BuildSettings
    {
        private readonly IConfiguration _configuration;

        public BuildSettings(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public int ProjectId => int.TryParse(_configuration.GetSection("BuildSettings")["ProjectId"], out var projectId) ? projectId : 1;
        public string BackendProjectPath => _configuration.GetSection("BuildSettings")["BackendProjectPath"] ?? "";
        public string FrontendProjectPath => _configuration.GetSection("BuildSettings")["FrontendProjectPath"] ?? "";
        public int AutoCompileInterval => int.TryParse(_configuration.GetSection("BuildSettings")["AutoCompileInterval"], out var interval) ? interval : 30;
        public int MaxErrorsToDisplay => int.TryParse(_configuration.GetSection("BuildSettings")["MaxErrorsToDisplay"], out var max) ? max : 100;
        public bool EnableAutoCompile => bool.TryParse(_configuration.GetSection("BuildSettings")["EnableAutoCompile"], out var enable) ? enable : false;
        public int CompileTimeoutSeconds => int.TryParse(_configuration.GetSection("BuildSettings")["CompileTimeoutSeconds"], out var timeout) ? timeout : 300;
    }

    /// <summary>
    /// 数据库设置配置
    /// </summary>
    public class DatabaseSettings
    {
        private readonly IConfiguration _configuration;

        public DatabaseSettings(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public int CommandTimeout => int.TryParse(_configuration.GetSection("DatabaseSettings")["CommandTimeout"], out var timeout) ? timeout : 30;
        public bool EnableSqlLogging => bool.TryParse(_configuration.GetSection("DatabaseSettings")["EnableSqlLogging"], out var logging) ? logging : true;
        public bool AutoCreateTables => bool.TryParse(_configuration.GetSection("DatabaseSettings")["AutoCreateTables"], out var create) ? create : false;
    }

    /// <summary>
    /// UI设置配置
    /// </summary>
    public class UISettings
    {
        private readonly IConfiguration _configuration;

        public UISettings(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public int RefreshInterval => int.TryParse(_configuration.GetSection("UISettings")["RefreshInterval"], out var interval) ? interval : 5;
        public bool ShowWarnings => bool.TryParse(_configuration.GetSection("UISettings")["ShowWarnings"], out var warnings) ? warnings : true;
        public bool ShowInfoMessages => bool.TryParse(_configuration.GetSection("UISettings")["ShowInfoMessages"], out var info) ? info : false;
        public int MaxDisplayRows => int.TryParse(_configuration.GetSection("UISettings")["MaxDisplayRows"], out var max) ? max : 1000;
    }
}
