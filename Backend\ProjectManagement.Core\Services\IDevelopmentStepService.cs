using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using static ProjectManagement.Core.Interfaces.IDevelopmentStepRepository;

namespace ProjectManagement.Core.Services
{
    /// <summary>
    /// 开发步骤服务接口
    /// </summary>
    public interface IDevelopmentStepService
    {
        // ==================== 基础CRUD ====================

        /// <summary>
        /// 根据ID获取开发步骤
        /// </summary>
        Task<DevelopmentStep?> GetStepByIdAsync(int stepId);

        /// <summary>
        /// 创建开发步骤
        /// </summary>
        Task<DevelopmentStep> CreateStepAsync(DevelopmentStep step);

        /// <summary>
        /// 更新开发步骤
        /// </summary>
        Task<DevelopmentStep> UpdateStepAsync(DevelopmentStep step);

        /// <summary>
        /// 删除开发步骤
        /// </summary>
        Task<bool> DeleteStepAsync(int stepId, bool deleteChildSteps = false);

        // ==================== 查询方法 ====================

        /// <summary>
        /// 获取项目的开发步骤（分页）
        /// </summary>
        Task<ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>> GetProjectStepsAsync(
            int projectId,
            int pageIndex = 1,
            int pageSize = 20,
            string? status = null,
            string? priority = null,
            string? stepType = null,
            string? keyword = null);

        /// <summary>
        /// 获取项目步骤树结构
        /// </summary>
        Task<List<DevelopmentStep>> GetProjectStepTreeAsync(int projectId);

        /// <summary>
        /// 获取可执行的步骤
        /// </summary>
        Task<List<DevelopmentStep>> GetExecutableStepsAsync(
            int projectId,
            string? stepType = null,
            string? priority = null);

        /// <summary>
        /// 搜索开发步骤
        /// </summary>
        Task<ProjectManagement.Core.Interfaces.PagedResult<DevelopmentStep>> SearchStepsAsync(
            int projectId,
            string keyword,
            int pageIndex = 1,
            int pageSize = 20);

        // ==================== 统计分析 ====================

        /// <summary>
        /// 获取项目步骤统计信息
        /// </summary>
        Task<ProjectManagement.Core.Interfaces.ProjectStepStatistics> GetProjectStepStatisticsAsync(int projectId);

        /// <summary>
        /// 获取步骤执行进度
        /// </summary>
        Task<StepExecutionProgress> GetStepExecutionProgressAsync(int projectId);

        /// <summary>
        /// 分析步骤复杂度
        /// </summary>
        Task<StepComplexityAnalysis> AnalyzeStepComplexityAsync(int stepId);

        // ==================== 执行管理 ====================

        /// <summary>
        /// 开始执行步骤
        /// </summary>
        Task<StepExecutionHistory> StartStepExecutionAsync(int stepId, string executorType = "Manual");

        /// <summary>
        /// 完成步骤执行
        /// </summary>
        Task<bool> CompleteStepExecutionAsync(string executionId, string result, string? generatedCode = null, string? outputFiles = null);

        /// <summary>
        /// 标记步骤执行失败
        /// </summary>
        Task<bool> FailStepExecutionAsync(string executionId, string errorMessage);

        /// <summary>
        /// 获取步骤执行历史
        /// </summary>
        Task<ProjectManagement.Core.Interfaces.PagedResult<StepExecutionHistory>> GetStepExecutionHistoryAsync(int stepId, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 获取步骤执行历史列表（不分页）
        /// </summary>
        Task<List<StepExecutionHistory>> GetStepExecutionHistoryListAsync(int stepId);

        // ==================== 依赖管理 ====================

        /// <summary>
        /// 获取步骤的依赖关系
        /// </summary>
        Task<List<StepDependency>> GetStepDependenciesAsync(int stepId);

        /// <summary>
        /// 获取依赖当前步骤的其他步骤
        /// </summary>
        Task<List<StepDependency>> GetStepDependentsAsync(int stepId);

        /// <summary>
        /// 添加步骤依赖关系
        /// </summary>
        Task<StepDependency> AddStepDependencyAsync(int stepId, int dependsOnStepId, string dependencyType = "Sequential", bool isRequired = true);

        /// <summary>
        /// 移除步骤依赖关系
        /// </summary>
        Task<bool> RemoveStepDependencyAsync(int dependencyId);

        /// <summary>
        /// 自动分析并建立步骤依赖关系
        /// </summary>
        Task<int> AutoAnalyzeDependenciesAsync(int projectId);

        /// <summary>
        /// 验证依赖关系
        /// </summary>
        Task<DependencyValidationResult> ValidateDependenciesAsync(int projectId);

        // ==================== 批量操作 ====================

        /// <summary>
        /// 移动步骤到新的父步骤下
        /// </summary>
        Task<bool> MoveStepAsync(int stepId, int? newParentStepId);

        /// <summary>
        /// 重新排序步骤
        /// </summary>
        Task<bool> ReorderStepsAsync(List<ProjectManagement.Core.Services.StepOrderInfo> stepOrders);

        /// <summary>
        /// 批量操作步骤
        /// </summary>
        Task<bool> BatchOperateStepsAsync(List<int> stepIds, string operation, object? value = null);

        // ==================== 模板序列应用 ====================

        /// <summary>
        /// 应用模板序列到开发步骤
        /// </summary>
        Task ApplyTemplateSequenceToStepsAsync(int sequenceId, List<int> stepIds);

        /// <summary>
        /// 获取步骤关联的模板序列信息
        /// </summary>
        Task<List<object>> GetStepTemplateSequencesAsync(int stepId);

        /// <summary>
        /// 移除步骤的模板序列关联
        /// </summary>
        Task RemoveStepTemplateSequenceAsync(int stepId, int sequenceId);
    }

}
