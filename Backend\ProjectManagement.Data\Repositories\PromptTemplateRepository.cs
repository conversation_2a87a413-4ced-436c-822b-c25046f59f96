using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using SqlSugar;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// Prompt模板仓储实现
/// </summary>
public class PromptTemplateRepository : BaseRepository<PromptTemplate>, IPromptTemplateRepository
{
    public PromptTemplateRepository(ISqlSugarClient db, ILogger<PromptTemplateRepository> logger)
        : base(db, logger)
    {
    }

    /// <summary>
    /// 根据分类ID获取模板列表
    /// </summary>
    public async Task<List<PromptTemplate>> GetByCategoryIdAsync(int categoryId)
    {
        try
        {
            return await _db.Queryable<PromptTemplate>()
                .Where(x => x.CategoryId == categoryId && !x.IsDeleted && x.IsEnabled)
                .OrderByDescending(x => x.UsageCount)
                .OrderByDescending(x => x.AverageRating)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据分类ID获取模板列表失败，CategoryId: {CategoryId}", categoryId);
            throw;
        }
    }

    /// <summary>
    /// 根据任务类型获取模板列表
    /// </summary>
    public async Task<List<PromptTemplate>> GetByTaskTypeAsync(string taskType)
    {
        try
        {
            return await _db.Queryable<PromptTemplate>()
                .Where(x => x.TaskType == taskType && !x.IsDeleted && x.IsEnabled)
                .OrderByDescending(x => x.IsDefault)
                .OrderByDescending(x => x.UsageCount)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型获取模板列表失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 根据模板类型获取模板列表
    /// </summary>
    public async Task<List<PromptTemplate>> GetByTemplateTypeAsync(string templateType)
    {
        try
        {
            return await _db.Queryable<PromptTemplate>()
                .Where(x => x.TemplateType == templateType && !x.IsDeleted && x.IsEnabled)
                .OrderByDescending(x => x.CreatedTime)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据模板类型获取模板列表失败，TemplateType: {TemplateType}", templateType);
            throw;
        }
    }

    /// <summary>
    /// 获取默认模板
    /// </summary>
    public async Task<PromptTemplate?> GetDefaultTemplateAsync(string taskType)
    {
        try
        {
            return await _db.Queryable<PromptTemplate>()
                .Where(x => x.TaskType == taskType && x.IsDefault && !x.IsDeleted && x.IsEnabled)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取默认模板失败，TaskType: {TaskType}", taskType);
            throw;
        }
    }

    /// <summary>
    /// 搜索模板
    /// </summary>
    public async Task<List<PromptTemplate>> SearchAsync(string keyword, int? categoryId = null, string? taskType = null)
    {
        try
        {
            var query = _db.Queryable<PromptTemplate>()
                .Where(x => !x.IsDeleted && x.IsEnabled);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(x => x.Name.Contains(keyword) ||
                                        x.Description!.Contains(keyword) ||
                                        x.Tags!.Contains(keyword));
            }

            if (categoryId.HasValue)
            {
                query = query.Where(x => x.CategoryId == categoryId.Value);
            }

            if (!string.IsNullOrEmpty(taskType))
            {
                query = query.Where(x => x.TaskType == taskType);
            }

            return await query
                .OrderByDescending(x => x.UsageCount)
                .OrderByDescending(x => x.AverageRating)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索模板失败，Keyword: {Keyword}", keyword);
            throw;
        }
    }

    /// <summary>
    /// 获取热门模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetPopularTemplatesAsync(int count = 10)
    {
        try
        {
            return await _db.Queryable<PromptTemplate>()
                .Where(x => !x.IsDeleted && x.IsEnabled)
                .OrderByDescending(x => x.UsageCount)
                .OrderByDescending(x => x.AverageRating)
                .Take(count)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门模板失败");
            throw;
        }
    }

    /// <summary>
    /// 获取用户收藏的模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetUserFavoriteTemplatesAsync(int userId)
    {
        try
        {
            // 先获取用户收藏的模板ID
            var favoriteTemplateIds = await _db.Queryable<UserPromptPreference>()
                .Where(p => p.UserId == userId && p.PreferenceType == "Favorite" && !p.IsDeleted && p.IsEnabled)
                .Select(p => p.TemplateId)
                .ToListAsync();

            if (!favoriteTemplateIds.Any())
                return new List<PromptTemplate>();

            return await _db.Queryable<PromptTemplate>()
                .Where(t => favoriteTemplateIds.Contains(t.Id) && !t.IsDeleted && t.IsEnabled)
                .OrderByDescending(t => t.UsageCount)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户收藏模板失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取用户最近使用的模板
    /// </summary>
    public async Task<List<PromptTemplate>> GetUserRecentTemplatesAsync(int userId, int count = 10)
    {
        try
        {
            // 先获取用户最近使用的模板ID
            var recentTemplateIds = await _db.Queryable<PromptUsageStats>()
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.UsedAt)
                .Take(count)
                .Select(s => s.TemplateId)
                .ToListAsync();

            if (!recentTemplateIds.Any())
                return new List<PromptTemplate>();

            return await _db.Queryable<PromptTemplate>()
                .Where(t => recentTemplateIds.Contains(t.Id) && !t.IsDeleted && t.IsEnabled)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户最近使用模板失败，UserId: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 更新模板使用统计
    /// </summary>
    public async Task UpdateUsageStatsAsync(int templateId)
    {
        try
        {
            await _db.Updateable<PromptTemplate>()
                .SetColumns(x => new PromptTemplate
                {
                    UsageCount = x.UsageCount + 1,
                    LastUsedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == templateId)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模板使用统计失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }

    /// <summary>
    /// 更新模板评分
    /// </summary>
    public async Task UpdateRatingAsync(int templateId, decimal rating)
    {
        try
        {
            await _db.Updateable<PromptTemplate>()
                .SetColumns(x => new PromptTemplate
                {
                    AverageRating = rating,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == templateId)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模板评分失败，TemplateId: {TemplateId}", templateId);
            throw;
        }
    }
}
