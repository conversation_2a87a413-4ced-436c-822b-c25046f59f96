-- =============================================
-- AI提示词相关表补充脚本
-- 添加缺少的Prompt相关表到ProjectManagementAI数据库
-- 创建日期: 2024-12-20
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. AI提示词分类表 (PromptCategories)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromptCategories')
BEGIN
    CREATE TABLE PromptCategories (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL, -- 分类名称
        Description nvarchar(500) NULL, -- 分类描述
        ParentId int NULL, -- 父分类ID，支持层级分类
        Icon nvarchar(50) NULL, -- 分类图标
        Color nvarchar(20) NULL, -- 分类颜色
        SortOrder int NOT NULL DEFAULT 0, -- 排序权重，数值越小越靠前
        IsEnabled bit NOT NULL DEFAULT 1, -- 是否启用
        TemplateCount int NOT NULL DEFAULT 0, -- 模板数量统计
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(), -- 创建时间
        UpdatedTime datetime2 NULL, -- 更新时间
        CreatedBy int NULL, -- 创建者ID
        UpdatedBy int NULL, -- 更新者ID
        IsDeleted bit NOT NULL DEFAULT 0, -- 是否删除
        DeletedTime datetime2 NULL, -- 删除时间
        DeletedBy int NULL, -- 删除者ID
        Version int NOT NULL DEFAULT 1, -- 版本号
        Remarks nvarchar(500) NULL, -- 备注

        CONSTRAINT PK_PromptCategories PRIMARY KEY (Id),
        CONSTRAINT FK_PromptCategories_ParentId FOREIGN KEY (ParentId) REFERENCES PromptCategories(Id)
    );

    -- 创建索引
    CREATE INDEX IX_PromptCategories_ParentId ON PromptCategories(ParentId);
    CREATE INDEX IX_PromptCategories_IsEnabled ON PromptCategories(IsEnabled);
    CREATE INDEX IX_PromptCategories_SortOrder ON PromptCategories(SortOrder);
    CREATE INDEX IX_PromptCategories_IsDeleted ON PromptCategories(IsDeleted);

    -- 添加表注释
    EXEC sp_addextendedproperty 'MS_Description', 'AI提示词分类管理表', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '分类ID，主键', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Id';
    EXEC sp_addextendedproperty 'MS_Description', '分类名称', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Name';
    EXEC sp_addextendedproperty 'MS_Description', '分类描述', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Description';
    EXEC sp_addextendedproperty 'MS_Description', '父分类ID，支持层级分类', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'ParentId';
    EXEC sp_addextendedproperty 'MS_Description', '分类图标', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Icon';
    EXEC sp_addextendedproperty 'MS_Description', '分类颜色', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Color';
    EXEC sp_addextendedproperty 'MS_Description', '排序权重，数值越小越靠前', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'SortOrder';
    EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'IsEnabled';
    EXEC sp_addextendedproperty 'MS_Description', '模板数量统计', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'TemplateCount';
    EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'CreatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'UpdatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'CreatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'UpdatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '是否删除', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'IsDeleted';
    EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'DeletedTime';
    EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'DeletedBy';
    EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Version';
    EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'PromptCategories', 'COLUMN', 'Remarks';

    PRINT 'AI提示词分类表 (PromptCategories) 创建成功';
END
ELSE
BEGIN
    PRINT 'AI提示词分类表 (PromptCategories) 已存在，跳过创建';
END
GO

-- =============================================
-- 2. AI提示词模板表 (PromptTemplates)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromptTemplates')
BEGIN
    CREATE TABLE PromptTemplates (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(200) NOT NULL, -- 模板名称
        Description nvarchar(max) NULL, -- 模板描述
        CategoryId int NOT NULL, -- 模板分类ID
        Content nvarchar(max) NOT NULL, -- 模板内容，支持参数占位符
        Parameters nvarchar(max) NULL, -- 模板参数定义，JSON格式
        TemplateType nvarchar(20) NOT NULL DEFAULT 'System', -- 模板类型: System=系统模板, User=用户自定义, Shared=共享模板
        TaskType nvarchar(50) NULL, -- 任务类型: RequirementAnalysis, CodeGeneration, Testing等
        Language nvarchar(50) NULL, -- 适用编程语言
        Framework nvarchar(100) NULL, -- 适用框架
        Difficulty nvarchar(20) NULL DEFAULT 'Medium', -- 难度级别: Easy, Medium, Hard
        EstimatedTokens int NULL, -- 预估Token数量
        IsEnabled bit NOT NULL DEFAULT 1, -- 是否启用
        UsageCount int NOT NULL DEFAULT 0, -- 使用次数统计
        AverageRating decimal(3,2) NULL, -- 平均评分（1-5分）
        LastUsedTime datetime2 NULL, -- 最后使用时间
        Tags nvarchar(500) NULL, -- 模板标签，用于搜索和分类
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(), -- 创建时间
        UpdatedTime datetime2 NULL, -- 更新时间
        CreatedBy int NULL, -- 创建者ID
        UpdatedBy int NULL, -- 更新者ID
        IsDeleted bit NOT NULL DEFAULT 0, -- 是否删除
        DeletedTime datetime2 NULL, -- 删除时间
        DeletedBy int NULL, -- 删除者ID
        Version int NOT NULL DEFAULT 1, -- 版本号
        Remarks nvarchar(500) NULL, -- 备注

        CONSTRAINT PK_PromptTemplates PRIMARY KEY (Id),
        CONSTRAINT FK_PromptTemplates_CategoryId FOREIGN KEY (CategoryId) REFERENCES PromptCategories(Id),
        CONSTRAINT FK_PromptTemplates_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_PromptTemplates_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT CK_PromptTemplates_TemplateType CHECK (TemplateType IN ('System', 'User', 'Shared')),
        CONSTRAINT CK_PromptTemplates_TaskType CHECK (TaskType IN ('RequirementAnalysis', 'CodeGeneration', 'Testing', 'Debugging', 'Documentation', 'Review')),
        CONSTRAINT CK_PromptTemplates_Difficulty CHECK (Difficulty IN ('Easy', 'Medium', 'Hard')),
        CONSTRAINT CK_PromptTemplates_AverageRating CHECK (AverageRating >= 1 AND AverageRating <= 5)
    );

    -- 创建索引
    CREATE INDEX IX_PromptTemplates_CategoryId ON PromptTemplates(CategoryId);
    CREATE INDEX IX_PromptTemplates_TemplateType ON PromptTemplates(TemplateType);
    CREATE INDEX IX_PromptTemplates_TaskType ON PromptTemplates(TaskType);
    CREATE INDEX IX_PromptTemplates_Language ON PromptTemplates(Language);
    CREATE INDEX IX_PromptTemplates_IsEnabled ON PromptTemplates(IsEnabled);
    CREATE INDEX IX_PromptTemplates_CreatedBy ON PromptTemplates(CreatedBy);
    CREATE INDEX IX_PromptTemplates_IsDeleted ON PromptTemplates(IsDeleted);
    CREATE INDEX IX_PromptTemplates_UsageCount ON PromptTemplates(UsageCount);
    CREATE INDEX IX_PromptTemplates_AverageRating ON PromptTemplates(AverageRating);

    -- 添加表注释
    EXEC sp_addextendedproperty 'MS_Description', 'AI提示词模板管理表', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '模板ID，主键', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Id';
    EXEC sp_addextendedproperty 'MS_Description', '模板名称', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Name';
    EXEC sp_addextendedproperty 'MS_Description', '模板描述', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Description';
    EXEC sp_addextendedproperty 'MS_Description', '模板分类ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'CategoryId';
    EXEC sp_addextendedproperty 'MS_Description', '模板内容，支持参数占位符', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Content';
    EXEC sp_addextendedproperty 'MS_Description', '模板参数定义，JSON格式', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Parameters';
    EXEC sp_addextendedproperty 'MS_Description', '模板类型: System=系统模板, User=用户自定义, Shared=共享模板', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'TemplateType';
    EXEC sp_addextendedproperty 'MS_Description', '任务类型: RequirementAnalysis, CodeGeneration, Testing等', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'TaskType';
    EXEC sp_addextendedproperty 'MS_Description', '适用编程语言', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Language';
    EXEC sp_addextendedproperty 'MS_Description', '适用框架', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Framework';
    EXEC sp_addextendedproperty 'MS_Description', '难度级别: Easy, Medium, Hard', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Difficulty';
    EXEC sp_addextendedproperty 'MS_Description', '预估Token数量', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'EstimatedTokens';
    EXEC sp_addextendedproperty 'MS_Description', '是否启用', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'IsEnabled';
    EXEC sp_addextendedproperty 'MS_Description', '使用次数统计', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'UsageCount';
    EXEC sp_addextendedproperty 'MS_Description', '平均评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'AverageRating';
    EXEC sp_addextendedproperty 'MS_Description', '最后使用时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'LastUsedTime';
    EXEC sp_addextendedproperty 'MS_Description', '模板标签，用于搜索和分类', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Tags';
    EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'CreatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'UpdatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'CreatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'UpdatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '是否删除', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'IsDeleted';
    EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'DeletedTime';
    EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'DeletedBy';
    EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Version';
    EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'PromptTemplates', 'COLUMN', 'Remarks';

    PRINT 'AI提示词模板表 (PromptTemplates) 创建成功';
END
ELSE
BEGIN
    PRINT 'AI提示词模板表 (PromptTemplates) 已存在，跳过创建';
END
GO

-- =============================================
-- 3. AI提示词使用统计表 (PromptUsageStats)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromptUsageStats')
BEGIN
    CREATE TABLE PromptUsageStats (
        Id int IDENTITY(1,1) NOT NULL,
        TemplateId int NOT NULL, -- 关联的模板ID
        UserId int NOT NULL, -- 使用者用户ID
        ProjectId int NULL, -- 关联的项目ID
        AIProvider nvarchar(50) NULL, -- AI提供商
        AIModel nvarchar(100) NULL, -- AI模型名称
        InputParameters nvarchar(max) NULL, -- 输入参数，JSON格式
        GeneratedPrompt nvarchar(max) NULL, -- 生成的完整提示词
        AIResponse nvarchar(max) NULL, -- AI响应内容（截取前1000字符）
        ResponseTimeMs int NULL, -- 响应时间（毫秒）
        TokenUsage int NULL, -- Token使用量
        Cost decimal(10,4) NULL, -- 本次调用成本
        IsSuccess bit NOT NULL DEFAULT 1, -- 是否成功
        ErrorMessage nvarchar(max) NULL, -- 错误信息
        UsedAt datetime2 NOT NULL DEFAULT GETDATE(), -- 使用时间
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(), -- 创建时间
        UpdatedTime datetime2 NULL, -- 更新时间
        CreatedBy int NULL, -- 创建者ID
        UpdatedBy int NULL, -- 更新者ID
        IsDeleted bit NOT NULL DEFAULT 0, -- 是否删除
        DeletedTime datetime2 NULL, -- 删除时间
        DeletedBy int NULL, -- 删除者ID
        Version int NOT NULL DEFAULT 1, -- 版本号
        Remarks nvarchar(500) NULL, -- 备注

        CONSTRAINT PK_PromptUsageStats PRIMARY KEY (Id),
        CONSTRAINT FK_PromptUsageStats_TemplateId FOREIGN KEY (TemplateId) REFERENCES PromptTemplates(Id),
        CONSTRAINT FK_PromptUsageStats_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
        CONSTRAINT FK_PromptUsageStats_ProjectId FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
        CONSTRAINT CK_PromptUsageStats_AIProvider CHECK (AIProvider IN ('Azure', 'OpenAI', 'DeepSeek', 'Claude', 'Ollama', 'Mock'))
    );

    -- 创建索引
    CREATE INDEX IX_PromptUsageStats_TemplateId ON PromptUsageStats(TemplateId);
    CREATE INDEX IX_PromptUsageStats_UserId ON PromptUsageStats(UserId);
    CREATE INDEX IX_PromptUsageStats_ProjectId ON PromptUsageStats(ProjectId);
    CREATE INDEX IX_PromptUsageStats_AIProvider ON PromptUsageStats(AIProvider);
    CREATE INDEX IX_PromptUsageStats_UsedAt ON PromptUsageStats(UsedAt);
    CREATE INDEX IX_PromptUsageStats_IsSuccess ON PromptUsageStats(IsSuccess);
    CREATE INDEX IX_PromptUsageStats_IsDeleted ON PromptUsageStats(IsDeleted);

    -- 添加表注释
    EXEC sp_addextendedproperty 'MS_Description', 'AI提示词使用统计表', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '统计ID，主键', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Id';
    EXEC sp_addextendedproperty 'MS_Description', '关联的模板ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'TemplateId';
    EXEC sp_addextendedproperty 'MS_Description', '使用者用户ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UserId';
    EXEC sp_addextendedproperty 'MS_Description', '关联的项目ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ProjectId';
    EXEC sp_addextendedproperty 'MS_Description', 'AI提供商', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIProvider';
    EXEC sp_addextendedproperty 'MS_Description', 'AI模型名称', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIModel';
    EXEC sp_addextendedproperty 'MS_Description', '输入参数，JSON格式', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'InputParameters';
    EXEC sp_addextendedproperty 'MS_Description', '生成的完整提示词', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'GeneratedPrompt';
    EXEC sp_addextendedproperty 'MS_Description', 'AI响应内容（截取前1000字符）', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIResponse';
    EXEC sp_addextendedproperty 'MS_Description', '响应时间（毫秒）', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ResponseTimeMs';
    EXEC sp_addextendedproperty 'MS_Description', 'Token使用量', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'TokenUsage';
    EXEC sp_addextendedproperty 'MS_Description', '本次调用成本', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Cost';
    EXEC sp_addextendedproperty 'MS_Description', '是否成功', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'IsSuccess';
    EXEC sp_addextendedproperty 'MS_Description', '错误信息', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ErrorMessage';
    EXEC sp_addextendedproperty 'MS_Description', '使用时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UsedAt';
    EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'CreatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UpdatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'CreatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UpdatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '是否删除', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'IsDeleted';
    EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'DeletedTime';
    EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'DeletedBy';
    EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Version';
    EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Remarks';

    -- 添加表注释
    EXEC sp_addextendedproperty 'MS_Description', 'AI提示词使用统计表', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '统计ID，主键', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Id';
    EXEC sp_addextendedproperty 'MS_Description', '关联的模板ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'TemplateId';
    EXEC sp_addextendedproperty 'MS_Description', '使用者用户ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UserId';
    EXEC sp_addextendedproperty 'MS_Description', '关联的项目ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ProjectId';
    EXEC sp_addextendedproperty 'MS_Description', 'AI提供商', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIProvider';
    EXEC sp_addextendedproperty 'MS_Description', 'AI模型名称', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIModel';
    EXEC sp_addextendedproperty 'MS_Description', '输入参数，JSON格式', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'InputParameters';
    EXEC sp_addextendedproperty 'MS_Description', '生成的完整提示词', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'GeneratedPrompt';
    EXEC sp_addextendedproperty 'MS_Description', 'AI响应内容（截取前1000字符）', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'AIResponse';
    EXEC sp_addextendedproperty 'MS_Description', '响应时间（毫秒）', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ResponseTimeMs';
    EXEC sp_addextendedproperty 'MS_Description', 'Token使用量', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'TokenUsage';
    EXEC sp_addextendedproperty 'MS_Description', '本次调用成本', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Cost';
    EXEC sp_addextendedproperty 'MS_Description', '是否成功', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'IsSuccess';
    EXEC sp_addextendedproperty 'MS_Description', '错误信息', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'ErrorMessage';
    EXEC sp_addextendedproperty 'MS_Description', '使用时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UsedAt';
    EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'CreatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UpdatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'CreatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'UpdatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '是否删除', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'IsDeleted';
    EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'DeletedTime';
    EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'DeletedBy';
    EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Version';
    EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'PromptUsageStats', 'COLUMN', 'Remarks';

    PRINT 'AI提示词使用统计表 (PromptUsageStats) 创建成功';
END
ELSE
BEGIN
    PRINT 'AI提示词使用统计表 (PromptUsageStats) 已存在，跳过创建';
END
GO

-- =============================================
-- 4. AI提示词评价表 (PromptRatings)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PromptRatings')
BEGIN
    CREATE TABLE PromptRatings (
        Id int IDENTITY(1,1) NOT NULL,
        TemplateId int NOT NULL, -- 关联的模板ID
        UserId int NOT NULL, -- 评价用户ID
        UsageStatsId int NULL, -- 关联的使用统计ID
        OverallRating int NOT NULL, -- 总体评分（1-5分）
        AccuracyRating int NULL, -- 准确性评分（1-5分）
        UsefulnessRating int NULL, -- 实用性评分（1-5分）
        ClarityRating int NULL, -- 清晰度评分（1-5分）
        EfficiencyRating int NULL, -- 效率评分（1-5分）
        Comments nvarchar(max) NULL, -- 评价意见
        Suggestions nvarchar(max) NULL, -- 改进建议
        RatedAt datetime2 NOT NULL DEFAULT GETDATE(), -- 评价时间
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(), -- 创建时间
        UpdatedTime datetime2 NULL, -- 更新时间
        CreatedBy int NULL, -- 创建者ID
        UpdatedBy int NULL, -- 更新者ID
        IsDeleted bit NOT NULL DEFAULT 0, -- 是否删除
        DeletedTime datetime2 NULL, -- 删除时间
        DeletedBy int NULL, -- 删除者ID
        Version int NOT NULL DEFAULT 1, -- 版本号
        Remarks nvarchar(500) NULL, -- 备注

        CONSTRAINT PK_PromptRatings PRIMARY KEY (Id),
        CONSTRAINT FK_PromptRatings_TemplateId FOREIGN KEY (TemplateId) REFERENCES PromptTemplates(Id),
        CONSTRAINT FK_PromptRatings_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
        CONSTRAINT FK_PromptRatings_UsageStatsId FOREIGN KEY (UsageStatsId) REFERENCES PromptUsageStats(Id),
        CONSTRAINT CK_PromptRatings_OverallRating CHECK (OverallRating >= 1 AND OverallRating <= 5),
        CONSTRAINT CK_PromptRatings_AccuracyRating CHECK (AccuracyRating >= 1 AND AccuracyRating <= 5),
        CONSTRAINT CK_PromptRatings_UsefulnessRating CHECK (UsefulnessRating >= 1 AND UsefulnessRating <= 5),
        CONSTRAINT CK_PromptRatings_ClarityRating CHECK (ClarityRating >= 1 AND ClarityRating <= 5),
        CONSTRAINT CK_PromptRatings_EfficiencyRating CHECK (EfficiencyRating >= 1 AND EfficiencyRating <= 5)
    );

    -- 创建索引
    CREATE INDEX IX_PromptRatings_TemplateId ON PromptRatings(TemplateId);
    CREATE INDEX IX_PromptRatings_UserId ON PromptRatings(UserId);
    CREATE INDEX IX_PromptRatings_UsageStatsId ON PromptRatings(UsageStatsId);
    CREATE INDEX IX_PromptRatings_OverallRating ON PromptRatings(OverallRating);
    CREATE INDEX IX_PromptRatings_RatedAt ON PromptRatings(RatedAt);
    CREATE INDEX IX_PromptRatings_IsDeleted ON PromptRatings(IsDeleted);

    -- 添加表注释
    EXEC sp_addextendedproperty 'MS_Description', 'AI提示词评价表', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings';

    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '评价ID，主键', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'Id';
    EXEC sp_addextendedproperty 'MS_Description', '关联的模板ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'TemplateId';
    EXEC sp_addextendedproperty 'MS_Description', '评价用户ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'UserId';
    EXEC sp_addextendedproperty 'MS_Description', '关联的使用统计ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'UsageStatsId';
    EXEC sp_addextendedproperty 'MS_Description', '总体评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'OverallRating';
    EXEC sp_addextendedproperty 'MS_Description', '准确性评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'AccuracyRating';
    EXEC sp_addextendedproperty 'MS_Description', '实用性评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'UsefulnessRating';
    EXEC sp_addextendedproperty 'MS_Description', '清晰度评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'ClarityRating';
    EXEC sp_addextendedproperty 'MS_Description', '效率评分（1-5分）', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'EfficiencyRating';
    EXEC sp_addextendedproperty 'MS_Description', '评价意见', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'Comments';
    EXEC sp_addextendedproperty 'MS_Description', '改进建议', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'Suggestions';
    EXEC sp_addextendedproperty 'MS_Description', '评价时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'RatedAt';
    EXEC sp_addextendedproperty 'MS_Description', '创建时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'CreatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '更新时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'UpdatedTime';
    EXEC sp_addextendedproperty 'MS_Description', '创建者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'CreatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '更新者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'UpdatedBy';
    EXEC sp_addextendedproperty 'MS_Description', '是否删除', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'IsDeleted';
    EXEC sp_addextendedproperty 'MS_Description', '删除时间', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'DeletedTime';
    EXEC sp_addextendedproperty 'MS_Description', '删除者ID', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'DeletedBy';
    EXEC sp_addextendedproperty 'MS_Description', '版本号', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'Version';
    EXEC sp_addextendedproperty 'MS_Description', '备注', 'SCHEMA', 'dbo', 'TABLE', 'PromptRatings', 'COLUMN', 'Remarks';

    PRINT 'AI提示词评价表 (PromptRatings) 创建成功';
END
ELSE
BEGIN
    PRINT 'AI提示词评价表 (PromptRatings) 已存在，跳过创建';
END
GO

-- =============================================
-- 5. 用户提示词偏好表 (UserPromptPreferences)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserPromptPreferences')
BEGIN
    CREATE TABLE UserPromptPreferences (
        Id int IDENTITY(1,1) NOT NULL,
        UserId int NOT NULL, -- 用户ID
        TemplateId int NOT NULL, -- 关联的模板ID
        PreferenceType nvarchar(20) NOT NULL, -- 偏好类型: Favorite=收藏, Recent=最近使用, Custom=自定义
        PreferenceValue nvarchar(max) NULL, -- 偏好值，JSON格式存储具体偏好设置
        Priority int NOT NULL DEFAULT 1, -- 优先级，数值越小优先级越高
        IsActive bit NOT NULL DEFAULT 1, -- 是否激活
        LastAccessTime datetime2 NULL, -- 最后访问时间
        AccessCount int NOT NULL DEFAULT 0, -- 访问次数
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(), -- 创建时间
        UpdatedTime datetime2 NULL, -- 更新时间
        CreatedBy int NULL, -- 创建者ID
        UpdatedBy int NULL, -- 更新者ID
        IsDeleted bit NOT NULL DEFAULT 0, -- 是否删除
        DeletedTime datetime2 NULL, -- 删除时间
        DeletedBy int NULL, -- 删除者ID
        Version int NOT NULL DEFAULT 1, -- 版本号
        Remarks nvarchar(500) NULL, -- 备注

        CONSTRAINT PK_UserPromptPreferences PRIMARY KEY (Id),
        CONSTRAINT FK_UserPromptPreferences_UserId FOREIGN KEY (UserId) REFERENCES Users(Id),
        CONSTRAINT FK_UserPromptPreferences_TemplateId FOREIGN KEY (TemplateId) REFERENCES PromptTemplates(Id),
        CONSTRAINT CK_UserPromptPreferences_PreferenceType CHECK (PreferenceType IN ('Favorite', 'Recent', 'Custom')),
        CONSTRAINT CK_UserPromptPreferences_Priority CHECK (Priority >= 1 AND Priority <= 100),
        CONSTRAINT UK_UserPromptPreferences_User_Template_Type UNIQUE (UserId, TemplateId, PreferenceType)
    );

    -- 创建索引
    CREATE INDEX IX_UserPromptPreferences_UserId ON UserPromptPreferences(UserId);
    CREATE INDEX IX_UserPromptPreferences_TemplateId ON UserPromptPreferences(TemplateId);
    CREATE INDEX IX_UserPromptPreferences_PreferenceType ON UserPromptPreferences(PreferenceType);
    CREATE INDEX IX_UserPromptPreferences_Priority ON UserPromptPreferences(Priority);
    CREATE INDEX IX_UserPromptPreferences_IsActive ON UserPromptPreferences(IsActive);
    CREATE INDEX IX_UserPromptPreferences_LastAccessTime ON UserPromptPreferences(LastAccessTime);
    CREATE INDEX IX_UserPromptPreferences_IsDeleted ON UserPromptPreferences(IsDeleted);

    PRINT '用户提示词偏好表 (UserPromptPreferences) 创建成功';
END
ELSE
BEGIN
    PRINT '用户提示词偏好表 (UserPromptPreferences) 已存在，跳过创建';
END
GO

-- =============================================
-- 初始化默认数据
-- =============================================

-- 初始化默认分类
IF NOT EXISTS (SELECT * FROM PromptCategories WHERE Name = '需求分析')
BEGIN
    INSERT INTO PromptCategories (Name, Description, Icon, Color, SortOrder, CreatedTime)
    VALUES
    ('需求分析', '用于需求收集、分析和整理的提示词模板', 'analysis', '#1890ff', 1, GETDATE()),
    ('代码生成', '用于各种代码生成任务的提示词模板', 'code', '#52c41a', 2, GETDATE()),
    ('测试生成', '用于测试用例和测试代码生成的提示词模板', 'test', '#faad14', 3, GETDATE()),
    ('文档生成', '用于技术文档和说明文档生成的提示词模板', 'document', '#722ed1', 4, GETDATE()),
    ('代码审查', '用于代码质量检查和优化建议的提示词模板', 'review', '#f5222d', 5, GETDATE()),
    ('调试辅助', '用于问题诊断和调试的提示词模板', 'debug', '#fa8c16', 6, GETDATE());

    PRINT '默认Prompt分类初始化完成';
END
ELSE
BEGIN
    PRINT '默认Prompt分类已存在，跳过初始化';
END
GO

-- =============================================
-- 初始化默认提示词模板
-- =============================================

-- 初始化默认模板数据
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '需求分析对话模板')
BEGIN
    -- 获取分类ID
    DECLARE @RequirementCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '需求分析');
    DECLARE @CodeGenCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '代码生成');
    DECLARE @TestCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '测试生成');
    DECLARE @DocumentCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '文档生成');
    DECLARE @ReviewCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '代码审查');
    DECLARE @DebugCategoryId int = (SELECT Id FROM PromptCategories WHERE Name = '调试辅助');

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, CreatedTime
    )
    VALUES
    -- 需求分析模板
    (
        '需求分析对话模板',
        '用于与用户进行需求分析对话，收集和整理项目需求',
        @RequirementCategoryId,
        N'你是一个专业的需求分析师，请与用户进行需求分析对话。

项目背景：{projectBackground}
当前讨论主题：{topic}

请按照以下步骤进行需求分析：

1. **需求理解**：
   - 仔细理解用户描述的需求
   - 识别关键功能点和业务流程
   - 确认技术约束和限制条件

2. **需求细化**：
   - 针对模糊或不完整的需求提出具体问题
   - 引导用户提供更详细的信息
   - 确认功能的优先级和重要性

3. **需求确认**：
   - 总结理解的需求要点
   - 确认是否有遗漏或误解
   - 为下一步设计提供清晰的需求基础

请以专业、友好的方式与用户对话，确保收集到完整、准确的需求信息。',
        N'{"projectBackground": {"type": "string", "description": "项目背景信息", "required": true}, "topic": {"type": "string", "description": "当前讨论的需求主题", "required": true}}',
        'System',
        'RequirementAnalysis',
        NULL,
        NULL,
        'Easy',
        800,
        '需求分析,对话,业务需求,功能需求',
        GETDATE()
    ),
    (
        '需求规格书生成模板',
        '根据需求对话内容生成标准的需求规格书文档',
        @RequirementCategoryId,
        N'请根据以下需求对话内容，生成一份完整的需求规格书。

需求对话记录：
{conversationHistory}

项目信息：
- 项目名称：{projectName}
- 项目类型：{projectType}
- 技术栈：{techStack}

请按照以下结构生成需求规格书：

## 1. 项目概述
- 项目背景
- 项目目标
- 项目范围

## 2. 功能需求
- 核心功能列表
- 功能详细描述
- 用户故事

## 3. 非功能需求
- 性能要求
- 安全要求
- 可用性要求
- 兼容性要求

## 4. 技术约束
- 技术栈要求
- 部署环境
- 第三方依赖

## 5. 验收标准
- 功能验收标准
- 性能验收标准
- 质量验收标准

请确保需求规格书内容完整、准确、可执行。',
        N'{"conversationHistory": {"type": "string", "description": "需求对话历史记录", "required": true}, "projectName": {"type": "string", "description": "项目名称", "required": true}, "projectType": {"type": "string", "description": "项目类型", "required": false}, "techStack": {"type": "string", "description": "技术栈", "required": false}}',
        'System',
        'RequirementAnalysis',
        NULL,
        NULL,
        'Medium',
        1200,
        '需求规格书,文档生成,需求整理,项目规划',
        GETDATE()
    ),

    -- 代码生成模板
    (
        'C# Web API控制器生成',
        '根据需求规格书生成C# Web API控制器代码',
        @CodeGenCategoryId,
        N'请根据以下需求规格书，生成C# Web API控制器代码。

需求规格书：
{specification}

实体模型：
{entityModel}

请生成包含以下内容的完整控制器：

1. **基础结构**：
   - 继承BaseController
   - 注入必要的服务
   - 添加适当的特性和注释

2. **CRUD操作**：
   - GET: 获取列表（支持分页、筛选、排序）
   - GET: 根据ID获取单个实体
   - POST: 创建新实体
   - PUT: 更新实体
   - DELETE: 删除实体

3. **业务操作**：
   - 根据需求添加特定的业务方法
   - 实现复杂的查询和操作

4. **代码规范**：
   - 遵循RESTful API设计原则
   - 添加完整的XML注释
   - 实现适当的异常处理
   - 添加参数验证

请确保生成的代码符合项目架构和编码规范。',
        N'{"specification": {"type": "string", "description": "需求规格书内容", "required": true}, "entityModel": {"type": "string", "description": "实体模型定义", "required": true}}',
        'System',
        'CodeGeneration',
        'C#',
        'ASP.NET Core',
        'Medium',
        1500,
        'C#,Web API,控制器,CRUD,RESTful',
        GETDATE()
    ),
    (
        'Vue组件生成模板',
        '根据UI设计和功能需求生成Vue组件代码',
        @CodeGenCategoryId,
        N'请根据以下设计和需求，生成Vue 3组件代码。

功能需求：
{requirements}

UI设计描述：
{uiDesign}

API接口：
{apiInterface}

请生成包含以下内容的完整Vue组件：

1. **组件结构**：
   - 使用Composition API
   - 合理的组件拆分
   - 响应式数据管理

2. **模板部分**：
   - 语义化的HTML结构
   - 使用Element Plus组件
   - 响应式布局设计

3. **脚本部分**：
   - TypeScript类型定义
   - 生命周期钩子
   - 事件处理方法
   - API调用逻辑

4. **样式部分**：
   - SCSS样式编写
   - 响应式设计
   - 主题适配

5. **功能特性**：
   - 表单验证
   - 数据加载状态
   - 错误处理
   - 用户交互反馈

请确保代码符合Vue 3最佳实践和项目规范。',
        N'{"requirements": {"type": "string", "description": "功能需求描述", "required": true}, "uiDesign": {"type": "string", "description": "UI设计描述", "required": true}, "apiInterface": {"type": "string", "description": "API接口定义", "required": false}}',
        'System',
        'CodeGeneration',
        'TypeScript',
        'Vue 3',
        'Medium',
        1800,
        'Vue,组件,TypeScript,Element Plus,前端',
        GETDATE()
    );

    PRINT '默认Prompt模板（第一批）初始化完成';
END
ELSE
BEGIN
    PRINT '默认Prompt模板已存在，跳过初始化';
END
GO

-- 继续添加更多默认模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '单元测试生成模板')
BEGIN
    -- 获取分类ID
    DECLARE @TestCategoryId2 int = (SELECT Id FROM PromptCategories WHERE Name = '测试生成');
    DECLARE @DocumentCategoryId2 int = (SELECT Id FROM PromptCategories WHERE Name = '文档生成');
    DECLARE @ReviewCategoryId2 int = (SELECT Id FROM PromptCategories WHERE Name = '代码审查');
    DECLARE @DebugCategoryId2 int = (SELECT Id FROM PromptCategories WHERE Name = '调试辅助');

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, CreatedTime
    )
    VALUES
    -- 测试生成模板
    (
        '单元测试生成模板',
        '根据代码和需求规格书生成完整的单元测试代码',
        @TestCategoryId2,
        N'请根据以下代码和需求规格书，生成完整的单元测试。

待测试代码：
{codeContent}

需求规格书：
{specification}

测试框架：{testFramework}

请生成包含以下内容的测试代码：

1. **测试类结构**：
   - 合理的测试类命名
   - 必要的测试初始化和清理
   - Mock对象的设置

2. **测试用例覆盖**：
   - 正常流程测试
   - 边界条件测试
   - 异常情况测试
   - 业务逻辑验证

3. **测试数据**：
   - 有效的测试数据
   - 边界值测试数据
   - 无效数据测试

4. **断言验证**：
   - 返回值验证
   - 状态变化验证
   - 副作用验证

5. **测试文档**：
   - 测试用例描述
   - 测试目的说明
   - 预期结果描述

请确保测试代码具有良好的可读性和维护性。',
        N'{"codeContent": {"type": "string", "description": "要测试的代码", "required": true}, "specification": {"type": "string", "description": "需求规格书", "required": false}, "testFramework": {"type": "string", "description": "测试框架", "required": false, "default": "xUnit"}}',
        'System',
        'Testing',
        'C#',
        'xUnit',
        'Medium',
        1600,
        '单元测试,测试用例,Mock,自动化测试,TDD',
        GETDATE()
    ),
    (
        '集成测试生成模板',
        '生成API接口和服务集成测试代码',
        @TestCategoryId2,
        N'请根据以下API接口和服务信息，生成集成测试代码。

API接口定义：
{apiDefinition}

服务依赖：
{serviceDependencies}

数据库架构：
{databaseSchema}

请生成包含以下内容的集成测试：

1. **测试环境设置**：
   - 测试数据库配置
   - 服务容器配置
   - 测试数据初始化

2. **API测试用例**：
   - HTTP请求测试
   - 响应状态码验证
   - 响应数据格式验证
   - 业务逻辑验证

3. **数据持久化测试**：
   - 数据库操作验证
   - 事务处理测试
   - 数据一致性检查

4. **服务集成测试**：
   - 服务间调用测试
   - 依赖注入验证
   - 配置加载测试

5. **性能测试**：
   - 响应时间测试
   - 并发处理测试
   - 资源使用测试

请确保测试覆盖主要的集成场景。',
        N'{"apiDefinition": {"type": "string", "description": "API接口定义", "required": true}, "serviceDependencies": {"type": "string", "description": "服务依赖关系", "required": false}, "databaseSchema": {"type": "string", "description": "数据库架构", "required": false}}',
        'System',
        'Testing',
        'C#',
        'ASP.NET Core',
        'Hard',
        2000,
        '集成测试,API测试,数据库测试,服务测试',
        GETDATE()
    ),

    -- 文档生成模板
    (
        'API文档生成模板',
        '根据控制器代码生成详细的API文档',
        @DocumentCategoryId2,
        N'请根据以下控制器代码，生成详细的API文档。

控制器代码：
{controllerCode}

项目信息：
{projectInfo}

请生成包含以下内容的API文档：

## API概述
- API版本信息
- 基础URL
- 认证方式
- 通用响应格式

## 接口列表

对于每个接口，请包含：

### {接口名称}
- **请求方式**：GET/POST/PUT/DELETE
- **请求路径**：/api/xxx
- **接口描述**：功能说明
- **请求参数**：
  - 路径参数
  - 查询参数
  - 请求体参数
- **响应格式**：
  - 成功响应示例
  - 错误响应示例
- **状态码说明**：
  - 200: 成功
  - 400: 请求错误
  - 401: 未授权
  - 404: 资源不存在
  - 500: 服务器错误

## 数据模型
- 请求模型定义
- 响应模型定义
- 枚举类型说明

## 错误码说明
- 业务错误码列表
- 错误信息描述

请确保文档内容准确、完整、易于理解。',
        N'{"controllerCode": {"type": "string", "description": "控制器代码", "required": true}, "projectInfo": {"type": "string", "description": "项目基本信息", "required": false}}',
        'System',
        'Documentation',
        NULL,
        NULL,
        'Easy',
        1200,
        'API文档,接口文档,Swagger,技术文档',
        GETDATE()
    ),
    (
        '技术设计文档模板',
        '根据需求规格书生成技术设计文档',
        @DocumentCategoryId2,
        N'请根据以下需求规格书，生成详细的技术设计文档。

需求规格书：
{specification}

技术栈：
{techStack}

架构约束：
{constraints}

请生成包含以下内容的技术设计文档：

## 1. 系统架构设计
- 整体架构图
- 技术栈选择说明
- 部署架构设计

## 2. 数据库设计
- ER图设计
- 表结构设计
- 索引设计
- 数据迁移策略

## 3. API设计
- RESTful API设计
- 接口规范定义
- 认证授权设计
- 版本控制策略

## 4. 前端设计
- 组件架构设计
- 状态管理设计
- 路由设计
- UI/UX设计规范

## 5. 安全设计
- 认证机制
- 授权策略
- 数据加密
- 安全防护措施

## 6. 性能设计
- 缓存策略
- 数据库优化
- 前端性能优化
- 监控和日志

## 7. 部署设计
- 环境配置
- CI/CD流程
- 容器化部署
- 监控告警

请确保设计方案技术可行、架构合理。',
        N'{"specification": {"type": "string", "description": "需求规格书", "required": true}, "techStack": {"type": "string", "description": "技术栈信息", "required": true}, "constraints": {"type": "string", "description": "架构约束条件", "required": false}}',
        'System',
        'Documentation',
        NULL,
        NULL,
        'Hard',
        2500,
        '技术设计,系统架构,数据库设计,API设计',
        GETDATE()
    );

    PRINT '默认Prompt模板（第二批）初始化完成';
END
ELSE
BEGIN
    PRINT '默认Prompt模板（第二批）已存在，跳过初始化';
END
GO

-- 添加最后一批默认模板
IF NOT EXISTS (SELECT * FROM PromptTemplates WHERE Name = '代码审查模板')
BEGIN
    -- 获取分类ID
    DECLARE @ReviewCategoryId3 int = (SELECT Id FROM PromptCategories WHERE Name = '代码审查');
    DECLARE @DebugCategoryId3 int = (SELECT Id FROM PromptCategories WHERE Name = '调试辅助');

    INSERT INTO PromptTemplates (
        Name, Description, CategoryId, Content, Parameters, TemplateType, TaskType,
        Language, Framework, Difficulty, EstimatedTokens, Tags, CreatedTime
    )
    VALUES
    -- 代码审查模板
    (
        '代码审查模板',
        '对提交的代码进行全面的质量审查和改进建议',
        @ReviewCategoryId3,
        N'请对以下代码进行全面的代码审查。

代码内容：
{codeContent}

代码类型：{codeType}
编程语言：{language}

请从以下维度进行审查：

## 1. 代码质量
- **可读性**：变量命名、函数命名、代码结构
- **可维护性**：代码复杂度、模块化程度
- **可扩展性**：设计模式使用、架构合理性

## 2. 编码规范
- **命名规范**：是否符合语言约定
- **格式规范**：缩进、空格、换行
- **注释规范**：注释完整性和准确性

## 3. 性能优化
- **算法效率**：时间复杂度和空间复杂度
- **资源使用**：内存泄漏、资源释放
- **数据库操作**：查询优化、索引使用

## 4. 安全性检查
- **输入验证**：参数校验、SQL注入防护
- **权限控制**：访问控制、数据保护
- **错误处理**：异常处理、敏感信息泄露

## 5. 测试覆盖
- **单元测试**：测试用例覆盖度
- **边界测试**：异常情况处理
- **集成测试**：模块间交互

## 审查结果
请提供：
- 发现的问题列表（按严重程度排序）
- 具体的改进建议
- 优化后的代码示例
- 最佳实践推荐

请确保审查意见具体、可操作。',
        N'{"codeContent": {"type": "string", "description": "要审查的代码", "required": true}, "codeType": {"type": "string", "description": "代码类型（如：控制器、服务、组件等）", "required": false}, "language": {"type": "string", "description": "编程语言", "required": false}}',
        'System',
        'Review',
        NULL,
        NULL,
        'Medium',
        1800,
        '代码审查,代码质量,性能优化,安全检查,最佳实践',
        GETDATE()
    ),
    (
        '架构审查模板',
        '对系统架构设计进行审查和优化建议',
        @ReviewCategoryId3,
        N'请对以下系统架构设计进行全面审查。

架构设计文档：
{architectureDoc}

技术栈：
{techStack}

业务需求：
{businessRequirements}

请从以下维度进行架构审查：

## 1. 架构合理性
- **分层设计**：是否合理分层，职责清晰
- **模块划分**：模块间耦合度和内聚性
- **技术选型**：技术栈是否适合业务需求

## 2. 可扩展性
- **水平扩展**：系统是否支持水平扩展
- **垂直扩展**：单机性能提升空间
- **功能扩展**：新功能添加的便利性

## 3. 可维护性
- **代码组织**：项目结构是否清晰
- **依赖管理**：依赖关系是否合理
- **配置管理**：配置是否集中管理

## 4. 性能设计
- **缓存策略**：缓存设计是否合理
- **数据库设计**：数据库架构和优化
- **负载均衡**：负载分配策略

## 5. 安全架构
- **认证授权**：身份验证和权限控制
- **数据安全**：数据传输和存储安全
- **网络安全**：网络层面的安全防护

## 6. 运维友好性
- **监控体系**：系统监控和告警
- **日志管理**：日志收集和分析
- **部署策略**：CI/CD和部署自动化

## 审查结果
请提供：
- 架构优势分析
- 潜在风险识别
- 改进建议和方案
- 技术债务评估

请确保建议具有可操作性和前瞻性。',
        N'{"architectureDoc": {"type": "string", "description": "架构设计文档", "required": true}, "techStack": {"type": "string", "description": "技术栈信息", "required": true}, "businessRequirements": {"type": "string", "description": "业务需求", "required": false}}',
        'System',
        'Review',
        NULL,
        NULL,
        'Hard',
        2200,
        '架构审查,系统设计,技术选型,性能优化,安全架构',
        GETDATE()
    ),

    -- 调试辅助模板
    (
        '错误诊断模板',
        '分析错误信息并提供解决方案',
        @DebugCategoryId3,
        N'请分析以下错误信息并提供解决方案。

错误信息：
{errorMessage}

错误堆栈：
{stackTrace}

相关代码：
{relatedCode}

运行环境：
{environment}

请按照以下步骤进行错误诊断：

## 1. 错误分析
- **错误类型**：识别错误的具体类型
- **错误原因**：分析导致错误的根本原因
- **影响范围**：评估错误的影响程度

## 2. 问题定位
- **代码定位**：指出具体的问题代码位置
- **数据分析**：分析相关的数据状态
- **环境因素**：检查环境配置问题

## 3. 解决方案
- **即时修复**：提供快速修复方案
- **根本解决**：提供彻底解决方案
- **预防措施**：避免类似问题再次发生

## 4. 代码修复
- **修复代码**：提供具体的修复代码
- **测试建议**：建议相应的测试方法
- **验证步骤**：验证修复效果的步骤

## 5. 最佳实践
- **编码建议**：相关的编码最佳实践
- **错误处理**：改进错误处理机制
- **监控建议**：添加相应的监控和日志

请确保解决方案具体可行，并提供详细的实施步骤。',
        N'{"errorMessage": {"type": "string", "description": "错误信息", "required": true}, "stackTrace": {"type": "string", "description": "错误堆栈信息", "required": false}, "relatedCode": {"type": "string", "description": "相关代码", "required": false}, "environment": {"type": "string", "description": "运行环境信息", "required": false}}',
        'System',
        'Debugging',
        NULL,
        NULL,
        'Medium',
        1500,
        '错误诊断,问题定位,调试,故障排除,解决方案',
        GETDATE()
    ),
    (
        '性能优化分析模板',
        '分析性能问题并提供优化建议',
        @DebugCategoryId3,
        N'请分析以下性能问题并提供优化建议。

性能问题描述：
{performanceIssue}

性能数据：
{performanceData}

相关代码：
{codeContent}

系统环境：
{systemEnvironment}

请按照以下维度进行性能分析：

## 1. 性能问题识别
- **瓶颈定位**：识别主要的性能瓶颈
- **指标分析**：分析关键性能指标
- **趋势分析**：性能变化趋势

## 2. 根因分析
- **代码层面**：算法复杂度、数据结构选择
- **数据库层面**：查询效率、索引使用
- **系统层面**：资源使用、配置优化
- **网络层面**：网络延迟、带宽使用

## 3. 优化方案
- **代码优化**：
  - 算法优化建议
  - 数据结构改进
  - 缓存策略应用

- **数据库优化**：
  - SQL查询优化
  - 索引优化建议
  - 数据库配置调优

- **系统优化**：
  - 内存使用优化
  - CPU使用优化
  - I/O操作优化

## 4. 实施建议
- **优先级排序**：按影响程度排序优化项
- **实施步骤**：详细的实施计划
- **风险评估**：优化过程中的风险点
- **效果预期**：预期的性能提升效果

## 5. 监控建议
- **性能指标**：需要监控的关键指标
- **告警设置**：性能告警阈值设置
- **持续优化**：长期性能优化策略

请提供具体可行的优化方案和实施步骤。',
        N'{"performanceIssue": {"type": "string", "description": "性能问题描述", "required": true}, "performanceData": {"type": "string", "description": "性能监控数据", "required": false}, "codeContent": {"type": "string", "description": "相关代码", "required": false}, "systemEnvironment": {"type": "string", "description": "系统环境信息", "required": false}}',
        'System',
        'Debugging',
        NULL,
        NULL,
        'Hard',
        2000,
        '性能优化,性能分析,调优,监控,系统优化',
        GETDATE()
    );

    PRINT '默认Prompt模板（第三批）初始化完成';
END
ELSE
BEGIN
    PRINT '默认Prompt模板（第三批）已存在，跳过初始化';
END
GO

PRINT '==============================================';
PRINT 'AI提示词相关表创建完成！';
PRINT '==============================================';
PRINT '';
PRINT '已创建以下5个表：';
PRINT '1. PromptCategories - AI提示词分类表';
PRINT '2. PromptTemplates - AI提示词模板表';
PRINT '3. PromptUsageStats - AI提示词使用统计表';
PRINT '4. PromptRatings - AI提示词评价表';
PRINT '5. UserPromptPreferences - 用户提示词偏好表';
PRINT '';
PRINT '已初始化6个默认分类：';
PRINT '- 需求分析、代码生成、测试生成';
PRINT '- 文档生成、代码审查、调试辅助';
PRINT '';
PRINT '已初始化10个默认提示词模板：';
PRINT '需求分析类：';
PRINT '  - 需求分析对话模板';
PRINT '  - 需求规格书生成模板';
PRINT '代码生成类：';
PRINT '  - C# Web API控制器生成';
PRINT '  - Vue组件生成模板';
PRINT '测试生成类：';
PRINT '  - 单元测试生成模板';
PRINT '  - 集成测试生成模板';
PRINT '文档生成类：';
PRINT '  - API文档生成模板';
PRINT '  - 技术设计文档模板';
PRINT '代码审查类：';
PRINT '  - 代码审查模板';
PRINT '  - 架构审查模板';
PRINT '调试辅助类：';
PRINT '  - 错误诊断模板';
PRINT '  - 性能优化分析模板';
PRINT '';
PRINT '现在可以正常使用AI提示词功能了！';
GO
