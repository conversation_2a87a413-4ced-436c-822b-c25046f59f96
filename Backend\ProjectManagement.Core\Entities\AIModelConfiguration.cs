using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// AI模型配置实体 - 对应DatabaseSchema.sql中的AIModelConfigurations表
/// 功能: 管理系统中使用的各种AI模型配置
/// 支持: 多模型支持、参数配置、状态管理、API密钥管理
/// </summary>
[SugarTable("AIModelConfigurations", "AI模型管理表")]
public class AIModelConfiguration
{
    /// <summary>
    /// AI模型配置唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "AI模型配置唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID，关联Users表，NULL表示系统级配置
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "用户ID，关联Users表")]
    public int? UserId { get; set; }

    /// <summary>
    /// AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3）
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "AI模型名称")]
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "API端点URL地址")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥（加密存储）
    /// </summary>
    [SugarColumn(Length = 255, IsNullable = true, ColumnDescription = "API密钥")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置，JSON格式存储（温度、最大令牌数等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "模型参数配置，JSON格式存储")]
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用该模型配置，0=禁用, 1=启用
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "是否启用该模型配置")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 配置创建时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "配置创建时间")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 配置最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "配置最后更新时间")]
    public DateTime? UpdatedAt { get; set; } = DateTime.Now;
}
