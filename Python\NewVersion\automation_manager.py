#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化管理器
整合本地UI自动化功能和后端API
"""

import sys
import os
import time
import threading
import requests
import uuid
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import json
import urllib3

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from vscode_automation import VSCodeAutomation
    from ui_actions import UIActions, create_ui_actions
    from screenshot_manager import ScreenshotManager
    # 注意：dynamic_ui_handler 和 copilot_core 已被移除，功能已集成到混合UI操作器中
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保父目录中的模块文件存在")

# 导入图片管理器
from template_image_manager import TemplateImageManager

# 导入DOM自动化集成
try:
    from copilot_dom_integration import CopilotDOMIntegration
    DOM_AUTOMATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ DOM自动化模块导入失败: {e}")
    DOM_AUTOMATION_AVAILABLE = False

# 导入图像条件执行器
try:
    from image_condition_executor import ImageConditionExecutor
    IMAGE_CONDITION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 图像条件执行器模块导入失败: {e}")
    IMAGE_CONDITION_AVAILABLE = False

# 导入区域图像条件执行器
try:
    from region_image_condition_executor import RegionImageConditionExecutor
    REGION_IMAGE_CONDITION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 区域图像条件执行器模块导入失败: {e}")
    REGION_IMAGE_CONDITION_AVAILABLE = False


class AutomationManager:
    """自动化管理器类"""

    def __init__(self, api_client):
        """
        初始化自动化管理器

        Args:
            api_client: API客户端实例
        """
        self.api_client = api_client

        # 初始化图片管理器
        self.image_manager = TemplateImageManager(api_client)

        # 初始化本地自动化组件
        self.init_local_components()

        # 执行状态
        self.is_executing = False
        self.current_execution = None
        self.execution_callbacks = []

        # 缓存
        self.templates_cache = {}
        self.sequences_cache = {}

        # 初始化DOM自动化集成
        self.dom_integration = None
        if DOM_AUTOMATION_AVAILABLE:
            try:
                self.dom_integration = CopilotDOMIntegration(self.api_client)
                print("✅ DOM自动化集成已初始化")
            except Exception as e:
                print(f"⚠️ DOM自动化集成初始化失败: {e}")

        # 初始化图像条件执行器
        self.image_condition_executor = None
        if IMAGE_CONDITION_AVAILABLE and self.local_components_available:
            try:
                self.image_condition_executor = ImageConditionExecutor(self)
                print("✅ 图像条件执行器已初始化")
            except Exception as e:
                print(f"⚠️ 图像条件执行器初始化失败: {e}")

        # 初始化区域图像条件执行器
        self.region_image_condition_executor = None
        if REGION_IMAGE_CONDITION_AVAILABLE and self.local_components_available:
            try:
                self.region_image_condition_executor = RegionImageConditionExecutor(self)
                print("✅ 区域图像条件执行器已初始化")
            except Exception as e:
                print(f"⚠️ 区域图像条件执行器初始化失败: {e}")

        print("自动化管理器初始化完成")

    def init_local_components(self):
        """初始化本地自动化组件"""
        try:
            # 初始化VSCode自动化
            try:
                self.vscode = VSCodeAutomation()
                print("✅ VSCode自动化初始化成功")
            except Exception as e:
                print(f"⚠️ VSCode自动化初始化失败: {e}")
                self.vscode = None

            # 尝试使用混合UI操作器，回退到传统UI操作器
            self.ui_actions = None
            try:
                self.ui_actions = create_ui_actions(use_hybrid=True)
                print("✅ 使用混合UI操作器 (UIA + 图像识别)")
            except Exception as e:
                print(f"混合UI操作器初始化失败: {e}")
                try:
                    self.ui_actions = UIActions()
                    print("⚠️ 回退到传统UI操作器")
                except Exception as e2:
                    print(f"传统UI操作器初始化也失败: {e2}")
                    print("❌ 无法初始化任何UI操作器，将使用基础功能")
                    self.ui_actions = None

            # 初始化截图管理器
            try:
                self.screenshot_manager = ScreenshotManager()
                print("✅ 截图管理器初始化成功")
            except Exception as e:
                print(f"⚠️ 截图管理器初始化失败: {e}")
                self.screenshot_manager = None

            # 注意：dynamic_ui 和 copilot_core 功能已集成到混合UI操作器中
            self.dynamic_ui = None  # 功能已集成到ui_actions中
            self.copilot_core = None  # 功能已集成到ui_actions中

            # 检查是否有可用的组件
            if self.ui_actions is not None:
                self.local_components_available = True
                print("✅ 本地自动化组件初始化成功")
            else:
                self.local_components_available = False
                print("⚠️ 本地自动化组件部分可用（UI操作器不可用）")

        except Exception as e:
            print(f"❌ 本地自动化组件初始化失败: {e}")
            self.local_components_available = False
            self.ui_actions = None
            self.vscode = None
            self.screenshot_manager = None

    def _check_ui_actions_available(self, action_name: str = "UI操作") -> bool:
        """检查UI操作器是否可用"""
        if self.ui_actions is None:
            print(f"❌ UI操作器不可用，无法执行{action_name}")
            # 尝试创建一个基础的UI操作器
            try:
                import pyautogui
                self.ui_actions = self._create_fallback_ui_actions()
                print(f"✅ 创建了基础UI操作器作为后备")
                return True
            except Exception as e:
                print(f"❌ 无法创建基础UI操作器: {e}")
                return False
        return True

    def _create_fallback_ui_actions(self):
        """创建一个基础的UI操作器作为后备"""
        class FallbackUIActions:
            def __init__(self):
                import pyautogui
                self.pyautogui = pyautogui
                pyautogui.FAILSAFE = True
                pyautogui.PAUSE = 0.3

            def click(self, x: int, y: int, button: str = 'left', clicks: int = 1) -> bool:
                try:
                    self.pyautogui.click(x, y, clicks=clicks, button=button)
                    return True
                except Exception as e:
                    print(f"点击失败: {e}")
                    return False

            def type_text(self, text: str) -> bool:
                try:
                    # 检测是否包含中文字符
                    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)

                    if has_chinese:
                        # 使用简化的剪贴板方法输入中文
                        return self._type_chinese_text_simple(text)
                    else:
                        # 英文直接使用pyautogui
                        self.pyautogui.write(text, interval=0.05)
                        return True
                except Exception as e:
                    print(f"输入文本失败: {e}")
                    return False

            def _type_chinese_text(self, text: str) -> bool:
                """使用剪贴板输入中文文本"""
                try:
                    import pyperclip
                    import time

                    print(f"📝 使用剪贴板输入中文: {text[:30]}{'...' if len(text) > 30 else ''}")
                    print(f"🔍 文本长度: {len(text)} 字符")
                    print(f"🔍 文本编码检查: {text.encode('utf-8')[:50]}...")

                    # 清空剪贴板，避免干扰
                    try:
                        pyperclip.copy("")
                        time.sleep(0.1)
                        print("🧹 清空剪贴板")
                    except:
                        pass

                    # 保存当前剪贴板内容
                    original_clipboard = ""
                    try:
                        original_clipboard = pyperclip.paste()
                        print(f"🔍 原剪贴板内容: {original_clipboard[:30]}{'...' if len(original_clipboard) > 30 else ''}")
                    except:
                        print("🔍 无法获取原剪贴板内容")

                    # 将文本复制到剪贴板，带强化重试机制
                    max_retries = 5
                    clipboard_success = False

                    for attempt in range(max_retries):
                        try:
                            print(f"📋 复制文本到剪贴板 (尝试 {attempt + 1}/{max_retries})...")

                            # 先清空剪贴板
                            pyperclip.copy("")
                            time.sleep(0.1)

                            # 复制目标文本
                            pyperclip.copy(text)
                            time.sleep(0.3)  # 增加等待时间

                            # 验证剪贴板内容
                            clipboard_content = pyperclip.paste()
                            if clipboard_content == text:
                                print(f"✅ 剪贴板复制成功")
                                clipboard_success = True
                                break
                            else:
                                print(f"⚠️ 剪贴板内容不匹配")
                                print(f"   期望: {text[:50]}{'...' if len(text) > 50 else ''}")
                                print(f"   实际: {clipboard_content[:50]}{'...' if len(clipboard_content) > 50 else ''}")
                                time.sleep(0.5)  # 增加重试间隔

                        except Exception as e:
                            print(f"⚠️ 剪贴板复制异常 (尝试 {attempt + 1}): {e}")
                            time.sleep(0.5)

                    if not clipboard_success:
                        print(f"❌ 剪贴板复制失败，已重试 {max_retries} 次")
                        return False

                    # 使用Ctrl+V粘贴，带重试机制
                    paste_retries = 3
                    for paste_attempt in range(paste_retries):
                        try:
                            print(f"⌨️ 执行Ctrl+V粘贴 (尝试 {paste_attempt + 1}/{paste_retries})...")

                            # 执行粘贴
                            self.pyautogui.hotkey('ctrl', 'v')
                            time.sleep(0.5)  # 增加等待时间

                            print(f"✅ 粘贴操作完成")
                            break

                        except Exception as e:
                            print(f"⚠️ 粘贴操作异常 (尝试 {paste_attempt + 1}): {e}")
                            if paste_attempt == paste_retries - 1:
                                print(f"❌ 粘贴操作失败")
                                return False
                            time.sleep(0.3)

                    # 恢复原剪贴板内容
                    try:
                        if original_clipboard:
                            print("🔄 恢复原剪贴板内容...")
                            pyperclip.copy(original_clipboard)
                            time.sleep(0.1)
                    except Exception as e:
                        print(f"⚠️ 恢复剪贴板内容失败: {e}")

                    print(f"✅ 中文输入完成")
                    return True

                except ImportError:
                    print("❌ 需要安装pyperclip: pip install pyperclip")
                    return False
                except Exception as e:
                    print(f"❌ 中文输入失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False

            def _type_chinese_text_simple(self, text: str) -> bool:
                """简化的中文文本输入"""
                try:
                    import pyperclip
                    import time

                    print(f"📝 简化中文输入: {text[:30]}{'...' if len(text) > 30 else ''}")

                    # 保存原剪贴板内容
                    original_clipboard = ""
                    try:
                        original_clipboard = pyperclip.paste()
                    except:
                        pass

                    # 复制文本到剪贴板
                    pyperclip.copy(text)
                    time.sleep(0.2)

                    # 粘贴
                    self.pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.3)

                    # 恢复原剪贴板内容
                    try:
                        if original_clipboard:
                            pyperclip.copy(original_clipboard)
                    except:
                        pass

                    print(f"✅ 简化中文输入完成")
                    return True

                except Exception as e:
                    print(f"❌ 简化中文输入失败: {e}")
                    return False

            def press_key(self, key) -> bool:
                try:
                    if isinstance(key, list):
                        self.pyautogui.hotkey(*key)
                    else:
                        self.pyautogui.press(key)
                    return True
                except Exception as e:
                    print(f"按键失败: {e}")
                    return False

            def key_combination(self, keys: list) -> bool:
                return self.press_key(keys)

            def click_template(self, image_path: str, confidence: float = 0.7, timeout: int = 5) -> bool:
                try:
                    import time
                    start_time = time.time()
                    while time.time() - start_time < timeout:
                        try:
                            location = self.pyautogui.locateOnScreen(image_path, confidence=confidence)
                            if location:
                                center = self.pyautogui.center(location)
                                self.pyautogui.click(center)
                                return True
                        except Exception:
                            pass
                        time.sleep(0.5)
                    return False
                except Exception as e:
                    print(f"模板点击失败: {e}")
                    return False

        return FallbackUIActions()

    def add_execution_callback(self, callback: Callable):
        """添加执行状态回调"""
        self.execution_callbacks.append(callback)

    def remove_execution_callback(self, callback: Callable):
        """移除执行状态回调"""
        if callback in self.execution_callbacks:
            self.execution_callbacks.remove(callback)

    def notify_execution_status(self, status: str, message: str = "", data: Any = None):
        """通知执行状态变化"""
        for callback in self.execution_callbacks:
            try:
                callback(status, message, data)
            except Exception as e:
                print(f"执行回调失败: {e}")

    # 模板管理
    def get_templates(self, refresh: bool = False) -> List[Dict]:
        """获取模板列表"""
        if refresh or not self.templates_cache:
            result = self.api_client.get_templates()
            if result and 'items' in result:
                self.templates_cache = {t['id']: t for t in result['items']}
            else:
                self.templates_cache = {}

        return list(self.templates_cache.values())

    def get_template(self, template_id: int) -> Optional[Dict]:
        """获取模板详情"""
        if template_id in self.templates_cache:
            return self.templates_cache[template_id]

        template = self.api_client.get_template(template_id)
        if template:
            self.templates_cache[template_id] = template

        return template

    def create_template(self, template_data: Dict) -> Optional[Dict]:
        """创建模板"""
        result = self.api_client.create_template(template_data)
        if result:
            self.templates_cache[result['id']] = result
        return result

    def update_template(self, template_id: int, template_data: Dict) -> Optional[Dict]:
        """更新模板"""
        result = self.api_client.update_template(template_id, template_data)
        if result:
            self.templates_cache[template_id] = result
        return result

    def delete_template(self, template_id: int) -> bool:
        """删除模板"""
        success = self.api_client.delete_template(template_id)
        if success and template_id in self.templates_cache:
            del self.templates_cache[template_id]
        return success

    # 序列管理
    def get_sequences(self, refresh: bool = False) -> List[Dict]:
        """获取序列列表"""
        if refresh or not self.sequences_cache:
            result = self.api_client.get_sequences()
            if result and 'items' in result:
                self.sequences_cache = {s['id']: s for s in result['items']}
            else:
                self.sequences_cache = {}

        return list(self.sequences_cache.values())

    def get_sequence(self, sequence_id: int) -> Optional[Dict]:
        """获取序列详情"""
        if sequence_id in self.sequences_cache:
            return self.sequences_cache[sequence_id]

        sequence = self.api_client.get_sequence(sequence_id)
        if sequence:
            self.sequences_cache[sequence_id] = sequence

        return sequence

    def create_sequence(self, sequence_data: Dict) -> Optional[Dict]:
        """创建序列"""
        result = self.api_client.create_sequence(sequence_data)
        if result:
            self.sequences_cache[result['id']] = result
        return result

    def update_sequence(self, sequence_id: int, sequence_data: Dict) -> Optional[Dict]:
        """更新序列"""
        result = self.api_client.update_sequence(sequence_id, sequence_data)
        if result:
            self.sequences_cache[sequence_id] = result
        return result

    def delete_sequence(self, sequence_id: int) -> bool:
        """删除序列"""
        success = self.api_client.delete_sequence(sequence_id)
        if success and sequence_id in self.sequences_cache:
            del self.sequences_cache[sequence_id]
        return success

    # 执行管理
    def execute_sequence(self, sequence_id: int, parameters: Dict = None) -> bool:
        """执行序列"""
        if self.is_executing:
            self.notify_execution_status("error", "已有序列正在执行中")
            return False

        if not self.local_components_available:
            self.notify_execution_status("error", "本地自动化组件不可用")
            return False

        # 获取序列详情
        sequence = self.get_sequence(sequence_id)
        if not sequence:
            self.notify_execution_status("error", "序列不存在")
            return False

        # 在后台线程中执行
        execution_thread = threading.Thread(
            target=self._execute_sequence_async,
            args=(sequence, parameters or {}),
            daemon=True
        )
        execution_thread.start()

        return True

    def start_automation_with_task_management(self, project_id: int, current_task_id: int, sequence_id: int = None, parameters: Dict = None) -> bool:
        """开始自动化操作并管理任务状态"""
        try:
            print(f"🚀 开始自动化操作，项目ID: {project_id}, 当前任务ID: {current_task_id}")

            # 1. 更新项目中所有任务的状态
            if hasattr(self.api_client, 'start_automation_operation'):
                result = self.api_client.start_automation_operation(project_id, current_task_id)
                if not result or not result.get('success'):
                    self.notify_execution_status("error", "更新任务状态失败")
                    return False
                print("✅ 任务状态更新完成")
            else:
                print("⚠️ API客户端不支持任务状态管理")

            # 2. 如果指定了序列，执行序列
            if sequence_id:
                return self.execute_sequence(sequence_id, parameters)

            return True

        except Exception as e:
            print(f"❌ 开始自动化操作失败: {e}")
            self.notify_execution_status("error", f"开始自动化操作失败: {e}")
            return False

    def _execute_sequence_async(self, sequence: Dict, parameters: Dict):
        """异步执行序列"""
        try:
            self.is_executing = True
            self.current_execution = {
                'sequence_id': sequence['id'],
                'sequence_name': sequence['name'],
                'start_time': time.time(),
                'status': 'running',
                'current_step': 0,
                'total_steps': len(sequence.get('steps', [])),
                'results': []
            }

            self.notify_execution_status("started", f"开始执行序列: {sequence['name']}", self.current_execution)

            # 通知后端开始执行
            api_result = self.api_client.execute_sequence_by_id(sequence['id'], parameters)
            if api_result:
                self.current_execution['execution_id'] = api_result.get('executionId')

            # 执行步骤
            steps = sequence.get('steps', [])
            for i, step in enumerate(steps):
                self.current_execution['current_step'] = i + 1
                self.notify_execution_status("step_started", f"执行步骤 {i+1}: {step.get('description', '')}", step)

                # 执行单个步骤
                step_result = self._execute_step(step, parameters)
                self.current_execution['results'].append(step_result)

                if step_result['success']:
                    self.notify_execution_status("step_completed", f"步骤 {i+1} 完成", step_result)
                else:
                    self.notify_execution_status("step_failed", f"步骤 {i+1} 失败: {step_result.get('error', '')}", step_result)

                    # 如果步骤失败且不允许继续，则停止执行
                    if not step.get('continueOnError', False):
                        break

                # 步骤间延迟
                step_delay = step.get('delayAfter', 0)
                if step_delay > 0:
                    time.sleep(step_delay)

            # 计算执行结果
            successful_steps = sum(1 for r in self.current_execution['results'] if r['success'])
            total_steps = len(self.current_execution['results'])

            if successful_steps == total_steps:
                self.current_execution['status'] = 'completed'
                self.notify_execution_status("completed", f"序列执行完成，成功 {successful_steps}/{total_steps} 步骤", self.current_execution)
            else:
                self.current_execution['status'] = 'failed'
                self.notify_execution_status("failed", f"序列执行失败，成功 {successful_steps}/{total_steps} 步骤", self.current_execution)

        except Exception as e:
            self.current_execution['status'] = 'error'
            self.notify_execution_status("error", f"序列执行错误: {str(e)}", self.current_execution)

        finally:
            self.current_execution['end_time'] = time.time()
            self.current_execution['duration'] = self.current_execution['end_time'] - self.current_execution['start_time']
            self.is_executing = False

    def _execute_step(self, step: Dict, parameters: Dict) -> Dict:
        """执行单个步骤"""
        try:
            action_type = step.get('actionType', 'click')
            step_parameters = step.get('parameters', {})
            # 合并参数 - 执行参数优先级更高
            merged_parameters = {**step_parameters, **parameters}


            # 根据动作类型执行（转换为小写进行比较）
            action_type_lower = action_type.lower()
            if action_type_lower == 'click':
                return self._execute_click_action(step, merged_parameters)
            elif action_type_lower == 'type':
                return self._execute_type_action(step, merged_parameters)
            elif action_type_lower == 'input':  # 添加Input类型支持
                return self._execute_input_action(step, merged_parameters)
            elif action_type_lower == 'wait':
                return self._execute_wait_action(step, merged_parameters)
            elif action_type_lower == 'screenshot':
                return self._execute_screenshot_action(step, merged_parameters)
            elif action_type_lower == 'copilot_chat':
                return self._execute_copilot_action(step, merged_parameters)
            elif action_type_lower == 'condition':
                return self._execute_condition_action(step, merged_parameters)
            elif action_type_lower == 'image_condition':
                return self._execute_image_condition_action(step, merged_parameters)
            elif action_type_lower == 'region_image_condition':
                return self._execute_region_image_condition_action(step, merged_parameters)
            else:
                return {
                    'success': False,
                    'error': f'不支持的动作类型: {action_type} (已转换为小写: {action_type_lower})',
                    'step_id': step.get('id'),
                    'action_type': action_type
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'step_id': step.get('id'),
                'action_type': step.get('actionType')
            }

    def _execute_click_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行点击动作"""
        # 检查UI操作器是否可用
        if self.ui_actions is None:
            print("❌ UI操作器不可用，无法执行点击操作")
            return {
                'success': False,
                'step_id': step.get('id'),
                'action_type': 'click',
                'error': 'UI操作器不可用',
                'parameters': parameters
            }

        template_name = parameters.get('template_name')
        template_id = step.get('templateId')

        if template_name:
            # 使用模板名称点击（从参数中获取）
            # 这种情况下需要根据template_name查找对应的模板
            templates = self.get_templates()
            template = next((t for t in templates if t.get('name') == template_name), None)
            if template:
                local_image_path = self.image_manager.get_template_image_path(template)
                if local_image_path:
                    success = self.ui_actions.click_template(
                        local_image_path,
                        confidence=parameters.get('confidence', 0.7),
                        timeout=parameters.get('timeout', 5)
                    )
                else:
                    success = False
            else:
                success = False
        elif template_id:
            # 使用模板ID点击
            template = self.get_template(template_id)
            if template:
                local_image_path = self.image_manager.get_template_image_path(template)
                if local_image_path:
                    # 动态调整置信度 - 如果是template_2，降低置信度
                    template_confidence = parameters.get('confidence', 0.7)
                    if 'template_2' in local_image_path:
                        template_confidence = 0.45  # 降低到0.45以匹配实际置信度0.461
                        print(f"🔧 为template_2降低置信度到: {template_confidence}")

                    success = self.ui_actions.click_template(
                        local_image_path,
                        confidence=template_confidence,
                        timeout=parameters.get('timeout', 5)
                    )
                else:
                    success = False
            else:
                success = False
        else:
            # 使用坐标点击
            x = parameters.get('x')
            y = parameters.get('y')
            if x is not None and y is not None:
                success = self.ui_actions.click(x, y)
            else:
                # 特殊处理：如果是发送按钮相关的步骤且没有配置
                step_description = step.get('description', '').lower()
                if '发送' in step_description or 'send' in step_description:
                    print("🔧 检测到发送按钮步骤但无模板配置")

                    # 方法1: 尝试查找发送按钮相关的模板
                    templates = self.get_templates()
                    send_template = None

                    # 查找包含"send"、"发送"等关键词的模板
                    print(f"🔍 当前可用模板: {[t.get('name', 'Unknown') for t in templates]}")
                    for template in templates:
                        template_name = template.get('name', '').lower()
                        print(f"🔍 检查模板: {template.get('name')} (小写: {template_name})")
                        if any(keyword in template_name for keyword in ['send', '发送', 'submit', '提交']):
                            send_template = template
                            print(f"✅ 找到发送按钮模板: {template.get('name')} (ID: {template.get('id')})")
                            break

                    if send_template:
                        # 使用找到的发送按钮模板
                        local_image_path = self.image_manager.get_template_image_path(send_template)
                        if local_image_path:
                            print("🖱️ 尝试点击发送按钮模板")

                            # 等待一下确保界面稳定
                            time.sleep(0.5)

                            # 尝试点击发送按钮
                            success = self.ui_actions.click_template(
                                local_image_path,
                                confidence=0.6,  # 使用较低的置信度
                                timeout=5
                            )

                            if success:
                                print("✅ 发送按钮点击完成")
                                # 等待一下确保点击生效
                                time.sleep(1.0)
                                print("✅ 通过模板点击发送成功")
                            else:
                                print("❌ 模板点击发送失败，尝试回车键")
                                success = self.ui_actions.press_key('enter')
                                if success:
                                    print("✅ 通过回车键发送成功")
                                else:
                                    print("❌ 回车键发送也失败")
                        else:
                            print("❌ 无法获取发送按钮模板图片，尝试回车键")
                            success = self.ui_actions.press_key('enter')
                    else:
                        # 方法2: 没找到模板，尝试多种发送方式
                        print("🔧 未找到发送按钮模板，尝试多种发送方式")

                        # 尝试1: 回车键
                        print("🔧 尝试方式1: 回车键")
                        success = self.ui_actions.press_key('enter')

                        if not success:
                            # 尝试2: Ctrl+Enter
                            print("🔧 尝试方式2: Ctrl+Enter")
                            success = self.ui_actions.press_key(['ctrl', 'enter'])

                        if not success:
                            # 尝试3: Shift+Enter
                            print("🔧 尝试方式3: Shift+Enter")
                            success = self.ui_actions.press_key(['shift', 'enter'])

                    if success:
                        print("✅ 发送操作成功")
                    else:
                        print("❌ 所有发送方式都失败了")
                        # 最后尝试：等待一下再试回车键
                        print("🔧 最后尝试：等待后再按回车键")
                        time.sleep(1.0)
                        success = self.ui_actions.press_key('enter')
                        if success:
                            print("✅ 延迟回车键发送成功")
                        else:
                            print("❌ 发送操作彻底失败")
                else:
                    success = False

        # 执行后等待
        wait_after = parameters.get('wait_after', 0)
        if wait_after > 0:
            time.sleep(wait_after)

        return {
            'success': success,
            'step_id': step.get('id'),
            'action_type': 'click',
            'parameters': parameters
        }

    def _execute_type_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行输入动作"""
        # 检查UI操作器是否可用
        if not self._check_ui_actions_available("文本输入"):
            return {
                'success': False,
                'step_id': step.get('id'),
                'action_type': 'type',
                'error': 'UI操作器不可用',
                'parameters': parameters
            }

        text = parameters.get('text', '')
        if text:
            success = self.ui_actions.type_text(text)
        else:
            success = False

        return {
            'success': success,
            'step_id': step.get('id'),
            'action_type': 'type',
            'parameters': parameters
        }

    def _execute_input_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行输入动作（用于序列执行）"""
        try:

            # 尝试多种方式获取templateId
            template_id = step.get('templateId') or step.get('TemplateId') or parameters.get('templateId') or parameters.get('TemplateId')
            print(f"🔍 获取到的templateId: {template_id}")

            success = False

            # 获取输入文本 - 直接从合并后的参数中获取
            input_text = parameters.get('ai_prompt', '') or parameters.get('message', '') or parameters.get('text', '')

            # 获取参考图片URL
            reference_images = parameters.get('reference_images', '') or parameters.get('referenceImages', '')

            print(f"📝 从参数获取的输入文本: {input_text[:50] if input_text else 'None'}{'...' if input_text and len(input_text) > 50 else ''}")
            print(f"🖼️ 参考图片数据: {reference_images[:200] if reference_images else 'None'}{'...' if reference_images and len(reference_images) > 200 else ''}")

            # 解析参考图片URL列表
            image_urls = self.parse_reference_images(reference_images)
            if image_urls:
                print(f"🔍 解析到 {len(image_urls)} 个图片URL: {image_urls}")

            print(f"📝 准备输入文本: {input_text[:50] if input_text else 'None'}{'...' if input_text and len(input_text) > 50 else ''}")

            if not input_text:
                # 如果没有指定输入文本，使用默认的AI Prompt
                default_prompts = {
                    'copilot': '请帮我生成一个Python函数来处理文件上传功能',
                    'chat': '你好，请帮我解决一个编程问题',
                    'search': 'Python最佳实践',
                    'default': '请帮我优化这段代码的性能和可读性'
                }

                # 根据步骤描述选择合适的默认提示词
                description = step.get('description', '').lower()
                if 'copilot' in description or 'chat' in description:
                    input_text = default_prompts['copilot']
                elif 'search' in description or '搜索' in description:
                    input_text = default_prompts['search']
                else:
                    input_text = default_prompts['default']

                print(f"💡 使用默认AI Prompt: {input_text}")

            if not input_text:
                print("❌ 仍然没有输入文本")
                success = False
            else:
                # 初始化success为None，表示还没有进行点击操作
                success = None

                if template_id:
                    # Input操作：检查是否需要点击模板
                    # 如果上一步已经点击了输入框，这里就不需要再点击了
                    print(f"⚠️ Input操作检测到templateId: {template_id}")
                    print(f"💡 Input操作通常不需要点击模板，因为上一步应该已经获得了焦点")
                    print(f"🔧 跳过模板点击，直接进行文本输入")
                    success = None  # 跳过点击，直接输入
                elif parameters.get('x') is not None and parameters.get('y') is not None:
                    # 如果有坐标，点击坐标
                    x, y = parameters.get('x'), parameters.get('y')
                    print(f"🖱️ 点击坐标: ({x}, {y})")
                    click_success = self.ui_actions.click(x, y)
                    if not click_success:
                        print("❌ 坐标点击失败")
                        success = False
                    else:
                        print("✅ 坐标点击成功")
                        time.sleep(0.3)  # 等待焦点切换
                else:
                    # 没有模板ID和坐标，直接输入（假设焦点已经在正确位置）
                    print("💡 没有指定点击位置，假设焦点已在输入框中，直接输入文本")

                # 如果前面的操作成功或者没有点击操作，进行内容输入
                if success != False:  # success可能是None，表示没有进行点击操作

                    # 处理参考图片
                    uploaded_images = []
                    if image_urls:
                        print(f"🖼️ 开始处理 {len(image_urls)} 个参考图片...")

                        for i, image_url in enumerate(image_urls):
                            print(f"📥 处理第 {i+1}/{len(image_urls)} 个图片: {image_url}")
                            local_image_path = self.download_reference_image(image_url)

                            if local_image_path:
                                print(f"📤 上传第 {i+1} 个图片到Copilot...")
                                upload_success = self.upload_image_to_copilot(local_image_path)
                                if upload_success:
                                    uploaded_images.append(local_image_path)
                                    print(f"✅ 第 {i+1} 个图片上传成功")
                                    time.sleep(1.0)  # 等待图片上传完成
                                else:
                                    print(f"❌ 第 {i+1} 个图片上传失败")
                            else:
                                print(f"❌ 第 {i+1} 个图片下载失败")

                        if uploaded_images:
                            print(f"✅ 成功上传 {len(uploaded_images)} 个图片")
                        else:
                            print("❌ 所有图片都上传失败，继续发送文字")

                    # 输入文本内容
                    if input_text:
                        print("⌨️ 开始输入文本...")

                        # 如果有图片，不需要清空内容（图片已经在输入框中）
                        if not uploaded_images:
                            # 清空现有内容
                            print("🧹 清空现有内容...")
                            try:
                                self.ui_actions.press_key(['ctrl', 'a'])
                                time.sleep(0.1)
                                print("✅ 清空操作完成")
                            except Exception as e:
                                print(f"❌ 清空操作失败: {e}")

                        # 输入文本
                        print(f"📝 准备输入文本，长度: {len(input_text)} 字符")
                        try:
                            success = self.ui_actions.type_text(input_text)

                            if success:
                                if uploaded_images:
                                    print(f"✅ {len(uploaded_images)} 个图片和文字输入成功")
                                else:
                                    print("✅ 文本输入成功")
                            else:
                                print("❌ 文本输入失败")
                        except Exception as e:
                            print(f"❌ 文本输入异常: {e}")
                            success = False
                    elif uploaded_images:
                        print(f"✅ 仅 {len(uploaded_images)} 个图片上传成功")
                        success = True
                    else:
                        print("❌ 没有文本内容也没有图片")
                        success = False

            # 执行后等待
            wait_after = parameters.get('wait_after', 0)
            if wait_after > 0:
                time.sleep(wait_after)

            return {
                'success': success,
                'step_id': step.get('id'),
                'action_type': 'input',
                'parameters': parameters
            }

        except Exception as e:
            print(f"❌ Input动作执行异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'step_id': step.get('id'),
                'action_type': 'input',
                'parameters': parameters
            }

    def _execute_wait_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行等待动作"""
        template_name = parameters.get('template_name')
        timeout = parameters.get('timeout', 5)

        if template_name:
            # 等待模板出现
            templates = self.get_templates()
            template = next((t for t in templates if t.get('name') == template_name), None)
            if template:
                local_image_path = self.image_manager.get_template_image_path(template)
                if local_image_path:
                    # 使用screenshot_manager等待模板出现
                    location = self.screenshot_manager.wait_for_template(
                        local_image_path,
                        timeout=timeout,
                        confidence=parameters.get('confidence', 0.7)
                    )
                    success = location is not None
                else:
                    success = False
            else:
                success = False
        else:
            # 简单的时间等待
            duration = parameters.get('duration', timeout)
            time.sleep(duration)
            success = True

        return {
            'success': success,
            'step_id': step.get('id'),
            'action_type': 'wait',
            'parameters': parameters
        }

    def _execute_screenshot_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行截图动作"""
        save_path = parameters.get('save_path', 'screenshot.png')
        success = self.screenshot_manager.take_screenshot(save_path)

        return {
            'success': success,
            'step_id': step.get('id'),
            'action_type': 'screenshot',
            'parameters': parameters,
            'result': {'screenshot_path': save_path} if success else None
        }

    def _execute_copilot_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行Copilot动作"""
        message = parameters.get('message', '')
        if message and self.copilot_core:
            success = self.copilot_core.send_message(message)
        else:
            success = False

        return {
            'success': success,
            'step_id': step.get('id'),
            'action_type': 'copilot_chat',
            'parameters': parameters
        }

    def _execute_condition_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行条件判断动作"""
        try:
            condition_expression = parameters.get('condition_expression', '')
            if not condition_expression:
                return {
                    'success': False,
                    'error': '条件表达式不能为空',
                    'step_id': step.get('id'),
                    'action_type': 'condition'
                }

            # 评估条件表达式
            condition_result = self._evaluate_condition(condition_expression)

            return {
                'success': True,
                'step_id': step.get('id'),
                'action_type': 'condition',
                'parameters': parameters,
                'result': condition_result
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'step_id': step.get('id'),
                'action_type': 'condition'
            }

    def _execute_image_condition_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行图像条件判断动作"""
        try:
            if not self.image_condition_executor:
                return {
                    'success': False,
                    'error': '图像条件执行器不可用',
                    'step_id': step.get('id'),
                    'action_type': 'image_condition'
                }

            # 使用图像条件执行器执行
            result = self.image_condition_executor.execute_image_condition(parameters)

            # 添加步骤信息
            result['step_id'] = step.get('id')
            result['action_type'] = 'image_condition'
            result['parameters'] = parameters

            return result

        except Exception as e:
            print(f"❌ 图像条件判断执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'step_id': step.get('id'),
                'action_type': 'image_condition'
            }

    def _execute_region_image_condition_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行区域图像条件判断动作"""
        try:
            if not self.region_image_condition_executor:
                return {
                    'success': False,
                    'error': '区域图像条件执行器不可用',
                    'step_id': step.get('id'),
                    'action_type': 'region_image_condition'
                }

            # 使用区域图像条件执行器执行
            result = self.region_image_condition_executor.execute_region_image_condition(parameters)

            # 添加步骤信息
            result['step_id'] = step.get('id')
            result['action_type'] = 'region_image_condition'
            result['parameters'] = parameters

            return result

        except Exception as e:
            print(f"❌ 区域图像条件判断执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'step_id': step.get('id'),
                'action_type': 'region_image_condition'
            }

    def _evaluate_condition(self, condition_expression: str) -> bool:
        """评估条件表达式"""
        try:
            print(f"🔍 评估条件表达式: {condition_expression}")

            # 简单的条件表达式评估
            # 支持基本的比较操作和布尔值

            # 处理布尔值
            if condition_expression.lower() in ['true', '1', 'yes']:
                return True
            elif condition_expression.lower() in ['false', '0', 'no']:
                return False

            # 处理等于比较
            if '==' in condition_expression:
                left, right = condition_expression.split('==', 1)
                left = left.strip().strip('"\'')
                right = right.strip().strip('"\'')
                return left == right

            # 处理不等于比较
            if '!=' in condition_expression:
                left, right = condition_expression.split('!=', 1)
                left = left.strip().strip('"\'')
                right = right.strip().strip('"\'')
                return left != right

            # 处理数值比较
            if '>=' in condition_expression:
                left, right = condition_expression.split('>=', 1)
                try:
                    return float(left.strip()) >= float(right.strip())
                except ValueError:
                    return False

            if '<=' in condition_expression:
                left, right = condition_expression.split('<=', 1)
                try:
                    return float(left.strip()) <= float(right.strip())
                except ValueError:
                    return False

            if '>' in condition_expression:
                left, right = condition_expression.split('>', 1)
                try:
                    return float(left.strip()) > float(right.strip())
                except ValueError:
                    return False

            if '<' in condition_expression:
                left, right = condition_expression.split('<', 1)
                try:
                    return float(left.strip()) < float(right.strip())
                except ValueError:
                    return False

            # 默认返回False
            print(f"⚠️ 无法解析条件表达式，返回False: {condition_expression}")
            return False

        except Exception as e:
            print(f"❌ 条件表达式评估失败: {e}")
            return False

    def stop_execution(self):
        """停止当前执行"""
        if self.is_executing:
            self.is_executing = False
            if self.current_execution:
                self.current_execution['status'] = 'stopped'
            self.notify_execution_status("stopped", "执行已停止")

    def get_execution_status(self) -> Optional[Dict]:
        """获取当前执行状态"""
        return self.current_execution

    def wait_for_execution_completion(self, timeout: int = 300) -> bool:
        """等待执行完成"""
        wait_time = 0
        while self.is_executing and wait_time < timeout:
            time.sleep(1)
            wait_time += 1

        return not self.is_executing

    def get_execution_result(self) -> Optional[Dict]:
        """获取执行结果"""
        if self.current_execution:
            return {
                'success': self.current_execution.get('status') == 'completed',
                'status': self.current_execution.get('status'),
                'results': self.current_execution.get('results', []),
                'duration': self.current_execution.get('duration', 0),
                'error_message': self.current_execution.get('error_message')
            }
        return None

    def sync_template_images(self) -> Dict:
        """同步所有模板图片到本地缓存"""
        templates = self.get_templates(refresh=True)
        return self.image_manager.sync_templates(templates)

    def clear_template_cache(self, template_id: Optional[int] = None):
        """清理模板图片缓存"""
        self.image_manager.clear_cache(template_id)

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return self.image_manager.get_cache_info()

    def parse_reference_images(self, reference_images: str) -> List[str]:
        """解析参考图片字符串，返回完整URL列表"""
        try:
            if not reference_images or not reference_images.strip():
                return []

            print(f"🔍 开始解析参考图片: {reference_images}")

            # 尝试解析JSON数组
            try:
                import json
                image_paths = json.loads(reference_images)
                if not isinstance(image_paths, list):
                    image_paths = [image_paths]
            except json.JSONDecodeError:
                # 如果不是JSON，尝试其他格式
                if reference_images.startswith('[') and reference_images.endswith(']'):
                    # 去掉方括号，按逗号分割
                    content = reference_images[1:-1]
                    image_paths = [path.strip().strip('"\'') for path in content.split(',')]
                else:
                    # 单个路径
                    image_paths = [reference_images.strip()]

            # 转换为完整URL
            base_url = self.api_client.base_url.rstrip('/')
            print(f"🔍 API基础URL: {base_url}")
            full_urls = []

            for path in image_paths:
                if not path:
                    continue

                path = path.strip().strip('"\'')

                if path.startswith('http://') or path.startswith('https://'):
                    # 已经是完整URL
                    full_urls.append(path)
                else:
                    # 相对路径，需要构建正确的图片访问URL
                    # 根据前端代码，开发步骤图片访问URL格式为：/api/DevelopmentSteps/image/{relativePath}
                    if path.startswith('uploads/development-steps/') or path.startswith('uploads\\development-steps\\'):
                        # 开发步骤图片，使用专门的API端点
                        path_normalized = path.replace('\\', '/')
                        full_url = f"{base_url}/api/DevelopmentSteps/image/{path_normalized}"
                    elif path.startswith('/'):
                        full_url = f"{base_url}{path}"
                    else:
                        full_url = f"{base_url}/{path}"
                    full_urls.append(full_url)

            print(f"✅ 解析完成，得到 {len(full_urls)} 个URL: {full_urls}")
            return full_urls

        except Exception as e:
            print(f"❌ 解析参考图片失败: {e}")
            return []

    def download_reference_image(self, image_url: str) -> Optional[str]:
        """下载参考图片到本地"""
        try:
            if not image_url or not image_url.strip():
                print("❌ 图片URL为空")
                return None

            print(f"📥 开始下载参考图片: {image_url}")

            # 创建参考图片目录
            ref_images_dir = Path(__file__).parent / "reference_images"
            ref_images_dir.mkdir(exist_ok=True)
            print(f"📁 参考图片目录: {ref_images_dir}")

            # 提取原始文件名
            try:
                # 从URL中提取文件名
                url_path = image_url.split('/')[-1]  # 获取最后一部分
                if '?' in url_path:
                    url_path = url_path.split('?')[0]  # 移除查询参数

                # 如果URL包含开发步骤路径，提取真实文件名
                if 'uploads/development-steps/' in image_url:
                    # 从完整路径中提取文件名
                    # 例如：从 "uploads/development-steps/61/675f57d6-62ba-46cc-839c-2e231cbb7c3b.png" 提取 "675f57d6-62ba-46cc-839c-2e231cbb7c3b.png"
                    path_parts = image_url.split('/')
                    if len(path_parts) >= 2:
                        filename = path_parts[-1]  # 获取最后一部分作为文件名
                    else:
                        filename = url_path
                else:
                    filename = url_path

                # 确保文件名有效
                if not filename or filename == '':
                    file_extension = Path(image_url).suffix or '.png'
                    filename = f"ref_{uuid.uuid4().hex[:8]}{file_extension}"

                # 设置本地保存路径（如果文件已存在则直接覆盖）
                local_path = ref_images_dir / filename
                if local_path.exists():
                    print(f"⚠️ 文件已存在，将覆盖: {local_path}")

            except Exception as e:
                print(f"⚠️ 提取文件名失败，使用默认命名: {e}")
                file_extension = Path(image_url).suffix or '.png'
                filename = f"ref_{uuid.uuid4().hex[:8]}{file_extension}"
                local_path = ref_images_dir / filename

            print(f"💾 本地保存路径: {local_path}")

            # 下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            print(f"🌐 发送HTTP请求到: {image_url}")

            # 对于HTTPS的localhost，禁用SSL验证
            verify_ssl = True
            if 'localhost' in image_url and image_url.startswith('https://'):
                verify_ssl = False
                print("🔓 检测到localhost HTTPS，禁用SSL验证")
                # 抑制SSL警告
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            response = requests.get(image_url, headers=headers, timeout=30, verify=verify_ssl)
            print(f"📊 HTTP响应状态: {response.status_code}")
            print(f"📊 响应头: {dict(response.headers)}")
            print(f"📊 内容长度: {len(response.content)} 字节")

            response.raise_for_status()

            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            print(f"📊 内容类型: {content_type}")

            if not content_type.startswith('image/'):
                print(f"⚠️ 警告：内容类型不是图片: {content_type}")
                print(f"📄 响应内容前100字符: {response.text[:100]}")

            # 保存图片
            with open(local_path, 'wb') as f:
                f.write(response.content)

            # 验证文件是否保存成功
            if local_path.exists():
                file_size = local_path.stat().st_size
                print(f"✅ 图片下载成功: {local_path} (大小: {file_size} 字节)")
                return str(local_path)
            else:
                print(f"❌ 文件保存失败: {local_path}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 下载参考图片失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def find_local_reference_image(self, image_url: str) -> Optional[str]:
        """查找本地已下载的参考图片"""
        try:
            # 从URL中提取原始文件名
            if 'uploads/development-steps/' in image_url:
                path_parts = image_url.split('/')
                if len(path_parts) >= 2:
                    original_filename = path_parts[-1]
                    if '?' in original_filename:
                        original_filename = original_filename.split('?')[0]
                else:
                    return None
            else:
                # 其他URL格式
                original_filename = Path(image_url).name
                if '?' in original_filename:
                    original_filename = original_filename.split('?')[0]

            if not original_filename:
                return None

            # 检查reference_images目录
            ref_images_dir = Path(__file__).parent / "reference_images"
            if not ref_images_dir.exists():
                return None

            # 查找匹配的文件
            exact_match = ref_images_dir / original_filename
            if exact_match.exists():
                print(f"🔍 找到本地图片: {exact_match}")
                return str(exact_match)

            print(f"🔍 本地未找到图片: {original_filename}")
            return None

        except Exception as e:
            print(f"❌ 查找本地图片失败: {e}")
            return None

    def upload_image_to_copilot(self, image_path: str) -> bool:
        """上传图片到Copilot Chat（通过复制粘贴）"""
        try:
            if not os.path.exists(image_path):
                print(f"❌ 图片文件不存在: {image_path}")
                return False

            print(f"📤 开始上传图片到Copilot: {image_path}")

            # 直接使用复制粘贴方式（最可靠的方法）
            if self.copy_paste_image(image_path):
                return True

            print("❌ 图片复制粘贴失败")
            return False

        except Exception as e:
            print(f"❌ 上传图片失败: {e}")
            return False

    def try_drag_drop_upload(self, image_path: str) -> bool:
        """尝试拖拽上传图片"""
        try:
            print("🔧 尝试拖拽上传图片")
            # 这里需要实现拖拽功能，暂时返回False
            # 可以使用pyautogui的拖拽功能
            return False
        except Exception as e:
            print(f"❌ 拖拽上传失败: {e}")
            return False

    def try_click_upload_button(self, image_path: str) -> bool:
        """尝试点击上传按钮上传图片"""
        try:
            print("🔧 尝试点击上传按钮")

            # 查找上传按钮模板
            templates = self.get_templates()
            upload_template = None

            for template in templates:
                template_name = template.get('name', '').lower()
                if any(keyword in template_name for keyword in ['upload', '上传', 'attach', '附件', 'file', '文件']):
                    upload_template = template
                    print(f"🔍 找到上传按钮模板: {template.get('name')}")
                    break

            if upload_template:
                local_image_path = self.image_manager.get_template_image_path(upload_template)
                if local_image_path:
                    # 点击上传按钮
                    if self.ui_actions.click_template(local_image_path, confidence=0.6, timeout=5):
                        time.sleep(1.0)

                        # 在文件对话框中输入文件路径
                        self.ui_actions.type_text(image_path)
                        time.sleep(0.5)

                        # 按回车确认
                        self.ui_actions.press_key('enter')
                        time.sleep(1.0)

                        print("✅ 点击上传按钮成功")
                        return True

            return False

        except Exception as e:
            print(f"❌ 点击上传按钮失败: {e}")
            return False

    def copy_paste_image(self, image_path: str) -> bool:
        """复制图片到剪贴板并粘贴到输入框"""
        try:
            print("📋 开始复制图片到剪贴板...")

            # 方法1: 使用PIL和win32clipboard (Windows)
            if self.copy_image_to_clipboard_pil(image_path):
                print("✅ 图片已复制到剪贴板")

                # 等待一下确保复制完成
                time.sleep(0.5)

                # 验证剪贴板内容
                if self.verify_clipboard_has_image():
                    print("✅ 剪贴板确认包含图片数据")
                else:
                    print("⚠️ 剪贴板可能没有图片数据")

                # 粘贴到输入框
                print("📋 粘贴图片到当前焦点位置...")

                # 使用多种方法尝试粘贴
                success = False

                # 方法1: 使用UI操作器
                if hasattr(self, 'ui_actions') and self.ui_actions:
                    success = self.ui_actions.press_key(['ctrl', 'v'])
                    if success:
                        print("✅ 通过UI操作器粘贴成功")

                # 方法2: 使用pyautogui作为备选
                if not success:
                    try:
                        import pyautogui
                        pyautogui.hotkey('ctrl', 'v')
                        success = True
                        print("✅ 通过pyautogui粘贴成功")
                    except Exception as e:
                        print(f"⚠️ pyautogui粘贴失败: {e}")

                if success:
                    print("✅ 图片粘贴操作完成")
                    time.sleep(1.5)  # 等待图片加载和处理
                    return True
                else:
                    print("❌ 所有粘贴方法都失败")
                    return False
            else:
                print("❌ 图片复制到剪贴板失败")
                return False

        except Exception as e:
            print(f"❌ 复制粘贴图片失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def verify_clipboard_has_image(self) -> bool:
        """验证剪贴板是否包含图片数据"""
        try:
            import win32clipboard

            win32clipboard.OpenClipboard()
            try:
                # 检查是否有图片格式的数据
                has_dib = win32clipboard.IsClipboardFormatAvailable(win32clipboard.CF_DIB)
                has_bitmap = win32clipboard.IsClipboardFormatAvailable(win32clipboard.CF_BITMAP)

                return has_dib or has_bitmap
            finally:
                win32clipboard.CloseClipboard()

        except Exception as e:
            print(f"⚠️ 验证剪贴板失败: {e}")
            return False

    def copy_image_to_clipboard_pil(self, image_path: str) -> bool:
        """使用PIL将图片复制到剪贴板"""
        try:
            from PIL import Image
            import io
            import win32clipboard

            print(f"🖼️ 加载图片: {image_path}")

            # 打开图片
            image = Image.open(image_path)

            # 转换为RGB格式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 将图片转换为字节流
            output = io.BytesIO()
            image.save(output, format='BMP')
            data = output.getvalue()[14:]  # 去掉BMP文件头

            # 复制到剪贴板
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
            win32clipboard.CloseClipboard()

            print("✅ 图片已复制到Windows剪贴板")
            return True

        except ImportError as e:
            print(f"❌ 缺少必要的库: {e}")
            print("💡 请安装: pip install Pillow pywin32")
            return False
        except Exception as e:
            print(f"❌ 复制图片到剪贴板失败: {e}")
            return False

    def execute_template_step(self, step: Dict, template_info: Dict, ai_prompt: str = None) -> bool:
        """执行带模板的步骤"""
        try:
            if not self.local_components_available:
                print("本地自动化组件不可用")
                return False

            action_type = step.get('actionType', 'click')
            template_name = template_info.get('name', '未知模板')

            print(f"执行模板步骤: {action_type} - {template_name}")
            if ai_prompt:
                print(f"AI任务要求: {ai_prompt}")

            # 获取模板图片路径
            local_image_path = self.image_manager.get_template_image_path(template_info)
            if not local_image_path:
                print(f"无法获取模板图片: {template_name}")
                return False

            # 记录执行历史
            self._record_execution_history(step, template_info, ai_prompt)

            # 根据动作类型执行（转换为小写进行比较）
            action_type_lower = action_type.lower()
            if action_type_lower == 'click':
                return self._execute_template_click(step, template_info, local_image_path, ai_prompt)
            elif action_type_lower == 'wait':
                return self._execute_template_wait(step, template_info, local_image_path, ai_prompt)
            elif action_type_lower == 'type':
                return self._execute_template_type(step, template_info, local_image_path, ai_prompt)
            elif action_type_lower == 'input':  # 支持input/Input类型
                return self._execute_template_input(step, template_info, local_image_path, ai_prompt)
            elif action_type_lower == 'verify':
                return self._execute_template_verify(step, template_info, local_image_path, ai_prompt)
            else:
                print(f"不支持的模板动作类型: {action_type} (已转换为小写: {action_type_lower})")
                return False

        except Exception as e:
            print(f"执行模板步骤失败: {e}")
            return False

    def execute_step(self, step: Dict, ai_prompt: str = None) -> bool:
        """执行单个步骤（无模板）"""
        try:
            if not self.local_components_available:
                print("本地自动化组件不可用")
                return False

            action_type = step.get('actionType', 'click')
            description = step.get('description', '无描述')

            print(f"执行步骤: {action_type} - {description}")
            if ai_prompt:
                print(f"AI任务要求: {ai_prompt}")

            # 记录执行历史
            self._record_execution_history(step, None, ai_prompt)

            # 获取步骤参数
            parameters = step.get('parameters', {})
            if isinstance(parameters, str):
                try:
                    import json
                    parameters = json.loads(parameters)
                except:
                    parameters = {}

            # 根据动作类型执行（转换为小写进行比较）
            action_type_lower = action_type.lower()
            if action_type_lower == 'click':
                return self._execute_coordinate_click(step, parameters)
            elif action_type_lower == 'type':
                return self._execute_text_input(step, parameters)
            elif action_type_lower == 'input':  # 支持input/Input类型
                return self._execute_ai_input(step, parameters, ai_prompt)
            elif action_type_lower == 'wait':
                return self._execute_simple_wait(step, parameters)
            elif action_type_lower == 'key':
                return self._execute_key_press(step, parameters)
            elif action_type_lower == 'screenshot':
                return self._execute_simple_screenshot(step, parameters)
            elif action_type_lower == 'pasteimages':
                result = self._execute_paste_images_action(step, parameters)
                return result.get('success', False)
            elif action_type_lower == 'confirmdialog':
                result = self._execute_confirm_dialog_action(step, parameters)
                return result.get('success', False)
            elif action_type_lower == 'userconfirm':
                result = self._execute_user_confirm_action(step, parameters)
                return result.get('success', False)
            else:
                print(f"不支持的步骤动作类型: {action_type} (已转换为小写: {action_type_lower})")
                return False

        except Exception as e:
            print(f"执行步骤失败: {e}")
            return False

    def _record_execution_history(self, step: Dict, template_info: Dict = None, ai_prompt: str = None):
        """记录执行历史"""
        try:
            import datetime

            history_record = {
                'timestamp': datetime.datetime.now().isoformat(),
                'step_id': step.get('id'),
                'action_type': step.get('actionType'),
                'description': step.get('description'),
                'ai_prompt': ai_prompt,
                'template_id': template_info.get('id') if template_info else None,
                'template_name': template_info.get('name') if template_info else None
            }

            print(f"📝 记录执行历史: {history_record}")

            # 这里可以调用API保存到数据库
            # self.api_client.save_execution_history(history_record)

        except Exception as e:
            print(f"记录执行历史失败: {e}")

    def _execute_template_click(self, step: Dict, template_info: Dict, image_path: str, ai_prompt: str = None) -> bool:
        """执行模板点击"""
        try:
            # 检查UI操作器是否可用
            if not self._check_ui_actions_available("模板点击"):
                return False

            confidence = template_info.get('confidence', 0.7)
            timeout = step.get('timeoutSeconds', 5)

            success = self.ui_actions.click_template(
                image_path,
                confidence=confidence,
                timeout=timeout
            )

            if success:
                print(f"✅ 模板点击成功: {template_info.get('name')}")
            else:
                print(f"❌ 模板点击失败: {template_info.get('name')}")

            return success

        except Exception as e:
            print(f"模板点击执行失败: {e}")
            return False

    def _execute_template_wait(self, step: Dict, template_info: Dict, image_path: str, ai_prompt: str = None) -> bool:
        """执行模板等待"""
        try:
            confidence = template_info.get('confidence', 0.7)
            timeout = step.get('timeoutSeconds', 5)

            location = self.screenshot_manager.wait_for_template(
                image_path,
                timeout=timeout,
                confidence=confidence
            )

            success = location is not None

            if success:
                print(f"✅ 模板等待成功: {template_info.get('name')}")
            else:
                print(f"❌ 模板等待超时: {template_info.get('name')}")

            return success

        except Exception as e:
            print(f"模板等待执行失败: {e}")
            return False

    def _execute_template_type(self, step: Dict, template_info: Dict, image_path: str, ai_prompt: str = None) -> bool:
        """执行模板输入"""
        try:
            # 先点击模板位置
            confidence = template_info.get('confidence', 0.7)
            timeout = step.get('timeoutSeconds', 5)

            click_success = self.ui_actions.click_template(
                image_path,
                confidence=confidence,
                timeout=timeout
            )

            if not click_success:
                print(f"❌ 无法点击模板: {template_info.get('name')}")
                return False

            # 获取要输入的文本
            parameters = step.get('parameters', {})
            if isinstance(parameters, str):
                try:
                    import json
                    parameters = json.loads(parameters)
                except:
                    parameters = {}

            text = parameters.get('text', '')
            if not text:
                print("❌ 没有指定要输入的文本")
                return False

            # 输入文本
            success = self.ui_actions.type_text(text)

            if success:
                print(f"✅ 模板输入成功: {template_info.get('name')} - {text}")
            else:
                print(f"❌ 模板输入失败: {template_info.get('name')}")

            return success

        except Exception as e:
            print(f"模板输入执行失败: {e}")
            return False

    def _execute_template_input(self, step: Dict, template_info: Dict, image_path: str, ai_prompt: str = None) -> bool:
        """执行模板输入（使用AI Prompt作为输入内容）"""
        try:
            print(f"🔍 开始执行模板输入操作...")
            print(f"   模板名称: {template_info.get('name')}")
            print(f"   图片路径: {image_path}")
            print(f"   AI Prompt: {ai_prompt[:50] if ai_prompt else 'None'}{'...' if ai_prompt and len(ai_prompt) > 50 else ''}")

            # 先点击模板位置
            confidence = template_info.get('confidence', 0.7)
            timeout = step.get('timeoutSeconds', 5)

            print(f"🖱️ 尝试点击模板，置信度: {confidence}, 超时: {timeout}秒")

            click_success = self.ui_actions.click_template(
                image_path,
                confidence=confidence,
                timeout=timeout
            )

            if not click_success:
                print(f"❌ 无法点击模板: {template_info.get('name')}")
                return False

            print(f"✅ 模板点击成功，准备输入内容...")

            # 使用AI Prompt作为输入内容
            input_text = ai_prompt if ai_prompt else ""

            # 如果没有AI Prompt，尝试从步骤参数获取
            if not input_text:
                parameters = step.get('parameters', {})
                if isinstance(parameters, str):
                    try:
                        import json
                        parameters = json.loads(parameters)
                    except:
                        parameters = {}
                input_text = parameters.get('text', '')
                print(f"📋 从参数获取输入文本: {input_text[:50] if input_text else 'None'}{'...' if input_text and len(input_text) > 50 else ''}")

            if not input_text:
                print("❌ 没有指定要输入的内容（AI Prompt或参数文本）")
                return False

            print(f"⌨️ 准备输入文本，长度: {len(input_text)} 字符")

            # 等待一下确保输入框获得焦点
            time.sleep(0.3)

            # 清空现有内容（Ctrl+A然后删除）
            print("🧹 清空现有内容...")
            self.ui_actions.key_combination(['ctrl', 'a'])
            time.sleep(0.1)

            # 输入AI Prompt内容
            print("📝 开始输入文本...")
            success = self.ui_actions.type_text(input_text)

            if success:
                print(f"✅ 模板输入成功: {template_info.get('name')} - AI Prompt已输入")
                print(f"📝 输入内容: {input_text[:100]}{'...' if len(input_text) > 100 else ''}")
            else:
                print(f"❌ 模板输入失败: {template_info.get('name')}")

            return success

        except Exception as e:
            print(f"模板输入执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _execute_template_verify(self, step: Dict, template_info: Dict, image_path: str, ai_prompt: str = None) -> bool:
        """执行模板验证"""
        try:
            confidence = template_info.get('confidence', 0.7)

            location = self.screenshot_manager.find_template_on_screen(
                image_path,
                confidence=confidence
            )

            success = location is not None

            if success:
                print(f"✅ 模板验证成功: {template_info.get('name')}")
            else:
                print(f"❌ 模板验证失败: {template_info.get('name')}")

            return success

        except Exception as e:
            print(f"模板验证执行失败: {e}")
            return False

    def _execute_coordinate_click(self, step: Dict, parameters: Dict) -> bool:
        """执行坐标点击"""
        try:
            x = parameters.get('x')
            y = parameters.get('y')

            if x is None or y is None:
                print("❌ 缺少点击坐标")
                return False

            success = self.ui_actions.click(int(x), int(y))

            if success:
                print(f"✅ 坐标点击成功: ({x}, {y})")
            else:
                print(f"❌ 坐标点击失败: ({x}, {y})")

            return success

        except Exception as e:
            print(f"坐标点击执行失败: {e}")
            return False

    def _execute_text_input(self, step: Dict, parameters: Dict) -> bool:
        """执行文本输入"""
        try:
            text = parameters.get('text', '')
            if not text:
                print("❌ 没有指定要输入的文本")
                return False

            success = self.ui_actions.type_text(text)

            if success:
                print(f"✅ 文本输入成功: {text}")
            else:
                print(f"❌ 文本输入失败: {text}")

            return success

        except Exception as e:
            print(f"文本输入执行失败: {e}")
            return False

    def _execute_ai_input(self, step: Dict, parameters: Dict, ai_prompt: str = None) -> bool:
        """执行AI输入（使用AI Prompt作为输入内容）"""
        try:
            # 优先使用AI Prompt作为输入内容
            input_text = ai_prompt if ai_prompt else ""

            # 如果没有AI Prompt，尝试从参数获取
            if not input_text:
                input_text = parameters.get('text', '')

            if not input_text:
                print("❌ 没有指定要输入的内容（AI Prompt或参数文本）")
                return False

            # 如果有指定坐标，先点击
            x = parameters.get('x')
            y = parameters.get('y')
            if x is not None and y is not None:
                click_success = self.ui_actions.click(int(x), int(y))
                if not click_success:
                    print(f"❌ 无法点击坐标: ({x}, {y})")
                    return False
                time.sleep(0.2)  # 等待点击生效

            # 清空现有内容（Ctrl+A然后删除）
            self.ui_actions.key_combination(['ctrl', 'a'])
            time.sleep(0.1)

            # 输入AI Prompt内容
            success = self.ui_actions.type_text(input_text)

            if success:
                print(f"✅ AI输入成功 - AI Prompt已输入")
                print(f"📝 输入内容: {input_text[:100]}{'...' if len(input_text) > 100 else ''}")
            else:
                print(f"❌ AI输入失败")

            return success

        except Exception as e:
            print(f"AI输入执行失败: {e}")
            return False

    def _execute_simple_wait(self, step: Dict, parameters: Dict) -> bool:
        """执行简单等待"""
        try:
            duration = parameters.get('duration', step.get('timeoutSeconds', 1))

            print(f"⏱️ 等待 {duration} 秒...")
            time.sleep(float(duration))
            print(f"✅ 等待完成")

            return True

        except Exception as e:
            print(f"等待执行失败: {e}")
            return False

    def _execute_key_press(self, step: Dict, parameters: Dict) -> bool:
        """执行按键操作"""
        try:
            key = parameters.get('key', '')
            if not key:
                print("❌ 没有指定要按的键")
                return False

            success = self.ui_actions.press_key(key)

            if success:
                print(f"✅ 按键成功: {key}")
            else:
                print(f"❌ 按键失败: {key}")

            return success

        except Exception as e:
            print(f"按键执行失败: {e}")
            return False

    def _execute_simple_screenshot(self, step: Dict, parameters: Dict) -> bool:
        """执行简单截图"""
        try:
            save_path = parameters.get('save_path', f'screenshot_{int(time.time())}.png')

            success = self.screenshot_manager.take_screenshot(save_path)

            if success:
                print(f"✅ 截图成功: {save_path}")
            else:
                print(f"❌ 截图失败: {save_path}")

            return success

        except Exception as e:
            print(f"截图执行失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        self.stop_execution()
        self.execution_callbacks.clear()
        self.templates_cache.clear()
        self.sequences_cache.clear()

        # 清理DOM自动化连接
        if hasattr(self, 'dom_integration') and self.dom_integration:
            self.dom_integration.disconnect()

    # DOM自动化相关方法
    def connect_to_copilot(self, debug_port: int = None) -> bool:
        """连接到VSCode Copilot"""
        if not self.dom_integration:
            print("❌ DOM自动化集成不可用")
            return False

        return self.dom_integration.connect(debug_port)

    def send_message_to_copilot(self, message: str) -> Dict:
        """发送消息到Copilot"""
        if not self.dom_integration:
            return {"success": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.send_message(message)

    def get_copilot_status(self) -> Dict:
        """获取Copilot状态"""
        if not self.dom_integration:
            return {"connected": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.check_copilot_status()

        # 清理DOM自动化连接
        if hasattr(self, 'dom_integration') and self.dom_integration:
            self.dom_integration.disconnect()

    # DOM自动化相关方法
    def connect_to_copilot(self, debug_port: int = None) -> bool:
        """连接到VSCode Copilot"""
        if not self.dom_integration:
            print("❌ DOM自动化集成不可用")
            return False

        return self.dom_integration.connect(debug_port)

    def disconnect_from_copilot(self):
        """断开Copilot连接"""
        if self.dom_integration:
            self.dom_integration.disconnect()

    def send_message_to_copilot(self, message: str) -> Dict:
        """发送消息到Copilot"""
        if not self.dom_integration:
            return {"success": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.send_message(message)

    def wait_for_copilot_response(self, timeout: int = None) -> Dict:
        """等待Copilot回复"""
        if not self.dom_integration:
            return {"success": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.wait_for_response(timeout)

    def send_and_wait_copilot(self, message: str, timeout: int = None) -> Dict:
        """发送消息并等待Copilot回复"""
        if not self.dom_integration:
            return {"success": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.send_and_wait(message, timeout)

    def get_copilot_chat_history(self) -> List[Dict]:
        """获取Copilot聊天历史"""
        if not self.dom_integration:
            return []

        return self.dom_integration.get_chat_history()

    def get_copilot_status(self) -> Dict:
        """获取Copilot状态"""
        if not self.dom_integration:
            return {"connected": False, "error": "DOM自动化集成不可用"}

        return self.dom_integration.check_copilot_status()

    def is_copilot_connected(self) -> bool:
        """检查是否连接到Copilot"""
        if not self.dom_integration:
            return False

        return self.dom_integration.is_connected

    def _execute_paste_images_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行图片粘贴动作"""
        try:
            reference_images = parameters.get('reference_images', [])

            if not reference_images:
                print("⚠️ 没有提供参考图片")
                return {'success': True, 'message': '没有参考图片需要粘贴'}

            print(f"🖼️ 开始粘贴 {len(reference_images)} 个参考图片...")

            uploaded_count = 0
            for i, image_ref in enumerate(reference_images):
                print(f"📤 处理第 {i+1}/{len(reference_images)} 个图片: {image_ref}")

                local_image_path = None

                # 优先查找本地已下载的图片
                if image_ref.startswith('http'):
                    # 先检查是否已经下载过
                    local_image_path = self.find_local_reference_image(image_ref)

                    if not local_image_path:
                        # 如果没有找到本地图片，则下载
                        print(f"  📥 本地未找到，开始下载...")
                        local_image_path = self.download_reference_image(image_ref)
                    else:
                        print(f"  ✅ 使用本地已下载的图片: {local_image_path}")
                else:
                    # 假设是本地路径
                    local_image_path = image_ref

                if local_image_path and os.path.exists(local_image_path):
                    upload_success = self.upload_image_to_copilot(local_image_path)
                    if upload_success:
                        uploaded_count += 1
                        print(f"✅ 第 {i+1} 个图片粘贴成功")
                        time.sleep(1.0)  # 等待图片上传完成
                    else:
                        print(f"❌ 第 {i+1} 个图片粘贴失败")
                else:
                    print(f"❌ 第 {i+1} 个图片文件不存在: {image_ref}")

            if uploaded_count > 0:
                return {'success': True, 'message': f'成功粘贴 {uploaded_count} 个图片'}
            else:
                return {'success': False, 'message': '所有图片粘贴都失败'}

        except Exception as e:
            print(f"❌ 图片粘贴失败: {e}")
            return {'success': False, 'message': f'图片粘贴失败: {str(e)}'}

    def _show_topmost_messagebox(self, message: str, title: str, flags: int) -> int:
        """显示置顶的消息框，确保在所有窗口前面"""
        try:
            import ctypes
            from ctypes import wintypes
            import time

            # 先尝试获取Python主窗口句柄
            python_hwnd = None
            try:
                # 查找Python窗口
                def enum_windows_proc(hwnd, lParam):
                    if ctypes.windll.user32.IsWindowVisible(hwnd):
                        length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            ctypes.windll.user32.GetWindowTextW(hwnd, buffer, length + 1)
                            window_title = buffer.value
                            if 'python' in window_title.lower() or 'main.py' in window_title.lower():
                                lParam.contents = ctypes.c_void_p(hwnd)
                                return False
                    return True

                result_hwnd = ctypes.c_void_p()
                EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.POINTER(ctypes.c_void_p))
                ctypes.windll.user32.EnumWindows(EnumWindowsProc(enum_windows_proc), ctypes.byref(result_hwnd))
                python_hwnd = result_hwnd.value
            except:
                pass

            # 如果找不到Python窗口，使用桌面窗口
            if not python_hwnd:
                python_hwnd = ctypes.windll.user32.GetDesktopWindow()

            # 添加置顶和前台标志
            MB_TOPMOST = 0x40000
            MB_SETFOREGROUND = 0x10000
            MB_SYSTEMMODAL = 0x1000  # 系统模态

            final_flags = flags | MB_TOPMOST | MB_SETFOREGROUND | MB_SYSTEMMODAL

            # 显示消息框
            result = ctypes.windll.user32.MessageBoxW(
                python_hwnd,
                message,
                title,
                final_flags
            )

            return result

        except Exception as e:
            print(f"⚠️ 显示置顶消息框失败，使用普通消息框: {e}")
            # 回退到普通消息框
            import ctypes
            return ctypes.windll.user32.MessageBoxW(0, message, title, flags)

    def _execute_confirm_dialog_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行确认对话框动作"""
        try:
            message = parameters.get('message', '确认要继续执行吗？')
            title = parameters.get('title', '确认操作')

            print(f"💬 显示确认对话框: {title}")
            print(f"   消息: {message}")

            # 使用Windows消息框
            import ctypes
            from ctypes import wintypes

            # MessageBox类型常量
            MB_YESNO = 0x4
            MB_ICONQUESTION = 0x20
            MB_DEFBUTTON2 = 0x100  # 默认选择"否"
            MB_TOPMOST = 0x40000   # 置顶显示
            MB_SETFOREGROUND = 0x10000  # 设置为前台窗口

            # 强制显示消息框在最前面
            result = self._show_topmost_messagebox(
                message,
                title,
                MB_YESNO | MB_ICONQUESTION | MB_DEFBUTTON2
            )

            # 返回值：6=Yes, 7=No
            user_confirmed = (result == 6)

            if user_confirmed:
                print("✅ 用户确认继续")
                return {'success': True, 'message': '用户确认继续', 'user_confirmed': True}
            else:
                print("❌ 用户取消操作")
                return {'success': False, 'message': '用户取消操作', 'user_confirmed': False}

        except Exception as e:
            print(f"❌ 确认对话框失败: {e}")
            return {'success': False, 'message': f'确认对话框失败: {str(e)}'}

    def _execute_user_confirm_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行用户确认动作（返回布尔值用于条件判断）"""
        try:
            message = parameters.get('message', '是否继续执行下一步？')

            print(f"💬 用户确认: {message}")

            # 使用Windows消息框
            import ctypes
            from ctypes import wintypes

            # MessageBox类型常量
            MB_YESNO = 0x4
            MB_ICONQUESTION = 0x20
            MB_DEFBUTTON2 = 0x100  # 默认选择"否"
            MB_TOPMOST = 0x40000   # 置顶显示
            MB_SETFOREGROUND = 0x10000  # 设置为前台窗口

            # 强制显示消息框在最前面
            result = self._show_topmost_messagebox(
                message,
                "用户确认",
                MB_YESNO | MB_ICONQUESTION | MB_DEFBUTTON2
            )

            # 返回值：6=Yes, 7=No
            user_confirmed = (result == 6)

            if user_confirmed:
                print("✅ 用户确认")
                return {'success': True, 'message': '用户确认', 'result': True}
            else:
                print("❌ 用户拒绝")
                return {'success': True, 'message': '用户拒绝', 'result': False}

        except Exception as e:
            print(f"❌ 用户确认失败: {e}")
            return {'success': False, 'message': f'用户确认失败: {str(e)}', 'result': False}

    def _execute_image_exists_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行图像存在检查动作"""
        try:
            template_name = parameters.get('templateName') or parameters.get('template_name')
            confidence = parameters.get('confidence', 0.8)

            if not template_name:
                return {'success': False, 'exists': False, 'message': '模板名称为空'}

            print(f"🔍 检查图像是否存在: {template_name}")

            # 构建模板路径
            template_path = f"templates/{template_name}.png"

            # 使用截图管理器查找模板
            location = self.screenshot_manager.find_template_on_screen(template_path, confidence)
            exists = location is not None

            print(f"🔍 图像 {template_name} {'存在' if exists else '不存在'}")

            return {
                'success': True,
                'exists': exists,
                'location': location,
                'template_name': template_name,
                'confidence': confidence
            }

        except Exception as e:
            print(f"❌ 图像存在检查失败: {e}")
            return {
                'success': False,
                'exists': False,
                'error': str(e),
                'message': f'图像存在检查失败: {str(e)}'
            }

    def _execute_input_text_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行输入文本动作"""
        try:
            text = parameters.get('text', '')

            if not text:
                return {'success': False, 'message': '输入文本为空'}

            print(f"📝 输入文本: {text}")

            # 使用UI操作器输入文本
            success = self.ui_actions.type_text(text)

            if success:
                print(f"✅ 文本输入成功")
            else:
                print(f"❌ 文本输入失败")

            return {
                'success': success,
                'message': '文本输入成功' if success else '文本输入失败',
                'text': text
            }

        except Exception as e:
            print(f"❌ 输入文本失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'输入文本失败: {str(e)}'
            }

    def _execute_input_text_action_enhanced(self, step: Dict, parameters: Dict) -> Dict:
        """增强的输入文本动作 - 包含重试和焦点管理"""
        try:
            text = parameters.get('text', '')
            ensure_focus = parameters.get('ensure_focus', True)
            retry_count = parameters.get('retry_count', 2)

            if not text:
                return {'success': False, 'message': '输入文本为空'}

            print(f"📝 增强输入文本: {text[:50]}{'...' if len(text) > 50 else ''}")

            # 检查UI操作器是否可用
            if not self._check_ui_actions_available("增强文本输入"):
                return {'success': False, 'message': 'UI操作器不可用'}

            success = False
            last_error = None

            for attempt in range(retry_count + 1):
                try:
                    if attempt > 0:
                        print(f"🔄 第 {attempt + 1} 次尝试输入文本...")
                        import time
                        time.sleep(0.5)  # 等待一下再重试

                    # 确保输入框有焦点
                    if ensure_focus:
                        print("🎯 确保输入框获得焦点...")
                        # 点击当前鼠标位置来确保焦点
                        try:
                            import pyautogui
                            current_pos = pyautogui.position()
                            self.ui_actions.click(current_pos.x, current_pos.y)
                            import time
                            time.sleep(0.3)
                        except Exception as e:
                            print(f"⚠️ 焦点设置失败: {e}")

                    # 尝试输入文本
                    success = self.ui_actions.type_text(text)

                    if success:
                        print(f"✅ 第 {attempt + 1} 次尝试成功")
                        break
                    else:
                        print(f"❌ 第 {attempt + 1} 次尝试失败")

                except Exception as e:
                    last_error = e
                    print(f"❌ 第 {attempt + 1} 次尝试异常: {e}")

            if success:
                print(f"✅ 增强文本输入成功")
            else:
                print(f"❌ 所有尝试都失败了")

            return {
                'success': success,
                'message': '增强文本输入成功' if success else f'增强文本输入失败: {last_error}',
                'text': text,
                'attempts': retry_count + 1
            }

        except Exception as e:
            print(f"❌ 增强输入文本失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'增强输入文本失败: {str(e)}'
            }

    def _execute_clear_input_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行清空输入框动作"""
        try:
            print("🧹 清空输入框")

            # 检查UI操作器是否可用
            if not self._check_ui_actions_available("清空输入框"):
                return {'success': False, 'message': 'UI操作器不可用'}

            # 使用Ctrl+A选择全部，然后删除
            try:
                # 方法1: Ctrl+A + Delete
                self.ui_actions.press_key(['ctrl', 'a'])
                import time
                time.sleep(0.1)
                self.ui_actions.press_key('delete')
                time.sleep(0.1)

                print("✅ 输入框清空成功 (Ctrl+A + Delete)")
                success = True

            except Exception as e:
                print(f"⚠️ 方法1失败，尝试方法2: {e}")
                try:
                    # 方法2: Ctrl+A + Backspace
                    self.ui_actions.press_key(['ctrl', 'a'])
                    import time
                    time.sleep(0.1)
                    self.ui_actions.press_key('backspace')
                    time.sleep(0.1)

                    print("✅ 输入框清空成功 (Ctrl+A + Backspace)")
                    success = True

                except Exception as e2:
                    print(f"❌ 所有清空方法都失败: {e2}")
                    success = False

            return {
                'success': success,
                'message': '输入框清空成功' if success else '输入框清空失败'
            }

        except Exception as e:
            print(f"❌ 清空输入框失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'清空输入框失败: {str(e)}'
            }
