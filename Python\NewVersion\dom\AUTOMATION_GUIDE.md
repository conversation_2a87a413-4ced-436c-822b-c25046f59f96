# 自动化操作框架使用指南

这是一个完整的HTML自动化操作框架，包含通用的HTML自动化和专门的VSCode自动化功能。

## 📁 文件结构

```
test/
├── automation_framework.py      # 通用HTML自动化框架
├── vscode_automation.py         # VSCode专用自动化类
├── automation_examples.py       # 通用自动化使用示例
├── vscode_automation_examples.py # VSCode自动化使用示例
├── demo.html                    # HTML演示页面
├── start_vscode_debug.bat       # VSCode调试模式启动脚本
└── AUTOMATION_GUIDE.md          # 本使用指南
```

## 🚀 快速开始

### 环境准备

1. **安装Python依赖**:
   ```bash
   # 基础依赖
   pip install selenium webdriver-manager requests

   # 剪贴板操作依赖（用于复制粘贴功能）
   pip install pyperclip
   ```

2. **安装Chrome浏览器** (最新版本)

3. **对于VSCode自动化，还需要**:
   - VSCode已安装
   - Copilot插件已安装并登录

4. **功能依赖说明**:
   - **基础自动化**: selenium, webdriver-manager
   - **复制粘贴功能**: pyperclip
   - **文件操作**: 内置模块（pathlib, subprocess）
   - **图片操作**: Windows系统支持（使用PowerShell）

## 📚 AutomationFramework 通用自动化框架

### 基础使用

```python
from automation_framework import AutomationFramework

# 创建自动化实例
automation = AutomationFramework(headless=False, window_size=(1200, 800))

# 启动浏览器
automation.start()

# 打开网页
automation.open_url("https://example.com")
# 或打开本地HTML文件
automation.open_file("demo.html")

# 进行自动化操作...

# 关闭浏览器
automation.stop()
```

### 使用上下文管理器（推荐）

```python
from automation_framework import AutomationFramework

automation = AutomationFramework()

# 自动管理浏览器生命周期
with automation.browser_session():
    automation.open_file("demo.html")
    
    # 填写表单
    automation.input_text("username", "测试用户")
    automation.input_text("email", "<EMAIL>")
    automation.select_option("gender", value="male")
    
    # 点击按钮
    automation.click("submit-button")
    
    # 获取文本
    result = automation.get_text("result-message")
    print(f"结果: {result}")
```

### 主要方法

#### 元素查找和操作

```python
# 查找元素 (支持多种定位方式)
element = automation.find_element("username", by="id")
elements = automation.find_elements(".button", by="css")

# 点击元素
automation.click("submit-btn", by="id")
automation.click("//button[text()='提交']", by="xpath")

# 输入文本
automation.input_text("username", "用户名", clear_first=True)

# 选择下拉框
automation.select_option("country", value="CN")
automation.select_option("city", text="北京")
automation.select_option("priority", index=0)
```

#### 数据提取

```python
# 获取文本内容
text = automation.get_text("message", by="id")

# 获取属性值
value = automation.get_attribute("username", "value")
placeholder = automation.get_attribute("email", "placeholder")

# 获取页面信息
info = automation.get_page_info()
print(f"页面标题: {info['title']}")
```

#### 等待机制

```python
# 等待元素出现
automation.wait_for_element("loading", condition="presence", timeout=10)

# 等待元素可见
automation.wait_for_element("modal", condition="visible", timeout=5)

# 等待元素可点击
automation.wait_for_element("submit", condition="clickable", timeout=15)

# 便捷方法
automation.wait_and_click("button", timeout=10)
automation.wait_and_input("input", "文本", timeout=5)
```

#### JavaScript执行

```python
# 执行JavaScript
result = automation.execute_script("return document.title;")

# 传递参数
automation.execute_script("arguments[0].style.border = '2px solid red';", element)

# 复杂操作
page_info = automation.execute_script("""
    return {
        title: document.title,
        buttonCount: document.querySelectorAll('button').length,
        windowSize: {width: window.innerWidth, height: window.innerHeight}
    };
""")
```

#### 便捷方法

```python
# 批量填写表单
form_data = {
    "username": "张三",
    "email": "<EMAIL>",
    "age": "30"
}
automation.fill_form(form_data)

# 延迟操作
automation.delayed_input("username", "延迟输入的文本", 2)  # 延迟2秒后输入
automation.delayed_click("submit", 3)  # 延迟3秒后点击

# 滚动到元素
automation.scroll_to_element("footer")

# 截图
automation.take_screenshot("test_result.png")

# 保存日志
automation.save_logs("test_session.log")
```

#### 复制粘贴操作

```python
# 键盘快捷键方式
automation.copy_text("source-element")  # 复制元素文本 (Ctrl+C)
automation.paste_text("target-element")  # 粘贴文本 (Ctrl+V)
automation.copy_and_paste("source", "target")  # 一步完成复制粘贴

# 剪贴板操作方式（推荐）
automation.set_clipboard_text("要复制的文本")  # 设置剪贴板
clipboard_content = automation.get_clipboard_text()  # 获取剪贴板内容
automation.paste_from_clipboard("target-element")  # 从剪贴板粘贴
```

#### 文件和图片操作

```python
# 文件上传
automation.upload_file("file-input", "C:/path/to/file.txt")  # 上传文件到文件输入框

# 复制文件/图片到剪贴板
automation.copy_file_to_clipboard("C:/path/to/document.pdf")  # 复制文件
automation.copy_image_to_clipboard("C:/path/to/image.png")  # 复制图片

# 粘贴文件/图片
automation.paste_file_from_clipboard("drop-zone")  # 从剪贴板粘贴文件 (Ctrl+V)

# 拖拽文件上传
automation.drag_and_drop_file("C:/path/to/file.txt", "upload-area")  # 模拟拖拽上传
```

## 🎯 VSCodeAutomation VSCode专用自动化

### VSCode设置

1. **启动VSCode调试模式**:
   ```bash
   # 使用批处理文件
   start_vscode_debug.bat
   
   # 或手动命令
   code --remote-debugging-port=9222 --remote-allow-origins=*
   ```

2. **在VSCode中**:
   - 打开Copilot Chat面板 (`Ctrl+Shift+P` -> "Copilot: Open Chat")
   - 确保Copilot已登录

### 基础使用

```python
from vscode_automation import VSCodeAutomation

# 创建VSCode自动化实例
vscode = VSCodeAutomation(debug_port=9222)

# 连接到VSCode
if vscode.connect_to_vscode():
    # 检查Copilot面板
    if vscode.find_copilot_panel():
        # 发送消息
        vscode.send_copilot_message("你好，请帮我写一个Python函数")
        
        # 等待回复
        response = vscode.wait_for_copilot_response(timeout=20)
        if response['success']:
            print("收到回复!")
    
    vscode.stop()
```

### 主要方法

#### Copilot操作

```python
# 发送消息
success = vscode.send_copilot_message("请解释Python装饰器")

# 等待回复
response = vscode.wait_for_copilot_response(timeout=30)
if response['success']:
    new_messages = response['new_messages']
    for msg in new_messages:
        print(f"回复: {msg['text']}")

# 发送并等待回复（便捷方法）
result = vscode.send_and_wait_response("什么是Python GIL?", timeout=25)
if result['success']:
    print("交互成功!")
```

#### 聊天历史

```python
# 获取聊天消息
messages = vscode.get_copilot_messages()
print(f"共有 {len(messages)} 条消息")

for msg in messages:
    print(f"消息: {msg['text'][:100]}...")
```

#### VSCode信息

```python
# 获取VSCode信息
info = vscode.get_vscode_info()
print(f"标题: {info['title']}")
print(f"Copilot面板存在: {info['copilotPanelExists']}")
print(f"Monaco编辑器数量: {info['monacoEditors']}")
```

#### 元素高亮

```python
# 高亮Copilot元素
vscode.highlight_copilot_elements()
time.sleep(3)  # 保持高亮3秒
vscode.restore_element_styles()
```

## 🔧 高级用法

### 自定义配置

```python
# 自定义浏览器配置
automation = AutomationFramework(
    headless=True,           # 无头模式
    window_size=(1920, 1080), # 窗口大小
    implicit_wait=15,        # 隐式等待时间
    page_load_timeout=60     # 页面加载超时
)

# VSCode自定义端口
vscode = VSCodeAutomation(
    debug_port=9223,         # 自定义调试端口
    headless=False,          # 显示浏览器
    window_size=(1400, 900)  # 窗口大小
)
```

### 延迟操作详解

```python
# 延迟输入 - 等待指定时间后输入文本
automation.delayed_input("username", "用户名", 2.5)  # 延迟2.5秒后输入

# 延迟点击 - 等待指定时间后点击元素
automation.delayed_click("submit-btn", 3)  # 延迟3秒后点击

# 组合使用延迟操作
automation.delayed_input("username", "admin", 1)
automation.delayed_input("password", "123456", 2)
automation.delayed_click("login-btn", 3)
```

### 复制粘贴高级用法

```python
# 复制粘贴文本内容
automation.copy_and_paste("source-text", "target-text")

# 使用剪贴板进行跨应用操作
automation.set_clipboard_text("Hello World!")
# 可以手动切换到其他应用粘贴，或继续在浏览器中粘贴
automation.paste_from_clipboard("input-field")

# 获取页面内容并复制到剪贴板
page_title = automation.get_text("h1")
automation.set_clipboard_text(page_title)
```

### 文件操作高级用法

```python
# 批量文件上传
files = ["file1.txt", "file2.pdf", "image.jpg"]
for i, file_path in enumerate(files):
    automation.upload_file(f"file-input-{i}", f"C:/uploads/{file_path}")

# 复制多种类型的文件
automation.copy_file_to_clipboard("C:/documents/report.pdf")
automation.paste_file_from_clipboard("pdf-drop-zone")

automation.copy_image_to_clipboard("C:/images/screenshot.png")
automation.paste_file_from_clipboard("image-upload-area")

# 检查文件上传结果
upload_result = automation.get_text("upload-status")
if "成功" in upload_result:
    print("文件上传成功")
```

### 错误处理

```python
try:
    with automation.browser_session():
        automation.open_url("https://example.com")
        
        # 尝试操作可能不存在的元素
        if automation.wait_for_element("optional-element", timeout=5):
            automation.click("optional-element")
        else:
            print("可选元素不存在，跳过操作")
        
        # 必须存在的元素
        if not automation.wait_for_element("required-element", timeout=10):
            raise Exception("必需元素未找到")
        
        automation.click("required-element")
        
except Exception as e:
    print(f"自动化操作失败: {e}")
    
    # 查看错误日志
    for log in automation.logs:
        if "ERROR" in log:
            print(f"错误: {log}")
```

### 批量操作

```python
# 批量发送Copilot消息
questions = [
    "什么是Python装饰器？",
    "如何处理Python异常？",
    "解释Python的GIL机制"
]

results = []
for question in questions:
    result = vscode.send_and_wait_response(question, timeout=30)
    results.append(result)
    time.sleep(2)  # 避免过于频繁

# 保存结果
import json
with open("batch_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

## 📖 运行示例

### 通用HTML自动化示例

```bash
# 运行所有通用示例
python automation_examples.py

# 运行单个示例（修改文件末尾）
# example_basic_usage()
# example_form_operations()
# example_javascript_operations()
```

### VSCode自动化示例

```bash
# 1. 先启动VSCode调试模式
start_vscode_debug.bat

# 2. 在VSCode中打开Copilot Chat面板

# 3. 运行VSCode示例
python vscode_automation_examples.py

# 运行单个示例（修改文件末尾）
# example_basic_connection()
# example_copilot_chat()
# example_comprehensive_test()
```

## 🛠️ 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**:
   ```bash
   pip install --upgrade webdriver-manager
   ```

2. **VSCode调试端口不可用**:
   ```bash
   # 检查端口占用
   netstat -an | findstr :9222
   
   # 重启VSCode调试模式
   taskkill /f /im Code.exe
   code --remote-debugging-port=9222 --remote-allow-origins=*
   ```

3. **元素查找失败**:
   - 增加等待时间
   - 使用不同的定位方式
   - 检查元素是否在iframe中
   - 使用JavaScript直接操作

4. **Copilot面板未找到**:
   - 确保Copilot插件已安装
   - 手动打开Copilot Chat面板
   - 检查面板ID是否正确

5. **剪贴板操作失败**:
   ```bash
   # 安装pyperclip库
   pip install pyperclip
   ```
   - 确保系统支持剪贴板操作
   - 检查是否有其他程序占用剪贴板

6. **文件上传失败**:
   - 检查文件路径是否正确（使用绝对路径）
   - 确认文件存在且有读取权限
   - 验证目标元素是否为 `<input type="file">`
   - 检查文件大小限制

7. **延迟操作不生效**:
   - 确认延迟时间参数位置正确
   - 检查元素是否在延迟时间内保持可用
   - 验证页面是否有动态加载影响

8. **图片复制失败**:
   - 确认图片格式支持（jpg, png, gif, bmp等）
   - 检查图片文件是否损坏
   - 确保在Windows系统上运行（当前限制）

### 调试技巧

```python
# 截图调试
automation.take_screenshot("debug_step1.png")

# 查看页面源码
page_source = automation.driver.page_source
print(page_source[:1000])  # 显示前1000字符

# 高亮元素
automation.execute_script(
    "arguments[0].style.border='3px solid red'", 
    element
)

# 查看日志
for log in automation.logs:
    print(log)

# 保存调试信息
automation.save_logs("debug_session.log")
```

## 💡 最佳实践

1. **使用上下文管理器**确保资源正确释放
2. **合理设置等待时间**，避免过长或过短
3. **优先使用ID和CSS选择器**，XPath作为备选
4. **添加适当的错误处理**和重试机制
5. **定期截图和保存日志**便于调试
6. **避免硬编码延迟**，使用显式等待
7. **模块化操作**，将复杂流程拆分为小步骤

### 新功能使用建议

8. **延迟操作使用**：
   - 优先使用 `delayed_input()` 和 `delayed_click()` 而不是 `time.sleep()`
   - 延迟时间根据实际需要设置，避免过长影响效率
   - 在需要等待页面响应时使用延迟操作

9. **复制粘贴操作**：
   - 优先使用剪贴板方式（`set_clipboard_text`, `paste_from_clipboard`）
   - 键盘快捷键方式作为备选
   - 跨应用操作时必须使用剪贴板方式

10. **文件操作**：
    - 使用绝对路径避免路径问题
    - 上传前检查文件是否存在
    - 对于大文件上传，适当增加超时时间
    - 图片操作优先使用专门的图片方法

11. **错误处理**：
    - 文件操作前检查文件存在性和权限
    - 剪贴板操作前确保pyperclip已安装
    - 延迟操作中添加元素状态检查

12. **性能优化**：
    - 批量操作时适当添加间隔避免过于频繁
    - 大文件操作时考虑分批处理
    - 合理使用延迟时间，平衡速度和稳定性

---

**祝您使用愉快！如有问题请查看示例代码或联系开发团队。**
