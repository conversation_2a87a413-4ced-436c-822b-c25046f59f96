using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 用户Prompt偏好实体 - 记录用户的提示词使用偏好
/// 功能: 存储用户的个性化设置和偏好
/// 支持: 个性化推荐、快速访问、自定义设置
/// </summary>
[SugarTable("UserPromptPreferences", "用户提示词偏好表")]
public class UserPromptPreference : BaseEntity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "用户ID")]
    public int UserId { get; set; }

    /// <summary>
    /// 关联的模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的模板ID")]
    public int TemplateId { get; set; }

    /// <summary>
    /// 偏好类型: Favorite=收藏, Recent=最近使用, Custom=自定义
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "偏好类型")]
    public string PreferenceType { get; set; } = string.Empty;

    /// <summary>
    /// 自定义参数配置，JSON格式
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "自定义参数配置")]
    public string? CustomParameters { get; set; }

    /// <summary>
    /// 使用频率权重
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "使用频率权重")]
    public int FrequencyWeight { get; set; } = 1;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "最后使用时间")]
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 排序权重
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "排序权重")]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否启用")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 备注信息
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "备注信息")]
    public string? Notes { get; set; }

    /// <summary>
    /// 用户 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }

    /// <summary>
    /// 关联的模板 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptTemplate? Template { get; set; }
}
