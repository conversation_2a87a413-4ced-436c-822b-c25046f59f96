<template>
  <div class="coding-tasks-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Edit /></el-icon>
            编码任务
          </h1>
          <p class="page-description">
            管理需要编码的开发步骤，跟踪编码进度和任务分配
          </p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createNewTask">
            <el-icon><Plus /></el-icon>
            创建编码任务
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-select
              v-model="selectedProjectId"
              placeholder="选择项目"
              clearable
              style="width: 200px; margin-right: 12px;"
              @change="handleProjectChange"
            >
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>

            <el-select
              v-model="filterStatus"
              placeholder="任务状态"
              clearable
              style="width: 150px; margin-right: 12px;"
              @change="handleSearch"
            >
              <el-option label="待开始" value="NotStarted" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="已阻塞" value="Blocked" />
            </el-select>

            <el-select
              v-model="filterPriority"
              placeholder="优先级"
              clearable
              style="width: 120px; margin-right: 12px;"
              @change="handleSearch"
            >
              <el-option label="高" value="High" />
              <el-option label="中" value="Medium" />
              <el-option label="低" value="Low" />
            </el-select>

            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务名称或描述"
              style="width: 250px; margin-right: 12px;"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-button @click="handleSearch">搜索</el-button>
          </div>

          <div class="filter-right">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="list">列表</el-radio-button>
              <el-radio-button label="card">卡片</el-radio-button>
              <el-radio-button label="kanban">看板</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#409eff"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalTasks }}</div>
                <div class="stats-label">总编码任务</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#e6a23c"><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ inProgressTasks }}</div>
                <div class="stats-label">进行中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ completedTasks }}</div>
                <div class="stats-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#f56c6c"><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ blockedTasks }}</div>
                <div class="stats-label">已阻塞</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务内容 -->
    <div class="tasks-content">
      <el-card shadow="never" v-if="selectedProjectId">
        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <el-table
            :data="tasks"
            v-loading="loading"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="taskName" label="任务名称" min-width="200">
              <template #default="{ row }">
                <div class="task-name-cell">
                  <el-link type="primary" @click="viewTaskDetail(row)">
                    {{ row.taskName }}
                  </el-link>
                  <div class="task-description">{{ row.description }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="assignedTo" label="分配给" width="120">
              <template #default="{ row }">
                <span v-if="row.assignedUserName">{{ row.assignedUserName }}</span>
                <el-text v-else type="info">未分配</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="estimatedHours" label="预估工时" width="100">
              <template #default="{ row }">
                <span v-if="row.estimatedHours">{{ row.estimatedHours }}h</span>
                <el-text v-else type="info">-</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="截止日期" width="120">
              <template #default="{ row }">
                <span v-if="row.dueDate">{{ formatDate(row.dueDate) }}</span>
                <el-text v-else type="info">-</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="stepCount" label="关联步骤" width="100">
              <template #default="{ row }">
                <el-link type="primary" @click="viewTaskSteps(row)">
                  {{ row.stepCount || 0 }} 个步骤
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button-group size="small">
                  <el-button @click="editTask(row)">编辑</el-button>
                  <el-button @click="assignTask(row)">分配</el-button>
                  <el-dropdown @command="(cmd) => handleTaskOperation(cmd, row)">
                    <el-button>
                      更多<el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="addSteps">添加步骤</el-dropdown-item>
                        <el-dropdown-item command="viewProgress">查看进度</el-dropdown-item>
                        <el-dropdown-item command="duplicate">复制任务</el-dropdown-item>
                        <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="totalCount"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <el-row :gutter="16">
            <el-col
              v-for="task in tasks"
              :key="task.id"
              :span="8"
              style="margin-bottom: 16px;"
            >
              <el-card class="task-card" @click="viewTaskDetail(task)">
                <template #header>
                  <div class="task-card-header">
                    <span class="task-title">{{ task.taskName }}</span>
                    <el-tag :type="getStatusType(task.status)" size="small">
                      {{ getStatusText(task.status) }}
                    </el-tag>
                  </div>
                </template>
                <div class="task-card-content">
                  <p class="task-description">{{ task.description }}</p>
                  <div class="task-meta">
                    <div class="meta-item">
                      <span class="meta-label">优先级:</span>
                      <el-tag :type="getPriorityType(task.priority)" size="small">
                        {{ task.priority }}
                      </el-tag>
                    </div>
                    <div class="meta-item" v-if="task.assignedUserName">
                      <span class="meta-label">分配给:</span>
                      <span>{{ task.assignedUserName }}</span>
                    </div>
                    <div class="meta-item" v-if="task.estimatedHours">
                      <span class="meta-label">预估:</span>
                      <span>{{ task.estimatedHours }}h</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">步骤:</span>
                      <span>{{ task.stepCount || 0 }} 个</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 看板视图 -->
        <div v-else-if="viewMode === 'kanban'" class="kanban-view">
          <el-empty description="看板视图功能正在开发中" />
        </div>
      </el-card>

      <!-- 未选择项目时的提示 -->
      <el-card shadow="never" v-else>
        <el-empty description="请先选择一个项目">
          <el-button type="primary" @click="navigateToProjects">
            前往项目管理
          </el-button>
        </el-empty>
      </el-card>
    </div>

    <!-- 创建任务对话框 -->
    <CodingTaskCreateDialog
      v-model="showCreateTaskDialog"
      :project-id="selectedProjectId"
      :task="selectedTask"
      @success="handleTaskCreateSuccess"
    />

    <!-- 任务详情对话框 -->
    <CodingTaskDetailDialog
      v-model="showTaskDetail"
      :task="selectedTask"
      @refresh="refreshData"
    />

    <!-- 添加步骤对话框 -->
    <AddStepsToTaskDialog
      v-model="showAddStepsDialog"
      :task="selectedTask"
      :project-id="selectedProjectId"
      @success="handleAddStepsSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Edit, Plus, Refresh, Search, Document, Clock, CircleCheck, Warning,
  ArrowDown
} from '@element-plus/icons-vue'
import { projectService } from '@/services/project'
import { codingTaskService } from '@/services/codingTask'
import type { Project } from '@/types'
import type { CodingTask } from '@/services/codingTask'
import dayjs from 'dayjs'

// 组件引入
import CodingTaskCreateDialog from '@/components/development/CodingTaskCreateDialog.vue'
import CodingTaskDetailDialog from '@/components/development/CodingTaskDetailDialog.vue'
import AddStepsToTaskDialog from '@/components/development/AddStepsToTaskDialog.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const projects = ref<Project[]>([])
const tasks = ref<CodingTask[]>([])
const selectedTask = ref<CodingTask | null>(null)
const selectedTasks = ref<CodingTask[]>([])

// 对话框状态
const showCreateTaskDialog = ref(false)
const showTaskDetail = ref(false)
const showAddStepsDialog = ref(false)

// 筛选和搜索
const selectedProjectId = ref<number>()
const searchKeyword = ref('')
const filterStatus = ref('')
const filterPriority = ref('')
const viewMode = ref<'list' | 'card' | 'kanban'>('list')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 统计数据
const totalTasks = ref(0)
const inProgressTasks = ref(0)
const completedTasks = ref(0)
const blockedTasks = ref(0)

// 生命周期
onMounted(() => {
  loadProjects()
  // 从URL参数中获取项目ID
  const projectId = route.query.projectId
  if (projectId) {
    selectedProjectId.value = Number(projectId)
  }
})

// 监听项目变化
watch(selectedProjectId, (newProjectId) => {
  if (newProjectId) {
    loadTasks()
    loadStatistics()
  }
})

// 方法
const loadProjects = async () => {
  try {
    const result = await projectService.getProjects({ pageNumber: 1, pageSize: 100 })
    projects.value = result.items as Project[]
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
  }
}

const loadTasks = async () => {
  if (!selectedProjectId.value) return

  loading.value = true
  try {
    const result = await codingTaskService.getProjectTasks({
      projectId: selectedProjectId.value,
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
      status: filterStatus.value || undefined,
      priority: filterPriority.value || undefined,
      searchKeyword: searchKeyword.value || undefined
    })

    tasks.value = result.items
    totalCount.value = result.totalCount
  } catch (error) {
    console.error('加载编码任务失败:', error)
    ElMessage.error('加载编码任务失败')
    tasks.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  if (!selectedProjectId.value) return

  try {
    const statistics = await codingTaskService.getProjectStatistics(selectedProjectId.value)
    totalTasks.value = statistics.totalTasks
    inProgressTasks.value = statistics.inProgressTasks
    completedTasks.value = statistics.completedTasks
    blockedTasks.value = statistics.blockedTasks
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用默认值
    totalTasks.value = 0
    inProgressTasks.value = 0
    completedTasks.value = 0
    blockedTasks.value = 0
  }
}

const refreshData = () => {
  loadTasks()
  loadStatistics()
}

const handleProjectChange = (projectId: number | undefined) => {
  if (projectId) {
    router.replace({
      query: { ...route.query, projectId: projectId.toString() }
    })
  } else {
    const query = { ...route.query }
    delete query.projectId
    router.replace({ query })
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadTasks()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTasks()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadTasks()
}

const handleSelectionChange = (selection: CodingTask[]) => {
  selectedTasks.value = selection
}

const viewTaskDetail = (task: CodingTask) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const createNewTask = () => {
  selectedTask.value = null
  showCreateTaskDialog.value = true
}

const editTask = (task: CodingTask) => {
  selectedTask.value = task
  showCreateTaskDialog.value = true
}

const assignTask = (task: CodingTask) => {
  // TODO: 实现任务分配功能
  console.log('分配任务:', task)
  ElMessage.info('任务分配功能正在开发中')
}

const viewTaskSteps = (task: CodingTask) => {
  // 跳转到开发步骤页面，并筛选该任务的步骤
  router.push(`/development/steps?projectId=${selectedProjectId.value}&taskId=${task.id}`)
}

const handleTaskOperation = async (command: string, task: CodingTask) => {
  selectedTask.value = task

  switch (command) {
    case 'addSteps':
      showAddStepsDialog.value = true
      break
    case 'viewProgress':
      // TODO: 显示任务进度
      ElMessage.info('查看进度功能正在开发中')
      break
    case 'duplicate':
      // TODO: 复制任务
      ElMessage.info('复制任务功能正在开发中')
      break
    case 'delete':
      await deleteTask(task)
      break
  }
}

const deleteTask = async (task: CodingTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除编码任务 "${task.taskName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await codingTaskService.deleteTask(task.id)
    ElMessage.success('编码任务删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除编码任务失败:', error)
      ElMessage.error('删除编码任务失败')
    }
  }
}

const handleTaskCreateSuccess = () => {
  ElMessage.success('编码任务创建成功')
  refreshData()
}

const handleAddStepsSuccess = () => {
  ElMessage.success('步骤添加成功')
  refreshData()
}

const navigateToProjects = () => {
  router.push('/projects')
}

// 辅助方法
const getStatusType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    NotStarted: 'info',
    InProgress: 'warning',
    Completed: 'success',
    Blocked: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    NotStarted: '待开始',
    InProgress: '进行中',
    Completed: '已完成',
    Blocked: '已阻塞'
  }
  return textMap[status] || status
}

const getPriorityType = (priority: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    High: 'danger',
    Medium: 'warning',
    Low: 'info'
  }
  return typeMap[priority] || 'info'
}

const formatDate = (date: Date) => {
  return dayjs(date).format('YYYY-MM-DD')
}
</script>

<style scoped>
.coding-tasks-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  align-items: center;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.tasks-content {
  margin-bottom: 24px;
}

.task-name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.card-view {
  min-height: 400px;
}

.task-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  font-weight: 500;
  color: #303133;
}

.task-card-content {
  height: 120px;
  display: flex;
  flex-direction: column;
}

.task-description {
  flex: 1;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0 0 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.task-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.meta-label {
  color: #909399;
  min-width: 40px;
}

.kanban-view {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
