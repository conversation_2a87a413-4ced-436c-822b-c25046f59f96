#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copilot DOM集成模块
将DOM自动化功能集成到主项目中
"""

import os
import sys
import time
import threading
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

from vscode_dom_automation import VSCodeDOM<PERSON>utomation
from copilot_message_handler import CopilotMessageHandler
from dom_automation_config import dom_config


class CopilotDOMIntegration:
    """Copilot DOM集成类"""
    
    def __init__(self, api_client=None):
        """
        初始化DOM集成
        
        Args:
            api_client: API客户端实例
        """
        self.api_client = api_client
        self.dom_automation = VSCodeDOMAutomation()
        self.message_handler = None
        self.is_connected = False
        self.connection_status = "disconnected"
        self.last_error = None
        
        # 回调函数
        self.on_message_received = None
        self.on_connection_changed = None
        self.on_error = None
        
        print("Copilot DOM集成模块初始化完成")
    
    def connect(self, debug_port: int = None) -> bool:
        """
        连接到VSCode Copilot
        
        Args:
            debug_port: 调试端口，默认从配置读取
            
        Returns:
            是否连接成功
        """
        if debug_port is None:
            debug_port = dom_config.get_debug_port()
        
        try:
            print(f"🔗 尝试连接到VSCode Copilot (端口: {debug_port})")
            
            # 检查调试端口是否可用
            if not self.dom_automation._check_debug_port(debug_port):
                self.connection_status = "port_unavailable"
                self.last_error = f"调试端口 {debug_port} 不可用"
                print(f"❌ {self.last_error}")
                print("💡 请先启动VSCode调试模式:")
                print(f"   code --remote-debugging-port={debug_port} --remote-allow-origins=*")
                self._notify_connection_changed()
                return False
            
            # 尝试Selenium连接
            if self.dom_automation.connect_via_selenium(debug_port):
                self.is_connected = True
                self.connection_status = "connected_selenium"
                self.message_handler = CopilotMessageHandler(self.dom_automation)
                print("✅ DOM自动化连接成功 (Selenium)")
                self._notify_connection_changed()
                return True
            
            # 尝试WebSocket连接
            if self.dom_automation.connect_via_websocket(debug_port):
                self.is_connected = True
                self.connection_status = "connected_websocket"
                self.message_handler = CopilotMessageHandler(self.dom_automation)
                print("✅ DOM自动化连接成功 (WebSocket)")
                self._notify_connection_changed()
                return True
            
            # 连接失败
            self.connection_status = "connection_failed"
            self.last_error = "所有连接方法都失败"
            print(f"❌ {self.last_error}")
            self._notify_connection_changed()
            return False
            
        except Exception as e:
            self.connection_status = "error"
            self.last_error = str(e)
            print(f"❌ 连接异常: {e}")
            self._notify_connection_changed()
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.dom_automation:
                self.dom_automation.disconnect()
            
            self.is_connected = False
            self.connection_status = "disconnected"
            self.message_handler = None
            self.last_error = None
            
            print("✅ DOM自动化连接已断开")
            self._notify_connection_changed()
            
        except Exception as e:
            print(f"⚠️ 断开连接时出错: {e}")
    
    def send_message(self, message: str) -> Dict:
        """
        发送消息到Copilot
        
        Args:
            message: 要发送的消息
            
        Returns:
            发送结果
        """
        if not self.is_connected or not self.dom_automation:
            return {
                "success": False,
                "error": "未连接到VSCode Copilot",
                "status": "not_connected"
            }
        
        try:
            print(f"📤 发送消息到Copilot: {message}")
            
            # 发送消息
            success = self.dom_automation.send_message_to_copilot(message)
            
            if success:
                return {
                    "success": True,
                    "message": message,
                    "status": "sent",
                    "timestamp": time.time()
                }
            else:
                return {
                    "success": False,
                    "error": "消息发送失败",
                    "status": "send_failed"
                }
                
        except Exception as e:
            error_msg = f"发送消息异常: {e}"
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "status": "exception"
            }
    
    def wait_for_response(self, timeout: int = None) -> Dict:
        """
        等待Copilot回复
        
        Args:
            timeout: 超时时间，默认从配置读取
            
        Returns:
            回复结果
        """
        if not self.is_connected or not self.message_handler:
            return {
                "success": False,
                "error": "未连接到VSCode Copilot",
                "status": "not_connected"
            }
        
        if timeout is None:
            timeout = dom_config.get_response_timeout()
        
        try:
            response = self.message_handler.wait_for_copilot_response(timeout)
            
            # 分析回复内容
            if response.get("status") == "success":
                analysis = self.message_handler.analyze_response_content(response)
                response["analysis"] = analysis
            
            return {
                "success": True,
                "response": response,
                "status": response.get("status", "unknown")
            }
            
        except Exception as e:
            error_msg = f"等待回复异常: {e}"
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "status": "exception"
            }
    
    def send_and_wait(self, message: str, timeout: int = None) -> Dict:
        """
        发送消息并等待回复
        
        Args:
            message: 要发送的消息
            timeout: 超时时间
            
        Returns:
            完整的交互结果
        """
        # 发送消息
        send_result = self.send_message(message)
        if not send_result.get("success"):
            return send_result
        
        # 等待回复
        response_result = self.wait_for_response(timeout)
        
        # 合并结果
        return {
            "success": response_result.get("success", False),
            "send_result": send_result,
            "response_result": response_result,
            "message": message,
            "status": response_result.get("status", "unknown")
        }
    
    def get_chat_history(self) -> List[Dict]:
        """获取聊天历史"""
        if not self.is_connected or not self.message_handler:
            return []
        
        try:
            return self.message_handler.get_copilot_messages()
        except Exception as e:
            print(f"❌ 获取聊天历史失败: {e}")
            return []
    
    def get_latest_response(self) -> Dict:
        """获取最新回复"""
        if not self.is_connected or not self.message_handler:
            return {"status": "not_connected", "error": "未连接"}
        
        try:
            return self.message_handler.get_latest_copilot_response()
        except Exception as e:
            print(f"❌ 获取最新回复失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def check_copilot_status(self) -> Dict:
        """检查Copilot状态"""
        if not self.is_connected:
            return {
                "connected": False,
                "status": self.connection_status,
                "error": self.last_error
            }
        
        try:
            # 检查输入框是否可用
            input_info = self.dom_automation.find_copilot_chat_input()
            
            # 获取最新状态
            latest_response = self.get_latest_response()
            
            return {
                "connected": True,
                "status": self.connection_status,
                "input_available": input_info.get("found", False) if input_info else False,
                "latest_status": latest_response.get("status", "unknown"),
                "message_count": latest_response.get("message_count", 0)
            }
            
        except Exception as e:
            return {
                "connected": True,
                "status": "error",
                "error": str(e)
            }
    
    def set_callbacks(self, on_message_received: Callable = None, 
                     on_connection_changed: Callable = None,
                     on_error: Callable = None):
        """设置回调函数"""
        self.on_message_received = on_message_received
        self.on_connection_changed = on_connection_changed
        self.on_error = on_error
    
    def _notify_connection_changed(self):
        """通知连接状态变化"""
        if self.on_connection_changed:
            try:
                self.on_connection_changed(self.is_connected, self.connection_status)
            except Exception as e:
                print(f"⚠️ 连接状态回调异常: {e}")
    
    def _notify_error(self, error: str):
        """通知错误"""
        if self.on_error:
            try:
                self.on_error(error)
            except Exception as e:
                print(f"⚠️ 错误回调异常: {e}")
    
    def start_monitoring(self, interval: float = None):
        """开始监控消息"""
        if interval is None:
            interval = dom_config.get_check_interval()
        
        def monitor_loop():
            while self.is_connected:
                try:
                    if self.message_handler:
                        latest = self.message_handler.get_latest_copilot_response()
                        if self.on_message_received:
                            self.on_message_received(latest)
                except Exception as e:
                    self._notify_error(f"监控异常: {e}")
                
                time.sleep(interval)
        
        if self.is_connected:
            monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
            monitor_thread.start()
            print("✅ 消息监控已启动")
    
    def get_connection_info(self) -> Dict:
        """获取连接信息"""
        return {
            "is_connected": self.is_connected,
            "connection_status": self.connection_status,
            "last_error": self.last_error,
            "debug_port": dom_config.get_debug_port(),
            "has_message_handler": self.message_handler is not None
        }
