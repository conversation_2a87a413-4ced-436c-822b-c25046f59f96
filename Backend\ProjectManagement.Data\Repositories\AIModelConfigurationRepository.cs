using SqlSugar;
using ProjectManagement.Core.Entities;
using System.Text.Json;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// AI模型配置仓储接口
/// </summary>
public interface IAIModelConfigurationRepository
{
    /// <summary>
    /// 获取所有AI模型配置
    /// </summary>
    Task<List<AIModelConfiguration>> GetAllAsync();

    /// <summary>
    /// 根据用户ID获取配置
    /// </summary>
    Task<List<AIModelConfiguration>> GetByUserIdAsync(int? userId);

    /// <summary>
    /// 根据模型名称获取配置
    /// </summary>
    Task<AIModelConfiguration?> GetByModelNameAsync(string modelName);

    /// <summary>
    /// 获取活跃的配置
    /// </summary>
    Task<List<AIModelConfiguration>> GetActiveConfigurationsAsync();

    /// <summary>
    /// 根据ID获取配置
    /// </summary>
    Task<AIModelConfiguration?> GetByIdAsync(int id);

    /// <summary>
    /// 创建配置
    /// </summary>
    Task<AIModelConfiguration> CreateAsync(AIModelConfiguration configuration);

    /// <summary>
    /// 更新配置
    /// </summary>
    Task<bool> UpdateAsync(AIModelConfiguration configuration);

    /// <summary>
    /// 删除配置
    /// </summary>
    Task<bool> DeleteAsync(int id);

    /// <summary>
    /// 启用/禁用配置
    /// </summary>
    Task<bool> ToggleActiveAsync(int id, bool isActive);

    /// <summary>
    /// 批量更新配置
    /// </summary>
    Task<bool> BatchUpdateAsync(List<AIModelConfiguration> configurations);

    /// <summary>
    /// 获取提供商统计信息
    /// </summary>
    Task<Dictionary<string, int>> GetProviderStatsAsync();
}

/// <summary>
/// AI模型配置仓储实现
/// </summary>
public class AIModelConfigurationRepository : IAIModelConfigurationRepository
{
    private readonly ISqlSugarClient _db;

    public AIModelConfigurationRepository(ISqlSugarClient db)
    {
        _db = db;
    }

    /// <summary>
    /// 获取所有AI模型配置
    /// </summary>
    public async Task<List<AIModelConfiguration>> GetAllAsync()
    {
        return await _db.Queryable<AIModelConfiguration>()
            .OrderBy(x => x.ModelName)
            .ToListAsync();
    }

    /// <summary>
    /// 根据用户ID获取配置
    /// </summary>
    public async Task<List<AIModelConfiguration>> GetByUserIdAsync(int? userId)
    {
        return await _db.Queryable<AIModelConfiguration>()
            .Where(x => x.UserId == userId)
            .OrderBy(x => x.ModelName)
            .ToListAsync();
    }

    /// <summary>
    /// 根据模型名称获取配置
    /// </summary>
    public async Task<AIModelConfiguration?> GetByModelNameAsync(string modelName)
    {
        return await _db.Queryable<AIModelConfiguration>()
            .Where(x => x.ModelName == modelName)
            .FirstAsync();
    }

    /// <summary>
    /// 获取活跃的配置
    /// </summary>
    public async Task<List<AIModelConfiguration>> GetActiveConfigurationsAsync()
    {
        return await _db.Queryable<AIModelConfiguration>()
            .Where(x => x.IsActive)
            .OrderBy(x => x.ModelName)
            .ToListAsync();
    }

    /// <summary>
    /// 根据ID获取配置
    /// </summary>
    public async Task<AIModelConfiguration?> GetByIdAsync(int id)
    {
        return await _db.Queryable<AIModelConfiguration>()
            .Where(x => x.Id == id)
            .FirstAsync();
    }

    /// <summary>
    /// 创建配置
    /// </summary>
    public async Task<AIModelConfiguration> CreateAsync(AIModelConfiguration configuration)
    {
        configuration.CreatedAt = DateTime.Now;
        configuration.UpdatedAt = DateTime.Now;

        var id = await _db.Insertable(configuration).ExecuteReturnIdentityAsync();
        configuration.Id = id;

        return configuration;
    }

    /// <summary>
    /// 更新配置
    /// </summary>
    public async Task<bool> UpdateAsync(AIModelConfiguration configuration)
    {
        configuration.UpdatedAt = DateTime.Now;

        var result = await _db.Updateable(configuration)
            .Where(x => x.Id == configuration.Id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <summary>
    /// 删除配置
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        var result = await _db.Deleteable<AIModelConfiguration>()
            .Where(x => x.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <summary>
    /// 启用/禁用配置
    /// </summary>
    public async Task<bool> ToggleActiveAsync(int id, bool isActive)
    {
        var result = await _db.Updateable<AIModelConfiguration>()
            .SetColumns(x => new AIModelConfiguration
            {
                IsActive = isActive,
                UpdatedAt = DateTime.Now
            })
            .Where(x => x.Id == id)
            .ExecuteCommandAsync();

        return result > 0;
    }

    /// <summary>
    /// 批量更新配置
    /// </summary>
    public async Task<bool> BatchUpdateAsync(List<AIModelConfiguration> configurations)
    {
        try
        {
            await _db.Ado.BeginTranAsync();

            foreach (var config in configurations)
            {
                config.UpdatedAt = DateTime.Now;
                await _db.Updateable(config)
                    .Where(x => x.Id == config.Id)
                    .ExecuteCommandAsync();
            }

            await _db.Ado.CommitTranAsync();
            return true;
        }
        catch
        {
            await _db.Ado.RollbackTranAsync();
            return false;
        }
    }

    /// <summary>
    /// 获取提供商统计信息
    /// </summary>
    public async Task<Dictionary<string, int>> GetProviderStatsAsync()
    {
        var stats = await _db.Queryable<AIModelConfiguration>()
            .GroupBy(x => x.ModelName)
            .Select(x => new { Provider = x.ModelName, Count = SqlFunc.AggregateCount(x.Id) })
            .ToListAsync();

        return stats.ToDictionary(x => x.Provider, x => x.Count);
    }
}

/// <summary>
/// AI模型配置扩展方法
/// </summary>
public static class AIModelConfigurationExtensions
{
    /// <summary>
    /// 解析模型参数
    /// </summary>
    public static T? GetModelParameters<T>(this AIModelConfiguration config) where T : class
    {
        if (string.IsNullOrEmpty(config.ModelParameters))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(config.ModelParameters);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 设置模型参数
    /// </summary>
    public static void SetModelParameters<T>(this AIModelConfiguration config, T parameters) where T : class
    {
        if (parameters == null)
        {
            config.ModelParameters = null;
            return;
        }

        try
        {
            config.ModelParameters = JsonSerializer.Serialize(parameters, new JsonSerializerOptions
            {
                WriteIndented = true
            });
        }
        catch
        {
            config.ModelParameters = null;
        }
    }

    /// <summary>
    /// 转换为AI模型配置DTO
    /// </summary>
    public static object ToProviderConfig(this AIModelConfiguration config)
    {
        var parameters = config.GetModelParameters<Dictionary<string, object>>();

        return new
        {
            config.Id,
            config.ModelName,
            config.ApiEndpoint,
            ApiKey = string.IsNullOrEmpty(config.ApiKey) ? "" : "***",
            Parameters = parameters ?? new Dictionary<string, object>(),
            config.IsActive,
            config.CreatedAt,
            config.UpdatedAt
        };
    }

    /// <summary>
    /// 验证配置完整性
    /// </summary>
    public static (bool IsValid, List<string> Errors) ValidateConfiguration(this AIModelConfiguration config)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(config.ModelName))
            errors.Add("模型名称不能为空");

        // 根据模型名称验证必需的配置
        var modelName = config.ModelName.ToLower();
        if (modelName.Contains("gpt") || modelName.Contains("openai"))
        {
            if (string.IsNullOrWhiteSpace(config.ApiKey))
                errors.Add("OpenAI模型需要配置API密钥");
        }
        else if (modelName.Contains("claude"))
        {
            if (string.IsNullOrWhiteSpace(config.ApiKey))
                errors.Add("Claude模型需要配置API密钥");
        }
        else if (modelName.Contains("deepseek"))
        {
            if (string.IsNullOrWhiteSpace(config.ApiKey))
                errors.Add("DeepSeek模型需要配置API密钥");
        }

        // 验证API端点格式
        if (!string.IsNullOrWhiteSpace(config.ApiEndpoint))
        {
            if (!Uri.TryCreate(config.ApiEndpoint, UriKind.Absolute, out _))
                errors.Add("API端点格式不正确");
        }

        // 验证模型参数JSON格式
        if (!string.IsNullOrWhiteSpace(config.ModelParameters))
        {
            try
            {
                JsonSerializer.Deserialize<object>(config.ModelParameters);
            }
            catch
            {
                errors.Add("模型参数JSON格式不正确");
            }
        }

        return (errors.Count == 0, errors);
    }
}
