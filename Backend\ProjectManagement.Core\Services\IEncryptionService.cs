namespace ProjectManagement.Core.Services;

/// <summary>
/// 加密服务接口
/// </summary>
public interface IEncryptionService
{
    /// <summary>
    /// 加密字符串
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <returns>加密后的字符串</returns>
    string Encrypt(string plainText);

    /// <summary>
    /// 解密字符串
    /// </summary>
    /// <param name="cipherText">密文</param>
    /// <returns>解密后的字符串</returns>
    string Decrypt(string cipherText);
}
