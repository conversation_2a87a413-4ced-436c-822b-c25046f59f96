// 用户基本信息
export interface User {
  id: number
  username: string
  email: string
  realName?: string
  phone?: string
  avatar?: string
  role: string
  status: number
  lastLoginTime?: string
  lastLoginAt?: string
  emailVerified: boolean
  twoFactorEnabled: boolean
  preferences?: string
  createdTime: string
  createdAt?: string
  updatedTime?: string
  updatedAt?: string
}

// 用户信息DTO（与后端对应）
export interface UserInfoDto {
  id: number
  username: string
  email: string
  realName?: string
  role: string
  status: number
  avatar?: string
  createdAt: string
  lastLoginAt?: string
}

// 用户更新请求
export interface UpdateUserRequestDto {
  realName?: string
  phone?: string
  avatar?: string
  role?: string
  status?: number
}

// 修改密码请求
export interface ChangePasswordRequestDto {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户统计信息
export interface UserStatisticsDto {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  inactiveUsers: number
}

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  pageNumber?: number
  search?: string
}

// 用户角色枚举
export enum UserRole {
  User = 'User',
  ProjectManager = 'ProjectManager',
  Developer = 'Developer',
  Tester = 'Tester',
  ProductManager = 'ProductManager',
  Admin = 'Admin',
  SuperAdmin = 'SuperAdmin'
}

// 用户状态枚举
export enum UserStatus {
  Active = 1,
  Inactive = 2,
  Suspended = 3,
  Pending = 4,
  Deleted = 5
}

// 用户权限
export interface UserPermission {
  id: number
  name: string
  description?: string
  resource: string
  action: string
}

// 用户会话信息
export interface UserSession {
  id: string
  userId: number
  deviceInfo?: string
  ipAddress?: string
  userAgent?: string
  loginTime: string
  lastActivityTime: string
  isActive: boolean
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  dashboard: {
    layout: string
    widgets: string[]
  }
}

// 用户活动日志
export interface UserActivity {
  id: number
  userId: number
  action: string
  resource?: string
  resourceId?: number
  description?: string
  ipAddress?: string
  userAgent?: string
  createdAt: string
}

// 用户团队关联
export interface UserTeam {
  id: number
  userId: number
  teamId: number
  role: string
  joinedAt: string
  isActive: boolean
}

// 用户项目关联
export interface UserProject {
  id: number
  userId: number
  projectId: number
  role: string
  permissions: string[]
  joinedAt: string
  isActive: boolean
}
