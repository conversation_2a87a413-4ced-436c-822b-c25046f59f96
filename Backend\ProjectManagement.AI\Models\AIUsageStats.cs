namespace ProjectManagement.AI.Models;

/// <summary>
/// AI使用统计信息
/// </summary>
public class AIUsageStats
{
    /// <summary>
    /// 总请求数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }

    /// <summary>
    /// 总Token使用量
    /// </summary>
    public long TotalTokens { get; set; }

    /// <summary>
    /// 总成本
    /// </summary>
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;

    /// <summary>
    /// 失败率
    /// </summary>
    public double FailureRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests * 100 : 0;

    /// <summary>
    /// 构造函数
    /// </summary>
    public AIUsageStats()
    {
        TotalRequests = 0;
        SuccessfulRequests = 0;
        FailedRequests = 0;
        AverageResponseTime = 0;
        TotalTokens = 0;
        TotalCost = 0;
        LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        TotalRequests = 0;
        SuccessfulRequests = 0;
        FailedRequests = 0;
        AverageResponseTime = 0;
        TotalTokens = 0;
        TotalCost = 0;
        LastUpdated = DateTime.Now;
    }

    /// <summary>
    /// 获取统计摘要
    /// </summary>
    /// <returns>统计摘要字符串</returns>
    public string GetSummary()
    {
        return $"总请求: {TotalRequests}, 成功: {SuccessfulRequests}, 失败: {FailedRequests}, " +
               $"成功率: {SuccessRate:F2}%, 平均响应时间: {AverageResponseTime:F2}ms, " +
               $"总Token: {TotalTokens}, 总成本: ${TotalCost:F4}";
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="responseTime">响应时间（毫秒）</param>
    /// <param name="success">是否成功</param>
    /// <param name="tokens">使用的Token数量</param>
    /// <param name="cost">成本</param>
    public void UpdateStats(double responseTime, bool success, int tokens = 0, decimal cost = 0)
    {
        TotalRequests++;
        
        if (success)
        {
            SuccessfulRequests++;
        }
        else
        {
            FailedRequests++;
        }

        // 更新平均响应时间
        AverageResponseTime = (AverageResponseTime * (TotalRequests - 1) + responseTime) / TotalRequests;

        TotalTokens += tokens;
        TotalCost += cost;
        LastUpdated = DateTime.Now;
    }
}
