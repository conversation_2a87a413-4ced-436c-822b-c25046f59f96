using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using ProjectManagement.Core.DTOs;

namespace ProjectManagement.AI.Services;

/// <summary>
/// 任务状态管理服务
/// </summary>
public interface ITaskStatusService
{
    /// <summary>
    /// 创建新任务
    /// </summary>
    Task<string> CreateTaskAsync(string taskType, int userId, int projectId, string message = "");

    /// <summary>
    /// 更新任务状态
    /// </summary>
    Task UpdateTaskStatusAsync(string taskId, string status, int progress = 0, string message = "", object? result = null, string? error = null);

    /// <summary>
    /// 获取任务状态
    /// </summary>
    Task<AITaskStatusDto?> GetTaskStatusAsync(string taskId);

    /// <summary>
    /// 删除任务
    /// </summary>
    Task RemoveTaskAsync(string taskId);

    /// <summary>
    /// 清理过期任务
    /// </summary>
    Task CleanupExpiredTasksAsync();
}

/// <summary>
/// 内存任务状态管理服务实现
/// </summary>
public class InMemoryTaskStatusService : ITaskStatusService
{
    private readonly ConcurrentDictionary<string, TaskInfo> _tasks = new();
    private readonly ILogger<InMemoryTaskStatusService> _logger;
    private readonly Timer _cleanupTimer;

    public InMemoryTaskStatusService(ILogger<InMemoryTaskStatusService> logger)
    {
        _logger = logger;

        // 每5分钟清理一次过期任务
        _cleanupTimer = new Timer(async _ => await CleanupExpiredTasksAsync(),
            null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    public Task<string> CreateTaskAsync(string taskType, int userId, int projectId, string message = "")
    {
        var taskId = Guid.NewGuid().ToString();
        var taskInfo = new TaskInfo
        {
            TaskId = taskId,
            TaskType = taskType,
            UserId = userId,
            ProjectId = projectId,
            Status = "Pending",
            Progress = 0,
            Message = message,
            StartTime = DateTime.UtcNow,
            EstimatedCompletion = DateTime.UtcNow.AddMinutes(5), // 默认5分钟
            CreatedAt = DateTime.UtcNow
        };

        _tasks.TryAdd(taskId, taskInfo);
        _logger.LogInformation("创建任务: {TaskId}, 类型: {TaskType}, 用户: {UserId}", taskId, taskType, userId);

        return Task.FromResult(taskId);
    }

    public Task UpdateTaskStatusAsync(string taskId, string status, int progress = 0, string message = "", object? result = null, string? error = null)
    {
        if (_tasks.TryGetValue(taskId, out var taskInfo))
        {
            taskInfo.Status = status;
            taskInfo.Progress = Math.Max(0, Math.Min(100, progress)); // 确保进度在0-100之间
            taskInfo.Message = message;
            taskInfo.Result = result;
            taskInfo.Error = error;
            taskInfo.UpdatedAt = DateTime.UtcNow;

            if (status == "Completed" || status == "Failed")
            {
                taskInfo.CompletedAt = DateTime.UtcNow;
            }

            _logger.LogInformation("更新任务状态: {TaskId}, 状态: {Status}, 进度: {Progress}%", taskId, status, progress);
        }
        else
        {
            _logger.LogWarning("尝试更新不存在的任务: {TaskId}", taskId);
        }

        return Task.CompletedTask;
    }

    public Task<AITaskStatusDto?> GetTaskStatusAsync(string taskId)
    {
        if (_tasks.TryGetValue(taskId, out var taskInfo))
        {
            var dto = new AITaskStatusDto
            {
                TaskId = taskInfo.TaskId,
                Status = taskInfo.Status,
                Progress = taskInfo.Progress,
                Message = taskInfo.Message,
                StartTime = taskInfo.StartTime,
                EstimatedCompletion = taskInfo.EstimatedCompletion,
                Error = taskInfo.Error,
                Result = taskInfo.Result
            };

            return Task.FromResult<AITaskStatusDto?>(dto);
        }

        _logger.LogWarning("查询不存在的任务: {TaskId}", taskId);
        return Task.FromResult<AITaskStatusDto?>(null);
    }

    public Task RemoveTaskAsync(string taskId)
    {
        if (_tasks.TryRemove(taskId, out var taskInfo))
        {
            _logger.LogInformation("删除任务: {TaskId}", taskId);
        }

        return Task.CompletedTask;
    }

    public Task CleanupExpiredTasksAsync()
    {
        var expiredTime = DateTime.UtcNow.AddHours(-2); // 2小时前的任务视为过期
        var expiredTasks = _tasks.Where(kvp => kvp.Value.CreatedAt < expiredTime).ToList();

        foreach (var (taskId, _) in expiredTasks)
        {
            _tasks.TryRemove(taskId, out _);
        }

        if (expiredTasks.Any())
        {
            _logger.LogInformation("清理过期任务: {Count} 个", expiredTasks.Count);
        }

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
    }
}

/// <summary>
/// 任务信息内部类
/// </summary>
internal class TaskInfo
{
    public string TaskId { get; set; } = string.Empty;
    public string TaskType { get; set; } = string.Empty;
    public int UserId { get; set; }
    public int ProjectId { get; set; }
    public string Status { get; set; } = string.Empty;
    public int Progress { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime? StartTime { get; set; }
    public DateTime? EstimatedCompletion { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public object? Result { get; set; }
    public string? Error { get; set; }
}
