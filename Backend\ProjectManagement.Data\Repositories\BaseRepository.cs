using System.Linq.Expressions;
using SqlSugar;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// SqlSugar基础仓储实现
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class BaseRepository<T> : IRepository<T> where T : BaseEntity, new()
{
    protected readonly ISqlSugarClient _db;
    protected readonly ILogger<BaseRepository<T>> _logger;

    public BaseRepository(ISqlSugarClient db, ILogger<BaseRepository<T>> logger)
    {
        _db = db;
        _logger = logger;
    }

    #region 查询操作

    public virtual async Task<T?> GetByIdAsync(int id)
    {
        try
        {
            return await _db.Queryable<T>()
                .Where(x => x.Id == id && !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体失败，ID: {Id}", id);
            throw;
        }
    }

    public virtual async Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
    {
        try
        {
            return await _db.Queryable<T>()
                .Where(predicate)
                .Where(x => !x.IsDeleted)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件获取实体失败");
            throw;
        }
    }

    public virtual async Task<List<T>> GetAllAsync(bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<T>();
            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有实体失败");
            throw;
        }
    }

    public virtual async Task<List<T>> GetListAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<T>().Where(predicate);
            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件获取实体列表失败");
            throw;
        }
    }

    public virtual async Task<PagedResult<T>> GetPagedListAsync(
        int pageIndex,
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool isAsc = true,
        bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<T>();

            // 添加删除过滤
            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }

            // 添加查询条件
            if (predicate != null)
            {
                query = query.Where(predicate);
            }

            // 添加排序
            if (orderBy != null)
            {
                query = isAsc ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
            }
            else
            {
                query = query.OrderByDescending(x => x.CreatedTime);
            }

            // 获取总数
            var totalCount = await query.CountAsync();

            // 分页查询
            var items = await query
                .ToPageListAsync(pageIndex, pageSize);

            return new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页查询失败，页码: {PageIndex}, 页大小: {PageSize}", pageIndex, pageSize);
            throw;
        }
    }

    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<T>().Where(predicate);
            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }
            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查实体是否存在失败");
            throw;
        }
    }

    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, bool includeDeleted = false)
    {
        try
        {
            var query = _db.Queryable<T>();
            if (!includeDeleted)
            {
                query = query.Where(x => !x.IsDeleted);
            }
            if (predicate != null)
            {
                query = query.Where(predicate);
            }
            return await query.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取记录数量失败");
            throw;
        }
    }

    #endregion

    #region 增删改操作

    public virtual async Task<T> AddAsync(T entity)
    {
        try
        {
            entity.CreatedTime = DateTime.Now;
            entity.Version = 1;

            var result = await _db.Insertable(entity).ExecuteReturnEntityAsync();
            _logger.LogInformation("添加实体成功，ID: {Id}", result.Id);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加实体失败");
            throw;
        }
    }

    public virtual async Task<int> AddRangeAsync(List<T> entities)
    {
        try
        {
            foreach (var entity in entities)
            {
                entity.CreatedTime = DateTime.Now;
                entity.Version = 1;
            }

            var result = await _db.Insertable(entities).ExecuteCommandAsync();
            _logger.LogInformation("批量添加实体成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量添加实体失败");
            throw;
        }
    }

    public virtual async Task<bool> UpdateAsync(T entity)
    {
        try
        {
            entity.UpdatedTime = DateTime.Now;
            entity.Version++;

            var result = await _db.Updateable(entity)
                .Where(x => x.Id == entity.Id && x.Version == entity.Version - 1)
                .ExecuteCommandAsync();

            if (result == 0)
            {
                throw new InvalidOperationException("实体已被其他用户修改，请刷新后重试");
            }

            _logger.LogInformation("更新实体成功，ID: {Id}", entity.Id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新实体失败，ID: {Id}", entity.Id);
            throw;
        }
    }

    public virtual async Task<int> UpdateRangeAsync(List<T> entities)
    {
        try
        {
            foreach (var entity in entities)
            {
                entity.UpdatedTime = DateTime.Now;
                entity.Version++;
            }

            var result = await _db.Updateable(entities).ExecuteCommandAsync();
            _logger.LogInformation("批量更新实体成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新实体失败");
            throw;
        }
    }

    public virtual async Task<int> UpdateAsync(Expression<Func<T, bool>> predicate, Expression<Func<T, T>> updateExpression)
    {
        try
        {
            var result = await _db.Updateable<T>()
                .SetColumns(updateExpression)
                .Where(predicate)
                .Where(x => !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("根据条件更新实体成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件更新实体失败");
            throw;
        }
    }

    public virtual async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var result = await _db.Deleteable<T>().In(id).ExecuteCommandAsync();
            _logger.LogInformation("物理删除实体成功，ID: {Id}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "物理删除实体失败，ID: {Id}", id);
            throw;
        }
    }

    public virtual async Task<bool> DeleteAsync(T entity)
    {
        try
        {
            var result = await _db.Deleteable(entity).ExecuteCommandAsync();
            _logger.LogInformation("物理删除实体成功，ID: {Id}", entity.Id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "物理删除实体失败，ID: {Id}", entity.Id);
            throw;
        }
    }

    public virtual async Task<int> DeleteAsync(Expression<Func<T, bool>> predicate)
    {
        try
        {
            var result = await _db.Deleteable<T>().Where(predicate).ExecuteCommandAsync();
            _logger.LogInformation("根据条件物理删除实体成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件物理删除实体失败");
            throw;
        }
    }

    public virtual async Task<bool> SoftDeleteAsync(int id, int? deletedBy = null)
    {
        try
        {
            var result = await _db.Updateable<T>()
                .SetColumns(x => new T
                {
                    IsDeleted = true,
                    DeletedTime = DateTime.Now,
                    DeletedBy = deletedBy
                })
                .Where(x => x.Id == id && !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("软删除实体成功，ID: {Id}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "软删除实体失败，ID: {Id}", id);
            throw;
        }
    }

    public virtual async Task<int> SoftDeleteAsync(Expression<Func<T, bool>> predicate, int? deletedBy = null)
    {
        try
        {
            var result = await _db.Updateable<T>()
                .SetColumns(x => new T
                {
                    IsDeleted = true,
                    DeletedTime = DateTime.Now,
                    DeletedBy = deletedBy
                })
                .Where(predicate)
                .Where(x => !x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("根据条件软删除实体成功，数量: {Count}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件软删除实体失败");
            throw;
        }
    }

    public virtual async Task<bool> RestoreAsync(int id)
    {
        try
        {
            var result = await _db.Updateable<T>()
                .SetColumns(x => new T
                {
                    IsDeleted = false,
                    DeletedTime = null,
                    DeletedBy = null
                })
                .Where(x => x.Id == id && x.IsDeleted)
                .ExecuteCommandAsync();

            _logger.LogInformation("恢复软删除实体成功，ID: {Id}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复软删除实体失败，ID: {Id}", id);
            throw;
        }
    }

    #endregion

    #region 事务操作

    public virtual async Task BeginTransactionAsync()
    {
        await Task.Run(() => _db.Ado.BeginTran());
    }

    public virtual async Task CommitTransactionAsync()
    {
        await Task.Run(() => _db.Ado.CommitTran());
    }

    public virtual async Task RollbackTransactionAsync()
    {
        await Task.Run(() => _db.Ado.RollbackTran());
    }

    #endregion
}
