@echo off
chcp 65001 >nul
title AI驱动的VSCode自动化客户端

echo.
echo ========================================
echo   AI驱动的VSCode自动化客户端 - GUI版本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

:: 检查是否在正确的目录
if not exist "gui_main.py" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查依赖是否安装
echo 检查依赖项...
python -c "import tkinter, yaml, httpx, psutil, loguru" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖项...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖项安装失败
        pause
        exit /b 1
    )
)

:: 启动GUI应用
echo 启动图形界面...
python gui_main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出
    pause
)
