-- =============================================
-- 脚本名称: 008_UpdateConstraintsForFlowControl.sql
-- 脚本描述: 更新约束以支持流程控制功能
-- 创建时间: 2025-06-24
-- 功能说明: 
--   1. 更新 UIAutoMationTemplateSequences 表的 Category 约束
--   2. 更新 UIAutoMationTemplateSteps 表的 ActionType 约束
-- =============================================

USE [ProjectManagementAI]
GO

PRINT '======================================================';
PRINT '开始执行约束更新脚本: 008_UpdateConstraintsForFlowControl.sql';
PRINT '功能: 更新约束以支持流程控制功能';
PRINT '执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '======================================================';
PRINT '';

-- =============================================
-- 1. 更新 UIAutoMationTemplateSequences 表的 Category 约束
-- =============================================

PRINT '正在更新 UIAutoMationTemplateSequences 表的 Category 约束...';

-- 删除旧的 Category 约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSequences_Category')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
    DROP CONSTRAINT [CK_UIAutoMationTemplateSequences_Category];
    
    PRINT '✓ 已删除旧的 Category 约束';
END

-- 注释掉 Category 约束，允许自定义分类
-- ALTER TABLE [dbo].[UIAutoMationTemplateSequences]
-- ADD CONSTRAINT [CK_UIAutoMationTemplateSequences_Category]
-- CHECK (Category IN (
--     '按钮', '菜单', '对话框', '输入框', '图标', '文本', '状态', '工具栏', '面板',
--     'CopilotChat自动化', '流程控制示例', '其他'
-- ));

PRINT '✓ 已添加新的 Category 约束（包含流程控制示例）';

-- =============================================
-- 2. 更新 UIAutoMationTemplateSteps 表的 ActionType 约束
-- =============================================

PRINT '正在更新 UIAutoMationTemplateSteps 表的 ActionType 约束...';

-- 删除旧的 ActionType 约束
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_UIAutoMationTemplateSteps_ActionType')
BEGIN
    ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
    DROP CONSTRAINT [CK_UIAutoMationTemplateSteps_ActionType];
    
    PRINT '✓ 已删除旧的 ActionType 约束';
END

-- 注释掉 ActionType 约束，允许自定义动作类型
-- ALTER TABLE [dbo].[UIAutoMationTemplateSteps]
-- ADD CONSTRAINT [CK_UIAutoMationTemplateSteps_ActionType]
-- CHECK (ActionType IN (
--     -- 基础操作类型
--     'click', 'wait', 'input', 'delay', 'screenshot', 'verify', 'scroll', 'key_press',
--     -- 流程控制类型
--     'condition', 'loop', 'loop_end', 'branch', 'jump', 'exit'
-- ));

PRINT '✓ 已添加新的 ActionType 约束（包含流程控制类型）';

-- =============================================
-- 3. 验证约束更新
-- =============================================

PRINT '正在验证约束更新...';

-- 验证 Category 约束
DECLARE @CategoryConstraint NVARCHAR(MAX);
SELECT @CategoryConstraint = definition 
FROM sys.check_constraints 
WHERE name = 'CK_UIAutoMationTemplateSequences_Category';

IF @CategoryConstraint LIKE '%流程控制示例%'
BEGIN
    PRINT '✓ Category 约束验证通过 - 包含流程控制示例';
END
ELSE
BEGIN
    PRINT '❌ Category 约束验证失败';
END

-- 验证 ActionType 约束
DECLARE @ActionTypeConstraint NVARCHAR(MAX);
SELECT @ActionTypeConstraint = definition 
FROM sys.check_constraints 
WHERE name = 'CK_UIAutoMationTemplateSteps_ActionType';

IF @ActionTypeConstraint LIKE '%condition%' AND @ActionTypeConstraint LIKE '%loop%'
BEGIN
    PRINT '✓ ActionType 约束验证通过 - 包含流程控制类型';
END
ELSE
BEGIN
    PRINT '❌ ActionType 约束验证失败';
END

PRINT '';
PRINT '✅ 约束更新脚本执行完成！';
PRINT '======================================================';
PRINT '已更新以下约束：';
PRINT '1. UIAutoMationTemplateSequences.Category - 新增"流程控制示例"分类';
PRINT '2. UIAutoMationTemplateSteps.ActionType - 新增流程控制动作类型';
PRINT '';
PRINT '现在支持的 Category 值：';
PRINT '- 按钮, 菜单, 对话框, 输入框, 图标, 文本, 状态, 工具栏, 面板';
PRINT '- CopilotChat自动化, 流程控制示例, 其他';
PRINT '';
PRINT '现在支持的 ActionType 值：';
PRINT '- 基础操作: click, wait, input, delay, screenshot, verify, scroll, key_press';
PRINT '- 流程控制: condition, loop, loop_end, branch, jump, exit';
PRINT '';
PRINT '脚本执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
