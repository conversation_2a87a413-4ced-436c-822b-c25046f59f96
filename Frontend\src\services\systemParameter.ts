import { ApiService } from './api'

export interface SystemParameterOption {
  value: string
  label: string
  description?: string
  extendedProperties?: string
}

export interface SystemParameter {
  id: number
  category: string
  parameterKey: string
  parameterValue: string
  displayName: string
  description?: string
  sortOrder: number
  isActive: boolean
  isSystem: boolean
  extendedProperties?: string
  createdTime: string
  updatedTime?: string
}

export interface CreateSystemParameterRequest {
  category: string
  parameterKey: string
  parameterValue: string
  displayName: string
  description?: string
  sortOrder?: number
  isActive?: boolean
  extendedProperties?: string
}

export interface UpdateSystemParameterRequest {
  parameterKey?: string
  parameterValue?: string
  displayName?: string
  description?: string
  sortOrder?: number
  isActive?: boolean
  extendedProperties?: string
}

/**
 * 系统参数服务
 */
export class SystemParameterService {
  /**
   * 获取所有分类
   */
  static async getCategories(): Promise<string[]> {
    return ApiService.get('/api/SystemParameters/categories')
  }

  /**
   * 根据分类获取参数选项
   */
  static async getParameterOptions(category: string, includeInactive = false): Promise<SystemParameterOption[]> {
    return ApiService.get(`/api/SystemParameters/categories/${category}/options`, {
      params: { includeInactive }
    })
  }

  /**
   * 根据分类获取参数列表
   */
  static async getParametersByCategory(category: string, includeInactive = false): Promise<SystemParameter[]> {
    return ApiService.get(`/api/SystemParameters/categories/${category}`, {
      params: { includeInactive }
    })
  }

  /**
   * 获取参数详情
   */
  static async getParameter(id: number): Promise<SystemParameter> {
    return ApiService.get(`/api/SystemParameters/${id}`)
  }

  /**
   * 创建系统参数
   */
  static async createParameter(data: CreateSystemParameterRequest): Promise<SystemParameter> {
    return ApiService.post('/api/SystemParameters', data)
  }

  /**
   * 更新系统参数
   */
  static async updateParameter(id: number, data: UpdateSystemParameterRequest): Promise<SystemParameter> {
    return ApiService.put(`/api/SystemParameters/${id}`, data)
  }

  /**
   * 删除系统参数
   */
  static async deleteParameter(id: number): Promise<void> {
    return ApiService.delete(`/api/SystemParameters/${id}`)
  }

  /**
   * 获取分类统计
   */
  static async getStatistics(): Promise<Record<string, number>> {
    return ApiService.get('/api/SystemParameters/statistics')
  }

  // 便捷方法：获取常用参数选项

  /**
   * 获取技术栈选项
   */
  static async getTechnologyStackOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('TechnologyStack')
  }

  /**
   * 获取优先级选项
   */
  static async getPriorityOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('Priority')
  }

  /**
   * 获取项目状态选项
   */
  static async getProjectStatusOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('ProjectStatus')
  }

  /**
   * 获取需求状态选项
   */
  static async getRequirementStatusOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('RequirementStatus')
  }

  /**
   * 获取步骤类型选项
   */
  static async getStepTypeOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('StepType')
  }

  /**
   * 获取组件类型选项
   */
  static async getComponentTypeOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('ComponentType')
  }

  /**
   * 获取数据库类型选项
   */
  static async getDatabaseTypeOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('DatabaseType')
  }

  /**
   * 获取原型类型选项
   */
  static async getPrototypeTypeOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('PrototypeType')
  }

  /**
   * 获取设备类型选项
   */
  static async getDeviceTypeOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('DeviceType')
  }

  /**
   * 获取保真度级别选项
   */
  static async getFidelityLevelOptions(): Promise<SystemParameterOption[]> {
    return this.getParameterOptions('FidelityLevel')
  }
}

// 系统参数预定义分类常量（用户可以创建新分类，不限于此列表）
export const SystemParameterCategory = {
  TechnologyStack: 'TechnologyStack',
  Priority: 'Priority',
  ProjectStatus: 'ProjectStatus',
  RequirementStatus: 'RequirementStatus',
  StepType: 'StepType',
  ComponentType: 'ComponentType',
  AIProvider: 'AIProvider',
  DatabaseType: 'DatabaseType',
  PrototypeType: 'PrototypeType',
  DeviceType: 'DeviceType',
  FidelityLevel: 'FidelityLevel',
  MessageType: 'MessageType',
  TaskStatus: 'TaskStatus',
  UserRole: 'UserRole',
  FileExtension: 'FileExtension',
  Framework: 'Framework',
  Tool: 'Tool',
  Other: 'Other'
} as const

export type SystemParameterCategoryType = typeof SystemParameterCategory[keyof typeof SystemParameterCategory]

// 预定义分类的显示名称映射
export const PredefinedCategoryDisplayNames: Record<string, string> = {
  TechnologyStack: '技术栈',
  Priority: '优先级',
  ProjectStatus: '项目状态',
  RequirementStatus: '需求状态',
  StepType: '步骤类型',
  ComponentType: '组件类型',
  AIProvider: 'AI提供商',
  DatabaseType: '数据库类型',
  PrototypeType: '原型类型',
  DeviceType: '设备类型',
  FidelityLevel: '保真度级别',
  MessageType: '消息类型',
  TaskStatus: '任务状态',
  UserRole: '用户角色',
  FileExtension: '文件扩展名',
  Framework: '框架',
  Tool: '工具',
  Other: '其他'
}
