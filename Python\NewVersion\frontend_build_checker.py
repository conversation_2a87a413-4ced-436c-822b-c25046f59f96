#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端项目编译检测器
支持Vue、React、Angular、TypeScript等前端项目的编译状态检测
"""

import subprocess
import json
import re
import os
from pathlib import Path
from typing import Dict, List, Optional
import time

class FrontendBuildChecker:
    """前端项目编译检测器"""
    
    def __init__(self, project_path: str):
        """
        初始化编译检测器
        
        Args:
            project_path: 前端项目路径
        """
        self.project_path = Path(project_path)
        self.project_type = self._detect_project_type()
        self.last_build_time = 0
        
    def _detect_project_type(self) -> str:
        """检测项目类型"""
        package_json = self.project_path / "package.json"
        
        if not package_json.exists():
            return "unknown"
            
        try:
            with open(package_json, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
            dependencies = {**package_data.get('dependencies', {}), 
                          **package_data.get('devDependencies', {})}
            
            # 检测项目类型
            if 'vue' in dependencies or '@vue/cli-service' in dependencies:
                return 'vue'
            elif 'react' in dependencies or 'react-scripts' in dependencies:
                return 'react'
            elif '@angular/core' in dependencies:
                return 'angular'
            elif 'typescript' in dependencies:
                return 'typescript'
            elif 'webpack' in dependencies:
                return 'webpack'
            else:
                return 'nodejs'
                
        except Exception as e:
            print(f"检测项目类型失败: {e}")
            return "unknown"
    
    def check_build_status(self, build_type: str = "check") -> Dict:
        """
        检查构建状态

        Args:
            build_type: 构建类型 ('check', 'build', 'lint', 'smart')

        Returns:
            构建结果信息
        """
        print(f"🔍 检查前端项目构建状态: {self.project_path}")
        print(f"📦 项目类型: {self.project_type}")

        try:
            # 智能检测模式：先检查开发服务器状态
            if build_type == "smart":
                return self._run_smart_check()
            elif build_type == "check":
                return self._run_type_check()
            elif build_type == "build":
                return self._run_build()
            elif build_type == "lint":
                return self._run_lint()
            else:
                return self._run_smart_check()  # 默认使用智能检测

        except Exception as e:
            return {
                'success': False,
                'error_type': 'exception',
                'message': f'检查过程异常: {str(e)}',
                'errors': [],
                'warnings': []
            }
    
    def _run_type_check(self) -> Dict:
        """运行类型检查（不生成文件，只检查错误）"""
        commands = self._get_check_commands()
        
        for cmd_info in commands:
            print(f"🔨 执行命令: {' '.join(cmd_info['cmd'])}")
            
            start_time = time.time()
            result = subprocess.run(
                cmd_info['cmd'],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                timeout=120,
                shell=True if os.name == 'nt' else False
            )
            
            build_time = time.time() - start_time
            self.last_build_time = build_time
            
            # 解析结果
            parsed_result = self._parse_output(
                result.stdout, 
                result.stderr, 
                result.returncode,
                cmd_info['type']
            )
            
            parsed_result['build_time'] = build_time
            parsed_result['command'] = ' '.join(cmd_info['cmd'])
            
            # 如果这个命令成功了，返回结果
            if parsed_result['success']:
                return parsed_result
            
            # 如果是关键命令失败，也返回结果
            if cmd_info.get('critical', False):
                return parsed_result
        
        # 所有命令都尝试过了
        return {
            'success': False,
            'message': '无法找到合适的检查命令',
            'errors': [],
            'warnings': [],
            'build_time': 0
        }
    
    def _get_check_commands(self) -> List[Dict]:
        """获取检查命令列表"""
        commands = []

        if self.project_type == 'vue':
            commands.extend([
                # 优先使用轻量级检查，避免与开发服务器冲突
                {'cmd': ['vue-tsc', '--noEmit'], 'type': 'typescript', 'critical': False},
                {'cmd': ['npm', 'run', 'type-check'], 'type': 'vue', 'critical': False},
                # 使用ESLint进行代码检查
                {'cmd': ['npm', 'run', 'lint', '--', '--no-fix'], 'type': 'eslint', 'critical': False},
                # 最后才尝试构建（可能与开发服务器冲突）
                {'cmd': ['npm', 'run', 'build', '--', '--mode', 'development'], 'type': 'vue', 'critical': True}
            ])
        elif self.project_type == 'react':
            commands.extend([
                {'cmd': ['tsc', '--noEmit'], 'type': 'typescript', 'critical': False},
                {'cmd': ['npm', 'run', 'type-check'], 'type': 'react', 'critical': False},
                {'cmd': ['npm', 'run', 'lint'], 'type': 'eslint', 'critical': False},
                {'cmd': ['npm', 'run', 'build'], 'type': 'react', 'critical': True}
            ])
        elif self.project_type == 'angular':
            commands.extend([
                {'cmd': ['ng', 'build', '--dry-run'], 'type': 'angular', 'critical': False},
                {'cmd': ['ng', 'lint'], 'type': 'angular', 'critical': False},
                {'cmd': ['npm', 'run', 'build'], 'type': 'angular', 'critical': True}
            ])
        elif self.project_type == 'typescript':
            commands.extend([
                {'cmd': ['tsc', '--noEmit'], 'type': 'typescript', 'critical': True},
                {'cmd': ['npm', 'run', 'build'], 'type': 'typescript', 'critical': False}
            ])
        else:
            # 通用Node.js项目
            commands.extend([
                {'cmd': ['npm', 'run', 'lint'], 'type': 'eslint', 'critical': False},
                {'cmd': ['npm', 'run', 'build'], 'type': 'nodejs', 'critical': True},
                {'cmd': ['npm', 'run', 'compile'], 'type': 'nodejs', 'critical': False}
            ])

        return commands

    def _run_smart_check(self) -> Dict:
        """智能检测模式：根据开发服务器状态选择合适的检测方法"""
        print("🧠 使用智能检测模式")

        # 1. 检查是否有开发服务器在运行
        dev_server_running = self._check_dev_server_running()

        if dev_server_running:
            print("⚠️ 检测到开发服务器正在运行，使用轻量级检查")
            return self._run_lightweight_check()
        else:
            print("✅ 开发服务器未运行，可以进行完整检查")
            return self._run_type_check()

    def _check_dev_server_running(self) -> bool:
        """检查开发服务器是否在运行"""
        import socket

        # 常见的开发服务器端口
        common_ports = [3000, 8080, 4200, 5173, 8000, 3001, 8081]

        for port in common_ports:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(1)
                    result = sock.connect_ex(('localhost', port))
                    if result == 0:
                        print(f"🌐 检测到端口 {port} 有服务运行")
                        return True
            except:
                continue

        return False

    def _run_lightweight_check(self) -> Dict:
        """轻量级检查：只进行类型检查和语法检查，不进行构建"""
        print("🪶 执行轻量级检查")

        lightweight_commands = []

        if self.project_type == 'vue':
            lightweight_commands = [
                {'cmd': ['vue-tsc', '--noEmit'], 'type': 'typescript', 'critical': True},
                {'cmd': ['npm', 'run', 'lint', '--', '--no-fix'], 'type': 'eslint', 'critical': False}
            ]
        elif self.project_type == 'react':
            lightweight_commands = [
                {'cmd': ['tsc', '--noEmit'], 'type': 'typescript', 'critical': True},
                {'cmd': ['npm', 'run', 'lint'], 'type': 'eslint', 'critical': False}
            ]
        elif self.project_type == 'typescript':
            lightweight_commands = [
                {'cmd': ['tsc', '--noEmit'], 'type': 'typescript', 'critical': True}
            ]
        else:
            # 对于其他项目类型，尝试基本的语法检查
            lightweight_commands = [
                {'cmd': ['npm', 'run', 'lint'], 'type': 'eslint', 'critical': True}
            ]

        # 执行轻量级命令
        for cmd_info in lightweight_commands:
            print(f"🔨 执行轻量级命令: {' '.join(cmd_info['cmd'])}")

            start_time = time.time()
            try:
                result = subprocess.run(
                    cmd_info['cmd'],
                    cwd=self.project_path,
                    capture_output=True,
                    text=True,
                    timeout=60,  # 轻量级检查超时时间更短
                    shell=True if os.name == 'nt' else False
                )

                build_time = time.time() - start_time
                self.last_build_time = build_time

                # 解析结果
                parsed_result = self._parse_output(
                    result.stdout,
                    result.stderr,
                    result.returncode,
                    cmd_info['type']
                )

                parsed_result['build_time'] = build_time
                parsed_result['command'] = ' '.join(cmd_info['cmd'])
                parsed_result['check_mode'] = 'lightweight'

                # 如果关键命令成功，返回结果
                if parsed_result['success'] or cmd_info.get('critical', False):
                    return parsed_result

            except subprocess.TimeoutExpired:
                print(f"⏰ 轻量级命令超时: {' '.join(cmd_info['cmd'])}")
                continue
            except FileNotFoundError:
                print(f"❌ 命令未找到: {' '.join(cmd_info['cmd'])}")
                continue
            except Exception as e:
                print(f"❌ 执行命令异常: {e}")
                continue

        # 如果所有轻量级命令都失败，返回基本成功状态
        return {
            'success': True,
            'message': '轻量级检查完成（开发服务器运行中）',
            'errors': [],
            'warnings': [],
            'build_time': 0,
            'check_mode': 'lightweight_fallback'
        }

    def _run_build(self) -> Dict:
        """运行完整构建"""
        if self.project_type == 'vue':
            cmd = ['npm', 'run', 'build']
        elif self.project_type == 'react':
            cmd = ['npm', 'run', 'build']
        elif self.project_type == 'angular':
            cmd = ['ng', 'build']
        else:
            cmd = ['npm', 'run', 'build']
        
        print(f"🔨 执行构建命令: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(
            cmd,
            cwd=self.project_path,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            shell=True if os.name == 'nt' else False
        )
        
        build_time = time.time() - start_time
        self.last_build_time = build_time
        
        parsed_result = self._parse_output(
            result.stdout, 
            result.stderr, 
            result.returncode,
            'build'
        )
        
        parsed_result['build_time'] = build_time
        parsed_result['command'] = ' '.join(cmd)
        
        return parsed_result
    
    def _run_lint(self) -> Dict:
        """运行代码检查"""
        cmd = ['npm', 'run', 'lint']
        
        print(f"🔍 执行代码检查: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(
            cmd,
            cwd=self.project_path,
            capture_output=True,
            text=True,
            timeout=120,
            shell=True if os.name == 'nt' else False
        )
        
        build_time = time.time() - start_time
        
        parsed_result = self._parse_output(
            result.stdout, 
            result.stderr, 
            result.returncode,
            'lint'
        )
        
        parsed_result['build_time'] = build_time
        parsed_result['command'] = ' '.join(cmd)
        
        return parsed_result
    
    def _parse_output(self, stdout: str, stderr: str, return_code: int, cmd_type: str) -> Dict:
        """解析命令输出"""
        full_output = stdout + stderr
        
        # 提取错误和警告
        errors = self._extract_errors(full_output, cmd_type)
        warnings = self._extract_warnings(full_output, cmd_type)
        
        return {
            'success': return_code == 0 and len(errors) == 0,
            'return_code': return_code,
            'errors': errors,
            'warnings': warnings,
            'error_count': len(errors),
            'warning_count': len(warnings),
            'raw_output': full_output,
            'project_type': self.project_type,
            'command_type': cmd_type
        }
    
    def _extract_errors(self, output: str, cmd_type: str) -> List[Dict]:
        """提取错误信息"""
        errors = []
        
        # TypeScript错误模式
        ts_error_pattern = r'(.+?)\((\d+),(\d+)\):\s*error\s+TS(\d+):\s*(.+)'
        
        # ESLint错误模式
        eslint_error_pattern = r'(.+?):(\d+):(\d+):\s*error\s+(.+)'
        
        # Vue错误模式
        vue_error_pattern = r'ERROR\s+in\s+(.+?)\((\d+),(\d+)\):\s*(.+)'
        
        patterns = [
            ('typescript', ts_error_pattern),
            ('eslint', eslint_error_pattern),
            ('vue', vue_error_pattern)
        ]
        
        for line in output.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            for pattern_type, pattern in patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    if pattern_type == 'typescript':
                        errors.append({
                            'file': match.group(1).strip(),
                            'line': int(match.group(2)),
                            'column': int(match.group(3)),
                            'code': f'TS{match.group(4)}',
                            'message': match.group(5).strip(),
                            'severity': 'error',
                            'type': 'typescript',
                            'raw_line': line
                        })
                    elif pattern_type == 'eslint':
                        errors.append({
                            'file': match.group(1).strip(),
                            'line': int(match.group(2)),
                            'column': int(match.group(3)),
                            'code': 'ESLINT',
                            'message': match.group(4).strip(),
                            'severity': 'error',
                            'type': 'eslint',
                            'raw_line': line
                        })
                    elif pattern_type == 'vue':
                        errors.append({
                            'file': match.group(1).strip(),
                            'line': int(match.group(2)),
                            'column': int(match.group(3)),
                            'code': 'VUE',
                            'message': match.group(4).strip(),
                            'severity': 'error',
                            'type': 'vue',
                            'raw_line': line
                        })
                    break
        
        return errors
    
    def _extract_warnings(self, output: str, cmd_type: str) -> List[Dict]:
        """提取警告信息"""
        warnings = []
        
        # TypeScript警告模式
        ts_warning_pattern = r'(.+?)\((\d+),(\d+)\):\s*warning\s+TS(\d+):\s*(.+)'
        
        # ESLint警告模式
        eslint_warning_pattern = r'(.+?):(\d+):(\d+):\s*warning\s+(.+)'
        
        patterns = [
            ('typescript', ts_warning_pattern),
            ('eslint', eslint_warning_pattern)
        ]
        
        for line in output.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            for pattern_type, pattern in patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    if pattern_type == 'typescript':
                        warnings.append({
                            'file': match.group(1).strip(),
                            'line': int(match.group(2)),
                            'column': int(match.group(3)),
                            'code': f'TS{match.group(4)}',
                            'message': match.group(5).strip(),
                            'severity': 'warning',
                            'type': 'typescript',
                            'raw_line': line
                        })
                    elif pattern_type == 'eslint':
                        warnings.append({
                            'file': match.group(1).strip(),
                            'line': int(match.group(2)),
                            'column': int(match.group(3)),
                            'code': 'ESLINT',
                            'message': match.group(4).strip(),
                            'severity': 'warning',
                            'type': 'eslint',
                            'raw_line': line
                        })
                    break
        
        return warnings

def test_frontend_build_checker():
    """测试前端构建检测器"""
    # 这里需要根据实际项目路径调整
    test_paths = [
        r"D:\Projects\ProjectManagement\Frontend",  # 假设的前端项目路径
        r"D:\Projects\ProjectManagement\Web",
        r"."  # 当前目录
    ]
    
    for project_path in test_paths:
        if Path(project_path).exists() and (Path(project_path) / "package.json").exists():
            print(f"🧪 测试前端构建检测: {project_path}")
            
            checker = FrontendBuildChecker(project_path)
            result = checker.check_build_status("check")
            
            print(f"✅ 项目类型: {checker.project_type}")
            print(f"✅ 检查结果: {'成功' if result['success'] else '失败'}")
            print(f"✅ 错误数量: {result['error_count']}")
            print(f"✅ 警告数量: {result['warning_count']}")
            print(f"✅ 检查时间: {result.get('build_time', 0):.2f}秒")
            
            if result['errors']:
                print("❌ 错误详情:")
                for error in result['errors'][:3]:  # 只显示前3个错误
                    print(f"   {error['file']}({error['line']},{error['column']}): {error['message']}")
            
            break
    else:
        print("⚠️ 未找到前端项目进行测试")

if __name__ == "__main__":
    test_frontend_build_checker()
