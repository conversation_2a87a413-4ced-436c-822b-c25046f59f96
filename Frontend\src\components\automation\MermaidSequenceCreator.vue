<template>
  <div class="mermaid-sequence-creator">
    <!-- 创建模式选择 -->
    <div class="creation-mode-tabs">
      <el-tabs v-model="activeMode" @tab-change="handleModeChange">
        <el-tab-pane label="AI 自然语言" name="ai">
          <el-icon><ChatDotRound /></el-icon>
          AI 智能生成
        </el-tab-pane>
        <el-tab-pane label="可视化编辑" name="visual">
          <el-icon><Share /></el-icon>
          流程图编辑
        </el-tab-pane>
        <el-tab-pane label="代码编辑器" name="code">
          <el-icon><DocumentCopy /></el-icon>
          编程语言
        </el-tab-pane>
        <!-- 隐藏传统表单tab -->
        <!--
        <el-tab-pane label="传统表单" name="form">
          <el-icon><List /></el-icon>
          表单编辑
        </el-tab-pane>
        -->
      </el-tabs>
    </div>

    <!-- AI 自然语言模式 -->
    <div v-if="activeMode === 'ai'" class="ai-mode">
      <div class="ai-input-section">
        <div class="section-header">
          <h3>描述你想要的自动化流程</h3>
          <p>用自然语言描述你的自动化需求，AI 将为你生成对应的流程图和序列</p>
        </div>

        <el-form :model="aiForm" label-width="100px">
          <el-form-item label="流程名称">
            <el-input
              v-model="aiForm.name"
              placeholder="例如：登录并发送消息的自动化流程"
              maxlength="100"
            />
          </el-form-item>

          <el-form-item label="流程描述">
            <el-input
              v-model="aiForm.description"
              type="textarea"
              :rows="6"
              placeholder="详细描述你想要的自动化流程，例如：
1. 首先打开登录页面
2. 输入用户名和密码
3. 点击登录按钮
4. 如果登录失败，重试最多3次
5. 登录成功后，导航到消息页面
6. 输入消息内容并发送
7. 验证消息发送成功"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="技术栈">
            <el-select
              v-model="aiForm.techStack"
              placeholder="选择目标应用的技术栈"
              style="width: 100%"
            >
              <el-option label="Web 应用 (HTML/CSS/JS)" value="web" />
              <el-option label="桌面应用 (WPF/WinForms)" value="desktop" />
              <el-option label="移动应用 (Android/iOS)" value="mobile" />
              <el-option label="VS Code 插件" value="vscode" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="aiGenerating"
              @click="generateFromAI"
              style="width: 100%"
            >
              <el-icon><MagicStick /></el-icon>
              AI 生成流程图
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- AI 生成结果 -->
      <div v-if="aiGeneratedMermaid" class="ai-result-section">
        <div class="section-header">
          <h3>AI 生成的流程图</h3>
          <div class="result-actions">
            <el-button size="small" @click="editGeneratedMermaid">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" @click="regenerateFromAI">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
          </div>
        </div>

        <div class="mermaid-preview">
          <div ref="aiMermaidRef" class="mermaid-diagram" v-html="aiRenderedSvg"></div>
        </div>

        <el-button
          type="success"
          size="large"
          @click="acceptAIGeneration"
          style="width: 100%; margin-top: 16px"
        >
          <el-icon><Check /></el-icon>
          使用此流程图创建序列
        </el-button>
      </div>
    </div>

    <!-- 可视化编辑模式 -->
    <div v-if="activeMode === 'visual'" class="visual-mode">
      <div class="visual-editor">
        <div class="editor-toolbar">
          <div class="toolbar-section">
            <span class="toolbar-label">添加节点：</span>
            <el-button-group>
              <el-button size="small" @click="addNode('basic')">
                <el-icon><Plus /></el-icon>
                基础操作
              </el-button>
              <el-button size="small" @click="addNode('condition')">
                <el-icon><Share /></el-icon>
                条件判断
              </el-button>
              <el-button size="small" @click="addNode('loop')">
                <el-icon><Refresh /></el-icon>
                循环
              </el-button>
            </el-button-group>
          </div>

          <div class="toolbar-section">
            <el-button size="small" @click="previewMermaid">
              <el-icon><View /></el-icon>
              预览
            </el-button>
            <el-button size="small" @click="clearDiagram">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </div>
        </div>

        <div class="editor-content">
          <div class="mermaid-code-editor">
            <div class="editor-header">
              <span>Mermaid 代码</span>
              <el-button size="small" text @click="showMermaidHelp">
                <el-icon><QuestionFilled /></el-icon>
                语法帮助
              </el-button>
            </div>
            <el-input
              v-model="visualMermaidCode"
              type="textarea"
              :rows="15"
              placeholder="在这里编辑 Mermaid 流程图代码..."
              @input="debouncedRenderMermaid"
            />
          </div>

          <div class="mermaid-preview">
            <div class="preview-header">
              <span>实时预览</span>
              <el-button size="small" text @click="refreshPreview">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="preview-container">
              <div v-if="visualRenderError" class="render-error">
                <el-icon><Warning /></el-icon>
                <span>{{ visualRenderError }}</span>
              </div>
              <div v-else ref="visualMermaidRef" class="mermaid-diagram" v-html="visualRenderedSvg"></div>
            </div>
          </div>
        </div>

        <el-button
          type="primary"
          size="large"
          :disabled="!visualMermaidCode || !!visualRenderError"
          @click="() => convertMermaidToSequence()"
          style="width: 100%; margin-top: 16px"
        >
          <el-icon><MagicStick /></el-icon>
          转换为序列步骤
        </el-button>
      </div>
    </div>

    <!-- 代码编辑器模式 -->
    <div v-if="activeMode === 'code'" class="code-mode">
      <CodeSequenceCreator
        :is-parent-maximized="props.isMaximized"
        :editing-sequence="props.editingSequence"
        @sequence-created="handleCodeSequenceCreated"
        @toggle-maximize="emit('toggle-maximize')"
      />
    </div>

    <!-- 隐藏传统表单模式 -->
    <!-- 传统表单模式已完全移除 -->

    <!-- Mermaid 语法帮助对话框 -->
    <el-dialog
      v-model="showHelp"
      title="Mermaid 流程图语法帮助"
      width="800px"
    >
      <div class="help-content">
        <h4>基本语法</h4>
        <pre><code>flowchart TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作]
    B -->|否| D[其他操作]
    C --> E[结束]
    D --> E</code></pre>

        <h4>节点形状</h4>
        <ul>
          <li><code>[文本]</code> - 矩形（基础操作）</li>
          <li><code>{文本}</code> - 菱形（条件判断）</li>
          <li><code>[[文本]]</code> - 子程序（循环）</li>
          <li><code>>文本]</code> - 旗帜（跳转/退出）</li>
          <li><code>((文本))</code> - 圆形（开始/结束）</li>
        </ul>

        <h4>连接线</h4>
        <ul>
          <li><code>--></code> - 普通连接</li>
          <li><code>-->|标签|</code> - 带标签连接</li>
          <li><code>-.-></code> - 虚线连接</li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ChatDotRound, Share, MagicStick, Edit, Refresh, Check, Plus, View, Delete,
  QuestionFilled, Warning, DocumentCopy
} from '@element-plus/icons-vue'
import mermaid from 'mermaid'
import { debounce } from 'lodash-es'
import { AIApiService } from '@/services/api'
import CodeSequenceCreator from './CodeSequenceCreator.vue'

// Props
interface Props {
  modelValue: boolean
  initialMode?: 'ai' | 'visual' | 'code'
  isMaximized?: boolean
  editingSequence?: any
}

const props = withDefaults(defineProps<Props>(), {
  initialMode: 'ai',
  isMaximized: false,
  editingSequence: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'sequence-created': [sequence: any]
  'toggle-maximize': []
}>()

// 响应式数据
const activeMode = ref<'ai' | 'visual' | 'code'>(props.initialMode)
const showHelp = ref(false)

// AI 模式数据
const aiGenerating = ref(false)
const aiForm = reactive({
  name: '',
  description: '',
  techStack: 'web'
})
const aiGeneratedMermaid = ref('')
const aiRenderedSvg = ref('')
const aiMermaidRef = ref<HTMLElement>()

// 可视化模式数据
const visualMermaidCode = ref(`flowchart TD
    Start([开始]) --> Step1[步骤1]
    Step1 --> Step2{条件判断}
    Step2 -->|是| Step3[执行操作]
    Step2 -->|否| Step4[其他操作]
    Step3 --> End([结束])
    Step4 --> End`)
const visualRenderedSvg = ref('')
const visualRenderError = ref('')
const visualMermaidRef = ref<HTMLElement>()

// 生命周期
onMounted(() => {
  initMermaid()
  if (activeMode.value === 'visual') {
    renderVisualMermaid()
  }
})

// 初始化 Mermaid
const initMermaid = () => {
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: 'basis'
    },
    securityLevel: 'loose'
  })
}

// AI 生成流程图
const generateFromAI = async () => {
  if (!aiForm.description.trim()) {
    ElMessage.warning('请输入流程描述')
    return
  }

  try {
    aiGenerating.value = true

    // 调用 AI 服务生成 Mermaid 代码
    const result = await AIApiService.post('/api/AI/generate-mermaid-sequence', {
      name: aiForm.name,
      description: aiForm.description,
      techStack: aiForm.techStack
    })

    aiGeneratedMermaid.value = (result as any).mermaidCode

    // 渲染生成的 Mermaid 代码
    await renderAIMermaid()

    ElMessage.success('AI 流程图生成成功')
  } catch (error) {
    console.error('AI 生成失败:', error)
    ElMessage.error('AI 生成失败，请重试')
  } finally {
    aiGenerating.value = false
  }
}

// 渲染 AI 生成的 Mermaid
const renderAIMermaid = async () => {
  if (!aiGeneratedMermaid.value) return

  try {
    const id = `ai-mermaid-${Date.now()}`
    const { svg } = await mermaid.render(id, aiGeneratedMermaid.value)
    aiRenderedSvg.value = svg
  } catch (error) {
    console.error('渲染 AI Mermaid 失败:', error)
    ElMessage.error('流程图渲染失败')
  }
}

// 渲染可视化编辑的 Mermaid
const renderVisualMermaid = async () => {
  if (!visualMermaidCode.value) return

  try {
    visualRenderError.value = ''
    const id = `visual-mermaid-${Date.now()}`
    const { svg } = await mermaid.render(id, visualMermaidCode.value)
    visualRenderedSvg.value = svg
  } catch (error) {
    console.error('渲染可视化 Mermaid 失败:', error)
    visualRenderError.value = '语法错误，请检查 Mermaid 代码'
    visualRenderedSvg.value = ''
  }
}

// 防抖渲染
const debouncedRenderMermaid = debounce(renderVisualMermaid, 500)

// 其他方法
const handleModeChange = (mode: string | number) => {
  activeMode.value = mode as 'ai' | 'visual' | 'code'
  if (mode === 'visual') {
    nextTick(() => renderVisualMermaid())
  }
}

const editGeneratedMermaid = () => {
  visualMermaidCode.value = aiGeneratedMermaid.value
  activeMode.value = 'visual'
  nextTick(() => renderVisualMermaid())
}

const regenerateFromAI = () => {
  aiGeneratedMermaid.value = ''
  aiRenderedSvg.value = ''
  generateFromAI()
}

const acceptAIGeneration = () => {
  convertMermaidToSequence(aiGeneratedMermaid.value)
}

// 处理代码序列创建
const handleCodeSequenceCreated = (sequenceData: any) => {
  emit('sequence-created', sequenceData)
}

const addNode = (type: string) => {
  // 添加节点的逻辑
  let nodeCode = ''
  const nodeId = `Node${Date.now()}`

  switch (type) {
    case 'basic':
      nodeCode = `\n    ${nodeId}[新步骤] --> End`
      break
    case 'condition':
      nodeCode = `\n    ${nodeId}{条件判断} -->|是| End\n    ${nodeId} -->|否| End`
      break
    case 'loop':
      nodeCode = `\n    ${nodeId}[[循环开始]] --> End`
      break
  }

  visualMermaidCode.value += nodeCode
  debouncedRenderMermaid()
}

const previewMermaid = () => {
  renderVisualMermaid()
}

const clearDiagram = () => {
  visualMermaidCode.value = 'flowchart TD\n    Start([开始]) --> End([结束])'
  renderVisualMermaid()
}

const refreshPreview = () => {
  renderVisualMermaid()
}

const showMermaidHelp = () => {
  showHelp.value = true
}

const convertMermaidToSequence = async (mermaidCode?: string) => {
  const code = mermaidCode || visualMermaidCode.value

  try {
    // 调用后端 API 将 Mermaid 代码转换为序列步骤
    const sequence = await AIApiService.post('/api/AI/convert-mermaid-to-sequence', {
      mermaidCode: code,
      name: aiForm.name || '新序列',
      description: aiForm.description || '通过流程图创建的序列'
    })

    emit('sequence-created', sequence)
    ElMessage.success('序列创建成功')
  } catch (error) {
    console.error('转换序列失败:', error)
    ElMessage.error('转换序列失败，请重试')
  }
}
</script>

<style scoped lang="scss">
.mermaid-sequence-creator {
  height: 100%;
  max-height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--el-fill-color-lighter);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-darker);
    border-radius: 4px;

    &:hover {
      background: var(--el-text-color-placeholder);
    }
  }

  .creation-mode-tabs {
    margin-bottom: 24px;
    flex-shrink: 0;

    :deep(.el-tabs__item) {
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }

  .ai-mode {
    flex: 1;
    display: flex;
    flex-direction: column;

    .ai-input-section {
      margin-bottom: 24px;
      flex-shrink: 0;

      .section-header {
        margin-bottom: 16px;

        h3 {
          margin: 0 0 8px 0;
          color: #303133;
        }

        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }

    .ai-result-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          color: #303133;
        }

        .result-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .visual-mode {
    flex: 1;
    display: flex;
    flex-direction: column;

    .visual-editor {
      flex: 1;
      display: flex;
      flex-direction: column;

      .editor-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 16px;

        .toolbar-section {
          display: flex;
          align-items: center;
          gap: 12px;

          .toolbar-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .editor-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;

        .mermaid-code-editor,
        .mermaid-preview {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          overflow: hidden;

          .editor-header,
          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
            font-size: 14px;
            font-weight: 500;
          }

          .preview-container {
            padding: 16px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;

            .render-error {
              display: flex;
              align-items: center;
              gap: 8px;
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .mermaid-preview {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    background-color: #ffffff;

    .mermaid-diagram {
      display: flex;
      justify-content: center;

      :deep(svg) {
        max-width: 100%;
        height: auto;
      }
    }
  }

  .code-mode {
    flex: 1;
    min-height: 750px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: visible;
    display: flex;
    flex-direction: column;
  }

  .form-mode {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .help-content {
    h4 {
      margin: 16px 0 8px 0;
      color: #303133;
    }

    pre {
      background-color: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
    }

    ul {
      margin: 8px 0;
      padding-left: 20px;

      li {
        margin: 4px 0;

        code {
          background-color: #f8f9fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }
}
</style>
