using SqlSugar;
using System.Text.Json;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 文档向量表
    /// </summary>
    [SugarTable("DocumentVectors")]
    public class DocumentVectorEntity : BaseEntity
    {
        /// <summary>
        /// 文档ID
        /// </summary>
        [SugarColumn(ColumnDescription = "文档ID", Length = 100, IsNullable = false)]
        public string DocumentId { get; set; } = string.Empty;

        /// <summary>
        /// 文档分块索引
        /// </summary>
        [SugarColumn(ColumnDescription = "文档分块索引", IsNullable = false)]
        public int ChunkIndex { get; set; }

        /// <summary>
        /// 文档内容
        /// </summary>
        [SugarColumn(ColumnDescription = "文档内容", ColumnDataType = "TEXT", IsNullable = false)]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 向量嵌入（JSON格式存储）
        /// </summary>
        [SugarColumn(ColumnDescription = "向量嵌入", ColumnDataType = "TEXT", IsNullable = false)]
        public string EmbeddingJson { get; set; } = string.Empty;

        /// <summary>
        /// 向量嵌入（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public float[] Embedding
        {
            get
            {
                if (string.IsNullOrEmpty(EmbeddingJson))
                    return Array.Empty<float>();

                try
                {
                    return JsonSerializer.Deserialize<float[]>(EmbeddingJson) ?? Array.Empty<float>();
                }
                catch
                {
                    return Array.Empty<float>();
                }
            }
            set
            {
                EmbeddingJson = JsonSerializer.Serialize(value);
            }
        }

        /// <summary>
        /// 相似度分数（查询时使用，不存储）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public float SimilarityScore { get; set; }

        /// <summary>
        /// 元数据（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDescription = "元数据", ColumnDataType = "TEXT", IsNullable = true)]
        public string? MetadataJson { get; set; }

        /// <summary>
        /// 元数据（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, object> Metadata
        {
            get
            {
                if (string.IsNullOrEmpty(MetadataJson))
                    return new Dictionary<string, object>();

                try
                {
                    return JsonSerializer.Deserialize<Dictionary<string, object>>(MetadataJson) 
                           ?? new Dictionary<string, object>();
                }
                catch
                {
                    return new Dictionary<string, object>();
                }
            }
            set
            {
                MetadataJson = JsonSerializer.Serialize(value);
            }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
