<template>
  <el-dialog
    v-model="dialogVisible"
    title="自定义细分开发步骤"
    width="70%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="step" class="custom-decompose-container">
      <!-- 原步骤信息 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>原步骤信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="步骤名称">{{ step.stepName }}</el-descriptions-item>
          <el-descriptions-item label="步骤类型">{{ step.stepType }}</el-descriptions-item>
          <el-descriptions-item label="优先级">{{ step.priority }}</el-descriptions-item>
          <el-descriptions-item label="技术栈">{{ step.technologyStack || '未指定' }}</el-descriptions-item>
          <el-descriptions-item label="预估工时">{{ step.estimatedHours || 0 }} 小时</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ step.stepDescription || '无描述' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 自定义细分表单 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>自定义细分配置</span>
        </template>
        
        <el-form :model="customDecomposeForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="细分数量">
                <el-input-number
                  v-model="customDecomposeForm.subStepCount"
                  :min="2"
                  :max="10"
                  placeholder="请输入子步骤数量"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认优先级">
                <el-select v-model="customDecomposeForm.defaultPriority" placeholder="选择默认优先级">
                  <el-option label="低" value="Low" />
                  <el-option label="中" value="Medium" />
                  <el-option label="高" value="High" />
                  <el-option label="紧急" value="Critical" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="默认步骤类型">
                <el-select v-model="customDecomposeForm.defaultStepType" placeholder="选择默认步骤类型">
                  <el-option label="开发" value="Development" />
                  <el-option label="测试" value="Testing" />
                  <el-option label="文档" value="Documentation" />
                  <el-option label="部署" value="Deployment" />
                  <el-option label="审查" value="Review" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="技术栈">
                <el-input
                  v-model="customDecomposeForm.technologyStack"
                  placeholder="继承自父步骤或自定义"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="步骤名称模板">
            <el-input
              v-model="customDecomposeForm.nameTemplate"
              placeholder="例如：{原步骤名称} - 子步骤{序号}"
            />
            <div class="form-tip">
              可用变量：{原步骤名称}、{序号}、{类型}
            </div>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 子步骤列表 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span>子步骤列表</span>
            <el-button size="small" @click="generateSubSteps">
              <el-icon><Refresh /></el-icon>
              生成子步骤
            </el-button>
          </div>
        </template>

        <div v-if="subSteps.length === 0" class="text-center text-gray-500 py-8">
          请先配置细分参数，然后点击"生成子步骤"
        </div>

        <div v-else>
          <el-table :data="subSteps" border>
            <el-table-column prop="stepName" label="步骤名称" min-width="200">
              <template #default="{ row, $index }">
                <el-input v-model="row.stepName" placeholder="请输入步骤名称" />
              </template>
            </el-table-column>

            <el-table-column prop="stepType" label="类型" width="120">
              <template #default="{ row }">
                <el-select v-model="row.stepType" placeholder="选择类型">
                  <el-option label="开发" value="Development" />
                  <el-option label="测试" value="Testing" />
                  <el-option label="文档" value="Documentation" />
                  <el-option label="部署" value="Deployment" />
                  <el-option label="审查" value="Review" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column prop="priority" label="优先级" width="120">
              <template #default="{ row }">
                <el-select v-model="row.priority" placeholder="选择优先级">
                  <el-option label="低" value="Low" />
                  <el-option label="中" value="Medium" />
                  <el-option label="高" value="High" />
                  <el-option label="紧急" value="Critical" />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column prop="estimatedHours" label="预估工时" width="120">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.estimatedHours"
                  :min="0"
                  :max="100"
                  :precision="1"
                  placeholder="工时"
                />
              </template>
            </el-table-column>

            <el-table-column prop="stepDescription" label="描述" min-width="200">
              <template #default="{ row }">
                <el-input
                  v-model="row.stepDescription"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入步骤描述"
                />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100">
              <template #default="{ $index }">
                <el-button
                  size="small"
                  type="danger"
                  @click="removeSubStep($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="mt-4 text-center">
            <el-button @click="addSubStep">
              <el-icon><Plus /></el-icon>
              添加子步骤
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="confirmDecompose"
          :loading="decomposing"
          :disabled="subSteps.length === 0"
        >
          确认细分
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { DevelopmentService } from '@/services/development'
import type { DevelopmentStep } from '@/types/development'

// Props
interface Props {
  modelValue: boolean
  step?: DevelopmentStep
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'decompose-success': [result: any]
}>()

// 响应式数据
const decomposing = ref(false)

// 自定义细分表单
const customDecomposeForm = ref({
  subStepCount: 3,
  defaultPriority: 'Medium',
  defaultStepType: 'Development',
  technologyStack: '',
  nameTemplate: '{原步骤名称} - 子步骤{序号}'
})

// 子步骤列表
const subSteps = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听步骤变化，重置状态
watch(() => props.step, (newStep) => {
  if (newStep) {
    // 重置表单
    customDecomposeForm.value.technologyStack = newStep.technologyStack || ''
    subSteps.value = []
  }
})

// 方法
const handleClose = () => {
  if (!decomposing.value) {
    dialogVisible.value = false
  }
}

const generateSubSteps = () => {
  if (!props.step) return

  const count = customDecomposeForm.value.subStepCount
  const template = customDecomposeForm.value.nameTemplate
  const originalName = props.step.stepName

  subSteps.value = []

  for (let i = 1; i <= count; i++) {
    const stepName = template
      .replace('{原步骤名称}', originalName)
      .replace('{序号}', i.toString())
      .replace('{类型}', customDecomposeForm.value.defaultStepType)

    subSteps.value.push({
      stepName,
      stepType: customDecomposeForm.value.defaultStepType,
      priority: customDecomposeForm.value.defaultPriority,
      estimatedHours: Math.round((props.step.estimatedHours || 8) / count * 10) / 10,
      stepDescription: `${originalName}的子步骤${i}`,
      technologyStack: customDecomposeForm.value.technologyStack || props.step.technologyStack
    })
  }

  ElMessage.success(`已生成 ${count} 个子步骤`)
}

const addSubStep = () => {
  if (!props.step) return

  const index = subSteps.value.length + 1
  subSteps.value.push({
    stepName: `${props.step.stepName} - 子步骤${index}`,
    stepType: customDecomposeForm.value.defaultStepType,
    priority: customDecomposeForm.value.defaultPriority,
    estimatedHours: 1,
    stepDescription: '',
    technologyStack: customDecomposeForm.value.technologyStack || props.step.technologyStack
  })
}

const removeSubStep = (index: number) => {
  subSteps.value.splice(index, 1)
}

const confirmDecompose = async () => {
  if (!props.step || subSteps.value.length === 0) {
    ElMessage.error('请先生成子步骤')
    return
  }

  decomposing.value = true

  try {
    // 调用后端API创建子步骤
    const result = await DevelopmentService.createCustomSubSteps(props.step.id, {
      subSteps: subSteps.value.map((subStep, index) => ({
        ...subStep,
        parentStepId: props.step!.id,
        projectId: props.step!.projectId,
        stepOrder: index + 1,
        stepLevel: (props.step!.stepLevel || 0) + 1
      }))
    })

    if (result.success) {
      ElMessage.success(`自定义细分成功，生成 ${subSteps.value.length} 个子步骤`)
      emit('decompose-success', result.data)
      dialogVisible.value = false
    } else {
      ElMessage.error(result.message || '自定义细分失败')
    }
  } catch (error: any) {
    console.error('自定义细分失败:', error)
    ElMessage.error(error.message || '自定义细分失败')
  } finally {
    decomposing.value = false
  }
}
</script>

<style scoped>
.custom-decompose-container {
  max-height: 70vh;
  overflow-y: auto;
}

.mb-4 {
  margin-bottom: 1rem;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #909399;
}

.py-8 {
  padding: 2rem 0;
}

.mt-4 {
  margin-top: 1rem;
}
</style>
