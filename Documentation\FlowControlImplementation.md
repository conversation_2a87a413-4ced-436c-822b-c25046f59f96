# 模板序列流程控制功能实现说明

## 概述

本文档说明了在 `http://localhost:3000/automation/sequences` 模板序列功能中实现的判断执行和循环执行操作方式。

## 实现的功能

### 1. 新增的动作类型

在原有的基础操作类型基础上，新增了以下流程控制类型：

#### 基础操作类型
- `click` - 点击操作
- `wait` - 等待操作  
- `input` - 输入操作
- `delay` - 延迟操作
- `screenshot` - 截图操作
- `verify` - 验证操作
- `scroll` - 滚动操作
- `key_press` - 按键操作

#### 流程控制类型
- `condition` - 条件判断：根据条件决定是否执行后续步骤
- `loop` - 循环开始：开始循环执行一组步骤
- `loop_end` - 循环结束：标记循环体的结束
- `branch` - 分支执行：根据条件选择不同的执行分支
- `jump` - 跳转：跳转到指定的步骤
- `exit` - 退出：退出序列执行

### 2. 数据库结构扩展

为 `UIAutoMationTemplateSteps` 表添加了以下字段：

```sql
-- 条件表达式字段
ConditionExpression nvarchar(1000) NULL

-- 跳转目标步骤ID字段  
JumpToStepId int NULL

-- 循环次数字段
LoopCount int NULL

-- 循环变量名字段
LoopVariable nvarchar(50) NULL

-- 分组标识字段
GroupId nvarchar(50) NULL
```

### 3. 前端界面增强

#### 步骤表单对话框
- 添加了流程控制字段的配置界面
- 根据动作类型动态显示相关配置项
- 支持条件表达式、循环参数、跳转目标等配置

#### 序列详情面板
- 更新了动作类型的显示标签和颜色
- 支持显示流程控制相关的参数信息

### 4. 后端API扩展

#### DTO类更新
- `TemplateStepDto` - 添加流程控制字段
- `CreateTemplateStepDto` - 添加创建时的流程控制字段
- `UpdateTemplateStepDto` - 添加更新时的流程控制字段

#### 控制器更新
- `TemplateStepController` - 支持流程控制字段的CRUD操作
- `TemplateSequenceController` - 支持序列中步骤的流程控制

### 5. 执行引擎

创建了 `FlowControlExecutionEngine` 类，实现了：

#### 条件判断执行
- 支持JavaScript风格的条件表达式
- 变量替换：使用 `{变量名}` 语法引用变量
- 条件结果处理：根据真假值执行不同的流程

#### 循环执行
- 支持固定次数循环和条件循环
- 循环变量管理：自动维护循环计数器
- 嵌套循环支持：使用栈结构管理循环上下文

#### 跳转控制
- 支持跳转到指定步骤ID
- 支持退出序列执行

#### 变量管理
- 全局变量存储和访问
- 变量在步骤间的传递和更新

## 使用示例

### 1. 条件判断示例

```json
{
  "actionType": "condition",
  "description": "检查截图是否成功",
  "conditionExpression": "{screenshot_taken} === true",
  "parameters": {
    "true_action": "continue",
    "false_action": "skip_to_step",
    "false_target": 5
  }
}
```

### 2. 循环执行示例

```json
{
  "actionType": "loop",
  "description": "循环点击按钮",
  "loopCount": 5,
  "loopVariable": "i",
  "groupId": "main_loop",
  "parameters": {
    "exit_condition": "{counter} >= 10"
  }
}
```

### 3. 跳转示例

```json
{
  "actionType": "jump",
  "description": "跳转到错误处理步骤",
  "jumpToStepId": 10
}
```

## 数据库迁移

执行以下迁移脚本来添加新功能：

1. `Database/03_Migrations/007_AddFlowControlFieldsToTemplateSteps.sql` - 添加流程控制字段
2. `Database/04_InitialData/007_FlowControlTemplateExamples.sql` - 添加示例数据

## 技术特点

### 1. 表达式引擎
- 支持变量替换
- 支持基本的比较操作（===, >=, >）
- 可扩展支持更复杂的表达式

### 2. 流程控制
- 基于状态机的执行模型
- 支持嵌套结构（循环嵌套、条件嵌套）
- 错误处理和异常恢复

### 3. 可扩展性
- 模块化设计，易于添加新的动作类型
- 插件式的执行引擎架构
- 支持自定义变量和参数

## 下一步计划

1. **表达式引擎增强**
   - 集成专业的表达式引擎（如 NCalc）
   - 支持更复杂的数学和逻辑运算

2. **调试功能**
   - 添加断点支持
   - 步骤级调试和变量查看
   - 执行轨迹记录

3. **可视化编辑器**
   - 流程图式的序列编辑界面
   - 拖拽式的步骤组织
   - 实时预览和验证

4. **性能优化**
   - 异步执行支持
   - 并行步骤执行
   - 资源管理和清理

## 总结

通过以上实现，模板序列功能现在支持：

✅ **条件判断执行** - 根据条件表达式决定执行路径  
✅ **循环执行** - 支持固定次数和条件循环  
✅ **跳转控制** - 支持步骤间跳转和序列退出  
✅ **变量管理** - 支持步骤间数据传递  
✅ **分组管理** - 支持逻辑分组和嵌套结构  

这些功能使得模板序列能够处理更复杂的自动化场景，提供了强大的流程控制能力。
