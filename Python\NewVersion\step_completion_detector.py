#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发步骤完成检测器
统一检测C#后端和前端项目的编译状态，判断开发步骤是否完成
"""

import time
from pathlib import Path
from typing import Dict, List, Optional, Union
from enum import Enum

# 导入已有的检测器
from csharp_error_capture import CSharpErrorCapture
from frontend_build_checker import FrontendBuildChecker

class ProjectType(Enum):
    """项目类型枚举"""
    CSHARP = "csharp"
    FRONTEND = "frontend"
    MIXED = "mixed"
    UNKNOWN = "unknown"

class StepCompletionDetector:
    """步骤完成检测器"""
    
    def __init__(self, project_config: Dict):
        """
        初始化检测器
        
        Args:
            project_config: 项目配置
            {
                'csharp_projects': ['path1', 'path2'],  # C#项目路径列表
                'frontend_projects': ['path1', 'path2'],  # 前端项目路径列表
                'detection_mode': 'strict',  # 检测模式: strict/loose
                'timeout': 300,  # 超时时间（秒）
                'retry_interval': 5,  # 重试间隔（秒）
                'max_retries': 10  # 最大重试次数
            }
        """
        self.config = project_config
        self.csharp_checkers = {}
        self.frontend_checkers = {}
        
        # 初始化C#检测器
        for project_path in self.config.get('csharp_projects', []):
            if Path(project_path).exists():
                self.csharp_checkers[project_path] = CSharpErrorCapture(project_path)
        
        # 初始化前端检测器
        for project_path in self.config.get('frontend_projects', []):
            if Path(project_path).exists():
                self.frontend_checkers[project_path] = FrontendBuildChecker(project_path)
    
    def detect_step_completion(self, step_info: Dict) -> Dict:
        """
        检测步骤是否完成
        
        Args:
            step_info: 步骤信息
            {
                'step_id': 'step_001',
                'step_name': '创建用户模型',
                'target_projects': ['backend', 'frontend'],  # 目标项目
                'completion_criteria': {
                    'compile_success': True,  # 编译成功
                    'no_errors': True,  # 无错误
                    'allow_warnings': True,  # 允许警告
                    'custom_checks': []  # 自定义检查
                }
            }
            
        Returns:
            检测结果
        """
        print(f"🔍 开始检测步骤完成状态: {step_info.get('step_name', 'Unknown')}")
        
        start_time = time.time()
        max_wait_time = self.config.get('timeout', 300)
        retry_interval = self.config.get('retry_interval', 5)
        max_retries = self.config.get('max_retries', 10)
        
        retry_count = 0
        
        while retry_count < max_retries and (time.time() - start_time) < max_wait_time:
            print(f"🔄 第 {retry_count + 1} 次检测...")
            
            # 执行检测
            detection_result = self._perform_detection(step_info)
            
            if detection_result['completed']:
                print(f"✅ 步骤完成检测成功!")
                return detection_result
            
            # 如果未完成，等待后重试
            print(f"⏳ 步骤未完成，{retry_interval}秒后重试...")
            print(f"   原因: {detection_result.get('reason', '未知')}")
            
            time.sleep(retry_interval)
            retry_count += 1
        
        # 超时或达到最大重试次数
        print(f"⏰ 检测超时或达到最大重试次数")
        return {
            'completed': False,
            'reason': 'timeout_or_max_retries',
            'retry_count': retry_count,
            'elapsed_time': time.time() - start_time,
            'details': {}
        }
    
    def _perform_detection(self, step_info: Dict) -> Dict:
        """执行一次检测"""
        criteria = step_info.get('completion_criteria', {})
        target_projects = step_info.get('target_projects', [])
        
        detection_results = {
            'csharp_results': {},
            'frontend_results': {},
            'overall_success': True,
            'error_count': 0,
            'warning_count': 0,
            'failed_projects': []
        }
        
        # 检测C#项目
        if 'backend' in target_projects or 'csharp' in target_projects:
            csharp_results = self._check_csharp_projects(criteria)
            detection_results['csharp_results'] = csharp_results
            
            if not csharp_results['success']:
                detection_results['overall_success'] = False
                detection_results['failed_projects'].extend(csharp_results['failed_projects'])
            
            detection_results['error_count'] += csharp_results['total_errors']
            detection_results['warning_count'] += csharp_results['total_warnings']
        
        # 检测前端项目
        if 'frontend' in target_projects or 'web' in target_projects:
            frontend_results = self._check_frontend_projects(criteria)
            detection_results['frontend_results'] = frontend_results
            
            if not frontend_results['success']:
                detection_results['overall_success'] = False
                detection_results['failed_projects'].extend(frontend_results['failed_projects'])
            
            detection_results['error_count'] += frontend_results['total_errors']
            detection_results['warning_count'] += frontend_results['total_warnings']
        
        # 判断是否完成
        completed = self._evaluate_completion(detection_results, criteria)
        
        return {
            'completed': completed,
            'reason': self._get_completion_reason(detection_results, criteria),
            'details': detection_results,
            'summary': {
                'total_errors': detection_results['error_count'],
                'total_warnings': detection_results['warning_count'],
                'failed_projects': detection_results['failed_projects']
            }
        }
    
    def _check_csharp_projects(self, criteria: Dict) -> Dict:
        """检查C#项目"""
        results = {
            'success': True,
            'total_errors': 0,
            'total_warnings': 0,
            'failed_projects': [],
            'project_results': {}
        }
        
        for project_path, checker in self.csharp_checkers.items():
            print(f"🔨 检查C#项目: {project_path}")
            
            # 执行编译检查
            compile_result = checker.compile_project(
                output_format='json',
                errors_only=not criteria.get('allow_warnings', True)
            )
            
            project_success = (
                compile_result['success'] and 
                (compile_result['error_count'] == 0 if criteria.get('no_errors', True) else True)
            )
            
            results['project_results'][project_path] = {
                'success': project_success,
                'errors': compile_result['error_count'],
                'warnings': compile_result['warning_count'],
                'compile_time': compile_result['compile_time']
            }
            
            results['total_errors'] += compile_result['error_count']
            results['total_warnings'] += compile_result['warning_count']
            
            if not project_success:
                results['success'] = False
                results['failed_projects'].append(f"C# - {Path(project_path).name}")
        
        return results
    
    def _check_frontend_projects(self, criteria: Dict) -> Dict:
        """检查前端项目"""
        results = {
            'success': True,
            'total_errors': 0,
            'total_warnings': 0,
            'failed_projects': [],
            'project_results': {}
        }
        
        for project_path, checker in self.frontend_checkers.items():
            print(f"🌐 检查前端项目: {project_path}")
            
            # 执行智能检查（会自动判断是否有开发服务器运行）
            check_result = checker.check_build_status("smart")
            
            project_success = (
                check_result['success'] and 
                (check_result['error_count'] == 0 if criteria.get('no_errors', True) else True)
            )
            
            results['project_results'][project_path] = {
                'success': project_success,
                'errors': check_result['error_count'],
                'warnings': check_result['warning_count'],
                'build_time': check_result.get('build_time', 0),
                'project_type': checker.project_type
            }
            
            results['total_errors'] += check_result['error_count']
            results['total_warnings'] += check_result['warning_count']
            
            if not project_success:
                results['success'] = False
                results['failed_projects'].append(f"Frontend - {Path(project_path).name}")
        
        return results
    
    def _evaluate_completion(self, results: Dict, criteria: Dict) -> bool:
        """评估是否完成"""
        # 严格模式：所有项目都必须成功
        if self.config.get('detection_mode', 'strict') == 'strict':
            return results['overall_success']
        
        # 宽松模式：允许一定数量的警告
        else:
            return (
                results['error_count'] == 0 and
                (results['warning_count'] <= criteria.get('max_warnings', 10))
            )
    
    def _get_completion_reason(self, results: Dict, criteria: Dict) -> str:
        """获取完成状态原因"""
        if results['overall_success']:
            return "all_projects_successful"
        
        reasons = []
        
        if results['error_count'] > 0:
            reasons.append(f"{results['error_count']}个编译错误")
        
        if not criteria.get('allow_warnings', True) and results['warning_count'] > 0:
            reasons.append(f"{results['warning_count']}个警告")
        
        if results['failed_projects']:
            reasons.append(f"失败项目: {', '.join(results['failed_projects'])}")
        
        return "; ".join(reasons) if reasons else "未知原因"
    
    def get_project_status_summary(self) -> Dict:
        """获取项目状态摘要"""
        summary = {
            'csharp_projects': len(self.csharp_checkers),
            'frontend_projects': len(self.frontend_checkers),
            'total_projects': len(self.csharp_checkers) + len(self.frontend_checkers)
        }
        
        return summary

def test_step_completion_detector():
    """测试步骤完成检测器"""
    # 配置示例
    config = {
        'csharp_projects': [
            r'D:\Projects\ProjectManagement\Backend\ProjectManagement.API'
        ],
        'frontend_projects': [
            # r'D:\Projects\ProjectManagement\Frontend'  # 如果有前端项目
        ],
        'detection_mode': 'strict',
        'timeout': 60,
        'retry_interval': 3,
        'max_retries': 3
    }
    
    # 步骤信息示例
    step_info = {
        'step_id': 'test_001',
        'step_name': '测试编译检测',
        'target_projects': ['backend'],
        'completion_criteria': {
            'compile_success': True,
            'no_errors': True,
            'allow_warnings': True
        }
    }
    
    print("🧪 测试步骤完成检测器")
    
    detector = StepCompletionDetector(config)
    print(f"📊 项目摘要: {detector.get_project_status_summary()}")
    
    result = detector.detect_step_completion(step_info)
    
    print(f"\n📋 检测结果:")
    print(f"   完成状态: {'✅ 已完成' if result['completed'] else '❌ 未完成'}")
    print(f"   原因: {result.get('reason', '未知')}")
    
    if 'summary' in result:
        summary = result['summary']
        print(f"   总错误数: {summary['total_errors']}")
        print(f"   总警告数: {summary['total_warnings']}")
        if summary['failed_projects']:
            print(f"   失败项目: {', '.join(summary['failed_projects'])}")

if __name__ == "__main__":
    test_step_completion_detector()
