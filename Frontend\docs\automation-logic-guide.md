# 自动化序列逻辑语句使用指南

## 概述

在自动化序列中，您可以使用逻辑语句来实现条件判断、循环控制和流程跳转等高级功能。这些功能让您的自动化序列更加智能和灵活。

## 🚀 快速开始

### 界面操作步骤

1. **访问页面**：打开 `http://localhost:3000/automation/sequences`
2. **创建序列**：点击"创建序列"按钮
3. **添加步骤**：在序列中点击"添加步骤"
4. **选择动作类型**：选择基础的UI操作（点击、输入、等待等）
5. **选择逻辑语句**：可选，选择逻辑控制功能（条件判断、图像检测等）
6. **配置参数**：
   - 填写基本的步骤信息（描述、模板等）
   - 设置条件表达式、循环参数等
   - 为复杂逻辑设置分组标识

### 界面结构说明

#### 动作类型下拉框
包含基础的UI操作动作：
- `click` - 点击操作
- `wait` - 等待操作
- `input` - 输入操作
- `delay` - 延时操作
- `screenshot` - 截图操作
- `verify` - 验证操作
- `scroll` - 滚动操作
- `key_press` - 按键操作

#### 逻辑语句下拉框（可选）
包含逻辑控制功能：
- `condition` - 条件判断
- `image_condition` - 图像条件判断 ⭐
- `region_image_condition` - 区域图像条件判断 ⭐
- `loop` - 循环开始
- `loop_end` - 循环结束
- `branch` - 分支执行
- `jump` - 跳转
- `exit` - 退出

### 使用方式

#### 纯动作步骤
只选择"动作类型"，不选择"逻辑语句"：
```
动作类型: click
逻辑语句: (不选择)
描述: 点击登录按钮
```

#### 带逻辑的步骤
同时选择"动作类型"和"逻辑语句"：
```
动作类型: click
逻辑语句: image_condition
描述: 如果看到登录按钮则点击
```

#### 纯逻辑步骤
可以只选择"逻辑语句"，不选择具体动作：
```
动作类型: wait
逻辑语句: condition
描述: 等待条件满足
```



## 逻辑字段说明

### 1. 条件表达式 (ConditionExpression)

条件表达式用于控制步骤是否执行，支持JavaScript语法。

**语法格式：**
```javascript
{变量名} 操作符 值
```

**示例：**
- `{result} === 'success'` - 检查结果是否为成功
- `{count} > 5` - 检查计数是否大于5
- `{status} !== 'error' && {retry} < 3` - 复合条件
- `{element_found} === true` - 检查元素是否找到
- `{response_time} < 1000` - 检查响应时间是否小于1秒

**支持的操作符：**
- 比较：`===`, `!==`, `>`, `<`, `>=`, `<=`
- 逻辑：`&&`, `||`, `!`
- 包含：`.includes()`, `.startsWith()`, `.endsWith()`

### 2. 跳转目标步骤 (JumpToStepId)

当条件表达式为真时，可以跳转到指定的步骤序号。

**使用场景：**
- 条件满足时跳过某些步骤
- 错误处理时跳转到特定步骤
- 根据结果选择不同的执行路径

### 3. 循环设置

#### 循环次数 (LoopCount)
- 正数：指定循环的确切次数
- `-1`：无限循环，直到满足退出条件
- `0`：不循环

#### 循环变量 (LoopVariable)
循环中使用的变量名，可以在条件表达式中引用。

**示例：**
- `counter` - 计数器变量
- `index` - 索引变量
- `attempt` - 尝试次数变量

### 4. 分组标识 (GroupId)

用于标识逻辑分组，如循环体、条件分支等。

**命名建议：**
- `loop_1`, `loop_2` - 循环分组
- `condition_a`, `condition_b` - 条件分支
- `error_handling` - 错误处理分组

## 实际应用场景

### 场景1：条件点击
```
步骤1: 截图检查按钮是否存在
条件表达式: {button_found} === true
跳转目标: 3

步骤2: 等待按钮出现
步骤3: 点击按钮
```

### 场景2：重试机制
```
步骤1: 尝试操作
条件表达式: {success} === false && {retry_count} < 3
循环次数: 3
循环变量: retry_count
分组标识: retry_loop

步骤2: 等待1秒
步骤3: 重新尝试操作
```

### 场景3：多条件判断
```
步骤1: 检查页面状态
条件表达式: {page_loaded} === true && {error_count} === 0
跳转目标: 5

步骤2: 处理加载错误
步骤3: 刷新页面
步骤4: 等待加载
步骤5: 继续正常流程
```

## 变量系统

### 内置变量
- `{step_result}` - 当前步骤执行结果
- `{execution_time}` - 步骤执行时间
- `{retry_count}` - 当前重试次数
- `{sequence_start_time}` - 序列开始时间

### 自定义变量
您可以在步骤参数中定义自定义变量，然后在条件表达式中使用。

**示例：**
```json
{
  "set_variable": "user_count",
  "value": 10
}
```

然后在条件表达式中使用：`{user_count} > 5`

## 最佳实践

### 1. 条件表达式编写
- 使用清晰的变量名
- 避免过于复杂的表达式
- 添加适当的注释说明

### 2. 循环控制
- 设置合理的循环次数上限
- 使用明确的退出条件
- 避免无限循环

### 3. 错误处理
- 为关键步骤设置条件检查
- 提供备用执行路径
- 记录错误信息用于调试

### 4. 性能优化
- 避免不必要的条件检查
- 合理使用跳转减少执行时间
- 优化循环逻辑

## 调试技巧

1. **使用日志输出**：在关键步骤添加日志输出，记录变量值
2. **分步测试**：先测试简单条件，再组合复杂逻辑
3. **变量监控**：在执行过程中监控变量值的变化
4. **条件验证**：使用浏览器控制台验证JavaScript表达式

## 注意事项

1. 条件表达式必须返回布尔值
2. 变量名区分大小写
3. 跳转目标步骤必须存在
4. 循环嵌套不要超过3层
5. 分组标识在同一序列中应唯一

## 图像条件判断功能

### 1. 基础图像条件 (image_condition)

**功能**：检测图片是否存在，并根据结果执行相应动作。

**配置参数**：
- **条件图像模板**：要检测的图片模板
- **目标图像模板**：条件满足时要操作的图片模板
- **目标动作**：点击/等待/验证
- **条件图像置信度**：图片匹配精确度 (0.1-1.0)
- **目标图像置信度**：目标图片匹配精确度
- **条件类型**：
  - **正向条件**：如果图像存在，则执行动作
  - **反向条件**：如果图像不存在，则执行动作

**使用示例**：
```
动作类型: image_condition
条件图像: "登录按钮"
目标图像: "用户名输入框"
目标动作: "点击"
条件类型: 正向条件
```

### 2. 区域图像条件 (region_image_condition)

**功能**：在指定区域内检测图片是否存在，更加精确。

**配置参数**：
- **区域图像模板**：定义检测区域的大图片
- **目标图像模板**：在区域内要检测的小图片
- **动作图像模板**：条件满足时要操作的图片
- **动作类型**：点击/等待/验证
- **条件类型**：正向/反向条件
- **区域扩展像素**：扩展检测区域范围

**使用示例**：
```
动作类型: region_image_condition
区域图像: "对话框区域"
目标图像: "确定按钮"
动作图像: "确定按钮"
动作类型: "点击"
条件类型: 正向条件
```

### 3. 实际应用场景

#### 场景1：智能等待元素出现
```
步骤1: image_condition
条件图像: "加载动画"
条件类型: 反向条件 (等待加载动画消失)
目标动作: 继续下一步
```

#### 场景2：错误处理
```
步骤1: image_condition
条件图像: "错误提示"
条件类型: 正向条件 (如果有错误)
目标图像: "重试按钮"
目标动作: "点击"
```

#### 场景3：在特定区域操作
```
步骤1: region_image_condition
区域图像: "工具栏区域"
目标图像: "保存图标"
动作图像: "保存按钮"
条件类型: 正向条件
```

### 4. 置信度设置建议

- **高精度** (0.8-1.0)：重要按钮、关键界面元素
- **中等精度** (0.6-0.8)：一般界面元素
- **低精度** (0.3-0.6)：容易变化的元素

通过合理使用这些逻辑语句和图像条件判断，您可以创建出功能强大、智能化的自动化序列。
