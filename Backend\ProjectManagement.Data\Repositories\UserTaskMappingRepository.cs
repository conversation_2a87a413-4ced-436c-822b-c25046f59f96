using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// 用户任务映射仓储实现
/// 功能: 管理用户任务映射的数据访问操作
/// 支持: CRUD操作、用户配置查询、任务类型查询、默认配置管理
/// </summary>
public class UserTaskMappingRepository : IUserTaskMappingRepository
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<UserTaskMappingRepository> _logger;

    public UserTaskMappingRepository(ISqlSugarClient db, ILogger<UserTaskMappingRepository> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 根据ID获取用户任务映射
    /// </summary>
    public async Task<UserTaskMapping?> GetByIdAsync(int id)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.Id == id)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 根据用户ID获取所有任务映射
    /// </summary>
    public async Task<List<UserTaskMapping>> GetByUserIdAsync(int userId)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId)
                .OrderBy(x => x.TaskType)
                .OrderBy(x => x.Priority)
                .ToListAsync()
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 根据用户ID和任务类型获取任务映射
    /// </summary>
    public async Task<List<UserTaskMapping>> GetByUserIdAndTaskTypeAsync(int userId, string taskType)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType)
                .OrderBy(x => x.Priority)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 根据用户ID、任务类型和提供商获取任务映射
    /// </summary>
    public async Task<UserTaskMapping?> GetByUserTaskProviderAsync(int userId, string taskType, string providerName)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.ProviderName == providerName)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射失败: {UserId}, {TaskType}, {ProviderName}", userId, taskType, providerName);
            throw;
        }
    }

    /// <summary>
    /// 获取用户指定任务类型的默认配置
    /// </summary>
    public async Task<UserTaskMapping?> GetDefaultByUserAndTaskTypeAsync(int userId, string taskType)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.IsDefault && x.IsActive)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户默认任务映射失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 获取用户指定任务类型的最高优先级配置
    /// </summary>
    public async Task<UserTaskMapping?> GetHighestPriorityByUserAndTaskTypeAsync(int userId, string taskType)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.IsActive)
                .OrderBy(x => x.Priority)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户最高优先级任务映射失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 根据提供商名称获取所有使用该提供商的任务映射
    /// </summary>
    public async Task<List<UserTaskMapping>> GetByProviderNameAsync(string providerName)
    {
        try
        {
            return await _db.Queryable<UserTaskMapping>()
                .Where(x => x.ProviderName == providerName)
                .OrderBy(x => x.UserId)
                .OrderBy(x => x.TaskType)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据提供商获取任务映射失败: {ProviderName}", providerName);
            throw;
        }
    }

    /// <summary>
    /// 获取所有任务映射（分页）
    /// </summary>
    public async Task<(List<UserTaskMapping> Items, int TotalCount)> GetPagedAsync(int pageIndex, int pageSize)
    {
        try
        {
            var query = _db.Queryable<UserTaskMapping>()
                .OrderBy(x => x.UserId)
                .OrderBy(x => x.TaskType)
                .OrderBy(x => x.Priority);

            var totalCount = await query.CountAsync();
            var items = await query
                .ToPageListAsync(pageIndex, pageSize);

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取任务映射失败: {PageIndex}, {PageSize}", pageIndex, pageSize);
            throw;
        }
    }

    /// <summary>
    /// 创建用户任务映射
    /// </summary>
    public async Task<UserTaskMapping> CreateAsync(UserTaskMapping mapping)
    {
        try
        {
            mapping.CreatedAt = DateTime.UtcNow;
            mapping.UpdatedAt = DateTime.UtcNow;

            // 如果设置为默认配置，需要先取消同类型的其他默认配置
            if (mapping.IsDefault)
            {
                await ClearDefaultAsync(mapping.UserId, mapping.TaskType);
            }

            var result = await _db.Insertable(mapping).ExecuteReturnEntityAsync();
            _logger.LogInformation("创建用户任务映射成功: {UserId}, {TaskType}, {ProviderName}",
                mapping.UserId, mapping.TaskType, mapping.ProviderName);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建用户任务映射失败: {UserId}, {TaskType}, {ProviderName}",
                mapping.UserId, mapping.TaskType, mapping.ProviderName);
            throw;
        }
    }

    /// <summary>
    /// 批量创建用户任务映射
    /// </summary>
    public async Task<List<UserTaskMapping>> CreateBatchAsync(List<UserTaskMapping> mappings)
    {
        try
        {
            var now = DateTime.UtcNow;
            foreach (var mapping in mappings)
            {
                mapping.CreatedAt = now;
                mapping.UpdatedAt = now;
            }

            await _db.Insertable(mappings).ExecuteCommandAsync();
            _logger.LogInformation("批量创建用户任务映射成功: {Count}个", mappings.Count);

            return mappings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建用户任务映射失败: {Count}个", mappings.Count);
            throw;
        }
    }

    /// <summary>
    /// 更新用户任务映射
    /// </summary>
    public async Task<bool> UpdateAsync(UserTaskMapping mapping)
    {
        try
        {
            mapping.UpdatedAt = DateTime.UtcNow;

            // 如果设置为默认配置，需要先取消同类型的其他默认配置
            if (mapping.IsDefault)
            {
                await ClearDefaultAsync(mapping.UserId, mapping.TaskType, mapping.Id);
            }

            var result = await _db.Updateable(mapping).ExecuteCommandAsync();
            _logger.LogInformation("更新用户任务映射成功: {Id}, {UserId}", mapping.Id, mapping.UserId);

            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户任务映射失败: {Id}", mapping.Id);
            throw;
        }
    }

    /// <summary>
    /// 批量更新用户任务映射
    /// </summary>
    public async Task<int> UpdateBatchAsync(List<UserTaskMapping> mappings)
    {
        try
        {
            var now = DateTime.UtcNow;
            foreach (var mapping in mappings)
            {
                mapping.UpdatedAt = now;
            }

            var result = await _db.Updateable(mappings).ExecuteCommandAsync();
            _logger.LogInformation("批量更新用户任务映射成功: {Count}个", mappings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新用户任务映射失败: {Count}个", mappings.Count);
            throw;
        }
    }

    /// <summary>
    /// 删除用户任务映射
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var result = await _db.Deleteable<UserTaskMapping>()
                .Where(x => x.Id == id)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除用户任务映射成功: {Id}", id);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户任务映射失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 删除用户的所有任务映射
    /// </summary>
    public async Task<int> DeleteByUserIdAsync(int userId)
    {
        try
        {
            var result = await _db.Deleteable<UserTaskMapping>()
                .Where(x => x.UserId == userId)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除用户所有任务映射成功: {UserId}, 删除数量: {Count}", userId, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户所有任务映射失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 删除指定任务类型的所有映射
    /// </summary>
    public async Task<int> DeleteByUserAndTaskTypeAsync(int userId, string taskType)
    {
        try
        {
            var result = await _db.Deleteable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType)
                .ExecuteCommandAsync();

            _logger.LogInformation("删除用户指定任务类型映射成功: {UserId}, {TaskType}, 删除数量: {Count}",
                userId, taskType, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除用户指定任务类型映射失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 启用/禁用任务映射
    /// </summary>
    public async Task<bool> ToggleActiveAsync(int id, bool isActive)
    {
        try
        {
            var result = await _db.Updateable<UserTaskMapping>()
                .SetColumns(x => new UserTaskMapping { IsActive = isActive, UpdatedAt = DateTime.UtcNow })
                .Where(x => x.Id == id)
                .ExecuteCommandAsync();

            _logger.LogInformation("切换任务映射状态成功: {Id}, {IsActive}", id, isActive);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换任务映射状态失败: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 设置默认配置
    /// </summary>
    public async Task<bool> SetDefaultAsync(int userId, string taskType, int mappingId)
    {
        try
        {
            // 先清除其他默认配置
            await ClearDefaultAsync(userId, taskType, mappingId);

            // 设置新的默认配置
            var result = await _db.Updateable<UserTaskMapping>()
                .SetColumns(x => new UserTaskMapping { IsDefault = true, UpdatedAt = DateTime.UtcNow })
                .Where(x => x.Id == mappingId && x.UserId == userId && x.TaskType == taskType)
                .ExecuteCommandAsync();

            _logger.LogInformation("设置默认任务映射成功: {UserId}, {TaskType}, {MappingId}",
                userId, taskType, mappingId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认任务映射失败: {UserId}, {TaskType}, {MappingId}",
                userId, taskType, mappingId);
            throw;
        }
    }

    /// <summary>
    /// 清除指定任务类型的默认配置
    /// </summary>
    public async Task<int> ClearDefaultAsync(int userId, string taskType, int? excludeMappingId = null)
    {
        try
        {
            var query = _db.Updateable<UserTaskMapping>()
                .SetColumns(x => new UserTaskMapping { IsDefault = false, UpdatedAt = DateTime.UtcNow })
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.IsDefault);

            if (excludeMappingId.HasValue)
            {
                query = query.Where(x => x.Id != excludeMappingId.Value);
            }

            var result = await query.ExecuteCommandAsync();
            _logger.LogInformation("清除默认任务映射成功: {UserId}, {TaskType}, 清除数量: {Count}",
                userId, taskType, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除默认任务映射失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 检查用户是否已配置指定任务类型的映射
    /// </summary>
    public async Task<bool> HasMappingForTaskTypeAsync(int userId, string taskType)
    {
        try
        {
            var count = await _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.IsActive)
                .CountAsync();

            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查任务映射存在性失败: {UserId}, {TaskType}", userId, taskType);
            throw;
        }
    }

    /// <summary>
    /// 获取用户任务映射统计信息
    /// </summary>
    public async Task<object> GetUserMappingStatsAsync(int userId)
    {
        try
        {
            var mappings = await GetByUserIdAsync(userId);

            var stats = new
            {
                TotalMappings = mappings.Count,
                ActiveMappings = mappings.Count(x => x.IsActive),
                InactiveMappings = mappings.Count(x => !x.IsActive),
                DefaultMappings = mappings.Count(x => x.IsDefault),
                TaskTypeStats = mappings.GroupBy(x => x.TaskType)
                    .Select(g => new
                    {
                        TaskType = g.Key,
                        Count = g.Count(),
                        ActiveCount = g.Count(x => x.IsActive),
                        HasDefault = g.Any(x => x.IsDefault)
                    }).ToList(),
                ProviderStats = mappings.GroupBy(x => x.ProviderName)
                    .Select(g => new
                    {
                        ProviderName = g.Key,
                        Count = g.Count(),
                        ActiveCount = g.Count(x => x.IsActive)
                    }).ToList()
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射统计失败: {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// 获取系统任务映射统计信息
    /// </summary>
    public async Task<object> GetSystemMappingStatsAsync()
    {
        try
        {
            var allMappings = await _db.Queryable<UserTaskMapping>().ToListAsync();

            var stats = new
            {
                TotalMappings = allMappings.Count,
                TotalUsers = allMappings.Select(x => x.UserId).Distinct().Count(),
                ActiveMappings = allMappings.Count(x => x.IsActive),
                InactiveMappings = allMappings.Count(x => !x.IsActive),
                TaskTypeStats = allMappings.GroupBy(x => x.TaskType)
                    .Select(g => new
                    {
                        TaskType = g.Key,
                        TotalCount = g.Count(),
                        ActiveCount = g.Count(x => x.IsActive),
                        UserCount = g.Select(x => x.UserId).Distinct().Count()
                    }).ToList(),
                ProviderStats = allMappings.GroupBy(x => x.ProviderName)
                    .Select(g => new
                    {
                        ProviderName = g.Key,
                        TotalCount = g.Count(),
                        ActiveCount = g.Count(x => x.IsActive),
                        UserCount = g.Select(x => x.UserId).Distinct().Count()
                    }).ToList()
            };

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统任务映射统计失败");
            throw;
        }
    }

    /// <summary>
    /// 验证任务映射配置是否有效
    /// </summary>
    public async Task<(bool IsValid, List<string> Errors)> ValidateMappingAsync(UserTaskMapping mapping)
    {
        try
        {
            var errors = new List<string>();

            // 基本验证
            var (isValid, validationErrors) = mapping.ValidateConfiguration();
            if (!isValid)
            {
                errors.AddRange(validationErrors);
            }

            // 检查重复
            var isDuplicate = await ExistsDuplicateAsync(mapping.UserId, mapping.TaskType,
                mapping.ProviderName, mapping.Id)
                .ConfigureAwait(false);
            if (isDuplicate)
            {
                errors.Add($"用户已存在相同的任务映射配置: {mapping.TaskType} -> {mapping.ProviderName}");
            }

            // 检查用户是否存在
            var userExists = await _db.Queryable<User>()
                .Where(x => x.Id == mapping.UserId)
                .AnyAsync()
                .ConfigureAwait(false);
            if (!userExists)
            {
                errors.Add($"用户不存在: {mapping.UserId}");
            }

            return (errors.Count == 0, errors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证任务映射失败: {UserId}, {TaskType}", mapping.UserId, mapping.TaskType);
            throw;
        }
    }

    /// <summary>
    /// 检查是否存在重复的任务映射
    /// </summary>
    public async Task<bool> ExistsDuplicateAsync(int userId, string taskType, string providerName, int? excludeId = null)
    {
        try
        {
            var query = _db.Queryable<UserTaskMapping>()
                .Where(x => x.UserId == userId && x.TaskType == taskType && x.ProviderName == providerName);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            return await query.AnyAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查重复任务映射失败: {UserId}, {TaskType}, {ProviderName}",
                userId, taskType, providerName);
            throw;
        }
    }
}
