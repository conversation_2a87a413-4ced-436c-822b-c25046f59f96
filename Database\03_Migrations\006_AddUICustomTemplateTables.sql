-- =============================================
-- 迁移脚本: 添加自定义模板相关表
-- 版本: 006
-- 创建时间: 2025-06-23
-- 描述: 创建自定义模板、模板序列、模板步骤和执行日志表
-- =============================================

USE ProjectManagementAI;
GO

PRINT '开始创建自定义模板相关表...';

-- =============================================
-- 1. 自定义UI自动化模板表 (CustomUIAutoMationTemplates)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND type in (N'U'))
BEGIN
    PRINT '创建CustomUIAutoMationTemplates表...';

    CREATE TABLE CustomUIAutoMationTemplates (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        Category nvarchar(50) NOT NULL,
        FilePath nvarchar(500) NOT NULL,
        Confidence decimal(3,2) NOT NULL DEFAULT 0.7,
        Tags nvarchar(500) NULL,
        Notes nvarchar(1000) NULL,
        UsageCount int NOT NULL DEFAULT 0,
        LastUsedTime datetime2 NULL,

        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_CustomUIAutoMationTemplates PRIMARY KEY (Id),
        CONSTRAINT UK_CustomUIAutoMationTemplates_Name UNIQUE (Name),
        CONSTRAINT FK_CustomUIAutoMationTemplates_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CustomUIAutoMationTemplates_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_CustomUIAutoMationTemplates_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_CustomUIAutoMationTemplates_Category CHECK (Category IN ('按钮', '菜单', '对话框', '输入框', '图标', '文本', '状态', '工具栏', '面板', 'CopilotChat自动化', '其他')),
        CONSTRAINT CK_CustomUIAutoMationTemplates_Confidence CHECK (Confidence >= 0.1 AND Confidence <= 1.0),
        CONSTRAINT CK_CustomUIAutoMationTemplates_UsageCount CHECK (UsageCount >= 0)
    );

    PRINT 'CustomUIAutoMationTemplates表创建成功';
END
ELSE
BEGIN
    PRINT 'CustomUIAutoMationTemplates表已存在，跳过创建';
END
GO

-- =============================================
-- 2. UI自动化模板序列表 (UIAutoMationTemplateSequences)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSequences]') AND type in (N'U'))
BEGIN
    PRINT '创建UIAutoMationTemplateSequences表...';

    CREATE TABLE UIAutoMationTemplateSequences (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        Category nvarchar(50) NOT NULL,
        Tags nvarchar(500) NULL,
        Notes nvarchar(1000) NULL,
        UsageCount int NOT NULL DEFAULT 0,
        LastUsedTime datetime2 NULL,
        IsActive bit NOT NULL DEFAULT 1,

        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_UIAutoMationTemplateSequences PRIMARY KEY (Id),
        CONSTRAINT UK_UIAutoMationTemplateSequences_Name UNIQUE (Name),
        CONSTRAINT FK_UIAutoMationTemplateSequences_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateSequences_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateSequences_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_UIAutoMationTemplateSequences_Category CHECK (Category IN ('按钮', '菜单', '对话框', '输入框', '图标', '文本', '状态', '工具栏', '面板', 'CopilotChat自动化', '其他')),
        CONSTRAINT CK_UIAutoMationTemplateSequences_UsageCount CHECK (UsageCount >= 0)
    );

    PRINT 'UIAutoMationTemplateSequences表创建成功';
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSequences表已存在，跳过创建';
END
GO

-- =============================================
-- 3. UI自动化模板步骤表 (UIAutoMationTemplateSteps)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSteps]') AND type in (N'U'))
BEGIN
    PRINT '创建UIAutoMationTemplateSteps表...';

    CREATE TABLE UIAutoMationTemplateSteps (
        Id int IDENTITY(1,1) NOT NULL,
        SequenceId int NOT NULL,
        TemplateId int NULL,
        StepOrder int NOT NULL,
        ActionType nvarchar(50) NOT NULL,
        Description nvarchar(500) NULL,
        Parameters nvarchar(max) NULL,
        TimeoutSeconds int NOT NULL DEFAULT 5,
        RetryCount int NOT NULL DEFAULT 0,
        MaxRetries int NOT NULL DEFAULT 3,
        IsActive bit NOT NULL DEFAULT 1,

        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_UIAutoMationTemplateSteps PRIMARY KEY (Id),
        CONSTRAINT FK_UIAutoMationTemplateSteps_SequenceId FOREIGN KEY (SequenceId) REFERENCES UIAutoMationTemplateSequences(Id) ON DELETE CASCADE,
        CONSTRAINT FK_UIAutoMationTemplateSteps_TemplateId FOREIGN KEY (TemplateId) REFERENCES CustomUIAutoMationTemplates(Id),
        CONSTRAINT FK_UIAutoMationTemplateSteps_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateSteps_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateSteps_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_UIAutoMationTemplateSteps_ActionType CHECK (ActionType IN ('click', 'wait', 'input', 'delay', 'screenshot', 'verify', 'scroll', 'key_press')),
        CONSTRAINT CK_UIAutoMationTemplateSteps_StepOrder CHECK (StepOrder > 0),
        CONSTRAINT CK_UIAutoMationTemplateSteps_TimeoutSeconds CHECK (TimeoutSeconds > 0),
        CONSTRAINT CK_UIAutoMationTemplateSteps_RetryCount CHECK (RetryCount >= 0 AND RetryCount <= MaxRetries),
        CONSTRAINT CK_UIAutoMationTemplateSteps_MaxRetries CHECK (MaxRetries >= 0 AND MaxRetries <= 10)
    );

    PRINT 'UIAutoMationTemplateSteps表创建成功';
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateSteps表已存在，跳过创建';
END
GO

-- =============================================
-- 4. UI自动化模板执行日志表 (UIAutoMationTemplateExecutionLogs)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateExecutionLogs]') AND type in (N'U'))
BEGIN
    PRINT '创建UIAutoMationTemplateExecutionLogs表...';

    CREATE TABLE UIAutoMationTemplateExecutionLogs (
        Id int IDENTITY(1,1) NOT NULL,
        SequenceId int NULL,
        TemplateId int NULL,
        StepId int NULL,
        ExecutionType nvarchar(50) NOT NULL,
        Status nvarchar(20) NOT NULL,
        StartTime datetime2 NOT NULL DEFAULT GETDATE(),
        EndTime datetime2 NULL,
        Duration int NULL,
        Result nvarchar(max) NULL,
        ErrorMessage nvarchar(max) NULL,
        ScreenshotPath nvarchar(500) NULL,
        ExecutedBy nvarchar(100) NULL,

        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,

        CONSTRAINT PK_UIAutoMationTemplateExecutionLogs PRIMARY KEY (Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_SequenceId FOREIGN KEY (SequenceId) REFERENCES UIAutoMationTemplateSequences(Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_TemplateId FOREIGN KEY (TemplateId) REFERENCES CustomUIAutoMationTemplates(Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_StepId FOREIGN KEY (StepId) REFERENCES UIAutoMationTemplateSteps(Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_UIAutoMationTemplateExecutionLogs_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_UIAutoMationTemplateExecutionLogs_ExecutionType CHECK (ExecutionType IN ('Template', 'Sequence', 'Step')),
        CONSTRAINT CK_UIAutoMationTemplateExecutionLogs_Status CHECK (Status IN ('Started', 'Running', 'Completed', 'Failed', 'Cancelled')),
        CONSTRAINT CK_UIAutoMationTemplateExecutionLogs_Duration CHECK (Duration IS NULL OR Duration >= 0)
    );

    PRINT 'UIAutoMationTemplateExecutionLogs表创建成功';
END
ELSE
BEGIN
    PRINT 'UIAutoMationTemplateExecutionLogs表已存在，跳过创建';
END
GO

-- =============================================
-- 创建索引
-- =============================================
PRINT '开始创建索引...';

-- CustomUIAutoMationTemplates 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = N'IX_CustomUIAutoMationTemplates_Category')
    CREATE INDEX IX_CustomUIAutoMationTemplates_Category ON CustomUIAutoMationTemplates(Category);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = N'IX_CustomUIAutoMationTemplates_CreatedBy')
    CREATE INDEX IX_CustomUIAutoMationTemplates_CreatedBy ON CustomUIAutoMationTemplates(CreatedBy);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CustomUIAutoMationTemplates]') AND name = N'IX_CustomUIAutoMationTemplates_IsDeleted')
    CREATE INDEX IX_CustomUIAutoMationTemplates_IsDeleted ON CustomUIAutoMationTemplates(IsDeleted);

-- UIAutoMationTemplateSequences 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSequences]') AND name = N'IX_UIAutoMationTemplateSequences_Category')
    CREATE INDEX IX_UIAutoMationTemplateSequences_Category ON UIAutoMationTemplateSequences(Category);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSequences]') AND name = N'IX_UIAutoMationTemplateSequences_IsActive')
    CREATE INDEX IX_UIAutoMationTemplateSequences_IsActive ON UIAutoMationTemplateSequences(IsActive);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSequences]') AND name = N'IX_UIAutoMationTemplateSequences_IsDeleted')
    CREATE INDEX IX_UIAutoMationTemplateSequences_IsDeleted ON UIAutoMationTemplateSequences(IsDeleted);

-- UIAutoMationTemplateSteps 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSteps]') AND name = N'IX_UIAutoMationTemplateSteps_SequenceId')
    CREATE INDEX IX_UIAutoMationTemplateSteps_SequenceId ON UIAutoMationTemplateSteps(SequenceId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSteps]') AND name = N'IX_UIAutoMationTemplateSteps_TemplateId')
    CREATE INDEX IX_UIAutoMationTemplateSteps_TemplateId ON UIAutoMationTemplateSteps(TemplateId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateSteps]') AND name = N'IX_UIAutoMationTemplateSteps_StepOrder')
    CREATE INDEX IX_UIAutoMationTemplateSteps_StepOrder ON UIAutoMationTemplateSteps(SequenceId, StepOrder);

-- UIAutoMationTemplateExecutionLogs 索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateExecutionLogs]') AND name = N'IX_UIAutoMationTemplateExecutionLogs_SequenceId')
    CREATE INDEX IX_UIAutoMationTemplateExecutionLogs_SequenceId ON UIAutoMationTemplateExecutionLogs(SequenceId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateExecutionLogs]') AND name = N'IX_UIAutoMationTemplateExecutionLogs_Status')
    CREATE INDEX IX_UIAutoMationTemplateExecutionLogs_Status ON UIAutoMationTemplateExecutionLogs(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutoMationTemplateExecutionLogs]') AND name = N'IX_UIAutoMationTemplateExecutionLogs_StartTime')
    CREATE INDEX IX_UIAutoMationTemplateExecutionLogs_StartTime ON UIAutoMationTemplateExecutionLogs(StartTime);

PRINT '索引创建完成';
GO

PRINT '==============================================';
PRINT '自定义UI自动化模板表迁移完成！';
PRINT '==============================================';
