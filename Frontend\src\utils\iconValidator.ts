// Element Plus Icons 验证工具
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 获取所有可用的图标名称
export const availableIcons = Object.keys(ElementPlusIconsVue)

// 验证图标是否存在
export function validateIcon(iconName: string): boolean {
  return availableIcons.includes(iconName)
}

// 获取相似的图标名称（用于建议替代）
export function getSimilarIcons(iconName: string): string[] {
  const lowerIconName = iconName.toLowerCase()
  return availableIcons.filter(name => 
    name.toLowerCase().includes(lowerIconName) ||
    lowerIconName.includes(name.toLowerCase())
  ).slice(0, 5)
}

// 常用图标映射（用于替换不存在的图标）
export const iconMapping: Record<string, string> = {
  'Code': 'EditPen',           // 代码 -> 编辑笔
  'Programming': 'EditPen',    // 编程 -> 编辑笔
  'Terminal': 'Monitor',       // 终端 -> 显示器
  'Console': 'Monitor',        // 控制台 -> 显示器
  'Database': 'Coin',          // 数据库 -> 硬币
  'Server': 'Monitor',         // 服务器 -> 显示器
  'Api': 'Connection',         // API -> 连接
  'Network': 'Connection',     // 网络 -> 连接
  'Cloud': 'Platform',         // 云 -> 平台
  'Deploy': 'Upload',          // 部署 -> 上传
  'Build': 'Tools',            // 构建 -> 工具
  'Test': 'CircleCheck',       // 测试 -> 圆形勾选
  'Bug': 'Warning',            // 错误 -> 警告
  'Error': 'CircleClose',      // 错误 -> 圆形关闭
  'Success': 'CircleCheck',    // 成功 -> 圆形勾选
  'Info': 'InfoFilled',        // 信息 -> 信息填充
  'Question': 'QuestionFilled', // 问题 -> 问题填充
}

// 获取图标的替代方案
export function getIconReplacement(iconName: string): string {
  if (validateIcon(iconName)) {
    return iconName
  }
  
  // 检查映射表
  if (iconMapping[iconName]) {
    return iconMapping[iconName]
  }
  
  // 尝试相似的图标
  const similar = getSimilarIcons(iconName)
  if (similar.length > 0) {
    return similar[0]
  }
  
  // 默认返回设置图标
  return 'Setting'
}

// 验证项目中使用的图标
export function validateProjectIcons() {
  const usedIcons = [
    'Plus', 'Folder', 'Loading', 'CircleCheck', 'Money',
    'FolderOpened', 'Document', 'EditPen', 'Setting',
    'Menu', 'Search', 'Bell', 'Sunny', 'Moon', 'User',
    'ArrowDown', 'SwitchButton', 'Platform', 'Fold', 'Expand'
  ]
  
  const results = usedIcons.map(icon => ({
    name: icon,
    exists: validateIcon(icon),
    replacement: getIconReplacement(icon)
  }))
  
  return results
}

// 打印图标验证结果
export function printIconValidation() {
  console.log('🔍 Element Plus Icons 验证结果:')
  console.log('可用图标总数:', availableIcons.length)
  console.log('')
  
  const results = validateProjectIcons()
  const invalid = results.filter(r => !r.exists)
  
  if (invalid.length === 0) {
    console.log('✅ 所有图标都有效!')
  } else {
    console.log('❌ 发现无效图标:')
    invalid.forEach(icon => {
      console.log(`  - ${icon.name} -> 建议使用: ${icon.replacement}`)
    })
  }
  
}

// 开发环境下自动验证
if (import.meta.env.DEV) {
  printIconValidation()
}
