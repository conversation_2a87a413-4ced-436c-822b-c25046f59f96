<template>
  <div class="playwright-config">
    <el-form :model="config" label-width="120px">
      <!-- 基础配置 -->
      <el-card class="config-section">
        <template #header>
          <span>🔧 基础配置</span>
        </template>
        
        <el-form-item label="无头模式">
          <el-switch v-model="config.headless" />
          <span class="config-tip">启用后浏览器将在后台运行</span>
        </el-form-item>
        
        <el-form-item label="视窗大小">
          <el-row :gutter="10">
            <el-col :span="11">
              <el-input-number 
                v-model="config.viewport.width" 
                :min="800" 
                :max="3840"
                placeholder="宽度"
              />
            </el-col>
            <el-col :span="2" class="text-center">×</el-col>
            <el-col :span="11">
              <el-input-number 
                v-model="config.viewport.height" 
                :min="600" 
                :max="2160"
                placeholder="高度"
              />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="超时时间">
          <el-input-number 
            v-model="config.timeout" 
            :min="5000" 
            :max="300000"
            :step="1000"
          />
          <span class="config-tip">毫秒</span>
        </el-form-item>
        
        <el-form-item label="基础URL">
          <el-input v-model="config.baseURL" placeholder="https://example.com" />
        </el-form-item>
      </el-card>

      <!-- 浏览器配置 -->
      <el-card class="config-section">
        <template #header>
          <span>🌐 浏览器配置</span>
        </template>
        
        <el-form-item label="浏览器类型">
          <el-checkbox-group v-model="config.browsers">
            <el-checkbox label="chromium">Chromium</el-checkbox>
            <el-checkbox label="firefox">Firefox</el-checkbox>
            <el-checkbox label="webkit">WebKit (Safari)</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="设备模拟">
          <el-select v-model="config.device" placeholder="选择设备" clearable>
            <el-option label="桌面" value="" />
            <el-option label="iPhone 12" value="iPhone 12" />
            <el-option label="iPhone 12 Pro" value="iPhone 12 Pro" />
            <el-option label="iPad" value="iPad" />
            <el-option label="Galaxy S21" value="Galaxy S21" />
            <el-option label="Pixel 5" value="Pixel 5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="用户代理">
          <el-input 
            v-model="config.userAgent" 
            type="textarea" 
            :rows="2"
            placeholder="自定义User-Agent（可选）"
          />
        </el-form-item>
      </el-card>

      <!-- 执行配置 -->
      <el-card class="config-section">
        <template #header>
          <span>⚡ 执行配置</span>
        </template>
        
        <el-form-item label="并行执行">
          <el-switch v-model="config.parallel" />
          <span class="config-tip">同时运行多个测试</span>
        </el-form-item>
        
        <el-form-item label="工作进程数" v-if="config.parallel">
          <el-input-number 
            v-model="config.workers" 
            :min="1" 
            :max="8"
          />
        </el-form-item>
        
        <el-form-item label="重试次数">
          <el-input-number 
            v-model="config.retries" 
            :min="0" 
            :max="5"
          />
        </el-form-item>
        
        <el-form-item label="全局超时">
          <el-input-number 
            v-model="config.globalTimeout" 
            :min="30000" 
            :max="1800000"
            :step="10000"
          />
          <span class="config-tip">毫秒，整个测试套件的超时时间</span>
        </el-form-item>
      </el-card>

      <!-- 报告配置 -->
      <el-card class="config-section">
        <template #header>
          <span>📊 报告配置</span>
        </template>
        
        <el-form-item label="报告格式">
          <el-checkbox-group v-model="config.reporters">
            <el-checkbox label="html">HTML报告</el-checkbox>
            <el-checkbox label="json">JSON报告</el-checkbox>
            <el-checkbox label="junit">JUnit XML</el-checkbox>
            <el-checkbox label="line">控制台输出</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="截图设置">
          <el-select v-model="config.screenshot">
            <el-option label="仅失败时" value="only-on-failure" />
            <el-option label="关闭" value="off" />
            <el-option label="开启" value="on" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="视频录制">
          <el-select v-model="config.video">
            <el-option label="仅失败时" value="retain-on-failure" />
            <el-option label="关闭" value="off" />
            <el-option label="开启" value="on" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="追踪">
          <el-select v-model="config.trace">
            <el-option label="仅失败时" value="retain-on-failure" />
            <el-option label="关闭" value="off" />
            <el-option label="开启" value="on" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 高级配置 -->
      <el-card class="config-section">
        <template #header>
          <span>🔬 高级配置</span>
        </template>
        
        <el-form-item label="忽略HTTPS错误">
          <el-switch v-model="config.ignoreHTTPSErrors" />
        </el-form-item>
        
        <el-form-item label="禁用JavaScript">
          <el-switch v-model="config.javaScriptEnabled" />
        </el-form-item>
        
        <el-form-item label="网络空闲等待">
          <el-switch v-model="config.waitForNetworkIdle" />
          <span class="config-tip">等待网络请求完成</span>
        </el-form-item>
        
        <el-form-item label="额外启动参数">
          <el-input 
            v-model="config.launchOptions" 
            type="textarea" 
            :rows="3"
            placeholder="--disable-web-security --allow-running-insecure-content"
          />
        </el-form-item>
      </el-card>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <el-button @click="resetToDefault">重置默认</el-button>
        <el-button @click="loadPreset('mobile')">移动端预设</el-button>
        <el-button @click="loadPreset('desktop')">桌面端预设</el-button>
        <el-button type="primary" @click="validateConfig">验证配置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface PlaywrightConfig {
  headless: boolean
  viewport: {
    width: number
    height: number
  }
  timeout: number
  baseURL: string
  browsers: string[]
  device: string
  userAgent: string
  parallel: boolean
  workers: number
  retries: number
  globalTimeout: number
  reporters: string[]
  screenshot: string
  video: string
  trace: string
  ignoreHTTPSErrors: boolean
  javaScriptEnabled: boolean
  waitForNetworkIdle: boolean
  launchOptions: string
}

const props = defineProps<{
  modelValue: PlaywrightConfig
}>()

const emit = defineEmits<{
  'update:modelValue': [value: PlaywrightConfig]
}>()

// 配置对象
const config = reactive<PlaywrightConfig>({
  headless: false,
  viewport: {
    width: 1920,
    height: 1080
  },
  timeout: 30000,
  baseURL: '',
  browsers: ['chromium'],
  device: '',
  userAgent: '',
  parallel: false,
  workers: 1,
  retries: 0,
  globalTimeout: 300000,
  reporters: ['html', 'line'],
  screenshot: 'only-on-failure',
  video: 'retain-on-failure',
  trace: 'retain-on-failure',
  ignoreHTTPSErrors: false,
  javaScriptEnabled: true,
  waitForNetworkIdle: false,
  launchOptions: ''
})

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config, newValue)
  }
}, { immediate: true, deep: true })

// 方法
const resetToDefault = () => {
  Object.assign(config, {
    headless: false,
    viewport: { width: 1920, height: 1080 },
    timeout: 30000,
    baseURL: '',
    browsers: ['chromium'],
    device: '',
    userAgent: '',
    parallel: false,
    workers: 1,
    retries: 0,
    globalTimeout: 300000,
    reporters: ['html', 'line'],
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    ignoreHTTPSErrors: false,
    javaScriptEnabled: true,
    waitForNetworkIdle: false,
    launchOptions: ''
  })
  ElMessage.success('配置已重置为默认值')
}

const loadPreset = (preset: string) => {
  if (preset === 'mobile') {
    Object.assign(config, {
      viewport: { width: 375, height: 667 },
      device: 'iPhone 12',
      browsers: ['webkit'],
      parallel: false,
      workers: 1
    })
    ElMessage.success('已加载移动端预设配置')
  } else if (preset === 'desktop') {
    Object.assign(config, {
      viewport: { width: 1920, height: 1080 },
      device: '',
      browsers: ['chromium', 'firefox'],
      parallel: true,
      workers: 2
    })
    ElMessage.success('已加载桌面端预设配置')
  }
}

const validateConfig = () => {
  const errors: string[] = []
  
  // 验证视窗大小
  if (config.viewport.width < 320 || config.viewport.height < 240) {
    errors.push('视窗大小过小')
  }
  
  // 验证超时时间
  if (config.timeout < 1000) {
    errors.push('超时时间不能小于1秒')
  }
  
  // 验证浏览器选择
  if (config.browsers.length === 0) {
    errors.push('至少选择一个浏览器')
  }
  
  // 验证工作进程数
  if (config.parallel && config.workers < 1) {
    errors.push('并行模式下工作进程数不能小于1')
  }
  
  // 验证报告格式
  if (config.reporters.length === 0) {
    errors.push('至少选择一种报告格式')
  }
  
  if (errors.length > 0) {
    ElMessage.error(`配置验证失败: ${errors.join(', ')}`)
  } else {
    ElMessage.success('配置验证通过')
  }
}
</script>

<style lang="scss" scoped>
.playwright-config {
  .config-section {
    margin-bottom: 16px;
    
    :deep(.el-card__header) {
      padding: 12px 16px;
      font-weight: 500;
    }
  }
  
  .config-tip {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .text-center {
    text-align: center;
    line-height: 32px;
  }
  
  .config-actions {
    margin-top: 20px;
    text-align: center;
    
    .el-button {
      margin: 0 8px;
    }
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
  
  :deep(.el-checkbox-group) {
    .el-checkbox {
      margin-right: 16px;
      margin-bottom: 8px;
    }
  }
}
</style>