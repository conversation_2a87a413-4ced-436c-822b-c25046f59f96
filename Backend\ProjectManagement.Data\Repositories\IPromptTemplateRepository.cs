using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// Prompt模板仓储接口
/// </summary>
public interface IPromptTemplateRepository : IRepository<PromptTemplate>
{
    /// <summary>
    /// 根据分类ID获取模板列表
    /// </summary>
    Task<List<PromptTemplate>> GetByCategoryIdAsync(int categoryId);

    /// <summary>
    /// 根据任务类型获取模板列表
    /// </summary>
    Task<List<PromptTemplate>> GetByTaskTypeAsync(string taskType);

    /// <summary>
    /// 根据模板类型获取模板列表
    /// </summary>
    Task<List<PromptTemplate>> GetByTemplateTypeAsync(string templateType);

    /// <summary>
    /// 获取默认模板
    /// </summary>
    Task<PromptTemplate?> GetDefaultTemplateAsync(string taskType);

    /// <summary>
    /// 搜索模板
    /// </summary>
    Task<List<PromptTemplate>> SearchAsync(string keyword, int? categoryId = null, string? taskType = null);

    /// <summary>
    /// 获取热门模板
    /// </summary>
    Task<List<PromptTemplate>> GetPopularTemplatesAsync(int count = 10);

    /// <summary>
    /// 获取用户收藏的模板
    /// </summary>
    Task<List<PromptTemplate>> GetUserFavoriteTemplatesAsync(int userId);

    /// <summary>
    /// 获取用户最近使用的模板
    /// </summary>
    Task<List<PromptTemplate>> GetUserRecentTemplatesAsync(int userId, int count = 10);

    /// <summary>
    /// 更新模板使用统计
    /// </summary>
    Task UpdateUsageStatsAsync(int templateId);

    /// <summary>
    /// 更新模板评分
    /// </summary>
    Task UpdateRatingAsync(int templateId, decimal rating);
}
