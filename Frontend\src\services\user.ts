import { ApiService } from './api'
import type {
  User,
  UserQueryParams,
  UpdateUserRequestDto,
  ChangePasswordRequestDto,
  UserStatisticsDto
} from '@/types/user'
import type { PagedResult } from '@/types'

export class UserService {
  /**
   * 获取用户列表
   */
  static async getUsers(params: UserQueryParams = {}): Promise<PagedResult<User>> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('pageNumber', params.page.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params.search) queryParams.append('search', params.search)

    const url = `/api/users${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    return ApiService.get<PagedResult<User>>(url)
  }

  /**
   * 根据ID获取用户详细信息
   */
  static async getUser(id: number): Promise<User> {
    return ApiService.get<User>(`/api/users/${id}`)
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: number, request: UpdateUserRequestDto): Promise<User> {
    return ApiService.put<User>(`/api/users/${id}`, request)
  }

  /**
   * 修改密码
   */
  static async changePassword(request: ChangePasswordRequestDto): Promise<void> {
    return ApiService.post<void>('/api/users/change-password', request)
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStatistics(): Promise<UserStatisticsDto> {
    return ApiService.get<UserStatisticsDto>('/api/users/statistics')
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<User> {
    return ApiService.get<User>('/api/auth/me')
  }

  /**
   * 获取所有用户（简化版，用于下拉选择）
   */
  static async getAllUsers(): Promise<User[]> {
    const result = await this.getUsers({ pageSize: 1000 })
    return result.items
  }

  /**
   * 上传用户头像
   */
  static async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<{
    success: boolean
    message: string
    data: {
      avatar: string
      user: User
    }
  }> {
    const formData = new FormData()
    formData.append('file', file)

    const config: any = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    if (onProgress) {
      config.onUploadProgress = (progressEvent: any) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    }

    return ApiService.post('/api/users/upload-avatar', formData, config)
  }
}

// 导出类和静态方法
export const userService = UserService
