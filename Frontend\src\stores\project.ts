import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ProjectService } from '@/services/project'
import type { 
  Project, 
  ProjectSummary, 
  CreateProjectRequest, 
  UpdateProjectRequest,
  UpdateProjectStatusRequest,
  ProjectStatistics,
  PagedResult 
} from '@/types'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<ProjectSummary[]>([])
  const currentProject = ref<Project | null>(null)
  const statistics = ref<ProjectStatistics | null>(null)
  const loading = ref(false)
  const pagination = ref({
    pageNumber: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0
  })
  const filters = ref({
    status: '',
    search: ''
  })

  // 计算属性
  const hasProjects = computed(() => projects.value.length > 0)
  const activeProjects = computed(() => 
    projects.value.filter(p => p.status === 'InProgress')
  )
  const completedProjects = computed(() => 
    projects.value.filter(p => p.status === 'Completed')
  )

  // 操作方法
  const fetchProjects = async (params?: {
    pageNumber?: number
    pageSize?: number
    status?: string
    search?: string
  }) => {
    try {
      loading.value = true
      
      const requestParams = {
        pageNumber: params?.pageNumber || pagination.value.pageNumber,
        pageSize: params?.pageSize || pagination.value.pageSize,
        status: params?.status || filters.value.status,
        search: params?.search || filters.value.search
      }

      const result = await ProjectService.getProjects(requestParams)
      
      projects.value = result.items
      pagination.value = {
        pageNumber: result.pageNumber,
        pageSize: result.pageSize,
        totalCount: result.totalCount,
        totalPages: result.totalPages
      }

      // 更新过滤器
      if (params?.status !== undefined) filters.value.status = params.status
      if (params?.search !== undefined) filters.value.search = params.search

      return result
    } finally {
      loading.value = false
    }
  }

  const fetchProject = async (id: number) => {
    try {
      loading.value = true
      const project = await ProjectService.getProject(id)
      currentProject.value = project
      return project
    } finally {
      loading.value = false
    }
  }

  const createProject = async (request: CreateProjectRequest) => {
    try {
      loading.value = true
      const project = await ProjectService.createProject(request)
      
      // 刷新项目列表
      await fetchProjects()
      
      return project
    } finally {
      loading.value = false
    }
  }

  const updateProject = async (id: number, request: UpdateProjectRequest) => {
    try {
      loading.value = true
      const project = await ProjectService.updateProject(id, request)
      
      // 更新当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = project
      }
      
      // 更新项目列表中的项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = {
          ...projects.value[index],
          name: project.name,
          description: project.description,
          priority: project.priority,
          updatedAt: project.updatedAt
        }
      }
      
      return project
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (id: number) => {
    try {
      loading.value = true
      await ProjectService.deleteProject(id)
      
      // 从列表中移除项目
      projects.value = projects.value.filter(p => p.id !== id)
      
      // 如果删除的是当前项目，清空当前项目
      if (currentProject.value?.id === id) {
        currentProject.value = null
      }
      
      // 更新分页信息
      pagination.value.totalCount = Math.max(0, pagination.value.totalCount - 1)
      pagination.value.totalPages = Math.ceil(pagination.value.totalCount / pagination.value.pageSize)
      
    } finally {
      loading.value = false
    }
  }

  const updateProjectStatus = async (id: number, request: UpdateProjectStatusRequest) => {
    try {
      loading.value = true
      await ProjectService.updateProjectStatus(id, request)
      
      // 更新当前项目状态
      if (currentProject.value?.id === id) {
        currentProject.value.status = request.status
        if (request.progress !== undefined) {
          currentProject.value.progress = request.progress
        }
      }
      
      // 更新项目列表中的项目状态
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index].status = request.status
        if (request.progress !== undefined) {
          projects.value[index].progress = request.progress
        }
      }
      
    } finally {
      loading.value = false
    }
  }

  const fetchStatistics = async () => {
    try {
      const stats = await ProjectService.getProjectStatistics()
      statistics.value = stats
      return stats
    } catch (error) {
      console.error('Failed to fetch project statistics:', error)
      throw error
    }
  }

  // 搜索项目
  const searchProjects = async (searchTerm: string) => {
    await fetchProjects({ 
      search: searchTerm, 
      pageNumber: 1 
    })
  }

  // 按状态过滤项目
  const filterByStatus = async (status: string) => {
    await fetchProjects({ 
      status, 
      pageNumber: 1 
    })
  }

  // 重置过滤器
  const resetFilters = async () => {
    filters.value = {
      status: '',
      search: ''
    }
    await fetchProjects({ 
      status: '', 
      search: '', 
      pageNumber: 1 
    })
  }

  // 分页操作
  const changePage = async (pageNumber: number) => {
    await fetchProjects({ pageNumber })
  }

  const changePageSize = async (pageSize: number) => {
    await fetchProjects({ 
      pageSize, 
      pageNumber: 1 
    })
  }

  // 清空状态
  const clearState = () => {
    projects.value = []
    currentProject.value = null
    statistics.value = null
    pagination.value = {
      pageNumber: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0
    }
    filters.value = {
      status: '',
      search: ''
    }
  }

  return {
    // 状态
    projects,
    currentProject,
    statistics,
    loading,
    pagination,
    filters,
    
    // 计算属性
    hasProjects,
    activeProjects,
    completedProjects,
    
    // 操作方法
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    updateProjectStatus,
    fetchStatistics,
    searchProjects,
    filterByStatus,
    resetFilters,
    changePage,
    changePageSize,
    clearState
  }
})
