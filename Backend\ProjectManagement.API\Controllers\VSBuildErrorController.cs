using Microsoft.AspNetCore.Mvc;
using ProjectManagement.AI.Services;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// Visual Studio 编译错误管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class VSBuildErrorController : ControllerBase
{
    private readonly ILogger<VSBuildErrorController> _logger;
    private readonly VSBuildErrorService _buildErrorService;
    private readonly IRepository<VSBuildError> _buildErrorRepository;
    private readonly IRepository<VSBuildSession> _buildSessionRepository;

    public VSBuildErrorController(
        ILogger<VSBuildErrorController> logger,
        VSBuildErrorService buildErrorService,
        IRepository<VSBuildError> buildErrorRepository,
        IRepository<VSBuildSession> buildSessionRepository)
    {
        _logger = logger;
        _buildErrorService = buildErrorService;
        _buildErrorRepository = buildErrorRepository;
        _buildSessionRepository = buildSessionRepository;
    }

    /// <summary>
    /// 开始监控项目编译
    /// </summary>
    [HttpPost("monitor-build")]
    public async Task<IActionResult> MonitorBuild([FromBody] MonitorBuildRequest request)
    {
        try
        {
            _logger.LogInformation("开始监控项目编译: {ProjectPath}", request.ProjectPath);

            var sessionId = await _buildErrorService.MonitorBuildAsync(
                request.ProjectPath,
                request.ProjectId,
                request.BuildConfiguration ?? "Debug",
                request.BuildPlatform ?? "AnyCPU"
            );

            return Ok(new { SessionId = sessionId, Message = "编译监控已启动" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "监控项目编译失败");
            return StatusCode(500, "监控项目编译失败");
        }
    }

    /// <summary>
    /// 请求AI修复编译错误
    /// </summary>
    [HttpPost("request-ai-fix/{sessionId}")]
    public async Task<IActionResult> RequestAIFix(string sessionId)
    {
        try
        {
            _logger.LogInformation("请求AI修复编译错误: {SessionId}", sessionId);

            var aiResponse = await _buildErrorService.RequestAIFixAsync(sessionId);

            return Ok(new { SessionId = sessionId, AIResponse = aiResponse });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求AI修复失败");
            return StatusCode(500, "请求AI修复失败");
        }
    }

    /// <summary>
    /// 获取编译会话详情
    /// </summary>
    [HttpGet("session/{sessionId}")]
    public async Task<IActionResult> GetBuildSession(string sessionId)
    {
        try
        {
            var session = await _buildErrorService.GetBuildSessionAsync(sessionId);
            if (session == null)
            {
                return NotFound($"找不到编译会话: {sessionId}");
            }

            return Ok(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编译会话失败");
            return StatusCode(500, "获取编译会话失败");
        }
    }

    /// <summary>
    /// 获取编译会话的错误列表
    /// </summary>
    [HttpGet("session/{sessionId}/errors")]
    public async Task<IActionResult> GetBuildErrors(string sessionId)
    {
        try
        {
            var errors = await _buildErrorService.GetBuildErrorsAsync(sessionId);
            return Ok(errors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编译错误列表失败");
            return StatusCode(500, "获取编译错误列表失败");
        }
    }

    /// <summary>
    /// 标记错误为已解决
    /// </summary>
    [HttpPut("error/{errorId}/resolve")]
    public async Task<IActionResult> ResolveError(int errorId)
    {
        try
        {
            await _buildErrorService.MarkErrorAsResolvedAsync(errorId);
            return Ok(new { Message = "错误已标记为已解决" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记错误为已解决失败");
            return StatusCode(500, "标记错误为已解决失败");
        }
    }

    /// <summary>
    /// 获取所有编译会话列表
    /// </summary>
    [HttpGet("sessions")]
    public async Task<IActionResult> GetBuildSessions([FromQuery] int? projectId = null,
        [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            int total;
            PagedResult<VSBuildSession> result;

            if (projectId.HasValue)
            {
                total = await _buildSessionRepository.CountAsync(x => x.ProjectId == projectId.Value);
                result = await _buildSessionRepository.GetPagedListAsync(
                    page, pageSize,
                    x => x.ProjectId == projectId.Value,
                    x => x.StartTime, false);
            }
            else
            {
                total = await _buildSessionRepository.CountAsync();
                result = await _buildSessionRepository.GetPagedListAsync(
                    page, pageSize,
                    null,
                    x => x.StartTime, false);
            }

            return Ok(new
            {
                Data = result.Items,
                Total = result.TotalCount,
                Page = page,
                PageSize = pageSize
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编译会话列表失败");
            return StatusCode(500, "获取编译会话列表失败");
        }
    }

    /// <summary>
    /// 获取编译错误统计
    /// </summary>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetBuildErrorStatistics([FromQuery] int? projectId = null)
    {
        try
        {
            int totalErrors;
            int resolvedErrors;
            List<VSBuildError> errorsByCode;

            if (projectId.HasValue)
            {
                totalErrors = await _buildErrorRepository.CountAsync(x => x.ProjectId == projectId.Value);
                resolvedErrors = await _buildErrorRepository.CountAsync(x => x.ProjectId == projectId.Value && x.IsResolved);
                errorsByCode = await _buildErrorRepository.GetListAsync(x => x.ProjectId == projectId.Value);
            }
            else
            {
                totalErrors = await _buildErrorRepository.CountAsync();
                resolvedErrors = await _buildErrorRepository.CountAsync(x => x.IsResolved);
                errorsByCode = await _buildErrorRepository.GetAllAsync();
            }

            var errorCodeStats = errorsByCode
                .Where(x => !string.IsNullOrEmpty(x.ErrorCode))
                .GroupBy(x => x.ErrorCode)
                .Select(g => new { ErrorCode = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(10)
                .ToList();

            return Ok(new
            {
                TotalErrors = totalErrors,
                ResolvedErrors = resolvedErrors,
                UnresolvedErrors = totalErrors - resolvedErrors,
                ResolutionRate = totalErrors > 0 ? (decimal)resolvedErrors / totalErrors * 100 : 0,
                TopErrorCodes = errorCodeStats
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取编译错误统计失败");
            return StatusCode(500, "获取编译错误统计失败");
        }
    }
}

/// <summary>
/// 监控编译请求模型
/// </summary>
public class MonitorBuildRequest
{
    /// <summary>
    /// 项目路径
    /// </summary>
    public string ProjectPath { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// 编译配置
    /// </summary>
    public string? BuildConfiguration { get; set; }

    /// <summary>
    /// 编译平台
    /// </summary>
    public string? BuildPlatform { get; set; }
}
