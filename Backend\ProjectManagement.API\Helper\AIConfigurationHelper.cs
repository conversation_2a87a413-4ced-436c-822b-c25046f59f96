using Microsoft.Extensions.Logging;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.API.Helper;

/// <summary>
/// AI配置助手类（静态方法）
/// 用于获取和管理用户的AI模型配置
/// </summary>
public static class AIConfigurationHelper
{
    /// <summary>
    /// 获取用户的AI模型配置
    /// </summary>
    /// <param name="aiConfigRepository">AI配置仓储</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="userId">用户ID</param>
    /// <param name="aiProviderConfigId">指定的AI提供商配置ID（可选）</param>
    /// <param name="encryptionService">加密服务（可选）</param>
    /// <returns>AI模型配置，如果没有找到则返回null</returns>
    public static async Task<AIModelConfig?> GetUserAIConfigurationAsync(
        IAIModelConfigurationRepository aiConfigRepository,
        ILogger logger,
        int userId,
        int? aiProviderConfigId = null,
        ProjectManagement.Core.Services.IEncryptionService? encryptionService = null)
    {
        try
        {
            AIModelConfiguration? aiModelConfig = null;

            if (aiProviderConfigId.HasValue)
            {
                // 使用指定的AI配置
                aiModelConfig = await aiConfigRepository.GetByIdAsync(aiProviderConfigId.Value);
                if (aiModelConfig == null || aiModelConfig.UserId != userId || !aiModelConfig.IsActive)
                {
                    logger.LogWarning("指定的AI配置不存在或不属于当前用户: ConfigId={ConfigId}, UserId={UserId}", 
                        aiProviderConfigId.Value, userId);
                    return null;
                }
            }
            else
            {
                // 使用用户的默认AI配置
                var aiModelConfigs = await aiConfigRepository.GetByUserIdAsync(userId);
                aiModelConfig = aiModelConfigs.FirstOrDefault(x => x.IsActive);
                
                if (aiModelConfig == null)
                {
                    logger.LogWarning("用户没有可用的AI配置: UserId={UserId}", userId);
                    return null;
                }
            }

            return ConvertToAIModelConfig(aiModelConfig, 4000, 0.7f, logger, encryptionService);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户AI配置失败: UserId={UserId}, ConfigId={ConfigId}", 
                userId, aiProviderConfigId);
            return null;
        }
    }

    /// <summary>
    /// 获取用户的AI模型配置（带参数解析）
    /// </summary>
    /// <param name="aiConfigRepository">AI配置仓储</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="userId">用户ID</param>
    /// <param name="aiProviderConfigId">指定的AI提供商配置ID（可选）</param>
    /// <param name="defaultMaxTokens">默认最大令牌数</param>
    /// <param name="defaultTemperature">默认温度参数</param>
    /// <param name="encryptionService">加密服务（可选）</param>
    /// <returns>AI模型配置</returns>
    public static async Task<AIModelConfig?> GetUserAIConfigurationAsync(
        IAIModelConfigurationRepository aiConfigRepository,
        ILogger logger,
        int userId,
        int? aiProviderConfigId = null,
        int defaultMaxTokens = 4000,
        float defaultTemperature = 0.7f,
        ProjectManagement.Core.Services.IEncryptionService? encryptionService = null)
    {
        try
        {
            AIModelConfiguration? aiModelConfig = null;

            if (aiProviderConfigId.HasValue)
            {
                // 使用指定的AI配置
                aiModelConfig = await aiConfigRepository.GetByIdAsync(aiProviderConfigId.Value);
                if (aiModelConfig == null || aiModelConfig.UserId != userId || !aiModelConfig.IsActive)
                {
                    logger.LogWarning("指定的AI配置不存在或不属于当前用户: ConfigId={ConfigId}, UserId={UserId}", 
                        aiProviderConfigId.Value, userId);
                    return null;
                }
            }
            else
            {
                // 使用用户的默认AI配置
                var aiModelConfigs = await aiConfigRepository.GetByUserIdAsync(userId);
                aiModelConfig = aiModelConfigs.FirstOrDefault(x => x.IsActive);
                
                if (aiModelConfig == null)
                {
                    logger.LogWarning("用户没有可用的AI配置: UserId={UserId}", userId);
                    return null;
                }
            }

            return ConvertToAIModelConfig(aiModelConfig, defaultMaxTokens, defaultTemperature, logger, encryptionService);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户AI配置失败: UserId={UserId}, ConfigId={ConfigId}", 
                userId, aiProviderConfigId);
            return null;
        }
    }

    /// <summary>
    /// 获取用户的AI模型配置实体
    /// </summary>
    /// <param name="aiConfigRepository">AI配置仓储</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="userId">用户ID</param>
    /// <param name="aiProviderConfigId">指定的AI提供商配置ID（可选）</param>
    /// <returns>AI模型配置实体</returns>
    public static async Task<AIModelConfiguration?> GetUserAIModelConfigurationAsync(
        IAIModelConfigurationRepository aiConfigRepository,
        ILogger logger,
        int userId, 
        int? aiProviderConfigId = null)
    {
        try
        {
            if (aiProviderConfigId.HasValue)
            {
                // 使用指定的AI配置
                var aiModelConfig = await aiConfigRepository.GetByIdAsync(aiProviderConfigId.Value);
                if (aiModelConfig == null || aiModelConfig.UserId != userId || !aiModelConfig.IsActive)
                {
                    logger.LogWarning("指定的AI配置不存在或不属于当前用户: ConfigId={ConfigId}, UserId={UserId}", 
                        aiProviderConfigId.Value, userId);
                    return null;
                }
                return aiModelConfig;
            }
            else
            {
                // 使用用户的默认AI配置
                var aiModelConfigs = await aiConfigRepository.GetByUserIdAsync(userId);
                var aiModelConfig = aiModelConfigs.FirstOrDefault(x => x.IsActive);
                
                if (aiModelConfig == null)
                {
                    logger.LogWarning("用户没有可用的AI配置: UserId={UserId}", userId);
                }
                
                return aiModelConfig;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取用户AI配置实体失败: UserId={UserId}, ConfigId={ConfigId}", 
                userId, aiProviderConfigId);
            return null;
        }
    }

    /// <summary>
    /// 将AIModelConfiguration转换为AIModelConfig
    /// </summary>
    /// <param name="aiModelConfig">AI模型配置实体</param>
    /// <param name="defaultMaxTokens">默认最大令牌数</param>
    /// <param name="defaultTemperature">默认温度参数</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="encryptionService">加密服务（可选）</param>
    /// <returns>AI模型配置</returns>
    private static AIModelConfig ConvertToAIModelConfig(
        AIModelConfiguration aiModelConfig,
        int defaultMaxTokens = 4000,
        float defaultTemperature = 0.7f,
        ILogger? logger = null,
        ProjectManagement.Core.Services.IEncryptionService? encryptionService = null)
    {
        // 解析模型参数
        var maxTokens = defaultMaxTokens;
        var temperature = defaultTemperature;
        var timeoutSeconds = 60; // 默认超时时间

        if (!string.IsNullOrEmpty(aiModelConfig.ModelParameters))
        {
            try
            {
                var modelParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(aiModelConfig.ModelParameters);
                if (modelParams != null)
                {
                    if (modelParams.TryGetValue("MaxTokens", out var maxTokensValue))
                        int.TryParse(maxTokensValue.ToString(), out maxTokens);

                    if (modelParams.TryGetValue("Temperature", out var temperatureValue))
                        float.TryParse(temperatureValue.ToString(), out temperature);

                    if (modelParams.TryGetValue("TimeoutSeconds", out var timeoutValue))
                        int.TryParse(timeoutValue.ToString(), out timeoutSeconds);
                }
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "解析AI配置参数失败，使用默认值: ConfigId={ConfigId}", aiModelConfig.Id);
            }
        }

        // 根据模型名称推断提供商
        string providerName = GetProviderFromModelName(aiModelConfig.ModelName);
        logger?.LogInformation("AI配置转换 - 原始模型名称: {OriginalModel}, 推断供应商: {Provider}, 端点: {Endpoint}",
            aiModelConfig.ModelName, providerName, aiModelConfig.ApiEndpoint);

        // 提取实际的模型名称（对于自定义供应商，去掉供应商前缀）
        string actualModelName = aiModelConfig.ModelName;
        if (aiModelConfig.ModelName.Contains("-"))
        {
            var parts = aiModelConfig.ModelName.Split('-', 2);
            if (parts.Length >= 2)
            {
                var potentialProvider = parts[0];
                var knownProviders = new[] { "azure", "openai", "deepseek", "claude", "ollama", "mock" };
                if (!knownProviders.Contains(potentialProvider.ToLower()))
                {
                    // 这是自定义供应商，使用后面的部分作为实际模型名称
                    actualModelName = parts[1];
                    logger?.LogInformation("自定义供应商 {Provider}，实际模型名称: {ActualModel}", providerName, actualModelName);
                }
            }
        }

        // 解密API密钥
        string apiKey = string.Empty;
        if (!string.IsNullOrEmpty(aiModelConfig.ApiKey))
        {
            try
            {
                if (encryptionService != null)
                {
                    apiKey = encryptionService.Decrypt(aiModelConfig.ApiKey);
                    logger?.LogInformation("API密钥解密成功，模型: {ModelName}, 原始长度: {OriginalLength}, 解密后长度: {DecryptedLength}",
                        aiModelConfig.ModelName, aiModelConfig.ApiKey.Length, apiKey.Length);
                }
                else
                {
                    apiKey = aiModelConfig.ApiKey;
                    logger?.LogWarning("加密服务未提供，使用原始API密钥: {ModelName}", aiModelConfig.ModelName);
                }
            }
            catch (Exception ex)
            {
                logger?.LogWarning(ex, "解密API密钥失败，使用原始值: {ModelName}", aiModelConfig.ModelName);
                apiKey = aiModelConfig.ApiKey;
            }
        }

        return new AIModelConfig
        {
            Provider = providerName,
            Model = actualModelName,
            ApiKey = apiKey,
            Endpoint = aiModelConfig.ApiEndpoint ?? string.Empty,
            MaxTokens = maxTokens,
            Temperature = temperature,
            TimeoutSeconds = timeoutSeconds
        };
    }

    /// <summary>
    /// 从模型名称获取提供商名称
    /// </summary>
    /// <param name="modelName">模型名称</param>
    /// <returns>提供商名称</returns>
    public static string GetProviderFromModelName(string modelName)
    {
        var name = modelName.ToLower();

        // 优先检查是否是自定义供应商格式：ProviderName-ModelName
        if (name.Contains("-"))
        {
            var parts = modelName.Split('-', 2);
            if (parts.Length >= 2)
            {
                var potentialProvider = parts[0];

                // 检查是否是已知的内置供应商名称
                var knownProviders = new[] { "azure", "openai", "deepseek", "claude", "ollama", "mock" };
                if (!knownProviders.Contains(potentialProvider.ToLower()))
                {
                    // 不是已知的内置供应商，认为是自定义供应商
                    return potentialProvider;
                }
            }
        }

        // 检查已知的模型名称模式（仅用于没有明确供应商前缀的情况）
        if (name.Contains("gpt") || name.Contains("openai"))
            return "OpenAI";
        if (name.Contains("claude"))
            return "Claude";
        if (name.Contains("deepseek"))
            return "DeepSeek";
        if (name.Contains("llama") || name.Contains("mistral") || name.Contains("qwen"))
            return "Ollama";
        if (name.Contains("azure"))
            return "Azure";
        if (name.Contains("mock"))
            return "Mock";

        return "Unknown";
    }



    /// <summary>
    /// 更新AI配置使用统计
    /// </summary>
    /// <param name="aiConfigRepository">AI配置仓储</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="configId">配置ID</param>
    /// <returns></returns>
    public static async Task UpdateUsageStatisticsAsync(
        IAIModelConfigurationRepository aiConfigRepository,
        ILogger logger,
        int configId)
    {
        try
        {
            var config = await aiConfigRepository.GetByIdAsync(configId);
            if (config != null)
            {
                // 这里可以添加使用统计逻辑
                // 比如更新使用次数、最后使用时间等
                config.UpdatedAt = DateTime.UtcNow;
                await aiConfigRepository.UpdateAsync(config);
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "更新AI配置使用统计失败: ConfigId={ConfigId}", configId);
            // 不抛出异常，避免影响主要功能
        }
    }
}
