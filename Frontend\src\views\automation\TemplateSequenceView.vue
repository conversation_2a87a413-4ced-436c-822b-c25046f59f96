<template>
  <div class="template-sequence">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><List /></el-icon>
          模板序列管理
        </h1>
        <p class="page-description">创建和管理CopilotChat自动化模板序列，实现复杂的自动化流程</p>
      </div>
      <div class="header-actions">
        <el-dropdown @command="handleCreateCommand">
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            创建序列
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="ai">
                <el-icon><ChatDotRound /></el-icon>
                AI 智能生成
              </el-dropdown-item>
              <el-dropdown-item command="mermaid">
                <el-icon><Share /></el-icon>
                流程图创建
              </el-dropdown-item>
              <el-dropdown-item command="code">
                <el-icon><DocumentCopy /></el-icon>
                代码编辑器
              </el-dropdown-item>
              <!-- 隐藏传统表单选项 -->
              <!--
              <el-dropdown-item command="form" divided>
                <el-icon><List /></el-icon>
                传统表单
              </el-dropdown-item>
              -->
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleImportExportCommand">
          <el-button>
            <el-icon><Share /></el-icon>
            导入导出
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export-all">
                <el-icon><Share /></el-icon>
                导出所有序列
              </el-dropdown-item>
              <el-dropdown-item command="export-selected" :disabled="selectedSequences.length === 0">
                <el-icon><Share /></el-icon>
                导出选中序列 ({{ selectedSequences.length }})
              </el-dropdown-item>
              <el-dropdown-item command="import" divided>
                <el-icon><Plus /></el-icon>
                导入序列
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button @click="$router.push('/automation/templates')">
          <el-icon><Setting /></el-icon>
          模板管理
        </el-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <el-card class="search-card">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索序列名称或描述"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="searchForm.category"
              placeholder="选择分类"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="searchForm.isActive"
              placeholder="状态"
              clearable
              style="width: 100%"
            >
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 序列列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>序列列表</span>
          <div class="header-actions">
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="sequences"
        style="width: 100%"
        @row-click="viewSequenceDetail"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="序列名称" min-width="200">
          <template #default="{ row }">
            <div class="sequence-name">
              <el-icon class="sequence-icon">
                <List v-if="row.isActive" />
                <CircleClose v-else />
              </el-icon>
              <span>{{ row.name }}</span>
              <el-tag v-if="!row.isActive" type="info" size="small">已禁用</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="150">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <!-- 隐藏步骤数列 -->
        <!--
        <el-table-column label="步骤数" width="120">
          <template #default="{ row }">
            <div class="step-count">
              <el-icon class="step-icon"><Operation /></el-icon>
              <span class="step-text">{{ getStepCountText(row.steps?.length || 0) }}</span>
            </div>
          </template>
        </el-table-column>
        -->
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="lastUsedTime" label="最后使用" width="150">
          <template #default="{ row }">
            <span v-if="row.lastUsedTime">{{ formatTime(row.lastUsedTime) }}</span>
            <span v-else class="text-muted">从未使用</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <!-- 隐藏执行按钮 -->
              <!--
              <el-button
                size="small"
                type="primary"
                :disabled="!row.isActive || !row.steps?.length"
                @click.stop="executeSequence(row)"
              >
                <el-icon><VideoPlay /></el-icon>
                执行
              </el-button>
              -->
              <el-button size="small" @click.stop="editSequence(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <!-- 隐藏复制按钮 -->
              <!--
              <el-button size="small" @click.stop="duplicateSequence(row)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              -->
              <el-button size="small" type="danger" @click.stop="deleteSequence(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 序列详情抽屉 -->
    <el-drawer
      v-model="showSequenceDetail"
      :title="currentSequence?.name || '序列详情'"
      size="60%"
      direction="rtl"
    >
      <SequenceDetailPanel
        v-if="currentSequence"
        :sequence="currentSequence"
        @update="handleSequenceUpdate"
        @execute="executeSequence"
      />
    </el-drawer>

    <!-- Mermaid 序列创建对话框 -->
    <el-dialog
      v-model="showMermaidCreator"
      :title="isCreatorMaximized ? '智能序列创建 (最大化)' : '智能序列创建'"
      :width="isCreatorMaximized ? '100%' : '90%'"
      :fullscreen="isCreatorMaximized"
      :close-on-click-modal="false"
      destroy-on-close
      :class="{ 'maximized-creator-dialog': isCreatorMaximized }"
      top="2vh"
      :style="{ height: isCreatorMaximized ? '100vh' : '96vh' }"
    >
      <MermaidSequenceCreator
        v-model="showMermaidCreator"
        :initial-mode="creatorInitialMode"
        :is-maximized="isCreatorMaximized"
        :editing-sequence="editingSequence"
        @sequence-created="handleMermaidSequenceCreated"
        @toggle-maximize="isCreatorMaximized = !isCreatorMaximized"
      />
      <!-- 隐藏传统表单内容 -->
      <!--
      <template #form-content>
        <SequenceFormDialog
          v-model="showSequenceDialog"
          :sequence="editingSequence"
          @success="handleSequenceSuccess"
        />
      </template>
      -->
    </el-dialog>

    <!-- 创建/编辑序列对话框 -->
    <SequenceFormDialog
      v-model="showSequenceDialog"
      :sequence="editingSequence"
      @success="handleSequenceSuccess"
    />

    <!-- 执行参数对话框 -->
    <ExecutionParametersDialog
      v-model="showExecutionDialog"
      :sequence="executingSequence"
      @execute="handleExecuteWithParameters"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  List, Plus, Setting, Search, Refresh, CircleClose, Operation,
  VideoPlay, Edit, DocumentCopy, Delete, ArrowDown, ChatDotRound, Share
} from '@element-plus/icons-vue'
import type { ElTagType } from '@/types/element-plus'
import CustomTemplateService, {
  type TemplateSequence,
  type PageQuery
} from '@/services/customTemplate'
import SequenceDetailPanel from '@/components/automation/SequenceDetailPanel.vue'
import SequenceFormDialog from '@/components/automation/SequenceFormDialog.vue'
import ExecutionParametersDialog from '@/components/automation/ExecutionParametersDialog.vue'
import MermaidSequenceCreator from '@/components/automation/MermaidSequenceCreator.vue'

// 响应式数据
const loading = ref(false)
const sequences = ref<TemplateSequence[]>([])
const categories = ref<string[]>([])
const selectedSequences = ref<TemplateSequence[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  isActive: undefined as boolean | undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框和抽屉状态
const showSequenceDetail = ref(false)
const showSequenceDialog = ref(false)
const showExecutionDialog = ref(false)
const showMermaidCreator = ref(false)
const isCreatorMaximized = ref(false)
const creatorInitialMode = ref<'ai' | 'visual' | 'code'>('ai')
const currentSequence = ref<TemplateSequence | null>(null)
const editingSequence = ref<TemplateSequence | null>(null)
const executingSequence = ref<TemplateSequence | null>(null)

// 计算属性
const queryParams = computed((): PageQuery => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  keyword: searchForm.keyword || undefined,
  category: searchForm.category || undefined,
  isActive: searchForm.isActive
}))

// 生命周期
onMounted(() => {
  loadData()
  loadCategories()
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const result = await CustomTemplateService.getSequences(queryParams.value)
    sequences.value = result.items
    pagination.total = result.total
  } catch (error) {
    console.error('加载序列列表失败:', error)
    ElMessage.error('加载序列列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    categories.value = await CustomTemplateService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const refreshData = () => {
  loadData()
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const viewSequenceDetail = async (sequence: TemplateSequence) => {
  try {
    // 获取完整的序列信息（包含步骤）
    currentSequence.value = await CustomTemplateService.getSequence(sequence.id)
    showSequenceDetail.value = true
  } catch (error) {
    console.error('获取序列详情失败:', error)
    ElMessage.error('获取序列详情失败')
  }
}

const showCreateSequenceDialog = () => {
  editingSequence.value = null
  showSequenceDialog.value = true
}

const editSequence = async (sequence: TemplateSequence) => {
  try {
    // 获取完整的序列信息（包含步骤）
    editingSequence.value = await CustomTemplateService.getSequence(sequence.id)
    // 使用智能序列创建器进行编辑，默认使用代码编辑器模式
    creatorInitialMode.value = 'code'
    showMermaidCreator.value = true
  } catch (error) {
    console.error('获取序列详情失败:', error)
    ElMessage.error('获取序列详情失败')
  }
}

const duplicateSequence = async (sequence: TemplateSequence) => {
  try {
    // 获取完整的序列信息（包含步骤）
    const fullSequence = await CustomTemplateService.getSequence(sequence.id)

    const newSequence = {
      ...fullSequence,
      name: `${fullSequence.name} - 副本`,
      id: undefined,
      createdTime: undefined,
      updatedTime: undefined
    }
    delete newSequence.id
    delete newSequence.createdTime
    delete newSequence.updatedTime

    editingSequence.value = newSequence as any
    showSequenceDialog.value = true
  } catch (error) {
    console.error('复制序列失败:', error)
    ElMessage.error('复制序列失败')
  }
}

const deleteSequence = async (sequence: TemplateSequence) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除序列 "${sequence.name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await CustomTemplateService.deleteSequence(sequence.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除序列失败:', error)
      ElMessage.error('删除序列失败')
    }
  }
}

const executeSequence = (sequence: TemplateSequence) => {
  if (!sequence.isActive) {
    ElMessage.warning('序列已禁用，无法执行')
    return
  }

  if (!sequence.steps?.length) {
    ElMessage.warning('序列没有配置步骤，无法执行')
    return
  }

  executingSequence.value = sequence
  showExecutionDialog.value = true
}

const handleExecuteWithParameters = async (parameters: Record<string, any>) => {
  if (!executingSequence.value) return

  try {
    ElMessage.info('正在执行序列...')
    const result = await CustomTemplateService.executeSequence(
      executingSequence.value.id,
      parameters
    )

    ElMessage.success(`序列执行已启动，执行ID: ${result.executionId}`)

    // 可以跳转到执行日志页面查看详情
    // this.$router.push(`/automation/executions/${result.executionId}`)
  } catch (error) {
    console.error('执行序列失败:', error)
    ElMessage.error('执行序列失败')
  }
}

const handleSequenceUpdate = () => {
  loadData()
}

const handleSequenceSuccess = () => {
  loadData()
}

// 处理创建命令
const handleCreateCommand = (command: string) => {
  switch (command) {
    case 'ai':
      creatorInitialMode.value = 'ai'
      showMermaidCreator.value = true
      break
    case 'mermaid':
      creatorInitialMode.value = 'visual'
      showMermaidCreator.value = true
      break
    case 'code':
      creatorInitialMode.value = 'code'
      showMermaidCreator.value = true
      break
    // 隐藏传统表单功能
    /*
    case 'form':
      editingSequence.value = null
      showSequenceDialog.value = true
      break
    */
  }
}

// 处理 Mermaid 序列创建成功
const handleMermaidSequenceCreated = async (sequenceData: any) => {
  try {
    // 这里可以直接使用 AI 生成的序列数据创建序列
    // 或者将数据传递给现有的创建流程
    console.log('AI 生成的序列数据:', sequenceData)

    // 关闭 Mermaid 创建器
    showMermaidCreator.value = false

    // 刷新序列列表
    await loadData()

    ElMessage.success('序列创建成功')
  } catch (error) {
    console.error('创建序列失败:', error)
    ElMessage.error('创建序列失败')
  }
}

// 处理表格选择变化
const handleSelectionChange = (selection: TemplateSequence[]) => {
  selectedSequences.value = selection
}

// 处理导入导出命令
const handleImportExportCommand = (command: string) => {
  switch (command) {
    case 'export-all':
      exportSequences()
      break
    case 'export-selected':
      exportSequences(selectedSequences.value.map(s => s.id))
      break
    case 'import':
      importSequences()
      break
  }
}

// 导出序列
const exportSequences = async (sequenceIds?: number[]) => {
  try {
    const blob = await CustomTemplateService.exportSequences(sequenceIds)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    const fileName = sequenceIds?.length
      ? `template_sequences_selected_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
      : `template_sequences_all_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`

    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success(`成功导出 ${sequenceIds?.length || '所有'} 个序列`)
  } catch (error) {
    console.error('导出序列失败:', error)
    ElMessage.error('导出序列失败')
  }
}

// 导入序列
const importSequences = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const result = await CustomTemplateService.importSequences(file)

      if (result.successCount > 0) {
        ElMessage.success(`成功导入 ${result.successCount} 个序列`)
        loadData() // 刷新列表
      }

      if (result.failedCount > 0) {
        ElMessage.warning(`${result.failedCount} 个序列导入失败`)

        // 显示详细错误信息
        if (result.errors.length > 0) {
          ElMessageBox.alert(
            result.errors.join('\n'),
            '导入错误详情',
            {
              confirmButtonText: '确定',
              type: 'warning'
            }
          )
        }
      }

      if (result.successCount === 0 && result.failedCount === 0) {
        ElMessage.info('没有序列被导入')
      }
    } catch (error) {
      console.error('导入序列失败:', error)
      ElMessage.error('导入序列失败')
    }
  }

  input.click()
}

// 工具方法
const getCategoryTagType = (category: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    '按钮': 'primary',
    '菜单': 'success',
    '对话框': 'warning',
    '输入框': 'info',
    '图标': 'danger',
    'CopilotChat自动化': 'primary',
    '其他': 'info'
  }
  return typeMap[category] || 'info'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 将数字转换为中文文字
const getStepCountText = (count: number): string => {
  if (count === 0) return '无步骤'

  const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千']

  if (count < 10) {
    return `${chineseNumbers[count]}步`
  } else if (count < 20) {
    return count === 10 ? '十步' : `一十${chineseNumbers[count % 10]}步`
  } else if (count < 100) {
    const tens = Math.floor(count / 10)
    const ones = count % 10
    return ones === 0 ? `${chineseNumbers[tens]}十步` : `${chineseNumbers[tens]}十${chineseNumbers[ones]}步`
  } else if (count < 1000) {
    const hundreds = Math.floor(count / 100)
    const remainder = count % 100
    if (remainder === 0) {
      return `${chineseNumbers[hundreds]}百步`
    } else if (remainder < 10) {
      return `${chineseNumbers[hundreds]}百零${chineseNumbers[remainder]}步`
    } else {
      const tens = Math.floor(remainder / 10)
      const ones = remainder % 10
      if (ones === 0) {
        return `${chineseNumbers[hundreds]}百${chineseNumbers[tens]}十步`
      } else {
        return `${chineseNumbers[hundreds]}百${chineseNumbers[tens]}十${chineseNumbers[ones]}步`
      }
    }
  } else {
    // 超过999步，直接显示数字
    return `${count}步`
  }
}
</script>

<style scoped lang="scss">
.template-sequence {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .page-description {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-row {
        align-items: center;
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .sequence-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .sequence-icon {
        color: #409eff;

        &.disabled {
          color: #c0c4cc;
        }
      }
    }

    .step-count {
      display: flex;
      align-items: center;
      gap: 6px;

      .step-icon {
        color: #409eff;
        font-size: 16px;
      }

      .step-text {
        color: #606266;
        font-size: 13px;
        font-weight: 500;
      }
    }

    .text-muted {
      color: #c0c4cc;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }

  // 表格行悬停效果
  :deep(.el-table__row) {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-sequence {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-actions {
        justify-content: flex-start;
      }
    }

    .search-card {
      .search-form {
        .el-col {
          margin-bottom: 12px;
        }
      }
    }

    .table-card {
      :deep(.el-table) {
        .el-table__header,
        .el-table__body {
          font-size: 12px;
        }
      }
    }
  }
}

/* 智能序列创建对话框样式 */
:deep(.el-dialog:has(.mermaid-sequence-creator)) {
  .el-dialog__body {
    padding: 20px !important;
    height: calc(85vh - 120px) !important;
    overflow: hidden !important;
  }

  .mermaid-sequence-creator {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .code-mode {
    flex: 1 !important;
    height: 100% !important;
    overflow: hidden !important;
  }
}

/* 最大化创建器对话框样式 */
:deep(.maximized-creator-dialog) {
  .el-dialog {
    margin: 0 !important;
    border-radius: 0 !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  .el-dialog__body {
    padding: 20px !important;
    height: calc(100vh - 120px) !important;
    overflow: hidden !important;
  }

  .mermaid-sequence-creator {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .code-mode {
    flex: 1 !important;
    height: 100% !important;
    overflow: hidden !important;
  }

  .code-sequence-creator {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .editor-container {
    flex: 1 !important;
    height: 100% !important;
    min-height: auto !important;
  }

  .editor-content {
    flex: 1 !important;
    height: 100% !important;
    min-height: auto !important;
  }

  .editor-content .monaco-editor-container {
    height: 100% !important;
  }
}
</style>
