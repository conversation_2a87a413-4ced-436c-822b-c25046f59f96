# Playwright自动化测试框架

## 📁 目录结构

```
tests/playwright/
├── e2e/                           # 端到端测试
│   └── playwright-testing.spec.ts # 基础功能测试
├── fixtures/                      # 测试夹具
│   └── base-fixtures.ts          # 基础测试夹具
├── utils/                         # 测试工具
│   ├── test-helpers.ts           # 测试辅助函数
│   └── test-data.ts              # 测试数据工厂
├── global-setup.ts               # 全局设置
├── global-teardown.ts            # 全局清理
└── README.md                     # 说明文档
```

## 🚀 快速开始

### 运行测试

```bash
# 安装浏览器（首次运行）
npm run test:playwright:install

# 运行所有测试
npm run test:playwright

# 有头模式运行（可以看到浏览器）
npm run test:playwright:headed

# 调试模式
npm run test:playwright:debug

# UI模式（图形界面）
npm run test:playwright:ui

# 查看测试报告
npm run test:playwright:report
```

## 🛠️ 核心组件

### TestHelpers 类
提供常用的测试辅助方法：
- `waitForPageLoad()` - 等待页面加载完成
- `navigateTo(path)` - 导航到指定页面
- `waitForElement(selector)` - 等待元素可见
- `takeScreenshot(name)` - 截图

### TestDataFactory 类
生成测试数据：
- `randomString(length)` - 生成随机字符串
- `createTestData()` - 生成测试数据

### 自定义Fixtures
使用扩展的测试夹具：
```typescript
import { test, expect } from '../fixtures/base-fixtures';

test('示例测试', async ({ page, helpers, testData }) => {
  // 使用辅助工具和测试数据
});
```

## 📝 编写测试

### 基本测试结构

```typescript
import { test, expect } from '@playwright/test';

test.describe('功能模块测试', () => {
  test('测试用例描述', async ({ page }) => {
    // 测试步骤
    await page.goto('http://localhost:3000');
    
    // 断言验证
    await expect(page.locator('body')).toBeVisible();
  });
});
```

## 🔧 配置

主要配置文件：`playwright.config.ts`

- 支持多浏览器：Chromium、Firefox、WebKit
- 自动截图和录像
- HTML测试报告
- 全局设置和清理

## 📊 测试报告

测试执行后会生成：
- HTML报告：`playwright-report/index.html`
- 截图：`test-results/screenshots/`
- 录像：`test-results/videos/`

## 🎯 与前端UI集成

访问 `http://localhost:3000/testing/playwright` 可以：
- 配置测试参数
- 管理测试文件
- 查看测试结果

---

这是一个精简的Playwright测试框架，包含了核心功能和基础结构。
