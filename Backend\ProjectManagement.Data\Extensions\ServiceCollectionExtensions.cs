using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlSugar;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Entities;
using ProjectManagement.Data.Configuration;
using ProjectManagement.Data.Repositories;

namespace ProjectManagement.Data.Extensions;

/// <summary>
/// 数据库初始化器（用于日志记录）
/// </summary>
public class DatabaseInitializer
{
}

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加数据访问层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddDataServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册SqlSugar
        services.AddSqlSugar(configuration);

        // 注册仓储
        services.AddRepositories();

        return services;
    }

    /// <summary>
    /// 添加SqlSugar服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSqlSugar(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<ISqlSugarClient>(provider =>
        {
            var logger = provider.GetService<ILogger<SqlSugarConfig>>();
            return SqlSugarConfig.CreateSqlSugarClient(configuration, logger);
        });

        return services;
    }

    /// <summary>
    /// 添加仓储服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // 注册通用仓储
        services.AddScoped(typeof(IRepository<>), typeof(BaseRepository<>));

        // 注册具体仓储
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IProjectRepository, ProjectRepository>();
        services.AddScoped<IRequirementConversationRepository, RequirementConversationRepository>();
        services.AddScoped<IRequirementDocumentRepository, RequirementDocumentRepository>();

        // Prompt工程相关仓储
        services.AddScoped<IPromptTemplateRepository, PromptTemplateRepository>();
        services.AddScoped<IPromptCategoryRepository, PromptCategoryRepository>();
        services.AddScoped<IRepository<PromptUsageStats>, BaseRepository<PromptUsageStats>>();
        services.AddScoped<IRepository<PromptRating>, BaseRepository<PromptRating>>();
        services.AddScoped<IRepository<UserPromptPreference>, BaseRepository<UserPromptPreference>>();

        // 开发步骤相关仓储
        services.AddScoped<IDevelopmentStepRepository, DevelopmentStepRepository>();
        services.AddScoped<IRepository<StepDependency>, BaseRepository<StepDependency>>();
        services.AddScoped<IRepository<StepExecutionHistory>, BaseRepository<StepExecutionHistory>>();

        // 自动化任务相关仓储
        services.AddScoped<IAutomationTaskRepository, AutomationTaskRepository>();

        // 自定义UI自动化模板相关仓储
        services.AddScoped<ICustomTemplateRepository, CustomTemplateRepository>();
        services.AddScoped<ITemplateSequenceRepository, TemplateSequenceRepository>();
        services.AddScoped<ITemplateStepRepository, TemplateStepRepository>();
        services.AddScoped<IExecutionLogRepository, ExecutionLogRepository>();

        return services;
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="createTables">是否创建表</param>
    /// <param name="seedData">是否初始化种子数据</param>
    public static void InitializeDatabase(this IServiceProvider serviceProvider, bool createTables = true, bool seedData = true)
    {
        using var scope = serviceProvider.CreateScope();
        var db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        var logger = scope.ServiceProvider.GetService<ILogger<DatabaseInitializer>>();

        try
        {
            if (createTables)
            {
                logger?.LogInformation("开始创建数据库表...");

                // 创建数据库和表
                db.CreateDatabaseAndTables(
                    typeof(ProjectManagement.Core.Entities.User),
                    typeof(ProjectManagement.Core.Entities.Project),
                    typeof(ProjectManagement.Core.Entities.RequirementDocument),
                    typeof(ProjectManagement.Core.Entities.RequirementConversation),
                    typeof(ProjectManagement.Core.Entities.ERDiagram),
                    typeof(ProjectManagement.Core.Entities.ContextDiagram),
                    typeof(ProjectManagement.Core.Entities.CodeGenerationTask),
                    typeof(ProjectManagement.Core.Entities.GeneratedCodeFile),
                    typeof(ProjectManagement.Core.Entities.TestTask),
                    typeof(ProjectManagement.Core.Entities.DeploymentTask),
                    typeof(ProjectManagement.Core.Entities.Issue),
                    typeof(ProjectManagement.Core.Entities.IssueResolution),
                    typeof(ProjectManagement.Core.Entities.AIModelConfiguration),
                    typeof(ProjectManagement.Core.Entities.CodingTask),
                    typeof(ProjectManagement.Core.Entities.CodingTaskStep),
                    typeof(ProjectManagement.Core.Entities.CodingTaskExecutionLog),
                    typeof(ProjectManagement.Core.Entities.WorkflowState),
                    typeof(ProjectManagement.Core.Entities.SystemLog),
                    // VS编译错误相关实体
                    typeof(ProjectManagement.Core.Entities.VSBuildError),
                    typeof(ProjectManagement.Core.Entities.VSBuildSession),
                    // Prompt工程相关实体
                    typeof(ProjectManagement.Core.Entities.PromptCategory),
                    typeof(ProjectManagement.Core.Entities.PromptTemplate),
                    typeof(ProjectManagement.Core.Entities.PromptUsageStats),
                    typeof(ProjectManagement.Core.Entities.PromptRating),
                    typeof(ProjectManagement.Core.Entities.UserPromptPreference),
                    // 开发步骤相关实体
                    typeof(ProjectManagement.Core.Entities.DevelopmentStep),
                    typeof(ProjectManagement.Core.Entities.StepDependency),
                    typeof(ProjectManagement.Core.Entities.StepExecutionHistory),
                    // 自动化任务相关实体
                    typeof(ProjectManagement.Core.Entities.AutomationTask),
                    // 自定义UI自动化模板相关实体
                    typeof(ProjectManagement.Core.Entities.CustomUIAutoMationTemplate),
                    typeof(ProjectManagement.Core.Entities.UIAutoMationTemplateSequence),
                    typeof(ProjectManagement.Core.Entities.UIAutoMationTemplateStep),
                    typeof(ProjectManagement.Core.Entities.UIAutoMationTemplateExecutionLog),
                    // 编译错误相关实体
                    typeof(ProjectManagement.Core.Entities.CompilationError),
                    typeof(ProjectManagement.Core.Entities.VSBuildError),
                    typeof(ProjectManagement.Core.Entities.VSBuildSession)
                );

                logger?.LogInformation("数据库表创建完成");
            }

            if (seedData)
            {
                logger?.LogInformation("开始初始化种子数据...");

                // 初始化种子数据
                SeedData(db, logger);

                logger?.LogInformation("种子数据初始化完成");
            }
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 初始化种子数据
    /// </summary>
    /// <param name="db">SqlSugar客户端</param>
    /// <param name="logger">日志记录器</param>
    private static void SeedData(ISqlSugarClient db, ILogger? logger)
    {
        try
        {
            // 检查是否已有管理员用户
            var adminExists = db.Queryable<ProjectManagement.Core.Entities.User>()
                .Where(x => x.Role == "SuperAdmin")
                .Any();

            if (!adminExists)
            {
                logger?.LogInformation("创建默认管理员用户...");

                // 创建默认管理员用户
                var adminUser = new ProjectManagement.Core.Entities.User
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"), // 默认密码: Admin123!
                    Salt = "admin_salt_2024",
                    RealName = "系统管理员",
                    Role = "SuperAdmin",
                    Status = 1, // Active
                    EmailVerified = true,
                    EmailVerifiedTime = DateTime.UtcNow,
                    CreatedTime = DateTime.UtcNow,
                    Version = 1
                };

                db.Insertable(adminUser).ExecuteCommand();
                logger?.LogInformation("默认管理员用户创建完成");
            }

            // 初始化Prompt分类
            InitializePromptCategories(db, logger);

            // 初始化默认Prompt模板
            InitializePromptTemplates(db, logger);

        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "种子数据初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 初始化Prompt分类
    /// </summary>
    private static void InitializePromptCategories(ISqlSugarClient db, ILogger? logger)
    {
        try
        {
            var categoryExists = db.Queryable<ProjectManagement.Core.Entities.PromptCategory>().Any();
            if (!categoryExists)
            {
                logger?.LogInformation("创建默认Prompt分类...");

                var categories = new List<ProjectManagement.Core.Entities.PromptCategory>
                {
                    new() { Name = "需求分析", Description = "用于需求收集和分析的提示词模板", Icon = "📋", Color = "#1890ff", SortOrder = 1 },
                    new() { Name = "代码生成", Description = "用于生成各种类型代码的提示词模板", Icon = "💻", Color = "#52c41a", SortOrder = 2 },
                    new() { Name = "测试生成", Description = "用于生成测试用例和测试代码的提示词模板", Icon = "🧪", Color = "#fa8c16", SortOrder = 3 },
                    new() { Name = "文档生成", Description = "用于生成技术文档的提示词模板", Icon = "📄", Color = "#722ed1", SortOrder = 4 },
                    new() { Name = "代码审查", Description = "用于代码质量分析和审查的提示词模板", Icon = "🔍", Color = "#eb2f96", SortOrder = 5 },
                    new() { Name = "性能优化", Description = "用于性能分析和优化建议的提示词模板", Icon = "⚡", Color = "#f5222d", SortOrder = 6 },
                    new() { Name = "通用对话", Description = "用于一般性AI对话的提示词模板", Icon = "💬", Color = "#13c2c2", SortOrder = 7 }
                };

                db.Insertable(categories).ExecuteCommand();
                logger?.LogInformation("默认Prompt分类创建完成");
            }
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Prompt分类初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 初始化默认Prompt模板
    /// </summary>
    private static void InitializePromptTemplates(ISqlSugarClient db, ILogger? logger)
    {
        try
        {
            var templateExists = db.Queryable<ProjectManagement.Core.Entities.PromptTemplate>().Any();
            if (!templateExists)
            {
                logger?.LogInformation("创建默认Prompt模板...");

                // 获取分类ID
                var categories = db.Queryable<ProjectManagement.Core.Entities.PromptCategory>().ToList();
                var requirementCategoryId = categories.FirstOrDefault(c => c.Name == "需求分析")?.Id ?? 1;
                var codeGenCategoryId = categories.FirstOrDefault(c => c.Name == "代码生成")?.Id ?? 2;
                var testCategoryId = categories.FirstOrDefault(c => c.Name == "测试生成")?.Id ?? 3;

                var templates = new List<ProjectManagement.Core.Entities.PromptTemplate>
                {
                    new()
                    {
                        Name = "软件需求分析",
                        Description = "分析用户输入的软件需求，提取功能性和非功能性需求",
                        CategoryId = requirementCategoryId,
                        Content = @"请分析以下软件需求，并以JSON格式返回结构化的分析结果：

需求描述：
{requirements}

请从以下维度进行分析：
1. 项目基本信息（项目名称、描述、目标）
2. 功能性需求（具体功能点）
3. 非功能性需求（性能、安全、可用性等）
4. 用户故事（从用户角度描述需求）
5. 验收标准（如何验证需求是否满足）
6. 技术约束和依赖
7. 风险评估
8. 可行性评分（1-10分）
9. 复杂度评分（1-10分）
10. 置信度评分（0-1）

请以JSON格式返回分析结果。",
                        TaskType = "RequirementAnalysis",
                        TemplateType = "System",
                        IsDefault = true,
                        Parameters = @"{""requirements"": {""type"": ""string"", ""description"": ""用户输入的需求描述"", ""required"": true}}",
                        Tags = "需求分析,JSON格式,结构化"
                    },
                    new()
                    {
                        Name = "Vue组件代码生成",
                        Description = "基于需求规格书生成Vue 3组件代码",
                        CategoryId = codeGenCategoryId,
                        Content = @"基于以下需求规格书，生成Vue 3 + TypeScript + Element Plus组件代码：

{specification}

请生成完整的、可运行的Vue组件代码，包含：
1. 组件模板（template）
2. 脚本逻辑（script setup）
3. 样式定义（style）
4. TypeScript类型定义
5. 必要的导入和依赖
6. 适当的注释
7. 错误处理
8. 响应式设计

请以JSON格式返回，包含文件路径和内容：
{{
    ""files"": [
        {{
            ""path"": ""文件路径"",
            ""content"": ""文件内容"",
            ""language"": ""vue""
        }}
    ],
    ""description"": ""组件说明"",
    ""instructions"": ""使用说明""
}}",
                        TaskType = "CodeGeneration",
                        TemplateType = "System",
                        IsDefault = true,
                        Parameters = @"{""specification"": {""type"": ""string"", ""description"": ""需求规格书内容"", ""required"": true}}",
                        Tags = "Vue3,TypeScript,Element Plus,前端组件"
                    },
                    new()
                    {
                        Name = "单元测试生成",
                        Description = "为指定代码生成完整的单元测试",
                        CategoryId = testCategoryId,
                        Content = @"基于以下代码和需求规格书，生成完整的单元测试：

代码内容：
{codeContent}

需求规格书：
{specification}

请生成完整的单元测试代码，包含：
1. 测试用例设计
2. 正常流程测试
3. 异常情况测试
4. 边界值测试
5. Mock数据和依赖
6. 断言验证
7. 测试覆盖率考虑

请使用适当的测试框架（如xUnit、NUnit、Jest等）格式输出测试代码。

请以JSON格式返回：
{{
    ""testCases"": [
        {{
            ""name"": ""测试用例名称"",
            ""description"": ""测试描述"",
            ""code"": ""测试代码"",
            ""expectedResult"": ""预期结果""
        }}
    ],
    ""mockData"": ""Mock数据定义"",
    ""setupCode"": ""测试初始化代码"",
    ""framework"": ""使用的测试框架""
}}",
                        TaskType = "Testing",
                        TemplateType = "System",
                        IsDefault = true,
                        Parameters = @"{""codeContent"": {""type"": ""string"", ""description"": ""要测试的代码"", ""required"": true}, ""specification"": {""type"": ""string"", ""description"": ""需求规格书"", ""required"": false}}",
                        Tags = "单元测试,测试用例,Mock,自动化测试"
                    }
                };

                db.Insertable(templates).ExecuteCommand();
                logger?.LogInformation("默认Prompt模板创建完成");
            }
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Prompt模板初始化失败");
            throw;
        }
    }
}