using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 系统参数管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SystemParametersController : ControllerBase
    {
        private readonly ISystemParameterRepository _systemParameterRepository;
        private readonly ILogger<SystemParametersController> _logger;

        public SystemParametersController(
            ISystemParameterRepository systemParameterRepository,
            ILogger<SystemParametersController> logger)
        {
            _systemParameterRepository = systemParameterRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<List<string>>> GetCategories()
        {
            try
            {
                var categories = await _systemParameterRepository.GetCategoriesAsync();
                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数分类失败");
                return StatusCode(500, new { message = "获取参数分类失败" });
            }
        }

        /// <summary>
        /// 根据分类获取参数选项
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="includeInactive">是否包含未启用的参数</param>
        [HttpGet("categories/{category}/options")]
        public async Task<ActionResult<List<SystemParameterOptionDto>>> GetParameterOptions(
            string category, 
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var parameters = await _systemParameterRepository.GetByCategoryAsync(category, includeInactive);
                var options = parameters.Select(p => new SystemParameterOptionDto
                {
                    Value = p.ParameterValue,
                    Label = p.DisplayName,
                    Description = p.Description,
                    ExtendedProperties = p.ExtendedProperties
                }).ToList();

                return Ok(options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数选项失败: {Category}", category);
                return StatusCode(500, new { message = "获取参数选项失败" });
            }
        }

        /// <summary>
        /// 根据分类获取参数列表
        /// </summary>
        /// <param name="category">参数分类</param>
        /// <param name="includeInactive">是否包含未启用的参数</param>
        [HttpGet("categories/{category}")]
        public async Task<ActionResult<List<SystemParameterDto>>> GetParametersByCategory(
            string category, 
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var parameters = await _systemParameterRepository.GetByCategoryAsync(category, includeInactive);
                var result = parameters.Select(MapToDto).ToList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类参数失败: {Category}", category);
                return StatusCode(500, new { message = "获取分类参数失败" });
            }
        }

        /// <summary>
        /// 获取参数详情
        /// </summary>
        /// <param name="id">参数ID</param>
        [HttpGet("{id}")]
        public async Task<ActionResult<SystemParameterDto>> GetParameter(int id)
        {
            try
            {
                var parameter = await _systemParameterRepository.GetByIdAsync(id);
                if (parameter == null || parameter.IsDeleted)
                {
                    return NotFound(new { message = "参数不存在" });
                }

                return Ok(MapToDto(parameter));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数详情失败: {Id}", id);
                return StatusCode(500, new { message = "获取参数详情失败" });
            }
        }

        /// <summary>
        /// 创建系统参数
        /// </summary>
        /// <param name="request">创建请求</param>
        [HttpPost]
        public async Task<ActionResult<SystemParameterDto>> CreateParameter([FromBody] CreateSystemParameterDto request)
        {
            try
            {
                var userId = GetCurrentUserId();

                // 检查键名是否已存在
                var exists = await _systemParameterRepository.KeyExistsAsync(request.Category, request.ParameterKey);
                if (exists)
                {
                    return BadRequest(new { message = "参数键名已存在" });
                }

                var parameter = new SystemParameter
                {
                    Category = request.Category,
                    ParameterKey = request.ParameterKey,
                    ParameterValue = request.ParameterValue,
                    DisplayName = request.DisplayName,
                    Description = request.Description,
                    SortOrder = request.SortOrder,
                    IsActive = request.IsActive,
                    IsSystem = false, // 用户创建的参数不是系统参数
                    ExtendedProperties = request.ExtendedProperties,
                    CreatedBy = userId,
                    CreatedTime = DateTime.Now
                };

                var created = await _systemParameterRepository.AddAsync(parameter);
                _logger.LogInformation("创建系统参数成功: {Category}.{Key}", request.Category, request.ParameterKey);

                return Ok(MapToDto(created));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建系统参数失败");
                return StatusCode(500, new { message = "创建系统参数失败" });
            }
        }

        /// <summary>
        /// 更新系统参数
        /// </summary>
        /// <param name="id">参数ID</param>
        /// <param name="request">更新请求</param>
        [HttpPut("{id}")]
        public async Task<ActionResult<SystemParameterDto>> UpdateParameter(int id, [FromBody] UpdateSystemParameterDto request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var parameter = await _systemParameterRepository.GetByIdAsync(id);

                if (parameter == null || parameter.IsDeleted)
                {
                    return NotFound(new { message = "参数不存在" });
                }

                // 检查键名是否已存在（排除当前记录）
                if (!string.IsNullOrEmpty(request.ParameterKey) && request.ParameterKey != parameter.ParameterKey)
                {
                    var exists = await _systemParameterRepository.KeyExistsAsync(parameter.Category, request.ParameterKey, id);
                    if (exists)
                    {
                        return BadRequest(new { message = "参数键名已存在" });
                    }
                }

                // 更新字段
                if (!string.IsNullOrEmpty(request.ParameterKey))
                    parameter.ParameterKey = request.ParameterKey;

                if (!string.IsNullOrEmpty(request.ParameterValue))
                    parameter.ParameterValue = request.ParameterValue;

                if (!string.IsNullOrEmpty(request.DisplayName))
                    parameter.DisplayName = request.DisplayName;

                if (request.Description != null)
                    parameter.Description = request.Description;

                if (request.SortOrder.HasValue)
                    parameter.SortOrder = request.SortOrder.Value;

                if (request.IsActive.HasValue)
                    parameter.IsActive = request.IsActive.Value;

                if (request.ExtendedProperties != null)
                    parameter.ExtendedProperties = request.ExtendedProperties;

                parameter.UpdatedBy = userId;
                parameter.UpdatedTime = DateTime.Now;

                var updated = await _systemParameterRepository.UpdateAsync(parameter);
                _logger.LogInformation("更新系统参数成功: {Id}", id);

                return Ok(MapToDto(parameter));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新系统参数失败: {Id}", id);
                return StatusCode(500, new { message = "更新系统参数失败" });
            }
        }

        /// <summary>
        /// 删除系统参数
        /// </summary>
        /// <param name="id">参数ID</param>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteParameter(int id)
        {
            try
            {
                var parameter = await _systemParameterRepository.GetByIdAsync(id);
                if (parameter == null || parameter.IsDeleted)
                {
                    return NotFound(new { message = "参数不存在" });
                }

                if (parameter.IsSystem)
                {
                    return BadRequest(new { message = "系统参数不能删除" });
                }

                var userId = GetCurrentUserId();
                await _systemParameterRepository.SoftDeleteAsync(id, userId);

                _logger.LogInformation("删除系统参数成功: {Id}", id);
                return Ok(new { message = "删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除系统参数失败: {Id}", id);
                return StatusCode(500, new { message = "删除系统参数失败" });
            }
        }

        /// <summary>
        /// 获取分类统计
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<Dictionary<string, int>>> GetStatistics()
        {
            try
            {
                var statistics = await _systemParameterRepository.GetCategoryStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取参数统计失败");
                return StatusCode(500, new { message = "获取参数统计失败" });
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        /// <summary>
        /// 实体转DTO
        /// </summary>
        private static SystemParameterDto MapToDto(SystemParameter parameter)
        {
            return new SystemParameterDto
            {
                Id = parameter.Id,
                Category = parameter.Category,
                ParameterKey = parameter.ParameterKey,
                ParameterValue = parameter.ParameterValue,
                DisplayName = parameter.DisplayName,
                Description = parameter.Description,
                SortOrder = parameter.SortOrder,
                IsActive = parameter.IsActive,
                IsSystem = parameter.IsSystem,
                ExtendedProperties = parameter.ExtendedProperties,
                CreatedTime = parameter.CreatedTime,
                UpdatedTime = parameter.UpdatedTime
            };
        }
    }
}
