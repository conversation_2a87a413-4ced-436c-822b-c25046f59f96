#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copilot消息处理器
处理消息获取、状态分析和回复监控
"""

import time
from typing import Dict, List, Optional


class CopilotMessageHandler:
    """Copilot消息处理器"""
    
    def __init__(self, dom_automation):
        """
        初始化消息处理器
        
        Args:
            dom_automation: DOM自动化实例
        """
        self.dom_automation = dom_automation
    
    def get_copilot_messages(self) -> List[Dict]:
        """获取Copilot聊天消息"""
        js_code = """
        // 查找包含聊天内容的monaco-scrollable-element
        const scrollableElements = document.querySelectorAll('.monaco-scrollable-element');
        let chatContainer = null;
        
        // 寻找包含聊天内容的容器
        for (const element of scrollableElements) {
            const listRows = element.querySelectorAll('.monaco-list-row');
            if (listRows.length > 0) {
                // 检查是否包含聊天相关内容
                const hasChat = Array.from(listRows).some(row => {
                    const text = row.textContent || '';
                    return text.includes('GitHub Copilot') || 
                           text.includes('yanxingzhao') ||
                           text.includes('Python') ||
                           text.includes('请') ||
                           text.length > 20; // 聊天消息通常比较长
                });
                
                if (hasChat) {
                    chatContainer = element;
                    break;
                }
            }
        }
        
        if (!chatContainer) {
            return [];
        }
        
        const messageElements = chatContainer.querySelectorAll('.monaco-list-row');
        const messages = [];
        
        messageElements.forEach((element, index) => {
            const text = element.textContent || element.innerText || '';
            
            // 改进消息类型识别
            const isUser = element.classList.contains('interactive-request') ||
                          element.classList.contains('chat-request-wrapper') ||
                          element.querySelector('.chat-request-wrapper') !== null ||
                          (text.length < 200 && !text.includes('GitHub Copilot') && !text.includes('Python'));
            
            const isAssistant = element.classList.contains('interactive-response') ||
                              element.classList.contains('chat-response-wrapper') ||
                              element.querySelector('.chat-response-wrapper') !== null ||
                              text.includes('GitHub Copilot') ||
                              (text.length > 100 && (text.includes('Python') || text.includes('语法')));
            
            // 检查是否包含错误或状态信息
            const hasError = text.includes('错误') || text.includes('Error') || 
                           text.includes('服务繁忙') || text.includes('Service busy') ||
                           text.includes('服务器繁忙') || text.includes('Server busy') ||
                           text.includes('超时') || text.includes('Timeout') ||
                           text.includes('网络错误') || text.includes('Network error') ||
                           text.includes('请稍后再试') || text.includes('Please try again');
            
            // 检查是否正在思考/生成
            const hasThinking = text.includes('正在思考') || text.includes('Thinking') ||
                              text.includes('正在生成') || text.includes('Generating') ||
                              text.includes('正在处理') || text.includes('Processing') ||
                              element.querySelector('.codicon-loading, .loading, .spinner') !== null;
            
            // 检查是否是完整回复
            const isComplete = !hasThinking && text.trim().length > 10 && 
                             !text.includes('...') && !hasError;
            
            if (text.trim().length > 0) {
                messages.push({
                    index: index,
                    text: text.trim(),
                    isUser: isUser,
                    isAssistant: isAssistant,
                    hasError: hasError,
                    hasThinking: hasThinking,
                    isComplete: isComplete,
                    className: element.className,
                    timestamp: Date.now(),
                    length: text.trim().length
                });
            }
        });
        
        return messages;
        """
        
        result = self.dom_automation.execute_javascript(js_code)
        return result.get("result", []) if result.get("success") else []
    
    def get_latest_copilot_response(self) -> Dict:
        """获取最新的Copilot回复并分析状态"""
        messages = self.get_copilot_messages()
        
        if not messages:
            return {"status": "no_messages", "content": "", "error": "未找到任何消息"}
        
        # 获取最后一条消息
        latest_message = messages[-1]
        text = latest_message.get("text", "")
        
        # 分析回复状态
        status_info = {
            "status": "unknown",
            "content": text,
            "message_count": len(messages),
            "is_user": latest_message.get("isUser", False),
            "is_assistant": latest_message.get("isAssistant", False),
            "has_error": latest_message.get("hasError", False),
            "has_thinking": latest_message.get("hasThinking", False),
            "length": latest_message.get("length", 0),
            "timestamp": latest_message.get("timestamp", 0)
        }
        
        # 判断具体状态
        if status_info["has_error"]:
            if "服务繁忙" in text or "Service busy" in text:
                status_info["status"] = "service_busy"
            elif "网络" in text or "Network" in text:
                status_info["status"] = "network_error"
            elif "超时" in text or "Timeout" in text:
                status_info["status"] = "timeout"
            else:
                status_info["status"] = "error"
        elif status_info["has_thinking"]:
            status_info["status"] = "thinking"
        elif status_info["is_user"]:
            status_info["status"] = "user_message"
        elif status_info["is_assistant"] and status_info["length"] > 10:
            status_info["status"] = "success"
        elif status_info["length"] < 10:
            status_info["status"] = "incomplete"
        else:
            status_info["status"] = "unknown"
        
        return status_info
    
    def wait_for_copilot_response(self, timeout: int = 30, check_interval: float = 1.0) -> Dict:
        """等待Copilot回复并返回状态"""
        start_time = time.time()
        initial_messages = self.get_copilot_messages()
        initial_count = len(initial_messages)
        
        print(f"⏳ 等待Copilot回复... (超时: {timeout}秒)")
        
        while time.time() - start_time < timeout:
            current_response = self.get_latest_copilot_response()
            current_count = current_response.get("message_count", 0)
            status = current_response.get("status", "unknown")
            
            # 如果有新消息
            if current_count > initial_count:
                if status == "success":
                    print("✅ 收到完整回复")
                    return current_response
                elif status == "service_busy":
                    print("⚠️ 服务繁忙，继续等待...")
                elif status == "error":
                    print("❌ 收到错误回复")
                    return current_response
                elif status == "thinking":
                    print("🤔 Copilot正在思考...")
                elif status == "incomplete":
                    print("📝 收到部分回复，继续等待...")
            
            time.sleep(check_interval)
        
        # 超时
        final_response = self.get_latest_copilot_response()
        final_response["status"] = "timeout"
        print("⏰ 等待超时")
        return final_response
    
    def analyze_response_content(self, response: Dict) -> Dict:
        """分析回复内容，提取有用信息"""
        content = response.get("content", "")
        status = response.get("status", "unknown")
        
        analysis = {
            "original_status": status,
            "content_length": len(content),
            "has_code": "```" in content or "def " in content or "class " in content,
            "has_error_info": any(keyword in content.lower() for keyword in [
                "error", "错误", "失败", "exception", "traceback"
            ]),
            "has_suggestion": any(keyword in content.lower() for keyword in [
                "建议", "推荐", "可以", "应该", "suggest", "recommend"
            ]),
            "language_detected": self._detect_programming_language(content),
            "confidence": self._calculate_confidence(response)
        }
        
        # 重新评估状态
        if analysis["has_error_info"] and status == "success":
            analysis["recommended_status"] = "error_in_content"
        elif analysis["content_length"] < 10:
            analysis["recommended_status"] = "too_short"
        elif analysis["confidence"] < 0.5:
            analysis["recommended_status"] = "low_confidence"
        else:
            analysis["recommended_status"] = status
        
        return analysis
    
    def _detect_programming_language(self, content: str) -> Optional[str]:
        """检测编程语言"""
        language_keywords = {
            "python": ["def ", "import ", "class ", "if __name__", "print("],
            "javascript": ["function ", "const ", "let ", "var ", "console.log"],
            "java": ["public class", "public static", "System.out"],
            "csharp": ["using ", "namespace ", "public class", "Console.WriteLine"],
            "cpp": ["#include", "int main", "std::", "cout"],
            "sql": ["SELECT", "FROM", "WHERE", "INSERT", "UPDATE"]
        }
        
        content_lower = content.lower()
        for language, keywords in language_keywords.items():
            if any(keyword.lower() in content_lower for keyword in keywords):
                return language
        
        return None
    
    def _calculate_confidence(self, response: Dict) -> float:
        """计算回复的可信度"""
        confidence = 1.0
        
        # 基于长度的置信度
        length = response.get("length", 0)
        if length < 10:
            confidence *= 0.2
        elif length < 50:
            confidence *= 0.6
        elif length > 500:
            confidence *= 0.9
        
        # 基于状态的置信度
        status = response.get("status", "unknown")
        if status == "success":
            confidence *= 1.0
        elif status == "thinking":
            confidence *= 0.3
        elif status == "error":
            confidence *= 0.1
        elif status == "service_busy":
            confidence *= 0.0
        
        # 基于内容质量的置信度
        content = response.get("content", "")
        if "GitHub Copilot" in content:
            confidence *= 1.1  # 官方回复
        if any(word in content for word in ["抱歉", "无法", "不能", "sorry", "cannot"]):
            confidence *= 0.7  # 负面回复
        
        return min(confidence, 1.0)
