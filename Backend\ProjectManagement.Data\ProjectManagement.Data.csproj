﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Entities\**" />
    <Compile Remove="Services\**" />
    <EmbeddedResource Remove="Entities\**" />
    <EmbeddedResource Remove="Services\**" />
    <None Remove="Entities\**" />
    <None Remove="Services\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.196" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectManagement.Core\ProjectManagement.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Repositories\" />
    <Folder Include="Configuration\" />
    <Folder Include="Extensions\" />
  </ItemGroup>

</Project>
