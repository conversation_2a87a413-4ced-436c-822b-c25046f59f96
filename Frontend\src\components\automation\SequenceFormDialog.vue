<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑序列' : '创建序列'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="序列名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入序列名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入序列描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态">
            <el-switch
              v-model="form.isActive"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签">
            <el-select
              v-model="form.tags"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <!-- 步骤配置 -->
      <el-form-item label="执行步骤">
        <div class="steps-container">
          <div class="steps-header">
            <span>配置自动化执行步骤</span>
            <el-button type="primary" size="small" @click="addStep">
              <el-icon><Plus /></el-icon>
              添加步骤
            </el-button>
          </div>

          <div v-if="form.steps.length === 0" class="empty-steps">
            <el-empty description="暂无步骤，请添加执行步骤" />
          </div>

          <draggable
            v-else
            v-model="form.steps"
            item-key="tempId"
            handle=".step-handle"
            @end="updateStepOrder"
          >
            <template #item="{ element: step, index }">
              <div class="step-item">
                <div class="step-header">
                  <div class="step-info">
                    <el-icon class="step-handle"><Rank /></el-icon>
                    <span class="step-number">步骤 {{ index + 1 }}</span>
                    <el-tag :type="getActionTypeTag(step.actionType)" size="small">
                      {{ getActionTypeLabel(step.actionType) }}
                    </el-tag>
                  </div>
                  <div class="step-actions">
                    <el-button size="small" @click="editStep(step, index)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="removeStep(index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="step-content">
                  <p class="step-description">{{ step.description || '暂无描述' }}</p>
                  <div class="step-details">
                    <span v-if="step.templateId" class="detail-item">
                      模板: {{ getTemplateName(step.templateId) }}
                    </span>
                    <span class="detail-item">
                      超时: {{ step.timeoutSeconds }}秒
                    </span>
                    <span class="detail-item">
                      重试: {{ step.maxRetries }}次
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>

    <!-- 步骤编辑对话框 -->
    <StepFormDialog
      v-model="showStepDialog"
      :step="currentStep"
      :step-index="currentStepIndex"
      @success="handleStepSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Edit, Delete, Rank } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import CustomTemplateService, {
  type TemplateSequence,
  type TemplateStep,
  type CustomTemplate
} from '@/services/customTemplate'
import StepFormDialog from './StepFormDialog.vue'

// Props
interface Props {
  modelValue: boolean
  sequence?: TemplateSequence | null
}

const props = withDefaults(defineProps<Props>(), {
  sequence: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const categories = ref<string[]>([])
const availableTags = ref<string[]>([])
const templates = ref<CustomTemplate[]>([])

// 步骤编辑
const showStepDialog = ref(false)
const currentStep = ref<Partial<TemplateStep> | null>(null)
const currentStepIndex = ref(-1)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  isActive: true,
  tags: [] as string[],
  notes: '',
  steps: [] as Array<Partial<TemplateStep> & { tempId: string }>
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.sequence)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入序列名称', trigger: 'blur' },
    { min: 2, max: 100, message: '序列名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入序列描述', trigger: 'blur' },
    { min: 5, max: 500, message: '描述长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 方法定义
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    category: '',
    isActive: true,
    tags: [],
    notes: '',
    steps: []
  })
}

// 监听器
watch(() => props.sequence, (newSequence) => {
  if (newSequence) {
    Object.assign(form, {
      name: newSequence.name,
      description: newSequence.description,
      category: newSequence.category,
      isActive: newSequence.isActive,
      tags: [...(newSequence.tags || [])],
      notes: newSequence.notes || '',
      steps: (newSequence.steps || []).map((step, index) => ({
        ...step,
        tempId: `step_${index}_${Date.now()}`
      }))
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadCategories()
  loadTags()
  loadTemplates()
})

// 方法
const loadCategories = async () => {
  try {
    categories.value = await CustomTemplateService.getCategories()
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadTags = async () => {
  try {
    availableTags.value = await CustomTemplateService.getTags()
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const loadTemplates = async () => {
  try {
    const result = await CustomTemplateService.getTemplates({ page: 1, pageSize: 1000 })
    templates.value = result.items
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

const addStep = () => {
  currentStep.value = {
    stepOrder: form.steps.length + 1,
    actionType: 'click',
    description: '',
    parameters: {},
    timeoutSeconds: 5,
    maxRetries: 3,
    isActive: true
  }
  currentStepIndex.value = -1
  showStepDialog.value = true
}

const editStep = (step: Partial<TemplateStep> & { tempId: string }, index: number) => {
  currentStep.value = { ...step }
  currentStepIndex.value = index
  showStepDialog.value = true
}

const removeStep = (index: number) => {
  form.steps.splice(index, 1)
  updateStepOrder()
}

const updateStepOrder = () => {
  form.steps.forEach((step, index) => {
    step.stepOrder = index + 1
  })
}

const handleStepSuccess = (step: Partial<TemplateStep>) => {
  const stepWithId = {
    ...step,
    tempId: currentStepIndex.value >= 0
      ? form.steps[currentStepIndex.value].tempId
      : `step_${Date.now()}_${Math.random()}`
  }

  // 如果是编辑现有步骤，确保保留原始ID
  if (currentStepIndex.value >= 0) {
    const originalStep = form.steps[currentStepIndex.value]
    // 保留原始步骤的ID（如果存在）
    if (originalStep.id && !stepWithId.id) {
      stepWithId.id = originalStep.id
    }
    form.steps[currentStepIndex.value] = stepWithId
  } else {
    form.steps.push(stepWithId)
  }

  updateStepOrder()
}

const getTemplateName = (templateId: number) => {
  const template = templates.value.find(t => t.id === templateId)
  return template?.name || `模板 #${templateId}`
}

const getActionTypeLabel = (actionType: string) => {
  const labels: Record<string, string> = {
    'click': '点击',
    'wait': '等待',
    'input': '输入',
    'delay': '延迟',
    'screenshot': '截图',
    'verify': '验证',
    'scroll': '滚动',
    'key_press': '按键'
  }
  return labels[actionType] || actionType
}

const getActionTypeTag = (actionType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const tags: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'click': 'primary',
    'wait': 'info',
    'input': 'success',
    'delay': 'warning',
    'screenshot': 'danger',
    'verify': 'info',
    'scroll': 'warning',
    'key_press': 'primary'
  }
  return tags[actionType] || 'info'
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    const sequenceData = {
      ...form,
      steps: form.steps.map(({ tempId, ...step }) => {
        // 保留原始ID（如果存在），用于后端识别是更新还是创建
        const stepData = { ...step }
        // 确保ID字段直接在步骤对象上，而不是在parameters中
        if (step.id) {
          stepData.id = step.id
        }

        console.log('处理步骤数据:', {
          原始步骤: step,
          处理后: stepData,
          有ID: !!step.id,
          ID值: step.id,
          stepOrder: step.stepOrder,
          actionType: step.actionType
        })

        return stepData
      })
    }

    console.log('提交序列数据:', {
      序列ID: props.sequence?.id,
      是否编辑: isEdit.value,
      步骤数量: sequenceData.steps.length,
      步骤详情: sequenceData.steps.map(s => ({ id: s.id, stepOrder: s.stepOrder, actionType: s.actionType }))
    })

    if (isEdit.value && props.sequence) {
      await CustomTemplateService.updateSequence(props.sequence.id, sequenceData as any)
      ElMessage.success('序列更新成功')
    } else {
      await CustomTemplateService.createSequence(sequenceData as any)
      ElMessage.success('序列创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存序列失败:', error)
    ElMessage.error('保存序列失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  resetForm()
}
</script>

<style scoped lang="scss">
.steps-container {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;

  .steps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    span {
      font-weight: 500;
      color: #303133;
    }
  }

  .empty-steps {
    padding: 20px 0;
  }

  .step-item {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }

    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      .step-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .step-handle {
          cursor: move;
          color: #909399;

          &:hover {
            color: #409eff;
          }
        }

        .step-number {
          font-weight: 500;
          color: #303133;
        }
      }

      .step-actions {
        display: flex;
        gap: 4px;
      }
    }

    .step-content {
      padding: 12px 16px;

      .step-description {
        margin: 0 0 8px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }

      .step-details {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;

        .detail-item {
          font-size: 12px;
          color: #909399;
          background-color: #f0f2f5;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 拖拽样式
:deep(.sortable-ghost) {
  opacity: 0.5;
}

:deep(.sortable-chosen) {
  transform: scale(1.02);
}
</style>
