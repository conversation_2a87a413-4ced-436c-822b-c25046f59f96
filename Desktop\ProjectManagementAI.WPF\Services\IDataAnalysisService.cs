using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 数据分析服务接口 - 项目数据可视化和分析
    /// </summary>
    public interface IDataAnalysisService
    {
        /// <summary>
        /// 生成项目仪表板数据
        /// </summary>
        Task<DashboardData> GenerateDashboardDataAsync(int? projectId = null);

        /// <summary>
        /// 项目进度分析
        /// </summary>
        Task<ProgressAnalysis> AnalyzeProjectProgressAsync(int projectId);

        /// <summary>
        /// 团队效率分析
        /// </summary>
        Task<TeamEfficiencyAnalysis> AnalyzeTeamEfficiencyAsync(int? teamId = null);

        /// <summary>
        /// 技术栈分析
        /// </summary>
        Task<TechnologyStackAnalysis> AnalyzeTechnologyStackAsync();

        /// <summary>
        /// 风险趋势分析
        /// </summary>
        Task<RiskTrendAnalysis> AnalyzeRiskTrendsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 成本效益分析
        /// </summary>
        Task<CostBenefitAnalysis> AnalyzeCostBenefitAsync(int projectId);

        /// <summary>
        /// 质量指标分析
        /// </summary>
        Task<QualityMetricsAnalysis> AnalyzeQualityMetricsAsync(int projectId);

        /// <summary>
        /// 预测分析
        /// </summary>
        Task<PredictiveAnalysis> PerformPredictiveAnalysisAsync(int projectId);

        /// <summary>
        /// 生成自定义报表
        /// </summary>
        Task<CustomReport> GenerateCustomReportAsync(ReportConfiguration config);

        /// <summary>
        /// 导出分析数据
        /// </summary>
        Task<ExportResult> ExportAnalysisDataAsync(string analysisType, ExportFormat format);
    }

    #region 数据模型

    /// <summary>
    /// 仪表板数据
    /// </summary>
    public class DashboardData
    {
        public ProjectOverview ProjectOverview { get; set; } = new();
        public List<ChartData> Charts { get; set; } = new();
        public List<KPIMetric> KPIs { get; set; } = new();
        public List<RecentActivity> RecentActivities { get; set; } = new();
        public List<Alert> Alerts { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// 项目概览
    /// </summary>
    public class ProjectOverview
    {
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int DelayedProjects { get; set; }
        public double OverallProgress { get; set; }
        public double AverageQualityScore { get; set; }
        public TimeSpan AverageDeliveryTime { get; set; }
    }

    /// <summary>
    /// 图表数据
    /// </summary>
    public class ChartData
    {
        public string ChartId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public ChartType Type { get; set; }
        public List<DataSeries> Series { get; set; } = new();
        public List<string> Labels { get; set; } = new();
        public ChartConfiguration Configuration { get; set; } = new();
    }

    /// <summary>
    /// 数据系列
    /// </summary>
    public class DataSeries
    {
        public string Name { get; set; } = string.Empty;
        public List<double> Data { get; set; } = new();
        public string Color { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }

    /// <summary>
    /// 图表配置
    /// </summary>
    public class ChartConfiguration
    {
        public string XAxisTitle { get; set; } = string.Empty;
        public string YAxisTitle { get; set; } = string.Empty;
        public bool ShowLegend { get; set; } = true;
        public bool ShowGrid { get; set; } = true;
        public Dictionary<string, object> CustomOptions { get; set; } = new();
    }

    /// <summary>
    /// KPI指标
    /// </summary>
    public class KPIMetric
    {
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
        public double PreviousValue { get; set; }
        public double ChangePercentage { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string Trend { get; set; } = string.Empty; // Up, Down, Stable
        public string Status { get; set; } = string.Empty; // Good, Warning, Critical
    }

    /// <summary>
    /// 最近活动
    /// </summary>
    public class RecentActivity
    {
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string User { get; set; } = string.Empty;
        public int? ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 警报
    /// </summary>
    public class Alert
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public AlertLevel Level { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public string ActionUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 进度分析
    /// </summary>
    public class ProgressAnalysis
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public double CurrentProgress { get; set; }
        public double ExpectedProgress { get; set; }
        public double ProgressVelocity { get; set; }
        public DateTime EstimatedCompletionDate { get; set; }
        public List<MilestoneProgress> Milestones { get; set; } = new();
        public List<ProgressTrend> Trends { get; set; } = new();
    }

    /// <summary>
    /// 里程碑进度
    /// </summary>
    public class MilestoneProgress
    {
        public string Name { get; set; } = string.Empty;
        public DateTime PlannedDate { get; set; }
        public DateTime? ActualDate { get; set; }
        public double Progress { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 进度趋势
    /// </summary>
    public class ProgressTrend
    {
        public DateTime Date { get; set; }
        public double Progress { get; set; }
        public double Velocity { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    /// <summary>
    /// 团队效率分析
    /// </summary>
    public class TeamEfficiencyAnalysis
    {
        public double OverallEfficiency { get; set; }
        public List<TeamMemberEfficiency> MemberEfficiencies { get; set; } = new();
        public List<EfficiencyTrend> Trends { get; set; } = new();
        public List<BottleneckAnalysis> Bottlenecks { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 团队成员效率
    /// </summary>
    public class TeamMemberEfficiency
    {
        public string MemberName { get; set; } = string.Empty;
        public double EfficiencyScore { get; set; }
        public int TasksCompleted { get; set; }
        public TimeSpan AverageTaskTime { get; set; }
        public double QualityScore { get; set; }
    }

    /// <summary>
    /// 效率趋势
    /// </summary>
    public class EfficiencyTrend
    {
        public DateTime Date { get; set; }
        public double EfficiencyScore { get; set; }
        public int TasksCompleted { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    /// <summary>
    /// 瓶颈分析
    /// </summary>
    public class BottleneckAnalysis
    {
        public string Area { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Impact { get; set; }
        public List<string> Suggestions { get; set; } = new();
    }

    /// <summary>
    /// 技术栈分析
    /// </summary>
    public class TechnologyStackAnalysis
    {
        public List<TechnologyUsage> TechnologyUsages { get; set; } = new();
        public List<TechnologyTrend> Trends { get; set; } = new();
        public List<TechnologyRecommendation> Recommendations { get; set; } = new();
        public Dictionary<string, int> PopularityRanking { get; set; } = new();
    }

    /// <summary>
    /// 技术使用情况
    /// </summary>
    public class TechnologyUsage
    {
        public string Technology { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int ProjectCount { get; set; }
        public double UsagePercentage { get; set; }
        public double SuccessRate { get; set; }
        public List<string> Projects { get; set; } = new();
    }

    /// <summary>
    /// 技术趋势
    /// </summary>
    public class TechnologyTrend
    {
        public string Technology { get; set; } = string.Empty;
        public List<TrendPoint> TrendPoints { get; set; } = new();
        public string TrendDirection { get; set; } = string.Empty; // Rising, Falling, Stable
    }

    /// <summary>
    /// 趋势点
    /// </summary>
    public class TrendPoint
    {
        public DateTime Date { get; set; }
        public double Value { get; set; }
    }

    /// <summary>
    /// 技术推荐
    /// </summary>
    public class TechnologyRecommendation
    {
        public string Technology { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public double RecommendationScore { get; set; }
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// 风险趋势分析
    /// </summary>
    public class RiskTrendAnalysis
    {
        public List<RiskTrend> RiskTrends { get; set; } = new();
        public double OverallRiskLevel { get; set; }
        public List<RiskCategory> RiskCategories { get; set; } = new();
        public List<string> MitigationSuggestions { get; set; } = new();
    }

    /// <summary>
    /// 风险趋势
    /// </summary>
    public class RiskTrend
    {
        public DateTime Date { get; set; }
        public double RiskLevel { get; set; }
        public string RiskType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 风险类别
    /// </summary>
    public class RiskCategory
    {
        public string Category { get; set; } = string.Empty;
        public double RiskLevel { get; set; }
        public int IncidentCount { get; set; }
        public string TrendDirection { get; set; } = string.Empty;
    }

    /// <summary>
    /// 成本效益分析
    /// </summary>
    public class CostBenefitAnalysis
    {
        public int ProjectId { get; set; }
        public double TotalCost { get; set; }
        public double EstimatedBenefit { get; set; }
        public double ROI { get; set; }
        public double PaybackPeriod { get; set; }
        public List<CostBreakdown> CostBreakdowns { get; set; } = new();
        public List<BenefitBreakdown> BenefitBreakdowns { get; set; } = new();
    }

    /// <summary>
    /// 成本分解
    /// </summary>
    public class CostBreakdown
    {
        public string Category { get; set; } = string.Empty;
        public double Amount { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 效益分解
    /// </summary>
    public class BenefitBreakdown
    {
        public string Category { get; set; } = string.Empty;
        public double Amount { get; set; }
        public double Percentage { get; set; }
    }

    /// <summary>
    /// 质量指标分析
    /// </summary>
    public class QualityMetricsAnalysis
    {
        public int ProjectId { get; set; }
        public double OverallQualityScore { get; set; }
        public List<QualityMetric> Metrics { get; set; } = new();
        public List<QualityTrend> Trends { get; set; } = new();
        public List<string> ImprovementSuggestions { get; set; } = new();
    }

    /// <summary>
    /// 质量指标
    /// </summary>
    public class QualityMetric
    {
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
        public double Target { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
    }

    /// <summary>
    /// 质量趋势
    /// </summary>
    public class QualityTrend
    {
        public DateTime Date { get; set; }
        public double QualityScore { get; set; }
        public string MetricName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 预测分析
    /// </summary>
    public class PredictiveAnalysis
    {
        public int ProjectId { get; set; }
        public List<Prediction> Predictions { get; set; } = new();
        public double ConfidenceLevel { get; set; }
        public string Model { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// 预测
    /// </summary>
    public class Prediction
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Probability { get; set; }
        public DateTime PredictedDate { get; set; }
        public double Impact { get; set; }
    }

    /// <summary>
    /// 自定义报表
    /// </summary>
    public class CustomReport
    {
        public string Title { get; set; } = string.Empty;
        public List<ReportSection> Sections { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 报表配置
    /// </summary>
    public class ReportConfiguration
    {
        public string Title { get; set; } = string.Empty;
        public List<string> DataSources { get; set; } = new();
        public Dictionary<string, object> Filters { get; set; } = new();
        public List<string> Metrics { get; set; } = new();
        public string Format { get; set; } = string.Empty;
    }

    /// <summary>
    /// 报表章节
    /// </summary>
    public class ReportSection
    {
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public List<ChartData> Charts { get; set; } = new();
        public List<TableData> Tables { get; set; } = new();
    }

    /// <summary>
    /// 表格数据
    /// </summary>
    public class TableData
    {
        public string Title { get; set; } = string.Empty;
        public List<string> Headers { get; set; } = new();
        public List<List<object>> Rows { get; set; } = new();
    }

    /// <summary>
    /// 导出结果
    /// </summary>
    public class ExportResult
    {
        public bool Success { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 图表类型
    /// </summary>
    public enum ChartType
    {
        Line,
        Bar,
        Pie,
        Doughnut,
        Area,
        Scatter,
        Radar,
        Gauge
    }

    /// <summary>
    /// 警报级别
    /// </summary>
    public enum AlertLevel
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// 导出格式
    /// </summary>
    public enum ExportFormat
    {
        Excel,
        PDF,
        CSV,
        JSON,
        XML
    }

    #endregion
}
