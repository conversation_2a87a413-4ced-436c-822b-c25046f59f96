<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Commands\**" />
    <Compile Remove="Handlers\**" />
    <Compile Remove="Mappings\**" />
    <Compile Remove="Queries\**" />
    <Compile Remove="Validators\**" />
    <EmbeddedResource Remove="Commands\**" />
    <EmbeddedResource Remove="Handlers\**" />
    <EmbeddedResource Remove="Mappings\**" />
    <EmbeddedResource Remove="Queries\**" />
    <EmbeddedResource Remove="Validators\**" />
    <None Remove="Commands\**" />
    <None Remove="Handlers\**" />
    <None Remove="Mappings\**" />
    <None Remove="Queries\**" />
    <None Remove="Validators\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="FluentValidation" Version="11.8.1" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
	  <PackageReference Include="SqlSugarCore" Version="5.1.4.196" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTOs\" />
    <Folder Include="Entities\" />
    <Folder Include="Enums\" />
    <Folder Include="Exceptions\" />
    <Folder Include="Interfaces\" />
    <Folder Include="Services\" />
  </ItemGroup>

</Project>
