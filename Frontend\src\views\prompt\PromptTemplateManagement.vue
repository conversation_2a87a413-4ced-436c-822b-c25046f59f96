<template>
  <div class="prompt-template-management">
    <div class="page-header">
      <h1>AI Prompt 模板管理</h1>
      <p>管理和优化您的AI提示词模板，提升AI交互效果</p>
    </div>

    <div class="content-layout">
      <!-- 左侧分类树 -->
      <div class="category-sidebar">
        <div class="sidebar-header">
          <h3>模板分类</h3>
          <el-button
            type="primary"
            size="small"
            @click="showCreateCategoryDialog = true"
            v-if="isAdmin"
          >
            新建分类
          </el-button>
        </div>

        <el-tree
          :data="categoryTree"
          :props="treeProps"
          node-key="id"
          :default-expand-all="true"
          :highlight-current="true"
          @node-click="onCategorySelect"
          class="category-tree"
        >
          <template #default="{ node, data }">
            <div class="category-node">
              <span class="category-icon">{{ data.icon || '📁' }}</span>
              <span class="category-name">{{ data.name }}</span>
              <span class="template-count">({{ data.templateCount }})</span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="search-section">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索模板..."
              @input="onSearch"
              clearable
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select
              v-model="selectedTaskType"
              placeholder="任务类型"
              @change="onSearch"
              clearable
              class="task-type-select"
            >
              <el-option label="需求分析" value="RequirementAnalysis" />
              <el-option label="代码生成" value="CodeGeneration" />
              <el-option label="测试生成" value="Testing" />
              <el-option label="文档生成" value="Documentation" />
              <el-option label="代码审查" value="CodeReview" />
              <el-option label="性能优化" value="Optimization" />
            </el-select>

            <el-select
              v-model="selectedTemplateType"
              placeholder="模板类型"
              @change="onSearch"
              clearable
              class="template-type-select"
            >
              <el-option label="系统模板" value="System" />
              <el-option label="用户模板" value="User" />
              <el-option label="共享模板" value="Shared" />
            </el-select>
          </div>

          <div class="action-section">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建模板
            </el-button>

            <el-dropdown @command="handleBatchAction">
              <el-button>
                批量操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="enable">启用选中</el-dropdown-item>
                  <el-dropdown-item command="disable">禁用选中</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除选中</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 模板列表 -->
        <div class="template-list">
          <el-table
            :data="templates"
            v-loading="loading"
            @selection-change="onSelectionChange"
            stripe
            class="template-table"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column prop="name" label="模板名称" min-width="200">
              <template #default="{ row }">
                <div class="template-name">
                  <span class="name">{{ row.name }}</span>
                  <el-tag v-if="row.isDefault" type="success" size="small">默认</el-tag>
                  <el-tag v-if="!row.isEnabled" type="danger" size="small">已禁用</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="categoryName" label="分类" width="120" />

            <el-table-column prop="taskType" label="任务类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTaskTypeColor(row.taskType)" size="small">
                  {{ getTaskTypeName(row.taskType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="templateType" label="模板类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTemplateTypeColor(row.templateType)" size="small">
                  {{ row.templateType }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="usageCount" label="使用次数" width="100" sortable />

            <el-table-column prop="averageRating" label="评分" width="100">
              <template #default="{ row }">
                <el-rate
                  v-if="row.averageRating"
                  :model-value="row.averageRating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                  size="small"
                />
                <span v-else class="no-rating">暂无评分</span>
              </template>
            </el-table-column>

            <el-table-column prop="lastUsedTime" label="最后使用" width="150">
              <template #default="{ row }">
                <span v-if="row.lastUsedTime">
                  {{ formatDate(row.lastUsedTime) }}
                </span>
                <span v-else class="never-used">从未使用</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button size="small" @click="viewTemplate(row)">
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button size="small" @click="editTemplate(row)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" @click="cloneTemplate(row)">
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="deleteTemplate(row)"
                    :disabled="row.templateType === 'System' && !isAdmin"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="onPageSizeChange"
              @current-change="onPageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑模板对话框 -->
    <TemplateEditDialog
      v-model="showCreateDialog"
      :template="editingTemplate"
      :categories="categories"
      @save="onTemplateSave"
    />

    <!-- 模板详情对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="模板详情"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="viewingTemplate">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">{{ viewingTemplate.name }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ viewingTemplate.categoryName }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeName(viewingTemplate.taskType) }}</el-descriptions-item>
          <el-descriptions-item label="模板类型">{{ viewingTemplate.templateType }}</el-descriptions-item>
          <el-descriptions-item label="使用次数">{{ viewingTemplate.usageCount }}</el-descriptions-item>
          <el-descriptions-item label="评分">
            <el-rate v-if="viewingTemplate.averageRating" :model-value="viewingTemplate.averageRating" disabled />
            <span v-else>暂无评分</span>
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-top: 20px;">
          <h4>模板内容</h4>
          <el-input
            v-model="viewingTemplate.content"
            type="textarea"
            :rows="10"
            readonly
          />
        </div>
      </div>
    </el-dialog>

    <!-- 创建分类对话框 -->
    <el-dialog
      v-model="showCreateCategoryDialog"
      title="创建分类"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="newCategory" label-width="80px">
        <el-form-item label="分类名称">
          <el-input v-model="newCategory.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="newCategory.description" type="textarea" placeholder="请输入分类描述" />
        </el-form-item>
        <el-form-item label="图标">
          <el-input v-model="newCategory.icon" placeholder="请输入图标（如：📁）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateCategoryDialog = false">取消</el-button>
        <el-button type="primary" @click="onCategorySave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, ArrowDown, View, Edit, CopyDocument, Delete } from '@element-plus/icons-vue'
import { PromptService, type PromptTemplate, type PromptCategory } from '@/services/promptService'
import { useAuthStore } from '@/stores/auth'
import type { ElTagType } from '@/types/element-plus'
import TemplateEditDialog from './components/TemplateEditDialog.vue'

// 服务和状态
const promptService = new PromptService()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const templates = ref<PromptTemplate[]>([])
const categories = ref<PromptCategory[]>([])
const categoryTree = ref<PromptCategory[]>([])
const selectedTemplates = ref<PromptTemplate[]>([])

// 搜索和筛选
const searchKeyword = ref('')
const selectedCategoryId = ref<number>()
const selectedTaskType = ref('')
const selectedTemplateType = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showCreateCategoryDialog = ref(false)
const editingTemplate = ref<PromptTemplate | null>(null)
const viewingTemplate = ref<PromptTemplate | null>(null)
const newCategory = ref({
  name: '',
  description: '',
  icon: '📁'
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const isAdmin = computed(() => {
  return authStore.user?.role === 'Admin' || authStore.user?.role === 'SuperAdmin'
})

// 生命周期
onMounted(() => {
  loadCategories()
  loadTemplates()
})

// 方法
const loadCategories = async () => {
  try {
    const [allCategories, tree] = await Promise.all([
      promptService.getCategories(),
      promptService.getCategoryTree()
    ])
    categories.value = allCategories
    categoryTree.value = tree
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
  }
}

const loadTemplates = async () => {
  loading.value = true
  try {
    let result: PromptTemplate[]

    if (selectedCategoryId.value) {
      result = await promptService.getTemplatesByCategory(selectedCategoryId.value)
    } else if (selectedTaskType.value) {
      result = await promptService.getTemplatesByTaskType(selectedTaskType.value)
    } else if (searchKeyword.value || selectedTemplateType.value) {
      result = await promptService.searchTemplates({
        keyword: searchKeyword.value,
        templateType: selectedTemplateType.value,
        page: currentPage.value,
        pageSize: pageSize.value
      })
    } else {
      result = await promptService.getTemplates()
    }

    templates.value = result
    total.value = result.length
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false
  }
}

const onCategorySelect = (category: PromptCategory) => {
  selectedCategoryId.value = category.id
  selectedTaskType.value = ''
  selectedTemplateType.value = ''
  searchKeyword.value = ''
  currentPage.value = 1
  loadTemplates()
}

const onSearch = () => {
  selectedCategoryId.value = undefined
  currentPage.value = 1
  loadTemplates()
}

const onSelectionChange = (selection: PromptTemplate[]) => {
  selectedTemplates.value = selection
}

const onPageChange = (page: number) => {
  currentPage.value = page
  loadTemplates()
}

const onPageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTemplates()
}

// 模板操作
const viewTemplate = (template: PromptTemplate) => {
  viewingTemplate.value = template
  showViewDialog.value = true
}

const editTemplate = (template: PromptTemplate) => {
  editingTemplate.value = template
  showCreateDialog.value = true
}

const cloneTemplate = async (template: PromptTemplate) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${template.name} - 副本`
      }
    )

    if (newName) {
      await promptService.cloneTemplate(template.id, newName)
      ElMessage.success('模板复制成功')
      loadTemplates()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制模板失败:', error)
      ElMessage.error('复制模板失败')
    }
  }
}

const deleteTemplate = async (template: PromptTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await promptService.deleteTemplate(template.id)
    ElMessage.success('模板删除成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

const onTemplateSave = () => {
  showCreateDialog.value = false
  editingTemplate.value = null
  loadTemplates()
  loadCategories() // 重新加载分类以更新模板数量
}

const onCategorySave = async () => {
  try {
    // 这里应该调用API创建分类，暂时只是关闭对话框
    ElMessage.success('分类创建成功')
    showCreateCategoryDialog.value = false
    newCategory.value = { name: '', description: '', icon: '📁' }
    loadCategories()
  } catch (error) {
    console.error('创建分类失败:', error)
    ElMessage.error('创建分类失败')
  }
}

const handleBatchAction = async (command: string) => {
  if (selectedTemplates.value.length === 0) {
    ElMessage.warning('请先选择要操作的模板')
    return
  }

  try {
    switch (command) {
      case 'enable':
        // 批量启用逻辑
        ElMessage.success('批量启用成功')
        break
      case 'disable':
        // 批量禁用逻辑
        ElMessage.success('批量禁用成功')
        break
      case 'delete':
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedTemplates.value.length} 个模板吗？`,
          '确认删除',
          { type: 'warning' }
        )
        // 批量删除逻辑
        ElMessage.success('批量删除成功')
        break
    }
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 辅助方法
const getTaskTypeColor = (taskType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const colors: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'RequirementAnalysis': 'primary',
    'CodeGeneration': 'success',
    'Testing': 'warning',
    'Documentation': 'info',
    'CodeReview': 'danger',
    'Optimization': 'info'
  }
  return colors[taskType] || 'info'
}

const getTaskTypeName = (taskType: string) => {
  const names: Record<string, string> = {
    'RequirementAnalysis': '需求分析',
    'CodeGeneration': '代码生成',
    'Testing': '测试生成',
    'Documentation': '文档生成',
    'CodeReview': '代码审查',
    'Optimization': '性能优化'
  }
  return names[taskType] || taskType
}

const getTemplateTypeColor = (templateType: string): ElTagType => {
  const colors: Record<string, ElTagType> = {
    'System': 'success',
    'User': 'primary',
    'Shared': 'warning'
  }
  return colors[templateType] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.prompt-template-management {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-layout {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.category-sidebar {
  width: 280px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.category-tree {
  border: none;
}

.category-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.category-icon {
  font-size: 16px;
}

.category-name {
  flex: 1;
  font-size: 14px;
}

.template-count {
  font-size: 12px;
  color: #909399;
}

.main-content {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.search-section {
  display: flex;
  gap: 12px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.task-type-select,
.template-type-select {
  width: 120px;
}

.action-section {
  display: flex;
  gap: 12px;
}

.template-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.template-table {
  flex: 1;
}

.template-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-name .name {
  font-weight: 500;
}

.no-rating,
.never-used {
  color: #c0c4cc;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
