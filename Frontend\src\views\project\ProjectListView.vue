<template>
  <div class="project-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">项目管理</h1>
        <p class="page-subtitle">管理您的AI驱动开发项目</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createProject">
          <el-icon><Plus /></el-icon>
          创建项目
        </el-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filter-section">
      <el-card>
        <el-row :gutter="16" align="middle">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索项目名称或描述"
              :prefix-icon="Search"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="项目状态"
              clearable
              @change="handleStatusFilter"
            >
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="priorityFilter"
              placeholder="优先级"
              clearable
              @change="handlePriorityFilter"
            >
              <el-option
                v-for="priority in priorityOptions"
                :key="priority.value"
                :label="priority.label"
                :value="priority.value"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button @click="resetFilters">重置</el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 项目列表 -->
    <div class="project-grid">
      <el-row :gutter="20">
        <el-col
          v-for="project in projects"
          :key="project.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
        >
          <el-card class="project-card" @click="viewProject(project.id)">
            <div class="project-header">
              <div class="project-title">{{ project.name }}</div>
              <el-dropdown @command="handleProjectAction">
                <el-button type="text" size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`view-${project.id}`">查看详情</el-dropdown-item>
                    <el-dropdown-item :command="`edit-${project.id}`">编辑项目</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${project.id}`" divided>删除项目</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <div class="project-description">
              {{ project.description || '暂无描述' }}
            </div>
            
            <div class="project-meta">
              <div class="meta-item" v-if="(project as any).projectType">
                <el-tag :type="getProjectTypeTagType((project as any).projectType)" size="small">
                  {{ getProjectTypeIcon((project as any).projectType) }} {{ getProjectTypeText((project as any).projectType) }}
                </el-tag>
              </div>
              <div class="meta-item">
                <el-tag :type="getStatusType(project.status)" size="small">
                  {{ getStatusLabel(project.status) }}
                </el-tag>
              </div>
              <div class="meta-item">
                <el-tag :type="getPriorityType(project.priority)" size="small">
                  {{ getPriorityLabel(project.priority) }}
                </el-tag>
              </div>
            </div>
            
            <div class="project-progress">
              <div class="progress-label">
                <span>进度</span>
                <span>{{ project.progress }}%</span>
              </div>
              <el-progress :percentage="project.progress" :show-text="false" />
            </div>
            
            <div class="project-footer">
              <div class="project-owner">
                <el-icon><User /></el-icon>
                {{ project.ownerName }}
              </div>
              <div class="project-date">
                {{ formatDate(project.updatedAt || project.createdAt) }}
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && projects.length === 0" class="empty-state">
      <el-empty description="暂无项目">
        <el-button type="primary" @click="createProject">创建第一个项目</el-button>
      </el-empty>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.totalCount > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.pageNumber"
        v-model:page-size="pagination.pageSize"
        :total="pagination.totalCount"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading text="加载中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { ProjectService } from '@/services/project'
import { ElMessage, ElMessageBox } from 'element-plus'
type ElTagType = 'success' | 'primary' | 'warning' | 'info' | 'danger'
import dayjs from 'dayjs'
import {
  Plus,
  Search,
  MoreFilled,
  User
} from '@element-plus/icons-vue'

const router = useRouter()
const projectStore = useProjectStore()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')

// 计算属性
const projects = computed(() => projectStore.projects)
const pagination = computed(() => projectStore.pagination)
const loading = computed(() => projectStore.loading)

const statusOptions = computed(() => ProjectService.getStatusOptions())
const priorityOptions = computed(() => ProjectService.getPriorityOptions())

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('MM-DD HH:mm')
}

const getStatusType = (status: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    'Planning': 'info',
    'InProgress': 'warning',
    'Testing': 'warning',
    'Deployed': 'success',
    'Completed': 'success',
    'Paused': 'danger',
    'Cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

const getPriorityType = (priority: string): ElTagType => {
  const typeMap: Record<string, ElTagType> = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'danger',
    'Critical': 'danger'
  }
  return typeMap[priority] || 'info'
}

const getStatusLabel = (status: string) => {
  return ProjectService.getStatusLabel(status)
}

const getPriorityLabel = (priority: string) => {
  return ProjectService.getPriorityLabel(priority)
}

// 项目类型相关方法
const getProjectTypeIcon = (projectType: string) => {
  const iconMap: Record<string, string> = {
    NEW_PROJECT: '🆕',
    FEATURE_ENHANCEMENT: '⚡',
    MAINTENANCE: '🔧',
    REFACTORING: '🔄',
    MIGRATION: '📦'
  }
  return iconMap[projectType] || '📋'
}

const getProjectTypeText = (projectType: string) => {
  const textMap: Record<string, string> = {
    NEW_PROJECT: '新项目',
    FEATURE_ENHANCEMENT: '功能增强',
    MAINTENANCE: '维护项目',
    REFACTORING: '重构项目',
    MIGRATION: '迁移项目'
  }
  return textMap[projectType] || projectType
}

const getProjectTypeTagType = (projectType: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    NEW_PROJECT: 'success',
    FEATURE_ENHANCEMENT: 'primary',
    MAINTENANCE: 'warning',
    REFACTORING: 'info',
    MIGRATION: 'danger'
  }
  return typeMap[projectType] || 'info'
}

const createProject = () => {
  router.push('/projects/create')
}

const viewProject = (id: number) => {
  router.push(`/projects/${id}`)
}

const handleSearch = () => {
  projectStore.searchProjects(searchKeyword.value)
}

const handleStatusFilter = () => {
  projectStore.filterByStatus(statusFilter.value)
}

const handlePriorityFilter = () => {
  // 实现优先级过滤逻辑
  projectStore.fetchProjects({
    status: statusFilter.value,
    search: searchKeyword.value,
    pageNumber: 1
  })
}

const resetFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  priorityFilter.value = ''
  projectStore.resetFilters()
}

const handleProjectAction = async (command: string) => {
  const [action, idStr] = command.split('-')
  const id = parseInt(idStr)
  
  switch (action) {
    case 'view':
      viewProject(id)
      break
    case 'edit':
      router.push(`/projects/${id}/edit`)
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          '确定要删除这个项目吗？此操作不可恢复。',
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await projectStore.deleteProject(id)
        ElMessage.success('项目删除成功')
      } catch (error) {
        // 用户取消或删除失败
      }
      break
  }
}

const handleSizeChange = (size: number) => {
  projectStore.changePageSize(size)
}

const handleCurrentChange = (page: number) => {
  projectStore.changePage(page)
}

// 组件挂载时获取数据
onMounted(() => {
  projectStore.fetchProjects()
})
</script>

<style lang="scss" scoped>
.project-list {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .page-subtitle {
        margin: 0;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .filter-section {
    margin-bottom: 24px;
  }
  
  .project-grid {
    margin-bottom: 24px;
    
    .project-card {
      height: 280px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }
      
      :deep(.el-card__body) {
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
      }
      
      .project-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        
        .project-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1.4;
          flex: 1;
          margin-right: 8px;
        }
      }
      
      .project-description {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .project-meta {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
        
        .meta-item {
          flex-shrink: 0;
        }
      }
      
      .project-progress {
        margin-bottom: 16px;
        
        .progress-label {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 8px;
        }
      }
      
      .project-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        
        .project-owner {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }
  
  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-list {
    .page-header {
      flex-direction: column;
      gap: 16px;
      
      .header-right {
        align-self: stretch;
      }
    }
    
    .filter-section {
      :deep(.el-row) {
        flex-direction: column;
        gap: 12px;
      }
    }
  }
}
</style>
