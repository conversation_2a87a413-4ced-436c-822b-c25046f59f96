graph TB
    %% 用户界面层
    subgraph "用户界面层"
        Web["💻<br/>Web界面<br/>(项目管理)"]
        Mobile["📱<br/>移动端<br/>(监控查看)"]
        IDE["🔧<br/>IDE插件<br/>(开发工具)"]
    end
    
    %% AI核心处理层
    subgraph "AI核心处理层"
        AICore["🤖 AI需求分析引擎<br/><br/>输入用户需求或项目描述: requirements<br/>自然语言处理<br/>需求理解与分类<br/>可行性评估"]
    end
    
    %% 文档生成层
    subgraph "文档生成服务"
        DocGen["📄 需求规格书生成器"]
        ERGen["🗄️ ER图生成器<br/>数据库实体设计<br/>实体关系建模"]
        ContextGen["🏗️ Context图生成器<br/>系统架构设计<br/>组件交互关系"]
    end
    
    %% AI模型层
    subgraph "AI模型服务层"
        CodeModel["Azure OpenAI / GPT-4<br/>代码生成模型"]
        AnalysisModel["需求分析模型"]
        TestModel["测试生成模型"]
    end
    
    %% 代码生成层
    subgraph "代码生成引擎"
        VueGen["Vue组件生成器<br/>前端界面代码"]
        CSharpGen["C# API生成器<br/>后端服务代码"]
        SQLGen["SQL Server脚本生成器<br/>数据库结构代码"]
        ConfigGen["配置文件生成器"]
    end
    
    %% 自动化流水线
    subgraph "自动化流水线"
        TestPipeline["🧪 自动化测试<br/>单元测试执行<br/>集成测试验证<br/>UI自动化测试"]
        DeployPipeline["🚀 自动部署<br/>CI/CD流水线<br/>应用打包发布<br/>环境配置部署"]
    end
    
    %% 运维监控层
    subgraph "智能运维层"
        Monitor["📊 系统监控<br/>性能指标分析<br/>自动扩缩容"]
        IssueHandler["🔧 问题处理<br/>故障自动检测<br/>智能故障诊断<br/>自动修复机制"]
    end
    
    %% 数据存储层
    subgraph "数据存储层"
        ProjectDB[("📊 项目数据库<br/>SQL Server<br/>项目信息<br/>需求数据")]
        CodeRepo[("📁 代码仓库<br/>Git Repository<br/>生成的代码<br/>版本控制")]
        KnowledgeBase[("🧠 知识库<br/>向量数据库<br/>最佳实践<br/>代码模板")]
    end
    
    %% 连接关系
    Web --> AICore
    Mobile --> AICore
    IDE --> AICore
    
    AICore -->|1:http请求:requirements| DocGen
    AICore -->|2:需求向量化查询| AnalysisModel
    
    DocGen --> ERGen
    DocGen --> ContextGen
    
    ERGen -->|3:组织context| CodeModel
    ContextGen -->|3:组织context| CodeModel
    
    CodeModel -->|4:交给模型回答| VueGen
    CodeModel --> CSharpGen
    CodeModel --> SQLGen
    CodeModel --> ConfigGen
    
    VueGen --> TestPipeline
    CSharpGen --> TestPipeline
    SQLGen --> TestPipeline
    ConfigGen --> TestPipeline
    
    TestPipeline --> DeployPipeline
    DeployPipeline --> Monitor
    Monitor --> IssueHandler
    
    IssueHandler -->|5:返回用户应答| AICore
    
    %% 数据流
    AICore -.-> ProjectDB
    CodeModel -.-> KnowledgeBase
    VueGen -.-> CodeRepo
    CSharpGen -.-> CodeRepo
    SQLGen -.-> CodeRepo
    
    %% 反馈循环
    IssueHandler -.->|持续优化<br/>性能数据分析<br/>用户反馈收集| AICore
    
    %% 样式定义
    classDef userInterface fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef aiCore fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef docService fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef aiModel fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef codeGen fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef pipeline fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef ops fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef storage fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    %% 应用样式
    class Web,Mobile,IDE userInterface
    class AICore aiCore
    class DocGen,ERGen,ContextGen docService
    class CodeModel,AnalysisModel,TestModel aiModel
    class VueGen,CSharpGen,SQLGen,ConfigGen codeGen
    class TestPipeline,DeployPipeline pipeline
    class Monitor,IssueHandler ops
    class ProjectDB,CodeRepo,KnowledgeBase storage
