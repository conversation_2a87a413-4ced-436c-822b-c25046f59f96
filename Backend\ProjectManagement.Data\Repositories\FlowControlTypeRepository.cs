using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 流程控制类型仓储实现
    /// </summary>
    public class FlowControlTypeRepository : BaseRepository<FlowControlType>, IFlowControlTypeRepository
    {
        public FlowControlTypeRepository(ISqlSugarClient db, ILogger<BaseRepository<FlowControlType>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 分页查询流程控制类型
        /// </summary>
        public async Task<PagedResultDto<FlowControlType>> GetPagedAsync(FlowControlTypeQueryDto query)
        {
            var queryable = _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                queryable = queryable.Where(x => 
                    x.Label.Contains(query.Keyword) || 
                    x.Value.Contains(query.Keyword) ||
                    (x.Description != null && x.Description.Contains(query.Keyword)));
            }

            // 执行类型过滤
            if (!string.IsNullOrEmpty(query.ExecutionType))
            {
                queryable = queryable.Where(x => x.ExecutionType == query.ExecutionType);
            }

            // 状态过滤
            if (query.IsActive.HasValue)
            {
                queryable = queryable.Where(x => x.IsActive == query.IsActive.Value);
            }

            // 是否内置类型过滤
            if (query.IsBuiltIn.HasValue)
            {
                queryable = queryable.Where(x => x.IsBuiltIn == query.IsBuiltIn.Value);
            }

            // 是否需要目标步骤过滤
            if (query.RequiresTarget.HasValue)
            {
                queryable = queryable.Where(x => x.RequiresTarget == query.RequiresTarget.Value);
            }

            // 是否可以嵌套过滤
            if (query.CanNest.HasValue)
            {
                queryable = queryable.Where(x => x.CanNest == query.CanNest.Value);
            }

            // 排序
            queryable = queryable.OrderBy(x => x.ExecutionType).OrderBy(x => x.SortOrder);

            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            return new PagedResultDto<FlowControlType>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 根据值获取流程控制类型
        /// </summary>
        public async Task<FlowControlType?> GetByValueAsync(string value)
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => x.Value == value && !x.IsDeleted)
                .FirstAsync();
        }

        /// <summary>
        /// 根据执行类型获取流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetByExecutionTypeAsync(string executionType)
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => x.ExecutionType == executionType && !x.IsDeleted && x.IsActive)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取启用的流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetActiveAsync()
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted && x.IsActive)
                .OrderBy(x => x.ExecutionType)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取启用的流程控制类型（按执行类型分组）
        /// </summary>
        public async Task<Dictionary<string, List<FlowControlType>>> GetActiveByExecutionTypeAsync()
        {
            var flowControlTypes = await GetActiveAsync();
            return flowControlTypes.GroupBy(x => x.ExecutionType ?? "unknown")
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        /// <summary>
        /// 检查流程控制类型值是否已存在
        /// </summary>
        public async Task<bool> ExistsValueAsync(string value, int? excludeId = null)
        {
            var queryable = _db.Queryable<FlowControlType>()
                .Where(x => x.Value == value && !x.IsDeleted);

            if (excludeId.HasValue)
            {
                queryable = queryable.Where(x => x.Id != excludeId.Value);
            }

            return await queryable.AnyAsync();
        }

        /// <summary>
        /// 获取最大排序顺序
        /// </summary>
        public async Task<int> GetMaxSortOrderAsync(string? executionType = null)
        {
            var queryable = _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted);

            if (!string.IsNullOrEmpty(executionType))
            {
                queryable = queryable.Where(x => x.ExecutionType == executionType);
            }

            var hasData = await queryable.AnyAsync();
            if (!hasData)
            {
                return 0;
            }

            return await queryable.MaxAsync(x => x.SortOrder);
        }

        /// <summary>
        /// 批量更新排序顺序
        /// </summary>
        public async Task<bool> UpdateSortOrdersAsync(Dictionary<int, int> sortOrders)
        {
            try
            {
                foreach (var kvp in sortOrders)
                {
                    await _db.Updateable<FlowControlType>()
                        .SetColumns(x => x.SortOrder == kvp.Value)
                        .SetColumns(x => x.UpdatedTime == DateTime.Now)
                        .Where(x => x.Id == kvp.Key)
                        .ExecuteCommandAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 启用/禁用流程控制类型
        /// </summary>
        public async Task<bool> SetActiveStatusAsync(int id, bool isActive)
        {
            return await _db.Updateable<FlowControlType>()
                .SetColumns(x => x.IsActive == isActive)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 获取内置流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetBuiltInAsync()
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted && x.IsBuiltIn)
                .OrderBy(x => x.ExecutionType)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取自定义流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetCustomAsync()
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted && !x.IsBuiltIn)
                .OrderBy(x => x.ExecutionType)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取需要目标步骤的流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetRequiresTargetAsync()
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted && x.IsActive && x.RequiresTarget)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取可以嵌套的流程控制类型
        /// </summary>
        public async Task<List<FlowControlType>> GetCanNestAsync()
        {
            return await _db.Queryable<FlowControlType>()
                .Where(x => !x.IsDeleted && x.IsActive && x.CanNest)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }
    }
}
