import { test as base } from '@playwright/test';
import { TestHelpers } from '../utils/test-helpers';
import { TestDataFactory } from '../utils/test-data';

// 扩展基础测试类型
type TestFixtures = {
  helpers: TestHelpers;
  testData: typeof TestDataFactory;
};

/**
 * 扩展的测试fixture，提供常用的测试工具
 */
export const test = base.extend<TestFixtures>({
  // 测试辅助工具
  helpers: async ({ page }, use) => {
    const helpers = new TestHelpers(page);
    await use(helpers);
  },

  // 测试数据工厂
  testData: async ({}, use) => {
    await use(TestDataFactory);
  }
});

export { expect } from '@playwright/test';
