# UI自动化管理系统 v2.0

## 📋 项目简介

UI自动化管理系统 v2.0 是一个现代化的Python GUI应用程序，结合后端API实现完整的UI自动化功能。该系统提供了直观的图形界面来管理自动化模板、序列和执行监控。

## ✨ 主要功能

### 🎯 核心功能
- **模板管理** - 创建、编辑、测试和管理UI自动化模板
- **序列管理** - 组合模板创建复杂的自动化序列
- **执行监控** - 实时监控自动化任务执行状态和历史
- **系统设置** - 灵活的配置管理和API连接设置

### 🔧 技术特性
- **现代化界面** - 基于Tkinter的现代化GUI设计
- **API集成** - 完整的后端API客户端集成
- **实时监控** - 实时执行状态监控和回调机制
- **多线程支持** - 后台任务处理，界面响应流畅
- **配置管理** - 完整的配置导入/导出功能

## 🚀 快速开始

### 环境要求
- Python 3.7 或更高版本
- Windows 操作系统
- 后端API服务器运行中

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 项目已在 D:\Projects\ProjectManagement\Python\NewVersion
   ```

2. **安装依赖**
   ```bash
   pip install requests
   # 其他依赖为Python标准库
   ```

3. **启动应用程序**
   ```bash
   # 方式1: 使用批处理文件
   start.bat
   
   # 方式2: 直接运行Python
   python main.py
   ```

## 📖 使用指南

### 1. 首次启动

1. 运行 `start.bat` 或 `python main.py`
2. 系统会自动检查API连接状态
3. 如果连接失败，请在"设置"选项卡中配置正确的API地址

### 2. 模板管理

**功能位置**: 📋 模板管理选项卡

- **查看模板**: 左侧列表显示所有可用模板
- **模板详情**: 右侧面板显示选中模板的详细信息
- **操作功能**: 
  - 创建新模板
  - 编辑现有模板
  - 测试模板功能
  - 删除模板

### 3. 序列管理

**功能位置**: 🔄 序列管理选项卡

- **序列列表**: 显示所有自动化序列及其状态
- **序列详情**: 查看序列的步骤和配置
- **执行序列**: 一键执行选中的自动化序列
- **序列编辑**: 创建和修改自动化序列

### 4. 执行监控

**功能位置**: 📊 执行监控选项卡

- **实时状态**: 显示当前正在执行的任务状态
- **进度跟踪**: 实时进度条和步骤信息
- **执行历史**: 查看历史执行记录和结果
- **执行控制**: 停止当前执行的任务

### 5. 系统设置

**功能位置**: ⚙️ 设置选项卡

- **API设置**: 配置后端API连接参数
- **自动化设置**: 调整自动化执行参数
- **界面设置**: 自定义界面外观和行为
- **高级设置**: 日志、性能等高级配置

## 🏗️ 系统架构

### 核心组件

```
main.py                 # 主应用程序入口
├── api_client.py       # API客户端，处理与后端的通信
├── automation_manager.py # 自动化管理器，整合本地和远程功能
├── template_manager_ui.py # 模板管理界面
├── sequence_manager_ui.py # 序列管理界面
├── execution_monitor_ui.py # 执行监控界面
└── settings_ui.py      # 设置界面
```

### 数据流

```
用户界面 ↔ 自动化管理器 ↔ API客户端 ↔ 后端API
                ↓
            本地自动化组件
            (VSCode, UI操作等)
```

## 🔌 API集成

### 支持的API端点

- **模板管理**
  - `GET /api/custom-templates` - 获取模板列表
  - `POST /api/custom-templates` - 创建模板
  - `PUT /api/custom-templates/{id}` - 更新模板
  - `DELETE /api/custom-templates/{id}` - 删除模板

- **序列管理**
  - `GET /api/template-sequences` - 获取序列列表
  - `POST /api/template-sequences` - 创建序列
  - `POST /api/template-sequences/{id}/execute` - 执行序列

- **执行监控**
  - `GET /api/template-execution-logs` - 获取执行日志
  - `GET /api/template-execution-logs/statistics` - 获取统计信息

### API配置

在设置界面中配置以下参数：
- **服务器地址**: 默认 `http://localhost:5001`
- **超时时间**: 默认 30 秒
- **重试次数**: 默认 3 次

## ⚙️ 配置文件

系统配置保存在 `config.json` 文件中，包含：

```json
{
  "api_url": "http://localhost:5001",
  "api_timeout": 30,
  "default_timeout": 5,
  "safe_mode": true,
  "auto_screenshot": true,
  "theme": "clam"
}
```

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查后端服务器是否运行
   - 验证API地址配置是否正确
   - 检查网络连接和防火墙设置

2. **模板执行失败**
   - 确认本地自动化组件可用
   - 检查模板配置是否正确
   - 查看执行日志获取详细错误信息

3. **界面响应缓慢**
   - 检查API响应时间
   - 调整刷新间隔设置
   - 关闭不必要的后台监控

### 日志文件

- **位置**: `./logs/` 目录
- **级别**: DEBUG, INFO, WARNING, ERROR
- **配置**: 在设置界面中调整日志级别和路径

## 🚧 开发计划

### v2.1 计划功能
- [ ] 模板编辑器增强
- [ ] 批量操作支持
- [ ] 更多自动化动作类型
- [ ] 插件系统

### v2.2 计划功能
- [ ] 云端同步
- [ ] 团队协作功能
- [ ] 高级调度器
- [ ] 性能优化

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确认系统环境和依赖是否正确安装

## 📄 许可证

本项目为内部开发工具，仅供项目团队使用。

---

**UI自动化管理系统 v2.0**  
*让自动化更简单，让效率更高*
