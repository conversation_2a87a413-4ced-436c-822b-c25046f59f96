-- =============================================
-- 插入增强版ER图生成Prompt模板
-- =============================================

USE ProjectManagementAI;
GO

-- 首先更新TaskType约束，添加对图表生成任务的支持
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromptTemplates_TaskType')
BEGIN
    ALTER TABLE PromptTemplates DROP CONSTRAINT CK_PromptTemplates_TaskType;
    PRINT '已删除旧的TaskType约束';
END

-- 创建新的TaskType约束，支持更多任务类型
ALTER TABLE PromptTemplates
ADD CONSTRAINT CK_PromptTemplates_TaskType
CHECK (TaskType IN (
    'RequirementAnalysis',      -- 需求分析
    'CodeGeneration',          -- 代码生成
    'Testing',                 -- 测试生成
    'Debugging',               -- 调试辅助
    'Documentation',           -- 文档生成
    'Review',                  -- 代码审查
    'ERDiagramGeneration',     -- ER图生成
    'ContextDiagramGeneration', -- 上下文图生成
    'DesignGeneration',        -- 设计生成
    'ArchitectureAnalysis',    -- 架构分析
    'RequirementDecomposition', -- 需求分解
    'DependencyAnalysis',      -- 依赖分析
    'ComplexityAnalysis'       -- 复杂度分析
));

PRINT '已更新TaskType约束，支持图表生成任务和需求分解相关任务';

-- 然后获取或创建分类
DECLARE @CategoryId int;
IF NOT EXISTS (SELECT 1 FROM PromptCategories WHERE Name = '设计生成')
BEGIN
    INSERT INTO PromptCategories (Name, Description, SortOrder, IsEnabled, CreatedTime)
    VALUES ('设计生成', 'ER图、上下文图等设计图表生成模板', 3, 1, GETDATE());
END
SET @CategoryId = (SELECT Id FROM PromptCategories WHERE Name = '设计生成');

-- 检查是否已存在ERDiagramGeneration类型的模板
IF NOT EXISTS (SELECT 1 FROM PromptTemplates WHERE TaskType = 'ERDiagramGeneration' AND Name = '增强版ER图生成模板')
BEGIN
    INSERT INTO PromptTemplates (
        Name,
        Description,
        CategoryId,
        TaskType,
        Content,
        Parameters,
        TemplateType,
        IsDefault,
        IsEnabled,
        CreatedBy,
        CreatedTime
    )
    VALUES (
        '增强版ER图生成模板',
        '结合项目背景和业务需求的专业ER图生成模板，支持完整的系统架构和业务实体设计',
        @CategoryId,
        'ERDiagramGeneration',
        N'你是一个专业的数据库架构师和系统分析师。请根据以下项目信息和需求，为"{{project_name}}"项目生成一个完整的{{databaseType}}数据库ER图。

## 项目背景信息
{{project_background}}

## 项目需求分析
{{project_requirements}}

{{#if requirement_document_info}}
## 特定需求文档
{{requirement_document_info}}
{{/if}}


## 生成要求

### 重要说明
**请仔细分析上述项目需求信息，根据实际的功能需求、用户故事和验收标准来设计数据库实体，而不是仅仅使用推荐的业务实体参考。**

### 数据库设计原则
1. **基于需求驱动设计**
   - 仔细分析项目需求文档中的功能性需求
   - 根据用户故事识别核心业务实体
   - 基于验收标准确定实体属性和关系
   - 考虑非功能性需求对数据库设计的影响

2. **遵循数据库设计最佳实践**
   - 第三范式规范化设计
   - 合理的索引策略
   - 支持软删除和审计字段
   - 考虑性能和扩展性

3. **字段设计规范**
   - 使用标准的{{databaseType}}数据类型
   - 主键统一使用int类型，命名为Id
   - 外键命名规范：关联表名+Id（如UserId, ProjectId）
   - 时间字段使用datetime2类型
   - 字符串字段指定合适的长度限制

4. **关系设计**
   - 明确定义实体间的关系（一对一、一对多、多对多）
   - 正确设置主键(PK)和外键(FK)标识
   - 考虑级联删除和更新策略

## 输出格式要求

**重要：请只返回纯Mermaid代码，不要包含任何解释文字、markdown标记或代码块标记。**

输出格式示例（请严格按照此格式）：

erDiagram
    %% 用户管理模块
    Users {
        int Id PK
        nvarchar(50) Username "NOT NULL, UNIQUE"
        nvarchar(100) Email "NOT NULL, UNIQUE"
        nvarchar(255) PasswordHash "NOT NULL"
        nvarchar(100) RealName
        nvarchar(20) Role "NOT NULL, DEFAULT User"
        bit Status "NOT NULL, DEFAULT 1"
        datetime2 CreatedTime "NOT NULL, DEFAULT GETDATE()"
        datetime2 UpdatedTime
        int CreatedBy
        int UpdatedBy
        bit IsDeleted "NOT NULL, DEFAULT 0"
        datetime2 DeletedTime
        int DeletedBy
        int Version "NOT NULL, DEFAULT 1"
        nvarchar(500) Remarks
    }

    Projects {
        int Id PK
        nvarchar(200) Name "NOT NULL"
        nvarchar(max) Description
        nvarchar(20) Status "NOT NULL, DEFAULT Planning"
        nvarchar(10) Priority "DEFAULT Medium"
        int OwnerId FK "NOT NULL"
        datetime2 StartDate
        datetime2 EndDate
        nvarchar(200) TechnologyStack
        datetime2 CreatedTime "NOT NULL, DEFAULT GETDATE()"
        datetime2 UpdatedTime
        int CreatedBy
        int UpdatedBy
        bit IsDeleted "NOT NULL, DEFAULT 0"
        datetime2 DeletedTime
        int DeletedBy
        int Version "NOT NULL, DEFAULT 1"
        nvarchar(500) Remarks
    }

    %% 定义关系
    Users ||--o{ Projects : "owns"

## 输出要求

**严格要求：**
1. **只返回Mermaid代码**: 不要包含任何解释文字、标题、说明或markdown格式
2. **不要使用代码块**: 不要使用```mermaid```标记，直接输出erDiagram开头的代码
3. **确保语法正确**: 严格遵循Mermaid ER图语法，避免使用不支持的语法
4. **字段约束格式**: 使用双引号包围约束，如"NOT NULL, UNIQUE"
5. **默认值格式**: DEFAULT后面的值不要用单引号，如DEFAULT User而不是DEFAULT User
6. **关系完整**: 确保所有实体间的关系都被正确定义
7. **业务逻辑**: 体现{{project_name}}项目的具体业务逻辑和数据流

{{#if requirementDocumentId}}
## 相关需求文档
请特别参考需求文档ID: {{requirementDocumentId}} 中的具体业务需求进行设计。
{{/if}}

## 设计指导原则

1. **优先考虑实际需求**：请根据上述项目需求文档中的具体功能需求来设计实体，而不是机械地使用推荐实体列表
2. **业务逻辑驱动**：确保ER图能够支持项目需求中描述的所有核心业务流程
3. **数据完整性**：设计合适的约束和关系来保证数据的完整性和一致性
4. **可扩展性**：考虑未来可能的功能扩展需求
5. **性能优化**：为高频查询的字段设计合适的索引策略

**最终要求**：
请基于以上要求和项目的实际需求，生成一个完整、专业、符合{{project_name}}项目业务逻辑的{{databaseType}}数据库ER图。

**输出格式**：直接输出以"erDiagram"开头的纯Mermaid代码，不要包含任何其他内容。

**特别注意**：如果项目需求文档中包含具体的业务实体和数据结构描述，请优先使用这些信息而不是通用的推荐实体。',
        N'[
            {"name": "projectId", "type": "int", "required": true, "description": "项目ID"},
            {"name": "databaseType", "type": "string", "required": true, "description": "数据库类型", "default": "SqlServer"},
            {"name": "diagramFormat", "type": "string", "required": false, "description": "图表格式", "default": "Mermaid"},
            {"name": "requirementDocumentId", "type": "int", "required": false, "description": "需求文档ID"}
        ]',
        'System', -- TemplateType
        1, -- IsDefault
        1, -- IsEnabled
        1, -- CreatedBy (admin user)
        GETDATE()
    );

    PRINT '增强版ER图生成模板插入成功';
END
ELSE
BEGIN
    PRINT '增强版ER图生成模板已存在，跳过插入';
END

-- 更新旧的默认模板为非默认
UPDATE PromptTemplates
SET IsDefault = 0
WHERE TaskType = 'ERDiagramGeneration'
  AND Name != '增强版ER图生成模板'
  AND IsDefault = 1;

-- 验证插入结果
SELECT
    Id, Name, TaskType, IsDefault, IsEnabled, CreatedTime
FROM PromptTemplates
WHERE TaskType = 'ERDiagramGeneration'
ORDER BY IsDefault DESC, CreatedTime DESC;

PRINT '模板更新完成！';
PRINT '增强版ER图生成模板已成功插入并设置为默认模板';
GO
