using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// DeepSeek AI服务提供商
/// </summary>
public class DeepSeekProvider : IAIProvider
{
    private readonly ILogger<DeepSeekProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly DeepSeekConfig _config;
    private readonly AIUsageStats _usageStats;

    public string Name => "DeepSeek";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "deepseek-chat", "deepseek-coder", "deepseek-math" };

    public DeepSeekProvider(
        ILogger<DeepSeekProvider> logger,
        HttpClient httpClient,
        IOptions<DeepSeekConfig> config)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config.Value;
        _usageStats = new AIUsageStats();

        // 配置HttpClient
        if (!string.IsNullOrEmpty(_config.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.ApiKey);
        }

        _httpClient.BaseAddress = new Uri(_config.Endpoint);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("DeepSeek开始生成文本，模型: {Model}", config.Model);

            // DeepSeek API格式
            var requestBody = new
            {
                model = GetDeepSeekModel(config.Model),
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = config.MaxTokens,
                temperature = config.Temperature,
                top_p = config.TopP,
                stream = false
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            // 设置请求头
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {config.ApiKey}");

            var response = await _httpClient.PostAsync(config.Endpoint, content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("DeepSeek API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"DeepSeek API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var generatedText = responseData
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString() ?? string.Empty;

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("DeepSeek文本生成完成，长度: {Length}", generatedText.Length);
            return generatedText;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "DeepSeek文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("DeepSeek开始获取向量嵌入");

            // DeepSeek目前可能不支持嵌入，返回模拟数据
            await Task.Delay(500);

            // 生成模拟的1536维向量
            var embedding = new float[1536];
            var random = new Random();
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2 - 1);
            }

            UpdateUsageStats(startTime, true);

            _logger.LogInformation("DeepSeek向量嵌入获取完成，维度: {Dimension}", embedding.Length);
            return embedding;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "DeepSeek向量嵌入获取失败");
            throw;
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            var testConfig = new AIModelConfig
            {
                Model = "deepseek-chat",
                MaxTokens = 10,
                Temperature = 0.1f
            };

            var result = await GenerateAsync("Hello", testConfig);
            IsAvailable = !string.IsNullOrEmpty(result);

            _logger.LogInformation("DeepSeek健康检查完成，状态: {Status}", IsAvailable ? "正常" : "异常");
            return IsAvailable;
        }
        catch (Exception ex)
        {
            IsAvailable = false;
            _logger.LogError(ex, "DeepSeek健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    #region 私有方法

    /// <summary>
    /// 获取DeepSeek模型名称
    /// </summary>
    private string GetDeepSeekModel(string model)
    {
        return model.ToLower() switch
        {
            "deepseek-chat" => "deepseek-chat",
            "deepseek-coder" => "deepseek-coder",
            "deepseek-math" => "deepseek-math",
            _ => "deepseek-chat"
        };
    }

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success, JsonElement? responseData = null)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        // 更新平均响应时间
        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        // 更新Token使用量
        if (success && responseData.HasValue)
        {
            try
            {
                if (responseData.Value.TryGetProperty("usage", out var usage))
                {
                    if (usage.TryGetProperty("total_tokens", out var totalTokens))
                    {
                        _usageStats.TotalTokens += totalTokens.GetInt64();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析Token使用量失败");
            }
        }

        _usageStats.LastUpdated = DateTime.Now;
    }

    #endregion
}

/// <summary>
/// DeepSeek配置
/// </summary>
public class DeepSeekConfig
{
    /// <summary>
    /// API端点
    /// </summary>
    public string Endpoint { get; set; } = "https://api.deepseek.com/v1/";

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300; // 5分钟超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}
