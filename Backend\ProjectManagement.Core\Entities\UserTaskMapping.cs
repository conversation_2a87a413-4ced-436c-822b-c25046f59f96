using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 用户任务映射实体 - 对应DatabaseSchema.sql中的UserTaskMappings表
/// 功能: 管理每个用户的个人任务映射配置，指定不同任务类型使用的AI提供商
/// 支持: 个性化任务映射、多提供商支持、优先级管理、默认配置
/// </summary>
[SugarTable("UserTaskMappings", "用户个人任务映射配置管理表")]
public class UserTaskMapping
{
    /// <summary>
    /// 用户任务映射唯一标识ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "用户任务映射唯一标识ID")]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID，关联Users表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "用户ID，关联Users表")]
    [Required(ErrorMessage = "用户ID不能为空")]
    public int UserId { get; set; }

    /// <summary>
    /// 任务类型: RequirementAnalysis(需求分析), CodeGeneration(代码生成), DocumentGeneration(文档生成), Embeddings(向量嵌入), Testing(测试), Debugging(调试)
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(50)", IsNullable = false, ColumnDescription = "任务类型")]
    [Required(ErrorMessage = "任务类型不能为空")]
    [StringLength(50, ErrorMessage = "任务类型长度不能超过50个字符")]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// AI提供商名称: Azure, OpenAI, DeepSeek, Claude, Ollama等
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(50)", IsNullable = false, ColumnDescription = "AI提供商名称")]
    [Required(ErrorMessage = "AI提供商名称不能为空")]
    [StringLength(50, ErrorMessage = "AI提供商名称长度不能超过50个字符")]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// 指定的AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3），可为空使用提供商默认模型
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(100)", IsNullable = true, ColumnDescription = "指定的AI模型名称")]
    [StringLength(100, ErrorMessage = "AI模型名称长度不能超过100个字符")]
    public string? ModelName { get; set; }

    /// <summary>
    /// 是否启用该映射配置，0=禁用, 1=启用
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否启用该映射配置")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 是否为该用户该任务类型的默认配置，0=否, 1=是
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否为该用户该任务类型的默认配置")]
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 优先级，数字越小优先级越高，用于多个配置时的选择
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "优先级")]
    [Range(1, 100, ErrorMessage = "优先级必须在1-100之间")]
    public int Priority { get; set; } = 1;

    /// <summary>
    /// 任务特定的配置参数，JSON格式存储（温度、最大令牌数、超时时间等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "任务特定的配置参数")]
    public string? ConfigurationParameters { get; set; }

    /// <summary>
    /// 配置描述说明
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(500)", IsNullable = true, ColumnDescription = "配置描述说明")]
    [StringLength(500, ErrorMessage = "配置描述长度不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 配置创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "配置创建时间")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 配置最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "配置最后更新时间")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 导航属性 - 关联的用户
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }

    /// <summary>
    /// 验证任务映射配置
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, List<string> Errors) ValidateConfiguration()
    {
        var errors = new List<string>();

        // 验证任务类型 - 允许自定义任务类型，只检查基本格式
        if (string.IsNullOrWhiteSpace(TaskType))
        {
            errors.Add("任务类型不能为空");
        }
        else if (TaskType.Length > 50)
        {
            errors.Add("任务类型长度不能超过50个字符");
        }
        else if (!System.Text.RegularExpressions.Regex.IsMatch(TaskType, @"^[A-Za-z][A-Za-z0-9]*$"))
        {
            errors.Add("任务类型必须以字母开头，只能包含字母和数字");
        }

        // 验证提供商名称 - 允许更多的提供商
        if (string.IsNullOrWhiteSpace(ProviderName))
        {
            errors.Add("AI提供商名称不能为空");
        }
        else if (ProviderName.Length > 50)
        {
            errors.Add("AI提供商名称长度不能超过50个字符");
        }

        // 验证优先级
        if (Priority < 1 || Priority > 10)
        {
            errors.Add("优先级必须在1-10之间");
        }

        // 验证配置参数JSON格式
        if (!string.IsNullOrEmpty(ConfigurationParameters))
        {
            try
            {
                System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(ConfigurationParameters);
            }
            catch (System.Text.Json.JsonException)
            {
                errors.Add("配置参数必须是有效的JSON格式");
            }
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 获取任务类型的显示名称
    /// </summary>
    /// <returns>任务类型的中文显示名称</returns>
    public string GetTaskTypeDisplayName()
    {
        return TaskType switch
        {
            "RequirementAnalysis" => "需求分析",
            "CodeGeneration" => "代码生成",
            "DocumentGeneration" => "文档生成",
            "Embeddings" => "向量嵌入",
            "Testing" => "测试",
            "Debugging" => "调试",
            _ => TaskType // 对于自定义任务类型，直接返回原始值
        };
    }

    /// <summary>
    /// 判断是否为系统预定义的任务类型
    /// </summary>
    /// <returns>是否为系统任务类型</returns>
    public bool IsSystemTaskType()
    {
        var systemTaskTypes = new[] { "RequirementAnalysis", "CodeGeneration", "DocumentGeneration", "Embeddings", "Testing", "Debugging" };
        return systemTaskTypes.Contains(TaskType);
    }

    /// <summary>
    /// 获取提供商的显示名称
    /// </summary>
    /// <returns>提供商的显示名称</returns>
    public string GetProviderDisplayName()
    {
        return ProviderName switch
        {
            "Azure" => "Azure OpenAI",
            "OpenAI" => "OpenAI",
            "DeepSeek" => "DeepSeek",
            "Claude" => "Anthropic Claude",
            "Ollama" => "Ollama",
            "Mock" => "模拟提供商",
            _ => ProviderName
        };
    }

    /// <summary>
    /// 转换为DTO对象
    /// </summary>
    /// <returns>UserTaskMappingDto对象</returns>
    public object ToDto()
    {
        return new
        {
            Id,
            UserId,
            TaskType,
            TaskTypeDisplayName = GetTaskTypeDisplayName(),
            ProviderName,
            ProviderDisplayName = GetProviderDisplayName(),
            ModelName,
            IsActive,
            IsDefault,
            Priority,
            ConfigurationParameters,
            Description,
            CreatedAt,
            UpdatedAt
        };
    }
}

/// <summary>
/// 任务类型枚举
/// </summary>
public static class TaskTypes
{
    public const string RequirementAnalysis = "RequirementAnalysis";
    public const string CodeGeneration = "CodeGeneration";
    public const string DocumentGeneration = "DocumentGeneration";
    public const string Embeddings = "Embeddings";
    public const string Testing = "Testing";
    public const string Debugging = "Debugging";

    /// <summary>
    /// 获取所有任务类型
    /// </summary>
    /// <returns>任务类型列表</returns>
    public static List<(string Value, string DisplayName)> GetAll()
    {
        return new List<(string, string)>
        {
            (RequirementAnalysis, "需求分析"),
            (CodeGeneration, "代码生成"),
            (DocumentGeneration, "文档生成"),
            (Embeddings, "向量嵌入"),
            (Testing, "测试"),
            (Debugging, "调试")
        };
    }
}

/// <summary>
/// AI提供商枚举
/// </summary>
public static class AIProviders
{
    public const string Azure = "Azure";
    public const string OpenAI = "OpenAI";
    public const string DeepSeek = "DeepSeek";
    public const string Claude = "Claude";
    public const string Ollama = "Ollama";
    public const string Mock = "Mock";

    /// <summary>
    /// 获取所有AI提供商
    /// </summary>
    /// <returns>AI提供商列表</returns>
    public static List<(string Value, string DisplayName)> GetAll()
    {
        return new List<(string, string)>
        {
            (Azure, "Azure OpenAI"),
            (OpenAI, "OpenAI"),
            (DeepSeek, "DeepSeek"),
            (Claude, "Anthropic Claude"),
            (Ollama, "Ollama"),
            (Mock, "模拟提供商")
        };
    }
}
