-- =============================================
-- 为开发步骤表添加参考图片字段
-- 功能: 支持开发步骤上传参考图片功能
-- 创建日期: 2025-06-24
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. 检查并添加 ReferenceImages 字段
-- =============================================

-- 检查字段是否已存在
IF NOT EXISTS (
    SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID(N'[dbo].[DevelopmentSteps]') 
    AND name = 'ReferenceImages'
)
BEGIN
    -- 添加参考图片字段
    ALTER TABLE [dbo].[DevelopmentSteps]
    ADD [ReferenceImages] NVARCHAR(MAX) NULL;
    
    PRINT '✓ ReferenceImages 字段添加成功';
    
    -- 添加字段注释
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'参考图片路径列表，JSON格式存储',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'DevelopmentSteps',
        @level2type = N'COLUMN',
        @level2name = N'ReferenceImages';
        
    PRINT '✓ ReferenceImages 字段注释添加成功';
END
ELSE
BEGIN
    PRINT '⚠ ReferenceImages 字段已存在，跳过添加';
END
GO

-- =============================================
-- 2. 更新视图以包含新字段
-- =============================================

-- 重新创建开发步骤概览视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_DevelopmentStepOverview]'))
    DROP VIEW [dbo].[vw_DevelopmentStepOverview];
GO

CREATE VIEW [dbo].[vw_DevelopmentStepOverview]
AS
SELECT
    ds.[Id],
    ds.[ProjectId],
    p.[Name] AS ProjectName,
    ds.[RequirementDocumentId],
    rd.[Title] AS RequirementTitle,
    ds.[StepName],
    ds.[StepDescription],
    ds.[StepType],
    ds.[Priority],
    ds.[Status],
    ds.[Progress],
    ds.[EstimatedHours],
    ds.[ActualHours],
    ds.[TechnologyStack],
    ds.[ComponentType],
    ds.[StepOrder],
    ds.[StepGroup],
    ds.[StepLevel],
    ds.[ParentStepId],
    parent.[StepName] AS ParentStepName,
    ds.[ReferenceImages],
    ds.[StartTime],
    ds.[EndTime],
    ds.[CompletedTime],
    ds.[CreatedTime],
    ds.[CreatedBy],
    creator.[Username] AS CreatorName,
    -- 依赖统计
    (SELECT COUNT(*) FROM [dbo].[StepDependencies] sd WHERE sd.[StepId] = ds.[Id] AND sd.[IsDeleted] = 0) AS DependencyCount,
    (SELECT COUNT(*) FROM [dbo].[StepDependencies] sd WHERE sd.[DependsOnStepId] = ds.[Id] AND sd.[IsDeleted] = 0) AS DependentCount,
    -- 子步骤统计
    (SELECT COUNT(*) FROM [dbo].[DevelopmentSteps] child WHERE child.[ParentStepId] = ds.[Id] AND child.[IsDeleted] = 0) AS ChildStepCount,
    -- 执行历史统计
    (SELECT COUNT(*) FROM [dbo].[StepExecutionHistory] seh WHERE seh.[StepId] = ds.[Id] AND seh.[IsDeleted] = 0) AS ExecutionCount
FROM [dbo].[DevelopmentSteps] ds
LEFT JOIN [dbo].[Projects] p ON ds.[ProjectId] = p.[Id]
LEFT JOIN [dbo].[RequirementDocuments] rd ON ds.[RequirementDocumentId] = rd.[Id]
LEFT JOIN [dbo].[DevelopmentSteps] parent ON ds.[ParentStepId] = parent.[Id]
LEFT JOIN [dbo].[Users] creator ON ds.[CreatedBy] = creator.[Id]
WHERE ds.[IsDeleted] = 0;
GO

PRINT '✓ vw_DevelopmentStepOverview 视图更新成功';
GO

-- =============================================
-- 3. 完成脚本
-- =============================================

PRINT '✓ 开发步骤参考图片字段添加完成！';
PRINT '';
PRINT '新增功能：';
PRINT '1. DevelopmentSteps.ReferenceImages - 参考图片路径字段（JSON格式）';
PRINT '2. 更新了 vw_DevelopmentStepOverview 视图';
PRINT '';
PRINT '✅ 数据库迁移脚本执行完成！';
GO
