-- =============================================
-- 简化的项目自动化视图
-- 功能: 提供常用的项目、任务、步骤和自动化模板查询
-- 创建时间: 2025-01-02
-- =============================================

USE ProjectManagementAI;
GO

-- 删除视图（如果存在）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_SimpleProjectAutomation')
BEGIN
    DROP VIEW V_SimpleProjectAutomation;
    PRINT '已删除现有视图 V_SimpleProjectAutomation';
END
GO

-- 创建简化的项目自动化视图
CREATE VIEW V_SimpleProjectAutomation AS
SELECT 
    -- 项目基本信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,
    
    -- 编码任务信息
    ct.Id AS CodingTaskId,
    ct.TaskName,
    ct.Status AS TaskStatus,
    ct.Priority AS TaskPriority,
    ct.StartDate AS TaskStartDate,
    ct.DueDate AS TaskDueDate,
    
    -- 开发步骤信息
    ds.Id AS DevelopmentStepId,
    ds.StepName,
    ds.StepType,
    ds.Status AS StepStatus,
    ds.Progress AS StepProgress,
    ds.StepOrder,
    ds.TechnologyStack,
    ds.ComponentType,
    
    -- 自动化关联信息
    stsa.Id AS AssociationId,
    stsa.Status AS AutomationStatus,
    stsa.Progress AS AutomationProgress,
    stsa.AppliedTime,
    stsa.ExecutionStartTime,
    stsa.ExecutionEndTime,
    
    -- 模板序列信息
    uts.Id AS TemplateSequenceId,
    uts.Name AS TemplateSequenceName,
    uts.Category AS TemplateCategory,
    uts.IsActive AS TemplateIsActive,
    
    -- 自定义模板信息
    cut.Id AS CustomTemplateId,
    cut.Name AS CustomTemplateName,
    cut.Category AS CustomTemplateCategory,
    
    -- 计算字段
    CASE 
        WHEN ct.DueDate IS NOT NULL AND ct.DueDate < GETDATE() AND ct.Status != 'Completed' 
        THEN 1 ELSE 0 
    END AS IsTaskOverdue,
    
    CASE 
        WHEN stsa.ExecutionStartTime IS NOT NULL AND stsa.ExecutionEndTime IS NOT NULL 
        THEN DATEDIFF(MINUTE, stsa.ExecutionStartTime, stsa.ExecutionEndTime)
        ELSE NULL 
    END AS AutomationDurationMinutes,
    
    -- 时间戳
    p.CreatedTime AS ProjectCreatedTime,
    ct.CreatedTime AS TaskCreatedTime,
    ds.CreatedTime AS StepCreatedTime

FROM Projects p
    LEFT JOIN CodingTasks ct ON p.Id = ct.ProjectId AND ct.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ct.Id = cts.CodingTaskId AND cts.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON cts.DevelopmentStepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId AND stsa.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id AND uts.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSteps utst ON uts.Id = utst.SequenceId AND utst.IsDeleted = 0
    LEFT JOIN CustomUIAutoMationTemplates cut ON utst.TemplateId = cut.Id AND cut.IsDeleted = 0

WHERE p.IsDeleted = 0;

GO

-- =============================================
-- 项目任务统计视图
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_ProjectTaskStats')
BEGIN
    DROP VIEW V_ProjectTaskStats;
    PRINT '已删除现有视图 V_ProjectTaskStats';
END
GO

CREATE VIEW V_ProjectTaskStats AS
SELECT 
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    
    -- 任务统计
    COUNT(DISTINCT ct.Id) AS TotalTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'Completed' THEN ct.Id END) AS CompletedTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'InProgress' THEN ct.Id END) AS InProgressTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'NotStarted' THEN ct.Id END) AS NotStartedTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'Blocked' THEN ct.Id END) AS BlockedTasks,
    
    -- 步骤统计
    COUNT(DISTINCT ds.Id) AS TotalSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Completed' THEN ds.Id END) AS CompletedSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'InProgress' THEN ds.Id END) AS InProgressSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Pending' THEN ds.Id END) AS PendingSteps,
    
    -- 自动化统计
    COUNT(DISTINCT stsa.Id) AS TotalAutomations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS CompletedAutomations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Running' THEN stsa.Id END) AS RunningAutomations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Failed' THEN stsa.Id END) AS FailedAutomations,
    
    -- 逾期统计
    COUNT(DISTINCT CASE WHEN ct.DueDate < GETDATE() AND ct.Status != 'Completed' THEN ct.Id END) AS OverdueTasks,
    
    -- 进度计算
    CASE 
        WHEN COUNT(DISTINCT ct.Id) > 0 
        THEN ROUND(CAST(COUNT(DISTINCT CASE WHEN ct.Status = 'Completed' THEN ct.Id END) AS FLOAT) / COUNT(DISTINCT ct.Id) * 100, 2)
        ELSE 0 
    END AS TaskCompletionRate,
    
    CASE 
        WHEN COUNT(DISTINCT ds.Id) > 0 
        THEN ROUND(CAST(COUNT(DISTINCT CASE WHEN ds.Status = 'Completed' THEN ds.Id END) AS FLOAT) / COUNT(DISTINCT ds.Id) * 100, 2)
        ELSE 0 
    END AS StepCompletionRate,
    
    -- 时间信息
    MIN(ct.StartDate) AS EarliestTaskStart,
    MAX(ct.DueDate) AS LatestTaskDue,
    p.CreatedTime AS ProjectCreatedTime

FROM Projects p
    LEFT JOIN CodingTasks ct ON p.Id = ct.ProjectId AND ct.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ct.Id = cts.CodingTaskId AND cts.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON cts.DevelopmentStepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId AND stsa.IsDeleted = 0

WHERE p.IsDeleted = 0
GROUP BY p.Id, p.Name, p.ProjectCode, p.Status, p.CreatedTime;

GO

-- =============================================
-- 自动化模板使用统计视图
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_TemplateUsageStats')
BEGIN
    DROP VIEW V_TemplateUsageStats;
    PRINT '已删除现有视图 V_TemplateUsageStats';
END
GO

CREATE VIEW V_TemplateUsageStats AS
SELECT 
    uts.Id AS TemplateSequenceId,
    uts.Name AS TemplateSequenceName,
    uts.Category,
    uts.IsActive,
    uts.UsageCount AS RecordedUsageCount,
    
    -- 实际使用统计
    COUNT(DISTINCT stsa.Id) AS ActualUsageCount,
    COUNT(DISTINCT stsa.StepId) AS UniqueStepsUsed,
    COUNT(DISTINCT p.Id) AS UniqueProjectsUsed,
    
    -- 执行结果统计
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS SuccessfulExecutions,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Failed' THEN stsa.Id END) AS FailedExecutions,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Running' THEN stsa.Id END) AS RunningExecutions,
    
    -- 成功率
    CASE 
        WHEN COUNT(DISTINCT stsa.Id) > 0 
        THEN ROUND(CAST(COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS FLOAT) / COUNT(DISTINCT stsa.Id) * 100, 2)
        ELSE 0 
    END AS SuccessRate,
    
    -- 平均执行时间
    AVG(CASE 
        WHEN stsa.ExecutionStartTime IS NOT NULL AND stsa.ExecutionEndTime IS NOT NULL 
        THEN DATEDIFF(MINUTE, stsa.ExecutionStartTime, stsa.ExecutionEndTime)
        ELSE NULL 
    END) AS AvgExecutionMinutes,
    
    -- 时间信息
    MIN(stsa.AppliedTime) AS FirstUsed,
    MAX(stsa.AppliedTime) AS LastUsed,
    uts.LastUsedTime AS RecordedLastUsed,
    uts.CreatedTime AS TemplateCreatedTime

FROM UIAutoMationTemplateSequences uts
    LEFT JOIN StepTemplateSequenceAssociations stsa ON uts.Id = stsa.SequenceId AND stsa.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ds.Id = cts.DevelopmentStepId AND cts.IsDeleted = 0
    LEFT JOIN CodingTasks ct ON cts.CodingTaskId = ct.Id AND ct.IsDeleted = 0
    LEFT JOIN Projects p ON ct.ProjectId = p.Id AND p.IsDeleted = 0

WHERE uts.IsDeleted = 0
GROUP BY uts.Id, uts.Name, uts.Category, uts.IsActive, uts.UsageCount, uts.LastUsedTime, uts.CreatedTime;

GO

PRINT '✅ 简化视图创建成功！';
PRINT '';
PRINT '已创建的视图：';
PRINT '1. V_SimpleProjectAutomation - 简化的项目自动化信息视图';
PRINT '2. V_ProjectTaskStats - 项目任务统计视图';
PRINT '3. V_TemplateUsageStats - 自动化模板使用统计视图';
PRINT '';
PRINT '使用示例：';
PRINT '-- 查看项目1的简化信息';
PRINT 'SELECT * FROM V_SimpleProjectAutomation WHERE ProjectId = 1;';
PRINT '';
PRINT '-- 查看所有项目的任务统计';
PRINT 'SELECT * FROM V_ProjectTaskStats ORDER BY ProjectId;';
PRINT '';
PRINT '-- 查看模板使用统计';
PRINT 'SELECT * FROM V_TemplateUsageStats ORDER BY ActualUsageCount DESC;';
GO
