-- =============================================
-- 附加专用视图集合
-- 功能: 提供更具体的查询视图
-- 创建时间: 2025-01-02
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. 项目任务概览视图
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_ProjectTaskOverview')
BEGIN
    DROP VIEW V_ProjectTaskOverview;
    PRINT '已删除现有视图 V_ProjectTaskOverview';
END
GO

CREATE VIEW V_ProjectTaskOverview AS
SELECT 
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    p.Status AS ProjectStatus,
    p.Priority AS ProjectPriority,
    p.Progress AS ProjectProgress,
    
    -- 编码任务统计
    COUNT(DISTINCT ct.Id) AS TotalCodingTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'Completed' THEN ct.Id END) AS CompletedCodingTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'InProgress' THEN ct.Id END) AS InProgressCodingTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'NotStarted' THEN ct.Id END) AS NotStartedCodingTasks,
    COUNT(DISTINCT CASE WHEN ct.Status = 'Blocked' THEN ct.Id END) AS BlockedCodingTasks,
    
    -- 开发步骤统计
    COUNT(DISTINCT ds.Id) AS TotalDevelopmentSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Completed' THEN ds.Id END) AS CompletedSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'InProgress' THEN ds.Id END) AS InProgressSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Pending' THEN ds.Id END) AS PendingSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Failed' THEN ds.Id END) AS FailedSteps,
    COUNT(DISTINCT CASE WHEN ds.Status = 'Blocked' THEN ds.Id END) AS BlockedSteps,
    
    -- 自动化模板关联统计
    COUNT(DISTINCT stsa.Id) AS TotalAutomationAssociations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS CompletedAutomations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Running' THEN stsa.Id END) AS InProgressAutomations,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Failed' THEN stsa.Id END) AS FailedAutomations,
    
    -- 工时统计
    SUM(ct.EstimatedHours) AS TotalEstimatedHours,
    SUM(ct.ActualHours) AS TotalActualHours,
    SUM(ds.EstimatedHours) AS StepEstimatedHours,
    SUM(ds.ActualHours) AS StepActualHours,
    
    -- 逾期统计
    COUNT(DISTINCT CASE WHEN ct.DueDate < GETDATE() AND ct.Status != 'Completed' THEN ct.Id END) AS OverdueTasks,
    
    -- 时间信息
    MIN(ct.StartDate) AS EarliestTaskStartDate,
    MAX(ct.DueDate) AS LatestTaskDueDate,
    p.CreatedTime AS ProjectCreatedTime,
    p.UpdatedTime AS ProjectUpdatedTime

FROM Projects p
    LEFT JOIN CodingTasks ct ON p.Id = ct.ProjectId AND ct.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ct.Id = cts.CodingTaskId AND cts.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON cts.DevelopmentStepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId AND stsa.IsDeleted = 0

WHERE p.IsDeleted = 0
GROUP BY p.Id, p.Name, p.ProjectCode, p.Status, p.Priority, p.Progress, p.CreatedTime, p.UpdatedTime;

GO

-- =============================================
-- 2. 自动化模板使用统计视图
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_AutomationTemplateUsage')
BEGIN
    DROP VIEW V_AutomationTemplateUsage;
    PRINT '已删除现有视图 V_AutomationTemplateUsage';
END
GO

CREATE VIEW V_AutomationTemplateUsage AS
SELECT 
    -- 模板序列信息
    uts.Id AS TemplateSequenceId,
    uts.Name AS TemplateSequenceName,
    uts.Description AS TemplateSequenceDescription,
    uts.Category AS TemplateSequenceCategory,
    uts.Tags AS TemplateSequenceTags,
    uts.IsActive AS TemplateSequenceIsActive,
    uts.UsageCount AS TemplateSequenceUsageCount,
    uts.LastUsedTime AS TemplateSequenceLastUsedTime,
    
    -- 分类信息
    tc.Name AS CategoryName,
    tc.Description AS CategoryDescription,
    
    -- 使用统计
    COUNT(DISTINCT stsa.Id) AS TotalAssociations,
    COUNT(DISTINCT stsa.StepId) AS UniqueStepsUsed,
    COUNT(DISTINCT p.Id) AS UniqueProjectsUsed,
    COUNT(DISTINCT ct.Id) AS UniqueCodingTasksUsed,
    
    -- 执行统计
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS SuccessfulExecutions,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Failed' THEN stsa.Id END) AS FailedExecutions,
    COUNT(DISTINCT CASE WHEN stsa.Status = 'Running' THEN stsa.Id END) AS InProgressExecutions,

    -- 成功率
    CASE
        WHEN COUNT(DISTINCT stsa.Id) > 0
        THEN ROUND(
            CAST(COUNT(DISTINCT CASE WHEN stsa.Status = 'Completed' THEN stsa.Id END) AS FLOAT) /
            COUNT(DISTINCT stsa.Id) * 100, 2
        )
        ELSE 0
    END AS SuccessRate,
    
    -- 模板步骤统计
    COUNT(DISTINCT utst.Id) AS TotalTemplateSteps,
    COUNT(DISTINCT cut.Id) AS UniqueCustomTemplatesUsed,
    
    -- 时间信息
    MIN(stsa.AppliedTime) AS FirstUsedTime,
    MAX(stsa.AppliedTime) AS LastUsedTime,
    AVG(DATEDIFF(MINUTE, stsa.ExecutionStartTime, stsa.ExecutionEndTime)) AS AvgExecutionTimeMinutes,
    
    uts.CreatedTime AS TemplateSequenceCreatedTime,
    uts.UpdatedTime AS TemplateSequenceUpdatedTime

FROM UIAutoMationTemplateSequences uts
    LEFT JOIN CustomTemplateCategories tc ON uts.CategoryId = tc.Id AND tc.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON uts.Id = stsa.SequenceId AND stsa.IsDeleted = 0
    LEFT JOIN DevelopmentSteps ds ON stsa.StepId = ds.Id AND ds.IsDeleted = 0
    LEFT JOIN CodingTaskSteps cts ON ds.Id = cts.DevelopmentStepId AND cts.IsDeleted = 0
    LEFT JOIN CodingTasks ct ON cts.CodingTaskId = ct.Id AND ct.IsDeleted = 0
    LEFT JOIN Projects p ON ct.ProjectId = p.Id AND p.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSteps utst ON uts.Id = utst.SequenceId AND utst.IsDeleted = 0
    LEFT JOIN CustomUIAutoMationTemplates cut ON utst.TemplateId = cut.Id AND cut.IsDeleted = 0

WHERE uts.IsDeleted = 0
GROUP BY uts.Id, uts.Name, uts.Description, uts.Category, uts.Tags, uts.IsActive, 
         uts.UsageCount, uts.LastUsedTime, uts.CreatedTime, uts.UpdatedTime,
         tc.Name, tc.Description;

GO

-- =============================================
-- 3. 开发步骤执行状态视图
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'V_DevelopmentStepExecution')
BEGIN
    DROP VIEW V_DevelopmentStepExecution;
    PRINT '已删除现有视图 V_DevelopmentStepExecution';
END
GO

CREATE VIEW V_DevelopmentStepExecution AS
SELECT 
    -- 项目信息
    p.Id AS ProjectId,
    p.Name AS ProjectName,
    p.ProjectCode,
    
    -- 编码任务信息
    ct.Id AS CodingTaskId,
    ct.TaskName AS CodingTaskName,
    ct.Status AS CodingTaskStatus,
    
    -- 开发步骤信息
    ds.Id AS DevelopmentStepId,
    ds.StepName,
    ds.StepDescription,
    ds.StepType,
    ds.Priority AS StepPriority,
    ds.Status AS StepStatus,
    ds.Progress AS StepProgress,
    ds.StepOrder,
    ds.StepLevel,
    ds.ParentStepId,
    ds.TechnologyStack,
    ds.ComponentType,
    ds.FileType,
    ds.FilePath,
    
    -- 执行时间信息
    ds.StartTime AS StepStartTime,
    ds.EndTime AS StepEndTime,
    ds.CompletedTime AS StepCompletedTime,
    ds.EstimatedHours AS StepEstimatedHours,
    ds.ActualHours AS StepActualHours,
    
    -- 执行结果
    ds.ExecutionResult,
    ds.ErrorMessage,
    ds.OutputPath,
    ds.GeneratedFiles,
    
    -- AI信息
    ds.AIPrompt,
    ds.AIProvider,
    ds.AIModel,
    CASE WHEN ds.AIGeneratedCode IS NOT NULL THEN 1 ELSE 0 END AS HasAIGeneratedCode,
    
    -- 自动化关联信息
    stsa.Id AS AssociationId,
    stsa.Status AS AutomationExecutionStatus,
    stsa.Progress AS AutomationExecutionProgress,
    stsa.ExecutionStartTime AS AutomationStartTime,
    stsa.ExecutionEndTime AS AutomationEndTime,
    stsa.ExecutionResult AS AutomationExecutionResult,
    stsa.ErrorMessage AS AutomationErrorMessage,
    
    -- 模板序列信息
    uts.Id AS TemplateSequenceId,
    uts.Name AS TemplateSequenceName,
    uts.Category AS TemplateSequenceCategory,
    
    -- 计算字段
    CASE 
        WHEN ds.StartTime IS NOT NULL AND ds.EndTime IS NOT NULL 
        THEN DATEDIFF(MINUTE, ds.StartTime, ds.EndTime)
        ELSE NULL 
    END AS ExecutionTimeMinutes,
    
    CASE 
        WHEN ds.EstimatedHours IS NOT NULL AND ds.EstimatedHours > 0 AND ds.ActualHours IS NOT NULL
        THEN ROUND(ds.ActualHours / ds.EstimatedHours * 100, 2)
        ELSE NULL 
    END AS HourUtilization,
    
    CASE 
        WHEN ds.ParentStepId IS NULL THEN 1 ELSE 0 
    END AS IsTopLevelStep,
    
    -- 时间戳
    ds.CreatedTime AS StepCreatedTime,
    ds.UpdatedTime AS StepUpdatedTime

FROM DevelopmentSteps ds
    INNER JOIN CodingTaskSteps cts ON ds.Id = cts.DevelopmentStepId AND cts.IsDeleted = 0
    INNER JOIN CodingTasks ct ON cts.CodingTaskId = ct.Id AND ct.IsDeleted = 0
    INNER JOIN Projects p ON ct.ProjectId = p.Id AND p.IsDeleted = 0
    LEFT JOIN StepTemplateSequenceAssociations stsa ON ds.Id = stsa.StepId AND stsa.IsDeleted = 0
    LEFT JOIN UIAutoMationTemplateSequences uts ON stsa.SequenceId = uts.Id AND uts.IsDeleted = 0

WHERE ds.IsDeleted = 0;

GO

PRINT '✅ 所有附加视图创建成功！';
PRINT '';
PRINT '已创建的视图：';
PRINT '1. V_ProjectTaskOverview - 项目任务概览统计';
PRINT '2. V_AutomationTemplateUsage - 自动化模板使用统计';
PRINT '3. V_DevelopmentStepExecution - 开发步骤执行状态';
GO
