using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 流程控制类型DTO
    /// </summary>
    public class FlowControlTypeDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 流程控制类型值
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 流程控制描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 是否内置类型
        /// </summary>
        public bool IsBuiltIn { get; set; }

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 是否需要目标步骤
        /// </summary>
        public bool RequiresTarget { get; set; }

        /// <summary>
        /// 是否可以嵌套
        /// </summary>
        public bool CanNest { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }
    }

    /// <summary>
    /// 创建流程控制类型DTO
    /// </summary>
    public class CreateFlowControlTypeDto
    {
        /// <summary>
        /// 流程控制类型值
        /// </summary>
        [Required(ErrorMessage = "流程控制类型值不能为空")]
        [StringLength(50, ErrorMessage = "流程控制类型值长度不能超过50个字符")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [Required(ErrorMessage = "显示名称不能为空")]
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 流程控制描述
        /// </summary>
        [StringLength(500, ErrorMessage = "流程控制描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        [StringLength(50, ErrorMessage = "执行类型长度不能超过50个字符")]
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 是否需要目标步骤
        /// </summary>
        public bool RequiresTarget { get; set; } = false;

        /// <summary>
        /// 是否可以嵌套
        /// </summary>
        public bool CanNest { get; set; } = false;
    }

    /// <summary>
    /// 更新流程控制类型DTO
    /// </summary>
    public class UpdateFlowControlTypeDto
    {
        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string? Label { get; set; }

        /// <summary>
        /// 流程控制描述
        /// </summary>
        [StringLength(500, ErrorMessage = "流程控制描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标名称
        /// </summary>
        [StringLength(100, ErrorMessage = "图标名称长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 颜色标识
        /// </summary>
        [StringLength(20, ErrorMessage = "颜色标识长度不能超过20个字符")]
        public string? Color { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int? SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 参数架构JSON
        /// </summary>
        public string? ParameterSchema { get; set; }

        /// <summary>
        /// 执行类型
        /// </summary>
        [StringLength(50, ErrorMessage = "执行类型长度不能超过50个字符")]
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 是否需要目标步骤
        /// </summary>
        public bool? RequiresTarget { get; set; }

        /// <summary>
        /// 是否可以嵌套
        /// </summary>
        public bool? CanNest { get; set; }
    }

    /// <summary>
    /// 流程控制类型查询DTO
    /// </summary>
    public class FlowControlTypeQueryDto : PageQueryDto
    {
        /// <summary>
        /// 关键词搜索
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 执行类型过滤
        /// </summary>
        public string? ExecutionType { get; set; }

        /// <summary>
        /// 状态过滤
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否内置类型
        /// </summary>
        public bool? IsBuiltIn { get; set; }

        /// <summary>
        /// 是否需要目标步骤
        /// </summary>
        public bool? RequiresTarget { get; set; }

        /// <summary>
        /// 是否可以嵌套
        /// </summary>
        public bool? CanNest { get; set; }
    }
}
