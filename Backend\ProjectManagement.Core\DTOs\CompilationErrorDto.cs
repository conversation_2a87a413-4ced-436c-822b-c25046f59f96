using System;

namespace ProjectManagement.Core.DTOs
{
    /// <summary>
    /// 编译错误DTO
    /// </summary>
    public class CompilationErrorDto
    {
        /// <summary>
        /// 错误ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// 项目类型 (Backend/Frontend)
        /// </summary>
        public string ProjectType { get; set; } = string.Empty;

        /// <summary>
        /// 编译会话ID
        /// </summary>
        public string? CompilationSessionId { get; set; }

        /// <summary>
        /// 严重程度 (Error/Warning/Info/Hidden)
        /// </summary>
        public string Severity { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 行号
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// 列号
        /// </summary>
        public int ColumnNumber { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目路径
        /// </summary>
        public string? ProjectPath { get; set; }

        /// <summary>
        /// 编译器版本
        /// </summary>
        public string? CompilerVersion { get; set; }

        /// <summary>
        /// 目标框架
        /// </summary>
        public string? TargetFramework { get; set; }

        /// <summary>
        /// 编译配置 (Debug/Release)
        /// </summary>
        public string? BuildConfiguration { get; set; }

        /// <summary>
        /// 编译时间
        /// </summary>
        public DateTime CompilationTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 时间戳（用于兼容Python客户端）
        /// </summary>
        public DateTime Timestamp => CompilationTime;
    }

    /// <summary>
    /// 编译错误查询参数
    /// </summary>
    public class CompilationErrorQueryDto
    {
        /// <summary>
        /// 项目类型 (Backend/Frontend)
        /// </summary>
        public string? ProjectType { get; set; }

        /// <summary>
        /// 严重程度 (Error/Warning/Info/Hidden)
        /// </summary>
        public string? Severity { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// 编译会话ID
        /// </summary>
        public string? CompilationSessionId { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 50;

        /// <summary>
        /// 是否只获取错误（不包含警告）
        /// </summary>
        public bool ErrorsOnly { get; set; } = false;
    }

    /// <summary>
    /// 编译错误统计DTO
    /// </summary>
    public class CompilationErrorStatsDto
    {
        /// <summary>
        /// 总错误数
        /// </summary>
        public int TotalErrors { get; set; }

        /// <summary>
        /// 总警告数
        /// </summary>
        public int TotalWarnings { get; set; }

        /// <summary>
        /// 后端错误数
        /// </summary>
        public int BackendErrors { get; set; }

        /// <summary>
        /// 前端错误数
        /// </summary>
        public int FrontendErrors { get; set; }

        /// <summary>
        /// 最新编译时间
        /// </summary>
        public DateTime? LastCompilationTime { get; set; }
    }
}
