using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectManagementAI.WPF.Services
{
    /// <summary>
    /// 知识图谱服务接口 - RAG、知识管理、智能检索
    /// </summary>
    public interface IKnowledgeGraphService
    {
        /// <summary>
        /// 构建项目知识图谱
        /// </summary>
        Task<KnowledgeGraph> BuildProjectKnowledgeGraphAsync(int projectId);

        /// <summary>
        /// 更新知识图谱
        /// </summary>
        Task<bool> UpdateKnowledgeGraphAsync(KnowledgeGraph graph);

        /// <summary>
        /// 智能检索 - RAG检索增强生成
        /// </summary>
        Task<RAGSearchResult> SearchWithRAGAsync(string query, SearchContext context);

        /// <summary>
        /// 语义搜索
        /// </summary>
        Task<IEnumerable<SemanticSearchResult>> SemanticSearchAsync(string query, int topK = 10);

        /// <summary>
        /// 添加知识节点
        /// </summary>
        Task<KnowledgeNode> AddKnowledgeNodeAsync(KnowledgeNode node);

        /// <summary>
        /// 建立知识关联
        /// </summary>
        Task<KnowledgeRelation> CreateRelationAsync(int fromNodeId, int toNodeId, string relationType);

        /// <summary>
        /// 获取相关知识
        /// </summary>
        Task<IEnumerable<KnowledgeNode>> GetRelatedKnowledgeAsync(int nodeId, int depth = 2);

        /// <summary>
        /// 知识推荐
        /// </summary>
        Task<IEnumerable<KnowledgeRecommendation>> GetKnowledgeRecommendationsAsync(int projectId);

        /// <summary>
        /// 导入文档到知识库
        /// </summary>
        Task<ImportResult> ImportDocumentAsync(string filePath, DocumentType type);

        /// <summary>
        /// 向量化文档内容
        /// </summary>
        Task<VectorEmbedding> EmbedDocumentAsync(string content);

        /// <summary>
        /// 相似度计算
        /// </summary>
        Task<double> CalculateSimilarityAsync(VectorEmbedding embedding1, VectorEmbedding embedding2);
    }

    #region 数据模型

    /// <summary>
    /// 知识图谱
    /// </summary>
    public class KnowledgeGraph
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int ProjectId { get; set; }
        public List<KnowledgeNode> Nodes { get; set; } = new();
        public List<KnowledgeRelation> Relations { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 知识节点
    /// </summary>
    public class KnowledgeNode
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // 文档、代码、需求、问题等
        public string Category { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public VectorEmbedding? Embedding { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 知识关联
    /// </summary>
    public class KnowledgeRelation
    {
        public int Id { get; set; }
        public int FromNodeId { get; set; }
        public int ToNodeId { get; set; }
        public string RelationType { get; set; } = string.Empty; // 依赖、引用、相关、包含等
        public double Weight { get; set; } = 1.0;
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// RAG搜索结果
    /// </summary>
    public class RAGSearchResult
    {
        public string Query { get; set; } = string.Empty;
        public List<RetrievedDocument> RetrievedDocuments { get; set; } = new();
        public string GeneratedResponse { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public List<string> Sources { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 检索到的文档
    /// </summary>
    public class RetrievedDocument
    {
        public int NodeId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public double RelevanceScore { get; set; }
        public string Source { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 语义搜索结果
    /// </summary>
    public class SemanticSearchResult
    {
        public int NodeId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public double SimilarityScore { get; set; }
        public string Type { get; set; } = string.Empty;
        public List<string> MatchedKeywords { get; set; } = new();
    }

    /// <summary>
    /// 搜索上下文
    /// </summary>
    public class SearchContext
    {
        public int? ProjectId { get; set; }
        public List<string> Categories { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new();
    }

    /// <summary>
    /// 知识推荐
    /// </summary>
    public class KnowledgeRecommendation
    {
        public int NodeId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public double RecommendationScore { get; set; }
        public string Type { get; set; } = string.Empty;
    }

    /// <summary>
    /// 文档导入结果
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public int ImportedNodesCount { get; set; }
        public int ImportedRelationsCount { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// 向量嵌入
    /// </summary>
    public class VectorEmbedding
    {
        public int Id { get; set; }
        public float[] Vector { get; set; } = Array.Empty<float>();
        public int Dimension { get; set; }
        public string Model { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 文档类型
    /// </summary>
    public enum DocumentType
    {
        Text,
        Markdown,
        PDF,
        Word,
        Code,
        API,
        Database,
        Configuration
    }

    #endregion
}
