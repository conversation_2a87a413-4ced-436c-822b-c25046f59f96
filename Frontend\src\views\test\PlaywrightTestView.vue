<template>
  <div class="playwright-test-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-row :gutter="20" align="middle">
        <el-col :xs="24" :sm="24" :md="10" :lg="8" :xl="8">
          <h2>🎭 Playwright现代化测试框架</h2>
        </el-col>
        <el-col :xs="24" :sm="24" :md="14" :lg="16" :xl="16" class="toolbar-actions">
          <!-- AI配置区域 -->
          <div class="ai-config-section">
            <el-select
              v-model="selectedAIProvider"
              placeholder="选择AI提供商"
              size="small"
              style="width: 180px; margin-right: 10px;"
              @change="onAIProviderChange"
            >
              <el-option
                v-for="provider in aiProviders"
                :key="provider.id"
                :label="`${provider.providerName} - ${provider.modelName}`"
                :value="provider.id"
              >
                <div class="ai-provider-option">
                  <div class="provider-name">{{ provider.providerName }}</div>
                  <div class="model-name">{{ provider.modelName }}</div>
                </div>
              </el-option>
            </el-select>
            <el-button
              size="small"
              type="info"
              @click="refreshAIProviders"
              :loading="loadingAIProviders"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>

          <!-- 创建和生成按钮组 -->
          <div class="button-group primary-actions">
            <el-button type="primary" size="small" @click="createNewTest">
              <el-icon><Plus /></el-icon>
              新建测试
            </el-button>
            <el-button type="success" size="small" @click="openAIGenerateDialog">
              <el-icon><MagicStick /></el-icon>
              AI生成
            </el-button>
          </div>
          
          <!-- 执行和录制按钮组 -->
          <div class="button-group execution-actions">
            <el-button type="success" size="small" @click="runSelectedTest" :disabled="!selectedTest">
              <el-icon><VideoPlay /></el-icon>
              运行
            </el-button>
            <el-button type="info" size="small" @click="recordTest">
              <el-icon><VideoCamera /></el-icon>
              录制
            </el-button>
          </div>
          
          <!-- 下载和脚本按钮组 -->
          <div class="button-group utility-actions">
            <el-dropdown @command="handleDownloadCommand">
              <el-button type="warning" size="small">
                <el-icon><Download /></el-icon>
                下载
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="single" :disabled="!selectedTest">下载当前测试</el-dropdown-item>
                  <el-dropdown-item command="all">下载所有测试</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button type="warning" size="small" @click="openScriptGenerateDialog">
              <el-icon><Tools /></el-icon>
              生成脚本
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧：测试列表 -->
      <el-col :span="6">
        <el-card class="test-list-card">
          <template #header>
            <div class="card-header">
              <span>📋 Playwright测试</span>
              <el-button type="text" @click="refreshTests">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          
          <!-- 测试分类 -->
          <el-tabs v-model="activeCategory" class="test-tabs">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane label="E2E测试" name="e2e" />
            <el-tab-pane label="组件测试" name="component" />
            <el-tab-pane label="API测试" name="api" />
          </el-tabs>
          
          <!-- 测试列表 -->
          <div class="test-list">
            <div
              v-for="test in filteredTests"
              :key="test.id"
              class="test-item"
              :class="{ active: selectedTest?.id === test.id }"
              @click="selectTest(test)"
            >
              <div class="test-info">
                <div class="test-name">{{ test.name }}</div>
                <div class="test-meta">
                  <el-tag :type="getStatusType(test.status)" size="small">
                    {{ test.status }}
                  </el-tag>
                  <span class="test-browser">{{ test.browser }}</span>
                </div>
              </div>
              <div class="test-actions">
                <el-button type="text" size="small" @click.stop="editTest(test)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="text" size="small" @click.stop="deleteTest(test)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 中间：测试编辑器 -->
      <el-col :span="12">
        <el-card class="editor-card">
          <template #header>
            <div class="card-header">
              <span>📝 测试编辑器</span>
              <div class="editor-actions">
                <el-button type="text" @click="saveTest" :disabled="!selectedTest">
                  <el-icon><Document /></el-icon>
                  保存
                </el-button>
                <el-button type="text" @click="formatCode" :disabled="!selectedTest">
                  <el-icon><Setting /></el-icon>
                  格式化
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-if="selectedTest" class="editor-content">
            <!-- 测试基本信息 -->
            <el-form :model="selectedTest" label-width="80px" class="test-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="测试名称">
                    <el-input v-model="selectedTest.name" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="浏览器">
                    <el-select v-model="selectedTest.browser" style="width: 100%">
                      <el-option label="Chromium" value="chromium" />
                      <el-option label="Firefox" value="firefox" />
                      <el-option label="WebKit" value="webkit" />
                      <el-option label="全部" value="all" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="描述">
                <el-input v-model="selectedTest.description" type="textarea" :rows="2" />
              </el-form-item>
            </el-form>
            
            <!-- 代码编辑器 -->
            <div class="code-editor">
              <el-tabs v-model="activeEditorTab">
                <el-tab-pane label="JavaScript代码" name="javascript">
                  <div class="editor-toolbar">
                    <el-button-group>
                      <el-button size="small" @click="insertTemplate('basic')">基础模板</el-button>
                      <el-button size="small" @click="insertTemplate('login')">登录模板</el-button>
                      <el-button size="small" @click="insertTemplate('api')">API测试</el-button>
                    </el-button-group>
                  </div>
                  <el-input
                    v-model="selectedTest.code"
                    type="textarea"
                    :rows="20"
                    placeholder="在这里编写您的Playwright测试代码..."
                    class="code-textarea"
                  />
                </el-tab-pane>
                <el-tab-pane label="配置" name="config">
                  <playwright-config v-model="selectedTest.config" />
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          
          <div v-else class="no-test-selected">
            <el-empty description="请选择一个测试进行编辑" />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：AI助手和执行结果 -->
      <el-col :span="6">
        <!-- AI测试助手 -->
        <AITestAssistant
          :current-test="selectedTest"
          :all-tests="tests"
          :selected-ai-provider="selectedAIProvider"
          :ai-providers="aiProviders"
          @update-test-code="handleUpdateTestCode"
          @show-code-diff="handleShowCodeDiff"
          @show-generated-tests="handleShowGeneratedTests"
          @apply-test-pattern="handleApplyTestPattern"
          @ai-provider-change="onAIProviderChange"
          class="ai-assistant-panel"
        />
        
        <el-card class="result-card">
          <template #header>
            <div class="card-header">
              <span>📊 执行结果</span>
              <el-button type="text" @click="clearResults">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </template>
          
          <!-- 执行状态 -->
          <div class="execution-status">
            <el-alert
              v-if="executionStatus"
              :title="executionStatus.title"
              :type="executionStatus.type"
              :description="executionStatus.description"
              show-icon
              :closable="false"
            />
          </div>
          
          <!-- 浏览器状态 -->
          <div class="browser-status" v-if="browserStatus">
            <el-descriptions :column="1" size="small" title="浏览器状态">
              <el-descriptions-item label="Chromium">
                <el-tag :type="browserStatus.chromium ? 'success' : 'danger'">
                  {{ browserStatus.chromium ? '可用' : '不可用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="Firefox">
                <el-tag :type="browserStatus.firefox ? 'success' : 'danger'">
                  {{ browserStatus.firefox ? '可用' : '不可用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="WebKit">
                <el-tag :type="browserStatus.webkit ? 'success' : 'danger'">
                  {{ browserStatus.webkit ? '可用' : '不可用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 实时日志 -->
          <div class="log-container">
            <div class="log-header">
              <span>📝 执行日志</span>
              <el-switch
                v-model="autoScroll"
                active-text="自动滚动"
                size="small"
              />
            </div>
            <div ref="logContainer" class="log-content">
              <div
                v-for="(log, index) in executionLogs"
                :key="index"
                class="log-item"
                :class="log.level"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
          
          <!-- 测试报告 -->
          <div class="test-report" v-if="testReport">
            <el-descriptions :column="1" size="small" title="测试报告">
              <el-descriptions-item label="总测试数">
                {{ testReport.total }}
              </el-descriptions-item>
              <el-descriptions-item label="通过">
                <span class="success-count">{{ testReport.passed }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="失败">
                <span class="error-count">{{ testReport.failed }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="跳过">
                {{ testReport.skipped }}
              </el-descriptions-item>
              <el-descriptions-item label="执行时间">
                {{ testReport.duration }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- AI测试生成对话框 -->
    <AITestGenerateDialog
      v-model="showAIGenerateDialog"
      :project-id="currentProjectId"
      @generated="handleAIGenerated"
    />

    <!-- 脚本生成对话框 -->
    <ScriptGenerateDialog
      v-model="showScriptGenerateDialog"
      :available-tests="tests"
      @generated="handleScriptGenerated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  VideoPlay,
  VideoCamera,
  Download,
  Refresh,
  Edit,
  Delete,
  Document,
  Setting,
  MagicStick,
  Tools,
  ArrowDown
} from '@element-plus/icons-vue'

// 导入组件
import PlaywrightConfig from '@/components/test/PlaywrightConfig.vue'
import AITestGenerateDialog from '@/components/test/AITestGenerateDialog.vue'
import AITestAssistant from '@/components/test/AITestAssistant.vue'
import ScriptGenerateDialog from '@/components/test/ScriptGenerateDialog.vue'
import { PlaywrightTestService, type PlaywrightTest } from '@/services/playwrightTest'
import { AITestGenerationService, batchGenerateTests, type TestGenerationConfig } from '@/services/aiTestGeneration'
import { AIProviderService } from '@/services/aiProvider'

interface ExecutionLog {
  timestamp: Date
  level: string
  message: string
}

interface TestReport {
  total: number
  passed: number
  failed: number
  skipped: number
  duration: string
}

interface AIProvider {
  id: number
  userId: number
  providerName: string
  modelName: string
  modelType: string
  isActive: boolean
  isDefault: boolean
}

// 响应式数据
const activeCategory = ref('all')
const activeEditorTab = ref('javascript')
const selectedTest = ref<PlaywrightTest | null>(null)
const autoScroll = ref(true)
const logContainer = ref<HTMLElement>()

const executionStatus = ref<{
  title: string
  type: 'error' | 'success' | 'primary' | 'warning' | 'info'
  description: string
} | null>(null)

const browserStatus = ref({
  chromium: true,
  firefox: true,
  webkit: false
})

const executionLogs = ref<ExecutionLog[]>([])
const testReport = ref<TestReport | null>(null)

// 测试数据 - 初始为空，将从API加载
const tests = ref<PlaywrightTest[]>([])

// AI生成相关
const showAIGenerateDialog = ref(false)
const showScriptGenerateDialog = ref(false)
const currentProjectId = ref(1) // 从路由或store获取当前项目ID

// AI提供商相关
const aiProviders = ref<AIProvider[]>([])
const selectedAIProvider = ref<number>()
const loadingAIProviders = ref(false)

// 计算属性
const filteredTests = computed(() => {
  let filtered = tests.value

  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(test => test.category === activeCategory.value)
  }

  return filtered
})

// 方法
const refreshTests = async () => {
  try {
    const loadingMessage = ElMessage({
      message: '正在加载测试列表...',
      type: 'info',
      duration: 0
    })

    const testList = await PlaywrightTestService.getTests()
    tests.value = testList.map(test => ({
      ...test,
      updatedTime: new Date(test.updatedTime)
    }))

    loadingMessage.close()
    ElMessage.success('测试列表已刷新')
  } catch (error) {
    console.error('加载测试列表失败:', error)
    ElMessage.error('加载测试列表失败，请检查网络连接')
  }
}

const selectTest = (test: PlaywrightTest) => {
  selectedTest.value = test
}

const createNewTest = () => {
  const newTest: PlaywrightTest = {
    id: Date.now(), // 临时ID，保存时会被替换为真实ID
    name: '新建测试',
    description: '',
    category: 'e2e',
    browser: 'chromium',
    code: `import { test, expect } from '@playwright/test';

test.describe('新建测试', () => {
  test('基础测试', async ({ page }) => {
    // 访问页面
    await page.goto('/');

    // 验证页面加载
    await expect(page.locator('body')).toBeVisible();

    // 在这里添加您的测试代码
  });
});`,
    config: {
      headless: false,
      viewport: { width: 1920, height: 1080 },
      timeout: 30000,
      baseURL: 'http://localhost:3000',
      browsers: ['chromium'],
      device: '',
      userAgent: '',
      parallel: false,
      workers: 1,
      retries: 0,
      globalTimeout: 300000,
      reporters: ['html', 'line'],
      screenshot: 'only-on-failure',
      video: 'retain-on-failure',
      trace: 'retain-on-failure',
      ignoreHTTPSErrors: false,
      javaScriptEnabled: true,
      waitForNetworkIdle: false,
      launchOptions: ''
    },
    tags: [],
    priority: 'medium',
    status: '草稿',
    createdTime: new Date(),
    updatedTime: new Date()
  }

  tests.value.push(newTest)
  selectedTest.value = newTest
  ElMessage.success('新测试已创建，请编辑后保存')
}

const editTest = (test: PlaywrightTest) => {
  selectedTest.value = test
}

const deleteTest = async (test: PlaywrightTest) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除测试 "${test.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 如果是临时测试（未保存到服务器），直接从本地删除
    if (!test.id || test.id > 1000000000) {
      const index = tests.value.findIndex(t => t.id === test.id)
      if (index > -1) {
        tests.value.splice(index, 1)
        if (selectedTest.value?.id === test.id) {
          selectedTest.value = null
        }
        ElMessage.success('测试删除成功')
      }
      return
    }

    // 调用API删除服务器上的测试
    const loadingMessage = ElMessage({
      message: '正在删除测试...',
      type: 'info',
      duration: 0
    })

    await PlaywrightTestService.deleteTest(test.id)

    // 从本地列表中删除
    const index = tests.value.findIndex(t => t.id === test.id)
    if (index > -1) {
      tests.value.splice(index, 1)
      if (selectedTest.value?.id === test.id) {
        selectedTest.value = null
      }
    }

    loadingMessage.close()
    ElMessage.success('测试删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除测试失败:', error)
      ElMessage.error('删除测试失败，请检查网络连接或联系管理员')
    }
  }
}

const saveTest = async () => {
  if (!selectedTest.value) return

  try {
    // 显示保存中状态
    const loadingMessage = ElMessage({
      message: '正在保存测试...',
      type: 'info',
      duration: 0
    })

    // 如果是新测试（没有id或id为临时id），则创建新测试
    if (!selectedTest.value.id || selectedTest.value.id > 1000000000) {
      const testData = {
        name: selectedTest.value.name,
        description: selectedTest.value.description,
        category: selectedTest.value.category,
        browser: selectedTest.value.browser,
        code: selectedTest.value.code,
        config: selectedTest.value.config,
        status: selectedTest.value.status
      }

      const createdTest = await PlaywrightTestService.createTest(testData)

      // 更新本地测试数据
      const index = tests.value.findIndex(t => t.id === selectedTest.value!.id)
      if (index > -1) {
        tests.value[index] = { ...createdTest, updatedTime: new Date() }
        selectedTest.value = tests.value[index]
      }

      loadingMessage.close()
      ElMessage.success('测试创建成功')
    } else {
      // 更新现有测试
      const testData = {
        name: selectedTest.value.name,
        description: selectedTest.value.description,
        category: selectedTest.value.category,
        browser: selectedTest.value.browser,
        code: selectedTest.value.code,
        config: selectedTest.value.config,
        status: selectedTest.value.status
      }

      const updatedTest = await PlaywrightTestService.updateTest(selectedTest.value.id, testData)

      // 更新本地测试数据
      const index = tests.value.findIndex(t => t.id === selectedTest.value!.id)
      if (index > -1) {
        tests.value[index] = { ...updatedTest, updatedTime: new Date() }
        selectedTest.value = tests.value[index]
      }

      loadingMessage.close()
      ElMessage.success('测试保存成功')
    }
  } catch (error) {
    console.error('保存测试失败:', error)
    ElMessage.error('保存测试失败，请检查网络连接或联系管理员')
  }
}

const formatCode = () => {
  if (!selectedTest.value) return
  
  // TODO: 实现代码格式化
  ElMessage.success('代码格式化完成')
}

const insertTemplate = (templateType: string) => {
  if (!selectedTest.value) return
  
  const templates = {
    basic: `const { test, expect } = require('@playwright/test');

test('基础测试', async ({ page }) => {
  await page.goto('https://example.com');
  
  // 在这里添加您的测试代码
  await expect(page).toHaveTitle(/Example/);
});`,
    
    login: `const { test, expect } = require('@playwright/test');

test('登录测试', async ({ page }) => {
  await page.goto('/login');
  
  // 填写登录表单
  await page.fill('[name="username"]', 'your_username');
  await page.fill('[name="password"]', 'your_password');
  
  // 点击登录按钮
  await page.click('button[type="submit"]');
  
  // 验证登录成功
  await expect(page.locator('.dashboard')).toBeVisible();
});`,
    
    api: `const { test, expect } = require('@playwright/test');

test('API测试', async ({ request }) => {
  // 发送GET请求
  const response = await request.get('/api/users');
  
  // 验证响应状态
  expect(response.status()).toBe(200);
  
  // 验证响应数据
  const data = await response.json();
  expect(data).toHaveProperty('users');
  expect(Array.isArray(data.users)).toBeTruthy();
});`
  }
  
  selectedTest.value.code = templates[templateType as keyof typeof templates] || ''
  ElMessage.success(`${templateType}模板已插入`)
}

const runSelectedTest = async () => {
  if (!selectedTest.value) return
  
  executionStatus.value = {
    title: '正在执行测试',
    type: 'warning',
    description: `正在运行测试: ${selectedTest.value.name}`
  }
  
  executionLogs.value = []
  
  // 模拟Playwright测试执行
  const logs = [
    { level: 'info', message: '启动Playwright测试运行器...' },
    { level: 'info', message: `启动${selectedTest.value.browser}浏览器...` },
    { level: 'info', message: '执行测试用例...' },
    { level: 'success', message: '页面导航成功' },
    { level: 'info', message: '执行用户交互...' },
    { level: 'success', message: '断言验证通过' },
    { level: 'success', message: '测试执行完成' }
  ]
  
  for (let i = 0; i < logs.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    executionLogs.value.push({
      timestamp: new Date(),
      ...logs[i]
    })
    
    if (autoScroll.value) {
      await nextTick()
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    }
  }
  
  executionStatus.value = {
    title: '执行完成',
    type: 'success',
    description: '测试执行成功'
  }
  
  testReport.value = {
    total: 1,
    passed: 1,
    failed: 0,
    skipped: 0,
    duration: '3.2秒'
  }
}

const recordTest = () => {
  ElMessage.info('启动Playwright录制功能...')
  // TODO: 实现测试录制功能
}

const downloadTest = async () => {
  if (!selectedTest.value) return

  try {
    const response = await fetch(`/api/playwright-tests/${selectedTest.value.id}/download`, {
      method: 'GET'
    })

    if (response.ok) {
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let fileName = `${selectedTest.value.name}.spec.ts`
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="(.+)"/)
        if (fileNameMatch) {
          fileName = fileNameMatch[1]
        }
      }

      // 创建下载链接
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success(`测试文件 "${fileName}" 下载成功`)
    } else {
      ElMessage.error('下载失败')
    }
  } catch (error) {
    console.error('下载测试失败:', error)
    ElMessage.error('下载测试失败')
  }
}

const downloadMultipleTests = async () => {
  try {
    const response = await fetch('/api/playwright-tests/download-multiple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        testIds: null, // 下载所有测试
        category: null,
        browser: null,
        projectId: null
      })
    })

    if (response.ok) {
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let fileName = `playwright-tests-${new Date().toISOString().slice(0, 10)}.zip`
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="(.+)"/)
        if (fileNameMatch) {
          fileName = fileNameMatch[1]
        }
      }

      // 创建下载链接
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success(`测试文件包 "${fileName}" 下载成功`)
    } else {
      ElMessage.error('下载失败')
    }
  } catch (error) {
    console.error('下载测试失败:', error)
    ElMessage.error('下载测试失败')
  }
}

const clearResults = () => {
  executionLogs.value = []
  executionStatus.value = null
  testReport.value = null
  ElMessage.success('执行结果已清空')
}

const getStatusType = (status: string): 'success' | 'warning' | 'danger' | 'info' => {
  const types: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    '就绪': 'success',
    '草稿': 'info',
    '运行中': 'warning',
    '失败': 'danger'
  }
  return types[status] || 'info'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN')
}

// AI生成相关方法
const openAIGenerateDialog = () => {
  showAIGenerateDialog.value = true
}

const openScriptGenerateDialog = () => {
  if (tests.value.length === 0) {
    ElMessage.warning('请先创建一些测试用例')
    return
  }
  showScriptGenerateDialog.value = true
}

const handleAIGenerated = (generatedTests: PlaywrightTest[]) => {
  // 将AI生成的测试添加到测试列表
  tests.value.push(...generatedTests)
  ElMessage.success(`成功生成 ${generatedTests.length} 个AI测试用例`)
  
  // 如果没有选中的测试，选中第一个生成的测试
  if (!selectedTest.value && generatedTests.length > 0) {
    selectedTest.value = generatedTests[0]
  }
}

// AI助手相关方法
const handleUpdateTestCode = (code: string) => {
  if (selectedTest.value) {
    selectedTest.value.code = code
    ElMessage.success('测试代码已更新')
  }
}

const handleShowCodeDiff = (params: any) => {
  // 显示代码对比对话框
  ElMessage.info('代码对比功能开发中...')
}

const handleShowGeneratedTests = (tests: any[]) => {
  // 显示生成的测试用例
  ElMessage.info(`查看 ${tests.length} 个生成的测试用例`)
}

const handleApplyTestPattern = (pattern: any) => {
  if (selectedTest.value) {
    selectedTest.value.code = pattern.code
    ElMessage.success(`已应用测试模式: ${pattern.name}`)
  }
}

const handleScriptGenerated = (result: any) => {
  ElMessage.success('自动化脚本生成完成')
  console.log('生成的脚本:', result)
}

// AI提供商相关方法
const loadAIProviders = async () => {
  try {
    loadingAIProviders.value = true
    const providers = await AIProviderService.getModelConfigurations()
    aiProviders.value = providers.map(provider => ({
      id: provider.id,
      userId: provider.userId,
      providerName: provider.modelName.split('-')[0] || 'Unknown',
      modelName: provider.modelName,
      modelType: 'Testing',
      isActive: provider.isActive,
      isDefault: false
    }))

    // 默认选择第一个活跃的提供商
    const activeProvider = aiProviders.value.find(p => p.isActive)
    if (activeProvider) {
      selectedAIProvider.value = activeProvider.id
    }

    console.log('AI提供商加载成功:', aiProviders.value)
  } catch (error) {
    console.error('加载AI提供商失败:', error)
    ElMessage.warning('加载AI提供商失败，请检查配置')
  } finally {
    loadingAIProviders.value = false
  }
}

const refreshAIProviders = async () => {
  await loadAIProviders()
  ElMessage.success('AI提供商列表已刷新')
}

const onAIProviderChange = (providerId: number) => {
  const provider = aiProviders.value.find(p => p.id === providerId)
  if (provider) {
    ElMessage.success(`已选择AI提供商: ${provider.providerName} - ${provider.modelName}`)
  }
}

const handleDownloadCommand = (command: string) => {
  switch (command) {
    case 'single':
      downloadTest()
      break
    case 'all':
      downloadMultipleTests()
      break
  }
}

onMounted(() => {
  // 初始化时加载测试列表
  refreshTests()

  // 加载AI提供商列表
  loadAIProviders()

  // 检查浏览器状态
  // TODO: 实际检查Playwright浏览器安装状态
})
</script>

<style lang="scss" scoped>
.playwright-test-container {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.toolbar {
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
    font-size: 20px;
    
    @media (max-width: 768px) {
      font-size: 18px;
      text-align: center;
      margin-bottom: 8px;
    }
    
    @media (max-width: 576px) {
      font-size: 16px;
    }
  }
  
  .toolbar-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;

    .ai-config-section {
      display: flex;
      align-items: center;
      padding: 6px 10px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;
      margin-right: 16px;

      .el-select {
        margin-right: 8px;
      }
    }
    
    .button-group {
      display: flex;
      gap: 6px;
      align-items: center;
      
      &.primary-actions {
        border-right: 1px solid var(--el-border-color-lighter);
        padding-right: 16px;
      }
      
      &.execution-actions {
        border-right: 1px solid var(--el-border-color-lighter);
        padding-right: 16px;
      }
      
      .el-button {
        margin: 0;
        
        &.el-button--small {
          padding: 8px 12px;
          font-size: 12px;
        }
      }
      
      .el-dropdown {
        .el-button {
          margin: 0;
        }
      }
    }
    
    // 响应式设计
    @media (max-width: 1200px) {
      gap: 12px;
      
      .button-group {
        gap: 4px;
        
        &.primary-actions,
        &.execution-actions {
          border-right: none;
          padding-right: 0;
        }
      }
    }
    
    @media (max-width: 768px) {
      justify-content: flex-start;
      margin-top: 12px;
      
      .button-group {
        flex-wrap: wrap;
        
        .el-button {
          padding: 6px 8px;
          font-size: 11px;
          
          .el-icon {
            margin-right: 2px;
          }
        }
      }
    }
    
    @media (max-width: 576px) {
      .button-group {
        .el-button {
          span:not(.el-icon) {
            display: none;
          }
          
          .el-icon {
            margin-right: 0;
          }
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  height: 0;
}

.ai-assistant-panel {
  margin-bottom: 16px;
  max-height: 400px;
}

.test-list-card,
.editor-card,
.result-card {
  height: 100%;
  
  :deep(.el-card__body) {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-tabs {
  margin-bottom: 16px;
  
  :deep(.el-tabs__header) {
    margin: 0 0 16px 0;
  }
}

.test-list {
  flex: 1;
  overflow-y: auto;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
  
  &.active {
    border-color: #409eff;
    background-color: #ecf5ff;
  }
  
  .test-info {
    flex: 1;
    
    .test-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .test-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .test-browser {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .test-actions {
    display: flex;
    gap: 4px;
  }
}

.editor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.test-form {
  margin-bottom: 16px;
}

.code-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .editor-toolbar {
    margin-bottom: 8px;
  }
  
  .code-textarea {
    :deep(textarea) {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  :deep(.el-tabs__content) {
    flex: 1;
    height: 0;
  }
  
  :deep(.el-tab-pane) {
    height: 100%;
  }
}

.no-test-selected {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.execution-status {
  margin-bottom: 16px;
}

.browser-status {
  margin-bottom: 16px;
}

.log-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .log-content {
    flex: 1;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }
  
  .log-item {
    margin-bottom: 4px;
    
    .log-time {
      color: #909399;
      margin-right: 8px;
    }
    
    .log-message {
      color: #303133;
    }
    
    &.info .log-message {
      color: #409eff;
    }
    
    &.success .log-message {
      color: #67c23a;
    }
    
    &.warning .log-message {
      color: #e6a23c;
    }
    
    &.error .log-message {
      color: #f56c6c;
    }
  }
}

// AI提供商选项样式
.ai-provider-option {
  .provider-name {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
  }

  .model-name {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-config-section {
    width: 100%;
    margin-bottom: 8px;
    margin-right: 0 !important;

    .el-select {
      flex: 1;
      margin-right: 8px;
    }
  }
}

.test-report {
  .success-count {
    color: #67c23a;
    font-weight: bold;
  }
  
  .error-count {
    color: #f56c6c;
    font-weight: bold;
  }
}
</style>