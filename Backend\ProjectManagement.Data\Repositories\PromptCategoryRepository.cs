using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using SqlSugar;

namespace ProjectManagement.Data.Repositories;

/// <summary>
/// Prompt分类仓储实现
/// </summary>
public class PromptCategoryRepository : BaseRepository<PromptCategory>, IPromptCategoryRepository
{
    public PromptCategoryRepository(ISqlSugarClient db, ILogger<PromptCategoryRepository> logger)
        : base(db, logger)
    {
    }

    /// <summary>
    /// 获取所有启用的分类（包含层级结构）
    /// </summary>
    public async Task<List<PromptCategory>> GetEnabledCategoriesAsync()
    {
        try
        {
            return await _db.Queryable<PromptCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用分类失败");
            throw;
        }
    }

    /// <summary>
    /// 获取根分类列表
    /// </summary>
    public async Task<List<PromptCategory>> GetRootCategoriesAsync()
    {
        try
        {
            return await _db.Queryable<PromptCategory>()
                .Where(x => x.ParentId == null && !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取根分类失败");
            throw;
        }
    }

    /// <summary>
    /// 获取子分类列表
    /// </summary>
    public async Task<List<PromptCategory>> GetChildCategoriesAsync(int parentId)
    {
        try
        {
            return await _db.Queryable<PromptCategory>()
                .Where(x => x.ParentId == parentId && !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.SortOrder)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取子分类失败，ParentId: {ParentId}", parentId);
            throw;
        }
    }

    /// <summary>
    /// 获取分类树形结构
    /// </summary>
    public async Task<List<PromptCategory>> GetCategoryTreeAsync()
    {
        try
        {
            var allCategories = await GetEnabledCategoriesAsync();
            var rootCategories = allCategories.Where(x => x.ParentId == null).ToList();

            foreach (var root in rootCategories)
            {
                BuildCategoryTree(root, allCategories);
            }

            return rootCategories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类树形结构失败");
            throw;
        }
    }

    /// <summary>
    /// 构建分类树形结构
    /// </summary>
    private void BuildCategoryTree(PromptCategory parent, List<PromptCategory> allCategories)
    {
        var children = allCategories.Where(x => x.ParentId == parent.Id).ToList();
        parent.Children = children;

        foreach (var child in children)
        {
            child.Parent = parent;
            BuildCategoryTree(child, allCategories);
        }
    }

    /// <summary>
    /// 更新分类模板数量
    /// </summary>
    public async Task UpdateTemplateCountAsync(int categoryId, int count)
    {
        try
        {
            await _db.Updateable<PromptCategory>()
                .SetColumns(x => new PromptCategory
                {
                    TemplateCount = count,
                    UpdatedTime = DateTime.Now
                })
                .Where(x => x.Id == categoryId)
                .ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新分类模板数量失败，CategoryId: {CategoryId}", categoryId);
            throw;
        }
    }

    /// <summary>
    /// 获取分类路径
    /// </summary>
    public async Task<List<PromptCategory>> GetCategoryPathAsync(int categoryId)
    {
        try
        {
            var path = new List<PromptCategory>();
            var current = await GetByIdAsync(categoryId);

            while (current != null)
            {
                path.Insert(0, current);
                if (current.ParentId.HasValue)
                {
                    current = await GetByIdAsync(current.ParentId.Value);
                }
                else
                {
                    break;
                }
            }

            return path;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类路径失败，CategoryId: {CategoryId}", categoryId);
            throw;
        }
    }
}
