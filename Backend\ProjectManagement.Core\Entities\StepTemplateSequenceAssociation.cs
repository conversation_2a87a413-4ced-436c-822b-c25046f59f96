using SqlSugar;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// 开发步骤与模板序列关联实体
    /// 功能: 记录开发步骤应用的UI自动化模板序列
    /// 支持: 多对多关联、应用时间跟踪、状态管理
    /// </summary>
    [SugarTable("StepTemplateSequenceAssociations", "开发步骤模板序列关联表")]
    public class StepTemplateSequenceAssociation : BaseEntity
    {
        /// <summary>
        /// 开发步骤ID，关联DevelopmentSteps表
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "开发步骤ID，关联DevelopmentSteps表")]
        public int StepId { get; set; }

        /// <summary>
        /// 模板序列ID，关联UIAutoMationTemplateSequences表
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "模板序列ID，关联UIAutoMationTemplateSequences表")]
        public int SequenceId { get; set; }

        /// <summary>
        /// 应用时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "模板序列应用时间")]
        public DateTime AppliedTime { get; set; }

        /// <summary>
        /// 应用状态: Active(活跃), Inactive(非活跃), Completed(已完成), Failed(失败)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false, ColumnDescription = "应用状态")]
        public string Status { get; set; } = "Active";

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "是否激活")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 执行进度 (0-100)
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "执行进度")]
        public int Progress { get; set; } = 0;

        /// <summary>
        /// 执行开始时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "执行开始时间")]
        public DateTime? ExecutionStartTime { get; set; }

        /// <summary>
        /// 执行完成时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "执行完成时间")]
        public DateTime? ExecutionEndTime { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "执行结果")]
        public string? ExecutionResult { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "错误信息")]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 应用者用户ID
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "应用者用户ID")]
        public int? AppliedByUserId { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "备注信息")]
        public string? Notes { get; set; }

        // 导航属性（不映射到数据库）
        [SugarColumn(IsIgnore = true)]
        public DevelopmentStep? Step { get; set; }

        [SugarColumn(IsIgnore = true)]
        public UIAutoMationTemplateSequence? Sequence { get; set; }

        [SugarColumn(IsIgnore = true)]
        public User? AppliedByUser { get; set; }

        /// <summary>
        /// 开始执行
        /// </summary>
        public void StartExecution()
        {
            Status = "Running";
            ExecutionStartTime = DateTime.Now;
            Progress = 0;
            UpdatedTime = DateTime.Now;
        }

        /// <summary>
        /// 完成执行
        /// </summary>
        public void CompleteExecution(string? result = null)
        {
            Status = "Completed";
            ExecutionEndTime = DateTime.Now;
            Progress = 100;
            ExecutionResult = result;
            UpdatedTime = DateTime.Now;
        }

        /// <summary>
        /// 执行失败
        /// </summary>
        public void FailExecution(string errorMessage)
        {
            Status = "Failed";
            ExecutionEndTime = DateTime.Now;
            ErrorMessage = errorMessage;
            UpdatedTime = DateTime.Now;
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(int progress)
        {
            if (progress < 0) progress = 0;
            if (progress > 100) progress = 100;
            
            Progress = progress;
            UpdatedTime = DateTime.Now;

            if (progress == 100 && Status == "Running")
            {
                CompleteExecution();
            }
        }

        /// <summary>
        /// 停用关联
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
            Status = "Inactive";
            UpdatedTime = DateTime.Now;
        }

        /// <summary>
        /// 激活关联
        /// </summary>
        public void Activate()
        {
            IsActive = true;
            Status = "Active";
            UpdatedTime = DateTime.Now;
        }

        /// <summary>
        /// 获取执行持续时间
        /// </summary>
        public TimeSpan? GetExecutionDuration()
        {
            if (ExecutionStartTime.HasValue && ExecutionEndTime.HasValue)
            {
                return ExecutionEndTime.Value - ExecutionStartTime.Value;
            }
            return null;
        }

        /// <summary>
        /// 检查是否可以执行
        /// </summary>
        public bool CanExecute()
        {
            return IsActive && (Status == "Active" || Status == "Failed");
        }

        /// <summary>
        /// 检查是否正在执行
        /// </summary>
        public bool IsExecuting()
        {
            return Status == "Running";
        }

        /// <summary>
        /// 检查是否已完成
        /// </summary>
        public bool IsCompleted()
        {
            return Status == "Completed";
        }

        /// <summary>
        /// 检查是否失败
        /// </summary>
        public bool IsFailed()
        {
            return Status == "Failed";
        }
    }
}
