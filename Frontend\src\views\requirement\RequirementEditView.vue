<template>
  <div class="requirement-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="text" @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="header-content">
          <h1 class="page-title">编辑需求文档</h1>
          <p class="page-subtitle">修改需求文档的内容和属性</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="form" class="edit-form">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <!-- 基本信息 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          
          <el-form-item label="需求标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入需求标题"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="所属项目" prop="projectId">
            <el-select v-model="form.projectId" placeholder="选择项目" style="width: 100%">
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="需求状态" prop="status">
            <el-select v-model="form.status" placeholder="选择状态" style="width: 200px">
              <el-option label="草稿" value="Draft" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="待审核" value="PendingReview" />
              <el-option label="已发布" value="Published" />
            </el-select>
          </el-form-item>
        </el-card>

        <!-- 需求内容 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>需求内容</span>
            </div>
          </template>
          
          <el-form-item label="需求描述" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              :rows="8"
              placeholder="请输入需求的详细描述，支持Markdown格式"
              maxlength="5000"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 功能性需求 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>功能性需求</span>
              <el-button
                type="primary"
                size="small"
                @click="generateFunctionalRequirements"
                :loading="generatingFunctional"
                style="margin-left: auto;"
              >
                <el-icon><MagicStick /></el-icon>
                AI生成
              </el-button>
            </div>
          </template>
          
          <el-form-item label="功能需求" prop="functionalRequirements">
            <el-input
              v-model="form.functionalRequirements"
              type="textarea"
              :rows="6"
              placeholder="请描述系统应该具备的功能特性"
              maxlength="3000"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 非功能性需求 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>非功能性需求</span>
              <el-button
                type="primary"
                size="small"
                @click="generateNonFunctionalRequirements"
                :loading="generatingNonFunctional"
                style="margin-left: auto;"
              >
                <el-icon><MagicStick /></el-icon>
                AI生成
              </el-button>
            </div>
          </template>
          
          <el-form-item label="非功能需求" prop="nonFunctionalRequirements">
            <el-input
              v-model="form.nonFunctionalRequirements"
              type="textarea"
              :rows="6"
              placeholder="请描述性能、安全、可用性等非功能性要求"
              maxlength="3000"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 用户故事 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>用户故事</span>
              <el-button
                type="primary"
                size="small"
                @click="generateUserStories"
                :loading="generatingUserStories"
                style="margin-left: auto;"
              >
                <el-icon><MagicStick /></el-icon>
                AI生成
              </el-button>
            </div>
          </template>
          
          <el-form-item label="用户故事" prop="userStories">
            <el-input
              v-model="form.userStories"
              type="textarea"
              :rows="6"
              placeholder="以用户的角度描述需求场景"
              maxlength="3000"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 验收标准 -->
        <el-card class="form-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><CircleCheck /></el-icon>
              <span>验收标准</span>
              <el-button
                type="primary"
                size="small"
                @click="generateAcceptanceCriteria"
                :loading="generatingAcceptance"
                style="margin-left: auto;"
              >
                <el-icon><MagicStick /></el-icon>
                AI生成
              </el-button>
            </div>
          </template>
          
          <el-form-item label="验收标准" prop="acceptanceCriteria">
            <el-input
              v-model="form.acceptanceCriteria"
              type="textarea"
              :rows="6"
              placeholder="定义需求完成的验收标准和测试条件"
              maxlength="3000"
              show-word-limit
            />
          </el-form-item>
        </el-card>
      </el-form>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="需求文档不存在或已被删除">
        <el-button type="primary" @click="$router.push('/requirements')">返回需求列表</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  ArrowLeft, Check, InfoFilled, Document, Setting, Monitor,
  User, CircleCheck, MagicStick
} from '@element-plus/icons-vue'
import { RequirementService } from '@/services/requirement'
import { ProjectService } from '@/services/project'
import type { RequirementDocument, ProjectSummary } from '@/types'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const form = ref<any>(null)
const projects = ref<ProjectSummary[]>([])
const formRef = ref<FormInstance>()
const requirementId = computed(() => parseInt(route.params.id as string))

// AI生成状态
const generatingFunctional = ref(false)
const generatingNonFunctional = ref(false)
const generatingUserStories = ref(false)
const generatingAcceptance = ref(false)

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入需求标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度应在2-200个字符之间', trigger: 'blur' }
  ],
  projectId: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择需求状态', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入需求描述', trigger: 'blur' }
  ]
}

// 方法
const loadRequirement = async () => {
  try {
    loading.value = true
    const response = await RequirementService.getRequirementDocument(requirementId.value)
    form.value = {
      title: response.title,
      projectId: response.projectId,
      status: response.status,
      content: response.content || '',
      functionalRequirements: response.functionalRequirements || '',
      nonFunctionalRequirements: response.nonFunctionalRequirements || '',
      userStories: response.userStories || '',
      acceptanceCriteria: response.acceptanceCriteria || ''
    }
  } catch (error: any) {
    console.error('加载需求详情失败:', error)
    ElMessage.error('加载需求详情失败')
    form.value = null
  } finally {
    loading.value = false
  }
}

const loadProjects = async () => {
  try {
    const response = await ProjectService.getProjects({ pageSize: 100 })
    projects.value = response.items || []
  } catch (error: any) {
    console.error('加载项目列表失败:', error)
  }
}

const handleSave = async () => {
  if (!formRef.value || !form.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    await RequirementService.updateRequirementDocument(requirementId.value, {
      title: form.value.title,
      content: form.value.content,
      status: form.value.status,
      functionalRequirements: form.value.functionalRequirements,
      nonFunctionalRequirements: form.value.nonFunctionalRequirements,
      userStories: form.value.userStories,
      acceptanceCriteria: form.value.acceptanceCriteria
    })

    ElMessage.success('需求文档更新成功')
    router.push(`/requirements/documents/${requirementId.value}`)
  } catch (error: any) {
    console.error('保存需求失败:', error)
    ElMessage.error('保存需求失败')
  } finally {
    saving.value = false
  }
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消编辑吗？未保存的更改将丢失。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning'
      }
    )
    router.back()
  } catch (error) {
    // 用户取消
  }
}

// AI生成方法（暂时使用模拟数据）
const generateFunctionalRequirements = async () => {
  if (!form.value?.content) {
    ElMessage.warning('请先填写需求描述')
    return
  }

  try {
    generatingFunctional.value = true
    // TODO: 调用AI服务生成功能性需求
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
    form.value.functionalRequirements = `基于需求描述生成的功能性需求：
1. 用户管理功能
2. 数据录入功能
3. 查询统计功能
4. 权限控制功能`
    ElMessage.success('功能性需求生成成功')
  } catch (error: any) {
    console.error('生成功能性需求失败:', error)
    ElMessage.error('生成功能性需求失败')
  } finally {
    generatingFunctional.value = false
  }
}

const generateNonFunctionalRequirements = async () => {
  if (!form.value?.content) {
    ElMessage.warning('请先填写需求描述')
    return
  }

  try {
    generatingNonFunctional.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    form.value.nonFunctionalRequirements = `基于需求描述生成的非功能性需求：
1. 性能要求：响应时间不超过2秒
2. 安全要求：数据加密传输
3. 可用性要求：系统可用性99.9%
4. 兼容性要求：支持主流浏览器`
    ElMessage.success('非功能性需求生成成功')
  } catch (error: any) {
    console.error('生成非功能性需求失败:', error)
    ElMessage.error('生成非功能性需求失败')
  } finally {
    generatingNonFunctional.value = false
  }
}

const generateUserStories = async () => {
  if (!form.value?.content) {
    ElMessage.warning('请先填写需求描述')
    return
  }

  try {
    generatingUserStories.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    form.value.userStories = `基于需求描述生成的用户故事：
作为一个用户，我希望能够注册账号，以便使用系统功能。
作为一个管理员，我希望能够管理用户权限，以便控制系统访问。
作为一个操作员，我希望能够录入数据，以便完成日常工作。`
    ElMessage.success('用户故事生成成功')
  } catch (error: any) {
    console.error('生成用户故事失败:', error)
    ElMessage.error('生成用户故事失败')
  } finally {
    generatingUserStories.value = false
  }
}

const generateAcceptanceCriteria = async () => {
  if (!form.value?.content) {
    ElMessage.warning('请先填写需求描述')
    return
  }

  try {
    generatingAcceptance.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    form.value.acceptanceCriteria = `基于需求描述生成的验收标准：
1. 用户能够成功注册并登录系统
2. 系统能够正确验证用户权限
3. 数据录入功能正常工作
4. 查询结果准确无误
5. 系统响应时间符合性能要求`
    ElMessage.success('验收标准生成成功')
  } catch (error: any) {
    console.error('生成验收标准失败:', error)
    ElMessage.error('生成验收标准失败')
  } finally {
    generatingAcceptance.value = false
  }
}

// 生命周期
onMounted(() => {
  loadProjects()
  loadRequirement()
})
</script>

<style lang="scss" scoped>
.requirement-edit {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-left {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }

      .page-subtitle {
        color: var(--el-text-color-secondary);
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.loading-container {
  padding: 20px;
}

.edit-form {
  .form-card {
    margin-bottom: 24px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
  }
}

.error-container {
  padding: 60px 20px;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .requirement-edit {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;

    .header-left {
      flex-direction: column;
      gap: 8px;
    }

    .header-actions {
      align-self: stretch;

      .el-button {
        flex: 1;
      }
    }
  }
}
</style>
