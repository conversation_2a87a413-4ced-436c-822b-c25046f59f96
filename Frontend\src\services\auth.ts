import { ApiService } from './api'
import type { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  User 
} from '@/types'

export class AuthService {
  /**
   * 用户登录
   */
  static async login(request: LoginRequest): Promise<LoginResponse> {
    return ApiService.post<LoginResponse>('/api/auth/login', request)
  }

  /**
   * 用户注册
   */
  static async register(request: RegisterRequest): Promise<void> {
    return ApiService.post<void>('/api/auth/register', request)
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    return ApiService.post<void>('/api/auth/logout')
  }

  /**
   * 刷新Token
   */
  static async refreshToken(refreshToken: string): Promise<LoginResponse> {
    return ApiService.post<LoginResponse>('/api/auth/refresh', { refreshToken })
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<User> {
    return ApiService.get<User>('/api/auth/me')
  }

  /**
   * 更新用户信息
   */
  static async updateProfile(user: Partial<User>): Promise<User> {
    return ApiService.put<User>('/api/auth/profile', user)
  }

  /**
   * 修改密码
   */
  static async changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<void> {
    return ApiService.post<void>('/api/auth/change-password', data)
  }

  /**
   * 发送邮箱验证
   */
  static async sendEmailVerification(): Promise<void> {
    return ApiService.post<void>('/api/auth/send-email-verification')
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<void> {
    return ApiService.post<void>('/api/auth/verify-email', { token })
  }

  /**
   * 发送密码重置邮件
   */
  static async sendPasswordReset(email: string): Promise<void> {
    return ApiService.post<void>('/api/auth/send-password-reset', { email })
  }

  /**
   * 重置密码
   */
  static async resetPassword(data: {
    token: string
    password: string
    confirmPassword: string
  }): Promise<void> {
    return ApiService.post<void>('/api/auth/reset-password', data)
  }

  /**
   * 启用双因子认证
   */
  static async enableTwoFactor(): Promise<{ qrCode: string; secret: string }> {
    return ApiService.post<{ qrCode: string; secret: string }>('/api/auth/enable-2fa')
  }

  /**
   * 验证双因子认证
   */
  static async verifyTwoFactor(code: string): Promise<void> {
    return ApiService.post<void>('/api/auth/verify-2fa', { code })
  }

  /**
   * 禁用双因子认证
   */
  static async disableTwoFactor(code: string): Promise<void> {
    return ApiService.post<void>('/api/auth/disable-2fa', { code })
  }
}
