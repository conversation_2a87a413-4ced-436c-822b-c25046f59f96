-- 为Playwright表添加缺失的Version和Remarks列
-- 执行时间：2024-12-19

-- 1. 为PlaywrightScripts表添加缺失的列
IF EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightScripts' AND xtype='U')
BEGIN
    -- 检查并添加Version列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightScripts') AND name = 'Version')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightScripts] ADD [Version] int NOT NULL DEFAULT 1;
        PRINT 'PlaywrightScripts表添加Version列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightScripts表Version列已存在';
    END

    -- 检查并添加Remarks列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightScripts') AND name = 'Remarks')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightScripts] ADD [Remarks] nvarchar(500) NULL;
        PRINT 'PlaywrightScripts表添加Remarks列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightScripts表Remarks列已存在';
    END
END

-- 2. 为PlaywrightExecutions表添加缺失的列
IF EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightExecutions' AND xtype='U')
BEGIN
    -- 检查并添加Version列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightExecutions') AND name = 'Version')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightExecutions] ADD [Version] int NOT NULL DEFAULT 1;
        PRINT 'PlaywrightExecutions表添加Version列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightExecutions表Version列已存在';
    END

    -- 检查并添加Remarks列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightExecutions') AND name = 'Remarks')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightExecutions] ADD [Remarks] nvarchar(500) NULL;
        PRINT 'PlaywrightExecutions表添加Remarks列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightExecutions表Remarks列已存在';
    END
END

-- 3. 为PlaywrightExecutionLogs表添加缺失的列
IF EXISTS (SELECT * FROM sysobjects WHERE name='PlaywrightExecutionLogs' AND xtype='U')
BEGIN
    -- 检查并添加Version列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightExecutionLogs') AND name = 'Version')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightExecutionLogs] ADD [Version] int NOT NULL DEFAULT 1;
        PRINT 'PlaywrightExecutionLogs表添加Version列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightExecutionLogs表Version列已存在';
    END

    -- 检查并添加Remarks列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlaywrightExecutionLogs') AND name = 'Remarks')
    BEGIN
        ALTER TABLE [dbo].[PlaywrightExecutionLogs] ADD [Remarks] nvarchar(500) NULL;
        PRINT 'PlaywrightExecutionLogs表添加Remarks列成功';
    END
    ELSE
    BEGIN
        PRINT 'PlaywrightExecutionLogs表Remarks列已存在';
    END
END

PRINT '所有缺失列添加完成！';
