graph TD
    A["用户需求输入<br/>阶段: 需求分析<br/>工具: Web界面/移动端APP<br/>AI参与: 无<br/>1.收集业务需求<br/>2.明确项目目标<br/>3.整理功能清单"] --> B["AI需求分析 🤖<br/>阶段: 需求分析<br/>工具: LLM大模型<br/>AI参与: 核心AI功能<br/>1.自然语言处理<br/>2.需求理解与分类<br/>3.可行性评估"]
    B --> C["生成需求规格书 🤖<br/>阶段: 规格确认<br/>工具: LLM大模型 + Word模板<br/>AI参与: 核心AI功能<br/>1.结构化需求文档<br/>2.功能性需求定义<br/>3.非功能性需求规范<br/>4.用户界面设计要求"]
    C --> D["生成ER图 🤖<br/>阶段: 规格确认<br/>工具: PlantUML + AI生成<br/>AI参与: 核心AI功能<br/>1.数据库实体设计<br/>2.实体关系建模<br/>3.表结构定义<br/>4.主外键约束设计"]
    C --> E["生成Context图 🤖<br/>阶段: 规格确认<br/>工具: Mermaid + AI生成<br/>AI参与: 核心AI功能<br/>1.系统架构设计<br/>2.组件交互关系<br/>3.外部系统接口<br/>4.数据流向分析"]
    D --> F["代码生成 🤖<br/>阶段: 程式开发<br/>工具: GitHub Copilot + 自定义模板<br/>AI参与: 核心AI功能<br/>1.前端Vue组件开发<br/>2.后端C# API实现<br/>3.SQL Server数据库脚本<br/>4.配置文件生成"]
    E --> F
    F --> G["自动化测试 🤖<br/>阶段: IT验收与测试<br/>工具: Jest + MSTest + Selenium<br/>AI参与: 辅助AI功能<br/>1.单元测试执行<br/>2.集成测试验证<br/>3.UI自动化测试<br/>4.测试报告生成"]
    G --> G1["UAT测试<br/>阶段: UAT测试<br/>工具: 测试管理平台 + 用户反馈<br/>AI参与: 无<br/>1.用户验收测试<br/>2.业务流程验证<br/>3.用户体验测试<br/>4.验收报告确认"]
    G1 --> H["自动部署<br/>阶段: 上线前准备<br/>工具: Azure DevOps + Docker<br/>AI参与: 无<br/>1.CI/CD流水线<br/>2.应用打包发布<br/>3.环境配置部署<br/>4.部署验证检查"]
    H --> I["智能运维 🤖<br/>阶段: Go-Live Support<br/>工具: Azure Monitor + Prometheus<br/>AI参与: 辅助AI功能<br/>1.系统监控告警<br/>2.性能指标分析<br/>3.自动扩缩容<br/>4.安全漏洞扫描"]
    I --> J["问题处理 🤖<br/>阶段: Go-Live Support<br/>工具: Azure AI + PagerDuty<br/>AI参与: 核心AI功能<br/>1.故障自动检测<br/>2.智能故障诊断<br/>3.自动修复机制<br/>4.人工介入升级"]
    J --> K["持续优化 🤖<br/>阶段: Go-Live Support<br/>工具: Application Insights + AI分析<br/>AI参与: 核心AI功能<br/>1.性能数据分析<br/>2.用户反馈收集<br/>3.代码重构建议<br/>4.系统升级规划"]
    K --> L["项目完成<br/>阶段: Go-Live Support<br/>工具: Azure DevOps + SharePoint<br/>AI参与: 无<br/>1.交付验收确认<br/>2.文档归档整理<br/>3.团队经验总结<br/>4.后续维护计划"]

    %% 样式定义
    classDef inputStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef aiStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef docStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef codeStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef testStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef deployStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef opsStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    %% 应用样式
    class A inputStyle
    class B aiStyle
    class C,D,E docStyle
    class F codeStyle
    class G testStyle
    class H deployStyle
    class I,J,K,L opsStyle
