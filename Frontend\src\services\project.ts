import { ApiService } from './api'
import type {
  Project,
  ProjectSummary,
  CreateProjectRequest,
  UpdateProjectRequest,
  UpdateProjectStatusRequest,
  ProjectStatistics,
  PagedResult
} from '@/types'

// 重新导出类型
export type { Project, ProjectSummary }

export class ProjectService {
  /**
   * 获取项目列表（分页）
   */
  static async getProjects(params: {
    pageNumber?: number
    pageSize?: number
    status?: string
    search?: string
  } = {}): Promise<PagedResult<ProjectSummary>> {
    const queryParams = new URLSearchParams()

    if (params.pageNumber) queryParams.append('pageNumber', params.pageNumber.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params.status) queryParams.append('status', params.status)
    if (params.search) queryParams.append('search', params.search)

    const url = `/api/projects${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    return ApiService.get<PagedResult<ProjectSummary>>(url)
  }

  /**
   * 获取所有项目的简单列表（用于下拉选择等）
   */
  static async getAllProjects(): Promise<ProjectSummary[]> {
    const result = await this.getProjects({ pageSize: 1000 }) // 获取大量项目
    return result.items
  }

  /**
   * 根据ID获取项目详情
   */
  static async getProject(id: number): Promise<Project> {
    return ApiService.get<Project>(`/api/projects/${id}`)
  }

  /**
   * 创建新项目
   */
  static async createProject(request: CreateProjectRequest): Promise<Project> {
    return ApiService.post<Project>('/api/projects', request)
  }

  /**
   * 更新项目信息
   */
  static async updateProject(id: number, request: UpdateProjectRequest): Promise<Project> {
    return ApiService.put<Project>(`/api/projects/${id}`, request)
  }

  /**
   * 删除项目
   */
  static async deleteProject(id: number): Promise<void> {
    return ApiService.delete<void>(`/api/projects/${id}`)
  }

  /**
   * 更新项目状态
   */
  static async updateProjectStatus(id: number, request: UpdateProjectStatusRequest): Promise<void> {
    return ApiService.patch<void>(`/api/projects/${id}/status`, request)
  }

  /**
   * 获取项目统计信息
   */
  static async getProjectStatistics(): Promise<ProjectStatistics> {
    return ApiService.get<ProjectStatistics>('/api/projects/statistics')
  }

  /**
   * 获取项目状态选项
   */
  static getStatusOptions(): Array<{ label: string; value: string; color: string }> {
    return [
      { label: '规划中', value: 'Planning', color: '#909399' },
      { label: '进行中', value: 'InProgress', color: '#409EFF' },
      { label: '测试中', value: 'Testing', color: '#E6A23C' },
      { label: '已部署', value: 'Deployed', color: '#67C23A' },
      { label: '已完成', value: 'Completed', color: '#67C23A' },
      { label: '已暂停', value: 'Paused', color: '#F56C6C' },
      { label: '已取消', value: 'Cancelled', color: '#909399' }
    ]
  }

  /**
   * 获取优先级选项
   */
  static getPriorityOptions(): Array<{ label: string; value: string; color: string }> {
    return [
      { label: '低', value: 'Low', color: '#67C23A' },
      { label: '中', value: 'Medium', color: '#E6A23C' },
      { label: '高', value: 'High', color: '#F56C6C' },
      { label: '紧急', value: 'Critical', color: '#F56C6C' }
    ]
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: string): string {
    const option = this.getStatusOptions().find(opt => opt.value === status)
    return option?.color || '#909399'
  }

  /**
   * 获取优先级颜色
   */
  static getPriorityColor(priority: string): string {
    const option = this.getPriorityOptions().find(opt => opt.value === priority)
    return option?.color || '#909399'
  }

  /**
   * 获取状态标签
   */
  static getStatusLabel(status: string): string {
    const option = this.getStatusOptions().find(opt => opt.value === status)
    return option?.label || status
  }

  /**
   * 获取优先级标签
   */
  static getPriorityLabel(priority: string): string {
    const option = this.getPriorityOptions().find(opt => opt.value === priority)
    return option?.label || priority
  }

  /**
   * 下载项目需求文档
   */
  static async downloadProjectRequirements(projectId: number): Promise<void> {
    return ApiService.download(`/api/projects/${projectId}/download-requirements`,
      `project-${projectId}-requirements.md`)
  }

  /**
   * 下载项目ER图
   */
  static async downloadProjectERDiagrams(projectId: number): Promise<void> {
    return ApiService.download(`/api/projects/${projectId}/download-er-diagrams`,
      `project-${projectId}-er-diagrams.mermaid`)
  }

  /**
   * 下载项目上下文图
   */
  static async downloadProjectContextDiagrams(projectId: number): Promise<void> {
    return ApiService.download(`/api/projects/${projectId}/download-context-diagrams`,
      `project-${projectId}-context-diagrams.mermaid`)
  }

  /**
   * 下载项目原型图
   */
  static async downloadProjectPrototypes(projectId: number): Promise<void> {
    return ApiService.download(`/api/projects/${projectId}/download-prototypes`,
      `project-${projectId}-prototypes.mermaid`)
  }
}

// 导出类和静态方法实例
export const projectService = ProjectService
