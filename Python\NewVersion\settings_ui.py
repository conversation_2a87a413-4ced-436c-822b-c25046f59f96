#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置UI
系统设置和配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from pathlib import Path
from typing import Dict, Any


class SettingsUI:
    """设置UI类"""

    def __init__(self, parent, api_client):
        """
        初始化设置UI

        Args:
            parent: 父容器
            api_client: API客户端
        """
        self.parent = parent
        self.api_client = api_client

        # 配置数据
        self.config = self.load_config()

        # 创建界面
        self.create_interface()

        # 加载当前设置
        self.load_current_settings()

    def create_interface(self):
        """创建界面"""
        # 主容器 - 减少内边距以充分利用空间
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(2, 5))

        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # API设置选项卡
        self.create_api_settings_tab()

        # 自动化设置选项卡 (隐藏)
        # self.create_automation_settings_tab()

        # 界面设置选项卡 (隐藏)
        # self.create_ui_settings_tab()

        # 高级设置选项卡
        self.create_advanced_settings_tab()

        # 底部按钮
        self.create_buttons(main_frame)

    def create_api_settings_tab(self):
        """创建API设置选项卡"""
        api_frame = ttk.Frame(self.notebook)
        self.notebook.add(api_frame, text="🌐 API设置")

        # API连接设置
        connection_frame = ttk.LabelFrame(api_frame, text="API连接设置", padding="10")
        connection_frame.pack(fill=tk.X, padx=10, pady=10)

        # 服务器地址
        ttk.Label(connection_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.api_url_var = tk.StringVar(value=self.config.get('api_url', 'https://localhost:61136'))
        ttk.Entry(connection_frame, textvariable=self.api_url_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)

        # 超时设置
        ttk.Label(connection_frame, text="超时时间(秒):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_timeout_var = tk.StringVar(value=str(self.config.get('api_timeout', 30)))
        ttk.Entry(connection_frame, textvariable=self.api_timeout_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 重试次数
        ttk.Label(connection_frame, text="重试次数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.api_retries_var = tk.StringVar(value=str(self.config.get('api_retries', 3)))
        ttk.Entry(connection_frame, textvariable=self.api_retries_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 测试连接按钮
        ttk.Button(connection_frame, text="测试连接", command=self.test_api_connection).grid(row=3, column=0, columnspan=2, pady=10)

        # 配置列权重
        connection_frame.columnconfigure(1, weight=1)

    def create_automation_settings_tab(self):
        """创建自动化设置选项卡"""
        auto_frame = ttk.Frame(self.notebook)
        self.notebook.add(auto_frame, text="🤖 自动化设置")

        # 执行设置
        exec_frame = ttk.LabelFrame(auto_frame, text="执行设置", padding="10")
        exec_frame.pack(fill=tk.X, padx=10, pady=10)

        # 默认超时时间
        ttk.Label(exec_frame, text="默认超时时间(秒):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.default_timeout_var = tk.StringVar(value=str(self.config.get('default_timeout', 5)))
        ttk.Entry(exec_frame, textvariable=self.default_timeout_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 默认重试次数
        ttk.Label(exec_frame, text="默认重试次数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.default_retries_var = tk.StringVar(value=str(self.config.get('default_retries', 3)))
        ttk.Entry(exec_frame, textvariable=self.default_retries_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 步骤间延迟
        ttk.Label(exec_frame, text="步骤间延迟(秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.step_delay_var = tk.StringVar(value=str(self.config.get('step_delay', 0.5)))
        ttk.Entry(exec_frame, textvariable=self.step_delay_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 安全模式
        self.safe_mode_var = tk.BooleanVar(value=self.config.get('safe_mode', True))
        ttk.Checkbutton(exec_frame, text="启用安全模式", variable=self.safe_mode_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 截图设置
        screenshot_frame = ttk.LabelFrame(auto_frame, text="截图设置", padding="10")
        screenshot_frame.pack(fill=tk.X, padx=10, pady=10)

        # 自动截图
        self.auto_screenshot_var = tk.BooleanVar(value=self.config.get('auto_screenshot', True))
        ttk.Checkbutton(screenshot_frame, text="执行时自动截图", variable=self.auto_screenshot_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 截图保存路径
        ttk.Label(screenshot_frame, text="截图保存路径:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.screenshot_path_var = tk.StringVar(value=self.config.get('screenshot_path', './screenshots'))
        ttk.Entry(screenshot_frame, textvariable=self.screenshot_path_var, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)
        ttk.Button(screenshot_frame, text="浏览", command=self.browse_screenshot_path).grid(row=1, column=2, padx=(5, 0), pady=5)

        # 配置列权重
        exec_frame.columnconfigure(1, weight=1)
        screenshot_frame.columnconfigure(1, weight=1)

    def create_ui_settings_tab(self):
        """创建界面设置选项卡"""
        ui_frame = ttk.Frame(self.notebook)
        self.notebook.add(ui_frame, text="🎨 界面设置")

        # 外观设置
        appearance_frame = ttk.LabelFrame(ui_frame, text="外观设置", padding="10")
        appearance_frame.pack(fill=tk.X, padx=10, pady=10)

        # 主题
        ttk.Label(appearance_frame, text="主题:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.theme_var = tk.StringVar(value=self.config.get('theme', 'clam'))
        theme_combo = ttk.Combobox(appearance_frame, textvariable=self.theme_var, state='readonly')
        theme_combo['values'] = ('default', 'clam', 'alt', 'classic')
        theme_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 字体大小
        ttk.Label(appearance_frame, text="字体大小:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.font_size_var = tk.StringVar(value=str(self.config.get('font_size', 10)))
        font_size_combo = ttk.Combobox(appearance_frame, textvariable=self.font_size_var, state='readonly')
        font_size_combo['values'] = ('8', '9', '10', '11', '12', '14', '16')
        font_size_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 行为设置
        behavior_frame = ttk.LabelFrame(ui_frame, text="行为设置", padding="10")
        behavior_frame.pack(fill=tk.X, padx=10, pady=10)

        # 启动时检查更新
        self.check_updates_var = tk.BooleanVar(value=self.config.get('check_updates', True))
        ttk.Checkbutton(behavior_frame, text="启动时检查更新", variable=self.check_updates_var).grid(row=0, column=0, sticky=tk.W, pady=5)

        # 最小化到系统托盘
        self.minimize_to_tray_var = tk.BooleanVar(value=self.config.get('minimize_to_tray', False))
        ttk.Checkbutton(behavior_frame, text="最小化到系统托盘", variable=self.minimize_to_tray_var).grid(row=1, column=0, sticky=tk.W, pady=5)

        # 自动保存配置
        self.auto_save_config_var = tk.BooleanVar(value=self.config.get('auto_save_config', True))
        ttk.Checkbutton(behavior_frame, text="自动保存配置", variable=self.auto_save_config_var).grid(row=2, column=0, sticky=tk.W, pady=5)

    def create_advanced_settings_tab(self):
        """创建高级设置选项卡"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="⚙️ 高级设置")

        # 直接创建主要内容区域，不使用滚动容器
        main_content_frame = ttk.Frame(advanced_frame)
        main_content_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=(2, 8))

        # 左右分栏布局
        left_column = ttk.Frame(main_content_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 4))

        right_column = ttk.Frame(main_content_frame)
        right_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(4, 0))

        # 日志设置 - 放在左栏
        log_frame = ttk.LabelFrame(left_column, text="日志设置", padding="8")
        log_frame.pack(fill=tk.X, pady=(0, 8))

        # 日志级别
        ttk.Label(log_frame, text="日志级别:").grid(row=0, column=0, sticky=tk.W, pady=3)
        self.log_level_var = tk.StringVar(value=self.config.get('log_level', 'INFO'))
        log_level_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var, state='readonly')
        log_level_combo['values'] = ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        log_level_combo.grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=3)

        # 日志文件路径
        ttk.Label(log_frame, text="日志文件路径:").grid(row=1, column=0, sticky=tk.W, pady=3)
        self.log_path_var = tk.StringVar(value=self.config.get('log_path', './logs'))
        ttk.Entry(log_frame, textvariable=self.log_path_var).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=3)
        ttk.Button(log_frame, text="浏览", command=self.browse_log_path).grid(row=1, column=2, padx=(5, 0), pady=3)

        # 性能设置 - 放在右栏
        performance_frame = ttk.LabelFrame(right_column, text="性能设置", padding="8")
        performance_frame.pack(fill=tk.X, pady=(0, 8))

        # 最大并发数
        ttk.Label(performance_frame, text="最大并发数:").grid(row=0, column=0, sticky=tk.W, pady=3)
        self.max_concurrent_var = tk.StringVar(value=str(self.config.get('max_concurrent', 1)))
        ttk.Entry(performance_frame, textvariable=self.max_concurrent_var, width=15).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=3)

        # 缓存大小
        ttk.Label(performance_frame, text="缓存大小(MB):").grid(row=1, column=0, sticky=tk.W, pady=3)
        self.cache_size_var = tk.StringVar(value=str(self.config.get('cache_size', 100)))
        ttk.Entry(performance_frame, textvariable=self.cache_size_var, width=15).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=3)

        # 文件下载设置 - 跨越两栏
        download_frame = ttk.LabelFrame(main_content_frame, text="文件下载设置", padding="8")
        download_frame.pack(fill=tk.X, pady=8)

        # 下载目录
        ttk.Label(download_frame, text="下载目录:").grid(row=0, column=0, sticky=tk.W, pady=3)
        self.download_path_var = tk.StringVar(value=self.config.get('download_path', ''))
        ttk.Entry(download_frame, textvariable=self.download_path_var).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=3)
        ttk.Button(download_frame, text="浏览", command=self.browse_download_path).grid(row=0, column=2, padx=(5, 0), pady=3)

        # 自定义Prompt设置 - 跨越两栏，占用更多空间
        prompt_frame = ttk.LabelFrame(main_content_frame, text="自定义Prompt设置", padding="8")
        prompt_frame.pack(fill=tk.BOTH, expand=True, pady=8)

        # 启用自定义Prompt
        self.enable_custom_prompt_var = tk.BooleanVar(value=self.config.get('enable_custom_prompt', False))
        ttk.Checkbutton(prompt_frame, text="启用自定义Prompt（执行步骤时追加到原有prompt后）",
                       variable=self.enable_custom_prompt_var).pack(anchor=tk.W, pady=3)

        # 自定义Prompt内容
        ttk.Label(prompt_frame, text="自定义Prompt内容:").pack(anchor=tk.W, pady=(8, 3))

        # 创建文本框和滚动条 - 充分利用可用空间
        text_frame = ttk.Frame(prompt_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=3)

        self.custom_prompt_text = tk.Text(text_frame, wrap=tk.WORD)  # 移除固定高度，让它自适应
        text_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.custom_prompt_text.yview)
        self.custom_prompt_text.configure(yscrollcommand=text_scrollbar.set)

        self.custom_prompt_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置默认内容
        default_prompt = self.config.get('custom_prompt', '')
        if default_prompt:
            self.custom_prompt_text.insert(tk.END, default_prompt)

        # 配置列权重以实现响应式布局
        log_frame.columnconfigure(1, weight=1)
        performance_frame.columnconfigure(1, weight=1)
        download_frame.columnconfigure(1, weight=1)

        # 配置主要内容区域的列权重
        main_content_frame.columnconfigure(0, weight=1)
        main_content_frame.columnconfigure(1, weight=1)

    def create_buttons(self, parent):
        """创建底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(8, 0))

        # 右侧按钮
        ttk.Button(button_frame, text="恢复默认", command=self.restore_defaults).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel_changes).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="应用", command=self.apply_settings).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="确定", command=self.save_and_close).pack(side=tk.RIGHT, padx=(5, 0))

        # 左侧按钮
        ttk.Button(button_frame, text="导入配置", command=self.import_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出配置", command=self.export_config).pack(side=tk.LEFT)

    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_path = Path(__file__).parent / "config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置失败: {e}")

        # 返回默认配置
        return {
            'api_url': 'https://localhost:61136',
            'api_timeout': 30,
            'api_retries': 3,
            'default_timeout': 5,
            'default_retries': 3,
            'step_delay': 0.5,
            'safe_mode': True,
            'auto_screenshot': True,
            'screenshot_path': './screenshots',
            'theme': 'clam',
            'font_size': 10,
            'check_updates': True,
            'minimize_to_tray': False,
            'auto_save_config': True,
            'log_level': 'INFO',
            'log_path': './logs',
            'max_concurrent': 1,
            'cache_size': 100,
            'download_path': '',
            'enable_custom_prompt': False,
            'custom_prompt': ''
        }

    def save_config(self):
        """保存配置"""
        try:
            config_path = Path(__file__).parent / "config.json"

            # 先读取现有配置
            existing_config = {}
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        existing_config = json.load(f)
                except Exception as e:
                    print(f"读取现有配置失败: {e}")

            # 获取当前UI的配置
            current_config = self.get_current_config()

            # 保留重要的配置项
            preserved_keys = ['clear_old_files', 'download_apis', 'element_detection']
            preserved_config = {}
            for key in preserved_keys:
                if key in existing_config:
                    preserved_config[key] = existing_config[key]
                    print(f"🔒 保留配置项: {key}")

            # 用当前UI配置更新现有配置
            existing_config.update(current_config)

            # 恢复保留的配置项
            existing_config.update(preserved_config)

            print(f"💾 保存配置，包含 {len(existing_config)} 个配置项")

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
            return False

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        config = {
            'api_url': self.api_url_var.get(),
            'api_timeout': int(self.api_timeout_var.get()),
            'api_retries': int(self.api_retries_var.get()),
            'log_level': self.log_level_var.get(),
            'log_path': self.log_path_var.get(),
            'max_concurrent': int(self.max_concurrent_var.get()),
            'cache_size': int(self.cache_size_var.get()),
            'download_path': self.download_path_var.get(),
            'enable_custom_prompt': self.enable_custom_prompt_var.get(),
            'custom_prompt': self.custom_prompt_text.get('1.0', tk.END).strip()
        }

        # 只有在变量存在时才添加这些配置（避免隐藏选项卡的变量不存在错误）
        if hasattr(self, 'default_timeout_var'):
            config.update({
                'default_timeout': float(self.default_timeout_var.get()),
                'default_retries': int(self.default_retries_var.get()),
                'step_delay': float(self.step_delay_var.get()),
                'safe_mode': self.safe_mode_var.get(),
                'auto_screenshot': self.auto_screenshot_var.get(),
                'screenshot_path': self.screenshot_path_var.get(),
            })

        if hasattr(self, 'theme_var'):
            config.update({
                'theme': self.theme_var.get(),
                'font_size': int(self.font_size_var.get()),
                'check_updates': self.check_updates_var.get(),
                'minimize_to_tray': self.minimize_to_tray_var.get(),
                'auto_save_config': self.auto_save_config_var.get(),
            })

        return config

    def browse_download_path(self):
        """浏览下载目录"""
        directory = filedialog.askdirectory(
            title="选择下载目录",
            initialdir=self.download_path_var.get() if self.download_path_var.get() else str(Path.home())
        )
        if directory:
            self.download_path_var.set(directory)

    def load_current_settings(self):
        """加载当前设置到界面"""
        # 重新加载配置
        self.config = self.load_config()

        # 更新API设置
        self.api_url_var.set(self.config.get('api_url', 'https://localhost:61136'))
        self.api_timeout_var.set(str(self.config.get('api_timeout', 30)))
        self.api_retries_var.set(str(self.config.get('api_retries', 3)))

        # 更新高级设置
        self.log_level_var.set(self.config.get('log_level', 'INFO'))
        self.log_path_var.set(self.config.get('log_path', './logs'))
        self.max_concurrent_var.set(str(self.config.get('max_concurrent', 1)))
        self.cache_size_var.set(str(self.config.get('cache_size', 100)))
        self.download_path_var.set(self.config.get('download_path', ''))

        # 更新自定义Prompt设置
        self.enable_custom_prompt_var.set(self.config.get('enable_custom_prompt', False))

        # 清空并重新设置自定义prompt内容
        self.custom_prompt_text.delete('1.0', tk.END)
        custom_prompt = self.config.get('custom_prompt', '')
        if custom_prompt:
            self.custom_prompt_text.insert('1.0', custom_prompt)

        # 更新其他设置（如果变量存在）
        if hasattr(self, 'default_timeout_var'):
            self.default_timeout_var.set(str(self.config.get('default_timeout', 5)))
            self.default_retries_var.set(str(self.config.get('default_retries', 3)))
            self.step_delay_var.set(str(self.config.get('step_delay', 0.5)))
            self.safe_mode_var.set(self.config.get('safe_mode', True))
            self.auto_screenshot_var.set(self.config.get('auto_screenshot', True))
            self.screenshot_path_var.set(self.config.get('screenshot_path', './screenshots'))

        if hasattr(self, 'theme_var'):
            self.theme_var.set(self.config.get('theme', 'clam'))
            self.font_size_var.set(str(self.config.get('font_size', 10)))
            self.check_updates_var.set(self.config.get('check_updates', True))
            self.minimize_to_tray_var.set(self.config.get('minimize_to_tray', False))
            self.auto_save_config_var.set(self.config.get('auto_save_config', True))

    def test_api_connection(self):
        """测试API连接"""
        try:
            # 临时更新API客户端配置
            old_url = self.api_client.base_url
            old_timeout = self.api_client.timeout

            self.api_client.base_url = self.api_url_var.get().rstrip('/')
            self.api_client.timeout = int(self.api_timeout_var.get())

            # 测试连接
            if self.api_client.test_connection():
                messagebox.showinfo("连接成功", "API连接测试成功！")
            else:
                messagebox.showerror("连接失败", "API连接测试失败，请检查服务器地址和网络连接。")

            # 恢复原配置
            self.api_client.base_url = old_url
            self.api_client.timeout = old_timeout

        except Exception as e:
            messagebox.showerror("连接错误", f"连接测试时发生错误: {e}")



    def browse_screenshot_path(self):
        """浏览截图路径"""
        path = filedialog.askdirectory(title="选择截图保存路径")
        if path:
            self.screenshot_path_var.set(path)

    def browse_log_path(self):
        """浏览日志路径"""
        path = filedialog.askdirectory(title="选择日志保存路径")
        if path:
            self.log_path_var.set(path)

    def apply_settings(self):
        """应用设置"""
        if self.save_config():
            messagebox.showinfo("成功", "设置已保存并应用！")

    def save_and_close(self):
        """保存并关闭"""
        if self.save_config():
            messagebox.showinfo("成功", "设置已保存！")

    def cancel_changes(self):
        """取消更改"""
        # 重新加载配置
        self.config = self.load_config()
        self.load_current_settings()
        messagebox.showinfo("取消", "已取消更改！")

    def restore_defaults(self):
        """恢复默认设置"""
        if messagebox.askyesno("确认", "确定要恢复默认设置吗？这将清除所有自定义配置。"):
            # 删除配置文件
            try:
                config_path = Path(__file__).parent / "config.json"
                if config_path.exists():
                    config_path.unlink()

                # 重新加载默认配置
                self.config = self.load_config()
                self.load_current_settings()

                messagebox.showinfo("成功", "已恢复默认设置！")
            except Exception as e:
                messagebox.showerror("错误", f"恢复默认设置失败: {e}")

    def import_config(self):
        """导入配置"""
        file_path = filedialog.askopenfilename(
            title="导入配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)

                # 更新配置
                self.config.update(imported_config)
                self.load_current_settings()

                messagebox.showinfo("成功", "配置导入成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导入配置失败: {e}")

    def export_config(self):
        """导出配置"""
        file_path = filedialog.asksaveasfilename(
            title="导出配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                config = self.get_current_config()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", "配置导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导出配置失败: {e}")

    def get_custom_prompt_settings(self) -> Dict[str, Any]:
        """获取自定义Prompt设置"""
        return {
            'enabled': self.enable_custom_prompt_var.get(),
            'content': self.custom_prompt_text.get('1.0', tk.END).strip()
        }

    def is_custom_prompt_enabled(self) -> bool:
        """检查是否启用了自定义Prompt"""
        return self.enable_custom_prompt_var.get()

    def get_custom_prompt_content(self) -> str:
        """获取自定义Prompt内容"""
        if self.is_custom_prompt_enabled():
            return self.custom_prompt_text.get('1.0', tk.END).strip()
        return ""
