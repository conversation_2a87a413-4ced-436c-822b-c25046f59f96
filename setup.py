#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python UI自动化编码系统 - 安装和设置脚本

用于初始化系统环境和配置。
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        if not Path("requirements.txt").exists():
            print("❌ 错误: requirements.txt文件不存在")
            return False
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖时出错: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "data",
        "logs",
        "logs/screenshots",
        "generated_code",
        "templates/vscode",
        "templates/augment",
        "templates/copilot"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建目录时出错: {e}")
        return False


def check_vscode():
    """检查VSCode安装"""
    print("\n🔍 检查VSCode安装...")
    
    try:
        # 尝试运行code命令
        result = subprocess.run(["code", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_lines = result.stdout.strip().split('\n')
            if version_lines:
                print(f"✅ VSCode已安装: {version_lines[0]}")
                return True
        
        print("⚠️  警告: 未找到VSCode命令行工具")
        print("请确保VSCode已安装并添加到PATH环境变量")
        return False
        
    except subprocess.TimeoutExpired:
        print("⚠️  警告: VSCode命令超时")
        return False
    except FileNotFoundError:
        print("⚠️  警告: 未找到VSCode")
        print("请安装VSCode并确保'code'命令可用")
        return False
    except Exception as e:
        print(f"⚠️  检查VSCode时出错: {e}")
        return False


def initialize_database():
    """初始化数据库"""
    print("\n🗄️  初始化数据库...")
    
    try:
        # 添加src目录到Python路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.task_management.task_database import TaskDatabase
        from src.task_management.task_loader import TaskLoader
        
        # 创建数据库
        db = TaskDatabase("data/tasks.db")
        loader = TaskLoader(db)
        
        print("✅ 数据库初始化成功")
        
        # 创建示例任务
        task_count = loader.create_sample_tasks()
        print(f"✅ 创建了 {task_count} 个示例任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False


def check_config():
    """检查配置文件"""
    print("\n⚙️  检查配置文件...")
    
    try:
        if not Path("config.yaml").exists():
            print("❌ 错误: config.yaml文件不存在")
            return False
        
        # 添加src目录到Python路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.utils.config_manager import ConfigManager
        
        config_manager = ConfigManager("config.yaml")
        
        # 验证配置
        if config_manager.validate_config():
            print("✅ 配置文件验证通过")
            
            # 创建必要目录
            config_manager.ensure_directories()
            print("✅ 配置目录已创建")
            
            return True
        else:
            print("⚠️  警告: 配置文件验证失败")
            print("请检查config.yaml中的路径设置")
            return False
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False


def run_tests():
    """运行测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        result = subprocess.run([sys.executable, "test_automation.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
            return True
        else:
            print("⚠️  部分测试失败，请查看上面的详细信息")
            return False
            
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False


def main():
    """主安装函数"""
    print("🚀 Python UI自动化编码系统 - 安装向导")
    print("=" * 60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_dependencies),
        ("创建目录结构", create_directories),
        ("检查VSCode", check_vscode),
        ("检查配置文件", check_config),
        ("初始化数据库", initialize_database),
        ("运行系统测试", run_tests)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name} 未完全成功")
        except Exception as e:
            print(f"❌ {step_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 安装完成")
    print("=" * 60)
    
    print(f"✅ 成功完成: {success_count}/{len(steps)} 个步骤")
    
    if success_count >= len(steps) - 1:  # 允许一个步骤失败
        print("\n🎉 系统安装成功！")
        print("\n📖 使用指南:")
        print("1. 运行 'python main.py' 启动交互式界面")
        print("2. 运行 'python main.py --list' 查看任务列表")
        print("3. 运行 'python main.py --batch' 执行所有待处理任务")
        print("4. 运行 'python test_automation.py' 进行系统测试")
        
        print("\n⚠️  重要提醒:")
        print("- 首次使用前需要创建UI模板（参考templates/README.md）")
        print("- 确保VSCode已安装Augment或Copilot插件")
        print("- 根据实际环境调整config.yaml中的路径设置")
        
    else:
        print("\n❌ 安装过程中遇到问题")
        print("请检查错误信息并解决相关问题后重新运行安装脚本")
    
    print(f"\n📝 日志文件: logs/automation.log")
    print("如需帮助，请查看README.md文档")


if __name__ == "__main__":
    main()
