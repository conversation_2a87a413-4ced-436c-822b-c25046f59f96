<template>
  <el-dialog
    v-model="visible"
    :title="`配置 ${provider?.displayName || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="provider" class="provider-config">
      <!-- 基本信息 -->
      <div class="config-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="提供商名称">{{ provider.displayName }}</el-descriptions-item>
          <el-descriptions-item label="内部名称">{{ provider.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="provider.isEnabled ? 'success' : 'danger'">
              {{ provider.isEnabled ? '已启用' : '已禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="可用性">
            <el-tag :type="provider.isAvailable ? 'success' : 'danger'">
              {{ provider.isAvailable ? '可用' : '不可用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ provider.description }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 配置表单 -->
      <div class="config-section">
        <h3 class="section-title">配置参数</h3>
        <el-form
          ref="configFormRef"
          :model="configForm"
          :rules="configRules"
          label-width="120px"
        >
          <!-- 通用配置 -->
          <el-form-item label="启用状态" prop="enabled">
            <el-switch v-model="configForm.enabled" />
          </el-form-item>

          <!-- Azure OpenAI 特定配置 -->
          <template v-if="provider.name === 'Azure'">
            <el-form-item label="端点地址" prop="endpoint" required>
              <el-input
                v-model="configForm.endpoint"
                placeholder="https://your-resource.openai.azure.com/"
              />
            </el-form-item>
            <el-form-item label="API密钥" prop="apiKey" required>
              <el-input
                v-model="configForm.apiKey"
                type="password"
                show-password
                placeholder="输入Azure OpenAI API密钥"
              />
            </el-form-item>
            <el-form-item label="GPT-4部署名" prop="gpt4DeploymentName">
              <el-input v-model="configForm.gpt4DeploymentName" placeholder="gpt-4" />
            </el-form-item>
            <el-form-item label="GPT-3.5部署名" prop="gpt35DeploymentName">
              <el-input v-model="configForm.gpt35DeploymentName" placeholder="gpt-35-turbo" />
            </el-form-item>
            <el-form-item label="嵌入模型部署名" prop="embeddingDeploymentName">
              <el-input v-model="configForm.embeddingDeploymentName" placeholder="text-embedding-ada-002" />
            </el-form-item>
          </template>

          <!-- OpenAI 特定配置 -->
          <template v-else-if="provider.name === 'OpenAI'">
            <el-form-item label="端点地址" prop="endpoint">
              <el-input
                v-model="configForm.endpoint"
                placeholder="https://api.openai.com/v1/"
              />
            </el-form-item>
            <el-form-item label="API密钥" prop="apiKey" required>
              <el-input
                v-model="configForm.apiKey"
                type="password"
                show-password
                placeholder="输入OpenAI API密钥"
              />
            </el-form-item>
          </template>

          <!-- DeepSeek 特定配置 -->
          <template v-else-if="provider.name === 'DeepSeek'">
            <el-form-item label="端点地址" prop="endpoint">
              <el-input
                v-model="configForm.endpoint"
                placeholder="https://api.deepseek.com/v1/"
              />
            </el-form-item>
            <el-form-item label="API密钥" prop="apiKey" required>
              <el-input
                v-model="configForm.apiKey"
                type="password"
                show-password
                placeholder="输入DeepSeek API密钥"
              />
            </el-form-item>
          </template>

          <!-- Claude 特定配置 -->
          <template v-else-if="provider.name === 'Claude'">
            <el-form-item label="端点地址" prop="endpoint">
              <el-input
                v-model="configForm.endpoint"
                placeholder="https://api.anthropic.com/v1/"
              />
            </el-form-item>
            <el-form-item label="API密钥" prop="apiKey" required>
              <el-input
                v-model="configForm.apiKey"
                type="password"
                show-password
                placeholder="输入Anthropic API密钥"
              />
            </el-form-item>
          </template>

          <!-- Ollama 特定配置 -->
          <template v-else-if="provider.name === 'Ollama'">
            <el-form-item label="端点地址" prop="endpoint">
              <el-input
                v-model="configForm.endpoint"
                placeholder="http://localhost:11434/"
              />
            </el-form-item>
          </template>

          <!-- 通用高级配置 -->
          <el-form-item label="超时时间(秒)" prop="timeoutSeconds">
            <el-input-number
              v-model="configForm.timeoutSeconds"
              :min="10"
              :max="300"
              :step="1"
              :precision="0"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="请输入超时时间"
            />
          </el-form-item>

          <el-form-item label="最大重试次数" prop="maxRetries">
            <el-input-number
              v-model="configForm.maxRetries"
              :min="0"
              :max="10"
              :step="1"
              :precision="0"
              controls
              controls-position="right"
              style="width: 100%"
              placeholder="请输入重试次数"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 支持的模型 -->
      <div class="config-section">
        <h3 class="section-title">支持的模型</h3>
        <div class="models-grid">
          <el-tag
            v-for="model in provider.supportedModels"
            :key="model"
            class="model-tag"
          >
            {{ model }}
          </el-tag>
        </div>
      </div>

      <!-- 使用统计 -->
      <div class="config-section">
        <h3 class="section-title">使用统计</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="总请求数" :value="provider.usageStats.totalRequests" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="成功请求数" :value="provider.usageStats.successfulRequests" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="失败请求数" :value="provider.usageStats.failedRequests" />
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="8">
            <el-statistic
              title="平均响应时间"
              :value="provider.usageStats.averageResponseTime"
              suffix="ms"
              :precision="0"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic title="总Token使用" :value="provider.usageStats.totalTokens" />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="总成本"
              :value="provider.usageStats.totalCost"
              prefix="$"
              :precision="4"
            />
          </el-col>
        </el-row>

        <div class="stats-actions">
          <el-button @click="resetStats" :loading="resettingStats">
            <el-icon><Refresh /></el-icon>
            重置统计
          </el-button>
        </div>
      </div>

      <!-- 连接测试 -->
      <div class="config-section">
        <h3 class="section-title">连接测试</h3>
        <div class="test-section">
          <el-button
            type="primary"
            @click="testConnection"
            :loading="testing"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>

          <div v-if="testResult" class="test-result">
            <el-alert
              :type="testResult.success ? 'success' : 'error'"
              :title="testResult.success ? '连接成功' : '连接失败'"
              :description="testResult.message"
              show-icon
              :closable="false"
            />
            <div v-if="testResult.success && testResult.responseTime" class="response-time">
              响应时间: {{ testResult.responseTime }}ms
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="validateConfig" :loading="validating">
          <el-icon><CircleCheck /></el-icon>
          验证配置
        </el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { AIProviderService, type AIProvider, type AIProviderConfig } from '@/services/aiProvider'
import { Refresh, Connection, CircleCheck, Check } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  provider: AIProvider | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const configFormRef = ref<FormInstance>()
const configForm = ref<AIProviderConfig>({
  name: '',
  enabled: false
})
const testResult = ref<any>(null)

const saving = ref(false)
const testing = ref(false)
const validating = ref(false)
const resettingStats = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const configRules: FormRules = {
  endpoint: [
    { required: true, message: '请输入端点地址', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' },
    { min: 10, message: 'API密钥长度至少10个字符', trigger: 'blur' }
  ]
}

// 监听提供商变化
watch(() => props.provider, (newProvider) => {
  if (newProvider) {
    loadProviderConfig()
    testResult.value = null
  }
}, { immediate: true })

// 方法
const loadProviderConfig = async () => {
  if (!props.provider) return

  try {
    const config = await AIProviderService.getProviderConfig(props.provider.name)
    configForm.value = { ...config }
  } catch (error: any) {
    console.error('加载提供商配置失败:', error)
    // 使用默认配置
    configForm.value = {
      name: props.provider.name,
      enabled: props.provider.isEnabled,
      timeoutSeconds: 60,
      maxRetries: 3
    }
  }
}

const validateConfig = async () => {
  if (!props.provider || !configFormRef.value) return

  try {
    validating.value = true
    const isValid = await configFormRef.value.validate()
    if (!isValid) return

    const result = await AIProviderService.validateConfig(props.provider.name, configForm.value)

    if (result.isValid) {
      ElMessage.success('配置验证通过')
    } else {
      ElMessage.error(`配置验证失败: ${result.errors.join(', ')}`)
    }

    if (result.warnings.length > 0) {
      ElMessage.warning(`警告: ${result.warnings.join(', ')}`)
    }
  } catch (error: any) {
    console.error('验证配置失败:', error)
    ElMessage.error('验证配置失败')
  } finally {
    validating.value = false
  }
}

const testConnection = async () => {
  if (!props.provider) return

  try {
    testing.value = true
    testResult.value = await AIProviderService.testProvider(props.provider.name)
  } catch (error: any) {
    console.error('测试连接失败:', error)
    testResult.value = {
      success: false,
      message: '测试连接失败'
    }
  } finally {
    testing.value = false
  }
}

const saveConfig = async () => {
  if (!props.provider || !configFormRef.value) return

  try {
    const isValid = await configFormRef.value.validate()
    if (!isValid) return

    saving.value = true
    await AIProviderService.updateProviderConfig(props.provider.name, configForm.value)

    ElMessage.success('配置已保存')
    emit('saved')
    handleClose()
  } catch (error: any) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const resetStats = async () => {
  if (!props.provider) return

  try {
    await ElMessageBox.confirm(
      '确定要重置统计数据吗？此操作不可恢复。',
      '重置统计',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    resettingStats.value = true
    await AIProviderService.resetProviderStats(props.provider.name)

    ElMessage.success('统计数据已重置')
    emit('saved')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重置统计失败:', error)
      ElMessage.error('重置统计失败')
    }
  } finally {
    resettingStats.value = false
  }
}

const handleClose = () => {
  visible.value = false
  testResult.value = null
}
</script>

<style lang="scss" scoped>
.provider-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }

  .models-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .model-tag {
      font-size: 12px;
    }
  }

  .stats-actions {
    margin-top: 20px;
    text-align: right;
  }

  .test-section {
    .test-result {
      margin-top: 16px;

      .response-time {
        margin-top: 8px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
