<template>
  <div class="project-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="$router.back()" type="text" class="back-btn">
          <el-icon><ArrowLeft /></el-icon>
          返回项目列表
        </el-button>
        <div class="project-title-section">
          <h1 class="page-title">{{ project?.name || '加载中...' }}</h1>
          <div class="project-meta">
            <el-tag :type="getStatusType(project?.status)" size="large">
              {{ getStatusLabel(project?.status) }}
            </el-tag>
            <el-tag :type="getPriorityType(project?.priority)" size="large">
              {{ getPriorityLabel(project?.priority) }}
            </el-tag>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="editProject" type="primary">
          <el-icon><Edit /></el-icon>
          编辑项目
        </el-button>
        <el-dropdown @command="handleAction">
          <el-button type="default">
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="downloadRequirements">下载需求文档</el-dropdown-item>
              <el-dropdown-item command="downloadERDiagrams">下载ER图</el-dropdown-item>
              <el-dropdown-item command="downloadContextDiagrams">下载上下文图</el-dropdown-item>
              <el-dropdown-item command="downloadPrototypes">下载原型图</el-dropdown-item>
              <el-dropdown-item command="duplicate" divided>复制项目</el-dropdown-item>
              <el-dropdown-item command="archive">归档项目</el-dropdown-item>
              <el-dropdown-item command="export">导出项目</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除项目</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 项目内容 -->
    <div v-else-if="project" class="project-content">
      <!-- 项目概览 -->
      <el-row :gutter="20" class="overview-section">
        <el-col :span="16">
          <el-card class="overview-card">
            <template #header>
              <div class="card-header">
                <span>项目概览</span>
              </div>
            </template>

            <div class="project-info">
              <div class="info-item">
                <label>项目描述：</label>
                <p>{{ project.description || '暂无描述' }}</p>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>项目负责人：</label>
                  <span>{{ project.ownerName }}</span>
                </div>
                <div class="info-item">
                  <label>创建时间：</label>
                  <span>{{ formatDate(project.createdAt) }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>开始时间：</label>
                  <span>{{ project.startDate ? formatDate(project.startDate) : '未设置' }}</span>
                </div>
                <div class="info-item">
                  <label>结束时间：</label>
                  <span>{{ project.endDate ? formatDate(project.endDate) : '未设置' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>预估工时：</label>
                  <span>{{ project.estimatedHours ? `${project.estimatedHours} 小时` : '未设置' }}</span>
                </div>
                <div class="info-item">
                  <label>实际工时：</label>
                  <span>{{ project.actualHours ? `${project.actualHours} 小时` : '未记录' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>项目预算：</label>
                  <span>{{ project.budget ? `¥${project.budget.toLocaleString()}` : '未设置' }}</span>
                </div>
                <div class="info-item">
                  <label>技术栈：</label>
                  <span>{{ project.technologyStack || '未设置' }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="progress-card">
            <template #header>
              <div class="card-header">
                <span>项目进度</span>
              </div>
            </template>

            <div class="progress-content">
              <div class="progress-circle">
                <el-progress
                  type="circle"
                  :percentage="project.progress"
                  :width="120"
                  :stroke-width="8"
                >
                  <template #default="{ percentage }">
                    <span class="percentage-text">{{ percentage }}%</span>
                  </template>
                </el-progress>
              </div>

              <div class="progress-actions">
                <el-button @click="updateProgress" type="primary" size="small">
                  更新进度
                </el-button>
                <el-button @click="updateStatus" type="default" size="small">
                  更新状态
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 项目导航标签 -->
      <!-- <el-card class="tabs-card">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="需求管理" name="requirements">
            <div class="tab-content">
              <div class="section-header">
                <h3>需求文档</h3>
                <el-button @click="createRequirement" type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  新建需求
                </el-button>
              </div>
              <div class="requirements-list">
                <el-empty v-if="requirements.length === 0" description="暂无需求文档">
                  <el-button @click="createRequirement" type="primary">创建第一个需求</el-button>
                </el-empty>
                <div v-else>
                  <div v-for="req in requirements" :key="req.id" class="requirement-item">
                    <div class="req-header">
                      <span class="req-title">{{ req.title }}</span>
                      <el-tag :type="getRequirementStatusType(req.status)" size="small">{{ getRequirementStatusText(req.status) }}</el-tag>
                    </div>
                    <p class="req-description">{{ req.description }}</p>
                    <div class="req-actions">
                      <el-button @click="viewRequirement(req.id)" type="text" size="small">查看</el-button>
                      <el-button @click="editRequirement(req.id)" type="text" size="small">编辑</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="设计文档" name="design">
            <div class="tab-content">
              <div class="section-header">
                <h3>设计文档</h3>
                <el-button @click="createDesign" type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  生成设计
                </el-button>
              </div>
              <div class="design-grid">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-card class="design-card" @click="viewERDiagram">
                      <div class="design-icon">
                        <el-icon size="32"><Grid /></el-icon>
                      </div>
                      <h4>ER图设计</h4>
                      <p>数据库实体关系图</p>
                      <el-tag v-if="hasERDiagram" type="success" size="small">已生成</el-tag>
                      <el-tag v-else type="info" size="small">未生成</el-tag>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="design-card" @click="viewContextDiagram">
                      <div class="design-icon">
                        <el-icon size="32"><Connection /></el-icon>
                      </div>
                      <h4>上下文图</h4>
                      <p>系统上下文关系图</p>
                      <el-tag v-if="hasContextDiagram" type="success" size="small">已生成</el-tag>
                      <el-tag v-else type="info" size="small">未生成</el-tag>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="开发步骤" name="steps">
            <div class="tab-content">
              <DevelopmentStepsPanel
                :project-id="projectId"
                :requirement-document-id="selectedRequirementId"
                ref="stepsPanel"
              />
            </div>
          </el-tab-pane>



          <el-tab-pane label="测试管理" name="testing">
            <div class="tab-content">
              <div class="section-header">
                <h3>测试任务</h3>
                <el-button @click="createTest" type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  创建测试
                </el-button>
              </div>
              <div class="testing-content">
                <el-empty v-if="testTasks.length === 0" description="暂无测试任务">
                  <el-button @click="createTest" type="primary">创建第一个测试</el-button>
                </el-empty>
                <div v-else class="test-tasks-list">
                  <div v-for="test in testTasks" :key="test.id" class="test-task-item">
                    <div class="test-header">
                      <span class="test-name">{{ test.testName }}</span>
                      <el-tag :type="getTestStatusType(test.status)" size="small">
                        {{ test.status }}
                      </el-tag>
                    </div>
                    <p class="test-description">{{ test.testType }} - {{ test.testFramework || '默认框架' }}</p>
                    <div class="test-actions">
                      <el-button @click="runTest(test)" type="text" size="small">运行</el-button>
                      <el-button @click="viewTestResult(test)" type="text" size="small">查看结果</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="部署管理" name="deployment">
            <div class="tab-content">
              <div class="section-header">
                <h3>部署任务</h3>
                <el-button @click="createDeployment" type="primary" size="small">
                  <el-icon><Upload /></el-icon>
                  创建部署
                </el-button>
              </div>
              <div class="deployment-content">
                <el-empty v-if="deploymentTasks.length === 0" description="暂无部署任务">
                  <el-button @click="createDeployment" type="primary">创建第一个部署</el-button>
                </el-empty>
                <div v-else class="deployment-tasks-list">
                  <div v-for="deploy in deploymentTasks" :key="deploy.id" class="deployment-task-item">
                    <div class="deploy-header">
                      <span class="deploy-name">{{ deploy.deploymentName }}</span>
                      <el-tag :type="getDeployStatusType(deploy.status)" size="small">
                        {{ deploy.status }}
                      </el-tag>
                    </div>
                    <p class="deploy-environment">环境: {{ deploy.environment }}</p>
                    <div class="deploy-actions">
                      <el-button @click="deployProject(deploy)" type="text" size="small">部署</el-button>
                      <el-button @click="viewDeployLog(deploy)" type="text" size="small">查看日志</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="问题管理" name="issues">
            <div class="tab-content">
              <div class="section-header">
                <h3>项目问题</h3>
                <el-button @click="createIssue" type="primary" size="small">
                  <el-icon><Plus /></el-icon>
                  报告问题
                </el-button>
              </div>
              <div class="issues-content">
                <el-empty v-if="issues.length === 0" description="暂无问题记录">
                  <el-button @click="createIssue" type="primary">报告第一个问题</el-button>
                </el-empty>
                <div v-else class="issues-list">
                  <div v-for="issue in issues" :key="issue.id" class="issue-item">
                    <div class="issue-header">
                      <span class="issue-title">{{ issue.title }}</span>
                      <div class="issue-tags">
                        <el-tag :type="getIssueSeverityType(issue.severity)" size="small">
                          {{ issue.severity }}
                        </el-tag>
                        <el-tag :type="getIssueStatusType(issue.status)" size="small">
                          {{ issue.status }}
                        </el-tag>
                      </div>
                    </div>
                    <p class="issue-description">{{ issue.description }}</p>
                    <div class="issue-actions">
                      <el-button @click="viewIssue(issue)" type="text" size="small">查看</el-button>
                      <el-button @click="resolveIssue(issue)" type="text" size="small">解决</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card> -->
    </div>

    <!-- 项目不存在 -->
    <div v-else class="not-found">
      <el-result
        icon="warning"
        title="项目不存在"
        sub-title="请检查项目ID是否正确"
      >
        <template #extra>
          <el-button @click="$router.push('/projects')" type="primary">
            返回项目列表
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 更新进度对话框 -->
    <el-dialog v-model="progressDialogVisible" title="更新项目进度" width="400px">
      <el-form :model="progressForm" label-width="80px">
        <el-form-item label="当前进度">
          <el-slider
            v-model="progressForm.progress"
            :min="0"
            :max="100"
            show-input
            :format-tooltip="formatTooltip"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="progressForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入进度更新说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="progressDialogVisible = false">取消</el-button>
          <el-button @click="confirmUpdateProgress" type="primary">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更新状态对话框 -->
    <el-dialog v-model="statusDialogVisible" title="更新项目状态" width="400px">
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="项目状态">
          <el-select v-model="statusForm.status" placeholder="请选择状态">
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="statusForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入状态更新说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button @click="confirmUpdateStatus" type="primary">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { ProjectService } from '@/services/project'
import { RequirementService } from '@/services/requirement'
import { DesignService } from '@/services/design'
import { ElMessage, ElMessageBox } from 'element-plus'
import DevelopmentStepsPanel from '@/components/development/DevelopmentStepsPanel.vue'
import dayjs from 'dayjs'
import type {
  RequirementSummary,
  TestTask,
  DeploymentTask,
  Issue
} from '@/types'

// 图标导入
import {
  ArrowLeft,
  Edit,
  ArrowDown,
  Plus,
  Grid,
  Connection,
  Upload
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()

// 响应式数据
const loading = ref(false)
const activeTab = ref('requirements')
const progressDialogVisible = ref(false)
const statusDialogVisible = ref(false)
const selectedRequirementId = ref<number>()
const stepsPanel = ref()

// 表单数据
const progressForm = ref({
  progress: 0,
  note: ''
})

const statusForm = ref({
  status: '',
  note: ''
})

// 模拟数据（实际应该从API获取）
const requirements = ref<RequirementSummary[]>([])
const erDiagrams = ref<any[]>([])
const contextDiagrams = ref<any[]>([])
const testTasks = ref<TestTask[]>([])
const deploymentTasks = ref<DeploymentTask[]>([])
const issues = ref<Issue[]>([])

// 计算属性
const project = computed(() => projectStore.currentProject)
const projectId = computed(() => parseInt(route.params.id as string))

const statusOptions = computed(() => ProjectService.getStatusOptions())

const hasERDiagram = computed(() => {
  return erDiagrams.value.length > 0
})

const hasContextDiagram = computed(() => {
  return contextDiagrams.value.length > 0
})

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const formatTooltip = (value: number) => {
  return `${value}%`
}

const getStatusType = (status?: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Planning': 'info',
    'InProgress': 'warning',
    'Testing': 'warning',
    'Deployed': 'success',
    'Completed': 'success',
    'Paused': 'danger',
    'Cancelled': 'info'
  }
  return typeMap[status || ''] || 'info'
}

const getPriorityType = (priority?: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'danger',
    'Critical': 'danger'
  }
  return typeMap[priority || ''] || 'info'
}

const getStatusLabel = (status?: string) => {
  return ProjectService.getStatusLabel(status || '')
}

const getPriorityLabel = (priority?: string) => {
  return ProjectService.getPriorityLabel(priority || '')
}

const getTestStatusType = (status: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Pending': 'info',
    'Running': 'warning',
    'Passed': 'success',
    'Failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getDeployStatusType = (status: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Pending': 'info',
    'Deploying': 'warning',
    'Success': 'success',
    'Failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getIssueSeverityType = (severity: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Low': 'success',
    'Medium': 'warning',
    'High': 'danger',
    'Critical': 'danger'
  }
  return typeMap[severity] || 'info'
}

const getIssueStatusType = (status: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Open': 'danger',
    'InProgress': 'warning',
    'Resolved': 'success',
    'Closed': 'info'
  }
  return typeMap[status] || 'info'
}

const getRequirementStatusType = (status: string): 'info' | 'warning' | 'success' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'info' | 'warning' | 'success' | 'danger' | 'primary'> = {
    'Draft': 'info',
    'InProgress': 'warning',
    'Completed': 'success',
    'PendingReview': 'primary',
    'Published': 'success'
  }
  return typeMap[status] || 'info'
}

const getRequirementStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'Draft': '草稿',
    'InProgress': '进行中',
    'Completed': '已完成',
    'PendingReview': '待审核',
    'Published': '已发布'
  }
  return statusMap[status] || status
}

// 页面操作
const editProject = () => {
  router.push(`/projects/${projectId.value}/edit`)
}

const handleAction = async (command: string) => {
  switch (command) {
    case 'downloadRequirements':
      await downloadRequirements()
      break
    case 'downloadERDiagrams':
      await downloadERDiagrams()
      break
    case 'downloadContextDiagrams':
      await downloadContextDiagrams()
      break
    case 'downloadPrototypes':
      await downloadPrototypes()
      break
    case 'duplicate':
      await duplicateProject()
      break
    case 'archive':
      await archiveProject()
      break
    case 'export':
      await exportProject()
      break
    case 'delete':
      await deleteProject()
      break
  }
}

// 下载功能
const downloadRequirements = async () => {
  if (!project.value) return

  try {
    ElMessage.info('正在下载项目需求文档...')

    // 使用ApiService下载，会自动携带认证信息
    await ProjectService.downloadProjectRequirements(projectId.value)

    ElMessage.success('需求文档下载已开始')
  } catch (error: any) {
    console.error('下载需求文档失败:', error)
    ElMessage.error('下载需求文档失败')
  }
}

const downloadERDiagrams = async () => {
  if (!project.value) return

  try {
    ElMessage.info('正在下载项目ER图...')

    // 使用ApiService下载，会自动携带认证信息
    await ProjectService.downloadProjectERDiagrams(projectId.value)

    ElMessage.success('ER图下载已开始')
  } catch (error: any) {
    console.error('下载ER图失败:', error)
    ElMessage.error('下载ER图失败')
  }
}

const downloadContextDiagrams = async () => {
  if (!project.value) return

  try {
    ElMessage.info('正在下载项目上下文图...')

    // 使用ApiService下载，会自动携带认证信息
    await ProjectService.downloadProjectContextDiagrams(projectId.value)

    ElMessage.success('上下文图下载已开始')
  } catch (error: any) {
    console.error('下载上下文图失败:', error)
    ElMessage.error('下载上下文图失败')
  }
}

const downloadPrototypes = async () => {
  if (!project.value) return

  try {
    ElMessage.info('正在下载项目原型图...')

    // 使用ApiService下载，会自动携带认证信息
    await ProjectService.downloadProjectPrototypes(projectId.value)

    ElMessage.success('原型图下载已开始')
  } catch (error: any) {
    console.error('下载原型图失败:', error)
    ElMessage.error('下载原型图失败')
  }
}

const duplicateProject = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要复制这个项目吗？',
      '确认复制',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    // TODO: 实现项目复制逻辑
    ElMessage.success('项目复制成功')
  } catch (error) {
    // 用户取消
  }
}

const archiveProject = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要归档这个项目吗？归档后项目将不再显示在活跃项目列表中。',
      '确认归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    // TODO: 实现项目归档逻辑
    ElMessage.success('项目归档成功')
  } catch (error) {
    // 用户取消
  }
}

const exportProject = async () => {
  try {
    // TODO: 实现项目导出逻辑
    ElMessage.success('项目导出成功')
  } catch (error) {
    ElMessage.error('项目导出失败')
  }
}

const deleteProject = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个项目吗？此操作不可恢复，将删除项目的所有相关数据。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await projectStore.deleteProject(projectId.value)
    ElMessage.success('项目删除成功')
    router.push('/projects')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('项目删除失败')
    }
  }
}

// 进度和状态更新
const updateProgress = () => {
  progressForm.value.progress = project.value?.progress || 0
  progressForm.value.note = ''
  progressDialogVisible.value = true
}

const confirmUpdateProgress = async () => {
  try {
    // 调用项目状态更新API，包含进度信息
    await projectStore.updateProjectStatus(projectId.value, {
      status: project.value?.status || 'InProgress',
      progress: progressForm.value.progress,
      note: progressForm.value.note
    })
    ElMessage.success('项目进度更新成功')
    progressDialogVisible.value = false
  } catch (error) {
    ElMessage.error('项目进度更新失败')
  }
}

const updateStatus = () => {
  statusForm.value.status = project.value?.status || ''
  statusForm.value.note = ''
  statusDialogVisible.value = true
}

const confirmUpdateStatus = async () => {
  try {
    await projectStore.updateProjectStatus(projectId.value, {
      status: statusForm.value.status,
      note: statusForm.value.note
    })
    ElMessage.success('项目状态更新成功')
    statusDialogVisible.value = false
  } catch (error) {
    ElMessage.error('项目状态更新失败')
  }
}

// 标签页操作
const handleTabClick = (tab: any) => {
  // 根据标签页加载相应数据
  switch (tab.name) {
    case 'requirements':
      loadRequirements()
      break
    case 'design':
      loadDesignData()
      break
    case 'testing':
      loadTestTasks()
      break
    case 'deployment':
      loadDeploymentTasks()
      break
    case 'issues':
      loadIssues()
      break
  }
}

// 数据加载方法
const loadRequirements = async () => {
  try {
    console.log('加载需求数据，项目ID:', projectId.value)
    const response = await RequirementService.getRequirementDocuments(projectId.value, 1, 50)
    const documents = response.items || []

    // 转换为RequirementSummary格式
    requirements.value = documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      description: doc.content ? doc.content.substring(0, 100) + '...' : '暂无描述',
      status: doc.status || 'Draft',
      version: doc.version || '1.0',
      createdAt: doc.createdAt
    }))

    console.log('需求数据加载完成:', requirements.value.length, '个需求')
  } catch (error) {
    console.error('加载需求数据失败:', error)
    requirements.value = []
  }
}

const loadDesignData = async () => {
  try {
    console.log('加载设计数据，项目ID:', projectId.value)
    // 加载ER图数据
    const erResponse = await DesignService.getERDiagrams(projectId.value)
    erDiagrams.value = erResponse || []
    console.log('ER图数据加载完成:', erDiagrams.value.length, '个ER图')

    // 加载上下文图数据
    const contextResponse = await DesignService.getContextDiagrams(projectId.value)
    contextDiagrams.value = contextResponse || []
    console.log('上下文图数据加载完成:', contextDiagrams.value.length, '个上下文图')
  } catch (error) {
    console.error('加载设计数据失败:', error)
    erDiagrams.value = []
    contextDiagrams.value = []
  }
}



const loadTestTasks = async () => {
  try {
    console.log('加载测试任务数据，项目ID:', projectId.value)
    // TODO: 实现测试任务数据加载
    testTasks.value = []
  } catch (error) {
    console.error('加载测试任务数据失败:', error)
    testTasks.value = []
  }
}

const loadDeploymentTasks = async () => {
  try {
    console.log('加载部署任务数据，项目ID:', projectId.value)
    // TODO: 实现部署任务数据加载
    deploymentTasks.value = []
  } catch (error) {
    console.error('加载部署任务数据失败:', error)
    deploymentTasks.value = []
  }
}

const loadIssues = async () => {
  try {
    console.log('加载问题数据，项目ID:', projectId.value)
    // TODO: 实现问题数据加载
    issues.value = []
  } catch (error) {
    console.error('加载问题数据失败:', error)
    issues.value = []
  }
}

// 功能操作方法
const createRequirement = () => {
  router.push(`/requirements/${projectId.value}/chat`)
}

const viewRequirement = (id: number) => {
  console.log('查看需求详情，ID:', id)
  router.push(`/requirements/documents/${id}`)
}

const editRequirement = (id: number) => {
  console.log('编辑需求，ID:', id)
  router.push(`/requirements/documents/${id}/edit`)
}

const createDesign = () => {
  router.push(`/design`)
}

const viewERDiagram = () => {
  router.push(`/design/${projectId.value}/er-diagram`)
}

const viewContextDiagram = () => {
  router.push(`/design/${projectId.value}/context-diagram`)
}



const createTest = () => {
  router.push('/testing')
}

const runTest = (test: any) => {
  // TODO: 运行测试
}

const viewTestResult = (test: any) => {
  // TODO: 查看测试结果
}

const createDeployment = () => {
  router.push('/deployment')
}

const deployProject = (deploy: any) => {
  // TODO: 执行部署
}

const viewDeployLog = (deploy: any) => {
  // TODO: 查看部署日志
}

const createIssue = () => {
  router.push('/issues')
}

const viewIssue = (issue: any) => {
  // TODO: 查看问题详情
}

const resolveIssue = (issue: any) => {
  // TODO: 解决问题
}

// 获取项目数据
const fetchProjectData = async () => {
  try {
    loading.value = true
    await projectStore.fetchProject(projectId.value)
  } catch (error) {
    console.error('Failed to fetch project:', error)
    ElMessage.error('获取项目信息失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await fetchProjectData()
  // 默认加载需求数据
  loadRequirements()

  // 检查URL参数，如果有requirementId则切换到开发步骤标签页
  const requirementId = route.query.requirementId
  if (requirementId) {
    selectedRequirementId.value = parseInt(requirementId as string)
    activeTab.value = 'steps'
  }
})
</script>

<style lang="scss" scoped>
.project-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    .header-left {
      flex: 1;

      .back-btn {
        margin-bottom: 12px;
        color: #606266;

        &:hover {
          color: #409eff;
        }
      }

      .project-title-section {
        .page-title {
          margin: 0 0 12px 0;
          font-size: 28px;
          font-weight: 600;
          color: #303133;
        }

        .project-meta {
          display: flex;
          gap: 8px;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .loading-container {
    padding: 40px 0;
  }

  .project-content {
    .overview-section {
      margin-bottom: 24px;

      .overview-card {
        .card-header {
          font-weight: 600;
          color: #303133;
        }

        .project-info {
          .info-item {
            margin-bottom: 16px;

            label {
              display: inline-block;
              width: 100px;
              font-weight: 500;
              color: #606266;
              margin-right: 8px;
            }

            p {
              margin: 4px 0 0 108px;
              color: #303133;
              line-height: 1.6;
            }

            span {
              color: #303133;
            }
          }

          .info-row {
            display: flex;
            gap: 40px;

            .info-item {
              flex: 1;
              margin-bottom: 16px;

              label {
                width: 80px;
              }

              p {
                margin: 4px 0 0 88px;
              }
            }
          }
        }
      }

      .progress-card {
        .card-header {
          font-weight: 600;
          color: #303133;
        }

        .progress-content {
          text-align: center;

          .progress-circle {
            margin-bottom: 24px;

            .percentage-text {
              font-size: 18px;
              font-weight: 600;
              color: #303133;
            }
          }

          .progress-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
          }
        }
      }
    }

    .tabs-card {
      .tab-content {
        padding: 20px 0;

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
          }
        }

        // 需求管理样式
        .requirements-list {
          .requirement-item {
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #fafafa;

            .req-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .req-title {
                font-weight: 500;
                color: #303133;
              }
            }

            .req-description {
              margin: 8px 0;
              color: #606266;
              line-height: 1.6;
            }

            .req-actions {
              display: flex;
              gap: 8px;
            }
          }
        }

        // 设计文档样式
        .design-grid {
          .design-card {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e4e7ed;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .design-icon {
              margin-bottom: 12px;
              color: #409eff;
            }

            h4 {
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }

            p {
              margin: 0 0 12px 0;
              color: #606266;
              font-size: 14px;
            }
          }
        }



        // 测试管理样式
        .testing-content {
          .test-tasks-list {
            .test-task-item {
              padding: 16px;
              border: 1px solid #e4e7ed;
              border-radius: 8px;
              margin-bottom: 12px;
              background: #fafafa;

              .test-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .test-name {
                  font-weight: 500;
                  color: #303133;
                }
              }

              .test-description {
                margin: 8px 0;
                color: #606266;
                line-height: 1.6;
              }

              .test-actions {
                display: flex;
                gap: 8px;
              }
            }
          }
        }

        // 部署管理样式
        .deployment-content {
          .deployment-tasks-list {
            .deployment-task-item {
              padding: 16px;
              border: 1px solid #e4e7ed;
              border-radius: 8px;
              margin-bottom: 12px;
              background: #fafafa;

              .deploy-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .deploy-name {
                  font-weight: 500;
                  color: #303133;
                }
              }

              .deploy-environment {
                margin: 8px 0;
                color: #606266;
                font-size: 14px;
              }

              .deploy-actions {
                display: flex;
                gap: 8px;
              }
            }
          }
        }

        // 问题管理样式
        .issues-content {
          .issues-list {
            .issue-item {
              padding: 16px;
              border: 1px solid #e4e7ed;
              border-radius: 8px;
              margin-bottom: 12px;
              background: #fafafa;

              .issue-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .issue-title {
                  font-weight: 500;
                  color: #303133;
                }

                .issue-tags {
                  display: flex;
                  gap: 8px;
                }
              }

              .issue-description {
                margin: 8px 0;
                color: #606266;
                line-height: 1.6;
              }

              .issue-actions {
                display: flex;
                gap: 8px;
              }
            }
          }
        }
      }
    }
  }

  .not-found {
    padding: 60px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-right {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .overview-section {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .tab-content {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .design-grid {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>
