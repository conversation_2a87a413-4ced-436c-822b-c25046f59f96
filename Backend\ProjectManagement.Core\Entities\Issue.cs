using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 问题/Issue实体 - 对应DatabaseSchema.sql中的Issues表
/// 功能: 管理项目开发过程中的问题、缺陷、功能请求等
/// 支持: 问题分类、优先级管理、状态跟踪、全文搜索
/// </summary>
[SugarTable("Issues", "项目问题和缺陷管理表")]
public class Issue : BaseEntity
{

    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 问题标题（支持全文搜索）
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = false, ColumnDescription = "问题标题")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 问题详细描述（支持全文搜索）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "问题详细描述")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 问题类型: Bug(缺陷), Feature(功能请求), Enhancement(改进), Task(任务)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "问题类型")]
    public string IssueType { get; set; } = string.Empty;

    /// <summary>
    /// 优先级: Low(低), Medium(中), High(高), Critical(紧急)
    /// </summary>
    [SugarColumn(Length = 10, IsNullable = true, ColumnDescription = "优先级")]
    public string Priority { get; set; } = "Medium";

    /// <summary>
    /// 问题状态: Open(开放), InProgress(处理中), Resolved(已解决), Closed(已关闭)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "问题状态")]
    public string Status { get; set; } = "Open";

    /// <summary>
    /// 分配给的用户ID，关联Users表
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "分配给的用户ID，关联Users表")]
    public int? AssignedTo { get; set; }

    /// <summary>
    /// 报告人用户ID，关联Users表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "报告人用户ID，关联Users表")]
    public int ReportedBy { get; set; }

    /// <summary>
    /// 问题标签，逗号分隔（如：前端,登录,紧急）
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "问题标签，逗号分隔")]
    public string? Labels { get; set; }

    /// <summary>
    /// 问题解决时间
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "问题解决时间")]
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 分配给的用户 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? AssignedToUser { get; set; }

    /// <summary>
    /// 报告人 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? ReportedByUser { get; set; }

    /// <summary>
    /// 问题解决记录 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<IssueResolution> IssueResolutions { get; set; } = new();
}
