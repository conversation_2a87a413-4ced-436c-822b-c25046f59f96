<template>
  <div class="sequence-detail">
    <!-- 序列基本信息 -->
    <el-card class="sequence-info">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-button size="small" @click="$emit('update')">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="序列名称">{{ sequence.name }}</el-descriptions-item>
        <el-descriptions-item label="分类">
          <el-tag :type="getCategoryTagType(sequence.category)">{{ sequence.category }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="sequence.isActive ? 'success' : 'info'">
            {{ sequence.isActive ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="使用次数">{{ sequence.usageCount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ formatTime(sequence.createdTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后使用" :span="2">
          <span v-if="sequence.lastUsedTime">{{ formatTime(sequence.lastUsedTime) }}</span>
          <span v-else class="text-muted">从未使用</span>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ sequence.description }}</el-descriptions-item>
        <el-descriptions-item v-if="sequence.tags?.length" label="标签" :span="2">
          <el-tag
            v-for="tag in sequence.tags"
            :key="tag"
            size="small"
            effect="plain"
            style="margin-right: 8px"
          >
            {{ tag }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="sequence.notes" label="备注" :span="2">
          {{ sequence.notes }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 执行步骤 -->
    <el-card class="sequence-steps">
      <template #header>
        <div class="card-header">
          <span>执行步骤 ({{ sequence.steps?.length || 0 }})</span>
          <div class="header-actions">
            <el-button-group class="view-toggle">
              <el-button
                size="small"
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
                列表视图
              </el-button>
              <el-button
                size="small"
                :type="viewMode === 'flowchart' ? 'primary' : 'default'"
                @click="viewMode = 'flowchart'"
              >
                <el-icon><Share /></el-icon>
                流程图
              </el-button>
            </el-button-group>
            <el-button
              size="small"
              type="primary"
              :disabled="!sequence.isActive || !sequence.steps?.length"
              @click="$emit('execute', sequence)"
            >
              <el-icon><VideoPlay /></el-icon>
              执行序列
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="!sequence.steps?.length" class="empty-steps">
        <el-empty description="暂无执行步骤" />
      </div>

      <!-- 流程图视图 -->
      <SequenceFlowChart
        v-else-if="viewMode === 'flowchart'"
        :sequence="sequence"
      />

      <!-- 列表视图 -->
      <div v-else class="steps-timeline">
        <div
          v-for="(step, index) in sequence.steps"
          :key="step.id || index"
          class="step-item"
          :class="{ disabled: !step.isActive }"
        >
          <div class="step-timeline">
            <div class="step-number">{{ step.stepOrder || index + 1 }}</div>
            <div v-if="index < sequence.steps.length - 1" class="step-line"></div>
          </div>

          <div class="step-content">
            <div class="step-header">
              <div class="step-info">
                <span class="step-action">{{ getActionTypeLabel(step.actionType) }}</span>
                <el-tag :type="getActionTypeTag(step.actionType)" size="small">
                  {{ step.actionType }}
                </el-tag>
                <el-tag v-if="!step.isActive" type="info" size="small">已禁用</el-tag>
              </div>
              <div class="step-meta">
                <span class="meta-item">超时: {{ step.timeoutSeconds }}s</span>
                <span class="meta-item">重试: {{ step.maxRetries }}次</span>
              </div>
            </div>

            <div class="step-description">
              {{ step.description || '暂无描述' }}
            </div>

            <div v-if="step.templateId" class="step-template">
              <el-icon><Picture /></el-icon>
              <span>模板: {{ getTemplateName(step.templateId) }}</span>
            </div>

            <div v-if="hasParameters(step)" class="step-parameters">
              <el-collapse>
                <el-collapse-item title="查看参数" :name="step.id || index">
                  <pre class="parameters-json">{{ formatParameters(step.parameters) }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 执行历史 -->
    <el-card class="execution-history">
      <template #header>
        <div class="card-header">
          <span>最近执行记录</span>
          <el-button size="small" @click="loadExecutionLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <div v-if="executionLogs.length === 0" class="empty-logs">
        <el-empty description="暂无执行记录" />
      </div>

      <el-table v-else :data="executionLogs" style="width: 100%">
        <el-table-column prop="startTime" label="执行时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            <span v-if="row.duration">{{ row.duration }}ms</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="executedBy" label="执行者" width="120" />
        <el-table-column prop="result" label="结果" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.result">{{ row.result }}</span>
            <span v-else-if="row.errorMessage" class="error-message">{{ row.errorMessage }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, VideoPlay, Picture, Refresh, List, Share } from '@element-plus/icons-vue'
import CustomTemplateService, {
  type TemplateSequence,
  type ExecutionLog,
  type CustomTemplate
} from '@/services/customTemplate'
import SequenceFlowChart from './SequenceFlowChart.vue'

// Props
interface Props {
  sequence: TemplateSequence
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  update: []
  execute: [sequence: TemplateSequence]
}>()

// 响应式数据
const viewMode = ref<'list' | 'flowchart'>('list')
const executionLogs = ref<ExecutionLog[]>([])
const templates = ref<CustomTemplate[]>([])

// 生命周期
onMounted(() => {
  loadExecutionLogs()
  loadTemplates()
})

// 方法
const loadExecutionLogs = async () => {
  try {
    const result = await CustomTemplateService.getExecutionLogs({
      page: 1,
      pageSize: 10,
      sequenceId: props.sequence.id
    })
    executionLogs.value = result.items
  } catch (error) {
    console.error('加载执行日志失败:', error)
    ElMessage.error('加载执行日志失败')
  }
}

const loadTemplates = async () => {
  try {
    const result = await CustomTemplateService.getTemplates({ page: 1, pageSize: 1000 })
    templates.value = result.items
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

const getCategoryTagType = (category: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '按钮': 'primary',
    '菜单': 'success',
    '对话框': 'warning',
    '输入框': 'info',
    '图标': 'danger',
    'CopilotChat自动化': 'primary',
    '其他': 'info'
  }
  return typeMap[category] || 'info'
}

const getActionTypeLabel = (actionType: string) => {
  const labels: Record<string, string> = {
    // UI操作类型
    'click': '点击',
    'wait': '等待',
    'input': '输入',
    'delay': '延迟',
    'screenshot': '截图',
    'verify': '验证',
    'scroll': '滚动',
    'key_press': '按键'
  }
  return labels[actionType] || actionType
}

const getActionTypeTag = (actionType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const tags: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    // UI操作类型
    'click': 'primary',
    'wait': 'info',
    'input': 'success',
    'delay': 'warning',
    'screenshot': 'danger',
    'verify': 'info',
    'scroll': 'warning',
    'key_press': 'primary'
  }
  return tags[actionType] || 'info'
}

const getTemplateName = (templateId: number) => {
  const template = templates.value.find(t => t.id === templateId)
  return template?.name || `模板 #${templateId}`
}

const hasParameters = (step: any) => {
  return step.parameters && Object.keys(step.parameters).length > 0
}

const formatParameters = (parameters: any) => {
  return JSON.stringify(parameters, null, 2)
}

const getStatusTagType = (status: string): 'warning' | 'success' | 'danger' | 'info' => {
  const typeMap: Record<string, 'warning' | 'success' | 'danger' | 'info'> = {
    'Started': 'info',
    'Running': 'warning',
    'Completed': 'success',
    'Failed': 'danger',
    'Cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'Started': '已开始',
    'Running': '运行中',
    'Completed': '已完成',
    'Failed': '失败',
    'Cancelled': '已取消'
  }
  return labels[status] || status
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped lang="scss">
.sequence-detail {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .view-toggle {
        .el-button {
          padding: 8px 12px;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  .sequence-info {
    margin-bottom: 20px;

    .text-muted {
      color: #c0c4cc;
    }
  }

  .sequence-steps {
    margin-bottom: 20px;

    .empty-steps {
      padding: 20px 0;
    }

    .steps-timeline {
      .step-item {
        display: flex;
        margin-bottom: 20px;

        &.disabled {
          opacity: 0.6;
        }

        .step-timeline {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 16px;

          .step-number {
            width: 32px;
            height: 32px;
            background-color: #409eff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            flex-shrink: 0;
          }

          .step-line {
            width: 2px;
            height: 40px;
            background-color: #e4e7ed;
            margin-top: 8px;
          }
        }

        .step-content {
          flex: 1;
          background-color: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          border-left: 3px solid #409eff;

          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .step-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .step-action {
                font-weight: 500;
                color: #303133;
              }
            }

            .step-meta {
              display: flex;
              gap: 12px;

              .meta-item {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .step-description {
            color: #606266;
            margin-bottom: 12px;
            line-height: 1.5;
          }

          .step-template {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #409eff;
            font-size: 14px;
            margin-bottom: 12px;
          }

          .step-parameters {
            .parameters-json {
              background-color: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              font-size: 12px;
              line-height: 1.4;
              overflow-x: auto;
              margin: 0;
            }
          }
        }
      }
    }
  }

  .execution-history {
    .empty-logs {
      padding: 20px 0;
    }

    .error-message {
      color: #f56c6c;
    }
  }
}
</style>
