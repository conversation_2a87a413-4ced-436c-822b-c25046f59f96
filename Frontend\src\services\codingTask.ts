import { ApiService } from './api'

/**
 * 编码任务接口
 */
export interface CodingTask {
  id: number
  projectId: number
  taskName: string
  description?: string
  status: string // NotStarted, InProgress, Completed, Blocked, Cancelled
  priority: string // High, Medium, Low
  assignedTo?: number
  assignedUserName?: string
  estimatedHours?: number
  actualHours?: number
  dueDate?: string
  actualStartTime?: string
  actualEndTime?: string
  tags?: string
  notes?: string
  isFinishCoding: boolean // 是否完成编码
  isFixError: boolean // 是否修复错误
  createdTime: string
  updatedTime?: string
  createdBy?: number
  updatedBy?: number
  stepCount?: number
}

/**
 * 编码任务统计信息
 */
export interface CodingTaskStatistics {
  totalTasks: number
  inProgressTasks: number
  completedTasks: number
  blockedTasks: number
  notStartedTasks: number
  highPriorityTasks: number
  upcomingDueTasks: number
  averageCompletionHours: number
}

/**
 * 分页查询参数
 */
export interface CodingTaskQueryParams {
  projectId: number
  pageIndex?: number
  pageSize?: number
  status?: string
  priority?: string
  assignedTo?: number
  searchKeyword?: string
}

/**
 * 分页查询结果
 */
export interface CodingTaskPagedResult {
  items: CodingTask[]
  totalCount: number
  pageIndex: number
  pageSize: number
  totalPages: number
}

/**
 * 编码任务批量更新状态请求
 */
export interface CodingTaskBatchUpdateStatusRequest {
  taskIds: number[]
  status: string
}

/**
 * 更新编码任务步骤状态标志请求
 */
export interface UpdateCodingTaskStepFlagsRequest {
  isFinishCoding?: boolean
  isFixError?: boolean
  status?: string
}

/**
 * 开始自动化操作请求
 */
export interface StartAutomationRequest {
  projectId: number
  currentTaskId: number
}

/**
 * 编码任务添加步骤请求
 */
export interface CodingTaskAddStepsRequest {
  stepIds: number[]
}

/**
 * 编码任务移除步骤请求
 */
export interface CodingTaskRemoveStepsRequest {
  stepIds: number[]
}

/**
 * 编码任务服务
 */
export class CodingTaskService {
  
  /**
   * 获取项目的编码任务列表
   */
  static async getProjectTasks(params: CodingTaskQueryParams): Promise<CodingTaskPagedResult> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params.pageIndex) queryParams.append('pageIndex', params.pageIndex.toString())
      if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
      if (params.status) queryParams.append('status', params.status)
      if (params.priority) queryParams.append('priority', params.priority)
      if (params.assignedTo) queryParams.append('assignedTo', params.assignedTo.toString())
      if (params.searchKeyword) queryParams.append('searchKeyword', params.searchKeyword)

      const response = await ApiService.get<{success: boolean, data: CodingTaskPagedResult}>(
        `/api/CodingTask/project/${params.projectId}?${queryParams.toString()}`
      )

      if (response.success && response.data) {
        return response.data
      } else {
        return {
          items: [],
          totalCount: 0,
          pageIndex: params.pageIndex || 1,
          pageSize: params.pageSize || 20,
          totalPages: 0
        }
      }
    } catch (error) {
      console.error('获取编码任务列表失败:', error)
      throw error
    }
  }

  /**
   * 获取编码任务详情
   */
  static async getTaskById(id: number): Promise<CodingTask> {
    try {
      const response = await ApiService.get<{success: boolean, data: CodingTask}>(`/api/CodingTask/${id}`)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error('获取编码任务详情失败')
    } catch (error) {
      console.error('获取编码任务详情失败:', error)
      throw error
    }
  }

  /**
   * 创建编码任务
   */
  static async createTask(task: Partial<CodingTask>): Promise<CodingTask> {
    try {
      const response = await ApiService.post<{success: boolean, data: CodingTask}>('/api/CodingTask', task)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error('创建编码任务失败')
    } catch (error) {
      console.error('创建编码任务失败:', error)
      throw error
    }
  }

  /**
   * 更新编码任务
   */
  static async updateTask(id: number, task: Partial<CodingTask>): Promise<CodingTask> {
    try {
      const response = await ApiService.put<{success: boolean, data: CodingTask}>(`/api/CodingTask/${id}`, task)
      if (response.success && response.data) {
        return response.data
      }
      throw new Error('更新编码任务失败')
    } catch (error) {
      console.error('更新编码任务失败:', error)
      throw error
    }
  }

  /**
   * 删除编码任务
   */
  static async deleteTask(id: number): Promise<boolean> {
    try {
      const response = await ApiService.delete<{success: boolean}>(`/api/CodingTask/${id}`)
      return response.success
    } catch (error) {
      console.error('删除编码任务失败:', error)
      throw error
    }
  }

  /**
   * 获取项目编码任务统计信息
   */
  static async getProjectStatistics(projectId: number): Promise<CodingTaskStatistics> {
    try {
      const response = await ApiService.get<{success: boolean, data: CodingTaskStatistics}>(
        `/api/CodingTask/project/${projectId}/statistics`
      )
      if (response.success && response.data) {
        return response.data
      }
      throw new Error('获取统计信息失败')
    } catch (error) {
      console.error('获取编码任务统计信息失败:', error)
      throw error
    }
  }

  /**
   * 获取用户分配的编码任务
   */
  static async getUserTasks(userId?: number, status?: string): Promise<CodingTask[]> {
    try {
      const params = new URLSearchParams()
      if (status) params.append('status', status)

      const url = userId 
        ? `/api/CodingTask/user/${userId}?${params.toString()}`
        : `/api/CodingTask/user?${params.toString()}`

      const response = await ApiService.get<{success: boolean, data: CodingTask[]}>(url)
      if (response.success && response.data) {
        return response.data
      }
      return []
    } catch (error) {
      console.error('获取用户编码任务失败:', error)
      throw error
    }
  }

  /**
   * 获取即将到期的编码任务
   */
  static async getUpcomingDueTasks(projectId: number, days: number = 7): Promise<CodingTask[]> {
    try {
      const response = await ApiService.get<{success: boolean, data: CodingTask[]}>(
        `/api/CodingTask/project/${projectId}/upcoming-due?days=${days}`
      )
      if (response.success && response.data) {
        return response.data
      }
      return []
    } catch (error) {
      console.error('获取即将到期任务失败:', error)
      throw error
    }
  }

  /**
   * 批量更新任务状态
   */
  static async batchUpdateStatus(request: CodingTaskBatchUpdateStatusRequest): Promise<number> {
    try {
      const response = await ApiService.put<{success: boolean, data: {updatedCount: number}}>(
        '/api/CodingTask/batch-status', request
      )
      if (response.success && response.data) {
        return response.data.updatedCount
      }
      return 0
    } catch (error) {
      console.error('批量更新任务状态失败:', error)
      throw error
    }
  }

  /**
   * 获取任务关联的开发步骤
   */
  static async getTaskSteps(taskId: number): Promise<any[]> {
    try {
      const response = await ApiService.get<{success: boolean, data: any[]}>(
        `/api/CodingTask/${taskId}/steps`
      )
      if (response.success && response.data) {
        return response.data
      }
      return []
    } catch (error) {
      console.error('获取任务关联步骤失败:', error)
      throw error
    }
  }

  /**
   * 添加开发步骤到编码任务
   */
  static async addStepsToTask(taskId: number, request: CodingTaskAddStepsRequest): Promise<number> {
    try {
      const response = await ApiService.post<{success: boolean, data: {addedCount: number}}>(
        `/api/CodingTask/${taskId}/steps`, request
      )
      if (response.success && response.data) {
        return response.data.addedCount
      }
      return 0
    } catch (error) {
      console.error('添加步骤到任务失败:', error)
      throw error
    }
  }

  /**
   * 从编码任务中移除开发步骤
   */
  static async removeStepsFromTask(taskId: number, request: CodingTaskRemoveStepsRequest): Promise<number> {
    try {
      const response = await ApiService.delete<{success: boolean, data: {removedCount: number}}>(
        `/api/CodingTask/${taskId}/steps`, { data: request }
      )
      if (response.success && response.data) {
        return response.data.removedCount
      }
      return 0
    } catch (error) {
      console.error('从任务移除步骤失败:', error)
      throw error
    }
  }

  /**
   * 更新编码任务步骤状态标志
   */
  static async updateTaskStepFlags(taskId: number, stepId: number, request: UpdateCodingTaskStepFlagsRequest): Promise<{
    taskStepId: number,
    taskId: number,
    stepId: number,
    status: string,
    isFinishCoding: boolean,
    isFixError: boolean
  }> {
    try {
      const response = await ApiService.patch<{
        success: boolean,
        data: {
          taskStepId: number,
          taskId: number,
          stepId: number,
          status: string,
          isFinishCoding: boolean,
          isFixError: boolean
        }
      }>(`/api/CodingTask/${taskId}/steps/${stepId}/flags`, request)

      if (response.success && response.data) {
        return response.data
      }
      throw new Error('更新编码任务步骤状态标志失败')
    } catch (error) {
      console.error('更新编码任务步骤状态标志失败:', error)
      throw error
    }
  }

  /**
   * 开始自动化操作 - 更新项目中所有任务状态
   */
  static async startAutomation(request: StartAutomationRequest): Promise<{
    projectId: number,
    currentTaskId: number,
    updatedCount: number,
    totalTasks: number,
    updateResults: Array<{
      taskId: number,
      taskName: string,
      oldStatus: string,
      newStatus: string,
      isCurrent: boolean
    }>
  }> {
    try {
      const response = await ApiService.post<{
        success: boolean,
        data: {
          projectId: number,
          currentTaskId: number,
          updatedCount: number,
          totalTasks: number,
          updateResults: Array<{
            taskId: number,
            taskName: string,
            oldStatus: string,
            newStatus: string,
            isCurrent: boolean
          }>
        }
      }>('/api/CodingTask/start-automation', request)

      if (response.success && response.data) {
        return response.data
      }
      throw new Error('开始自动化操作失败')
    } catch (error) {
      console.error('开始自动化操作失败:', error)
      throw error
    }
  }
}

// 导出服务类和实例
export const codingTaskService = CodingTaskService
export default CodingTaskService
