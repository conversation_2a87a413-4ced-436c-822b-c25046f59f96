<template>
  <div class="performance-analysis-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>性能分析</span>
          <el-button type="text" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else class="performance-content">
        <!-- 性能指标概览 -->
        <div class="performance-overview">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="平均响应时间" :value="data.avgResponseTime" suffix="ms" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功率" :value="data.successRate" suffix="%" :precision="1" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="P95响应时间" :value="data.p95ResponseTime" suffix="ms" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="错误率" :value="data.errorRate" suffix="%" :precision="2">
                <template #suffix>
                  <span :class="data.errorRate > 5 ? 'error-high' : 'error-normal'">
                    {{ data.errorRate.toFixed(2) }}%
                  </span>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>

        <!-- 响应时间分布 -->
        <div class="response-time-distribution">
          <h4>响应时间分布</h4>
          <div ref="responseTimeChart" class="chart-container"></div>
        </div>

        <!-- 提供商性能对比 -->
        <div class="provider-performance">
          <h4>提供商性能对比</h4>
          <el-table :data="data.providerPerformance" style="width: 100%">
            <el-table-column prop="provider" label="提供商" />
            <el-table-column prop="avgResponseTime" label="平均响应时间" align="right">
              <template #default="{ row }">
                {{ row.avgResponseTime }}ms
              </template>
            </el-table-column>
            <el-table-column prop="successRate" label="成功率" align="right">
              <template #default="{ row }">
                <el-tag :type="getSuccessRateType(row.successRate)">
                  {{ row.successRate.toFixed(1) }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="p95ResponseTime" label="P95响应时间" align="right">
              <template #default="{ row }">
                {{ row.p95ResponseTime }}ms
              </template>
            </el-table-column>
            <el-table-column prop="errorRate" label="错误率" align="right">
              <template #default="{ row }">
                <el-tag :type="getErrorRateType(row.errorRate)">
                  {{ row.errorRate.toFixed(2) }}%
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 性能趋势 -->
        <div class="performance-trend">
          <h4>性能趋势</h4>
          <div ref="performanceTrendChart" class="chart-container"></div>
        </div>

        <!-- 性能优化建议 -->
        <div class="performance-suggestions">
          <h4>性能优化建议</h4>
          <el-alert
            v-for="suggestion in data.suggestions"
            :key="suggestion.id"
            :title="suggestion.title"
            :description="suggestion.description"
            :type="suggestion.type"
            show-icon
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  data: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  refresh: []
}>()

const responseTimeChart = ref<HTMLElement>()
const performanceTrendChart = ref<HTMLElement>()
let responseTimeChartInstance: echarts.ECharts | null = null
let performanceTrendChartInstance: echarts.ECharts | null = null

const initCharts = () => {
  initResponseTimeChart()
  initPerformanceTrendChart()
}

const initResponseTimeChart = () => {
  if (!responseTimeChart.value) return

  responseTimeChartInstance = echarts.init(responseTimeChart.value)
  updateResponseTimeChart()
}

const initPerformanceTrendChart = () => {
  if (!performanceTrendChart.value) return

  performanceTrendChartInstance = echarts.init(performanceTrendChart.value)
  updatePerformanceTrendChart()
}

const updateResponseTimeChart = () => {
  if (!responseTimeChartInstance || !props.data?.responseTimeDistribution) return

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '响应时间分布',
        type: 'pie',
        radius: '50%',
        data: props.data.responseTimeDistribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  responseTimeChartInstance.setOption(option)
}

const updatePerformanceTrendChart = () => {
  if (!performanceTrendChartInstance || !props.data?.performanceTrend) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均响应时间', '成功率']
    },
    xAxis: {
      type: 'category',
      data: props.data.performanceTrend.map((item: any) => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '响应时间(ms)',
        position: 'left'
      },
      {
        type: 'value',
        name: '成功率(%)',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '平均响应时间',
        type: 'line',
        yAxisIndex: 0,
        data: props.data.performanceTrend.map((item: any) => item.avgResponseTime),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '成功率',
        type: 'line',
        yAxisIndex: 1,
        data: props.data.performanceTrend.map((item: any) => item.successRate),
        smooth: true,
        lineStyle: { color: '#67C23A' },
        itemStyle: { color: '#67C23A' }
      }
    ]
  }

  performanceTrendChartInstance.setOption(option)
}

const getSuccessRateType = (rate: number) => {
  if (rate >= 95) return 'success'
  if (rate >= 90) return 'warning'
  return 'danger'
}

const getErrorRateType = (rate: number) => {
  if (rate <= 1) return 'success'
  if (rate <= 5) return 'warning'
  return 'danger'
}

const refreshData = () => {
  emit('refresh')
}

const resizeCharts = () => {
  if (responseTimeChartInstance) {
    responseTimeChartInstance.resize()
  }
  if (performanceTrendChartInstance) {
    performanceTrendChartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
  
  window.addEventListener('resize', resizeCharts)
})

watch(() => props.data, () => {
  if (responseTimeChartInstance) {
    updateResponseTimeChart()
  }
  if (performanceTrendChartInstance) {
    updatePerformanceTrendChart()
  }
}, { deep: true })
</script>

<style scoped>
.performance-analysis-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.performance-content {
  padding: 10px 0;
}

.performance-overview {
  margin-bottom: 30px;
}

.response-time-distribution,
.provider-performance,
.performance-trend {
  margin-bottom: 30px;
}

.response-time-distribution h4,
.provider-performance h4,
.performance-trend h4,
.performance-suggestions h4 {
  margin-bottom: 15px;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.error-high {
  color: #F56C6C;
}

.error-normal {
  color: #67C23A;
}
</style>
