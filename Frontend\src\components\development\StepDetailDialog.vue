<template>
  <el-dialog
    v-model="dialogVisible"
    title="开发步骤详情"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="step" class="step-detail-container">
      <!-- 基本信息 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <div class="header-buttons">
              <el-button type="success" size="small" @click="decomposeStep">
                <el-icon><Operation /></el-icon>
                AI分解
              </el-button>
              <el-button type="primary" size="small" @click="editStep">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="步骤名称">{{ step.stepName }}</el-descriptions-item>
          <el-descriptions-item label="步骤类型">{{ step.stepType }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(step.priority) as any">{{ step.priority }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(step.status) as any">{{ step.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            <el-progress :percentage="step.progress" :stroke-width="8" />
          </el-descriptions-item>
          <el-descriptions-item label="技术栈">{{ step.technologyStack || '未指定' }}</el-descriptions-item>
          <el-descriptions-item label="预估工时">{{ step.estimatedHours || 0 }} 小时</el-descriptions-item>
          <el-descriptions-item label="实际工时">{{ step.actualHours || 0 }} 小时</el-descriptions-item>
          <el-descriptions-item label="文件路径" :span="2">{{ step.filePath || '未指定' }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div class="step-description">{{ step.stepDescription || '无描述' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- Tab 页签 -->
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 依赖关系 -->
        <el-tab-pane label="依赖关系" name="dependencies">
          <StepDependencyManager
            v-if="step.projectId"
            :step-id="step.id"
            :project-id="step.projectId"
            @refresh="$emit('refresh')"
          />
          <div v-else class="empty-state">
            <el-empty description="缺少项目信息，无法加载依赖关系" />
          </div>
        </el-tab-pane>

        <!-- 复杂度分析 -->
        <el-tab-pane label="复杂度分析" name="complexity">
          <StepComplexityAnalysis
            :step-id="step.id"
            :step="step"
          />
        </el-tab-pane>

        <!-- 执行历史 -->
        <el-tab-pane label="执行历史" name="history">
          <StepExecutionHistory
            :step-id="step.id"
          />
        </el-tab-pane>

        <!-- AI 提示词 -->
        <el-tab-pane label="AI 提示词" name="prompt">
          <div class="ai-prompt-container">
            <el-input
              v-model="step.aiPrompt"
              type="textarea"
              :rows="10"
              readonly
              placeholder="暂无 AI 提示词"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="success" @click="decomposeStep">AI分解</el-button>
        <el-button type="primary" @click="editStep">编辑步骤</el-button>
      </div>
    </template>

    <!-- AI分解对话框 -->
    <StepDecomposeDialog
      v-model="showDecomposeDialog"
      :step="step"
      @decompose-success="handleDecomposeSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Edit, Operation } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { DevelopmentStep } from '@/types/development'
import StepDependencyManager from './StepDependencyManager.vue'
import StepComplexityAnalysis from './StepComplexityAnalysis.vue'
import StepExecutionHistory from './StepExecutionHistory.vue'
import StepDecomposeDialog from './StepDecomposeDialog.vue'

// Props
interface Props {
  modelValue: boolean
  step?: DevelopmentStep
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  step: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
  'edit': [step: DevelopmentStep]
  'decompose-success': [result: any]
}>()

// 响应式数据
const activeTab = ref('dependencies')
const showDecomposeDialog = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听步骤变化，重置 tab
watch(() => props.step, () => {
  activeTab.value = 'dependencies'
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const editStep = () => {
  if (props.step) {
    emit('edit', props.step)
  }
}

const decomposeStep = () => {
  if (props.step) {
    showDecomposeDialog.value = true
  } else {
    ElMessage.warning('请先选择一个步骤')
  }
}

const handleDecomposeSuccess = (result: any) => {
  ElMessage.success(`步骤分解成功，生成 ${result.childSteps?.length || 0} 个子步骤`)
  showDecomposeDialog.value = false
  emit('decompose-success', result)
  emit('refresh')
}

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    'Critical': 'danger',
    'High': 'warning',
    'Medium': 'info',
    'Low': 'success'
  }
  return types[priority] || 'info'
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'Pending': 'info',
    'InProgress': 'warning',
    'Completed': 'success',
    'Failed': 'danger',
    'Blocked': 'danger'
  }
  return types[status] || 'info'
}
</script>

<style scoped>
.step-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.step-description {
  white-space: pre-wrap;
  word-break: break-word;
}

.ai-prompt-container {
  padding: 16px 0;
}

.dialog-footer {
  text-align: right;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
