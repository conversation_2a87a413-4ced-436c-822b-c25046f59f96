// API连接测试脚本
// 使用方法: node test-api-connection.js

const https = require('https');
const http = require('http');

// 测试配置
const configs = [
  { protocol: 'https', host: 'localhost', port: 61136, path: '/api/health' },
  { protocol: 'http', host: 'localhost', port: 61137, path: '/api/health' },
  { protocol: 'https', host: 'localhost', port: 61136, path: '/swagger' },
  { protocol: 'http', host: 'localhost', port: 61137, path: '/swagger' }
];

function testConnection(config) {
  return new Promise((resolve) => {
    const client = config.protocol === 'https' ? https : http;
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path,
      method: 'GET',
      timeout: 5000,
      rejectUnauthorized: false // 忽略SSL证书验证
    };

    const req = client.request(options, (res) => {
      resolve({
        ...config,
        status: res.statusCode,
        success: true,
        message: `连接成功 (${res.statusCode})`
      });
    });

    req.on('error', (err) => {
      resolve({
        ...config,
        status: null,
        success: false,
        message: err.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        ...config,
        status: null,
        success: false,
        message: '连接超时'
      });
    });

    req.end();
  });
}

async function runTests() {
  console.log('🔍 测试API连接...\n');

  for (const config of configs) {
    const url = `${config.protocol}://${config.host}:${config.port}${config.path}`;
    console.log(`测试: ${url}`);
    
    const result = await testConnection(config);
    
    if (result.success) {
      console.log(`✅ ${result.message}`);
    } else {
      console.log(`❌ ${result.message}`);
    }
    console.log('');
  }

  console.log('📋 建议:');
  console.log('1. 如果HTTPS连接成功，使用: VITE_API_BASE_URL=https://localhost:61136');
  console.log('2. 如果只有HTTP连接成功，使用: VITE_API_BASE_URL=http://localhost:61137');
  console.log('3. 如果都失败，请检查后端服务是否启动');
}

runTests().catch(console.error);
