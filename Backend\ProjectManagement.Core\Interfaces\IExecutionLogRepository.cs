using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// 执行日志仓储接口
    /// </summary>
    public interface IExecutionLogRepository : IRepository<UIAutoMationTemplateExecutionLog>
    {
        /// <summary>
        /// 分页查询执行日志
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResultDto<UIAutoMationTemplateExecutionLog>> GetPagedAsync(ExecutionLogQueryDto query);

        /// <summary>
        /// 根据序列ID获取执行日志
        /// </summary>
        /// <param name="sequenceId">序列ID</param>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetBySequenceIdAsync(int sequenceId, int count = 50);

        /// <summary>
        /// 根据模板ID获取执行日志
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetByTemplateIdAsync(int templateId, int count = 50);

        /// <summary>
        /// 根据步骤ID获取执行日志
        /// </summary>
        /// <param name="stepId">步骤ID</param>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetByStepIdAsync(int stepId, int count = 50);

        /// <summary>
        /// 获取最近的执行日志
        /// </summary>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetRecentAsync(int count = 20);

        /// <summary>
        /// 根据执行者获取执行日志
        /// </summary>
        /// <param name="executedBy">执行者</param>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetByExecutorAsync(string executedBy, int count = 50);

        /// <summary>
        /// 根据状态获取执行日志
        /// </summary>
        /// <param name="status">执行状态</param>
        /// <param name="count">数量限制</param>
        /// <returns>执行日志列表</returns>
        Task<List<UIAutoMationTemplateExecutionLog>> GetByStatusAsync(string status, int count = 50);

        /// <summary>
        /// 获取执行统计信息
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计信息</returns>
        Task<ExecutionStatisticsDto> GetStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 更新执行状态
        /// </summary>
        /// <param name="id">日志ID</param>
        /// <param name="status">新状态</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="result">执行结果</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateStatusAsync(int id, string status, DateTime? endTime = null, 
            string? result = null, string? errorMessage = null);

        /// <summary>
        /// 记录执行开始
        /// </summary>
        /// <param name="executionType">执行类型</param>
        /// <param name="sequenceId">序列ID</param>
        /// <param name="templateId">模板ID</param>
        /// <param name="stepId">步骤ID</param>
        /// <param name="executedBy">执行者</param>
        /// <returns>日志ID</returns>
        Task<int> LogExecutionStartAsync(string executionType, int? sequenceId = null, 
            int? templateId = null, int? stepId = null, string? executedBy = null);

        /// <summary>
        /// 记录执行完成
        /// </summary>
        /// <param name="id">日志ID</param>
        /// <param name="result">执行结果</param>
        /// <param name="screenshotPath">截图路径</param>
        /// <returns>是否成功</returns>
        Task<bool> LogExecutionCompletedAsync(int id, string? result = null, string? screenshotPath = null);

        /// <summary>
        /// 记录执行失败
        /// </summary>
        /// <param name="id">日志ID</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="screenshotPath">截图路径</param>
        /// <returns>是否成功</returns>
        Task<bool> LogExecutionFailedAsync(int id, string errorMessage, string? screenshotPath = null);

        /// <summary>
        /// 清理旧日志
        /// </summary>
        /// <param name="daysToKeep">保留天数</param>
        /// <returns>清理的记录数</returns>
        Task<int> CleanupOldLogsAsync(int daysToKeep = 30);
    }

    /// <summary>
    /// 执行统计信息DTO
    /// </summary>
    public class ExecutionStatisticsDto
    {
        /// <summary>
        /// 总执行次数
        /// </summary>
        public int TotalExecutions { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessfulExecutions { get; set; }

        /// <summary>
        /// 失败次数
        /// </summary>
        public int FailedExecutions { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 平均执行时间（毫秒）
        /// </summary>
        public double AverageExecutionTime { get; set; }

        /// <summary>
        /// 按状态分组的统计
        /// </summary>
        public Dictionary<string, int> StatusStats { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 按执行类型分组的统计
        /// </summary>
        public Dictionary<string, int> TypeStats { get; set; } = new Dictionary<string, int>();
    }
}
