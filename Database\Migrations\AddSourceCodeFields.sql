-- 为 UIAutomationTemplateSequences 表添加原生代码字段
-- 执行日期: 2025-06-27
-- 目的: 直接保存用户编写的原生代码，便于编辑和管理

USE [ProjectManagementAI]
GO

-- 检查字段是否已存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UIAutomationTemplateSequences]') AND name = 'SourceCode')
BEGIN
    ALTER TABLE [dbo].[UIAutomationTemplateSequences]
    ADD [SourceCode] NVARCHAR(MAX) NULL
    
    PRINT '已添加 SourceCode 字段'
END
ELSE
BEGIN
    PRINT 'SourceCode 字段已存在'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UIAutomationTemplateSequences]') AND name = 'CodeLanguage')
BEGIN
    ALTER TABLE [dbo].[UIAutomationTemplateSequences]
    ADD [CodeLanguage] NVARCHAR(50) NULL DEFAULT 'javascript'
    
    PRINT '已添加 CodeLanguage 字段'
END
ELSE
BEGIN
    PRINT 'CodeLanguage 字段已存在'
END

-- 为新字段添加注释
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UIAutomationTemplateSequences]') AND name = 'SourceCode')
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'用户编写的原生代码（JavaScript、Python等）', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutomationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'SourceCode'
END

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[UIAutomationTemplateSequences]') AND name = 'CodeLanguage')
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'代码语言类型（javascript、python、typescript等）', 
        @level0type = N'SCHEMA', @level0name = N'dbo', 
        @level1type = N'TABLE', @level1name = N'UIAutomationTemplateSequences', 
        @level2type = N'COLUMN', @level2name = N'CodeLanguage'
END

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UIAutomationTemplateSequences]') AND name = 'IX_UIAutomationTemplateSequences_CodeLanguage')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_UIAutomationTemplateSequences_CodeLanguage]
    ON [dbo].[UIAutomationTemplateSequences] ([CodeLanguage])
    WHERE [CodeLanguage] IS NOT NULL
    
    PRINT '已创建 CodeLanguage 索引'
END

-- 显示表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'UIAutomationTemplateSequences' 
    AND COLUMN_NAME IN ('SourceCode', 'CodeLanguage')
ORDER BY ORDINAL_POSITION

PRINT '数据库迁移完成！'
