-- =============================================
-- 项目管理AI系统数据库字段中文注释脚本 - 第四部分（最后部分）
-- 为剩余表的字段添加中文描述注释
-- 版本: 1.0
-- 创建日期: 2024-12-19
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 15. IssueResolutions表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'解决记录唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联的问题ID',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'IssueId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'解决类型：AutoFixed=AI自动修复, ManualFixed=手动修复, CodeGenerated=代码生成, TestAdded=添加测试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'ResolutionType';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'解决方案详细描述',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'ResolutionDescription';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'AI生成的修复代码（如果适用）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'GeneratedCode';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'解决者：AI=AI系统, User=用户手动',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'ResolvedBy';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'解决记录创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions',
    @level2type = N'Column', @level2name = N'CreatedAt';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'问题解决方案记录表，记录问题的解决过程和方案，支持AI自动修复，支持多种解决方式、代码生成、解决历史追踪',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'IssueResolutions';
GO

-- =============================================
-- 16. WorkflowStates表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'工作流状态唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'当前阶段：RequirementGathering=需求收集, DocumentGeneration=文档生成, DiagramGeneration=图表生成, CodeGeneration=代码生成, Testing=测试, Deployment=部署, IssueHandling=问题处理',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'CurrentStage';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'阶段状态：Pending=待开始, InProgress=进行中, Completed=已完成, Failed=失败',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'StageStatus';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'阶段相关数据，JSON格式存储',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'StageData';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'阶段开始时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'StartedAt';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'阶段完成时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates',
    @level2type = N'Column', @level2name = N'CompletedAt';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'项目工作流管理表，跟踪项目在AI驱动开发流程中的当前阶段，支持阶段状态跟踪、流程数据存储、时间记录',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'WorkflowStates';
GO

-- =============================================
-- 17. SystemLogs表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志记录唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联的项目ID，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联的用户ID，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'UserId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志级别：Info=信息, Warning=警告, Error=错误, Debug=调试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'LogLevel';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'组件名称：AI Service=AI服务, Code Generator=代码生成器, Test Runner=测试运行器等，长度100字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'Component';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志消息内容',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'Message';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'异常信息（如果有）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'Exception';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'附加数据，JSON格式存储',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'AdditionalData';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs',
    @level2type = N'Column', @level2name = N'CreatedAt';

-- 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'系统运行日志管理表，记录系统运行过程中的各种日志信息，支持多级别日志、组件分类、异常记录、数据追踪',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'SystemLogs';
GO

-- =============================================
-- 脚本完成信息
-- =============================================
PRINT '==============================================';
PRINT '数据库字段中文注释添加完成！';
PRINT '==============================================';
PRINT '';
PRINT '已为以下17个表的所有字段添加中文注释：';
PRINT '1. Users - 用户表';
PRINT '2. Projects - 项目表';
PRINT '3. AIModelConfigurations - AI模型配置表';
PRINT '4. UserAIConfigurations - 用户AI配置表';
PRINT '5. UserTaskMappings - 用户任务映射表';
PRINT '6. RequirementConversations - 需求对话记录表';
PRINT '7. RequirementDocuments - 需求文档表';
PRINT '8. ERDiagrams - ER图表';
PRINT '9. ContextDiagrams - 上下文图表';
PRINT '10. CodeGenerationTasks - 代码生成任务表';
PRINT '11. GeneratedCodeFiles - 生成的代码文件表';
PRINT '12. TestTasks - 测试任务表';
PRINT '13. DeploymentTasks - 部署任务表';
PRINT '14. Issues - 问题表';
PRINT '15. IssueResolutions - 问题解决记录表';
PRINT '16. WorkflowStates - 工作流状态表';
PRINT '17. SystemLogs - 系统日志表';
PRINT '';
PRINT '注释内容包括：';
PRINT '- 字段用途和含义的详细中文描述';
PRINT '- 字段数据类型和长度说明';
PRINT '- 枚举值的中文对照';
PRINT '- 外键关联关系说明';
PRINT '- 业务规则和约束说明';
PRINT '';
PRINT '使用说明：';
PRINT '1. 在SQL Server Management Studio中可以查看字段注释';
PRINT '2. 注释信息有助于数据库维护和开发';
PRINT '3. 可以通过系统视图查询注释信息';
PRINT '4. 建议在修改表结构时同步更新注释';
PRINT '';
PRINT '脚本执行完成时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '==============================================';
GO
