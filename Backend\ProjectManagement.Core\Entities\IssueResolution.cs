using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Issue处理记录实体 - 对应DatabaseSchema.sql中的IssueResolutions表
/// 功能: 记录问题的解决过程和方案，支持AI自动修复
/// 支持: 多种解决方式、代码生成、解决历史追踪
/// </summary>
[SugarTable("IssueResolutions", "问题解决方案记录表")]
public class IssueResolution : BaseEntity
{

    /// <summary>
    /// 关联的问题ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的问题ID")]
    public int IssueId { get; set; }

    /// <summary>
    /// 解决类型: AutoFixed(AI自动修复), ManualFixed(手动修复), CodeGenerated(代码生成), TestAdded(添加测试)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "解决类型")]
    public string? ResolutionType { get; set; }

    /// <summary>
    /// 解决方案详细描述
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "解决方案详细描述")]
    public string? ResolutionDescription { get; set; }

    /// <summary>
    /// AI生成的修复代码（如果适用）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI生成的修复代码")]
    public string? GeneratedCode { get; set; }

    /// <summary>
    /// 解决者: AI(AI系统), User(用户手动)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "解决者")]
    public string? ResolvedBy { get; set; }

    /// <summary>
    /// 关联的问题 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Issue? Issue { get; set; }
}
