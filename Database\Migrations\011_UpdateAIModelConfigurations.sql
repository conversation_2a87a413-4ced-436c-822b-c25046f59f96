
-- =============================================
-- 迁移脚本: 更新AIModelConfigurations表结构
-- 版本: 011
-- 日期: 2025-01-02
-- 描述: 删除ModelType字段，添加UserId字段
-- =============================================

USE [ProjectManagementAI]
GO


PRINT '开始执行AIModelConfigurations表结构更新...'

-- 检查表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AIModelConfigurations')
BEGIN
    PRINT '错误: AIModelConfigurations表不存在'
    RETURN
END

-- 1. 添加UserId字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AIModelConfigurations') AND name = 'UserId')
BEGIN
    PRINT '添加UserId字段...'
    ALTER TABLE AIModelConfigurations 
    ADD UserId int NULL
    
    -- 添加外键约束
    IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
    BEGIN
        ALTER TABLE AIModelConfigurations
        ADD CONSTRAINT FK_AIModelConfigurations_Users 
        FOREIGN KEY (UserId) REFERENCES Users(Id)
        ON DELETE CASCADE
        
        PRINT 'UserId外键约束添加成功'
    END
    ELSE
    BEGIN
        PRINT '警告: Users表不存在，跳过外键约束创建'
    END
    
    PRINT 'UserId字段添加成功'
END
ELSE
BEGIN
    PRINT 'UserId字段已存在，跳过添加'
END

-- 2. 删除ModelType字段（如果存在）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('AIModelConfigurations') AND name = 'ModelType')
BEGIN
    PRINT '删除ModelType字段...'
    
    -- 首先删除相关的检查约束
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_AIModelConfigurations_ModelType')
    BEGIN
        ALTER TABLE AIModelConfigurations 
        DROP CONSTRAINT CK_AIModelConfigurations_ModelType
        PRINT 'ModelType检查约束已删除'
    END
    
	drop index AIModelConfigurations.IX_AIModelConfigurations_ModelType
    -- 删除字段
    ALTER TABLE AIModelConfigurations 
    DROP COLUMN ModelType
    
    PRINT 'ModelType字段删除成功'
END
ELSE
BEGIN
    PRINT 'ModelType字段不存在，跳过删除'
END

-- 3. 更新现有数据（将现有记录设置为系统级配置）
UPDATE AIModelConfigurations 
SET UserId = NULL 
WHERE UserId IS NOT NULL

PRINT '现有数据已更新为系统级配置'

-- 4. 验证表结构
PRINT '验证表结构...'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'AIModelConfigurations'
ORDER BY ORDINAL_POSITION

PRINT 'AIModelConfigurations表结构更新完成'
PRINT '============================================='
