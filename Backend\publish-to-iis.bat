@echo off
echo ========================================
echo 发布 ProjectManagement.API 到远程 IIS
echo ========================================

cd /d "%~dp0"
cd ProjectManagement.API

echo 正在清理之前的发布文件...
if exist "bin\Release\net8.0\publish" rmdir /s /q "bin\Release\net8.0\publish"

echo 正在发布项目...
dotnet publish -c Release -o "bin\Release\net8.0\publish" --self-contained false

if %ERRORLEVEL% NEQ 0 (
    echo 发布失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 检查发布文件完整性
echo ========================================

set PUBLISH_DIR=%CD%\bin\Release\net8.0\publish

echo 检查关键文件:
if exist "%PUBLISH_DIR%\ProjectManagement.API.dll" (
    echo ✅ ProjectManagement.API.dll
) else (
    echo ❌ ProjectManagement.API.dll 缺失
)

if exist "%PUBLISH_DIR%\web.config" (
    echo ✅ web.config
) else (
    echo ❌ web.config 缺失
)

if exist "%PUBLISH_DIR%\appsettings.json" (
    echo ✅ appsettings.json
) else (
    echo ❌ appsettings.json 缺失
)

if exist "%PUBLISH_DIR%\appsettings.Production.json" (
    echo ✅ appsettings.Production.json
) else (
    echo ❌ appsettings.Production.json 缺失
)

if exist "%PUBLISH_DIR%\wwwroot" (
    echo ✅ wwwroot 文件夹
) else (
    echo ❌ wwwroot 文件夹缺失
)

echo.
echo ========================================
echo 发布成功！
echo ========================================
echo 发布目录: %PUBLISH_DIR%
echo.
echo 📋 部署到IIS服务器的步骤:
echo 1. 将发布目录中的所有文件复制到 D:\AI\Backend\ 目录
echo 2. 将项目根目录的 index.html 复制到 D:\AI\ 目录
echo 3. 确保服务器已安装 ASP.NET Core Hosting Bundle
echo 4. 确保应用程序池设置为 "无托管代码"
echo 5. 重启IIS应用程序池
echo.
echo 🌐 访问地址:
echo - IIS默认页面: http://localhost:62573/index.html (自动重定向到Swagger)
echo - 后端根路径: http://localhost:62573/backend/
echo - API文档: http://localhost:62573/backend/swagger/index.html
echo - 健康检查: http://localhost:62573/backend/health
echo.
echo 📁 发布文件列表:
dir "%PUBLISH_DIR%" /b
echo.
pause
