using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// Prompt使用统计实体 - 记录提示词模板的使用情况
/// 功能: 统计模板使用频率、效果、性能等数据
/// 支持: 使用分析、效果评估、优化建议
/// </summary>
[SugarTable("PromptUsageStats", "AI提示词使用统计表")]
public class PromptUsageStats : BaseEntity
{
    /// <summary>
    /// 关联的模板ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "关联的模板ID")]
    public int TemplateId { get; set; }

    /// <summary>
    /// 使用者用户ID
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "使用者用户ID")]
    public int UserId { get; set; }

    /// <summary>
    /// 关联的项目ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的项目ID")]
    public int? ProjectId { get; set; }

    /// <summary>
    /// 使用的AI提供商
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "使用的AI提供商")]
    public string AIProvider { get; set; } = string.Empty;

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "使用的AI模型")]
    public string AIModel { get; set; } = string.Empty;

    /// <summary>
    /// 输入参数，JSON格式
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "输入参数")]
    public string? InputParameters { get; set; }

    /// <summary>
    /// 生成的最终提示词
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "生成的最终提示词")]
    public string GeneratedPrompt { get; set; } = string.Empty;

    /// <summary>
    /// AI响应内容（截取前1000字符）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "AI响应内容")]
    public string? AIResponse { get; set; }

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "响应时间（毫秒）")]
    public int? ResponseTimeMs { get; set; }

    /// <summary>
    /// Token使用量
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "Token使用量")]
    public int? TokenUsage { get; set; }

    /// <summary>
    /// 成本（美元）
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "成本（美元）")]
    public decimal? Cost { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "是否成功")]
    public bool IsSuccess { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true, ColumnDescription = "错误信息")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 使用时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "使用时间")]
    public DateTime UsedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的模板 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PromptTemplate? Template { get; set; }

    /// <summary>
    /// 使用者 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? User { get; set; }

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }
}
