using System;
using System.IO;
using Newtonsoft.Json;

namespace BuildMonitor.Services
{
    /// <summary>
    /// 用户设置服务，替代 Properties.Settings
    /// </summary>
    public class UserSettingsService
    {
        private readonly string _settingsFilePath;
        private UserSettings _settings;

        public UserSettingsService()
        {
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "BuildMonitor"
            );
            
            Directory.CreateDirectory(appDataPath);
            _settingsFilePath = Path.Combine(appDataPath, "user_settings.json");
            
            LoadSettings();
        }

        public string RememberedUsername
        {
            get => _settings.RememberedUsername ?? string.Empty;
            set
            {
                _settings.RememberedUsername = value;
                SaveSettings();
            }
        }

        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    _settings = JsonConvert.DeserializeObject<UserSettings>(json) ?? new UserSettings();
                }
                else
                {
                    _settings = new UserSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load user settings: {ex.Message}");
                _settings = new UserSettings();
            }
        }

        private void SaveSettings()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                File.WriteAllText(_settingsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save user settings: {ex.Message}");
            }
        }

        public void ClearRememberedUsername()
        {
            RememberedUsername = string.Empty;
        }
    }

    /// <summary>
    /// 用户设置数据模型
    /// </summary>
    public class UserSettings
    {
        public string? RememberedUsername { get; set; }
    }
}
