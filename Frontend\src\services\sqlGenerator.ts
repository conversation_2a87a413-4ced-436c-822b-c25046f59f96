/**
 * SQL生成服务
 * 从Mermaid ER图解析并生成SQL脚本
 */

export interface TableColumn {
  name: string
  type: string
  length?: number
  nullable: boolean
  primaryKey: boolean
  foreignKey?: {
    table: string
    column: string
  }
  defaultValue?: string
  comment?: string
}

export interface TableInfo {
  name: string
  columns: TableColumn[]
  indexes?: string[]
  comment?: string
}

export interface SQLGenerationOptions {
  databaseType: 'SqlServer' | 'MySQL' | 'PostgreSQL' | 'Oracle' | 'SQLite'
  includeDropStatements: boolean
  includeComments: boolean
  includeIndexes: boolean
  includeForeignKeys: boolean
  generateChineseComments?: boolean // 新增：是否生成中文注释
  tablePrefix?: string
  charset?: string
}

export class SQLGenerator {
  /**
   * 从Mermaid ER图定义解析表结构
   */
  static parseMermaidER(mermaidDefinition: string): TableInfo[] {
    const tables: TableInfo[] = []

    try {
      // 解析Mermaid ER图语法
      const lines = mermaidDefinition.split('\n').map(line => line.trim()).filter(line => line)

      let currentTable: TableInfo | null = null

      for (const line of lines) {
        // 跳过注释和空行
        if (line.startsWith('%%') || line.startsWith('erDiagram') || !line) {
          continue
        }

        // 解析表定义
        if (line.includes('{') && !line.includes('}')) {
          // 表开始
          const tableName = line.replace('{', '').trim()
          currentTable = {
            name: tableName,
            columns: [],
            comment: `${tableName}表`
          }
        } else if (line === '}' && currentTable) {
          // 表结束
          tables.push(currentTable)
          currentTable = null
        } else if (currentTable && line.includes(' ')) {
          // 解析字段定义
          const column = this.parseColumnDefinition(line)
          if (column) {
            currentTable.columns.push(column)
          }
        } else if (line.includes('||--||') || line.includes('}|--||') || line.includes('||--o{')) {
          // 解析关系（暂时跳过，后续可以用来生成外键）
          continue
        }
      }

      // 处理单行表定义（如果有的话）
      if (tables.length === 0) {
        // 尝试解析简化的ER图格式
        tables.push(...this.parseSimplifiedER(mermaidDefinition))
      }

    } catch (error) {
      console.error('解析Mermaid ER图失败:', error)
    }

    return tables
  }

  /**
   * 解析字段定义
   */
  private static parseColumnDefinition(line: string): TableColumn | null {
    try {
      // 示例格式: "int id PK" 或 "varchar name"
      const parts = line.trim().split(/\s+/)
      if (parts.length < 2) return null

      const type = parts[0]
      const name = parts[1]
      const modifiers = parts.slice(2)

      const column: TableColumn = {
        name: name,
        type: this.normalizeDataType(type),
        nullable: !modifiers.includes('NOT_NULL') && !modifiers.includes('PK'),
        primaryKey: modifiers.includes('PK') || modifiers.includes('PRIMARY_KEY')
      }

      // 解析长度
      const lengthMatch = type.match(/\((\d+)\)/)
      if (lengthMatch) {
        column.length = parseInt(lengthMatch[1])
        column.type = type.replace(/\(\d+\)/, '')
      }

      // 解析外键
      if (modifiers.includes('FK')) {
        // 简化处理，实际应该从关系定义中解析
        column.foreignKey = {
          table: 'referenced_table',
          column: 'id'
        }
      }

      return column
    } catch (error) {
      console.error('解析字段定义失败:', line, error)
      return null
    }
  }

  /**
   * 解析简化的ER图格式
   */
  private static parseSimplifiedER(mermaidDefinition: string): TableInfo[] {
    const tables: TableInfo[] = []

    // 这里可以添加对其他ER图格式的支持
    // 例如从关系定义中推断表结构

    return tables
  }

  /**
   * 标准化数据类型
   */
  private static normalizeDataType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'int': 'int',
      'integer': 'int',
      'varchar': 'varchar',
      'string': 'varchar',
      'text': 'text',
      'datetime': 'datetime',
      'date': 'date',
      'time': 'time',
      'boolean': 'bit',
      'bool': 'bit',
      'decimal': 'decimal',
      'float': 'float',
      'double': 'float'
    }

    return typeMap[type.toLowerCase()] || type
  }

  /**
   * 生成SQL脚本
   */
  static generateSQL(tables: TableInfo[], options: SQLGenerationOptions): string {
    let sql = ''

    // 添加头部注释
    sql += `-- SQL脚本自动生成\n`
    sql += `-- 数据库类型: ${options.databaseType}\n`
    sql += `-- 生成时间: ${new Date().toLocaleString()}\n\n`

    // 生成DROP语句
    if (options.includeDropStatements) {
      sql += this.generateDropStatements(tables, options)
      sql += '\n'
    }

    // 生成CREATE TABLE语句
    for (const table of tables) {
      sql += this.generateCreateTableSQL(table, options)
      sql += '\n'
    }

    // 生成索引
    if (options.includeIndexes) {
      sql += this.generateIndexSQL(tables, options)
      sql += '\n'
    }

    // 生成外键约束
    if (options.includeForeignKeys) {
      sql += this.generateForeignKeySQL(tables, options)
      sql += '\n'
    }

    return sql
  }

  /**
   * 生成DROP语句
   */
  private static generateDropStatements(tables: TableInfo[], options: SQLGenerationOptions): string {
    let sql = '-- 删除表（如果存在）\n'

    // 反向删除以避免外键约束问题
    for (let i = tables.length - 1; i >= 0; i--) {
      const table = tables[i]
      const tableName = options.tablePrefix ? `${options.tablePrefix}${table.name}` : table.name

      switch (options.databaseType) {
        case 'SqlServer':
          sql += `IF OBJECT_ID('${tableName}', 'U') IS NOT NULL DROP TABLE [${tableName}];\n`
          break
        case 'MySQL':
          sql += `DROP TABLE IF EXISTS \`${tableName}\`;\n`
          break
        case 'PostgreSQL':
          sql += `DROP TABLE IF EXISTS "${tableName}" CASCADE;\n`
          break
        default:
          sql += `DROP TABLE IF EXISTS ${tableName};\n`
      }
    }

    return sql + '\n'
  }

  /**
   * 生成CREATE TABLE语句
   */
  private static generateCreateTableSQL(table: TableInfo, options: SQLGenerationOptions): string {
    const tableName = options.tablePrefix ? `${options.tablePrefix}${table.name}` : table.name
    let sql = ''

    // 表注释
    if (options.includeComments && table.comment) {
      sql += `-- ${table.comment}\n`
    }

    // CREATE TABLE开始
    switch (options.databaseType) {
      case 'SqlServer':
        sql += `CREATE TABLE [${tableName}] (\n`
        break
      case 'MySQL':
        sql += `CREATE TABLE \`${tableName}\` (\n`
        break
      case 'PostgreSQL':
        sql += `CREATE TABLE "${tableName}" (\n`
        break
      default:
        sql += `CREATE TABLE ${tableName} (\n`
    }

    // 字段定义
    const columnDefinitions = table.columns.map(column => {
      return '    ' + this.generateColumnDefinition(column, options)
    })

    sql += columnDefinitions.join(',\n')

    // 主键约束
    const primaryKeys = table.columns.filter(col => col.primaryKey).map(col => col.name)
    if (primaryKeys.length > 0) {
      sql += ',\n    ' + this.generatePrimaryKeyConstraint(primaryKeys, options)
    }

    sql += '\n)'

    // 表选项
    if (options.databaseType === 'MySQL' && options.charset) {
      sql += ` ENGINE=InnoDB DEFAULT CHARSET=${options.charset}`
    }

    sql += ';\n'

    return sql
  }

  /**
   * 生成字段定义
   */
  private static generateColumnDefinition(column: TableColumn, options: SQLGenerationOptions): string {
    let definition = ''

    // 字段名
    switch (options.databaseType) {
      case 'SqlServer':
        definition += `[${column.name}]`
        break
      case 'MySQL':
        definition += `\`${column.name}\``
        break
      case 'PostgreSQL':
        definition += `"${column.name}"`
        break
      default:
        definition += column.name
    }

    // 数据类型
    definition += ' ' + this.getDataTypeSQL(column, options)

    // NULL约束
    definition += column.nullable ? ' NULL' : ' NOT NULL'

    // 默认值
    if (column.defaultValue) {
      definition += ` DEFAULT ${column.defaultValue}`
    }

    // 字段注释
    if (options.includeComments && column.comment) {
      if (options.databaseType === 'MySQL') {
        definition += ` COMMENT '${column.comment}'`
      }
    }

    return definition
  }

  /**
   * 获取数据类型SQL
   */
  private static getDataTypeSQL(column: TableColumn, options: SQLGenerationOptions): string {
    let type = column.type

    // 根据数据库类型调整
    switch (options.databaseType) {
      case 'SqlServer':
        if (type === 'varchar' && column.length) {
          return `NVARCHAR(${column.length})`
        }
        if (type === 'text') return 'NVARCHAR(MAX)'
        if (type === 'bit') return 'BIT'
        break
      case 'MySQL':
        if (type === 'varchar' && column.length) {
          return `VARCHAR(${column.length})`
        }
        if (type === 'bit') return 'TINYINT(1)'
        break
      case 'PostgreSQL':
        if (type === 'varchar' && column.length) {
          return `VARCHAR(${column.length})`
        }
        if (type === 'bit') return 'BOOLEAN'
        break
    }

    // 添加长度
    if (column.length && ['varchar', 'char', 'decimal'].includes(type)) {
      return `${type.toUpperCase()}(${column.length})`
    }

    return type.toUpperCase()
  }

  /**
   * 生成主键约束
   */
  private static generatePrimaryKeyConstraint(primaryKeys: string[], options: SQLGenerationOptions): string {
    const keyList = primaryKeys.map(key => {
      switch (options.databaseType) {
        case 'SqlServer':
          return `[${key}]`
        case 'MySQL':
          return `\`${key}\``
        case 'PostgreSQL':
          return `"${key}"`
        default:
          return key
      }
    }).join(', ')

    return `PRIMARY KEY (${keyList})`
  }

  /**
   * 生成索引SQL
   */
  private static generateIndexSQL(tables: TableInfo[], options: SQLGenerationOptions): string {
    let sql = '-- 创建索引\n'

    for (const table of tables) {
      if (table.indexes) {
        for (const index of table.indexes) {
          sql += `CREATE INDEX IX_${table.name}_${index} ON ${table.name} (${index});\n`
        }
      }
    }

    return sql
  }

  /**
   * 生成外键SQL
   */
  private static generateForeignKeySQL(tables: TableInfo[], options: SQLGenerationOptions): string {
    let sql = '-- 创建外键约束\n'

    for (const table of tables) {
      for (const column of table.columns) {
        if (column.foreignKey) {
          const tableName = options.tablePrefix ? `${options.tablePrefix}${table.name}` : table.name
          const refTableName = options.tablePrefix ? `${options.tablePrefix}${column.foreignKey.table}` : column.foreignKey.table

          sql += `ALTER TABLE ${tableName} ADD CONSTRAINT FK_${table.name}_${column.name} `
          sql += `FOREIGN KEY (${column.name}) REFERENCES ${refTableName}(${column.foreignKey.column});\n`
        }
      }
    }

    return sql
  }
}
