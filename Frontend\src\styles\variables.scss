// 主题色彩
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #C0C4CC;

// 背景色
$bg-color: #FFFFFF;
$bg-color-page: #F2F3F5;
$bg-color-overlay: #FFFFFF;

// 边框颜色
$border-color: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 50%;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 字体
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

$font-weight-primary: 500;
$font-weight-secondary: 100;

// 层级
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// 断点
$sm: 768px;
$md: 992px;
$lg: 1200px;
$xl: 1920px;

// 侧边栏
$sidebar-width: 240px;
$sidebar-width-collapsed: 64px;

// 头部
$header-height: 60px;

// 面包屑
$breadcrumb-height: 50px;

// 项目状态颜色
$status-planning: #909399;
$status-in-progress: #409EFF;
$status-testing: #E6A23C;
$status-deployed: #67C23A;
$status-completed: #67C23A;
$status-paused: #F56C6C;
$status-cancelled: #909399;

// 优先级颜色
$priority-low: #67C23A;
$priority-medium: #E6A23C;
$priority-high: #F56C6C;
$priority-critical: #F56C6C;

// 动画时间
$transition-base: 0.3s;
$transition-fast: 0.2s;
$transition-slow: 0.5s;
