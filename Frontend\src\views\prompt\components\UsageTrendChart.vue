<template>
  <div class="usage-trend-chart">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  data: any[]
  metric: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !props.data) return

  const option = {
    title: {
      text: getChartTitle(),
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      top: 30,
      data: getLegendData()
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      name: getYAxisName()
    },
    series: getSeriesData()
  }

  chartInstance.setOption(option)
}

const getChartTitle = () => {
  const titles: Record<string, string> = {
    usage: '使用量趋势',
    successRate: '成功率趋势',
    responseTime: '响应时间趋势'
  }
  return titles[props.metric] || '趋势图'
}

const getYAxisName = () => {
  const names: Record<string, string> = {
    usage: '使用次数',
    successRate: '成功率(%)',
    responseTime: '响应时间(ms)'
  }
  return names[props.metric] || ''
}

const getLegendData = () => {
  if (props.metric === 'usage') {
    return ['总使用量', '成功次数', '失败次数']
  } else if (props.metric === 'successRate') {
    return ['成功率']
  } else if (props.metric === 'responseTime') {
    return ['平均响应时间', '最大响应时间']
  }
  return []
}

const getSeriesData = () => {
  if (props.metric === 'usage') {
    return [
      {
        name: '总使用量',
        type: 'line',
        data: props.data.map(item => item.totalUsage || 0),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '成功次数',
        type: 'line',
        data: props.data.map(item => item.successCount || 0),
        smooth: true,
        lineStyle: { color: '#67C23A' },
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '失败次数',
        type: 'line',
        data: props.data.map(item => item.failureCount || 0),
        smooth: true,
        lineStyle: { color: '#F56C6C' },
        itemStyle: { color: '#F56C6C' }
      }
    ]
  } else if (props.metric === 'successRate') {
    return [
      {
        name: '成功率',
        type: 'line',
        data: props.data.map(item => item.successRate || 0),
        smooth: true,
        lineStyle: { color: '#67C23A' },
        itemStyle: { color: '#67C23A' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ]
          }
        }
      }
    ]
  } else if (props.metric === 'responseTime') {
    return [
      {
        name: '平均响应时间',
        type: 'line',
        data: props.data.map(item => item.avgResponseTime || 0),
        smooth: true,
        lineStyle: { color: '#E6A23C' },
        itemStyle: { color: '#E6A23C' }
      },
      {
        name: '最大响应时间',
        type: 'line',
        data: props.data.map(item => item.maxResponseTime || 0),
        smooth: true,
        lineStyle: { color: '#F56C6C' },
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }
  return []
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
  
  window.addEventListener('resize', resizeChart)
})

watch([() => props.data, () => props.metric], () => {
  if (chartInstance) {
    updateChart()
  }
}, { deep: true })

watch(() => props.loading, (newLoading) => {
  if (!newLoading && chartContainer.value && !chartInstance) {
    nextTick(() => {
      initChart()
    })
  }
})
</script>

<style scoped>
.usage-trend-chart {
  width: 100%;
  height: 400px;
}

.loading-container {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
