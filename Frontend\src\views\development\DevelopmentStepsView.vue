<template>
  <div class="development-steps-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><List /></el-icon>
            开发步骤
          </h1>
          <p class="page-description" v-if="!selectedRequirementId">
            查看和管理所有项目的开发步骤
          </p>
          <p class="page-description" v-else>
            查看需求 #{{ selectedRequirementId }} 的开发步骤
          </p>
        </div>
        <div class="header-actions">
          <el-select
            v-model="selectedProjectId"
            placeholder="选择项目"
            style="width: 200px"
            @change="handleProjectChange"
            clearable
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>

          <el-button type="primary" @click="showDecomposeDialog = true">
            <el-icon><Plus /></el-icon>
            分解需求
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <el-card shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索步骤名称或描述..."
              :prefix-icon="Search"
              style="width: 300px"
              @input="handleSearch"
              clearable
            />

            <el-select v-model="filterStatus" placeholder="状态" clearable style="width: 120px">
              <el-option label="待处理" value="Pending" />
              <el-option label="进行中" value="InProgress" />
              <el-option label="已完成" value="Completed" />
              <el-option label="失败" value="Failed" />
              <el-option label="阻塞" value="Blocked" />
            </el-select>

            <el-select v-model="filterPriority" placeholder="优先级" clearable style="width: 120px">
              <el-option label="低" value="Low" />
              <el-option label="中" value="Medium" />
              <el-option label="高" value="High" />
              <el-option label="紧急" value="Critical" />
            </el-select>

            <el-select v-model="filterStepType" placeholder="类型" clearable style="width: 120px">
              <el-option label="开发" value="Development" />
              <el-option label="测试" value="Testing" />
              <el-option label="文档" value="Documentation" />
              <el-option label="部署" value="Deployment" />
              <el-option label="审查" value="Review" />
            </el-select>
          </div>

          <div class="filter-right">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="list">列表</el-radio-button>
              <el-radio-button label="card">卡片</el-radio-button>
              <el-radio-button label="tree">树形</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 步骤内容 -->
    <div class="steps-content">
      <el-card shadow="never" v-if="selectedProjectId">
        <DevelopmentStepsPanel
          :project-id="selectedProjectId"
          :requirement-document-id="selectedRequirementId"
          :show-tree="viewMode === 'tree'"
          ref="stepsPanel"
        />
      </el-card>

      <!-- 未选择项目时的提示 -->
      <el-card shadow="never" v-else>
        <el-empty description="请先选择一个项目">
          <el-button type="primary" @click="navigateToProjects">
            前往项目管理
          </el-button>
        </el-empty>
      </el-card>
    </div>

    <!-- 需求分解对话框 -->
    <RequirementDecomposeDialog
      v-model="showDecomposeDialog"
      :project-id="selectedProjectId || 0"
      :requirement-document-id="selectedRequirementId"
      @success="handleDecomposeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { List, Plus, Search } from '@element-plus/icons-vue'
import { ProjectService } from '@/services/project'
import DevelopmentStepsPanel from '@/components/development/DevelopmentStepsPanel.vue'
import RequirementDecomposeDialog from '@/components/development/RequirementDecomposeDialog.vue'
import type { ProjectSummary } from '@/types'
import type { DecompositionResult } from '@/types/development'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const projects = ref<ProjectSummary[]>([])
const selectedProjectId = ref<number>()
const selectedRequirementId = ref<number>()
const showDecomposeDialog = ref(false)
const stepsPanel = ref()

// 筛选条件
const searchKeyword = ref('')
const filterStatus = ref('')
const filterPriority = ref('')
const filterStepType = ref('')
const viewMode = ref<'list' | 'card' | 'tree'>('list')

// 方法
const loadProjects = async () => {
  try {
    loading.value = true
    const result = await ProjectService.getProjects({ pageSize: 100 })
    projects.value = result.items

    // 如果URL中有projectId参数，自动选择
    const projectId = route.query.projectId
    if (projectId && projects.value.some(p => p.id === parseInt(projectId as string))) {
      selectedProjectId.value = parseInt(projectId as string)
    } else if (projects.value.length > 0) {
      // 默认选择第一个项目
      selectedProjectId.value = projects.value[0].id
    }

    // 如果URL中有requirementId参数，保存它
    const requirementId = route.query.requirementId
    if (requirementId) {
      selectedRequirementId.value = parseInt(requirementId as string)
    }
  } catch (error) {
    ElMessage.error('加载项目列表失败')
  } finally {
    loading.value = false
  }
}

const handleProjectChange = (projectId: number | undefined) => {
  if (projectId) {
    // 更新URL参数
    router.replace({
      query: { ...route.query, projectId: projectId.toString() }
    })
  } else {
    // 清除URL参数
    const query = { ...route.query }
    delete query.projectId
    router.replace({ query })
  }
}

const handleSearch = () => {
  // 搜索逻辑由DevelopmentStepsPanel组件处理
  if (stepsPanel.value) {
    stepsPanel.value.handleSearch()
  }
}

const handleDecomposeSuccess = (result: DecompositionResult) => {
  ElMessage.success(`需求分解成功，生成 ${result.data?.steps.length || 0} 个开发步骤`)
  if (stepsPanel.value) {
    stepsPanel.value.refreshSteps()
  }
}

const navigateToProjects = () => {
  router.push('/projects')
}

// 监听器
watch([filterStatus, filterPriority, filterStepType], () => {
  // 筛选逻辑由DevelopmentStepsPanel组件处理
})

// 生命周期
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.development-steps-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-toolbar {
  margin-bottom: 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.steps-content {
  min-height: 600px;
}
</style>
