using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SqlSugar;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using ProjectManagement.Core.Models;

namespace ProjectManagement.Core.Services
{
    /// <summary>
    /// 编译错误服务实现
    /// </summary>
    public class CompilationErrorService : ICompilationErrorService
    {
        private readonly ISqlSugarClient _db;

        public CompilationErrorService(ISqlSugarClient db)
        {
            _db = db;
        }

        /// <summary>
        /// 获取编译错误列表
        /// </summary>
        public async Task<Interfaces.PagedResult<CompilationErrorDto>> GetCompilationErrorsAsync(CompilationErrorQueryDto query)
        {
            var queryable = _db.Queryable<CompilationError>()
                .Where(e => !e.IsDeleted);

            // 应用过滤条件
            if (!string.IsNullOrEmpty(query.ProjectType))
            {
                queryable = queryable.Where(e => e.ProjectType == query.ProjectType);
            }

            if (!string.IsNullOrEmpty(query.Severity))
            {
                queryable = queryable.Where(e => e.Severity == query.Severity);
            }

            if (query.ProjectId.HasValue)
            {
                queryable = queryable.Where(e => e.ProjectId == query.ProjectId);
            }

            if (!string.IsNullOrEmpty(query.CompilationSessionId))
            {
                queryable = queryable.Where(e => e.CompilationSessionId == query.CompilationSessionId);
            }

            if (query.ErrorsOnly)
            {
                queryable = queryable.Where(e => e.Severity == "Error");
            }

            // 获取总数
            var totalCount = await queryable.CountAsync();

            // 分页和排序
            var items = await queryable
                .OrderByDescending(e => e.CompilationTime)
                .OrderByDescending(e => e.Id)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(e => new CompilationErrorDto
                {
                    Id = e.Id,
                    ProjectId = e.ProjectId,
                    ProjectType = e.ProjectType,
                    CompilationSessionId = e.CompilationSessionId,
                    Severity = e.Severity,
                    Code = e.Code,
                    Message = e.Message,
                    FilePath = e.FilePath,
                    LineNumber = e.LineNumber,
                    ColumnNumber = e.ColumnNumber,
                    ProjectName = e.ProjectName,
                    ProjectPath = e.ProjectPath,
                    CompilerVersion = e.CompilerVersion,
                    TargetFramework = e.TargetFramework,
                    BuildConfiguration = e.BuildConfiguration,
                    CompilationTime = e.CompilationTime,
                    CreatedTime = e.CreatedTime
                })
                .ToListAsync();

            return new Interfaces.PagedResult<CompilationErrorDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 根据ID获取编译错误
        /// </summary>
        public async Task<CompilationErrorDto?> GetCompilationErrorByIdAsync(int id)
        {
            var error = await _db.Queryable<CompilationError>()
                .Where(e => e.Id == id && !e.IsDeleted)
                .Select(e => new CompilationErrorDto
                {
                    Id = e.Id,
                    ProjectId = e.ProjectId,
                    ProjectType = e.ProjectType,
                    CompilationSessionId = e.CompilationSessionId,
                    Severity = e.Severity,
                    Code = e.Code,
                    Message = e.Message,
                    FilePath = e.FilePath,
                    LineNumber = e.LineNumber,
                    ColumnNumber = e.ColumnNumber,
                    ProjectName = e.ProjectName,
                    ProjectPath = e.ProjectPath,
                    CompilerVersion = e.CompilerVersion,
                    TargetFramework = e.TargetFramework,
                    BuildConfiguration = e.BuildConfiguration,
                    CompilationTime = e.CompilationTime,
                    CreatedTime = e.CreatedTime
                })
                .FirstAsync();

            return error;
        }

        /// <summary>
        /// 获取编译错误统计信息
        /// </summary>
        public async Task<CompilationErrorStatsDto> GetCompilationErrorStatsAsync(string? projectType = null, int? projectId = null)
        {
            var queryable = _db.Queryable<CompilationError>()
                .Where(e => !e.IsDeleted);

            if (!string.IsNullOrEmpty(projectType))
            {
                queryable = queryable.Where(e => e.ProjectType == projectType);
            }

            if (projectId.HasValue)
            {
                queryable = queryable.Where(e => e.ProjectId == projectId);
            }

            var errors = await queryable.ToListAsync();

            var stats = new CompilationErrorStatsDto
            {
                TotalErrors = errors.Count(e => e.Severity == "Error"),
                TotalWarnings = errors.Count(e => e.Severity == "Warning"),
                BackendErrors = errors.Count(e => e.ProjectType == "Backend" && e.Severity == "Error"),
                FrontendErrors = errors.Count(e => e.ProjectType == "Frontend" && e.Severity == "Error"),
                LastCompilationTime = errors.Any() ? errors.Max(e => e.CompilationTime) : null
            };

            return stats;
        }

        /// <summary>
        /// 清空编译错误
        /// </summary>
        public async Task<int> ClearCompilationErrorsAsync(string? projectType = null, int? projectId = null, string? sessionId = null)
        {
            var queryable = _db.Queryable<CompilationError>()
                .Where(e => !e.IsDeleted);

            if (!string.IsNullOrEmpty(projectType))
            {
                queryable = queryable.Where(e => e.ProjectType == projectType);
            }

            if (projectId.HasValue)
            {
                queryable = queryable.Where(e => e.ProjectId == projectId);
            }

            if (!string.IsNullOrEmpty(sessionId))
            {
                queryable = queryable.Where(e => e.CompilationSessionId == sessionId);
            }

            var errors = await queryable.ToListAsync();

            foreach (var error in errors)
            {
                error.IsDeleted = true;
                error.DeletedTime = DateTime.Now;
            }

            await _db.Updateable(errors).ExecuteCommandAsync();
            return errors.Count;
        }

        /// <summary>
        /// 批量添加编译错误
        /// </summary>
        public async Task<int> AddCompilationErrorsAsync(IEnumerable<CompilationErrorDto> errors)
        {
            var entities = errors.Select(dto => new CompilationError
            {
                ProjectId = dto.ProjectId,
                ProjectType = dto.ProjectType,
                CompilationSessionId = dto.CompilationSessionId,
                Severity = dto.Severity,
                Code = dto.Code,
                Message = dto.Message,
                FilePath = dto.FilePath,
                LineNumber = dto.LineNumber,
                ColumnNumber = dto.ColumnNumber,
                ProjectName = dto.ProjectName,
                ProjectPath = dto.ProjectPath,
                CompilerVersion = dto.CompilerVersion,
                TargetFramework = dto.TargetFramework,
                BuildConfiguration = dto.BuildConfiguration,
                CompilationTime = dto.CompilationTime,
                CreatedTime = DateTime.Now
            }).ToList();

            await _db.Insertable(entities).ExecuteCommandAsync();

            return entities.Count;
        }

        /// <summary>
        /// 删除编译错误
        /// </summary>
        public async Task<bool> DeleteCompilationErrorAsync(int id)
        {
            var error = await _db.Queryable<CompilationError>()
                .FirstAsync(e => e.Id == id && !e.IsDeleted);

            if (error == null)
                return false;

            error.IsDeleted = true;
            error.DeletedTime = DateTime.Now;

            await _db.Updateable(error).ExecuteCommandAsync();
            return true;
        }

        /// <summary>
        /// 获取最新的编译会话ID列表
        /// </summary>
        public async Task<List<string>> GetRecentCompilationSessionsAsync(string? projectType = null, int limit = 10)
        {
            var queryable = _db.Queryable<CompilationError>()
                .Where(e => !e.IsDeleted && !string.IsNullOrEmpty(e.CompilationSessionId));

            if (!string.IsNullOrEmpty(projectType))
            {
                queryable = queryable.Where(e => e.ProjectType == projectType);
            }

            var errors = await queryable.ToListAsync();

            var sessions = errors
                .GroupBy(e => e.CompilationSessionId)
                .Select(g => new { SessionId = g.Key, LastTime = g.Max(e => e.CompilationTime) })
                .OrderByDescending(x => x.LastTime)
                .Take(limit)
                .Select(x => x.SessionId!)
                .ToList();

            return sessions;
        }
    }
}
