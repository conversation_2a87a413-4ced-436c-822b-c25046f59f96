# AI驱动软件开发自动化系统 - 数据库脚本

## 📁 目录结构

```
Database/
├── 01_Schema/                          # 数据库架构文件
│   ├── DatabaseSchema.sql              # 主数据库架构文件
│   └── README.md                       # 架构说明文档
├── 02_InitialData/                     # 初始化数据文件
│   ├── InitialData.sql                 # 主初始化数据脚本
│   ├── InitialAIModelConfigurations.sql # AI模型配置数据
│   └── README.md                       # 初始化数据说明
├── 03_Migrations/                      # 数据库迁移脚本
│   ├── 001_AddConversationId.sql       # 添加对话ID字段
│   ├── 002_AddMissingUserFields.sql    # 添加用户表缺失字段
│   ├── 003_AddUserAIConfiguration.sql  # 添加用户AI配置
│   ├── 004_FixBCryptHashes.sql         # 修复BCrypt密码哈希
│   └── README.md                       # 迁移脚本说明
├── 04_Documentation/                   # 数据库文档
│   ├── DatabaseDesign.md               # 数据库设计文档
│   ├── TableSummary.md                 # 表结构摘要
│   ├── ERDiagram.mermaid               # ER图定义
│   └── README.md                       # 文档说明
├── 05_Tools/                          # 工具和辅助脚本
│   ├── GenerateBCryptHashes.cs         # BCrypt密码生成工具
│   └── README.md                       # 工具说明
└── README.md                          # 总体说明文档（本文件）
```

## 🚀 快速开始

### 1. 全新安装

如果是全新安装，请按以下顺序执行：

```sql
-- 1. 创建数据库架构
执行: 01_Schema/DatabaseSchema.sql

-- 2. 初始化数据
执行: 02_InitialData/InitialData.sql
执行: 02_InitialData/InitialAIModelConfigurations.sql
```

### 2. 现有数据库升级

如果已有数据库需要升级，请按顺序执行迁移脚本：

```sql
-- 按顺序执行迁移脚本
执行: 03_Migrations/001_AddConversationId.sql
执行: 03_Migrations/002_AddMissingUserFields.sql
执行: 03_Migrations/003_AddUserAIConfiguration.sql
执行: 03_Migrations/004_FixBCryptHashes.sql
```

## 📋 数据库表结构概览

### 核心业务表
- **Users** - 用户信息管理
- **Projects** - 项目信息管理
- **RequirementConversations** - AI需求对话记录
- **RequirementDocuments** - 需求规格书
- **ERDiagrams** - 实体关系图
- **ContextDiagrams** - 系统上下文图

### AI功能表
- **CodeGenerationTasks** - AI代码生成任务
- **GeneratedCodeFiles** - 生成的代码文件
- **TestTasks** - AI测试生成和执行
- **AIModelConfigurations** - AI模型配置
- **UserAIConfigurations** - 用户个人AI配置
- **UserTaskMappings** - 用户任务映射配置

### 管理和监控表
- **DeploymentTasks** - 自动化部署管理
- **Issues** - 问题和缺陷管理
- **IssueResolutions** - 问题解决方案记录
- **WorkflowStates** - 工作流程状态
- **SystemLogs** - 系统运行日志

## 🔧 维护说明

### 添加新的迁移脚本

1. 在 `03_Migrations/` 目录下创建新文件
2. 使用递增的编号命名：`005_描述.sql`
3. 包含完整的验证和回滚逻辑
4. 更新本README文件

### 备份建议

- 执行任何迁移脚本前，请先备份数据库
- 建议在测试环境先验证迁移脚本
- 保留迁移执行日志

## 📖 相关文档

- [数据库设计文档](04_Documentation/DatabaseDesign.md)
- [表结构摘要](04_Documentation/TableSummary.md)
- [ER图](04_Documentation/ERDiagram.mermaid)

## ⚠️ 注意事项

1. **执行顺序很重要** - 请严格按照编号顺序执行迁移脚本
2. **备份数据** - 执行任何脚本前请备份数据库
3. **测试环境验证** - 在生产环境执行前，请在测试环境验证
4. **权限要求** - 执行脚本需要数据库管理员权限
5. **字符编码** - 所有脚本使用UTF-8编码，支持中文

## 🔐 默认用户账号

执行初始化脚本后，系统将创建以下默认用户（密码均为：password）：

- **admin** - 超级管理员 (<EMAIL>)
- **developer1** - 开发人员 (<EMAIL>)
- **pm1** - 项目经理 (<EMAIL>)
- **tester1** - 测试人员 (<EMAIL>)
- **product1** - 产品经理 (<EMAIL>)

**重要提示：请在首次登录后立即修改默认密码！**

## 📞 技术支持

如果在执行数据库脚本过程中遇到问题，请：

1. 检查SQL Server版本兼容性
2. 确认数据库连接权限
3. 查看错误日志详细信息
4. 参考相关文档或联系技术支持

---

*最后更新：2024-06-19*
