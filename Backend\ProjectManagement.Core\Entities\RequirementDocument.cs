using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 需求规格书实体 - 对应DatabaseSchema.sql中的RequirementDocuments表
/// 功能: 存储AI生成的需求规格书文档
/// 支持: 版本控制、状态管理、全文搜索、多格式需求
/// </summary>
[SugarTable("RequirementDocuments", "需求文档管理表")]
public class RequirementDocument : BaseEntity
{
    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 需求文档标题
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = false, ColumnDescription = "需求文档标题")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 需求文档完整内容（支持全文搜索）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "需求文档完整内容")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 功能性需求详细描述（支持全文搜索）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "功能性需求详细描述")]
    public string? FunctionalRequirements { get; set; }

    /// <summary>
    /// 非功能性需求详细描述（支持全文搜索）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "非功能性需求详细描述")]
    public string? NonFunctionalRequirements { get; set; }

    /// <summary>
    /// 用户故事集合，JSON格式存储
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "用户故事集合，JSON格式存储")]
    public string? UserStories { get; set; }

    /// <summary>
    /// 验收标准详细说明
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "验收标准详细说明")]
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// 文档版本号（如：1.0, 1.1, 2.0）
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "文档版本号")]
    public string DocumentVersion { get; set; } = "1.0";

    /// <summary>
    /// 文档状态: Draft(草稿), Review(审核中), Approved(已批准), Rejected(已拒绝), Published(已发布)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "文档状态")]
    public string Status { get; set; } = "Draft";

    /// <summary>
    /// 生成方式: AI(AI生成), Manual(手动创建), Hybrid(混合方式)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "生成方式")]
    public string GeneratedBy { get; set; } = "AI";

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的ER图 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ERDiagram> ERDiagrams { get; set; } = new();

    /// <summary>
    /// 关联的上下文图 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ContextDiagram> ContextDiagrams { get; set; } = new();

    /// <summary>
    /// 关联的代码生成任务 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<CodeGenerationTask> CodeGenerationTasks { get; set; } = new();
}
