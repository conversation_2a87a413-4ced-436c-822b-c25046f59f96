-- =============================================
-- Selenium测试相关表创建脚本
-- 版本: 1.0
-- 创建日期: 2025-06-25
-- 描述: 创建Selenium自动化测试相关的数据库表
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. Selenium测试脚本表 (SeleniumScripts)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumScripts')
BEGIN
    CREATE TABLE SeleniumScripts (
        Id int IDENTITY(1,1) NOT NULL,
        Name nvarchar(100) NOT NULL,
        Description nvarchar(500) NULL,
        Category nvarchar(50) NOT NULL DEFAULT 'ui',
        Code ntext NULL,
        ConfigJson ntext NULL,
        TagsJson nvarchar(1000) NULL,
        Priority nvarchar(20) NOT NULL DEFAULT 'medium',
        Status nvarchar(20) NOT NULL DEFAULT 'draft',
        ProjectId int NULL,
        LastExecutedTime datetime2 NULL,
        ExecutionCount int NOT NULL DEFAULT 0,
        SuccessCount int NOT NULL DEFAULT 0,
        AvgDuration int NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumScripts PRIMARY KEY (Id),
        CONSTRAINT FK_SeleniumScripts_Projects FOREIGN KEY (ProjectId) REFERENCES Projects(Id),
        CONSTRAINT FK_SeleniumScripts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumScripts_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumScripts_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumScripts_Category CHECK (Category IN ('ui', 'api', 'integration', 'performance')),
        CONSTRAINT CK_SeleniumScripts_Priority CHECK (Priority IN ('low', 'medium', 'high')),
        CONSTRAINT CK_SeleniumScripts_Status CHECK (Status IN ('draft', 'ready', 'running', 'failed', 'archived'))
    );
    
    PRINT '✅ SeleniumScripts 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumScripts 表已存在，跳过创建';
END
GO

-- =============================================
-- 2. Selenium执行记录表 (SeleniumExecutions)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumExecutions')
BEGIN
    CREATE TABLE SeleniumExecutions (
        Id int IDENTITY(1,1) NOT NULL,
        ExecutionId nvarchar(50) NOT NULL,
        ScriptId int NOT NULL,
        Status nvarchar(20) NOT NULL DEFAULT 'running',
        StartTime datetime2 NOT NULL,
        EndTime datetime2 NULL,
        Duration int NULL,
        ErrorMessage ntext NULL,
        ConfigJson ntext NULL,
        StatsJson ntext NULL,
        ScreenshotsJson ntext NULL,
        Environment nvarchar(500) NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumExecutions PRIMARY KEY (Id),
        CONSTRAINT UK_SeleniumExecutions_ExecutionId UNIQUE (ExecutionId),
        CONSTRAINT FK_SeleniumExecutions_Scripts FOREIGN KEY (ScriptId) REFERENCES SeleniumScripts(Id) ON DELETE CASCADE,
        CONSTRAINT FK_SeleniumExecutions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutions_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutions_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumExecutions_Status CHECK (Status IN ('running', 'success', 'failed', 'stopped', 'timeout'))
    );
    
    PRINT '✅ SeleniumExecutions 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumExecutions 表已存在，跳过创建';
END
GO

-- =============================================
-- 3. Selenium执行日志表 (SeleniumExecutionLogs)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SeleniumExecutionLogs')
BEGIN
    CREATE TABLE SeleniumExecutionLogs (
        Id int IDENTITY(1,1) NOT NULL,
        ExecutionId int NOT NULL,
        Level nvarchar(20) NOT NULL DEFAULT 'info',
        Message ntext NOT NULL,
        Step nvarchar(200) NULL,
        Timestamp datetime2 NOT NULL DEFAULT GETDATE(),
        ExtraData ntext NULL,
        
        -- BaseEntity 字段
        CreatedTime datetime2 NOT NULL DEFAULT GETDATE(),
        UpdatedTime datetime2 NULL,
        CreatedBy int NULL,
        UpdatedBy int NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedTime datetime2 NULL,
        DeletedBy int NULL,
        Version int NOT NULL DEFAULT 1,
        Remarks nvarchar(500) NULL,
        
        CONSTRAINT PK_SeleniumExecutionLogs PRIMARY KEY (Id),
        CONSTRAINT FK_SeleniumExecutionLogs_Executions FOREIGN KEY (ExecutionId) REFERENCES SeleniumExecutions(Id) ON DELETE CASCADE,
        CONSTRAINT FK_SeleniumExecutionLogs_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutionLogs_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(Id),
        CONSTRAINT FK_SeleniumExecutionLogs_DeletedBy FOREIGN KEY (DeletedBy) REFERENCES Users(Id),
        CONSTRAINT CK_SeleniumExecutionLogs_Level CHECK (Level IN ('debug', 'info', 'warning', 'error', 'success'))
    );
    
    PRINT '✅ SeleniumExecutionLogs 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ SeleniumExecutionLogs 表已存在，跳过创建';
END
GO

-- =============================================
-- 4. 创建索引
-- =============================================

-- SeleniumScripts 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Name')
    CREATE INDEX IX_SeleniumScripts_Name ON SeleniumScripts(Name);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Category')
    CREATE INDEX IX_SeleniumScripts_Category ON SeleniumScripts(Category);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_Status')
    CREATE INDEX IX_SeleniumScripts_Status ON SeleniumScripts(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_ProjectId')
    CREATE INDEX IX_SeleniumScripts_ProjectId ON SeleniumScripts(ProjectId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_CreatedBy')
    CREATE INDEX IX_SeleniumScripts_CreatedBy ON SeleniumScripts(CreatedBy);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumScripts_IsDeleted')
    CREATE INDEX IX_SeleniumScripts_IsDeleted ON SeleniumScripts(IsDeleted);

-- SeleniumExecutions 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_ScriptId')
    CREATE INDEX IX_SeleniumExecutions_ScriptId ON SeleniumExecutions(ScriptId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_Status')
    CREATE INDEX IX_SeleniumExecutions_Status ON SeleniumExecutions(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_StartTime')
    CREATE INDEX IX_SeleniumExecutions_StartTime ON SeleniumExecutions(StartTime);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_CreatedBy')
    CREATE INDEX IX_SeleniumExecutions_CreatedBy ON SeleniumExecutions(CreatedBy);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutions_IsDeleted')
    CREATE INDEX IX_SeleniumExecutions_IsDeleted ON SeleniumExecutions(IsDeleted);

-- SeleniumExecutionLogs 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_ExecutionId')
    CREATE INDEX IX_SeleniumExecutionLogs_ExecutionId ON SeleniumExecutionLogs(ExecutionId);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_Level')
    CREATE INDEX IX_SeleniumExecutionLogs_Level ON SeleniumExecutionLogs(Level);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_Timestamp')
    CREATE INDEX IX_SeleniumExecutionLogs_Timestamp ON SeleniumExecutionLogs(Timestamp);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SeleniumExecutionLogs_IsDeleted')
    CREATE INDEX IX_SeleniumExecutionLogs_IsDeleted ON SeleniumExecutionLogs(IsDeleted);

PRINT '✅ 所有索引创建完成';
GO

-- =============================================
-- 5. 添加中文注释
-- =============================================

-- SeleniumScripts 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'Selenium测试脚本表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'主键ID',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'脚本名称',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Name';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'脚本描述',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Description';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'脚本分类(ui/api/integration/performance)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Category';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'Python代码内容',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Code';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'配置信息JSON',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'ConfigJson';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'标签JSON数组',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'TagsJson';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'优先级(low/medium/high)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Priority';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'状态(draft/ready/running/failed/archived)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'关联项目ID',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'ProjectId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'最后执行时间',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'LastExecutedTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行次数',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'ExecutionCount';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'成功次数',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'SuccessCount';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'平均执行时长(秒)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumScripts',
    @level2type = N'COLUMN', @level2name = N'AvgDuration';

-- SeleniumExecutions 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'Selenium执行记录表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行唯一标识',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'ExecutionId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'脚本ID',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'ScriptId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行状态(running/success/failed/stopped/timeout)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'开始时间',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'StartTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'结束时间',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'EndTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行时长(秒)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'Duration';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'错误信息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutions',
    @level2type = N'COLUMN', @level2name = N'ErrorMessage';

-- SeleniumExecutionLogs 表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'Selenium执行日志表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行记录ID',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs',
    @level2type = N'COLUMN', @level2name = N'ExecutionId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志级别(debug/info/warning/error/success)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs',
    @level2type = N'COLUMN', @level2name = N'Level';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'日志消息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs',
    @level2type = N'COLUMN', @level2name = N'Message';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'执行步骤',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs',
    @level2type = N'COLUMN', @level2name = N'Step';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'时间戳',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'SeleniumExecutionLogs',
    @level2type = N'COLUMN', @level2name = N'Timestamp';

PRINT '✅ 所有表注释添加完成';
GO

PRINT '🎉 Selenium测试相关表创建脚本执行完成！';
PRINT '📋 已创建的表:';
PRINT '   - SeleniumScripts (测试脚本表)';
PRINT '   - SeleniumExecutions (执行记录表)';
PRINT '   - SeleniumExecutionLogs (执行日志表)';
PRINT '🔗 已创建的外键关系:';
PRINT '   - SeleniumScripts → Projects';
PRINT '   - SeleniumExecutions → SeleniumScripts';
PRINT '   - SeleniumExecutionLogs → SeleniumExecutions';
PRINT '📊 已创建的索引用于优化查询性能';
PRINT '💬 已添加中文字段注释';
GO
