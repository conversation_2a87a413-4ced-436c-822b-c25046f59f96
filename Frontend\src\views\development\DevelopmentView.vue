<template>
  <div class="development-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Operation /></el-icon>
            开发管理
          </h1>
          <p class="page-description">
            管理项目开发步骤，跟踪开发进度，自动化代码生成
          </p>
        </div>
        <div class="header-actions">
          <!-- 如果是从AI聊天跳转过来，显示返回按钮 -->
          <el-button
            v-if="fromAIChat"
            @click="returnToAIChat"
            type="info"
          >
            <el-icon><Back /></el-icon>
            返回AI聊天
          </el-button>

          <el-button type="primary" @click="showDecomposeDialog = true">
            <el-icon><Plus /></el-icon>
            分解需求
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>

          <!-- AI聊天快捷入口 -->
          <el-button @click="openAIChat" type="success">
            <el-icon><ChatDotRound /></el-icon>
            AI开发助手
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#409eff"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalSteps }}</div>
                <div class="stats-label">总开发步骤</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ completedSteps }}</div>
                <div class="stats-label">已完成步骤</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#e6a23c"><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ inProgressSteps }}</div>
                <div class="stats-label">进行中步骤</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#f56c6c"><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ blockedSteps }}</div>
                <div class="stats-label">阻塞步骤</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 编码任务统计 -->
      <el-row :gutter="16" style="margin-top: 16px;">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#909399"><Edit /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalCodingTasks }}</div>
                <div class="stats-label">编码任务</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#e6a23c"><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ inProgressCodingTasks }}</div>
                <div class="stats-label">编码中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ completedCodingTasks }}</div>
                <div class="stats-label">编码完成</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#f56c6c"><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ blockedCodingTasks }}</div>
                <div class="stats-label">编码阻塞</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Star /></el-icon>
            <span>快捷操作</span>
          </div>
        </template>

        <el-row :gutter="16">
          <el-col :span="6">
            <div class="action-item" @click="navigateToSteps">
              <div class="action-icon">
                <el-icon><List /></el-icon>
              </div>
              <div class="action-content">
                <h3>查看开发步骤</h3>
                <p>浏览和管理所有项目的开发步骤</p>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="action-item" @click="navigateToCodingTasks">
              <div class="action-icon">
                <el-icon><Edit /></el-icon>
              </div>
              <div class="action-content">
                <h3>编码任务</h3>
                <p>管理需要编码的开发步骤和任务分配</p>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="action-item" @click="navigateToAssociations">
              <div class="action-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="action-content">
                <h3>关联管理</h3>
                <p>管理步骤依赖关系和模板序列关联</p>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="action-item" @click="navigateToExecution">
              <div class="action-icon">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="action-content">
                <h3>执行管理</h3>
                <p>监控和管理开发步骤的执行状态</p>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16" style="margin-top: 16px;">
          <el-col :span="6">
            <div class="action-item" @click="navigateToDecompose">
              <div class="action-icon">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="action-content">
                <h3>需求分解</h3>
                <p>将需求文档分解为具体的开发步骤</p>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16" style="margin-top: 16px;">
          <el-col :span="6">
            <div class="action-item" @click="navigateToBuildErrors">
              <div class="action-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="action-content">
                <h3>VS编译错误管理</h3>
                <p>监控VS编译错误并获取AI修复建议</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>最近活动</span>
            <el-button type="text" size="small" @click="viewAllActivities">
              查看全部
            </el-button>
          </div>
        </template>

        <div class="activity-list" v-loading="activitiesLoading">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <el-icon :color="getActivityColor(activity.type)">
                <component :is="getActivityIcon(activity.type)" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-description">{{ activity.description }}</div>
              <div class="activity-time">{{ formatTime(activity.createdTime) }}</div>
            </div>
          </div>

          <el-empty v-if="recentActivities.length === 0" description="暂无最近活动" />
        </div>
      </el-card>
    </div>

    <!-- 项目进度概览 -->
    <div class="project-progress">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><TrendCharts /></el-icon>
            <span>项目进度概览</span>
          </div>
        </template>

        <div class="progress-list" v-loading="progressLoading">
          <div
            v-for="project in projectProgress"
            :key="project.projectId"
            class="progress-item"
            @click="navigateToProject(project.projectId)"
          >
            <div class="progress-info">
              <div class="project-name">{{ (project as any).projectName || '未知项目' }}</div>
              <div class="progress-stats">
                <span>{{ project.completedSteps }}/{{ project.totalSteps }} 步骤</span>
                <span class="progress-percentage">{{ (project.overallProgress || 0).toFixed(1) }}%</span>
              </div>
            </div>
            <div class="progress-bar">
              <el-progress
                :percentage="project.overallProgress || 0"
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </div>

          <el-empty v-if="projectProgress.length === 0" description="暂无项目数据" />
        </div>
      </el-card>
    </div>

    <!-- 需求分解对话框 -->
    <RequirementDecomposeDialog
      v-model="showDecomposeDialog"
      :project-id="0"
      @success="handleDecomposeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Operation, Plus, Refresh, Document, CircleCheck, Clock, Warning,
  Star, List, VideoPlay, TrendCharts, Connection, Edit, Back, ChatDotRound
} from '@element-plus/icons-vue'
import { developmentService } from '@/services/development'
import RequirementDecomposeDialog from '@/components/development/RequirementDecomposeDialog.vue'
import type {
  StepExecutionProgress,
  DecompositionResult
} from '@/types/development'
import dayjs from 'dayjs'

const router = useRouter()

// 获取路由参数，检查是否从AI聊天跳转过来
const route = router.currentRoute.value
const fromAIChat = ref(route.query.fromAIChat === 'true')
const aiChatConversationId = ref(route.query.conversationId as string)
const aiChatMode = ref(route.query.mode as string)

// 响应式数据
const activitiesLoading = ref(false)
const progressLoading = ref(false)
const showDecomposeDialog = ref(false)

// 统计数据
const totalSteps = ref(0)
const completedSteps = ref(0)
const inProgressSteps = ref(0)
const blockedSteps = ref(0)

// 编码任务统计
const totalCodingTasks = ref(0)
const inProgressCodingTasks = ref(0)
const completedCodingTasks = ref(0)
const blockedCodingTasks = ref(0)

// 最近活动
const recentActivities = ref<any[]>([])

// 项目进度
const projectProgress = ref<StepExecutionProgress[]>([])

// 方法
const refreshData = async () => {
  await Promise.all([
    loadStatistics(),
    loadRecentActivities(),
    loadProjectProgress()
  ])
}

const loadStatistics = async () => {
  try {
    // 这里应该调用获取全局统计的API
    // 暂时使用模拟数据
    totalSteps.value = 156
    completedSteps.value = 89
    inProgressSteps.value = 23
    blockedSteps.value = 5

    // 加载编码任务统计
    await loadCodingTaskStatistics()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadCodingTaskStatistics = async () => {
  try {
    // TODO: 调用编码任务统计API
    // 暂时使用模拟数据
    totalCodingTasks.value = 12
    inProgressCodingTasks.value = 5
    completedCodingTasks.value = 6
    blockedCodingTasks.value = 1
  } catch (error) {
    console.error('加载编码任务统计失败:', error)
    // 使用默认值
    totalCodingTasks.value = 0
    inProgressCodingTasks.value = 0
    completedCodingTasks.value = 0
    blockedCodingTasks.value = 0
  }
}

const loadRecentActivities = async () => {
  activitiesLoading.value = true
  try {
    // 模拟最近活动数据
    recentActivities.value = [
      {
        id: 1,
        type: 'decompose',
        title: '需求分解完成',
        description: '用户管理模块已分解为 12 个开发步骤',
        createdTime: new Date(Date.now() - 1000 * 60 * 30)
      },
      {
        id: 2,
        type: 'execute',
        title: '步骤执行完成',
        description: '创建用户实体类已完成',
        createdTime: new Date(Date.now() - 1000 * 60 * 60 * 2)
      },
      {
        id: 3,
        type: 'block',
        title: '步骤被阻塞',
        description: '用户认证接口因依赖未完成而被阻塞',
        createdTime: new Date(Date.now() - 1000 * 60 * 60 * 4)
      }
    ]
  } catch (error) {
    console.error('加载最近活动失败:', error)
  } finally {
    activitiesLoading.value = false
  }
}

const loadProjectProgress = async () => {
  progressLoading.value = true
  try {
    // 模拟项目进度数据
    projectProgress.value = [
      {
        projectId: 1,
        projectName: '电商管理系统',
        totalSteps: 45,
        completedSteps: 32,
        overallProgress: 71.1
      },
      {
        projectId: 2,
        projectName: '用户管理平台',
        totalSteps: 28,
        completedSteps: 15,
        overallProgress: 53.6
      }
    ] as any[]
  } catch (error) {
    console.error('加载项目进度失败:', error)
  } finally {
    progressLoading.value = false
  }
}

// 导航方法
const navigateToSteps = () => {
  router.push('/development/steps')
}

const navigateToDecompose = () => {
  router.push('/development/decompose')
}

const navigateToAssociations = () => {
  router.push('/development/associations')
}

const navigateToExecution = () => {
  // 跳转到执行管理页面
  router.push('/development/execution')
}

const navigateToCodingTasks = () => {
  router.push('/development/coding-tasks')
}

const navigateToBuildErrors = () => {
  router.push('/development/build-errors')
}

const navigateToProject = (projectId: number) => {
  router.push(`/projects/${projectId}?tab=steps`)
}

const viewAllActivities = () => {
  // 跳转到活动历史页面
  router.push('/development/activities')
}

const handleDecomposeSuccess = (result: DecompositionResult) => {
  ElMessage.success(`需求分解成功，生成 ${result.data?.steps.length || 0} 个开发步骤`)
  refreshData()
}

// 辅助方法
const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    decompose: 'Operation',
    execute: 'VideoPlay',
    complete: 'CircleCheck',
    block: 'Warning',
    fail: 'CircleClose'
  }
  return iconMap[type] || 'InfoFilled'
}

const getActivityColor = (type: string) => {
  const colorMap: Record<string, string> = {
    decompose: '#409eff',
    execute: '#e6a23c',
    complete: '#67c23a',
    block: '#f56c6c',
    fail: '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

const formatTime = (time: Date) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// AI聊天相关方法
const returnToAIChat = () => {
  if (aiChatConversationId.value && aiChatMode.value) {
    // 返回到AI聊天页面，并恢复对话状态
    router.push({
      path: '/ai-chat',
      query: {
        conversationId: aiChatConversationId.value,
        mode: aiChatMode.value,
        returnFrom: 'development'
      }
    })
  } else {
    // 如果没有对话ID，直接跳转到开发模式
    router.push({
      path: '/ai-chat',
      query: {
        mode: 'development',
        returnFrom: 'development'
      }
    })
  }
}

const openAIChat = () => {
  // 打开AI聊天页面的开发模式
  const query: any = {
    mode: 'development'
  }

  router.push({
    path: '/ai-chat',
    query
  })
}

// 生命周期
onMounted(() => {
  refreshData()

  // 如果是从AI聊天跳转过来且有项目ID，可以进行相应的初始化
  if (fromAIChat.value && route.query.projectId) {
    // 可以根据项目ID进行特定的初始化操作
    console.log('从AI聊天跳转，项目ID:', route.query.projectId)
  }
})
</script>

<style scoped>
.development-view {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.quick-actions,
.recent-activities,
.project-progress {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 600;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.action-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 16px;
}

.action-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.activity-list,
.progress-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.progress-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background 0.3s;
}

.progress-item:hover {
  background: #f8f9fa;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.project-name {
  font-weight: 500;
  color: #303133;
}

.progress-stats {
  font-size: 14px;
  color: #606266;
}

.progress-percentage {
  margin-left: 8px;
  font-weight: 500;
  color: #409eff;
}

.progress-bar {
  width: 100%;
}
</style>
