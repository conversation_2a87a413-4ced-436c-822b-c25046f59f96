using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;
using Microsoft.Extensions.Logging;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 模板序列仓储实现
    /// </summary>
    public class TemplateSequenceRepository : BaseRepository<UIAutoMationTemplateSequence>, ITemplateSequenceRepository
    {
        public TemplateSequenceRepository(ISqlSugarClient db, ILogger<BaseRepository<UIAutoMationTemplateSequence>> logger) : base(db, logger)
        {
        }

        /// <summary>
        /// 分页查询序列
        /// </summary>
        public async Task<PagedResultDto<UIAutoMationTemplateSequence>> GetPagedAsync(TemplateSequenceQueryDto query)
        {
            var queryable = _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted);

            // 关键词搜索
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                queryable = queryable.Where(x => x.Name.Contains(query.Keyword) ||
                                               x.Description!.Contains(query.Keyword));
            }

            // 分类过滤
            if (!string.IsNullOrEmpty(query.Category))
            {
                queryable = queryable.Where(x => x.Category == query.Category);
            }

            // 状态过滤
            if (query.IsActive.HasValue)
            {
                queryable = queryable.Where(x => x.IsActive == query.IsActive.Value);
            }

            // 标签过滤
            if (query.Tags != null && query.Tags.Count > 0)
            {
                foreach (var tag in query.Tags)
                {
                    queryable = queryable.Where(x => x.Tags!.Contains($"\"{tag}\""));
                }
            }

            // 创建者过滤
            if (query.CreatedBy.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedBy == query.CreatedBy.Value);
            }

            // 创建时间范围过滤
            if (query.CreatedTimeStart.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedTime >= query.CreatedTimeStart.Value);
            }
            if (query.CreatedTimeEnd.HasValue)
            {
                queryable = queryable.Where(x => x.CreatedTime <= query.CreatedTimeEnd.Value);
            }

            // 排序
            var sortBy = query.SortBy ?? "CreatedTime";
            var sortDirection = query.SortDirection ?? "desc";

            queryable = sortDirection.ToLower() == "asc"
                ? queryable.OrderBy(sortBy)
                : queryable.OrderBy($"{sortBy} desc");

            // 分页
            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .ToPageListAsync(query.Page, query.PageSize);

            // 批量获取步骤数量
            if (items.Any())
            {
                var sequenceIds = items.Select(x => x.Id).ToList();

                // 使用字典来存储每个序列的步骤数量
                var stepCountDict = new Dictionary<int, int>();

                foreach (var sequenceId in sequenceIds)
                {
                    var stepCount = await _db.Queryable<UIAutoMationTemplateStep>()
                        .Where(x => x.SequenceId == sequenceId && !x.IsDeleted)
                        .CountAsync();
                    stepCountDict[sequenceId] = stepCount;
                }

                // 为每个序列设置步骤数量
                foreach (var item in items)
                {
                    var stepCount = stepCountDict.GetValueOrDefault(item.Id, 0);

                    // 创建一个简单的步骤列表来表示数量
                    item.Steps = new List<UIAutoMationTemplateStep>();
                    for (int i = 0; i < stepCount; i++)
                    {
                        item.Steps.Add(new UIAutoMationTemplateStep { Id = i + 1 });
                    }
                }
            }

            return new PagedResultDto<UIAutoMationTemplateSequence>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = query.Page,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 获取序列详情（包含步骤）
        /// </summary>
        public async Task<UIAutoMationTemplateSequence?> GetWithStepsAsync(int id)
        {
            var sequence = await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => x.Id == id && !x.IsDeleted)
                .FirstAsync();

            if (sequence != null)
            {
                sequence.Steps = await _db.Queryable<UIAutoMationTemplateStep>()
                    .Where(x => x.SequenceId == id && !x.IsDeleted)
                    .OrderBy(x => x.StepOrder)
                    .ToListAsync();
            }

            return sequence;
        }

        /// <summary>
        /// 根据分类获取序列
        /// </summary>
        public async Task<List<UIAutoMationTemplateSequence>> GetByCategoryAsync(string category)
        {
            return await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted && x.Category == category)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 根据标签获取序列
        /// </summary>
        public async Task<List<UIAutoMationTemplateSequence>> GetByTagsAsync(List<string> tags)
        {
            var queryable = _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted);

            foreach (var tag in tags)
            {
                queryable = queryable.Where(x => x.Tags!.Contains($"\"{tag}\""));
            }

            return await queryable.OrderBy(x => x.Name).ToListAsync();
        }

        /// <summary>
        /// 搜索序列
        /// </summary>
        public async Task<List<UIAutoMationTemplateSequence>> SearchAsync(string keyword)
        {
            return await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted &&
                           (x.Name.Contains(keyword) || x.Description!.Contains(keyword)))
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 获取启用的序列
        /// </summary>
        public async Task<List<UIAutoMationTemplateSequence>> GetActiveAsync()
        {
            return await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted && x.IsActive)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 增加使用次数
        /// </summary>
        public async Task<bool> IncrementUsageCountAsync(int id)
        {
            return await _db.Updateable<UIAutoMationTemplateSequence>()
                .SetColumns(x => x.UsageCount == x.UsageCount + 1)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 更新最后使用时间
        /// </summary>
        public async Task<bool> UpdateLastUsedTimeAsync(int id, DateTime lastUsedTime)
        {
            return await _db.Updateable<UIAutoMationTemplateSequence>()
                .SetColumns(x => x.LastUsedTime == lastUsedTime)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 获取最常用的序列
        /// </summary>
        public async Task<List<UIAutoMationTemplateSequence>> GetMostUsedAsync(int count = 10)
        {
            return await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted)
                .OrderBy(x => x.UsageCount, OrderByType.Desc)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 检查序列名称是否存在
        /// </summary>
        public async Task<bool> ExistsNameAsync(string name, int? excludeId = null)
        {
            var queryable = _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted && x.Name == name);

            if (excludeId.HasValue)
            {
                queryable = queryable.Where(x => x.Id != excludeId.Value);
            }

            return await queryable.AnyAsync();
        }

        /// <summary>
        /// 根据名称获取序列
        /// </summary>
        public async Task<UIAutoMationTemplateSequence?> GetByNameAsync(string name)
        {
            return await _db.Queryable<UIAutoMationTemplateSequence>()
                .Where(x => !x.IsDeleted && x.Name == name)
                .FirstAsync();
        }

        /// <summary>
        /// 批量删除序列
        /// </summary>
        public async Task<bool> BatchDeleteAsync(List<int> ids)
        {
            try
            {
                _db.Ado.BeginTran();

                // 删除序列
                await _db.Updateable<UIAutoMationTemplateSequence>()
                    .SetColumns(x => x.IsDeleted == true)
                    .SetColumns(x => x.UpdatedTime == DateTime.Now)
                    .Where(x => ids.Contains(x.Id))
                    .ExecuteCommandAsync();

                // 删除相关步骤
                await _db.Updateable<UIAutoMationTemplateStep>()
                    .SetColumns(x => x.IsDeleted == true)
                    .SetColumns(x => x.UpdatedTime == DateTime.Now)
                    .Where(x => ids.Contains(x.SequenceId))
                    .ExecuteCommandAsync();

                _db.Ado.CommitTran();
                return true;
            }
            catch
            {
                _db.Ado.RollbackTran();
                return false;
            }
        }

        /// <summary>
        /// 启用/禁用序列
        /// </summary>
        public async Task<bool> SetActiveStatusAsync(int id, bool isActive)
        {
            return await _db.Updateable<UIAutoMationTemplateSequence>()
                .SetColumns(x => x.IsActive == isActive)
                .SetColumns(x => x.UpdatedTime == DateTime.Now)
                .Where(x => x.Id == id)
                .ExecuteCommandAsync() > 0;
        }
    }
}
