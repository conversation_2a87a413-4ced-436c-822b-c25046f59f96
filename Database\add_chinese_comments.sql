-- =============================================
-- 项目管理AI系统数据库字段中文注释脚本
-- 为所有表的字段添加中文描述注释
-- 版本: 1.0
-- 创建日期: 2024-12-19
-- =============================================

USE ProjectManagementAI;
GO

-- =============================================
-- 1. Users表字段注释
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户登录名，唯一标识，长度50字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Username';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户邮箱地址，用于登录和通知，长度100字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Email';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'密码哈希值，使用安全算法加密，长度255字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'PasswordHash';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'密码盐值，增强密码安全性，长度50字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Salt';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户真实姓名，长度50字符，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'RealName';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'手机号码，用于通知和验证，长度20字符，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Phone';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户头像URL地址，长度255字符，可为空',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Avatar';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户角色：User=普通用户, ProjectManager=项目经理, Developer=开发人员, Tester=测试人员, ProductManager=产品经理, Admin=管理员, SuperAdmin=超级管理员',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Role';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户状态：1=活跃, 2=非活跃, 3=锁定, 4=待验证, 5=已删除',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'最后登录时间，用于统计活跃度',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'LastLoginTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'最后登录IP地址，安全审计用',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'LastLoginIp';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'邮箱是否已验证：0=未验证, 1=已验证',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'EmailVerified';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'邮箱验证时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'EmailVerifiedTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'是否启用双因子认证：0=未启用, 1=已启用',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'TwoFactorEnabled';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'双因子认证密钥，用于TOTP算法',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'TwoFactorSecret';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'用户偏好设置，JSON格式存储（主题、语言、通知设置等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Preferences';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'刷新令牌，用于JWT认证',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'RefreshToken';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'刷新令牌过期时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'RefreshTokenExpiryTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'最后登录时间（JWT认证使用）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'LastLoginAt';

-- BaseEntity字段注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'记录创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'CreatedTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'记录最后更新时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'UpdatedTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'创建人ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'CreatedBy';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'更新人ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'UpdatedBy';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'软删除标记：0=未删除, 1=已删除',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'IsDeleted';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'删除时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'DeletedTime';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'删除人ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'DeletedBy';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'版本号，用于乐观锁并发控制',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Version';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', @value = N'备注信息，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users',
    @level2type = N'Column', @level2name = N'Remarks';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'系统用户信息管理表，存储所有用户的基本信息、权限、状态等，支持多角色权限管理、双因子认证、用户偏好设置',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Users';
GO

-- =============================================
-- 2. Projects表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目名称，用于显示和识别，长度200字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Name';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目详细描述，包括目标、范围等，无长度限制',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Description';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目编号，业务唯一标识（如：PROJ-2024-001），长度50字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'ProjectCode';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目负责人ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'OwnerId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目状态：Planning=规划中, InProgress=进行中, Testing=测试中, Deployed=已部署, Completed=已完成',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Status';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目优先级：Low=低, Medium=中, High=高, Critical=紧急',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Priority';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目计划开始日期',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'StartDate';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目计划结束日期',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'EndDate';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目进度百分比，范围0-100',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Progress';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'预估工时（小时），精度10,2',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'EstimatedHours';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'实际工时（小时），精度10,2',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'ActualHours';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目预算，精度18,2',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'Budget';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'技术栈信息，JSON格式存储',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects',
    @level2type = N'Column', @level2name = N'TechnologyStack';

-- Projects表的BaseEntity字段注释（与Users表相同，这里简化处理）
-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'项目信息管理表，存储软件开发项目的基本信息、状态、进度等，支持项目生命周期管理、多状态跟踪、优先级管理',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'Projects';
GO

-- =============================================
-- 3. AIModelConfigurations表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI模型配置唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3），长度100字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'ModelName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'模型用途类型：RequirementAnalysis=需求分析, CodeGeneration=代码生成, Testing=测试, Debugging=调试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'ModelType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'API端点URL地址，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'ApiEndpoint';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'API密钥（加密存储），长度255字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'ApiKey';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'模型参数配置，JSON格式存储（温度、最大令牌数等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'ModelParameters';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'是否启用该模型配置：0=禁用, 1=启用',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'IsActive';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'配置创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'配置最后更新时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations',
    @level2type = N'Column', @level2name = N'UpdatedAt';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI模型管理表，管理系统中使用的各种AI模型配置，支持多模型支持、参数配置、状态管理、API密钥管理',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'AIModelConfigurations';
GO

-- =============================================
-- 4. UserAIConfigurations表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户AI配置唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'UserId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI提供商名称：Azure, OpenAI, DeepSeek, Claude, Ollama等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ProviderName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ModelName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'模型用途类型：RequirementAnalysis=需求分析, CodeGeneration=代码生成, Testing=测试, Debugging=调试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ModelType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'API端点URL地址',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ApiEndpoint';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户个人API密钥（加密存储）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ApiKey';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'模型参数配置，JSON格式存储（温度、最大令牌数等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'ModelParameters';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'是否启用该配置：0=禁用, 1=启用',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'IsActive';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'是否为该用户的默认配置：0=否, 1=是',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations',
    @level2type = N'Column', @level2name = N'IsDefault';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户个人AI模型配置管理表，管理每个用户的个人AI模型配置和API密钥，支持个人API密钥、模型偏好、使用统计、成本控制',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserAIConfigurations';
GO

-- =============================================
-- 5. UserTaskMappings表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户任务映射唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'UserId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'任务类型：RequirementAnalysis=需求分析, CodeGeneration=代码生成, DocumentGeneration=文档生成, Embeddings=向量嵌入, Testing=测试, Debugging=调试',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'TaskType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI提供商名称：Azure, OpenAI, DeepSeek, Claude, Ollama等',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'ProviderName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'指定的AI模型名称（如：GPT-4, DeepSeek-Coder, Claude-3），可为空使用提供商默认模型',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'ModelName';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'是否启用该映射配置：0=禁用, 1=启用',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'IsActive';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'是否为该用户该任务类型的默认配置：0=否, 1=是',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'IsDefault';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'优先级，数字越小优先级越高，用于多个配置时的选择，范围1-100',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'Priority';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'任务特定的配置参数，JSON格式存储（温度、最大令牌数、超时时间等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'ConfigurationParameters';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'配置描述说明，长度500字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'Description';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'配置创建时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'CreatedAt';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'配置最后更新时间',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings',
    @level2type = N'Column', @level2name = N'UpdatedAt';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户个人任务映射配置管理表，管理每个用户的个人任务映射配置，指定不同任务类型使用的AI提供商，支持个性化任务映射、多提供商支持、优先级管理、默认配置',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'UserTaskMappings';
GO

-- =============================================
-- 6. RequirementConversations表字段注释
-- =============================================
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'对话记录唯一标识ID，自增主键',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'Id';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'所属项目ID，关联Projects表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'ProjectId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户ID，关联Users表',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'UserId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'对话会话ID，用于标识同一次对话会话，长度50字符',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'ConversationId';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'用户输入的消息内容（需求描述、问题等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'UserMessage';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI系统的回复内容（分析结果、澄清问题等）',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'AIResponse';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'消息类型：Requirement=需求收集, Clarification=澄清问题, Confirmation=确认需求',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'MessageType';

EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'对话发生的时间戳',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations',
    @level2type = N'Column', @level2name = N'Timestamp';

-- 表注释
EXEC sp_addextendedproperty
    @name = N'MS_Description', @value = N'AI需求对话管理表，记录用户与AI系统的需求收集对话过程，支持对话历史追踪、需求澄清过程、AI交互记录',
    @level0type = N'Schema', @level0name = N'dbo',
    @level1type = N'Table', @level1name = N'RequirementConversations';
GO
