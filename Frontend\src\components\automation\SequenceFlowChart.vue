<!-- eslint-disable vue/no-unused-components -->
<template>
  <div class="sequence-flow-chart">
    <div class="chart-header">
      <div class="chart-title">
        <el-icon><Share /></el-icon>
        <span>流程图视图</span>
      </div>
      <div class="chart-actions">
        <el-button size="small" @click="refreshChart">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="exportChart">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <div class="chart-container">
      <div v-if="loading" class="chart-loading">
        <div class="loading-spinner"></div>
        <span>生成流程图中...</span>
      </div>

      <div v-else-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button size="small" type="primary" @click="generateChart">重试</el-button>
      </div>

      <div v-else class="mermaid-container">
        <div ref="mermaidRef" class="mermaid-diagram" v-html="renderedSvg">
        </div>
      </div>
    </div>

    <!-- 图例说明 -->
    <div class="chart-legend">
      <div class="legend-title">图例说明</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color basic"></div>
          <span>基础操作</span>
        </div>
        <div class="legend-item">
          <div class="legend-color condition"></div>
          <span>条件判断</span>
        </div>
        <div class="legend-item">
          <div class="legend-color loop"></div>
          <span>循环控制</span>
        </div>
        <div class="legend-item">
          <div class="legend-color jump"></div>
          <span>跳转/退出</span>
        </div>
        <div class="legend-item">
          <div class="legend-color disabled"></div>
          <span>已禁用</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElButton, ElIcon } from 'element-plus'
import { Share, Refresh, Download, Warning } from '@element-plus/icons-vue'
import mermaid from 'mermaid'
import type { TemplateSequence } from '@/services/customTemplate'

// 临时类型定义，直到服务中添加完整类型
interface TemplateStep {
  id?: number
  stepOrder?: number
  actionType: string
  description?: string
  parameters?: any
  isActive?: boolean
  conditionExpression?: string
  jumpToStepId?: number
  loopCount?: number
  loopVariable?: string
  groupId?: string
}

// Props
interface Props {
  sequence: TemplateSequence
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const error = ref('')
const mermaidCode = ref('')
const renderedSvg = ref('')
const mermaidRef = ref<HTMLElement>()

// 生命周期
onMounted(() => {
  initMermaid()
  generateChart()
})

// 监听序列变化
watch(() => props.sequence, () => {
  generateChart()
}, { deep: true })

// 初始化 Mermaid
const initMermaid = () => {
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: 'basis'
    },
    themeVariables: {
      primaryColor: '#409eff',
      primaryTextColor: '#ffffff',
      primaryBorderColor: '#409eff',
      lineColor: '#606266',
      sectionBkgColor: '#f8f9fa',
      altSectionBkgColor: '#ffffff',
      gridColor: '#e4e7ed',
      secondaryColor: '#67c23a',
      tertiaryColor: '#e6a23c'
    },
    securityLevel: 'loose'
  })
}

// 生成流程图
const generateChart = async () => {
  if (!props.sequence?.steps?.length) {
    error.value = '暂无步骤数据'
    return
  }

  try {
    loading.value = true
    error.value = ''

    const code = generateMermaidCode(props.sequence.steps)
    mermaidCode.value = code

    // 调试信息
    console.log('生成的 Mermaid 代码:', code)

    await nextTick()
    await renderMermaid()

  } catch (err) {
    console.error('生成流程图失败:', err)
    error.value = '生成流程图失败，请检查步骤配置'
  } finally {
    loading.value = false
  }
}

// 生成 Mermaid 代码
const generateMermaidCode = (steps: TemplateStep[]): string => {
  const sortedSteps = [...steps].sort((a, b) => (a.stepOrder || 0) - (b.stepOrder || 0))

  let code = 'flowchart TD\n'

  // 添加开始节点
  code += '    Start([开始])\n'

  // 生成步骤节点定义
  sortedSteps.forEach((step, index) => {
    const stepId = `Step${step.stepOrder || index + 1}`
    code += generateStepNode(step, stepId)
  })

  // 添加结束节点
  code += '    End([结束])\n\n'

  // 生成简化的连接线 - 避免复杂的条件判断
  code += '    Start --> Step1\n'

  sortedSteps.forEach((step, index) => {
    const stepId = `Step${step.stepOrder || index + 1}`
    const nextStepId = index < sortedSteps.length - 1
      ? `Step${sortedSteps[index + 1].stepOrder || index + 2}`
      : 'End'

    // 简化连接逻辑，只处理基本的顺序连接
    if (step.actionType === 'condition') {
      code += `    ${stepId} -->|是| ${nextStepId}\n`
      code += `    ${stepId} -->|否| End\n`
    } else if (step.actionType === 'loop_end') {
      code += `    ${stepId} -->|继续| Step${Math.max(1, (step.stepOrder || index + 1) - 3)}\n`
      code += `    ${stepId} -->|结束| ${nextStepId}\n`
    } else {
      code += `    ${stepId} --> ${nextStepId}\n`
    }
  })

  // 添加样式
  code += '\n' + generateStyles(sortedSteps)

  return code
}

// 生成步骤节点
const generateStepNode = (step: TemplateStep, stepId: string): string => {
  const label = getStepLabel(step)
  const shape = getStepShape(step.actionType)

  return `    ${stepId}${shape.start}"${label}"${shape.end}\n`
}

// 生成步骤连接
const generateStepConnection = (
  step: TemplateStep,
  stepId: string,
  nextStepId: string,
  allSteps: TemplateStep[]
): string => {
  let connections = ''

  switch (step.actionType) {
    case 'condition':
      // 条件判断有两个分支
      connections += `    ${stepId} -->|是| ${getConditionTrueTarget(step, allSteps)}\n`
      connections += `    ${stepId} -->|否| ${getConditionFalseTarget(step, allSteps)}\n`
      break

    case 'loop':
      // 循环开始连接到下一步
      connections += `    ${stepId} --> ${nextStepId}\n`
      break

    case 'loop_end':
      // 循环结束，可能回到循环开始或继续
      const loopStart = findLoopStart(step, allSteps)
      if (loopStart) {
        connections += `    ${stepId} -->|继续循环| Step${loopStart.stepOrder}\n`
        connections += `    ${stepId} -->|退出循环| ${nextStepId}\n`
      } else {
        connections += `    ${stepId} --> ${nextStepId}\n`
      }
      break

    case 'jump':
      // 跳转到指定步骤
      const targetStep = findStepById(step.jumpToStepId, allSteps)
      if (targetStep) {
        connections += `    ${stepId} --> Step${targetStep.stepOrder}\n`
      } else {
        connections += `    ${stepId} --> End\n`
      }
      break

    case 'exit':
      // 直接退出
      connections += `    ${stepId} --> End\n`
      break

    default:
      // 普通步骤连接到下一步
      if (nextStepId !== 'End') {
        if (step.isActive) {
          connections += `    ${stepId} --> ${nextStepId}\n`
        } else {
          connections += `    ${stepId} -.-> ${nextStepId}\n`
        }
      } else {
        connections += `    ${stepId} --> End\n`
      }
      break
  }

  return connections
}

// 获取步骤标签
const getStepLabel = (step: TemplateStep): string => {
  const actionLabel = getActionTypeLabel(step.actionType)
  const description = step.description || actionLabel
  const stepNum = step.stepOrder || ''

  let label = `${stepNum}. ${description}`

  // 添加额外信息 - 使用简化的标签，避免换行问题
  if (step.conditionExpression) {
    // 简化条件表达式显示，并转义引号
    let shortCondition = step.conditionExpression.length > 15
      ? step.conditionExpression.substring(0, 15) + '...'
      : step.conditionExpression
    // 转义引号和特殊字符
    shortCondition = shortCondition.replace(/"/g, "'").replace(/\{/g, '').replace(/\}/g, '')
    label += ` [${shortCondition}]`
  }

  if (step.loopCount) {
    label += ` (${step.loopCount}次)`
  }

  if (!step.isActive) {
    label += ' [已禁用]'
  }

  return label
}

// 获取步骤形状
const getStepShape = (actionType: string): { start: string, end: string } => {
  switch (actionType) {
    case 'condition':
    case 'branch':
      return { start: '{', end: '}' } // 菱形
    case 'loop':
    case 'loop_end':
      return { start: '[[', end: ']]' } // 子程序形状
    case 'jump':
    case 'exit':
      return { start: '>', end: ']' } // 旗帜形状
    default:
      return { start: '[', end: ']' } // 矩形
  }
}

// 辅助函数
const getActionTypeLabel = (actionType: string): string => {
  const labels: Record<string, string> = {
    'click': '点击', 'wait': '等待', 'input': '输入', 'delay': '延迟',
    'screenshot': '截图', 'verify': '验证', 'scroll': '滚动', 'key_press': '按键',
    'condition': '条件判断', 'loop': '循环开始', 'loop_end': '循环结束',
    'branch': '分支执行', 'jump': '跳转', 'exit': '退出'
  }
  return labels[actionType] || actionType
}

const getConditionTrueTarget = (step: TemplateStep, allSteps: TemplateStep[]): string => {
  // 解析参数中的true_target
  try {
    const params = typeof step.parameters === 'string'
      ? JSON.parse(step.parameters)
      : step.parameters
    const target = params?.true_target
    if (target) {
      const targetStep = allSteps.find(s => s.id === target || s.stepOrder === target)
      return targetStep ? `Step${targetStep.stepOrder}` : 'End'
    }
  } catch (e) {
    console.warn('解析条件参数失败:', e)
  }

  // 默认继续下一步
  const currentIndex = allSteps.findIndex(s => s.id === step.id)
  const nextStep = allSteps[currentIndex + 1]
  return nextStep ? `Step${nextStep.stepOrder}` : 'End'
}

const getConditionFalseTarget = (step: TemplateStep, allSteps: TemplateStep[]): string => {
  // 解析参数中的false_target
  try {
    const params = typeof step.parameters === 'string'
      ? JSON.parse(step.parameters)
      : step.parameters
    const target = params?.false_target
    if (target) {
      const targetStep = allSteps.find(s => s.id === target || s.stepOrder === target)
      return targetStep ? `Step${targetStep.stepOrder}` : 'End'
    }
  } catch (e) {
    console.warn('解析条件参数失败:', e)
  }

  // 默认继续下一步
  const currentIndex = allSteps.findIndex(s => s.id === step.id)
  const nextStep = allSteps[currentIndex + 1]
  return nextStep ? `Step${nextStep.stepOrder}` : 'End'
}

const findLoopStart = (loopEndStep: TemplateStep, allSteps: TemplateStep[]): TemplateStep | null => {
  const groupId = loopEndStep.groupId
  if (!groupId) return null

  return allSteps.find(step =>
    step.actionType === 'loop' &&
    step.groupId === groupId &&
    (step.stepOrder || 0) < (loopEndStep.stepOrder || 0)
  ) || null
}

const findStepById = (stepId: number | undefined, allSteps: TemplateStep[]): TemplateStep | null => {
  if (!stepId) return null
  return allSteps.find(step => step.id === stepId) || null
}

// 生成样式
const generateStyles = (steps: TemplateStep[]): string => {
  let styles = '\n'

  steps.forEach((step, index) => {
    const stepId = `Step${step.stepOrder || index + 1}`
    const styleClass = getStepStyleClass(step)
    styles += `    class ${stepId} ${styleClass}\n`
  })

  // 定义样式类
  styles += `
    classDef basic fill:#409eff,stroke:#409eff,stroke-width:2px,color:#fff
    classDef condition fill:#e6a23c,stroke:#e6a23c,stroke-width:2px,color:#fff
    classDef loop fill:#67c23a,stroke:#67c23a,stroke-width:2px,color:#fff
    classDef jump fill:#f56c6c,stroke:#f56c6c,stroke-width:2px,color:#fff
    classDef disabled fill:#c0c4cc,stroke:#c0c4cc,stroke-width:2px,color:#fff
  `

  return styles
}

const getStepStyleClass = (step: TemplateStep): string => {
  if (!step.isActive) return 'disabled'

  switch (step.actionType) {
    case 'condition':
    case 'branch':
      return 'condition'
    case 'loop':
    case 'loop_end':
      return 'loop'
    case 'jump':
    case 'exit':
      return 'jump'
    default:
      return 'basic'
  }
}

// 渲染 Mermaid 图表
const renderMermaid = async () => {
  if (!mermaidCode.value) return

  try {
    // 生成唯一ID
    const id = `mermaid-${Date.now()}`

    // 渲染图表
    const { svg } = await mermaid.render(id, mermaidCode.value)
    renderedSvg.value = svg

  } catch (err) {
    console.error('渲染 Mermaid 图表失败:', err)
    console.error('Mermaid 代码:', mermaidCode.value)

    // 尝试验证 Mermaid 代码的基本语法
    const lines = mermaidCode.value.split('\n')
    lines.forEach((line, index) => {
      if (line.includes('"') && !line.match(/^.*"[^"]*".*$/)) {
        console.error(`第 ${index + 1} 行可能有引号问题:`, line)
      }
    })

    throw new Error(`图表渲染失败: ${(err as any)?.message || err}`)
  }
}

// 刷新图表
const refreshChart = () => {
  generateChart()
}

// 导出图表
const exportChart = () => {
  if (!renderedSvg.value) return

  // 创建下载链接
  const svgBlob = new Blob([renderedSvg.value], { type: 'image/svg+xml;charset=utf-8' })
  const svgUrl = URL.createObjectURL(svgBlob)

  const downloadLink = document.createElement('a')
  downloadLink.href = svgUrl
  downloadLink.download = `${props.sequence.name}-flowchart.svg`
  document.body.appendChild(downloadLink)
  downloadLink.click()
  document.body.removeChild(downloadLink)
  URL.revokeObjectURL(svgUrl)

  ElMessage.success('流程图已导出')
}
</script>

<style scoped lang="scss">
.sequence-flow-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .chart-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #303133;
    }

    .chart-actions {
      display: flex;
      gap: 8px;
    }
  }

  .chart-container {
    min-height: 400px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background-color: #ffffff;

    .chart-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      gap: 16px;
      color: #909399;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #e4e7ed;
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }

    .chart-error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      gap: 16px;
      color: #f56c6c;
    }

    .mermaid-container {
      padding: 20px;
      overflow: auto;

      .mermaid-diagram {
        display: flex;
        justify-content: center;

        :deep(svg) {
          max-width: 100%;
          height: auto;
        }
      }
    }
  }

  .chart-legend {
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .legend-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 12px;
    }

    .legend-items {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: #606266;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 3px;

          &.basic { background-color: #409eff; }
          &.condition { background-color: #e6a23c; }
          &.loop { background-color: #67c23a; }
          &.jump { background-color: #f56c6c; }
          &.disabled { background-color: #c0c4cc; }
        }
      }
    }
  }
}
</style>
