using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ProjectManagement.AI.Services
{
    /// <summary>
    /// 流程控制执行引擎
    /// 负责处理条件判断、循环执行、跳转等流程控制逻辑
    /// </summary>
    public class FlowControlExecutionEngine
    {
        private readonly ILogger<FlowControlExecutionEngine> _logger;
        private readonly Dictionary<string, object> _variables;
        private readonly Stack<LoopContext> _loopStack;

        public FlowControlExecutionEngine(ILogger<FlowControlExecutionEngine> logger)
        {
            _logger = logger;
            _variables = new Dictionary<string, object>();
            _loopStack = new Stack<LoopContext>();
        }

        /// <summary>
        /// 执行序列步骤
        /// </summary>
        /// <param name="steps">步骤列表</param>
        /// <param name="initialVariables">初始变量</param>
        /// <returns>执行结果</returns>
        public async Task<ExecutionResult> ExecuteSequenceAsync(
            List<UIAutoMationTemplateStep> steps,
            Dictionary<string, object>? initialVariables = null)
        {
            try
            {
                // 初始化变量
                if (initialVariables != null)
                {
                    foreach (var kvp in initialVariables)
                    {
                        _variables[kvp.Key] = kvp.Value;
                    }
                }

                var result = new ExecutionResult
                {
                    Success = true,
                    StartTime = DateTime.Now,
                    ExecutedSteps = new List<StepExecutionResult>()
                };

                int currentStepIndex = 0;
                while (currentStepIndex < steps.Count)
                {
                    var step = steps[currentStepIndex];

                    if (!step.IsActive)
                    {
                        _logger.LogInformation("跳过已禁用的步骤: {StepId}", step.Id);
                        currentStepIndex++;
                        continue;
                    }

                    var stepResult = await ExecuteStepAsync(step, steps);
                    result.ExecutedSteps.Add(stepResult);

                    if (!stepResult.Success)
                    {
                        result.Success = false;
                        result.ErrorMessage = stepResult.ErrorMessage;
                        break;
                    }

                    // 处理流程控制
                    var nextStepIndex = HandleFlowControl(step, stepResult, steps, currentStepIndex);
                    if (nextStepIndex == -1) // 退出序列
                    {
                        break;
                    }

                    currentStepIndex = nextStepIndex;
                }

                result.EndTime = DateTime.Now;
                result.Duration = (result.EndTime - result.StartTime).TotalMilliseconds;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行序列时发生错误");
                return new ExecutionResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 执行单个步骤
        /// </summary>
        private async Task<StepExecutionResult> ExecuteStepAsync(
            UIAutoMationTemplateStep step,
            List<UIAutoMationTemplateStep> allSteps)
        {
            var stepResult = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                StartTime = DateTime.Now,
                Success = true
            };

            try
            {
                _logger.LogInformation("执行步骤: {StepId}, 类型: {ActionType}", step.Id, step.ActionType);

                switch (step.ActionType)
                {
                    case ActionTypes.Condition:
                        stepResult = await ExecuteConditionStep(step);
                        break;

                    case ActionTypes.Loop:
                        stepResult = await ExecuteLoopStep(step);
                        break;

                    case ActionTypes.LoopEnd:
                        stepResult = await ExecuteLoopEndStep(step);
                        break;

                    case ActionTypes.Jump:
                        stepResult = await ExecuteJumpStep(step);
                        break;

                    case ActionTypes.Exit:
                        stepResult = await ExecuteExitStep(step);
                        break;

                    default:
                        // 基础操作类型，这里只是模拟执行
                        stepResult = await ExecuteBasicActionStep(step);
                        break;
                }

                stepResult.EndTime = DateTime.Now;
                stepResult.Duration = (stepResult.EndTime - stepResult.StartTime).TotalMilliseconds;

                return stepResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行步骤失败: {StepId}", step.Id);
                stepResult.Success = false;
                stepResult.ErrorMessage = ex.Message;
                stepResult.EndTime = DateTime.Now;
                return stepResult;
            }
        }

        /// <summary>
        /// 执行条件判断步骤
        /// </summary>
        private async Task<StepExecutionResult> ExecuteConditionStep(UIAutoMationTemplateStep step)
        {
            var result = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true
            };

            if (string.IsNullOrEmpty(step.ConditionExpression))
            {
                result.Success = false;
                result.ErrorMessage = "条件表达式不能为空";
                return result;
            }

            // 评估条件表达式
            var conditionResult = EvaluateCondition(step.ConditionExpression);
            result.Result = conditionResult;

            _logger.LogInformation("条件判断结果: {Result}, 表达式: {Expression}",
                conditionResult, step.ConditionExpression);

            return result;
        }

        /// <summary>
        /// 执行循环开始步骤
        /// </summary>
        private async Task<StepExecutionResult> ExecuteLoopStep(UIAutoMationTemplateStep step)
        {
            var result = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true
            };

            var loopContext = new LoopContext
            {
                GroupId = step.GroupId ?? $"loop_{step.Id}",
                LoopVariable = step.LoopVariable ?? "i",
                MaxIterations = step.LoopCount ?? 10,
                CurrentIteration = 0
            };

            _loopStack.Push(loopContext);

            // 设置循环变量
            _variables[loopContext.LoopVariable] = loopContext.CurrentIteration;

            _logger.LogInformation("开始循环: {GroupId}, 最大次数: {MaxIterations}",
                loopContext.GroupId, loopContext.MaxIterations);

            return result;
        }

        /// <summary>
        /// 执行循环结束步骤
        /// </summary>
        private async Task<StepExecutionResult> ExecuteLoopEndStep(UIAutoMationTemplateStep step)
        {
            var result = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true
            };

            if (_loopStack.Count == 0)
            {
                result.Success = false;
                result.ErrorMessage = "没有匹配的循环开始";
                return result;
            }

            var loopContext = _loopStack.Peek();
            loopContext.CurrentIteration++;

            // 更新循环变量
            _variables[loopContext.LoopVariable] = loopContext.CurrentIteration;

            // 检查是否继续循环
            var shouldContinue = loopContext.MaxIterations == -1 ||
                               loopContext.CurrentIteration < loopContext.MaxIterations;

            if (!shouldContinue)
            {
                _loopStack.Pop();
                _logger.LogInformation("循环结束: {GroupId}, 总次数: {TotalIterations}",
                    loopContext.GroupId, loopContext.CurrentIteration);
            }
            else
            {
                _logger.LogInformation("循环继续: {GroupId}, 当前次数: {CurrentIteration}",
                    loopContext.GroupId, loopContext.CurrentIteration);
            }

            result.Result = shouldContinue;
            return result;
        }

        /// <summary>
        /// 执行跳转步骤
        /// </summary>
        private Task<StepExecutionResult> ExecuteJumpStep(UIAutoMationTemplateStep step)
        {
            var result = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true,
                Result = step.JumpToStepId
            };

            _logger.LogInformation("跳转到步骤: {TargetStepId}", step.JumpToStepId);
            return Task.FromResult(result);
        }

        /// <summary>
        /// 执行退出步骤
        /// </summary>
        private Task<StepExecutionResult> ExecuteExitStep(UIAutoMationTemplateStep step)
        {
            var result = new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true,
                Result = "exit"
            };

            _logger.LogInformation("退出序列执行");
            return Task.FromResult(result);
        }

        /// <summary>
        /// 执行基础操作步骤（模拟）
        /// </summary>
        private async Task<StepExecutionResult> ExecuteBasicActionStep(UIAutoMationTemplateStep step)
        {
            // 这里只是模拟执行，实际应该调用具体的UI自动化操作
            await Task.Delay(100); // 模拟执行时间

            return new StepExecutionResult
            {
                StepId = step.Id,
                ActionType = step.ActionType,
                Success = true,
                Result = "completed"
            };
        }

        /// <summary>
        /// 处理流程控制逻辑
        /// </summary>
        private int HandleFlowControl(
            UIAutoMationTemplateStep step,
            StepExecutionResult stepResult,
            List<UIAutoMationTemplateStep> allSteps,
            int currentIndex)
        {
            switch (step.ActionType)
            {
                case ActionTypes.Condition:
                    return HandleConditionFlow(step, stepResult, allSteps, currentIndex);

                case ActionTypes.LoopEnd:
                    return HandleLoopEndFlow(step, stepResult, allSteps, currentIndex);

                case ActionTypes.Jump:
                    return HandleJumpFlow(step, stepResult, allSteps, currentIndex);

                case ActionTypes.Exit:
                    return -1; // 退出序列

                default:
                    return currentIndex + 1; // 继续下一步
            }
        }

        /// <summary>
        /// 处理条件判断的流程控制
        /// </summary>
        private int HandleConditionFlow(
            UIAutoMationTemplateStep step,
            StepExecutionResult stepResult,
            List<UIAutoMationTemplateStep> allSteps,
            int currentIndex)
        {
            var conditionResult = (bool)(stepResult.Result ?? false);

            // 解析参数中的流程控制配置
            var parameters = ParseParameters(step.Parameters);

            if (conditionResult)
            {
                // 条件为真
                var trueAction = parameters.GetValueOrDefault("true_action", "continue")?.ToString();
                if (trueAction == "jump_to_step")
                {
                    var targetStepId = parameters.GetValueOrDefault("true_target");
                    return FindStepIndex(allSteps, targetStepId);
                }
                return currentIndex + 1;
            }
            else
            {
                // 条件为假
                var falseAction = parameters.GetValueOrDefault("false_action", "continue")?.ToString();
                if (falseAction == "skip_to_step")
                {
                    var targetStepId = parameters.GetValueOrDefault("false_target");
                    return FindStepIndex(allSteps, targetStepId);
                }
                else if (falseAction == "exit")
                {
                    return -1;
                }
                return currentIndex + 1;
            }
        }

        /// <summary>
        /// 处理循环结束的流程控制
        /// </summary>
        private int HandleLoopEndFlow(
            UIAutoMationTemplateStep step,
            StepExecutionResult stepResult,
            List<UIAutoMationTemplateStep> allSteps,
            int currentIndex)
        {
            var shouldContinue = (bool)(stepResult.Result ?? false);

            if (shouldContinue && _loopStack.Count > 0)
            {
                var loopContext = _loopStack.Peek();
                // 找到对应的循环开始步骤
                var loopStartIndex = FindLoopStartIndex(allSteps, loopContext.GroupId);
                return loopStartIndex + 1; // 跳转到循环体的第一个步骤
            }

            return currentIndex + 1; // 继续下一步
        }

        /// <summary>
        /// 处理跳转的流程控制
        /// </summary>
        private int HandleJumpFlow(
            UIAutoMationTemplateStep step,
            StepExecutionResult stepResult,
            List<UIAutoMationTemplateStep> allSteps,
            int currentIndex)
        {
            var targetStepId = stepResult.Result;
            return FindStepIndex(allSteps, targetStepId);
        }

        /// <summary>
        /// 评估条件表达式
        /// </summary>
        private bool EvaluateCondition(string expression)
        {
            try
            {
                // 替换变量
                var evaluatedExpression = ReplaceVariables(expression);

                // 这里使用简单的表达式评估
                // 实际项目中可以使用更强大的表达式引擎，如 NCalc
                return EvaluateSimpleExpression(evaluatedExpression);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估条件表达式失败: {Expression}", expression);
                return false;
            }
        }

        /// <summary>
        /// 替换表达式中的变量
        /// </summary>
        private string ReplaceVariables(string expression)
        {
            var pattern = @"\{(\w+)\}";
            return Regex.Replace(expression, pattern, match =>
            {
                var variableName = match.Groups[1].Value;
                if (_variables.TryGetValue(variableName, out var value))
                {
                    return value?.ToString() ?? "null";
                }
                return "null";
            });
        }

        /// <summary>
        /// 评估简单表达式（示例实现）
        /// </summary>
        private bool EvaluateSimpleExpression(string expression)
        {
            // 这是一个简化的实现，实际项目中应该使用专业的表达式引擎

            // 处理一些常见的比较操作
            if (expression.Contains("==="))
            {
                var parts = expression.Split("===", StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2)
                {
                    var left = parts[0].Trim().Trim('"', '\'');
                    var right = parts[1].Trim().Trim('"', '\'');
                    return left == right;
                }
            }

            if (expression.Contains(">="))
            {
                var parts = expression.Split(">=", StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2 &&
                    double.TryParse(parts[0].Trim(), out var left) &&
                    double.TryParse(parts[1].Trim(), out var right))
                {
                    return left >= right;
                }
            }

            if (expression.Contains(">"))
            {
                var parts = expression.Split(">", StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2 &&
                    double.TryParse(parts[0].Trim(), out var left) &&
                    double.TryParse(parts[1].Trim(), out var right))
                {
                    return left > right;
                }
            }

            // 处理布尔值
            if (bool.TryParse(expression.Trim(), out var boolResult))
            {
                return boolResult;
            }

            // 默认返回 false
            return false;
        }

        /// <summary>
        /// 解析参数JSON
        /// </summary>
        private Dictionary<string, object> ParseParameters(string? parametersJson)
        {
            if (string.IsNullOrEmpty(parametersJson))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(parametersJson)
                       ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 查找步骤索引
        /// </summary>
        private int FindStepIndex(List<UIAutoMationTemplateStep> steps, object? targetStepId)
        {
            if (targetStepId == null) return -1;

            if (int.TryParse(targetStepId.ToString(), out var stepId))
            {
                for (int i = 0; i < steps.Count; i++)
                {
                    if (steps[i].Id == stepId)
                        return i;
                }
            }

            return -1;
        }

        /// <summary>
        /// 查找循环开始步骤的索引
        /// </summary>
        private int FindLoopStartIndex(List<UIAutoMationTemplateStep> steps, string groupId)
        {
            for (int i = 0; i < steps.Count; i++)
            {
                if (steps[i].ActionType == ActionTypes.Loop &&
                    steps[i].GroupId == groupId)
                {
                    return i;
                }
            }
            return -1;
        }

        /// <summary>
        /// 设置变量值
        /// </summary>
        public void SetVariable(string name, object value)
        {
            _variables[name] = value;
        }

        /// <summary>
        /// 获取变量值
        /// </summary>
        public object? GetVariable(string name)
        {
            return _variables.TryGetValue(name, out var value) ? value : null;
        }

        /// <summary>
        /// 获取所有变量
        /// </summary>
        public Dictionary<string, object> GetAllVariables()
        {
            return new Dictionary<string, object>(_variables);
        }
    }

    /// <summary>
    /// 循环上下文
    /// </summary>
    public class LoopContext
    {
        public string GroupId { get; set; } = string.Empty;
        public string LoopVariable { get; set; } = string.Empty;
        public int MaxIterations { get; set; }
        public int CurrentIteration { get; set; }
    }

    /// <summary>
    /// 执行结果
    /// </summary>
    public class ExecutionResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public double Duration { get; set; }
        public List<StepExecutionResult> ExecutedSteps { get; set; } = new();
    }

    /// <summary>
    /// 步骤执行结果
    /// </summary>
    public class StepExecutionResult
    {
        public int StepId { get; set; }
        public string ActionType { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public object? Result { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public double Duration { get; set; }
    }
}
