using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectManagement.AI.Interfaces;
using ProjectManagement.AI.Models;
using ProjectManagement.Core.DTOs.AI;
using System.Text;
using System.Text.Json;

namespace ProjectManagement.AI.Providers;

/// <summary>
/// OpenAI服务提供商
/// </summary>
public class OpenAIProvider : IAIProvider
{
    private readonly ILogger<OpenAIProvider> _logger;
    private readonly HttpClient _httpClient;
    private readonly OpenAIConfig _config;
    private readonly AIUsageStats _usageStats;

    public string Name => "OpenAI";
    public bool IsAvailable { get; private set; } = true;
    public List<string> SupportedModels => new() { "gpt-4", "gpt-3.5-turbo", "text-embedding-ada-002" };

    public OpenAIProvider(
        ILogger<OpenAIProvider> logger,
        HttpClient httpClient,
        IOptions<OpenAIConfig> config)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config.Value;
        _usageStats = new AIUsageStats();

        // 配置HttpClient
        if (!string.IsNullOrEmpty(_config.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.ApiKey);
        }

        _httpClient.BaseAddress = new Uri(_config.Endpoint);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    /// <summary>
    /// 生成文本
    /// </summary>
    public async Task<string> GenerateAsync(string prompt, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("OpenAI开始生成文本，模型: {Model}", config.Model);

            // 使用传入配置中的API密钥，如果没有则使用默认配置
            var apiKey = !string.IsNullOrEmpty(config.ApiKey) ? config.ApiKey : _config.ApiKey;
            var endpoint = !string.IsNullOrEmpty(config.Endpoint) ? config.Endpoint : _config.Endpoint;

            if (string.IsNullOrEmpty(apiKey))
            {
                throw new Exception("OpenAI API密钥未配置");
            }

            // 调试日志：显示API密钥的前几个和后几个字符
            var maskedApiKey = apiKey.Length > 8 ?
                $"{apiKey.Substring(0, 4)}...{apiKey.Substring(apiKey.Length - 4)}" :
                "****";
            _logger.LogInformation("使用API密钥: {MaskedApiKey}, 长度: {Length}", maskedApiKey, apiKey.Length);

            var requestBody = new
            {
                model = config.Model,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = config.MaxTokens,
                temperature = config.Temperature,
                top_p = config.TopP,
                frequency_penalty = config.FrequencyPenalty,
                presence_penalty = config.PresencePenalty
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // 创建请求消息并设置Authorization头
            var requestUrl = endpoint.TrimEnd('/') + "/chat/completions";
            _logger.LogInformation("OpenAI请求URL: {RequestUrl}, 模型: {Model}", requestUrl, config.Model);

            var request = new HttpRequestMessage(HttpMethod.Post, requestUrl)
            {
                Content = content
            };
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

            // 创建一个新的HttpClient实例来避免BaseAddress冲突
            using var customHttpClient = new HttpClient();
            var timeoutSeconds = config.TimeoutSeconds > 0 ? config.TimeoutSeconds : _config.TimeoutSeconds;
            customHttpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);

            var response = await customHttpClient.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("OpenAI API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"OpenAI API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var generatedText = responseData
                .GetProperty("choices")[0]
                .GetProperty("message")
                .GetProperty("content")
                .GetString() ?? string.Empty;

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("OpenAI文本生成完成，长度: {Length}", generatedText.Length);
            return generatedText;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "OpenAI文本生成失败");
            throw;
        }
    }

    /// <summary>
    /// 获取向量嵌入
    /// </summary>
    public async Task<float[]> GetEmbeddingsAsync(string text, AIModelConfig config)
    {
        var startTime = DateTime.Now;

        try
        {
            _logger.LogInformation("OpenAI开始获取向量嵌入");

            var requestBody = new
            {
                input = text,
                model = "text-embedding-ada-002"
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("embeddings", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("OpenAI嵌入API调用失败: {StatusCode}, {Content}",
                    response.StatusCode, errorContent);
                throw new Exception($"OpenAI嵌入API调用失败: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

            var embeddingArray = responseData
                .GetProperty("data")[0]
                .GetProperty("embedding")
                .EnumerateArray()
                .Select(x => x.GetSingle())
                .ToArray();

            UpdateUsageStats(startTime, true, responseData);

            _logger.LogInformation("OpenAI向量嵌入获取完成，维度: {Dimension}", embeddingArray.Length);
            return embeddingArray;
        }
        catch (Exception ex)
        {
            UpdateUsageStats(startTime, false);
            _logger.LogError(ex, "OpenAI向量嵌入获取失败");
            throw;
        }
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            var testConfig = new AIModelConfig
            {
                Model = "gpt-3.5-turbo",
                MaxTokens = 10,
                Temperature = 0.1f
            };

            var result = await GenerateAsync("Hello", testConfig);
            IsAvailable = !string.IsNullOrEmpty(result);

            _logger.LogInformation("OpenAI健康检查完成，状态: {Status}", IsAvailable ? "正常" : "异常");
            return IsAvailable;
        }
        catch (Exception ex)
        {
            IsAvailable = false;
            _logger.LogError(ex, "OpenAI健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取使用统计
    /// </summary>
    public async Task<AIUsageStats> GetUsageStatsAsync()
    {
        return await Task.FromResult(_usageStats);
    }

    #region 私有方法

    /// <summary>
    /// 更新使用统计
    /// </summary>
    private void UpdateUsageStats(DateTime startTime, bool success, JsonElement? responseData = null)
    {
        var responseTime = (DateTime.Now - startTime).TotalMilliseconds;

        _usageStats.TotalRequests++;
        if (success)
        {
            _usageStats.SuccessfulRequests++;
        }
        else
        {
            _usageStats.FailedRequests++;
        }

        // 更新平均响应时间
        _usageStats.AverageResponseTime =
            (_usageStats.AverageResponseTime * (_usageStats.TotalRequests - 1) + responseTime) / _usageStats.TotalRequests;

        // 更新Token使用量
        if (success && responseData.HasValue)
        {
            try
            {
                if (responseData.Value.TryGetProperty("usage", out var usage))
                {
                    if (usage.TryGetProperty("total_tokens", out var totalTokens))
                    {
                        _usageStats.TotalTokens += totalTokens.GetInt64();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析Token使用量失败");
            }
        }

        _usageStats.LastUpdated = DateTime.Now;
    }

    #endregion
}

/// <summary>
/// OpenAI配置
/// </summary>
public class OpenAIConfig
{
    /// <summary>
    /// API端点
    /// </summary>
    public string Endpoint { get; set; } = "https://api.openai.com/v1/";

    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300; // 5分钟超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}
