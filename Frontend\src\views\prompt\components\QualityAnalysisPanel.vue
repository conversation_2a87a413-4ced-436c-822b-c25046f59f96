<template>
  <div class="quality-analysis-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>质量分析</span>
          <el-button type="text" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else class="quality-content">
        <!-- 质量指标概览 -->
        <div class="quality-overview">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="平均质量评分" :value="data.avgQualityScore" :precision="1" suffix="/10" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="高质量回答率" :value="data.highQualityRate" suffix="%" :precision="1" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="用户满意度" :value="data.userSatisfaction" suffix="%" :precision="1" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="重试率" :value="data.retryRate" suffix="%" :precision="2">
                <template #suffix>
                  <span :class="data.retryRate > 10 ? 'retry-high' : 'retry-normal'">
                    {{ data.retryRate.toFixed(2) }}%
                  </span>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>

        <!-- 质量分布 -->
        <div class="quality-distribution">
          <h4>质量评分分布</h4>
          <div ref="qualityDistributionChart" class="chart-container"></div>
        </div>

        <!-- 任务类型质量对比 -->
        <div class="task-quality">
          <h4>任务类型质量对比</h4>
          <el-table :data="data.taskQuality" style="width: 100%">
            <el-table-column prop="taskType" label="任务类型" />
            <el-table-column prop="avgScore" label="平均评分" align="right">
              <template #default="{ row }">
                <el-rate
                  v-model="row.avgScore"
                  disabled
                  show-score
                  text-color="#ff9900"
                  :max="10"
                  :score-template="'{value}/10'"
                />
              </template>
            </el-table-column>
            <el-table-column prop="highQualityRate" label="高质量率" align="right">
              <template #default="{ row }">
                <el-tag :type="getQualityRateType(row.highQualityRate)">
                  {{ row.highQualityRate.toFixed(1) }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sampleCount" label="样本数量" align="right" />
          </el-table>
        </div>

        <!-- 质量趋势 -->
        <div class="quality-trend">
          <h4>质量趋势</h4>
          <div ref="qualityTrendChart" class="chart-container"></div>
        </div>

        <!-- 质量问题分析 -->
        <div class="quality-issues">
          <h4>质量问题分析</h4>
          <el-table :data="data.qualityIssues" style="width: 100%">
            <el-table-column prop="issue" label="问题类型" />
            <el-table-column prop="frequency" label="出现频率" align="right">
              <template #default="{ row }">
                {{ row.frequency }}次
              </template>
            </el-table-column>
            <el-table-column prop="impact" label="影响程度" align="right">
              <template #default="{ row }">
                <el-tag :type="getImpactType(row.impact)">
                  {{ row.impact }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="suggestion" label="改进建议" />
          </el-table>
        </div>

        <!-- 质量改进建议 -->
        <div class="quality-suggestions">
          <h4>质量改进建议</h4>
          <el-alert
            v-for="suggestion in data.suggestions"
            :key="suggestion.id"
            :title="suggestion.title"
            :description="suggestion.description"
            :type="suggestion.type"
            show-icon
            :closable="false"
            style="margin-bottom: 10px;"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

interface Props {
  data: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  refresh: []
}>()

const qualityDistributionChart = ref<HTMLElement>()
const qualityTrendChart = ref<HTMLElement>()
let qualityDistributionChartInstance: echarts.ECharts | null = null
let qualityTrendChartInstance: echarts.ECharts | null = null

const initCharts = () => {
  initQualityDistributionChart()
  initQualityTrendChart()
}

const initQualityDistributionChart = () => {
  if (!qualityDistributionChart.value) return

  qualityDistributionChartInstance = echarts.init(qualityDistributionChart.value)
  updateQualityDistributionChart()
}

const initQualityTrendChart = () => {
  if (!qualityTrendChart.value) return

  qualityTrendChartInstance = echarts.init(qualityTrendChart.value)
  updateQualityTrendChart()
}

const updateQualityDistributionChart = () => {
  if (!qualityDistributionChartInstance || !props.data?.qualityDistribution) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.qualityDistribution.map((item: any) => item.range)
    },
    yAxis: {
      type: 'value',
      name: '数量'
    },
    series: [
      {
        name: '质量分布',
        type: 'bar',
        data: props.data.qualityDistribution.map((item: any) => item.count),
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }

  qualityDistributionChartInstance.setOption(option)
}

const updateQualityTrendChart = () => {
  if (!qualityTrendChartInstance || !props.data?.qualityTrend) return

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均质量评分', '高质量率', '用户满意度']
    },
    xAxis: {
      type: 'category',
      data: props.data.qualityTrend.map((item: any) => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '评分',
        max: 10
      },
      {
        type: 'value',
        name: '百分比(%)',
        max: 100
      }
    ],
    series: [
      {
        name: '平均质量评分',
        type: 'line',
        yAxisIndex: 0,
        data: props.data.qualityTrend.map((item: any) => item.avgScore),
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '高质量率',
        type: 'line',
        yAxisIndex: 1,
        data: props.data.qualityTrend.map((item: any) => item.highQualityRate),
        smooth: true,
        lineStyle: { color: '#67C23A' },
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '用户满意度',
        type: 'line',
        yAxisIndex: 1,
        data: props.data.qualityTrend.map((item: any) => item.userSatisfaction),
        smooth: true,
        lineStyle: { color: '#E6A23C' },
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }

  qualityTrendChartInstance.setOption(option)
}

const getQualityRateType = (rate: number) => {
  if (rate >= 80) return 'success'
  if (rate >= 60) return 'warning'
  return 'danger'
}

const getImpactType = (impact: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'success'
  }
  return typeMap[impact] || 'info'
}

const refreshData = () => {
  emit('refresh')
}

const resizeCharts = () => {
  if (qualityDistributionChartInstance) {
    qualityDistributionChartInstance.resize()
  }
  if (qualityTrendChartInstance) {
    qualityTrendChartInstance.resize()
  }
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
  
  window.addEventListener('resize', resizeCharts)
})

watch(() => props.data, () => {
  if (qualityDistributionChartInstance) {
    updateQualityDistributionChart()
  }
  if (qualityTrendChartInstance) {
    updateQualityTrendChart()
  }
}, { deep: true })
</script>

<style scoped>
.quality-analysis-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.quality-content {
  padding: 10px 0;
}

.quality-overview {
  margin-bottom: 30px;
}

.quality-distribution,
.task-quality,
.quality-trend,
.quality-issues {
  margin-bottom: 30px;
}

.quality-distribution h4,
.task-quality h4,
.quality-trend h4,
.quality-issues h4,
.quality-suggestions h4 {
  margin-bottom: 15px;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.retry-high {
  color: #F56C6C;
}

.retry-normal {
  color: #67C23A;
}
</style>
