#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copilot聊天控制器
专门用于VSCode Copilot聊天的DOM操作
"""

from typing import Dict, List, Any
from datetime import datetime
from vscode_dom_controller import VSCodeDOMController


class CopilotChatController:
    """Copilot聊天控制器"""

    def __init__(self, dom_controller: VSCodeDOMController):
        """
        初始化Copilot聊天控制器
        
        Args:
            dom_controller: DOM控制器实例
        """
        self.dom = dom_controller
        
        # Copilot特定的选择器
        self.copilot_selectors = {
            # 输入相关
            'chat_input': '.monaco-scrollable-element .view-lines',
            'input_textarea': 'textarea[aria-label*="Chat"]',
            'input_container': '.monaco-scrollable-element',
            'view_lines': '.view-lines.monaco-mouse-cursor-text',
            'view_line': '.view-line',
            'text_span': '.mtk1',
            
            # 消息相关
            'chat_messages': '.monaco-list-row',
            'message_content': '.monaco-tl-row',
            'user_message': '[data-message-author="user"]',
            'assistant_message': '[data-message-author="assistant"]',
            
            # 按钮相关
            'send_button': '[aria-label*="Send"], [title*="Send"], button[aria-label*="发送"]',
            'clear_button': '[aria-label*="Clear"], [title*="Clear"], button[aria-label*="清除"]',
            'stop_button': '[aria-label*="Stop"], [title*="Stop"], button[aria-label*="停止"]',
            
            # 状态相关
            'loading_indicator': '.loading, .spinner, [aria-label*="loading"]',
            'error_message': '.error, .warning, [role="alert"]'
        }

    def get_copilot_chat_records(self) -> List[Dict[str, Any]]:
        """
        获取VSCode Copilot Chat面板中的聊天记录
        专门针对 id='workbench.panel.chat' 下面的 class="monaco-list-rows"
        
        Returns:
            聊天记录列表，包含详细信息
        """
        try:
            # 使用JavaScript文件执行
            result = self.dom.execute_js_file('copilot_chat_utils.js', 'getCopilotChatRecords')
            
            if result.get('success'):
                response = result.get('result', {})
                if response.get('success'):
                    print(f"✅ 成功获取到 {response.get('totalRecords', 0)} 条聊天记录")
                    return response.get('records', [])
                else:
                    print(f"❌ 获取聊天记录失败: {response.get('error', '未知错误')}")
                    return []
            else:
                print(f"❌ JavaScript执行失败: {result.get('error', '未知错误')}")
                return []
            
        except Exception as e:
            print(f"❌ 获取Copilot聊天记录时出错: {e}")
            return []

    def get_copilot_chat_summary(self) -> Dict[str, Any]:
        """
        获取Copilot Chat面板的摘要信息
        
        Returns:
            聊天面板摘要信息
        """
        try:
            # 使用JavaScript文件执行
            result = self.dom.execute_js_file('copilot_chat_utils.js', 'getCopilotChatSummary')
            return result.get('result', {}) if result.get('success') else {}
            
        except Exception as e:
            print(f"❌ 获取Copilot聊天摘要失败: {e}")
            return {}

    def is_copilot_ready(self) -> bool:
        """
        检查Copilot是否就绪
        
        Returns:
            是否就绪
        """
        try:
            # 使用JavaScript文件执行
            result = self.dom.execute_js_file('copilot_chat_utils.js', 'checkCopilotChatPanel')
            if result.get('success'):
                panel_info = result.get('result', {})
                return panel_info.get('exists', False) and panel_info.get('hasContainer', False)
            
            return False
            
        except Exception as e:
            print(f"❌ 检查Copilot状态失败: {e}")
            return False

    def get_formatted_chat_records(self) -> str:
        """
        获取格式化的聊天记录文本
        
        Returns:
            格式化的聊天记录字符串
        """
        try:
            records = self.get_copilot_chat_records()
            if not records:
                return "❌ 未获取到聊天记录\n\n可能的原因:\n1. Copilot Chat面板未打开\n2. 面板中没有聊天记录\n3. VSCode未连接"
            
            # 构建简化的聊天记录显示
            formatted_text = ""
            current_time = datetime.now()

            for i, record in enumerate(records):
                # 优先使用innerText，然后是清理后的内容，最后是textContent
                content = record.get('innerText', '').strip()
                if not content:
                    content = record.get('cleanContent', '').strip()
                if not content:
                    content = record.get('textContent', '').strip()

                if content:
                    # 识别消息类型
                    message_type = record.get('messageType', 'unknown')
                    author = record.get('author', 'unknown')

                    # 为每条消息生成时间戳（按顺序递减几分钟，模拟对话时间）
                    message_time = current_time.replace(second=0, microsecond=0)
                    # 最新的消息使用当前时间，之前的消息依次减去几分钟
                    minutes_ago = (len(records) - 1 - i) * 2  # 每条消息间隔2分钟
                    if minutes_ago > 0:
                        from datetime import timedelta
                        message_time = message_time - timedelta(minutes=minutes_ago)

                    time_str = message_time.strftime("%Y-%m-%d %H:%M")

                    # 根据消息类型添加前缀
                    if message_type == 'user' or author == 'user':
                        prefix = f"👤 用户 [{time_str}]:"
                    elif message_type == 'assistant' or author == 'assistant':
                        prefix = f"🤖 Copilot [{time_str}]:"
                    else:
                        # 尝试从内容推断
                        if content.startswith('GitHub Copilot') or 'function' in content or '//' in content:
                            prefix = f"🤖 Copilot [{time_str}]:"
                        else:
                            prefix = f"👤 用户 [{time_str}]:"

                    # 清理GitHub Copilot前缀
                    if content.startswith('GitHub Copilot'):
                        content = content.replace('GitHub Copilot', '').strip()

                    # 添加消息到格式化文本
                    formatted_text += f"{prefix}\n{content}\n\n"
            
            return formatted_text
            
        except Exception as e:
            return f"❌ 获取聊天记录时出错: {e}"
