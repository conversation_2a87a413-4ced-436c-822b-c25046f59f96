using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.Core.Entities
{
    /// <summary>
    /// UI自动化模板步骤实体
    /// </summary>
    [SugarTable("UIAutoMationTemplateSteps", "UI自动化模板步骤表")]
    public class UIAutoMationTemplateStep : BaseEntity
    {
        /// <summary>
        /// 所属序列ID
        /// </summary>
        [SugarColumn(ColumnDescription = "所属序列ID", IsNullable = false)]
        [Required(ErrorMessage = "所属序列ID不能为空")]
        public int SequenceId { get; set; }

        /// <summary>
        /// 关联模板ID（可选）
        /// </summary>
        [SugarColumn(ColumnDescription = "关联模板ID", IsNullable = true)]
        public int? TemplateId { get; set; }

        /// <summary>
        /// 步骤顺序
        /// </summary>
        [SugarColumn(ColumnDescription = "步骤顺序", IsNullable = false)]
        [Range(1, int.MaxValue, ErrorMessage = "步骤顺序必须大于0")]
        public int StepOrder { get; set; }

        /// <summary>
        /// 动作类型（基础UI操作类型）
        /// </summary>
        [SugarColumn(ColumnDescription = "动作类型", Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "动作类型不能为空")]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 逻辑语句类型（可选，用于条件判断、循环等逻辑控制）
        /// </summary>
        [SugarColumn(ColumnDescription = "逻辑语句类型", Length = 50, IsNullable = true)]
        public string? LogicType { get; set; }

        /// <summary>
        /// 步骤描述
        /// </summary>
        [SugarColumn(ColumnDescription = "步骤描述", Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "步骤描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 参数配置（JSON格式）
        /// </summary>
        [SugarColumn(ColumnDescription = "参数配置", ColumnDataType = "nvarchar(max)", IsNullable = true)]
        public string? Parameters { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        [SugarColumn(ColumnDescription = "超时时间", IsNullable = false)]
        [Range(1, 300, ErrorMessage = "超时时间必须在1到300秒之间")]
        public int TimeoutSeconds { get; set; } = 5;

        /// <summary>
        /// 重试次数
        /// </summary>
        [SugarColumn(ColumnDescription = "重试次数", IsNullable = false)]
        [Range(0, 10, ErrorMessage = "重试次数必须在0到10次之间")]
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [SugarColumn(ColumnDescription = "最大重试次数", IsNullable = false)]
        [Range(0, 10, ErrorMessage = "最大重试次数必须在0到10次之间")]
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(ColumnDescription = "是否启用", IsNullable = false)]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 条件表达式（用于条件判断类型的步骤）
        /// 支持JavaScript表达式，如：{result} === 'success' || {count} > 5
        /// </summary>
        [SugarColumn(ColumnDescription = "条件表达式", Length = 1000, IsNullable = true)]
        public string? ConditionExpression { get; set; }

        /// <summary>
        /// 跳转目标步骤ID（用于跳转类型的步骤）
        /// </summary>
        [SugarColumn(ColumnDescription = "跳转目标步骤ID", IsNullable = true)]
        public int? JumpToStepId { get; set; }

        /// <summary>
        /// 循环次数（用于循环类型的步骤）
        /// -1表示无限循环，直到满足退出条件
        /// </summary>
        [SugarColumn(ColumnDescription = "循环次数", IsNullable = true)]
        public int? LoopCount { get; set; }

        /// <summary>
        /// 循环变量名（用于循环类型的步骤）
        /// </summary>
        [SugarColumn(ColumnDescription = "循环变量名", Length = 50, IsNullable = true)]
        public string? LoopVariable { get; set; }

        /// <summary>
        /// 分组标识（用于标识循环体、条件分支等逻辑分组）
        /// </summary>
        [SugarColumn(ColumnDescription = "分组标识", Length = 50, IsNullable = true)]
        public string? GroupId { get; set; }

        /// <summary>
        /// 所属序列（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public UIAutoMationTemplateSequence? Sequence { get; set; }

        /// <summary>
        /// 关联模板（导航属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public CustomUIAutoMationTemplate? Template { get; set; }

        /// <summary>
        /// 参数字典（不映射到数据库）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, object> ParametersDictionary
        {
            get
            {
                if (string.IsNullOrEmpty(Parameters))
                    return new Dictionary<string, object>();

                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(Parameters)
                           ?? new Dictionary<string, object>();
                }
                catch
                {
                    return new Dictionary<string, object>();
                }
            }
            set
            {
                Parameters = value?.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(value) : null;
            }
        }
    }

    /// <summary>
    /// 动作类型枚举
    /// </summary>
    public static class ActionTypes
    {
        // 基础操作类型
        public const string Click = "click";
        public const string Wait = "wait";
        public const string Input = "input";
        public const string Delay = "delay";
        public const string Screenshot = "screenshot";
        public const string Verify = "verify";
        public const string Scroll = "scroll";
        public const string KeyPress = "key_press";

        // 流程控制类型
        public const string Condition = "condition";        // 条件判断
        public const string Loop = "loop";                  // 循环执行
        public const string LoopEnd = "loop_end";          // 循环结束标记
        public const string Branch = "branch";             // 分支执行
        public const string Jump = "jump";                 // 跳转到指定步骤
        public const string Exit = "exit";                 // 退出序列执行

        /// <summary>
        /// 获取所有动作类型
        /// </summary>
        public static readonly string[] All = {
            Click, Wait, Input, Delay, Screenshot, Verify, Scroll, KeyPress,
            Condition, Loop, LoopEnd, Branch, Jump, Exit
        };

        /// <summary>
        /// 基础操作类型
        /// </summary>
        public static readonly string[] BasicActions = {
            Click, Wait, Input, Delay, Screenshot, Verify, Scroll, KeyPress
        };

        /// <summary>
        /// 流程控制类型
        /// </summary>
        public static readonly string[] FlowControlActions = {
            Condition, Loop, LoopEnd, Branch, Jump, Exit
        };

        /// <summary>
        /// 验证动作类型是否有效
        /// </summary>
        public static bool IsValid(string actionType)
        {
            return All.Contains(actionType);
        }

        /// <summary>
        /// 判断是否为基础操作类型
        /// </summary>
        public static bool IsBasicAction(string actionType)
        {
            return BasicActions.Contains(actionType);
        }

        /// <summary>
        /// 判断是否为流程控制类型
        /// </summary>
        public static bool IsFlowControlAction(string actionType)
        {
            return FlowControlActions.Contains(actionType);
        }
    }
}
