using ProjectManagement.Core.Entities;

namespace ProjectManagement.Core.Interfaces
{
    /// <summary>
    /// Playwright脚本仓储接口
    /// </summary>
    public interface IPlaywrightScriptRepository : IRepository<PlaywrightScript>
    {
        /// <summary>
        /// 根据分类获取脚本列表
        /// </summary>
        /// <param name="category">分类</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>脚本列表</returns>
        Task<(List<PlaywrightScript> items, int total)> GetByCategory(string category, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 根据项目ID获取脚本列表
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>脚本列表</returns>
        Task<(List<PlaywrightScript> items, int total)> GetByProjectId(int projectId, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 根据浏览器类型获取脚本列表
        /// </summary>
        /// <param name="browser">浏览器类型</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>脚本列表</returns>
        Task<(List<PlaywrightScript> items, int total)> GetByBrowser(string browser, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 搜索脚本
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="category">分类</param>
        /// <param name="browser">浏览器</param>
        /// <param name="projectId">项目ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>脚本列表</returns>
        Task<(List<PlaywrightScript> items, int total)> Search(
            string? keyword = null,
            string? category = null,
            string? browser = null,
            int? projectId = null,
            int pageIndex = 1,
            int pageSize = 20);

        /// <summary>
        /// 更新执行统计
        /// </summary>
        /// <param name="scriptId">脚本ID</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="duration">执行时长</param>
        /// <returns></returns>
        Task UpdateExecutionStats(int scriptId, bool isSuccess, int? duration = null);

        /// <summary>
        /// 获取脚本统计信息
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <returns>统计信息</returns>
        Task<object> GetStatistics(int? projectId = null);
    }
}
