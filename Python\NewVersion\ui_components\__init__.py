#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI组件模块
包含开发步骤管理UI的各个组件
"""

__version__ = "1.0.0"
__author__ = "Development Team"

# 导出主要组件
from .project_task_manager import ProjectTaskManager
from .step_manager import StepManager
from .execution_manager import ExecutionManager
from .error_manager import ErrorManager
from .copilot_manager import CopilotManager
from .template_manager import TemplateManager

__all__ = [
    'ProjectTaskManager',
    'StepManager', 
    'ExecutionManager',
    'ErrorManager',
    'CopilotManager',
    'TemplateManager'
]
