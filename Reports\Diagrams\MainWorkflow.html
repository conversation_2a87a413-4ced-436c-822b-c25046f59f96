<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI驱动软件开发自动化系统 - 主流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }

        .diagram-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .mermaid {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 0 auto;
        }

        .description {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .description h2 {
            color: #2c3e50;
            margin-top: 0;
        }

        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .step-card {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .step-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1.1em;
        }

        .step-card ul {
            margin: 0;
            padding-left: 20px;
            color: #6c757d;
        }

        .step-card li {
            margin: 5px 0;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #6c757d;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .process-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI驱动软件开发自动化系统</h1>
            <p>主流程图 - 涵盖传统SDLC七大阶段的AI驱动自动化流程</p>
        </div>

        <div class="diagram-container">
            <div class="mermaid">
graph TD
    A["用户需求输入<br/>阶段: 需求分析<br/>工具: Web界面/移动端APP<br/>AI参与: 无<br/>1.收集业务需求<br/>2.明确项目目标<br/>3.整理功能清单"] --> B["AI需求分析 🤖<br/>阶段: 需求分析<br/>工具: LLM大模型<br/>AI参与: 核心AI功能<br/>1.自然语言处理<br/>2.需求理解与分类<br/>3.可行性评估"]
    B --> C["生成需求规格书 🤖<br/>阶段: 规格确认<br/>工具: LLM大模型 + Word模板<br/>AI参与: 核心AI功能<br/>1.结构化需求文档<br/>2.功能性需求定义<br/>3.非功能性需求规范<br/>4.用户界面设计要求"]
    C --> D["生成ER图 🤖<br/>阶段: 规格确认<br/>工具: PlantUML + AI生成<br/>AI参与: 核心AI功能<br/>1.数据库实体设计<br/>2.实体关系建模<br/>3.表结构定义<br/>4.主外键约束设计"]
    C --> E["生成Context图 🤖<br/>阶段: 规格确认<br/>工具: Mermaid + AI生成<br/>AI参与: 核心AI功能<br/>1.系统架构设计<br/>2.组件交互关系<br/>3.外部系统接口<br/>4.数据流向分析"]
    D --> F["代码生成 🤖<br/>阶段: 程式开发<br/>工具: GitHub Copilot + 自定义模板<br/>AI参与: 核心AI功能<br/>1.前端Vue组件开发<br/>2.后端C# API实现<br/>3.SQL Server数据库脚本<br/>4.配置文件生成"]
    E --> F
    F --> G["自动化测试 🤖<br/>阶段: IT验收与测试<br/>工具: Jest + MSTest + Selenium<br/>AI参与: 辅助AI功能<br/>1.单元测试执行<br/>2.集成测试验证<br/>3.UI自动化测试<br/>4.测试报告生成"]
    G --> G1["UAT测试<br/>阶段: UAT测试<br/>工具: 测试管理平台 + 用户反馈<br/>AI参与: 无<br/>1.用户验收测试<br/>2.业务流程验证<br/>3.用户体验测试<br/>4.验收报告确认"]
    G1 --> H["自动部署<br/>阶段: 上线前准备<br/>工具: Azure DevOps + Docker<br/>AI参与: 无<br/>1.CI/CD流水线<br/>2.应用打包发布<br/>3.环境配置部署<br/>4.部署验证检查"]
    H --> I["智能运维 🤖<br/>阶段: Go-Live Support<br/>工具: Azure Monitor + Prometheus<br/>AI参与: 辅助AI功能<br/>1.系统监控告警<br/>2.性能指标分析<br/>3.自动扩缩容<br/>4.安全漏洞扫描"]
    I --> J["问题处理 🤖<br/>阶段: Go-Live Support<br/>工具: Azure AI + PagerDuty<br/>AI参与: 核心AI功能<br/>1.故障自动检测<br/>2.智能故障诊断<br/>3.自动修复机制<br/>4.人工介入升级"]
    J --> K["持续优化 🤖<br/>阶段: Go-Live Support<br/>工具: Application Insights + AI分析<br/>AI参与: 核心AI功能<br/>1.性能数据分析<br/>2.用户反馈收集<br/>3.代码重构建议<br/>4.系统升级规划"]
    K --> L["项目完成<br/>阶段: Go-Live Support<br/>工具: Azure DevOps + SharePoint<br/>AI参与: 无<br/>1.交付验收确认<br/>2.文档归档整理<br/>3.团队经验总结<br/>4.后续维护计划"]

    %% 样式定义
    classDef inputStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef aiStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef docStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef codeStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef testStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef deployStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef opsStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    %% 应用样式
    class A inputStyle
    class B aiStyle
    class C,D,E docStyle
    class F codeStyle
    class G testStyle
    class H deployStyle
    class I,J,K,L opsStyle
            </div>
        </div>

        <div class="description">
            <h2>流程说明</h2>
            <p>本系统采用AI驱动的方式，实现从用户需求输入到项目完成交付的全流程自动化。整个流程分为13个主要步骤，涵盖传统软件开发的7个关键阶段，其中75%的步骤涉及AI参与。</p>

            <div class="process-steps">
                <div class="step-card">
                    <h3>📋 需求分析阶段</h3>
                    <ul>
                        <li>用户需求输入（无AI参与）</li>
                        <li>AI需求分析 🤖（核心AI功能）</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>✅ 规格确认阶段</h3>
                    <ul>
                        <li>生成需求规格书 🤖（核心AI功能）</li>
                        <li>生成ER图 🤖（核心AI功能）</li>
                        <li>生成Context图 🤖（核心AI功能）</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>💻 程式开发阶段</h3>
                    <ul>
                        <li>代码生成 🤖（核心AI功能）</li>
                        <li>Vue + C# + SQL Server</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>🧪 IT验收与测试阶段</h3>
                    <ul>
                        <li>自动化测试 🤖（辅助AI功能）</li>
                        <li>Jest + MSTest + Selenium</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>👥 UAT测试阶段</h3>
                    <ul>
                        <li>用户验收测试（无AI参与）</li>
                        <li>业务流程验证</li>
                        <li>用户体验测试</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>🚀 上线前准备阶段</h3>
                    <ul>
                        <li>自动部署（无AI参与）</li>
                        <li>Azure DevOps + Docker</li>
                    </ul>
                </div>

                <div class="step-card">
                    <h3>🔧 Go-Live Support阶段</h3>
                    <ul>
                        <li>智能运维 🤖（辅助AI功能）</li>
                        <li>问题处理 🤖（核心AI功能）</li>
                        <li>持续优化 🤖（核心AI功能）</li>
                        <li>项目完成（无AI参与）</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e3f2fd; border-radius: 8px;">
                <h3 style="margin: 0 0 10px 0; color: #1976d2;">AI参与统计</h3>
                <ul style="margin: 0; color: #424242;">
                    <li><strong>核心AI功能</strong>：7个步骤（54%）- 需求分析、文档生成、设计、代码生成、问题处理、优化</li>
                    <li><strong>辅助AI功能</strong>：2个步骤（15%）- 自动化测试、智能运维</li>
                    <li><strong>无AI参与</strong>：4个步骤（31%）- 需求输入、UAT测试、部署、项目完成</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 AI驱动软件开发自动化系统 | 生成时间: <span id="currentTime"></span></p>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
