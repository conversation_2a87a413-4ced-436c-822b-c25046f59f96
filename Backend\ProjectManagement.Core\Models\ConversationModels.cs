namespace ProjectManagement.Core.Models;

/// <summary>
/// 对话token统计信息
/// </summary>
public class ConversationTokenStats
{
    /// <summary>
    /// 对话会话ID
    /// </summary>
    public string ConversationId { get; set; } = string.Empty;

    /// <summary>
    /// 总消息数量
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// 用户消息预估token数
    /// </summary>
    public int EstimatedUserTokens { get; set; }

    /// <summary>
    /// AI回复预估token数
    /// </summary>
    public int EstimatedAiTokens { get; set; }

    /// <summary>
    /// 总预估token数
    /// </summary>
    public int EstimatedTotalTokens { get; set; }
}

/// <summary>
/// 需求对话统计信息
/// </summary>
public class RequirementConversationStatistics
{
    /// <summary>
    /// 总对话数
    /// </summary>
    public int TotalConversations { get; set; }

    /// <summary>
    /// 今日对话数
    /// </summary>
    public int TodayConversations { get; set; }

    /// <summary>
    /// 消息类型分布
    /// </summary>
    public Dictionary<string, int> MessageTypeBreakdown { get; set; } = new();
}
