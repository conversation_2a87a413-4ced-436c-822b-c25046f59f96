using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Text.Json;

namespace ProjectManagement.AI.Services;

/// <summary>
/// 自动化任务生成服务
/// 负责将开发步骤转换为自动化任务
/// </summary>
public class AutomationTaskGeneratorService
{
    private readonly IAutomationTaskRepository _taskRepository;
    private readonly IDevelopmentStepRepository _stepRepository;
    private readonly ILogger<AutomationTaskGeneratorService> _logger;

    public AutomationTaskGeneratorService(
        IAutomationTaskRepository taskRepository,
        IDevelopmentStepRepository stepRepository,
        ILogger<AutomationTaskGeneratorService> logger)
    {
        _taskRepository = taskRepository;
        _stepRepository = stepRepository;
        _logger = logger;
    }

    /// <summary>
    /// 为开发步骤生成自动化任务
    /// </summary>
    /// <param name="stepId">开发步骤ID</param>
    /// <returns>生成的任务列表</returns>
    public async Task<List<AutomationTask>> GenerateTasksForStepAsync(int stepId)
    {
        try
        {
            var step = await _stepRepository.GetByIdAsync(stepId);
            if (step == null)
            {
                throw new ArgumentException($"开发步骤不存在: {stepId}");
            }

            var tasks = new List<AutomationTask>();

            // 根据步骤类型生成不同的任务
            switch (step.StepType?.ToLower())
            {
                case "component":
                case "page":
                case "service":
                    tasks.AddRange(await GenerateCodeGenerationTasksAsync(step));
                    break;

                case "database":
                    tasks.AddRange(await GenerateDatabaseTasksAsync(step));
                    break;

                case "api":
                    tasks.AddRange(await GenerateApiTasksAsync(step));
                    break;

                case "test":
                    tasks.AddRange(await GenerateTestTasksAsync(step));
                    break;

                case "deployment":
                    tasks.AddRange(await GenerateDeploymentTasksAsync(step));
                    break;

                default:
                    tasks.AddRange(await GenerateGenericTasksAsync(step));
                    break;
            }

            // 批量创建任务
            if (tasks.Count > 0)
            {
                await _taskRepository.CreateTasksAsync(tasks);
                _logger.LogInformation("为步骤 {StepId} 生成了 {Count} 个自动化任务", stepId, tasks.Count);
            }

            return tasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为步骤 {StepId} 生成自动化任务失败", stepId);
            throw;
        }
    }

    /// <summary>
    /// 为项目的所有开发步骤生成自动化任务
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>生成的任务总数</returns>
    public async Task<int> GenerateTasksForProjectAsync(int projectId)
    {
        try
        {
            var steps = await _stepRepository.GetByProjectIdPagedAsync(projectId, 1, 1000);
            var totalTasks = 0;

            foreach (var step in steps.Items)
            {
                var tasks = await GenerateTasksForStepAsync(step.Id);
                totalTasks += tasks.Count;
            }

            _logger.LogInformation("为项目 {ProjectId} 的 {StepCount} 个步骤生成了 {TaskCount} 个自动化任务",
                projectId, steps.Items.Count, totalTasks);

            return totalTasks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为项目 {ProjectId} 生成自动化任务失败", projectId);
            throw;
        }
    }

    /// <summary>
    /// 生成代码生成任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateCodeGenerationTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        // 主要代码生成任务
        var codeTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.CodeGeneration,
            TaskName = $"生成{step.StepType}: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                stepType = step.StepType,
                stepName = step.StepName,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                fileType = step.FileType,
                filePath = step.FilePath,
                componentType = step.ComponentType,
                estimatedHours = step.EstimatedHours,
                aiPrompt = step.AIPrompt
            }),
            Priority = GetPriorityFromPriority(step.Priority),
            TimeoutMinutes = GetTimeoutFromEstimatedHours(step.EstimatedHours),
            Tags = $"{step.StepType},代码生成,{step.Priority}"
        };

        tasks.Add(codeTask);

        // 如果是组件，还需要生成样式文件
        if (step.StepType?.ToLower() == "component")
        {
            var styleTask = new AutomationTask
            {
                ProjectId = step.ProjectId,
                SourceType = AutomationTaskSourceType.DevelopmentStep,
                SourceId = step.Id,
                TaskType = AutomationTaskType.FileOperation,
                TaskName = $"生成样式文件: {step.StepName}",
                Description = $"为组件 {step.StepName} 生成CSS/SCSS样式文件",
                TaskData = JsonSerializer.Serialize(new
                {
                    operation = "create_style_file",
                    componentName = step.StepName,
                    styleType = "scss"
                }),
                Priority = AutomationTaskPriority.Medium,
                TimeoutMinutes = 10,
                Dependencies = JsonSerializer.Serialize(new[] { codeTask.Id }),
                Tags = $"样式,CSS,{step.StepType}"
            };

            tasks.Add(styleTask);
        }

        return tasks;
    }

    /// <summary>
    /// 生成数据库任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateDatabaseTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        // 数据库脚本生成任务
        var dbTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.DatabaseOperation,
            TaskName = $"生成数据库脚本: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                operation = "generate_script",
                stepName = step.StepName,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                aiPrompt = step.AIPrompt
            }),
            Priority = AutomationTaskPriority.High, // 数据库操作优先级较高
            TimeoutMinutes = 30,
            Tags = $"数据库,SQL,{step.StepType}"
        };

        tasks.Add(dbTask);

        return tasks;
    }

    /// <summary>
    /// 生成API任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateApiTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        // API控制器生成任务
        var apiTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.CodeGeneration,
            TaskName = $"生成API控制器: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                apiType = "controller",
                stepName = step.StepName,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                componentType = step.ComponentType,
                endpoints = ExtractEndpointsFromDescription(step.StepDescription)
            }),
            Priority = AutomationTaskPriority.High,
            TimeoutMinutes = 20,
            Tags = $"API,控制器,{step.StepType}"
        };

        tasks.Add(apiTask);

        // API文档生成任务
        var docTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.FileOperation,
            TaskName = $"生成API文档: {step.StepName}",
            Description = $"为API {step.StepName} 生成Swagger文档",
            TaskData = JsonSerializer.Serialize(new
            {
                operation = "generate_api_doc",
                apiName = step.StepName,
                description = step.StepDescription
            }),
            Priority = AutomationTaskPriority.Medium,
            TimeoutMinutes = 15,
            Dependencies = JsonSerializer.Serialize(new[] { apiTask.Id }),
            Tags = $"文档,Swagger,API"
        };

        tasks.Add(docTask);

        return tasks;
    }

    /// <summary>
    /// 生成测试任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateTestTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        var testTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.TestExecution,
            TaskName = $"生成测试用例: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                testType = "unit_test",
                stepName = step.StepName,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                componentType = step.ComponentType
            }),
            Priority = AutomationTaskPriority.Medium,
            TimeoutMinutes = 25,
            Tags = $"测试,单元测试,{step.StepType}"
        };

        tasks.Add(testTask);

        return tasks;
    }

    /// <summary>
    /// 生成部署任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateDeploymentTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        var deployTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.DeploymentOperation,
            TaskName = $"部署配置: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                deploymentType = "configuration",
                stepName = step.StepName,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                componentType = step.ComponentType
            }),
            Priority = AutomationTaskPriority.High,
            TimeoutMinutes = 45,
            Tags = $"部署,配置,{step.StepType}"
        };

        tasks.Add(deployTask);

        return tasks;
    }

    /// <summary>
    /// 生成通用任务
    /// </summary>
    private async Task<List<AutomationTask>> GenerateGenericTasksAsync(DevelopmentStep step)
    {
        var tasks = new List<AutomationTask>();

        var genericTask = new AutomationTask
        {
            ProjectId = step.ProjectId,
            SourceType = AutomationTaskSourceType.DevelopmentStep,
            SourceId = step.Id,
            TaskType = AutomationTaskType.FileOperation,
            TaskName = $"处理步骤: {step.StepName}",
            Description = step.StepDescription,
            TaskData = JsonSerializer.Serialize(new
            {
                operation = "generic_step",
                stepName = step.StepName,
                stepType = step.StepType,
                description = step.StepDescription,
                technologyStack = step.TechnologyStack,
                fileType = step.FileType,
                componentType = step.ComponentType
            }),
            Priority = GetPriorityFromPriority(step.Priority),
            TimeoutMinutes = GetTimeoutFromEstimatedHours(step.EstimatedHours),
            Tags = $"通用,{step.StepType},{step.Priority}"
        };

        tasks.Add(genericTask);

        return tasks;
    }

    /// <summary>
    /// 根据优先级获取任务优先级
    /// </summary>
    private static string GetPriorityFromPriority(string? priority)
    {
        return priority?.ToLower() switch
        {
            "critical" => AutomationTaskPriority.Critical,
            "high" => AutomationTaskPriority.High,
            "medium" => AutomationTaskPriority.Medium,
            "low" => AutomationTaskPriority.Low,
            _ => AutomationTaskPriority.Medium
        };
    }

    /// <summary>
    /// 根据预估工时获取超时时间
    /// </summary>
    private static int GetTimeoutFromEstimatedHours(decimal? estimatedHours)
    {
        if (!estimatedHours.HasValue)
            return 30;

        return estimatedHours.Value switch
        {
            >= 8 => 120, // 8小时以上给2小时超时
            >= 4 => 60,  // 4-8小时给1小时超时
            >= 2 => 30,  // 2-4小时给30分钟超时
            _ => 15      // 2小时以下给15分钟超时
        };
    }

    /// <summary>
    /// 从描述中提取API端点信息
    /// </summary>
    private static List<string> ExtractEndpointsFromDescription(string? description)
    {
        if (string.IsNullOrEmpty(description))
            return new List<string>();

        // 简单的端点提取逻辑，实际可能需要更复杂的解析
        var endpoints = new List<string>();
        var lines = description.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            if (line.Contains("GET") || line.Contains("POST") || line.Contains("PUT") || line.Contains("DELETE"))
            {
                endpoints.Add(line.Trim());
            }
        }

        return endpoints;
    }
}
