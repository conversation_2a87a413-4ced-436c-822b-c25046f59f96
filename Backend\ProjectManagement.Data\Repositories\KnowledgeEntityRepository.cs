using SqlSugar;
using ProjectManagement.Core.DTOs.KnowledgeGraph;
using ProjectManagement.Core.Entities;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 知识实体仓储实现
    /// </summary>
    public class KnowledgeEntityRepository : IKnowledgeEntityRepository
    {
        private readonly ISqlSugarClient _db;

        public KnowledgeEntityRepository(ISqlSugarClient db)
        {
            _db = db;
        }

        public async Task<KnowledgeEntity> CreateAsync(KnowledgeEntity entity)
        {
            var entityData = new KnowledgeEntityEntity
            {
                Type = entity.Type,
                Name = entity.Name,
                ReferenceId = entity.ReferenceId,
                Properties = entity.Properties,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var id = await _db.Insertable(entityData).ExecuteReturnIdentityAsync();
            entity.Id = id;
            return entity;
        }

        public async Task<KnowledgeEntity?> GetByIdAsync(int id)
        {
            var entityData = await _db.Queryable<KnowledgeEntityEntity>()
                .Where(e => e.Id == id)
                .FirstAsync();

            return entityData == null ? null : MapToModel(entityData);
        }

        public async Task<List<KnowledgeEntity>> GetByTypeAsync(string type)
        {
            var entities = await _db.Queryable<KnowledgeEntityEntity>()
                .Where(e => e.Type == type)
                .ToListAsync();

            return entities.Select(MapToModel).ToList();
        }

        public async Task<List<KnowledgeEntity>> GetByTypeAndNameAsync(string type, string name)
        {
            var entities = await _db.Queryable<KnowledgeEntityEntity>()
                .Where(e => e.Type == type && e.Name.Contains(name))
                .ToListAsync();

            return entities.Select(MapToModel).ToList();
        }

        public async Task<KnowledgeEntity?> GetByTypeAndReferenceIdAsync(string type, string referenceId)
        {
            var entityData = await _db.Queryable<KnowledgeEntityEntity>()
                .Where(e => e.Type == type && e.ReferenceId == referenceId)
                .FirstAsync();

            return entityData == null ? null : MapToModel(entityData);
        }

        public async Task<KnowledgeEntity> UpdateAsync(KnowledgeEntity entity)
        {
            var entityData = new KnowledgeEntityEntity
            {
                Id = entity.Id,
                Type = entity.Type,
                Name = entity.Name,
                ReferenceId = entity.ReferenceId,
                Properties = entity.Properties,
                UpdatedAt = DateTime.UtcNow
            };

            await _db.Updateable(entityData)
                .IgnoreColumns(e => e.CreatedAt)
                .ExecuteCommandAsync();

            return entity;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var result = await _db.Deleteable<KnowledgeEntityEntity>()
                .Where(e => e.Id == id)
                .ExecuteCommandAsync();

            return result > 0;
        }

        public async Task<List<KnowledgeEntity>> SearchAsync(string query, string? type = null)
        {
            var queryable = _db.Queryable<KnowledgeEntityEntity>();

            if (!string.IsNullOrEmpty(type))
            {
                queryable = queryable.Where(e => e.Type == type);
            }

            if (!string.IsNullOrEmpty(query))
            {
                queryable = queryable.Where(e =>
                    e.Name.Contains(query) ||
                    e.PropertiesJson.Contains(query));
            }

            var entities = await queryable.ToListAsync();
            return entities.Select(MapToModel).ToList();
        }

        /// <summary>
        /// 将Entity映射为Model
        /// </summary>
        private KnowledgeEntity MapToModel(KnowledgeEntityEntity entity)
        {
            return new KnowledgeEntity
            {
                Id = entity.Id,
                Type = entity.Type,
                Name = entity.Name,
                ReferenceId = entity.ReferenceId ?? string.Empty,
                Properties = entity.Properties,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }
    }
}
