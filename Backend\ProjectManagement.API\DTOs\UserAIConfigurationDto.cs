using System.ComponentModel.DataAnnotations;

namespace ProjectManagement.API.DTOs;

/// <summary>
/// 用户AI配置DTO
/// </summary>
public class UserAIConfigurationDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// AI提供商名称
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// AI模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型用途类型
    /// </summary>
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥（脱敏显示）
    /// </summary>
    public string? ApiKeyMasked { get; set; }

    /// <summary>
    /// 模型参数配置
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 是否为默认配置
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// 每日令牌限制
    /// </summary>
    public int? DailyTokenLimit { get; set; }

    /// <summary>
    /// 每月令牌限制
    /// </summary>
    public int? MonthlyTokenLimit { get; set; }

    /// <summary>
    /// 总令牌使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 总请求次数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求次数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求次数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 预估总成本
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// 当月成本
    /// </summary>
    public decimal CurrentMonthCost { get; set; }

    /// <summary>
    /// 当日已使用令牌数
    /// </summary>
    public int CurrentDayTokens { get; set; }

    /// <summary>
    /// 当月已使用令牌数
    /// </summary>
    public long CurrentMonthTokens { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;

    /// <summary>
    /// 是否超出每日限制
    /// </summary>
    public bool IsOverDailyLimit => DailyTokenLimit.HasValue && CurrentDayTokens >= DailyTokenLimit.Value;

    /// <summary>
    /// 是否超出每月限制
    /// </summary>
    public bool IsOverMonthlyLimit => MonthlyTokenLimit.HasValue && CurrentMonthTokens >= MonthlyTokenLimit.Value;
}

/// <summary>
/// 创建用户AI配置请求DTO
/// </summary>
public class CreateUserAIConfigurationDto
{
    /// <summary>
    /// AI提供商名称
    /// </summary>
    [Required(ErrorMessage = "提供商名称不能为空")]
    [StringLength(50, ErrorMessage = "提供商名称长度不能超过50个字符")]
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// AI模型名称
    /// </summary>
    [Required(ErrorMessage = "模型名称不能为空")]
    [StringLength(100, ErrorMessage = "模型名称长度不能超过100个字符")]
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型用途类型
    /// </summary>
    [Required(ErrorMessage = "模型类型不能为空")]
    [StringLength(50, ErrorMessage = "模型类型长度不能超过50个字符")]
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [StringLength(500, ErrorMessage = "API端点长度不能超过500个字符")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    [StringLength(255, ErrorMessage = "API密钥长度不能超过255个字符")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置（JSON格式）
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 是否为默认配置
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// 每日令牌限制
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "每日令牌限制必须大于0")]
    public int? DailyTokenLimit { get; set; }

    /// <summary>
    /// 每月令牌限制
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "每月令牌限制必须大于0")]
    public int? MonthlyTokenLimit { get; set; }
}

/// <summary>
/// 更新用户AI配置请求DTO
/// </summary>
public class UpdateUserAIConfigurationDto
{
    /// <summary>
    /// AI模型名称
    /// </summary>
    [StringLength(100, ErrorMessage = "模型名称长度不能超过100个字符")]
    public string? ModelName { get; set; }

    /// <summary>
    /// API端点URL地址
    /// </summary>
    [StringLength(500, ErrorMessage = "API端点长度不能超过500个字符")]
    public string? ApiEndpoint { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    [StringLength(255, ErrorMessage = "API密钥长度不能超过255个字符")]
    public string? ApiKey { get; set; }

    /// <summary>
    /// 模型参数配置（JSON格式）
    /// </summary>
    public string? ModelParameters { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 是否为默认配置
    /// </summary>
    public bool? IsDefault { get; set; }

    /// <summary>
    /// 每日令牌限制
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "每日令牌限制必须大于0")]
    public int? DailyTokenLimit { get; set; }

    /// <summary>
    /// 每月令牌限制
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "每月令牌限制必须大于0")]
    public int? MonthlyTokenLimit { get; set; }
}

/// <summary>
/// 用户AI使用统计DTO
/// </summary>
public class UserAIUsageStatisticsDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// 总请求次数
    /// </summary>
    public int TotalRequests { get; set; }

    /// <summary>
    /// 成功请求次数
    /// </summary>
    public int SuccessfulRequests { get; set; }

    /// <summary>
    /// 失败请求次数
    /// </summary>
    public int FailedRequests { get; set; }

    /// <summary>
    /// 总令牌使用量
    /// </summary>
    public long TotalTokensUsed { get; set; }

    /// <summary>
    /// 总成本
    /// </summary>
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 当月成本
    /// </summary>
    public decimal CurrentMonthCost { get; set; }

    /// <summary>
    /// 当日令牌使用量
    /// </summary>
    public int CurrentDayTokens { get; set; }

    /// <summary>
    /// 当月令牌使用量
    /// </summary>
    public long CurrentMonthTokens { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;

    /// <summary>
    /// 按提供商分组的统计
    /// </summary>
    public Dictionary<string, ProviderUsageStatisticsDto> ProviderStatistics { get; set; } = new();

    /// <summary>
    /// 按模型类型分组的统计
    /// </summary>
    public Dictionary<string, ModelTypeUsageStatisticsDto> ModelTypeStatistics { get; set; } = new();
}

/// <summary>
/// 提供商使用统计DTO
/// </summary>
public class ProviderUsageStatisticsDto
{
    /// <summary>
    /// 提供商名称
    /// </summary>
    public string ProviderName { get; set; } = string.Empty;

    /// <summary>
    /// 请求次数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// 令牌使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }
}

/// <summary>
/// 模型类型使用统计DTO
/// </summary>
public class ModelTypeUsageStatisticsDto
{
    /// <summary>
    /// 模型类型
    /// </summary>
    public string ModelType { get; set; } = string.Empty;

    /// <summary>
    /// 请求次数
    /// </summary>
    public int Requests { get; set; }

    /// <summary>
    /// 令牌使用量
    /// </summary>
    public long TokensUsed { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal Cost { get; set; }
}
