<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :title="isEdit ? '编辑模板' : '创建模板'"
    width="80%"
    :close-on-click-modal="false"
    class="template-edit-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="template-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入模板名称"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="分类" prop="categoryId">
            <el-select
              v-model="form.categoryId"
              placeholder="请选择分类"
              style="width: 100%"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务类型" prop="taskType">
            <el-select
              v-model="form.taskType"
              placeholder="请选择任务类型"
              style="width: 100%"
            >
              <el-option label="需求分析" value="RequirementAnalysis" />
              <el-option label="代码生成" value="CodeGeneration" />
              <el-option label="测试生成" value="Testing" />
              <el-option label="文档生成" value="Documentation" />
              <el-option label="代码审查" value="CodeReview" />
              <el-option label="性能优化" value="Optimization" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="模板类型" prop="templateType">
            <el-select
              v-model="form.templateType"
              placeholder="请选择模板类型"
              style="width: 100%"
              :disabled="isEdit && template?.templateType === 'System'"
            >
              <el-option label="用户模板" value="User" />
              <el-option label="共享模板" value="Shared" />
              <el-option
                label="系统模板"
                value="System"
                v-if="isAdmin"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="支持的AI提供商">
        <el-select
          v-model="supportedProvidersList"
          multiple
          placeholder="请选择支持的AI提供商"
          style="width: 100%"
          @change="onProvidersChange"
        >
          <el-option label="OpenAI" value="OpenAI" />
          <el-option label="Azure OpenAI" value="AzureOpenAI" />
          <el-option label="Claude" value="Claude" />
          <el-option label="DeepSeek" value="DeepSeek" />
          <el-option label="Ollama" value="Ollama" />
        </el-select>
      </el-form-item>

      <el-form-item label="标签">
        <el-input
          v-model="form.tags"
          placeholder="请输入标签，用逗号分隔"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设为默认">
            <el-switch
              v-model="form.isDefault"
              :disabled="!isAdmin && form.templateType === 'System'"
            />
            <span class="form-tip">设为该任务类型的默认模板</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="启用状态">
            <el-switch v-model="form.isEnabled" />
            <span class="form-tip">禁用后模板将不可使用</span>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 模板内容编辑区 -->
      <div class="content-section">
        <div class="section-header">
          <h3>模板内容</h3>
          <div class="content-actions">
            <el-button size="small" @click="showParameterHelper = true">
              参数助手
            </el-button>
            <el-button size="small" @click="previewTemplate">
              预览
            </el-button>
            <el-button size="small" @click="analyzeQuality">
              质量分析
            </el-button>
          </div>
        </div>

        <div class="content-editor">
          <el-tabs v-model="activeTab" class="editor-tabs">
            <el-tab-pane label="模板内容" name="content">
              <el-form-item prop="content">
                <el-input
                  v-model="form.content"
                  type="textarea"
                  :rows="15"
                  placeholder="请输入模板内容，使用 {参数名} 格式定义参数占位符"
                  class="content-textarea"
                />
              </el-form-item>
            </el-tab-pane>

            <el-tab-pane label="参数定义" name="parameters">
              <ParameterEditor
                v-model="form.parameters"
                @update:model-value="onParametersChange"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">
          取消
        </el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存
        </el-button>
      </div>
    </template>

    <!-- 参数助手对话框 -->
    <ParameterHelperDialog
      v-model="showParameterHelper"
      @insert="insertParameter"
    />

    <!-- 预览对话框 -->
    <TemplatePreviewDialog
      v-model="showPreview"
      :template="form"
      :preview-content="previewContent"
    />

    <!-- 质量分析对话框 -->
    <QualityAnalysisDialog
      v-model="showQualityAnalysis"
      :analysis="qualityAnalysis"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { PromptService, type PromptTemplate, type PromptCategory } from '@/services/promptService'
import { useAuthStore } from '@/stores/auth'
// import ParameterEditor from './ParameterEditor.vue'
// import ParameterHelperDialog from './ParameterHelperDialog.vue'
// import TemplatePreviewDialog from './TemplatePreviewDialog.vue'
// import QualityAnalysisDialog from './QualityAnalysisDialog.vue'

// Props & Emits
interface Props {
  modelValue: boolean
  template?: PromptTemplate | null
  categories: PromptCategory[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [template?: PromptTemplate]
}>()

// 服务和状态
const promptService = new PromptService()
const authStore = useAuthStore()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const activeTab = ref('content')
const showParameterHelper = ref(false)
const showPreview = ref(false)
const showQualityAnalysis = ref(false)
const previewContent = ref('')
const qualityAnalysis = ref<any>(null)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  categoryId: 0,
  content: '',
  parameters: '',
  templateType: 'User',
  taskType: '',
  supportedProviders: '',
  isDefault: false,
  isEnabled: true,
  tags: ''
})

// 支持的提供商列表（用于多选）
const supportedProvidersList = ref<string[]>([])

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 200, message: '模板名称长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  templateType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 10, message: '模板内容至少需要 10 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.template)
const isAdmin = computed(() => {
  return authStore.user?.role === 'Admin' || authStore.user?.role === 'SuperAdmin'
})

// 监听器
watch(() => props.modelValue, (visible) => {
  if (visible) {
    resetForm()
    if (props.template) {
      loadTemplate()
    }
  }
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    categoryId: 0,
    content: '',
    parameters: '',
    templateType: 'User',
    taskType: '',
    supportedProviders: '',
    isDefault: false,
    isEnabled: true,
    tags: ''
  })
  supportedProvidersList.value = []
  activeTab.value = 'content'
}

const loadTemplate = () => {
  if (!props.template) return

  Object.assign(form, {
    name: props.template.name,
    description: props.template.description || '',
    categoryId: props.template.categoryId,
    content: props.template.content,
    parameters: props.template.parameters || '',
    templateType: props.template.templateType,
    taskType: props.template.taskType,
    supportedProviders: props.template.supportedProviders || '',
    isDefault: props.template.isDefault,
    isEnabled: props.template.isEnabled,
    tags: props.template.tags || ''
  })

  // 解析支持的提供商
  if (form.supportedProviders) {
    supportedProvidersList.value = form.supportedProviders.split(',').map(p => p.trim())
  }
}

const onProvidersChange = () => {
  form.supportedProviders = supportedProvidersList.value.join(',')
}

const onParametersChange = (parameters: string) => {
  form.parameters = parameters
}

const insertParameter = (parameterText: string) => {
  // 在光标位置插入参数
  const textarea = document.querySelector('.content-textarea textarea') as HTMLTextAreaElement
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const content = form.content
    form.content = content.substring(0, start) + parameterText + content.substring(end)

    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + parameterText.length, start + parameterText.length)
    })
  }
}

const previewTemplate = async () => {
  if (!form.content) {
    ElMessage.warning('请先输入模板内容')
    return
  }

  try {
    // 这里可以调用预览API，暂时直接显示内容
    previewContent.value = form.content
    showPreview.value = true
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败')
  }
}

const analyzeQuality = async () => {
  if (!form.content) {
    ElMessage.warning('请先输入模板内容')
    return
  }

  try {
    qualityAnalysis.value = await promptService.analyzePromptQuality(form.content, form.taskType)
    showQualityAnalysis.value = true
  } catch (error) {
    console.error('质量分析失败:', error)
    ElMessage.error('质量分析失败')
  }
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    saving.value = true

    if (isEdit.value && props.template) {
      // 更新模板
      await promptService.updateTemplate(props.template.id, {
        name: form.name,
        description: form.description,
        categoryId: form.categoryId,
        content: form.content,
        parameters: form.parameters,
        supportedProviders: form.supportedProviders,
        isEnabled: form.isEnabled,
        tags: form.tags
      })
      ElMessage.success('模板更新成功')
    } else {
      // 创建模板
      await promptService.createTemplate({
        name: form.name,
        description: form.description,
        categoryId: form.categoryId,
        content: form.content,
        parameters: form.parameters,
        templateType: form.templateType,
        taskType: form.taskType,
        supportedProviders: form.supportedProviders,
        isDefault: form.isDefault,
        isEnabled: form.isEnabled,
        tags: form.tags
      })
      ElMessage.success('模板创建成功')
    }

    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.template-edit-dialog {
  .template-form {
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-tip {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }

  .content-section {
    margin-top: 20px;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .content-actions {
    display: flex;
    gap: 8px;
  }

  .content-editor {
    .editor-tabs {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }

    .content-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
