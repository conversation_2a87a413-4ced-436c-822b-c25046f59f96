using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using System.Security.Claims;

namespace ProjectManagement.API.Controllers;

/// <summary>
/// 用户任务映射控制器
/// 功能: 管理用户个人任务映射配置的API接口
/// 支持: CRUD操作、任务类型查询、默认配置管理、统计信息
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UserTaskMappingController : ControllerBase
{
    private readonly IUserTaskMappingRepository _repository;
    private readonly ILogger<UserTaskMappingController> _logger;

    public UserTaskMappingController(
        IUserTaskMappingRepository repository,
        ILogger<UserTaskMappingController> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out int userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("无法获取用户ID");
    }

    /// <summary>
    /// 获取当前用户的所有任务映射
    /// </summary>
    /// <returns>任务映射列表</returns>
    [HttpGet]
    public async Task<ActionResult<List<object>>> GetUserTaskMappings()
    {
        try
        {
            var userId = GetCurrentUserId();
            var mappings = await _repository.GetByUserIdAsync(userId);

            var result = mappings.Select(m => m.ToDto()).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射失败");
            return StatusCode(500, new { message = "获取任务映射失败" });
        }
    }

    /// <summary>
    /// 根据任务类型获取用户任务映射
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <returns>任务映射列表</returns>
    [HttpGet("by-task-type/{taskType}")]
    public async Task<ActionResult<List<object>>> GetByTaskType(string taskType)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mappings = await _repository.GetByUserIdAndTaskTypeAsync(userId, taskType);

            var result = mappings.Select(m => m.ToDto()).ToList();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据任务类型获取任务映射失败: {TaskType}", taskType);
            return StatusCode(500, new { message = "获取任务映射失败" });
        }
    }

    /// <summary>
    /// 获取用户指定任务类型的默认配置
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <returns>默认任务映射配置</returns>
    [HttpGet("default/{taskType}")]
    public async Task<ActionResult<object>> GetDefaultByTaskType(string taskType)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetDefaultByUserAndTaskTypeAsync(userId, taskType);

            if (mapping == null)
            {
                return NotFound(new { message = $"未找到任务类型 {taskType} 的默认配置" });
            }

            return Ok(mapping.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取默认任务映射失败: {TaskType}", taskType);
            return StatusCode(500, new { message = "获取默认任务映射失败" });
        }
    }

    /// <summary>
    /// 获取用户指定任务类型的最高优先级配置
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <returns>最高优先级的任务映射配置</returns>
    [HttpGet("highest-priority/{taskType}")]
    public async Task<ActionResult<object>> GetHighestPriorityByTaskType(string taskType)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetHighestPriorityByUserAndTaskTypeAsync(userId, taskType);

            if (mapping == null)
            {
                return NotFound(new { message = $"未找到任务类型 {taskType} 的配置" });
            }

            return Ok(mapping.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最高优先级任务映射失败: {TaskType}", taskType);
            return StatusCode(500, new { message = "获取任务映射失败" });
        }
    }

    /// <summary>
    /// 根据ID获取任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <returns>任务映射详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<object>> GetById(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetByIdAsync(id);

            if (mapping == null)
            {
                return NotFound(new { message = "任务映射不存在" });
            }

            // 验证所有权
            if (mapping.UserId != userId)
            {
                return Forbid("无权访问此任务映射");
            }

            return Ok(mapping.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务映射失败: {Id}", id);
            return StatusCode(500, new { message = "获取任务映射失败" });
        }
    }

    /// <summary>
    /// 创建任务映射
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <returns>创建的任务映射</returns>
    [HttpPost]
    public async Task<ActionResult<object>> Create([FromBody] CreateUserTaskMappingRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();

            var mapping = new UserTaskMapping
            {
                UserId = userId,
                TaskType = request.TaskType,
                ProviderName = request.ProviderName,
                ModelName = request.ModelName,
                IsActive = request.IsActive ?? true,
                IsDefault = request.IsDefault ?? false,
                Priority = request.Priority ?? 1,
                ConfigurationParameters = request.ConfigurationParameters,
                Description = request.Description
            };

            // 验证配置
            var (isValid, errors) = await _repository.ValidateMappingAsync(mapping);
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var result = await _repository.CreateAsync(mapping);
            _logger.LogInformation("创建用户任务映射成功: {UserId}, {TaskType}, {ProviderName}",
                userId, request.TaskType, request.ProviderName);

            return CreatedAtAction(nameof(GetById), new { id = result.Id }, result.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建任务映射失败");
            return StatusCode(500, new { message = "创建任务映射失败" });
        }
    }

    /// <summary>
    /// 更新任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] UpdateUserTaskMappingRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetByIdAsync(id);

            if (mapping == null)
            {
                return NotFound(new { message = "任务映射不存在" });
            }

            // 验证所有权
            if (mapping.UserId != userId)
            {
                return Forbid("无权修改此任务映射");
            }

            // 更新字段
            if (!string.IsNullOrEmpty(request.TaskType))
                mapping.TaskType = request.TaskType;
            if (!string.IsNullOrEmpty(request.ProviderName))
                mapping.ProviderName = request.ProviderName;
            if (request.ModelName != null)
                mapping.ModelName = request.ModelName;
            if (request.IsActive.HasValue)
                mapping.IsActive = request.IsActive.Value;
            if (request.IsDefault.HasValue)
                mapping.IsDefault = request.IsDefault.Value;
            if (request.Priority.HasValue)
                mapping.Priority = request.Priority.Value;
            if (request.ConfigurationParameters != null)
                mapping.ConfigurationParameters = request.ConfigurationParameters;
            if (request.Description != null)
                mapping.Description = request.Description;

            // 验证配置
            var (isValid, errors) = await _repository.ValidateMappingAsync(mapping);
            if (!isValid)
            {
                return BadRequest(new { message = "配置验证失败", errors });
            }

            var success = await _repository.UpdateAsync(mapping);
            if (!success)
            {
                return StatusCode(500, new { message = "更新任务映射失败" });
            }

            _logger.LogInformation("更新用户任务映射成功: {Id}, {UserId}", id, userId);
            return Ok(new { message = "任务映射更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新任务映射失败: {Id}", id);
            return StatusCode(500, new { message = "更新任务映射失败" });
        }
    }

    /// <summary>
    /// 删除任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetByIdAsync(id);

            if (mapping == null)
            {
                return NotFound(new { message = "任务映射不存在" });
            }

            // 验证所有权
            if (mapping.UserId != userId)
            {
                return StatusCode(403, new { message = "无权删除此任务映射" });
            }

            var success = await _repository.DeleteAsync(id);
            if (!success)
            {
                return StatusCode(500, new { message = "删除任务映射失败" });
            }

            _logger.LogInformation("删除用户任务映射成功: {Id}, {UserId}", id, userId);
            return Ok(new { message = "任务映射删除成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除任务映射失败: {Id}", id);
            return StatusCode(500, new { message = "删除任务映射失败" });
        }
    }

    /// <summary>
    /// 启用/禁用任务映射
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <param name="request">切换请求</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/toggle")]
    public async Task<ActionResult> Toggle(int id, [FromBody] ToggleActiveRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetByIdAsync(id);

            if (mapping == null)
            {
                return NotFound(new { message = "任务映射不存在" });
            }

            // 验证所有权
            if (mapping.UserId != userId)
            {
                return StatusCode(403, new { message = "无权修改此任务映射" });
            }

            var success = await _repository.ToggleActiveAsync(id, request.IsActive);
            if (!success)
            {
                return StatusCode(500, new { message = "切换任务映射状态失败" });
            }

            _logger.LogInformation("切换用户任务映射状态成功: {Id}, {UserId}, {IsActive}", id, userId, request.IsActive);
            return Ok(new { message = request.IsActive ? "任务映射已启用" : "任务映射已禁用" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换任务映射状态失败: {Id}", id);
            return StatusCode(500, new { message = "切换任务映射状态失败" });
        }
    }

    /// <summary>
    /// 设置默认配置
    /// </summary>
    /// <param name="id">任务映射ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/set-default")]
    public async Task<ActionResult> SetDefault(int id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mapping = await _repository.GetByIdAsync(id);

            if (mapping == null)
            {
                return NotFound(new { message = "任务映射不存在" });
            }

            // 验证所有权
            if (mapping.UserId != userId)
            {
                return Forbid("无权修改此任务映射");
            }

            var success = await _repository.SetDefaultAsync(userId, mapping.TaskType, id);
            if (!success)
            {
                return StatusCode(500, new { message = "设置默认配置失败" });
            }

            _logger.LogInformation("设置默认任务映射成功: {Id}, {UserId}, {TaskType}", id, userId, mapping.TaskType);
            return Ok(new { message = "默认配置设置成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认配置失败: {Id}", id);
            return StatusCode(500, new { message = "设置默认配置失败" });
        }
    }

    /// <summary>
    /// 批量创建任务映射
    /// </summary>
    /// <param name="request">批量创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("batch")]
    public async Task<ActionResult<List<object>>> CreateBatch([FromBody] BatchCreateUserTaskMappingRequest request)
    {
        try
        {
            var userId = GetCurrentUserId();
            var mappings = new List<UserTaskMapping>();

            foreach (var item in request.Mappings)
            {
                var mapping = new UserTaskMapping
                {
                    UserId = userId,
                    TaskType = item.TaskType,
                    ProviderName = item.ProviderName,
                    ModelName = item.ModelName,
                    IsActive = item.IsActive ?? true,
                    IsDefault = item.IsDefault ?? false,
                    Priority = item.Priority ?? 1,
                    ConfigurationParameters = item.ConfigurationParameters,
                    Description = item.Description
                };

                // 验证配置
                var (isValid, errors) = await _repository.ValidateMappingAsync(mapping);
                if (!isValid)
                {
                    return BadRequest(new {
                        message = $"配置验证失败: {item.TaskType} -> {item.ProviderName}",
                        errors
                    });
                }

                mappings.Add(mapping);
            }

            var results = await _repository.CreateBatchAsync(mappings);
            _logger.LogInformation("批量创建用户任务映射成功: {UserId}, {Count}个", userId, results.Count);

            return Ok(results.Select(r => r.ToDto()).ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量创建任务映射失败");
            return StatusCode(500, new { message = "批量创建任务映射失败" });
        }
    }

    /// <summary>
    /// 获取用户任务映射统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("stats")]
    public async Task<ActionResult<object>> GetStats()
    {
        try
        {
            var userId = GetCurrentUserId();
            var stats = await _repository.GetUserMappingStatsAsync(userId);

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户任务映射统计失败");
            return StatusCode(500, new { message = "获取统计信息失败" });
        }
    }

    /// <summary>
    /// 获取可用的任务类型列表
    /// </summary>
    /// <returns>任务类型列表</returns>
    [HttpGet("task-types")]
    public ActionResult<List<object>> GetTaskTypes()
    {
        try
        {
            var taskTypes = TaskTypes.GetAll()
                .Select(t => new { Value = t.Value, DisplayName = t.DisplayName })
                .ToList();

            return Ok(taskTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务类型列表失败");
            return StatusCode(500, new { message = "获取任务类型列表失败" });
        }
    }

    /// <summary>
    /// 获取可用的AI提供商列表
    /// </summary>
    /// <returns>AI提供商列表</returns>
    [HttpGet("providers")]
    public ActionResult<List<object>> GetProviders()
    {
        try
        {
            var providers = AIProviders.GetAll()
                .Select(p => new { Value = p.Value, DisplayName = p.DisplayName })
                .ToList();

            return Ok(providers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI提供商列表失败");
            return StatusCode(500, new { message = "获取AI提供商列表失败" });
        }
    }

    /// <summary>
    /// 检查用户是否已配置指定任务类型的映射
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <returns>是否已配置</returns>
    [HttpGet("has-mapping/{taskType}")]
    public async Task<ActionResult<object>> HasMapping(string taskType)
    {
        try
        {
            var userId = GetCurrentUserId();
            var hasMapping = await _repository.HasMappingForTaskTypeAsync(userId, taskType);

            return Ok(new { HasMapping = hasMapping });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查任务映射存在性失败: {TaskType}", taskType);
            return StatusCode(500, new { message = "检查任务映射失败" });
        }
    }
}

/// <summary>
/// 创建用户任务映射请求
/// </summary>
public class CreateUserTaskMappingRequest
{
    public string TaskType { get; set; } = string.Empty;
    public string ProviderName { get; set; } = string.Empty;
    public string? ModelName { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDefault { get; set; }
    public int? Priority { get; set; }
    public string? ConfigurationParameters { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 更新用户任务映射请求
/// </summary>
public class UpdateUserTaskMappingRequest
{
    public string? TaskType { get; set; }
    public string? ProviderName { get; set; }
    public string? ModelName { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsDefault { get; set; }
    public int? Priority { get; set; }
    public string? ConfigurationParameters { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 切换激活状态请求
/// </summary>
public class ToggleActiveRequest
{
    public bool IsActive { get; set; }
}

/// <summary>
/// 批量创建用户任务映射请求
/// </summary>
public class BatchCreateUserTaskMappingRequest
{
    public List<CreateUserTaskMappingRequest> Mappings { get; set; } = new();
}