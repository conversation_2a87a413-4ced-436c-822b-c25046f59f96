<template>
  <div v-if="hasMultipleTabs" class="app-tabs">
    <el-tabs
      v-model="activeTabName"
      type="card"
      closable
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
      @contextmenu.prevent="handleContextMenu"
    >
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.title"
        :name="tab.name"
        :closable="tab.closable"
      >
        <template #label>
          <span class="tab-label">
            <el-icon v-if="tab.icon" class="tab-icon">
              <component :is="tab.icon" />
            </el-icon>
            <span class="tab-title">{{ tab.title }}</span>
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="contextmenu"
      :teleported="false"
      @command="handleContextMenuCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="refresh">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-dropdown-item>
          <el-dropdown-item 
            command="closeOthers"
            :disabled="tabs.length <= 1"
          >
            <el-icon><Close /></el-icon>
            关闭其他
          </el-dropdown-item>
          <el-dropdown-item 
            command="closeAll"
            :disabled="!hasClosableTabs"
          >
            <el-icon><CircleClose /></el-icon>
            关闭所有
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useTabsStore } from '@/stores/tabs'
import { ElMessage } from 'element-plus'
import { Refresh, Close, CircleClose } from '@element-plus/icons-vue'
import type { TabsPaneContext } from 'element-plus'

const router = useRouter()
const route = useRoute()
const tabsStore = useTabsStore()

// 响应式引用
const contextMenuRef = ref()
const contextMenuTabName = ref('')

// 计算属性
const tabs = computed(() => tabsStore.tabs)
const hasMultipleTabs = computed(() => tabsStore.hasMultipleTabs)
const activeTabName = computed({
  get: () => tabsStore.activeTab,
  set: (value: string) => {
    const tab = tabsStore.setActiveTab(value)
    if (tab && tab.name !== route.name) {
      // 只有当目标tab与当前路由不同时才进行导航
      router.push({
        path: tab.path,
        query: tab.query
      } as any)
    }
  }
})

const hasClosableTabs = computed(() => 
  tabs.value.some(tab => tab.closable)
)

// 方法
const handleTabClick = (pane: TabsPaneContext) => {
  const tab = tabsStore.getTab(pane.paneName as string)
  if (tab && tab.name !== route.name) {
    // 只有当目标tab与当前路由不同时才进行导航
    router.push({
      path: tab.path,
      query: tab.query
    } as any)
  }
}

const handleTabRemove = (tabName: string | number) => {
  const nextTab = tabsStore.removeTab(String(tabName))
  if (nextTab) {
    router.push({
      path: nextTab.path,
      query: nextTab.query
    } as any)
  }
}

const handleContextMenu = (event: MouseEvent) => {
  // 找到被右键点击的标签
  const target = event.target as HTMLElement
  const tabElement = target.closest('.el-tabs__item')
  if (tabElement) {
    const tabName = tabElement.getAttribute('aria-controls')?.replace('pane-', '')
    if (tabName) {
      contextMenuTabName.value = tabName
    }
  }
}

const handleContextMenuCommand = (command: string) => {
  const currentTabName = contextMenuTabName.value || tabsStore.activeTab
  
  switch (command) {
    case 'refresh':
      // 刷新当前页面
      window.location.reload()
      break
      
    case 'closeOthers':
      tabsStore.removeOtherTabs(currentTabName)
      ElMessage.success('已关闭其他标签页')
      break
      
    case 'closeAll':
      const remainingTab = tabsStore.removeAllTabs()
      if (remainingTab) {
        router.push({
          path: remainingTab.path,
          query: remainingTab.query
        } as any)
      }
      ElMessage.success('已关闭所有可关闭的标签页')
      break
  }
}

// 监听路由变化，同步标签状态
watch(route, (newRoute) => {
  if (newRoute.name) {
    tabsStore.addTab(newRoute)
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.app-tabs {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  
  :deep(.el-tabs) {
    .el-tabs__header {
      margin: 0;
      border-bottom: none;
      
      .el-tabs__nav-wrap {
        padding: 0 16px;
        
        .el-tabs__nav {
          border: none;
          
          .el-tabs__item {
            border: 1px solid var(--el-border-color-light);
            border-bottom: none;
            margin-right: 4px;
            border-radius: 4px 4px 0 0;
            background: var(--el-bg-color-page);
            transition: all 0.3s ease;
            
            &:hover {
              background: var(--el-color-primary-light-9);
            }
            
            &.is-active {
              background: var(--el-bg-color);
              border-color: var(--el-color-primary);
              color: var(--el-color-primary);
            }
            
            .tab-label {
              display: flex;
              align-items: center;
              gap: 6px;
              
              .tab-icon {
                font-size: 14px;
              }
              
              .tab-title {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
    
    .el-tabs__content {
      display: none; // 隐藏标签内容，因为我们使用router-view
    }
  }
}
</style>
