using SqlSugar;

namespace ProjectManagement.Core.Entities;

/// <summary>
/// 原型图实体 - 存储UI原型图设计
/// 功能: 存储AI生成的界面原型图，包括线框图、交互流程图等
/// 支持: Mermaid格式、多种原型图类型、版本控制
/// </summary>
[SugarTable("Prototypes", "原型图管理表")]
public class Prototype : BaseEntity
{
    /// <summary>
    /// 所属项目ID，关联Projects表
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "所属项目ID，关联Projects表")]
    public int ProjectId { get; set; }

    /// <summary>
    /// 关联的需求文档ID，可为空
    /// </summary>
    [SugarColumn(IsNullable = true, ColumnDescription = "关联的需求文档ID，可为空")]
    public int? RequirementDocumentId { get; set; }

    /// <summary>
    /// 原型图名称（如：用户登录页面原型、商品列表线框图）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "原型图名称")]
    public string PrototypeName { get; set; } = string.Empty;

    /// <summary>
    /// 原型图类型：Wireframe(线框图)、UserFlow(用户流程图)、ComponentDiagram(组件关系图)、InteractionFlow(交互流程图)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "原型图类型")]
    public string PrototypeType { get; set; } = "Wireframe";

    /// <summary>
    /// Mermaid格式的原型图定义代码
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = false, ColumnDescription = "Mermaid格式的原型图定义代码")]
    public string MermaidDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 原型图描述说明
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "原型图描述说明")]
    public string? Description { get; set; }

    /// <summary>
    /// 目标用户群体（如：管理员、普通用户、访客）
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "目标用户群体")]
    public string? TargetUsers { get; set; }

    /// <summary>
    /// 页面/功能模块信息，JSON格式存储
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "页面/功能模块信息，JSON格式存储")]
    public string? PageModules { get; set; }

    /// <summary>
    /// 交互流程信息，JSON格式存储（用户操作路径、页面跳转等）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "交互流程信息，JSON格式存储")]
    public string? InteractionFlows { get; set; }

    /// <summary>
    /// UI组件信息，JSON格式存储（按钮、表单、列表等组件定义）
    /// </summary>
    [SugarColumn(ColumnDataType = "nvarchar(max)", IsNullable = true, ColumnDescription = "UI组件信息，JSON格式存储")]
    public string? UIComponents { get; set; }

    /// <summary>
    /// 原型图版本号（如：1.0, 1.1, 2.0）
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "原型图版本号")]
    public string PrototypeVersion { get; set; } = "1.0";

    /// <summary>
    /// 设备类型：Desktop(桌面端)、Mobile(移动端)、Tablet(平板)、Responsive(响应式)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "设备类型")]
    public string? DeviceType { get; set; } = "Desktop";

    /// <summary>
    /// 保真度级别：Low(低保真)、Medium(中保真)、High(高保真)
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true, ColumnDescription = "保真度级别")]
    public string? FidelityLevel { get; set; } = "Low";

    /// <summary>
    /// 关联的项目 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public Project? Project { get; set; }

    /// <summary>
    /// 关联的需求文档 - 导航属性
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RequirementDocument? RequirementDocument { get; set; }
}
