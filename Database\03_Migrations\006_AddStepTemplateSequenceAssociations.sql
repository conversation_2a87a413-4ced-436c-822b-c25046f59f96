-- =============================================
-- 添加开发步骤与模板序列关联表
-- 迁移脚本: 006_AddStepTemplateSequenceAssociations.sql
-- 版本: 1.0
-- 创建日期: 2024-12-24
-- 描述: 为开发步骤添加UI自动化模板序列关联功能
-- =============================================

USE ProjectManagementAI;
GO

-- 检查数据库是否存在
IF DB_NAME() != 'ProjectManagementAI'
BEGIN
    PRINT '❌ 错误：请确保连接到 ProjectManagementAI 数据库';
    RETURN;
END
GO

PRINT '开始执行迁移脚本: 006_AddStepTemplateSequenceAssociations.sql';
PRINT '======================================================';
GO

-- =============================================
-- 1. 检查前置条件
-- =============================================

-- 检查DevelopmentSteps表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DevelopmentSteps')
BEGIN
    PRINT '❌ 错误：DevelopmentSteps表不存在，请先执行005_AddDevelopmentStepsTables.sql';
    RETURN;
END

-- 检查UIAutoMationTemplateSequences表是否存在
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UIAutoMationTemplateSequences')
BEGIN
    PRINT '❌ 错误：UIAutoMationTemplateSequences表不存在，请先创建UI自动化模板相关表';
    RETURN;
END

PRINT '✓ 前置条件检查通过';
GO

-- =============================================
-- 2. 创建StepTemplateSequenceAssociations表
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'StepTemplateSequenceAssociations')
BEGIN
    PRINT '正在创建 StepTemplateSequenceAssociations 表...';
    
    CREATE TABLE [dbo].[StepTemplateSequenceAssociations] (
        -- 主键
        [Id] int IDENTITY(1,1) NOT NULL,
        
        -- 关联字段
        [StepId] int NOT NULL,
        [SequenceId] int NOT NULL,
        
        -- 应用信息
        [AppliedTime] datetime2 NOT NULL DEFAULT GETDATE(),
        [Status] nvarchar(20) NOT NULL DEFAULT 'Active',
        [IsActive] bit NOT NULL DEFAULT 1,
        
        -- 执行信息
        [Progress] int NOT NULL DEFAULT 0,
        [ExecutionStartTime] datetime2 NULL,
        [ExecutionEndTime] datetime2 NULL,
        [ExecutionResult] nvarchar(max) NULL,
        [ErrorMessage] nvarchar(max) NULL,
        
        -- 用户信息
        [AppliedByUserId] int NULL,
        [Notes] nvarchar(500) NULL,
        
        -- BaseEntity 字段
        [CreatedTime] datetime2 NOT NULL DEFAULT GETDATE(),
        [UpdatedTime] datetime2 NULL,
        [CreatedBy] int NULL,
        [UpdatedBy] int NULL,
        [IsDeleted] bit NOT NULL DEFAULT 0,
        [DeletedTime] datetime2 NULL,
        [DeletedBy] int NULL,
        [Version] int NOT NULL DEFAULT 1,
        [Remarks] nvarchar(500) NULL,
        
        -- 约束
        CONSTRAINT [PK_StepTemplateSequenceAssociations] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_DevelopmentSteps] 
            FOREIGN KEY ([StepId]) REFERENCES [dbo].[DevelopmentSteps]([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_UIAutoMationTemplateSequences] 
            FOREIGN KEY ([SequenceId]) REFERENCES [dbo].[UIAutoMationTemplateSequences]([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_Users_AppliedBy] 
            FOREIGN KEY ([AppliedByUserId]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_Users_CreatedBy] 
            FOREIGN KEY ([CreatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_Users_UpdatedBy] 
            FOREIGN KEY ([UpdatedBy]) REFERENCES [dbo].[Users]([Id]),
        CONSTRAINT [FK_StepTemplateSequenceAssociations_Users_DeletedBy] 
            FOREIGN KEY ([DeletedBy]) REFERENCES [dbo].[Users]([Id]),
        
        -- 检查约束
        CONSTRAINT [CK_StepTemplateSequenceAssociations_Status] 
            CHECK ([Status] IN ('Active', 'Inactive', 'Running', 'Completed', 'Failed')),
        CONSTRAINT [CK_StepTemplateSequenceAssociations_Progress] 
            CHECK ([Progress] >= 0 AND [Progress] <= 100)
    );
    
    PRINT '✓ StepTemplateSequenceAssociations 表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️  StepTemplateSequenceAssociations 表已存在，跳过创建';
END
GO

-- =============================================
-- 3. 创建索引
-- =============================================

PRINT '正在创建索引...';

-- 唯一索引：防止同一步骤重复关联同一序列
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StepTemplateSequenceAssociations_StepId_SequenceId_Unique')
BEGIN
    CREATE UNIQUE INDEX [IX_StepTemplateSequenceAssociations_StepId_SequenceId_Unique]
    ON [dbo].[StepTemplateSequenceAssociations] ([StepId], [SequenceId])
    WHERE [IsDeleted] = 0 AND [IsActive] = 1;
    
    PRINT '✓ 唯一索引创建成功';
END

-- 查询优化索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StepTemplateSequenceAssociations_StepId')
BEGIN
    CREATE INDEX [IX_StepTemplateSequenceAssociations_StepId]
    ON [dbo].[StepTemplateSequenceAssociations] ([StepId])
    WHERE [IsDeleted] = 0;
    
    PRINT '✓ StepId 索引创建成功';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StepTemplateSequenceAssociations_SequenceId')
BEGIN
    CREATE INDEX [IX_StepTemplateSequenceAssociations_SequenceId]
    ON [dbo].[StepTemplateSequenceAssociations] ([SequenceId])
    WHERE [IsDeleted] = 0;
    
    PRINT '✓ SequenceId 索引创建成功';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StepTemplateSequenceAssociations_Status_IsActive')
BEGIN
    CREATE INDEX [IX_StepTemplateSequenceAssociations_Status_IsActive]
    ON [dbo].[StepTemplateSequenceAssociations] ([Status], [IsActive])
    WHERE [IsDeleted] = 0;
    
    PRINT '✓ 状态索引创建成功';
END

-- =============================================
-- 4. 添加表注释
-- =============================================

PRINT '正在添加表注释...';

-- 表注释
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'开发步骤与UI自动化模板序列关联表，用于记录开发步骤应用的模板序列信息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations';

-- 字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键，自增', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'Id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'开发步骤ID，关联DevelopmentSteps表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'StepId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'模板序列ID，关联UIAutoMationTemplateSequences表', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'SequenceId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'模板序列应用时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'AppliedTime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'应用状态：Active(活跃), Inactive(非活跃), Running(运行中), Completed(已完成), Failed(失败)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'Status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否激活', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'IsActive';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'执行进度(0-100)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'Progress';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'执行开始时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'ExecutionStartTime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'执行完成时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'ExecutionEndTime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'执行结果', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'ExecutionResult';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'错误信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'ErrorMessage';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'应用者用户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'AppliedByUserId';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'StepTemplateSequenceAssociations', @level2type = N'COLUMN', @level2name = N'Notes';

PRINT '✓ 表注释添加成功';
GO

-- =============================================
-- 5. 验证创建结果
-- =============================================

PRINT '正在验证创建结果...';

-- 验证表是否创建成功
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'StepTemplateSequenceAssociations')
BEGIN
    PRINT '✓ StepTemplateSequenceAssociations 表验证成功';
    
    -- 显示表结构信息
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'StepTemplateSequenceAssociations'
    ORDER BY ORDINAL_POSITION;
    
END
ELSE
BEGIN
    PRINT '❌ StepTemplateSequenceAssociations 表创建失败';
END
GO

-- =============================================
-- 6. 完成脚本
-- =============================================

PRINT '';
PRINT '✅ 迁移脚本执行完成！';
PRINT '======================================================';
PRINT '已创建以下数据库对象：';
PRINT '1. StepTemplateSequenceAssociations 表 - 开发步骤与模板序列关联表';
PRINT '2. 相关索引 - 优化查询性能';
PRINT '3. 外键约束 - 保证数据完整性';
PRINT '4. 检查约束 - 保证数据有效性';
PRINT '5. 表和字段注释 - 提供文档说明';
PRINT '';
PRINT '功能特性：';
PRINT '- 支持开发步骤与UI自动化模板序列的多对多关联';
PRINT '- 记录应用时间、执行状态、进度等信息';
PRINT '- 支持软删除和审计跟踪';
PRINT '- 防止重复关联的唯一约束';
PRINT '- 完整的外键约束保证数据完整性';
PRINT '';
PRINT '下一步：';
PRINT '1. 在应用程序中使用 StepTemplateSequenceAssociation 实体';
PRINT '2. 实现模板序列应用的业务逻辑';
PRINT '3. 测试关联功能的正确性';
PRINT '';
PRINT '脚本执行时间: ' + CONVERT(VARCHAR, GETDATE(), 120);
GO
