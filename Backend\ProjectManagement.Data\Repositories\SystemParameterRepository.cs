using Microsoft.Extensions.Logging;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;
using SqlSugar;

namespace ProjectManagement.Data.Repositories
{
    /// <summary>
    /// 系统参数仓储实现
    /// </summary>
    public class SystemParameterRepository : BaseRepository<SystemParameter>, ISystemParameterRepository
    {
        public SystemParameterRepository(ISqlSugarClient db, ILogger<SystemParameterRepository> logger)
            : base(db, logger)
        {
        }

        /// <summary>
        /// 根据分类获取参数列表
        /// </summary>
        public async Task<List<SystemParameter>> GetByCategoryAsync(string category, bool includeInactive = false)
        {
            try
            {
                var query = _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && !x.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(x => x.IsActive);
                }

                var result = await query
                    .OrderBy(x => new { x.SortOrder, x.DisplayName })
                    .ToListAsync();

                _logger.LogInformation("获取分类参数成功: {Category}, 数量: {Count}", category, result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类参数失败: {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// 根据分类和键名获取参数
        /// </summary>
        public async Task<SystemParameter?> GetByKeyAsync(string category, string key)
        {
            try
            {
                var result = await _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && x.ParameterKey == key && !x.IsDeleted)
                    .FirstAsync();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据键名获取参数失败: {Category}.{Key}", category, key);
                throw;
            }
        }

        /// <summary>
        /// 根据分类和值获取参数
        /// </summary>
        public async Task<SystemParameter?> GetByValueAsync(string category, string value)
        {
            try
            {
                var result = await _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && x.ParameterValue == value && !x.IsDeleted)
                    .FirstAsync();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据值获取参数失败: {Category}.{Value}", category, value);
                throw;
            }
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        public async Task<List<string>> GetCategoriesAsync()
        {
            try
            {
                var result = await _db.Queryable<SystemParameter>()
                    .Where(x => !x.IsDeleted)
                    .GroupBy(x => x.Category)
                    .Select(x => x.Category)
                    .ToListAsync();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类列表失败");
                throw;
            }
        }

        /// <summary>
        /// 批量更新排序
        /// </summary>
        public async Task<bool> UpdateSortOrderAsync(List<SystemParameter> parameters)
        {
            try
            {
                foreach (var parameter in parameters)
                {
                    await _db.Updateable<SystemParameter>()
                        .SetColumns(x => new SystemParameter { SortOrder = parameter.SortOrder, UpdatedTime = DateTime.Now })
                        .Where(x => x.Id == parameter.Id)
                        .ExecuteCommandAsync();
                }

                _logger.LogInformation("批量更新排序成功，数量: {Count}", parameters.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新排序失败");
                throw;
            }
        }

        /// <summary>
        /// 检查键名是否存在
        /// </summary>
        public async Task<bool> KeyExistsAsync(string category, string key, int? excludeId = null)
        {
            try
            {
                var query = _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && x.ParameterKey == key && !x.IsDeleted);

                if (excludeId.HasValue)
                {
                    query = query.Where(x => x.Id != excludeId.Value);
                }

                var count = await query.CountAsync();
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查键名是否存在失败: {Category}.{Key}", category, key);
                throw;
            }
        }

        /// <summary>
        /// 获取分类统计
        /// </summary>
        public async Task<Dictionary<string, int>> GetCategoryStatisticsAsync()
        {
            try
            {
                var result = await _db.Queryable<SystemParameter>()
                    .Where(x => !x.IsDeleted && x.IsActive)
                    .GroupBy(x => x.Category)
                    .Select(x => new { Category = x.Category, Count = SqlFunc.AggregateCount(x.Id) })
                    .ToListAsync();

                return result.ToDictionary(x => x.Category, x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分类统计失败");
                throw;
            }
        }

        /// <summary>
        /// 根据分类获取键值对字典
        /// </summary>
        public async Task<Dictionary<string, string>> GetKeyValueDictionaryAsync(string category, bool includeInactive = false)
        {
            try
            {
                var query = _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && !x.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(x => x.IsActive);
                }

                var result = await query
                    .OrderBy(x => x.SortOrder)
                    .Select(x => new { x.ParameterKey, x.ParameterValue })
                    .ToListAsync();

                return result.ToDictionary(x => x.ParameterKey, x => x.ParameterValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取键值对字典失败: {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// 根据分类获取显示名称字典
        /// </summary>
        public async Task<Dictionary<string, string>> GetDisplayNameDictionaryAsync(string category, bool includeInactive = false)
        {
            try
            {
                var query = _db.Queryable<SystemParameter>()
                    .Where(x => x.Category == category && !x.IsDeleted);

                if (!includeInactive)
                {
                    query = query.Where(x => x.IsActive);
                }

                var result = await query
                    .OrderBy(x => x.SortOrder)
                    .Select(x => new { x.ParameterValue, x.DisplayName })
                    .ToListAsync();

                return result.ToDictionary(x => x.ParameterValue, x => x.DisplayName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取显示名称字典失败: {Category}", category);
                throw;
            }
        }
    }
}
