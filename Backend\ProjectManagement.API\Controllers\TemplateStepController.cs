using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectManagement.Core.DTOs;
using ProjectManagement.Core.Entities;
using ProjectManagement.Core.Interfaces;

namespace ProjectManagement.API.Controllers
{
    /// <summary>
    /// 模板步骤控制器
    /// </summary>
    [ApiController]
    [Route("api/template-steps")]
    [Authorize]
    public class TemplateStepController : ControllerBase
    {
        private readonly ITemplateStepRepository _stepRepository;
        private readonly ILogger<TemplateStepController> _logger;

        public TemplateStepController(
            ITemplateStepRepository stepRepository,
            ILogger<TemplateStepController> logger)
        {
            _stepRepository = stepRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取步骤详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<TemplateStepDto>> GetStep(int id)
        {
            try
            {
                var step = await _stepRepository.GetByIdAsync(id);
                if (step == null || step.IsDeleted)
                {
                    return NotFound("步骤不存在");
                }

                return Ok(MapToDto(step));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取步骤详情失败，ID: {Id}", id);
                return StatusCode(500, "获取步骤详情失败");
            }
        }

        /// <summary>
        /// 创建步骤
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<TemplateStepDto>> CreateStep([FromBody] CreateTemplateStepDto dto)
        {
            try
            {
                var step = new UIAutoMationTemplateStep
                {
                    SequenceId = dto.SequenceId,
                    TemplateId = dto.TemplateId,
                    StepOrder = dto.StepOrder,
                    ActionType = dto.ActionType,
                    LogicType = dto.LogicType, // 添加逻辑语句类型
                    Description = dto.Description,
                    Parameters = System.Text.Json.JsonSerializer.Serialize(dto.Parameters ?? new Dictionary<string, object>()),
                    TimeoutSeconds = dto.TimeoutSeconds,
                    MaxRetries = dto.MaxRetries,
                    IsActive = dto.IsActive,
                    // 流程控制字段
                    ConditionExpression = dto.ConditionExpression,
                    JumpToStepId = dto.JumpToStepId,
                    LoopCount = dto.LoopCount,
                    LoopVariable = dto.LoopVariable,
                    GroupId = dto.GroupId,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    IsDeleted = false
                };

                var result = await _stepRepository.AddAsync(step);

                _logger.LogInformation("创建步骤成功，ID: {Id}, 序列ID: {SequenceId}", result.Id, dto.SequenceId);
                return CreatedAtAction(nameof(GetStep), new { id = result.Id }, MapToDto(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建步骤失败");
                return StatusCode(500, "创建步骤失败");
            }
        }

        /// <summary>
        /// 更新步骤
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<TemplateStepDto>> UpdateStep(int id, [FromBody] UpdateTemplateStepDto dto)
        {
            try
            {
                var step = await _stepRepository.GetByIdAsync(id);
                if (step == null || step.IsDeleted)
                {
                    return NotFound("步骤不存在");
                }

                // 更新字段
                if (dto.TemplateId.HasValue)
                    step.TemplateId = dto.TemplateId.Value;

                if (dto.StepOrder.HasValue)
                    step.StepOrder = dto.StepOrder.Value;

                if (!string.IsNullOrEmpty(dto.ActionType))
                    step.ActionType = dto.ActionType;

                if (!string.IsNullOrEmpty(dto.LogicType))
                    step.LogicType = dto.LogicType;

                if (!string.IsNullOrEmpty(dto.Description))
                    step.Description = dto.Description;

                if (dto.Parameters != null)
                    step.Parameters = System.Text.Json.JsonSerializer.Serialize(dto.Parameters);

                if (dto.TimeoutSeconds.HasValue)
                    step.TimeoutSeconds = dto.TimeoutSeconds.Value;

                if (dto.MaxRetries.HasValue)
                    step.MaxRetries = dto.MaxRetries.Value;

                if (dto.IsActive.HasValue)
                    step.IsActive = dto.IsActive.Value;

                // 更新流程控制字段
                if (!string.IsNullOrEmpty(dto.ConditionExpression))
                    step.ConditionExpression = dto.ConditionExpression;

                if (dto.JumpToStepId.HasValue)
                    step.JumpToStepId = dto.JumpToStepId.Value;

                if (dto.LoopCount.HasValue)
                    step.LoopCount = dto.LoopCount.Value;

                if (!string.IsNullOrEmpty(dto.LoopVariable))
                    step.LoopVariable = dto.LoopVariable;

                if (!string.IsNullOrEmpty(dto.GroupId))
                    step.GroupId = dto.GroupId;

                step.UpdatedTime = DateTime.Now;

                await _stepRepository.UpdateAsync(step);

                _logger.LogInformation("更新步骤成功，ID: {Id}", id);
                return Ok(MapToDto(step));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新步骤失败，ID: {Id}", id);
                return StatusCode(500, "更新步骤失败");
            }
        }

        /// <summary>
        /// 删除步骤
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteStep(int id)
        {
            try
            {
                var step = await _stepRepository.GetByIdAsync(id);
                if (step == null || step.IsDeleted)
                {
                    return NotFound("步骤不存在");
                }

                // 软删除
                step.IsDeleted = true;
                step.UpdatedTime = DateTime.Now;
                await _stepRepository.UpdateAsync(step);

                _logger.LogInformation("删除步骤成功，ID: {Id}", id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除步骤失败，ID: {Id}", id);
                return StatusCode(500, "删除步骤失败");
            }
        }

        #region 私有方法

        /// <summary>
        /// 映射到DTO
        /// </summary>
        private static TemplateStepDto MapToDto(UIAutoMationTemplateStep step)
        {
            Dictionary<string, object> parameters;
            try
            {
                parameters = string.IsNullOrEmpty(step.Parameters)
                    ? new Dictionary<string, object>()
                    : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(step.Parameters) ?? new Dictionary<string, object>();
            }
            catch
            {
                parameters = new Dictionary<string, object>();
            }

            return new TemplateStepDto
            {
                Id = step.Id,
                SequenceId = step.SequenceId,
                TemplateId = step.TemplateId,
                StepOrder = step.StepOrder,
                ActionType = step.ActionType,
                LogicType = step.LogicType, // 添加逻辑语句类型
                Description = step.Description,
                Parameters = parameters,
                TimeoutSeconds = step.TimeoutSeconds,
                RetryCount = 0, // 当前重试次数，默认为0
                MaxRetries = step.MaxRetries,
                IsActive = step.IsActive,
                // 流程控制字段
                ConditionExpression = step.ConditionExpression,
                JumpToStepId = step.JumpToStepId,
                LoopCount = step.LoopCount,
                LoopVariable = step.LoopVariable,
                GroupId = step.GroupId,
                CreatedTime = step.CreatedTime,
                UpdatedTime = step.UpdatedTime
            };
        }

        #endregion
    }
}
