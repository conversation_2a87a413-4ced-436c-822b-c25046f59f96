-- 为现有用户添加需求分解任务映射
USE ProjectManagementAI;

-- 为用户ID=1添加 RequirementDecomposition 任务映射
INSERT INTO UserTaskMappings (
    UserId, TaskType, ProviderName, ModelName, IsActive, IsDefault, Priority,
    ConfigurationParameters, Description, CreatedAt, UpdatedAt
)
VALUES (
    1, 'RequirementDecomposition', 'DeepSeek', 'deepseek-chat', 1, 1, 50,
    NULL, '需求分解任务映射', GETDATE(), GETDATE()
);

-- 为用户ID=1添加 DependencyAnalysis 任务映射
INSERT INTO UserTaskMappings (
    UserId, TaskType, ProviderName, ModelName, IsActive, IsDefault, Priority,
    ConfigurationParameters, Description, CreatedAt, UpdatedAt
)
VALUES (
    1, 'DependencyAnalysis', 'DeepSeek', 'deepseek-chat', 1, 1, 51,
    NULL, '依赖关系分析任务映射', GETDATE(), GETDATE()
);

-- 显示添加的映射
SELECT UserId, TaskType, ProviderName, ModelName, Priority, IsActive, IsDefault
FROM UserTaskMappings
WHERE TaskType IN ('RequirementDecomposition', 'DependencyAnalysis')
ORDER BY UserId, TaskType;
