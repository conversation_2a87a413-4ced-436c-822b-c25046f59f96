/**
 * AI对话服务 - 为不同功能场景提供统一的AI对话接口
 */

import { RequirementService } from './requirement'
import { AIApiService } from './api'

// AI对话响应接口
export interface AIDialogResponse {
  messageId: string
  conversationId: string
  userMessage: string
  aiResponse: string
  timestamp: string
}

// AI对话服务接口
export interface AIDialogService {
  sendMessage: (conversationId: string, message: string, context?: any) => Promise<AIDialogResponse>
  getHistory?: (conversationId: string) => Promise<Array<{
    userMessage: string
    aiResponse: string
    timestamp: string
  }>>
}

// 上下文信息接口
export interface AIDialogContext {
  projectId?: number
  projectName?: string
  taskType?: string
  aiProvider?: string
  aiProviderConfigId?: number
  promptTemplateId?: number
  [key: string]: any
}

// 需求分析AI服务
export class RequirementAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    return await RequirementService.sendMessage(
      conversationId,
      message,
      context?.projectId,
      context?.aiProviderConfigId
    )
  }

  async getHistory(conversationId: string) {
    return await RequirementService.getConversationHistory(conversationId)
  }
}

// 需求分解AI服务
export class RequirementDecomposeAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    // 构建需求分解的专用提示词
    const prompt = this.buildDecomposePrompt(message, context)

    // 调用通用AI接口
    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'RequirementDecomposition',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildDecomposePrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为需求分解专家，我需要帮助用户将需求分解为具体的开发步骤。

当前上下文：`

    if (context?.projectName) {
      prompt += `\n- 项目名称：${context.projectName}`
    }
    if (context?.taskType) {
      prompt += `\n- 任务类型：${context.taskType}`
    }

    prompt += `\n\n用户消息：${message}

请根据用户的需求，提供以下帮助：
1. 理解和澄清需求细节
2. 建议合适的分解策略
3. 推荐技术栈和工具
4. 评估工作量和优先级
5. 识别潜在的依赖关系

请以友好、专业的方式回复，并引导用户提供更多必要信息。`

    return prompt
  }
}

// 代码生成AI服务
export class CodeGenerationAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildCodeGenerationPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'CodeGeneration',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildCodeGenerationPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为代码生成专家，我需要帮助用户生成高质量的代码。

当前上下文：`

    if (context?.projectName) {
      prompt += `\n- 项目名称：${context.projectName}`
    }
    if (context?.technologyStack) {
      prompt += `\n- 技术栈：${context.technologyStack}`
    }

    prompt += `\n\n用户需求：${message}

请根据用户的需求，提供以下帮助：
1. 理解代码生成需求
2. 选择合适的技术方案
3. 生成符合最佳实践的代码
4. 提供代码说明和使用指南
5. 建议测试方案

请确保生成的代码具有良好的可读性、可维护性和性能。`

    return prompt
  }
}

// 测试生成AI服务
export class TestGenerationAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildTestGenerationPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'TestGeneration',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildTestGenerationPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为测试专家，我需要帮助用户生成全面的测试方案和测试代码。

当前上下文：`

    if (context?.projectName) {
      prompt += `\n- 项目名称：${context.projectName}`
    }
    if (context?.testingFramework) {
      prompt += `\n- 测试框架：${context.testingFramework}`
    }

    prompt += `\n\n用户需求：${message}

请根据用户的需求，提供以下帮助：
1. 分析测试需求和范围
2. 设计测试策略和方案
3. 生成单元测试代码
4. 生成集成测试代码
5. 提供测试数据和Mock方案

请确保测试覆盖率高，测试用例全面且易于维护。`

    return prompt
  }
}

// 文档生成AI服务
export class DocumentationAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildDocumentationPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'Documentation',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildDocumentationPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为技术文档专家，我需要帮助用户生成清晰、完整的技术文档。

当前上下文：`

    if (context?.projectName) {
      prompt += `\n- 项目名称：${context.projectName}`
    }
    if (context?.documentType) {
      prompt += `\n- 文档类型：${context.documentType}`
    }

    prompt += `\n\n用户需求：${message}

请根据用户的需求，提供以下帮助：
1. 理解文档需求和目标受众
2. 设计文档结构和大纲
3. 生成详细的文档内容
4. 提供代码示例和图表
5. 确保文档的可读性和实用性

请确保文档内容准确、结构清晰、易于理解和维护。`

    return prompt
  }
}

// 项目管理AI服务
export class ProjectManagementAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildProjectPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'ProjectManagement',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildProjectPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为项目管理专家，我需要帮助用户创建和管理软件开发项目。

我的专业能力包括：
1. 项目规划和时间管理
2. 风险评估和控制
3. 团队协作和资源分配
4. 项目进度跟踪
5. 质量管理和交付

当前上下文：`

    if (context?.projectId) {
      prompt += `\n- 项目ID: ${context.projectId}`
    }
    if (context?.projectName) {
      prompt += `\n- 项目名称: ${context.projectName}`
    }

    prompt += `\n\n用户问题: ${message}

请提供专业的项目管理建议，如果用户想要创建项目，请帮助分析项目需求并提供创建建议。`

    return prompt
  }
}

// 设计生成AI服务
export class DesignGenerationAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildDesignPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'DesignGeneration',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  private buildDesignPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为系统设计专家，我需要帮助用户生成各种设计图表和文档。

我的专业能力包括：
1. ER图设计和数据库建模
2. 系统架构图和上下文图
3. 原型图和界面设计
4. 流程图和时序图
5. 技术架构和部署图

当前上下文：`

    if (context?.projectId) {
      prompt += `\n- 项目ID: ${context.projectId}`
    }
    if (context?.projectName) {
      prompt += `\n- 项目名称: ${context.projectName}`
    }

    prompt += `\n\n用户需求: ${message}

请分析用户的设计需求，提供专业的设计建议，并在适当时候建议生成具体的设计图表。`

    return prompt
  }
}

// 通用AI服务
export class GeneralAIService implements AIDialogService {
  async sendMessage(conversationId: string, message: string, context?: AIDialogContext): Promise<AIDialogResponse> {
    const prompt = this.buildGeneralPrompt(message, context)

    return await AIApiService.post('/api/ai/chat', {
      conversationId,
      message: prompt,
      taskType: 'General',
      projectId: context?.projectId,
      aiProviderConfigId: context?.aiProviderConfigId,
      promptTemplateId: context?.promptTemplateId
    }) as AIDialogResponse
  }

  /**
   * 发送流式AI对话消息
   */
  async sendStreamMessage(
    conversationId: string,
    message: string,
    context?: AIDialogContext,
    onChunk?: (chunk: string) => void,
    onComplete?: (fullResponse: string, actions?: any[]) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch('/api/ai/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        },
        body: JSON.stringify({
          conversationId,
          message,
          taskType: 'General',
          projectId: context?.projectId,
          aiProviderConfigId: context?.aiProviderConfigId,
          promptTemplateId: context?.promptTemplateId
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let fullResponse = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              switch (data.type) {
                case 'start':
                  // 开始接收消息
                  break
                case 'chunk':
                  fullResponse += data.content
                  onChunk?.(data.content)
                  break
                case 'actions':
                  // 接收到建议操作
                  onComplete?.(fullResponse, data.actions)
                  return
                case 'end':
                  // 消息接收完成
                  onComplete?.(fullResponse)
                  return
                case 'error':
                  onError?.(data.message)
                  return
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      onError?.(error instanceof Error ? error.message : '发送消息失败')
    }
  }

  private buildGeneralPrompt(message: string, context?: AIDialogContext): string {
    let prompt = `作为AI助手，我可以帮助您解决各种软件开发相关的问题。

我的能力包括：
1. 技术咨询和最佳实践建议
2. 代码问题解答
3. 架构设计建议
4. 工具和框架推荐
5. 学习路径规划

当前上下文：`

    if (context?.projectId) {
      prompt += `\n- 项目ID: ${context.projectId}`
    }
    if (context?.projectName) {
      prompt += `\n- 项目名称: ${context.projectName}`
    }

    prompt += `\n\n用户问题: ${message}

请提供有帮助的回答和建议。`

    return prompt
  }
}

// AI服务工厂
export class AIDialogServiceFactory {
  private static services: Map<string, AIDialogService> = new Map()

  static getService(taskType: string): AIDialogService {
    if (!this.services.has(taskType)) {
      switch (taskType) {
        case 'General':
          this.services.set(taskType, new GeneralAIService())
          break
        case 'ProjectManagement':
          this.services.set(taskType, new ProjectManagementAIService())
          break
        case 'RequirementAnalysis':
          this.services.set(taskType, new RequirementAIService())
          break
        case 'RequirementDecomposition':
          this.services.set(taskType, new RequirementDecomposeAIService())
          break
        case 'DesignGeneration':
          this.services.set(taskType, new DesignGenerationAIService())
          break
        case 'CodeGeneration':
          this.services.set(taskType, new CodeGenerationAIService())
          break
        case 'TestGeneration':
          this.services.set(taskType, new TestGenerationAIService())
          break
        case 'Documentation':
          this.services.set(taskType, new DocumentationAIService())
          break
        default:
          // 默认使用通用AI服务
          this.services.set(taskType, new GeneralAIService())
      }
    }
    return this.services.get(taskType)!
  }

  static registerService(taskType: string, service: AIDialogService) {
    this.services.set(taskType, service)
  }
}

// 预定义的对话配置
export const AI_DIALOG_CONFIGS = {
  RequirementAnalysis: {
    title: 'AI需求分析助手',
    welcomeTitle: '👋 您好！我是AI需求分析助手',
    welcomeDescription: `
      <p>我可以帮助您：</p>
      <ul>
        <li>📝 理解和分析您的项目需求</li>
        <li>🎯 识别功能和非功能需求</li>
        <li>📊 评估项目可行性和复杂度</li>
        <li>📋 生成详细的需求规格书</li>
        <li>🗺️ 推荐技术架构方案</li>
      </ul>
      <p>请描述您的项目需求，我会为您提供专业的分析建议。</p>
    `,
    inputPlaceholder: '请描述您的项目需求，例如：我想开发一个在线商城系统...',
    taskType: 'RequirementAnalysis'
  },
  RequirementDecomposition: {
    title: 'AI需求分解助手',
    welcomeTitle: '🔧 您好！我是AI需求分解助手',
    welcomeDescription: `
      <p>我可以帮助您：</p>
      <ul>
        <li>📋 将复杂需求分解为具体步骤</li>
        <li>⚡ 评估开发工作量和优先级</li>
        <li>🔗 识别步骤间的依赖关系</li>
        <li>🛠️ 推荐合适的技术栈和工具</li>
        <li>📅 制定开发计划和里程碑</li>
      </ul>
      <p>请告诉我您想要分解的需求，我会帮您制定详细的开发计划。</p>
    `,
    inputPlaceholder: '请描述您想要分解的需求，例如：用户登录注册功能...',
    taskType: 'RequirementDecomposition'
  },
  CodeGeneration: {
    title: 'AI代码生成助手',
    welcomeTitle: '💻 您好！我是AI代码生成助手',
    welcomeDescription: `
      <p>我可以帮助您：</p>
      <ul>
        <li>⚡ 快速生成高质量代码</li>
        <li>🏗️ 创建项目架构和模板</li>
        <li>🔧 实现具体功能模块</li>
        <li>📚 提供代码说明和文档</li>
        <li>✅ 遵循最佳实践和规范</li>
      </ul>
      <p>请告诉我您需要生成什么代码，我会为您提供完整的实现方案。</p>
    `,
    inputPlaceholder: '请描述您需要的代码功能，例如：用户认证中间件...',
    taskType: 'CodeGeneration'
  }
}
